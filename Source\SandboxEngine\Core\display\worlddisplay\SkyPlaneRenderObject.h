#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"

namespace Rainbow {

class Mesh;
class SkyPlaneRenderer;

class SkyPlaneRenderObject: public RenderObject
{
private:
	SharePtr<MaterialInstance> m_SkyMaterial;
	SharePtr<Mesh> m_pMesh;

	SharePtr<Mesh> m_CloudMesh;
	SharePtr<MaterialInstance> m_CloudMaterial;

	SharePtr<Mesh> m_AuroraMesh1;
	SharePtr<MaterialInstance> m_AuroraMaterial1;

	SharePtr<Mesh> m_AuroraMesh2;
	SharePtr<MaterialInstance> m_AuroraMaterial2;

	Mesh* m_pSunMesh;
	Mesh* m_pMoonMesh;
	Mesh* m_pRainbowMesh;
	VertexLayout* m_VertexLayout;
	SharePtr<MaterialInstance> m_SunMaterial;

	SharePtr<MaterialInstance> m_MoonMaterial;

	SharePtr<MaterialInstance> m_RainbowMaterial;

public:
	explicit SkyPlaneRenderObject(SkyPlaneRenderer* component);
	~SkyPlaneRenderObject();
	
	virtual bool PrepareRender(const PrimitiveFrameNode& frameNode) override;
	virtual void ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator) override;

	void OnChangeSkyMaterial();
	void OnChangeSunMaterial();
	void OnChangeMoonMaterial();
};

}  // namespace Rainbow

