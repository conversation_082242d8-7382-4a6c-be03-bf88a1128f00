#pragma once
#include "Graphics/Mesh/Mesh.h"
#include "Recast.h"

// Internal representation for transformed source meshes
struct NavMeshTriangleData
{
    NavMeshTriangleData()
        : vertices(kMemTempAlloc)
    {}

    dynamic_array<Rainbow::Vector3f> vertices;
    Rainbow::Mesh::TemporaryIndexContainer indices;
};

// Store recast allocations for one tile
struct TileAllocations
{
    TileAllocations()
        : m_heightField(NULL)
        , m_polyMesh(NULL)
        , m_detailMesh(NULL)
        , m_chf(NULL)
        , m_cset(NULL)
        , m_triAreas(kMemTempAlloc)
        , m_ownsData(false)
    {}

    ~TileAllocations()
    {
        if (!m_ownsData)
            return;

        rcFreeHeightField(m_heightField);
        rcFreePolyMesh(m_polyMesh);
        rcFreePolyMeshDetail(m_detailMesh);
        rcFreeCompactHeightfield(m_chf);
        rcFreeContourSet(m_cset);
    }

    rcHeightfield* m_heightField;
    rcPolyMesh* m_polyMesh;
    rcPolyMeshDetail* m_detailMesh;
    rcCompactHeightfield* m_chf;
    rcContourSet* m_cset;

    NavMeshTriangleData m_triData;
    dynamic_array<UInt8> m_triAreas;

    bool m_ownsData;
};
