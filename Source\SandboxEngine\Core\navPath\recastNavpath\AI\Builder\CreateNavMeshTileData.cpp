//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//


#include "CreateNavMeshTileData.h"
#include "NavMesh/NavMesh.h"
#include "MathUtil.h"
#include "Utilities/Align.h"
#include "Utilities/Utility.h"
#include <math.h>
#include <stdlib.h>

static unsigned short kMeshNullIdx = 0xffff;


struct BVItem
{
    unsigned short bmin[3];
    unsigned short bmax[3];
    int i;
};

template<int AXIS>
static int CompareItem(const void* va, const void* vb)
{
    const BVItem* a = (const BVItem*)va;
    const BVItem* b = (const BVItem*)vb;
    if (a->bmin[AXIS] < b->bmin[AXIS])
        return -1;
    if (a->bmin[AXIS] > b->bmin[AXIS])
        return 1;
    return 0;
}

static void CalcExtends(BVItem* items, const int /*nitems*/, const int imin, const int imax,
    unsigned short* bmin, unsigned short* bmax)
{
    bmin[0] = items[imin].bmin[0];
    bmin[1] = items[imin].bmin[1];
    bmin[2] = items[imin].bmin[2];

    bmax[0] = items[imin].bmax[0];
    bmax[1] = items[imin].bmax[1];
    bmax[2] = items[imin].bmax[2];

    for (int i = imin + 1; i < imax; ++i)
    {
        const BVItem& it = items[i];
        if (it.bmin[0] < bmin[0])
            bmin[0] = it.bmin[0];
        if (it.bmin[1] < bmin[1])
            bmin[1] = it.bmin[1];
        if (it.bmin[2] < bmin[2])
            bmin[2] = it.bmin[2];

        if (it.bmax[0] > bmax[0])
            bmax[0] = it.bmax[0];
        if (it.bmax[1] > bmax[1])
            bmax[1] = it.bmax[1];
        if (it.bmax[2] > bmax[2])
            bmax[2] = it.bmax[2];
    }
}

inline int LongestAxis(unsigned short x, unsigned short y, unsigned short z)
{
    int axis = 0;
    unsigned short maxVal = x;
    if (y > maxVal)
    {
        axis = 1;
        maxVal = y;
    }
    if (z > maxVal)
    {
        axis = 2;
    }
    return axis;
}

static void Subdivide(BVItem* items, int nitems, int imin, int imax, int& curNode, NavMeshBVNode* nodes)
{
    int inum = imax - imin;
    int icur = curNode;

    NavMeshBVNode& node = nodes[curNode++];

    if (inum == 1)
    {
        // Leaf
        node.bmin[0] = items[imin].bmin[0];
        node.bmin[1] = items[imin].bmin[1];
        node.bmin[2] = items[imin].bmin[2];

        node.bmax[0] = items[imin].bmax[0];
        node.bmax[1] = items[imin].bmax[1];
        node.bmax[2] = items[imin].bmax[2];

        node.i = items[imin].i;
    }
    else
    {
        // Split
        CalcExtends(items, nitems, imin, imax, node.bmin, node.bmax);

        int axis = LongestAxis(node.bmax[0] - node.bmin[0],
            node.bmax[1] - node.bmin[1],
            node.bmax[2] - node.bmin[2]);

        if (axis == 0)
        {
            // Sort along x-axis
            qsort(items + imin, inum, sizeof(BVItem), CompareItem<0>);
        }
        else if (axis == 1)
        {
            // Sort along y-axis
            qsort(items + imin, inum, sizeof(BVItem), CompareItem<1>);
        }
        else
        {
            // Sort along z-axis
            qsort(items + imin, inum, sizeof(BVItem), CompareItem<2>);
        }

        int isplit = imin + inum / 2;

        // Left
        Subdivide(items, nitems, imin, isplit, curNode, nodes);
        // Right
        Subdivide(items, nitems, isplit, imax, curNode, nodes);

        int iescape = curNode - icur;
        // Negative index means escape.
        node.i = -iescape;
    }
}

// use detail polygons to calculate conservative min/max vertical bounds.
static void CalculatePolygonHeightMinMax(unsigned short* heightMinMax, int nvp, const NavMeshCreateParams* params,
    const NavMeshPolyDetail* navDMeshes, const float* navDVerts)
{
    for (int ip = 0; ip < params->polyCount; ++ip)
    {
        // Loop over all vertices (including detail verts) in polygon to get min/max height.
        float ymin, ymax;

        const unsigned short* p = &params->polys[2 * nvp * ip];
        ymin = ymax = params->bmin[1] + params->ch * params->verts[3 * p[0] + 1];

        for (int iv = 1; iv < nvp; ++iv)
        {
            if (p[iv] == kMeshNullIdx)
                break;
            float y = params->bmin[1] + params->ch * params->verts[3 * p[iv] + 1];
            ymin = std::min(ymin, y);
            ymax = std::max(ymax, y);
        }

        // Loop over detail polygons
        {
            const NavMeshPolyDetail& dtl = navDMeshes[ip];
            const int vbase = dtl.vertBase;
            const int vcount = dtl.vertCount;

            for (int id = 0; id < vcount; ++id)
            {
                float y = navDVerts[3 * (vbase + id) + 1];
                ymin = std::min(ymin, y);
                ymax = std::max(ymax, y);
            }
        }

        // Convert bounds to 'cs' units and clamp to unsigned short range.
        // Note: the BV tree uses 'cs' units for height also (i.e. not 'ch')
        int iymin = clamp((int)(floorf(ymin - params->bmin[1]) / params->cs) , 0 , 0xffff);
        int iymax = clamp((int)(ceilf(ymax - params->bmin[1]) / params->cs) , 0 , 0xffff);

        heightMinMax[2 * ip + 0] = (unsigned short)iymin;
        heightMinMax[2 * ip + 1] = (unsigned short)iymax;
    }
}

// use pre-calculate height min/max values per polygon
static int CreateBVTreeWithHeightMinMax(const unsigned short* verts, const int /*nverts*/,
    const unsigned short* polys, const int npolys, const int nvp,
    const float cs, const float ch,
    const int /*nnodes*/, const unsigned short* heightMinMax, NavMeshBVNode* nodes)
{
    // Build tree
    dynamic_array<BVItem> items(kMemTempAlloc);
    items.resize_uninitialized(npolys);

    for (int i = 0; i < npolys; i++)
    {
        BVItem& it = items[i];
        it.i = i;
        // Calc polygon bounds.
        const unsigned short* p = &polys[i * nvp * 2];
        it.bmin[0] = it.bmax[0] = verts[p[0] * 3 + 0];
        it.bmin[1] = heightMinMax[2 * i + 0];
        it.bmax[1] = heightMinMax[2 * i + 1];
        it.bmin[2] = it.bmax[2] = verts[p[0] * 3 + 2];

        for (int j = 1; j < nvp; ++j)
        {
            if (p[j] == kMeshNullIdx)
                break;
            unsigned short x = verts[p[j] * 3 + 0];
            unsigned short z = verts[p[j] * 3 + 2];

            if (x < it.bmin[0])
                it.bmin[0] = x;
            if (z < it.bmin[2])
                it.bmin[2] = z;

            if (x > it.bmax[0])
                it.bmax[0] = x;
            if (z > it.bmax[2])
                it.bmax[2] = z;
        }
    }

    int curNode = 0;
    Subdivide(items.begin(), npolys, 0, npolys, curNode, nodes);

    return curNode;
}

static void DetailMeshesInfo(const NavMeshCreateParams* params, int* uniqueVertCount, int* triCount)
{
    const int nvp = params->nvp;

    int uniqueDetailVertCount = 0;
    int detailTriCount = 0;

    if (params->detailMeshes)
    {
        // Has detail mesh, count unique detail vertex count and use input detail tri count.
        detailTriCount = params->detailTriCount;
        for (int i = 0; i < params->polyCount; ++i)
        {
            const unsigned short* p = &params->polys[i * nvp * 2];
            int ndv = params->detailMeshes[i * 4 + 1];
            int nv = 0;
            for (int j = 0; j < nvp; ++j)
            {
                if (p[j] == kMeshNullIdx)
                    break;
                nv++;
            }
            uniqueDetailVertCount += ndv - nv;
        }
    }
    else
    {
        // No input detail mesh, build detail mesh from nav polys.
        for (int i = 0; i < params->polyCount; ++i)
        {
            const unsigned short* p = &params->polys[i * nvp * 2];
            int nv = 0;
            for (int j = 0; j < nvp; ++j)
            {
                if (p[j] == kMeshNullIdx)
                    break;
                nv++;
            }
            detailTriCount += nv - 2;
        }
    }

    *uniqueVertCount = uniqueDetailVertCount;
    *triCount = detailTriCount;
}

// Creates a navmesh tile from params
bool CreateNavMeshTileData(const NavMeshCreateParams* params, dynamic_array<unsigned char>* outData)
{
    if (params->nvp > kNavMeshVertsPerPoly)
        return false;
    if (params->vertCount >= 0xffff)
        return false;
    if (!params->vertCount || !params->verts)
        return false;
    if (!params->polyCount || !params->polys)
        return false;

    const int nvp = params->nvp;
    const int vertexSize = 3 * sizeof(float);

    // Off-mesh connectionss are stored as polygons, adjust values.
    const int totPolyCount = params->polyCount;
    const int totVertCount = params->vertCount;

    // Find portal edges which are at tile borders.
    int edgeCount = 0;
    int portalCount = 0;
    for (int i = 0; i < params->polyCount; ++i)
    {
        const unsigned short* p = &params->polys[i * 2 * nvp];
        for (int j = 0; j < nvp; ++j)
        {
            if (p[j] == kMeshNullIdx)
                break;
            edgeCount++;

            if (p[nvp + j] & 0x8000)
            {
                unsigned short dir = p[nvp + j] & 0xf;
                if (dir != 0xf)
                    portalCount++;
            }
        }
    }

    // Find unique detail vertices.
    int uniqueDetailVertCount, detailTriCount;
    DetailMeshesInfo(params, &uniqueDetailVertCount, &detailTriCount);

    // Calculate data size
    const int headerSize = Align4(sizeof(NavMeshDataHeader));
    const int vertsSize = Align4(vertexSize * totVertCount);
    const int polysSize = Align4(sizeof(NavMeshPoly) * totPolyCount);
    const int detailMeshesSize = Align4(sizeof(NavMeshPolyDetail) * params->polyCount);
    const int detailVertsSize = Align4(vertexSize * uniqueDetailVertCount);
    const int detailTrisSize = Align4(sizeof(NavMeshPolyDetailIndex) * 4 * detailTriCount);
    const int bvTreeSize = params->buildBvTree ? Align4(sizeof(NavMeshBVNode) * params->polyCount * 2) : 0;

    const int dataSize = headerSize + vertsSize + polysSize +
        detailMeshesSize + detailVertsSize + detailTrisSize +
        bvTreeSize;

    outData->resize_initialized(dataSize, 0);

    unsigned char* d = outData->begin();
    NavMeshDataHeader* header = (NavMeshDataHeader*)d; d += headerSize;
    float* navVerts = (float*)d; d += vertsSize;
    NavMeshPoly* navPolys = (NavMeshPoly*)d; d += polysSize;
    NavMeshPolyDetail* navDMeshes = (NavMeshPolyDetail*)d; d += detailMeshesSize;
    float* navDVerts = (float*)d; d += detailVertsSize;
    NavMeshPolyDetailIndex* navDTris = (NavMeshPolyDetailIndex*)d; d += detailTrisSize;
    NavMeshBVNode* navBvtree = (NavMeshBVNode*)d; d += bvTreeSize;


    // Store header
    header->magic = kNavMeshMagic;
    header->version = kNavMeshVersion;
    header->x = params->tileX;
    header->y = params->tileY;
    header->agentTypeId = params->agentTypeId;
    header->polyCount = totPolyCount;
    header->vertCount = totVertCount;
    header->bmin = params->bmin;
    header->bmax = params->bmax;
    header->detailMeshCount = params->polyCount;
    header->detailVertCount = uniqueDetailVertCount;
    header->detailTriCount = detailTriCount;
    header->bvQuantFactor = 1.0f / params->cs;
    // We use only 2*polyCount-1 nodes for the bv-tree.
    // For now (backwards compatibility with serialized data) we still
    // serialize 2*polyCount - but make sure to never access that last element.
    header->bvNodeCount = params->buildBvTree ? params->polyCount * 2 : 0;

    // Store vertices
    // Mesh vertices
    for (int i = 0; i < params->vertCount; ++i)
    {
        const unsigned short* iv = &params->verts[i * 3];
        float* v = &navVerts[i * 3];
        v[0] = params->bmin[0] + iv[0] * params->cs;
        v[1] = params->bmin[1] + iv[1] * params->ch;
        v[2] = params->bmin[2] + iv[2] * params->cs;
    }

    // Store polygons
    // Mesh polys
    const unsigned short* src = params->polys;
    for (int i = 0; i < params->polyCount; ++i)
    {
        NavMeshPoly* p = &navPolys[i];
        p->vertCount = 0;
        p->flags = params->polyFlags[i];
        p->area = params->polyAreas[i];
        for (int j = 0; j < nvp; ++j)
        {
            if (src[j] == kMeshNullIdx)
                break;
            p->verts[j] = src[j];
            if (src[nvp + j] & 0x8000)
            {
                // Border or portal edge.
                unsigned short dir = src[nvp + j] & 0xf;
                if (dir == 0xf) // Border
                    p->neis[j] = 0;
                else if (dir == 0) // Portal x-
                    p->neis[j] = kNavMeshExtLink | 4;
                else if (dir == 1) // Portal z+
                    p->neis[j] = kNavMeshExtLink | 2;
                else if (dir == 2) // Portal x+
                    p->neis[j] = kNavMeshExtLink | 0;
                else if (dir == 3) // Portal z-
                    p->neis[j] = kNavMeshExtLink | 6;
            }
            else
            {
                // Normal connection
                p->neis[j] = src[nvp + j] + 1;
            }

            p->vertCount++;
        }
        src += nvp * 2;
    }


    // Store detail meshes and vertices.
    // The nav polygon vertices are stored as the first vertices on each mesh.
    // We compress the mesh data by skipping them and using the navmesh coordinates.
    if (params->detailMeshes)
    {
        unsigned short vbase = 0;
        for (int i = 0; i < params->polyCount; ++i)
        {
            NavMeshPolyDetail& dtl = navDMeshes[i];
            const int vb = (int)params->detailMeshes[i * 4 + 0];
            const int ndv = (int)params->detailMeshes[i * 4 + 1];
            const int nv = navPolys[i].vertCount;
            dtl.vertBase = (unsigned int)vbase;
            dtl.triBase = (unsigned int)params->detailMeshes[i * 4 + 2];
            dtl.vertCount = (NavMeshPolyDetailIndex)(ndv - nv);
            dtl.triCount = (NavMeshPolyDetailIndex)params->detailMeshes[i * 4 + 3];
            // Copy vertices except the first 'nv' verts which are equal to nav poly verts.
            if (ndv - nv)
            {
                memcpy(&navDVerts[vbase * 3], &params->detailVerts[(vb + nv) * 3], vertexSize * (ndv - nv));
                vbase += (unsigned short)(ndv - nv);
            }
        }
        // Store triangles.
        memcpy(navDTris, params->detailTris, sizeof(NavMeshPolyDetailIndex) * 4 * params->detailTriCount);
    }
    else
    {
        // Create dummy detail mesh by triangulating polys.
        int tbase = 0;
        for (int i = 0; i < params->polyCount; ++i)
        {
            NavMeshPolyDetail& dtl = navDMeshes[i];
            const int nv = navPolys[i].vertCount;
            dtl.vertBase = 0;
            dtl.vertCount = 0;
            dtl.triBase = (unsigned int)tbase;
            dtl.triCount = (NavMeshPolyDetailIndex)(nv - 2);
            // Triangulate polygon (local indices).
            for (int j = 2; j < nv; ++j)
            {
                NavMeshPolyDetailIndex* t = &navDTris[tbase * 4];
                t[0] = 0;
                t[1] = (NavMeshPolyDetailIndex)(j - 1);
                t[2] = (NavMeshPolyDetailIndex)j;
                // Bit for each edge that belongs to poly boundary.
                t[3] = (1 << 2);
                if (j == 2)
                    t[3] |= (1 << 0);
                if (j == nv - 1)
                    t[3] |= (1 << 4);
                tbase++;
            }
        }
    }

    // Store and create BVtree.
    if (params->buildBvTree)
    {
        dynamic_array<unsigned short> heightMinMax(kMemTempAlloc);
        heightMinMax.resize_uninitialized(2 * params->polyCount);

        CalculatePolygonHeightMinMax(heightMinMax.data(), nvp, params, navDMeshes, navDVerts);
        CreateBVTreeWithHeightMinMax(params->verts, params->vertCount, params->polys, params->polyCount,
            nvp, params->cs, params->ch, params->polyCount * 2, heightMinMax.data(), navBvtree);
    }

    return true;
}
