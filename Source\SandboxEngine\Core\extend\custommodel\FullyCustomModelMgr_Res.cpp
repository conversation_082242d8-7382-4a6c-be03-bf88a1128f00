
#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"

#include "File/FileManager.h"
#include "CustomModelMgr.h"
#include "RoomSyncResMgr.h"
#include "WorldManager.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"

#include "ActorManagerInterface.h"
#include "DefManagerProxy.h"
#include "ClientInfoProxy.h"
#include "WorldStringManagerProxy.h"
#include <time.h>
#include "Core/blocks/container_world.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

void FullyCustomModelMgr::addDownload(std::string skey)
{
	for (auto iter = m_WaitDownloadListBySyncHostDeque.begin(); iter != m_WaitDownloadListBySyncHostDeque.end(); iter++)
	{
		if (iter->skey == skey)
			return;
	}

	FCM_Download_Info info;
	info.skey = skey;
	info.state = FCM_DOWNLOAD_WAIT;

	m_WaitDownloadListBySyncHostDeque.push_back(info);

	if (m_WaitDownloadListBySyncHostDeque.size() == 1)
	{
		preReqDownload();
	}
}

void FullyCustomModelMgr::preReqDownload()
{
	auto iter = m_WaitDownloadListBySyncHostDeque.begin();
	if (iter != m_WaitDownloadListBySyncHostDeque.end())
	{
		iter->trytimes--;
		if (iter->downloadurl.empty())
		{
			iter->state = FCM_DOWNLOAD_REQ_URL;
			reqDownloadUrl(iter->skey);
		}
		else
		{
			iter->state = FCM_DOWNLOAD_ING;
			reqDownloadFile(iter->downloadurl, FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC, 0, iter->skey);
		}
	}
}

void FullyCustomModelMgr::reqDownloadUrl(std::string skey)
{
	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	RoomSyncResMgr::getSingletonPtr()->reqDownloadUrlToHost(RoomResType::FCM, skey);
}

void FullyCustomModelMgr::respDownloadUrl(std::string skey, std::string url)
{
	if (!url.empty())
	{
		if (url == "no_exist")
		{
			if (!m_WaitDownloadListBySyncHostDeque.empty())
			{
				m_WaitDownloadListBySyncHostDeque.pop_front();
				preReqDownload();
				LOG_INFO("respDownloadUrl host no_exist");
			}
		}
		else
		{
			reqDownloadFile(url, FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC, 0, skey);
		}
	}
	else
	{
		if (!m_WaitDownloadListBySyncHostDeque.empty())
		{
			auto info = m_WaitDownloadListBySyncHostDeque.front();
			if (info.trytimes > 0)
				m_WaitDownloadListBySyncHostDeque.push_back(info);
			m_WaitDownloadListBySyncHostDeque.pop_front();

			preReqDownload();
			LOG_INFO("respDownloadUrl host upload fail");
		}
	}
}

void FullyCustomModelMgr::reqDownloadFile(std::string url, int type, int version/* =0 */, std::string externdata/* ="" */)
{
	LOG_INFO("reqDownloadFile");
	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	std::string filename = "";
	int index = url.find("time/");
	if (index != string::npos)
		filename = url.substr(index + 5);
	else
	{
		char sTime[32] = { 0 };
#if defined(_WIN32)
		sprintf(sTime, "%I64d", time(NULL));
#else
		sprintf(sTime, "%ld", time(NULL));
#endif
		filename = sTime;
	}

	if (type == FCM_CLIENT_DOWNLOAD_BY_CLIENT_EDIT)
	{
		m_DownloadEditModelInfo.filename = filename;
		std::string filepath = "data/http/custommodels/" + filename + ".fcm";
		m_DownloadEditModelInfo.version = version;
		if (GetFileManager().IsFileExistWritePath(filepath.c_str()))
		{
			respDownloadFile(0, "", type);
		}
		else
		{
			//GetGameEventQue().postPreOpenEditFCMUI(PRE_OPEN_DOWNLOADING);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("state", PRE_OPEN_DOWNLOADING);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PRE_OPEN_EDIT_FCM_UI", sandboxContext);
			RoomSyncResMgr::getSingletonPtr()->reqDownloadFile(url, filepath.c_str(), type, "", [this](int result, std::string skey, int type) {
				respDownloadFile(result, skey, type);
				});
		}

	}
	else if (type == FCM_HOST_DOWNLOAD_BY_EDIT_FINISH)
	{
		auto iter = m_WaitDownloadListByClientEdit.find(url);
		if (iter == m_WaitDownloadListByClientEdit.end())
		{
			FCM_Download_Info info;
			info.filename = filename;
			std::string filepath = "data/http/custommodels/" + filename + ".fcm";
			info.version = version;
			info.externdata = externdata;

			m_WaitDownloadListByClientEdit[url] = info;

			if (GetFileManager().IsFileExistWritePath(filepath.c_str()))
			{
				respDownloadFile(0, url, type);
			}
			else
			{
				RoomSyncResMgr::getSingletonPtr()->reqDownloadFile(url, filepath.c_str(), type, url, [this](int result, std::string skey, int type) {
					respDownloadFile(result, skey, type);
					});
			}
		}
	}
	else if (type == FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC)
	{
		for (auto iter = m_WaitDownloadListBySyncHostDeque.begin(); iter != m_WaitDownloadListBySyncHostDeque.end(); iter++)
		{
			if (iter->skey == externdata)
			{
				iter->filename = iter->skey;//直接使用filename 在1秒内2次执行可能会出现文件名重名导致客户刷新问题qinpeng
				iter->downloadurl = url;
				iter->state = FCM_DOWNLOAD_ING;
				iter->version = version;
				iter->externdata = externdata;

				std::string filepath = "data/http/custommodels/" + iter->skey + ".fcm";

				if (GetFileManager().IsFileExistWritePath(filepath.c_str()))
				{
					respDownloadFile(0, externdata, type);
				}
				else
				{
					WorldManager* wm = GetWorldManagerPtr();

					RoomSyncResMgr::getSingletonPtr()->reqDownloadFile(url, filepath.c_str(), type, externdata, [this](int result, std::string skey, int type) {
						respDownloadFile(result, skey, type);
						});
				}
				return;
			}

		}
	}
}

void FullyCustomModelMgr::respDownloadFile(int result, std::string skey, int type)
{
	if (type == FCM_CLIENT_DOWNLOAD_BY_CLIENT_EDIT)
	{
		if (result == 0)
		{
			std::string filepath = "data/http/custommodels/" + m_DownloadEditModelInfo.filename + ".fcm";
			FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
			if (fullycustommodel->load(filepath, m_DownloadEditModelInfo.filename, FCM_LOAD_IGNORE_CHECK_UIN))
			{
				fullycustommodel->setVersion(m_DownloadEditModelInfo.version);
				std::string skey = fullycustommodel->getKey();
				removeOldFullyCustomModel(MAP_MODEL_CLASS, skey);
				m_vMapFcms.push_back(fullycustommodel);

				//资源下载完全，打开界面开始编辑
				setCurEditModel(fullycustommodel->getKey());
				//GetGameEventQue().postOpenEditFullyCustomModel(fullycustommodel->isEdited());
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Bool("edited", fullycustommodel->isEdited());
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_EDIT_FULLYCUSTOMMODEL", sandboxContext);
				}
			}
			else
				ENG_DELETE(fullycustommodel);
		}
		else
		{
			//GetGameEventQue().postPreOpenEditFCMUI(PRE_OPEN_DOWNLOAD_FAIL);
		   MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("state", PRE_OPEN_DOWNLOADING);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("PRE_OPEN_DOWNLOAD_FAIL", sandboxContext);
			//LOG_INFO("respDownloadFile FCM_CLIENT_DOWNLOAD_BY_CLIENT_EDIT fail");
			//WarningStringMsg("respDownloadFile(): result:%d", result);
			//WarningStringMsg("respDownloadFile(): skey:%s", skey.c_str());
		}
	}
	else if (type == FCM_HOST_DOWNLOAD_BY_EDIT_FINISH)
	{
		int uin = -1;
		int operateType = -1;
		std::string name = "";
		std::string desc = "";
		std::string makekey = "";

		auto iter = m_WaitDownloadListByClientEdit.find(skey);
		jsonxx::Object externdataObj;
		if (iter != m_WaitDownloadListByClientEdit.end())
		{
			if (externdataObj.parse(iter->second.externdata))
			{
				if (externdataObj.has<jsonxx::Number>("uin"))
					uin = (int)externdataObj.get<jsonxx::Number>("uin");


				if (externdataObj.has<jsonxx::Number>("operatetype"))
					operateType = (int)externdataObj.get<jsonxx::Number>("operatetype");

				if (externdataObj.has<jsonxx::String>("name"))
					name = externdataObj.get<jsonxx::String>("name");

				if (externdataObj.has<jsonxx::String>("desc"))
					desc = externdataObj.get<jsonxx::String>("desc");

				if (externdataObj.has<jsonxx::String>("makekey"))
					makekey = externdataObj.get<jsonxx::String>("makekey");
			}
		}

		if (result == 0)
		{
			std::string filepath = "data/http/custommodels/" + iter->second.filename + ".fcm";
			FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
			if (fullycustommodel->load(filepath, iter->second.filename, GetClientInfoProxy()->getUin()))
			{
				fullycustommodel->setVersion(iter->second.version);
				std::string skey = fullycustommodel->getKey();
				auto* resInfo = FullyCustomModelMgr::GetInstancePtr()->getDownloadResInfo(skey);
				if (resInfo)
				{
					resInfo->downloadurl = iter->first;
					resInfo->version = iter->second.version;
				}


				removeOldFullyCustomModel(MAP_MODEL_CLASS, skey);
				m_vMapFcms.push_back(fullycustommodel);

				std::string topath = "data/http/custommodels/" + skey + ".fcm";
				GetFileManager().RenameWritePathFile(filepath.c_str(), topath.c_str());

				std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

				char destpath[256] = { 0 };
				sprintf(destpath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, skey.c_str());
				GetFileManager().CopyWritePathFileToWritePath(topath.c_str(), destpath);


				if (iter != m_WaitDownloadListByClientEdit.end())
				{
					m_WaitDownloadListByClientEdit.erase(iter);
					IClientPlayer* player = GetWorldManagerPtr()->getPlayerByUin(uin);
					if (operateType >= CLOSE_EDIT_FCM_UI_TYPE::SAVE_AND_CREATE_BLOCK && player && GetIPlayerControl())
					{

						char createpath[256] = { 0 };
						char makeKey[64];
						if (makekey.empty())
#if defined(_WIN32)
							sprintf(makeKey, "%d%I64d", GetIPlayerControl()->GetIUin(), time(NULL));
#else
							sprintf(makeKey, "%d%ld", GetIPlayerControl()->GetIUin(), time(NULL));
#endif
						else
							sprintf(makeKey, "%s", makekey.c_str());

						sprintf(createpath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, makeKey);
						GetFileManager().CopyWritePathFileToWritePath(destpath, createpath);

						FullyCustomModel* makeModel = ENG_NEW(FullyCustomModel)();
						if (makeModel->load(createpath, makeKey, GetClientInfoProxy()->getUin(), NULL, false, NULL, m_nSpecialType))
						{
							makeModel->save(createpath, makeKey, GetIPlayerControl()->GetIUin(), GetIPlayerControl()->GetPlayerControlNickname(), makeModel->getName(), makeModel->getDesc(), operateType);
							m_vMapFcms.push_back(makeModel);
							createObjectByCurEditModel(player, player->getCurOpenedContainerPos(), operateType, makeKey, name, desc);
						}
						else
						{
							ENG_DELETE(makeModel);
						}
					}


					if (!player)
						return;

					syncCloseUI2Client(uin, SAVE_CLOSE_FCM_UI_SUCCESS, player->getCurOpenedContainerPos(), player->GetPlayerWorld()->getCurMapID(), skey);
					WorldContainer* container = player->getCurOpenedContainer();
					if (container) {
						container->updateFullyCustomModelData(skey);
						player->cleanupOpenedContainer();
					}
				}
				else
					syncCloseUI2Client(uin, SAVE_CLOSE_FCM_UI_DOWNLOAD_MISS);
			}
			else
			{
				ENG_DELETE(fullycustommodel);
				syncCloseUI2Client(uin, SAVE_CLOSE_FCM_UI_HOST_LOAD_FAIL);
			}

		}
		else
		{
			syncCloseUI2Client(uin, SAVE_CLOSE_FCM_UI_DOWNLOAD_FAIL);
		}
	}
	else if (type == FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC)
	{
		if (m_WaitDownloadListBySyncHostDeque.empty())
		{
			LOG_INFO("respDownloadFile m_WaitDownloadListBySyncHostDeque is empty");
			return;
		}

		if (result == 0)
		{
			auto info = m_WaitDownloadListBySyncHostDeque.front();

			if (info.skey == skey)
			{
				std::string filepath = "data/http/custommodels/" + info.filename + ".fcm";
				FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
				if (fullycustommodel && fullycustommodel->load(filepath, info.filename, FCM_LOAD_IGNORE_CHECK_UIN))
				{
					std::string skey = fullycustommodel->getKey();
					removeOldFullyCustomModel(MAP_MODEL_CLASS, skey);
					m_vMapFcms.push_back(fullycustommodel);
					if (GetWorldManagerPtr() && GetIPlayerControl())
					{
						//刷新生物
						World* pworld = GetWorldManagerPtr()->getWorld(GetIPlayerControl()->GetPlayerControlCurMapID());
						if (pworld)
							pworld->getActorMgr()->resetActorsByFullyCustomModel(skey);

						//刷新手上模型
						GetWorldManagerPtr()->resetAllPlayerHandModel();
						if (GetIPlayerControl())
							GetIPlayerControl()->resetHandModel();

						//刷新方块
						if (pworld)
							pworld->getContainerMgr()->resetContainerByFullyCustomModel(skey);

						//刷新快捷栏图标
						InComplete_Icon iconinfo;
						iconinfo.missmodelnum = 99999;
						iconinfo.tryrefreshtime = 100;
						m_IncompleteIcons[skey] = iconinfo;

						SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("ModelDownloadFinish",
							SandboxContext(nullptr).SetData_String("modelfile", skey).SetData_Number("modeltype", fullycustommodel->getModelType()));
					}
				}
				else
				{
					GetFileManager().DeleteWritePathFileOrDir(filepath.c_str()); //文件有问题就删除这个文件 否则不会再次下载这个文件 codeby qinpeng
				}
				m_WaitDownloadListBySyncHostDeque.pop_front();
				preReqDownload();
				return;
			}
		}
		else
		{
			auto info = m_WaitDownloadListBySyncHostDeque.front();
			if (info.trytimes > 0)
				m_WaitDownloadListBySyncHostDeque.push_back(info);
			m_WaitDownloadListBySyncHostDeque.pop_front();

			preReqDownload();
			LOG_INFO("respDownloadFile FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC fail");
		}
	}
}

#ifdef UGC_PROFILE_ENABLED
float FullyCustomModelMgr::getMemStatForUGC()
{
	size_t resSize = 0;

	for (size_t i = 0; i < m_vMapFcms.size(); i++)
	{
		if (!m_vMapFcms[i])
		{
			continue;
		}

		resSize += m_vMapFcms[i]->getMemStatForUGC();
	}

	for (size_t i = 0; i < m_vResFcms.size(); i++)
	{
		if (!m_vResFcms[i])
		{
			continue;
		}

		resSize += m_vResFcms[i]->getMemStatForUGC();
	}

	for (size_t i = 0; i < m_vEquipResFcms.size(); i++)
	{
		if (!m_vEquipResFcms[i])
		{
			continue;
		}

		resSize += m_vEquipResFcms[i]->getMemStatForUGC();
	}

	for (size_t i = 0; i < m_vPreviewResFcms.size(); i++)
	{
		if (!m_vPreviewResFcms[i])
		{
			continue;
		}

		resSize += m_vPreviewResFcms[i]->getMemStatForUGC();
	}

	for (size_t i = 0; i < m_vEditorFcms.size(); i++)
	{
		if (!m_vEditorFcms[i])
		{
			continue;
		}

		resSize += m_vEditorFcms[i]->getMemStatForUGC();
	}

	return resSize / 1024.0f / 1024.0f;
}
#endif // UGC_PROFILE_ENABLED

void FullyCustomModelMgr::reqUploadFullyCustomModel(std::string skey, std::string filename, int uin, int type, std::string data/* ="" */)
{
	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	char path[256] = { 0 };
	std::string externdata = "";
	if (type == FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH)
	{
		FCM_Res_Info info;
		info.downloadurl = "";
		info.version = 0;
		info.waitUrlUins.push_back(uin);
		info.type = type;
		m_FCMResInfos[skey] = info;

		externdata = data;

		sprintf(path, "data/http/custommodels/%s.fcm", filename.c_str());
	}
	else
	{
		auto iter = m_FCMResInfos.find(skey);
		if (iter != m_FCMResInfos.end())
		{
			iter->second.waitUrlUins.push_back(uin);
			if (!iter->second.downloadurl.empty())
				respUploadFullyCustomModel(0, iter->second.downloadurl, skey, "");
			return;
		}

		if (m_CurOWID <= 0)
		{
			if (type == FCM_HOST_UPLOAD_BY_CLIENT_SYNC) {
				//上传失败，通知客机继续尝试
				//vector<int> list({ uin });
				RoomSyncResMgr::getSingletonPtr()->respDownloadUrlToClient(&uin, 1, RoomResType::FCM, skey, "");
			}
			return;
		}


		std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

		sprintf(path, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, filename.c_str());
		if (!GetFileManager().IsFileExistWritePath(path))
		{
			std::string modRoot("");
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_getModRootByCustomRes", SandboxContext(NULL).SetData_String("skey", skey));
			if (result.IsSuccessed())
			{
				modRoot = result.GetData_String();
			}
			if (modRoot.empty())
			{
				LOG_INFO("@ respUploadFullyCustomModel %s skey no exist mod", skey.c_str());

				if (type == FCM_HOST_UPLOAD_BY_CLIENT_SYNC) {
					//上传失败，通知客机继续尝试
					RoomSyncResMgr::getSingletonPtr()->respDownloadUrlToClient(&uin, 1, RoomResType::FCM, skey, "no_exist");
				}
				return;
			}

			sprintf(path, "%s/resource/custommodel/fully/%s.fcm", modRoot.c_str(), filename.c_str());
		}

		FCM_Res_Info info;
		info.downloadurl = "";
		info.version = 0;
		info.waitUrlUins.push_back(uin);
		info.type = type;
		m_FCMResInfos[skey] = info;
	}


	RoomSyncResMgr::getSingletonPtr()->uploadRoomRes(UploadResFileInfo::RoomFullyCustomModel, path, skey, externdata, [this](int result, std::string downloadurl, std::string skey, std::string data) {
		respUploadFullyCustomModel(result, downloadurl, skey, data);
		});
}

void FullyCustomModelMgr::respUploadFullyCustomModel(int result, std::string downloadurl, std::string skey, std::string externdata)
{
	LOG_INFO("@ respUploadFullyCustomModel result %d downloadurl: %s skey: %s, externdata:%s", result, downloadurl.c_str(), skey.c_str(), externdata.c_str());

	auto iter = m_FCMResInfos.find(skey);

	if (iter != m_FCMResInfos.end())
	{
		iter->second.downloadurl = downloadurl;

		int type = iter->second.type;
		int curVersion = 0;

		LOG_INFO("@respUploadFullyCustomModel type:%d", type);
		if (type == FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH)
		{
			if (result == 0)
			{
				auto fullyCustomModel = findFullyCustomModel(MAP_MODEL_CLASS, iter->first);
				if (fullyCustomModel)
				{
					//fullyCustomModel->addVersion(1);
					curVersion = fullyCustomModel->getVersion() + 1;

					char path[256] = { 0 };
					sprintf(path, "data/http/custommodels/%s.fcm", fullyCustomModel->getFileName().c_str());
					GetFileManager().DeleteWritePathFileOrDir(path);
				}

				//TODO通知主机下载
				std::string skey = iter->first;
				std::string url = iter->second.downloadurl;
				int operateType = -1;
				std::string name = "";
				std::string desc = "";
				std::string makekey = "";
				jsonxx::Object externdataObj;
				if (externdataObj.parse(externdata))
				{
					if (externdataObj.has<jsonxx::Number>("operatetype"))
						operateType = (int)externdataObj.get<jsonxx::Number>("operatetype");

					if (externdataObj.has<jsonxx::String>("name"))
						name = externdataObj.get<jsonxx::String>("name");

					if (externdataObj.has<jsonxx::String>("desc"))
						desc = externdataObj.get<jsonxx::String>("desc");

					if (externdataObj.has<jsonxx::String>("makekey"))
						makekey = externdataObj.get<jsonxx::String>("makekey");
				}

				syncCloseUI2Host(operateType, url, curVersion, makekey, name, desc);
			}
			else
			{
				//GetGameEventQue().postCloseEditFullyCustomModel(SAVE_CLOSE_FCM_UI_UNLOAD_FAIL);
				MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("result", SAVE_CLOSE_FCM_UI_UNLOAD_FAIL);
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
					MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CLOSE_EDIT_FULLYCUSTOMMODEL", sandboxContext);
			}
				

		}
		else if (type == FCM_HOST_UPLOAD_BY_CLIENT_SYNC)
		{
			if (result == 0)
			{
				LOG_INFO("@respUploadFullyCustomModel result: 0");
				if (!RoomSyncResMgr::getSingletonPtr())
					return;
				for (size_t i = 0; i < iter->second.waitUrlUins.size(); i++)
				{
					LOG_INFO("@respUploadFullyCustomModel waitUrlUin: %d", iter->second.waitUrlUins[i]);
				}

				//把下载地址同步给客机，客机去下载资源
				RoomSyncResMgr::getSingletonPtr()->respDownloadUrlToClient(&iter->second.waitUrlUins[0], iter->second.waitUrlUins.size(), RoomResType::FCM, iter->first, downloadurl);
			}
			else
			{
				LOG_INFO("@respUploadFullyCustomModel result: %d", result);
				//上传失败，通知客机继续尝试
				RoomSyncResMgr::getSingletonPtr()->respDownloadUrlToClient(&iter->second.waitUrlUins[0], iter->second.waitUrlUins.size(), RoomResType::FCM, iter->first, "");
			}

			iter->second.waitUrlUins.clear();
		}
		else if (type == FCM_HOST_UPLOAD_BY_CLIENT_EDIT)
		{
			for (int i = 0; i < (int)iter->second.waitUrlUins.size(); i++)
			{
				if (!GetWorldManagerPtr())
					break;

				IClientPlayer* player = GetWorldManagerPtr()->getPlayerByUin(iter->second.waitUrlUins[i]);
				if (!player)
					continue;

				auto fullyCustomModel = findFullyCustomModel(MAP_MODEL_CLASS, iter->first);
				if (!fullyCustomModel)
					continue;

				curVersion = fullyCustomModel->getVersion();
				iter->second.version = curVersion;

				player->syncOpenFCMUIToClient(player->getCurOpenedContainerPos(), fullyCustomModel->isEdited(), downloadurl, curVersion);

				if (result != 0)  //上传失败 清理容器相关
				{
					player->cleanupOpenedContainer();
				}
			}

			iter->second.waitUrlUins.clear();
		}


		if (type == FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH || result != 0)   //上传不成功时也要把信息清除掉
		{
			m_FCMResInfos.erase(iter);
			return;
		}
		else
		{
		}
	}
	else
	{
		LOG_INFO("@respUploadFullyCustomModel iter end");
	}
}

FCM_Res_Info* FullyCustomModelMgr::getDownloadResInfo(std::string skey)
{
	auto iter = m_FCMResInfos.find(skey);
	if (iter != m_FCMResInfos.end() && !iter->second.downloadurl.empty())
	{
		return &iter->second;
	}

	return NULL;
}

bool FullyCustomModelMgr::moveFcmRes(int destlibtype, std::string filename, FullyCustomModel* fcm, ResourceFolderSetInfo* destclass)
{
	if (!fcm || !CustomModelMgr::GetInstancePtr() || (destlibtype == MAP_LIB && m_CurOWID <= 0))
		return false;

	auto* existFcm = findFullyCustomModel((int)destlibtype, filename, fcm->isPackingFCM());
	if (!existFcm)
	{
		char srcPath[128] = { 0 };
		char destPath[128] = { 0 };
		if (destlibtype == MAP_LIB)
			getResPath(PUBLIC_LIB, filename, fcm->isPackingFCM(), srcPath);
		else if (destlibtype == PUBLIC_LIB)
			getResPath(MAP_LIB, filename, fcm->isPackingFCM(), srcPath);

		getResPath(destlibtype, filename, fcm->isPackingFCM(), destPath);

		if (GetFileManager().IsFileExistWritePath(destPath))
			return true;

		if (!GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath))
			return false;

		bool loadSuccess = false;
		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(destPath, filename, GetClientInfoProxy()->getUin()))
		{
			bool ignorecheck = false;
			if (ResourceCenter::GetInstancePtr())
				ignorecheck = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename) != NULL;  //下载的完全自定义模型、微缩组合，其子部件加载忽略检�?
			loadSuccess = fullycustommodel->moveSubModelRes(destlibtype, m_CurOWID, NULL, ignorecheck);

		}

		if (loadSuccess)
		{
			if (fcm->isPackingFCM())
			{
				if (m_pPackingFCMMgr)
					m_pPackingFCMMgr->addPackingFcm(destlibtype, fullycustommodel);
			}
			else
			{
				if (destlibtype == MAP_LIB)
					m_vMapFcms.push_back(fullycustommodel);
				else if (destlibtype == PUBLIC_LIB)
					m_vResFcms.push_back(fullycustommodel);
			}

		}
		else
		{
			ENG_DELETE(fullycustommodel);
			return false;
		}
	}

	if (destlibtype == MAP_LIB)
	{
		bool needAddDef = true;

		//地图库里已经有这�?模型数据
		if (existFcm)
		{
			/**************************************************************************************************************/
			//	这里处理一下地图资源删除的状态，原因�?地图资源删除时因为地图本�?很�?�地方已经用了这�?资源，所以并不会立即删除�?
			//	而是放到一�?列表里和记录一�?删除状态，等退出地图的时候才删除	

			existFcm->setLeaveWorldDel(false);			//删除状态置�?
			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(filename, true);
			if (customItem)   //地图里有这个完全�?定义模型生成的道具（ItemDef、MonsterDef等）
			{
				//检测一�? 如果已经在删除列表里，从列表清�??
				CustomModelMgr::GetInstancePtr()->checkRemoveWaitDelCustomItem(customItem->itemid);
				needAddDef = false;
			}
			/**************************************************************************************************************/
		}

		//增加相�?�应的ItemDef或者MonsterDef,以便于在地图内使�?�?(ps后续考虑将这块的逻辑�?到拿资源到快捷栏时再添加 而不�?资源移动到地图库就添�?)
		if (needAddDef)
		{
			int type = fcm->getModelType();
			if (type == -1) type = FULLY_ACTOR_MODEL;

			int id = getFreeId(type);
			int involvedId = 0;
			if (type == FULLY_ACTOR_MODEL)
				involvedId = getFreeId(FULLY_ITEM_MODEL);

			if ((id <= 0 || (type == FULLY_ACTOR_MODEL && involvedId <= 0)))	//没有�?分配的id�?
			{
				if (GetIPlayerControl())
					GetIPlayerControl()->iNotifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
				return false;
			}

			CustomModelMgr::GetInstancePtr()->addCustomItemData(id, filename, destclass->classname, type, involvedId, destclass->classindex);
			GetDefManagerProxy()->addDefByCustomModel(id, type, filename, fcm->getModelName(), fcm->getModelDesc(), Rainbow::Vector3f(0, 0, 0), involvedId);

			//文字安全检测上�?
			std::string content = fcm->getModelName() + ";" + fcm->getModelDesc();
			if (fcm->isPackingFCM())
			{
				//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
				//	.SetData_Number("type", 7)
				//	.SetData_String("content", content)
				//	.SetData_String("key", filename));
				GetWorldStringManagerProxy()->insert(filename, content, SAVEFILETYPE::PACKING_FCM);
			}
			else
			{
				//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
				//	.SetData_Number("type", 6)
				//	.SetData_String("content", content)
				//	.SetData_String("key", filename));
				GetWorldStringManagerProxy()->insert(filename, content, SAVEFILETYPE::FULLY_CUSTOM_MODEL);
			}
		}
	}

	return true;
}

bool FullyCustomModelMgr::removeResByResourceCenter(int libtype, std::string skey)
{
	if (libtype == MAP_LIB)
	{
		auto iter = m_vMapFcms.begin();
		for (; iter != m_vMapFcms.end(); iter++)
		{
			if ((*iter)->getKey() == skey)
			{
				(*iter)->setLeaveWorldDel(true);
				if (CustomModelMgr::GetInstancePtr())
					CustomModelMgr::GetInstancePtr()->addWaitDelCustomItem(skey);
				return true;
			}
		}
	}
	else if (libtype == PUBLIC_LIB)
	{
		auto iter = m_vResFcms.begin();
		for (; iter != m_vResFcms.end(); iter++)
		{
			if ((*iter)->getKey() != skey)
				continue;

			char path[256];
			sprintf(path, "data/custommodel/fully/%s.fcm", skey.c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
			ENG_DELETE(*iter);
			m_vResFcms.erase(iter);
			return true;
		}
	}

	if (m_pPackingFCMMgr)
		return m_pPackingFCMMgr->removeResByResourceCenter(libtype, skey);

	return false;
}