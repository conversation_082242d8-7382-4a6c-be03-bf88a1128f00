#ifndef __GeoSolidEnum_h__
#define __GeoSolidEnum_h__ 1

#define BASIC_GEO_SOLID_SCALE (100)

#define COMPOSITE_USE_DOUBLE 1

#define GEO_SOLID_RECYCLE 0

#include <unordered_set>
#include <cmath>

namespace MNSandbox { namespace GeometrySolid {
	//位域：4
	enum class GeoSolidShape : char
	{
		NONE = -1,
		//立方体
		CUBOID,
		//楔型、直三棱柱
		WEDGE,
		//直四棱锥
		PYRAMID,
		//圆柱
		CYLINDER,
		//圆锥
		CONE,
		//球体
		SPHERE,
		//镂空组合体
		COMPOSITE,
		//一个矩形
		RECTANGLE,
		//三角形
		//TRIANGLE,
	};

	//节省枚举作为位域的内存。默认球的任意面都是球面
	//避免新增
	enum class GeoSolidFace : char
	{
		UNKNOWN = -1,
		FRONT,
		BACK,
		LEFT,
		RIGHT,
		TOP,
		BOTTOM,
		SLOPE,
		SIDE,
	};

	enum class InteractMethod : char {
		HOLLOW,
		UNION,
		INTERSECT,
		SIMPLY_UNION,
	};

	#if COMPOSITE_USE_DOUBLE
	using GsDigit = double;
	const static GsDigit kEpsilon = 1e-6;
	const static GsDigit kOnePI = 3.14159265358979323846;
	const static GsDigit kHalfPI = kOnePI * 0.5;
	const static GsDigit kTwoPI = kOnePI * 2;
	#else
	using GsDigit = float;
	const static GsDigit kEpsilon = 1e-6f;
	#endif

	inline double InvSqrt(double p) { return 1.0 / std::sqrt(p); }
}}

#endif//__GeoSolidEnum_h__
