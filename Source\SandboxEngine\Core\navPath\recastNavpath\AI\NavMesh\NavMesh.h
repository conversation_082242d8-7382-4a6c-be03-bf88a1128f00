//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON>@inside.org
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Public/NavMeshTypes.h"
#include "Math/Quaternionf.h"
#include "FreeList.h"
#include "Utilities/dynamic_array.h"
#include "Utilities/vector_map.h"
#include "Math/Vector3f.h"
#include "Geometry/AABB.h"
#include "Jobs/AtomicSafetyHandle.h"
#include "Jobs/JobTypes.h"
#include "Public/NavMeshBuildSettings.h"

// Maximum number of vertices per navigation polygon.
static const int kNavMeshVertsPerPoly = 6;
static const int kMaxNeis = 4;

static const int kNavMeshMagic = 'D' << 24 | 'N' << 16 | 'A' << 8 | 'V'; //'DNAV';
static const int kNavMeshVersion = 16;

static const unsigned short kNavMeshExtLink = 0x8000;
static const unsigned int kNavMeshNullLink = 0xffffffff;

// Flags for addTile
enum NavMeshTileFlags
{
    kTileLeakData = 0x00,   // memory is owned by someone else - navmesh should not free it.
    kTileFreeData = 0x01    // Navmesh owns the tile memory and should free it.
};

enum NavMeshLinkDirectionFlags
{
    kLinkDirectionOneWay = 0,
    kLinkDirectionTwoWay = 1
};

// Flags returned by FindStraightPath().
enum NavMeshStraightPathFlags
{
    kStraightPathStart = 0x01,              // The vertex is the start position.
    kStraightPathEnd = 0x02,                // The vertex is the end position.
    kStraightPathOffMeshConnection = 0x04   // The vertex is start of an off-mesh link.
};

// Flags describing polygon properties.
enum NavMeshPolyTypes
{
    kPolyTypeGround = 0,                    // Regular ground polygons.
    kPolyTypeOffMeshConnection = 1          // Off-mesh connections.
};

// Structure describing the navigation polygon data.
struct NavMeshPoly
{
    unsigned short  verts[kNavMeshVertsPerPoly];// Indices to vertices of the poly.
    unsigned short  neis[kNavMeshVertsPerPoly]; // Refs to neighbours of the poly.
    unsigned int    flags;                      // Flags (see NavMeshPolyFlags).
    unsigned char   vertCount;                  // Number of vertices.
    unsigned char   area;                       // Area type of the polygon
};

// The type for the detail mesh vertices index.
typedef unsigned short NavMeshPolyDetailIndex;

// Structure describing polygon detail triangles.
struct NavMeshPolyDetail
{
    unsigned int vertBase;                      // Offset to detail vertex array.
    unsigned int triBase;                       // Offset to detail triangle array.
    NavMeshPolyDetailIndex vertCount;           // Number of vertices in the detail mesh.
    NavMeshPolyDetailIndex triCount;            // Number of triangles.
};

// Structure describing a link to another polygon.
struct NavMeshLink
{
    NavMeshPolyRef ref;                     // Neighbour reference.
    unsigned int next;                      // Index to next link.
    unsigned char edge;                     // Index to polygon edge which owns this link.
    unsigned char side;                     // If boundary link, defines on which side the link is.
    unsigned char bmin, bmax;               // If boundary link, defines the sub edge area.
};

struct NavMeshBVNode
{
    unsigned short bmin[3], bmax[3];        // BVnode bounds
    int i;                                  // Index to item or if negative, escape index.
};


struct OffMeshConnectionParams
{
    Rainbow::Vector3f startPos;
    Rainbow::Vector3f endPos;
    Rainbow::Vector3f up;
    float width;                            // If width > 0.0f, link is segment-to-segment.
    float costModifier;                     // Modify navmesh cost (multiplier applied to euclidean distance)
    unsigned char linkDirection;            // Link connection direction flags (NavMeshLinkDirectionFlags)
    unsigned int flags;
    unsigned char area;
    unsigned short linkType;                // enum OffMeshLinkType
    int userID;                             // User ID to identify this connection.
    int agentTypeID;                        // Which agent type can use this link
};

struct OffMeshLinkEndPoint
{
    Rainbow::Vector3f pos;                           // Position of the end point (based on input)
    Rainbow::Vector3f mapped[2];                     // Position mapped on navmesh
    NavMeshTileRef tileRef;                 // Reference to tile when the point is connected - otherwise zero
};

struct OffMeshConnection
{
    inline OffMeshConnection() : salt(1) {}
    inline ~OffMeshConnection() {}

    int agentTypeID;                        // Which agent type can use this link
    Rainbow::MinMaxAABB bounds;
    OffMeshLinkEndPoint endPoints[2];       // Connection end points
    Rainbow::Vector3f axisX, axisY, axisZ;
    float width;                            // Width of the link.
    float costModifier;                     // Modify navmesh cost (multiplier applied to euclidean distance)
    unsigned char linkDirection;            // Link connection direction flags (NavMeshLinkDirectionFlags)
    unsigned int flags;                     // Poly flags
    unsigned char area;                     // Area type
    unsigned short linkType;                // enum OffMeshLinkType
    int userID;                             // User ID to identify this connection.
    unsigned int firstLink;                 // Index to first link
    unsigned int salt;                      // Salt of the connection, increased on delete
    unsigned int next;                      // Next offmesh con in the active linked list, or freelist.
};

struct NavMeshDataHeader
{
    int magic;                              // Magic number, used to identify the data.
    int version;                            // Data version number.
    int x, y;                               // Location of the tile on the grid.
    unsigned int agentTypeId;               // User ID of the tile.
    int polyCount;                          // Number of polygons in the tile.
    int vertCount;                          // Number of vertices in the tile.
    int detailMeshCount;                    // Number of detail meshes.
    int detailVertCount;                    // Number of detail vertices.
    int detailTriCount;                     // Number of detail triangles.
    int bvNodeCount;                        // Number of BVtree nodes.
    Rainbow::Vector3f bmin, bmax;                    // Bounding box of the tile.
    float bvQuantFactor;                    // BVtree quantization factor (world to bvnode coords)
};

struct NavMeshTile
{
    NavMeshTile() : salt(1), header(0) {}
    unsigned long long surfaceID;                          // Surface where the tile belongs to.
    unsigned int salt;                      // Counter describing modifications to the tile.
    const NavMeshDataHeader* header;        // Pointer to tile header.
    unsigned int* polyLinks;                // First link index for each polygon.
    NavMeshPoly* polys;                         // Pointer to the polygons (will be updated when tile is added).
    const Rainbow::Vector3f* verts;                      // Pointer to the vertices (will be updated when tile added).
    const NavMeshPolyDetail* detailMeshes;      // Pointer to detail meshes (will be updated when tile added).
    const Rainbow::Vector3f* detailVerts;                // Pointer to detail vertices (will be updated when tile added).
    const NavMeshPolyDetailIndex* detailTris;   // Pointer to detail triangles (will be updated when tile added).
    const NavMeshBVNode* bvTree;                // Pointer to BVtree nodes (will be updated when tile added).
    const unsigned char* data;              // Pointer to tile data.
    int dataSize;                           // Size of the tile data.
    int flags;                              // Tile flags, see NavMeshTileFlags.
    unsigned int next;                      // Next tile in spatial grid, or next in freelist.

    // Transform support - consider sharing between a number of tiles
    Rainbow::Quaternionf rotation;
    Rainbow::Vector3f position;
    int transformed;
};

struct NavMeshProcessCallback
{
    virtual void ProcessPolygons(const NavMeshTile* tile, const NavMeshPolyRef* polyRefs, const NavMeshPoly** polys, const int itemCount) = 0;
};


class NavMesh
{
public:
    NavMesh();
    ~NavMesh();

    // Creates a surface - set of tiles sharing some data
    // Reserves room for tiles belonging for a given surface.
    // Params:
    //  tileCount - (in) number of tiles to reserve memory for.
    //  settings - (in) settings for tiles belonging to this surface.
    //
    int CreateSurface(int reserveTileCount, const NavMeshBuildSettings& settings,
        const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& rot);

    unsigned long long CreateSurfaceById(int x, int z, const NavMeshBuildSettings& settings);

    unsigned long long GetSurfaceId(int x, int z, int agentid);
    // Removes a surface
    // Params:
    //  surfaceID - (in) id of surface to remove.
    void RemoveSurface(unsigned long long surfaceID);

    const NavMeshBuildSettings* GetSurfaceSettings(int surfaceID) const;
    void SetSurfaceSettings(unsigned long long surfaceID, const NavMeshBuildSettings& settings);

    // Adds new tile into the navmesh.
    // The add will fail if the data is in wrong format,
    // there is not enough tiles left, or if there is a tile already at the location.
    // Params:
    //  data - (in) Data of the new tile mesh.
    //  dataSize - (in) Data size of the new tile mesh.
    //  flags - (in) Tile flags, see NavMeshTileFlags.
    //  surfaceID - (in) surface identifier for the tile.
    //  result - (out,optional) tile ref if the tile was successfully added.
    NavMeshStatus AddTile(const unsigned char* data, int dataSize, NavMeshTileFlags flags,
        unsigned long long surfaceID, NavMeshTileRef* result);

    // Removes specified tile.
    // Params:
    //  ref - (in) Reference to the tile to remove.
    //  surfaceID - (in) surface identifier for the tile
    //  data - (out) Data associated with deleted tile.
    //  dataSize - (out) Size of the data associated with deleted tile.
    NavMeshStatus RemoveTile(NavMeshTileRef ref, unsigned long long surfaceID, const unsigned char** data, int* dataSize);

    // Returns tile references of a tile based on tile pointer.
    NavMeshTileRef GetTileRef(const NavMeshTile* tile) const;

    // Returns tile based on references.
    const NavMeshTile* GetTileByRef(NavMeshTileRef ref) const;

    // Returns the number of tiles currently allocated.
    // Use together with GetTile to access all tiles.
    int GetMaxTiles() const
    {
        return m_tiles.Capacity();
    }

    // Returns a pointer to tile in the tile free list.
    // If the tile 'header' pointer is 0, the tile is invalid.
    // Params:
    //  i - (in) Index to the tile to retrieve, max index is GetMaxTiles()-1.
    // Returns: Pointer to specified tile.
    const NavMeshTile* GetTile(int i) const
    {
        return &m_tiles[i];
    }

    // Returns pointer to tile and polygon pointed by the polygon reference.
    // Params:
    //  ref - (in) reference to a polygon.
    //  tile - (out) pointer to the tile containing the polygon.
    //  poly - (out) pointer to the polygon.
    NavMeshStatus GetTileAndPolyByRef(const NavMeshPolyRef ref, const NavMeshTile** tile, const NavMeshPoly** poly) const;
    NavMeshStatus GetTileAndPolyByRef(const NavMeshPolyRef ref, NavMeshTile** tile, const NavMeshPoly** poly);

    // Returns pointer to tile and polygon pointed by the polygon reference.
    // Note: this function does not check if 'ref' s valid, and is thus faster. Use only with valid refs!
    // Params:
    //  ref - (in) reference to a polygon.
    //  tile - (out) pointer to the tile containing the polygon.
    //  poly - (out) pointer to the polygon.
    void GetTileAndPolyByRefUnsafe(const NavMeshPolyRef ref, const NavMeshTile** tile, const NavMeshPoly** poly) const;

    // Returns true if polygon reference points to valid data.
    bool IsValidPolyRef(NavMeshPolyRef ref) const;

    // Returns the agentTypeId that is allowed to use the polygon.
    int GetAgentTypeIdForPolyRef(NavMeshPolyRef ref) const;

    // Returns base poly id for specified tile, polygon refs can be deducted from this.
    NavMeshPolyRef GetPolyRefBase(const NavMeshTile* tile) const;

    unsigned int GetPolyFlags(NavMeshPolyRef ref) const;
    unsigned char GetPolyArea(NavMeshPolyRef ref) const;
    void GetPolyFlagsAndArea(NavMeshPolyRef ref, unsigned int* flags, unsigned char* area) const;

    // Returns vertices and neighbours of polygon pointed to by the polygon reference.
    // Note: this function returns 0 if ref is invalid
    // Params:
    //  ref - (in) reference to a polygon.
    //  verts - (out, optional) pointer to polygon vertices, must have a capacity of at least kNavMeshVertsPerPoly.
    //  neighbours - (out, optional) pointer to the polygon neighbours, must have a capacity of at least kNavMeshVertsPerPoly*maxNeisPerEdge.
    //  maxNeisPerEdge - (int) maximum number of neighbours stored in 'neighbours' per edge.
    int GetPolyGeometry(NavMeshPolyRef ref, Rainbow::Vector3f* verts, NavMeshPolyRef* neighbours, int maxNeisPerEdge) const;

    // Returns polygons which overlap the query box.
    // Params:
    //  typeID - (in) query surfaces with this id. -1 means query all surfaces.
    //  center[3] - (in) the center of the search box.
    //  extents[3] - (in) the extents of the search box.
    //  callback - (in/out) pointer to callback interface to batch-process the results, see NavMeshProcessCallback.
    void QueryPolygons(int typeID, const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents,
        NavMeshProcessCallback* callback) const;

    static inline NavMeshPolyRef EncodeLinkId(unsigned int salt, unsigned int ip)
    {
        const unsigned int tileMax = (1 << kPolyRefTileBits) - 1;
        return EncodePolyId(salt, tileMax, kPolyTypeOffMeshConnection, ip);
    }

    // Encodes a poly id.
    static inline NavMeshPolyRef EncodePolyId(unsigned int salt, unsigned int it, unsigned int type, unsigned int ip)
    {
        return ((NavMeshPolyRef)salt << (kPolyRefPolyBits + kPolyRefTypeBits + kPolyRefTileBits)) |
            ((NavMeshPolyRef)it   << (kPolyRefPolyBits + kPolyRefTypeBits)) |
            ((NavMeshPolyRef)type << (kPolyRefPolyBits)) |
            (NavMeshPolyRef)ip;
    }

    static inline NavMeshPolyRef EncodeBasePolyId(unsigned int type, unsigned int ip)
    {
        return ((NavMeshPolyRef)type << (kPolyRefPolyBits)) | (NavMeshPolyRef)ip;
    }

    // Decodes a poly id.
    static inline void DecodePolyId(unsigned int* salt, unsigned int* it, unsigned int* type, unsigned int* ip, NavMeshPolyRef ref)
    {
        *salt = (unsigned int)((ref >> (kPolyRefPolyBits + kPolyRefTypeBits + kPolyRefTileBits)) & kPolyRefSaltMask);
        *it   = (unsigned int)((ref >> (kPolyRefPolyBits + kPolyRefTypeBits)) & kPolyRefTileMask);
        *type = (unsigned int)((ref >> (kPolyRefPolyBits)) & kPolyRefTypeMask);
        *ip   = (unsigned int)(ref & kPolyRefPolyMask);
    }

    // Decodes a tile salt.
    static inline unsigned int DecodePolyIdSalt(NavMeshPolyRef ref)
    {
        return (unsigned int)((ref >> (kPolyRefPolyBits + kPolyRefTypeBits + kPolyRefTileBits)) & kPolyRefSaltMask);
    }

    // Decodes a tile id.
    static inline unsigned int DecodePolyIdTile(NavMeshPolyRef ref)
    {
        return (unsigned int)((ref >> (kPolyRefPolyBits + kPolyRefTypeBits)) & kPolyRefTileMask);
    }

    // Decodes a type.
    static inline unsigned int DecodePolyIdType(NavMeshPolyRef ref)
    {
        return (unsigned int)((ref >> (kPolyRefPolyBits)) & kPolyRefTypeMask);
    }

    // Decodes a poly id.
    static inline unsigned int DecodePolyIdPoly(NavMeshPolyRef ref)
    {
        return (unsigned int)(ref & kPolyRefPolyMask);
    }

    const NavMeshLink* GetFirstLink(NavMeshPolyRef ref) const;

    inline const NavMeshLink* GetNextLink(const NavMeshLink* link) const
    {
        if (link == NULL) return NULL;
        if (link->next == kNavMeshNullLink) return NULL;
        Assert(link->next < m_links.Capacity());
        return &m_links[link->next];
    }

    inline const NavMeshLink* GetLink(unsigned int i) const
    {
        if (i == kNavMeshNullLink) return NULL;
        Assert(i < m_links.Capacity());
        return &m_links[i];
    }

    inline void BumpTimeStamp()
    {
        m_timeStamp += 1;
        if (m_timeStamp == 0) // use 0 for forcing stale
            m_timeStamp = 1;
    }

    inline unsigned int GetTimeStamp() const
    {
        return m_timeStamp;
    }

    // Returns start and end location of an off-mesh link polygon.
    // Params:
    //  prevRef - (in) ref to the polygon before the link (used to select direction).
    //  polyRef - (in) ref to the off-mesh link polygon.
    //  startPos[3] - (out) start point of the link.
    //  endPos[3] - (out) end point of the link.
    // Returns: true if link is found.
    NavMeshStatus GetOffMeshConnectionEndPoints(NavMeshPolyRef prevRef, NavMeshPolyRef offMeshPolyRef, Rainbow::Vector3f* startPos, Rainbow::Vector3f* endPos) const;

    NavMeshStatus GetNearestOffMeshConnectionEndPoints(NavMeshPolyRef prevRef, NavMeshPolyRef offMeshPolyRef, NavMeshPolyRef nextRef, const Rainbow::Vector3f& currentPos,
        Rainbow::Vector3f* startPos, Rainbow::Vector3f* endPos) const;

    // polyRef - polyRef of the offmeshlink polygon
    // costOverride - use this as cost OffMeshLink cost modifier instead of what is specified by areas.
    NavMeshStatus SetOffMeshConnectionCostModifier(NavMeshPolyRef polyRef, float costOverride);
    NavMeshStatus SetOffMeshConnectionFlags(NavMeshPolyRef polyRef, unsigned int flags);

    NavMeshStatus GetOffMeshConnectionUserID(NavMeshPolyRef polyref, int* userID) const;
    NavMeshStatus SetOffMeshConnectionUserID(NavMeshPolyRef polyref, const int userID);

    const OffMeshConnection* GetOffMeshConnection(const NavMeshPolyRef ref) const;

    NavMeshPolyRef AddOffMeshConnection(const struct OffMeshConnectionParams* params, float connectRadius, float connectHeight);
    NavMeshStatus RemoveOffMeshConnection(const NavMeshPolyRef ref);

    const OffMeshConnection* GetFirstOffMeshConnection() const;
    const OffMeshConnection* GetNextOffMeshConnection(const OffMeshConnection* data) const;

    // Returns polygon edge points based on detail mesh.
    // If 'maxPoints' is less than the number of points on the edge, the edge is simplified.
    // Params:
    //  tile - (in) pointer to tile where the polygon lies.
    //  poly - (in) polygon index
    //  edge - (in) edge index
    //  points - (out) array to store the edge points (maxPoints*3).
    //  pointCount - (out) number of points stored.
    //  maxPoints - (in) max number of points that can fit into points, must be >= 2.
    NavMeshStatus GetPolyEdgeDetailPoints(const NavMeshTile* tile, int poly, int edge,
        Rainbow::Vector3f* points, int* pointCount, const int maxPoints) const;

    // Returns closest point on polygon.
    void ClosestPointOnPolyInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly,
        const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const;

    // Returns closest point on the boundary of the polygon.
    void ClosestPointOnPolyBoundaryInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const;

    NavMeshStatus GetSurfaceTransform(int surfaceID, Rainbow::Vector3f* pos, Rainbow::Quaternionf* rot) const;

    void AddWriteDependency(const Rainbow::JobFence& fence);
    void SyncWriteDependencies();
    void ForgetCompletedDependencies();

private:

    // Returns all polygons in neighbour tile based on portal defined by the segment.
    int FindConnectingPolys(const Rainbow::Vector3f* points, const int pointCount,
        const NavMeshTile* tile, int side,
        NavMeshPolyRef* con, struct PortalArea* conarea, int maxcon, float portalHeight) const;

    // Builds internal polygons links for a tile.
    void ConnectIntLinks(NavMeshTile* tile);

    // Builds external polygon links for a tile.
    void ConnectExtLinks(NavMeshTile* tile, NavMeshTile* target, int side, float portalHeight);

    void RemoveLinkBetween(NavMeshPolyRef from, NavMeshPolyRef to);

    // Removes all internal and external links from a tile.
    void UnconnectLinks(NavMeshTile* tile);

    // Queries polygons within a tile.
    void QueryPolygonsInTile(const NavMeshTile* tile, const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, NavMeshProcessCallback* callback) const;
    // Find nearest polygon within a tile.
    NavMeshPolyRef FindNearestPoly(int typeID, const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, Rainbow::Vector3f* nearestPt) const;

    void FindPolygonsOverlappingSegment(int typeID, const Rainbow::Vector3f& pa, const Rainbow::Vector3f& pb, const float height,
        NavMeshPolyRef* polys, float* overlapMinMax, int* polyCount, const int maxPolys) const;

    OffMeshConnection* GetOffMeshConnectionUnsafe(const NavMeshPolyRef ref);

    void ConnectOffMeshConnection(unsigned int index, float connectRadius, float connectHeight);
    void ConnectOffMeshConnectionsToTile(NavMeshTile* tile);

    void UnconnectOffMeshConnection(unsigned int index);
    void UnconnectOffMeshConnectionsToTile(NavMeshTileRef ref);

    typedef std::pair<int, int> TileLoc;
    // Tile location (i,j) to global tile index
    typedef vector_map<TileLoc, unsigned int> TileLUT;

    struct SurfaceData
    {
        NavMeshBuildSettings m_Settings;
        Rainbow::MinMaxAABB m_WorldBounds;

        Rainbow::Quaternionf m_Rotation;
        Rainbow::Vector3f m_Position;

        TileLUT m_TileLUT;
    };

    typedef vector_map<unsigned long long, SurfaceData> SurfaceIDMap;
    SurfaceIDMap m_SurfaceIDToData;

    FreeList<NavMeshTile> m_tiles;
    FreeList<NavMeshLink> m_links;
    FreeList<OffMeshConnection> m_offMeshConnections;
    unsigned int m_firstOffMeshConnection;
    unsigned int m_timeStamp;

    dynamic_array<Rainbow::JobFence> m_WriteDependencies;

    static unsigned long long s_SurfaceIDGen;
};

static inline unsigned int GetPolyIndex(const NavMeshTile* tile, const NavMeshPoly* poly)
{
    Assert(poly);
    Assert(tile);
    Assert(tile->header);
    const unsigned int ip = (unsigned int)(poly - tile->polys);
    Assert(ip < tile->header->polyCount);
    return ip;
}

inline Rainbow::Vector3f TileToWorld(const NavMeshTile& tile, const Rainbow::Vector3f& position)
{
    if (!tile.transformed)
        return position;
    Rainbow::Matrix4x4f mat;
    mat.SetTR(tile.position, tile.rotation);
    return mat.MultiplyPoint3(position);
}

inline Rainbow::Vector3f TileToWorldVector(const NavMeshTile& tile, const Rainbow::Vector3f& direction)
{
    if (!tile.transformed)
        return direction;
    Rainbow::Matrix4x4f mat;
    mat.SetTR(tile.position, tile.rotation);
    return mat.MultiplyVector3(direction);
}

inline void TileToWorld(Rainbow::Vector3f* worldPositions, const NavMeshTile& tile, int npositions, const Rainbow::Vector3f* positions)
{
    if (tile.transformed)
    {
        Rainbow::Matrix4x4f mat;
        mat.SetTR(tile.position, tile.rotation);
        for (int i = 0; i < npositions; ++i)
            worldPositions[i] = mat.MultiplyPoint3(positions[i]);

        return;
    }

    for (int i = 0; i < npositions; ++i)
        worldPositions[i] = positions[i];
}

inline Rainbow::Vector3f WorldToTile(const NavMeshTile& tile, const Rainbow::Vector3f& position)
{
    if (!tile.transformed)
        return position;
    Rainbow::Matrix4x4f mat;
    mat.SetTRInverse(tile.position, tile.rotation);
    return mat.MultiplyPoint3(position);
}
