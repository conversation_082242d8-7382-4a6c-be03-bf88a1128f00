
#ifndef __OGRE_CAMERAANIM_H__
#define __OGRE_CAMERAANIM_H__

#include "OgreShared.h"
#include "Mesh/LegacyOgreKeyFrameArray.h"

namespace Rainbow
{
	class Camera;
}

namespace MINIW
{

	class _OgreExport CameraAnimation
	{
	public:
		CameraAnimation();
		~CameraAnimation();

		void update();

		void beginCapture(int seqid, Rainbow::Camera *pcamera);
		int endCapture(); //return seq id

		void beginPlay(int seqid, Rainbow::Camera *pcamera);
		void endPlay();
		bool isPlaying();

	private:
		void addFrameData();

	public:
		Rainbow::KeyFrameArray<Rainbow::Vector3f> m_TranslateKeys;
		Rainbow::KeyFrameArray<Rainbow::Quaternionf> m_RotateKeys;
		Rainbow::KeyFrameArray<float>m_FovKeys;

	private:
		Rainbow::Camera *m_CurCamera;
		int m_LastFrameTick;
		unsigned int m_CurTick;
		int m_CaptureSeqID;
		int m_PlaySeqID;

		std::vector<Rainbow::Vector3f> m_CurPosKeys;
		std::vector<Rainbow::Quaternionf> m_CurRotKeys;
		std::vector<float>m_CurFovKeys;
		std::vector<unsigned int>m_CurTimeKeys;
	};
}

#endif