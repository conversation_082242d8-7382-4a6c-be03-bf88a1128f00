/**
* file : SceneEffectRectangle
* func : 场景效果 （矩形）
* by : pengdapu
*/
#include "SceneEffectRectangle.h"
#include "world_types.h"
#include "world.h"
#include "SandboxMacros.h"

using namespace MNSandbox;
using namespace Rainbow;

SceneEffectRectangle::SceneEffectRectangle()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_eSes = SceneEffectShape::RECTANGLE;
}

SceneEffectRectangle::~SceneEffectRectangle()
{
}

void SceneEffectRectangle::OnClear()
{
	for (int i = 0; i < 4; ++i)
	{
		SANDBOX_DELETE(m_aLines[i]);
	}
}

void SceneEffectRectangle::Refresh()
{
	OnClear();
	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* line = m_aLines[i] = SANDBOX_NEW(SceneEffectLine);
		line->SetStroke(m_iStroke);
		line->SetMtlType(m_MtlType);
	}
	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectRectangle::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* line = m_aLines[i];
		if (line) line->OnDraw(pWorld);
	}
}

void SceneEffectRectangle::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	Matrix4x4f matRotate;
	QuaternionfToMatrix(q, matRotate);
	Vector3f aVs[4];
	Vector3f ve = m_EndPos.toVector3() - m_StartPos.toVector3();
	ve *= vs;
	aVs[0].Set(m_StartPos.x, m_StartPos.y, 0);
	aVs[1].Set(m_StartPos.x, m_EndPos.y, 0);
	aVs[2].Set(m_EndPos.x, m_EndPos.y, 0);
	aVs[3].Set(m_EndPos.x, m_StartPos.y, 0);

	for (int i = 0; i < 4; ++i)
	{
		aVs[i] = matRotate.MultiplyPoint3(aVs[i]);
		aVs[i] += vc;
	}

	for (int i = 0; i < 4; ++i)
	{
		if (!m_aLines[i])
		{
			continue;
		}
		m_aLines[i]->SetPos(aVs[i], aVs[(i+1) % 4], false);
		m_aLines[i]->Refresh();
	}
}