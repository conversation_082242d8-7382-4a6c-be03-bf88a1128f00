
#include "FreeFlyCamera.h"
#include "OgrePrerequisites.h"
#include "CameraManager.h"
#include "GameCamera.h"
#include "Common/OgreShared.h"
#include "ClientInfoProxy.h"

using namespace MINIW;
using namespace Rainbow;

FreeFlyCamera::FreeFlyCamera(CameraManager* cameraManager): CameraBase(cameraManager), m_RotX(0), m_RotY(0),
                                                            m_StoryMoveSpeed(0),
                                                            m_StoryRotSpeed(0)
{
	m_MoveSpeed = 200;
	m_RotSpeed = 3.0f;
	lastMousePosX = 0;
	lastMousePosY = 0;
	m_MoveStrafe = 0;
	m_MoveForward = 0;
	m_RotateX = 0;
	m_RotateY = 0;
	isInStoryMode = false;
	m_MoveType = UniformSpeedMove;
}

void FreeFlyCamera::update(float deltaSeconds)
{
	if (isInStoryMode)
	{
		if (m_MoveType == UniformSpeedMove)
		{
			if (DistanceSqr(m_CameraTransform.pos, m_TargetPos) > 50.0f)
			{
				Vector3f tmpPos = m_TargetPos - m_CameraTransform.pos;
				Rainbow::Vector3f deltaPos = deltaSeconds* m_StoryMoveSpeed * tmpPos.NormalizeSafe();
				m_CameraTransform.pos += deltaPos;
			}
		}
		else if (m_MoveType == LerpMove)
		{
			if (DistanceSqr(m_CameraTransform.pos, m_TargetPos) > 50.0f)
			{
				m_CameraTransform.pos = Lerp(m_CameraTransform.pos, m_TargetPos, m_StoryMoveSpeed * deltaSeconds);
			}
		}

		// todo_yanxiongjian
		//if (Quaternionf::Angle(m_CameraTransform.rot, m_TargetRot) > 1)
		//{
		//	//m_CameraTransform.rot = Quaternionf.RotateTowards(m_CameraTransform.rot, m_TargetRot, m_StoryRotSpeed * deltaSeconds);
		//	m_CameraTransform.rot = Quaternionf::Slerp(m_CameraTransform.rot, m_TargetRot, m_StoryRotSpeed * deltaSeconds);
		//	m_RotatePitch = m_CameraTransform.rot.EulerAngle().x;
		//	m_RotateYaw = m_CameraTransform.rot.EulerAngle().y;
		//}

		return;
	}

	//Rainbow::Vector3f deltaPos = (m_MoveForward * Rainbow::Vector3f::zAxis + m_MoveStrafe * Rainbow::Vector3f::yAxis)* m_MoveSpeed * deltaSeconds;
	Rainbow::Vector3f deltaPos = (m_MoveForward * GetForward() + m_MoveStrafe * GetRight())* m_MoveSpeed * deltaSeconds;
	m_CameraTransform.pos += deltaPos;
}

int FreeFlyCamera::onInputEvent(const Rainbow::InputEvent &event)
{
	if (isInStoryMode)
	{
		return Rainbow::INPUTMSG_PASS;
	}

	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);

	//m_RotX = 0;
	//m_RotY = 0;
	// todo ʹ�� event.delta ���� lastMousePosX/Y
	if (event.type == InputEvent::kLeftButton)
	{
		lastMousePosX = event.mousePosition.x;
		lastMousePosY = event.mousePosition.y;

	}
	else if (event.type == InputEvent::kMouseMove)
	{
		const int dx = static_cast<int>(event.mousePosition.x - lastMousePosX);
		const int dy = static_cast<int>(event.mousePosition.y - lastMousePosY);

		m_pCameraManager->m_GameCamera->rotate(float(dx) / w, float(dy) / h);

		lastMousePosX = event.mousePosition.x;
		lastMousePosY = event.mousePosition.y;

		m_RotX = float(dx) * m_RotSpeed / w;
		m_RotY = float(dy) * m_RotSpeed / h;

		m_RotateY += m_RotX * 180.0f;
		if (m_RotateY > 360.0f) m_RotateY -= 360.0f;
		if (m_RotateY < 0) m_RotateY += 360.0f;

		const float anglex = 89.0f;
		m_RotateX += m_RotY * 90.0f;
		if (m_RotateX < -anglex) m_RotateX = -anglex;
		if (m_RotateX > anglex) m_RotateX = anglex;

		m_CameraTransform.rot = AngleEulerToQuaternionf(Rainbow::Vector3f(m_RotateX, m_RotateY, 0));
	}
	else if (event.type == InputEvent::kKeyDown)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveForward = 1.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward = -1.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe = -1.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe = 1.0f;
		}
	}
	else if (event.type == InputEvent::kKeyUp)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveForward = 0.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward = 0.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe = 0.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe = 0.0f;
		}
	}

	return Rainbow::INPUTMSG_PASS;
}

void FreeFlyCamera::onSwitchTo()
{
	//m_CameraTransform.pos = GetIPlayerControl()->GetPlayerControlPosition().toVector3() + Rainbow::Vector3f(0, 300, 0);
	m_CameraTransform.pos = m_pCameraManager->m_GameCamera->getPosition().toVector3();
	m_CameraTransform.rot = m_pCameraManager->m_GameCamera->getRotation();
}

void FreeFlyCamera::translateCamera(Rainbow::Vector3f targetPos, Quaternionf targetRot, float moveSpeed, float rotSpeed)
{
	m_TargetPos = targetPos;
	m_TargetRot = targetRot;
	m_MoveType = UniformSpeedMove;
	m_StoryMoveSpeed = moveSpeed;
	m_StoryRotSpeed = rotSpeed;
}

void FreeFlyCamera::lerpCamera(Rainbow::Vector3f targetPos, Quaternionf targetRot, float moveSpeed, float rotSpeed)
{
	m_TargetPos = targetPos;
	m_TargetRot = targetRot;
	m_StoryMoveSpeed = moveSpeed;
	m_StoryRotSpeed = rotSpeed;
	m_MoveType = LerpMove;
}

