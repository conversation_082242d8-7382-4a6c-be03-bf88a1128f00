#ifndef __WORLD_UI_MANAGER_H__
#define __WORLD_UI_MANAGER_H__
#include "Utilities/Singleton.h"
#include <string>
#include <chrono>
#include <ctime>
#include "world_types.h"

#define BLOCK_BAR_TEST

namespace Rainbow
{
	class ProgressBarIn3D;
	class Image3D;
	class ImageBoard3D;
};

class EXPORT_SANDBOXENGINE WorldUIMgr
{
public:
	WorldUIMgr();
	~WorldUIMgr();

	void showUIBlockHealthBar(World* pworld, const WCoord& blockpos, const WCoord& playerPos, int totalValue, int curValue, int showMode);
	void hideBlockHealthBar();
	void tick();
	void addHarmedBlockToUI(const WCoord& pos);
	int getCurBlockHealth() { return m_nCurBlockHP; };
private:

	//Rainbow::ProgressBarIn3D* m_BlockHPProgressObj = NULL;//血条比例对象
	//Rainbow::ImageBoard3D* m_BlockHPLineImage;			// 勋章图标

	int m_nBlockHealthBarTickCount = 0;
	int m_nCurBlockHP = 0;

#ifdef IWORLD_SERVER_BUILD
	//time_t m_llLastLogicRunTime = 0;
	//std::unordered_map<WCoord, std::time_t, WCoordHashCoder> m_mBlockHealthList;
#else
	std::unordered_map<WCoord, char, WCoordHashCoder> m_mBlockHealthList;
#endif
	//fairygui::GComponent* m_blockHealthBar;
};


#endif//__WORLD_UI_MANAGER_H__