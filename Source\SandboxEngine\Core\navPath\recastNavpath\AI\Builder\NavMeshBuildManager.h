#pragma once
#include "Public/NavMeshBindingTypes.h"
#include "Public/NavMeshBuildSettings.h"
#include "Utilities/dynamic_array.h"
namespace Rainbow {
    struct JobGroupID;
    class JobQueue;
}
class NavMeshData;
class NavMeshBuildOperation;
struct SharedMeshBuildSource;

class NavMeshBuildManager
{
public:
    NavMeshBuildManager() : m_JobQueue(NULL), m_AsyncOperations(kMemAI) {}
    virtual ~NavMeshBuildManager();

    void UpdateAsyncOperations();
    void ReleaseAsyncOperations();

    void Purge(const NavMeshData* data);
    bool IsAsyncOperationing(Rainbow::AABB& inBounds);

    void SyncOperationFence(Rainbow::JobGroupID& groupID);
    void ClearOperationFenceWithoutSync(Rainbow::JobGroupID& groupID);

    // Used for Script API
    static void UpdateNavMeshDataAsync(NavMeshData* data, const NavMeshBuildSettings& buildSettings,
        const SharedMeshBuildSource* sources, size_t nsources, const Rainbow::AABB& localBounds);

    static bool UpdateNavMeshData(NavMeshData* data, const NavMeshBuildSettings& buildSettings,
        const SharedMeshBuildSource* sources, size_t nsources, const Rainbow::AABB& localBounds);

private:
    void ExecuteAsync(NavMeshBuildOperation* op);

    Rainbow::JobQueue* m_JobQueue;
    dynamic_array<NavMeshBuildOperation*> m_AsyncOperations;
};
