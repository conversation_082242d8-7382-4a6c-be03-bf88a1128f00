#include "SceneEffectFan.h"
#include "world_types.h"
#include "world.h"
#include "WorldRender.h"
#include "SandboxMacros.h"

#include "SandboxBodyComponent.h"
#include "SandboxSceneRoot.h"
#include "BlockScene.h"
#include "SandboxSceneManager.h"
#include "WorldScene.h"
#include "SandboxSceneObject.h"
#include "SandboxAutoRef.h"
#include "Components/MeshRenderer.h"
#include "Entity/OgreModel.h"

using namespace Rainbow;

SceneEffectFan::SceneEffectFan(CurveFace* curveFaces)
{
	SetCurveFaces(curveFaces);
}

SceneEffectFan::~SceneEffectFan()
{
	MNSandbox::AutoRef<MNSandbox::SceneRoot> worksapce = MNSandbox::GetSceneManager().GetWorkspace();
	WorldScene* pScene = worksapce->GetScene().ToCast<WorldScene>();
	World* pWorld = pScene->GetOwner();
	if (nullptr != mpModel)
	{
		pWorld->getScene()->RemoveGameObject(mpModel->GetGameObject());
		mpModel = nullptr;
	}
}

void SceneEffectFan::draw(const Vector3f& vOrigin, const Vector3f& vBeginPos, const Vector3f& vEndPos, const std::string& szTexture)
{
	if (m_vOrigin == vOrigin &&
		m_vBeginPos == vBeginPos &&
		m_vEndPos == vEndPos &&
		!m_szTexture.compare(szTexture))
	{
		return;
	}
	m_vOrigin = vOrigin;
	m_vBeginPos = vBeginPos;
	m_vEndPos = vEndPos;
	m_szTexture = szTexture;

	mDrawVertices.clear();
	mDrawIndices.clear();
	Vector3f dir = vBeginPos - vOrigin;

	float fLength = dir.Length();

	Vector3f vB = vBeginPos - vOrigin;
	vB.Normalize();
	Vector3f vE = vEndPos - vOrigin;
	vE.Normalize();
	
	float f = Angle(vB, vE) * 180.0f / 3.1415926f;
	vertex v0, v1;

	v0.mPosition = vOrigin;
	mDrawVertices.push_back(v0);

	for (float i = 0; i < f; i += 1.0f)
	{
		auto v = Lerp(vBeginPos, vEndPos, i / f);
		v -= vOrigin;
		v.Normalize();
		v1.mPosition = vOrigin + v * fLength;
		mDrawVertices.push_back(v1);
	}
	v1.mPosition = vOrigin + vE * fLength;
	mDrawVertices.push_back(v1);

	for (int i = 0; i < mDrawVertices.size() - 1; ++i)
	{
		mDrawIndices.push_back(0);
		mDrawIndices.push_back(i);
		mDrawIndices.push_back(i + 1);
	}
	if (mDrawVertices.size() > 0)
	{
		MNSandbox::AutoRef<MNSandbox::SceneRoot> worksapce = MNSandbox::GetSceneManager().GetWorkspace();
		WorldScene* pScene = worksapce->GetScene().ToCast<WorldScene>();
		World* pWorld = pScene->GetOwner();
		if (nullptr != mpModel)
		{
			pWorld->getScene()->RemoveGameObject(mpModel->GetGameObject());
		}
		mpModel = MNSandbox::BodyComponent::CreateObjModel(&mDrawVertices[0],
			sizeof(vertex),
			mDrawVertices.size(),
			&mDrawIndices[0],
			mDrawIndices.size(),
			szTexture,
			"Materials/MiniGame/legacy/legacy_stdmtl_brush.templatemat",
			false);

		mpModel->SetPosition(Vector3f(0, 0, 0));
		pWorld->getScene()->AddGameObject(mpModel->GetGameObject());
	}
}



void SceneEffectFan::Refresh()
{

}

void SceneEffectFan::OnDraw(World* pWorld)
{

}

void SceneEffectFan::setPosition(const Vector3f& pos)
{
	mPostion = pos;
}

void SceneEffectFan::setSmooth(float level)
{
	if (level > 0 && level <= 5.0f)
	{
		mSmoothLevel = 1.0f / (level * 360);
	}
}

