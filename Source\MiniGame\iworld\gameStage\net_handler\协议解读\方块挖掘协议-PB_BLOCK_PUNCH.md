# 方块挖掘协议 - PB_BLOCK_PUNCH

## 协议概述

PB_BLOCK_PUNCH 协议是沙盒游戏中处理方块挖掘操作的核心网络协议。该协议支持多种挖掘模式，包括普通挖掘、充能挖掘、多重挖掘等，并支持载具挖掘功能。

- **PB_BLOCK_PUNCH_CH**: 客户端发送给服务器
- **PB_BLOCK_PUNCH_HC**: 服务器发送给客户端

## 协议定义

### PB_BlockPunchCH (客户端 → 服务器)

```protobuf
message PB_BlockPunchCH
{
    optional int32 status = 1;                  // 挖掘状态(开始/进行中/结束)
    optional int32 face = 2;                    // 挖掘面方向
    optional int32 digmethod = 3;               // 挖掘方法
    optional game.common.PB_Vector3 blockpos = 4;  // 目标方块位置
    optional uint64 vehicleObjID = 5 [default=0];  // 载具对象ID
    optional uint32 clienttick = 6;             // 客户端时间戳
}
```

**协议定义位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:104-112`

### PB_BlockPunchHC (服务器 → 客户端)

```protobuf
message PB_BlockPunchHC
{
    optional uint64 objid = 1;                  // 玩家对象ID
    optional int32 status = 2;                  // 挖掘状态
    optional int32 face = 3;                    // 挖掘面方向
    optional int32 digmethod = 4;               // 挖掘方法
    optional game.common.PB_Vector3 blockpos = 5;  // 方块位置
    optional uint64 vehicleObjID = 6;           // 载具对象ID
}
```

## 挖掘状态枚举

```cpp
enum PLAYEROP_STATUS
{
    PLAYEROP_STATUS_BEGIN = 0,      // 开始挖掘
    PLAYEROP_STATUS_CONTINUE = 1,   // 持续挖掘
    PLAYEROP_STATUS_END = 2         // 结束挖掘
};
```

## 挖掘方法枚举

```cpp
enum DIG_METHOD_T
{
    DIG_METHOD_NORMAL = 0,          // 普通挖掘
    DIG_METHOD_CHARGE = 1,          // 充能挖掘
    DIG_METHOD_MULTI = 2,           // 多重挖掘
    DIG_METHOD_VEHICLE = 3          // 载具挖掘
};
```

## 协议流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant OtherClients as 其他客户端

    Note over Client: 玩家开始挖掘方块
    Client->>Client: DigStateAction::digBlock()
    Client->>Client: 构造PB_BlockPunchCH协议
    Client->>Server: PB_BLOCK_PUNCH_CH (status=BEGIN)

    Note over Server: 服务器验证和处理
    Server->>Server: handleBlockPunch2Host()
    Server->>Server: 反作弊检查
    Server->>Server: 获取DigState状态
    Server->>Server: digState->digBlock()

    Note over Client: 持续挖掘过程
    Client->>Server: PB_BLOCK_PUNCH_CH (status=CONTINUE)
    Server->>Server: 更新挖掘进度

    Note over Client: 完成挖掘
    Client->>Server: PB_BLOCK_PUNCH_CH (status=END)
    Server->>Server: 完成方块破坏

    Note over Server: 广播给其他玩家
    Server->>Server: notifyPunchBlock2Tracking()
    Server->>OtherClients: PB_BLOCK_PUNCH_HC

    Note over OtherClients: 客户端同步
    OtherClients->>OtherClients: handleBlockPunch2Client()
    OtherClients->>OtherClients: 播放挖掘效果
```

## 详细实现流程

### 1. 客户端发送协议

**触发位置**: `Source\SandboxGame\Play\player\state\action\DigStateAction.cpp:881-896`

```cpp
bool DigStateAction::digBlock(const WCoord &targetblock, DirectionType targetface, int status, DIG_METHOD_T digmethod)
{
    // ... 其他逻辑 ...

    if (mpCtrl->getWorld()->isRemoteMode())
    {
        PB_BlockPunchCH blockPunchCH;
        blockPunchCH.set_face(targetface);
        blockPunchCH.set_status(status);
        blockPunchCH.set_digmethod(digmethod);
        blockPunchCH.set_clienttick(tick);

        LOG_WARNING("digBlock status = %d digmethod = %d", status, digmethod);

        PB_Vector3* blockPos = blockPunchCH.mutable_blockpos();
        blockPos->set_x(targetblock.x);
        blockPos->set_y(targetblock.y);
        blockPos->set_z(targetblock.z);

        GameNetManager::getInstance()->sendToHost(PB_BLOCK_PUNCH_CH, blockPunchCH);
    }
    return true;
}
```

**载具挖掘发送**: `Source\SandboxGame\Play\player\state\action\DigStateAction.cpp:1285-1291`

```cpp
// 载具挖掘时添加载具ID
blockPunchCH.set_vehicleobjid(pVeh->getObjId());
GameNetManager::getInstance()->sendToHost(PB_BLOCK_PUNCH_CH, blockPunchCH);
```

### 2. 服务器处理协议

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1625-1701`

```cpp
void MpGameSurviveNetHandler::handleBlockPunch2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
    if (player == NULL) return;

    PB_BlockPunchCH blockPunchCH;
    blockPunchCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    long long vehID = blockPunchCH.vehicleobjid();
    WCoord blockpos = MPVEC2WCoord(blockPunchCH.blockpos());
    ClientActor* actor = objId2ActorOnClient(vehID);
    auto vehActor = dynamic_cast<ActorVehicleAssemble*>(actor);
    long long tick = blockPunchCH.clienttick();

    // 反作弊检查
    if (player->GetCheatHandler())
    {
        World* world = nullptr;
        if (vehActor)
        {
            world = vehActor->getVehicleWorld();
        }
        if (blockPunchCH.status() == PLAYEROP_STATUS_BEGIN)
        {
            if (!player->GetCheatHandler()->CheckClickBlock(blockpos, 0, world))
                return;
        }
    }

    // 获取挖掘状态并执行
    auto pState = player->getActionStatePtr("Dig");
    if (nullptr != pState)
    {
        auto pDigState = dynamic_cast<DigState*>(pState);
        if (nullptr != pDigState)
        {
            if (vehActor)
            {
                // 载具挖掘逻辑
                pDigState->digBlockWithVehicle(blockpos, (DirectionType)blockPunchCH.face(),
                                             blockPunchCH.status(), (DIG_METHOD_T)blockPunchCH.digmethod(), vehActor);
            }
            else
            {
                // 普通挖掘逻辑
                pDigState->digBlock(blockpos, (DirectionType)blockPunchCH.face(),
                                  blockPunchCH.status(), (DIG_METHOD_T)blockPunchCH.digmethod());
            }
        }
    }
}
```

**关键处理步骤**:

1. 验证玩家状态
2. 解析协议数据
3. **反作弊检查** - 验证方块点击合法性
4. 区分普通挖掘和载具挖掘
5. 获取 DigState 状态对象
6. 执行相应的挖掘逻辑

### 3. 服务器响应广播

**响应函数**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:114-128`

```cpp
void ClientPlayer::notifyPunchBlock2Tracking(const WCoord& blockpos, DirectionType face, int status, DIG_METHOD_T digmethod, long long vehID)
{
    if (!m_pWorld->isRemoteMode())
    {
        PB_BlockPunchHC blockPunchHC;
        blockPunchHC.set_objid(getObjId());
        blockPunchHC.set_status(status);
        blockPunchHC.set_face(face);
        blockPunchHC.set_digmethod(digmethod);
        blockPunchHC.mutable_blockpos()->set_x(blockpos.x);
        blockPunchHC.mutable_blockpos()->set_y(blockpos.y);
        blockPunchHC.mutable_blockpos()->set_z(blockpos.z);
        blockPunchHC.set_vehicleobjid(vehID);

        m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_PUNCH_HC, blockPunchHC, this);
    }
}
```

### 4. 客户端接收响应

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:3760-3823`

```cpp
void MpGameSurviveNetHandler::handleBlockPunch2Client(const PB_PACKDATA_CLIENT &pkg)
{
    PB_BlockPunchHC blockPunchHC;
    blockPunchHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockPunchHC.objid()));
    if (player == NULL) return;

    long long vehID = blockPunchHC.vehicleobjid();

    if (vehID != 0)
    {
        // 载具挖掘处理
        auto pState = player->getActionStatePtr("Dig");
        auto actor = objId2ActorOnClient(vehID);
        if (actor && pState)
        {
            auto vehActor = dynamic_cast<ActorVehicleAssemble*>(actor);
            auto pDigState = dynamic_cast<DigState*>(pState);
            if (vehActor && pDigState)
            {
                pDigState->digBlockWithVehicle(MPVEC2WCoord(blockPunchHC.blockpos()),
                                             (DirectionType)blockPunchHC.face(),
                                             blockPunchHC.status(),
                                             (DIG_METHOD_T)blockPunchHC.digmethod(), vehActor);
            }
        }
    }
    else
    {
        // 普通挖掘处理
        auto pState = player->getCurrentActionStatePtr();
        auto pDigState = dynamic_cast<DigState*>(pState);
        if (pDigState)
        {
            pDigState->digBlock(MPVEC2WCoord(blockPunchHC.blockpos()),
                              (DirectionType)blockPunchHC.face(),
                              blockPunchHC.status(),
                              (DIG_METHOD_T)blockPunchHC.digmethod());
        }
    }
}
```

## 协议注册

**注册位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp`

```cpp
// 服务器端处理器注册
REGIS_HOST_HANDLER(PB_BLOCK_PUNCH_CH, handleBlockPunch2Host);

// 客户端处理器注册 (行号481)
REGIS_CLIENT_HANDLER(PB_BLOCK_PUNCH_HC, handleBlockPunch2Client);
```

## 反作弊机制

服务器端实现了完善的反作弊检查：

```cpp
if (player->GetCheatHandler())
{
    if (blockPunchCH.status() == PLAYEROP_STATUS_BEGIN)
    {
        if (!player->GetCheatHandler()->CheckClickBlock(blockpos, 0, world))
            return;  // 检查失败，拒绝处理
    }
}
```

## 埋点统计

在方块被成功破坏时，会进行数据埋点：

**埋点位置**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:3484-3491`

```cpp
// 埋点上报
std::ostringstream oss;
oss << "[" << blockpos.x << "," << blockpos.y << "," << blockpos.z << "]";
std::string location = oss.str();

GameAnalytics::TrackEvent("block_destroy", {
    {"block_id",blockid},
    {"location",location}
});
```

## 协议特点

1. **状态机制**: 支持开始、持续、结束三种挖掘状态
2. **多种挖掘模式**: 普通、充能、多重、载具挖掘
3. **载具支持**: 完整的载具挖掘功能
4. **反作弊保护**: 服务器端验证挖掘操作合法性
5. **时间同步**: 客户端时间戳用于同步验证
6. **数据统计**: 完整的挖掘行为埋点

## 相关协议

- **PB_BLOCK_INTERACT**: 方块放置协议
- **PB_BLOCK_EXPLOIT**: 特殊开采协议
- **PB_BLOCK_ATTACK**: 方块攻击协议
