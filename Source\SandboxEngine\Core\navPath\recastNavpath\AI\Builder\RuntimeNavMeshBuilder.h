#pragma once
#include "Public/NavMeshBindingTypes.h"
namespace Rainbow {
    class AABB;
}
struct SharedMeshBuildSource;
class NavMeshData;
struct NavMeshBuildSettings;

// Opaque struct to be used as a handle. Internally it contains the build state
struct BuildNavMeshInfo;

BuildNavMeshInfo* CreateBuildNavMeshInfo();
void DestroyBuildNavMeshInfo(BuildNavMeshInfo* info);

void AcquireSharedMeshData(BuildNavMeshInfo* info, const SharedMeshBuildSource* sources, size_t nsources,
    const Rainbow::Vector3f& position, const Rainbow::Quaternionf& rotation, const Rainbow::AABB& localBounds);
void ReleaseSharedMeshData(BuildNavMeshInfo* info);

// Flag the given BuildNavMeshInfo instance as cancel
// Make scheduling or computations abort as soon as they can
void Cancel(BuildNavMeshInfo* info);

// Sync and wait for NavMesh computation jobs to be completed
// Use Cancel(info) prior to that call if you want to abort the build process as soon as possible
void SyncComputationFence(BuildNavMeshInfo* info);

bool Done(const BuildNavMeshInfo* info);

float Progress(const BuildNavMeshInfo* info);

void ScheduleNavMeshDataUpdate(const NavMeshData* data, BuildNavMeshInfo* info,
    const NavMeshBuildSettings& settings, const Rainbow::AABB& localBuildBounds);
void IntegrateNavMeshDataUpdate(NavMeshData* data, BuildNavMeshInfo* info, const Rainbow::AABB& localBounds);
