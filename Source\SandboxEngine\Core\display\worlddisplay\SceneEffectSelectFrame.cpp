/*
*	file: SceneEffectSelectFrame
*	func: 沙盒选区效果
*	by : yangzy
*/

#include "SceneEffectSelectFrame.h"
#include "SandboxGlobalNotify.h"
#include "SandboxCameraObject.h"
#include "world.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectSelectFrame::SceneEffectSelectFrame()
{
	m_iStroke = 1;
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
}

SceneEffectSelectFrame::SceneEffectSelectFrame(const WCoord& startpos, const WCoord& endpos, 
	int stroke, CURVEFACEMTLTYPE mtltype, CurveFace* curveFaces)
{
	m_iStroke = stroke;
	m_MtlType = mtltype;

	Normalize(startpos, endpos);
	SetCurveFaces(curveFaces);
}

SceneEffectSelectFrame::~SceneEffectSelectFrame()
{
	OnClear();
}

void SceneEffectSelectFrame::SetPos(const WCoord& startpos, const WCoord& endpos)
{
	Normalize(startpos, endpos);
}

void SceneEffectSelectFrame::SetColor(MNColor selectFrameColor, MNColor coverColor, MNColor endArrowColor)
{
	m_SelectFrameColor = BlockVector(selectFrameColor._r, selectFrameColor._g, selectFrameColor._b, selectFrameColor._a);
	m_CoverColor = BlockVector(coverColor._r, coverColor._g, coverColor._b, coverColor._a);
	m_EndArrowColor = BlockVector(endArrowColor._r, endArrowColor._g, endArrowColor._b, endArrowColor._a);
}

void SceneEffectSelectFrame::OnClear()
{
	m_VertexsBottom.clear();
	m_VertexsTop.clear();
	m_VertexsLeft.clear();
	m_VertexsRight.clear();
	m_VertexsFront.clear();
	m_VertexsBehind.clear();
	m_Indices.clear();

	SANDBOX_DELETE(m_optFrame);
	SANDBOX_DELETE(m_optFrameAlpha);
	SANDBOX_DELETE(m_endArrow);
}

void SceneEffectSelectFrame::Refresh()
{
	//判断有效
	if (!IsValid())
	{
		return;
	}

	CalcVertexsAndIndices();
}

void SceneEffectSelectFrame::OnDraw(World* pWorld)
{
	//判断有效
	if (!IsValid() || !pWorld || !m_bShow)
	{
		return;
	}

	if (m_optFrame)
	{
		m_optFrame->OnDraw(pWorld);
	}

	if (m_optFrameAlpha)
	{
		m_optFrameAlpha->OnDraw(pWorld);
	}

	if (m_endArrow && m_bShowEndArrow)
	{
		m_endArrow->OnDraw(pWorld);
	}

	auto CurveRender = m_CurveFaces ? m_CurveFaces : pWorld->getRender()->getCurveRender();
	if (CurveRender)
	{
		static CURVEFACEMTLTYPE mtlType = CURVEFACEMTL_TEXWHITE_EX;

		for (auto i = 0; i < m_VertexsBottom.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsBottom[i * 4],  m_VertexsBottom[i * 4 + 1], m_VertexsBottom[i * 4 + 2], m_VertexsBottom[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartBottom, verts, m_Indices);
		}

		for (auto i = 0; i < m_VertexsTop.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsTop[i * 4],  m_VertexsTop[i * 4 + 1], m_VertexsTop[i * 4 + 2], m_VertexsTop[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartTop, verts, m_Indices);
		}

		for (auto i = 0; i < m_VertexsLeft.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsLeft[i * 4],  m_VertexsLeft[i * 4 + 1], m_VertexsLeft[i * 4 + 2], m_VertexsLeft[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartLeft, verts, m_Indices);
		}

		for (auto i = 0; i < m_VertexsRight.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsRight[i * 4],  m_VertexsRight[i * 4 + 1], m_VertexsRight[i * 4 + 2], m_VertexsRight[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartRight, verts, m_Indices);
		}

		for (auto i = 0; i < m_VertexsFront.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsFront[i * 4],  m_VertexsFront[i * 4 + 1], m_VertexsFront[i * 4 + 2], m_VertexsFront[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartFront, verts, m_Indices);
		}

		for (auto i = 0; i < m_VertexsBehind.size() / 4; i++)
		{
			std::vector<BlockGeomVert> verts = { m_VertexsBehind[i * 4],  m_VertexsBehind[i * 4 + 1], m_VertexsBehind[i * 4 + 2], m_VertexsBehind[i * 4 + 3] };
			CurveRender->addRect((int)mtlType, m_StartBehind, verts, m_Indices);
		}
	}
}

bool SceneEffectSelectFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectSelectFrame::SetEndArrowPos(MNCoord3f pos)
{
	m_endArrowPos = pos;

	if (m_endArrow)
	{
		m_endArrow->SetCurveFaces(m_CurveFaces);
		m_endArrow->SetArrow(m_endArrowPos, Vector3f::yAxis, DIR_POS_Y);
		m_endArrow->Refresh();
	}
}

void SceneEffectSelectFrame::Normalize(const WCoord& startpos, const WCoord& endpos)
{
	m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
	m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
	m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
	m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
	m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
	m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
}

bool SceneEffectSelectFrame::IsValid()
{
	//数据校验
	if (m_EndPos == m_StartPos)
	{
		return false;
	}

	//判断整除
	Vector3f dim = (m_EndPos - m_StartPos).toVector3();
	if ((int)dim.x % BLOCK_SIZE != 0 || (int)dim.y % BLOCK_SIZE != 0 || (int)dim.z % BLOCK_SIZE != 0)
	{
		return false;
	}

	return true;
}

void SceneEffectSelectFrame::CalcVertexsAndIndices()
{
	OnClear(); 
	
	//框效果
	m_optFrame = SANDBOX_NEW(SceneEffectFrame, m_StartPos, m_EndPos);
	m_optFrame->SetStroke(m_iStroke);
	m_optFrame->SetColor(m_SelectFrameColor);
	m_optFrame->SetMtlType(m_MtlType);
	m_optFrame->SetCurveFaces(m_CurveFaces);
	m_optFrame->Refresh();

	m_optFrameAlpha = SANDBOX_NEW(SceneEffectFrame, m_StartPos, m_EndPos);
	m_optFrameAlpha->SetStroke(m_iStroke);

	auto color = m_SelectFrameColor;
	color.w = 30;
	m_optFrameAlpha->SetColor(color);
	m_optFrameAlpha->SetMtlType(CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY);
	m_optFrameAlpha->SetCurveFaces(m_CurveFaces);
	m_optFrameAlpha->Refresh();

	//结束箭头
	m_endArrow = SANDBOX_NEW(SceneEffectArrow, m_endArrowPos, Vector3f::yAxis, DIR_POS_Y);
	m_endArrow->SetCurveFaces(m_CurveFaces);
	m_endArrow->SetArrowRange(30, 60);
	m_endArrow->SetMtlType(m_MtlType);
	m_endArrow->SetColor(m_EndArrowColor);
	m_endArrow->Refresh();

	Box<int> box(m_StartPos, m_EndPos);
	auto xLen = box.GetLenX();
	auto yLen = box.GetLenY();
	auto zLen = box.GetLenZ();

	//面刷效果	
	Vector3f borderDim = Vector3f(m_BorderWidth, m_BorderWidth, m_BorderWidth);
	Vector3f dim = Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	dim -= (2 * borderDim);
	
	//轴偏移
	Vector3f offsetX = Vector3f(BLOCK_SIZE, 0, 0);
	Vector3f offsetY = Vector3f(0, BLOCK_SIZE, 0);
	Vector3f offsetZ = Vector3f(0, 0, BLOCK_SIZE);

	//前
	Vector3f vertexFront[4];
	Vector3f startposFront = Vector3f(xLen - m_BorderWidth, yLen - m_BorderWidth, 0);
	m_StartFront = m_StartPos;
	m_StartFront.x -= ((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE - 1) * BLOCK_SIZE;
	m_StartFront.y -= ((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE - 1) * BLOCK_SIZE;

	vertexFront[0] = startposFront;
	vertexFront[1] = startposFront - Vector3f(dim.x, 0, 0);
	vertexFront[2] = startposFront - Vector3f(0, dim.y, 0);
	vertexFront[3] = startposFront - Vector3f(dim.x, 0, 0) - Vector3f(0, dim.y, 0);

	//后
	Vector3f vertexBehind[4];
	Vector3f startposBehind = Vector3f(m_BorderWidth, yLen - m_BorderWidth, zLen);
	m_StartBehind = m_StartPos;
	m_StartBehind.y -= ((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE - 1) * BLOCK_SIZE;

	vertexBehind[0] = startposBehind;
	vertexBehind[1] = startposBehind + Vector3f(dim.x, 0, 0);
	vertexBehind[2] = startposBehind - Vector3f(0, dim.y, 0);
	vertexBehind[3] = startposBehind - Vector3f(0, dim.y, 0) + Vector3f(dim.x, 0, 0);

	//左
	Vector3f vertexLeft[4];
	Vector3f startposLeft = Vector3f(0, yLen - m_BorderWidth, m_BorderWidth);
	m_StartLeft = m_StartPos;
	m_StartLeft.y -= ((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE - 1) * BLOCK_SIZE;

	vertexLeft[0] = startposLeft;
	vertexLeft[1] = startposLeft + Vector3f(0, 0, dim.z);
	vertexLeft[2] = startposLeft - Vector3f(0, dim.y, 0);
	vertexLeft[3] = startposLeft - Vector3f(0, dim.y, 0) + Vector3f(0, 0, dim.z);

	//右
	Vector3f vertexRight[4];
	Vector3f startposRight = Vector3f(xLen, m_BorderWidth, m_BorderWidth);
	m_StartRight = m_StartPos;

	vertexRight[0] = startposRight;
	vertexRight[1] = startposRight + Vector3f(0, 0, dim.z);
	vertexRight[2] = startposRight + Vector3f(0, dim.y, 0);
	vertexRight[3] = startposRight + Vector3f(0, dim.y, dim.z);

	//底
	Vector3f vertexBottom[4];
	Vector3f startposBottom = Vector3f(m_BorderWidth, 0, m_BorderWidth);
	m_StartBottom = m_StartPos;

	vertexBottom[0] = startposBottom;
	vertexBottom[1] = startposBottom + Vector3f(0, 0, dim.z);
	vertexBottom[2] = startposBottom + Vector3f(dim.x, 0, 0);
	vertexBottom[3] = startposBottom + Vector3f(0, 0, dim.z) + Vector3f(dim.x, 0, 0);

	//顶
	Vector3f vertexTop[4];
	Vector3f startposTop = Vector3f(xLen - m_BorderWidth, yLen, m_BorderWidth);
	m_StartTop = m_StartPos;
	m_StartTop.x -= ((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE - 1) * BLOCK_SIZE;

	vertexTop[0] = startposTop;
	vertexTop[1] = startposTop + Vector3f(0, 0, dim.z);
	vertexTop[2] = startposTop - Vector3f(dim.x, 0, 0);
	vertexTop[3] = startposTop - Vector3f(dim.x, 0, 0) + Vector3f(0, 0, dim.z);

	float u[3][2], v[3][2];
	u[0][0] = 0.0f; u[0][1] = dim.z;
	v[0][0] = 0.0f; v[0][1] = dim.x;
	u[1][0] = 0.0f; u[1][1] = dim.z;
	v[1][0] = 0.0f; v[1][1] = dim.y;
	u[2][0] = 0.0f; u[2][1] = dim.x;
	v[2][0] = 0.0f; v[2][1] = dim.y;

	m_Indices.push_back(0);
	m_Indices.push_back(2);
	m_Indices.push_back(1);
	m_Indices.push_back(1);
	m_Indices.push_back(2);
	m_Indices.push_back(3);

	m_VertexsBottom.resize(((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE) * ((m_EndPos.z - m_StartPos.z) / BLOCK_SIZE) * 4);
	m_VertexsTop.resize(((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE) * ((m_EndPos.z - m_StartPos.z) / BLOCK_SIZE) * 4);
	m_VertexsLeft.resize(((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE) * ((m_EndPos.z - m_StartPos.z) / BLOCK_SIZE) * 4);
	m_VertexsRight.resize(((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE) * ((m_EndPos.z - m_StartPos.z) / BLOCK_SIZE) * 4);
	m_VertexsFront.resize(((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE) * ((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE) * 4);
	m_VertexsBehind.resize(((m_EndPos.x - m_StartPos.x) / BLOCK_SIZE) * ((m_EndPos.y - m_StartPos.y) / BLOCK_SIZE) * 4);
	
	auto nVertexIndex = 0;

	//底面
	nVertexIndex = 0;
	for (auto x = 0; x < (m_EndPos.x - m_StartPos.x) / BLOCK_SIZE; x++)
	{
		auto newOffsetX = offsetX * (float)x;
		for (auto z = 0; z < (m_EndPos.z - m_StartPos.z) / BLOCK_SIZE; z++)
		{
			auto newOffsetZ = offsetZ * (float)z;
			SceneEffectLine::FillVertBuffer(m_VertexsBottom[nVertexIndex++], vertexBottom[0] + newOffsetX + newOffsetZ, u[0][0], v[0][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBottom[nVertexIndex++], vertexBottom[1] + newOffsetX + newOffsetZ, u[0][1], v[0][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBottom[nVertexIndex++], vertexBottom[2] + newOffsetX + newOffsetZ, u[0][0], v[0][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBottom[nVertexIndex++], vertexBottom[3] + newOffsetX + newOffsetZ, u[0][1], v[0][1], m_CoverColor);
		}
	}

	//顶面
	nVertexIndex = 0;
	for (auto x = 0; x < (m_EndPos.x - m_StartPos.x) / BLOCK_SIZE; x++)
	{
		auto newOffsetX = offsetX * (float)x;
		for (auto z = 0; z < (m_EndPos.z - m_StartPos.z) / BLOCK_SIZE; z++)
		{
			auto newOffsetZ = offsetZ * (float)z;
			SceneEffectLine::FillVertBuffer(m_VertexsTop[nVertexIndex++], vertexTop[0] + newOffsetX + newOffsetZ, u[0][0], v[0][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsTop[nVertexIndex++], vertexTop[1] + newOffsetX + newOffsetZ, u[0][1], v[0][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsTop[nVertexIndex++], vertexTop[2] + newOffsetX + newOffsetZ, u[0][0], v[0][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsTop[nVertexIndex++], vertexTop[3] + newOffsetX + newOffsetZ, u[0][1], v[0][1], m_CoverColor);
		}
	}

	//左面
	nVertexIndex = 0;
	for (auto y = 0; y < (m_EndPos.y - m_StartPos.y) / BLOCK_SIZE; y++)
	{
		auto newOffsetY = offsetY * (float)y;
		for (auto z = 0; z < (m_EndPos.z - m_StartPos.z) / BLOCK_SIZE; z++)
		{
			auto newOffsetZ = offsetZ * (float)z;
			SceneEffectLine::FillVertBuffer(m_VertexsLeft[nVertexIndex++], vertexLeft[0] + newOffsetY + newOffsetZ, u[1][0], v[1][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsLeft[nVertexIndex++], vertexLeft[1] + newOffsetY + newOffsetZ, u[1][1], v[1][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsLeft[nVertexIndex++], vertexLeft[2] + newOffsetY + newOffsetZ, u[1][0], v[1][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsLeft[nVertexIndex++], vertexLeft[3] + newOffsetY + newOffsetZ, u[1][1], v[1][1], m_CoverColor);
		}
	}

	//右面
	nVertexIndex = 0;
	for (auto y = 0; y < (m_EndPos.y - m_StartPos.y) / BLOCK_SIZE; y++)
	{
		auto newOffsetY = offsetY * (float)y;
		for (auto z = 0; z < (m_EndPos.z - m_StartPos.z) / BLOCK_SIZE; z++)
		{
			auto newOffsetZ = offsetZ * (float)z;
			SceneEffectLine::FillVertBuffer(m_VertexsRight[nVertexIndex++], vertexRight[0] + newOffsetY + newOffsetZ, u[1][0], v[1][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsRight[nVertexIndex++], vertexRight[1] + newOffsetY + newOffsetZ, u[1][1], v[1][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsRight[nVertexIndex++], vertexRight[2] + newOffsetY + newOffsetZ, u[1][0], v[1][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsRight[nVertexIndex++], vertexRight[3] + newOffsetY + newOffsetZ, u[1][1], v[1][1], m_CoverColor);
		}
	}

	//前面
	nVertexIndex = 0;
	for (auto x = 0; x < (m_EndPos.x - m_StartPos.x) / BLOCK_SIZE; x++)
	{
		auto newOffsetX = offsetX * (float)x;
		for (auto y = 0; y < (m_EndPos.y - m_StartPos.y) / BLOCK_SIZE; y++)
		{
			auto newOffsetY = offsetY * (float)y;
			SceneEffectLine::FillVertBuffer(m_VertexsFront[nVertexIndex++], vertexFront[0] + newOffsetX + newOffsetY, u[2][0], v[2][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsFront[nVertexIndex++], vertexFront[1] + newOffsetX + newOffsetY, u[2][1], v[2][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsFront[nVertexIndex++], vertexFront[2] + newOffsetX + newOffsetY, u[2][0], v[2][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsFront[nVertexIndex++], vertexFront[3] + newOffsetX + newOffsetY, u[2][1], v[2][1], m_CoverColor);
		}
	}

	//后面
	nVertexIndex = 0;
	for (auto x = 0; x < (m_EndPos.x - m_StartPos.x) / BLOCK_SIZE; x++)
	{
		auto newOffsetX = offsetX * (float)x;
		for (auto y = 0; y < (m_EndPos.y - m_StartPos.y) / BLOCK_SIZE; y++)
		{
			auto newOffsetY = offsetY * (float)y;
			SceneEffectLine::FillVertBuffer(m_VertexsBehind[nVertexIndex++], vertexBehind[0] + newOffsetX + newOffsetY, u[2][0], v[2][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBehind[nVertexIndex++], vertexBehind[1] + newOffsetX + newOffsetY, u[2][1], v[2][0], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBehind[nVertexIndex++], vertexBehind[2] + newOffsetX + newOffsetY, u[2][0], v[2][1], m_CoverColor);
			SceneEffectLine::FillVertBuffer(m_VertexsBehind[nVertexIndex++], vertexBehind[3] + newOffsetX + newOffsetY, u[2][1], v[2][1], m_CoverColor);
		}
	}
}

void SceneEffectSelectFrame::CallbackNodeChanged(SandboxNode* node, SandboxNode::NODECHANGE eNodeChanged)
{
	if (eNodeChanged == SandboxNode::NODECHANGE::AttributeChanged && node && node->ToCast<SandboxCameraObject>() && m_endArrow)
	{
		auto pCameraNode = node->ToCast<SandboxCameraObject>();
		auto v = m_endArrowPos - pCameraNode->GetWorldPosition();

		static float fMinRatio = 1.0;
		auto ratio = v.GetLength() / 1000.0f;
		m_endArrowScaleRatio = ratio < fMinRatio ? fMinRatio : ratio;

		m_endArrow->SetCurveFaces(m_CurveFaces);
		m_endArrow->SetRatio(m_endArrowScaleRatio);
		m_endArrow->Refresh();
	}
}