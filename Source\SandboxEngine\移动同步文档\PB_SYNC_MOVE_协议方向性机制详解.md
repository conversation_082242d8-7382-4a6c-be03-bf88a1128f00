# PB_SYNC_MOVE 协议方向性机制详解

## 问题背景

在 `ClientActor::tick()` 中，每一帧都会执行 `preMoveTick()` 和 `afterMoveTick()`，为什么：
- 服务器端只执行 PB_SYNC_MOVE_HC 协议发送？
- 客户端只执行 PB_SYNC_MOVE_CH 协议发送？

## 核心机制重新分析

### 1. 关键判断条件的真正含义

#### hasUIControl() - 类继承层次的区别
```cpp
// SandboxGame/Play/player/ClientPlayer.h:437
class ClientPlayer : public ActorLiving, public IClientPlayer
{
    virtual bool hasUIControl()
    {
        return false;  // ClientPlayer基类返回false
    }
}

// SandboxGame/Play/player/PlayerControl.h:81,260
class PlayerControl : public ClientPlayer, public IControlInterface, public IPlayerControl
{
    virtual bool hasUIControl()
    {
        return true;  // PlayerControl重写返回true
    }
}
```

**关键发现**：
- **服务器端（iworld）**：所有连接的玩家都创建为 `ClientPlayer` 对象
- **客户端游戏**：本地玩家是 `PlayerControl` 对象，其他玩家是 `ClientPlayer` 对象

#### isRemoteMode() - World对象方法
```cpp
// SandboxEngine/Core/worldData/world_gen.cpp:956
/**
 * @return true 客机, false 主机
*/
bool World::isRemoteMode() const
{
    return m_isRemote || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false);
}

void World::setRemoteMode(bool isClientMode)
{
    m_isRemote = isClientMode;
}
```

**含义**：
- `true` = 客机模式  
- `false` = 主机模式（包括服务器端）

### 2. 协议发送条件重新分析

#### PB_SYNC_MOVE_CH (客户端→服务器)
```cpp
// SandboxGame/Play/gameplay/mpgameplay/MpPlayerControl.cpp:2308
void MpPlayerControl::afterMoveTick()
{
    if (m_MoveControl->isMotionUpEnd())
        m_MoveControl->addEvent(IMT_UpEnd);
    if (isNewMoveSyncSwitchOn() && m_pWorld && m_pWorld->isRemoteMode())
        syncMove2Host();
    PlayerControl::afterMoveTick();
}
```

**发送条件**：
- `isNewMoveSyncSwitchOn()` - 新同步开关开启
- `m_pWorld->isRemoteMode()` - **必须是客机模式**
- 只有 `PlayerControl` 类型的对象（客户端本地玩家）会调用此方法

#### PB_SYNC_MOVE_HC (服务器→客户端)  
```cpp
// SandboxGame/Play/player/ClientPlayer_Base.cpp:1284
void ClientPlayer::checkMoveResult()
{
    // 前置检查...
    if (hasUIControl())
        return;  // 有UI控制权则直接返回
    // ...
    if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isRemote())
    {// 主机跑的逻辑
        // 发送 PB_SYNC_MOVE_HC 协议逻辑
        if (ret == CRT_RollBack) {
            GetGameNetManagerPtr()->sendToClient(getUin(), PB_SYNC_MOVE_HC, pbMoveSync);
        }
    }
}
```

**发送条件**：
- `!hasUIControl()` - **没有UI控制权**
- `!GetWorldManagerPtr()->isRemote()` - **主机模式（服务器端）**

### 3. 真正的协议方向性机制

```mermaid
graph TB
    subgraph Server["服务器端iworld"]
        SW["World: isRemoteMode=false"]
        SP1["ClientPlayer1: hasUIControl=false"]
        SP2["ClientPlayer2: hasUIControl=false"] 
        SP3["ClientPlayer3: hasUIControl=false"]
        
        SW --> SP1
        SW --> SP2
        SW --> SP3
    end
    
    subgraph Client1["客户端1"]
        C1W["World: isRemoteMode=true"]
        C1P["PlayerControl: hasUIControl=true"]
        
        C1W --> C1P
    end
    
    subgraph Client2["客户端2"]
        C2W["World: isRemoteMode=true"]
        C2P["PlayerControl: hasUIControl=true"]
        
        C2W --> C2P
    end
    
    SP1 -->|"checkMoveResult发送HC"| C1P
    SP2 -->|"checkMoveResult发送HC"| C2P
    
    C1P -->|"afterMoveTick发送CH"| SP1
    C2P -->|"afterMoveTick发送CH"| SP2
    
    style SP1 fill:#ffcccc
    style SP2 fill:#ffcccc
    style C1P fill:#ccffcc
    style C2P fill:#ccffcc
```

### 4. 协议方向性保证的真实机制

#### 服务器端（iworld）
- **所有玩家对象类型**：`ClientPlayer`
- **hasUIControl() 返回值**：全部返回 `false`
- **isRemoteMode() 返回值**：`false`（服务器端）
- **afterMoveTick()**: 不会调用 `syncMove2Host()`，因为不满足 `isRemoteMode()` 条件
- **checkMoveResult()**: 会执行，因为 `!hasUIControl()` 为 `true`，可能发送HC协议

#### 客户端
- **本地玩家对象类型**：`PlayerControl`  
- **hasUIControl() 返回值**：`true`
- **isRemoteMode() 返回值**：`true`（客户端）
- **afterMoveTick()**: 会调用 `syncMove2Host()`，因为满足 `isRemoteMode()` 条件，发送CH协议
- **checkMoveResult()**: 直接返回，因为 `hasUIControl()` 为 `true`，不发送HC协议

## 总结

协议方向性的保证机制实际上依赖于：

1. **对象类型差异**：
   - 服务器端使用 `ClientPlayer`（`hasUIControl()=false`）
   - 客户端本地玩家使用 `PlayerControl`（`hasUIControl()=true`）

2. **World模式差异**：
   - 服务器端：`isRemoteMode()=false`
   - 客户端：`isRemoteMode()=true`

3. **条件组合过滤**：
   - **PB_SYNC_MOVE_CH**：需要 `isRemoteMode()=true` **且** 对象是 `PlayerControl` 类型
   - **PB_SYNC_MOVE_HC**：需要 `hasUIControl()=false` **且** `isRemoteMode()=false`

这种设计确保了：
- 只有客户端的本地玩家会发送CH协议给服务器
- 只有服务器端的玩家对象会发送HC协议给客户端
- 避免了协议循环和冲突

## 关键发现：触发机制的真相

### 1. 纠错协议的触发依赖关系

**服务器端纠错确实依赖于客户端先发送协议**：

```cpp
// MpGameSurviveHostHandler.cpp:7445,7447 - 服务器处理CH协议时
if (tick_valid)
    player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), tick);
else  
    player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), 0);
```

**`setCheckMoveResult()` 的作用**：
```cpp
// ClientPlayer_Base.cpp:1622
void ClientPlayer::setCheckMoveResult(unsigned long long id, const WCoord &pos, unsigned long long tick)
{
    if (m_MoveControl)
        m_MoveControl->setMoveCheck(id, pos, tick);  // 设置检查参数
}
```

### 2. 检查触发时机

**服务器端在下一帧的 `preMoveTick()` 中**：
```cpp
// ClientPlayer.cpp:3285
void ClientPlayer::preMoveTick()
{
    if (m_MoveControl && !hasUIControl())  // 服务器端所有ClientPlayer返回false
    {
        checkMoveResult();  // 执行检查并可能发送HC协议
    }
    // ...
}
```

### 3. 触发链路

```
客户端发送CH协议 → 服务器接收设置检查参数 → 下一帧触发检查 → 可能发送HC纠错协议
```

**重要结论**：
- 如果客户端没有发送 `PB_SYNC_MOVE_CH`，服务器端就无法触发检查流程
- 服务器端不会主动检测位置不同步，必须依赖客户端的移动同步协议触发
- 这可能是位置不同步bug的根本原因之一

## 最终总结

这种设计巧妙地通过类型系统和模式判断，确保了：
1. **协议发送的严格方向性**
2. **避免了协议循环和冲突**  
3. **实现了高效的多人游戏移动同步**
4. **但也引入了依赖性**：服务器纠错依赖客户端主动同步

每个进程中的对象都有明确的职责分工，形成了一个完整、可靠的移动同步机制。但在调试位置不同步问题时，需要重点检查客户端是否正常发送CH协议。 