#pragma once
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/MeshRenderData.h"
class CurveFace;

namespace Rainbow
{
	class MeshVertexLayout;
	class MeshVertexFormat;

	class CurveFaceRenderObject : public BaseRenderObject
	{
	public:
		CurveFaceRenderObject(CurveFace* face);
		~CurveFaceRenderObject();

		virtual Matrix4x4f CalculateLocalToWorld() const override;
		virtual AABB CalculateWorldBounds() const override;
		virtual bool PrepareRender(const PrimitiveFrameNode& frameNode) override;
		virtual void ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator) override;
	private:
		CurveFace* m_CurveFace;
		MeshRenderData m_MeshRenderData;
		ShaderChannelMask m_Mask;
		MeshVertexFormat* m_MeshVertexFormat;
		dynamic_array<int> m_SubMeshIndex;
	};
}