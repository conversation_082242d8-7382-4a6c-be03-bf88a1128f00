﻿#include "SandboxRenderSetting.h"
#include "display/WorldRender.h"
#include "luaConstProxy/LuaInterfaceProxy.h"
#include "blocks/BlockMaterialMgr.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "IWorldConfigProxy.h"
#include "Event/MiniGlobalEvent.h"
#include "display/worlddisplay/SkyPlane.h"
#include "Render/RenderSettingConfigData.h"
#include "Graphics/LegacyGlobalShaderParam.h"
#include "Core/CallBacks/GlobalCallbacks.h"
#if PLATFORM_ANDROID || PLATFORM_OHOS
#include "Platforms/ChannelList.h"
#endif//OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
#include "SandboxConfig.h"

namespace Rainbow
{

	static SandboxGraphicSettings* s_SandboxGraphicSettings = nullptr;

	Vector4f g_GrassWind = Vector4f(0.0f, 0.0f, 1.0f, 1.0f);	//草顶点动画 xyz->风对3个轴的影响强度, w->整体强度
	Vector4f g_GrassWave = Vector4f(1.0f, 0.0f, 0.5f, 5.0f);//草顶点动画 xyz->3个轴摆动的幅度,w->整体面积大小

	static void DestroyDefaultSandboxRenderSettings(void* data)
	{
		s_SandboxGraphicSettings->Destroy();
		ENG_DELETE(s_SandboxGraphicSettings);
	}

	static void CreateDefaultSandboxRenderSettings(void* data) 
	{
		s_SandboxGraphicSettings = ENG_NEW(SandboxGraphicSettings)();
		s_SandboxGraphicSettings->Initialize();
	}

	RegisterRuntimeInitializeAndCleanup SandboxRenderSettingDataInitialize(CreateDefaultSandboxRenderSettings, DestroyDefaultSandboxRenderSettings);

	SandboxRenderSetting* GetSandboxRenderSettingPtr()
	{
		return &s_SandboxGraphicSettings->GetCurSetting();
	}

	SandboxRenderSetting& GetSandboxRenderSetting()
	{
		return s_SandboxGraphicSettings->GetCurSetting();

	}
	void SetSandboxGraphicQuality(int level, bool overrideCustomSetting)
	{
		if (level < 0 || level >= (int)GraphicsQuality::kGraphicsQualityCount) return;
		GraphicsQuality qualityLevel = (GraphicsQuality)level;
		GetGraphicsQualitySetting().SetGraphicsQuality(qualityLevel);
		SandboxRenderSetting& sandboxRenderSetting = GetSandboxRenderSetting();
		sandboxRenderSetting.AcceptRenderSetting(overrideCustomSetting);
	}

	void InitSandboxRenderSettings()
	{

	}

	SandboxGraphicSettings& GetSandboxGraphicSettings() 
	{
		Assert(s_SandboxGraphicSettings != nullptr);
		return *s_SandboxGraphicSettings;
	}


	GodrayParameter::GodrayParameter()
		:m_UseCustomSetting(false)
	{
		m_WaterConfig.m_BloomScale = 0.45f;
		m_WaterConfig.m_BloomThreshold = 0.5f;
		m_WaterConfig.m_BloomMaxBrighness = 1.0f;
		m_WaterConfig.m_BloomTint = ColorRGBAf(1.8f, 0.45f, 0, 1.0f);

		m_GroundConfig.m_BloomScale = 0.45f;
		m_GroundConfig.m_BloomThreshold = 0.0f;
		m_GroundConfig.m_BloomMaxBrighness = 1.0f;
		m_GroundConfig.m_BloomTint = ColorRGBAf(1.8f, 0.45f, 0, 1.0f);
		
		m_CustomConfig.m_BloomScale = 0.45f;
		m_CustomConfig.m_BloomThreshold = 0.5f;
		m_CustomConfig.m_BloomMaxBrighness = 1.0f;
		m_CustomConfig.m_BloomTint = ColorRGBAf(1.8f,0.45f,0,1.0f);

	}
	void GodrayParameter::AcceptConfig(PostprocessGodraySetting& setting, PostprocessGodraySetting& target)
	{
		target.m_BloomScale = setting.m_BloomScale;
		target.m_BloomThreshold = setting.m_BloomThreshold;
		target.m_BloomMaxBrighness = setting.m_BloomMaxBrighness;
		target.m_BloomTint = setting.m_BloomTint;
	}
	//void GodrayParameter::SetSky(bool isUgcCustom /* = false */)
	//{
	//	GodRaySetting& godraySetting = GetPostprocessSetting().m_GodRaySetting;
	//	// 高级创造模式自定义参数，这里不修改了
	//	if (!isUgcCustom)
	//	{
	//		godraySetting.m_GodrayBloomScale = 0.45;
	//	}
	//	godraySetting.m_GodrayBloomThreshold = 0.5f;
	//	godraySetting.m_GodrayBloomMaxBrighness = 1.0f;
	//	godraySetting.m_GodrayBloomTint = ColorRGBAf::white;
	//}

	//void GodrayParameter::SetWater()
	//{
	//	GodRaySetting& godraySetting = GetPostprocessSetting().m_GodRaySetting;
	//	godraySetting.m_GodrayBloomScale = 0.45f;
	//	godraySetting.m_GodrayBloomThreshold = 0.0f;
	//	godraySetting.m_GodrayBloomMaxBrighness = 1.0f;
	//	godraySetting.m_GodrayBloomTint = ColorRGBAf::white;
	//}

	//void GodrayParameter::SetCustom() 
	//{
	//	//GodRaySetting& godraySetting = GetPostprocessSetting().m_GodRaySetting;
	//	//godraySetting.m_GodrayBloomScale = m_BloomScale;
	//	//godraySetting.m_GodrayBloomThreshold = m_BloomThreshold;
	//	//godraySetting.m_GodrayBloomMaxBrighness = 1.0f;
	//	//godraySetting.m_GodrayBloomTint = m_BloomTint;
	//}


	SandboxRenderSettingData::SandboxRenderSettingData()
		: m_UseBilinearSampler(false)
		, m_KeepOriginTextureFormat(false)
		, m_UsePlaneSkybox(false)
		, m_FogEnable(false)
		, m_ShadowEnable(false)
		, m_WaterReflectEnable(false)
		, m_SkyboxLevel(kSkyboxLevelLow)
		, m_UseGrassAnimation(false)
		, m_UseGrassSnowCover(false)
		, m_GodrayEnable(false)
		, m_UseTerrainNoiseUV(false)
		, m_RainRipple(false)
		, m_BloomEnable(false)
		, m_DofEnable(false)
	{
	}






	SandboxRenderSetting::SandboxRenderSetting()
		: m_Data()
	{

	}

	void SandboxRenderSetting::Tick()
	{
		//tick godray enable
		float hours = GetWorldManagerPtr()->getHours();
		if (m_Data.m_GodrayEnable)
		{
			bool enable = CanShowGodray(hours);// CanShowShadow(hours);
			InnerSetGodrayEnable(enable, false);
		}
		//tick shadow enable
		bool enable = WorldRenderer::m_CurShadowOpen && GetGfxDevice().GetRenderer() != GfxDeviceRenderer::kGfxRendererOpenGLES20;
		if (enable) 
		{
			bool enableShadow = CanShowShadow(hours);// hours > 4.0f && hours < 20.0f;
			if (enableShadow != GetRenderSetting().GetEnableShadow())
			{
				SetShadowEnable(enableShadow, false);
			}
		}
	}

	void SandboxRenderSetting::AcceptRenderSetting(bool overrdeCustomSetting)
	{

		RenderSetting& renderSetting = GetRenderSetting();
		bool fogEnable = m_Data.m_FogEnable;//&& WorldRenderer::m_CurFogIndex != 0;
		bool shadowEnable = m_Data.m_ShadowEnable;//&& WorldRenderer::m_CurShadowOpen;
		bool reflectWater = m_Data.m_WaterReflectEnable;
		if (overrdeCustomSetting) 
		{
			WorldRenderer::m_CurFogIndex = fogEnable;
			WorldRenderer::m_CurShadowOpen = shadowEnable;
			GetIWorldConfigProxy()->setGameData("fog", WorldRenderer::m_CurFogIndex);
			GetIWorldConfigProxy()->setGameData("shadow", WorldRenderer::m_CurShadowOpen);
			GetIWorldConfigProxy()->setGameData("reflect", reflectWater);


		}
	
		// 高级创造天空开关开启时，雾效不被系统设置影响
		bool isUGCSky = false;
		WorldRenderer* worldRender = GetWorldRender();
		if (worldRender)
		{
			isUGCSky = worldRender->getIsUGCCustomSky();
		}
		if (!isUGCSky)
		{
			Rainbow::FogType fogType = fogEnable ? Rainbow::FogType::kFogTypeLinear : Rainbow::FogType::kFogTypeDisable;
			renderSetting.GetFogConfig().m_FogType = fogType;
		}

		bool isChange = SetShadowEnable(shadowEnable, false);

		if (!isChange) 
		{
			UpdateTerrianVertexLayout();
		}

		if (!isUGCSky)
		{
			GetISandboxActorSubsystem()->SetEffectEnable(reflectWater);
		}

		SetSkyboxLevel(m_Data.m_SkyboxLevel);

		SetGrassAnimationEnable(m_Data.m_UseGrassAnimation);
		SetGrassSnowCoverEnable(m_Data.m_UseGrassSnowCover);


		SetGodrayEnable(m_Data.m_GodrayEnable);

		SetAntialiasingEnable(m_Data.m_AntialiasingEnable);
	}

	bool SandboxRenderSetting::SetShadowEnable(bool value, bool showTips)
	{
		//if is opengles 2.0 can't open shadow effect
		GfxDeviceRenderer deviceRenderer = GetGfxDevice().GetRenderer();
		if (deviceRenderer == GfxDeviceRenderer::kGfxRendererOpenGLES20)
		{
			GetRenderSetting().SetEnableShadow(false);
			if (value && showTips)
			{
				GetLuaInterfaceProxy().showGameTips(55192);
			}
			return false;
		}
		bool isOpen = GetRenderSetting().GetEnableShadow();
		GetRenderSetting().SetEnableShadow(value);
		if (isOpen != value) 
		{
			Rainbow::GetMiniGlobalEvent().CallShadowSwitchCallback(isOpen);
			return true;
		}
		return false;
	}

	void SandboxRenderSetting::SetSkyboxLevel(SkyboxLevel level)
	{
		m_Data.m_SkyboxLevel = level;
		if (GetWorldManagerPtr())
		{
			unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
			World* world = GetWorldManagerPtr()->getWorld(mapid);
			WorldRenderer* worldRender = world != nullptr ? world->GetWorldRenderer() : nullptr;
			//Assert(worldRender != nullptr);
			//skybox
			if (worldRender != nullptr)
			{
				worldRender->getSky()->UpdateSkyboxLevel();
			}
		}
	}

	void SandboxRenderSetting::SetGodrayEnable(bool enable)
	{
		m_Data.m_GodrayEnable = enable;
		InnerSetGodrayEnable(m_Data.m_GodrayEnable);
	}

	void SandboxRenderSetting::SetCausticsEnable(bool enable)
	{
		bool preEnable = m_Data.m_UseTerrainNoiseUV;
		if (preEnable != enable) 
		{
			m_Data.m_UseTerrainNoiseUV = enable;
			UpdateTerrianVertexLayout();
		}
	}

	void SandboxRenderSetting::SetGrassSnowCoverEnable(bool enable)
	{
		m_Data.m_UseGrassSnowCover = enable;

		//vertex animation
		g_BlockMtlMgr.SetVertexSnowCoverEffectEnable(m_Data.m_UseGrassSnowCover);
	}

	void SandboxRenderSetting::SetGrassAnimationEnable(bool enable)
	{
		m_Data.m_UseGrassAnimation = enable;
		//vertex animation
		g_BlockMtlMgr.SetVertexAnimationEffectEnable(m_Data.m_UseGrassAnimation );
		if (GetWorldManagerPtr() == nullptr) return;
		unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
		World* world = GetWorldManagerPtr()->getWorld(mapid);
		WorldRenderer* worldRender = world != nullptr ? world->GetWorldRenderer() : nullptr;
		if (worldRender != nullptr) 
		{
			worldRender->UpdateHightlightBlockVertexAnimation();
		}

	}
	bool SandboxRenderSetting::CanShowShadow(float hour)
	{
		return true;
		//return CanShowShadowByTime(hour);
	}

	bool SandboxRenderSetting::IsNightMode(float hour)
	{
		return hour <= 5.0f || hour >= 19.0f;
	}

	bool SandboxRenderSetting::CanShowShadowByTime(float hour)
	{
		return hour > 5.0f && hour < 19.0f;
	}
	void SandboxRenderSetting::SetGrassAnimationPower(Vector4f wind, Vector4f wave)
	{
		g_GrassWind = wind;
		g_GrassWave = wave;
		g_BlockMtlMgr.SetVertexAnimationEffectEnable(m_Data.m_UseGrassAnimation);
	}

	void SandboxRenderSetting::GetGrassAnimationParams(Vector4f& wind, Vector4f& wave)
	{
		wind = g_GrassWind;
		wave = g_GrassWave;
	}

	void SandboxRenderSetting::SetRainRippleEnable(bool enable)
	{
		if (enable != m_Data.m_RainRipple)
		{
			//暂时注释
			//GetLegacyGlobalShaderParamManager()->LoadRainRippleTexture();
			//m_Data.m_RainRipple = enable;
			//UpdateTerrianVertexLayout();
		}
	}

	void SandboxRenderSetting::UpdateTerrianVertexLayout()
	{
		//Rainbow::GetMiniGlobalEvent().CallShadowSwitchCallback(false);
	}

	void SandboxRenderSetting::InnerSetGodrayEnable(bool value, bool check_hour)
	{
		WorldRenderer* worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			bool pass = true;
			if (check_hour)
			{
				float hours = GetWorldManagerPtr()->getHours();
				pass = !IsNightMode(hours);
			}

			//check other postprocess
			//bool openPostprocess = GetPostProcessingEnable();
			//worldRender->setPostporcessEnable(openPostprocess);
			bool isUgcPostEffect = worldRender->getIsUGCCustomPostEffect();
			// 高级创造打开自定义后处理，体积光不受标志位影响
			if (!isUgcPostEffect)
			{
				worldRender->setGodrayEnable((pass && value));
			}
		}
	}

	bool SandboxRenderSetting::CanShowGodray(float hour)
	{
		bool enable = !IsNightMode(hour);
		if (enable) 
		{
			//check skybox type
			unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
			World* world = GetWorldManagerPtr()->getWorld(mapid);
			WorldRenderer* worldRender = world != nullptr ? world->GetWorldRenderer() : nullptr;			
			SkyPlane* skyPlane = worldRender != nullptr ? worldRender->getSky() : nullptr;
			bool isUgcPostEffect = worldRender != nullptr ? worldRender->getIsUGCCustomPostEffect() : false;
			if (skyPlane != nullptr) 
			{
				bool ret = false;
				Planet planet = skyPlane->getPlanet();
				// 高级创造打开后处理，所有星球都可以显示godRay
				if (planet == PLANET_EARTH || planet == PLANET_VOLCANO || isUgcPostEffect)
				{
					//check weather type
					WeatherType weatherType = skyPlane->GetWeatherType();					
					if (weatherType == WEATHER_SUNSHINE || weatherType == UNDER_WATER || weatherType == VOLCANO)
					{
						WeatherType nextWeatherType = skyPlane->GetNextWeatherType();
						if (nextWeatherType == WEATHER_SUNSHINE || nextWeatherType == UNDER_WATER || nextWeatherType == VOLCANO) 
						{
							ret = true;
						} 
					}
					if (weatherType == WeatherType::CUSTOM) ret = true;
				}			
				if (ret) 
				{
					//check sun is display
					ret = skyPlane->SunIsDisplay();
				}
				return ret;
			}
		}

		return false;
	}

	WorldRenderer* SandboxRenderSetting::GetWorldRender()
	{
		if (GetWorldManagerPtr() == nullptr)
		{
			return nullptr;
		}
		unsigned short mapid = GetWorldManagerPtr()->m_RenderEyeMap;
		World* world = GetWorldManagerPtr()->getWorld(mapid);
		WorldRenderer* worldRender = world != nullptr ? world->GetWorldRenderer() : nullptr;
		//Assert(worldRender != nullptr);
		return worldRender;
	}

	bool SandboxRenderSetting::GetPostProcessingEnable()
	{
		return m_Data.m_DofEnable || m_Data.m_BloomEnable || m_Data.m_GodrayEnable;
	}



	void SandboxRenderSetting::SetBloomEnable(bool enable)
	{
		m_Data.m_BloomEnable = enable;
		InnerSetBloomEnable(m_Data.m_BloomEnable);
	}

	void SandboxRenderSetting::SetPostporcessEnable(bool enable)
	{
		auto worldRender = GetWorldRender();
		if (nullptr == worldRender)
		{
			return;
		}
		// 高级创造打开自定义后处理，后处理不受影响
		if (worldRender && !worldRender->getIsUGCCustomPostEffect())
		{
			worldRender->setPostporcessEnable(enable);
		}
	}

	void SandboxRenderSetting::SetLUTEnable(bool enable)
	{
		auto worldRender = GetWorldRender();
		if (worldRender && !worldRender->getIsUGCCustomPostEffect())
		{
			if (enable)
			{
				if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame())
				{
					worldRender->setLUTsEnable(true);
					worldRender->SetDefaultLUTTexture(nullptr);
				}
				else
				{
					if (worldRender->getCustomLUTEnable())
						worldRender->setLUTsEnable(true);
					else
						worldRender->setLUTsEnable(false);

					SharePtr<Texture2D> tonemap = GetAssetManager().LoadAsset<Texture2D>("luts/luttonemap.png");
					worldRender->SetDefaultLUTTexture(tonemap);
				}
			}
			else
			{
				worldRender->setLUTsEnable(false);
				worldRender->SetDefaultLUTTexture(nullptr);
			}
		}
	}

	void SandboxRenderSetting::InnerSetBloomEnable(bool value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			//check other postprocess
			//bool openPostprocess = GetPostProcessingEnable();
			//worldRender->setPostporcessEnable(openPostprocess);
			bool isUGCPostEffect = worldRender->getIsUGCCustomPostEffect();
			if (!isUGCPostEffect && worldRender->getBloomEnable() != value)
			{
				worldRender->setBloomEnable(value);
			}
		}
	}

	void SandboxRenderSetting::SetBloomIntensity(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setBloomIntensity(value);
		}
	}

	void SandboxRenderSetting::SetBloomThreadHold(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setBloomThreadhold(value);
		}
	}

	void SandboxRenderSetting::SetDofEnable(bool enable)
	{
		m_Data.m_DofEnable = enable;
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			//check other postprocess
			//bool openPostprocess = GetPostProcessingEnable();
			//worldRender->setPostporcessEnable(openPostprocess);
			if (worldRender->getDofEnable() != enable)
			{
				worldRender->setDofEnable(enable);
			}
		}
	}

	void SandboxRenderSetting::SetDofFocalRegion(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setDofFocalRegion(value);
		}
	}

	void SandboxRenderSetting::SetDofNearTransitionRegion(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setDofNearTransitionRegion(value);
		}
	}

	void SandboxRenderSetting::SetDofFarTransitionRegion(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setDofFarTransitionRegion(value);
		}
	}

	void SandboxRenderSetting::SetDofFocalDistance(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setDofFocalDistance(value);
		}
	}

	void SandboxRenderSetting::SetDofScale(float value)
	{
		auto worldRender = GetWorldRender();
		if (worldRender != nullptr)
		{
			worldRender->setDofScale(value);
		}
	}

	void SandboxRenderSetting::SetAntialiasingEnable(bool value)
	{
		WorldRenderer* worldRenderer = GetWorldRender();
		if (worldRenderer) 
		{
			worldRenderer->setAntialiasing(value);
		}
	}

	void SandboxRenderSetting::SetAntialiasingMethod(Rainbow::AntialiasingMethod method)
	{
		WorldRenderer* worldRenderer = GetWorldRender();
		if (worldRenderer)
		{
			worldRenderer->setAntialiasingMethod(method);
		}
	}

	bool SandboxRenderSetting::GetAntialiasingMethod(Rainbow::AntialiasingMethod& method)
	{
		WorldRenderer* worldRenderer = GetWorldRender();
		if (worldRenderer)
		{
			return worldRenderer->getAntialiasingMethod(method);
		}
		else
		{
			return false;
		}
	}

	void SandboxRenderSetting::SetAntialiasingQuality(Rainbow::AntialiasingQuality quality)
	{
		WorldRenderer* worldRenderer = GetWorldRender();
		if (worldRenderer)
		{
			worldRenderer->setAntialiasingQuality(quality);
		}
	}

	bool SandboxRenderSetting::GetAntialiasingQuality(Rainbow::AntialiasingQuality& quality)
	{
		WorldRenderer* worldRenderer = GetWorldRender();
		if (worldRenderer)
		{
			return worldRenderer->getAntialiasingQuality(quality);
		}
		else
		{
			return false;
		}
	}

	//void SandboxRenderSetting::SetGodRayScale(float value)
	//{
	//	if (GetWorldRender() != nullptr) GetPostprocessSetting().m_GodRaySetting.m_GodrayBloomScale = value;
	//}

	//void SandboxRenderSetting::SetGodRayThreshold(float value)
	//{
	//	if (GetWorldRender() != nullptr) GetPostprocessSetting().m_GodRaySetting.m_GodrayBloomThreshold = value;
	//}

	//void SandboxRenderSetting::SetGodRayTint(Rainbow::ColorRGBAf& value)
	//{
	//	if (GetWorldRender() != nullptr) GetPostprocessSetting().m_GodRaySetting.m_GodrayBloomTint = value;
	//}

	void SandboxRenderSetting::SetShadowDistance(float value)
	{
		if (GetWorldRender() != nullptr) GetRenderSetting().GetShadowConfig().m_ShadowDistance = value;
	}

	void SandboxRenderSetting::SetShadowNumCascade(unsigned int value)
	{
		if (GetWorldRender() != nullptr) GetRenderSetting().GetShadowConfig().m_NumCascade = value;
	}

	void SandboxRenderSetting::SetShadowTextureTileSize(UInt32 value)
	{
		if (GetWorldRender() != nullptr) GetRenderSetting().GetShadowConfig().m_TextureTileSize = value;
	}

	void SandboxRenderSetting::SetShadowDistributeExponent(float value)
	{
		if (GetWorldRender() != nullptr) GetRenderSetting().GetShadowConfig().m_DistributeExponent = value;
	}

	void SandboxRenderSetting::SetShadowFarScaleFactor(float value)
	{
		if (GetWorldRender() != nullptr) GetRenderSetting().GetShadowConfig().m_FarScaleFactor = value;
	}

	SandboxGraphicSettings::SandboxGraphicSettings()
	{
		SetDefaultGraphicsQualityMobile();
		SetDefaultGraphicsQualityPC();
	}
	
	void SandboxGraphicSettings::Initialize()
	{
		GlobalCallbacks::Get().m_PostGfxDeviceInitCallbacks.Register(OnPostGfxDeviceInit);
	}

	void SandboxGraphicSettings::Destroy()
	{
		GlobalCallbacks::Get().m_PostGfxDeviceInitCallbacks.Unregister(OnPostGfxDeviceInit);
	}	

	SandboxRenderSetting& SandboxGraphicSettings::GetCurSetting()
	{
		GraphicsQualitySetting& graphicsSetting = GetGraphicsQualitySetting();
		GraphicsQuality quality = graphicsSetting.GetGraphicsQuality();
		GraphicsPlatform platform = graphicsSetting.GetGraphicsPlatform();
		return m_RenderSettings[quality][platform];
	}
	
	
	void SandboxGraphicSettings::OnPostGfxDeviceInit()
	{

#if 0   

		//RAINBOW_ENGINE_VER < 4.6.7
		GetRenderSettingConfigDataPtr()->ApplyAllConfig();

#else

		//RAINBOW_ENGINE_VER >= 4.6.7
#   if !RAINBOW_SERVER
		GlobalCallbacks::Get().m_BeginPlayCallbacks.Register([]() {
			RenderSettingConfigData renderSettingConfigData(kMemDefault);
			renderSettingConfigData.ResetToDefaultConfigData();
			renderSettingConfigData.Apply();
#       if PLATFORM_WIN
			// GetRenderSettingConfigDataPtr()->SetCurrentGraphicPlatform(GraphicsPlatform::kGraphicsPlatformPC);
			GetGraphicsQualitySetting().SetGraphicsPlatform(GraphicsPlatform::kGraphicsPlatformPC);
			//GetRenderSettingConfigDataPtr()->SetCurrentGraphicPlatform(GraphicsPlatform::kGraphicsPlatformMobile);
			//GetGraphicsQualitySetting().SetGraphicsPlatform(GraphicsPlatform::kGraphicsPlatformMobile);
#       else
			// GetRenderSettingConfigDataPtr()->SetCurrentGraphicPlatform(GraphicsPlatform::kGraphicsPlatformMobile);
			GetGraphicsQualitySetting().SetGraphicsPlatform(GraphicsPlatform::kGraphicsPlatformMobile);
#       endif

			//special handing ios platform, close dynamic resolution and draw ui on rendertexture
			OnInitGraphicsQualitySetting();
	    });
#   endif // RAINBOW_SERVER

#endif
	}

	void SandboxGraphicSettings::OnInitGraphicsQualitySetting()
	{
		GraphicsQualitySetting& graphicsSetting = GetGraphicsQualitySetting();
		for (size_t qualityIdx = 0; qualityIdx < kGraphicsQualityCount; qualityIdx++)
		{
			for (size_t platformIdx = 0; platformIdx < kGraphicsPlatformCount; platformIdx++)
			{
				RenderSetting& renderSetting = graphicsSetting.GetRenderSetting((GraphicsQuality)qualityIdx, (GraphicsPlatform)platformIdx);
				GraphicsQuality quality = (GraphicsQuality)qualityIdx;
				if (quality == kGraphicsQualityLow)
				{
					renderSetting.SetDynamicResolutionFactor(0.0f);
				}
				else if (quality == kGraphicsQualityMedium)
				{
					renderSetting.SetDynamicResolutionFactor(0.2f);
				}
				else if (quality == kGraphicsQualityHigh)
				{
					renderSetting.SetDynamicResolutionFactor(0.5f);
				}
#if PLATFORM_IOS
				renderSetting.SetAllowDynamicResolution(true);
				renderSetting.SetUIUsePhysicsSize(false);
#elif PLATFORM_ANDROID || PLATFORM_OHOS
				renderSetting.SetAllowDynamicResolution(true);
#endif
			}
		}
	}

	void SandboxGraphicSettings::OnRestoreGraphicsQualitySetting()
	{
		OnInitGraphicsQualitySetting();
	}

	void SandboxGraphicSettings::SetGodrayParams(PPtr<PostprocessGodray> postprocessGodray, bool onWater)
	{
		PostprocessGodraySetting& targetSetting = postprocessGodray->m_Setting;
		if (m_GodrayParameter.m_UseCustomSetting) 
		{
			m_GodrayParameter.AcceptConfig(m_GodrayParameter.m_CustomConfig, targetSetting);
		}
		else 
		{
			if (onWater) m_GodrayParameter.AcceptConfig(m_GodrayParameter.m_WaterConfig, targetSetting);
			else m_GodrayParameter.AcceptConfig(m_GodrayParameter.m_GroundConfig, targetSetting);
		}
	}

	void SandboxGraphicSettings::SetGodrayParamsUGC(PPtr<PostprocessGodray> postprocessGodray, const ColorRGBAf &color)
	{
		PostprocessGodraySetting& targetSetting = postprocessGodray->m_Setting;
		PostprocessGodraySetting tempSetting = m_GodrayParameter.m_GroundConfig;
		tempSetting.m_BloomScale = targetSetting.m_BloomScale;
		tempSetting.m_BloomTint = color;
		m_GodrayParameter.AcceptConfig(tempSetting, targetSetting);
	}

	void SandboxGraphicSettings::SetGodRayScale(float value)
	{
		m_GodrayParameter.m_CustomConfig.m_BloomScale = value;
	}

	void SandboxGraphicSettings::SetGodRayThreshold(float value)
	{
		m_GodrayParameter.m_CustomConfig.m_BloomThreshold = value;
	}

	void SandboxGraphicSettings::SetGodRayTint(const Rainbow::ColorRGBAf& value)
	{
		m_GodrayParameter.m_CustomConfig.m_BloomTint = value;
	}

	void SandboxGraphicSettings::UseCustomGodrayParams(bool value) 
	{
		m_GodrayParameter.m_UseCustomSetting = value;
	}

	void SandboxGraphicSettings::SetDefaultGraphicsQualityMobile()
	{
			{
				SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityLow][kGraphicsPlatformMobile];
				renderSetting.m_Data.m_UseBilinearSampler = false;
				renderSetting.m_Data.m_KeepOriginTextureFormat = false;
				renderSetting.m_Data.m_FogEnable = false;
				renderSetting.m_Data.m_ShadowEnable = false;
				renderSetting.m_Data.m_WaterReflectEnable = false;
				renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelLow;
				renderSetting.m_Data.m_UseGrassAnimation = false;
				renderSetting.m_Data.m_GodrayEnable = false;
				renderSetting.m_Data.m_UseTerrainNoiseUV = false;
				renderSetting.m_Data.m_AntialiasingEnable = false;

			}
			{
				SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityMedium][kGraphicsPlatformMobile];
				renderSetting.m_Data.m_UseBilinearSampler = false;
				renderSetting.m_Data.m_KeepOriginTextureFormat = false;
				renderSetting.m_Data.m_FogEnable = true;
				renderSetting.m_Data.m_ShadowEnable = true;
				renderSetting.m_Data.m_WaterReflectEnable = false;
				renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelMedium;
				renderSetting.m_Data.m_UseGrassAnimation = true;
				renderSetting.m_Data.m_GodrayEnable = false;
				renderSetting.m_Data.m_UseTerrainNoiseUV = false;
				renderSetting.m_Data.m_AntialiasingEnable = false;
			}
			{
				SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityHigh][kGraphicsPlatformMobile];
				renderSetting.m_Data.m_UseBilinearSampler = false;
				renderSetting.m_Data.m_KeepOriginTextureFormat = true;
				renderSetting.m_Data.m_FogEnable = true;
				renderSetting.m_Data.m_ShadowEnable = true;
				renderSetting.m_Data.m_WaterReflectEnable = true;
				renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelHigh;
				renderSetting.m_Data.m_UseGrassAnimation = true;
				renderSetting.m_Data.m_GodrayEnable = true;
				renderSetting.m_Data.m_UseTerrainNoiseUV = true;
				renderSetting.m_Data.m_AntialiasingEnable = true;

			}
			{
				SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityUltra][kGraphicsPlatformMobile];
				renderSetting.m_Data.m_UseBilinearSampler = false;
				renderSetting.m_Data.m_KeepOriginTextureFormat = true;
				renderSetting.m_Data.m_FogEnable = true;
				renderSetting.m_Data.m_ShadowEnable = true;
				renderSetting.m_Data.m_WaterReflectEnable = true;
				renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelHigh;
				renderSetting.m_Data.m_UseGrassAnimation = true;
				renderSetting.m_Data.m_GodrayEnable = true;
				renderSetting.m_Data.m_UseTerrainNoiseUV = true;
				renderSetting.m_Data.m_AntialiasingEnable = true;
			}
	}

	void SandboxGraphicSettings::SetDefaultGraphicsQualityPC()
	{
		{
			SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityLow][kGraphicsPlatformPC];
			renderSetting.m_Data.m_UseBilinearSampler = false;
			renderSetting.m_Data.m_KeepOriginTextureFormat = false;

			renderSetting.m_Data.m_FogEnable = false;
			renderSetting.m_Data.m_ShadowEnable = false;
			renderSetting.m_Data.m_WaterReflectEnable = false;
			renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelLow;
			renderSetting.m_Data.m_UseGrassAnimation = false;
			renderSetting.m_Data.m_GodrayEnable = false;
			renderSetting.m_Data.m_UseTerrainNoiseUV = false;
			renderSetting.m_Data.m_AntialiasingEnable = false;

		}
		{
			SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityMedium][kGraphicsPlatformPC];
			renderSetting.m_Data.m_UseBilinearSampler = false;
			renderSetting.m_Data.m_KeepOriginTextureFormat = false;

			renderSetting.m_Data.m_FogEnable = true;
			renderSetting.m_Data.m_ShadowEnable = true;
			renderSetting.m_Data.m_WaterReflectEnable = false;
			renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelMedium;
			renderSetting.m_Data.m_UseGrassAnimation = true;
			renderSetting.m_Data.m_GodrayEnable = false;
			renderSetting.m_Data.m_UseTerrainNoiseUV = false;
			renderSetting.m_Data.m_AntialiasingEnable = true;

		}
		{
			SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityHigh][kGraphicsPlatformPC];
			renderSetting.m_Data.m_UseBilinearSampler = false;
			renderSetting.m_Data.m_KeepOriginTextureFormat = true;

			renderSetting.m_Data.m_FogEnable = true;
			renderSetting.m_Data.m_ShadowEnable = true;
			renderSetting.m_Data.m_WaterReflectEnable = true;
			renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelHigh;
			renderSetting.m_Data.m_UseGrassAnimation = true;
			renderSetting.m_Data.m_GodrayEnable = true;
			renderSetting.m_Data.m_UseTerrainNoiseUV = true;
			renderSetting.m_Data.m_AntialiasingEnable = true;

		}
		{
			SandboxRenderSetting& renderSetting = m_RenderSettings[kGraphicsQualityUltra][kGraphicsPlatformPC];
			renderSetting.m_Data.m_UseBilinearSampler = false;
			renderSetting.m_Data.m_KeepOriginTextureFormat = true;

			renderSetting.m_Data.m_FogEnable = true;
			renderSetting.m_Data.m_ShadowEnable = true;
			renderSetting.m_Data.m_WaterReflectEnable = true;
			renderSetting.m_Data.m_SkyboxLevel = kSkyboxLevelHigh;
			renderSetting.m_Data.m_UseGrassAnimation = true;
			renderSetting.m_Data.m_GodrayEnable = true;
			renderSetting.m_Data.m_UseTerrainNoiseUV = true;
			renderSetting.m_Data.m_AntialiasingEnable = true;

		}

	}





}