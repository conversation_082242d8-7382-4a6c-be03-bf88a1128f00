
#ifndef __EFFECT_DESTROYBLOCK_H__
#define __EFFECT_DESTROYBLOCK_H__

#include "BaseClass/PPtr.h"
#include "BaseEffect.h"
#include "world_types.h"
#include "OgreWCoord.h"
#include "SandboxGame.h"
class World;

namespace Rainbow
{
	class ParticleNode;
}

class EXPORT_SANDBOXGAME EffectDestroyBlock;
class EffectDestroyBlock : public Rainbow::BaseEffect //tolua_exports
{ //tolua_exports
    protected:
    friend class Rainbow::EffectDeleteProxy;
    virtual ~EffectDestroyBlock();
public:
	//tolua_begin
	EffectDestroyBlock(World *pworld, int subtype, const WCoord &pos, DirectionType dir, int age, int id);

	virtual void tick();
	virtual void update(float dtime);
	void SetPosition(const WCoord& pos);
	Rainbow::AABB GetAABB() override;
	//tolua_end
private:
	WCoord m_Pos;
	DirectionType m_Face;
	int m_LiveTick;
	int m_Age;

	Rainbow::PPtr<Rainbow::ParticleNode> m_PtNode;
}; //tolua_exports

class BlockDecalMesh;
class EffectCrackBlock : public Rainbow::BaseEffect //tolua_exports
{ //tolua_exports
    protected:
    friend class Rainbow::EffectDeleteProxy;
    virtual ~EffectCrackBlock();
public:
	//tolua_begin
	EffectCrackBlock(World *pworld, const WCoord &blockpos, const char* texture);

	virtual void tick();
	virtual void update(float dtime);
	Rainbow::AABB GetAABB() override;
	void setCrackProcess(int process, WORLD_ID actorid);
	int getCrackProcess();
	void setLiveTime(int count) { m_nLiveTime = count; }
	//tolua_end
private:
	World *m_World;
	WCoord m_BlockPos;
	Rainbow::PPtr<BlockDecalMesh> m_Decal;
	int m_CurBlockID;
	int m_nLiveTime = -1;

	struct ActorCrackData
	{
		WORLD_ID actorid;
		int process;
	};
	std::vector<ActorCrackData>m_ActorCracks;
}; //tolua_exports

#endif