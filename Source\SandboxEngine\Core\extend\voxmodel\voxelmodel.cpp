﻿
#include "voxelmodel.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "blocks/special_blockid.h"
#include "WorldProxy.h"

#include "blocks/BlockMaterialMgr.h"
#include "blueprint.h"
#include "ClientActorHelper.h"
#include "chunk.h"
using namespace MINIW;
using namespace Rainbow;

#define VOXELMODEL_DEBUG_LOG 1
#if VOXELMODEL_DEBUG_LOG
#define VOXELMODEL_LOG(...) \
    do { \
        WarningStringMsg("[VoxelModel] " __VA_ARGS__); \
    } while(0)
#else
#define VOXELMODEL_LOG(...)
#endif

VoxelModel::VoxelModel() : m_VoxelData(NULL),m_containersData(NULL), m_DimX(0), m_DimY(0), m_DimZ(0)
{
}

VoxelModel::~VoxelModel()
{
    if(m_VoxelData) free(m_VoxelData);
    if(m_containersData){ 
        free(m_containersData);
        m_containersData = NULL;
    }
}

struct VoxelFileHeader
{
    char magicnum[4];
    int version;
};

class FileChunk
{
public:
    FileChunk() : m_ChunkID(0), m_ChunkData(NULL)
    {

    }

    ~FileChunk()
    {
        if(m_ChunkData) free(m_ChunkData);

        for(size_t i=0; i<m_Children.size(); i++)
        {
            ENG_DELETE(m_Children[i]);
        }
    }

    int loadFromFile(AutoRefPtr<DataStream> fp);
    FileChunk *getChild(unsigned int chunkid);

public:
    unsigned int m_ChunkID;
    void *m_ChunkData;

    std::vector<FileChunk *>m_Children;
};

unsigned int CHUNKID(unsigned int c1, unsigned int c2, unsigned int c3, unsigned int c4)
{
    return (c1<<0) | (c2<<8) | (c3<<16) | (c4<<24);
}

int FileChunk::loadFromFile(AutoRefPtr<DataStream> fp)
{
    fp->Read(&m_ChunkID, sizeof(int));

    char c4 = m_ChunkID>>24;
    char c3 = (m_ChunkID>>16)&0xff;
    char c2 = (m_ChunkID>>8)&0xff;
    char c1 = (m_ChunkID>>0)&0xff;

    assert(CHUNKID(c1,c2,c3,c4) == m_ChunkID);

    int chunksize, children_size;
    fp->Read(&chunksize, sizeof(int));
    fp->Read(&children_size, sizeof(int));

    if(chunksize > 0)
    {
        m_ChunkData = malloc(chunksize);
        fp->Read(m_ChunkData, chunksize);
    }

    while(children_size > 0)
    {
        FileChunk *pchild = ENG_NEW(FileChunk)();
        int childsize = pchild->loadFromFile(fp);

        m_Children.push_back(pchild);

        children_size -= childsize;
    }

    return chunksize+children_size+12;
}

FileChunk *FileChunk::getChild(unsigned int chunkid)
{
    for(size_t i=0; i<m_Children.size(); i++)
    {
        if(m_Children[i]->m_ChunkID == chunkid) return m_Children[i];
    }
    return NULL;
}

struct OneVoxel
{
    unsigned char x;
    unsigned char y;
    unsigned char z;
    unsigned char c;
};

bool VoxelModel::loadVoxelFile(const char *path, int blockid)
{
    VOXELMODEL_LOG("loadVoxelFile, path:%s, blockid:%d", path, blockid);
    AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path);
    if(!fp)
    {
        LOG_INFO("failed to open: %s", path);
        return false;
    }

    VoxelFileHeader header;
    fp->Read(&header, sizeof(header));
    if(header.magicnum[0]!='V' || header.magicnum[1]!='O' || header.magicnum[2]!='X' || header.magicnum[3]!=' ')
    {
        LOG_INFO("wrong voxel magic number: %s", path);
        return false;
    }

    FileChunk *rootchunk = ENG_NEW( FileChunk );
    rootchunk->loadFromFile(fp);

    int *xyzdim = (int *)rootchunk->getChild(CHUNKID('S','I','Z','E'))->m_ChunkData;
    m_DimX = xyzdim[0];
    m_DimY = xyzdim[2];
    m_DimZ = xyzdim[1];

    unsigned int *voxeldata = (unsigned int *)rootchunk->getChild(CHUNKID('X','Y','Z','I'))->m_ChunkData;
    unsigned int nvoxel = voxeldata[0];

	m_VoxelData = (BLOCK_DATA_TYPE*)malloc(m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));
	memset(m_VoxelData, 0, m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));

    VoxelPalette *palette = NULL;
    if (blockid <= 0)
    {
        palette = GetDefManagerProxy()->getVoxlPalette(-blockid);
        if (palette == NULL) return false;
    }

    for(unsigned int i=0; i<nvoxel; i++)
    {
        OneVoxel v;
        *(unsigned int *)&v = voxeldata[i+1];

        int index = vox_xyz2Index(v.x, v.z, v.y);
        if (palette) blockid = palette->BlockID[v.c - 1];
        m_VoxelData[index] = Block::makeBlockData(blockid, 0);
    }

    for(int y=1; y<m_DimY; y++)
    {
        for(int z=0; z<m_DimZ; z++)
        {
            for(int x=0; x<m_DimX; x++)
            {
                BLOCK_DATA_TYPE&curblock = m_VoxelData[vox_xyz2Index(x,y,z)];
                int curid = Block::toResID(curblock);
                if(IsDoorBlock(curid))
                {
                    BLOCK_DATA_TYPE downblock = m_VoxelData[vox_xyz2Index(x,y-1,z)];
                    if(Block::toResID(downblock) == curid)
                    {
                        curblock = Block::makeBlockData(curid, 1<<2);
                    }
                }
            }
        }
    }

    ENG_DELETE(rootchunk);

    return true;
}

struct VMOFileHeader
{
    char magicnum[4];
    int version;
    int sizex;
    int sizey;
    int sizez;
};

bool VoxelModel::loadVMOFile(const char *path)
{
    VOXELMODEL_LOG("loadVMOFile, path:%s", path);
    AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path, true);
    if(!fp)
    {
        LOG_INFO("failed to open: %s", path);
        return false;
    }

    VMOFileHeader header;
    fp->Read(&header, sizeof(header));
    if(header.magicnum[0]!='V' || header.magicnum[1]!='M' || header.magicnum[2]!='O' || header.magicnum[3]!='F')
    {
        LOG_INFO("wrong voxel magic number: %s", path);
        return false;
    }

    m_DimX = header.sizex;
    m_DimY = header.sizey;
    m_DimZ = header.sizez;

	m_VoxelData = (BLOCK_DATA_TYPE*)malloc(m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));
	if (header.version == 101 || header.version == 201)
	{
		fp->Read(m_VoxelData, m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));
	}
	else
	{
		unsigned short* pVoxelData = (unsigned short*)malloc(m_DimX * m_DimY * m_DimZ * 2);
		fp->Read(pVoxelData, m_DimX * m_DimY * m_DimZ * 2);

		for (int i = 0; i < m_DimX * m_DimY * m_DimZ; i++)
		{
			m_VoxelData[i] = Block::changeBlockNewFormatData(pVoxelData[i], 0);
		}
		free(pVoxelData);
	}
    //
    if(header.version >= 200){
        //v200扩展 容器信息
        int containersDataSize;
        if(!fp->Read(&containersDataSize, sizeof(int))){
            return false;
        }
        void *buf = malloc(containersDataSize);
        if(buf == NULL){ 
            return false;
        }
        if(!fp->Read(buf, containersDataSize))
        {
            free(buf);
            return false;
        }
        //
        flatbuffers::Verifier verifier((const uint8_t *)buf, containersDataSize);
        if (!FBSave::VerifyBluePrintBuffer(verifier))
        {
            free(buf);
            return false;
        }

        const FBSave::BluePrint *blueprint = FBSave::GetBluePrint(buf);
        if (blueprint == NULL)
        {
            free(buf);
            return false;
        }

        m_containersData = buf;
        return true;
    }
    return true;
}

bool VoxelModel::saveVMOFile(const char *path)
{
    VMOFileHeader header;
    header.magicnum[0] = 'V';
    header.magicnum[1] = 'M';
    header.magicnum[2] = 'O';
    header.magicnum[3] = 'F';
    header.version = 101;
    header.sizex = m_DimX;
    header.sizey = m_DimY;
    header.sizez = m_DimZ;

	std::stringstream buf;
	buf.write((const char*)&header, sizeof(header));
	buf.write((const char*)m_VoxelData, m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));
	std::string data = buf.str();
	bool succeed = GetFileManager().SaveToWritePath(path, data.data(), data.size(), true);
    VOXELMODEL_LOG("saveVMOFile, path:%s, succeed:%d", path, succeed);

    return succeed;
}

void VoxelModel::placeInWorld(WorldProxy *pworld, const WCoord &pos, bool completely, int placedir)
{
	if (!pworld)
		return;

    VOXELMODEL_LOG("placeInWorld, pos:%d, %d, %d, completely:%d, placedir:%d", pos.x, pos.y, pos.z, completely, placedir);

    for (int y = 0; y < m_DimY; y++)
    {
        if (y + pos.y >= CHUNK_BLOCK_Y) break;

        for (int z = 0; z < m_DimZ; z++)
        {
            for (int x = 0; x<m_DimX; x++)
            {
                BLOCK_DATA_TYPE c = m_VoxelData[(y*m_DimZ + z)*m_DimX + x];
                WCoord blockpos = pos;			

                if (placedir == DIR_NEG_Z) blockpos += WCoord(x, y, z);
                else if (placedir == DIR_POS_Z) blockpos += WCoord(-x, y, -z);
                else if (placedir == DIR_NEG_X) blockpos += WCoord(z, y, -x);
                else blockpos += WCoord(-z, y, x);

				//chunk没加载出来时，加载一下，避免生成的模型被截断
				/*if (pworld->getWorld()->getChunk(blockpos) == NULL)
				{
					pworld->getWorld()->syncLoadChunk(blockpos, 1);
				}*/
				
				int oldBlockID = pworld->getBlockID(blockpos);
				int oldBlockData = pworld->getBlockData(blockpos);

                if (c > 0) {
                    pworld->setBlockAll(blockpos, Block::toResID(c), Block::toBlockData(c), 2);
                }
                else if (completely) {
                    pworld->setBlockAll(blockpos, 0, 0, 2);
                }
				// undo,redo
				//string jimuID = "action_loadVoxel";
				//UndoOperationManager::getInstance()->saveJimuRunOperationSaveSubOper(
				//	new UndoOperationSetBlock(
				//	blockpos, oldBlockID, oldBlockData,
				//	pworld->getBlockID(blockpos), pworld->getBlockData(blockpos)), jimuID);
            }
        }
    }

	if (m_containersData)
	{
		const FBSave::BluePrint* blueprint = FBSave::GetBluePrint(m_containersData);
		auto* bpContainers = blueprint->containers();
		for (size_t icon = 0; icon < bpContainers->size(); icon++)
		{
			auto* bpCon = bpContainers->Get(icon);
			auto* con = bpCon->container();
			WorldContainer* obj = CreateWorldContainerFromChunkContainer(con);
			if (obj)
			{
				WCoord relativePos = Coord3ToWCoord(bpCon->relativepos());
				int x = relativePos.x;
				int y = relativePos.y;
				int z = relativePos.z;

				WCoord blockpos = pos;

				if (placedir == DIR_NEG_Z) blockpos += WCoord(x, y, z);
				else if (placedir == DIR_POS_Z) blockpos += WCoord(-x, y, -z);
				else if (placedir == DIR_NEG_X) blockpos += WCoord(z, y, -x);
				else blockpos += WCoord(-z, y, x);

				auto* pChunk = pworld->getWorld()->getChunk(blockpos);
				if (pChunk && obj->load(con->container()) && obj->canAddToChunk(pChunk))
				{
					obj->m_BlockPos = blockpos;
					auto* containerMgr = pworld->getWorld()->getContainerMgr();
					if (containerMgr)
					{
						containerMgr->spawnContainer(obj);
					}
				}
				else
				{
					OGRE_DELETE(obj);
				}
			}
		}
	}
}

void VoxelModel::placeInWorld(World *pworld, const WCoord &pos, bool completely, int placedir)
{
    MainWorldProxy proxy(pworld);
    placeInWorld(&proxy, pos, completely, placedir);
}

void VoxelModel::removeInWorld(WorldProxy *pworld, const WCoord &pos, int placedir)
{
    VOXELMODEL_LOG("removeInWorld, pos:%d, %d, %d, placedir:%d", pos.x, pos.y, pos.z, placedir);

    for (int y = 0; y < m_DimY; y++)
    {
        if (y + pos.y >= CHUNK_BLOCK_Y) break;

        for (int z = 0; z < m_DimZ; z++)
        {
            for (int x = 0; x<m_DimX; x++)
            {
                WCoord blockpos = pos;
                if (placedir == DIR_NEG_Z) blockpos += WCoord(x, y, z);
                else if (placedir == DIR_POS_Z) blockpos += WCoord(-x, y, -z);
                else if (placedir == DIR_NEG_X) blockpos += WCoord(z, y, -x);
                else blockpos += WCoord(-z, y, x);

                pworld->setBlockAll(blockpos, 0, 0, 2);
            }
        }
    }
}

void VoxelModel::removeInWorld(World *pworld, const WCoord &pos, int placedir)
{
    MainWorldProxy proxy(pworld);
    removeInWorld(&proxy, pos, placedir);
}

bool VoxelModel::captureFromWorldAndSaveV2(World *pworld, const WCoord &begin, const WCoord &end,const char *path)
{
	if (m_VoxelData)
	{
		free(m_VoxelData);
		m_VoxelData = NULL;
	}
	if (m_containersData)
	{
		free(m_containersData);
		m_containersData = NULL;
	}

    int x0, x1, y0, y1, z0, z1;
    if(begin.x > end.x){x0 = end.x;	x1 = begin.x;}
    else{x0 = begin.x; x1 = end.x;}

    if(begin.y > end.y){y0 = end.y;	y1 = begin.y;}
    else{y0 = begin.y; y1 = end.y;}

    if(begin.z > end.z){z0 = end.z;	z1 = begin.z;}
    else{z0 = begin.z; z1 = end.z;}

    m_DimX = x1 - x0 + 1;
    m_DimY = y1 - y0 + 1;
    m_DimZ = z1 - z0 + 1;

	m_VoxelData = (BLOCK_DATA_TYPE*)malloc(m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));

    std::vector<BluePrintContainer> Containers;
    for(int y=y0; y<=y1; y++)
    {
        for(int x=x0; x<=x1; x++)
        {
            for(int z=z0; z<=z1; z++)
            {
                const WCoord &pos = WCoord(x,y,z);
                Block pblock = pworld->getBlock(pos);
                m_VoxelData[vox_xyz2Index(x-x0, y-y0, z-z0)] = pblock.getAll();

//                 int blockid = pblock.getResID();
                if (g_BlockMtlMgr.getMaterial(pblock.getResID())->hasContainer()){
                    WorldContainer *srcContainer = pworld->getContainerMgr()->getContainer(pos);
                    if(srcContainer == NULL) continue;

                    BluePrintContainer container;
                    container.relativepos = WCoord(x - x0, y - y0, z - z0);
                    container.container = srcContainer;
                    Containers.push_back(container);
                }
            }
        }
    }

    flatbuffers::FlatBufferBuilder builder;
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BPChunkContainer>>> containersoffset = 0;
    std::vector<flatbuffers::Offset<FBSave::BPChunkContainer>> containers;
    auto iter2 = Containers.begin();
    for (; iter2 != Containers.end(); iter2++)
    {
        auto relativePos = WCoordToCoord3(iter2->relativepos);
        flatbuffers::Offset<FBSave::ChunkContainer> container;

        if (!iter2->container->getNeedClear())
        {
            container = iter2->container->save(builder);
            containers.push_back(FBSave::CreateBPChunkContainer(builder, container, &relativePos));
        }	
    }
    containersoffset = builder.CreateVector(containers);
    builder.Finish(CreateBluePrint(builder, 0, 0, containersoffset));

    VMOFileHeader header;
    header.magicnum[0] = 'V';
    header.magicnum[1] = 'M';
    header.magicnum[2] = 'O';
    header.magicnum[3] = 'F';
    header.version = 201;//200  v2
    header.sizex = m_DimX;
    header.sizey = m_DimY;
    header.sizez = m_DimZ;

	std::stringstream buf;
	buf.write((const char*)&header, sizeof(header));
	buf.write((const char*)m_VoxelData, m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));
	int size = builder.GetSize();
	buf.write((const char*)&size, sizeof(size));
	buf.write((const char*)builder.GetBufferPointer(), size);
	std::string data = buf.str();
	bool succeed = GetFileManager().SaveToWritePath(path, data.data(), data.size(), true);

	return succeed;
}

void VoxelModel::captureFromWorld(World *pworld, const WCoord &begin, const WCoord &end)
{
    if(m_VoxelData) free(m_VoxelData);

    int x0, x1, y0, y1, z0, z1;
    if(begin.x > end.x){x0 = end.x;	x1 = begin.x;}
    else{x0 = begin.x; x1 = end.x;}

    if(begin.y > end.y){y0 = end.y;	y1 = begin.y;}
    else{y0 = begin.y; y1 = end.y;}

    if(begin.z > end.z){z0 = end.z;	z1 = begin.z;}
    else{z0 = begin.z; z1 = end.z;}

    m_DimX = x1 - x0 + 1;
    m_DimY = y1 - y0 + 1;
    m_DimZ = z1 - z0 + 1;

	m_VoxelData = (BLOCK_DATA_TYPE*)malloc(m_DimX * m_DimY * m_DimZ * sizeof(BLOCK_DATA_TYPE));

    for(int y=y0; y<=y1; y++)
    {
        for(int x=x0; x<=x1; x++)
        {
            for(int z=z0; z<=z1; z++)
            {
                Block pblock = pworld->getBlock(WCoord(x,y,z));
                m_VoxelData[vox_xyz2Index(x-x0, y-y0, z-z0)] = pblock.getAll();
            }
        }
    }
}
namespace Voxel {

    struct RGBA {
        RGBA();
        RGBA(uint32_t color);
        RGBA(uint32_t* color);
        RGBA(uint8_t a, uint8_t r, uint8_t g, uint8_t b);


        uint32_t pack() const;
        void unpack(uint32_t color);

        uint8_t r, g, b, a;
    };

    RGBA::RGBA() {
        a = 0;
        r = 0;
        g = 0;
        b = 0;
    }

    RGBA::RGBA(uint32_t color) {
        unpack(color);
    }

    RGBA::RGBA(uint32_t* color) {
        unpack(*color);
    }

    RGBA::RGBA(uint8_t a, uint8_t r, uint8_t g, uint8_t b)
        : a(a), r(r), g(g), b(b) {}


    uint32_t RGBA::pack() const {
        uint32_t ret = (a << 24) | (r << 16) | (g << 8) | b;
        return ret;
    }

    void RGBA::unpack(uint32_t color) {
        a = (color & 0xFF000000) >> 24;
        r = (color & 0x00FF0000) >> 16;
        g = (color & 0x0000FF00) >> 8;
        b = color & 0x000000FF;
    }

    template<typename... C>
    static inline std::vector<RGBA> makePalette(C... colors) {
        return std::vector<RGBA>{ (RGBA(colors))... };
    }

    std::vector<RGBA> blockPalette = makePalette(

        0xffffcdd2, 0xfff8bbd0, 0xffe1bee7, 0xffd1c4e9, 0xffc5cae9, 0xffbbdefb, 0xffb3e5fc, 0xffb2ebf2, 0xffb2dfdb, 0xffc8e6c9, 0xffdcedc8, 0xfff0f4c3, 0xfffff9c4, 0xffffecb3, 0xffffe0b2, 0xffffccbc, 0xff000000, 0xff000000,
        0xffef9a9a, 0xfff48fb1, 0xffce93d8, 0xffb39ddb, 0xff9fa8da, 0xff90caf9, 0xff81d4fa, 0xff80deea, 0xff80cbc4, 0xffa5d6a7, 0xffc5e1a5, 0xffe6ee9c, 0xfffff59d, 0xffffe082, 0xffffcc80, 0xffffab91, 0xffd7ccc8, 0xffffffff,
        0xffe57373, 0xfff06292, 0xffba68c8, 0xff9575cd, 0xff7986cb, 0xff64b5f6, 0xff4fc3f7, 0xff4dd0e1, 0xff4db6ac, 0xff81c784, 0xffaed581, 0xffdce775, 0xfffff176, 0xffffd54f, 0xffffb74d, 0xffff8a65, 0xffbcaaa4, 0xffeeeeee,
        0xffef5350, 0xffec407a, 0xffab47bc, 0xff7e57c2, 0xff5c6bc0, 0xff42a5f5, 0xff29b6f6, 0xff26c6da, 0xff26a69a, 0xff66bb6a, 0xff9ccc65, 0xffd4e157, 0xffffee58, 0xffffca28, 0xffffa726, 0xffff7043, 0xffa1887f, 0xffe0e0e0,
        0xfff44336, 0xffe91e63, 0xff9c27b0, 0xff673ab7, 0xff3f51b5, 0xff2196f3, 0xff03a9f4, 0xff00bcd4, 0xff009688, 0xff4caf50, 0xff8bc34a, 0xffcddc39, 0xffffeb3b, 0xffffc107, 0xffff9800, 0xffff5722, 0xff8d6e63, 0xffbdbdbd,
        0xffe53935, 0xffd81b60, 0xff8e24aa, 0xff5e35b1, 0xff3949ab, 0xff1e88e5, 0xff039be5, 0xff00acc1, 0xff00897b, 0xff43a047, 0xff7cb342, 0xffc0ca33, 0xfffdd835, 0xffffb300, 0xfffb8c00, 0xfff4511e, 0xff795548, 0xff9e9e9e,
        0xffd32f2f, 0xffc2185b, 0xff7b1fa2, 0xff512da8, 0xff303f9f, 0xff1976d2, 0xff0288d1, 0xff0097a7, 0xff00796b, 0xff388e3c, 0xff689f38, 0xffafb42b, 0xfffbc02d, 0xffffa000, 0xfff57c00, 0xffe64a19, 0xff6d4c41, 0xff757575,
        0xffc62828, 0xffad1457, 0xff6a1b9a, 0xff4527a0, 0xff283593, 0xff1565c0, 0xff0277bd, 0xff00838f, 0xff00695c, 0xff2e7d32, 0xff558b2f, 0xff9e9d24, 0xfff9a825, 0xffff8f00, 0xffef6c00, 0xffd84315, 0xff5d4037, 0xff616161,
        0xffb71c1c, 0xff880e4f, 0xff4a148c, 0xff311b92, 0xff1a237e, 0xff0d47a1, 0xff01579b, 0xff006064, 0xff004d40, 0xff1b5e20, 0xff33691e, 0xff827717, 0xfff57f17, 0xffff6f00, 0xffe65100, 0xffbf360c, 0xff4e342e, 0xff424242,
        0xffd50000, 0xffc51162, 0xffaa00ff, 0xff6200ea, 0xff304ffe, 0xff2962ff, 0xff0091ea, 0xff00b8d4, 0xff00bfa5, 0xff00c853, 0xff64dd17, 0xffaeea00, 0xffffd600, 0xffffab00, 0xffff6d00, 0xffdd2c00, 0xff3e2723, 0xff212121,
        0xffff1744, 0xfff50057, 0xffd500f9, 0xff651fff, 0xff3d5afe, 0xff2979ff, 0xff00b0ff, 0xff00e5ff, 0xff1de9b6, 0xff00e676, 0xff76ff03, 0xffc6ff00, 0xffffea00, 0xffffc400, 0xffff9100, 0xffff3d00, 0xffeceff1, 0xff607d8b,
        0xffff5252, 0xffff4081, 0xffe040fb, 0xff7c4dff, 0xff536dfe, 0xff448aff, 0xff40c4ff, 0xff18ffff, 0xff64ffda, 0xff69f0ae, 0xffb2ff59, 0xffeeff41, 0xffffff00, 0xffffd740, 0xffffab40, 0xffff6e40, 0xffcfd8dc, 0xff546e7a,
        0xffff8a80, 0xffff80ab, 0xffea80fc, 0xffb388ff, 0xff8c9eff, 0xff82b1ff, 0xff80d8ff, 0xff84ffff, 0xffa7ffeb, 0xffb9f6ca, 0xffccff90, 0xfff4ff81, 0xffffff8d, 0xffffe57f, 0xffffd180, 0xffff9e80, 0xff90a4ae, 0xff37474f,
        0xfffce0e3, 0xfffad8e3, 0xffefd5f3, 0xffe8e0f7, 0xffdfe2f5, 0xffd6eafb, 0xffd9f3ff, 0xffd1f5f9, 0xffd1f0ed, 0xffddf2de, 0xffedf9df, 0xfff8fbdb, 0xfffffcdd, 0xfffff4d5, 0xffffefd7, 0xffffe3e0, 0xff78909c, 0xff263238,
        0xff000000, 0xff000000, 0xff000000, 0xff000000
    );

    std::array<uint32_t, 256> blockIdRGBA = {
        /* 667 */  0xffffffff , 0xfff8bbd0 , 0xffe1bee7 , 0xffd1c4e9 , 0xffc5cae9 , 0xffbbdefb , 0xffb3e5fc , 0xffb2ebf2 , 0xffb2dfdb , 0xffc8e6c9 , 0xffdcedc8 , 0xfff0f4c3 , 0xfffff9c4 , 0xffffecb3 , 0xffffe0b2 , 0xffffccbc ,
        /* 668 */	0xffef6c00 , 0xffffcdd2 , 0xffef9a9a , 0xfff48fb1 , 0xffce93d8 , 0xffb39ddb , 0xff9fa8da , 0xfffff176 , 0xff81d4fa , 0xff80deea , 0xff80cbc4 , 0xffa5d6a7 , 0xffc5e1a5 , 0xffe6ee9c , 0xfffff59d , 0xffffe082 ,
        /* 669 */	0xff9c27b0 , 0xffffab91 , 0xffd7ccc8 , 0xff000000 , 0xffe57373 , 0xff039be5 , 0xffba68c8 , 0xff9575cd , 0xff7986cb , 0xff64b5f6 , 0xff4fc3f7 , 0xff4dd0e1 , 0xff4db6ac , 0xff81c784 , 0xffaed581 , 0xffdce775 ,
        /* 670 */	0xff90caf9 , 0xffffd54f , 0xffffb74d , 0xffff8a65 , 0xffbcaaa4 , 0xffeeeeee , 0xffef5350 , 0xffec407a , 0xffab47bc , 0xff7e57c2 , 0xff5c6bc0 , 0xff42a5f5 , 0xff29b6f6 , 0xff26c6da , 0xff26a69a , 0xff66bb6a ,
        /* 671 */	0xffffd740 , 0xffd4e157 , 0xffffee58 , 0xffffca28 , 0xffffa726 , 0xffff7043 , 0xffa1887f , 0xffe0e0e0 , 0xfff44336 , 0xffe91e63 , 0xffffcc80 , 0xff673ab7 , 0xff3f51b5 , 0xff2196f3 , 0xff03a9f4 , 0xff00bcd4 ,
        /* 672 */	0xffcddc39 , 0xff4caf50 , 0xff8bc34a , 0xff009688 , 0xffffeb3b , 0xffffc107 , 0xffff9800 , 0xffff5722 , 0xff8d6e63 , 0xff4e342e , 0xffe53935 , 0xffd81b60 , 0xff8e24aa , 0xff5e35b1 , 0xff3949ab , 0xffff6d00 ,
        /* 673 */	0xfff06292 , 0xff00acc1 , 0xff00897b , 0xff43a047 , 0xff7cb342 , 0xffc0ca33 , 0xfffdd835 , 0xffffb300 , 0xfffb8c00 , 0xfff4511e , 0xff795548 , 0xff9e9e9e , 0xffd32f2f , 0xffc2185b , 0xff7b1fa2 , 0xff512da8 ,
        /* 674 */	0xff616161 , 0xff1976d2 , 0xff0288d1 , 0xff0097a7 , 0xff00796b , 0xff388e3c , 0xff689f38 , 0xffafb42b , 0xfffbc02d , 0xffffa000 , 0xfff57c00 , 0xffe64a19 , 0xff6d4c41 , 0xff757575 , 0xffa7ffeb , 0xffad1457 ,
        /* 675 */	0xffbdbdbd , 0xff4527a0 , 0xff283593 , 0xff1565c0 , 0xff0277bd , 0xff00838f , 0xff00695c , 0xffb2ff59 , 0xff558b2f , 0xff9e9d24 , 0xfff9a825 , 0xffff8f00 , 0xffd9f3ff , 0xffd84315 , 0xffffea00 , 0xff303f9f ,
        /* 676 */	0xff00bfa5 , 0xff880e4f , 0xff4a148c , 0xff311b92 , 0xff1a237e , 0xff0d47a1 , 0xff01579b , 0xff006064 , 0xff004d40 , 0xff1b5e20 , 0xff33691e , 0xff827717 , 0xfff57f17 , 0xffff6f00 , 0xffe65100 , 0xffbf360c ,
        /* 677 */	0xff6a1b9a , 0xff424242 , 0xffd50000 , 0xffc51162 , 0xffaa00ff , 0xff6200ea , 0xff304ffe , 0xff2962ff , 0xff0091ea , 0xff00b8d4 , 0xffb71c1c , 0xff00c853 , 0xff64dd17 , 0xffaeea00 , 0xffffd600 , 0xffffab00 ,
        /* 678 */	0xff1e88e5 , 0xffdd2c00 , 0xff3e2723 , 0xff000000 , 0xffff1744 , 0xfff50057 , 0xffd500f9 , 0xff651fff , 0xff3d5afe , 0xff2979ff , 0xff00b0ff , 0xff00e5ff , 0xff1de9b6 , 0xff00e676 , 0xff76ff03 , 0xffc6ff00 ,
        /* 679 */	0xff5d4037 , 0xffffc400 , 0xffff9100 , 0xffff3d00 , 0xffeceff1 , 0xff607d8b , 0xffff5252 , 0xffff4081 , 0xffe040fb , 0xff7c4dff , 0xff536dfe , 0xff448aff , 0xff40c4ff , 0xff18ffff , 0xff64ffda , 0xff69f0ae ,
        /* 680 */	0xff2e7d32 , 0xffeeff41 , 0xffffff00 , 0xff9ccc65 , 0xffffab40 , 0xffff6e40 , 0xffcfd8dc , 0xff546e7a , 0xffff8a80 , 0xffff80ab , 0xffea80fc , 0xffb388ff , 0xff8c9eff , 0xff82b1ff , 0xff80d8ff , 0xff84ffff ,
        /* 681 */  0xffc62828 , 0xffb9f6ca , 0xffccff90 , 0xfff4ff81 , 0xffffff8d , 0xffffe57f , 0xffffd180 , 0xffff9e80 , 0xff90a4ae , 0xff37474f , 0xfffce0e3 , 0xfffad8e3 , 0xffefd5f3 , 0xffe8e0f7 , 0xffdfe2f5 , 0xffd6eafb ,
        /* 682 */  0xff212121 , 0xffd1f5f9 , 0xffd1f0ed , 0xffddf2de , 0xffedf9df , 0xfff8fbdb , 0xfffffcdd , 0xfffff4d5 , 0xffffefd7 , 0xffffe3e0 , 0xff78909c , 0xff263238 , 0xff000000 , 0xff000000 , 0xff000000 , 0xff000000 ,
    };

    uint32_t GetNearestRGBA(RGBA& rgba) {
        float min = 99999999.0f;
        RGBA  nearestRGBA(0xffffffff);
        for (auto rgba1 : blockPalette) {
            float diffBlue = rgba.b - rgba1.b;
            float diffGreen = rgba.g - rgba1.g;
            float diffRed = rgba.r - rgba1.r;
            float sum = diffBlue * diffBlue + diffGreen * diffGreen + diffRed * diffRed;
            float norm2 = sqrt(sum);
            if (norm2 < min) {
                min = norm2;
                nearestRGBA = rgba1;
            }
        }
        return nearestRGBA.pack();
    }
    int FindblockIdRGBAIndex(RGBA& rgba) {
        uint32_t hexrgb = rgba.pack();
        for (size_t i = 0; i < blockIdRGBA.size(); i++)
        {
            if (hexrgb == blockIdRGBA[i])
                return i;
        }
        return 0;
    }

    BlockCoord GetBlockCoord(RGBA& rgba, WCoord wcoord) {
        int n = FindblockIdRGBAIndex(rgba) + 1;

        int blockIdIndex = n / 16;
        int colorIndex = n % 16;
        if (colorIndex == 0)
        {
            colorIndex = 15;
            --blockIdIndex;
        }
        else {
            --colorIndex;
        }
        int blockId = 0;

        switch (blockIdIndex)
        {
        case 0:
            blockId = 667;
            break;
        case 1:
            blockId = 668;
            break;
        case 2:
            blockId = 669;
            break;
        case 3:
            blockId = 670;
            break;
        case 4:
            blockId = 671;
            break;
        case 5:
            blockId = 672;
            break;
        case 6:
            blockId = 673;
            break;
        case 7:
            blockId = 674;
            break;
        case 8:
            blockId = 675;
            break;
        case 9:
            blockId = 676;
            break;
        case 10:
            blockId = 677;
            break;
        case 11:
            blockId = 678;
            break;
        case 12:
            blockId = 679;
            break;
        case 13:
            blockId = 680;
            break;
        case 14:
            blockId = 681;
            break;
        case 15:
            blockId = 682;
            break;

        }
        BlockCoord blockCoord(blockId, colorIndex, wcoord);
        return blockCoord;
    }

    BLOCK_DATA_TYPE GetBlockByColor(RGBA& rgba) {
        int n = FindblockIdRGBAIndex(rgba) + 1;

        int blockIdIndex = n / 16;
        int colorIndex = n % 16;
        if (colorIndex == 0)
        {
            colorIndex = 15;
            --blockIdIndex;
        }
        else {
            --colorIndex;
        }
        int blockId = 0;

        switch (blockIdIndex)
        {
        case 0:
            blockId = 667;
            break;
        case 1:
            blockId = 668;
            break;
        case 2:
            blockId = 669;
            break;
        case 3:
            blockId = 670;
            break;
        case 4:
            blockId = 671;
            break;
        case 5:
            blockId = 672;
            break;
        case 6:
            blockId = 673;
            break;
        case 7:
            blockId = 674;
            break;
        case 8:
            blockId = 675;
            break;
        case 9:
            blockId = 676;
            break;
        case 10:
            blockId = 677;
            break;
        case 11:
            blockId = 678;
            break;
        case 12:
            blockId = 679;
            break;
        case 13:
            blockId = 680;
            break;
        case 14:
            blockId = 681;
            break;
        case 15:
            blockId = 682;
            break;

        }

        return Block::makeBlockData(blockId, colorIndex);
    }
}

bool VoxelModel::loadVoxelFile(const char* path) {
    FILE* fp1 = fopen(path, "rb");
    if (fp1 == NULL) return NULL;

    VOXELMODEL_LOG("loadVoxelFile, path:%s", path);

    AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path);
    //DataStream *fp = FileManager::getSingleton().openFile(path, true);
    if (!fp.IsValid())
    {
        LOG_INFO("failed to open: %s", path);
        return false;
    }

    VoxelFileHeader header;
    fp->Read(&header, sizeof(header));
    if (header.magicnum[0] != 'V' || header.magicnum[1] != 'O' || header.magicnum[2] != 'X' || header.magicnum[3] != ' ')
    {
        LOG_INFO("wrong voxel magic number: %s", path);
        return false;
    }

    FileChunk* rootchunk = SANDBOX_NEW(FileChunk);
    rootchunk->loadFromFile(fp);
   // OGRE_DELETE(fp);

    int* xyzdim = (int*)rootchunk->getChild(CHUNKID('S', 'I', 'Z', 'E'))->m_ChunkData;
    m_DimX = xyzdim[0];
    m_DimY = xyzdim[2];
    m_DimZ = xyzdim[1];

    unsigned int* voxeldata = (unsigned int*)rootchunk->getChild(CHUNKID('X', 'Y', 'Z', 'I'))->m_ChunkData;
    unsigned int nvoxel = voxeldata[0];

    unsigned int* palettedata = (unsigned int*)rootchunk->getChild(CHUNKID('R', 'G', 'B', 'A'))->m_ChunkData;
    std::vector<Voxel::RGBA>* palette = new std::vector<Voxel::RGBA>;
    palette->resize(256);
    memcpy(&(*palette)[0], &palettedata[0], 4 * 256);

    for (unsigned int i = 0; i < nvoxel; i++)
    {
        OneVoxel v;
        *(unsigned int*)&v = voxeldata[i + 1];

        Voxel::RGBA color = (*palette)[v.c - 1];
        Voxel::RGBA color1 = Voxel::GetNearestRGBA(color);
        BlockCoord blockCoord = GetBlockCoord(color1, WCoord(v.x, v.z, v.y));
        m_Blocks.push_back(blockCoord);
    }
    /*if (m_blocks.size() > 0) {
        world *pworld = g_pplayerctrl->getworld();
        for (size_t i = 0; i < m_blocks.size(); i++)
        {
            blockcoord blockc = m_blocks[i];
            pworld->setblockall(blockc.m_pos.x + 100, blockc.m_pos.y + 10, blockc.m_pos.z + 100, blockc.getresid(), blockc.getdata());
        }
    }*/

    delete palette;
    OGRE_DELETE(rootchunk);

    return true;
}
std::vector<BlockCoord>& VoxelModel::getVoxelDataToBlock() {
	return m_Blocks;
}

int VoxelModel::loadVox(const char* path, int blockid, std::vector<VoxelModel*>& vModel)
{
    vModel.clear();
    AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path);
    if (!fp)
    {
        LOG_INFO("failed to open: %s", path);
        return -1;//找不到vox文件
    }

    VOXELMODEL_LOG("loadVox, path:%s, blockid:%d", path, blockid);

    VoxelFileHeader header;
    fp->Read(&header, sizeof(header));
    if (header.magicnum[0] != 'V' || header.magicnum[1] != 'O' || header.magicnum[2] != 'X' || header.magicnum[3] != ' ')
    {
        LOG_INFO("wrong voxel magic number: %s", path);
        return -2;//不是vox文件格式
    }

    FileChunk* rootchunk = ENG_NEW(FileChunk);
    rootchunk->loadFromFile(fp);

    if (!rootchunk)
        return -3;//找不到vox的主区块

    int iChunkNum = 0;
    FileChunk* pPackChunk = rootchunk->getChild(CHUNKID('n', 'G', 'R', 'P'));
    if (pPackChunk && pPackChunk->m_ChunkData)
    {
        iChunkNum = ((unsigned int*)pPackChunk->m_ChunkData)[2];
        if (iChunkNum > 32)
        {
            iChunkNum = 0;
        }
    }

    unsigned int iSizeChunkId = CHUNKID('S', 'I', 'Z', 'E');
    unsigned int iXYZChunkId = CHUNKID('X', 'Y', 'Z', 'I');
    if (iChunkNum == 0)
    {
        if (rootchunk->getChild(iSizeChunkId) && rootchunk->getChild(iXYZChunkId))
        {
            //此处兼容老的vox
            iChunkNum = 1;
        }
    }

    if (rootchunk->m_Children.size() <= 2 * iChunkNum)
        return -4;//vox区块数据异常

    int iDimX;
    int iDimY;
    int iDimZ;
    FileChunk* pUserPalette = rootchunk->getChild(CHUNKID('R', 'G', 'B', 'A'));
    unsigned int user_palette[256];
    if (pUserPalette)
    {
        memset(user_palette, 0, sizeof(user_palette));

        unsigned int* palettedata = (unsigned int*)pUserPalette->m_ChunkData;
        for (int i = 0; i <= 254; i++) {
            user_palette[i + 1] = palettedata[i];
        }
    }

    for (int i = 0; i < iChunkNum; i++)
    {
        FileChunk* pChunk = rootchunk->m_Children[i * 2];
        if (pChunk && iSizeChunkId == pChunk->m_ChunkID)
        {
            auto pChunkData = (int*)pChunk->m_ChunkData;
            iDimX = pChunkData[0];
            iDimY = pChunkData[2];
            iDimZ = pChunkData[1];
        }
        else
        {
            break;
        }

        pChunk = rootchunk->m_Children[i * 2 + 1];
        if (pChunk && iXYZChunkId == pChunk->m_ChunkID)
        {
            unsigned int* voxeldata = (unsigned int*)pChunk->m_ChunkData;
            unsigned int nvoxel = voxeldata[0];

            VoxelModel* pModel = ENG_NEW(VoxelModel)();
            pModel->m_DimX = iDimX;
            pModel->m_DimY = iDimY;
            pModel->m_DimZ = iDimZ;

            pModel->m_VoxelData = (BLOCK_DATA_TYPE*)malloc(iDimX * iDimY * iDimZ * sizeof(BLOCK_DATA_TYPE));
            memset(pModel->m_VoxelData, 0, iDimX * iDimY * iDimZ * sizeof(BLOCK_DATA_TYPE));

            if (pUserPalette)
            {
                std::map<unsigned int, unsigned int> mBlockIdDataCache;
                for (unsigned int j = 0; j < nvoxel; j++)
                {
                    OneVoxel v;
                    *(unsigned int*)&v = voxeldata[j + 1];

                    int index = pModel->vox_xyz2Index(v.x, v.z, v.y);
                    unsigned int color = user_palette[v.c];
                    if (mBlockIdDataCache.find(color) == mBlockIdDataCache.end()) {
                        Voxel::RGBA colorRGBA1(((color & 0xff) << 16) | (color & 0xff00) | ((color & 0xff0000) >> 16));
                        Voxel::RGBA color2 = Voxel::GetNearestRGBA(colorRGBA1);
                        pModel->m_VoxelData[index] = Voxel::GetBlockByColor(color2); //Block::makeBlockData(blockId, data);
                        mBlockIdDataCache[color] = pModel->m_VoxelData[index];
                    }
                    else
                    {
                        pModel->m_VoxelData[index] = mBlockIdDataCache[color];
                    }
                }
                mBlockIdDataCache.clear();
            }
            else
            {
                VoxelPalette* palette = NULL;
                if (blockid <= 0)
                {
                    palette = GetDefManagerProxy()->getVoxlPalette(-blockid);
                    if (!palette)
                    {
                        ENG_DELETE(pModel);
                        ENG_DELETE(rootchunk);
                        return 0;
                    }
                }

                for (unsigned int j = 0; j < nvoxel; j++)
                {
                    OneVoxel v;
                    *(unsigned int*)&v = voxeldata[j + 1];

                    int index = pModel->vox_xyz2Index(v.x, v.z, v.y);
                    if (palette) blockid = palette->BlockID[v.c - 1];
                    pModel->m_VoxelData[index] = Block::makeBlockData(blockid, 0);
                }
            }

            for (int y = 1; y < iDimY; y++)
            {
                for (int z = 0; z < iDimZ; z++)
                {
                    for (int x = 0; x < iDimX; x++)
                    {
                        BLOCK_DATA_TYPE& curblock = pModel->m_VoxelData[pModel->vox_xyz2Index(x, y, z)];
                        int curid = Block::toResID(curblock);
                        if (IsDoorBlock(curid))
                        {
                            unsigned short downblock = pModel->m_VoxelData[pModel->vox_xyz2Index(x, y - 1, z)];
                            if (Block::toResID(downblock) == curid)
                            {
                                curblock = Block::makeBlockData(curid, 1 << 2);
                            }
                        }
                    }
                }
            }

            vModel.push_back(pModel);
        }
        else
        {
            break;
        }
    }

    ENG_DELETE(rootchunk);
    return 0;
}