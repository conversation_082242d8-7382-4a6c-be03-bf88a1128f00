#pragma once

#include "SandboxAssetBaseHttp.h"
namespace Rainbow {
	namespace Http {
		class WebRequest;
		class UpLoadFileTask;
	}
}




namespace MNSandbox {

	class UploadHttp :public AssetBaseHttp {
		DECLARE_REF_NEW_INSTANCE(UploadHttp)

	public:
		UploadHttp();
		virtual ~UploadHttp();
		virtual void RegisterByReqData(HttpReq* ptr)override;
	private:
		void CacheDownloadRequst(AutoRef<HttpReq> reqData);
		void CacheUploadRequst(AutoRef<HttpReq> reqData);
		void GetAndCheckResIdRequst(AutoRef<HttpReq> reqData);
		void UploadFileRequst(AutoRef<HttpReq> reqData);
		void BackpackAddResRequst(AutoRef<HttpReq> reqData);
		void BackpackUpdateResRequst(AutoRef<HttpReq> reqData);
	private:
	};
}