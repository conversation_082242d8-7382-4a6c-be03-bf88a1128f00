//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//


#include <float.h>
#include "LocalBoundary.h"
#include "AI/MathUtil.h"
#include "NavMesh/NavMeshQuery.h"
#include "NavMesh/NavMesh.h"
#include "Math/Vector3f.h"


LocalBoundary::LocalBoundary() :
    m_center(FLT_MAX, FLT_MAX, FLT_MAX),
    m_centerRef(0),
    m_range(0),
    m_segmentCount(0)
{
}

LocalBoundary::~LocalBoundary()
{
}

void LocalBoundary::Reset()
{
    m_center.Set(FLT_MAX, FLT_MAX, FLT_MAX);
    m_centerRef = 0;
    m_range = 0;
    m_segmentCount = 0;
}

void LocalBoundary::AddSegment(const float dist, const Rainbow::Vector3f& start, const Rainbow::Vector3f& end)
{
    // Insert neighbour based on the distance.
    Segment* seg = NULL;
    if (m_segmentCount == 0)
    {
        // First, trivial accept.
        seg = &m_segments[0];
    }
    else if (dist >= m_segments[m_segmentCount - 1].dist)
    {
        // Further than the last segment, skip.
        if (m_segmentCount >= kMaxSegments)
            return;
        // Last, trivial accept.
        seg = &m_segments[m_segmentCount];
    }
    else
    {
        // Insert inbetween.
        int i;
        for (i = 0; i < m_segmentCount; ++i)
            if (dist <= m_segments[i].dist)
                break;

        if (i == m_segmentCount)
            return;                     //< Avoid having a potential NAN cause memory-thrashing below

        const int tgt = i + 1;
        const int n = std::min(m_segmentCount - i, kMaxSegments - tgt);
        DebugAssert(tgt + n <= kMaxSegments);
        if (n > 0)
            memmove(&m_segments[tgt], &m_segments[i], sizeof(Segment) * n);
        seg = &m_segments[i];
    }

    seg->dist = dist;
    seg->start = start;
    seg->end = end;

    if (m_segmentCount < kMaxSegments)
        m_segmentCount++;
}

void LocalBoundary::Update(const NavMeshPolyRef centerRef, const Rainbow::Vector3f& center, const float collisionQueryRange,
    const NavMeshQuery* navquery, const QueryFilter* filter)
{
    static const int kMaxLocalPolys = 16;
    static const int kMaxSegmentsPerPoly = kNavMeshVertsPerPoly * 3;

    const NavMesh* navmesh = navquery->GetAttachedNavMesh();

    if (centerRef == 0)
    {
        m_center.Set(FLT_MAX, FLT_MAX, FLT_MAX);
        m_centerRef = 0;
        m_range = 0;
        m_segmentCount = 0;
        return;
    }

    m_center = center;
    m_centerRef = centerRef;
    m_range = collisionQueryRange;

    m_segmentCount = 0;

    // First query non-overlapping polygons.
    NavMeshPolyRef locals[kMaxLocalPolys];
    int nlocals = 0;
    navquery->FindLocalNeighbourhood(centerRef, center, collisionQueryRange,
        filter, locals, 0, &nlocals, kMaxLocalPolys);


    const NavMeshTile* tile = navquery->GetAttachedNavMesh()->GetTileByRef(centerRef);

    // Secondly, store all polygon edges.
    Rainbow::Vector3f segs[kMaxSegmentsPerPoly * 2];
    NavMeshPolyRef segmentRefs[kMaxSegmentsPerPoly];
    int nsegs = 0;
    for (int j = 0; j < nlocals; ++j)
    {
        navquery->GetPolyWallSegments(locals[j], filter, segs, segmentRefs, &nsegs, kMaxSegmentsPerPoly);
        for (int k = 0; k < nsegs; ++k)
        {
            if (segmentRefs[k] != 0)
            {
                if (filter->PassFilter(navmesh->GetPolyFlags(segmentRefs[k])))
                {
                    // Skip Valid portal
                    continue;
                }
            }

            Rainbow::Vector3f start = segs[k * 2];
            Rainbow::Vector3f end = segs[k * 2 + 1];
            if (tile)
            {
                start = TileToWorld(*tile, start);
                end = TileToWorld(*tile, end);
            }
            // Skip too distant segments.
            float tseg;
            const float distSqr = SqrDistancePointSegment2D(&tseg, center, start, end);
            if (distSqr > Rainbow::Sqr(collisionQueryRange))
                continue;
            AddSegment(distSqr, start, end);
        }
    }
}
