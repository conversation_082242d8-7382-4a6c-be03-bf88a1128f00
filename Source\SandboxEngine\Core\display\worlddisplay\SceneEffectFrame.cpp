/**
* file : SceneEffectFrame
* func : 区域特效框
* by : chenzh
*/
#include "SceneEffectFrame.h"
#include "SceneEffectLine.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SandboxReflex.h"
#include "base/reflex/SandboxReflexTypeEnumEx.h"

using namespace MNSandbox;
using namespace Rainbow;

/** @enum
 * @name  SceneEffectFrameShowMode
 * @brief  场景效果帧显示模式
 */
static MNSandbox::ReflexEnumDesc<SceneEffectFrame::SHOWMODE> REnum_BorderColor("SceneEffectFrameShowMode", (int)MNSandbox::REFLEXTYPEENUM_ENUM_BORDERCOLOR,{
	{SceneEffectFrame::SHOWMODE::X, "X"},      /**< X轴 */
	{SceneEffectFrame::SHOWMODE::Y, "Y"},      /**< Y轴 */
	{SceneEffectFrame::SHOWMODE::Z, "Z"},      /**< Z轴 */
	{SceneEffectFrame::SHOWMODE::XY, "XY"},    /**< XY轴 */
	{SceneEffectFrame::SHOWMODE::XZ, "XZ"},    /**< XZ轴 */
	{SceneEffectFrame::SHOWMODE::YZ, "YZ"},    /**< YZ轴 */
	{SceneEffectFrame::SHOWMODE::XYZ, "XYZ"},  /**< XYZ轴 */
	});

SceneEffectFrame::SceneEffectFrame()
{
	m_MtlType = CURVEFACEMTL_TEXWHITE;
	m_eSes = SceneEffectShape::BOX_FRAME;
}

SceneEffectFrame::SceneEffectFrame(const WCoord startpos, const WCoord endpos, int stroke, 
	CURVEFACEMTLTYPE mtltype, CurveFace* curveFaces)
{
	m_iStroke = stroke;
	m_MtlType = mtltype;
	m_eSes = SceneEffectShape::BOX_FRAME;

	m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
	m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
	m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
	m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
	m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
	m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
	m_StartPosBlock = m_StartPos / BLOCK_SIZE;
	m_EndPosBlock = m_EndPos / BLOCK_SIZE;

	SetCurveFaces(curveFaces);
}

SceneEffectFrame::~SceneEffectFrame()
{
	if (m_drawCnt > 0)
	{
		for (unsigned i = 0; i < m_drawCnt; ++i)
		{
			SANDBOX_DELETE(m_drawData[i]._line);
			m_drawData[i]._lineCnt = 0;
		}
		m_drawCnt = 0;
	}
	for( auto item : mLines )
	{
		SANDBOX_DELETE(item);
	}
	mLines.clear();
}

void SceneEffectFrame::OnClear()
{
	if (m_drawCnt > 0)
	{
		for (unsigned i = 0; i < m_drawCnt; ++i)
		{
			SANDBOX_DELETE(m_drawData[i]._line);
			m_drawData[i]._lineCnt = 0;
		}
		m_drawCnt = 0;
	}
}

void SceneEffectFrame::Refresh()
{
	OnClear();
	RefreshBoxFrame(m_StartPos, m_EndPos);
}

void SceneEffectFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}

#if 1
	for( auto item : mLines )
	{
		item->OnDraw(pWorld);
	}
#else
	for (int i = 0; i < m_drawCnt; ++i)
	{
		DrawLine& data = m_drawData[i];
		for (int j = 0; j < data._lineCnt; ++j)
		{
			data._line->SetDrawPos(data._startDrawPos[j]);
			data._line->OnDraw(pWorld);
		}
	}
#endif
}

bool SceneEffectFrame::IsActive(World* pWorld) const
{
	return (m_StartPosBlock.y >= 0 && m_EndPosBlock.y >= 0) && (pWorld->getChunk(m_StartPosBlock) || pWorld->getChunk(m_EndPosBlock)) || m_AlwaysShow;
}

void SceneEffectFrame::RefreshBoxFrame(const WCoord& beg, const WCoord& end)
{
#if 1
	for (auto item : mLines)
	{
		SANDBOX_DELETE(item);
	}
	mLines.clear();
	Vector3f v[8];
	v[0].x = beg.x;
	v[0].y = beg.y;
	v[0].z = beg.z;

	v[1].x = end.x;
	v[1].y = beg.y;
	v[1].z = beg.z;

	v[2].x = end.x;
	v[2].y = end.y;
	v[2].z = beg.z;

	v[3].x = beg.x;
	v[3].y = end.y;
	v[3].z = beg.z;

	v[4].x = beg.x;
	v[4].y = beg.y;
	v[4].z = end.z;

	v[5].x = end.x;
	v[5].y = beg.y;
	v[5].z = end.z;

	v[6].x = end.x;
	v[6].y = end.y;
	v[6].z = end.z;

	v[7].x = beg.x;
	v[7].y = end.y;
	v[7].z = end.z;

	for (int i = 0; i < 8; ++i)
	{
		v[i] = m_qRotation * v[i];
		v[i] += m_vCenter;
	}
	for( int i =0; i < 3; ++i )
	{
		{
			auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[i].x, v[i].y, v[i].z), WCoord(v[1 + i].x, v[1 + i].y, v[1 + i].z), m_iStroke, m_MtlType, false, m_CurveFaces);
			effectLine->SetColor(m_Color);
			effectLine->Refresh();
			mLines.push_back(effectLine);
		}

		{
			auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[i].x, v[i].y, v[i].z), WCoord(v[4 + i].x, v[4 + i].y, v[4 + i].z), m_iStroke, m_MtlType, false, m_CurveFaces);
			effectLine->SetColor(m_Color);
			effectLine->Refresh();
			mLines.push_back(effectLine);
		}

		{
			auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[4+i].x, v[4+i].y, v[4 + i].z), WCoord(v[5 + i].x, v[5 + i].y, v[5 + i].z), m_iStroke, m_MtlType, false, m_CurveFaces);
			effectLine->SetColor(m_Color);
			effectLine->Refresh();
			mLines.push_back(effectLine);
		}
	}
	{
		auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[3].x, v[3].y, v[3].z), WCoord(v[0].x, v[0].y, v[0].z), m_iStroke, m_MtlType, false, m_CurveFaces);
		effectLine->SetColor(m_Color);
		effectLine->Refresh();
		mLines.push_back(effectLine);
	}
	{
		auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[7].x, v[7].y, v[7].z), WCoord(v[4].x, v[4].y, v[4].z), m_iStroke, m_MtlType, false, m_CurveFaces);
		effectLine->SetColor(m_Color);
		effectLine->Refresh();
		mLines.push_back(effectLine);
	}
	{
		auto effectLine = SANDBOX_NEW(SceneEffectLine, WCoord(v[3].x, v[3].y, v[3].z), WCoord(v[7].x, v[7].y, v[7].z), m_iStroke, m_MtlType, false, m_CurveFaces);
		effectLine->SetColor(m_Color);
		effectLine->Refresh();
		mLines.push_back(effectLine);
	}
#else

	// 1
	if (((unsigned)m_showMode & (unsigned)SHOWMODE::X) != 0 && beg.x != end.x)
	{
		auto effectLine1 = SANDBOX_NEW(SceneEffectLine, beg, pos1, m_iStroke, m_MtlType);
		effectLine1->SetColor(m_Color);
		effectLine1->Refresh();

		// 存
		auto& data = m_drawData[m_drawCnt++];
		data._line = effectLine1;
		data._startDrawPos[data._lineCnt++] = beg;
		if (end.y != beg.y)
			data._startDrawPos[data._lineCnt++] = pos2;
		if (end.z != beg.z)
			data._startDrawPos[data._lineCnt++] = pos3;
		if (end.y != beg.y && end.z != beg.z)
			data._startDrawPos[data._lineCnt++] = WCoord(beg.x, end.y, end.z);
	}

	// 2
	if (((unsigned)m_showMode & (unsigned)SHOWMODE::Y) != 0 && beg.y != end.y)
	{
		auto effectLine1 = SANDBOX_NEW(SceneEffectLine, beg, pos2, m_iStroke, m_MtlType);
		effectLine1->SetColor(m_Color);
		effectLine1->Refresh();

		// 存
		auto& data = m_drawData[m_drawCnt++];
		data._line = effectLine1;
		data._startDrawPos[data._lineCnt++] = beg;
		if (end.x != beg.x)
			data._startDrawPos[data._lineCnt++] = pos1;
		if (end.z != beg.z)
			data._startDrawPos[data._lineCnt++] = pos3;
		if (end.x != beg.x && end.z != beg.z)
			data._startDrawPos[data._lineCnt++] = WCoord(end.x, beg.y, end.z);
	}

	// 3
	if (((unsigned)m_showMode & (unsigned)SHOWMODE::Z) != 0 && beg.z != end.z)
	{
		auto effectLine1 = SANDBOX_NEW(SceneEffectLine, beg, pos3, m_iStroke, m_MtlType);
		effectLine1->SetColor(m_Color);
		effectLine1->Refresh();

		// 存
		auto& data = m_drawData[m_drawCnt++];
		data._line = effectLine1;
		data._startDrawPos[data._lineCnt++] = beg;
		if (end.x != beg.x)
			data._startDrawPos[data._lineCnt++] = pos1;
		if (end.y != beg.y)
			data._startDrawPos[data._lineCnt++] = pos2;
		if (end.x != beg.x && end.y != beg.y)
			data._startDrawPos[data._lineCnt++] = WCoord(end.x, end.y, beg.z);
	}
#endif
}
