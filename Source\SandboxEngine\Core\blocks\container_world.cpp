#include "blocks/container_world.h"

#include "IPlayerControl.h"
#include "PlayManagerInterface.h"
#include "IClientItem.h"
#include "IClientActor.h"
#include "IClientPlayer.h"
#include "GameNetManager.h"
#include "LuaInterfaceProxy.h"
#include "blocks/special_blockid.h"
#include "blocks/BlockMaterialMgr.h"
#include "container_buildblueprint.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "FullyCustomModelMgr.h"
#include "BlockScene.h"
#include "ClientActorHelper.h"
#include "Environment.h"
#include "WorldStringManagerProxy.h"
#include "Optick/optick.h"
#include "IActorBody.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "Text3D/ProgressBarWithText3D.h"
#include "Text3D/Image3D.h"
#include "LuaInterfaceProxy.h"
#include "CustomModelMgr.h"
#include "CommonUtil.h"
#include "chunk.h"
//#include "container_computer.h"
#include "TaskData.h"
#include "WorldManager.h"
#include "IActorLocoMotion.h"
#include "IClientMob.h"
#include "IActorAttrib.h"
#include "ActorBodySequence.h"
#include "IGameMode.h"
#include "ClientActorDef.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "Utilities/robin_map.h"
#include "Misc/TimeManager.h"
#include <typeinfo>


#if CONTAINER_DEBUG_LOG
#define CONTAINER_LOG(...) \
    do { \
        WarningStringMsg("[Container] " __VA_ARGS__); \
    } while(0)
#else
#define CONTAINER_LOG(...)
#endif

using namespace MNSandbox;
WorldContainer::~WorldContainer()
{
	m_World = NULL;
	m_vehicleWorld = NULL;

}

void WorldContainer::enterWorld(World* pworld)
{
	if (pworld->isVehicleWorld()) {
		pworld->Event2().Emit<World*&>("UpdateWorld", m_World);
		m_vehicleWorld = pworld;
	}
	else {
		m_World = pworld;
	}
}

bool WorldContainer::isOpenning()
{
	return m_OpenUINs.size() > 0;
}



void WorldContainer::resetOpenUINs()
{
	auto tuins = m_OpenUINs;
	if (m_World && !m_World->isRemoteMode() && tuins.size() > 0 && m_World->getActorMgr())
	{
		auto am = m_World->getActorMgr();
		for (auto it = tuins.begin(); it != tuins.end(); ++it)
		{
			auto player = am->iFindPlayerByUin(*it);
			if (player == NULL) continue;

			player->closeContainer();
		}
	}
	m_OpenUINs.clear();
}
void WorldContainer::leaveWorld()
{
	unRegisterUpdateTick();
	unRegisterUpdateDisplay();

	auto tuins = m_OpenUINs;
	if (m_World && !m_World->isRemoteMode() && tuins.size() > 0 && m_World->getActorMgr())
	{
		auto am = m_World->getActorMgr();
		for (auto it = tuins.begin(); it != tuins.end(); ++it)
		{
			auto player = am->iFindPlayerByUin(*it);
			if (player == NULL) continue;

			player->closeContainer();
		}
	}
	m_OpenUINs.clear();
	m_World = nullptr;
	m_vehicleWorld = NULL;
}

flatbuffers::Offset<FBSave::ContainerCommon> WorldContainer::saveContainerCommon(SAVE_BUFFER_BUILDER& builder)
{
	auto pos = WCoordToCoord3(m_BlockPos);
	return FBSave::CreateContainerCommon(builder, m_ObjId, &pos, m_OwnerUin);
}

bool WorldContainer::loadContainerCommon(const FBSave::ContainerCommon* srcdata)
{
	if (srcdata->blockpos())
	{
		m_ObjId = srcdata->wid();
		m_BlockPos = Coord3ToWCoord(srcdata->blockpos());
		m_OwnerUin = srcdata->owner();

		return true;
	}
	return false;
}

void WorldContainer::addOpenUIN(int uin)
{
	if (std::find(m_OpenUINs.begin(), m_OpenUINs.end(), uin) == m_OpenUINs.end())
	{
		m_OpenUINs.push_back(uin);
	}
}

void WorldContainer::removeOpenUIN(int uin)
{
	m_OpenUINs.erase(std::remove(m_OpenUINs.begin(), m_OpenUINs.end(), uin), m_OpenUINs.end());
}

void WorldContainer::afterChangeGrid(int index)
{
	if (m_World == NULL) return;

	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
		if (!m_World->isRemoteMode())
		{
			BlockMaterial* pmtl = getBlockMtl();
			if (pmtl) m_World->comparatorInputChange(m_BlockPos, pmtl->m_BlockResID);
		}
	}

	if (index < 0)
		return;

	auto it = m_OpenUINs.begin();
	while (it != m_OpenUINs.end())
	{
		IClientPlayer* player = m_World->getActorMgr()->iFindPlayerByUin(*it);
		if (player && player->checkIsOpenContainer(m_BlockPos, index)) {
			it++;
		}
		else {
			it = m_OpenUINs.erase(it);
		}
	}

	if (m_World && !m_World->isRemoteMode())
	{
		if ((index >= STORAGE_START_INDEX && index < CRAFT_START_INDEX) || (index >= FURNACE_START_INDEX && index < PRODUCT_LIST_TWO_INDEX)
			|| (index >= REPAIR_START_INDEX && index < SIGNS_START_INDEX) || (index >= FUNNEL_START_INDEX && index < HORSE_EQUIP_INDEX)
			|| (index >= SENSOR_START_INDEX && index < RECIPE_PRODUCT_LIST_INDEX) || (index >= FURNACE_OXY_START_INDEX && index < BRUSHMONSTER_START_INDEX)
			|| (index >= BOOKCABINET_START_INDEX) || (index >= STOVE_START_INFEX) || (index >= EXT_BACKPACK_START_INDEX)) {

			auto grid = index2Grid(index);
			if (grid)
			{
				int blockid = m_World->getBlockID(m_BlockPos);

				// 观察者事件接口
				ObserverEvent obevent;
				obevent.SetData_Position((float)m_BlockPos.x, (float)m_BlockPos.y, (float)m_BlockPos.z);
				obevent.SetData_Block(blockid);
				obevent.SetData_Item(grid->getItemID(), grid->getNum());
				GetObserverEventManager().OnTriggerEvent("Backpack.ItemChange", &obevent);
			}
		}
	}
}

BlockMaterial* WorldContainer::getBlockMtl()
{
	if (m_World == NULL) return NULL;
	if (m_BlockMtl == NULL)
	{
		m_BlockMtl = m_World->getBlockMaterial(m_BlockPos);
		if (m_vehicleWorld)
		{
			m_BlockMtl = m_vehicleWorld->getBlockMaterial(m_BlockPos);
		}
	}
	return m_BlockMtl;
}

void WorldContainer::notifyChange2Openers(int gridindex, bool attrib, std::string text/* =""  */)
{
	if (m_World && m_World->isRemoteMode()) return;
	if (m_OpenUINs.empty()) return;

	if (m_AttachToUI)
	{
		if (gridindex >= 0)
		{
			//ge GetGameEventQue().postBackpackChange(gridindex);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", gridindex);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
		if (attrib)
		{
			//ge GetGameEventQue().postBackPackAttribChange();
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
		}
	}

	PB_UpdateContainerHC updateContainerHC;
	PB_Vector3* pos = updateContainerHC.mutable_pos();
	pos->set_x(m_BlockPos.x);
	pos->set_y(m_BlockPos.y);
	pos->set_z(m_BlockPos.z);
	updateContainerHC.set_baseindex(m_BaseIndex);
	updateContainerHC.set_text(text);

	if (gridindex >= 0)
	{
		GetISandboxActorSubsystem()->StoreGridData(updateContainerHC.add_iteminfo(), index2Grid(gridindex));
	}

	if (attrib)
	{
		getItemAndAttrib(NULL, updateContainerHC.mutable_attribinfo());
	}

	GetGameNetManagerPtr()->sendToClientMulti(&m_OpenUINs[0], m_OpenUINs.size(), PB_UPDATE_CONTAINER_HC, updateContainerHC);
}

void WorldContainer::replaceGridByBluePrint(WorldContainer* container)
{
	int num = getGridNum();
	for (int i = 0; i < num; i++)
	{
		BackPackGrid* grid = index2Grid(m_BaseIndex + i);
		if (!grid || grid->isEmpty())
			continue;
		auto* itemDef = GetDefManagerProxy()->getItemDef(grid->getItemID());
		if (!itemDef)
			continue;

		BackPackGrid* srcGrid = container->getReplaceGrid(grid->getItemID());
		if (srcGrid)
		{
			if (itemDef->StackMax <= 1 || grid->hasRuneOrEnchants())
			{
				grid->clear();
				grid->setItem(*srcGrid, 1);
				srcGrid->addNum(-1);
			}
			else
				srcGrid->addNum(-srcGrid->getNum());

			if (srcGrid->getNum() <= 0)
				srcGrid->clear();
		}
	}
}

int WorldValueContainer::getObjType() const
{
	return OBJ_TYPE_VALUE;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldValueContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerValue(builder, basedata, m_SubType, m_Value);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerValue, actor.Union());
}

bool WorldValueContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerValue*>(srcdata);
	loadContainerCommon(src->basedata());

	m_SubType = src->subtype();
	m_Value = src->value();

	return true;
}

int WorldStringContainer::getObjType() const
{
	return OBJ_TYPE_STRING;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldStringContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto str = builder.CreateString(m_Str);
	auto actor = FBSave::CreateContainerString(builder, basedata, m_SubType, str);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerString, actor.Union());
}

bool WorldStringContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerString*>(srcdata);
	loadContainerCommon(src->basedata());

	m_SubType = src->subtype();
	m_Str = src->value()->c_str();

	return true;
}

void WorldStringContainer::setText(const char* text)
{
	m_Str = text;

	//保存chunk数据
	Chunk* pchunk = m_World->getChunk(m_BlockPos);
	if (pchunk)
	{
		pchunk->m_Dirty = true;
	}
}

//------------------------------------------------------------------------------------------------------------
WorldFurnace::WorldFurnace() : WorldContainer(FURNACE_START_INDEX)
{
	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_MeltTicks = 0;
	m_isMelting = false;
	m_meltOnce_Time = 200.00;
}

WorldFurnace::WorldFurnace(const WCoord& blockpos) : WorldContainer(blockpos, FURNACE_START_INDEX)
{
	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_MeltTicks = 0;
	m_isMelting = false;
	m_meltOnce_Time = 200.00;
}

WorldFurnace::~WorldFurnace()
{

}

void WorldFurnace::meltOnce()
{
	assert(m_Grids[GRID_MTL].getNum() > 0);
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());
	if (def == NULL) return;

	GetDefManagerProxy()->checkCrcCode(CRCCODE_FURNACE);
	auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
	if (GetIPlayerControl())
	{

		player->addAchievement(1, ACHIEVEMENT_FURNACEITEM, def->Result, 1);
		player->addOWScore(def->Score);
	}
	/*if (TaskSubSystem::GetTaskSubSystem())
	{
		TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_FURNACE_ITEM, m_BlockPos * BLOCK_SIZE, def->Result);
	}*/
	WCoord pos = m_BlockPos * BLOCK_SIZE;
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("type", TASKSYS_FURNACE_ITEM).
			SetData_Userdata("WCoord", "trackPos", &pos).
			SetData_Number("target1", def->Result).
			SetData_Number("target2", 0).
			SetData_Number("goalnum", 1);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
	}
	bool resultchanged = true;
	if (0 == m_Grids[GRID_RESULT].enough)
	{
		SetBackPackGrid(m_Grids[GRID_RESULT], def->Result, 1);
	}
	else
	{
		const ItemDef* resultdef = GetDefManagerProxy()->getItemDef(def->Result);
		if (resultdef && m_Grids[GRID_RESULT].getNum() >= resultdef->StackMax)
		{
			m_World->getActorMgr()->SpawnIClientItem(BlockBottomCenter(TopCoord(m_BlockPos)), def->Result, 1);
			resultchanged = false;
		}
		else
			SetBackPackGrid(m_Grids[GRID_RESULT], def->Result, m_Grids[GRID_RESULT].getNum() + 1);
	}
	SetBackPackGridWithClear(m_Grids[GRID_MTL], def->MaterialID, m_Grids[GRID_MTL].getNum() - 1);

	if (m_Grids[GRID_MTL].getNum() == 0) m_isMelting = false;
	m_MeltTicks = 0;

	if (resultchanged) afterChangeGrid(m_BaseIndex + GRID_RESULT);
	afterChangeGrid(m_BaseIndex + GRID_MTL);

	ObserverEvent obevent;
	obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
	obevent.SetData_Furance(def->ID);
	GetObserverEventManager().OnTriggerEvent("Furnace.end", &obevent);
}

void WorldFurnace::addHeatOnce()
{
	if (m_Grids[GRID_MTL].getNum() > 0 && m_Grids[GRID_FUEL].getNum() > 0)
	{
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL].getItemID());

		if ((m_Grids[GRID_RESULT].isEmpty() || 0 == m_Grids[GRID_RESULT].enough || m_Grids[GRID_RESULT].getItemID() == GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID(), true)->Result))
		{
			if (def == NULL || def->Heat == 0)
			{
				m_isMelting = false;
				return;
			}

			m_CurHeat = def->Heat;
			m_MaxHeat = def->Heat;
			if (def->ContainerID > 0)
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], def->ContainerID, 1);
			else
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], m_Grids[GRID_FUEL].getItemID(), m_Grids[GRID_FUEL].getNum() - 1);

			afterChangeGrid(m_BaseIndex + GRID_FUEL);
		}
		else
		{
			m_isMelting = false;
		}

	}
	else if (m_Grids[GRID_MTL].getNum() > 0)
	{
		m_isMelting = false;
	}
}

BackPackGrid* WorldFurnace::index2Grid(int index)
{
	assert(index >= m_BaseIndex && index < m_BaseIndex + 3);

	return &m_Grids[index - m_BaseIndex];
}

void WorldFurnace::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
	//notifyChange2Openers(index, false);

	if (m_Grids[GRID_MTL].getNum() > 0)
	{
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());
		if (def && (m_Grids[GRID_RESULT].isEmpty() || 0 == m_Grids[GRID_RESULT].enough || m_Grids[GRID_RESULT].getItemID() == def->Result))
		{
			if (def && def->Result > 0 && (m_CurHeat > 0 || m_Grids[GRID_FUEL].getNum() > 0))
			{
				if (m_Grids[GRID_RESULT].isEmpty() || 0 == m_Grids[GRID_RESULT].enough)
				{
					//SetBackPackGrid(m_Grids[GRID_RESULT], def->Result, 1, -1, 0, 0);
					SetBackPackGrid(m_Grids[GRID_RESULT], 0, 0, -1, 0, 0);
					//notifyChange2Openers(FURNACE_START_INDEX+GRID_RESULT, false);
				}
				if (m_CurHeat == 0) addHeatOnce();

				if (m_CurHeat > 0) m_isMelting = true;
			}
		}
		else
		{
			m_isMelting = false;
			m_MeltTicks = 0;
			notifyChange2Openers(-1, true);
		}

	}
	else
	{
		m_isMelting = false;
		m_MeltTicks = 0;
		notifyChange2Openers(-1, true);
		//GetGameEventQue().postBackPackAttribChange();
	}

	m_NeedSave = true;
}

void WorldFurnace::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < 3; i++)
	{
		//ge GetGameEventQue().postBackpackChange(FURNACE_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", FURNACE_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	//ge GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void WorldFurnace::onDetachUI()
{
	m_AttachToUI = false;
}

bool WorldFurnace::canPutItem(int index)
{
	if (index == m_BaseIndex + GRID_RESULT) return false;
	else return true;
}

void WorldFurnace::onHeatOnOff()
{
	World* pworld = m_World;
	int blockdata = pworld->getBlockData(m_BlockPos);
	int burning = blockdata & 4;

	if (m_CurHeat == 0 && burning != 0 || m_CurHeat > 0 && burning == 0)
	{
		if (m_CurHeat == 0) pworld->setBlockData(m_BlockPos, blockdata & 3, 2);
		else pworld->setBlockData(m_BlockPos, blockdata | 4, 2);
	}
}

void WorldContainer::dropOneItem(BackPackGrid& itemgrid)
{
	if (!itemgrid.isEmpty())
	{
		dropOneItem(itemgrid, WCoord(GenRandomInt(10, 90), GenRandomInt(10, 90), GenRandomInt(10, 90)));
	}
}

void WorldContainer::dropOneItem(BackPackGrid& itemgrid,const WCoord& BlockPos)
{
	if (!itemgrid.isEmpty())
	{
		WCoord pos = m_BlockPos * BLOCK_SIZE + BlockPos;
		if (m_vehicleWorld)
		{
			m_vehicleWorld->Event2().Emit<WCoord&, const Rainbow::Vector3f&, bool, int>("ConvertVector3f2WCoord", 
				pos, 
				Rainbow::Vector3f((float)m_BlockPos.x, (float)m_BlockPos.y, (float)m_BlockPos.z),
				false, 
				-1);
			pos += WCoord(GenRandomInt(10, 90), GenRandomInt(10, 90), GenRandomInt(10, 90));
		}
		if (m_World)
		{
			IClientItem* item = m_World->getActorMgr()->SpawnIClientItem(pos, itemgrid);
			if (item)
			{
				IActorLocoMotion* locmove = item->GetItemLocoMotion();
				float scale = 0.05f;
				locmove->SetMotion(Rainbow::Vector3f(GenGaussian() * scale, GenGaussian() * scale + 0.2f, GenGaussian() * scale));
				item->SetItemSpawnType(DESTROYBOX);
			}
		}
	}
}

bool WorldContainer::doOpenContainer()
{
	BaseContainer::SetLuaOpenContainer(getContainerName(), this);
	BlockMaterial* mtl = getBlockMtl();
	
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("baseindex", getBaseIndex()).
		SetData_Number("blockid", mtl ? mtl->m_BlockResID : 0).
		SetData_Number("posx", m_BlockPos.x).
		SetData_Number("posy", m_BlockPos.y).
		SetData_Number("posz", m_BlockPos.z).
		SetData_Bool("isRideContainer", m_isRideContainer);


	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	{
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_CONTAINER", sandboxContext);
	}
	return true;
}

void WorldContainer::registerUpdateTick()
{
	if (!m_World || !m_World->getContainerMgr())
	{
		return;
	}
	if(!m_vehicleWorld)
		m_World->getContainerMgr()->registerUpdateTickContainer(this);
}

void WorldContainer::registerUpdateDisplay()
{
	if (!m_World || !m_World->getContainerMgr())
	{
		return;
	}
	if (!m_vehicleWorld)
		m_World->getContainerMgr()->registerUpdateDisplayContainer(this);
}

void WorldContainer::unRegisterUpdateTick()
{
	if (!m_World || !m_World->getContainerMgr())
	{
		return;
	}
	if (!m_vehicleWorld)
		m_World->getContainerMgr()->unRegisterUpdateTickContainer(this);
}

void WorldContainer::unRegisterUpdateDisplay()
{
	if (!m_World || !m_World->getContainerMgr())
	{
		return;
	}
	if (!m_vehicleWorld)
		m_World->getContainerMgr()->unRegisterUpdateDisplayContainer(this);
}

void WorldContainer::setNeedClear()
{
	if (m_NeedClear)
	{
		return;
	}

	if (m_World && m_World->getContainerMgr())
	{
		m_World->getContainerMgr()->setContainerNeedClear(this);
	}

	m_NeedClear = true;
}

void WorldFurnace::dropItems()
{
	for (int i = 0; i < 3; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

void WorldFurnace::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);

	registerUpdateTick();
}

void WorldFurnace::updateTick()
{
	if (m_World->isRemoteMode()) return;

	bool hasprogress = false;
	bool oldmelting = m_isMelting;
	int oldmeltticks = m_MeltTicks;
	int oldcurheat = m_CurHeat;
	int oldmaxheat = m_MaxHeat;

	if (m_isMelting)
	{
		if (m_MeltTicks == 0)
		{
			const FurnaceDef* furnacedef = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());
			if (furnacedef)
			{
				ObserverEvent obevent;
				obevent.SetData_Position(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z);
				obevent.SetData_Furance(furnacedef->ID);
				GetObserverEventManager().OnTriggerEvent("Furnace.begin", &obevent);
			}
		}
		m_MeltTicks++;
		if (m_MeltTicks >= m_meltOnce_Time)
		{
			meltOnce();
		}
		hasprogress = true;
	}

	if (m_CurHeat > 0)
	{
		m_CurHeat--;
		if (m_CurHeat == 0)
		{
			addHeatOnce();
		}
		onHeatOnOff();
		hasprogress = true;
	}
	else if (m_isMelting)
	{
		m_isMelting = false;
	}

	if (hasprogress)
	{
		notifyChange2Openers(-1, true);
		//GetGameEventQue().postBackPackAttribChange();
	}

	if (oldmelting != m_isMelting || oldmeltticks != m_MeltTicks || oldcurheat != m_CurHeat || oldmaxheat != m_MaxHeat)
	{
		m_NeedSave = true;
	}
}

float WorldFurnace::getHeatPercent()
{
	return float(m_CurHeat) / float(m_MaxHeat);
}

float WorldFurnace::getMeltTicksPercent()
{
	return float(m_MeltTicks / m_meltOnce_Time);
}

void WorldFurnace::clear()
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++) {
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty()) {
			grid.clear();
			afterChangeGrid(grid.getIndex());
		}
	}
}

int WorldFurnace::addItemByCount(int itemid, int num)
{
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(itemid);
	if (def == NULL) return -1;

	int index = m_BaseIndex;
	BackPackGrid* dest;
	if (def->Heat > 0) {
		dest = &m_Grids[0];
		index += GRID_MTL;
	}
	else {
		dest = &m_Grids[1];
		index += GRID_FUEL;
	}

	if (!dest->isEmpty() && dest->getItemID() != itemid) { return -1; }

	int addnum = num;
	if (dest->isEmpty()) {
		SetBackPackGrid(*dest, itemid, num);
		afterChangeGrid(index);
	}
	else {
		addnum = dest->addNum(num);
	}

	return addnum;
}

void WorldFurnace::removeItemByCount(int itemid, int num)
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldFurnace::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> grids[3];
	for (int i = 0; i < 3; i++)
	{
		grids[i] = m_Grids[i].save(builder);
	}

	auto items = builder.CreateVector(grids, 3);

	auto actor = FBSave::CreateContainerFurnace(builder, basedata, items, m_CurHeat, m_MaxHeat, m_MeltTicks, m_isMelting);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFurnace, actor.Union());
}

extern int g_BackgridCheckNumMethod;

bool WorldFurnace::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFurnace*>(srcdata);
	loadContainerCommon(src->basedata());

	m_CurHeat = src->curheat();
	m_MaxHeat = src->maxheat();
	m_MeltTicks = src->meltticks();
	m_isMelting = src->melting() != 0;

	g_BackgridCheckNumMethod = 1;
	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].load(src->items()->Get(i));
	}
	g_BackgridCheckNumMethod = 0;

	//onHeatOnOff();
	return true;
}

int WorldFurnace::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < 3; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}

	pAttrInfos->Add(getHeatPercent());
	pAttrInfos->Add(getMeltTicksPercent());

	return 3;
}

int WorldFurnace::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	int dir = params & 7;
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());

	if (def == NULL) return -1;

	BackPackGrid* dest;
	if (dir == DIR_POS_Y)
	{
		if (def->Heat > 0) return -1;
		dest = &m_Grids[0];
	}
	else
	{
		if (def->Heat <= 0) return -1;
		dest = &m_Grids[1];
	}

	return InsertItemIntoArray(this, dest, 1, grid, num);
}

bool WorldFurnace::canInsertItem(const BackPackGrid& grid, int param)
{
	int dir = param & 7;

	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());
	if (def == NULL) return false;
	BackPackGrid* dest;
	if (dir == DIR_POS_Y)
	{
		if (def->Heat > 0) return false;
		dest = &m_Grids[0];
	}
	else
	{
		if (def->Heat <= 0) return false;
		dest = &m_Grids[1];
	}
	return CheckInsertItemIntoArray(this, dest, 1, grid) <= 0;
}

BackPackGrid* WorldFurnace::onExtractItem(int params)
{
	if (m_Grids[2].isEmpty())
	{
		if (m_Grids[1].getItemID() == ITEM_BUCKET || m_Grids[1].getItemID() == ITEM_TITANIUM_BUCKET) return &m_Grids[1];
		return NULL;
	}
	else return &m_Grids[2];
}

int WorldFurnace::calComparatorInputOverride()
{
	return CalculateItemsComparatorInput(&m_Grids[0], 3);
}

/*LLDO:氧气炉,Oxygen_Begin,******************************************************************* *//*LLDO:氧气炉,Oxygen_Begin,******************************************************************* */
WorldFurnaceOxy::WorldFurnaceOxy()
{
	m_BaseIndex = FURNACE_OXY_START_INDEX;

	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_MeltTicks = 0;
	m_isMelting = false;
	m_meltOnce_Time = 100.00;
	m_nOxygenValue = 0;
	m_IsCoverExisted = 0;
	m_OxyTick = 0;
	m_SafeAreaFX = NULL;
	maxOxyValue = 300.0;
	m_RefreshOxygenTick = 0;
	m_bSafeArea = false;
}

WorldFurnaceOxy::WorldFurnaceOxy(const WCoord& blockpos) : WorldFurnace(blockpos)
{
	m_BaseIndex = FURNACE_OXY_START_INDEX;

	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_CurHeat = 0;
	m_MaxHeat = 1;
	m_MeltTicks = 0;
	m_isMelting = false;
	m_meltOnce_Time = 100.00;
	m_nOxygenValue = 0;
	m_IsCoverExisted = 0;
	m_OxyTick = 0;
	m_SafeAreaFX = NULL;
	maxOxyValue = 300.0;
	m_RefreshOxygenTick = 0;
	m_bSafeArea = false;
}

WorldFurnaceOxy::~WorldFurnaceOxy()
{
}

//直接用熔炉的
void WorldFurnaceOxy::updateTick()
{
	if (m_World->isRemoteMode()) return;

	bool hasprogress = false;
	bool oldmelting = m_isMelting;
	int oldmeltticks = m_MeltTicks;
	int oldcurheat = m_CurHeat;
	int oldmaxheat = m_MaxHeat;

	if (m_isMelting)
	{
		m_MeltTicks++;
		if (m_MeltTicks >= m_meltOnce_Time)
		{
			meltOnce();
		}
		hasprogress = true;
	}

	if (m_CurHeat > 0)
	{
		m_CurHeat--;
		if (m_CurHeat == 0)
		{
			addHeatOnce();
		}
		onHeatOnOff();
		hasprogress = true;
	}
	else if (m_isMelting)
	{
		m_isMelting = false;
	}

	//氧气罩存在, 消耗氧气的时候,进度要更新
	if (m_IsCoverExisted)
		hasprogress = true;

	if (hasprogress)
	{
		notifyChange2Openers(-1, true);
		//GetGameEventQue().postBackPackAttribChange();
	}

	//消耗氧气
	if (1 == m_IsCoverExisted)
	{
		if (m_World->getBlockID(TopCoord(m_BlockPos)) != BLOCK_OXYGEN_JAR_EMPTY)
		{
			m_RefreshOxygenTick++;
			GetISandboxActorSubsystem()->BlockAlienTotemStepUpdateSafeArea(m_RefreshOxygenTick, m_World, m_BlockPos, GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
		}

		m_OxyTick++;
		if (m_OxyTick >= 20)
			deleteOxy();

	}

	if (oldmelting != m_isMelting || oldmeltticks != m_MeltTicks || oldcurheat != m_CurHeat || oldmaxheat != m_MaxHeat)
	{
		m_NeedSave = true;
	}
}

void WorldFurnaceOxy::meltOnce()
{
	bool bIsMeltOnce = false;
	assert(m_Grids[GRID_MTL].getNum() > 0);
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());
	if (def == NULL)
		return;

	//氧气罩存在且值<80; 或氧气罩不存在; 或者格子为空
	if ((1 == m_IsCoverExisted && m_nOxygenValue <= (maxOxyValue - def->OxyValue)) || 1 != m_IsCoverExisted)
		bIsMeltOnce = true;

	if (bIsMeltOnce)
	{
		assert(m_Grids[GRID_MTL].getNum() > 0);
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());
		if (def == NULL)
			return;

		GetDefManagerProxy()->checkCrcCode(CRCCODE_FURNACE);
		SetBackPackGridWithClear(m_Grids[GRID_MTL], def->MaterialID, m_Grids[GRID_MTL].getNum() - 1);

		m_nOxygenValue += def->OxyValue;
		if (m_nOxygenValue >= maxOxyValue)
		{
			m_nOxygenValue = (int)maxOxyValue;

			//产生氧气罩
			if (1 != m_IsCoverExisted)
			{
				m_IsCoverExisted = 1;
				BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(BLOCK_FURNACE_OXYGEN);
				if (pmtl)
				{
					GetISandboxActorSubsystem()->FurnaceOxygenTriggerOxygenJar(m_World, m_BlockPos, pmtl);
				}
				ShowCover();
			}
		}
		else
		{

		}

		if (m_Grids[GRID_MTL].getNum() == 0)
			m_isMelting = false;

		m_MeltTicks = 0;
		//afterChangeGrid(m_BaseIndex + GRID_RESULT);
		afterChangeGrid(m_BaseIndex + GRID_MTL);
	}
	else
	{
		//停止消耗材料
		m_isMelting = false;
		m_MeltTicks = 0;
	}
}

void WorldFurnaceOxy::addHeatOnce()
{
	if (m_Grids[GRID_MTL].getNum() > 0 && m_Grids[GRID_FUEL].getNum() > 0)
	{
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_FUEL].getItemID());

		if (m_nOxygenValue < maxOxyValue)
		{
			if (def == NULL || def->Heat == 0)
			{
				m_isMelting = false;
				return;
			}

			m_CurHeat = def->Heat;
			m_MaxHeat = def->Heat;
			if (def->ContainerID > 0)
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], def->ContainerID, 1);
			else
				SetBackPackGridWithClear(m_Grids[GRID_FUEL], m_Grids[GRID_FUEL].getItemID(), m_Grids[GRID_FUEL].getNum() - 1);

			afterChangeGrid(m_BaseIndex + GRID_FUEL);
		}
		else
		{
			m_isMelting = false;
		}

	}
	else if (m_Grids[GRID_MTL].getNum() > 0)
	{
		m_isMelting = false;
	}
}

void WorldFurnaceOxy::deleteOxy()
{
	m_OxyTick = 0;

	bool have_oxygen_jar_full = false;
	World* pworld = m_World;
	WCoord up(m_BlockPos.x, m_BlockPos.y + 1, m_BlockPos.z);
	int blockID = pworld->getBlockID(up);
	if (blockID == BLOCK_OXYGEN_JAR_EMPTY && pworld->getBlockData(up) > 3)
	{
		have_oxygen_jar_full = true;
	}

	if (m_nOxygenValue > 0 && (m_IsCoverExisted || have_oxygen_jar_full))
	{
		//氧气罩存在则消耗氧气
		m_nOxygenValue -= 1;
	}
	else
	{
		//去掉氧气罩
		if (1 == m_IsCoverExisted)
		{
			m_IsCoverExisted = 0;
			ShowCover();
		}
	}

	afterChangeGrid(m_BaseIndex + GRID_RESULT);
	onHeatOnOff();
}

void WorldFurnaceOxy::onHeatOnOff()
{
	World* pworld = m_World;
	int blockdata = pworld->getBlockData(m_BlockPos);
	int burning = blockdata & 4;

	if (m_IsCoverExisted == 0 && burning != 0 || m_IsCoverExisted > 0 && burning == 0)
	{
		if (m_IsCoverExisted == 0)
			pworld->setBlockData(m_BlockPos, blockdata & 3, 2);
		else
			pworld->setBlockData(m_BlockPos, blockdata | 4, 2);
	}
}

void WorldFurnaceOxy::ShowCover()
{
	World* pworld = m_World;
	WCoord up(m_BlockPos.x, m_BlockPos.y + 1, m_BlockPos.z);
	int blockdata = pworld->getBlockData(m_BlockPos);
	int burning = blockdata & 8;

	if (m_IsCoverExisted == 1 && pworld->getBlockID(up) != BLOCK_OXYGEN_JAR_EMPTY)
	{
		m_RefreshOxygenTick = 0;
		if (false == m_bSafeArea && m_World->onClient())
		{
			WCoord pos = BlockBottomCenter(m_BlockPos);
#ifndef IWORLD_SERVER_BUILD
			m_SafeAreaFX = ENG_NEW(EffectParticle)(m_World, "particles/alientotem.ent", pos, 0);
			m_SafeAreaFX->setScale((float)GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
			m_World->getEffectMgr()->addEffect(m_SafeAreaFX);
#endif
			m_bSafeArea = true;
		}

		if (!m_World->isRemoteMode()) GetISandboxActorSubsystem()->BlockAlienTotemCreateSafeArea(m_World, m_BlockPos, GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
	}
	else
	{
		if (m_SafeAreaFX)
		{
			m_SafeAreaFX->setNeedClear();
			m_SafeAreaFX = NULL;
		}
		m_bSafeArea = false;

		if (!m_World->isRemoteMode()) GetISandboxActorSubsystem()->BlockAlienTotemDestroySafeArea(BLOCK_PLANTSPACE_TOTEM, m_World, m_BlockPos, GetLuaInterfaceProxy().get_lua_const()->planet_safearea_radius);
	}
}

float WorldFurnaceOxy::GetOxygenValue()
{
	return (float)m_nOxygenValue;
}

float WorldFurnaceOxy::GetDegree()
{
	float degree = m_nOxygenValue / maxOxyValue;

	if (degree < 0)
		degree = 0;

	if (degree > 1)
		degree = 1;

	return degree;
}

//属性变化同步到客机
int WorldFurnaceOxy::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < 3; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}

	pAttrInfos->Add(getHeatPercent());
	pAttrInfos->Add(getMeltTicksPercent());
	pAttrInfos->Add(GetOxygenValue());
	pAttrInfos->Add(GetDegree());

	return 3;
}

void WorldFurnaceOxy::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
	//notifyChange2Openers(index, false);

	if (m_Grids[GRID_MTL].getNum() > 0)
	{
		const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(m_Grids[GRID_MTL].getItemID());

		/*氧气值<100才燃烧 */
		if (def && m_nOxygenValue < maxOxyValue)
		{
			if (def && (m_CurHeat > 0 || m_Grids[GRID_FUEL].getNum() > 0))
			{
				if (m_CurHeat == 0)
					addHeatOnce();

				if (m_CurHeat > 0 && (1 != m_IsCoverExisted || (1 == m_IsCoverExisted && m_nOxygenValue <= (maxOxyValue - def->OxyValue))))
					m_isMelting = true;
			}
		}
		else
		{
			m_isMelting = false;
			m_MeltTicks = 0;
			notifyChange2Openers(-1, true);
		}

	}
	else
	{
		m_isMelting = false;
		m_MeltTicks = 0;
		notifyChange2Openers(-1, true);
	}

	m_NeedSave = true;
}

void WorldFurnaceOxy::leaveWorld()
{
	m_IsCoverExisted = 0;
	ShowCover();

	WorldContainer::leaveWorld();
}

int WorldFurnaceOxy::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	int dir = params & 7;
	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());
	if (def == NULL) return -1;

	BackPackGrid* dest;
	if (dir == DIR_POS_Y)
	{
		//上面材料, 只让氧气材料漏下来
		if (def->OxyValue <= 0) return -1;
		dest = &m_Grids[0];
	}
	else
	{
		//旁边漏燃料, 非燃料不让漏下来
		if (def->Heat <= 0) return -1;
		dest = &m_Grids[1];
	}

	return InsertItemIntoArray(this, dest, 1, grid, num);
}

bool WorldFurnaceOxy::canInsertItem(const BackPackGrid& grid, int param)
{
	int dir = param & 7;

	const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialID(grid.getItemID());
	if (def == NULL) return false;

	BackPackGrid* dest;
	if (dir == DIR_POS_Y)
	{
		//上面材料, 只让氧气材料漏下来
		if (def->OxyValue <= 0) return false;
		dest = &m_Grids[0];
	}
	else
	{
		//旁边漏燃料, 非燃料不让漏下来
		if (def->Heat <= 0) return false;
		dest = &m_Grids[1];
	}

	return CheckInsertItemIntoArray(this, dest, 1, grid) <= 0;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldFurnaceOxy::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> grids[3];
	for (int i = 0; i < 3; i++)
	{
		grids[i] = m_Grids[i].save(builder);
	}

	auto items = builder.CreateVector(grids, 3);

	//auto actor = FBSave::CreateContainerFurnace(builder, basedata, items, m_CurHeat, m_MaxHeat, m_MeltTicks, m_isMelting);
	auto actor = FBSave::CreateContainerFurnaceOxy(builder, basedata, items, m_CurHeat, m_MaxHeat, m_MeltTicks, m_isMelting, m_nOxygenValue, m_IsCoverExisted);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerFurnaceOxy, actor.Union());
}

bool WorldFurnaceOxy::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerFurnaceOxy*>(srcdata);
	loadContainerCommon(src->basedata());

	m_CurHeat = src->curheat();
	m_MaxHeat = src->maxheat();
	m_MeltTicks = src->meltticks();
	m_isMelting = src->melting() != 0;

	g_BackgridCheckNumMethod = 1;
	for (int i = 0; i < 3; i++)
	{
		m_Grids[i].load(src->items()->Get(i));
	}
	g_BackgridCheckNumMethod = 0;

	m_nOxygenValue = src->oxyvalue();
	m_IsCoverExisted = src->hascover();

	//onHeatOnOff();
	//ShowCover();
	return true;
}

void WorldFurnaceOxy::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < 3; i++)
	{
		//ge GetGameEventQue().postBackpackChange(FURNACE_OXY_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", FURNACE_OXY_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	//ge GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void WorldFurnaceOxy::onDetachUI()
{
	m_AttachToUI = false;
}

WorldBlueprint::WorldBlueprint() : WorldContainer(BLUEPRINT_START_INDEX)
{
	for (int i = 0; i < GRID_MAX; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_bIsWorking = false;
	m_nMakeOneTick = 100;
	m_nCurWorkTick = 0;
	m_nRetID = ITEM_BLUEPRINT;
	m_DataFileName = "";
}

WorldBlueprint::WorldBlueprint(const WCoord& blockpos) : WorldContainer(blockpos, BLUEPRINT_START_INDEX)
{
	for (int i = 0; i < GRID_MAX; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}

	m_bIsWorking = false;
	m_nMakeOneTick = 100;
	m_nCurWorkTick = 0;
	m_nRetID = ITEM_BLUEPRINT;
	m_DataFileName = "";
}

WorldBlueprint::~WorldBlueprint()
{
}

void WorldBlueprint::updateTick()
{
	if (m_World->isRemoteMode()) return;

	if (m_bIsWorking)
	{
		//正在工作
		m_nCurWorkTick++;
		if (m_nCurWorkTick >= m_nMakeOneTick)
		{
			//制作完成.
			ProductOne();
		}
		//更新制作进度
		notifyChange2Openers(-1, true);
	}
}

void WorldBlueprint::SetSheetData(std::string& retStr)
{
	jsonxx::Object sheetData;
	std::string uin = "12345";
	sheetData << "uin" << uin;
	sheetData << "authorname" << m_DataAuthorName;
	sheetData << "sheetname" << m_SheetName;
	sheetData << "nickname" << m_DataNickName;
	sheetData << "filename" << m_DataFileName;
	sheetData << "dimx" << m_DataDim.x;
	sheetData << "dimy" << m_DataDim.y;
	sheetData << "dimz" << m_DataDim.z;

	retStr = sheetData.json();
	if (GetIPlayerControl())
	{
		char ckey[64] = { 0 };
#if defined(_WIN32)
		sprintf(ckey, "%d%I64d", GetIPlayerControl()->GetIUin(), time(NULL));
#else
		sprintf(ckey, "%d%ld", GetIPlayerControl()->GetIUin(), time(NULL));
#endif
		GetWorldStringManagerProxy()->insert(ckey, m_SheetName, SAVEFILETYPE::BLUE_PRINT);
		//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
		//	.SetData_Number("type", 14)
		//	.SetData_String("content", m_SheetName)
		//	.SetData_String("key", ckey));
	}
}

void WorldBlueprint::ProductOne()
{
	assert(m_Grids[GRID_MTL].getNum() > 0);
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_Grids[GRID_MTL].getItemID());
	if (def == NULL) return;

	const ItemDef* resultdef = GetDefManagerProxy()->getItemDef(m_nRetID);
	if (resultdef && m_Grids[GRID_RESULT].getNum() >= resultdef->StackMax)
	{
		//蓝图图纸的格子满了, 提示已满
		//ge GetGameEventQue().postInfoTips("full!!!");
		CommonUtil::GetInstance().PostInfoTips("full!!!");
		m_bIsWorking = false;
		m_nCurWorkTick = 0;
		return;
	}

	bool resultchanged = true;
	//产生生成物
	if (0 == m_Grids[GRID_RESULT].enough)
	{
		//直接把数量设为1
		SetBackPackGrid(m_Grids[GRID_RESULT], m_nRetID, 1);
		std::string userdata_str;
		SetSheetData(userdata_str);
		m_Grids[GRID_RESULT].userdata_str = userdata_str;
	}
	else
	{
		const ItemDef* resultdef = GetDefManagerProxy()->getItemDef(m_nRetID);
		SetBackPackGrid(m_Grids[GRID_RESULT], m_nRetID, m_Grids[GRID_RESULT].getNum() + 1);
		//上面GetIPlayerControl()是个全局变量, 联机的时候客机写入蓝图数据有问题, 这个全局变量是主机的, 所以找不到客机的格子.
		std::string userdata_str;
		SetSheetData(userdata_str);
		m_Grids[GRID_RESULT].userdata_str = userdata_str;
	}
	/*if (GetIPlayerControl())
	{
		GetIPlayerControl()->statisticToWorld(GetIPlayerControl()->GetIUin(), 30002, "", GetIPlayerControl()->getCurWorldType(), "1064");
	}*/
	//消耗原料
	SetBackPackGridWithClear(m_Grids[GRID_MTL], def->ID, m_Grids[GRID_MTL].getNum() - 1);
	/*if (GetIPlayerControl())
	{
		GetIPlayerControl()->statisticToWorld(GetIPlayerControl()->GetIUin(), 30002, "", GetIPlayerControl()->getCurWorldType(), "11805");
	}*/
	//重置工作状态
	m_bIsWorking = false;
	m_nCurWorkTick = 0;

	if (resultchanged)
		afterChangeGrid(m_BaseIndex + GRID_RESULT);

	afterChangeGrid(m_BaseIndex + GRID_MTL);
	//TODO 新的图纸名字覆盖蓝图文件保存的原有名字，并保存
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_changeBPName", SandboxContext(nullptr).SetData_String("dataFileName", m_DataFileName).SetData_String("sheetName", m_SheetName));

}




flatbuffers::Offset<FBSave::ChunkContainer> WorldBlueprint::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	flatbuffers::Offset<FBSave::ItemGrid> grids[GRID_MAX];
	for (int i = 0; i < GRID_MAX; i++)
	{
		grids[i] = m_Grids[i].save(builder);
	}

	auto items = builder.CreateVector(grids, GRID_MAX);
	auto dim = WCoordToCoord3(m_DataDim);
	auto coypBlockPos = WCoordToCoord3(m_copyBlockPos);
	auto knotPos = WCoordToCoord3(m_knotPos);
	auto actor = FBSave::CreateContainerBlueprint(builder, basedata, items, \
		builder.CreateString(m_SheetName), \
		builder.CreateString(m_DataFileName), \
		builder.CreateString(m_DataNickName), \
		& dim, \
		& coypBlockPos, \
		& knotPos
	);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerBlueprint, actor.Union());
}

bool WorldBlueprint::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBlueprint*>(srcdata);
	loadContainerCommon(src->basedata());

	g_BackgridCheckNumMethod = 1;
	for (int i = 0; i < GRID_MAX; i++)
	{
		m_Grids[i].load(src->items()->Get(i));
	}
	g_BackgridCheckNumMethod = 0;

	if (src->datafilename()) {
		m_DataFileName = std::string(src->datafilename()->c_str());
	}

	if (src->datanickname()) {
		m_DataNickName = std::string(src->datanickname()->c_str());
	}

	if (src->dim())
		m_DataDim = Coord3ToWCoord(src->dim());

	if (src->copyblockpos())
		m_copyBlockPos = Coord3ToWCoord(src->copyblockpos());

	if (src->knotpos())
		m_knotPos = Coord3ToWCoord(src->knotpos());

	m_bIsWorking = false;

	return true;
}

void WorldBlueprint::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < GRID_MAX; i++)
	{
		//ge GetGameEventQue().postBackpackChange(BLUEPRINT_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", BLUEPRINT_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
	//ge GetGameEventQue().postBackPackAttribChange();
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_ATTRIB_CHANGE", sandboxContext);
}

void WorldBlueprint::onDetachUI()
{
	m_AttachToUI = false;
}

BackPackGrid* WorldBlueprint::index2Grid(int index)
{
	assert(index >= m_BaseIndex && index < m_BaseIndex + 2);

	return &m_Grids[index - m_BaseIndex];
}

void WorldBlueprint::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);

	if (m_Grids[GRID_MTL].getNum() > 0)
	{

	}
	else
	{
		//制作途中取出了材料, 立刻停止.
		m_bIsWorking = false;
		m_nCurWorkTick = 0;
	}

	notifyChange2Openers(-1, true);
}

bool WorldBlueprint::canPutItem(int index)
{
	if (index == m_BaseIndex + GRID_RESULT) return false;
	else return true;
}

void WorldBlueprint::leaveWorld()
{
	WorldContainer::leaveWorld();
}

int WorldBlueprint::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < GRID_MAX; i++)
		{
			if (m_Grids[i].isEmpty()) continue;

			GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), &m_Grids[i]);
		}
	}

	pAttrInfos->Add(getAttrib(0));

	return GRID_MAX;
}

int WorldBlueprint::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	return 0;
}

void WorldBlueprint::StartWorking(const char* sheetName, const char* authorName)
{
	if (m_bIsWorking)
		return;
	else
	{
		//没有挨着复制方块放置
		if (m_DataFileName == "")
		{
			return;
		}

		//请放入材料
		if (m_Grids[GRID_MTL].getNum() < 1)
		{
			//ge GetGameEventQue().postInfoTips(9291);
			CommonUtil::GetInstance().PostInfoTips(9291);
			return;
		}

		//请去除生成物
		const ItemDef* resultdef = GetDefManagerProxy()->getItemDef(m_nRetID);
		if (resultdef && m_Grids[GRID_RESULT].getNum() >= resultdef->StackMax)
		{
			//蓝图图纸的格子满了, 提示已满
			//ge GetGameEventQue().postInfoTips(9292);
			CommonUtil::GetInstance().PostInfoTips(9292);
			return;
		}

		m_SheetName = sheetName;
		m_DataAuthorName = authorName;
		m_bIsWorking = true;
	}
}

void WorldBlueprint::SetFileInfo(char* pszFileName, const char* pszNickName, WCoord dim, WCoord copyBlockPos, WCoord knotPos)
{
	m_DataFileName = pszFileName;
	m_DataNickName = pszNickName;
	m_DataDim = dim;
	m_copyBlockPos = copyBlockPos;
	m_knotPos = knotPos;

	m_World->markBlockForUpdate(m_BlockPos);
}

//-----------------------------------------------------------------------------------------------------------------
WorldMeasureDistance::WorldMeasureDistance()
{
	m_BlockID = 1066;
}

WorldMeasureDistance::WorldMeasureDistance(const WCoord& blockpos) : WorldContainer(blockpos, BLUEPRINT_START_INDEX)
{
	m_BlockID = 1066;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldMeasureDistance::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);
	auto actor = FBSave::CreateContainerBlueprint(builder, basedata);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerMeasureDistance, actor.Union());
}

bool WorldMeasureDistance::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBlueprint*>(srcdata);
	loadContainerCommon(src->basedata());
	return true;
}

void WorldMeasureDistance::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateDisplay();

	UpdateFindPos();
}

void WorldMeasureDistance::leaveWorld()
{
	UpdateFindPos();

	WorldContainer::leaveWorld();
}

void WorldMeasureDistance::OnBlockChanged()
{
	int maxDistance = 64;
	m_findPos.clear();

	if (m_vehicleWorld != NULL)
	{
		maxDistance = 19;

		if (GetISandboxActorSubsystem()->FindBlockIn6Direction(m_vehicleWorld, m_BlockID, m_findPos, m_BlockPos, maxDistance))
		{

		}
	}
	else if (m_World)
	{
		if (GetISandboxActorSubsystem()->FindBlockIn6Direction(m_World, m_BlockID, m_findPos, m_BlockPos, maxDistance))
		{

		}

	}


}

void WorldMeasureDistance::UpdateFindPos()
{
	OnBlockChanged();

	for (auto it = m_findPos.begin(); it != m_findPos.end(); it++)
	{
		WCoord blockpos = *it;

		WorldMeasureDistance* container = NULL;
		if (m_vehicleWorld)
		{
			container = dynamic_cast<WorldMeasureDistance*>(m_vehicleWorld->getContainerMgr()->getContainer(blockpos));
		}
		else if (m_World)
		{
			container = dynamic_cast<WorldMeasureDistance*>(m_World->getContainerMgr()->getContainer(blockpos));
		}
		if (container)
		{
			container->OnBlockChanged();
		}
	}
}

void WorldMeasureDistance::updateDisplay(float dtime)
{
#ifdef DEDICATED_SERVER
	return;
#endif
	if (true)
	{
		int maxDistance = m_vehicleWorld != NULL ? 19 : 64;
		std::vector<WCoord> findPos;
		WCoord startPos = WCoord(0, 0, 0);

		WCoord origin = BlockCenterCoord(m_BlockPos);
		if (m_vehicleWorld != NULL)
		{
			m_vehicleWorld->Event2().Emit<WCoord&, const WCoord&>("ConvertWCoord", origin, m_BlockPos);
		}


		int id = 10;
		float w = BLOCK_SIZE / 4; //宽度1/4个格子
		//if(findBlockIn6Direction(m_World, m_BlockID, findPos, m_BlockPos, maxDistance))
		if (true)
		{
			int i = 0;
			for (auto it = m_findPos.begin(); it != m_findPos.end(); it++)
			{
				WCoord blockpos = *it;
				WCoord dim;
				if (m_vehicleWorld)
				{
					m_vehicleWorld->Event2().Emit<WCoord&, const WCoord&>("ConvertWCoord", blockpos, blockpos);
					dim = blockpos - origin;
				}
				else
				{
					dim = BlockCenterCoord(blockpos) - origin;
				}

				std::vector<BlockGeomVert>verts1;
				std::vector<BlockGeomVert>verts2;
				std::vector<BlockGeomVert>verts3;
				std::vector<BlockGeomVert>verts4;
				std::vector<unsigned short>indices;

				if (GetISandboxActorSubsystem()->RegionReplicator_GetVertXCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
				{
					//int dir = dim.x > 0 ? 1 : -1;
					//X1												
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//X2
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirY = dim.y > 0 ? 1 : -1;
						verts1[i].pos.y += dim.y;
						verts2[i].pos.y += dim.y;
						verts3[i].pos.y += dim.y;
						verts4[i].pos.y += dim.y;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//X3
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirZ = dim.z > 0 ? 1 : -1;
						verts1[i].pos.z += dim.z;
						verts2[i].pos.z += dim.z;
						verts3[i].pos.z += dim.z;
						verts4[i].pos.z += dim.z;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//X4
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirY = dim.y > 0 ? 1 : -1;
						verts1[i].pos.y -= dim.y;
						verts2[i].pos.y -= dim.y;
						verts3[i].pos.y -= dim.y;
						verts4[i].pos.y -= dim.y;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
				}

				verts1.clear();
				verts2.clear();
				verts3.clear();
				verts4.clear();
				indices.clear();
				if (GetISandboxActorSubsystem()->RegionReplicator_GetVertZCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
				{
					int dir = dim.z > 0 ? 1 : -1;
					//Z1
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Z2
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirY = dim.y > 0 ? 1 : -1;
						verts1[i].pos.y += dim.y;
						verts2[i].pos.y += dim.y;
						verts3[i].pos.y += dim.y;
						verts4[i].pos.y += dim.y;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Z3
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirX = dim.x > 0 ? 1 : -1;
						verts1[i].pos.x += dim.x;
						verts2[i].pos.x += dim.x;
						verts3[i].pos.x += (short)(dim.x + dirX * w);
						verts4[i].pos.x += (short)(dim.x - dirX * w);
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Z4
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirY = dim.y > 0 ? 1 : -1;
						verts1[i].pos.y -= dim.y;
						verts2[i].pos.y -= dim.y;
						verts3[i].pos.y -= dim.y;
						verts4[i].pos.y -= dim.y;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
				}

				verts1.clear();
				verts2.clear();
				verts3.clear();
				verts4.clear();
				indices.clear();
				if (GetISandboxActorSubsystem()->RegionReplicator_GetVertYCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
				{
					int dir = dim.y > 0 ? 1 : -1;
					//Y1
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Y2
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirX = dim.x > 0 ? 1 : -1;
						verts1[i].pos.x += dim.x;
						verts2[i].pos.x += dim.x;
						verts3[i].pos.x += dim.x;
						verts4[i].pos.x += dim.x;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Y3
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirZ = dim.z > 0 ? 1 : -1;
						verts1[i].pos.z += dim.z;
						verts2[i].pos.z += dim.z;
						verts3[i].pos.z += dim.z;
						verts4[i].pos.z += dim.z;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

					//Y4
					for (int i = 0; i < (int)verts1.size(); i++)
					{
						int dirX = dim.x > 0 ? 1 : -1;
						verts1[i].pos.x -= dim.x;
						verts2[i].pos.x -= dim.x;
						verts3[i].pos.x -= dim.x;
						verts4[i].pos.x -= dim.x;
					}
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
					m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
				}
			}
		}
	}
}
//-----------------------------------------------------------------------------------------------------------------

WorldStorageBox::WorldStorageBox() : WorldContainer(STORAGE_START_INDEX), m_AppendBox(NULL), m_ParentBox(NULL), m_GridCount(0), m_isNeedDestroyWhenEmpty(false)
{
	for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

WorldStorageBox::WorldStorageBox(const WCoord& blockpos) : WorldContainer(blockpos, STORAGE_START_INDEX),
m_AppendBox(NULL), m_ParentBox(NULL), m_GridCount(0), m_isNeedDestroyWhenEmpty(false)
{
	for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

WorldStorageBox::~WorldStorageBox()
{
}

void WorldStorageBox::leaveWorld()
{
	//只使用主box的m_OpenUINs
	if (m_ParentBox)
	{
		m_ParentBox->resetOpenUINs();
		m_ParentBox->append(NULL);
		m_World = nullptr;
		return;
	}

	append(NULL);

	WorldContainer::leaveWorld();
}

int WorldStorageBox::getGridCount()
{
	if (m_GridCount > 0) return m_GridCount;

	if (m_ParentBox || m_AppendBox)
	{
		return STORAGEBOX_CAPACITY * 2;
	}
	return STORAGEBOX_CAPACITY;
}

int WorldStorageBox::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	if (pItemInfos)
	{
		for (int i = 0; i < STORAGEBOX_CAPACITY * 2; i++)
		{
			BackPackGrid* tgrid = nullptr;
			if (i < STORAGEBOX_CAPACITY)
			{
				tgrid = m_ParentBox ? &m_ParentBox->m_Grids[i] : &m_Grids[i];
			}
			else
			{
				if (m_ParentBox) tgrid = &m_Grids[i - STORAGEBOX_CAPACITY];
				else if (m_AppendBox) tgrid = &m_AppendBox->m_Grids[i - STORAGEBOX_CAPACITY];
			}

			if (tgrid && !tgrid->isEmpty())
			{
				GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), tgrid, i + STORAGE_START_INDEX);
			}
		}
	}

	if (m_GridCount > 0)
	{
		return m_GridCount;
	}
	else
	{
		return (m_ParentBox != nullptr || m_AppendBox != nullptr) ? STORAGEBOX_CAPACITY * 2 : STORAGEBOX_CAPACITY;
	}
}

void WorldStorageBox::syncItemUserdata(IClientPlayer* player)
{
	assert(player);

	for (int i = 0; i < STORAGEBOX_CAPACITY * 2; i++)
	{
		BackPackGrid* tgrid = nullptr;
		if (i < STORAGEBOX_CAPACITY)
		{
			tgrid = m_ParentBox ? &m_ParentBox->m_Grids[i] : &m_Grids[i];
		}
		else
		{
			if (m_ParentBox) tgrid = &m_Grids[i - STORAGEBOX_CAPACITY];
			else if (m_AppendBox) tgrid = &m_AppendBox->m_Grids[i - STORAGEBOX_CAPACITY];
		}

		if (tgrid && !tgrid->isEmpty()
			&& !tgrid->userdata_str.empty())
		{
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(player->getUin());
			itemGridUserData.set_gridindex(tgrid->getIndex());
			itemGridUserData.set_userdatastr(tgrid->userdata_str.c_str(), tgrid->userdata_str.size());

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
		}
	}
}

int WorldStorageBox::onInsertItem(const BackPackGrid& grid, int num, int params)
{
	//if (dynamic_cast<ChestMaterial*>(getBlockMtl()) == NULL) return 0;
	bool issame = false;
	GetISandboxActorSubsystem()->IsBlockType(issame, getBlockMtl(), BlockCheckType::BLOCK_CHEST_MATER);
	if (!issame) return 0;

	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;

	GridCopyData gridcopydata(&grid);
	gridcopydata.num = num;
	//gridcopydata.userdata = NULL;
	//gridcopydata.userdata_str = "";//NULL;
	return container->addItem_byGridCopyData(gridcopydata);//修改 通过新增接口的结构体传递格子信息  便于扩展数据  code by:tanzhenyu
}

bool WorldStorageBox::canInsertItem(const BackPackGrid& grid, int param)
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	int numgrid = getGridCount();
	if (numgrid > STORAGEBOX_CAPACITY) numgrid = STORAGEBOX_CAPACITY;
	BackPackGrid cgrid;
	cgrid.setItem(grid.getItemID(), grid.getNum());
	int num = CheckInsertItemIntoArray(container, &m_Grids[0], numgrid, cgrid);
	if (num > 0)
	{
		if (!m_AppendBox)
		{
			return false;
		}
		cgrid.setNum(num);
		return CheckInsertItemIntoArray(container, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, cgrid) <= 0;
	}
	return true;
}

bool WorldStorageBox::checkPutItem(int itemid, int num)
{
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (!def) return false;

	int putnum = 0;
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.isEmpty())
		{
			putnum += def->StackMax;
			if (putnum >= num) return true;
		}
		else if (grid.getItemID() == itemid)
		{
			putnum += (def->StackMax - grid.getNum());
			if (putnum >= num) return true;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.isEmpty())
			{
				putnum += def->StackMax;
				if (putnum >= num) return true;
			}
			else if (grid.getItemID() == itemid)
			{
				putnum += (def->StackMax - grid.getNum());
				if (putnum >= num) return true;
			}
		}
	}
	return false;
}

BackPackGrid* WorldStorageBox::getGridByItemID(int itemid)
{
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.getItemID() == itemid)
		{
			return &grid;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.getItemID() == itemid)
			{
				return &grid;
			}
		}
	}

	return NULL;
}

BackPackGrid* WorldStorageBox::onExtractItem(int params)
{
	BlockMaterial* blkmtl = getBlockMtl();
	if (blkmtl == NULL) return NULL;
	//if (dynamic_cast<ChestMaterial*>(blkmtl) == NULL) return NULL;
	bool issame = false;
	GetISandboxActorSubsystem()->IsBlockType(issame, blkmtl, BlockCheckType::BLOCK_CHEST_MATER);
	if (!issame) return nullptr;
	
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return NULL;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && !grid->isEmpty()) return grid;
	}
	setAppendBox(blkmtl->m_BlockResID);
	return NULL;
}

void WorldStorageBox::setAppendBox(int blockid)
{
	if (m_World && (isHorizontalBigChest(blockid) || isVerticalBigChest(blockid)))
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		int placedir = blockdata & 3;
		int ismirror = (blockdata & 4) > 0;
		WCoord tempPos;
		bool ismajor = true;
		if (isHorizontalBigChest(blockid))
		{
			ismajor = !ismirror;
			DirectionType face = DIR_NOT_INIT;
			if (placedir == DIR_NEG_X) face = DIR_POS_Z;
			if (placedir == DIR_POS_X) face = DIR_NEG_Z;
			if (placedir == DIR_NEG_Z) face = DIR_NEG_X;
			if (placedir == DIR_POS_Z) face = DIR_POS_X;

			if (ismirror) //righter
				tempPos = NeighborCoord(m_BlockPos, face);
			else
				tempPos = NeighborCoord(m_BlockPos, ReverseDirection(face));
		}
		else if (isVerticalBigChest(blockid))
		{
			ismajor = ismirror;
			if (ismirror)
				tempPos = NeighborCoord(m_BlockPos, DIR_NEG_Y);
			else
				tempPos = NeighborCoord(m_BlockPos, DIR_POS_Y);
		}

		if (tempPos != m_BlockPos) //取相邻位置的方块
		{
			if (m_World->getBlockID(tempPos) == blockid)
			{
				WorldContainer* pContainer = m_World->getContainerMgr()->getContainer(tempPos);
				WorldStorageBox* pStorageBox = dynamic_cast<WorldStorageBox*>(pContainer);
				if (pStorageBox != NULL && pStorageBox->m_AppendBox == NULL)
				{
					bool isEmpty = true;
					int grids = pStorageBox->getGridCount();
					for (int i = 0; i < grids; i++)
					{
						BackPackGrid* grid = pStorageBox->index2Grid(i + STORAGE_START_INDEX);
						if (grid && !grid->isEmpty())
						{
							isEmpty = false;
						}
					}
					if (!isEmpty)
					{
						if (ismajor) //当前方块为主箱 左方块或下方块
						{
							WorldStorageBox* pCurrBox = dynamic_cast<WorldStorageBox*>(this);
							if (pCurrBox != NULL) pCurrBox->append(pStorageBox);
						}
						else
						{
							pStorageBox->append(dynamic_cast<WorldStorageBox*>(this));
						}
					}
				}
			}
		}
	}
}

flatbuffers::Offset<FBSave::ContainerStorage> WorldStorageBox::saveContainerStorage(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerStorage(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_GridCount);
	return actor;
}

void WorldStorageBox::onSubtractItem(BackPackGrid* grid, int num)
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* ptmp = container->index2Grid(i + STORAGE_START_INDEX);
		if (ptmp == grid)
		{
			assert(num <= grid->getNum());
			grid->addNum(-num);
			if (grid->getNum() == 0) grid->clear();

			container->afterChangeGrid(i + STORAGE_START_INDEX);
			break;
		}
	}
}

int WorldStorageBox::calComparatorInputOverride()
{
	if (m_ParentBox) return m_ParentBox->calComparatorInputOverride();
	else
	{
		int count = m_AppendBox ? STORAGEBOX_CAPACITY * 2 : STORAGEBOX_CAPACITY;
		const BackPackGrid* grids[STORAGEBOX_CAPACITY * 2];

		for (int i = 0; i < count; i++)
		{
			grids[i] = index2Grid(STORAGE_START_INDEX + i);
		}

		return CalculateItemsComparatorInput(grids, count);
	}
}

BackPackGrid* WorldStorageBox::index2Grid(int index)
{
	assert(index >= STORAGE_START_INDEX);
	index -= STORAGE_START_INDEX;

	if (index >= getGridCount()) return NULL;

	if (index < STORAGEBOX_CAPACITY) return &m_Grids[index];
	else if (m_AppendBox && index < STORAGEBOX_CAPACITY * 2)
	{
		assert(index < 60 && m_AppendBox != NULL);
		return &m_AppendBox->m_Grids[index - STORAGEBOX_CAPACITY];
	}
	return NULL;
}

void WorldStorageBox::afterChangeGrid(int index)
{
	//assert(m_ParentBox == NULL); //主box才会被调用

	WorldContainer::afterChangeGrid(index);

	if (GetWorldManagerPtr()->isGameMakerMode())
	{
		if (m_BlockMtl && m_BlockMtl->m_BlockResID == BLOCK_INITITEMBOX)
		{
			if (m_BlockPos == GetWorldManagerPtr()->m_RuleMgr->getInitItemBoxPos())
			{
				GetWorldManagerPtr()->m_RuleMgr->resetGameInitItems(true, m_Grids, STORAGEBOX_CAPACITY);
			}
		}
		else if (m_BlockMtl && m_BlockMtl->m_BlockResID == BLOCK_REVIVEITEMBOX)
		{
			if (m_BlockPos == GetWorldManagerPtr()->m_RuleMgr->getReviveItemBoxPos())
			{
				GetWorldManagerPtr()->m_RuleMgr->resetGameInitItems(false, m_Grids, STORAGEBOX_CAPACITY);
			}
		}
	}

	if (m_AttachToUI)
	{
		//GetGameEventQue().postBackpackChange(index);
		//m_NeedSave =true;

	/*
		int offset = index-STORAGE_START_INDEX;
		if(offset < 30) g_CSMgr->sendChangeStorageBoxItem(this, offset);
		else if(m_AppendBox)
		{
			g_CSMgr->sendChangeStorageBoxItem(m_AppendBox, offset-30);
		} */
		//ge GetGameEventQue().postStorageboxUpdatePoint(index);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", index);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_UPDATE_STORAGEBOX_POINT", sandboxContext);
		}
	}
}

void WorldStorageBox::dropItems()
{
	for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

void WorldStorageBox::dropItems(WCoord BlockPos)
{
	for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
	{
		dropOneItem(m_Grids[i], BlockPos);
	}
}

void WorldStorageBox::addOpenUIN(int uin)
{
	if (m_ParentBox)
	{
		m_ParentBox->addOpenUIN(uin);
	}
	else WorldContainer::addOpenUIN(uin);
}

void WorldStorageBox::removeOpenUIN(int uin)
{
	if (m_ParentBox)
	{
		m_ParentBox->removeOpenUIN(uin);
	}
	else WorldContainer::removeOpenUIN(uin);
}

bool WorldStorageBox::canPutItem(int index)
{
	return true;
}

void WorldStorageBox::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
	{
		//ge GetGameEventQue().postBackpackChange(STORAGE_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", STORAGE_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	if (m_AppendBox)
	{
		m_AppendBox->m_AttachToUI = true;
		for (int i = 0; i < STORAGEBOX_CAPACITY; i++)
		{
			//ge GetGameEventQue().postBackpackChange(STORAGE_START_INDEX + i + STORAGEBOX_CAPACITY);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("grid_index", STORAGE_START_INDEX + i + STORAGEBOX_CAPACITY);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
			}
		}
	}
}

static void SetStorageBlockClosed(World* pworld, const WCoord& pos)
{
	int blockid = pworld->getBlockID(pos.x, pos.y, pos.z);
	int blockdata = pworld->getBlockData(pos.x, pos.y, pos.z);

	// 大储物箱data数据新增meshIndex 不能抹掉 code-by:lizb
	if (isHorizontalBigChest(blockid) || isVerticalBigChest(blockid))
	{
		blockdata = blockdata & 7;
	}
	else
	{
		blockdata = blockdata % 4;
	}

	pworld->setBlockData(pos.x, pos.y, pos.z, blockdata);
}
void WorldStorageBox::onDetachUI()
{
	m_AttachToUI = false;

	//g_CSMgr->sendOpenStorageBox(this, false);
	SetStorageBlockClosed(m_World, m_BlockPos);

	if (m_AppendBox)
	{
		m_AppendBox->m_AttachToUI = false;
		//g_CSMgr->sendOpenStorageBox(m_AppendBox, false);
		SetStorageBlockClosed(m_World, m_AppendBox->m_BlockPos);
	}
}

void WorldStorageBox::append(WorldStorageBox* box)
{
	if (box && (box->m_ParentBox || box->m_AppendBox))
		return;
	if (m_AppendBox) m_AppendBox->m_ParentBox = NULL;
	m_AppendBox = box;
	if (m_AppendBox)
	{
		m_AppendBox->resetOpenUINs();
		m_AppendBox->m_ParentBox = this;
	}
}

BackPackGrid* WorldStorageBox::genRandomGrid(bool clear_others)
{
	std::vector<int>indices;
	for (int i = 0; i < sizeof(m_Grids) / sizeof(BackPackGrid); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty())
		{
			indices.push_back(i);
		}
	}

	if (indices.empty()) return NULL;
	else
	{
		int sel = GenRandomInt(indices.size());
		if (clear_others)
		{
			for (int i = 0; i < (int)indices.size(); i++)
			{
				if (i == sel) continue;
				m_Grids[indices[i]].clear();
			}
		}
		return &m_Grids[indices[sel]];
	}
}

bool WorldStorageBox::checkEmptyGrid(int resid)
{
	for (int i = 0; i < (int)(sizeof(m_Grids) / sizeof(BackPackGrid)); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (grid.isEmpty())
		{
			return true;
		}
		if (grid.getItemID() == resid)
		{
			return true;
		}
	}
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (grid.isEmpty())
			{
				return true;
			}
			if (grid.getItemID() == resid)
			{
				return true;
			}
		}
	}
	return false;
}

bool WorldStorageBox::isCompletelyEmpty()
{
	// 检查主储物箱是否为空
	for (int i = 0; i < (int)(sizeof(m_Grids) / sizeof(BackPackGrid)); i++)
	{
		BackPackGrid& grid = m_Grids[i];
		if (!grid.isEmpty())
		{
			return false;
		}
	}
	
	// 检查附加储物箱是否为空（如果存在）
	if (m_AppendBox != NULL)
	{
		for (int i = 0; i < sizeof(m_AppendBox->m_Grids) / sizeof(BackPackGrid); i++)
		{
			BackPackGrid& grid = m_AppendBox->m_Grids[i];
			if (!grid.isEmpty())
			{
				return false;
			}
		}
	}
	
	return true;
}

bool WorldStorageBox::isNeedDestroyWhenEmpty()
{
	return m_isNeedDestroyWhenEmpty;
}

void WorldStorageBox::setItem(int offset, int resid, int num, const char* userdata)
{
	assert(offset >= 0 && offset < sizeof(m_Grids) / sizeof(BackPackGrid));

	BackPackGrid& grid = m_Grids[offset];
	SetBackPackGrid(grid, resid, num);
	grid.setUserdataStr(userdata);

	afterChangeGrid(grid.getIndex());
}

int WorldStorageBox::addItem_byGridCopyData(const GridCopyData& gridcopydata)
{
	int num = gridcopydata.num;
	int numgrid = getGridCount();
	if (numgrid > STORAGEBOX_CAPACITY) numgrid = STORAGEBOX_CAPACITY;

	int sum = InsertItemToSameGrids(this, 0, &m_Grids[0], numgrid, gridcopydata.resid, num);
	if (sum < num && m_AppendBox != NULL)
	{
		sum += InsertItemToSameGrids(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, gridcopydata.resid, num - sum);
	}

	if (sum < num)
	{
		GridCopyData tmpdata(gridcopydata);
		tmpdata.num = num - sum;
		sum += InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[0], numgrid, tmpdata);
		if (sum < num && m_AppendBox != NULL)
		{
			GridCopyData tmpdata2(gridcopydata);
			tmpdata2.num = num - sum;
			sum += InsertItemToEmptyGrids_byGridCopyData(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, tmpdata2);
		}
	}

	return sum;
}

//int WorldStorageBox::addItem(int resid, int num, int durable, int toughness, int enchantnum, const int enchants[], void* userdata, const char* userdata_str)
//{
//	int numgrid = getGridCount();
//	if (numgrid > STORAGEBOX_CAPACITY) numgrid = STORAGEBOX_CAPACITY;
//
//	int sum = InsertItemToSameGrids(this, 0, &m_Grids[0], numgrid, resid, num);
//	if (sum < num && m_AppendBox != NULL)
//	{
//		sum += InsertItemToSameGrids(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, resid, num - sum);
//	}
//
//	if (sum < num)
//	{
//		sum += InsertItemToEmptyGrids(this, 0, &m_Grids[0], numgrid, resid, num - sum, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//		if (sum < num && m_AppendBox != NULL)
//		{
//			sum += InsertItemToEmptyGrids(this, STORAGEBOX_CAPACITY, &m_AppendBox->m_Grids[0], STORAGEBOX_CAPACITY, resid, num - sum, durable, toughness, enchantnum, enchants, userdata, userdata_str);
//		}
//	}
//
//	return sum;
//}

void WorldStorageBox::clear()
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && !grid->isEmpty())
		{
			grid->clear();
			container->afterChangeGrid(grid->getIndex());
		}
	}
}

int WorldStorageBox::addItemByCount(int itemid, int count)
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return 0;

	GridCopyData copydata;
	copydata.resid = itemid;
	copydata.num = count;

	ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def)
	{
		int result = GetISandboxActorSubsystem()->AddGridData(this, itemid, copydata);

		if (result >= 0)
		{
			return result;
		}
	}

	return addItem_byGridCopyData(copydata);
}

void WorldStorageBox::removeItemByCount(int itemid, int count)
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;
	int maxgrids = container->getGridCount();

	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = container->index2Grid(i + STORAGE_START_INDEX);
		if (grid && grid->getItemID() == itemid)
		{
			if (count >= grid->getNum())
			{
				count -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				container->afterChangeGrid(grid->getIndex());
			}
			else
			{
				grid->addNum(-count);
				container->afterChangeGrid(grid->getIndex());
				return;
			}
		}
	}
}

void WorldStorageBox::removeItemByIndex(int index, int num)
{
	WorldStorageBox* container = m_ParentBox != NULL ? m_ParentBox : this;
	if (container == NULL) return;

	BackPackGrid* pgrid = container->index2Grid(index);
	if (pgrid == NULL) return;

	if (num > pgrid->getNum()) num = pgrid->getNum();

	if (pgrid->addNum(-num) == 0) pgrid->clear();

	container->afterChangeGrid(index);
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldStorageBox::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerStorage(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_GridCount, m_isNeedDestroyWhenEmpty);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerStorage, actor.Union());
}

bool WorldStorageBox::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerStorage*>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}

	m_GridCount = src->key();

	m_isNeedDestroyWhenEmpty = src->clearflag();

	return true;
}

WorldStorageBoxPassword::WorldStorageBoxPassword() : m_nPassWord(-1)
{
}

WorldStorageBoxPassword::WorldStorageBoxPassword(const WCoord& blockpos) : WorldStorageBox(blockpos), m_nPassWord(-1)
{
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldStorageBoxPassword::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerStoragePassword(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_GridCount, m_nPassWord);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerStoragePassword, actor.Union());
}

bool WorldStorageBoxPassword::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerStoragePassword*>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}

	m_GridCount = src->key();
	m_nPassWord = src->password();

	return true;
}
//-----------------------------------------------------------------------------------------------------------------
WorldContainerMgr::WorldContainerMgr(World* world) : m_World(world), m_Containers(257)
{
	m_ScanningContainers = false;
	m_EmptyBoxCleanupInterval = 100; // 100 = 20 * 5
	m_EmptyBoxCleanupEnabled = true;  // 默认启用空储物箱清理
	m_EmptyBoxCleanupTimer = 0;       // 初始化定时器
}

WorldContainerMgr::~WorldContainerMgr()
{
}

void WorldContainerMgr::addContainerByServer(WorldContainer* container)
{
	Chunk* pchunk = m_World->getChunk(container->m_BlockPos);
	if (pchunk == NULL) return;

	WorldContainer* oldcontainer = getContainerExt(container->m_BlockPos);
	if (oldcontainer) doDeleteContainer(oldcontainer); //setContainerNeedClear 在客机上会出现时机问题


	m_Containers[container->m_BlockPos] = container;
	pchunk->addContainer(container);
	container->enterWorld(m_World);
}

void WorldContainerMgr::spawnContainer(WorldContainer* container, bool sync_spawn)
{
	Chunk* pchunk = m_World->getChunk(container->m_BlockPos);
	if (pchunk == NULL) {
		LOG_INFO("spawnContainer %d, %d, %d, pchunk == NULL. NOT spawn", container->m_BlockPos.x, container->m_BlockPos.y, container->m_BlockPos.z);
		return;
	}

	// 先把老的Container干掉
	WorldContainer* oldcontainer = getContainerExt(container->m_BlockPos);
	if (oldcontainer && container != oldcontainer)
	{
		//containerMgr在遍历中, 并且oldcontainer已经标记为销毁了, 就不进行删除了
		if (!(m_ScanningContainers && oldcontainer->m_NeedClear))
		{
			doDeleteContainer(oldcontainer);
			//setContainerNeedClear(oldcontainer);
		}
	}
	container->m_ObjId = GetISandboxActorSubsystem()->GenNextObjId();
	if (m_ScanningContainers) m_AddContainers.push_back(container);
	else m_Containers[container->m_BlockPos] = container;

	pchunk->addContainer(container);
	container->enterWorld(m_World);
}

void WorldContainerMgr::destroyContainer(const WCoord& pos, bool needDropItem)
{
	//LOG_INFO("destroyContainer %d, %d, %d", pos.x, pos.y, pos.z);
	ContainHashTable::Element* ele = m_Containers.find(pos);
	if (ele)
	{
		ele->value->setNeedDropItem(needDropItem);
		if (!ele->value->m_NeedClear)
		{
			//ele->value->m_NeedClear = true;
			setContainerNeedClear(ele->value);
		}
	}
}

void WorldContainerMgr::setContainerNeedClear(WorldContainer* container)
{
	{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_ContainerMgrLock);
		if (container->m_NeedClear)
		{
			return;
		}

		container->m_NeedClear = true;
	}

	/*{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
		m_DelContainers.insert(container);
	}*/
}

void WorldContainerMgr::clearContainers(bool delboard)
{
	/*{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
		for (auto delcontainer : m_DelContainers)
		{
			doDeleteContainer(delcontainer);
		}
		m_DelContainers.clear();
	}

	{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
        ContainHashTable::Element* iter = m_Containers.iterate(NULL);
		while (iter)
		{
			m_DelContainers.insert(iter->value);
			iter = m_Containers.iterate(iter);
		}
	}

	{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
		for (auto delcontainer : m_DelContainers)
		{
			doDeleteContainer(delcontainer);
		}
		m_DelContainers.clear();
	}*/
	m_DelContainers1.resize(0);

	ContainHashTable::Element* iter = m_Containers.iterate(NULL);
	while (iter)
	{
		WorldContainer* container = iter->value;

		m_DelContainers1.push_back(container);
		iter = m_Containers.iterate(iter);
	}

	for (size_t i = 0; i < m_DelContainers1.size(); i++)
	{
		doDeleteContainer(m_DelContainers1[i], delboard);
	}
}

WorldContainer* WorldContainerMgr::getContainer(const WCoord& pos)
{
	ContainHashTable::Element* ele = m_Containers.find(pos);
	if (ele && !ele->value->m_NeedClear)
	{
		return ele->value;
	}
	else
	{
		// 如果m_ScanningContainers==true，会出现查找不到container的情况
		if (m_ScanningContainers)
		{
			for (int i = 0; i < (int)m_AddContainers.size(); i++)
			{
				if (m_AddContainers[i] && m_AddContainers[i]->m_BlockPos == pos)
				{
					return m_AddContainers[i];
				}
			}
		}
		return NULL;
	}
}

WorldContainer* WorldContainerMgr::getContainerExt(const WCoord& pos)
{
	ContainHashTable::Element* ele = m_Containers.find(pos);
	if (ele)
	{
		return ele->value;
	}
	else return NULL;
}

void WorldContainerMgr::addContainerByChunk(WorldContainer* container)
{
	if (m_Containers.find(container->m_BlockPos) == NULL)
	{
		container->enterWorld(m_World);
		m_Containers[container->m_BlockPos] = container;
	}
	else
	{
		//assert(0);
	}
}

void WorldContainerMgr::removeContainerByChunk(WorldContainer* container)
{
	if (container == NULL) return;

	ContainHashTable::Element* ele = m_Containers.find(container->m_BlockPos);
	if (ele)
	{
		WorldContainer* container = ele->value;
		container->leaveWorld();
		m_Containers.erase(ele);
	}
	else
	{
		ContainHashTable::Element* iter = m_Containers.iterate(NULL);
		while (iter)
		{
			if (iter->value == container)
			{
				container->leaveWorld();
				m_Containers.erase(iter);
				break;
			}
			iter = m_Containers.iterate(iter);
		}
	}

	for (auto iter = m_AddContainers.begin(); iter != m_AddContainers.end(); iter++)
	{
		if ((*iter) == container)
		{
			m_AddContainers.erase(iter);
			break;
		}
	}
	
	/*{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
		m_DelContainers.erase(container);
	}*/
}

void WorldContainerMgr::registerUpdateTickContainer(WorldContainer* container)
{
	//m_UpdateTickContainers.insert(container);
}

void WorldContainerMgr::registerUpdateDisplayContainer(WorldContainer* container)
{
	//m_UpdateDisplayContainers.insert(container);
}

void WorldContainerMgr::unRegisterUpdateTickContainer(WorldContainer* container)
{
	//m_UpdateTickContainers.erase(container);
}

void WorldContainerMgr::unRegisterUpdateDisplayContainer(WorldContainer* container)
{
	//m_UpdateDisplayContainers.erase(container);
}

WorldContainer* WorldContainerMgr::addFurnace(int x, int y, int z)
{
	return spawnFurnace(WCoord(x, y, z), 0, true);
}

void WorldContainerMgr::removeFurnace(int x, int y, int z)
{
	assert(getContainer(WCoord(x, y, z))->getObjType() == OBJ_TYPE_FURNACE);
	destroyContainer(WCoord(x, y, z));
}

WorldContainer* WorldContainerMgr::getFurnace(int x, int y, int z)
{
	WorldContainer* container = getContainer(WCoord(x, y, z));
	if (container)
	{
		assert(container->getObjType() == OBJ_TYPE_FURNACE);
		GetISandboxActorSubsystem()->IsContainerTypeOrCreate(container, ContainerCheckType::CONTAINER_FURNACE, false, WCoord(x, y, z));
		return container;
	}
	else return NULL;
}

WorldContainer* WorldContainerMgr::addContainer(int x, int y, int z, int iType, int iOwner)
{
	WCoord pos(x, y, z);
	WorldContainer* pContainer = NULL;
	switch (iType)
	{
	case OBJ_TYPE_BOX:
		pContainer = SANDBOX_NEW(WorldStorageBox, pos);
		break;
	case OBJ_TYPE_FURNACE:
		GetISandboxActorSubsystem()->IsContainerTypeOrCreate(pContainer, ContainerCheckType::CONTAINER_FURNACE, true, pos);
		break;
	case OBJ_TYPE_FUNNEL:
		GetISandboxActorSubsystem()->IsContainerTypeOrCreate(pContainer, ContainerCheckType::CONTAINER_WORLD_FUNNEL, true, pos);
		break;
	case OBJ_TYPE_EMITTER:
		GetISandboxActorSubsystem()->IsContainerTypeOrCreate(pContainer, ContainerCheckType::CONTAINER_WORLD_EMITTER, true, pos);
		break;
	default:
		return NULL;
	}

	if (!pContainer) { return NULL; }
	pContainer->m_OwnerUin = iOwner;
	spawnContainer(pContainer, true);

	return pContainer;
}

WorldContainer* WorldContainerMgr::getContainer(int x, int y, int z)
{
	WorldContainer* pContainer = getContainer(WCoord(x, y, z));
	if (!pContainer || (pContainer->getObjType() != OBJ_TYPE_BOX && pContainer->getObjType() != OBJ_TYPE_FURNACE
		&& pContainer->getObjType() != OBJ_TYPE_FUNNEL && pContainer->getObjType() != OBJ_TYPE_EMITTER && OBJ_TYPE_HONOR_FRAME != pContainer->getObjType() && OBJ_TYPE_FARMLAND != pContainer->getObjType() && pContainer->getObjType() != OBJ_TYPE_COLORPALETTE
		&& pContainer->getObjType() != OBJ_TYPE_FISHFRAME && pContainer->getObjType() != OBJ_TYPE_SMALL_TORCH && pContainer->getObjType() != OBJ_TYPE_POPULUS_LEAF && pContainer->getObjType() != OBJ_TYPE_PERISTELE && pContainer->getObjType() != OBJ_TYPE_MONSTERSUMMONER)) {
		return NULL;
	}

	return pContainer;
}

void WorldContainerMgr::removeContainer(int x, int y, int z)
{
	destroyContainer(WCoord(x, y, z));
}

WorldStringContainer* WorldContainerMgr::getStringContainer(int x, int y, int z)
{
	WorldContainer* pContainer = getContainer(WCoord(x, y, z));
	if (!pContainer || (pContainer->getObjType() != OBJ_TYPE_STRING))
		return NULL;

	return (dynamic_cast<WorldStringContainer*>(pContainer));
}
WorldContainer* WorldContainerMgr::getSignsContainer(int x, int y, int z)
{
	WorldContainer* pContainer = getContainer(WCoord(x, y, z));
	if (!pContainer || (pContainer->getObjType() != OBJ_TYPE_SIGNS))
		return NULL;

	GetISandboxActorSubsystem()->IsContainerTypeOrCreate(pContainer, ContainerCheckType::CONTAINER_WORLD_SIGNES, false, WCoord(x, y, z));
	return pContainer;
}

void WorldContainerMgr::removeContainerFromAdd(WorldContainer* pContainer)
{
	for (int i = 0; i < m_AddContainers.size(); ++i)
	{
		if (pContainer == m_AddContainers[i])
		{
			m_AddContainers[i] = nullptr;
			break;
		}
	}
}

void WorldContainerMgr::resetContainerByFullyCustomModel(std::string skey)
{
	ContainHashTable::Element* iter = m_Containers.iterate(NULL);
	while (iter)
	{
		WorldContainer* container = iter->value;
		if (container && (container->getObjType() == OBJ_TYPE_FULLYCUSTOMMODEL || container->getObjType() == OBJ_TYPE_ITEMEXPO))
		{
			container->resetEntityBySyncData(skey);
		}
		iter = m_Containers.iterate(iter);
	}
}

void WorldContainerMgr::resetContainerByImportModel()
{
	ContainHashTable::Element* iter = m_Containers.iterate(NULL);
	while (iter)
	{
		WorldContainer* container = iter->value;
		if (container && (container->getObjType() == OBJ_TYPE_IMPORTMODEL || container->getObjType() == OBJ_TYPE_ITEMEXPO || container->getObjType() == OBJ_TYPE_MOD_CONTAINER || container->getObjType() == OBJ_TYPE_MOD_CONTAINER_TRANSFER))
		{
			container->resetEntityBySyncData("");
		}
		iter = m_Containers.iterate(iter);
	}
}

WorldStorageBox* WorldContainerMgr::addStorageBox(int x, int y, int z)
{
	return spawnStorageBox(WCoord(x, y, z), 0, true);
}

class RandomPermutation
{
public:
	RandomPermutation(int n);
	int popNumber(ChunkRandGen& randgen);

	std::vector<int>m_Numbers;
};

RandomPermutation::RandomPermutation(int n)
{
	for (int i = 0; i < n; i++) m_Numbers.push_back(i);
}

int RandomPermutation::popNumber(ChunkRandGen& randgen)
{
	if (m_Numbers.empty()) return -1;

	int i = randgen.get(int(m_Numbers.size()));
	int num = m_Numbers[i];
	m_Numbers[i] = m_Numbers.back();
	m_Numbers.pop_back();

	return num;
}

void WorldContainerMgr::generateChestItems(std::vector<GenerateItemDesc>& items, int chestid, ChunkRandGen* randgen, int selectmethod)
{
	const ChestDef* chests[100];
	int numchest = 0;

	items.resize(0);
	if (randgen == NULL) randgen = GetDefaultRandGen();

	if (selectmethod == 0)
	{
		for (int igroup = 0; igroup < 100; igroup++)
		{
			DefDataTable<ChestDef>& chestDefTable = GetDefManagerProxy()->getChestDefTable();
			const ChestDef* def = chestDefTable.GetRecord(chestid * 100 + igroup + 1);
			if (def == NULL) break;

			if (randgen->get(10000) < def->GroupOdds) chests[numchest++] = def;
		}
	}
	else
	{
		int odds[100];
		for (int igroup = 0; igroup < 100; igroup++)
		{
			DefDataTable<ChestDef>& chestDefTable = GetDefManagerProxy()->getChestDefTable();
			const ChestDef* def = chestDefTable.GetRecord(chestid * 100 + igroup + 1);
			if (def == NULL) break;

			chests[numchest] = def;
			odds[numchest] = def->GroupOdds;
			numchest++;
		}
		int index = SelectFromOddsArray(odds, numchest, randgen);
		if (index < 0) return;

		chests[0] = chests[index];
		numchest = 1;
	}

	GenerateItemDesc desc;
	for (int igroup = 0; igroup < numchest; igroup++)
	{
		const ChestDef* def = chests[igroup];

		if (def->OddsMethod == 0)
		{
			for (int i = 0; i < MAX_CHESTDEF_ITEMS; i++)
			{
				if (def->ItemID[i] == 0) break;
				if (randgen->get(10000) >= def->ItemOdds[i]) continue;

				desc.itemid = def->ItemID[i];
				desc.itemnum = def->ItemNum[i];
				items.push_back(desc);
			}
		}
		else
		{
			int i = SelectFromOddsArray(def->ItemOdds, MAX_CHESTDEF_ITEMS, randgen);
			if (i < 0) continue;

			desc.itemid = def->ItemID[i];
			desc.itemnum = def->ItemNum[i];
			items.push_back(desc);
		}
	}
}

WorldStorageBox* WorldContainerMgr::addDungeonChest(const WCoord& pos, int chestid, ChunkRandGen* randgen)
{
	if (randgen == NULL) randgen = GetDefaultRandGen();

	WorldStorageBox* chest = getStorageBox(pos.x, pos.y, pos.z);
	if (chest == NULL) return NULL;

	chest->clear();

	std::vector<GenerateItemDesc>items;
	generateChestItems(items, chestid, randgen, 0);

	RandomPermutation rp(20);
	WCoord chunkPos = BlockDivSection(pos);
	for (size_t i = 0; i < items.size(); i++)
	{
		std::string userData;
		if (items[i].itemid == ITEM_TREASURE_DRIFT)
		{
			int retx = 0;
			int retz = 0;
			if (m_World->genTreasureBox(chunkPos.x, chunkPos.z, retx, retz))
			{
				char ch[80] = { 0 };
				sprintf(ch, "2x%dz%d", retx, retz);
				userData = ch;
			}
			else
			{
				continue;
			}
		}
		int index = rp.popNumber(*randgen);
		if (index < 0) break;

		chest->setItem(index, items[i].itemid, items[i].itemnum, userData.c_str());
	}

	return chest;
}

void WorldContainerMgr::addRandomItemToChest(const WCoord& pos, int chestid, ChunkRandGen* randgen)
{
	if (randgen == NULL) randgen = GetDefaultRandGen();

	WorldStorageBox* chest = getStorageBox(pos.x, pos.y, pos.z);
	if (chest == NULL)
		return;

	std::vector<GenerateItemDesc>items;
	generateChestItems(items, chestid, randgen, 0);

	RandomPermutation rp(20);
	for (size_t i = 0; i < items.size(); i++)
	{
		int index = rp.popNumber(*randgen);
		if (index < 0) break;

		chest->setItem(index, items[i].itemid, items[i].itemnum);
	}
}

void WorldContainerMgr::removeStorageBox(int x, int y, int z)
{
	assert(getContainer(WCoord(x, y, z))->getObjType() == OBJ_TYPE_BOX);
	destroyContainer(WCoord(x, y, z));
}

WorldStorageBox* WorldContainerMgr::getStorageBox(int x, int y, int z)
{
	WorldContainer* container = getContainerExt(WCoord(x, y, z));
	if (container)
	{
		assert(container->getObjType() == OBJ_TYPE_BOX && dynamic_cast<WorldStorageBox*>(container) != NULL);
		return static_cast<WorldStorageBox*>(container);
	}
	return NULL;
}

WorldStorageBox* WorldContainerMgr::spawnStorageBox(const WCoord& pos, int owneruin, bool sync_spawn)
{
	WorldStorageBox* obj = ENG_NEW(WorldStorageBox)(pos);
	obj->m_OwnerUin = owneruin;

	spawnContainer(obj, sync_spawn);
	return obj;
}

WorldContainer* WorldContainerMgr::spawnFurnace(const WCoord& pos, int owneruin, bool sync_spawn)
{
	WorldContainer* obj = nullptr; // ENG_NEW(FurnaceContainer)(pos);

	GetISandboxActorSubsystem()->IsContainerTypeOrCreate(obj, ContainerCheckType::CONTAINER_FURNACE, true, pos);

	obj->m_OwnerUin = owneruin;

	spawnContainer(obj, sync_spawn);
	return obj;
}

WorldValueContainer* WorldContainerMgr::spawnComparator(const WCoord& pos, int owneruin, bool sync_spawn)
{
	WorldValueContainer* obj = ENG_NEW(WorldValueContainer)(pos);
	obj->m_SubType = 0;
	obj->m_OwnerUin = owneruin;

	spawnContainer(obj, sync_spawn);
	return obj;
}

WorldValueContainer* WorldContainerMgr::getComparator(const WCoord& pos)
{
	Rainbow::HashTable<WCoord, WorldContainer*, WCoordHashCoder>::Element* iter = m_Containers.find(pos);
	if (iter)
	{
		return dynamic_cast<WorldValueContainer*>(iter->value);
	}
	else return NULL;
}

void WorldContainerMgr::updateTick() {
    OPTICK_EVENT();
    //OPTICK_TAG("num", m_Containers.size());
    m_DelContainers1.resize(0);
    m_ScanningContainers = true;

    ContainHashTable::Element* iter = m_Containers.iterate(NULL);
    {
        OPTICK_EVENT("container tick");
        //OPTICK_TAG("container", m_Containers.size());
        METRIC_GAUGE_SET(game_mapplay_info, m_Containers.size(), { "type", "container_total" });
#if USE_OPTICK || USE_METRIC_STATICS
        static tsl::robin_map<const char*, int> containerTypeCount;
        containerTypeCount.clear();
#if USE_OPTICK
        const int max_sorted_count = 3;
        //0最大， n最小
        static const char* sorted_names[max_sorted_count] = { "---", "---", "---" };
        static size_t sorted_count[max_sorted_count] = { 0, 0, 0 };
#endif // USE_OPTICK
#endif 
        while (iter) {
            WorldContainer* container = iter->value;
            if (container == NULL) continue;
            if (container->m_NeedClear) {
                m_DelContainers1.push_back(container);
            }
            else if (m_World && m_World->blockExists(container->m_BlockPos)) {
#if USE_OPTICK || USE_METRIC_STATICS
                auto newCount = containerTypeCount[typeid(*container).name()]++;
                METRIC_DYNAMIC_GAUGE_SET(game_mapplay_info, newCount, { "type", "container" }, { "obj", typeid(*container).name() });
#if USE_OPTICK
                for (int i = 0; i < max_sorted_count; i++) {
                    if (newCount > sorted_count[i]) {
                        sorted_count[i] = newCount;
                        sorted_names[i] = typeid(*container).name();
                        break;
                    }
                }
#endif//USE_OPTICK
#endif
                container->updateTick();
            }

            iter = m_Containers.iterate(iter);
        }
#if USE_OPTICK
        OPTICK_TAG("1st", sorted_names[0]);
        OPTICK_TAG("2nd", sorted_names[1]);
        OPTICK_TAG("3rd", sorted_names[2]);
        OPTICK_TAG("value", sorted_count[0], sorted_count[1], sorted_count[2]);
#endif // USE_OPTICK
    }
    m_ScanningContainers = false;

    for (size_t i = 0; i < m_AddContainers.size(); i++) {
        WorldContainer* container = m_AddContainers[i];
        if (container == NULL) continue;
        m_Containers[container->m_BlockPos] = container;
    }
    m_AddContainers.resize(0);
    for (size_t i = 0; i < m_DelContainers1.size(); i++) {
        doDeleteContainer(m_DelContainers1[i]);
    }

    /*{
        Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
        for (auto delcontainer : m_DelContainers)
        {
            doDeleteContainer(delcontainer);
        }
        m_DelContainers.clear();
    }

    {
        OPTICK_EVENT("container tick");
        OPTICK_TAG("container", m_Containers.size());
#if USE_OPTICK
        static tsl::robin_map<const char*, int> containerTypeCount;
        containerTypeCount.clear();
#endif
        for (auto container : m_UpdateTickContainers)
        {
            if (m_World && m_World->blockExists(container->m_BlockPos))
            {
#if USE_OPTICK
                containerTypeCount[typeid(*container).name()]++;
#endif
                container->updateTick();
            }
        }

#if USE_OPTICK
        //将最多的3个容器类型输出
        std::vector<std::pair<const char*, int>> containerTypeCountVec(containerTypeCount.begin(), containerTypeCount.end());
        std::sort(containerTypeCountVec.begin(), containerTypeCountVec.end(), [](const std::pair<const char*, int>& a, const std::pair<const char*, int>& b) { return a.second > b.second; });
        const char* _1stname = "---";
        int _1stValue = 0;
        if (containerTypeCountVec.size() > 0)
        {
            _1stname = containerTypeCountVec[0].first;
            _1stValue = containerTypeCountVec[0].second;
        }
        OPTICK_TAG("1st", _1stname);
        const char* _2ndname = "---";
        int _2ndValue = 0;
        if (containerTypeCountVec.size() > 1)
        {
            _2ndname = containerTypeCountVec[1].first;
            _2ndValue = containerTypeCountVec[1].second;
        }
        OPTICK_TAG("2nd", _2ndname);
        const char* _3rdname = "---";
        int _3rdValue = 0;
        if (containerTypeCountVec.size() > 2)
        {
            _3rdname = containerTypeCountVec[2].first;
            _3rdValue = containerTypeCountVec[2].second;
        }
        OPTICK_TAG("3rd", _3rdname);
        OPTICK_TAG("value", _1stValue, _2ndValue, _3rdValue);
#endif
    }*/

	updateStatistics();
	
	// 定时清理空储物箱
	if (m_EmptyBoxCleanupEnabled) {
		m_EmptyBoxCleanupTimer++;
		if (m_EmptyBoxCleanupTimer >= m_EmptyBoxCleanupInterval) {
			m_EmptyBoxCleanupTimer = 0;
			cleanupEmptyStorageBoxes();
		}
	}
}

void WorldContainerMgr::cleanupEmptyStorageBoxes()
{
	if (!m_World) return;
	
	std::vector<WorldContainer*> emptyBoxesToDelete;
	
	// 遍历所有容器，查找空的储物箱
	ContainHashTable::Element* iter = m_Containers.iterate(NULL);
	while (iter) {
		WorldContainer* container = iter->value;
		if (container && !container->m_NeedClear) {
			// 检查是否为储物箱类型
			if (container->getObjType() == OBJ_TYPE_BOX) {
				WorldStorageBox* storageBox = dynamic_cast<WorldStorageBox*>(container);
				
				// 添加调试信息
				//if (!storageBox) {
				//	CONTAINER_LOG("Dynamic cast failed! Container at (%d,%d,%d)", 
				//		container->m_BlockPos.x, container->m_BlockPos.y, container->m_BlockPos.z);
				//	CONTAINER_LOG("  - Type name: %s", typeid(*container).name());
				//	CONTAINER_LOG("  - Container name: %s", container->getContainerName());
				//	CONTAINER_LOG("  - Object type: %d", container->getObjType());
				//	
				//	// 尝试其他可能的储物箱类型
				//	if (auto passwordBox = dynamic_cast<WorldStorageBoxPassword*>(container)) {
				//		CONTAINER_LOG("  - Found WorldStorageBoxPassword");
				//		storageBox = passwordBox;
				//	}
				//}
				CONTAINER_LOG("Container type = %s", typeid(*container).name());
				if (storageBox && !storageBox->isOpenning() && storageBox->isCompletelyEmpty() && storageBox->isNeedDestroyWhenEmpty()) {
					// 储物箱完全为空且未被玩家打开，标记删除
					emptyBoxesToDelete.push_back(container);
				}
			}
		}
		iter = m_Containers.iterate(iter);
	}
	
	// 删除空的储物箱
	for (WorldContainer* container : emptyBoxesToDelete) {
		// 不掉落物品，因为储物箱本身就是空的
		//container->setNeedDropItem(false);
		//setContainerNeedClear(container);
		m_World->destroyBlock(container->m_BlockPos.x, container->m_BlockPos.y, container->m_BlockPos.z, false);
		m_World->setBlockAll(container->m_BlockPos, 0, 0, 3, true);
	}
	
	// 如果删除了任何储物箱，输出日志信息
	if (!emptyBoxesToDelete.empty()) {
		LOG_INFO("check container: has deleted %d storage box.", (int)emptyBoxesToDelete.size());
	}
}

void WorldContainerMgr::doDeleteContainer(WorldContainer* container, bool delboard)
{
	if (container == NULL) return;
	if (m_World == NULL) return;
	if (!m_World->isRemoteMode()) {
		if (container->getNeedDropItem())
			container->dropItems();
	}
	container->leaveWorld();
	auto ele = m_Containers.find(container->m_BlockPos);
	if (ele && ele->value == container)
	{
		m_Containers.erase(ele);
	}
	else//对于极其特殊的情况进行处理,避免后面的崩溃
	{
		ContainHashTable::Element* iter = m_Containers.iterate(NULL);
		while (iter)
		{
			if (iter->value == container)
			{
				m_Containers.erase(iter);
				break;
			}
			iter = m_Containers.iterate(iter);
		}
	}

	for (auto iter = m_AddContainers.begin(); iter != m_AddContainers.end(); iter++)
	{
		if ((*iter) == container)
		{
			m_AddContainers.erase(iter);
			break;
		}
	}

	Chunk* pchunk = m_World->getChunk(container->m_BlockPos);
	assert(pchunk);
	if (pchunk) pchunk->removeContainer(container, true, delboard);
}

void WorldContainerMgr::updateDisplay(float dtime)
{
	OPTICK_EVENT();
	/*m_ScanningContainers = true;
	m_ScanningContainers = false;

	for (size_t i = 0; i < m_AddContainers.size(); i++)
	{
		WorldContainer* container = m_AddContainers[i];
		if (container == NULL) continue;
		m_Containers[container->m_BlockPos] = container;
	}
	m_AddContainers.resize(0);

	if (m_DelContainers.size() > 0)
	{
		Rainbow::ReadWriteLock::AutoWriteLock locker(m_DelContainerLock);
		for (auto delcontainer : m_DelContainers)
		{
			doDeleteContainer(delcontainer);
		}
		m_DelContainers.clear();
	}

	for (auto container : m_UpdateDisplayContainers)
	{
		container->updateDisplay(dtime);
	}*/

	m_DelContainers1.resize(0);
	m_ScanningContainers = true;
	ContainHashTable::Element* iter = m_Containers.iterate(NULL);
	while (iter)
	{
		WorldContainer* container = iter->value;
		if (container) container->updateDisplay(dtime);

		iter = m_Containers.iterate(iter);
	}
	m_ScanningContainers = false;

	for (size_t i = 0; i < m_AddContainers.size(); i++)
	{
		WorldContainer* container = m_AddContainers[i];
		if (container == NULL) continue;
		m_Containers[container->m_BlockPos] = container;
	}
	m_AddContainers.resize(0);

	for (size_t i = 0; i < m_DelContainers1.size(); i++)
	{
		doDeleteContainer(m_DelContainers1[i]);
	}
}

WorldContainer* WorldContainerMgr::GetContainer(int x, int y, int z, int type)
{
	WorldContainer* pContainer = getContainer(WCoord(x, y, z));
	if (!pContainer || (pContainer->getObjType() != type))
		return NULL;
	else
		return pContainer;
}

//-----------------------------------------------------WorldBookCabinet--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldBookCabinet
功    能：书架 存放书籍
********************************************************************************************* */
WorldBookCabinet::WorldBookCabinet() : WorldContainer(BOOKCABINET_START_INDEX)
{
	for (int i = 0; i < BOOKCABINET_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

WorldBookCabinet::WorldBookCabinet(const WCoord& blockpos) : WorldContainer(blockpos, BOOKCABINET_START_INDEX)
{
	for (int i = 0; i < BOOKCABINET_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldBookCabinet::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	auto actor = FBSave::CreateContainerStorage(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count));

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerBookCabinet, actor.Union());
}

bool WorldBookCabinet::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerStorage*>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}

	return true;
}

void WorldBookCabinet::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
}

BackPackGrid* WorldBookCabinet::index2Grid(int index)
{
	assert(index >= BOOKCABINET_START_INDEX);
	index -= BOOKCABINET_START_INDEX;

	if (index >= getGridCount()) return NULL;

	if (index < BOOKCABINET_CAPACITY) return &m_Grids[index];
	return NULL;
}

int WorldBookCabinet::getGridCount()
{
	//if(m_GridCount > 0) return m_GridCount;

	return BOOKCABINET_CAPACITY;
}

void WorldBookCabinet::dropItems()
{
	for (int i = 0; i < BOOKCABINET_CAPACITY; i++)
	{
		dropOneItem(m_Grids[i]);
	}
}

void WorldBookCabinet::syncItemUserdata(IClientPlayer* player)
{
	assert(player);

	for (int i = 0; i < BOOKCABINET_CAPACITY; i++)
	{
		BackPackGrid* tgrid = nullptr;
		tgrid = &m_Grids[i];

		if (tgrid && !tgrid->isEmpty()
			&& !tgrid->userdata_str.empty())
		{
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(player->getUin());
			itemGridUserData.set_gridindex(tgrid->getIndex());
			itemGridUserData.set_userdatastr(tgrid->userdata_str.c_str(), tgrid->userdata_str.size());

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
		}
	}
}

int WorldBookCabinet::getBookNum()
{
	int num = 0;
	for (int i = 0; i < BOOKCABINET_CAPACITY; i++)
	{
		if (m_Grids[i].def)
			num++;
	}

	return num;
};

//-----------------------------------------------------WorldBonFire--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldBonFire
功    能：篝火 可以烤肉
********************************************************************************************* */
WorldBonFire::WorldBonFire() : WorldContainer(BONFIRE_START_INDEX)
{
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	m_HeatValue = 0;
	m_iPlaySoundTime = 0;
	changeRoastState(ROAST_EMPTY);
	m_iSoundEatRef = 0;
	m_iSoundDanceRef = 0;
	//m_bSoundEatPlayed = false;
	m_bSoundDancePlayed = false;
	m_iPlayTick = 0;
	memset(m_RoastState, 0, sizeof(m_RoastState));
	memset(m_RoastCountdown, 0, sizeof(m_RoastCountdown));

	m_Progress = NULL;
	for (int i = 0; i < BONFIRE_ICONNUM; ++i)
	{
		m_FireIcon[i] = NULL;
	}
#ifndef IWORLD_SERVER_BUILD
	m_Progress = Rainbow::ProgressBarWithText3D::Create(7, 700, 75, 700.0f, 75.0f, true, true);
	m_Progress->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
	for (int i = 0; i < BONFIRE_ICONNUM; ++i)
	{
		m_FireIcon[i] = Rainbow::Image3D::Create(700, 75, 700.0f, 75.0f, true, true);
		m_FireIcon[i]->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
	}
#endif // !IWORLD_SERVER_BUILD

	m_LastFireStatus = 0;
	m_LastRoastState = 0;
}

WorldBonFire::WorldBonFire(const WCoord& blockpos) : WorldContainer(blockpos, BONFIRE_START_INDEX)
{
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	m_HeatValue = 0;
	m_iPlaySoundTime = 0;
	changeRoastState(ROAST_EMPTY);
	m_iSoundEatRef = 0;
	m_iSoundDanceRef = 0;
	m_iPlayTick = 0;
	memset(m_RoastState, 0, sizeof(m_RoastState));
	memset(m_RoastCountdown, 0, sizeof(m_RoastCountdown));

	m_Progress = NULL;
	for (int i = 0; i < BONFIRE_ICONNUM; ++i)
	{
		m_FireIcon[i] = NULL;
	}

#ifndef IWORLD_SERVER_BUILD
	m_Progress = Rainbow::ProgressBarWithText3D::Create(7, 700, 75, 700.0f, 75.0f, true, true);
	m_Progress->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
	for (int i = 0; i < BONFIRE_ICONNUM; ++i)
	{
		m_FireIcon[i] = Rainbow::Image3D::Create(700, 75, 700.0f, 75.0f, true, true);
		m_FireIcon[i]->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_HUD);
	}
#endif // !IWORLD_SERVER_BUILD

}

WorldBonFire::~WorldBonFire()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Progress);
	for (int i = 0; i < BONFIRE_ICONNUM; ++i)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_FireIcon[i]);
	}
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldBonFire::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	unsigned char indices[NUMGRIDS];
	int count = 0;

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			indices[count] = (unsigned char)i;
			count++;
		}
	}

	std::vector<uint64_t> mods;
	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		mods.push_back(*iter);
	}
	auto offset = m_RoastSeq.empty() ? 0 : builder.CreateVector(&m_RoastSeq[0], m_RoastSeq.size());
	auto actor = FBSave::CreateContainerBonFire(builder, basedata, builder.CreateVector(items, count), builder.CreateVector(indices, count), m_HeatValue, 0, 0, m_HeatCountdown, m_AttractCountdown, m_AttractState, builder.CreateVector(mods), builder.CreateVector(m_RoastState, getGridNum()), builder.CreateVector(m_RoastCountdown, getGridNum()), offset);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerBonFire, actor.Union());
}

bool WorldBonFire::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBonFire*>(srcdata);
	loadContainerCommon(src->basedata());

	auto items = src->items();
	auto indices = src->indices();

	for (size_t i = 0; i < items->size(); i++)
	{
		int index = indices->Get(i);
		assert(index >= 0 && index < sizeof(m_Grids) / sizeof(m_Grids[0]));

		m_Grids[index].load(items->Get(i));
	}
	m_HeatValue = src->heatvalue();
	m_RoastState[0] = src->roaststate();	// roaststate 已废弃
	if (m_RoastState[0] != WorldBonFire::ROAST_EMPTY)
		m_RoastSeq.push_back(0);
	m_RoastCountdown[0] = src->roastcountdown(); //  roastcountdown 已废弃
	m_HeatCountdown = src->heatcountdown();
	m_AttractCountdown = src->attractcountdown();
	m_AttractState = src->attractstate();
	auto mods = src->bindactors();
	if (mods) {
		for (size_t i = 0; i < mods->size(); i++)
		{
			WORLD_ID modId = mods->Get(i);
			if (modId <= 0)
			{
				break;
			}
			m_BindMods.insert(modId);
		}
	}
	if (src->roaststate_array())
	{
		for (size_t i = 0; i < src->roaststate_array()->size(); i++)
		{
			m_RoastState[i] = src->roaststate_array()->Get(i);
		}
	}
	if (src->roastcountdown_array())
	{
		for (size_t i = 0; i < src->roastcountdown_array()->size(); i++)
		{
			m_RoastCountdown[i] = src->roastcountdown_array()->Get(i);
		}
	}
	if (src->roastseq())
	{
		m_RoastSeq.clear();
		for (size_t i = 0; i < src->roastseq()->size(); i++)
		{
			m_RoastSeq.push_back(src->roastseq()->Get(i));
		}
	}

	return true;
}

void WorldBonFire::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
}

BackPackGrid* WorldBonFire::index2Grid(int index)
{
	assert(index >= BONFIRE_START_INDEX);
	index -= BONFIRE_START_INDEX;

	if (index >= getGridCount()) return NULL;

	if (index < BONFIRE_CAPACITY) return &m_Grids[index];
	return NULL;
}

int WorldBonFire::getGridCount()
{
	return BONFIRE_CAPACITY;
}

void WorldBonFire::leaveWorld()
{
	/*if (m_bSoundEatPlayed)
	{
		m_World->stopSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "misc.eat");
		m_bSoundEatPlayed = false;
	} */
	if (m_bSoundDancePlayed)
	{
		m_World->stopSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "ent.3200.dance");
		m_bSoundDancePlayed = false;
	}

	if (m_Progress)
	{
		m_Progress->DetachFromScene();
	}
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Progress);

	for (int i = 0; i < BONFIRE_ICONNUM; i++)
	{
		if (m_FireIcon[i])
		{
			m_FireIcon[i]->DetachFromScene();
		}
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_FireIcon[i]);
	}

	WorldContainer::leaveWorld();
}

void WorldBonFire::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	if (m_World && m_World->onClient())
	{
		// 进度条可视距离
		float dist = 16 * BLOCK_FSIZE;
		WCoord pos = BlockCenterCoord(m_BlockPos) + WCoord(0, int(1.1 * BLOCK_SIZE), 0);

		if (m_Progress)
		{
			m_Progress->setBoardRes("ui/mobile/texture0/homechest", "img_bar_homeland");
			m_Progress->setBarRes("ui/mobile/texture0/homechest", "img_bar_homeland_h");
			m_Progress->setBoardOffset(330.0f, 50.0f);
			m_Progress->setBarByBoardOffset(6.7f, 0.3f);
			m_Progress->setBarScale(0.2f);
			m_Progress->setBoardScale(0.2f);
			m_Progress->SetVisibleDistance(dist);
			m_Progress->AttachToScene(m_World->getScene());
			m_Progress->setVisible(true);
			m_Progress->SetPosition(pos.toWorldPos());
			m_Progress->setText("");
		}

		static int iconPos[3][2] = {
			{340, 55},
			{330, 55},
			{320, 55}
		};
		float iconStartX = 0;
		float iconStartY = 0;
		for (int i = 0; i < BONFIRE_ICONNUM; ++i)
		{
			if (m_FireIcon[i])
			{
				iconStartX = iconPos[i][0];
				iconStartY = iconPos[i][1];

				m_FireIcon[i]->setImageRes("ui/mobile/texture0/common", "icon_fire");
				m_FireIcon[i]->setScale(0.1f);
				m_FireIcon[i]->setOffset(iconStartX, iconStartY);
				m_FireIcon[i]->setVisible(false);
				m_FireIcon[i]->AttachToScene(m_World->getScene());
				m_FireIcon[i]->SetPosition(pos.toWorldPos());
				m_FireIcon[i]->SetVisibleDistance(dist);
			}
		}

		int fireNum = getMeatNum() == 0 ? 0 : getFireStatus();
		if (fireNum > 0)
		{
			for (int i = 0; i < BONFIRE_ICONNUM; ++i)
			{
				if (i < fireNum && m_FireIcon[i])
				{
					m_FireIcon[i]->setVisible(true);
				}
			}
		}
	}
}

void WorldBonFire::updateDisplay(float dtTime)
{
#ifdef IWORLD_SERVER_BUILD
	return;
#endif
	if (m_Progress)
	{
		int sum = getRoastSumTick();
		if (sum == 0)
		{
			m_Progress->setVisible(false);
			m_Progress->setPercent(0);
		}
		else
		{
			if (getFireStatus() != 0)
			{
				float value = GetCurCookedMeatProgress() * 100;
				m_Progress->setPercent(100 - (int)value);
				m_Progress->setVisible(true);
			}
		}
		const char* rawEmptyMeat = "#cFF0000%d#";	// 红色
		const char* rawMeat = "#cFFFFFF#%d";
		const char* rawCookedMeat = "#cFF0000%d#";// 红色
		const char* cookedMeat = "#cFFFFFF#%d";
		char rawMeatStr[16];
		char cookedMeatStr[16];
		if (GetStateMeatNum(WorldBonFire::ROAST_COOKED) == 0)
		{
			sprintf(cookedMeatStr, rawCookedMeat, 0);
		}
		else
		{
			sprintf(cookedMeatStr, cookedMeat, GetStateMeatNum(WorldBonFire::ROAST_COOKED));
		}
		if (GetStateMeatNum(WorldBonFire::ROAST_RAW) == 0)
		{
			sprintf(rawMeatStr, rawEmptyMeat, 0);
		}
		else
		{
			sprintf(rawMeatStr, rawMeat, GetStateMeatNum(WorldBonFire::ROAST_RAW));
		}
		const char* text = "%s/%s";
		char result[256];
		sprintf(result, text, cookedMeatStr, rawMeatStr);
		if (GetStateMeatNum(WorldBonFire::ROAST_COOKED) == 0 && GetStateMeatNum(WorldBonFire::ROAST_RAW) == 0)
			m_Progress->setText("");
		else
			m_Progress->setText(result);
		m_Progress->update(dtTime);
	}

	int fireNum = getMeatNum() == 0 ? 0 : getFireStatus();
	if (fireNum > 0)
	{
		for (int i = 0; i < BONFIRE_ICONNUM; ++i)
		{
			if (i < fireNum && m_FireIcon[i])
			{
				m_FireIcon[i]->setVisible(true);
				m_FireIcon[i]->update(dtTime);
			}
			else {
				if (m_FireIcon[i])
					m_FireIcon[i]->setVisible(false);
			}
		}
	}
	else {
		for (int i = 0; i < BONFIRE_ICONNUM; ++i)
		{
			if (m_FireIcon[i])
			{
				m_FireIcon[i]->setVisible(false);
			}
		}
	}
}

int WorldBonFire::GetStateMeatNum(int state)
{
	int num = 0;
	for (int i = 0; i < getGridNum(); i++)
	{
		if (m_RoastState[i] == state)
			num++;
	}
	return num;
}

int WorldBonFire::GetAnyMeat(int state)
{
	for (int i = 0; i < getGridNum(); i++)
	{
		if (m_RoastState[i] == state)
			return i;
	}
	return 0;
}

float WorldBonFire::GetCurCookedMeatProgress()
{
	if (m_RoastSeq.empty())
		return 0;
	int curCookedIndex = m_RoastSeq.front();
	int needTick = getRoastCountDown(WorldBonFire::ROAST_RAW) * 100 / getHeatWeakRate(getFireStatus());
	return (float)m_RoastCountdown[curCookedIndex] / needTick;
}


void WorldBonFire::updateTick()
{
	// 烤肉状态切换
	if (m_HeatValue > 0)
	{
		if (!m_RoastSeq.empty() && m_RoastSeq.front() < getGridNum())
		{
			if (m_RoastSeq.front() < getGridNum())
			{
				m_RoastCountdown[m_RoastSeq.front()]--;
				if (isCooked(m_RoastSeq.front()))
				{
					//code_by:huangfubin 计算成就
					const FurnaceDef* def = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(getGridItemId(m_RoastSeq.front()), false, 2);
					if (GetIPlayerControl() && def)
					{
						auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
						player->addAchievement(1, ACHIEVEMENT_FURNACEITEM, def->Result, 1);
						player->addOWScore(def->Score);
					}
					/*if (TaskSubSystem::GetTaskSubSystem())
					{
						TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_COOKING, m_BlockPos * BLOCK_SIZE, def->Result);
					}*/
					WCoord pos = m_BlockPos * BLOCK_SIZE;
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr()&& def) {
						MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
							SetData_Number("type", TASKSYS_COOKING).
							SetData_Userdata("WCoord", "trackPos", &pos).
							SetData_Number("target1", def->Result).
							SetData_Number("target2", 0).
							SetData_Number("goalnum", 1);
						MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
					}
					setGridItemId(getNextStateItemId(m_RoastSeq.front()), m_RoastSeq.front());
					changeRoastState(ROAST_COOKED, m_RoastSeq.front());
					m_RoastSeq.erase(m_RoastSeq.begin());
				}
			}
		}
	}
	//else if (isBurnt())		// 去除烤焦状态
	//{
	//	setGridItemId(getNextStateItemId()); //这里先后顺序不要乱哦
	//	changeRoastState(ROAST_BURNT);
	//}

	//检查是否有不在状态的野人，并解绑
	checkBindModsState();
	m_iPlayTick++;
#ifndef IWORLD_SERVER_BUILD
	if (m_iSoundEatRef > 0)
	{
		//if (!m_bSoundEatPlayed)
		//{
			/*MINIW::SoundCreateInfo3D info;
			info.mindistance = 1600.0f;
			info.maxdistance = 600000.0f;
			info.volume = 1.0f;
			info.pitch = 1.0f;
			info.pos = (m_BlockPos*BLOCK_SIZE).toVector3();
			info.velocity.Set(0,0,0);
			info.isloop = true;

			if(MINIW::SoundSystem::GetInstancePtr())
				m_EatSnd = Rainbow::SoundSystem::GetInstance().playSound3DControl("sounds/npc/eat.ogg", info); */

				//m_World->playSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "misc.eat", 1.0f, 1.0f, true);
		if (m_iPlayTick % 10 == 1 && m_World->getEffectMgr())
			m_World->getEffectMgr()->playSound(m_BlockPos * BLOCK_SIZE, "misc.eat", 1.0f, 1.0f, 3);

		//	m_bSoundEatPlayed = true;
		//}
	}
	/*else if (m_bSoundEatPlayed)
	{
		//OGRE_RELEASE(m_EatSnd);
		m_bSoundEatPlayed = false;
		m_World->stopSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "misc.eat");
	} */
	if (m_iSoundDanceRef > 0)
	{
		if (!m_bSoundDancePlayed)
		{
			/*MINIW::SoundCreateInfo3D info;
			info.mindistance = 1600.0f;
			info.maxdistance = 600000.0f;
			info.volume = 1.0f;
			info.pitch = 1.0f;
			info.pos = (m_BlockPos*BLOCK_SIZE).toVector3();
			info.velocity.Set(0,0,0);
			info.isloop = true;

			if(MINIW::SoundSystem::GetInstancePtr())
				m_DanceSnd = Rainbow::SoundSystem::GetInstance().playSound3DControl("sounds/ent/3200/dance.ogg", info); */
			m_World->playSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "ent.3200.dance", 1.0f, 1.0f, true);
			m_bSoundDancePlayed = true;
		}
	}
	else if (m_bSoundDancePlayed)
	{
		//OGRE_RELEASE(m_DanceSnd);
		m_bSoundDancePlayed = false;
		m_World->stopSoundEffect(m_BlockPos.x, m_BlockPos.y, m_BlockPos.z, "ent.3200.dance");
	}
#endif

	// 检查火焰状态
	if (m_HeatValue > 0)
	{
		// 火焰减弱
		if (m_HeatCountdown > 0)
		{
			m_HeatCountdown--;
		}
		if (m_HeatCountdown <= 0)
		{
			addHeat(-1);
			m_HeatCountdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_weak_rate;
		}

		// 吸引野人倒计时
		if (m_AttractCountdown > 0)
		{
			m_AttractCountdown--;
			if (m_AttractCountdown <= 0) //倒计时到了
			{
				if (m_AttractState == ATTRACT_DANCE_PAUSE || m_AttractState == ATTRACT_EAT_PAUSE) //暂停--重置吸引
				{
					changeAttractState(ATTRACT_NONE);
				}
				else if (m_AttractState == ATTRACT_DANCING && hasDancingActor()) //跳舞 -- 吃肉
				{
					changeAttractState(ATTRACT_EATING);
				}
				else if (m_AttractState == ATTRACT_EATING && hasEatingActor()) //吃肉 -- 睡觉
				{
					//如果还有火，没有进入吃肉状态的野人解绑；
					if (m_HeatValue > 0)
					{
						std::vector<WORLD_ID> tempvec;
						for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
						{
							if (GetWorldManagerPtr())
							{
								IClientMob* pmob = GetWorldManagerPtr()->findMobByWID(*iter);
								if (pmob && pmob->GetDanceState() != 3)
								{
									tempvec.push_back(*iter);
								}
							}
						}
						for (auto iter = tempvec.begin(); iter != tempvec.end(); iter++)
						{
							unbindActor(*iter);
						}
						changeAttractState(ATTRACT_SLEEPING); //野人睡觉的状态在这里面切换
					}
					else //如果火熄灭了，所有野人解绑
					{
						unbindAllActors();
						changeAttractState(ATTRACT_NONE);
					}
					// codeby wudeshen 改回吃熟肉
					for (int i = 0; i < getGridNum(); i++)
					{
						if (m_RoastState[i] == WorldBonFire::ROAST_COOKED)
						{
							//肉被吃掉，
							//removeItemByCount(getGridItemId(), i);
							removeItemByCount(getGridItemId(i), 1, i);
							break;
						}
					}
					//篝火烤肉的状态变成默认，
					//changeRoastState(ROAST_EMPTY);
					if (m_World && GetStateMeatNum(WorldBonFire::ROAST_EMPTY) == getGridNum()) {
						m_World->setBlockAll(m_BlockPos, BLOCK_BONFIRE, m_World->getBlockData(m_BlockPos) & 0x7);
					}
				}
			}
			else if (m_AttractState == ATTRACT_DANCING && !hasDancingActor()) //跳舞--暂停
			{
				changeAttractState(ATTRACT_DANCE_PAUSE);
			}
			else if (m_AttractState == ATTRACT_EATING && !hasEatingActor()) //吃肉--暂停
			{
				changeAttractState(ATTRACT_EAT_PAUSE);
			}
		}
	}
}

void WorldBonFire::dropItems()
{
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
	{
		//dropOneItem(m_Grids[i]);
		WCoord pos = m_BlockPos * BLOCK_SIZE + WCoord(GenRandomInt(10, 90), GenRandomInt(20, 90), GenRandomInt(10, 90));
		if (getGridItemId(i) <= 0) continue;

		IClientItem* item = m_World->getActorMgr()->SpawnIClientItem(pos, getGridItemId(i), 1);
		if (item)
		{
			IClientActor* actor = dynamic_cast<IClientActor*>(item);
			IActorLocoMotion* locmove = item->GetItemLocoMotion();
			float scale = 0.05f;
			locmove->SetMotion(Rainbow::Vector3f(GenGaussian() * scale, GenGaussian() * scale + 0.2f, GenGaussian() * scale));
			IActorAttrib* attr = actor->GetIActorAttrib();
			if (attr)
				attr->setImmuneToFire(1);
		}
		m_Grids[i].reset(m_BaseIndex + i);
		m_RoastState[i] = WorldBonFire::ROAST_EMPTY;
		m_RoastCountdown[i] = 0;
	}
	m_RoastSeq.clear();
}

void WorldBonFire::syncItemUserdata(IClientPlayer* player)
{
	assert(player);

	for (int i = 0; i < BONFIRE_CAPACITY; i++)
	{
		BackPackGrid* tgrid = nullptr;
		tgrid = &m_Grids[i];

		if (tgrid && !tgrid->isEmpty()
			&& !tgrid->userdata_str.empty())
		{
			PB_ItemGridUserData itemGridUserData;
			itemGridUserData.set_uin(player->getUin());
			itemGridUserData.set_gridindex(tgrid->getIndex());
			itemGridUserData.set_userdatastr(tgrid->userdata_str.c_str(), tgrid->userdata_str.size());

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
		}
	}
}

int WorldBonFire::getMeatNum()
{
	int num = 0;
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
	{
		if (m_RoastState[i] != WorldBonFire::ROAST_EMPTY)
			num++;
	}

	return num;
};

//只有一个格子，只添加一个肉
int WorldBonFire::addItemByCount(int itemid, int num)
{
	// 只有配了表的才能放篝火
	const FurnaceDef* furnaceDef = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(itemid, false, 2);
	if (!furnaceDef)
		return 0;
	int putIndex = 0;
	GridCopyData data(itemid, 1, -1, -1, 0, NULL, 0, NULL, NULL, "", NULL);
	int n = InsertItemToEmptyGrids_byGridCopyData(this, 0, &m_Grids[0], getGridCount(), data, &putIndex);
	//int n = InsertItemToEmptyGrids(this, 0, &m_Grids[0], getGridCount(), itemid, 1, -1, 0, NULL, NULL, "", &putIndex);
	if (n)
	{
		changeRoastState(ROAST_RAW, putIndex);//添加肉的时候要设置相应烧烤状态
		changeAttractState(ATTRACT_NONE);//重新开始吸引野人
		m_RoastSeq.push_back(putIndex);
	}
	return n;
}

void WorldBonFire::removeItemByCount(int itemid, int num, int index)
{
	int maxgrids = getGridNum();
	for (int i = 0; i < maxgrids; i++)
	{
		BackPackGrid* grid = index2Grid(i + m_BaseIndex);
		if (grid && grid->getItemID() == itemid && i == index)
		{
			if (num >= grid->getNum())
			{
				num -= grid->getNum();
				grid->addNum(-grid->getNum());
				grid->clear();
				afterChangeGrid(grid->getIndex());
				changeRoastState(ROAST_EMPTY, i);	//拿走肉的时候，将烧烤状态重置
				auto iter = m_RoastSeq.begin();
				while (iter != m_RoastSeq.end())
				{
					if (*iter == i)
					{
						iter = m_RoastSeq.erase(iter);
						continue;
					}
					iter++;
				}
				return;
			}
			else
			{
				grid->addNum(-num);
				afterChangeGrid(grid->getIndex());
				return;
			}
			changeAttractState(ATTRACT_NONE);	//把肉拿走，应该被吸引的野人也解除
		}
	}
}

int WorldBonFire::getGridItemId(int index)
{
	for (int i = 0; i < getGridNum(); i++)
		if (index == i && m_Grids[i].def)
			return m_Grids[i].def->ID;

	return 0;
}

void WorldBonFire::setGridItemId(int itemId, int index)
{
	m_Grids[index].setItem(itemId, 1);
	afterChangeGrid(m_BaseIndex);
}

void WorldBonFire::addHeat(int iValue)
{
	if (m_HeatValue <= 0 && iValue < 0) { return; }

	int f_status = getFireStatus();
	m_HeatValue += iValue;
	if (m_HeatValue > GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_max) {
		m_HeatValue = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_max;
	}
	int s_status = getFireStatus();
	if (m_HeatValue > 0 && f_status != s_status)
	{
		for (int i = 0; i < BONFIRE_CAPACITY; i++)
		{
			if (m_RoastCountdown[i] > 0)
			{
				m_RoastCountdown[i] = m_RoastCountdown[i] * getHeatWeakRate(f_status) / getHeatWeakRate(s_status);
			}
		}

		if (m_World) {
			int iBlockdata = m_World->getBlockData(m_BlockPos);
			iBlockdata = (iBlockdata & 0x8) + (s_status << 1) + (iBlockdata & 0x1);
			m_World->setBlockAll(m_BlockPos, BLOCK_BONFIRE, iBlockdata);
		}
	}
	//else if (m_RoastState != ROAST_EMPTY)
	//{
	//	m_RoastCountdown = getRoastCountDown(m_RoastState);
	//}

	if (m_HeatValue <= 0) //火熄灭了
	{
		m_HeatValue = 0;

		if (m_World) {
			int iBlockdata = m_World->getBlockData(m_BlockPos);
			iBlockdata = (iBlockdata & 0x9);
			m_World->setBlockAll(m_BlockPos, BLOCK_BONFIRE, iBlockdata);
		}
		//跳舞中的野人直接进入吃肉状态，其它状态不变，只是吃完肉以后不睡觉，解散
		if (m_AttractState == ATTRACT_DANCING)
		{
			changeAttractState(ATTRACT_EATING);
		}
	}
}

void WorldBonFire::setHeat(int iValue)
{
	bool startFire = (m_HeatValue <= 0 && iValue > 0);
	m_HeatValue = iValue;
	if (m_HeatValue > GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_max) {
		m_HeatValue = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_max;
	}
	if (startFire)
	{
		for (int i = 0; i < BONFIRE_CAPACITY; i++)
			m_RoastCountdown[i] = getRoastCountDown(m_RoastState[i]) * 100 / getHeatWeakRate(getFireStatus());
	}
}

int WorldBonFire::getFireStatus()
{
	int iFireStatus = FIRE_BIG;
	if (m_HeatValue <= 0) {
		iFireStatus = FIRE_EXTINCT;
	}
	else if (m_HeatValue >= GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_small && m_HeatValue < GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_medium) {
		iFireStatus = FIRE_SMALL;
	}
	else if (m_HeatValue >= GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_medium && m_HeatValue < GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.heatvalue_big) {
		iFireStatus = FIRE_MEDIUM;
	}

	return iFireStatus;
}

int WorldBonFire::getRoastSumTick()
{
	int cnt = 0;
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
		if (m_RoastState[i] != WorldBonFire::ROAST_EMPTY)
			cnt += getRoastCountDown(WorldBonFire::ROAST_RAW) * 100 / getHeatWeakRate(getFireStatus());
	return cnt;
}

int WorldBonFire::getRestRoastTick()
{
	int cnt = 0;
	for (int i = 0; i < BONFIRE_CAPACITY; i++)
		cnt += m_RoastCountdown[i];
	return cnt;
}

bool WorldBonFire::isCooked(int index)
{
	if (index >= BONFIRE_CAPACITY)
		return false;
	if (ROAST_RAW == m_RoastState[index])
	{
		if (m_RoastCountdown[index] <= 0)
		{
			return true;
		}
	}
	return false;
}

bool WorldBonFire::isBurnt()
{
	//if (ROAST_COOKED == m_RoastState)
	//{
	//	if (m_RoastCountdown<=0)
	//	{
	//		return true;
	//	}
	//}
	return false;
}

void WorldBonFire::changeRoastState(int nstate, int index)
{
	if (index >= getGridNum())
		return;
	bool bUpdate = m_RoastState[index] != nstate;
	m_RoastState[index] = nstate;
	if (nstate != WorldBonFire::ROAST_COOKED)
	{
		m_RoastCountdown[index] = getRoastCountDown(nstate);
		int fireStatus = getFireStatus();
		m_RoastCountdown[index] = m_RoastCountdown[index] * 100 / getHeatWeakRate(fireStatus);
	}

	if (bUpdate && m_World) {
		m_World->markBlockForUpdate(m_BlockPos, true);
	}
}

int WorldBonFire::getNextStateItemId(int index)
{
	if (index >= getGridNum())
		return 0;
	int itemid = 0;
	if (ROAST_COOKED == m_RoastState[index])
	{
		itemid = 12497; //烤糊的肉
	}
	else if (ROAST_RAW == m_RoastState[index])
	{
		itemid = 12519; //默认烤鸡肉，根据熔炉配置表读取
		if (m_Grids[index].def)
		{
			const FurnaceDef* furnaceDef = GetDefManagerProxy()->getFurnaceDefByMaterialIDWithType(m_Grids[index].def->ID, false, 2);
			if (furnaceDef && furnaceDef->Result)
			{
				itemid = furnaceDef->Result;
			}
		}
	}
	return itemid;
}

int WorldBonFire::getHeatWeakRate(int fireStatus)
{
	int tick_rate = 1;
	if (FIRE_SMALL == fireStatus)
	{
		tick_rate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_small;
	}
	else if (FIRE_MEDIUM == fireStatus)
	{
		tick_rate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_medium;
	}
	else if (FIRE_BIG == fireStatus)
	{
		tick_rate = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.tick_rate_big;
	}
	return tick_rate;
}

int WorldBonFire::getRoastCountDown(int state)
{
	int countdown = 0;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSurviveGameConfig())
	{
		if (state == ROAST_RAW)
		{
			countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.raw_to_cooked;
		}
		else if (state == ROAST_COOKED)
		{
			countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.cooked_to_burnt;
		}
	}
	return countdown;
}

int WorldBonFire::getRoastState(int index)
{
	if (index >= getGridNum())
		return 0;
	return m_RoastState[index];
}

void WorldBonFire::changeAttractState(int nstate)
{
	m_AttractState = nstate;
	m_AttractCountdown = getAttractCountDown(nstate);
	if (nstate == ATTRACT_NONE)
	{
		unbindAllActors();
	}
	else if (nstate == ATTRACT_DANCING) {
		if (m_World) {
			WCoord center = BlockCenterCoord(m_BlockPos);
			CollideAABB box;
			box.setPosDim(WCoord(center.x - 500, center.y - 300, center.z - 500), WCoord(1000, 600, 1000));

			std::vector<IClientActor*> actors;
			actors.clear();
			m_World->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);

			IClientMob* pMob = NULL;
			for (unsigned int i = 0; i < actors.size(); i++) {
				pMob = dynamic_cast<IClientMob*>(actors[i]);
				if (pMob && pMob->checkIfSavageSleeping()) {//这里要唤醒这些之前在睡觉的野人
					actors[i]->setFlagBit(ACTORFLAG_AI_SLEEP, false);
					actors[i]->setFlagBit(ACTORFLAG_AI_WARNING, true);
					pMob->SetDanceState(0);
					pMob->setTraceDist(1000);
				}
			}
		}
	}
	else if (nstate == ATTRACT_SLEEPING)
	{
		//野人睡觉
		for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
		{
			if (GetWorldManagerPtr())
			{
				IClientMob* pmob = GetWorldManagerPtr()->findMobByWID(*iter);
				if (pmob)
				{
					IClientActor* actor = dynamic_cast<IClientActor*>(pmob);
					actor->playAnim(SEQ_GET_UP);
					actor->setFlagBit(ACTORFLAG_AI_SLEEP, true);
				}
			}
		}
	}
}

int WorldBonFire::getAttractStatus()
{
	return m_AttractState;
}

int WorldBonFire::getAttractCountDown(int nstate)
{
	int countdown = 0;
	if (nstate == ATTRACT_DANCING)
	{
		countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.dance_tick;
	}
	else if (nstate == ATTRACT_DANCE_PAUSE)
	{
		countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.dance_pause_tick;
	}
	else if (nstate == ATTRACT_EATING)
	{
		countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.eat_tick;
	}
	else if (nstate == ATTRACT_EAT_PAUSE)
	{
		countdown = GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.eat_pause_tick;
	}
	return countdown;
}

void WorldBonFire::bindActor(WORLD_ID modId)
{
	auto it = m_BindMods.find(modId);
	if (it != m_BindMods.end() || !m_World) { return; }

	m_BindMods.insert(modId);
	IClientMob* pmob = m_World->getActorMgr()->iFindMobByWID(modId);
	if (pmob)
	{
		pmob->setPersistance(true);
		pmob->setTraceDist(500);
	}
}

void WorldBonFire::unbindActor(WORLD_ID modID)
{
	auto it = m_BindMods.find(modID);
	if (it == m_BindMods.end() || !m_World) { return; }

	m_BindMods.erase(modID);
	IClientMob* pmob = m_World->getActorMgr()->iFindMobByWID(modID);
	if (pmob)
	{
		pmob->SetDanceState(0);
		pmob->setPersistance(false);
		pmob->setTraceDist(3200);
	}
}

void WorldBonFire::unbindAllActors()
{
	if (!m_World) { return; }

	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		IClientMob* pmob = m_World->getActorMgr()->iFindMobByWID(*iter);
		if (pmob)
		{
			pmob->SetDanceState(0);
			pmob->setPersistance(false);
			pmob->setTraceDist(3200);
		}
	}
	m_BindMods.clear();
}

bool WorldBonFire::hasArriveActor()
{
	if (!m_World) { return false; }

	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(*iter);
		if (actor && !actor->isDead())
		{
			return true;
		}
	}

	return false;
}

bool WorldBonFire::hasDancingActor()
{
	if (!m_World) { return false; }

	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(*iter);
		IClientMob* pmob = dynamic_cast<IClientMob*>(actor);
		if (pmob && pmob->GetDanceState() == 2)
		{
			return true;
		}
	}
	return false;
}

bool WorldBonFire::hasEatingActor()
{
	if (!m_World) { return false; }

	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(*iter);
		IClientMob* pmob = dynamic_cast<IClientMob*>(actor);
		if (pmob && pmob->GetDanceState() == 3)
		{
			return true;
		}
	}
	return false;
}

bool WorldBonFire::checkIfAttractMobs()
{
	return (getMeatNum() > 0 && getHeat() > 0);
}

void WorldBonFire::checkBindModsState()
{
	if (!m_World) { return; }

	std::vector<WORLD_ID> tempvec;
	for (auto iter = m_BindMods.begin(); iter != m_BindMods.end(); iter++)
	{
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(*iter);
		//ClientMob * pmob = dynamic_cast<ClientMob*>(actor);
		if (actor && actor->isDead())
		{
			tempvec.push_back(*iter);
		}
	}
	for (auto iter = tempvec.begin(); iter != tempvec.end(); iter++)
	{
		unbindActor(*iter);
	}
}

void WorldBonFire::modArrive()
{
	if (checkIfAttractMobs())
	{
		if (getAttractStatus() == ATTRACT_NONE || getAttractStatus() == ATTRACT_DANCE_PAUSE)
		{
			changeAttractState(ATTRACT_DANCING);
		}
		else if (getAttractStatus() == ATTRACT_EAT_PAUSE)
		{
			changeAttractState(ATTRACT_EATING);
		}
	}
}

void WorldBonFire::wakeMobAndEndAttract()
{
	//叫醒所有野人
	//for (auto iter=m_BindMods.begin(); iter!=m_BindMods.end(); iter++)
	//{
	//	ClientMob * pmob = GetIPlayerControl()->getActorMgr()->findMobByWID(*iter);
	//	if (pmob)
	//	{
	//		pmob->setFlagBit(ACTORFLAG_AI_SLEEP,false);
	//		pmob->setFlagBit(ACTORFLAG_AI_WARNING, true);
	//		pmob->setPersistance(false);
	//		pmob->setTraceDist(1000);
	//		pmob->SetDanceState(0);
	//	}
	//}
	//解绑
	m_BindMods.clear();
	//吸引状态切换
	changeAttractState(ATTRACT_NONE);
}

bool WorldBonFire::checkIfAttractFull()
{
	return ((int)m_BindMods.size() >= GetWorldManagerPtr()->getSurviveGameConfig()->tamingsavagecfg.max_attract_num);
}

void WorldBonFire::setLastFireStatus(int lastFireStatus)
{
	m_LastFireStatus = lastFireStatus;
}

void WorldBonFire::setLastRoastState(int lastRoastState)
{
	m_LastRoastState = lastRoastState;
}

//-----------------------------------------------------WorldBed--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldBed
功    能：床 野人休息绑定点
********************************************************************************************* */
WorldBed::WorldBed()
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_desertSleepCount = -1;
}
WorldBed::WorldBed(const WCoord& blockpos) :WorldContainer(blockpos, 0)
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_desertSleepCount = -1;
}
bool WorldBed::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBed*>(srcdata);
	loadContainerCommon(src->basedata());

	m_BindActor = src->bindactor();
	m_ActorStatus = src->actorstatus();

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldBed::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerBed(builder, basedata, m_BindActor, (int8_t)m_ActorStatus);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerBed, actor.Union());
}

void WorldBed::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	if (GetWorldManagerPtr())
	{
		//游商先判断
		TravelingTraderInfo& traderInfo = GetWorldManagerPtr()->GetTravelingTraderInfo();
		if (traderInfo.housingLevel > 0 && traderInfo.bedPos == m_BlockPos)
		{
			m_ActorStatus = traderInfo.inHome ? ENUM_BED_STATUS_TRADER_IN_HOME : ENUM_BED_STATUS_TRADER_OUT_HOME;
		}
		else
		{
			//世界数据存储了一份床的绑定关系, 主动修正状态
			m_ActorStatus = GetWorldManagerPtr()->getWorldInfoManager()->getVillageBedStatus(m_BlockPos, m_BindActor);
			if (m_ActorStatus == ENUM_BED_STATUS_NO_BIND_ACOTR)
			{
				m_BindActor = 0;
			}
		}
	}
	updateBedEffect();
	pworld->markBlockForUpdate(m_BlockPos, true);
}

void WorldBed::leaveWorld() {
	WorldContainer::leaveWorld();
	stopBedEffect();
}

void WorldBed::setBedStatus(short status)
{
	stopBedEffect(); //先停掉之前的
	m_ActorStatus = status;
	updateBedEffect(); //更新现在的
}

void WorldBed::updateTick()
{
	//m_desertSleepCount = -1;
}

void WorldBed::updateBedEffect() {
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		if ((blockdata & 4) != 0)
		{
			return;
		}
		WCoord effectPos = getEffectPos(blockdata);

		int maxage = Rainbow::MAX_INT;
		if (m_ActorStatus == ENUM_BED_STATUS_DIING)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_die.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_betray.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_normal.ent", effectPos, maxage);
		}
		//游商 新增
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022_goout.ent", effectPos, maxage);
		}
	}
}

void WorldBed::stopBedEffect()
{
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		stopBedEffectByBlockdata(blockdata);
	}
}

void WorldBed::stopBedEffectByBlockdata(int blockdata)
{
	if ((blockdata & 4) != 0)
	{
		return;
	}
	WCoord effectPos = getEffectPos(blockdata);

	if (m_ActorStatus == ENUM_BED_STATUS_DIING)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_die.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_betray.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_normal.ent", effectPos);
	}
	//游商 新增
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022_goout.ent", effectPos);
	}
}

WCoord WorldBed::getEffectPos(int blockdata)
{
	int placedir = blockdata & 3;
	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
	if (placedir == DIR_NEG_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
	}
	else if (placedir == DIR_POS_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
	}
	else if (placedir == DIR_NEG_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
	}
	else if (placedir == DIR_POS_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
	}
	return effectPos;
}

//-----------------------------------------------------WorldCanvas--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldCanvas
功    能：帐篷
********************************************************************************************* */
WorldCanvas::WorldCanvas()
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_stage = 0;
	m_inDesertWindCount = 0;
	m_isPlayEffecting = false;
	m_effectPos = WCoord(0, 0, 0);
}
WorldCanvas::WorldCanvas(const WCoord& blockpos) :WorldContainer(blockpos, 0)
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_stage = 0;
	m_inDesertWindCount = 0;
	m_isPlayEffecting = false;
	m_effectPos = WCoord(0, 0, 0);
}

WorldCanvas::~WorldCanvas()
{
}

bool WorldCanvas::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerCanvas*>(srcdata);
	loadContainerCommon(src->basedata());

	m_BindActor = src->bindactor();
	m_ActorStatus = src->actorstatus();
	m_stage = src->stage();
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldCanvas::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerCanvas(builder, basedata, m_BindActor, (int8_t)m_ActorStatus, m_stage);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerCanvas, actor.Union());
}

void WorldCanvas::enterWorld(World* pworld) {
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	//if (g_WorldMgr)
	//{
	//	//世界数据存储了一份床的绑定关系, 主动修正状态
	//	m_ActorStatus = g_WorldMgr->getWorldInfoManager()->getVillageBedStatus(m_BlockPos, m_BindActor);
	//	if (m_ActorStatus == ENUM_BED_STATUS_NO_BIND_ACOTR)
	//	{
	//		m_BindActor = 0;
	//	}
	//}
	updateBedEffect();
}

void WorldCanvas::leaveWorld() 
{
	needPlaySleepEffect(false);
	WorldContainer::leaveWorld();
	stopBedEffect();
}

void WorldCanvas::updateTick()
{
	if (!m_World) return;
	if (m_World->isRemoteMode()) return;

	//检测游商的状态
	if (GetWorldManagerPtr())
	{
		TravelingTraderInfo& traderInfo = GetWorldManagerPtr()->GetTravelingTraderInfo();
		if (traderInfo.housingLevel > 0 && traderInfo.bedPos == m_BlockPos)
		{
			setBedStatus(traderInfo.inHome ? ENUM_BED_STATUS_TRADER_IN_HOME : ENUM_BED_STATUS_TRADER_OUT_HOME);
			return;
		}
	}

	int intervalSecond = 120;
	if (g_WorldMgr)
	{
		intervalSecond = g_WorldMgr->m_SurviveGameConfig->canvasconfig.interval;
	}

	if (m_inDesertWindCount++ == 20 * intervalSecond && m_stage < 2)
	{
		m_inDesertWindCount = 0;

		if (m_World->getWeatherMgr()->getSandDuststormPower(m_BlockPos * BLOCK_SIZE) > 0)
		{
			doRandomChangeStage();
		}
	}


	//如果睡觉者的位置不在床上了，就不让它睡了，改变它的状态。
	//怪物
	if (m_BindActor)
	{
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(m_BindActor);
		if (actor && actor->isSleeping())
		{
			//WCoord sleepPos = CoordDivBlock(BedLogicHandle::getSleepPosition(m_World, m_BlockPos));
			WCoord sleepPos = CoordDivBlock(GetISandboxActorSubsystem()->BedLogicHandleGetSleepPosition(m_World, m_BlockPos));
			if (sleepPos != CoordDivBlock(actor->getPosition()))
			{
				IClientPlayer* player = dynamic_cast<IClientPlayer*>(actor);
				if (player)
				{
					player->PlayerWakeUp(true, true, false);
				}
				else
				{
					IClientMob* mob = dynamic_cast<IClientMob*>(actor);
					if (mob)
					{
						mob->MobWakeUp();
					}
				}

			}
		}
	}
	//玩家
	GetISandboxActorSubsystem()->BedLogicHandleOccupy(m_World, m_BlockPos);
	bool isSleep = GetISandboxActorSubsystem()->BlockCanvasHaveActorSleep(m_World, m_BlockPos);
	needPlaySleepEffect(isSleep);
}

bool WorldCanvas::remoteMarkBlockForUpdate()
{
	return true;
}

void WorldCanvas::setBedStatus(short status)
{
	stopBedEffect(); //先停掉之前的
	m_ActorStatus = status;
	updateBedEffect(); //更新现在的
}

void WorldCanvas::updateBedEffect() {
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		if ((blockdata & 4) != 0)
		{
			return;
		}
		WCoord effectPos = getEffectPos(blockdata);

		int maxage = Rainbow::MAX_INT;
		if (m_ActorStatus == ENUM_BED_STATUS_DIING)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_die.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_betray.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_normal.ent", effectPos, maxage);
		}
		//游商 新增
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022_goout.ent", effectPos, maxage);
		}
	}
}

void WorldCanvas::stopBedEffect()
{
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		stopBedEffectByBlockdata(blockdata);
	}
}

void WorldCanvas::stopBedEffectByBlockdata(int blockdata)
{
	if ((blockdata & 4) != 0)
	{
		return;
	}
	WCoord effectPos = getEffectPos(blockdata);

	if (m_ActorStatus == ENUM_BED_STATUS_DIING)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_die.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_betray.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_normal.ent", effectPos);
	}
	//游商 新增
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022_goout.ent", effectPos);
	}
}

char WorldCanvas::addStage()
{
	if (m_stage < 2)
	{
		m_stage++;
		refreshBlockMesh();
	}

	if (m_stage == 2)
	{
		//起床
		IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(m_BindActor);

		IClientPlayer* player = dynamic_cast<IClientPlayer*>(actor);
		if (player)
		{
			player->PlayerWakeUp(true, true, false);
		}
		else
		{
			IClientMob* mob = dynamic_cast<IClientMob*>(actor);
			if (mob)
			{
				mob->MobWakeUp();
			}
		}
	}

	return m_stage;
}
char WorldCanvas::subStage()
{
	if (m_stage > 0)
	{
		m_stage--;
		refreshBlockMesh();
	}
	return m_stage;
}

void WorldCanvas::doRandomChangeStage()
{
	float rate = 0.2f;
	if (g_WorldMgr)
	{
		rate = g_WorldMgr->m_SurviveGameConfig->canvasconfig.rate;
	}

	if (GenRandomInt(0, 100) > (100 - rate * 100))
	{
		addStage();
	}
}

void WorldCanvas::needPlaySleepEffect(bool isNeed)
{
	//BlockCanvas* mtl = dynamic_cast<BlockCanvas*>(g_BlockMtlMgr.getMaterial(BLOCK_CANVAS));
	bool issame = false;
	GetISandboxActorSubsystem()->IsBlockType(issame, g_BlockMtlMgr.getMaterial(BLOCK_CANVAS), BlockCheckType::BLOCK_CANVAS_MATER);
	if (!issame) return;
	if (isNeed == !m_isPlayEffecting && m_World && issame)
	{
		if (m_World->getEffectMgr() == nullptr ) return;
		if (isNeed)
		{
			m_effectPos = GetISandboxActorSubsystem()->BlockCanvasGetSleepEffectPos(m_World, m_BlockPos);
			m_World->getEffectMgr()->playParticleEffectAsync("particles/zhangpeng.ent", m_effectPos, 0);
			refreshBlockMesh();
		}
		else
		{
			m_World->getEffectMgr()->stopParticleEffect("particles/zhangpeng.ent", m_effectPos);
			refreshBlockMesh();
		}

		m_isPlayEffecting = isNeed;
	}
}

void WorldCanvas::whenPlayerWakeup(IClientPlayer* player)
{
	World* world = player->GetPlayerWorld();
	if (!world)
	{
		return;
	}
	WCoord blockPos = CoordDivBlock(player->iGetPosition());

	if (world->getWeatherMgr()->getSandDuststormPower(WCoord(blockPos.x * BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE)) <= 0)
	{
		return;
	}

	//WorldCanvas* container = dynamic_cast<WorldCanvas*>(BedLogicHandle::getCoreContainer(world, blockPos));
	WorldCanvas* container = dynamic_cast<WorldCanvas*>(GetISandboxActorSubsystem()->BedLogicHandleGetCoreContainer(world, blockPos));
	if (container)
	{
		container->doRandomChangeStage();
	}
}

bool WorldCanvas::repair(IClientPlayer* player, World* world, int x, int y, int z)
{
	//WorldCanvas* container = dynamic_cast<WorldCanvas*>(BedLogicHandle::getCoreContainer(world, WCoord(x, y, z)));
	WorldCanvas* container = dynamic_cast<WorldCanvas*>(GetISandboxActorSubsystem()->BedLogicHandleGetCoreContainer(world, WCoord(x, y, z)));
	if (container)
	{
		if (container->getStage() > 0)
		{
			container->subStage();
			return true;
		}
		else
		{
			return false;
		}
	}
	else
	{
		return false;
	}
}

void WorldCanvas::refreshBlockMesh()
{
	if (m_World)
	{
		m_World->markBlockForUpdate(m_BlockPos, true);
	}
}

WCoord WorldCanvas::getEffectPos(int blockdata)
{
	int placedir = blockdata & 3;
	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
	if (placedir == DIR_NEG_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
	}
	else if (placedir == DIR_POS_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
	}
	else if (placedir == DIR_NEG_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
	}
	else if (placedir == DIR_POS_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
	}
	return effectPos;
}

/**********************************************************************************************
类    名：WorldVillageTotem
功    能：村庄图腾 激活村庄
********************************************************************************************* */
WorldVillageTotem::WorldVillageTotem()
{
}
WorldVillageTotem::WorldVillageTotem(const WCoord& blockpos) :WorldContainer(blockpos, 0)
{
}
bool WorldVillageTotem::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerVillageTotem*>(srcdata);
	loadContainerCommon(src->basedata());

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldVillageTotem::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerVillageTotem(builder, basedata);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerVillageTotem, actor.Union());
}

void WorldVillageTotem::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getWorldInfoManager()->getVillagePoint(getOwnerUin()) != m_BlockPos)
	{
		return;
	}
	int blockdata = pworld->getBlockData(m_BlockPos);
	if ((8 & blockdata) || (4 & blockdata))
	{
		return;
	}
	if (GetIPlayerControl() && getOwnerUin() == GetIPlayerControl()->GetIUin())
	{
		int maxage = Rainbow::MAX_INT;
		EffectParticle* peffect = pworld->getEffectMgr()->playParticleEffectAsync("particles/scene_halo.ent", getEffectPos(), maxage, 0, 0, false);
		if (peffect)
		{
			peffect->setScale(2.0);
		}
	}
}
void WorldVillageTotem::leaveWorld()
{
	m_World->getEffectMgr()->stopParticleEffect("particles/scene_halo.ent", getEffectPos());
	WorldContainer::leaveWorld();
}

WCoord WorldVillageTotem::getEffectPos()
{
	return BlockBottomCenter(m_BlockPos) + WCoord(0, 20, 0);
}

/**********************************************************************************************
类    名：WorldVillagerFlag
功    能：工作旗帜 工作绑定点
********************************************************************************************* */
WorldVillagerFlag::WorldVillagerFlag()
{
}
WorldVillagerFlag::WorldVillagerFlag(const WCoord& blockpos) :WorldContainer(blockpos, 0)
{
}
bool WorldVillagerFlag::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerVillagerFlag*>(srcdata);
	loadContainerCommon(src->basedata());

	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldVillagerFlag::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerVillagerFlag(builder, basedata);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerVillagerFlag, actor.Union());
}

void WorldVillagerFlag::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	int blockdata = pworld->getBlockData(m_BlockPos);
	if ((8 & blockdata) || (4 & blockdata))
	{
		return;
	}
	if (GetIPlayerControl() && getOwnerUin() == GetIPlayerControl()->GetIUin())
	{
		int maxage = Rainbow::MAX_INT;
		pworld->getEffectMgr()->playParticleEffectAsync("particles/ball_team_0.ent", getEffectPos(), maxage, 0, 0, false);
	}
}
void WorldVillagerFlag::leaveWorld()
{
	m_World->getEffectMgr()->stopParticleEffect("particles/ball_team_0.ent", getEffectPos());
	WorldContainer::leaveWorld();
}

WCoord WorldVillagerFlag::getEffectPos()
{
	return BlockBottomCenter(m_BlockPos) + WCoord(0, 20, 0);
}

//-----------------------------------------------------WorldShellbed--------------------------------------------------------------------------
/**********************************************************************************************
类    名：WorldShellbed
功    能：贝壳床
**********************************************************************************************/
WorldShellbed::WorldShellbed()
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_ShellbeddesertSleepCount = -1;
}
WorldShellbed::WorldShellbed(const WCoord& blockpos) :WorldContainer(blockpos, 0)
{
	m_BindActor = 0;
	m_ActorStatus = ENUM_BED_STATUS_NO_BIND_ACOTR;
	m_ShellbeddesertSleepCount = -1;
}
bool WorldShellbed::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBed*>(srcdata);
	loadContainerCommon(src->basedata());

	m_BindActor = src->bindactor();
	m_ActorStatus = src->actorstatus();
	return true;
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldShellbed::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerBed(builder, basedata, m_BindActor, (int8_t)m_ActorStatus);

	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerShellBed, actor.Union());
}

void WorldShellbed::enterWorld(World* pworld) {
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	if (g_WorldMgr)
	{
		//游商先判断
		TravelingTraderInfo& traderInfo = GetWorldManagerPtr()->GetTravelingTraderInfo();
		if (traderInfo.housingLevel > 0 && traderInfo.bedPos == m_BlockPos)
		{
			m_ActorStatus = traderInfo.inHome ? ENUM_BED_STATUS_TRADER_IN_HOME : ENUM_BED_STATUS_TRADER_OUT_HOME;
		}
		else
		{
			//世界数据存储了一份床的绑定关系, 主动修正状态
			m_ActorStatus = g_WorldMgr->getWorldInfoManager()->getVillageBedStatus(m_BlockPos, m_BindActor);
			if (m_ActorStatus == ENUM_BED_STATUS_NO_BIND_ACOTR)
			{
				m_BindActor = 0;
			}
		}
	}
	updateBedEffect();
}

void WorldShellbed::leaveWorld() {
	WorldContainer::leaveWorld();
	stopBedEffect();
}

void WorldShellbed::setBedStatus(short status)
{
	stopBedEffect(); //先停掉之前的
	m_ActorStatus = status;
	updateBedEffect(); //更新现在的
}

void WorldShellbed::updateTick()
{
	//检测游商的状态
	if (GetWorldManagerPtr())
	{
		TravelingTraderInfo& traderInfo = GetWorldManagerPtr()->GetTravelingTraderInfo();
		if (traderInfo.housingLevel > 0 && traderInfo.bedPos == m_BlockPos)
		{
			setBedStatus(traderInfo.inHome ? ENUM_BED_STATUS_TRADER_IN_HOME : ENUM_BED_STATUS_TRADER_OUT_HOME);
			return;
		}
	}

	if (m_BindActor > 0 && m_World && m_World->getBiomeType(m_BlockPos.x, m_BlockPos.z) == BIOME_DESERT)
	{
		const int maxSleepCount = 30;
		if (m_ShellbeddesertSleepCount++ == maxSleepCount)
		{
			//起床
			IClientActor* actor = m_World->getActorMgr()->iFindActorByWID(m_BindActor);

			IClientPlayer* player = dynamic_cast<IClientPlayer*>(actor);
			if (player)
			{
				player->PlayerWakeUp(true, true, false);
				auto livingattrib = actor->getActorComponent(ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB);
				if (livingattrib)
				{
					livingattrib->Event2().Emit<int, int, int, int, long long>("LivingAttrib_addBuff",
						VIGILANCE_BUFF,
						1,
						-1,
						0,
						0);
				}
				//player->getLivingAttrib()->addBuff(VIGILANCE_BUFF, 1);
			}
			else
			{
				IClientMob* mob = dynamic_cast<IClientMob*>(actor);
				if (mob && actor->isSleeping())
				{
					mob->MobWakeUp();
					auto livingattrib = actor->getActorComponent(ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB);
					if (livingattrib)
					{
						livingattrib->Event2().Emit<int, int, int, int, long long>("LivingAttrib_addBuff",
							VIGILANCE_BUFF,
							1,
							-1,
							0,
							0);
					}
					//mob->getLivingAttrib()->addBuff(VIGILANCE_BUFF, 1);
				}
			}
			m_ShellbeddesertSleepCount = -1;
		}
	}
	else
	{
		m_ShellbeddesertSleepCount = -1;
	}
}

void WorldShellbed::updateBedEffect() {
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		if ((blockdata & 4) != 0)
		{
			return;
		}
		WCoord effectPos = getEffectPos(blockdata);

		int maxage = Rainbow::MAX_INT;
		if (m_ActorStatus == ENUM_BED_STATUS_DIING)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_agonal.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_die.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_betray.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3200_normal.ent", effectPos, maxage);
		}
		//游商 新增
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022.ent", effectPos, maxage);
		}
		else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
		{
			m_World->getEffectMgr()->playParticleEffectAsync("particles/mob_3022_goout.ent", effectPos, maxage);
		}
	}
}

void WorldShellbed::stopBedEffect()
{
	if (m_World)
	{
		int blockdata = m_World->getBlockData(m_BlockPos);
		stopBedEffectByBlockdata(blockdata);
	}
}

void WorldShellbed::stopBedEffectByBlockdata(int blockdata)
{
	if ((blockdata & 4) != 0)
	{
		return;
	}
	WCoord effectPos = getEffectPos(blockdata);

	if (m_ActorStatus == ENUM_BED_STATUS_DIING)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_agonal.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_DIED)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_die.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_ESCAPE)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_betray.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_NORMAL)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3200_normal.ent", effectPos);
	}
	//游商 新增
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_IN_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022.ent", effectPos);
	}
	else if (m_ActorStatus == ENUM_BED_STATUS_TRADER_OUT_HOME)
	{
		m_World->getEffectMgr()->stopParticleEffect("particles/mob_3022_goout.ent", effectPos);
	}
}

WCoord WorldShellbed::getEffectPos(int blockdata)
{
	int placedir = blockdata & 3;
	WCoord effectPos = NeighborCoord(m_BlockPos, ReverseDirection(placedir));
	if (placedir == DIR_NEG_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(30, 100, 0);
	}
	else if (placedir == DIR_POS_X)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(-30, 100, 0);
	}
	else if (placedir == DIR_NEG_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, 30);
	}
	else if (placedir == DIR_POS_Z)
	{
		effectPos = BlockCenterCoord(effectPos) + WCoord(0, 100, -30);
	}
	return effectPos;
}

/****************************************************************WorldBells****************************************************************************/

WorldBells::WorldBells()
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
}

WorldBells::WorldBells(WCoord BlockPos)
	:WorldContainer(BlockPos, 0)
{
	m_pEntity = nullptr;
	m_curAnimId = -1;
}

void WorldBells::enterWorld(World* pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

#ifndef IWORLD_SERVER_BUILD	
	int blockdata = pworld->getBlockData(m_BlockPos);
	int dir = blockdata & 3;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(ITEM_BELLS);
	if (def == NULL)
	{
		return;
	}
	char path[100] = { 0 };
	sprintf(path, "entity/%s/body.omod", def->Model.c_str());

	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
		Rainbow::Entity::Destory(m_pEntity);
	}

	WCoord pos = m_BlockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);
	Rainbow::Vector3f angle;
	angle = Rainbow::Vector3f::zero;

	if (dir == DIR_NEG_X)
	{
		angle.y = 90;
	}
	else if (dir == DIR_POS_X)
	{
		angle.y = 270;
	}
	else if (dir == DIR_NEG_Z)
	{

	}
	else if (dir == DIR_POS_Z)
	{
		angle.y = 180;
	}

	m_pEntity = Rainbow::Entity::Create();

#if ENTITY_MODIFY_MODEL_ASYNC
	m_pEntity->LoadAsync(path,false);
#else
	m_pEntity = Rainbow::Entity::Create();
	Rainbow::Model* model = NULL;
	model = g_BlockMtlMgr.getModel(path);
	if (!model) return;
	m_pEntity->Load(model);
#endif

	m_pEntity->ShowSkins(true);
	m_pEntity->SetRotation(angle.y, angle.x, angle.z);
	m_pEntity->SetPosition(pos.toWorldPos());

	if (m_World)
		m_pEntity->AttachToScene(pworld->getScene());
#endif
}
void WorldBells::leaveWorld()
{
	if (m_pEntity)
	{
		m_pEntity->DetachFromScene();
	}
	WorldContainer::leaveWorld();
}

WorldBells::~WorldBells()
{
	Rainbow::Entity::Destory(m_pEntity);
}

void WorldBells::updateTick()
{
	//m_World;
	if (m_World->getWeatherMgr())
	{
		int power = m_World->getWeatherMgr()->getTempestStage(m_BlockPos * BLOCK_SIZE);
		if (power == PART_WEATHER_STAGE_NORMAL || power == PART_WEATHER_STAGE_STRENGTH_END)
		{
			if (!hasAnimPlaying(100923))
			{
				playAnim(100923);

				playSound(m_World, "item.1243.aeolianbells2");
			}
		}
		else if (power == PART_WEATHER_STAGE_STRENGTH)
		{
			if (!hasAnimPlaying(100924))
			{
				playAnim(100924);
				playSound(m_World, "item.1243.aeolianbells3");
			}
		}
		else
		{
			if (!hasAnimPlaying(100100))
			{
				playAnim(100100);
			}
		}
	}
}

void WorldBells::updateDisplay(float dtime)
{
	// 补光照
	if (m_pEntity)
	{
		Rainbow::Vector4f lightparam(0, 0, 0, 0);
		auto wp = m_pEntity->GetWorldPosition();
		if (m_World) m_World->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(wp));
		lightparam.x += 0.2f;
		lightparam.y += 0.2f;
		m_pEntity->SetInstanceData(lightparam);
		unsigned int dtick = Rainbow::TimeToTick(dtime);
		m_pEntity->Tick(dtick);
	}
}

void WorldBells::playAnim(int animId, int times)
{
	if (m_pEntity)
	{
		if (m_curAnimId != animId)
		{
			m_curAnimId = animId;
			m_pEntity->StopAnim();
			m_pEntity->PlayAnim(animId, times);
		}
	}
}

void WorldBells::stopAnim()
{
	if (m_pEntity)
	{
		m_pEntity->StopAnim();
	}
}

bool WorldBells::hasAnimPlaying(int anim)
{
	if (m_pEntity)
	{
		return m_pEntity->HasAnimPlaying(anim);
	}
	return false;
}

void WorldBells::playSound(World* pworld, const char* name)
{
	pworld->getEffectMgr()->playPosSound(m_BlockPos, name, 1.0, 1.0, false, false);
}

bool WorldBells::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerShells*>(srcdata);
	return loadContainerCommon(src->basedata());
}

flatbuffers::Offset<FBSave::ChunkContainer> WorldBells::save(SAVE_BUFFER_BUILDER& builder)
{
	auto basedata = saveContainerCommon(builder);

	auto actor = FBSave::CreateContainerShells(builder, basedata);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerShells, actor.Union());
}

ContainerStatistics::ContainerStatistics() {
    m_stats.reset();
	m_lastPrintTime = Rainbow::GetTimeSec();
}

void ContainerStatistics::updateStats(WorldContainerMgr* mgr) {
    m_stats.reset();
    
    // 遍历所有容器统计信息
    mgr->forEachWorldContainer([this, mgr](WorldContainer& container) {
        m_stats.totalContainers++;
        
        // 统计容器类型
        std::string containerName = container.getContainerName();
        m_stats.containerTypeCount[containerName]++;
        
        // 统计活跃容器
        if (mgr->getUpdateTickContainers().find(&container) != mgr->getUpdateTickContainers().end() ||
            mgr->getUpdateDisplayContainers().find(&container) != mgr->getUpdateDisplayContainers().end()) {
            m_stats.activeContainers++;
        }
        
        // 统计打开的容器
        if (!container.m_OpenUINs.empty()) {
            m_stats.uiOpenContainers++;
        }
        
        return true;
    });
}

void ContainerStatistics::printStats() {
    CONTAINER_LOG("[ContainerStats] =========== Container Statistics ===========");
    CONTAINER_LOG("[ContainerStats] Total Containers: %d", m_stats.totalContainers);
    CONTAINER_LOG("[ContainerStats] Active Containers: %d", m_stats.activeContainers);
    CONTAINER_LOG("[ContainerStats] UI Open Containers: %d", m_stats.uiOpenContainers);
    
    CONTAINER_LOG("[ContainerStats] Container Types:");
    for (const auto& pair : m_stats.containerTypeCount) {
        CONTAINER_LOG("[ContainerStats]   - %s: %d", pair.first.c_str(), pair.second);
    }
    CONTAINER_LOG("[ContainerStats] =========================================");
}

void WorldContainerMgr::updateStatistics() {
    m_statistics.updateStats(this);
    
    float currentTime = Rainbow::GetTimeSec();
    if (currentTime - m_statistics.m_lastPrintTime >= 60.0f) {
        m_statistics.printStats();
        m_statistics.m_lastPrintTime = currentTime;
    }
}

bool AirDropStorageBox::doOpenContainer()
{
	BaseContainer::SetLuaOpenContainer(getContainerName(), this);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("baseindex", getBaseIndex()).
		SetData_Number("blockid", m_chestId).
		SetData_Number("posx", m_BlockPos.x).
		SetData_Number("posy", m_BlockPos.y).
		SetData_Number("posz", m_BlockPos.z).
		SetData_Bool("isRideContainer", m_isRideContainer);


	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
	{
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_CONTAINER", sandboxContext);
	}
	return true;
}

int AirDropStorageBox::getObjType() const  {
	return OBJ_TYPE_CUBECHEST;
}
