#include "UGCPlayerControl.h"
#include "PCControlLua.h"
#include "TouchControlLua.h"
#include "UGCPCControlLua.h"
#include "UGCTouchControl.h"
#include "UGCModeManager.h"
#include "UGCPlayerInputHelper.h"

#include "WorldRender.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"

#include "defmanager.h"
#include "ClientActorManager.h"
#include "BlockMaterialMgr.h"
#include "ClientGameManager.h"
#include "BlockScene.h"
#include "GetClientInfo.h"
#include "OgreUtils.h"
#include "GameMode.h"
#include "PlayerAttrib.h"
#include "PlayerStateController.h"
#include "MpGameSurvive.h"
#include "InputInfo.h"
#include "PlayerInputHelper.h"
#include "VehicleControlInputs.h"
#include "ActorBoat.h"
#include "ActorTrainCar.h"
#include "ActorRocket.h"
#include "ActorPumpkinHorse.h"
#include "BlockRailNew.h"
#include "BlockRopeHead.h"
#include "CurveFace.h"
#include "RoleSkinCsv.h"
#include "ItemDefCsv.h"
#include "ItemSkillDefCsv.h"

#include "ItemSkillComponent.h"
#include "Sound/MusicManager.h"
#include "OpenContainerComponent.h"
#include "Core/GameEngine.h"
#include "GameUI.h"
#include "Graphics/ScreenManager.h"
#include "ObserverEvent.h"
#include "BlockDefCsv.h"
#include "ClientAccount.h"
#include "EditorSelect.h"
#include "UGCModeGame.h"
#include "UGCCmdManager.h"
#include "UGCBlockCmd.h"
#include "UGCActorCmd.h"
#include "UGCAABBRender.h"
#include "ActorInPortal.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "GameNetManager.h"
#include "TriggerObjLibManager.h"
#include "CarryComponent.h"
#include "SandboxSceneObject.h"
#include "UGCEcosysUnitMgr.h"
#include "ModelItemMesh.h"
#include "PedalBlockComponent.h"
#include "WorldManager.h"
#include "EffectManager.h"
#include "GameEvent.h"
#include "MoveControl.h"
#include "PlayerLocoMotion.h"
#include "PrefabEditModeMgr.h"
#include "IMiniDeveloperProxy.h"

const int gSpecialItemRange = 128;
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

extern PedalBlockType GetPedalBlockType(World* world, const WCoord& blockpos);
extern PedalBlockType GetPedalBlockTypeByBlockId(int blockid);

IMPLEMENT_SCENEOBJECTCLASS(UGCPlayerControl)
//unsigned int PlayerControl::s_currentLogicFrame = 0;

UGCPlayerControl::UGCPlayerControl() : PlayerControl()
{
	m_lastToolType = UGCTOOLTYPE_NULL;
	m_LimitBeginPos = WCoord(0, 0, 0);
	m_LimitEndPos = WCoord(0, 0, 0);
	m_buildViewMode = CAMERA_FPS;
	m_bIsContinuePlace = false;
	m_pSpecialEffect = NULL;
}

bool UGCPlayerControl::init(int uin, const char* nickname, int playerindex, const char* customjson)
{
	if (!ClientPlayer::init(uin, nickname, playerindex, customjson)) return false;
	//switchCurrentItem();
	m_AccoutSkinID = GetClientAccountMgr().getAccountInfo()->RoleInfo.SkinID;
	if (m_Body) m_Body->show(false);
	std::string sType, sID;
	parsePlayerBaseModelID(m_strCustomModel, sType, sID);
	if (m_pCamera) m_pCamera->setCameraMode(m_CameraModel);
	//getAttrib()->equip(EQUIP_PIFENG, 2205, 100);
	registerFirstPersonIK();
	GetGameEventQue().postShortcutSelected(getPlayerAttrib()->getCurShotcut());

	m_InputInfo = ENG_NEW(InputInfo)();

	m_InputHelper = ENG_NEW(UGCPlayerInputHelper)();
	ScriptVM::game()->setUserTypePointer("InputHelper", "PlayerInputHelper", m_InputHelper);

	/*TouchControlLua* pTouchControl = NULL;
	SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("TouchControlLuaImpl", "CreateTouchControl", ">u[TouchControlLua]", &pTouchControl);
	m_TouchCtrl = pTouchControl;*/

	if (GetClientInfoProxy()->isMobile())
	{
		m_TouchCtrl = ENG_NEW(UGCTouchControl);
	}
	else if(GetClientInfoProxy()->isPC())
	{
		m_PCCtrl = ENG_NEW(UGCPCControl);
	}
	//TODO
	//if (m_PCCtrl) {
	//	m_PCCtrl->destroy();
	//}

	/*if (UGCModeManager::getSingleton()->IsEditorMode())
	{
		UGCPCControlLua* pUGCPCControl = NULL;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("UGCPCControlLuaImpl", "CreatePCControl", ">u[UGCPCControlLua]", &pUGCPCControl);
		m_PCCtrl = pUGCPCControl;
	}
	else
	{
		PCControlLua* pPCControl = NULL;
		SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("PCControlLuaImpl", "CreatePCControl", ">u[PCControlLua]", &pPCControl);
		m_PCCtrl = pPCControl;
	}*/

	//UGCPCControlLua* pUGCPCControl = NULL;
	//SandboxCoreDriver::GetInstance().GetLua().CallFunctionM("UGCPCControlLuaImpl", "CreatePCControl", ">u[UGCPCControlLua]", &pUGCPCControl);
	//m_PCCtrl = pUGCPCControl;
	

	m_PlayerAnimation = ENG_NEW(PlayerAnimation)(this);
	//GetActorBody()->setNeedUpdateAnim(true);

	m_VehicleControlInputs = ENG_NEW(VehicleControlInputs)();

	m_CurrentCameraConfig.init();

	{
		if (!m_ActorAttribTrigger)
			m_ActorAttribTrigger = ENG_NEW(ActorAttribut)();

		PlayerAttrib* pattr = getPlayerAttrib();
		LivingAttrib* lattr = getLivingAttrib();
		m_ActorAttribTrigger->MaxHP = getAttrib()->getMaxHP();
		m_ActorAttribTrigger->NowHP = getAttrib()->getHP();
		m_ActorAttribTrigger->HPRecover = getAttrib()->getHPRecover();
		m_ActorAttribTrigger->MaxHunger = 100.0f;
		m_ActorAttribTrigger->NowHunger = pattr->getFoodLevel();
		m_ActorAttribTrigger->MaxOxygen = lattr->getMaxOxygen();
		m_ActorAttribTrigger->NowOxygen = lattr->getOxygen();
		m_ActorAttribTrigger->MoveSpeed = getAttrib()->getSpeedAtt(Actor_Walk_Speed);
		m_ActorAttribTrigger->RunSpeed = getAttrib()->getSpeedAtt(Actor_Run_Speed);
		m_ActorAttribTrigger->SwimSpeed = getAttrib()->getSpeedAtt(Actor_Swim_Speed);
		m_ActorAttribTrigger->JumpSpeed = getAttrib()->getSpeedAtt(Actor_Jump_Speed);
		m_ActorAttribTrigger->SneakSpeed = getAttrib()->getSpeedAtt(Actor_Sneak_Speed);
		m_ActorAttribTrigger->Dodge = (float)getAiInvulnerableProb();
		m_ActorAttribTrigger->NearAttack = lattr->getAttackBaseLua(ATTACK_PUNCH);
		m_ActorAttribTrigger->RemoteAttack = lattr->getAttackBaseLua(ATTACK_RANGE);
		m_ActorAttribTrigger->NearArmor = lattr->getArmorBaseLua(ATTACK_PUNCH);
		m_ActorAttribTrigger->RemoteArmor = lattr->getArmorBaseLua(ATTACK_RANGE);
		m_ActorAttribTrigger->Dimension = getScale();
		m_ActorAttribTrigger->Level = (int)ceil((float)pattr->getExp() / 100);
	}

	//if (UGCModeManager::getSingleton()->IsEditing())
	//{
	//	setFlying(true);
	//	setViewMode(CAMERA_FPS);
	//}
	//else
	//{
		setFlying(false);
	//}

	return true;
}

UGCPlayerControl::~UGCPlayerControl()
{
	RemoveSpecialItemEffect();
	MINIW::ScriptVM::game()->callFunction("CloseSpecailItemView", "");
}

void UGCPlayerControl::enterWorld(World *pworld)
{
	PlayerControl::enterWorld(pworld);
	UGCAABBRender::GetInstancePtr()->OnEnterWorld();
	EditorSelect::GetInstancePtr()->OnWorldChange(pworld);
	UGCCmdManager::GetInstancePtr()->OnWorldChange(pworld);
}

void UGCPlayerControl::setFlying(bool b)
{
	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		PlayerControl::setFlying(b);
		return;
	}
	
	if (UGCModeManager::getSingleton()->IsEditing()) {
		b = true;
	}

	bool isflying = getFlying();
	ClientPlayer::setFlying(b);

	if (isflying != b)
	{
		//enter fly mode
		if (b)
		{
			m_StateController->performMoveTransition("ToFly");
			if (getLocoMotion()->m_Motion.y <= 10.0f) getLocoMotion()->m_Motion.y = 10.0f;
		}

		//exit fly mode
		else
		{
			m_StateController->performMoveTransition("ToIdle");
		}

		//GetGameEventQue().postSimpleEvent(GIE_FLYMODE_CHANGE);
	}
}

void UGCPlayerControl::TickNormal()
{
	m_IsEyeInWaterLastFrame = m_IsEyeInWater;

	if (getLivingAttrib()) {
		GetMusicManager().SetReversePlay(getLivingAttrib()->getEquipItemWithType(EQUIP_HEAD) == 12246);
	}

	int eyeblock = 0;
	if (m_pWorld == NULL)
		return;

	WorldManager* pWorldMgr = GetWorldManagerPtr();
	if (!pWorldMgr)
		return;

	bool isflying = getFlying();
	if (!isflying) {
		if (isNewMoveSyncSwitchOn())
			changeMoveFlag(IFC_Fly, true);
		else
			setFlying(true);
	}

	if (m_pWorld->getCurMapID() == pWorldMgr->m_RenderEyeMap)
	{
		WCoord eyepos = CoordDivBlock(pWorldMgr->m_RenderEyePos);
		eyeblock = m_pWorld->getBlockID(eyepos);
		if (g_pPlayerCtrl)
		{
			auto portalComponent = g_pPlayerCtrl->getActorInPortal();
			if (portalComponent && portalComponent->isInPortal()) eyeblock = BLOCK_PORTAL;
		}
	}

	m_IsEyeInWater = IsWaterBlockID(eyeblock) || isWaterPlantID(eyeblock);

	if (m_IsEyeInWater && !m_IsEyeInWaterLastFrame)
	{
		OnPlayerEyeEnterWater();
	}

	if (!m_IsEyeInWater && m_IsEyeInWaterLastFrame)
	{
		OnPlayerEyeOutOfWater();
	}

	pWorldMgr->m_RenderEyeMap = m_pWorld->getCurMapID();
	pWorldMgr->m_RenderEyePos = getEyePosition();
	PitchYaw2Direction(pWorldMgr->m_RenderEyeDir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);

	if (m_CameraModel) {
		//隐藏手臂等
		m_CameraModel->show(false);
	}

	if (GetClientInfo()->isMobile())
	{
		bool showusebtn = false;
		bool showGunUseBtns = false;
		bool showCrosshair = false;
		ActorHorse* horse;
		int curtool = getCurToolID();
		const ItemDef* def = ItemDefCsv::getInstance()->get(curtool);

		if (def)
		{
			if (def->UseTarget == ITEM_USE_BOW || def->UseTarget == ITEM_USE_CLICKBUTTON || def->UseTarget == ITEM_USE_GUN || def->UseTarget == ITEM_USE_ADVANCEDDIG || def->UseTarget == ITEM_USE_CHARGETHROW || def->UseTarget == ITEM_USE_HOOK)
			{
				showCrosshair = true;
			}

			if (curtool > 0 && def->UseTarget >= ITEM_USE_CLICKBUTTON) showusebtn = true;
			else if ((horse = getFacedHorse()) && horse->getRiddenByActor() == NULL) showusebtn = true;

			if (def->UseTarget == ITEM_USE_GUN) showGunUseBtns = true;

			for (int i = 0; i < (int)def->SkillID.size(); i++)
			{
				if (def->SkillID[i])
				{
					const ItemSkillDef* skilldef = ItemSkillDefCsv::getInstance()->get(def->SkillID[i]);
					for (int j = 0; j < (int)skilldef->SkillFuncions.size(); j++)
					{
						ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*) & skilldef->SkillFuncions[j];
						if (functiondef->oper_id == 7)//有投射效果
						{
							showCrosshair = true;
						}
					}
					if (skilldef->Key == 2)
					{
						showusebtn = true;
						showCrosshair = true;
					}
				}
			}
		}
		else if (curtool == 0)	//空手
		{
			if ((horse = getFacedHorse()) && horse->getRiddenByActor() == NULL) showusebtn = true;
		}

		//坐骑右键使用技能
		auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
		if (PlayerRidComp)
		{
			ActorHorse* riding = dynamic_cast<ActorHorse*>(PlayerRidComp->getRidingActor());
			if (riding && riding->showUseBtn())
			{
				showusebtn = true;
			}
		}

		bool showBallUseBtn = false;
		bool showBasketBallUseBtn = false;
		if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
		{
			showusebtn = true;
			showCrosshair = false;
			showGunUseBtns = false;
			showBasketBallUseBtn = false;
			showBallUseBtn = true;
		}

		if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
		{
			showusebtn = true;
			showCrosshair = true;
			showGunUseBtns = false;
			showBallUseBtn = false;
			showBasketBallUseBtn = true;
		}

		if (isShapeShift())
		{
			showusebtn = false;
			showCrosshair = false;
			showGunUseBtns = false;
			showBallUseBtn = false;
			showBasketBallUseBtn = false;
		}

		//铲子 耙 显示使用按钮
		if (IsHoelID(curtool) || IsShovelID(curtool))
		{
			showusebtn = true;
		}

		//右键使用
		if(GetDefManagerProxy()->IsShowUseBtnForItemRightUse(curtool))
		{
			showusebtn = true;
		}

		if (m_TouchCtrl) {
			m_TouchCtrl->showUseBtn(showusebtn);
			m_TouchCtrl->showCrosshair(showCrosshair);
			m_TouchCtrl->showGunBtns(showGunUseBtns);
			m_TouchCtrl->showBallUseBtn(showBallUseBtn);
			m_TouchCtrl->showBasketBallUseBtn(showBasketBallUseBtn);
			m_TouchCtrl->tick();
		}
	}
	else
	{
		if (m_PCCtrl) m_PCCtrl->tick();
	}

	if (pWorldMgr->isGameMakerRunMode())
	{
		if ((!pWorldMgr->m_RuleMgr->hasPrePoint(getTeam()) && pWorldMgr->m_RuleMgr->getGameStage() != CGAME_STAGE_RUN)
			|| pWorldMgr->m_RuleMgr->getGameStage() == CGAME_STAGE_RESETCOUNTDOWN)
		{
			setStill();
			getLocoMotion()->m_Motion.Set(0.0f, 0.0f, 0.0f);
		}
	}

	// 坐在椅子上不可移动
	if (getSitting())
	{
		setStill();
		getLocoMotion()->m_Motion.Set(0.0f, 0.0f, 0.0f);
	}

	//被限制行动时，不允许移动
	if (getFreezing() == FREEZING_STATE_NOMOVE)
	{
		setStill();
	}

	SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WeaponSkin_System_CheckIdleAnimPlay", SandboxContext(nullptr).SetData_Userdata("PlayerControl", "player", this));

	SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("Music_CheckisCanMove", SandboxContext(nullptr));
	//演奏时根据配置，不允许移动
	if (!result.IsSuccessed()) {
		setStill();
	}

	if ((m_MoveForward != 0 || m_MoveRight != 0 || m_MoveUp != 0) && !checkActionAttrState(ENABLE_MOVE))
	{
		setStill();
		notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14000);
	}

	// static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveForward = m_MoveForward;
	// static_cast<LivingLocoMotion*>(getLocoMotion())->m_MoveStrafing = m_MoveRight;

	bool bNoClip = true;
	if (isFlying())
	{
		float speed = 15.0f;

		//GM模式以及登录账号为外审人员账号的情况下, 使上升下降的速度和移动速度一样
		if (GetClientInfo()->isGMMode() || GetClientInfo()->IsCurrentUserOuterChecker())
		{
			auto funcWrapper = getFuncWrapper();
			speed = funcWrapper ? funcWrapper->getAIMoveSpeed() : 15.0f;
		}

		WCoord& pos = getPosition();
		if (m_MoveUp > 0)
			getLocoMotion()->m_Motion.y += speed;
		else if (m_MoveUp < 0)
		{
			getLocoMotion()->m_Motion.y -= speed;
			
			/*if (pos.y < -10 * BLOCK_SIZE)
				getLocoMotion()->m_Motion.y = 0;*/
		}

		if (m_MoveForward > EPSILON || m_MoveForward < -EPSILON || m_MoveRight > EPSILON || m_MoveRight < -EPSILON)
		{
			WCoord s, e;
			m_pWorld->getChunkRangeXZ(s.x, s.z, e.x, e.z);
			int iMinRange = MIN_INT / 16;
			int iMaxRange = MAX_INT / 16;
			bool bLimitTileX = s.x > iMinRange && e.x < iMaxRange;
			bool bLimitTileZ = s.z > iMinRange && e.z < iMaxRange;

			if (bNoClip && (bLimitTileX || bLimitTileZ))
			{
				float forward = m_MoveForward;
				float strafing = m_MoveRight;

				float r = strafing * strafing + forward * forward;
				if (r > 0.0001f)
				{
					r = Sqrt(r);
					if (r < 1.0f) r = 1.0f;
					r = speed / r;
					strafing *= r;
					forward *= r;

					Vector3f fdir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
					Vector3f sdir = Yaw2StrafingDir(getLocoMotion()->m_RotateYaw);

					float dx = forward * fdir.x + strafing * sdir.x;
					float dz = forward * fdir.z + strafing * sdir.z;
					int iPosX = pos.x;
					int iPosZ = pos.z;
					if (dx > EPSILON || dx < -EPSILON)
					{
						iPosX += (dx > EPSILON ? BLOCK_SIZE : -BLOCK_SIZE);
					}

					if (dz > EPSILON || dz < -EPSILON)
					{
						iPosZ += (dz > EPSILON ? BLOCK_SIZE : -BLOCK_SIZE);
					}

					m_pWorld->getRangeXZ(s.x, s.z, e.x, e.z);
					if ((bLimitTileX && (iPosX < s.x || iPosX > e.x)) || (bLimitTileZ && (iPosZ < s.z || iPosZ > e.z)))
					{
						bNoClip = false;
					}
				}
			}
		}
	}

	resetEyeDir();
	updateMoveControlFromInputinfo();
	getLocoMotion()->setNoClip(UGCModeManager::getSingleton()->IsPlayerClip() ? false : bNoClip);	//使与方块无碰撞

	ClientPlayer::tick();
	HandMoveLimit();

	if (m_SwitchTick > 0) m_SwitchTick--;

	//if (!isDead())
	//{

	//}

	if (m_iTickTriggerCount >= 10)
	{
		m_iTickTriggerCount = 0;
		triggerActorAttribChunk();
	}

	m_iTickTriggerCount++;

	if (m_iTryShapeShiftID > 0 && m_Body && !m_Body->hasAnimPlaying(SEQ_SHAPE_SHIFT))
	{
		shapeShift();
	}

	if (m_iRecoverViewMode >= 0 && m_bWaitRecoverViewMode && m_Body && !m_Body->hasAnimPlaying(SEQ_RE_SHAPE_SHIFT))
	{
		setViewMode(m_iRecoverViewMode);
		m_iRecoverViewMode = -1;
		m_bWaitRecoverViewMode = false;
	}

	if (GetClientInfo()->isMobile() && getFuncWrapper())
	{
		getFuncWrapper()->setAIMoveSpeed(20);
	}
}

void UGCPlayerControl::tick()
{
	UpdateSpecialItemEffect();

	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		if (m_RecoilTotalRotate > 0.f)
		{
			int iViewMode = g_pPlayerCtrl->getViewMode();
			if (iViewMode == m_lastViewMode)
			{
				float recoilRecoverySpeed = m_fFirstRecoverySpeed;
				if (iViewMode == CAMERA_THIRD_SHOULDER)
				{
					recoilRecoverySpeed = m_fThirdRecoverySpeed;
				}
				m_RecoilTotalRotate -= recoilRecoverySpeed;
				float offset = abs(m_pCamera->m_RotatePitch - m_LastRotateX) / 90.0f;
				if (offset >= 0.001)
				{
					if (offset < recoilRecoverySpeed) recoilRecoverySpeed = offset;
					m_pCamera->rotate(0.0, recoilRecoverySpeed);
				}
			}
		}
		PlayerControl::tick();
		return;
	}

	TickNormal();
	if (m_MoveControl)
		m_MoveControl->endTick();
}

void UGCPlayerControl::setViewMode(int mode, bool ignoreBasketBaller/* =false */)
{
	if (!UGCModeManager::getSingleton()->IsRunning() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		m_buildViewMode = mode;
	}
	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		PlayerControl::setViewMode(mode, ignoreBasketBaller);
		return;
	}

	WorldManager* pWorldMgr = GetWorldManagerPtr();
	if (!pWorldMgr)
		return;

	if (pWorldMgr->getCameraEditState() <= 0)
	{
		mode = CAMERA_FPS;
	}		
	
	m_ViewMode = mode;

	if (m_pCamera)
	{
		m_pCamera->setMode(CameraControlMode(m_ViewMode));
		if (m_ViewMode == CameraControlMode::CAMERA_TPS_BACK_2)
		{
			m_pCamera->setDistMultiply(1.2f);
		}
	}

	m_CameraOffset.Set(0, 0, 0);
}

void UGCPlayerControl::resetEyeDir(bool checkopway)
{
	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		PlayerControl::resetEyeDir(checkopway);
		return;
	}

	Rainbow::Vector3f lookdir(m_pCamera->getLookDir());
	//m_Body->setLookAt(getEyePosition().toVector3() + lookdir * 100.0f);//TODO:合并代码存疑
	float yaw = 0;
	float pitch = 0;
	Direction2PitchYaw(&yaw, &pitch, lookdir);
	m_Body->setLookTargetYaw(yaw);
	m_Body->setLookTargetPitch(pitch);
	m_Body->setDeltaLookPitch(30);
	m_Body->setDeltaLookYaw(30);
	m_Body->setisLookAt(true);
	//没有被限制行动时，允许旋转视角
	if (getFreezing() != FREEZING_STATE_NOMOVE)
	{
		getLocoMotion()->setMoveDir(lookdir);
	}

	//隐藏角色
	m_Body->show(false);
}

bool UGCPlayerControl::canBeCollidedWith()
{
	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
		return true;
	else
		return false;
}

void UGCPlayerControl::changeGameMode(bool scenefallback)
{
	//森林生成器场景特效
	RemoveSpecialItemEffect();

	//先调用父类，否则快捷栏的存档就不是按照编辑模式来存了
	PlayerControl::changeGameMode(scenefallback);

	UGCModeGame* curGame = dynamic_cast<UGCModeGame *>(ClientGameManager::getInstance()->getCurGame());
	if (!curGame)
		return;

	if (UGCModeManager::getSingleton()->IsEditing())
	{
		m_lastToolType = UGCModeManager::getSingleton()->GetToolType();
		curGame->setToolMode(UGCTOOLTYPE_NULL);
		UGCModeManager::getSingleton()->SetType(UGCMODETYPE_RUN);
		showUI(false);
		getLocoMotion()->setNoClip(false);
		m_Body->show(true);
		setFlying(false);
		//EnablePixelMap(true);
		WorldRenderer* pWorldRender = m_pWorld->getRender();
		if (pWorldRender)
		{
			pWorldRender->SetUseCustomRenderPipeline(false);
		}

		MINIW::ScriptVM::game()->callFunction("UGCGameModeChanged", "i", UGCMODETYPE_RUN);
	}
	else if (UGCModeManager::getSingleton()->IsRunning())
	{
		UGCModeManager::getSingleton()->SetType(UGCMODETYPE_EDIT);
		//showUI(true);
		//m_LocoMotion->setNoClip(true);
		//m_Body->show(false);
		//setFlying(true);
		//setViewMode(CAMERA_FPS);
		curGame->SetGameType(UGCModeManager::getSingleton()->GetGameType());
		MINIW::ScriptVM::game()->callFunction("UGCGameModeChanged", "i", UGCMODETYPE_EDIT);
	}
	
	//TODO 暂时写法
	if (UGCModeManager::getSingleton()->IsRunning())
	{
		//curGame->setToolMode(UGCTOOLTYPE_NULL);
		//EditorSelect::Destroy();//TODO 暂时先注释@yangjia
	}
	else if (UGCModeManager::getSingleton()->IsEditing())
	{
		//if (m_lastToolType == UGCTOOLTYPE_SELECTSINGLE || m_lastToolType == UGCTOOLTYPE_SELECTMULTIPLE)
		//{
		//	//暂时只有单选和多选回到上次的模式
		//	curGame->setToolMode(m_lastToolType);
		//}
		//else
		//{
		//	curGame->setToolMode(UGCTOOLTYPE_PLACEBLOCK);
		//}
			
		//TODO 暂时这样做，解决奔溃问题
		EditorSelect::GetInstancePtr()->Load();
	}

	//初始化输入
	if (GetClientInfo()->isMobile())
	{
		UGCTouchControl* touchControl = dynamic_cast<UGCTouchControl*>(getTouchControl());
		if (touchControl)
		{
			touchControl->OnChangeGameType();
		}
	}
	//编辑转玩法切换视角
	if (g_WorldMgr && g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN)
	{
		int mode = 0;
		int defcam = (int)g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA);
		mode = defcam;
		if (9 == defcam || 8 == defcam)
		{
			mode = CAMERA_TPS_BACK_2;
		}
		else if (defcam >= 3 && defcam <= 5)
		{
			mode = defcam - 3;
		}
		else if (6 == defcam || 7 == defcam)
		{
			mode = defcam - 2;
		}
		else if (10 == defcam || 11 == defcam)
		{
			mode = defcam - 6;
		}
		g_pPlayerCtrl->setViewMode(mode);
	}

	/*
	if (g_WorldMgr && g_WorldMgr->m_RuleMgr && g_pPlayerCtrl)
	{
		float val = 0.f;
		int optid = 0;
		g_WorldMgr->m_RuleMgr->getRuleOptionID(GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE, optid, val);
		auto locaMotion = g_pPlayerCtrl->getLocoMotion();
		if (locaMotion)
		{
			PlayerLocoMotion* loc = static_cast<PlayerLocoMotion*>(locaMotion);
			if (optid == 2)
			{
				loc->switchPhysType(RolePhysType::PHYS_RIGIDBODY);
			}
			else {
				loc->switchPhysType(RolePhysType::PHYS_ROLECONTROLLER);
			}
		}
	}
	*/
}

//高级编辑模式下，有些方块只能放置不能交互。
//字牌 模型工作台 未处理
bool UGCPlayerControl::CanInteractBlock(const WCoord& targetblock)
{
	int toolid = getCurToolID();
	if (UGCSPECIALITEM_TREE == toolid || UGCSPECIALITEM_RIVER == toolid || UGCSPECIALITEM_BP == toolid)
	{
		return false;
	}
	//非高级编辑模式 都不屏蔽
	if (!(UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH))
	{
		return true;
	}

	if (toolid == 11100)//手持式编辑器，查看方块详情，允许交互
	{
		return true;
	}
	if (toolid == 11071)//视角编辑器不能用
	{
		return false;
	}

	int blockid = m_pWorld->getBlockID(targetblock);

	//发射装置 感应方块 红外感应方块 钢琴 星站-传送舱一级,星站-传送舱二级 星站-控制台 跳舞厅 蓝图工作台 不能交互
	int blocks[] = { 717, 1032, 1168, 726, 595, 597, 594, 780, 1063 };
	int size = sizeof(blocks) / sizeof(int);
	for (int i = 0; i < size; i++)
	{
		if (blocks[i] == blockid)
			return false;
	}
	
	const BlockDef *def = BlockDefCsv::getInstance()->get(blockid, false);
	Rainbow::FixedString type = def->Type;
	//床、椅子、沙发、帐篷 睡袋 初始化道具箱不能交互
	if (type == "bed" || type == "chair" || type == "shafa" || type == "canvas" || type == "sleepingbag" || type == "initchest")
	{
		return false;
	}

	//可以堆叠，不能交互（箱子，收集传输器）
	if (type == "chest" || type == "bigchest" || type == "funnel")//
	{
		//如果手持箱子 则可以继续放置
		return toolid == blockid;
	}

	return true;
}

//高级编辑模式(后续要区分拼搭模式跟高级模式)下，有些方块只能放置不能交互。
bool UGCPlayerControl::interactBlock(const WCoord& targetblock, DirectionType targetface, const Rainbow::Vector3f& colpoint)
{
	if (!CanInteractBlock(targetblock))
		return false;
	
	int toolid = getCurToolID();
	int originBlockId = 0;
	int originBlockData = 0;
	int oldBlockId = m_pWorld->getBlockID(targetblock);
	int oldBlockData = m_pWorld->getBlockData(targetblock);

	if (UGCModeManager::getSingleton()->GetToolType() == UGCTOOLTYPE_REPLACEBLOCK && toolid > BLOCK_AIR && toolid < BLOCK_UNLOAD)
	{
		originBlockId = m_pWorld->getBlockID(targetblock);
		originBlockData = m_pWorld->getBlockData(targetblock);
		m_pWorld->setBlockAir(targetblock);
	}

	PedalBlockType pedaltype = GetPedalBlockTypeByBlockId(toolid);
	PedalBlockComponent* pedalblockCom = GetComponent< PedalBlockComponent>();
	if (pedalblockCom && pedalblockCom->GetCurState() >= PedalInteractState::ReadyConnect &&
		(pedaltype > PedalBlockType::Null && pedaltype < PedalBlockType::TargetDoor) &&
		GetPedalBlockType(m_pWorld, targetblock) == PedalBlockType::Null)
	{
		pedalblockCom->CancelSelect();
		return true;
	}


	bool bRet = PlayerControl::interactBlock(targetblock, targetface, colpoint);

	if (UGCModeManager::getSingleton()->IsEditing())
	{
		if (bRet)
			UGCModeManager::getSingleton()->StatisticsPlaceBlockId(false, getCurToolID());

		int blockid = m_pWorld->getBlockID(targetblock);
		DirectionType hitface; //
		hitface = ReverseDirection(targetface);
		WCoord placepos;
		BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);

		if (oldBlockId == BLOCK_AIR && blockid != oldBlockId)
		{
			originBlockId = oldBlockId;
			originBlockData = oldBlockData;
			placepos = targetblock;
		}
		else if (pmtl && pmtl->isReplaceable() && toolid != blockid)
		{
			placepos = targetblock;
		}
		else if (pmtl && pmtl->canPlacedAgain(m_pWorld, toolid, colpoint, targetblock, true, hitface))
		{
			placepos = targetblock;
		}
		else
		{
			placepos = NeighborCoord(targetblock, targetface);
		}
		if (UGCModeManager::getSingleton()->GetToolType() == UGCTOOLTYPE_REPLACEBLOCK && toolid > BLOCK_AIR && toolid < BLOCK_UNLOAD)
			placepos = targetblock;

		addBlockCmd(placepos, originBlockId, originBlockData);

		if (pedalblockCom) {
			pedalblockCom->InteractBlock(m_pWorld, targetblock);
		}
	}
	
	return bRet;
}

bool UGCPlayerControl::attackBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, ATTACK_TYPE attacktype)
{
	if (m_pWorld == NULL)
		return false;

	// 运行模式或构建模式下使用标准实现
	if (UGCModeManager::getSingleton()->IsRunning() ||
		UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		int iViewMode = g_pPlayerCtrl->getViewMode();
		if (iViewMode != m_lastViewMode || m_LastRotateX <= 0.f)
		{
			m_lastViewMode = iViewMode;
			m_LastRotateX = m_pCamera->m_RotatePitch;
			m_RecoilTotalRotate = 0.f;
		}
		float recoilSpeed = m_fFirstRecoilSpeed;
		float recoilMaxSpeed = m_fFirstMaxRecoil;
		if (iViewMode == CAMERA_THIRD_SHOULDER)
		{
			recoilSpeed = m_fThirdRecoilSpeed;
			recoilMaxSpeed = m_fThirdMaxRecoil;
		}

		if (abs(m_pCamera->m_RotatePitch - m_LastRotateX) < recoilMaxSpeed)
		{
			m_RecoilTotalRotate += recoilSpeed;
			m_pCamera->rotate(0.f, -recoilSpeed);
		}
		return PlayerControl::attackBlock(blockpos, dgmethod, attacktype);
	}

	// UGC编辑模式特殊处理：跳过部分基类检查，先验证方块存在
	int blockid = m_pWorld->getBlockID(blockpos);
	if (blockid == 0)
		return false;

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl)
		return false;

	// 在编辑模式下，使用更宽松的权限检查
	bool bCanAttackAllBlocks = GetClientInfoProxy()->IsCurrentUserOuterChecker();
	int blockdata = m_pWorld->getBlockData(blockpos);
	if (m_pWorld->CheckBlockSettingEnable(pmtl, ENABLE_DESTROYED) == 0 &&
		!UGCModeManager::getSingleton()->IsEditing() &&
		pmtl->getDestroyHardness(blockdata, this) < 0 &&
		!bCanAttackAllBlocks)
		return false;

	// UGC编辑模式通过检查后，调用基类方法完成后续处理
	return PlayerControl::attackBlock(blockpos, dgmethod, attacktype);
}

void UGCPlayerControl::addBlockCmd(const WCoord& targetblock, const int orginBlockId/* = 0*/, const int originBlockData/* = 0*/)
{
	if (!UGCModeManager::getSingleton()->IsEditing() || UGCModeManager::getSingleton()->GetGameType() != UGCGAMETYPE::UGCGAMETYPE_HIGH)
		return;

	int toolid = getCurToolID();

	if (toolid < 4096 || (toolid >= 90001 && toolid <= ((1 << 19) - 1)))
	{
		vector<WCoord> relativeBlocks;
		if (!EditorSelect::GetInstancePtr()->GetRelativeBlock(targetblock, relativeBlocks))
			return;

		if (relativeBlocks.size() == 0)
			return;

		UGCBlockCmd* pBlockCmd = UGCCmdManager::GetInstancePtr()->GetBlockCmd();
		if (pBlockCmd == NULL)
			return;
		pBlockCmd->StartNewCmd();
		for (auto& pos : relativeBlocks)
		{
			pBlockCmd->AppendBlockInfo(pos, true, orginBlockId, originBlockData);
			pBlockCmd->AppendBlockInfo(pos, false);
		}
		pBlockCmd->EndCurrentCmd();
	}
}

bool UGCPlayerControl::placeBlock(int blockid, int x, int y, int z, int face, float facept_x/* = 0*/, float facept_y/* = 0*/, float facept_z/* = 0*/, bool placeinto /*= false*/, bool isagainplace /*= false*/, int extendData /* = 0*/, World* world /*= NULL*/)
{
	if (blockid == 3 && UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH)
	{
		WCoord placepos(x, y, z);
		int blockdata = 0;
		bool bRet = m_pWorld->setBlockAll(placepos, blockid, blockdata, 2);
		return bRet;
	}
	else
	{
		MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->OnBlockPlace(WCoord(x,y,z), blockid);
		bool bRet = PlayerControl::placeBlock(blockid, x, y, z, face, facept_x, facept_y, facept_z, placeinto, isagainplace, extendData, world);
		return bRet;
	}

	return false;
}

void UGCPlayerControl::destroyBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, bool destroy_effect, bool gamerule_forbid)
{
	if (m_pWorld->isRemoteMode()) return;
	int blockid = m_pWorld->getBlockID(blockpos);
	int blockdata = m_pWorld->getBlockData(blockpos);

	if (blockid == 3 && UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH)
	{
		//m_pWorld->playerDestroyBlock(blockpos, m_MineType, getLivingAttrib()->getDigProbEnchant(), getCurToolID(), 2);
		m_pWorld->setBlockAll(blockpos, 0, 0, 3);	//暂时flag还是得是3, 2的话动态水无法自动回收
	}
	else
	{
		MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->OnBlockPlace(blockpos, 0);
		PlayerControl::destroyBlock(blockpos, dgmethod, destroy_effect, gamerule_forbid);
	}
}

void UGCPlayerControl::playBlockPlaceSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = BlockDefCsv::getInstance()->get(blockid);
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));
	
	if (UGCModeManager::getSingleton()->IsEditing())
	{
		centerpos = getPosition();
	}

	const char* placesound = def->PlaceSound;
	if (placesound[0] == 0) placesound = def->DigSound;
	if (placesound[0] == 0) placesound = "blockd.grass";

	m_pWorld->getEffectMgr()->playSound(centerpos, placesound, GSOUND_PLACE);
}

void UGCPlayerControl::playBlockDigSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = BlockDefCsv::getInstance()->get(blockid);
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	if (UGCModeManager::getSingleton()->IsEditing())
	{
		centerpos = getPosition();
	}

	const char* digsound = def->DigSound;
	if (digsound[0] == 0) digsound = "blockd.grass";

	m_pWorld->getEffectMgr()->playSound(centerpos, digsound, GSOUND_DESTROY);
}

int UGCPlayerControl::onInputEvent(const Rainbow::InputEvent& ev)
{
	if (UGCModeManager::getSingleton()->IsEditing())
	{
		if (ev.type == InputEvent::kMouseDown)
		{
			UGCModeManager::getSingleton()->OnTouchBegin(ev.mousePosition.x, ev.mousePosition.y);
		}		
		else if (ev.type == InputEvent::kMouseUp)
		{
			UGCModeManager::getSingleton()->OnTouchEnd(ev.mousePosition.x, ev.mousePosition.y);
		}
	}

	return PlayerControl::onInputEvent(ev);
}

bool UGCPlayerControl::castShadow()
{
	//到'UGCManager::CheckShadowTick()'中去处理
	/*if (g_WorldMgr && g_WorldMgr->m_RuleMgr)
	{
		int val = (int)g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_ROLESHADOW);
		bool isOpen = false;
		if (val == 1) {
			isOpen = true;
		}

		if (isOpen != WorldRenderer::m_CurShadowOpen)
		{
			WorldRenderer::m_CurShadowOpen = isOpen;
			WorldRenderer::SetShadowEnable(isOpen);
		}

		return isOpen;
	}*/

	return true;
}

bool UGCPlayerControl::useTriggerAreaTool()
{
	WorldManager* pWorldMgr = GetWorldManagerPtr();
	if (!pWorldMgr)
		return false;

	if (pWorldMgr->isAreaToolMode() || pWorldMgr->isPositionToolMode() || pWorldMgr->isDisplayBoardToolMode())
	{
		CTriggerObjLibManager* mgr = CTriggerObjLibManager::GetInstancePtr();
		if (mgr)
		{
			mgr->UpdateCurToolMode();
		}
	}
	else if (pWorldMgr->isGameMakerToolMode() && pWorldMgr->isActorToolMode()) 		// 右键进行生物选择和放置
	{
		CTriggerLivingToolMgr* pLivingToolMgr = CTriggerLivingToolMgr::GetInstancePtr();
		if (pLivingToolMgr)
		{
			if (pLivingToolMgr->getSelectType() == TOOL_SELECT_TYPE_WORLD)
			{
				MINIW::WorldRay ray = UGCModeManager::getSingleton()->GetViewRayByMousePt(m_CurMouseX, m_CurMouseY, 1);
				ray.m_Range = 64 * BLOCK_FSIZE; // 最远32格

				ActorExcludes excludes;
				IntersectResult presult;
				WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
				if (presult.actor) // 生物
				{
					pLivingToolMgr->setSelectObjID(presult.actor->getObjId());
				}
				else
				{
					pLivingToolMgr->setSelectObjID(0);
				}
			}
			else if (pLivingToolMgr->getSelectType() == TOOL_SELECT_TYPE_MOD)
			{
				pLivingToolMgr->setSelectObjIDFromMod(0);
			}
		}
	}
	return true;
}

//地形编辑器位置预览会用到
bool UGCPlayerControl::getAimPos(int& outx, int& outy, int& outz, int distance/* = 20*/, bool calibration/* = false*/)
{
	MINIW::WorldRay ray;
	auto tmpOrigin = getEyePosition();

	if (UGCModeManager::getSingleton()->IsEditing() && g_pPlayerCtrl && g_pPlayerCtrl->m_pCamera->isCameraVaild())
	{
		ray = UGCModeManager::getSingleton()->GetViewRayByMousePt(m_CurMouseX, m_CurMouseY, distance);
	}
	else
	{
		ray.m_Dir = MINIW::Normalize(getLocoMotion()->getLookDir());
	}

	ray.m_Origin = tmpOrigin.toWorldPos();
	ray.m_Range = distance * BLOCK_FSIZE;
	const auto origin = tmpOrigin.toVector3();
	const auto& dir = ray.m_Dir;
	const auto& range = ray.m_Range;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);

	IntersectResult presult;
	if (!m_pWorld)
	{
		return false;
	}

	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::ACTOR) // 生物
	{
		outx = presult.collide_pos.x;
		outy = presult.collide_pos.y;
		outz = presult.collide_pos.z;
		return true;
	}
	else if (intertype == WorldPickResult::BLOCK) // 方块
	{
		// 计算射线命中点
		float blocksize = calibration ? 99.0f : 100.0f;
		Rainbow::Vector3f blockposBeg = presult.block.toVector3() * blocksize;
		Rainbow::Vector3f blockposEnd = blockposBeg + 100.0f;
		Rainbow::Vector3f planePos; // x, y, z

		// 检测法线
		planePos.x = (dir.x > 0) ? blockposBeg.x : blockposEnd.x;
		planePos.y = (dir.y > 0) ? blockposBeg.y : blockposEnd.y;
		planePos.z = (dir.z > 0) ? blockposBeg.z : blockposEnd.z;

		// 射线方程 : pos = origin + dir * k -> k = [0.0f, +infinity)
		Rainbow::Vector3f ret;
		do
		{
			float k = 0.0f;
			if (dir.x != 0 && (k = (planePos.x - origin.x) / dir.x) >= 0.0f)
			{
				ret.x = planePos.x;
				ret.y = origin.y + dir.y * k;
				ret.z = origin.z + dir.z * k;

				if (ret.y >= blockposBeg.y && ret.y <= blockposEnd.y
					&& ret.z >= blockposBeg.z && ret.z <= blockposEnd.z)
				{
					break;
				}
			}
			if (dir.y != 0 && (k = (planePos.y - origin.y) / dir.y) >= 0.0f)
			{
				ret.x = origin.x + dir.x * k;
				ret.y = planePos.y;
				ret.z = origin.z + dir.z * k;

				if (ret.x >= blockposBeg.x && ret.x <= blockposEnd.x
					&& ret.z >= blockposBeg.z && ret.z <= blockposEnd.z)
				{
					break;
				}
			}
			if (dir.z != 0 && (k = (planePos.z - origin.z) / dir.z) >= 0.0f)
			{
				ret.x = origin.x + dir.x * k;
				ret.y = origin.y + dir.y * k;
				ret.z = planePos.z;

				if (ret.x >= blockposBeg.x && ret.x <= blockposEnd.x
					&& ret.y >= blockposBeg.y && ret.y <= blockposEnd.y)
				{
					break;
				}
			}

			//assert(false && "aim point - calc block precision failed!");
			ret = (blockposBeg + blockposEnd) * 0.5f;
		} while (0);

		outx = (int)ret.x;
		outy = (int)ret.y;
		outz = (int)ret.z;
		return true;
	}
	else
	{
		// 取最远点
		Rainbow::Vector3f endpos = origin + dir * range;
		outx = (int)endpos.x;
		outy = (int)endpos.y;
		outz = (int)endpos.z;
		return true;
	}
}

float UGCPlayerControl::GetPickRangeEX()
{
	return 64 * BLOCK_SIZE;
}

int UGCPlayerControl::doPick(bool pickliquid, bool ignorecarried/* =false */, bool addboxpickrange/* =false */, bool isPlaceOnActor/* = false*/)
{
	/*if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		return PlayerControl::doPick(pickliquid, ignorecarried, addboxpickrange);
	}*/

	//因为桶子需要持续检测是否在汲取液体，这里将各类桶子的pickliguid值都置为true
	if (getCurToolID() == ITEM_WOODEN_BUCKET || getCurToolID() == ITEM_BUCKET || getCurToolID() == ITEM_TITANIUM_BUCKET)
	{
		pickliquid = true;
	}
	MINIW::WorldRay ray;
	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		ray.m_Origin = (getPosition() + WCoord(0, 5, 0)).toWorldPos();
		ray.m_Dir = getLocoMotion()->getLookDir();
		ray.m_Dir.y = 0;
		ray.m_Dir = MINIW::Normalize(ray.m_Dir);
	}
	else
	{
		if (m_ViewMode == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = m_pCamera->getLookDir();
		}
		else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else
		{
			float x = m_CurMouseX;
			float y = m_CurMouseY;
			m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
		}
	}

	if (UGCModeManager::getSingleton()->IsRunning() || UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		ray.m_Range = GetPickRange(this);
	}
	else
	{
		ray.m_Range = GetPickRangeEX();
	}

	m_PickResult.liquids.resize(0);
	m_PickResult.intersect_actor = m_PickResult.intersect_block = m_PickResult.isIntersectLiquid = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);
	if (ignorecarried)
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp && CarryComp->getCarringActor())
		{
			excludes.addActor(CarryComp->getCarringActor());
		}
	}


	if (pickliquid)
	{
		m_PickType = (int)GetUGCModeManagerPtr()->UGCPickAll(m_pWorld, ray, &m_PickResult, excludes, isPlaceOnActor, PICK_METHOD_CLICKLIQUID);
	}
	else m_PickType = (int)GetUGCModeManagerPtr()->UGCPickAll(m_pWorld, ray, &m_PickResult, excludes, isPlaceOnActor);

	return m_PickType;
}

////////////////////////////////////////////////////////////
void UGCPlayerControl::SetMoveLimit(WCoord begin, WCoord end)
{
	int minx = begin.x < end.x ? begin.x : end.x;
	int miny = begin.y < end.y ? begin.y : end.y;
	int minz = begin.z < end.z ? begin.z : end.z;
	int maxx = begin.x > end.x ? begin.x : end.x;
	int maxy = begin.y > end.y ? begin.y : end.y;
	int maxz = begin.z > end.z ? begin.z : end.z;

	m_LimitBeginPos = WCoord(minx, miny, minz);
	m_LimitEndPos = WCoord(maxx, maxy, maxz);
}

void UGCPlayerControl::CancelMoveLimit()
{
	m_LimitBeginPos = WCoord(0, 0, 0);
	m_LimitEndPos = WCoord(0, 0, 0);
}

bool UGCPlayerControl::IsInLimitArea(WCoord pos)
{
	if (pos.x >= m_LimitBeginPos.x && pos.x <= m_LimitEndPos.x &&
		pos.y >= m_LimitBeginPos.y && pos.y <= m_LimitEndPos.y &&
		pos.z >= m_LimitBeginPos.z && pos.z <= m_LimitEndPos.z)
		return true;

	return false;
}

bool UGCPlayerControl::CanUseItem(int itemid)
{
	//非高级编辑模式 都不屏蔽
	if (!(UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH))
	{
		return true;
	}
	//信纸11806 指令芯片11809 微缩组合装置10501 冲天炮12822 视角编辑器11071
	if(itemid == 11806 || itemid == 11809 || itemid == 10501 || itemid == 12822 || itemid == 11071)
	{
		return false;
	}

	return true;
}


bool UGCPlayerControl::useItem(int itemid, int status, bool onshift /* = false */, unsigned int useTick /* = 0 */)
{
	if (OnSpecialItemUse(itemid, status))
	{
		return true;
	}
	else
	{
		if (UGCModeManager::getSingleton()->GetToolType() == UGCTOOLTYPE_REMOVEBLOCK)
		{
			return false;
		}

		if (!CanUseItem(itemid))
			return false;

		bool ret = PlayerControl::useItem(itemid, status, onshift, useTick);

		if (itemid == UGCSPECIALITEM_TREE && m_CameraModel)
		{
			m_CameraModel->playItemAnim(100928);
		}

		return ret;
	}
}

bool UGCPlayerControl::OnSpecialItemUse(int itemid, int status)
{
	if (!m_pWorld->isRemoteMode() && UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_BUILD)
	{
		if (itemid == UGCSPECIALITEM_BP)
		{
			OnBluePrintItemUse();
			return true;
		}
	}

	return false;
}

bool UGCPlayerControl::OnBluePrintItemUse()
{
	if (UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_BUILD)
	{
		setCurItemSkillID(0);

		getBody()->playAttack();
		bool scripthandled = false;

		WCoord tmpPos = getLocoMotion()->getPosition();
		DirectionType tmpface = DIR_POS_Y;

		MINIW::ScriptVM::game()->callFunction("UGCBluePrintTool_OnUse", "u[ClientPlayer]u[World]iiii>b", this, m_pWorld, tmpPos.x, tmpPos.y, tmpPos.z, tmpface, &scripthandled/*, &setBlockAllRet*/);

		return scripthandled;
	}
	
	return false;
}


//bool UGCPlayerControl::OnTreeItemUse()
//{
//	MINIW::WorldRay ray = UGCModeManager::getSingleton()->GetViewRayByMousePt(m_CurMouseX, m_CurMouseY, 1);
//	ray.m_Range = gSpecialItemRange * BLOCK_FSIZE; // 最远32格
//
//	ActorExcludes excludes;
//	IntersectResult presult;
//	excludes.addActorWithRiding(this);
//	int intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
//	if (intertype == 1)
//	{
//		WCoord blockpos = presult.block;
//		blockpos.y = blockpos.y + 1;
//		int blockid = m_pWorld->getBlockID(blockpos);
//		if (blockid < 4095)
//		{
//			int index = GetCurSpecialItemIndex();
//			if (index >= 1 && index < UGCEcosysUnitTypeMax) {
//				if (m_CameraModel) {
//					m_CameraModel->playItemAnim(100928);
//				}
//
//				UGCEcosysParams params;
//				UGCEcosysUnitMgr::GetInstancePtr()->AddUnitToWorld(UGCEcosysUnitType(index), m_pWorld, blockpos, params);
//				//0315埋点:树木生成器埋点
//				std::stringstream sReportStr("");
//				sReportStr << index;
//				UGCModeManager::getSingleton()->StandReportEvent("5005", "MINI_ADVANCEDMODEASSEMBLING_MAINSCENE", "TreeMakerUse", "click", sReportStr.str());
//			}
//		}
//	}
//
//	return true;
//}

//bool UGCPlayerControl::OnRiverItemUse()
//{
//	MINIW::WorldRay ray = UGCModeManager::getSingleton()->GetViewRayByMousePt(m_CurMouseX, m_CurMouseY, 1);
//	ray.m_Range = gSpecialItemRange * BLOCK_FSIZE; // 最远32格
//
//	ActorExcludes excludes;
//	IntersectResult presult;
//	excludes.addActorWithRiding(this);
//	int intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
//	if (intertype == 1)
//	{
//		int index = GetCurSpecialItemIndex();
//		UGCEcosysParams params;
//		params.type = index;
//		UGCEcosysUnitMgr::GetInstancePtr()->AddUnitToWorld(UGCEcosysUnitTypeRiver, m_pWorld, presult.block, params);
//	}
//
//	return true;
//}

void UGCPlayerControl::UpdateSpecialItemEffect()
{
	if (!m_pWorld)
		return;

	if (UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE_BUILD)
	{
		if (m_lastToolId == UGCSPECIALITEM_TREE)
		{
			EffectManager* effectmgr = m_pWorld->getEffectMgr();
			if (effectmgr) {
				MINIW::WorldRay ray = UGCModeManager::getSingleton()->GetViewRayByMousePt(m_CurMouseX, m_CurMouseY, 1);
				ray.m_Range = gSpecialItemRange * BLOCK_FSIZE;
				ActorExcludes excludes;
				IntersectResult presult;
				excludes.addActorWithRiding(this);
				WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
				if (intertype == WorldPickResult::BLOCK)
				{
					WCoord blockpos = presult.block;
					blockpos.y = blockpos.y + 1;
					blockpos = BlockCenterCoord(blockpos);

					//effectmgr->playParticleEffect("particles/fangyu_up.ent", blockpos, 10, 0, 90, false, gSpecialItemRange);
					if (!m_pSpecialEffect) {
						m_pSpecialEffect = ENG_NEW(EffectParticle)(m_pWorld, "particles/ball_team_5.ent", blockpos, 0, gSpecialItemRange);
						m_pSpecialEffect->setRotation(0, 90, 0);
						m_pSpecialEffect->setScale(2);
						effectmgr->addEffect(m_pSpecialEffect);
					}

					m_pSpecialEffect->show(true);
					m_pSpecialEffect->setPosition(blockpos.x, blockpos.y, blockpos.z);
				}
			}
		}
		else
		{
			if (m_pSpecialEffect)
				m_pSpecialEffect->show(false);
		}
	}
	else
	{
		if (m_pSpecialEffect)
			m_pSpecialEffect->show(false);
	}
}

void UGCPlayerControl::RemoveSpecialItemEffect()
{
	if (m_pWorld && m_pSpecialEffect)
	{
		EffectManager* mgr = m_pWorld->getEffectMgr();
		if (mgr) {
			mgr->removeEffect(m_pSpecialEffect);
		}
	}

	m_pSpecialEffect = NULL;
}

void UGCPlayerControl::SetTreeItemIndex(int itemid, int index)
{
	m_treeItemIndex = index;
	SwitchWeaponSubMesh(itemid, index);
}

bool UGCPlayerControl::SwitchWeaponSubMesh(int itemid, int index)
{
	ModelItemMesh* weaponModel = dynamic_cast<ModelItemMesh*>(getBody()->getWeaponModel());
	if (!weaponModel)
		return false;

	UGCModeManager::getSingleton()->SwitchItemModelMesh(weaponModel, itemid, index);

	if (m_CameraModel && m_CameraModel->m_ToolModel)
	{
		weaponModel = dynamic_cast<ModelItemMesh*>(m_CameraModel->m_ToolModel);
		if(weaponModel)
			UGCModeManager::getSingleton()->SwitchItemModelMesh(weaponModel, itemid, index);
	}

	return true;
}

bool UGCPlayerControl::HandMoveLimit()
{
	if (m_LimitEndPos.isZero() && m_LimitBeginPos.isZero())
		return false;

	WCoord OldPosition = getLocoMotion()->m_OldPosition;
	WCoord CurPosition = getLocoMotion()->m_Position;

	if (IsInLimitArea(OldPosition) && !IsInLimitArea(CurPosition))
	{
		//m_LocoMotion->setPosition(OldPosition.x, OldPosition.y, OldPosition.z);
		//return true;
		if (CurPosition.x < m_LimitBeginPos.x)
			CurPosition.x = m_LimitBeginPos.x;

		if (CurPosition.x > m_LimitEndPos.x)
			CurPosition.x = m_LimitEndPos.x;


		if (CurPosition.y < m_LimitBeginPos.y)
			CurPosition.y = m_LimitBeginPos.y;

		if (CurPosition.y > m_LimitEndPos.y)
			CurPosition.y = m_LimitEndPos.y;


		if (CurPosition.z < m_LimitBeginPos.z)
			CurPosition.z = m_LimitBeginPos.z;

		if (CurPosition.z > m_LimitEndPos.z)
			CurPosition.z = m_LimitEndPos.z;

		getLocoMotion()->setPosition(CurPosition.x, CurPosition.y, CurPosition.z);
	}

	return false;
}

bool UGCPlayerControl::CanMove()
{
	bool bCanMove = true;

	// 坐在椅子上不可移动
	if (getSitting())
	{
		bCanMove = false;
	}

	//被限制行动时，不允许移动
	if (getFreezing() == FREEZING_STATE_NOMOVE)
	{
		bCanMove = false;
	}

	SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("Music_CheckisCanMove", SandboxContext(nullptr));
	//演奏时根据配置，不允许移动
	if (!result.IsSuccessed()) {
		bCanMove = false;
	}

	return bCanMove;
}

//篮球衣跟足球衣不改变模式
void UGCPlayerControl::changeOPWay()
{
	//高级编辑模式 屏蔽
	if (UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_HIGH)
	{
		int iToolId = getLivingAttrib()->getEquipItem(EQUIP_WEAPON);
		auto RidComp = getRiddenComponent();
		bool isRiding = RidComp ? RidComp->isRiding() : false;
		if (iToolId == ITEM_FOOTBALLWEAR && !isRiding)
			return;

		if (iToolId == ITEM_BASKETBALLWEAR && !isRiding)
			return;
	}

	PlayerControl::changeOPWay();
}

void UGCPlayerControl::OnGameTypeChanged(UGCGAMETYPE gameType)
{
	if (gameType == UGCGAMETYPE_BUILD)
	{
		//改由lua控制
		//EnablePixelMap(true);
		showUI(false);
		getLocoMotion()->setNoClip(false);
		m_Body->show(true);
		setFlying(false);
		setViewMode(m_buildViewMode);

		if (m_pWorld)
		{
			WorldRenderer* pWorldRender = m_pWorld->getRender();
			if (pWorldRender)
			{
				pWorldRender->SetUseCustomRenderPipeline(false);
			}
		}
	}
	else if (gameType == UGCGAMETYPE_HIGH)
	{
		//EnablePixelMap(false);
		showUI(true);
		getLocoMotion()->setNoClip(true);
		m_Body->show(false);
		setFlying(true);
		/*
		设置在地面为false，解决ActorBody中播放SEQ_RUN和SEQ_WALK动作时有脚印问题，因为ActorLocoMotion::doMoveStep中noClip的情况下不会再去计算m_OnGround的值，所以
		当在地面上切换高级模式的时候m_OnGround值就用的是非高级时的旧值
		*/
		getLocoMotion()->setOnGround(false);
		setViewMode(CAMERA_FPS);

		if (GetClientInfo()->isMobile())
		{
			getTouchControl()->ResetAllInput();
		}
		else if (GetClientInfo()->isPC())
		{
			getPCControl()->ResetAllInput();
		}

		if (m_pWorld)
		{
			WorldRenderer* pWorldRender = m_pWorld->getRender();
			if (pWorldRender)
			{
				pWorldRender->SetUseCustomRenderPipeline(true);
			}
		}
	}

	if (GetClientInfo()->isMobile())
	{
		UGCTouchControl* touchControl = dynamic_cast<UGCTouchControl*>(getTouchControl());
		if (touchControl)
		{
			touchControl->OnChangeGameType();
		}
	}
}


void UGCPlayerControl::onSetCurShortcut(int i)
{
	PlayerControl::onSetCurShortcut(i);

	int itemid = getCurToolID();
	//高级编辑模式 拼搭模式特殊道具
	if (UGCModeManager::getSingleton()->IsEditing() && UGCModeManager::getSingleton()->GetGameType() == UGCGAMETYPE::UGCGAMETYPE_BUILD)
	{
		if (UGCSPECIALITEM_TREE == itemid || UGCSPECIALITEM_RIVER == itemid || UGCSPECIALITEM_BP == itemid)
		{
			MINIW::ScriptVM::game()->callFunction("OpenSpecailItemView", "i", itemid);
			return;
		}
	}

	MINIW::ScriptVM::game()->callFunction("CloseSpecailItemView", "");
}

void UGCPlayerControl::EnablePixelMap(bool bEnable)
{
	int isEnable = 1;

	if (!bEnable)
		isEnable = 0;

	PixelMapConfig* pConfig = &g_WorldMgr->m_SurviveGameConfig->pixelMapConfig;
	if (pConfig)
		pConfig->isEnable = isEnable;
}

BlockScene* UGCPlayerControl::getScene()
{
	if (m_pWorld == NULL)
		return NULL;
	if (PrefabEditModeMgr::GetInstancePtr()->GetScene() != NULL)
		return PrefabEditModeMgr::GetInstancePtr()->GetScene();
	else
		return m_pWorld->getScene();
}