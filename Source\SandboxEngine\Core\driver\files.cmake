SET(CUR_DIR "")

add_filtered_std("${CUR_DIR}/")
add_filtered_std("${CUR_DIR}/asset")
add_filtered_std("${CUR_DIR}/asset/ability")
add_filtered_std("${CUR_DIR}/asset/instance")
add_filtered_std("${CUR_DIR}/asset/object")
add_filtered_std("${CUR_DIR}/asset/ref")
add_filtered_std("${CUR_DIR}/asset/new")
add_filtered_std("${CUR_DIR}/asset/new/http")
add_filtered_std("${CUR_DIR}/asset/new/ref")
add_filtered_std("${CUR_DIR}/asset/new/upload")
add_filtered_std("${CUR_DIR}/asset/http")
add_filtered_std("${CUR_DIR}/asset/sync")
add_filtered_std("${CUR_DIR}/base")
add_filtered_std("${CUR_DIR}/base/reflex")
add_filtered_std("${CUR_DIR}/base/timer")
add_filtered_std("${CUR_DIR}/base/stream")
add_filtered_std("${CUR_DIR}/base/tick")
add_filtered_std("${CUR_DIR}/event")
add_filtered_std("${CUR_DIR}/event/notify")
add_filtered_std("${CUR_DIR}/event/remote")
add_filtered_std("${CUR_DIR}/factory")
add_filtered_std("${CUR_DIR}/fps")
add_filtered_std("${CUR_DIR}/fps/statistics")
add_filtered_std("${CUR_DIR}/math")
add_filtered_std("${CUR_DIR}/platform")
add_filtered_std("${CUR_DIR}/scene")
add_filtered_std("${CUR_DIR}/scene/map")
add_filtered_std("${CUR_DIR}/scene/node")
add_filtered_std("${CUR_DIR}/script")
add_filtered_std("${CUR_DIR}/script/bridge")
add_filtered_std("${CUR_DIR}/script/linker")
add_filtered_std("${CUR_DIR}/script/userdata")
add_filtered_std("${CUR_DIR}/util")
add_filtered_std("${CUR_DIR}/util/thread")
add_filtered_std("${CUR_DIR}/miscdata")

