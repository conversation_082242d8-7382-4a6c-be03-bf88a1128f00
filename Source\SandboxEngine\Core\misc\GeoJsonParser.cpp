#include "GeoJsonParser.h"

class ValidationResult;

Feature GeoJsonParser::parseFeature(const json& featureJson) {
    Feature feature;
    
    // 验证type
    if (featureJson["type"] != "Feature") {
        throw std::runtime_error("Invalid Feature type");
    }

    // 解析geometry
    if (featureJson.contains("geometry") && !featureJson["geometry"].is_null()) {
        feature.geometry = parseGeometry(featureJson["geometry"]);
    }

    // 解析properties
    if (featureJson.contains("properties")) {
        feature.properties = featureJson["properties"];
    }

    // 解析id（可选）
    if (featureJson.contains("id")) {
        if (featureJson["id"].is_string()) {
            feature.id = featureJson["id"].get<std::string>();
        } else if (featureJson["id"].is_number()) {
            feature.id = std::to_string(featureJson["id"].get<int64_t>());
        }
    }

    // 解析CRS（如果存在）
    if (featureJson.contains("crs")) {
        feature.crs = parseCRS(featureJson["crs"]);
    }

    return feature;
}

std::shared_ptr<Geometry> GeoJsonParser::parseGeometry(const json& geometryJson) {
    std::string type = geometryJson["type"].get<std::string>();
    GeometryType geoType = getGeometryType(type);
    
    switch (geoType) {
        case GeometryType::Point: {
            GeoPoint point = parsePoint(geometryJson["coordinates"]);
            return std::make_shared<PointGeometry>(point);
        }
        case GeometryType::MultiPoint: {
            std::vector<GeoPoint> points = parseMultiPoint(geometryJson["coordinates"]);
            return std::make_shared<MultiPointGeometry>(points);
        }
        case GeometryType::LineString: {
            std::vector<GeoPoint> points = parseLineString(geometryJson["coordinates"]);
            return std::make_shared<LineStringGeometry>(points);
        }
        case GeometryType::MultiLineString: {
            auto lines = parseMultiLineString(geometryJson["coordinates"]);
            return std::make_shared<MultiLineStringGeometry>(lines);
        }
        case GeometryType::Polygon: {
            auto rings = parsePolygon(geometryJson["coordinates"]);
            return std::make_shared<PolygonGeometry>(rings);
        }
        case GeometryType::MultiPolygon: {
            auto polygons = parseMultiPolygon(geometryJson["coordinates"]);
            return std::make_shared<MultiPolygonGeometry>(polygons);
        }
        case GeometryType::GeometryCollection: {
            auto geometries = parseGeometryCollection(geometryJson["geometries"]);
            return std::make_shared<GeometryCollectionGeometry>(geometries);
        }
        default:
            throw std::runtime_error("Unsupported geometry type: " + type);
    }
}

std::vector<Feature> GeoJsonParser::parseFeatureCollection(const std::string& geojsonStr) {
    json geojson = json::parse(geojsonStr);
    std::vector<Feature> features;

    if (geojson["type"] != "FeatureCollection") {
        throw std::runtime_error("Invalid GeoJSON: root type must be FeatureCollection");
    }

    // 解析顶层CRS（如果存在）
    CRS collectionCrs;
    if (geojson.contains("crs")) {
        collectionCrs = parseCRS(geojson["crs"]);
    }

    for (const auto& featureJson : geojson["features"]) {
        Feature feature = parseFeature(featureJson);
        // 如果Feature没有自己的CRS，使用FeatureCollection的CRS
        if (feature.crs.type == CRSType::Default && collectionCrs.type != CRSType::Default) {
            feature.crs = collectionCrs;
        }
        features.push_back(feature);
    }

    return features;
}

GeoPoint GeoJsonParser::parsePoint(const json& coordinates) {
    if (!coordinates.is_array() || coordinates.size() < 2) {
        throw std::runtime_error("Invalid Point coordinates");
    }
    
    double lon = coordinates[0].get<double>();
    double lat = coordinates[1].get<double>();
    double alt = coordinates.size() > 2 ? coordinates[2].get<double>() : 0.0;
    
    return GeoPoint(lon, lat, alt);
}

std::vector<GeoPoint> GeoJsonParser::parseLineString(const json& coordinates) {
    std::vector<GeoPoint> points;
    for (const auto& coord : coordinates) {
        points.push_back(parsePoint(coord));
    }
    return points;
}

std::vector<std::vector<GeoPoint>> GeoJsonParser::parsePolygon(const json& coordinates) {
    std::vector<std::vector<GeoPoint>> rings;
    for (const auto& ring : coordinates) {
        rings.push_back(parseLineString(ring));
    }
    return rings;
}

GeometryType GeoJsonParser::getGeometryType(const std::string& typeStr) {
    if (typeStr == "Point") return GeometryType::Point;
    if (typeStr == "MultiPoint") return GeometryType::MultiPoint;
    if (typeStr == "LineString") return GeometryType::LineString;
    if (typeStr == "MultiLineString") return GeometryType::MultiLineString;
    if (typeStr == "Polygon") return GeometryType::Polygon;
    if (typeStr == "MultiPolygon") return GeometryType::MultiPolygon;
    if (typeStr == "GeometryCollection") return GeometryType::GeometryCollection;
    return GeometryType::Unknown;
}

json GeoJsonParser::geometryToJson(const std::shared_ptr<Geometry>& geometry) {
    json geometryJson;
    
    switch (geometry->type) {
        case GeometryType::Point: {
            auto point = std::static_pointer_cast<PointGeometry>(geometry);
            geometryJson["type"] = "Point";
            geometryJson["coordinates"] = pointToJson(point->point);
            break;
        }
        case GeometryType::MultiPoint: {
            auto multiPoint = std::static_pointer_cast<MultiPointGeometry>(geometry);
            geometryJson["type"] = "MultiPoint";
            json coordinates = json::array();
            for (const auto& point : multiPoint->points) {
                coordinates.push_back(pointToJson(point));
            }
            geometryJson["coordinates"] = coordinates;
            break;
        }
        case GeometryType::LineString: {
            auto lineString = std::static_pointer_cast<LineStringGeometry>(geometry);
            geometryJson["type"] = "LineString";
            geometryJson["coordinates"] = lineStringToJson(lineString->points);
            break;
        }
        case GeometryType::MultiLineString: {
            auto multiLineString = std::static_pointer_cast<MultiLineStringGeometry>(geometry);
            geometryJson["type"] = "MultiLineString";
            json coordinates = json::array();
            for (const auto& line : multiLineString->lineStrings) {
                coordinates.push_back(lineStringToJson(line));
            }
            geometryJson["coordinates"] = coordinates;
            break;
        }
        case GeometryType::Polygon: {
            auto polygon = std::static_pointer_cast<PolygonGeometry>(geometry);
            geometryJson["type"] = "Polygon";
            geometryJson["coordinates"] = polygonToJson(polygon->rings);
            break;
        }
        case GeometryType::MultiPolygon: {
            auto multiPolygon = std::static_pointer_cast<MultiPolygonGeometry>(geometry);
            geometryJson["type"] = "MultiPolygon";
            json coordinates = json::array();
            for (const auto& polygon : multiPolygon->polygons) {
                coordinates.push_back(polygonToJson(polygon));
            }
            geometryJson["coordinates"] = coordinates;
            break;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            geometryJson["type"] = "GeometryCollection";
            json geometries = json::array();
            for (const auto& geom : collection->geometries) {
                geometries.push_back(geometryToJson(geom));
            }
            geometryJson["geometries"] = geometries;
            break;
        }
        default:
            throw std::runtime_error("Unsupported geometry type for conversion");
    }
    
    return geometryJson;
}

json GeoJsonParser::featureToJson(const Feature& feature) {
    json featureJson;
    
    featureJson["type"] = "Feature";
    
    // 添加几何信息
    if (feature.geometry) {
        featureJson["geometry"] = geometryToJson(feature.geometry);
    } else {
        featureJson["geometry"] = nullptr;
    }
    
    // 添加属性
    featureJson["properties"] = feature.properties;
    featureJson["id"] = feature.id;
    // 添加ID（如果存在）
   /* if (!feature.id.empty()) {
        // 尝试将id转换为数字（如果可能的话）
        try {
            size_t pos;
            long long numId = std::stoll(feature.id, &pos);
            if (pos == feature.id.length()) {
                featureJson["id"] = numId;
            } else {
                featureJson["id"] = feature.id;
            }
        } catch (...) {
            featureJson["id"] = feature.id;
        }
    }*/
    
    // 添加CRS（如果不是默认CRS）
    if (feature.crs.type != CRSType::Default) {
        featureJson["crs"] = crsToJson(feature.crs);
    }
    
    return featureJson;
}

json GeoJsonParser::featureCollectionToJson(const std::vector<Feature>& features) {
    json collection;
    collection["type"] = "FeatureCollection";
    
    // 检查是否所有feature使用相同的非默认CRS
    CRS commonCrs;
    bool hasCommonCrs = false;
    
    if (!features.empty()) {
        commonCrs = features[0].crs;
        hasCommonCrs = (commonCrs.type != CRSType::Default);
        
        for (size_t i = 1; i < features.size() && hasCommonCrs; ++i) {
            if (features[i].crs.type != commonCrs.type ||
                features[i].crs.name != commonCrs.name ||
                features[i].crs.href != commonCrs.href) {
                hasCommonCrs = false;
            }
        }
    }

    // 如果有共同的非默认CRS，添加到FeatureCollection级别
    if (hasCommonCrs) {
        collection["crs"] = crsToJson(commonCrs);
    }

    json featuresJson = json::array();
    for (const auto& feature : features) {
        json featureJson = featureToJson(feature);
        // 如果使用共同的CRS，移除单个feature的CRS
        if (hasCommonCrs) {
            featureJson.erase("crs");
        }
        featuresJson.push_back(featureJson);
    }
    collection["features"] = featuresJson;
    
    return collection;
}

std::string GeoJsonParser::stringify(const json& j, bool pretty) {
    return pretty ? j.dump(2) : j.dump();
}

json GeoJsonParser::pointToJson(const GeoPoint& point) {
    json coordinates = json::array();
    coordinates.push_back(point.longitude);
    coordinates.push_back(point.latitude);
    if (point.altitude != 0.0) {
        coordinates.push_back(point.altitude);
    }
    return coordinates;
}

json GeoJsonParser::lineStringToJson(const std::vector<GeoPoint>& points) {
    json coordinates = json::array();
    for (const auto& point : points) {
        coordinates.push_back(pointToJson(point));
    }
    return coordinates;
}

json GeoJsonParser::polygonToJson(const std::vector<std::vector<GeoPoint>>& rings) {
    json coordinates = json::array();
    for (const auto& ring : rings) {
        coordinates.push_back(lineStringToJson(ring));
    }
    return coordinates;
}

CRS GeoJsonParser::parseCRS(const json& crsJson) {
    if (!crsJson.contains("type")) {
        throw std::runtime_error("CRS must have a type");
    }

    std::string type = crsJson["type"].get<std::string>();
    
    if (type == "name") {
        if (!crsJson.contains("properties") || !crsJson["properties"].contains("name")) {
            throw std::runtime_error("Name CRS must have a name property");
        }
        return CRS::createName(crsJson["properties"]["name"].get<std::string>());
    }
    else if (type == "link") {
        if (!crsJson.contains("properties") || !crsJson["properties"].contains("href")) {
            throw std::runtime_error("Link CRS must have an href property");
        }
        std::string href = crsJson["properties"]["href"].get<std::string>();
        std::string linkType = crsJson["properties"].contains("type") ? 
            crsJson["properties"]["type"].get<std::string>() : "";
        return CRS::createLink(href, linkType);
    }
    
    throw std::runtime_error("Unsupported CRS type: " + type);
}

json GeoJsonParser::crsToJson(const CRS& crs) {
    // 如果是默认CRS (WGS84)，根据规范不输出CRS对象
    if (crs.type == CRSType::Default) {
        return nullptr;
    }

    json crsJson;
    json properties;

    if (crs.type == CRSType::Name) {
        crsJson["type"] = "name";
        properties["name"] = crs.name;
    }
    else if (crs.type == CRSType::Link) {
        crsJson["type"] = "link";
        properties["href"] = crs.href;
        if (!crs.linkType.empty()) {
            properties["type"] = crs.linkType;
        }
    }

    crsJson["properties"] = properties;
    return crsJson;
}

std::vector<GeoPoint> GeoJsonParser::parseMultiPoint(const json& coordinates) {
    std::vector<GeoPoint> points;
    for (const auto& coord : coordinates) {
        points.push_back(parsePoint(coord));
    }
    return points;
}

std::vector<std::vector<GeoPoint>> GeoJsonParser::parseMultiLineString(const json& coordinates) {
    std::vector<std::vector<GeoPoint>> lines;
    for (const auto& line : coordinates) {
        lines.push_back(parseLineString(line));
    }
    return lines;
}

std::vector<std::vector<std::vector<GeoPoint>>> GeoJsonParser::parseMultiPolygon(const json& coordinates) {
    std::vector<std::vector<std::vector<GeoPoint>>> polygons;
    for (const auto& polygon : coordinates) {
        polygons.push_back(parsePolygon(polygon));
    }
    return polygons;
}

std::vector<std::shared_ptr<Geometry>> GeoJsonParser::parseGeometryCollection(
    const json& geometries) {
    std::vector<std::shared_ptr<Geometry>> collection;
    for (const auto& geometry : geometries) {
        collection.push_back(parseGeometry(geometry));
    }
    return collection;
}

double GeoJsonParser::GeometryCalculator::toRadians(double degrees) {
    return degrees * PI / 180.0;
}

double GeoJsonParser::GeometryCalculator::haversineDistance(const GeoPoint& p1, const GeoPoint& p2) {
    double dLat = toRadians(p2.latitude - p1.latitude);
    double dLon = toRadians(p2.longitude - p1.longitude);
    
    double lat1 = toRadians(p1.latitude);
    double lat2 = toRadians(p2.latitude);
    
    double a = std::sin(dLat/2) * std::sin(dLat/2) +
               std::sin(dLon/2) * std::sin(dLon/2) * std::cos(lat1) * std::cos(lat2);
    double c = 2 * std::atan2(std::sqrt(a), std::sqrt(1-a));
    
    return EARTH_RADIUS * c;
}

double GeoJsonParser::GeometryCalculator::distance(const GeoPoint& p1, const GeoPoint& p2) {
    return haversineDistance(p1, p2);
}

double GeoJsonParser::GeometryCalculator::length(const std::vector<GeoPoint>& points) {
    double total = 0.0;
    for (size_t i = 1; i < points.size(); ++i) {
        total += distance(points[i-1], points[i]);
    }
    return total;
}

double GeoJsonParser::GeometryCalculator::length(const std::shared_ptr<Geometry>& geometry) {
    switch (geometry->type) {
        case GeometryType::LineString: {
            auto lineString = std::static_pointer_cast<LineStringGeometry>(geometry);
            return length(lineString->points);
        }
        case GeometryType::MultiLineString: {
            auto multiLineString = std::static_pointer_cast<MultiLineStringGeometry>(geometry);
            double total = 0.0;
            for (const auto& line : multiLineString->lineStrings) {
                total += length(line);
            }
            return total;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            double total = 0.0;
            for (const auto& geom : collection->geometries) {
                total += length(geom);
            }
            return total;
        }
        default:
            return 0.0;
    }
}

double GeoJsonParser::GeometryCalculator::signedArea(const std::vector<GeoPoint>& ring) {
    double area = 0.0;
    for (size_t i = 0; i < ring.size(); ++i) {
        size_t j = (i + 1) % ring.size();
        area += toRadians(ring[i].longitude) * std::sin(toRadians(ring[j].latitude));
        area -= toRadians(ring[j].longitude) * std::sin(toRadians(ring[i].latitude));
    }
    area = area * EARTH_RADIUS * EARTH_RADIUS / 2.0;
    return area;
}

double GeoJsonParser::GeometryCalculator::area(const std::vector<GeoPoint>& ring) {
    return std::abs(signedArea(ring));
}

double GeoJsonParser::GeometryCalculator::area(const std::vector<std::vector<GeoPoint>>& rings) {
    if (rings.empty()) return 0.0;
    
    // 外环面积
    double total = area(rings[0]);
    
    // 减去所有内环面积
    for (size_t i = 1; i < rings.size(); ++i) {
        total -= area(rings[i]);
    }
    
    return total;
}

double GeoJsonParser::GeometryCalculator::area(const std::shared_ptr<Geometry>& geometry) {
    switch (geometry->type) {
        case GeometryType::Polygon: {
            auto polygon = std::static_pointer_cast<PolygonGeometry>(geometry);
            return area(polygon->rings);
        }
        case GeometryType::MultiPolygon: {
            auto multiPolygon = std::static_pointer_cast<MultiPolygonGeometry>(geometry);
            double total = 0.0;
            for (const auto& polygon : multiPolygon->polygons) {
                total += area(polygon);
            }
            return total;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            double total = 0.0;
            for (const auto& geom : collection->geometries) {
                total += area(geom);
            }
            return total;
        }
        default:
            return 0.0;
    }
}

void GeoJsonParser::GeometryCalculator::updateBoundingBox(BoundingBox& box, const GeoPoint& point) {
    box.minLon = std::min(box.minLon, point.longitude);
    box.maxLon = std::max(box.maxLon, point.longitude);
    box.minLat = std::min(box.minLat, point.latitude);
    box.maxLat = std::max(box.maxLat, point.latitude);
}

GeoJsonParser::GeometryCalculator::BoundingBox 
GeoJsonParser::GeometryCalculator::getBoundingBox(const std::shared_ptr<Geometry>& geometry) {
    BoundingBox box;
    
    switch (geometry->type) {
        case GeometryType::Point: {
            auto point = std::static_pointer_cast<PointGeometry>(geometry);
            updateBoundingBox(box, point->point);
            break;
        }
        case GeometryType::MultiPoint: {
            auto multiPoint = std::static_pointer_cast<MultiPointGeometry>(geometry);
            for (const auto& point : multiPoint->points) {
                updateBoundingBox(box, point);
            }
            break;
        }
        case GeometryType::LineString: {
            auto lineString = std::static_pointer_cast<LineStringGeometry>(geometry);
            for (const auto& point : lineString->points) {
                updateBoundingBox(box, point);
            }
            break;
        }
        case GeometryType::MultiLineString: {
            auto multiLineString = std::static_pointer_cast<MultiLineStringGeometry>(geometry);
            for (const auto& line : multiLineString->lineStrings) {
                for (const auto& point : line) {
                    updateBoundingBox(box, point);
                }
            }
            break;
        }
        case GeometryType::Polygon: {
            auto polygon = std::static_pointer_cast<PolygonGeometry>(geometry);
            for (const auto& ring : polygon->rings) {
                for (const auto& point : ring) {
                    updateBoundingBox(box, point);
                }
            }
            break;
        }
        case GeometryType::MultiPolygon: {
            auto multiPolygon = std::static_pointer_cast<MultiPolygonGeometry>(geometry);
            for (const auto& polygon : multiPolygon->polygons) {
                for (const auto& ring : polygon) {
                    for (const auto& point : ring) {
                        updateBoundingBox(box, point);
                    }
                }
            }
            break;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            for (const auto& geom : collection->geometries) {
                auto geomBox = getBoundingBox(geom);
                box.minLon = std::min(box.minLon, geomBox.minLon);
                box.maxLon = std::max(box.maxLon, geomBox.maxLon);
                box.minLat = std::min(box.minLat, geomBox.minLat);
                box.maxLat = std::max(box.maxLat, geomBox.maxLat);
            }
            break;
        }
        default:
            // Unknown 类型不需要特殊处理
            break;
    }
    
    return box;
}

bool GeoJsonParser::GeometryCalculator::pointInPolygon(
    const GeoPoint& point, const std::vector<GeoPoint>& ring) {
    bool inside = false;
    size_t j = ring.size() - 1;
    
    for (size_t i = 0; i < ring.size(); i++) {
        if ((ring[i].latitude > point.latitude) != (ring[j].latitude > point.latitude) &&
            (point.longitude < (ring[j].longitude - ring[i].longitude) * 
             (point.latitude - ring[i].latitude) / (ring[j].latitude - ring[i].latitude) +
             ring[i].longitude)) {
            inside = !inside;
        }
        j = i;
    }
    
    return inside;
}

bool GeoJsonParser::GeometryCalculator::pointInPolygon(
    const GeoPoint& point, const std::vector<std::vector<GeoPoint>>& rings) {
    if (rings.empty()) return false;
    
    // 检查是否在外环内
    if (!pointInPolygon(point, rings[0])) return false;
    
    // 检查是否在任何内环内
    for (size_t i = 1; i < rings.size(); ++i) {
        if (pointInPolygon(point, rings[i])) return false;
    }
    
    return true;
}

ValidationResult GeoJsonParser::validate(const json& geojson) {
    ValidationResult result;
    
    // Validate that input is an object
    if (!geojson.is_object()) {
        result.addError("GeoJSON must be an object");
        return result;
    }

    // Validate type property
    if (!validateType(geojson, result)) {
        return result;
    }

    std::string type = geojson["type"].get<std::string>();

    // Validate based on type
    if (type == "Feature") {
        // Parse and validate Feature
        try {
            Feature feature = parseFeature(geojson);
            result = validateFeature(feature);
        } catch (const std::exception& e) {
            result.addError("Failed to parse Feature: " + std::string(e.what()));
        }
    }
    else if (type == "FeatureCollection") {
        // Validate FeatureCollection
        if (!geojson.contains("features") || !geojson["features"].is_array()) {
            result.addError("FeatureCollection must have 'features' array");
            return result;
        }
        try {
            std::vector<Feature> features;
            for (const auto& featureJson : geojson["features"]) {
                features.push_back(parseFeature(featureJson));
            }
            result = validateFeatureCollection(features);
        } catch (const std::exception& e) {
            result.addError("Failed to parse FeatureCollection: " + std::string(e.what()));
        }
    }
    else {
        // Validate as geometry
        try {
            auto geometry = parseGeometry(geojson);
            result = validateGeometry(geometry);
        } catch (const std::exception& e) {
            result.addError("Failed to parse Geometry: " + std::string(e.what()));
        }
    }

    // Validate optional bbox if present
    if (geojson.contains("bbox")) {
        if (!validateBoundingBox(geojson["bbox"], result)) {
            result.addWarning("Invalid bbox property");
        }
    }

    return result;
}

ValidationResult GeoJsonParser::validateGeometry(const std::shared_ptr<Geometry>& geometry) {
    ValidationResult result;
    
    if (!geometry) {
        result.addError("Null geometry");
        return result;
    }

    switch (geometry->type) {
        case GeometryType::Point: {
            auto point = std::static_pointer_cast<PointGeometry>(geometry);
            validatePosition(point->point, result);
            break;
        }
        case GeometryType::MultiPoint: {
            auto multiPoint = std::static_pointer_cast<MultiPointGeometry>(geometry);
            for (const auto& point : multiPoint->points) {
                validatePosition(point, result);
            }
            break;
        }
        case GeometryType::LineString: {
            auto lineString = std::static_pointer_cast<LineStringGeometry>(geometry);
            if (lineString->points.size() < 2) {
                result.addError("LineString must have at least 2 points");
            }
            for (const auto& point : lineString->points) {
                validatePosition(point, result);
            }
            break;
        }
        case GeometryType::MultiLineString: {
            auto multiLineString = std::static_pointer_cast<MultiLineStringGeometry>(geometry);
            for (const auto& line : multiLineString->lineStrings) {
                if (line.size() < 2) {
                    result.addError("Each LineString in MultiLineString must have at least 2 points");
                }
                for (const auto& point : line) {
                    validatePosition(point, result);
                }
            }
            break;
        }
        case GeometryType::Polygon: {
            auto polygon = std::static_pointer_cast<PolygonGeometry>(geometry);
            validatePolygonRings(polygon->rings, result);
            break;
        }
        case GeometryType::MultiPolygon: {
            auto multiPolygon = std::static_pointer_cast<MultiPolygonGeometry>(geometry);
            for (const auto& polygon : multiPolygon->polygons) {
                validatePolygonRings(polygon, result);
            }
            break;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            if (collection->geometries.empty()) {
                result.addWarning("GeometryCollection is empty");
            }
            for (const auto& geom : collection->geometries) {
                auto subResult = validateGeometry(geom);
                if (!subResult.isValid) {
                    result.isValid = false;
                    result.error = "Invalid geometry in collection: " + subResult.error;
                }
                result.warnings.insert(result.warnings.end(), 
                                     subResult.warnings.begin(), 
                                     subResult.warnings.end());
            }
            break;
        }
        case GeometryType::Unknown:
            result.addError("Unknown geometry type");
            break;
    }

    return result;
}

ValidationResult GeoJsonParser::validateFeature(const Feature& feature) {
    ValidationResult result;

    // Validate geometry
    if (!feature.geometry) {
        result.addWarning("Feature has null geometry");
    } else {
        auto geometryResult = validateGeometry(feature.geometry);
        if (!geometryResult.isValid) {
            result.isValid = false;
            result.error = "Invalid geometry: " + geometryResult.error;
        }
        result.warnings.insert(result.warnings.end(), 
                             geometryResult.warnings.begin(), 
                             geometryResult.warnings.end());
    }

    // Validate properties (must be an object or null, already enforced by json type)
    if (!feature.properties.is_object() && !feature.properties.is_null()) {
        result.addError("Feature properties must be an object or null");
    }

    // Validate id (if present)
    if (!feature.id.empty()) {
        // ID validation could be extended based on specific requirements
        if (feature.id.find_first_not_of("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_") != std::string::npos) {
            result.addWarning("Feature ID contains non-alphanumeric characters");
        }
    }

    return result;
}

ValidationResult GeoJsonParser::validateFeatureCollection(const std::vector<Feature>& features) {
    ValidationResult result;

    if (features.empty()) {
        result.addWarning("FeatureCollection is empty");
    }

    // Validate each feature
    for (size_t i = 0; i < features.size(); ++i) {
        auto featureResult = validateFeature(features[i]);
        if (!featureResult.isValid) {
            result.isValid = false;
            result.error = "Invalid feature at index " + std::to_string(i) + ": " + featureResult.error;
            break;
        }
        // Add warnings with feature index
        for (const auto& warning : featureResult.warnings) {
            result.addWarning("Feature " + std::to_string(i) + ": " + warning);
        }
    }

    return result;
}

bool GeoJsonParser::validateCoordinates(double lon, double lat, double alt, 
                                      ValidationResult& result) {
    // Validate longitude (-180 to 180)
    if (lon < -180.0 || lon > 180.0) {
        result.addError("Longitude must be between -180 and 180 degrees");
        return false;
    }

    // Validate latitude (-90 to 90)
    if (lat < -90.0 || lat > 90.0) {
        result.addError("Latitude must be between -90 and 90 degrees");
        return false;
    }

    // Altitude can be any finite number
    if (!std::isfinite(alt)) {
        result.addError("Altitude must be a finite number");
        return false;
    }

    return true;
}

bool GeoJsonParser::validatePosition(const GeoPoint& point, ValidationResult& result) {
    return validateCoordinates(point.longitude, point.latitude, point.altitude, result);
}

bool GeoJsonParser::validateLinearRing(const std::vector<GeoPoint>& ring, ValidationResult& result) {
    // A LinearRing must have at least 4 points
    if (ring.size() < 4) {
        result.addError("LinearRing must have at least 4 points");
        return false;
    }

    // First and last points must be the same
    const GeoPoint& first = ring.front();
    const GeoPoint& last = ring.back();
    if (first.longitude != last.longitude || 
        first.latitude != last.latitude || 
        first.altitude != last.altitude) {
        result.addError("LinearRing's first and last points must be identical");
        return false;
    }

    // Validate each point in the ring
    for (const auto& point : ring) {
        if (!validatePosition(point, result)) {
            return false;
        }
    }

    return true;
}

bool GeoJsonParser::validatePolygonRings(const std::vector<std::vector<GeoPoint>>& rings, ValidationResult& result) {
    if (rings.empty()) {
        result.addError("Polygon must have at least one ring (exterior)");
        return false;
    }

    // Validate exterior ring
    if (!validateLinearRing(rings[0], result)) {
        return false;
    }

    // Validate interior rings (holes)
    for (size_t i = 1; i < rings.size(); ++i) {
        if (!validateLinearRing(rings[i], result)) {
            return false;
        }
    }

    // Optional: Add warning if rings might be in wrong winding order
    // Exterior ring should be counterclockwise, interior rings clockwise
    if (!calculateRingOrientation(rings[0])) {
        result.addWarning("Exterior ring should be counterclockwise");
    }
    for (size_t i = 1; i < rings.size(); ++i) {
        if (calculateRingOrientation(rings[i])) {
            result.addWarning("Interior ring " + std::to_string(i) + " should be clockwise");
        }
    }

    return true;
}

bool GeoJsonParser::validateBoundingBox(const json& bbox, ValidationResult& result) {
    if (!bbox.is_array()) {
        result.addError("BoundingBox must be an array");
        return false;
    }

    // BoundingBox must have 4 or 6 elements
    if (bbox.size() != 4 && bbox.size() != 6) {
        result.addError("BoundingBox must have exactly 4 or 6 elements");
        return false;
    }

    // All elements must be numbers
    for (const auto& value : bbox) {
        if (!value.is_number()) {
            result.addError("BoundingBox elements must be numbers");
            return false;
        }
    }

    // For 2D bbox: [west, south, east, north]
    if (bbox.size() == 4) {
        double west = bbox[0];
        double south = bbox[1];
        double east = bbox[2];
        double north = bbox[3];

        if (west > east) {
            result.addWarning("West longitude is greater than east longitude (possibly crosses antimeridian)");
        }
        if (south > north) {
            result.addError("South latitude is greater than north latitude");
            return false;
        }
        if (!validateCoordinates(west, south, 0, result) || 
            !validateCoordinates(east, north, 0, result)) {
            return false;
        }
    }
    // For 3D bbox: [west, south, min, east, north, max]
    else {
        double minAlt = bbox[2];
        double maxAlt = bbox[5];
        if (minAlt > maxAlt) {
            result.addError("Minimum altitude is greater than maximum altitude");
            return false;
        }
    }

    return true;
}

bool GeoJsonParser::validateGeometryType(const std::string& type, ValidationResult& result) {
    const std::vector<std::string> validGeometryTypes = {
        "Point",
        "MultiPoint",
        "LineString",
        "MultiLineString",
        "Polygon",
        "MultiPolygon",
        "GeometryCollection"
    };

    if (std::find(validGeometryTypes.begin(), validGeometryTypes.end(), type) 
        == validGeometryTypes.end()) {
        result.addError("Invalid geometry type: " + type);
        return false;
    }

    return true;
}

std::vector<double> GeoJsonParser::BoundingBox::toVector() const {
    if (has3D) {
        return {west, south, minAlt, east, north, maxAlt};
    }
    return {west, south, east, north};
}

GeoJsonParser::BoundingBox GeoJsonParser::BoundingBox::fromVector(
    const std::vector<double>& bbox) {
    BoundingBox box;
    if (bbox.size() == 4) {
        box.west = bbox[0];
        box.south = bbox[1];
        box.east = bbox[2];
        box.north = bbox[3];
        box.has3D = false;
    } else if (bbox.size() == 6) {
        box.west = bbox[0];
        box.south = bbox[1];
        box.minAlt = bbox[2];
        box.east = bbox[3];
        box.north = bbox[4];
        box.maxAlt = bbox[5];
        box.has3D = true;
    }
    return box;
}

bool GeoJsonParser::BoundingBox::isValid() const {
    // 检查纬度范围
    if (south < -90.0 || south > 90.0 || north < -90.0 || north > 90.0) {
        return false;
    }
    
    // 检查经度范围
    if (west < -180.0 || west > 180.0 || east < -180.0 || east > 180.0) {
        return false;
    }
    
    // 检查南北关系
    if (south > north) {
        return false;
    }
    
    // 检查高度关系（如果有高度信息）
    if (has3D && minAlt > maxAlt) {
        return false;
    }
    
    return true;
}

bool GeoJsonParser::BoundingBox::crossesAntimeridian() const {
    return west > east;
}

void GeoJsonParser::updateBoundingBox(BoundingBox& box, const GeoPoint& point) {
    // 更新经度范围
    if (point.longitude < box.west) box.west = point.longitude;
    if (point.longitude > box.east) box.east = point.longitude;
    
    // 更新纬度范围
    if (point.latitude < box.south) box.south = point.latitude;
    if (point.latitude > box.north) box.north = point.latitude;
    
    // 更新高度范围（如果高度不为0）
    if (point.altitude != 0.0) {
        box.has3D = true;
        if (point.altitude < box.minAlt) box.minAlt = point.altitude;
        if (point.altitude > box.maxAlt) box.maxAlt = point.altitude;
    }
}

GeoJsonParser::BoundingBox GeoJsonParser::calculateBoundingBox(
    const std::shared_ptr<Geometry>& geometry) {
    BoundingBox box;
    
    if (!geometry) return box;

    switch (geometry->type) {
        case GeometryType::Point: {
            auto point = std::static_pointer_cast<PointGeometry>(geometry);
            updateBoundingBox(box, point->point);
            break;
        }
        case GeometryType::MultiPoint: {
            auto multiPoint = std::static_pointer_cast<MultiPointGeometry>(geometry);
            for (const auto& point : multiPoint->points) {
                updateBoundingBox(box, point);
            }
            break;
        }
        case GeometryType::LineString: {
            auto lineString = std::static_pointer_cast<LineStringGeometry>(geometry);
            for (const auto& point : lineString->points) {
                updateBoundingBox(box, point);
            }
            break;
        }
        case GeometryType::MultiLineString: {
            auto multiLineString = std::static_pointer_cast<MultiLineStringGeometry>(geometry);
            for (const auto& line : multiLineString->lineStrings) {
                for (const auto& point : line) {
                    updateBoundingBox(box, point);
                }
            }
            break;
        }
        case GeometryType::Polygon: {
            auto polygon = std::static_pointer_cast<PolygonGeometry>(geometry);
            for (const auto& ring : polygon->rings) {
                for (const auto& point : ring) {
                    updateBoundingBox(box, point);
                }
            }
            break;
        }
        case GeometryType::MultiPolygon: {
            auto multiPolygon = std::static_pointer_cast<MultiPolygonGeometry>(geometry);
            for (const auto& polygon : multiPolygon->polygons) {
                for (const auto& ring : polygon) {
                    for (const auto& point : ring) {
                        updateBoundingBox(box, point);
                    }
                }
            }
            break;
        }
        case GeometryType::GeometryCollection: {
            auto collection = std::static_pointer_cast<GeometryCollectionGeometry>(geometry);
            for (const auto& geom : collection->geometries) {
                auto geomBox = calculateBoundingBox(geom);
                box = mergeBoundingBoxes(box, geomBox);
            }
            break;
        }
        default:
            // Unknown 类型不需要特殊处理
            break;
    }
    
    return box;
}

GeoJsonParser::BoundingBox GeoJsonParser::mergeBoundingBoxes(
    const BoundingBox& box1, const BoundingBox& box2) {
    BoundingBox result;
    
    // 如果一个box是空的，直接返回另一个
    if (box1.east < box1.west) return box2;
    if (box2.east < box2.west) return box1;
    
    // 合并经纬度范围
    result.west = std::min(box1.west, box2.west);
    result.south = std::min(box1.south, box2.south);
    result.east = std::max(box1.east, box2.east);
    result.north = std::max(box1.north, box2.north);
    
    // 合并高度范围
    if (box1.has3D || box2.has3D) {
        result.has3D = true;
        result.minAlt = std::min(box1.minAlt, box2.minAlt);
        result.maxAlt = std::max(box1.maxAlt, box2.maxAlt);
    }
    
    return result;
} 

bool GeoJsonParser::calculateRingOrientation(const std::vector<GeoPoint>& ring) {
    // 使用 shoelace formula 计算面积符号来判断方向
    double area = 0.0;
    for (size_t i = 0; i < ring.size() - 1; ++i) {
        area += (ring[i+1].longitude - ring[i].longitude) * 
                (ring[i+1].latitude + ring[i].latitude);
    }
    // 逆时针方向时面积为正
    return area > 0;
}

std::string GeoJsonParser::geometryTypeToString(GeometryType type) {
    switch (type) {
        case GeometryType::Point:
            return "Point";
        case GeometryType::MultiPoint:
            return "MultiPoint";
        case GeometryType::LineString:
            return "LineString";
        case GeometryType::MultiLineString:
            return "MultiLineString";
        case GeometryType::Polygon:
            return "Polygon";
        case GeometryType::MultiPolygon:
            return "MultiPolygon";
        case GeometryType::GeometryCollection:
            return "GeometryCollection";
        case GeometryType::Unknown:
        default:
            return "Unknown";
    }
}

bool GeoJsonParser::validateType(const json& obj, ValidationResult& result) {
    // Check if type property exists and is a string
    if (!obj.contains("type") || !obj["type"].is_string()) {
        result.addError("Missing or invalid 'type' property");
        return false;
    }

    // Get the type string
    std::string type = obj["type"].get<std::string>();

    // Valid GeoJSON types
    const std::vector<std::string> validTypes = {
        "Point",
        "MultiPoint",
        "LineString",
        "MultiLineString",
        "Polygon",
        "MultiPolygon",
        "GeometryCollection",
        "Feature",
        "FeatureCollection"
    };

    // Check if the type is valid
    if (std::find(validTypes.begin(), validTypes.end(), type) == validTypes.end()) {
        result.addError("Invalid GeoJSON type: " + type);
        return false;
    }

    return true;
}

