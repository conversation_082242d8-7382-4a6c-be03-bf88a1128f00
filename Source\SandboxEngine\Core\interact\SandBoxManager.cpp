#include <string>
#include <vector>
#include <algorithm>
#include <cmath>
#include <ctime>
#include <cstdarg>
#include "SandBoxManager.h"

#include "OgreScriptLuaVM.h"
#include "world.h"
#include "GameNetManager.h"
#include "MpActorManager.h"
#include "SandboxCoreDriver.h"
#include "WorldManager.h"
#include "IClientActor.h"

#include "GameStatic.h"
#include "Optick/optick.h"

using namespace std;
using namespace MINIW;
using namespace MNSandbox;

void IEventSandboxCallbackExcute::OnExecute(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen)
{
	if (m_pCallBackFun)
	{
		m_pCallBackFun(wEventID, pszContext);
	}
}

void IEventLuaMsgCallbackExcute::OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata)
{
	jsonxx::Object *obj = (jsonxx::Object *)context;
	if (obj)
	{
		SandboxCoreLuaDirector& luaDirector = GetCoreLuaDirector();
		luaDirector.CallFunctionM("SandboxLuaMsg", "Execute", "ws", userdata, obj->json().c_str());
	}
}

SandBoxManager::SandBoxManager()
{
	m_callback = NULL;
	m_pIEventSandboxCallbackExcute = NULL;
	m_pIMsgSandboxCallbackExcute = ENG_NEW(IEventLuaMsgCallbackExcute)();
	registerAllActionFuncs();
}

SandBoxManager::~SandBoxManager()
{
	if (m_pIMsgSandboxCallbackExcute)
	{
		ENG_DELETE(m_pIMsgSandboxCallbackExcute);
	}
	if (m_pIEventSandboxCallbackExcute)
	{
		ENG_DELETE(m_pIEventSandboxCallbackExcute);
	}
}

void SandBoxManager::registerAllActionFuncs()
{

}

void SandBoxManager::setSandBoxManagerCallbackfun(pSandboxCallbackfun callback)
{
	m_callback = callback;
	if (m_pIEventSandboxCallbackExcute)
	{
		ENG_DELETE(m_pIEventSandboxCallbackExcute);
	}
	m_pIEventSandboxCallbackExcute = ENG_NEW(IEventSandboxCallbackExcute)(callback);
}

void SandBoxManager::addAction(unsigned int action, sandBoxActionFunc func)
{
	m_actionsMap.insert(map<unsigned int, sandBoxActionFunc>::value_type (action, func));
}

void SandBoxManager::subscibeEvent(int eventid)
{
	Rainbow::Mutex::AutoLock lockfunc(m_ActionMutex);
	s_subscibe scibe(eventid, 1);
	m_EventToSubscibe.push_back(scibe);
}

void SandBoxManager::unSubscibeEvent(int eventid)
{
	Rainbow::Mutex::AutoLock lockfunc(m_ActionMutex);
	s_subscibe scibe(eventid, 0);
	m_EventToSubscibe.push_back(scibe);
}

void SandBoxManager::doActionRet(int action, const char* msg_json)
{
	if (m_callback)
	{
		m_callback(action, msg_json);
	}
}

void SandBoxManager::doAction(int action, const char* msg_json)
{
	Rainbow::Mutex::AutoLock lockfunc(m_ActionMutex);
	if (!msg_json)
		msg_json = "";
	m_ActionsTodo.push_back(s_action(action, msg_json));
}

/*各种action*/
const char* SandBoxManager::doAction_(int action, const char* msg_json)
{
	jsonxx::Array obj;
	static string result;		// 因为要返回结果， 这里用static了
	result = "";
	std::string name;
	if (obj.parse(msg_json))
	{
		doAction(action, &obj, result);
	}
	else
	{
		doAction(action, NULL, result);
	}
	// 直接把结果返回到lua
	return result.c_str();
}

void SandBoxManager::doAction(int action, jsonxx::Array *obj, string &result)
{
	auto iter = this->m_actionsMap.find(action);
	if(iter != this->m_actionsMap.end())
	{
		sandBoxActionFunc func = iter->second;
		(this->*func)(action, obj, result);
	}
}

bool SandBoxManager::subscibe_(IEventExcute* pSink, unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, const char* pszDesc)
{
	return m_eventMap.Subscibe(pSink, wEventID, bSrcType, dwSrcID, pszDesc);
}

bool SandBoxManager::unSubscibe_(IEventExcute* pSink, unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID)
{
	return m_eventMap.UnSubscibe(pSink, wEventID, bSrcType, dwSrcID);
}

void SandBoxManager::tick()
{
	OPTICK_EVENT("SandBoxManager::tick")
	int acts = 0;
	int action = -1;
	string msg;
	{
		OPTICK_EVENT("DoAction");
		OPTICK_TAG("acts", m_ActionsTodo.size());
		while (m_ActionsTodo.size())
		{
				acts++;
			{
				Rainbow::Mutex::AutoLock lockfunc(m_ActionMutex);
				auto iter = m_ActionsTodo.begin();
				if (iter != m_ActionsTodo.end())
				{
					action = iter->m_nAction;
					msg = iter->m_sMsg_json;
				}
				m_ActionsTodo.erase(iter);
			}
			if (action != -1)
			{
				doAction_(action, msg.c_str());
				action = -1;
			}
		}
	}

	int eventid = -1;
	int subcrib = 0;
	{
		OPTICK_EVENT("DoSubscibe");
		OPTICK_TAG("subscibes", m_EventToSubscibe.size());
		while (m_EventToSubscibe.size())
		{
			{
				Rainbow::Mutex::AutoLock lockfunc(m_ActionMutex);
				auto iter = m_EventToSubscibe.begin();
				if (iter != m_EventToSubscibe.end())
				{
					eventid = iter->m_nEventid;
					subcrib = iter->m_nState;
				}
				m_EventToSubscibe.erase(iter);
			}
			if (eventid != -1)
			{
				if (m_pIEventSandboxCallbackExcute)
				{
					if (subcrib)
						subscibe_(m_pIEventSandboxCallbackExcute, eventid, 0, 0, NULL);
					else
						unSubscibe_(m_pIEventSandboxCallbackExcute, eventid, 0, 0);
				}
				eventid = -1;
			}
		}
	}
	CheckTimer();
}

bool SandBoxManager::DoEvent(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen)
{
	return m_eventMap.DoEvent(wEventID, bSrcType, dwSrcID, pszContext, nLen);
}

void SandBoxManager::update(float deltaTime)
{ 

}

bool SandBoxManager::SetTimer(unsigned long timerID, unsigned long interval, ITimerHandler * handler, unsigned long callTimes, const char * debugInfo)
{
	return m_TimerAxis.SetTimer(timerID, interval, handler, INFINITY_CALL, debugInfo);
}

bool SandBoxManager::KillTimer( unsigned long timerID,ITimerHandler * handler )
{ 
	return m_TimerAxis.KillTimer(timerID, handler);
}

void SandBoxManager::CheckTimer()
{ 
	m_TimerAxis.CheckTimer();
}

int SandBoxManager::getTickTime()
{
	return m_TimerAxis.getTickTime();
}

bool SandBoxManager::hasTimer(unsigned long timerID,ITimerHandler * handler)
{
	return m_TimerAxis.hasTimer(timerID, handler);
}

bool SandBoxManager::doEventEx(const char* eventname, void* context, int nLen)
{
	return m_eventMapEx.DoEvent(eventname, context, nLen);
}

bool SandBoxManager::doEventExBin(const char* eventname, int uin, void* context, int nLen)
{
	return m_eventMapEx.DoEventBin(eventname, uin, context, nLen);
}

bool SandBoxManager::subscibeEx(IEventExcuteEx* pSink, const char* eventname, functionCallback fCallback, const char* pszDesc, unsigned long long userdata)
{
	return m_eventMapEx.Subscibe(pSink, eventname, fCallback, nullptr, pszDesc, userdata);
}

bool SandBoxManager::subscibeBin(IEventExcuteBin* pSink, const char* eventname, functionCallbackBin fCallback, const char* pszDesc, unsigned long long userdata)
{
	return m_eventMapEx.Subscibe(pSink, eventname, nullptr, fCallback, pszDesc, userdata);
}

bool SandBoxManager::subscibePureBin(IEventExcuteBin* pSink, const char* eventname, functionCallbackBin fCallback, const char* pszDesc, unsigned long long userdata)
{
	return m_eventMapEx.Subscibe(pSink, eventname, nullptr, fCallback, pszDesc, userdata, true);
}

bool SandBoxManager::subscibeNetEx(IEventExcuteEx* pSink, const char* eventname, functionCallback fCallback,char* pszDesc)
{
	return m_eventMapEx.Subscibe(pSink, eventname, fCallback, nullptr, pszDesc, 0);
}

bool SandBoxManager::unSubscibeEx(IEventExcuteExBase* pSink, const char* eventname)
{
	return m_eventMapEx.UnSubscibe(pSink, eventname);
}

bool SandBoxManager::sendToClient(int targetUin, const char* eventname, void* context, int nLen, PacketReliability reliability)
{
	PB_Custom_Msg  pbCustomMsg;
	pbCustomMsg.set_msgname(eventname);
	pbCustomMsg.set_content((const char*)context, nLen);
	pbCustomMsg.set_ziplen(nLen);
	pbCustomMsg.set_unziplen(0);
	GetGameNetManagerPtr()->sendToClient(targetUin, PB_CUSTOM_MSG, pbCustomMsg, 0, true, reliability);
	return true;
}

bool SandBoxManager::sendToClientMulti(int* uins, size_t cnt, const char* eventname, void* context, int nLen, PacketReliability reliability)
{
	PB_Custom_Msg  pbCustomMsg;
	pbCustomMsg.set_msgname(eventname);
	pbCustomMsg.set_content((const char*)context, nLen);
	pbCustomMsg.set_ziplen(nLen);
	pbCustomMsg.set_unziplen(0);
	GetGameNetManagerPtr()->sendToClientMulti(uins, (int)cnt, PB_CUSTOM_MSG, pbCustomMsg, 0, true, reliability);
	return true;
}

bool SandBoxManager::sendBroadCast(const char* eventname, void* context, int nLen)
{
	PB_Custom_Msg  pbCustomMsg;
	pbCustomMsg.set_msgname(eventname);
	pbCustomMsg.set_content((const char*)context, nLen);
	pbCustomMsg.set_ziplen(nLen);
	pbCustomMsg.set_unziplen(0);
	GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_MSG, pbCustomMsg);
	return true;
}

bool SandBoxManager::sendToTrackingPlayers(IClientActor* actor, const char* eventname, void* context, int nLen)
{
	if (actor)
	{
		PB_Custom_Msg  pbCustomMsg;
		pbCustomMsg.set_msgname(eventname);
		pbCustomMsg.set_content((const char*)context, nLen);
		pbCustomMsg.set_ziplen(nLen);
		pbCustomMsg.set_unziplen(0);
		actor->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_CUSTOM_MSG, pbCustomMsg, actor, true);
		return true;
	}
	else
	{
		return false;
	}
}

bool SandBoxManager::sendToClient(int targetUin, const char* eventname, char* content)
{
	jsonxx::Object object;
	if (object.parse(content))
	{
		unsigned char *input = NULL;
		int len = 0;
		object.saveBinary(input, len);
		sendToClient(targetUin, eventname, input, len);
		free(input);
		return true;
	}
	else
	{
		return false;
	}
}

bool SandBoxManager::sendBroadCast(const char* eventname, char* content)
{
	jsonxx::Object object;
	if (object.parse(content))
	{
		unsigned char *input = NULL;
		int len = 0;
		object.saveBinary(input, len);
		PB_Custom_Msg  pbCustomMsg;
		sendBroadCast(eventname, input, len);
		free(input);
		return true;
	}
	else
	{
		return false;
	}
}

bool SandBoxManager::sendToTrackingPlayers(long long objid, const char* eventname, char* content)
{
	IClientActor *actor = GetWorldManagerPtr()->findActorByWID(objid);
	jsonxx::Object object;
	if (actor && object.parse(content))
	{
		unsigned char *input = NULL;
		int len = 0;
		object.saveBinary(input, len);
		//PB_Custom_Msg  pbCustomMsg;
		//actor->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_CUSTOM_MSG, pbCustomMsg, actor, true);
		sendToTrackingPlayers(actor, eventname, input, len);
		free(input);
		return true;
	}
	else
	{
		return false;
	}
}

bool SandBoxManager::sendToHost(const char* eventname, void* context, int nLen, PacketReliability reliability)
{
	PB_Custom_Msg  pbCustomMsg;
	pbCustomMsg.set_msgname(eventname);
	pbCustomMsg.set_content((const char*)context, nLen);
	pbCustomMsg.set_ziplen(nLen);
	pbCustomMsg.set_unziplen(0);
	GetGameNetManagerPtr()->sendToHost(PB_CUSTOM_MSG, pbCustomMsg, 0, reliability);
	return true;
}

bool SandBoxManager::sendToHost(const char* eventname, char* content)
{
	if (!eventname)
		return false;
	jsonxx::Object object;
	if (object.parse(content))
	{
		unsigned char *input = NULL;
		int len = 0;
		object.saveBinary(input, len);
		PB_Custom_Msg  pbCustomMsg;
		pbCustomMsg.set_msgname(eventname);
		pbCustomMsg.set_content((const char*)input, len);
		pbCustomMsg.set_ziplen(len);
		pbCustomMsg.set_unziplen(0);
		sendToHost(eventname, input, len);
		free(input);
		return true;
	}
	else
	{
		return false;
	}
}

bool SandBoxManager::subscibeEx(const char* eventname, unsigned long long userdata)
{
	return m_eventMapEx.Subscibe(m_pIMsgSandboxCallbackExcute, eventname, nullptr, nullptr, nullptr, userdata);
}


MINIW::GameStatic<SandBoxManager>  s_SandBoxManager(MINIW::kInitManual,99);	
SandBoxManager& GetSandBoxManager()											
{													
	return *GetSandBoxManagerPtr();							
}													

SandBoxManager* GetSandBoxManagerPtr()									
{													
	return s_SandBoxManager.EnsureInitialized();				
}

bool SandBoxManager::unSubscibeEx(char* eventname)
{
	return unSubscibeEx(m_pIMsgSandboxCallbackExcute, eventname);
}


SandBoxManager& SandBoxManager::getSingleton()
{													
	return GetSandBoxManager();
}													

SandBoxManager* SandBoxManager::getSingletonPtr()
{													
	return GetSandBoxManagerPtr();
}


