$#include "base/SandboxMacros.h"
$#pragma warning(disable:4800)
$cfile "base/SandboxType.h"
$cfile "base/SandboxRef.h"
$cfile "base/SandboxObject.h"
$cfile "base/SandboxComponent.h"
$cfile "base/SandboxGameObject.h"
$cfile "base/SandboxCoreModule.h"
$cfile "event/SandboxParam.h"
$cfile "event/SandboxParamGroup.h"
$pfile "event/SandboxParamObject.pkg"
$cfile "event/SandboxCallback.h"
$cfile "event/SandboxContext.h"
$cfile "event/SandboxResult.h"
$cfile "event/SandboxEventDispatcherManager.h"
$cfile "event/SandboxSchedulerManager.h"
$cfile "event/SandboxEventQueue.h"
$cfile "event/SandboxEventQueueManager.h"
$cfile "script/SandboxLuaPluginManager.h"
$cfile "script/SandboxCoreLuaDirector.h"
$cfile "scene/SandboxSceneObject.h"
$cfile "factory/SandboxFactory.h"
$cfile "factory/SandboxCoreFactorys.h"
$cfile "factory/SandboxFactoryImplements.h"
$cfile "SandboxCoreDriver.h"
$cfile "SandboxCoreManagers.h"

$using namespace MNSandbox;
