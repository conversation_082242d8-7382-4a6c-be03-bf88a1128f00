#include "SandboxAssetRequestMgr.h"
#include "Network/HttpManager.h"
#include "SandboxTimer.h"
#include "CloudAsset/CloudAssetUser.h"
#include "SandboxGlobalNotify.h"
#include "Utilities/Word.h"
#include "Platforms/PlatformInterface.h"
#include "SandboxMiscDataMgr.h"
#include "File/FileManager.h"

#if MMKV_ENABLED
#include "MMKV.h"
#endif

#ifdef _WIN32
#include "Platforms/Win/WinUnicode.h"
#endif

namespace MNSandbox {
	SandboxMiscDataMgr::SandboxMiscDataMgr()
	{
#if MMKV_ENABLED
		m_DirtyCacheMap.clear();
		m_pMmkvInstance = NULL;

		if (MMKV::getRootDir().empty())
		{
			std::string temp = Rainbow::GetFileManager().GetWritePathRoot();
			temp += "mmkv";
#if PLATFORM_WIN
			//MMKVPath_t rootpath = string2MMKVPath_t(temp);
			std::wstring rootpath;
			ConvertUTF8ToWideString(temp, rootpath);
			MMKV::initializeMMKV(rootpath);
#else
			MMKV::initializeMMKV(temp);
#endif
		}
#endif // MMKV
	}

	SandboxMiscDataMgr::~SandboxMiscDataMgr()
	{
		m_DirtyCacheMap.clear();
	}

	bool SandboxMiscDataMgr::OpenData(std::string& path)
	{
#if MMKV_ENABLED
		if (m_pMmkvInstance && m_sRootPath == path)
			return true;
		CloseData();

#if PLATFORM_WIN
		MMKVPath_t rootpath;
		ConvertUTF8ToWideString(path, rootpath);
#else
		MMKVPath_t rootpath = string2MMKVPath_t(path);
#endif
		std::string mmapID = "sandbox_map_data";
#ifndef MMKV_ANDROID
		m_pMmkvInstance = MMKV::mmkvWithID(mmapID, MMKV_SINGLE_PROCESS, nullptr, &rootpath);
#else
		m_pMmkvInstance = MMKV::mmkvWithID(DEFAULT_MMAP_ID, mmkv::DEFAULT_MMAP_SIZE, MMKV_SINGLE_PROCESS, nullptr, &rootpath);
#endif
		
		if (m_pMmkvInstance)
		{
			m_sRootPath = path;
			return true;
		}
		else
		{
			return false;
		}
#endif // MMKV
		return false;
	}

	bool SandboxMiscDataMgr::CloseData()
	{
#if MMKV_ENABLED
		m_DirtyCacheMap.clear();
		if (m_pMmkvInstance)
		{
			m_pMmkvInstance->sync();
			m_pMmkvInstance->clearMemoryCache();
			m_pMmkvInstance->close();
			m_pMmkvInstance = NULL;
			return true;
		}
#endif // MMKV
		return false;
	}

	bool SandboxMiscDataMgr::WriteData(std::string& key, std::string& value)
	{
#if MMKV_ENABLED
		m_DirtyCacheMap[key] = value;
#endif // MMKV
		return true;
	}

	bool SandboxMiscDataMgr::ReadData(std::string& key, std::string& value)
	{
#if MMKV_ENABLED
		if (m_DirtyCacheMap.find(key) != m_DirtyCacheMap.end())
		{
			value = m_DirtyCacheMap[key];
			return true;
		}
		else if (m_pMmkvInstance)
		{
			mmkv::MMBuffer temp =  m_pMmkvInstance->getBytes(key);
			if (temp.length())
			{
				value.assign((const char*)temp.getPtr(), temp.length());
				return true;
			}
		}
#endif // MMKV
		return false;
	}

	void SandboxMiscDataMgr::SaveAllData(std::unordered_map<std::string, int>& dataToSave)
	{
#if MMKV_ENABLED
		std::vector<std::string> allKeys;
		if (m_pMmkvInstance)
		{
			allKeys = m_pMmkvInstance->allKeys();
			for (int i = 0; i < allKeys.size(); i++)
			{
				if (dataToSave.find(allKeys[i]) == dataToSave.end())
				{
					m_pMmkvInstance->removeValueForKey(allKeys[i]);
				}
			}

			auto iter = m_DirtyCacheMap.begin();
			while (iter != m_DirtyCacheMap.end())
			{
				if (dataToSave.find(iter->first) != dataToSave.end())
				{
					m_pMmkvInstance->set(iter->second, iter->first);
				}
				iter++;
			}
			m_DirtyCacheMap.clear();
			m_pMmkvInstance->sync();
			m_pMmkvInstance->clearMemoryCache();
		}
#endif // MMKV
	}

	void SandboxMiscDataMgr::SaveDirtyData()
	{
#if MMKV_ENABLED
		auto iter = m_DirtyCacheMap.begin();
		while (iter != m_DirtyCacheMap.end())
		{
			if (m_pMmkvInstance)
			{
				m_pMmkvInstance->set(iter->second, iter->first);
			}
			iter++;
		}
		m_DirtyCacheMap.clear();
#endif // MMKV
	}

	void SandboxMiscDataMgr::ClearDirtyData()
	{
#if MMKV_ENABLED
		m_DirtyCacheMap.clear();
#endif // MMKV
	}

	bool SandboxMiscDataMgr::HasData(std::string& key)
	{
#if MMKV_ENABLED
		if (m_pMmkvInstance && m_pMmkvInstance->containsKey(key))
		{
			return true;
		}
#endif // MMKV
		return false;
	}
}
