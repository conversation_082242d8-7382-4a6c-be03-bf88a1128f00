/**
* file : worldDef
* func : world相关定义数据等
* by : chen<PERSON><PERSON>
*/
#ifndef __WORLD_DEF_H__
#define __WORLD_DEF_H__

#include "OgrePrerequisites.h"
#include "world_types.h"
#include "proto_common.h"

class IClientPlayer;

#define CHUNK_FREE_DIRTY_TICK (120*1000)
#define CHUNK_WRITE_DELAY (120*1000)
//#define CHUNK_WRITE_DELAY (5*1000)
#define CHUNK_CTRL_DELAY (8*1000)
#define NEWBIEWORLDID	9999999
#define NEWBIEWORLD2ID	9999901
#define SELECTROLEWORLDID   9999900
#define TERRAINVIEWTEMPWORLDID   9999998

//--------------#define MAX_MAP    3--------如果有新的map要添加，请同步去修改MAX_MAP宏
#define MAPID_GROUND 0
#define MAPID_LIEYANSTAR 1  //地心地图改名叫烈焰星，MAPID_EARTHCORE替换成MAPID_LIEYANSTAR，modified by hansom
#define MAPID_MENGYANSTAR 2
//--------------#define MAX_MAP    3--------如果有新的map要添加，请同步去修改MAX_MAP宏

#define CACHE_CHUNK_DIM 17
namespace Rainbow
{
	class Model;
}

struct BlockActor;

//tolua_begin
enum WorldPlayingType
{
	NORMAL_WORLD = 0,
	HOME_GARDEN_WORLD = 1,
	HOME_GARDEN_CUSTOMIZE_WORLD = 6,
};

enum WorldCType
{
	CTYPE_ARCHIVE = 1,
	CTYPE_RECREATION = 2,
};
//tolua_end

//tolua_begin
typedef struct tagChunkKey
{
	unsigned short MapID;
	int x;
	int z;

	bool operator <(const tagChunkKey& other) const
	{
		if (MapID != other.MapID)
		{
			return (MapID < other.MapID);
		}
		else if (x != other.x)
		{
			return (x < other.x);
		}
		else
		{
			return (z < other.z);
		}
	}
}ChunkKey;

typedef struct tagGridUPAfter
{
	Chunk *pLastChunk;
	WCoord Min;
	WCoord Max;
}GridUPAfter;

typedef struct tagSetionSvrAfter
{
	int x;
	int z;
	unsigned short mapid;
	bool valid;
	unsigned int tick;
	WCoord Min;
	WCoord Max;
}SetionSvrAfter;

typedef struct tagGridChg
{
	unsigned short mapid;
	unsigned int tick;
	OWGRIDCHG stGridChg;
}GridChg;


enum BLOCK_MOVE_TYPE
{
	BLOCKMOVE_FALLING = 0,
};
 //tolua_end
//tolua_begin
enum GRAVITY_TYPE
{
	GRAVITY_LIVING = 0,
	GRAVITY_ITEM,

	MAX_GRAVITY_TYPE
};
//tolua_end

typedef bool (*RemoveBlockFunc)(World *pworld, const WCoord &grid); //返回是否要remove

//tolua_begin
enum PICK_METHOD
{
	PICK_METHOD_CLICK = 0, //鼠标点击
	PICK_METHOD_SOLID,     //箭
	PICK_METHOD_CLICKLIQUID, //点击流体
	PICK_METHOD_SOLID_ALL,     //箭返回所有生物
	PICK_METHOD_BLOCK,				//dir方向遇到的所有方块
	PICK_METHOD_RAYBLOCK, //扳手道具可以检测到射线方块
	PICK_METHOD_CLICK_INCLUDE_LIQUID,//检测包括流体只是检测是否射中流体，规则还是按PICK_METHOD_CLICK就碰到固体方块才算碰到方块
};
//tolua_end
//tolua_begin
namespace MNSandbox {
	class SceneTransObject;
}

struct IntersectResult
{
	bool intersect_block;
	bool intersect_actor;
	WCoord block;
	bool isIntersectLiquid;
	WCoord firstIntersectBlock;
	std::vector<WCoord> blockVector;
	DirectionType face;
	MNSandbox::SceneTransObject* transobj;
	IClientActor *actor;
	std::string actorpart;  // 碰撞actor的伤害盒位置
	std::vector<IClientActor *> actors;
	std::vector<float> collide_ts;
	float collide_t;
	Rainbow::Vector3f facepoint; //相交点在面内的位置[0,1]
	Rainbow::Vector3f vecThroughTerrain;	//穿透地形的位置
	WCoord bound_minpos;
	WCoord bound_maxpos;
	std::vector<WCoord> liquids; //射线穿透的液体
	WCoord collide_pos;
	WORLD_ID m_PickObjId;
	std::vector<BlockActor> blocks; //射线穿透的方块

	IntersectResult():intersect_block(true),intersect_actor(false), isIntersectLiquid(false),actor(NULL), m_PickObjId(0), transobj(NULL)
		, vecThroughTerrain(Rainbow::Vector3f(0.0)), face(DIR_NOT_INIT)
	{


	}

	~IntersectResult()
	{
		actor = NULL;
	}

};
//tolua_end

// 传送点信息
//tolua_begin
struct TransferInfo
{
	IClientPlayer *player;
	int targetmap;
	int destStarStationId;
	WCoord position;
};
//tolua_end
//tolua_begin
struct TeleportInfo
{
	IClientPlayer *player;
	int targetmap;
};
//tolua_end

//射线检测到的信息
struct BlockActor {
	IClientActor* obj;
	WCoord blockPos;
	float t0;//aabb入口距离
	float t1;//aabb出口距离
	float per;//受击点Y百分比
	DirectionType face;
	Rainbow::Vector3f point;	//交点
	int blockid;

	BlockActor()
	{
		obj = NULL;
		blockPos = WCoord(0, 0, 0);
		t0 = 0.0f;
		t1 = 0.0f;
		per = 0.0f;
		face = DIR_COUNT;
		point = Rainbow::Vector3f::zero;
		blockid = 0;
	}
};

EXPORT_SANDBOXENGINE extern int signum(float x);
EXPORT_SANDBOXENGINE extern float intbound(float s, float ds);

Rainbow::Model* genModel(std::vector<WCoord> vpos, std::vector<unsigned int> color);

#endif
