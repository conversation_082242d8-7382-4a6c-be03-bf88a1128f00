
#ifndef __WORLDRENDER_H__
#define __WORLDRENDER_H__
#include "LegacyOgreColourValue.h"
#include "SandboxEngine.h"
#include "CameraClipSetting.h"
#include "DynamicParaments.h"
#include "UICommon/RenderTextureHolder.h"
#include "Render/Postprocess/PostprocessGodRay.h"
#include "Render/Postprocess/PostprocessBloom.h"
#include "Render/Postprocess/PostprocessLUTs.h"
#include "Render/Postprocess/PostprocessDof.h"
#include "Render/Postprocess/PostprocessAntialiasing.h"
#include "Render/Postprocess/PostprocessChain.h"
#include "Render/Postprocess/PostprocessSSAO.h"

class WCoord;
class World;
class BlockMaterialMgr;
class ParticleManager;
class SkyPlane;
class BlockScene;
class RainSnowRenderable;
class BlockDecalMesh;

class CurveFace;
class CollideObject;
class CurveScreen;
class DuststormRenderable;
class TempestRenderable;
class WeatherRender;
class EffectParticle;
struct DynamicSectionMeshRendererData;
namespace Rainbow
{
	class Entity;
	class RenderLines;
	class SandstormEffect;
	class Camera;
	class TerrainDecalEffect;
	class SkyPlane;
	class DirectionalLight;
	class SkyLight;
	class ShadowFace;
	class Vector3f;
	class CustomRenderPipeline;
}
//tolua_begin
struct DayNightLight
{
	Rainbow::ColourValue ambient;
	Rainbow::ColourValue dirlight; //sun or moon
};
//tolua_end
//tolua_begin
enum PARTICLE_EFFECT
{
	PTFX_BLOCK_DESTROYED = 0,
	PTFX_BLOCK_DESTROYING,
	PTFX_BLOCK_FALLGROUND,
};
//tolua_end
//tolua_begin
enum CURVEFACEMTLTYPE // 曲线显示材质类型
{
	CURVEFACEMTL_RAILHINT = 0,		// "blocks/rail_hint.png"

	CURVEFACEMTL_RAILRED,			// "blocks/rail_red.png"
	CURVEFACEMTL_TEX12006,			// "itemmods/12006/texture1.png"
	CURVEFACEMTL_TEXRED,			// "itemmods/12007/texture_red.png"
	CURVEFACEMTL_TEXBLUE,			// "itemmods/12007/texture_blue.png"
	CURVEFACEMTL_TEXWHITE,			// "itemmods/12007/texture_white.png"

	CURVEFACEMTL_TEX12008,			// "itemmods/12008/texture1.png"
	CURVEFACEMTL_JIGUANG,			// "blocks/jiguang.png"
	CURVEFACEMTL_COPYRANGELINE,		// "blocks/copy_range_line.png"
	CURVEFACEMTL_CONSTRUCTIONLINE,	// "blocks/construction_line.png"
	CURVEFACEMTL_DISTANCELINE,		// "blocks/distance_line.png"

	CURVEFACEMTL_GREENLINE,			// "blocks/green_line.png"
	CURVEFACEMTL_DIRECTION,			// "blocks/direction.png"
	CURVEFACEMTL_1BLUE,				// "blocks/l_blue.png"
	CURVEFACEMTL_1YELLOW,			// "blocks/l_yellow.png"
	CURVEFACEMTL_1RED,				// "blocks/l_red.png"

	CURVEFACEMTL_ICOVECENTER,		// "blocks/ico_ve_center.png"
	CURVEFACEMTL_YELLOWGREEN,		// "blocks/l_yellowgreen.png"
	CURVEFACEMTL_LIGHTBLUE,			// "blocks/l_lightblue.png"
	CURVEFACEMTL_LIGHTNING2,		// "particles/texture/lightning2.png"
	CURVEFACEMTL_LIGHTNING1,		// "particles/texture/lightning1.png"

	CURVEFACEMTL_LIGHTNING3,		// "particles/texture/lightning3.png"
	CURVEFACEMTL_TRIGGERGUIDE,		// "blocks/l_white.png"
	CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, // "itemmods/12007/texture_white.png" 
	CURVEFACEMTL_TEXWHITE_EX,		// "itemmods/12007/texture_white.png" 
};
//tolua_end

enum FirstPersonCameraPipelineType 
{
	FirstPersonNormal,					//其他普通手持
	FirstPersonCustomGenMesh,	//微缩模型
	FirstPersonUGC,					    //UGC操作自定义的,不看见手的
};

class BlockPlacementPreview;

class EXPORT_SANDBOXENGINE WorldRenderer { //tolua_exports
public:
	//tolua_begin
	WorldRenderer(World *pworld, bool is_own=true);
	~WorldRenderer();

	void tick();
	void update(float dtime);

	void setWireBlockPos(const WCoord &blockpos);
	void clearWireBlock();

	void addShadow(const WCoord &center, int radius, float shadowlevel);
	void addTerrainDecalEffect(const Rainbow::Vector3f& position, int radius, const char* texture_path, const char* inner_texture, Rainbow::ColorRGBAf tintColor, float duration);
	CurveFace *getCurveRender() const
	{
		return m_CurveFaces;
	}

	CurveScreen* getCurveScreenRender() const
	{
		return m_CurveScreenFaces;
	}

	BlockScene *getScene() const
	{
		return m_Scene;
	}
	Rainbow::Camera* GetCamera() const { return m_Camera; }
	//Rainbow::Camera* GetFrontCamera() const { return m_FrontCamera; }

	Rainbow::SkyPlane* getSky();
	//void setEnableGodray(bool value);
	//bool getEnableGodray() { return m_EnableGodray; };

	//Rainbow::PlanarReflection* GetPlanarReflection() const { return m_PlanarReflection; }

	//Rainbow::RenderLines *getRenderLines(long container,const WCoord &pos);
	void removeRenderLines(long container);
	int addGlobalEffect(const char* path, int id);
	void removeGlobalEffect(int id);
	void setGlobalEffectPos(int id, int x, int y, int z);
	void setGlobalEffectScale(int id, float sx, float sy, float sz);
	void setRenderCamera(Rainbow::Camera* pcamera);

	void setCloudDensity(int d);
	Rainbow::ColourValue getCurSkyLight();
	Rainbow::ColourValue getCurTorchLight();
	Rainbow::ColourValue getCurAmbient();
	Rainbow::ColourValue getLighting(const WCoord &pos);
	void setSkyTex(const std::string& path);
	static int m_CurFogIndex;
	static bool m_CurShadowOpen;
	//tolua_end
	static void SetShadowEnable(bool value, bool showTips = false);
	void SetEngineCurrentScene();
	CameraClipSetting& GetCameraClipSetting();
	float GetActorClipValue();
	float GetParticleClipValue();
	float GetConfigParticleClipMax();
	UInt32 GetCullPolicy();
	void SetWaterCullMode(Rainbow::CullMode cullmode);
	void setPostporcessEnable(bool enable);
	bool getPostporcessEnable();
	void setIsUGCCustomPostEffect(bool enable);
	void setGodrayEnable(bool value);
	void setBloomEnable(bool value);
	void setLUTsEnable(bool value);
	void setDofEnable(bool value);
	void setAntialiasing(bool value);
	bool getGodrayEnable();
	bool getBloomEnable();
	bool getLUTsEnable();
	bool getDofEnable();

	void setCustomLUTEnable(bool value);
	bool getCustomLUTEnable() { return m_CustomLUTEnable; }

	void setAntialiasingMethod(Rainbow::AntialiasingMethod method);
	bool getAntialiasingMethod(Rainbow::AntialiasingMethod& method);
	void setAntialiasingQuality(Rainbow::AntialiasingQuality quality);
	bool getAntialiasingQuality(Rainbow::AntialiasingQuality& quality);

	void setGodrayBloomScale(float f);
	float getGodrayBloomScale();
	void setGodrayColor(Rainbow::ColorRGBAf color);
	Rainbow::ColorRGBAf getGodrayColor();
	void setBloomIntensity(float f);
	float getBloomIntensity();
	void setBloomThreadhold(float f);
	void setLUTsContrast(Rainbow::ColorRGBAf color);
	Rainbow::ColorRGBAf getLUTsContrast();
	void setLUTsSaturation(Rainbow::ColorRGBAf color);
	Rainbow::ColorRGBAf getLUTsSaturation();
	void setLUTsGain(Rainbow::ColorRGBAf color);
	Rainbow::ColorRGBAf getLUTsGain();
	void setLUTsGamma(Rainbow::ColorRGBAf color);
	Rainbow::ColorRGBAf getLUTsGamma();
	void setLUTsOffset(Rainbow::ColorRGBAf color);
	void setLUTsLUTTex(Rainbow::SharePtr<Rainbow::Texture2D> texture);
	Rainbow::SharePtr<Rainbow::Texture2D> getLUTsLUTTex();
	void SetDefaultLUTTexture(Rainbow::SharePtr<Rainbow::Texture2D> texture);
	void setLUTsTemperatureType(Rainbow::TemperatureType type);
	void setLUTsWhiteTemp(float f);
	void setLUTsWhiteTint(float f);
	void setLUTsColorCorrectionShadowsMax(float f);
	void setLUTsColorCorrectionHighlightsMin(float f);
	void setLUTsBlueCorrection(float f);
	void setLUTsExpandGamut(float f);
	void setLUTsToneCurveAmout(float f);

	void setLUTsShadowContrast(Rainbow::ColorRGBAf color);
	void setLUTsShadowSaturation(Rainbow::ColorRGBAf color);
	void setLUTsShadowGain(Rainbow::ColorRGBAf color);
	void setLUTsShadowGamma(Rainbow::ColorRGBAf color);
	void setLUTsShadowOffset(Rainbow::ColorRGBAf color);
	void setLUTsMidtoneContrast(Rainbow::ColorRGBAf color);
	void setLUTsMidtoneSaturation(Rainbow::ColorRGBAf color);
	void setLUTsMidtoneGain(Rainbow::ColorRGBAf color);
	void setLUTsMidtoneGamma(Rainbow::ColorRGBAf color);
	void setLUTsMidtoneOffset(Rainbow::ColorRGBAf color);
	void setLUTsHighlightContrast(Rainbow::ColorRGBAf color);
	void setLUTsHighlightSaturation(Rainbow::ColorRGBAf color);
	void setLUTsHighlightGain(Rainbow::ColorRGBAf color);
	void setLUTsHighlightGamma(Rainbow::ColorRGBAf color);
	void setLUTsHighlightOffset(Rainbow::ColorRGBAf color);

	void setFilmicTonemapSlope(float f);
	void setFilmicTonemapToe(float f);
	void setFilmicTonemapShoulder(float f);
	void setFilmicTonemapBlackClip(float f);
	void setFilmicTonemapWhiteClip(float f);
		 
	void setDofFocalRegion(float value);
	void setDofNearTransitionRegion(float value);
	void setDofFarTransitionRegion(float value);
	void setDofFocalDistance(float value);
	void setDofScale(float value);

	void setGTAOActive(bool value);
	void setGTAOThicknessblend(float value);
	void setGTAOFalloffStartRatio(float value);
	void setGTAOFalloffEnd(float value);
	void setGTAOFadeoutDistance(float value);
	void setGTAOFadeoutRadius(float value);
	void setGTAOIntensity(float value);
	void setGTAOPower(float value);

	void setChromaticAberrationIntencity(float value);
	void setChromaticAberrationStartOffset(float value);
	void setChromaticAberrationIterationSamples(float value);
	void setChromaticAberrationIterationStep(float value);

	bool getIsUGCCustomPostEffect();
	bool getIsUGCCustomSky();
	Rainbow::SandstormEffect* getSandstormEffect();
	Rainbow::DirectionalLight* GetLight()
	{
		return m_Light;
	}
	void SetStudioCtrlLightDirSwitch(bool change)
	{
		m_StudioCtrlLightDirSwitch = change;
	}

	void UpdateHightlightBlockVertexAnimation();

	void SetDynamicRendererData(const WCoord& blockWorldPos, bool enable);
	void RemoveDynamicRenderData(const WCoord& blockWorldPos);

	std::map<WCoord, DynamicSectionMeshRendererData*>& GetDynamicRenderData();

	
	void SetUseCustomRenderPipeline(bool value);

	void ChangeCustomRenderPipelineType(FirstPersonCameraPipelineType type);

	void SetWeaponItemType(int type);

	// Block placement preview
	void InitBlockPlacementPreview();
	BlockPlacementPreview* GetBlockPlacementPreview() { return m_BlockPlacementPreview; }

private:
	void OnUpdateWeaponItem();
	void doBlockRandomEffects(const WCoord &center);
	void tickEnv();
	void updateFogColor(int eyeblock);
	void updateFogRange();
	void updateShadowEnable();
	void updateWaterFog(int depth);
	void updateEnvShaderParam();
	Rainbow::SharePtr<Rainbow::PostprocessChain> createPostprocessChain();
	Rainbow::CustomRenderPipeline* createFirstPersonRenderPipeline();
	Rainbow::CustomRenderPipeline* createCustomModelFirstPersonRenderPipeline();
	Rainbow::CustomRenderPipeline* createCustomRenderPipeline();

	bool isEnablePostprocess();

	void ClearDynamicRendererData();
private:
	World *m_World;
	//主场景
	BlockScene *m_Scene;
	Rainbow::SharePtr<Rainbow::PostprocessChain> m_PostprocessChain;
	Rainbow::PPtr<Rainbow::PostprocessGodray> m_PostprocessGodray;
	Rainbow::PPtr<Rainbow::PostprocessBloom> m_PostprocessBloom;
	Rainbow::PPtr<Rainbow::PostprocessLUTs> m_PostprocessLUTs;
	Rainbow::PPtr<Rainbow::PostprocessDof> m_PostprocessDof;
	Rainbow::PPtr<Rainbow::PostprocessAntialiasing> m_PostprcessAntialiasing;
	Rainbow::PPtr<Rainbow::PostprocessSSAO> m_PostprocessSSAO;
	
	BlockDecalMesh *m_BlockLine;
	Rainbow::ShadowFace *m_ShadowFaces;
	CurveFace *m_CurveFaces;
	CurveScreen* m_CurveScreenFaces;
	bool m_EnableGodray;
	bool m_IsUGCCustomPostEffect;
	bool m_CustomLUTEnable;

	std::map<long, Rainbow::RenderLines*> m_RenderLineVec;
	Rainbow::SkyPlane *m_Sky;
	int m_TargetCloudDensity;
	int m_CurCloudDensity;
	RainSnowRenderable *m_RainSnow;
	Rainbow::SharePtr<Rainbow::RenderTextureHolder>  m_RenderTextureHolder;
	std::map<int, Rainbow::Entity *>m_GlobalEffects;
	int m_IDCounter;
	
	Rainbow::Camera* m_Camera;
	int m_WeaponItemType;/*ICON_GEN_MESH ...*/
	//Rainbow::Camera* m_FrontCamera;	// 处理老引擎在RL_SCENE_FRONT上的object
	Rainbow::CustomRenderPipeline* m_FirstPersonRenderPipeline;
	Rainbow::CustomRenderPipeline* m_CustomModelFirstPersonRenderPipeline;
	Rainbow::CustomRenderPipeline* m_CustomRenderPipeline;

	Rainbow::DirectionalLight* m_Light;
	Rainbow::SkyLight* m_Skylight;
	Rainbow::Vector3f m_InitLightRotation;
	//Rainbow::Vector4f m_ShadowBias;
	CameraClipSetting m_CameraClipSetting;
	dynamic_array<int> m_VecBlockIDOpenlightwave{ 100,101,102,103,104,105,106,107,108,247 };
	dynamic_array<int> m_VecBlockIDOpenRainRipple{ 100,101,102,103,104,105,106,107,108,247 };
	Rainbow::SandstormEffect* m_SandstormEffect;
	DuststormRenderable* m_Duststorm;
	bool m_StudioCtrlLightDirSwitch = false;
	TempestRenderable* m_Tempest;
	std::map<unsigned short, std::list<std::pair<WCoord,EffectParticle*>>> mInViewBlockParticle;
	Rainbow::TerrainDecalEffect* m_TerrainDecalEffect;
	WeatherRender* m_WeatherRender;
	int m_Tick = 0;
	std::map<WCoord, DynamicSectionMeshRendererData*> m_DynamicRendererData;
	Rainbow::EnvData m_EnvData;
public:
	int m_WaterFogNearRange;
	int m_WaterFogFarRange;
	Rainbow::ColourValue m_WaterColorVal;
	BlockPlacementPreview* m_BlockPlacementPreview;
}; //tolua_exports

inline Rainbow::SkyPlane* WorldRenderer::getSky() { return m_Sky; }

#endif
