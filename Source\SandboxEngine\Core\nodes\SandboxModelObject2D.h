#pragma once

#include "SandboxGameObject.h"
#include "SandboxNotify.h"
#include "SandboxFlags.h"
#include "SandboxList.h" 
#include "SandboxCoreFactorys.h"
#include "GameScene/MovableObject.h"//#include "OgreMovableObject.h"
#include "SandboxSceneObject.h"
#include "Geometry/Ray.h"
#include <string>
#include "Common/LegacyOgreColourValue.h"
#include "GeometryObject/GeometryObject.h"
#include "SandboxEngine.h"
#include "SandboxNodeEnums.h"
#include "EnginePrefix.h"
#include "RakNetTime.h"
#include "SandboxOctree.h"
#include "2d/CCNode.h"
#include "2d/CCSprite.h"
#include "SandboxModelObject.h"
#include "SandboxTransObject2D.h"

class World;
namespace MNSandbox {
	class SceneTransObject2D;

	class EXPORT_SANDBOXENGINE SceneModelObject2D : public SceneTransObject2D
	{
		DECLARE_SCENEOBJECTCLASS(SceneModelObject2D)
	protected:
		SceneModelObject2D();
	public:
		enum PropFlag2D
		{
			PropMesh2D,
			PropLocalSize2D,
		};
		virtual ~SceneModelObject2D();
		virtual void OnHandleInit() override;
		inline void SetMainTextureIdR(const SceneModelObject::ModelAssetType& strTexId) { SetMainTextureId(strTexId); }
		inline bool GetMainTextureIdR(SceneModelObject::ModelAssetType& strTexId) const { return GetMainTextureId(strTexId); }
		Rainbow::Vector2f GetSize() const;
		virtual void SetSize(const Rainbow::Vector2f& size);
		//virtual Rainbow::Vector2f GetCenter() const;
		void SetCenter(const Rainbow::Vector2f& center);
		void SetPropSetWaiting(PropFlag2D prop, bool set);
		bool GetPropSetWaiting(PropFlag2D prop) { return (m_nPropSetWaiting & (1 << (unsigned char)prop)) != 0; }
		virtual void OnLoadReflexEnd()override;

		virtual void SetColor(const Rainbow::ColorQuad& cq);
		const Rainbow::ColorQuad& GetColor() const { return m_cq; }
		virtual void SetMainTextureId(const std::string& strTexId);
		bool GetMainTextureId(std::string& strTexId) const;


		virtual void onPhysXTouch2D(SceneModelObject2D* obj, const Rainbow::Vector2f& pos, const Rainbow::Vector2f& noraml);
		virtual void onPhysXTouchEnd2D(SceneModelObject2D* obj, const Rainbow::Vector2f& pos, const Rainbow::Vector2f& noraml);

		virtual void createLocomotion();

		Notify<AutoRef<SandboxNode>, Rainbow::Vector2f, Rainbow::Vector2f>* GetTouched();
		Notify<AutoRef<SandboxNode>>* GetTouchEnded();

		virtual void OnEnterScene(Scene* scene) override;
		virtual void OnLeaveScene(Scene* scene) override;

		//����ͬ�����ù�����λ����Ϣ
		virtual void SetWorldPositionPhysx(const Rainbow::Vector2f& pos);
		virtual void SetWorldRotationPhysx(const float& angle);



		virtual void OnUpdateTransform() override;

		void UpdateVelocity()
		{
			OnAttributeChanged(this, &RCP_Velocity);
		}

		void UpdateAngleVelocity()
		{
			OnAttributeChanged(this, &RCP_AngleVelocity);
		}
		virtual bool IsPhysicsSyncAtrr2D(ReflexValue* descriptor);
	public:

		virtual void SetEnableGravity(const bool& b);
		virtual bool GetEnableGravity(bool& b ) const;

		virtual void SetAnchored(const bool& b);
		virtual bool GetAnchored( bool& b) const;

		virtual void SetIsStatic(const bool& b);
		virtual bool GetIsStatic( bool& b ) const;

		virtual void SetGravity(const float& f);
		virtual bool GetGravity( float& f ) const;

//		virtual void SetPhysx2DType( const MNSandbox::LocomotionComponent2D::PHYSX2D_TYPE& tp) ;
//		virtual bool GetPhysx2DType(MNSandbox::LocomotionComponent2D::PHYSX2D_TYPE& tp) const;

		
		virtual bool GetMass( float& f ) const;

		virtual void SetDensity(const float& f);
		virtual bool GetDensity(float& f) const;


		virtual void SetRestitution(const float& f);
		virtual bool GetRestitution( float& f ) const;

		virtual void SetFriction(const float& f);
		virtual bool GetFriction( float&f ) const;

		virtual void SetVelocity(const Rainbow::Vector2f& v);
		virtual bool  GetVelocity(Rainbow::Vector2f& v) const;

		virtual void SetAngleVelocity(const float& f);
		virtual bool GetAngleVelocity( float& f ) const;

		virtual void SetCanCollide(const bool& b);
		virtual bool GetCanCollide( bool& b ) const;

		virtual void SetCanTouch(const bool& b);
		virtual bool GetCanTouch( bool& b ) const;

		virtual void SetGroupID(const unsigned int& id);
		virtual bool GetGroupID(unsigned int& id) const;

		virtual void SetEnable(const bool& b);
		virtual bool GetEnable(bool& b) const;

		virtual void SetEnableCCD(const  bool& b);
		virtual bool GetEnableCCD( bool& b ) const;


		virtual void SetEnableDrawCollider(const bool& b);
		virtual bool GetEnableDrawCollider(bool& b) const;

		virtual void SetPhysxVerts(const std::vector<std::string>& verts);
		virtual bool GetPhysxVerts(std::vector<std::string>& verts) const;

		virtual void SetFlippedX(const  bool& b);
		virtual bool GetFlippedX() const;

		virtual void SetFlippedY(const  bool& b);
		virtual bool GetFlippedY() const;


		virtual void OnSetLocalRotation(const float& rot) override;
		virtual void OnSetLocalPosition(const Rainbow::Vector2f& pos) override;
		virtual void OnSetLocalScale(const Rainbow::Vector2f& scale) override;

		virtual void OnSetWorldPosition(const Rainbow::Vector2f& pos) override;
		virtual void OnSetWorldRotation(const float& rot) override;
		virtual void OnSetWorldScale(const Rainbow::Vector2f& scale) override;

		virtual void OnSetParent(SandboxNode* parent) override;


		

		bool IsEnterScene() const;

		//�ٶ�
		static ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> RCP_Velocity;
		//���ٶ�
		static ReflexClassParam<SceneModelObject2D, float> RCP_AngleVelocity;

		void SetSrcSize(const std::vector<int>& s);
		bool GetSrcSize(std::vector<int>& s) const;

	protected:
		static ReflexClassParam<SceneModelObject2D, SceneModelObject::ModelAssetType> RCP_MainTextureId;
		static ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> RCP_Size;
		static ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> RCP_Center;
		static ReflexClassParam<SceneModelObject2D, Rainbow::ColorQuad> RCP_Color;

		static ReflexClassNotify<SceneModelObject2D, AutoRef<SandboxNode>, Rainbow::Vector2f, Rainbow::Vector2f> Touched;
		static ReflexClassNotify<SceneModelObject2D, AutoRef<SandboxNode>> TouchEnded;

		//�Ƿ�������
		static ReflexClassParam<SceneModelObject2D, bool> RCP_EnableGravity;
		//�Ƿ��ê
		static ReflexClassParam<SceneModelObject2D, bool> RCP_Anchor;
		//����
		static ReflexClassParam<SceneModelObject2D, float> RCP_Gravity;
		//����
		static ReflexClassParam<SceneModelObject2D, float> RCP_Mass;
		//�ܶ�
		static ReflexClassParam<SceneModelObject2D, float> RCP_Density;
		//����
		static ReflexClassParam<SceneModelObject2D, float> RCP_Restitution;
		//Ħ����
		static ReflexClassParam<SceneModelObject2D, float> RCP_Friction;

		static ReflexClassParam<SceneModelObject2D, bool> RCP_CanCollide;
		static ReflexClassParam<SceneModelObject2D, bool> RCP_CanTouch;
		//bool m_bIsConvex;
		static ReflexClassParam<SceneModelObject2D, unsigned int> RCP_GroupID;
		static ReflexClassParam<SceneModelObject2D, bool> RCP_Enable;
		//�Ƿ���CCD��⣬���Է�ֹ��������ƶ�������·�����͸  ��Ӱ�����ܣ�����Ĭ�ϲ�����
		static ReflexClassParam<SceneModelObject2D, bool> RCP_EnableCCD;
		static ReflexClassParam<SceneModelObject2D, bool> RCP_EnableDrawCollider;

		//ͼƬԭʼ��С
		static ReflexClassParam<SceneModelObject2D,std::vector<int>> RCP_SrcSize;

//		static ReflexClassParam<SceneModelObject2D, MNSandbox::LocomotionComponent2D::PHYSX2D_TYPE> RCP_PhysXType;
//		static ReflexEnumDesc<MNSandbox::LocomotionComponent2D::PHYSX2D_TYPE> R_PhysicsEnumType;

		static ReflexClassParam<SceneModelObject2D, std::vector<std::string>> RCP_PhysxVerts;

		static ReflexClassParam<SceneModelObject2D, bool> RCP_FlippedX;
		static ReflexClassParam<SceneModelObject2D, bool> RCP_FlippedY;
		Rainbow::Vector2i m_srcSize;

		std::string m_strTexId;
		AssetObject *m_pAssetTexture;
		Rainbow::SharePtr<Rainbow::Texture2D> m_tex2d; // ���ص�����
		Rainbow::ColorQuad m_cq;
		unsigned char m_nPropSetWaiting : 6;
//		LocomotionComponent2D* m_pLocomotion;
		bool m_bEnterScene;
		bool m_bFlippedX;
		bool m_bFlippedY;
	};
}

