// @author: maxll @time: 2022年8月6日14:11:09
// @comment: lua二进制功能(主要用于发送消息包)

#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <cstring>

struct lua_State;

namespace MNSandbox{
    namespace Binary{

        void RegisterMetatable(lua_State*);
        void RegisterClassLibrary(lua_State*);
        
        class UInt8Vec
        {
        public:
            UInt8Vec(){}

            UInt8Vec(unsigned char* buf, std::size_t length)
            {
                buffer.resize(length);
                std::memcpy((void*)buffer.data(),(const void*)buf, length);
            }

            UInt8Vec(std::size_t length)
            {
                buffer.resize(length,'\0');
            }

            UInt8Vec(unsigned char* buf) : UInt8Vec(buf, std::strlen((const char*)buf))
            {}

            UInt8Vec(const std::string& str) : UInt8Vec((unsigned char*)str.data(), str.length())
            {}



            UInt8Vec(const std::vector<char>& buf) : UInt8Vec((unsigned char*)buf.data(), buf.size())
            {}


            UInt8Vec(const UInt8Vec& other)
                : buffer(other.buffer)
            {}

            UInt8Vec(UInt8Vec&& other) noexcept
                : buffer(std::move(other.buffer))
            {}

            UInt8Vec& operator=(const UInt8Vec& other) noexcept
            {
                buffer = other.buffer;
                return *this;
            }

            UInt8Vec& operator=(UInt8Vec&& other) noexcept
            {
                buffer = std::move(other.buffer);
                return *this;
            }

            bool operator==(const UInt8Vec& v) const
            {
                return buffer == v.buffer;
            }

            void convertToStr(std::string& ret, bool clear = false)
            {
                ret = std::string((const char*)buffer.data(),buffer.size());
                if(clear)
                {
                    buffer.clear();
                }
            }

            std::vector<std::uint8_t> buffer;
        };
    }
}
