#pragma once
//#include "OgreVertexIndexData.h"
#include "OgreShared.h"
#include "obj_parser.h"
namespace MINIW //tolua_exports
{ //tolua_exports

	class ObjMesh { //tolua_exports
	public:
		//tolua_begin
		ObjMesh() :/*m_pIndexData(NULL), m_pVertexData(NULL),*/m_MaxHeight(0) {} //tolua_exports
		~ObjMesh() {
			/*OGRE_RELEASE(m_pIndexData);
			OGRE_RELEASE(m_pVertexData);*/
		}
		void LoadObj(const obj_scene_data& scene_obj);

		//std::vector<SubObjMesh> m_subObjMeshs;

		/*IndexData *m_pIndexData;
		VertexData *m_pVertexData;*/
		float m_MaxHeight;
		//tolua_end
	}; //tolua_exports



	bool ParseObjModel(const char* path, ObjMesh& out);
} //tolua_exports