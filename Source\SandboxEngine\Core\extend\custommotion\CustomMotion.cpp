
#include <time.h>
#include "File/FileManager.h"
#include "CustomMotion_generated.h"
#include "CustomMotion.h"
#include "Mesh/LegacyOgreAnimTrackBone.h"
#include "Common/OgreShared.h"
#include "OgreUtils.h"
#include "WorldManager.h"
#include "ClientInfoProxy.h"

#include "ClientActorHelper.h"
#include "CustomCommonHead.h"
#include "OgreModelData.h"

using namespace MINIW;
using namespace Rainbow;

CustomMotion::CustomMotion() : m_sName(""), m_sAuthName(""), m_iAuthUin(-1), m_iMotionId(0), m_iDuration(0), m_sKey("")
{
	m_MotionDatas.clear();
}

CustomMotion::~CustomMotion()
{

}

bool CustomMotion::load(std::string path, int checkuin)
{
	AutoRefPtr<DataStream> pstream = GetFileManager().OpenFile(path.c_str());
	if (!pstream) {
		LOG_WARNING("load(%s): pstream NULL", path.c_str());
		return false;
	}

	int buflen = pstream->Size();
	void *buf = malloc(buflen);
	pstream->Read(buf, buflen);

	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	if (!FBSave::VerifyCustomMotionBuffer(verifier))
	{
		free(buf);
		LOG_WARNING("load(): FBSave verify false");
		return false;
	}

	const FBSave::CustomMotion *fbsCm = FBSave::GetCustomMotion(buf);
	if (fbsCm == NULL)
	{
		free(buf);
		LOG_WARNING("load(): custommotion NULL");
		return false;
	}

	if (checkuin < 0)
		checkuin = GetClientInfoProxy()->getUin();

	m_iAuthUin = fbsCm->authuin();

	if (m_iAuthUin != checkuin && (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), m_iAuthUin) == false))
	{
		free(buf);
		LOG_WARNING("load(): m_iAuthUin != checkuin");
		return false;
	}

	m_sName = fbsCm->name()->c_str();
	m_sAuthName = fbsCm->authorname()->c_str();
	
	m_iDuration = fbsCm->duration();
	m_sKey = fbsCm->key()->c_str();

	m_MotionDatas.clear();
	for (size_t i = 0; i < fbsCm->bonemotions()->size(); i++)
	{
		auto fbsObcm = fbsCm->bonemotions()->Get(i);

		CustomMotionData cmd;
		cmd.clear();
		for (size_t k = 0; k < fbsObcm->keyframes()->size(); k++)
		{
			auto fbsKfd = fbsObcm->keyframes()->Get(k);
			cmd.addFbs(fbsKfd);
		}

		m_MotionDatas[fbsObcm->bonename()->c_str()] = cmd;
	}

	free(buf);
	return true;
}

bool CustomMotion::save(std::string path)
{
	flatbuffers::FlatBufferBuilder builder;

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::OneBoneCustomMotion>>> onebonemotionsoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::OneBoneCustomMotion>> onebonemotions;

	auto iter = m_MotionDatas.begin();
	for (; iter != m_MotionDatas.end(); iter++)
	{
		auto &cmd = iter->second;
		size_t tickSize = cmd.ticks.size();
		if (tickSize < cmd.posoffsets.size() || tickSize < cmd.quats.size() || tickSize < cmd.scale3s.size())
			continue;

		std::vector<flatbuffers::Offset<FBSave::KeyFrameData>> vFbsKfds;
		for (size_t i = 0; i < tickSize; i++)
		{
			auto fbsv3Offset = Vector3ToVec3(cmd.posoffsets[i]);
			Vector3f eulerZYX = QuaternionToEulerAngle(cmd.quats[i], math::RotationOrder::kOrderZYX);
			auto fbsv3Scale = Vector3ToVec3(cmd.scale3s[i]);
			vFbsKfds.push_back(FBSave::CreateKeyFrameData(builder, 
				cmd.ticks[i], &fbsv3Offset, (int16_t)eulerZYX.x, (int16_t)eulerZYX.y, (int16_t)eulerZYX.z,
				cmd.scales[i], &fbsv3Scale
			));
		}
	


		onebonemotions.push_back(FBSave::CreateOneBoneCustomMotion(builder, builder.CreateString(iter->first), builder.CreateVector(vFbsKfds)));
	}

	onebonemotionsoffset = builder.CreateVector(onebonemotions);

	unsigned long t = (unsigned long)time(NULL);
	auto custommotion = FBSave::CreateCustomMotion(builder, builder.CreateString(m_sName), builder.CreateString(m_sAuthName), m_iAuthUin, m_iMotionId, t, m_iDuration, builder.CreateString(m_sKey), onebonemotionsoffset);
	builder.Finish(custommotion);

	return GetFileManager().SaveToWritePath(path.c_str(), builder.GetBufferPointer(), builder.GetSize());
}

int CustomMotion::getMotionId()
{
	return m_iMotionId;
}

void CustomMotion::setMotionId(int motionid)
{
	m_iMotionId = motionid;
}

int CustomMotion::getDuration()
{
	return m_iDuration;
}

void CustomMotion::setDuration(int duration)
{
	m_iDuration = duration;
}

void CustomMotion::setMotionInfo(std::string skey, int authuin, std::string authname, int duration, int motionid/* =0 */)
{
	m_sKey = skey;
	m_sAuthName = authname;
	m_iAuthUin = authuin;
	m_iDuration = duration;
	m_iMotionId = motionid;
}

void CustomMotion::insertCustomKeyFrame(std::string bonename, const unsigned int tick, Rainbow::Vector3f &translate, Rainbow::Quaternionf &quat, float scale)
{
	Vector3f scale3(scale);
	insertCustomKeyFrame(bonename, tick, translate, quat, scale3);
}

void CustomMotion::insertCustomKeyFrame(std::string bonename, const unsigned int tick, Rainbow::Vector3f &translate, Rainbow::Quaternionf &quat, Rainbow::Vector3f& scale3)
{
	auto iter = m_MotionDatas.find(bonename);
	CustomMotionData* pCmd = nullptr;
	if (iter != m_MotionDatas.end())
	{
		pCmd = &iter->second;
	}
	else
	{
		m_MotionDatas[bonename] = CustomMotionData();
		pCmd = &m_MotionDatas[bonename];
	}
	CustomMotionData& cmd = *pCmd;
	cmd.add(tick, translate, quat, scale3);
}

void CustomMotion::setMotionData(SharePtr<Rainbow::ModelData> modeldata, int motionid, AnimPlayMode mode)
{
	if (modeldata)
	{
		modeldata->addCustomSequenceIDWithCopiedBones(motionid, mode, 0, m_iDuration);
		auto iter = m_MotionDatas.begin();
		for (; iter != m_MotionDatas.end(); iter++)
		{
			CustomMotionData& cmd = iter->second;
			int nkey = cmd.ticks.size();
			if(nkey <= 0)
				continue;

			modeldata->setCustomKeyFrames(motionid, iter->first.c_str(), nkey, 
				&cmd.ticks[0], &cmd.posoffsets[0], &cmd.quats[0], &cmd.scale3s[0]);
		}

		modeldata->setCustomSequenceEndtime(motionid, m_iDuration);
	}
}
