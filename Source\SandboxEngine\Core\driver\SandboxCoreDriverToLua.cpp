/*
** Lua binding: SandboxCoreDriverToLua
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_SandboxCoreDriverToLua_open (lua_State* tolua_S);

#include "base/SandboxMacros.h"
#pragma warning(disable:4800)
#include "base/SandboxMgrBase.h"
#include "base/SandboxType.h"
#include "base/SandboxRef.h"
#include "base/SandboxObject.h"
#include "base/SandboxComponent.h"
#include "base/SandboxGameObject.h"
#include "base/SandboxCoreModule.h"
#include "event/SandboxParam.h"
#include "event/SandboxParamGroup.h"
#include "event/SandboxParamObject.h"
#include "event/SandboxCallback.h"
#include "event/SandboxContext.h"
#include "event/SandboxResult.h"
#include "event/SandboxEventDispatcherManager.h"
#include "event/SandboxSchedulerManager.h"
#include "event/SandboxEventQueue.h"
#include "event/SandboxEventQueueManager.h"
#include "script/SandboxLuaPluginManager.h"
#include "script/SandboxCoreLuaDirector.h"
#include "scene/SandboxSceneObject.h"
#include "factory/SandboxFactory.h"
#include "factory/SandboxCoreFactorys.h"
#include "factory/SandboxFactoryImplements.h"
#include "SandboxCoreDriver.h"
#include "SandboxCoreManagers.h"
using namespace MNSandbox;

/* function to release collected object via destructor */
#ifdef __cplusplus

static int tolua_collect_WeakRef_MNSandbox__SandboxNode_ (lua_State* tolua_S)
{
 WeakRef<MNSandbox::SandboxNode>* self = (WeakRef<MNSandbox::SandboxNode>*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_MNSandbox__Callback (lua_State* tolua_S)
{
 MNSandbox::Callback* self = (MNSandbox::Callback*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_MNSandbox__Object (lua_State* tolua_S)
{
 MNSandbox::Object* self = (MNSandbox::Object*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_SandboxMgrBase (lua_State* tolua_S)
{
 SandboxMgrBase* self = (SandboxMgrBase*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}
#endif


/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"MNSandbox::SandboxParam");
 tolua_usertype(tolua_S,"MNSandbox::Callback");
 tolua_usertype(tolua_S,"MNSandbox::SandboxParamObject<SandboxContext>");
 tolua_usertype(tolua_S,"MNSandbox::SandboxResult");
 tolua_usertype(tolua_S,"MNSandbox::SandboxLuaPluginManager");
 tolua_usertype(tolua_S,"MNSandbox::SandboxCoreDriver");
 tolua_usertype(tolua_S,"MemoryPoolRef");
 tolua_usertype(tolua_S,"EventObjectManager");
 tolua_usertype(tolua_S,"WeakRef<MNSandbox::SandboxNode>");
 tolua_usertype(tolua_S,"MNSandbox::SandboxEventQueue");
 tolua_usertype(tolua_S,"MNSandbox::SandboxCoreLuaDirector");
 tolua_usertype(tolua_S,"MNSandbox::SandboxFactory_SandboxSceneComponent");
 tolua_usertype(tolua_S,"MNSandbox::SandboxCoreFactorys");
 tolua_usertype(tolua_S,"MNSandbox::SandboxNode");
 tolua_usertype(tolua_S,"MNSandbox::Object");
 tolua_usertype(tolua_S,"MNSandbox::Ref");
 tolua_usertype(tolua_S,"MNSandbox::SandboxCoreModule");
 tolua_usertype(tolua_S,"MNSandbox::SandboxContext");
 tolua_usertype(tolua_S,"MNSandbox::SandboxEventQueueManager");
 tolua_usertype(tolua_S,"MNSandbox::SandboxFactoryNormal");
 tolua_usertype(tolua_S,"MNSandbox::SandboxSchedulerManager");
 tolua_usertype(tolua_S,"SandboxFactoryNormal<MNSandbox::SceneComponent>");
 tolua_usertype(tolua_S,"MNSandbox::SandboxEventDispatcherManager");
 tolua_usertype(tolua_S,"MNSandbox::noncopyable");
 tolua_usertype(tolua_S,"MNSandbox::SandboxFactory");
 tolua_usertype(tolua_S,"MNSandbox::SandboxParamGroup");
 tolua_usertype(tolua_S,"MNSandbox::SandboxParamObject<SandboxResult>");
 tolua_usertype(tolua_S,"SandboxMgrBase");
 tolua_usertype(tolua_S,"MNSandbox::Component");
 tolua_usertype(tolua_S,"MNSandbox::SandboxCoreManagers");
 tolua_usertype(tolua_S,"MNSandbox::GameObject");
 tolua_usertype(tolua_S,"SandboxCorePools");
}

/* method: new of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SandboxMgrBase* tolua_ret = (SandboxMgrBase*)  Mtolua_new((SandboxMgrBase)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SandboxMgrBase");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00_local
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SandboxMgrBase* tolua_ret = (SandboxMgrBase*)  Mtolua_new((SandboxMgrBase)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SandboxMgrBase");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_delete00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onInit of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onInit00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onInit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onInit'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onInit();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onInit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onLoad of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLoad00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLoad00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onLoad'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onLoad();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onLoad'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onUpdate of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onUpdate00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onUpdate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
  float dt = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onUpdate'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onUpdate(dt);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onUpdate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onTick of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onTick00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onGameModeChange of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onGameModeChange00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onGameModeChange00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
  int iGameMode = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onGameModeChange'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onGameModeChange(iGameMode);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onGameModeChange'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onDestroy of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onDestroy00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onDestroy00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onDestroy'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onDestroy();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onDestroy'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onEnterWorld of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onEnterWorld00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onEnterWorld00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Object* pWorld = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onEnterWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onEnterWorld(pWorld);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onEnterWorld'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onLeaveWorld of class  SandboxMgrBase */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLeaveWorld00
static int tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLeaveWorld00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxMgrBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxMgrBase* self = (SandboxMgrBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onLeaveWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onLeaveWorld();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onLeaveWorld'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: _ID of class  MNSandbox::Ref */
#ifndef TOLUA_DISABLE_tolua_get_MNSandbox__Ref__ID
static int tolua_get_MNSandbox__Ref__ID(lua_State* tolua_S)
{
  MNSandbox::Ref* self = (MNSandbox::Ref*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '_ID'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->_ID);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: _ID of class  MNSandbox::Ref */
#ifndef TOLUA_DISABLE_tolua_set_MNSandbox__Ref__ID
static int tolua_set_MNSandbox__Ref__ID(lua_State* tolua_S)
{
  MNSandbox::Ref* self = (MNSandbox::Ref*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '_ID'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->_ID = ((unsigned)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::Object* tolua_ret = (MNSandbox::Object*)  Mtolua_new((MNSandbox::Object)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Object");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00_local
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::Object* tolua_ret = (MNSandbox::Object*)  Mtolua_new((MNSandbox::Object)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Object");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_delete00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Init of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_Init00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_Init00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Init'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->Init();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Init'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Release of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_Release00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_Release00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Release'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->Release();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Release'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Event of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Event'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventDispatcherManager& tolua_ret = (MNSandbox::SandboxEventDispatcherManager&)  self->Event();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventDispatcherManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Event'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Event2 of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event200
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event200(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Event2'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   EventObjectManager& tolua_ret = (EventObjectManager&)  self->Event2();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"EventObjectManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Event2'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SchedulerMgr of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_SchedulerMgr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_SchedulerMgr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SchedulerMgr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxSchedulerManager& tolua_ret = (MNSandbox::SandboxSchedulerManager&)  self->SchedulerMgr();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxSchedulerManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SchedulerMgr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: LuaPluginMgr of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_LuaPluginMgr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_LuaPluginMgr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'LuaPluginMgr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxLuaPluginManager& tolua_ret = (MNSandbox::SandboxLuaPluginManager&)  self->LuaPluginMgr();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxLuaPluginManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'LuaPluginMgr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: EventQueue of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_EventQueue00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_EventQueue00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'EventQueue'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventQueueManager& tolua_ret = (MNSandbox::SandboxEventQueueManager&)  self->EventQueue();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueueManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'EventQueue'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: PreTick of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_PreTick00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_PreTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'PreTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->PreTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'PreTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Tick of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_Tick00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_Tick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Tick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->Tick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Tick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: PostTick of class  MNSandbox::Object */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Object_PostTick00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Object_PostTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* self = (MNSandbox::Object*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'PostTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->PostTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'PostTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DestroyComponent of class  MNSandbox::GameObject */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_DestroyComponent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_DestroyComponent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::GameObject",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Component",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::GameObject* self = (MNSandbox::GameObject*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Component* comp = ((MNSandbox::Component*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DestroyComponent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->DestroyComponent(comp);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DestroyComponent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetComponentByName of class  MNSandbox::GameObject */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_GetComponentByName00
static int tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_GetComponentByName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::GameObject",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::GameObject* self = (const MNSandbox::GameObject*)  tolua_tousertype(tolua_S,1,0);
  const std::string tolua_var_1 = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetComponentByName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Component* tolua_ret = (MNSandbox::Component*)  self->GetComponentByName(tolua_var_1);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Component");
   tolua_pushcppstring(tolua_S,(const char*)tolua_var_1);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetComponentByName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AddComponent of class  MNSandbox::GameObject */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_AddComponent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_AddComponent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::GameObject",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Component",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::GameObject* self = (MNSandbox::GameObject*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Component* comp = ((MNSandbox::Component*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AddComponent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->AddComponent(comp);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AddComponent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: RemoveComponent of class  MNSandbox::GameObject */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_RemoveComponent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_RemoveComponent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::GameObject",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Component",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::GameObject* self = (MNSandbox::GameObject*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Component* comp = ((MNSandbox::Component*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'RemoveComponent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->RemoveComponent(comp);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'RemoveComponent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: HasKey of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__HasKey00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__HasKey00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'HasKey'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->HasKey(key);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'HasKey'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string value = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_String(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)value);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_String'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const char* value = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_String(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
  }
 }
 return 1;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String02
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String02(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string value = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_String(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)key);
   tolua_pushcppstring(tolua_S,(const char*)value);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String01(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String03
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String03(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const char* value = ((const char*)  tolua_tostring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_String(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String02(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_String'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String(key);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String02
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String02(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string def = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String(key,def);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String01(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_str of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_str'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->GetData_str();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_str'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_str of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_str'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->GetData_str(key);
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Number of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  double value = ((double)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Number(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Number'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Number of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  double value = ((double)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Number(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Number of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   double tolua_ret = (double)  self->GetData_Number();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Number'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Number of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  double def = ((double)  tolua_tonumber(tolua_S,3,0.0f));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   double tolua_ret = (double)  self->GetData_Number(key,def);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Bool of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  bool value = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Bool(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Bool'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Bool of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  bool value = ((bool)  tolua_toboolean(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Bool(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Bool of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetData_Bool();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Bool'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Bool of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  bool def = ((bool)  tolua_toboolean(tolua_S,3,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetData_Bool(key,def);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isuserdata(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  void* value = ((void*)  tolua_touserdata(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Userdata(luaType,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)luaType);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Userdata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isuserdata(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  void* value = ((void*)  tolua_touserdata(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxContext& tolua_ret = (MNSandbox::SandboxContext&)  self->SetData_Userdata(luaType,key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxContext");
   tolua_pushcppstring(tolua_S,(const char*)luaType);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   void* tolua_ret = (void*)  self->GetData_Userdata(luaType);
   tolua_pushuserdata(tolua_S,(void*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luaType);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Userdata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxContext> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxContext>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxContext>* self = (MNSandbox::SandboxParamObject<SandboxContext>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   void* tolua_ret = (void*)  self->GetData_Userdata(luaType,key);
   tolua_pushuserdata(tolua_S,(void*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luaType);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: HasKey of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__HasKey00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__HasKey00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'HasKey'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->HasKey(key);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'HasKey'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string value = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_String(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)value);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_String'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const char* value = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_String(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
  }
 }
 return 1;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String02
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String02(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string value = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_String(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)key);
   tolua_pushcppstring(tolua_S,(const char*)value);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String01(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String03
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String03(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const char* value = ((const char*)  tolua_tostring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_String(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String02(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_String'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String(key);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_String of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String02
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String02(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string def = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_String'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->GetData_String(key,def);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String01(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_str of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_str'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->GetData_str();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_str'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_str of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_str'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->GetData_str(key);
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Number of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  double value = ((double)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Number(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Number'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Number of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  double value = ((double)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Number(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Number of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   double tolua_ret = (double)  self->GetData_Number();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Number'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Number of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  double def = ((double)  tolua_tonumber(tolua_S,3,0.0f));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Number'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   double tolua_ret = (double)  self->GetData_Number(key,def);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Bool of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  bool value = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Bool(value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Bool'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Bool of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  bool value = ((bool)  tolua_toboolean(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Bool(key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Bool of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetData_Bool();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Bool'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Bool of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  bool def = ((bool)  tolua_toboolean(tolua_S,3,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Bool'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetData_Bool(key,def);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isuserdata(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  void* value = ((void*)  tolua_touserdata(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Userdata(luaType,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)luaType);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetData_Userdata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isuserdata(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
  void* value = ((void*)  tolua_touserdata(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxResult& tolua_ret = (MNSandbox::SandboxResult&)  self->SetData_Userdata(luaType,key,value);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxResult");
   tolua_pushcppstring(tolua_S,(const char*)luaType);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   void* tolua_ret = (void*)  self->GetData_Userdata(luaType);
   tolua_pushuserdata(tolua_S,(void*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luaType);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetData_Userdata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetData_Userdata of class  MNSandbox::SandboxParamObject<SandboxResult> */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxParamObject<SandboxResult>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxParamObject<SandboxResult>* self = (MNSandbox::SandboxParamObject<SandboxResult>*)  tolua_tousertype(tolua_S,1,0);
  const std::string luaType = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string key = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetData_Userdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   void* tolua_ret = (void*)  self->GetData_Userdata(luaType,key);
   tolua_pushuserdata(tolua_S,(void*)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luaType);
   tolua_pushcppstring(tolua_S,(const char*)key);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  MNSandbox::Callback */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::Callback",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::Callback* tolua_ret = (MNSandbox::Callback*)  Mtolua_new((MNSandbox::Callback)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Callback");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  MNSandbox::Callback */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00_local
static int tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::Callback",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::Callback* tolua_ret = (MNSandbox::Callback*)  Mtolua_new((MNSandbox::Callback)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsValid of class  MNSandbox::Callback */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Callback_IsValid00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Callback_IsValid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::Callback",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::Callback* self = (const MNSandbox::Callback*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsValid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsValid();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsValid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetListener of class  MNSandbox::Callback */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_Callback_GetListener00
static int tolua_SandboxCoreDriverToLua_MNSandbox_Callback_GetListener00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::Callback",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::Callback* self = (const MNSandbox::Callback*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetListener'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Object* tolua_ret = (MNSandbox::Object*)  self->GetListener();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Object");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetListener'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetSender of class  MNSandbox::SandboxContext */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_SetSender00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_SetSender00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxContext",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxContext* self = (MNSandbox::SandboxContext*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetSender'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetSender(obj);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetSender'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetSender of class  MNSandbox::SandboxContext */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_GetSender00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_GetSender00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxContext",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxContext* self = (MNSandbox::SandboxContext*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetSender'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Object* tolua_ret = (MNSandbox::Object*)  self->GetSender();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Object");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSender'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetReceiver of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetReceiver00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetReceiver00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxResult* self = (MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetReceiver'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetReceiver(obj);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetReceiver'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetReceiver of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetReceiver00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetReceiver00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxResult* self = (MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetReceiver'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Object* tolua_ret = (MNSandbox::Object*)  self->GetReceiver();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Object");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetReceiver'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetSuccess of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetSuccess00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetSuccess00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxResult* self = (MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
  bool success = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetSuccess'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetSuccess(success);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetSuccess'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsSuccessed of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsSuccessed00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsSuccessed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxResult* self = (const MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsSuccessed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsSuccessed();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsSuccessed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsFailed of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsFailed00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsFailed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxResult* self = (const MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsFailed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsFailed();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsFailed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsExecuted of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecuted00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecuted00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxResult* self = (const MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsExecuted'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsExecuted();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsExecuted'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetExecutedCount of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetExecutedCount00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetExecutedCount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxResult* self = (const MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetExecutedCount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   unsigned tolua_ret = (unsigned)  self->GetExecutedCount();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetExecutedCount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsExecSuccessed of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecSuccessed00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecSuccessed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxResult* self = (const MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsExecSuccessed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsExecSuccessed();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsExecSuccessed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AddExecCount of class  MNSandbox::SandboxResult */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_AddExecCount00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_AddExecCount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxResult",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxResult* self = (MNSandbox::SandboxResult*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AddExecCount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->AddExecCount();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AddExecCount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateEventDispatcher of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_CreateEventDispatcher00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_CreateEventDispatcher00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  unsigned long long flags = ((unsigned long long)  tolua_tonumber(tolua_S,3,s_defDispatcherFlags));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateEventDispatcher'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->CreateEventDispatcher(eventname,flags);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateEventDispatcher'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DestroyEventDispatcher of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyEventDispatcher00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyEventDispatcher00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DestroyEventDispatcher'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->DestroyEventDispatcher(eventname);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DestroyEventDispatcher'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DestroyAllDispatchers of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyAllDispatchers00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyAllDispatchers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DestroyAllDispatchers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->DestroyAllDispatchers();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DestroyAllDispatchers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SubscribeEvent of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,3,0));
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SubscribeEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->SubscribeEvent(eventname,obj,sLuaHandle);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SubscribeEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SubscribeEvent of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,3,0));
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,4,0));
  const std::string callbackName = ((const std::string)  tolua_tocppstring(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SubscribeEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->SubscribeEvent(eventname,obj,sLuaHandle,callbackName);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)eventname);
   tolua_pushcppstring(tolua_S,(const char*)callbackName);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: SubscribeEventWithCreateEvent of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,3,0));
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SubscribeEventWithCreateEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->SubscribeEventWithCreateEvent(eventname,obj,sLuaHandle);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SubscribeEventWithCreateEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SubscribeEventWithCreateEvent of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"MNSandbox::Object",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,3,0));
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,4,0));
  const std::string callbackName = ((const std::string)  tolua_tocppstring(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SubscribeEventWithCreateEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->SubscribeEventWithCreateEvent(eventname,obj,sLuaHandle,callbackName);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)eventname);
   tolua_pushcppstring(tolua_S,(const char*)callbackName);
  }
 }
 return 3;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: Subscribe of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Subscribe00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Subscribe00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const MNSandbox::Callback",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const MNSandbox::Callback* callback = ((const MNSandbox::Callback*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Subscribe'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->Subscribe(eventname,*callback);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Subscribe'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Unsubscribe of class  MNSandbox::SandboxEventDispatcherManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Unsubscribe00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Unsubscribe00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventDispatcherManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const MNSandbox::Callback",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventDispatcherManager* self = (MNSandbox::SandboxEventDispatcherManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const MNSandbox::Callback* callback = ((const MNSandbox::Callback*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'Unsubscribe'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->Unsubscribe(eventname,*callback);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Unsubscribe'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateScheduler of class  MNSandbox::SandboxSchedulerManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxSchedulerManager",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxSchedulerManager* self = (MNSandbox::SandboxSchedulerManager*)  tolua_tousertype(tolua_S,1,0);
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateScheduler'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->CreateScheduler(sLuaHandle);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateScheduler'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateScheduler of class  MNSandbox::SandboxSchedulerManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler01
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxSchedulerManager",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MNSandbox::SandboxSchedulerManager* self = (MNSandbox::SandboxSchedulerManager*)  tolua_tousertype(tolua_S,1,0);
  long const long sLuaHandle = ((long const long)  tolua_tonumber(tolua_S,2,0));
  const std::string name = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateScheduler'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->CreateScheduler(sLuaHandle,name);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)name);
  }
 }
 return 2;
tolua_lerror:
 return tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: DestroyScheduler of class  MNSandbox::SandboxSchedulerManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroyScheduler00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroyScheduler00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxSchedulerManager",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const MNSandbox::Callback",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxSchedulerManager* self = (MNSandbox::SandboxSchedulerManager*)  tolua_tousertype(tolua_S,1,0);
  const MNSandbox::Callback* callback = ((const MNSandbox::Callback*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DestroyScheduler'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->DestroyScheduler(*callback);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DestroyScheduler'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DestroySchedulerByName of class  MNSandbox::SandboxSchedulerManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroySchedulerByName00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroySchedulerByName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxSchedulerManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxSchedulerManager* self = (MNSandbox::SandboxSchedulerManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DestroySchedulerByName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->DestroySchedulerByName(name);
   tolua_pushcppstring(tolua_S,(const char*)name);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DestroySchedulerByName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetCallback of class  MNSandbox::SandboxSchedulerManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_GetCallback00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_GetCallback00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxSchedulerManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxSchedulerManager* self = (MNSandbox::SandboxSchedulerManager*)  tolua_tousertype(tolua_S,1,0);
  const std::string name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetCallback'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Callback tolua_ret = (MNSandbox::Callback)  self->GetCallback(name);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((MNSandbox::Callback)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(MNSandbox::Callback));
     tolua_pushusertype(tolua_S,tolua_obj,"MNSandbox::Callback");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
   tolua_pushcppstring(tolua_S,(const char*)name);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetCallback'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: PushEvent of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PushEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PushEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"MNSandbox::SandboxContext",0,&tolua_err)) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueue* self = (MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::SandboxContext context = *((MNSandbox::SandboxContext*)  tolua_tousertype(tolua_S,3,0));
  bool repeatable = ((bool)  tolua_toboolean(tolua_S,4,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'PushEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->PushEvent(eventname,context,repeatable);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'PushEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: PopEvent of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PopEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PopEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"MNSandbox::SandboxContext",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueue* self = (MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
  std::string outEventname = ((std::string)  tolua_tocppstring(tolua_S,2,0));
  MNSandbox::SandboxContext* outContext = ((MNSandbox::SandboxContext*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'PopEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->PopEvent(outEventname,*outContext);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)outEventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'PopEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: TriggerEvent of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_TriggerEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_TriggerEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueue* self = (MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
  int max = ((int)  tolua_tonumber(tolua_S,2,-1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'TriggerEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->TriggerEvent(max);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'TriggerEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: HasEvent of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_HasEvent00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_HasEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxEventQueue* self = (const MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
  const std::string eventname = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'HasEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->HasEvent(eventname);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)eventname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'HasEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetSize of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_GetSize00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_GetSize00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const MNSandbox::SandboxEventQueue* self = (const MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetSize'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   unsigned tolua_ret = (unsigned)  self->GetSize();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSize'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetDispatchMaxNum of class  MNSandbox::SandboxEventQueue */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_SetDispatchMaxNum00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_SetDispatchMaxNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueue",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueue* self = (MNSandbox::SandboxEventQueue*)  tolua_tousertype(tolua_S,1,0);
  unsigned maxnum = ((unsigned)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetDispatchMaxNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetDispatchMaxNum(maxnum);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetDispatchMaxNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetInstance of class  MNSandbox::SandboxEventQueueManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetInstance00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetInstance00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::SandboxEventQueueManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,nullptr));
  {
   MNSandbox::SandboxEventQueueManager& tolua_ret = (MNSandbox::SandboxEventQueueManager&)  MNSandbox::SandboxEventQueueManager::GetInstance(obj);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueueManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetInstance'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetManualQueue of class  MNSandbox::SandboxEventQueueManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetManualQueue00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetManualQueue00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::SandboxEventQueueManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,nullptr));
  {
   MNSandbox::SandboxEventQueue& tolua_ret = (MNSandbox::SandboxEventQueue&)  MNSandbox::SandboxEventQueueManager::GetManualQueue(obj);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueue");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetManualQueue'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetAutoQueue of class  MNSandbox::SandboxEventQueueManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetAutoQueue00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetAutoQueue00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::SandboxEventQueueManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MNSandbox::Object",1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::Object* obj = ((MNSandbox::Object*)  tolua_tousertype(tolua_S,2,nullptr));
  {
   MNSandbox::SandboxEventQueue& tolua_ret = (MNSandbox::SandboxEventQueue&)  MNSandbox::SandboxEventQueueManager::GetAutoQueue(obj);
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueue");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetAutoQueue'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ManualQueue of class  MNSandbox::SandboxEventQueueManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_ManualQueue00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_ManualQueue00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueueManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueueManager* self = (MNSandbox::SandboxEventQueueManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ManualQueue'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventQueue& tolua_ret = (MNSandbox::SandboxEventQueue&)  self->ManualQueue();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueue");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ManualQueue'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: AutoQueue of class  MNSandbox::SandboxEventQueueManager */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_AutoQueue00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_AutoQueue00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxEventQueueManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxEventQueueManager* self = (MNSandbox::SandboxEventQueueManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'AutoQueue'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventQueue& tolua_ret = (MNSandbox::SandboxEventQueue&)  self->AutoQueue();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueue");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'AutoQueue'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: LoadLuaFile of class  MNSandbox::SandboxCoreLuaDirector */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_LoadLuaFile00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_LoadLuaFile00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreLuaDirector",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreLuaDirector* self = (MNSandbox::SandboxCoreLuaDirector*)  tolua_tousertype(tolua_S,1,0);
  const std::string luafile = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'LoadLuaFile'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->LoadLuaFile(luafile);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luafile);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'LoadLuaFile'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: DoLuaFile of class  MNSandbox::SandboxCoreLuaDirector */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_DoLuaFile00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_DoLuaFile00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreLuaDirector",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreLuaDirector* self = (MNSandbox::SandboxCoreLuaDirector*)  tolua_tousertype(tolua_S,1,0);
  const std::string luafile = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'DoLuaFile'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->DoLuaFile(luafile);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)luafile);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'DoLuaFile'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetInstance of class  MNSandbox::SandboxCoreFactorys */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetInstance00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetInstance00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::SandboxCoreFactorys",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::SandboxCoreFactorys& tolua_ret = (MNSandbox::SandboxCoreFactorys&)  MNSandbox::SandboxCoreFactorys::GetInstance();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxCoreFactorys");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetInstance'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetFactory of class  MNSandbox::SandboxCoreFactorys */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetFactory00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetFactory00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreFactorys",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreFactorys* self = (MNSandbox::SandboxCoreFactorys*)  tolua_tousertype(tolua_S,1,0);
  const std::string factoryName = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetFactory'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxFactory* tolua_ret = (MNSandbox::SandboxFactory*)  self->GetFactory(factoryName);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::SandboxFactory");
   tolua_pushcppstring(tolua_S,(const char*)factoryName);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetFactory'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetInstancePtr of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetInstancePtr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetInstancePtr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MNSandbox::SandboxCoreDriver* tolua_ret = (MNSandbox::SandboxCoreDriver*)  MNSandbox::SandboxCoreDriver::GetInstancePtr();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::SandboxCoreDriver");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetInstancePtr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetManagers of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetManagers00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetManagers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreDriver* self = (MNSandbox::SandboxCoreDriver*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetManagers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxCoreManagers& tolua_ret = (MNSandbox::SandboxCoreManagers&)  self->GetManagers();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxCoreManagers");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetManagers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPools of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetPools00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetPools00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreDriver* self = (MNSandbox::SandboxCoreDriver*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPools'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   SandboxCorePools& tolua_ret = (SandboxCorePools&)  self->GetPools();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"SandboxCorePools");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPools'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetLua of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetLua00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetLua00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreDriver* self = (MNSandbox::SandboxCoreDriver*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetLua'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxCoreLuaDirector& tolua_ret = (MNSandbox::SandboxCoreLuaDirector&)  self->GetLua();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxCoreLuaDirector");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetLua'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetFactorys of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetFactorys00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetFactorys00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreDriver* self = (MNSandbox::SandboxCoreDriver*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetFactorys'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxCoreFactorys& tolua_ret = (MNSandbox::SandboxCoreFactorys&)  self->GetFactorys();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxCoreFactorys");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetFactorys'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetGlobalServiceNode of class  MNSandbox::SandboxCoreDriver */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetGlobalServiceNode00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetGlobalServiceNode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreDriver",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreDriver* self = (MNSandbox::SandboxCoreDriver*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetGlobalServiceNode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WeakRef<MNSandbox::SandboxNode> tolua_ret = (WeakRef<MNSandbox::SandboxNode>)  self->GetGlobalServiceNode();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WeakRef<MNSandbox::SandboxNode>)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WeakRef<MNSandbox::SandboxNode>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WeakRef<MNSandbox::SandboxNode>));
     tolua_pushusertype(tolua_S,tolua_obj,"WeakRef<MNSandbox::SandboxNode>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetGlobalServiceNode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetSchedulerMgr of class  MNSandbox::SandboxCoreManagers */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetSchedulerMgr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetSchedulerMgr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreManagers",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreManagers* self = (MNSandbox::SandboxCoreManagers*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetSchedulerMgr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxSchedulerManager& tolua_ret = (MNSandbox::SandboxSchedulerManager&)  self->GetSchedulerMgr();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxSchedulerManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSchedulerMgr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetEventDispatcherMgr of class  MNSandbox::SandboxCoreManagers */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventDispatcherMgr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventDispatcherMgr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreManagers",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreManagers* self = (MNSandbox::SandboxCoreManagers*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetEventDispatcherMgr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventDispatcherManager& tolua_ret = (MNSandbox::SandboxEventDispatcherManager&)  self->GetEventDispatcherMgr();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventDispatcherManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetEventDispatcherMgr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetEventQueueMgr of class  MNSandbox::SandboxCoreManagers */
#ifndef TOLUA_DISABLE_tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventQueueMgr00
static int tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventQueueMgr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MNSandbox::SandboxCoreManagers",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MNSandbox::SandboxCoreManagers* self = (MNSandbox::SandboxCoreManagers*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetEventQueueMgr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxEventQueueManager& tolua_ret = (MNSandbox::SandboxEventQueueManager&)  self->GetEventQueueMgr();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"MNSandbox::SandboxEventQueueManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetEventQueueMgr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_SandboxCoreDriverToLua_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"SandboxMgrBase","SandboxMgrBase","",tolua_collect_SandboxMgrBase);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SandboxMgrBase, "SandboxMgrBase", "SandboxMgrBase", "")
#endif
  #else
  tolua_cclass(tolua_S,"SandboxMgrBase","SandboxMgrBase","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SandboxMgrBase, "SandboxMgrBase", "SandboxMgrBase", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"SandboxMgrBase");
   tolua_function(tolua_S,"new",tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00);
   tolua_function(tolua_S,"new_local",tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00_local);
   tolua_function(tolua_S,".call",tolua_SandboxCoreDriverToLua_SandboxMgrBase_new00_local);
   tolua_function(tolua_S,"delete",tolua_SandboxCoreDriverToLua_SandboxMgrBase_delete00);
   tolua_function(tolua_S,"onInit",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onInit00);
   tolua_function(tolua_S,"onLoad",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLoad00);
   tolua_function(tolua_S,"onUpdate",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onUpdate00);
   tolua_function(tolua_S,"onTick",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onTick00);
   tolua_function(tolua_S,"onGameModeChange",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onGameModeChange00);
   tolua_function(tolua_S,"onDestroy",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onDestroy00);
   tolua_function(tolua_S,"onEnterWorld",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onEnterWorld00);
   tolua_function(tolua_S,"onLeaveWorld",tolua_SandboxCoreDriverToLua_SandboxMgrBase_onLeaveWorld00);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"Ref","MNSandbox::Ref","MNSandbox::noncopyable",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Ref, "Ref", "MNSandbox::Ref", "MNSandbox::noncopyable")
#endif
   tolua_beginmodule(tolua_S,"Ref");
    tolua_variable(tolua_S,"_ID",tolua_get_MNSandbox__Ref__ID,tolua_set_MNSandbox__Ref__ID);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"Object","MNSandbox::Object","MNSandbox::Ref",tolua_collect_MNSandbox__Object);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Object, "Object", "MNSandbox::Object", "MNSandbox::Ref")
#endif
   #else
   tolua_cclass(tolua_S,"Object","MNSandbox::Object","MNSandbox::Ref",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Object, "Object", "MNSandbox::Object", "MNSandbox::Ref")
#endif
   #endif
   tolua_beginmodule(tolua_S,"Object");
    tolua_function(tolua_S,"new",tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00);
    tolua_function(tolua_S,"new_local",tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00_local);
    tolua_function(tolua_S,".call",tolua_SandboxCoreDriverToLua_MNSandbox_Object_new00_local);
    tolua_function(tolua_S,"delete",tolua_SandboxCoreDriverToLua_MNSandbox_Object_delete00);
    tolua_function(tolua_S,"Init",tolua_SandboxCoreDriverToLua_MNSandbox_Object_Init00);
    tolua_function(tolua_S,"Release",tolua_SandboxCoreDriverToLua_MNSandbox_Object_Release00);
    tolua_function(tolua_S,"Event",tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event00);
    tolua_function(tolua_S,"Event2",tolua_SandboxCoreDriverToLua_MNSandbox_Object_Event200);
    tolua_function(tolua_S,"SchedulerMgr",tolua_SandboxCoreDriverToLua_MNSandbox_Object_SchedulerMgr00);
    tolua_function(tolua_S,"LuaPluginMgr",tolua_SandboxCoreDriverToLua_MNSandbox_Object_LuaPluginMgr00);
    tolua_function(tolua_S,"EventQueue",tolua_SandboxCoreDriverToLua_MNSandbox_Object_EventQueue00);
    tolua_function(tolua_S,"PreTick",tolua_SandboxCoreDriverToLua_MNSandbox_Object_PreTick00);
    tolua_function(tolua_S,"Tick",tolua_SandboxCoreDriverToLua_MNSandbox_Object_Tick00);
    tolua_function(tolua_S,"PostTick",tolua_SandboxCoreDriverToLua_MNSandbox_Object_PostTick00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"Component","MNSandbox::Component","MNSandbox::Object",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Component, "Component", "MNSandbox::Component", "MNSandbox::Object")
#endif
   tolua_beginmodule(tolua_S,"Component");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"GameObject","MNSandbox::GameObject","MNSandbox::Object",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::GameObject, "GameObject", "MNSandbox::GameObject", "MNSandbox::Object")
#endif
   tolua_beginmodule(tolua_S,"GameObject");
    tolua_function(tolua_S,"DestroyComponent",tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_DestroyComponent00);
    tolua_function(tolua_S,"GetComponentByName",tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_GetComponentByName00);
    tolua_function(tolua_S,"AddComponent",tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_AddComponent00);
    tolua_function(tolua_S,"RemoveComponent",tolua_SandboxCoreDriverToLua_MNSandbox_GameObject_RemoveComponent00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxCoreModule","MNSandbox::SandboxCoreModule","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxCoreModule, "SandboxCoreModule", "MNSandbox::SandboxCoreModule", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxCoreModule");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxParam","MNSandbox::SandboxParam","MNSandbox::Ref",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxParam, "SandboxParam", "MNSandbox::SandboxParam", "MNSandbox::Ref")
#endif
   tolua_beginmodule(tolua_S,"SandboxParam");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxParamGroup","MNSandbox::SandboxParamGroup","MemoryPoolRef",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxParamGroup, "SandboxParamGroup", "MNSandbox::SandboxParamGroup", "MemoryPoolRef")
#endif
   tolua_beginmodule(tolua_S,"SandboxParamGroup");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxParamObject_SandboxContext_","MNSandbox::SandboxParamObject<SandboxContext>","MemoryPoolRef",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxParamObject<SandboxContext>, "SandboxParamObject_SandboxContext_", "MNSandbox::SandboxParamObject<SandboxContext>", "MemoryPoolRef")
#endif
   tolua_beginmodule(tolua_S,"SandboxParamObject_SandboxContext_");
    tolua_function(tolua_S,"HasKey",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__HasKey00);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String00);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String01);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String02);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_String03);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String00);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String01);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_String02);
    tolua_function(tolua_S,"GetData_str",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str00);
    tolua_function(tolua_S,"GetData_str",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_str01);
    tolua_function(tolua_S,"SetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number00);
    tolua_function(tolua_S,"SetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Number01);
    tolua_function(tolua_S,"GetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number00);
    tolua_function(tolua_S,"GetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Number01);
    tolua_function(tolua_S,"SetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool00);
    tolua_function(tolua_S,"SetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Bool01);
    tolua_function(tolua_S,"GetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool00);
    tolua_function(tolua_S,"GetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Bool01);
    tolua_function(tolua_S,"SetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata00);
    tolua_function(tolua_S,"SetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__SetData_Userdata01);
    tolua_function(tolua_S,"GetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata00);
    tolua_function(tolua_S,"GetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxContext__GetData_Userdata01);
   tolua_endmodule(tolua_S);
   tolua_cclass(tolua_S,"SandboxParamObject_SandboxResult_","MNSandbox::SandboxParamObject<SandboxResult>","MemoryPoolRef",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxParamObject<SandboxResult>, "SandboxParamObject_SandboxResult_", "MNSandbox::SandboxParamObject<SandboxResult>", "MemoryPoolRef")
#endif
   tolua_beginmodule(tolua_S,"SandboxParamObject_SandboxResult_");
    tolua_function(tolua_S,"HasKey",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__HasKey00);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String00);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String01);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String02);
    tolua_function(tolua_S,"SetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_String03);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String00);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String01);
    tolua_function(tolua_S,"GetData_String",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_String02);
    tolua_function(tolua_S,"GetData_str",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str00);
    tolua_function(tolua_S,"GetData_str",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_str01);
    tolua_function(tolua_S,"SetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number00);
    tolua_function(tolua_S,"SetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Number01);
    tolua_function(tolua_S,"GetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number00);
    tolua_function(tolua_S,"GetData_Number",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Number01);
    tolua_function(tolua_S,"SetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool00);
    tolua_function(tolua_S,"SetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Bool01);
    tolua_function(tolua_S,"GetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool00);
    tolua_function(tolua_S,"GetData_Bool",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Bool01);
    tolua_function(tolua_S,"SetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata00);
    tolua_function(tolua_S,"SetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__SetData_Userdata01);
    tolua_function(tolua_S,"GetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata00);
    tolua_function(tolua_S,"GetData_Userdata",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxParamObject_SandboxResult__GetData_Userdata01);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"Callback","MNSandbox::Callback","",tolua_collect_MNSandbox__Callback);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Callback, "Callback", "MNSandbox::Callback", "")
#endif
   #else
   tolua_cclass(tolua_S,"Callback","MNSandbox::Callback","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::Callback, "Callback", "MNSandbox::Callback", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"Callback");
    tolua_function(tolua_S,"new",tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00);
    tolua_function(tolua_S,"new_local",tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00_local);
    tolua_function(tolua_S,".call",tolua_SandboxCoreDriverToLua_MNSandbox_Callback_new00_local);
    tolua_function(tolua_S,"IsValid",tolua_SandboxCoreDriverToLua_MNSandbox_Callback_IsValid00);
    tolua_function(tolua_S,"GetListener",tolua_SandboxCoreDriverToLua_MNSandbox_Callback_GetListener00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxContext","MNSandbox::SandboxContext","MNSandbox::SandboxParamObject<SandboxContext>",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxContext, "SandboxContext", "MNSandbox::SandboxContext", "MNSandbox::SandboxParamObject<SandboxContext>")
#endif
   tolua_beginmodule(tolua_S,"SandboxContext");
    tolua_function(tolua_S,"SetSender",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_SetSender00);
    tolua_function(tolua_S,"GetSender",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxContext_GetSender00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxResult","MNSandbox::SandboxResult","MNSandbox::SandboxParamObject<SandboxResult>",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxResult, "SandboxResult", "MNSandbox::SandboxResult", "MNSandbox::SandboxParamObject<SandboxResult>")
#endif
   tolua_beginmodule(tolua_S,"SandboxResult");
    tolua_function(tolua_S,"SetReceiver",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetReceiver00);
    tolua_function(tolua_S,"GetReceiver",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetReceiver00);
    tolua_function(tolua_S,"SetSuccess",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_SetSuccess00);
    tolua_function(tolua_S,"IsSuccessed",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsSuccessed00);
    tolua_function(tolua_S,"IsFailed",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsFailed00);
    tolua_function(tolua_S,"IsExecuted",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecuted00);
    tolua_function(tolua_S,"GetExecutedCount",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_GetExecutedCount00);
    tolua_function(tolua_S,"IsExecSuccessed",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_IsExecSuccessed00);
    tolua_function(tolua_S,"AddExecCount",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxResult_AddExecCount00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxEventDispatcherManager","MNSandbox::SandboxEventDispatcherManager","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxEventDispatcherManager, "SandboxEventDispatcherManager", "MNSandbox::SandboxEventDispatcherManager", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxEventDispatcherManager");
    tolua_function(tolua_S,"CreateEventDispatcher",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_CreateEventDispatcher00);
    tolua_function(tolua_S,"DestroyEventDispatcher",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyEventDispatcher00);
    tolua_function(tolua_S,"DestroyAllDispatchers",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_DestroyAllDispatchers00);
    tolua_function(tolua_S,"SubscribeEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent00);
    tolua_function(tolua_S,"SubscribeEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEvent01);
    tolua_function(tolua_S,"SubscribeEventWithCreateEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent00);
    tolua_function(tolua_S,"SubscribeEventWithCreateEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_SubscribeEventWithCreateEvent01);
    tolua_function(tolua_S,"Subscribe",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Subscribe00);
    tolua_function(tolua_S,"Unsubscribe",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventDispatcherManager_Unsubscribe00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxSchedulerManager","MNSandbox::SandboxSchedulerManager","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxSchedulerManager, "SandboxSchedulerManager", "MNSandbox::SandboxSchedulerManager", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxSchedulerManager");
    tolua_function(tolua_S,"CreateScheduler",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler00);
    tolua_function(tolua_S,"CreateScheduler",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_CreateScheduler01);
    tolua_function(tolua_S,"DestroyScheduler",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroyScheduler00);
    tolua_function(tolua_S,"DestroySchedulerByName",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_DestroySchedulerByName00);
    tolua_function(tolua_S,"GetCallback",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxSchedulerManager_GetCallback00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxEventQueue","MNSandbox::SandboxEventQueue","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxEventQueue, "SandboxEventQueue", "MNSandbox::SandboxEventQueue", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxEventQueue");
    tolua_function(tolua_S,"PushEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PushEvent00);
    tolua_function(tolua_S,"PopEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_PopEvent00);
    tolua_function(tolua_S,"TriggerEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_TriggerEvent00);
    tolua_function(tolua_S,"HasEvent",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_HasEvent00);
    tolua_function(tolua_S,"GetSize",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_GetSize00);
    tolua_function(tolua_S,"SetDispatchMaxNum",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueue_SetDispatchMaxNum00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxEventQueueManager","MNSandbox::SandboxEventQueueManager","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxEventQueueManager, "SandboxEventQueueManager", "MNSandbox::SandboxEventQueueManager", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxEventQueueManager");
    tolua_function(tolua_S,"GetInstance",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetInstance00);
    tolua_function(tolua_S,"GetManualQueue",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetManualQueue00);
    tolua_function(tolua_S,"GetAutoQueue",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_GetAutoQueue00);
    tolua_function(tolua_S,"ManualQueue",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_ManualQueue00);
    tolua_function(tolua_S,"AutoQueue",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxEventQueueManager_AutoQueue00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxLuaPluginManager","MNSandbox::SandboxLuaPluginManager","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxLuaPluginManager, "SandboxLuaPluginManager", "MNSandbox::SandboxLuaPluginManager", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxLuaPluginManager");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxCoreLuaDirector","MNSandbox::SandboxCoreLuaDirector","MNSandbox::SandboxCoreModule",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxCoreLuaDirector, "SandboxCoreLuaDirector", "MNSandbox::SandboxCoreLuaDirector", "MNSandbox::SandboxCoreModule")
#endif
   tolua_beginmodule(tolua_S,"SandboxCoreLuaDirector");
    tolua_function(tolua_S,"LoadLuaFile",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_LoadLuaFile00);
    tolua_function(tolua_S,"DoLuaFile",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreLuaDirector_DoLuaFile00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxNode","MNSandbox::SandboxNode","MNSandbox::GameObject",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxNode, "SandboxNode", "MNSandbox::SandboxNode", "MNSandbox::GameObject")
#endif
   tolua_beginmodule(tolua_S,"SandboxNode");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxFactory","MNSandbox::SandboxFactory","MNSandbox::Ref",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxFactory, "SandboxFactory", "MNSandbox::SandboxFactory", "MNSandbox::Ref")
#endif
   tolua_beginmodule(tolua_S,"SandboxFactory");
   tolua_endmodule(tolua_S);
   tolua_cclass(tolua_S,"SandboxFactoryNormal","MNSandbox::SandboxFactoryNormal","MNSandbox::SandboxFactory",NULL);
   tolua_beginmodule(tolua_S,"SandboxFactoryNormal");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxCoreFactorys","MNSandbox::SandboxCoreFactorys","MNSandbox::SandboxCoreModule",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxCoreFactorys, "SandboxCoreFactorys", "MNSandbox::SandboxCoreFactorys", "MNSandbox::SandboxCoreModule")
#endif
   tolua_beginmodule(tolua_S,"SandboxCoreFactorys");
    tolua_function(tolua_S,"GetInstance",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetInstance00);
    tolua_function(tolua_S,"GetFactory",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreFactorys_GetFactory00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxFactory_SandboxSceneComponent","MNSandbox::SandboxFactory_SandboxSceneComponent","SandboxFactoryNormal<MNSandbox::SceneComponent>",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxFactory_SandboxSceneComponent, "SandboxFactory_SandboxSceneComponent", "MNSandbox::SandboxFactory_SandboxSceneComponent", "SandboxFactoryNormal<MNSandbox::SceneComponent>")
#endif
   tolua_beginmodule(tolua_S,"SandboxFactory_SandboxSceneComponent");
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxCoreDriver","MNSandbox::SandboxCoreDriver","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxCoreDriver, "SandboxCoreDriver", "MNSandbox::SandboxCoreDriver", "")
#endif
   tolua_beginmodule(tolua_S,"SandboxCoreDriver");
    tolua_function(tolua_S,"GetInstancePtr",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetInstancePtr00);
    tolua_function(tolua_S,"GetManagers",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetManagers00);
    tolua_function(tolua_S,"GetPools",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetPools00);
    tolua_function(tolua_S,"GetLua",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetLua00);
    tolua_function(tolua_S,"GetFactorys",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetFactorys00);
    tolua_function(tolua_S,"GetGlobalServiceNode",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreDriver_GetGlobalServiceNode00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_cclass(tolua_S,"SandboxCoreManagers","MNSandbox::SandboxCoreManagers","MNSandbox::SandboxCoreModule",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(MNSandbox::SandboxCoreManagers, "SandboxCoreManagers", "MNSandbox::SandboxCoreManagers", "MNSandbox::SandboxCoreModule")
#endif
   tolua_beginmodule(tolua_S,"SandboxCoreManagers");
    tolua_function(tolua_S,"GetSchedulerMgr",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetSchedulerMgr00);
    tolua_function(tolua_S,"GetEventDispatcherMgr",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventDispatcherMgr00);
    tolua_function(tolua_S,"GetEventQueueMgr",tolua_SandboxCoreDriverToLua_MNSandbox_SandboxCoreManagers_GetEventQueueMgr00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_SandboxCoreDriverToLua (lua_State* tolua_S) {
 return tolua_SandboxCoreDriverToLua_open(tolua_S);
};
#endif

