/**
* file : SceneEffectSpotLightFrame
* func : 场景效果 （圆锥、圆台框）
* by : pengdapu
*/
#include "SceneEffectSpotLightFrame.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "SceneEffectEllipse.h"
#include "WorldRender.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectSpotLightFrame::SceneEffectSpotLightFrame()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_eSes = SceneEffectShape::SPOT_LIGHT_FRAME;
	m_iStroke = 1;
	SetRadius(300);
}

SceneEffectSpotLightFrame::~SceneEffectSpotLightFrame()
{
}

void SceneEffectSpotLightFrame::SetRadius(float radius)
{
	if (m_radius == radius)
		return;

	m_radius = radius;
	m_originRadius = radius;
	m_fInnerRadius = m_radius * sin(m_fInnerAngle);
	m_fOuterRadius = m_radius * sin(m_fOuterAngle);
}

void SceneEffectSpotLightFrame::SetOuterAngle(float angle)
{
	if (m_fOuterAngle == angle)
		return;

	m_fOuterAngle = angle;
	m_fOuterRadius = m_radius * sin(m_fOuterAngle);
}

void SceneEffectSpotLightFrame::SetInnerAngle(float angle)
{
	if (m_fInnerAngle == angle)
		return;

	m_fInnerAngle = angle;
	m_fInnerRadius = m_radius * sin(m_fInnerAngle);
}

void SceneEffectSpotLightFrame::OnClear()
{
	for (int i = 0; i < 2; ++i)
	{
		SANDBOX_DELETE(m_aEllipses[i]);
	}
	for (int i = 0; i < 4; ++i)
	{
		SANDBOX_DELETE(m_aGeneratrices[i]);
	}
}

void SceneEffectSpotLightFrame::Refresh()
{
	OnClear();
	for (int i = 0; i < 2; ++i)
	{
		SceneEffectEllipse* ellipse = m_aEllipses[i] = SANDBOX_NEW(SceneEffectEllipse);
		ellipse->SetCenter(m_vCenter);
		ellipse->SetColor(m_Color, m_Color);
		ellipse->SetStroke(m_iStroke);
		ellipse->SetMtlType(m_MtlType);
		ellipse->SetRotationAxis(Vector3f::zAxis, Vector3f::zAxis);
	}

	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* generatrix = m_aGeneratrices[i] = SANDBOX_NEW(SceneEffectLine);
		generatrix->SetStroke(m_iStroke);
		generatrix->SetColor(m_Color);
		generatrix->SetMtlType(m_MtlType);
	}

	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectSpotLightFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	for (int i = 0; i < 2; ++i)
	{
		SceneEffectEllipse* ellipse = m_aEllipses[i];
		if (ellipse) ellipse->OnDraw(pWorld);
	}
	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* generatrix = m_aGeneratrices[i];
		if (generatrix) generatrix->OnDraw(pWorld);
	}
}

bool SceneEffectSpotLightFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectSpotLightFrame::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;

	Matrix4x4f matRotate;
	QuaternionfToMatrix(q, matRotate);

	const float h = m_radius * vs.z;
	const Vector3f vt = vc;

	const float hi = m_radius * cos(m_fInnerAngle) * vs.z;
	const float ho = m_radius * cos(m_fOuterAngle) * vs.z;

	Vector3f vcInner(0, 0, hi);
	vcInner = matRotate.MultiplyPoint3(vcInner);
	vcInner += vc;
	Vector3f vcOuter(0, 0, ho);
	vcOuter = matRotate.MultiplyPoint3(vcOuter);
	vcOuter += vc;

	if (m_aEllipses[0])
	{
		m_aEllipses[0]->SetRadius(m_fInnerRadius);
		m_aEllipses[0]->RefreshEllipseFrame(vcInner, q, m_fInnerRadius * vs.x, m_fInnerRadius * vs.y);
	}
	if (m_aEllipses[1])
	{
		m_aEllipses[1]->SetRadius(m_fOuterRadius);
		m_aEllipses[1]->RefreshEllipseFrame(vcOuter, q, m_fOuterRadius * vs.x, m_fOuterRadius * vs.y);
	}

	Vector3f aVs[4];
	//Inner
	aVs[0].Set(m_fInnerRadius * vs.x, 0, hi);
	aVs[1].Set(-m_fInnerRadius * vs.x, 0, hi);
	//Outer
	aVs[2].Set(0, m_fOuterRadius * vs.y, ho);
	aVs[3].Set(0, -m_fOuterRadius * vs.y, ho);

	for (int i = 0; i < 4; ++i)
	{
		aVs[i] = matRotate.MultiplyPoint3(aVs[i]);
		aVs[i] += vc;
	}
	for (int i = 0; i < 4; ++i)
	{
		if (!m_aGeneratrices[i])
		{
			continue;
		}
		m_aGeneratrices[i]->SetPos(vt, aVs[i], false);
		m_aGeneratrices[i]->Refresh();
	}
}
