#include "IWorldConfig.h"
#include <functional>
#include "OgrePrerequisites.h"
#include "json/jsonxx.h"
#include "NoticeManager.h"
#include "MiniReportMgr.h"
#include "Platforms/PlatformInterface.h"
#include "ClientInterface.h"
#include "Common/OgreStringUtil.h"
#include "ChannelConfig.h"
#include "GameStatic.h"
#include "Sound/MusicManager.h"
#include "BaseClass/SharePtr.h"
#include "File/Packages/DataStream.h"
#include "File/FileManager.h"
#include "ClientInfo.h"
#include "SandBoxManager.h"
#include "File/FileUtilities.h"
#include "File/BufferEncrypt.h"
#include "Misc/Application.h"
#include "VMProtect.h"
#include "GameLanguageCsv.h"
#include "GetClientInfo.h"
#include "IModuleConf.h"
#include "ClientUrl/ClientUrl.h"
#include "MultiLocalMgr.h"
#include "MiniReportMgr.h"
#include "Achievementmanager.h"
#include "OWorldList/OWorldList.h"
#include "OWorldList/OWorldUtils.h"
#include "GraphicsMgr.h"
#include "WorldManager.h"
#include "ChangeModelMgr.h"
#include "MapEditManager.h"
#include "WorldListMgr.h"
#include "version.h"
#ifdef MODULE_FUNCTION_ENABLE_YOUME_TALK
#include "YouMeVoiceEngineImp.h"
#endif
#include "SandboxStringUtil.h"

#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
#include "HomelandSystem.h"
#endif
#include "DeveloperSystem.h"
#include "SandboxCoreSystem.h"
#include "SandboxGameSystem.h"
#include "IworldSystem.h"
#include "GamePlaySystem.h"
#ifdef MODULE_FUNCTION_ENABLE_BUSSINESS
#include "MiniBuzzSytem.h"
//#include "weaponskin/WeaponSkinSytem.h"
#endif

#include "SandboxListener.h"
#include "SandboxEventObjectManager.h"
#ifdef IWORLD_UNIVERSE_BUILD
#include "GameZoneCsv.h"
#include "SandboxGlobalNotifyCore.h"
#endif
#include "ClientAccount.h"
#include "VMProtect.h"
#include "UGCGameCamera.h"
#include "UGCRuntime.h"
#include "PlayerControl.h"

#include "CrashReportMgr.h"
#include "ActorBody.h"
#include "ActorLocoMotion.h"
#include "Utilities/HashStringFunctions.h"

#if PLATFORM_WIN
#include "platform/win32/WinUtil.h"
#endif

using namespace MNSandbox;
//using MINIW::Root;
using namespace MINIW;
using namespace Rainbow;
using Rainbow::XMLNode;
using Rainbow::StringUtil;

//IMPLEMENT_LAZY_SINGLETON(IWorldConfig)

IWorldConfig* IWorldConfig::getInstance()
{
	return GetIWorldConfigPtr();
}

IWorldConfig::IWorldConfig()
{
	m_lang = -1;
	createEvent();
}

IWorldConfig::~IWorldConfig()
{

}

void IWorldConfig::LoadWorldCfg()
{
	bool IworldConfigIsOK = false;
	dynamic_array<UInt8> data;

	VMP_BEGIN("IWorldConfig_LoadWorldCfg");

	auto loadCryptedData = [&data, this](const char* filename)
	{
		bool successful = LoadFileAsBinary(filename, data);
		if(!successful || data.empty())
		{
			GetFileManager().DeleteWritePathFileOrDir(filename);
			return false;
		}

		if (GetFileManager().CheckBufferEncrypt(data))
		{
			//数据解密
			dynamic_array<UInt8> buffer(kMemTempAlloc);
			GetFileManager().Decrypt(data, buffer);

			return m_Config.loadBuffer(buffer.data(), buffer.size());
		}
		else {
			return m_Config.loadBuffer(data.data(), data.size());
		}
	};

#if PLATFORM_WIN

	// studio 读取devices目录下的配置
#ifndef BUILD_MINI_EDITOR_APP
	if (GetFileManager().IsFileExistWritePath("iworld.cfg"))
	{
		AutoRefPtr<DataStream> dataStream = GetFileManager().OpenFileWritePath("iworld.cfg");
		IworldConfigIsOK = m_Config.loadBuffer(dataStream->GetMemoryImage(), dataStream->Size());
	}
#endif // BUILD_MINI_EDITOR_APP

    if (!IworldConfigIsOK)
    {
        IworldConfigIsOK = loadCryptedData("devices/iworld.cfg");
    }

#else // !PLATFORM_WIN
    core::string path = "iworld.cfg";
    GetFileManager().ToWritePathFull(path, path);
    LogStringMsg("ToWritePathFull path=%s", path.c_str());

    if (GetFileManager().IsFileExistWritePath(path.c_str()))
    {
        // mobile platform's iworld.cfg is encrypted.
        IworldConfigIsOK = loadCryptedData(path.c_str());
    }

    if (!IworldConfigIsOK)
    {
        // copy assets/iworld.cfg to writable_path
        // and make sure that OpenFile("iworld.cfg") does not open writable_path/iworld.cfg
#if PLATFORM_LINUX
        AutoRefPtr<DataStream> dataStream = GetFileManager().OpenFile("devices/iworld.cfg", true, FileOpType::kFileOpAll);
#elif PLATFORM_IOS
        // 上面的读取失败，则从 ipa 内读取
        AutoRefPtr<DataStream> dataStream = GetFileManager().OpenFile("iworld.cfg", true, FileOpType::kFileOpAll);
#elif PLATFORM_ANDROID
        // 上面的读取失败，则从 apk 内读取
        AutoRefPtr<DataStream> dataStream = GetFileManager().OpenFile("iworld.cfg", true, FileOpType::kFileOpAll);
#elif PLATFORM_OHOS
        // 上面的读取失败，则从 hap 内读取
        AutoRefPtr<DataStream> dataStream = GetFileManager().OpenFile("iworld.cfg", true, FileOpType::kFileOpAll);
#endif
        IworldConfigIsOK                = m_Config.loadBuffer(dataStream->GetMemoryImage(), dataStream->Size());
    }
#endif

    AssertMsg(IworldConfigIsOK, "LoadWorldCfg() failed");
    VMP_END;
}

void IWorldConfig::initStatistics()
{
	int curtime = (int)MINIW::GetTimeStamp();

	//老的客户端的用户, 设置好初始值, 不列入到新手统计里面
	XMLNode rootnode = m_Config.getRootNode();
	XMLNode node = rootnode.getOrCreateChild("GameData");
	if(!node.hasChild("Statistics"))
	{
		setStatistics("createtime", curtime);
		setStatistics("laststart", curtime);
		setStatistics("createworlds", 0);
		setStatistics("curachieve", -1);
	}
}

void IWorldConfig::setStatistics(const char *name, int var, bool force_save)
{
	XMLNode rootnode = m_Config.getRootNode();
	XMLNode node = rootnode.getOrCreateChild("GameData");

	XMLNode child = node.getOrCreateChild("Statistics");
	child.setAttribInt(name, var);

	if(force_save) SaveFile();
}

int IWorldConfig::getStatistics(const char *name)
{
	int val = 0;
	XMLNode node = m_Config.getNodeByPath("GameData.Statistics");
	if(!node.isNull() && node.hasAttrib(name))
	{
		val = node.attribToInt(name);
	}
	return val;
}

void IWorldConfig::setGameHotkey(const char *name, int code)
{
	XMLNode rootnode = m_Config.getRootNode();
	XMLNode node = rootnode.getOrCreateChild("GameData");

	XMLNode child = node.getOrCreateChild("Hotkey");
	child.setAttribInt(name, code);	

	SaveFile();
}

int IWorldConfig::getGameHotkey(const char *name)
{
	int val = -1;
	XMLNode node = m_Config.getNodeByPath("GameData.Hotkey", true);
	if(!node.isNull()) 
	{
		if(node.hasAttrib(name))
		{
			val = node.attribToInt(name);
		}
	}

	return val;
}

void IWorldConfig::setGameDataPath(const char* path, const char* attr, int val) {
	XMLNode node = m_Config.getRootNode();

	char name[256] = { 0 };
	const char *pcur = path;

	//拆分path
	while (pcur != NULL && *pcur != 0)
	{
		const char *pdot = strchr(pcur, '.');
		if (pdot)
		{
			size_t len = pdot - pcur;
			memcpy(name, pcur, len);
			name[len] = 0;
			pcur = pdot + 1;
		}
		else
		{
			strncpy(name, pcur, 256);
			pcur = NULL;
		}

		node = node.getOrCreateChild(name);
	}

	node.setAttribInt(attr, val);
	SaveFile();
}

int IWorldConfig::getGameDataPath(const char* path, const char* attr) {
    OPTICK_EVENT();
	XMLNode node = m_Config.getNodeByPath(path);
	if (!node.isNull())
	{
		if (node.hasAttrib(attr)) {
			int val = node.attribToInt(attr);
			return val;
		}
	}
	return 0;
}


void IWorldConfig::setGameData(const char *name, int val)
{
	if (!m_Config.isValid())
	{
		AssertMsg(false, "function setGameData(): iworld.cfg is not loaded!");
		LOG_WARNING("function setGameData(): iworld.cfg is not loaded!");
		return;
	}

	//LL:20220324:语言码特殊专属处理
	if (strcmp(name, "lang") == 0)
	{
		setLanguageId(val);
		return;
	}

	if (strcmp(name, "view_distance") == 0 && m_recordUseViewDistance != -1)
	{
		val = m_recordUseViewDistance;
	}

	//XMLNode node = m_Config.getNodeByPath("GameData.Buddy");
	//if(!node.isNull()) time1 = node.attribToInt("resettime");
	XMLNode rootnode = m_Config.getRootNode();
	XMLNode node = rootnode.getOrCreateChild("GameData");

	XMLNode child = node.getOrCreateChild("Settinig");
	child.setAttribInt(name, val);	

	if (strcmp(name,"limiteFps") == 0)
	{
		jsonxx::Object resObj;
		resObj << "fpsstate" << val;
		//限制帧率
		if (GetSandBoxManagerPtr())
		{
			std::string jsonStr = resObj.json();
			GetSandBoxManagerPtr()->DoEvent(SandBoxMgrEventID::EVENT_IWORLD_MODULE_FPS, 0, 0, const_cast<char*>(jsonStr.c_str()), jsonStr.length());
		}
	}

	SaveFile();
}

void IWorldConfig::setRecordViewDistance(int val)
{
	m_recordUseViewDistance = val;
}

int IWorldConfig::getGameData(const Rainbow::NoFreeFixedString& name) {
    OPTICK_EVENT();
    static Rainbow::NoFreeFixedString lang("lang");
    if (name == lang)
    {
        return getLanguageId();
    }
    auto it = m_ConfigCache.find(name.GetHash());
    if (it != m_ConfigCache.end())
    {
        return it.value();
    }
    auto val = getGameData(name.c_str());
    m_ConfigCache.insert(it, { name.GetHash(), val });
    return val;
}

bool IWorldConfig::hasGameData(const Rainbow::NoFreeFixedString& name) {
    OPTICK_EVENT();
    auto it = m_ConfigCache.find(name.GetHash());
    if (it != m_ConfigCache.end()) {
        return it.value() != InvalidValue;
    }
    bool has = hasGameData(name.c_str());
    if (has) {
        getGameData(name);
    }
    else {
        m_ConfigCache.insert(it, { name.GetHash(), InvalidValue });
    }
    return has;
}

int IWorldConfig::getGameData(const char *name)
{
    OPTICK_EVENT();
	#ifdef IWORLD_SERVER_BUILD
	if(strcmp(name, "game_env") == 0 && strlen(GetClientInfo()->getEnterParam("game_env")) > 0)
	{
		int env_ = atoi(GetClientInfo()->getEnterParam("game_env"));
		return env_;
	}
	#endif
	
	//LL:20220324:语言码特殊专属处理
	if (strcmp(name, "lang") == 0)
		return getLanguageId();

	/*#ifdef IWORLD_ADVANCE_BUILD
	if (strcmp(name, "game_env") == 0  && m_nApiId == 199)
	{
		return 2;
	}
	else if (strcmp(name, "game_env") == 0  && m_nApiId != 199)
	{
		return 12;
	}
	#endif*/

    //有些代码走lua进来的，这里还是算一个hash。看看在不在缓存
    //逻辑有点冗余，但可以尽量保证lua调用的情况下最优
    auto nameHash = Rainbow::ComputeFNV1aHash(name);
    auto it = m_ConfigCache.find(nameHash);
    if (it != m_ConfigCache.end()) {
        return it.value();
    }

	int val = 0;
	XMLNode node = m_Config.getNodeByPath("GameData.Settinig");
	if(!node.isNull()) 
	{
		if(node.hasAttrib(name))
		{
			val = node.attribToInt(name);
			if (strcmp(name, "view_distance") == 0 && (val < 1 || val > 8))  //非法视野范围值，值太大会导致地图内很卡，加个保护
			{
				node.setAttribInt(name, 2);
				val = 2;
			}
		}
		else
		{
			if(strcmp(name, "view_distance") == 0)
			{
				node.setAttribInt(name, 2);
				val = 2;
			}
			else if(strcmp(name, "showchip") == 0 || strcmp(name, "rocker") == 0 || strcmp(name, "camerashake") == 0  || strcmp(name, "view") == 0
				|| strcmp(name, "voiceopen") == 0 || strcmp(name, "speakerswitch") == 0 || strcmp(name, "popups") == 0 || strcmp(name, "fpsbuttom") == 0 || strcmp(name, "physxparam") == 0
				|| strcmp(name, "developerfloat") == 0 || strcmp(name, "hideOwnerName") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "fog") == 0)
			{
				node.setAttribInt(name, 2);
				val = 2;
			}
			else if (strcmp(name, "treeshape") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "radarSteering") == 0)
			{
				node.setAttribInt(name, 0);
				val = 0;
			}
			else if (strcmp(name, "recordingInterface") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "cantrace") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "damageswitch") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "bloodswitch") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "blockshape") == 0)
			{
				node.setAttribInt(name, 0);
				val = 0;
			}
			else if (strcmp(name, "isremote") == 0)
			{
				node.setAttribInt(name, 0);
				val = 0;
			}
			else if (strcmp(name, "graphic_setting") == 0)
			{
				node.setAttribInt(name, 2);
				val = 2;
			}
			else if (strcmp(name, "cloud_server_ver") == 0)
			{
				node.setAttribInt(name, 0);
				val = 0;
			}
			else if (strcmp(name, "botlami") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else if (strcmp(name, "combo_attack") == 0 || strcmp(name, "hideOtherName") == 0)
			{
				node.setAttribInt(name, 0);
				val = 0;
			}
			else if (strcmp(name, "combo_timer") == 0)
			{
				node.setAttribInt(name, 1);
				val = 1;
			}
			else
			{
				if(strcmp(name, "lang") == 0)
					node.setAttribStr(name, "zh-cn");
				else if(strcmp(name, "limiteFps") != 0)
					node.setAttribInt(name, 0);
				val = 0;
			}

			SaveFile();
		}
        m_ConfigCache.insert({ nameHash, val });
	}
	if (val<0 && strcmp(name,"game_env")==0)
	{
		val = 0; //防止game_env被串改成非法的值
	}

	return val;
}

bool IWorldConfig::hasGameData(const char* name)
{
    OPTICK_EVENT();
	XMLNode node = m_Config.getNodeByPath("GameData.Settinig");
	if (!node.isNull())
	{
		if (node.hasAttrib(name))
		{
			return true;
		}
	}
	return false;
}

std::string IWorldConfig::GetLanguage()
{
	return GameLanguageCsv::getInstance()->id2code(getLanguageId());
}

void IWorldConfig::onModuleConfChangeCountry()
{
	IModuleConf::onChangeCountry();
}

void IWorldConfig::onClientUrlChangeCountry()
{
	ClientUrl::onChangeCountry();
}

void IWorldConfig::onMultiLocalMgrCountry()
{
	MultiLocalMgr::getSingleton()->onCountry();
}

void IWorldConfig::httpReportMgrInitUrl()
{
	Mini::GetHttpReportMgr().initUrl();
}

std::string IWorldConfig::GetClientUrl(const std::string& key)
{
	return ClientUrl::GetUrl(key).url;
}

int IWorldConfig::GetClientUrlPort(const std::string& key)
{
	return ClientUrl::GetUrl(key).port;
}

std::string IWorldConfig::GetUrlString(const std::string& key, const std::string& suffix)
{
	return ClientUrl::GetUrlString(key, suffix);

}
/*---------------------------------------------
* :20220324:判断字符串是否为数字
*/
inline bool __isNumber(const char* str,int len)
{
    if (NULL == str || len <= 0)
        return false;

    for (int i = 0; i < len;++i)
    {
        char c = *str;
        if ( c < '0' || c > '9')
            return false;

        str++;
    }

    return true;
}

/*---------------------------------------------
* :20220324:获取语言ID
*/
int  IWorldConfig::getLanguageId()
{
    //已读取过
    if (-1 != m_lang)
        return m_lang;

	if (! GetClientInfo()->getLanguageCfgLoadRet())
	{
		//语言配置没加载前，使用默认值
		return getOsLanguageId();
	}

    //读取配置--未配置则写入
    std::string confLang = MINIW::GetLanguageConf();
	//如果是cn使用0
	if (confLang == "cn") {
		return 0;
	}

	MNSandbox::MNStringUtil::trim(confLang, MNSandbox::MNStringUtil::ms_trimDefaultSpaceLine);
    // 原生存储的是ID
	if (!confLang.empty())
	{
		if (__isNumber(confLang.c_str(), confLang.length()))
		{
			int id = atoi(confLang.c_str());
			//配置映射
			if (GameLanguageCsv::getInstance()->containsId(id))
			{
				m_lang = GameLanguageCsv::getInstance()->idLanguage(id).id;
				return m_lang;
			}
		}
		//配置映射
		if (GameLanguageCsv::getInstance()->containsCode(confLang.c_str()))
		{
			m_lang = GameLanguageCsv::getInstance()->code2id(confLang.c_str());
			return m_lang;
		}
	}
	//-- 复位并存储
	m_lang = resetLanguageId();
	//
    return m_lang;
}


/*---------------------------------------------
* :20220324:设置语言ID
*/
void IWorldConfig::setLanguageId(int val)
{
	std::string sVal = GameLanguageCsv::getInstance()->id2code(val);
	//--写入环境配置
	MINIW::SetLanguageConf(val, sVal.c_str());
}

/*---------------------------------------------
* :LL::20220324:获取系统语言映射语言ID
*/
int  IWorldConfig::getOsLanguageId()
{
    //------初始化语言
	static std::string osLang = MINIW::GetOsLanguage(); //国内是没有语言配置表的，lua调用getGameData("lang)会一直调用到这里直接去原生层取，比较频繁。这里改成static变量避免一直调用到原生层code by:keguanqiang-20230313
    //----- 初始化映射
    return GameLanguageCsv::getInstance()->flagLanguage(osLang.c_str()).id;
}

/*---------------------------------------------
* :LL::20220328:复位语言
*/
int IWorldConfig::resetLanguageId()
{
    //-- 获取系统映射
    int lang = getOsLanguageId();
    //--保存
    setLanguageId(lang);

    return lang;
}

XMLNode IWorldConfig::getRootNode()
{
	return m_Config.getRootNode();
}

/*
1.这个函数只是是Lua端的一个下载回调
不是加载本地xml文件的，，这里不能放其他初始化的逻辑.
2.这个函数在国内移动不会调用
by:DemonYan
*/
void IWorldConfig::onLoadGameVersionXmlAfterDownload(Rainbow::XMLData &config)
{
	NoticeManager::getInstance()->setShowCount(0);
	NoticeManager::getInstance()->setInterval(3600);

	int lang_ = getGameData("lang");
	int evn_ = getGameData("game_env");

	//按语言计算节点名字
	char noticeContent_[32] = { 0 };
	char noticeBrief_[32] = { 0 };
	if (lang_ == 0)
	{	//中文
		sprintf(noticeContent_, "noticeContent");
		sprintf(noticeBrief_, "noticeBrief");
	}
	else
	{
		//海外按语言选择一次公告内容
		sprintf(noticeContent_, "noticeContent_%d", lang_);
		sprintf(noticeBrief_, "noticeBrief_%d", lang_);
	}


	//1 获得公告版本号
	int noticeCode = 0;
	XMLNode nodeNotice = config.getRootNode().getChild("noticeCode");
	if (!nodeNotice.isNull()) {
		noticeCode = atoi(nodeNotice.getText());
	}


	//2 计算公告内容
	if (noticeCode > 0)
	{
		//计算每天公告次数和时间间隔
		XMLNode nodeCotent = config.getRootNode().getChild("noticeShowCount");
		if (!nodeCotent.isNull()) {
			NoticeManager::getInstance()->setShowCount(atoi(nodeCotent.getText()));
		}
		nodeCotent = config.getRootNode().getChild("noticeInterval");
		if (!nodeCotent.isNull()) {
			NoticeManager::getInstance()->setInterval(atoi(nodeCotent.getText()));
		}

		//首次查找 _1 _2 _3
		nodeCotent = config.getRootNode().getChild(noticeContent_);
		if (!nodeCotent.isNull())
		{
			const char *content = nodeCotent.getText();
			GetChannelConfig().showUpdateFrame(content, 2, noticeCode, 0);
		}
		else
		{
			//二次查找使用_x
			nodeCotent = config.getRootNode().getChild("noticeContent_x");  
			if (!nodeCotent.isNull())
			{
				const char *content = nodeCotent.getText();
				GetChannelConfig().showUpdateFrame(content, 2, noticeCode, 0);
			}
			else
			{
				//三次查找用原始配置
				nodeCotent = config.getRootNode().getChild("noticeContent");
				if (!nodeCotent.isNull())
				{
					const char *content = nodeCotent.getText();
					GetChannelConfig().showUpdateFrame(content, 2, noticeCode, 0);
				}
			}
		}
	}


	//3 计算控制参数
	XMLNode nodeParams = config.getRootNode().getChild("Params");
	if (!nodeParams.isNull())
	{
		const char *content = nodeParams.getText();
		GetChannelConfig().showUpdateFrame(content, 3, 0, 0);
	}


	//4 公告摘要
	XMLNode briefSwitch = config.getRootNode().getChild("briefSwitch");
	if (!briefSwitch.isNull() && atoi(briefSwitch.getText()) == 1)
	{
		//首次查找 _1 _2 _3
		XMLNode noticeBrief = config.getRootNode().getChild(noticeBrief_);
		if (!noticeBrief.isNull())
		{
			const char *content = noticeBrief.getText();
			GetChannelConfig().showUpdateFrame(content, 4, 0, 0);
		}
		else 
		{
			//二次查找 使用_x
			noticeBrief = config.getRootNode().getChild("noticeBrief_x");
			if (!noticeBrief.isNull())
			{
				const char *content = noticeBrief.getText();
				GetChannelConfig().showUpdateFrame(content, 4, 0, 0);
			}
			else
			{
				//三次查找用原始配置
				XMLNode noticeBrief = config.getRootNode().getChild("noticeBrief");
				if (!noticeBrief.isNull())
				{
					const char *content = noticeBrief.getText();
					GetChannelConfig().showUpdateFrame(content, 4, 0, 0);
				}
			}
		}
	}

}

void IWorldConfig::SaveFile()
{
	//dynamic_array<UInt8> buffer(kMemTempAlloc);
	//m_Config.toBuffer(buffer);
	//dynamic_array<UInt8> encodeBuffer(kMemTempAlloc);
	//GetFileManager().Encrypt(buffer, encodeBuffer);
	//GetFileManager().SaveToWritePath("iworld.cfg", encodeBuffer.data(), encodeBuffer.size());
	//新引擎加解密iworld.cfg文件的逻辑有bug，这里临时先修改为不加密保存

	//studio不拷贝iworld.cfg
#ifdef BUILD_MINI_EDITOR_APP
	return;
#endif // BUILD_MINI_EDITOR_APP
    OPTICK_EVENT();

	core::string strFullPath;
	GetFileManager().ToWritePathFull("iworld.cfg", strFullPath);
	m_Config.saveFile(strFullPath);

    m_ConfigCache.clear();
}

void IWorldConfig::InitYouMeVoiceEngine(int uin)
{
#ifdef MODULE_FUNCTION_ENABLE_YOUME_TALK
#if OGRE_PLATFORM != OGRE_PLATFORM_LINUXPC

    //LOG_INFO("YouMeVocieEnable: %d", YouMeVocieEnable());
    if (GetYouMeVoiceEngineImpPtr() && GetChannelConfig().YouMeVocieEnable() && uin > 1)
    {
        YOUME_RTC_SERVER_REGION rtc_region = RTC_CN_SERVER;
        if (getGameData("game_env") >= 10)
        {
            rtc_region = RTC_SG_SERVER;
#ifdef IWORLD_UNIVERSE_BUILD
			std::string country = GetSharedCountry(true);
			int voiceZone = GameZoneCsv::getInstance()->getVoiceZone(country.c_str());
			rtc_region = (YOUME_RTC_SERVER_REGION)voiceZone;
#endif
        }
        GetYouMeVoiceEngineImpPtr()->init(rtc_region, "");
    }
#endif
#endif // IWORLD_YOUME_SDK
}

bool IWorldConfig::loadWorldAchievements(long long owid, int specialType)
{
	return AchievementManager::GetInstancePtr()->loadWorldAchievements(owid, specialType);
}

void IWorldConfig::setAchievementArryNum(int objid, int goal, int id, int num)
{
	return AchievementManager::GetInstancePtr()->setAchievementArryNum(objid, goal, id, num);
}

bool IWorldConfig::changeOWorldWsize(long long owid, unsigned int wsize)
{
	return OWorldList::GetInstance().changeOWorldWsize(owid, wsize);
}

//声音相关参数已移入 Rainbow::AudioManager() 
bool IWorldConfig::InitSoundSystem()
{

	SetSoundSystem();
	return true;
}

void IWorldConfig::SetSoundSystem(float ratio/* =1.0 */)
{
	XMLNode node = m_Config.getRootNode().getChild("GameData");
	if (!node.isNull())
	{
		XMLNode child = node.getChild("Settinig");
		if (!child.isNull())
		{
			//if (m_pSoundSystem)
			{
				bool musicOpen = child.hasAttrib("musicopen") && child.attribToInt("musicopen") == 1;
#ifdef IWORLD_UNIVERSE_BUILD
				bool isGameSetNodeMusicOpen = true;
				GlobalNotifyCore::GetInstance().MusicOpen.Emit(musicOpen, isGameSetNodeMusicOpen);
				if (musicOpen && isGameSetNodeMusicOpen)
#else
				if (musicOpen)
#endif // IWORLD_UNIVERSE_BUILD

				{
					float vol = child.attribToInt("musicvolume") / 100.0f;
					Rainbow::GetMusicManager().SetGlobalMusicVolume(vol * ratio);
				}
				else
				{
					Rainbow::GetMusicManager().SetGlobalMusicVolume(0.0f);

				}

				if (child.hasAttrib("soundopen") && child.attribToInt("soundopen") == 1)
				{
					float vol = child.attribToInt("volume") / 100.0f;
					Rainbow::GetMusicManager().SetGlobalSoundVolume(vol * ratio);
				}
				else
				{
					Rainbow::GetMusicManager().SetGlobalSoundVolume(0.0f);
				}
			}
		}
	}
}

void IWorldConfig::removeTrigger(long long objid)
{
    GraphicsMgr* mgr = GraphicsMgr::GetInstancePtr();
    if (mgr)
    {
        mgr->removeTrigger(objid);
    }
}

void IWorldConfig::removeChangeModelActor(long long objid)
{
    ChangeModelMgr* mgr2 = ChangeModelMgr::GetInstancePtr();
    if (mgr2)
    {
        mgr2->removeChangeModelActor(objid);
    }
}

bool IWorldConfig::loadOWDesc(OWORLD& oworld, long long owid, int specialType)
{
	return OWorldList::GetInstance().loadOWDesc(oworld, owid, specialType);
}

void IWorldConfig::checkOWAuthorUin(long long owid, int& authoruin)
{
	OWorldList::GetInstance().checkOWAuthorUin(owid, authoruin);
}

bool IWorldConfig::GetIsStartEdit()
{
	return MapEditManager::GetInstance().GetIsStartEdit();
}

bool IWorldConfig::ExcuteCmdWithRBClicked()
{
	return MapEditManager::GetInstance().ExcuteCmdWithRBClicked();
}

int IWorldConfig::onInputEvent(const Rainbow::InputEvent& event)
{
	return MapEditManager::GetInstance().onInputEvent(event);
}

void IWorldConfig::CopyMyWorldDataEx(WorldDesc* desc, const OWORLD& src)
{
	CopyMyWorldData(desc, src);
}

std::string IWorldConfig::GetIWorldVersionStr()
{
	return GetClientInfo()->GetClientVersionStr();
}

MINIW::GameStatic<IWorldConfig> s_IWorldConfig(MINIW::kInitManual);
IWorldConfig& GetIWorldConfig()
{
	return *s_IWorldConfig.EnsureInitialized();
}

IWorldConfig* GetIWorldConfigPtr()
{
	return s_IWorldConfig.EnsureInitialized();
}

//IworldSubsystem begin

void IWorldConfig::createEvent()
{
	typedef ListenerFunctionRef<bool&, int> ListenerOnSwitch;
	AutoRef<ListenerOnSwitch> listenerOnSwitch = SANDBOX_NEW(ListenerOnSwitch, [&](bool& ret, int type) -> void {
		ret = this->OnSwitch((MacroDefination)type);
	});
	MNSandbox::GetGlobalEvent().Subscribe("IWorldConfig_OnSwitch", listenerOnSwitch);
}
bool IWorldConfig::CreatePlugin(MINIW::ScriptVM* vm)
{
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), SandboxGameSystem);
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), SandboxCoreSystem);
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), HomelandSystem);
#endif
#ifdef MODULE_FUNCTION_ENABLE_BUSSINESS
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), MiniBuzzSytem);
#endif
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), DeveloperSystem);
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), IworldSystem);
	CREATE_PLUGIN(PluginManager::GetInstancePtr(), GamePlaySystem);

	PluginManager::GetInstancePtr()->SetScriptVM(vm);
	vm->setUserTypePointer("IworldSystemConfig", "IworldSystemConfig", IWorldConfig::getInstance());
	PluginManager::GetInstancePtr()->Awake();

	return true;
}

bool IWorldConfig::OnSwitch(MacroDefination type)
{
	switch (type)
	{
	case MACRO_IWORLD_YOUME_SDK:
#ifdef MODULE_FUNCTION_ENABLE_YOUME_TALK
#if OGRE_PLATFORM != OGRE_PLATFORM_LINUXPC
		return true;
#endif
#endif
		break;

	case MACRO_IWORLD_SERVER_BUILD:
#ifdef IWORLD_SERVER_BUILD
		return true;
#endif
		break;

	case MACRO_IWORLD_ADVANCE_BUILD:
#ifdef IWORLD_ADVANCE_BUILD
		return true;
#endif
		break;

	default:
		break;
	}
	return false;
}

bool IWorldConfig::DestroyAllPlugin()
{
	if (PluginManager::GetInstancePtr())
		PluginManager::GetInstancePtr()->Shut();
	DESTROY_ALL_PLUGIN(PluginManager::GetInstancePtr());
	return true;
}

void IWorldConfig::ForceOpenDebugWindow()
{
	MNSandbox::Config::GetSingleton().SetForceOpenDebugWindow(true);
}

int IWorldConfig::GetSandboxGameData(const char* name)
{
	int value = 0;
	XMLNode node = m_Config.getNodeByPath("GameData.Sandbox");
	if (!node.isNull())
	{
		if (node.hasAttrib(name))
		{
			value = node.attribToInt(name);
		}
	}
	return value;
}

//IworldSubsystem end
const AchievementInfo* IWorldConfig::getAccountAchievementInfo(int objid, int id)
{
	return AchievementManager::GetInstancePtr()->getAccountAchievementInfo(objid, id);
}

void IWorldConfig::readWorldDesc(OWORLD& oworld, const char* root, long long owid)
{
	OWorldList::GetInstance().readWorldDesc(oworld, root, owid);
}

void IWorldConfig::addMyWorld(const OWORLD& world)
{
	ClientAccountMgr::GetInstance().getMyWorldList()->addMyWorld(world);
}

OWORLD* IWorldConfig::findWorldDesc(long long owid)
{
	return  OWorldList::GetInstance().findWorldDesc(owid);
}

void IWorldConfig::VmpBeginUltra(const char* marker)
{
	VMP_BEGIN_ULTRA(marker);
}

void IWorldConfig::VmpEnd()
{
	VMP_END;
}

bool IWorldConfig::IsUGCGame()
{
	return UGCRuntime::GetInst()->IsUGCGame();
}

int IWorldConfig::getUinFromOWID(long long owid)
{
	return OWorldUtils::getUinFromOWID(owid);
}

void IWorldConfig::ClientGameJankReport(bool exit)
{
	Mini::GetHttpReportMgr().reportJankInfo(exit);
}

void IWorldConfig::reportRaknetInfo(RakNet::RakNetStatistics* info)
{
	Mini::GetHttpReportMgr().reportRaknetInfo(info);
}

std::string IWorldConfig::getSession_id()
{
	return Mini::HttpReportMgr::GetInstancePtr()->getSession_id();
}

std::string IWorldConfig::getGameSessionId()
{
	return Mini::HttpReportMgr::GetInstancePtr()->getGameSessionId().c_str();
}

std::string IWorldConfig::generateGameSessionId(const std::string& worldIdStr)
{
#ifdef IWORLD_UNIVERSE_BUILD
	return Mini::HttpReportMgr::GetInstancePtr()->generateGameSessionId(worldIdStr);
#else
	return Mini::HttpReportMgr::GetInstancePtr()->generateGameSessionId();
#endif
}

void IWorldConfig::generateGameSessionSeed()
{
	//海外统一生成GameSessionId规则，进入地图后才真正赋值到上报日志的公参中
#ifdef IWORLD_UNIVERSE_BUILD
	Mini::HttpReportMgr::GetInstancePtr()->generateGameSessionSeed();
#else
#endif

}

void IWorldConfig::SetReportCrashMgrWorldId(long long worldid)
{
    if (MINIW::GetReportCrashMgrPtr()) //收集地图ID
    {
        MINIW::GetReportCrashMgrPtr()->SetWorldId(worldid);
    }
#if PLATFORM_WIN
	WIN_UTIL::SetGlobalWorldID(worldid);
#endif
}

std::string IWorldConfig::GetSandboxGameString(const char* name)
{
	XMLNode node = m_Config.getNodeByPath("GameData.Sandbox");
	if (!node.isNull())
	{
		if (node.hasAttrib(name))
		{
			return node.attribToString(name);
		}
	}
	return std::string();
}

//IworldSubsystem end