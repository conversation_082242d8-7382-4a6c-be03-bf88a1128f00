#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "IActorBody.h"
#include "ImageMesh.h"
#include "Texture/TextureRenderGen.h"
#include "File/FileManager.h"

#include "worldData/worldDef.h"
#include "blocks/BlockMaterialMgr.h"
#include "WorldManager.h"

using namespace MNSandbox;
using namespace Rainbow;

FullyCustomModelMgr::FullyCustomModelMgr() : m_pCurEditActorBody(NULL), m_pCurEditFcm(NULL), m_bLoadedRes(false), m_nSpecialType(NORMAL_WORLD)
{
#ifndef IWORLD_SERVER_BUILD
	m_TextureGen = ENG_NEW_LABEL(TextureRenderGen, kMemGame)();

	m_IconDescs.clear();

	m_IncompleteIcons.clear();
	m_iRefreshIconTick = 0;
	m_iAutoSaveTick = 0;
	m_CurOWID = -1;
#else
    m_TextureGen = NULL;	
#endif
	m_pPackingFCMMgr = ENG_NEW(PackingFullyCustomModelMgr)();
}

FullyCustomModelMgr::~FullyCustomModelMgr()
{
	clearMapFullyModels();
	clearResFullyModels();
	clearEquipResFullyModels();
	clearPreviewResFullyModels();

	if (m_pCurEditActorBody)
		SANDBOX_DELETE(m_pCurEditActorBody);

	auto iterIcon = m_IconDescs.begin();
	for (; iterIcon != m_IconDescs.end(); iterIcon++)
	{
		ENG_DELETE(iterIcon->second);
	}
	m_IconDescs.clear();

	ENG_DELETE_LABEL(m_TextureGen, kMemGame);

	m_pCurEditFcm = NULL;

	ENG_DELETE(m_pPackingFCMMgr);
}

void FullyCustomModelMgr::leaveWorld()
{
	m_WaitDownloadListBySyncHostDeque.clear();
	m_WaitDownloadListByClientEdit.clear();
	m_FCMResInfos.clear();

	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	for (int i = 0; i < (int)m_vMapFcms.size(); i++)
	{
		char path[256];
		if (m_vMapFcms[i]->isLeaveWorldDel())
		{
			sprintf(path, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, m_vMapFcms[i]->getFileName().c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}
		ENG_DELETE(m_vMapFcms[i]);
	}
	m_vMapFcms.clear();

	m_pCurEditFcm = NULL;

	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->leaveWorld(m_CurOWID, m_nSpecialType);

	m_CurOWID = -1;
	m_nSpecialType = NORMAL_WORLD;
	auto iterIcon = m_IconDescs.begin();
	for (; iterIcon != m_IconDescs.end(); iterIcon++)
	{
		ENG_DELETE(iterIcon->second);
	}
	m_IconDescs.clear();
}


void FullyCustomModelMgr::onSwitchAccountSucceed(int uin)
{
	m_bLoadedRes = false;
	clearResFullyModels();
	loadResFullyCustomModel();

	loadEquipFullyCustomModel();
}

Rainbow::Model* FullyCustomModelMgr::getDefaultBoneModel()
{
	if (!m_defaultBoneModelData)
	{
		auto model = g_BlockMtlMgr.getModel("entity/custommodel/bone.omod");
		if (model)
		{
			m_defaultBoneModelData = model->GetModelData();
			Model::Destory(model);
		}
	}
	return Model::CreateInstanceLegacy(m_defaultBoneModelData);
}
