#pragma once
/**
* file : SandboxNotify
* func : 沙盒通知类
* by : chenzh
*/
#include "event/notify/SandboxNotifyInterface.h"
#include "event/notify/SandboxListenerInterface.h"
#include "event/notify/SandboxTCallback.h"
#include <vector>


namespace MNSandbox {

	template<typename... Args>
	class Notify;
	template<typename... Args>
	class Listener;
	class ListenerLua;

	/**
	* 通知基础类型
	*/
	template<typename... Args>
	class NotifyBase : public NotifyInterface
	{
		DECLARE_REFCLASS_TEMPLATEBASE(NotifyBase)
	public:
		typedef AutoRef<Listener<Args...>> TListener;
		typedef AutoRef<ListenerLua> TListenerLua;
	public:

		/* ()运算符 */
		bool operator()(const Args&... args) { return Emit(args...); }
		/* 触发 */
		virtual bool SANDBOXAPI Emit(const Args&... args) = 0;

		/* 订阅 sort 越小越优先执行 */
		inline void Subscribe(const TListener& listener, int sort=ms_ListenerDefaultSort) { _subscribe(listener, sort); }
		inline void SubscribeLua(const TListenerLua& listener, int sort=ms_ListenerDefaultSort) { _subscribe(listener, sort); }
		/* 取消订阅 */
		inline void Unsubscribe(const TListener& listener) { _unsubscribe(listener); }
		inline void UnsubscribeLua(const TListenerLua& listener) { _unsubscribe(listener); }
		/* 是否有效 */
		virtual bool IsValid() = 0;
		/* 清空 */
		virtual void Clear() = 0;
		/* 获取绑定的数量 */
		virtual size_t GetListenerCount() const = 0;
	};

	template<>
	class NotifyBase<> : public NotifyInterface
	{
		DECLARE_REFCLASS_TEMPLATEBASE(NotifyBase)
	public:
		typedef AutoRef<Listener<>> TListener;
		typedef AutoRef<ListenerLua> TListenerLua;
	public:

		/* ()运算符 */
		bool operator()() { return Emit(); }
		/* 触发 */
		virtual bool SANDBOXAPI Emit() = 0;

		/* 订阅 sort 越小越优先执行 */
		inline void Subscribe(const TListener& listener, int sort = ms_ListenerDefaultSort) { _subscribe(listener, sort); }
		inline void SubscribeLua(const TListenerLua& listener, int sort = ms_ListenerDefaultSort) { _subscribe(listener, sort); }
		/* 取消订阅 */
		inline void Unsubscribe(const TListener& listener) { _unsubscribe(listener); }
		inline void UnsubscribeLua(const TListenerLua& listener) { _unsubscribe(listener); }
		/* 是否有效 */
		virtual bool IsValid() = 0;
		/* 清空 */
		virtual void Clear() = 0;
		/* 获取绑定的数量 */
		virtual size_t GetListenerCount() const = 0;
	};

	/**
	* 通知
	*/
	template<typename... Args>
	class Notify : public NotifyBase<Args...>
	{
	public:
		typedef typename NotifyBase<Args...>::ThisType Super;
		typedef Notify ThisType;
	public:
		typedef Listeners<Args...> TListeners;
	public:
		Notify() {}
		virtual ~Notify() { DestroyListeners(); }

		/* 触发 */
		virtual bool SANDBOXAPI Emit(const Args&... args) override
		{
			if (m_allListeners)
			{
				auto listeners = m_allListeners; // 确保不会在过程中被释放
				return listeners->Call(args...);
			}
			return false;
		}

		/* 是否有效 */
		virtual bool IsValid() override { return (m_allListeners && m_allListeners->IsValid()); }
		/* 清空 */
		virtual void Clear() override { if (m_allListeners) m_allListeners->Clear(); }
		/* 获取绑定的数量 */
		virtual size_t GetListenerCount() const override { return m_allListeners ? m_allListeners->GetListenerCount() : 0; }

#ifdef SANDBOX_DEBUG_LISTENER
		/* 填入调用位置 */
		void Dev_SetDebugMsg(const std::string& msg) { CreateListeners(); m_allListeners->Dev_SetDebugMsg(msg); }
		const std::string& Dev_GetDebugMsg() { return m_allListeners ? m_allListeners->Dev_GetDebugMsg() : DefaultEmpty::s_emptyString; }
#endif

	protected:
		/* 监听 */
		virtual void OnAddListener(const AutoRef<ListenerInterface>& v, int sort) override
		{
			CreateListeners();
			m_allListeners->AddListener(v, sort);
		}
		/* 取消监听 */
		virtual void OnRemoveListener(const AutoRef<ListenerInterface>& v) override
		{
			if (m_allListeners)
				m_allListeners->RemoveListener(v);
		}

		// 创建listeners
		void CreateListeners()
		{
			if (!m_allListeners)
			{
				SANDBOXPROFILE_PUSHDATA(this, NOTIFY, GetRTTI()->GetType());
				m_allListeners = SANDBOX_NEW(TListeners, this);
			}
		}
		// 释放listeners
		void DestroyListeners()
		{
			if (m_allListeners)
			{
				SANDBOX_RELEASE(m_allListeners);
				SANDBOXPROFILE_POPDATA(this);
			}
		}

	protected:
		/* 监听列表 */
		AutoRef<TListeners> m_allListeners;
	};

	template<>
	class Notify<> : public NotifyBase<>
	{
	public:
		typedef typename NotifyBase<>::ThisType Super;
		typedef Notify ThisType;
	public:
		typedef Listeners<> TListeners;
	public:
		Notify() {}
		virtual ~Notify() { DestroyListeners(); }

		/* 触发 */
		virtual bool SANDBOXAPI Emit() override
		{
            OPTICK_EVENT();
			if (m_allListeners)
			{
				auto listeners = m_allListeners; // 确保不会在过程中被释放
				return listeners->Call();
			}
			return false;
		}

		/* 是否有效 */
		virtual bool IsValid() override { return (m_allListeners && m_allListeners->IsValid()); }
		/* 清空 */
		virtual void Clear() override { if (m_allListeners) m_allListeners->Clear(); }
		/* 获取绑定的数量 */
		virtual size_t GetListenerCount() const override { return m_allListeners ? m_allListeners->GetListenerCount() : 0; }

#ifdef SANDBOX_DEBUG_LISTENER
		/* 填入调用位置 */
		void Dev_SetDebugMsg(const std::string& msg) { CreateListeners(); m_allListeners->Dev_SetDebugMsg(msg); }
		const std::string& Dev_GetDebugMsg() { return m_allListeners ? m_allListeners->Dev_GetDebugMsg() : DefaultEmpty::s_emptyString; }
#endif

	protected:
		/* 监听 */
		virtual void OnAddListener(const AutoRef<ListenerInterface>& v, int sort) override
		{
			CreateListeners();
			m_allListeners->AddListener(v, sort);
		}
		/* 取消监听 */
		virtual void OnRemoveListener(const AutoRef<ListenerInterface>& v) override
		{
			if (m_allListeners)
				m_allListeners->RemoveListener(v);
		}

		// 创建listeners
		void CreateListeners()
		{
			if (!m_allListeners)
			{
				SANDBOXPROFILE_PUSHDATA(this, NOTIFY, GetRTTI()->GetType());
				m_allListeners = SANDBOX_NEW(TListeners, this);
			}
		}
		// 释放listeners
		void DestroyListeners()
		{
			if (m_allListeners)
			{
				SANDBOX_RELEASE(m_allListeners);
				SANDBOXPROFILE_POPDATA(this);
			}
		}

	protected:
		/* 监听列表 */
		AutoRef<TListeners> m_allListeners;
	};

	//////////////////////////////////////////////////////////////////////////////////////////

	/**
	* 通知
	*/
	template<typename... Args>
	class NotifyWithCallback : public Notify<Args...>
	{
		using Super = Notify<Args...>;
	public:
		NotifyWithCallback() = default;
		virtual ~NotifyWithCallback() = default;

		// 设置注册回调
		inline void SetAddListenerCallback(TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* callback) { m_callbackReg = callback; }
		inline TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* GetAddListenerCallback() { return m_callbackReg; }
		// 设置移除注册回调
		inline void SetRemoveListenerCallback(TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* callback) { m_callbackUnreg = callback; }
		inline TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* GetRemoveListenerCallback() { return m_callbackUnreg; }

	protected:
		/* 监听 */
		virtual void OnAddListener(const AutoRef<ListenerInterface>& v, int sort) override;
		/* 取消监听 */
		virtual void OnRemoveListener(const AutoRef<ListenerInterface>& v) override;

	protected:
		TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* m_callbackReg = nullptr;
		TCallback<NotifyInterface*, const AutoRef<ListenerInterface>&>* m_callbackUnreg = nullptr;
	};

	template<typename... Args>
	void NotifyWithCallback<Args...>::OnAddListener(const AutoRef<ListenerInterface>& v, int sort)
	{
		Super::OnAddListener(v, sort);
		if (m_callbackReg)
			m_callbackReg->Emit(this, v);
	}

	template<typename... Args>
	void NotifyWithCallback<Args...>::OnRemoveListener(const AutoRef<ListenerInterface>& v)
	{
		Super::OnRemoveListener(v);
		if (m_callbackUnreg)
			m_callbackUnreg->Emit(this, v);
	}

}