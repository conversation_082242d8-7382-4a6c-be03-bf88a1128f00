#include "SandboxBinary.h"
#include "SandboxRefBridge.h"
#include "SandboxReflexTypePolicy.h"
#include "SandboxReflexTypePolicyEx.h"

namespace MNSandbox{

	// 桥接代码，C++不要使用
    class UInt8VecBridge : public Bridge<Binary::UInt8Vec>
	{
	public:
		typedef ThisType Super;
		typedef UInt8VecBridge ThisType;

		static void Push(lua_State* L, const Binary::UInt8Vec& v)
		{
			PushNewObject(L, v);
		}

		// static void Push(lua_State *L, const std::string& v)
		// {
		// 	PushNewObject(L, v);
		// }

		// static void Push(lua_State *L, const std::vector<char>& v)
		// {
		// 	PushNewObject(L, v);
		// }
		static int New(lua_State *L)
		{
			auto params = lua_gettop(L);
			if(params == 0)
			{
				PushNewObject(L);
				return 1;
			}
			else if(params == 1)
			{
				if(lua_isnumber(L, -1))
				{
					std::size_t bufSize = lua_tonumber(L,-1);
					PushNewObject(L, bufSize);
					return 1;
				}
				else if(lua_isstring(L,-1))
				{
					std::size_t bufSize = 0;
					auto str = lua_tolstring(L,-1, &bufSize);
					PushNewObject(L,(unsigned char*)str, bufSize);
					return 1;
				}
			}
			return 0;
		}

		static void RegisterMetatable(lua_State *L)
		{
			Super::RegisterMetatable(L);
		}

		static void RegisterClassLibrary(lua_State* L)
		{
			luaL_register(L, "UInt8Vec", ms_ClassLibrary);
			lua_pop(L, 1);
		}

		static int ToString(lua_State *L)
		{
			auto &v = *UInt8VecBridge::GetObject(L, 1);
			auto isClear = lua_toboolean(L,2);
			std::string ret;
			v.convertToStr(ret,isClear);
			lua_pushlstring(L,ret.data(),ret.length());
			return 1;
		}

		static int OnLength(lua_State* L)
		{
			auto& v = *UInt8VecBridge::GetObject(L, 1);
			lua_pushnumber(L, (std::size_t)v.buffer.size());
			return 1;
		}

		static int OnToStr(lua_State* L)
		{
			lua_pushvalue(L, -1);
			lua_pushcclosure(L, UInt8VecBridge::ToString, 1);
			return 1;
		}
	private:
		static const luaL_Reg ms_ClassLibrary[];
	};

	const luaL_Reg UInt8VecBridge::ms_ClassLibrary[] = {
		{"new", UInt8VecBridge::New},
		{"New", UInt8VecBridge::New},
		{nullptr, nullptr}
	};

    template<>
	const std::string Bridge<Binary::UInt8Vec>::ms_ClassName = "Bridge_UInt8Vector";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Binary::UInt8Vec>::ms_defParams =
	{
		{"Length", UInt8VecBridge::OnLength},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Binary::UInt8Vec>::ms_defFunctions =
	{
		{"ToStr", UInt8VecBridge::OnToStr},
	};
	SANDBOX_BRIDGE_DEF(Binary::UInt8Vec);

	template<>
	int Bridge<Binary::UInt8Vec>::OnIndex(lua_State* L, Binary::UInt8Vec& object, const char* k)
	{
		// 当返回0时候没法判断转换是否成功
		auto idx = atoi(k);
		if ((idx > 0 || (idx == 0 && strcmp(k, "0") == 0)) && idx < object.buffer.size())
		{
			lua_pushnumber(L,(std::uint8_t)object.buffer[idx]);
			return 1;
		}

		std::string key = k;
		auto iter = ms_defParams.find(key);
		if (iter != ms_defParams.end())
		{
			return iter->second(L);
		}
		
		auto iter2 = ms_defFunctions.find(key); 
		if (iter2 != ms_defFunctions.end())
		{
			lua_pushcfunction(L, iter2->second);
			return 1;
		}
		return OnIndexError(L, k);
	}

	template<>
	void Bridge<Binary::UInt8Vec>::OnNewIndex(lua_State* L, Binary::UInt8Vec& object, const char* key)
	{
		auto idx = atoi(key);
		if ((idx > 0 || (idx == 0 && strcmp(key, "0") == 0)) && idx < object.buffer.size())
		{
			auto isInt = lua_isnumber(L, 3);
			if(isInt)
			{
				// range out of exception
				object.buffer[idx] = (std::uint8_t)(lua_tointeger(L, 3));
			}
		}
	}

	// Binary::UInt8Vec
	template<>
	void ReflexPolicyFunc<Binary::UInt8Vec>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		// 二进制转化jsonArray会影响性能
		MNJsonArray jsonarray;
		jsonarray << (std::uint32_t)v.buffer.size();
		for(auto& one : v.buffer)
		{
			jsonarray << (std::uint8_t)(one);
		}
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<Binary::UInt8Vec>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0))
		{
			auto bufSize = (std::uint32_t)jsona.get<MNJsonNumber>(0);
			if(bufSize >= 0 && bufSize <= 0x10000)
			{
				auto& buffer = v.buffer;
				buffer.resize(bufSize);
				for(auto idx = 0; idx < bufSize; idx++)	
				{
					if (jsona.has<MNJsonNumber>(idx + 1))
					{
						buffer[idx] = (std::uint8_t)jsona.get<MNJsonNumber>(idx + 1);
					}
				}
				return true;
			}
		}
		SANDBOX_ASSERT(false);
		return false;
	}

	template<>
	bool ReflexPolicyFunc<Binary::UInt8Vec>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *UInt8VecBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<Binary::UInt8Vec>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		UInt8VecBridge::Push(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<Binary::UInt8Vec>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		return ToString("{vector<uint8_t>:size(", v.buffer.size(), ")}");
	}
	template<>
	size_t ReflexPolicyFunc<Binary::UInt8Vec>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<unsigned>((unsigned)v.buffer.size());
		for (auto& cell : v.buffer)
		{
			len += out->WriteNumber<unsigned char>(cell);
		}
		return len;
	}
	template<>
	bool ReflexPolicyFunc<Binary::UInt8Vec>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		unsigned size = 0;
		if (!in->ReadNumber<unsigned>(size))
			return false;
		if (size > 0x10000)
			return false;

		unsigned char val;
		v.buffer.resize(size);
		for (auto& cell : v.buffer)
		{
			if (!in->ReadNumber<unsigned char>(val))
				return false;
			cell = val;
		}
		return true;
	}
	RegisterReflexTypePolicyClass(Binary::UInt8Vec, REFLEXTYPEENUM_UINT8VEC, ReflexPolicyFunc<Binary::UInt8Vec>);


	// AutoRef<Binary::UInt8Vec>
	// typedef AutoRef<Binary::UInt8Vec> autoref_uint8vec;
	// template<>
	// void ReflexPolicyFunc<autoref_uint8vec>::CallbackSerialize(const void* data, MNJsonVal& out)
	// {
	// 	// auto& v = Data(data);
	// 	// MNJsonObject jsonobj = ReflexPolicyFunc<Binary::UInt8Vec>::CallbackSerialize(v);
	// 	// out.import(jsonobj);
	// }
	// template<>
	// bool ReflexPolicyFunc<autoref_uint8vec>::CallbackUnserialize(void* data, const MNJsonVal& in)
	// {
	// 	return true;
	// }
	// template<>
	// bool ReflexPolicyFunc<autoref_uint8vec>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	// {
	// 	auto& v = Data(data);
	// 	return true;
	// }
	// template<>
	// int ReflexPolicyFunc<autoref_uint8vec>::CallbackLuaPushC(const void* data, lua_State* L)
	// {
	// 	auto& v = Data(data);
	// 	return 1;
	// }
	// template<>
	// std::string ReflexPolicyFunc<autoref_uint8vec>::CallbackToString(const void* data)
	// {
	// 	auto& v = Data(data);
	// 	char szTemp[128];
	// 	sprintf(szTemp, "[node(%s)]",);
	// 	return szTemp;
	// }
	// RegisterReflexTypePolicyClass(autoref_uint8vec, ReflexPolicyFunc<autoref_uint8vec>);

	namespace Binary
	{

        void RegisterMetatable(lua_State* L)
		{
			UInt8VecBridge::RegisterMetatable(L);
		}

        void RegisterClassLibrary(lua_State* L)
		{
			UInt8VecBridge::RegisterClassLibrary(L);
		}
    }
}
