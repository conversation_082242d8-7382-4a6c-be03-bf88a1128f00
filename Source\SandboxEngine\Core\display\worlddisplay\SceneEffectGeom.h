/*
*	file: SceneEffectGeom
*	func: 在世界场景中绘制几何图形
*	by: chenzh
*	time: 2021.1.22
*/
#ifndef __SCENE_EFFECT_GEOM_H__
#define __SCENE_EFFECT_GEOM_H__

#include "Math/Quaternionf.h"
#include "Math/Vector3f.h"

#include "SandboxWeakRef.h"
#include "SandboxSceneObject.h"
#include "WorldRender.h"
#include "BlockMeshVert.h"
#include "SandboxColor.h"

#include <vector>

class CurveFace;
class World;

typedef int SceneEffectID; // 场景特效ID

enum class SceneEffectShape {
	UNKNOWN,
	EDGE,
	ELLIPSE,
	RECTANGLE,
	BOX,
	BOX_FRAME,
	SPHERE,
	SPHERE_FRAME,
	HEMISPHERE_FRAME,
	//包括圆锥、圆台
	CONE_FRAME,
	//甜甜圈
	DONUT_FRAME,
	//聚光灯专用，有内外半径的双层圆锥
	SPOT_LIGHT_FRAME,
};

// 场景效果
class EXPORT_SANDBOXENGINE SceneEffectGeom //tolua_exports
{ //tolua_exports
public:
	SceneEffectGeom();
	SceneEffectGeom(CurveFace* curveFaces);
	virtual ~SceneEffectGeom();
	//tolua_begin

	virtual void OnClear() {}
	virtual void Refresh() = 0;
	//TODO: 2024-06-03 11:49:05: 优化：用刷新代替删除和新建的组合
	virtual void OnDraw(World* pWorld) = 0;
	virtual bool IsActive(World* pWorld) const;

	void SetAwayShow(bool alwaysShow);
	void SetShow(bool bShow);
	void SetCurveFaces(CurveFace* curveFaces);

	// 设置起始位置
	void SetPos(const WCoord startpos, const WCoord endpos);

	// 设置起始位置，末点位置
	void SetPos(const WCoord startpos, const WCoord endpos,
		Rainbow::Vector3f vPos,
		Rainbow::Quaternionf rotation,
		Rainbow::Vector3f vScale,
		MNSandbox::AutoRef<MNSandbox::SandboxNode> node);

	// 设置起始位置
	void SetPos(const WCoord startpos, const WCoord endpos, bool bNormalize);

	WCoord GetStartPos() const { return m_StartPos; }
	WCoord GetEndPos() const { return m_EndPos; }

	void SetRotation(const Rainbow::Quaternionf& q) { m_qRotation = q; }

	virtual void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs);

	// 设置圆心位置
	void SetCenter(const MNSandbox::MNCoord3f& center);
	const MNSandbox::MNCoord3f& GetCenter() const { return m_vCenter; }

	// 设置半径
	virtual void SetRadius(float radius);
	float GetRadius() const { return m_radius; }


	virtual void SetHeight(float h);

	// 设置颜色
	void SetColor(const Rainbow::ColorQuad& cq) { SetColor(BlockVector(cq.r, cq.g, cq.b, cq.a)); }
	virtual void SetColor(BlockVector color) { m_Color = color; }
	void SetColor(MNSandbox::MNColor color) { SetColor(BlockVector(color._r, color._g, color._b, color._a)); }
	BlockVector GetColor() const { return m_Color; }

	// 设置宽度
	void SetStroke(int width, bool bRefresh = false);
	int GetStroke() const { return m_iStroke; }

	// 设置材质类型
	void SetMtlType(const CURVEFACEMTLTYPE& mtlType) { m_MtlType = mtlType; }
	CURVEFACEMTLTYPE GetMtlType() const { return m_MtlType; }

	void SetEffectShape(const SceneEffectShape& eSes) { m_eSes = eSes; }
	SceneEffectShape GetEffectShape() const { return m_eSes; }

	void SetOwnerNode(MNSandbox::AutoRef<MNSandbox::SandboxNode> sn) { m_ownerNode = sn; }
	MNSandbox::AutoRef<MNSandbox::SandboxNode> GetOwnerNode() { return m_ownerNode; }

protected:
	// 属性
	MNSandbox::MNCoord3f m_vCenter = { 0, 0, 0 };
	Rainbow::Quaternionf m_qRotation = { 0, 0, 0, 1 };
	MNSandbox::MNCoord3f m_vScale = { 1, 1, 1 };

	WCoord m_StartPos = WCoord(0, -1, 0);
	WCoord m_EndPos = WCoord(0, -1, 0);
	WCoord m_StartPosBlock = WCoord(0, -1, 0);
	WCoord m_EndPosBlock = WCoord(0, -1, 0);
	
	BlockVector m_Color = MakeBlockVector(255, 255, 255, 255);

	int m_iStroke = 1;
	float m_radius = 0.0;
	float m_originRadius = 0.0;
	float m_fHeight;

	CurveFace* m_CurveFaces = nullptr;
	MNSandbox::AutoRef<MNSandbox::SandboxNode> m_ownerNode = nullptr;

	CURVEFACEMTLTYPE m_MtlType = CURVEFACEMTL_TEXWHITE;

	SceneEffectShape m_eSes = SceneEffectShape::UNKNOWN;

	bool m_bShow = true;
	bool m_AlwaysShow = false; //需要一直显示

	//tolua_end
}; //tolua_exports

#endif
