#pragma once
/**
* file : SandboxListenerInterface
* func : 沙盒监听类
* by : chenzh
*/
#include "SandboxNotifyInterface.h"
#include "SandboxAutoRef.h"
#include "SandboxWeakRef.h"
#include "SandboxLuaFunction.h"


//#define SANDBOX_LISTENERS_USE_LIST

namespace MNSandbox {

	class Ref;
	class LuaFunction;
	namespace Lua {
		class LinkerData;
	}


	/**
	* Lua监听
	*/
	class EXPORT_SANDBOXDRIVERMODULE ListenerLua : public ListenerInterface
	{
		DECLARE_REFCLASS(ListenerLua)
	public:
		ListenerLua(AutoRef<LuaFunction> callback);
		virtual ~ListenerLua();

		/* lua function */
		const AutoRef<LuaFunction>& GetLuaFunction() const { return m_callback; }

		/* 触发 */
		//template<typename... Args>
		//bool Exec(Args... args)
		//{
		//	if (!m_callback)
		//	{
		//		// lua链接被释放了，可能是lua State 已经被销毁了
		//		ClearBindNotify(); // 从监听移除
		//		return false;
		//	}

		//	m_callback->CallLuaFunction(std::forward<Args>(args)...);
		//	if (GetRunTimes() > 0)
		//		CostRunTime();
		//	return false;
		//}

		//template<typename... Args>
		//bool Exec2(Args&&... args)
		//{
		//	if (!m_callback)
		//	{
		//		// lua链接被释放了，可能是lua State 已经被销毁了
		//		ClearBindNotify(); // 从监听移除
		//		return false;
		//	}

		//	m_callback->CallLuaFunction(std::forward<Args>(args)...);
		//	if (GetRunTimes() > 0)
		//		CostRunTime();
		//	return false;
		//}

		bool ExecLinkerdata(const AutoRef<Lua::LinkerData>& linkerdata)
		{
			if (!m_callback)
			{
				// lua链接被释放了，可能是lua State 已经被销毁了
				ClearBindNotify(); // 从监听移除
				return false;
			}

			m_callback->CallLuaFunctionLinkerData(linkerdata);
			if (GetRunTimes() > 0)
				CostRunTime();
			return false;
		}

	private:
		void OnCallbackRelease();

	private:
		/* 绑定的lua function */
		AutoRef<LuaFunction> m_callback;
		/* 监听 */
		AutoRef<ListenerInterface> m_listenCallback;
	};

	//////////////////////////////////////////////////////////////////////////////////////////

	/* 监听组，接口类 */
	class EXPORT_SANDBOXDRIVERMODULE ListenersInterface : public Ref
	{
	protected:
		ListenersInterface();
		ListenersInterface(NotifyInterface* notify);
	public:
		virtual ~ListenersInterface();

		virtual void Release() override { Clear(); }

		/* 所属通知 */
		void SetNotify(NotifyInterface* notify) { m_notify = notify; }
		NotifyInterface* GetNotify() const { return m_notify; }

		/* 监听 */
		void AddListener(const AutoRef<ListenerInterface>& v, int sort=ms_ListenerDefaultSort);
		/* 取消监听 */
		void RemoveListener(const AutoRef<ListenerInterface>& v);

		/* 刷新监听 */
		void Refresh();

		/* 是否有效 */
		bool IsValid() const { return !m_listenersNormal.empty() || !m_listenersSort.empty(); }

		/* 清空 */
		void Clear();

		/* 是否正在运行中 */
		bool IsRunning() const { return m_runningTimes != 0; }

		/* 获取绑定数量 */
		size_t GetListenerCount() const { return m_listenersNormal.size() + m_listenersSort.size(); }

#ifdef SANDBOX_DEBUG_LISTENER
		/* 填入调用位置 */
		void Dev_SetDebugMsg(const std::string& msg) { m_devMsg = msg; }
		const std::string& Dev_GetDebugMsg() { return m_devMsg; }
#endif

	protected:

		/* 监听当前状态 */
		enum ListenerState
		{
			ListenerState_Normal = 0,
			ListenerState_New, // 新增
			ListenerState_Remove, // 移除
		};
		/* 监听结构 */
		struct ListenerInfo
		{
			AutoRef<ListenerInterface> _listener;
			ListenerState _state;
			int _sort; // 默认值为 ms_ListenerDefaultSort(100)
		};
#ifdef SANDBOX_LISTENERS_USE_LIST
		typedef std::list<ListenerInfo> ListenerInfoGroup;
#else
		typedef std::vector<ListenerInfo> ListenerInfoGroup;
#endif

	private:
		void ReleaseSelfListeners(ListenerInfoGroup& container);
		void RemoveAllListeners(bool& found, const AutoRef<ListenerInterface>& v, ListenerInfoGroup& container);
		void RefreshAllListeners(ListenerInfoGroup& container);
		void RefreshAllListeners2(ListenerInfoGroup& container);
		void ClearAllListenersInRunning(ListenerInfoGroup& container);
		void ClearAllListenersOutRunning(ListenerInfoGroup& container);

	protected:
		static const unsigned m_runningMaxTimes = 100; // 默认循环执行最大次数100次 

		/* 执行次数 */
		unsigned m_runningTimes : 30;
		/* 是否需要刷新 */
		unsigned m_needRefresh : 1;
	protected:
		/* 所属的通知 */
		NotifyInterface* m_notify = nullptr;
		/* 监听组数据 */
		ListenerInfoGroup m_listenersNormal; // 排序100的都放在这里
		ListenerInfoGroup m_listenersSort; // 排序非100的都放在这里
#ifdef SANDBOX_DEBUG_LISTENER
		std::string m_devMsg;
#endif
	};

	//////////////////////////////////////////////////////////////////////////////////////////

	/* 监听组 */
	template<typename... Args>
	class Listeners : public ListenersInterface
	{
	public:
		using Super = ListenersInterface;
		using ListenerInfoGroup = typename Super::ListenerInfoGroup;
		using TListener = Listener<Args...>;
	public:
		Listeners()
			: Super()
		{}
		Listeners(NotifyInterface* notify)
			: Super(notify)
		{}
		virtual ~Listeners()
		{}

		/* 调用 */
		bool Call(const Args&... args)
		{
			if (!IsValid())
				return false;

			if (m_runningTimes >= m_runningMaxTimes)
				return false;

			m_linkerdata = nullptr; // 执行之前，先清理上一次的缓存

			// 如果有修改，在这里刷新修改
			if (m_needRefresh)
			{
				Refresh();
			}

			++m_runningTimes;
			bool result = OnCall(args...);
			--m_runningTimes;

			// 如果有修改，在这里刷新修改
			if (m_needRefresh)
			{
				Refresh();
			}

			m_linkerdata = nullptr;
			return result;
		}

		/* 调用回调 */
		bool CallListener(AutoRef<ListenerInterface> listener, const Args&... args) // 防止listener 中途释放，这里必须传实例，不要传引用
		{
			SANDBOX_ASSERT(listener);
			switch (listener->GetListenerType())
			{
			case ListenerInterface::LISTENERTYPE::NORMAL:
				return listener.StaticToCast<TListener>()->Exec(args...);
			case ListenerInterface::LISTENERTYPE::LUA:
				{
					if (!m_linkerdata)
					{
						m_linkerdata = LuaFunction::PushParamsToLinkerData(args...);
						if (!m_linkerdata)
						{
							SANDBOX_ASSERT(false);
							return false;
						}
					}
					return listener.StaticToCast<ListenerLua>()->ExecLinkerdata(m_linkerdata);
				}
			default:
				SANDBOX_ASSERT(false); break;
			}
			return false;
		}
		static bool CallListenerStatic(AutoRef<ListenerInterface> listener, const Args&... args) // 防止listener 中途释放，这里必须传实例，不要传引用
		{
			SANDBOX_ASSERT(listener);
			switch (listener->GetListenerType())
			{
			case ListenerInterface::LISTENERTYPE::NORMAL:
				return listener.StaticToCast<TListener>()->Exec(args...);
			case ListenerInterface::LISTENERTYPE::LUA:
				{
					AutoRef<Lua::LinkerData> linkerdata = LuaFunction::PushParamsToLinkerData(args...);
					return listener.StaticToCast<ListenerLua>()->ExecLinkerdata(linkerdata);
				}
			default:
				SANDBOX_ASSERT(false); break;
			}
			return false;
		}

	private:
		// call 执行
		bool OnCall(const Args&... args)
		{
			if (m_listenersSort.empty())
			{
				// 没有其他顺序
				if (ExecCallback(m_listenersNormal, args...))
					return true;
			}
			else
			{
				// 按顺序来
				unsigned idxSort = 0;
				bool normalOK = false;
#ifdef SANDBOX_LISTENERS_USE_LIST
				auto iterBeg = m_listenersSort.begin();
				auto iterEnd = m_listenersSort.end();
				for (auto iter = iterBeg; iter != iterEnd; ++iter)
				{
					ListenerInfo& info = *iter;
#else
				unsigned size = (unsigned)m_listenersSort.size();
				while (idxSort < size)
				{
					ListenerInfo& info = m_listenersSort.at(idxSort);
#endif

					// 大于100 先执行normal
					if (!normalOK && info._sort > ms_ListenerDefaultSort)
					{
						normalOK = true;
						if (ExecCallback(m_listenersNormal, args...))
							return true;
					}

					if (info._state == ListenerState_Normal)
					{
						if (CallListener(info._listener, args...))
							return true;
					}
					++idxSort;
				}
				if (!normalOK)
				{
					normalOK = true;
					if (ExecCallback(m_listenersNormal, args...))
						return true;
				}
			}
			return false; // 失败
		}

		// 执行
		bool ExecCallback(ListenerInfoGroup& container, const Args&... args)
		{
			unsigned idx = 0;
#ifdef SANDBOX_LISTENERS_USE_LIST
			auto iterBeg = container.begin();
			auto iterEnd = container.end();
			for (auto iter = iterBeg; iter != iterEnd; ++iter)
			{
				ListenerInfo& info = *iter;
#else
			unsigned size = (unsigned)container.size();
			while (idx < size)
			{
				ListenerInfo& info = container.at(idx);
#endif
				if (info._state == ListenerState_Normal)
				{
					if (CallListener(info._listener, args...))
						return true;
				}
				++idx;
			}
			return false;
		}

	private:
		AutoRef<Lua::LinkerData> m_linkerdata; // lua 执行参数缓存
	};

	//////////////////////////////////////////////////////////////////////////////////////////

	/* 监听组，特例化没有参数的类 */
	template<>
	class Listeners<> : public ListenersInterface
	{
	public:
		using Super = ListenersInterface;
		using ListenerInfoGroup = typename Super::ListenerInfoGroup;
		using TListener = Listener<>;
	public:
		Listeners()
			: Super()
		{}
		Listeners(NotifyInterface* notify)
			: Super(notify)
		{}
		virtual ~Listeners() = default;

		/* 调用 */
		bool Call()
		{
			if (!IsValid())
				return false;

			if (m_runningTimes >= m_runningMaxTimes)
				return false;

			// 如果有修改，在这里刷新修改
			if (m_needRefresh)
			{
				Refresh();
			}

			++m_runningTimes;
			bool result = OnCall();
			--m_runningTimes;

			// 如果有修改，在这里刷新修改
			if (m_needRefresh)
			{
				Refresh();
			}
			return result;
		}

		/* 调用回调 */
		bool CallListener(AutoRef<ListenerInterface> listener) // 防止listener 中途释放，这里必须传实例，不要传引用
		{
			SANDBOX_ASSERT(listener);
			if (!listener) return false;
			switch (listener->GetListenerType())
			{
			case ListenerInterface::LISTENERTYPE::NORMAL:
				return listener.StaticToCast<TListener>()->Exec();
			case ListenerInterface::LISTENERTYPE::LUA:
				return listener.StaticToCast<ListenerLua>()->ExecLinkerdata(nullptr);
			default:
				SANDBOX_ASSERT(false); break;
			}
			return false;
		}
		static bool CallListenerStatic(AutoRef<ListenerInterface> listener) // 防止listener 中途释放，这里必须传实例，不要传引用
		{
			SANDBOX_ASSERT(listener);
			switch (listener->GetListenerType())
			{
			case ListenerInterface::LISTENERTYPE::NORMAL:
				return listener.StaticToCast<TListener>()->Exec();
			case ListenerInterface::LISTENERTYPE::LUA:
				return listener.StaticToCast<ListenerLua>()->ExecLinkerdata(nullptr);
			default:
				SANDBOX_ASSERT(false); break;
			}
			return false;
		}

	private:
		// call 执行
		bool OnCall()
		{
			if (m_listenersSort.empty())
			{
				// 没有其他顺序
				if (ExecCallback(m_listenersNormal))
					return true;
			}
			else
			{
				// 按顺序来
				unsigned idxSort = 0;
				bool normalOK = false;
#ifdef SANDBOX_LISTENERS_USE_LIST
				auto iterBeg = m_listenersSort.begin();
				auto iterEnd = m_listenersSort.end();
				for (auto iter = iterBeg; iter != iterEnd; ++iter)
				{
					ListenerInfo& info = *iter;
#else
				unsigned size = (unsigned)m_listenersSort.size();
				while (idxSort < size)
				{
					ListenerInfo& info = m_listenersSort.at(idxSort);
#endif

					// 大于100 先执行normal
					if (!normalOK && info._sort > ms_ListenerDefaultSort)
					{
						normalOK = true;
						if (ExecCallback(m_listenersNormal))
							return true;
					}

					if (info._state == ListenerState_Normal)
					{
						if (CallListener(info._listener))
							return true;
					}
					++idxSort;
				}
				if (!normalOK)
				{
					normalOK = true;
					if (ExecCallback(m_listenersNormal))
						return true;
				}
			}
			return false; // 失败
		}

		// 执行
		bool ExecCallback(ListenerInfoGroup& container)
		{
			unsigned idx = 0;
#ifdef SANDBOX_LISTENERS_USE_LIST
			auto iterBeg = container.begin();
			auto iterEnd = container.end();
			for (auto iter = iterBeg; iter != iterEnd; ++iter)
			{
				ListenerInfo& info = *iter;
#else
			unsigned size = (unsigned)container.size();
			while (idx < size)
			{
				ListenerInfo& info = container.at(idx);
#endif
				if (info._state == ListenerState_Normal)
				{
					if (CallListener(info._listener))
						return true;
				}
				++idx;
			}
			return false;
		}
	};

}
