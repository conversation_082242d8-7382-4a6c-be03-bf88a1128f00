
#ifndef __I_CLIENT_ACTOR_LOCOMOTION_H__
#define __I_CLIENT_ACTOR_LOCOMOTION_H__
#include "OgreWCoord.h"
#include "Math/Vector3f.h"
#include "world_types.h"
#include "defdata.h"

namespace MNSandbox
{
	class EventObjectManager;
}
namespace game
{
	namespace hc
	{
		class PB_VehicleMoveHC;
	}
}
class IClientActor;

/* ActorLocoMotion 基类 */
class EXPORT_SANDBOXENGINE IActorLocoMotion
{
public:
	IActorLocoMotion() {}
	virtual ~IActorLocoMotion() {}
	/*
		获取Event2
	*/
	virtual MNSandbox::EventObjectManager* GetEvent2() = 0;
	virtual int GetBoundSize() = 0;
	virtual int GetBoundHeight() = 0;

	virtual void gotoPosition(const WCoord& pos, float yaw, float pitch) = 0;
	virtual int GetYOffset() = 0;
	virtual bool GetInLava() = 0;
	virtual bool GetInHurt() = 0;
	virtual void SetRotateYaw(float yaw) = 0;
	virtual float GetRotateYaw() = 0;
	virtual float GetPrevRotateYaw() = 0;
	virtual float GetRotationPitch() = 0;
	virtual bool GetIsBlocked() = 0;
	virtual Rainbow::Vector3f GetMotion() = 0;
	virtual TickPosition GetTickPosition() = 0;

	virtual void SetMotion(const Rainbow::Vector3f& motion) = 0;
	virtual void setRotatePitch(float p) = 0;
	virtual void prepareTick() = 0;
	virtual WCoord& getPosition() = 0;
	virtual void SetPosition(const WCoord& positon) = 0;
	virtual bool IsPhysActor() = 0;
	virtual Rainbow::WorldPos getFramePosition() = 0;
	virtual bool getOnGround() = 0;
	virtual void getCollideBox(CollideAABB& box) = 0;
	virtual void getCollideBoxs(std::vector<CollideAABB>& boxs) = 0;
	virtual void getMultiTypeCollidBoxs(std::vector<TypeCollideAABB>& boxs) = 0;
	virtual void combatAttackCollideBox(CollideAABB& box) = 0;
	virtual bool needFullRotation() = 0;
	virtual void getRotation(Rainbow::Quaternionf& quat) = 0;

	virtual void BroadVehicleAssembleLocoMotionData(game::hc::PB_VehicleMoveHC* vehicleMoveHC, IClientActor* actor) = 0;
	virtual void SetVehicleAssembleLocoMotionData(std::vector<WCoord>& m_vLastPosition, bool& syncpos, std::vector<Rainbow::Quaternionf>& m_vLastRot, bool& rot_enough, float QUAT_SQ_VEHICLE, int DPSQ) = 0;

	virtual void setTarget(const WCoord& target, float speed) = 0;
	virtual void setJumping(bool b) = 0;

	virtual bool getJumping() = 0;
	virtual Rainbow::Vector3f getLookDir() = 0;
};

#endif
