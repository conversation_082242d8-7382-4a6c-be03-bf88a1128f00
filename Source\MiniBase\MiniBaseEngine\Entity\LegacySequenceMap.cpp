#include "LegacySequenceMap.h"
#include "Utilities/RuntimeStatic.h"
#include "File/FileManager.h"
#include "GameStatic.h"
#include "File/FileUtilities.h"
namespace Rainbow
{
	static char* ReadInt(char* buffer, int& v)
	{
		assert(buffer != NULL);
		char* pseq = strchr(buffer, ',');
		if (pseq)
		{
			*pseq = 0;
			v = atoi(buffer);
			return pseq + 1;
		}
		else
		{
			v = atoi(buffer);
			return NULL;
		}
	}
	static char* ReadFloat(char* buffer, float& v)
	{
		assert(buffer != NULL);
		char* pseq = strchr(buffer, ',');
		if (pseq)
		{
			*pseq = 0;
			v = atof(buffer);
			return pseq + 1;
		}
		else
		{
			v = atof(buffer);
			return NULL;
		}
	}

	SequenceMap::SequenceMap()
	{
		Init("csvdef/utf8/animmap.csv");
	}

	void SequenceMap::Init(const char* path)
	{
		AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path, true);
		//if (fp == NULL) // warning: comparison between NULL and non-pointer ('bool' and NULL)
		if (!fp)
		{
			return;
		}

		char buffer[1024];
		while (!fp->Eof())
		{
			if (fp->ReadLine(buffer, sizeof(buffer)) == 0) continue;

			SeqDesc desc;
			desc.seqid = 0;
			buffer[sizeof(buffer) - 1] = 0;

			char* pbuf = ReadInt(buffer, desc.seqid);
			pbuf = ReadInt(pbuf, desc.loopmode);
			pbuf = ReadInt(pbuf, desc.priority);
			pbuf = ReadInt(pbuf, desc.subseq[0]);
			pbuf = ReadInt(pbuf, desc.subseq[1]);
			pbuf = ReadInt(pbuf, desc.replay);
			pbuf = ReadInt(pbuf, desc.halfbody);
			pbuf = ReadInt(pbuf, desc.freeView);
			pbuf = ReadFloat(pbuf, desc.speed);
			pbuf = ReadInt(pbuf, desc.nobreak);
			if (desc.speed == 0) desc.speed = 1.0f;
			if (desc.seqid > 0) m_SeqMap[desc.seqid] = desc;
		}
	}

	SequenceMap::~SequenceMap()
	{
	}

	bool SequenceMap::CanBreak(int seq)
	{
		auto seqdesc = findSequenceDesc(seq);
		if (seqdesc)
			return seqdesc->nobreak == 0;
		return true;
	}

	SequenceMap::SeqDesc* SequenceMap::findSequenceDesc(int seq)
	{
		std::map<int, SeqDesc>::iterator iter = m_SeqMap.find(seq);
		if (iter == m_SeqMap.end()) return NULL;
		else return &iter->second;
	}

	SequenceMap::SeqDesc* SequenceMap::getOrCreateSequenceDesc(int seq)
	{
		std::map<int, SeqDesc>::iterator iter = m_SeqMap.find(seq);
		if (iter == m_SeqMap.end())
		{
			SeqDesc desc;
			desc.seqid = seq;
			desc.loopmode = 0;
			desc.priority = 2;
			desc.subseq[0] = -1;
			desc.subseq[1] = -1;
			desc.replay = 0;
			desc.halfbody = 0;
			m_SeqMap[desc.seqid] = desc;
			return &m_SeqMap[desc.seqid];
		}
		else return &iter->second;
	}

	void SequenceMap::copySequenceDesc(int srcSeq, int desSeq)
	{
		if (findSequenceDesc(desSeq))
			return;

		SeqDesc* srcDesc = findSequenceDesc(srcSeq);
		if (!srcDesc)
			return;

		SeqDesc desDesc;
		desDesc.seqid = desSeq;
		desDesc.loopmode = srcDesc->loopmode;
		desDesc.priority = srcDesc->priority;
		desDesc.subseq[0] = srcDesc->subseq[0];
		desDesc.subseq[1] = srcDesc->subseq[1];
		desDesc.replay = srcDesc->replay;
		desDesc.halfbody = srcDesc->halfbody;

		m_SeqMap[desSeq] = desDesc;
	}

	MINIW::GameStatic<SequenceMap> s_SequenceMap(MINIW::kInitManual);
	SequenceMap& GetSequenceMap()
	{
		return *s_SequenceMap.EnsureInitialized();
	}
}