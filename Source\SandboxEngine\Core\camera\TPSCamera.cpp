#include "IPlayerControl.h"
#include "IClientActor.h"
#include "TPSCamera.h"
#include "OgrePrerequisites.h"
#include "OgreScriptLuaVM.h"
#include "CameraManager.h"
#include "GameCamera.h"
#include "IClientGameInterface.h"
#include "InputInfo.h"
#include "IClientGameManagerInterface.h"
#include "GameInfoProxy.h"
#include "ClientInfoProxy.h"
#include "ui_scriptfunc.h"
#include "PlayManagerInterface.h"

using namespace MINIW;
using namespace Rainbow;

TPSCamera::TPSCamera(CameraManager* cameraManager): CameraBase(cameraManager), m_MoveStrafe(0), m_MoveForward(0),
                                                    m_RotX(0), m_RotY(0)
{
	m_MoveSpeed = 2;
	m_RotSpeed = 3;
}

TPSCamera::~TPSCamera()
{

}

void TPSCamera::update(float deltaSeconds)
{
	//m_CameraTransform.pos = GetIPlayerControl()->GetPlayerControlPosition().toVector3() + Rainbow::Vector3f(0,300,0);

/*
	Rainbow::Vector3f offset(0, -40.0f, 0);
	float dist = (GetIPlayerControl()->getLocoMotion()->m_RotateYaw / -80.0f);
	if (dist < 0) dist = 0;
	dist = (600.0f - dist*450.0f)*m_DistMultiply;
	WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -m_LookDir, dist);

	m_CameraTransform.pos = pos.toVector3();
	m_CameraTransform*/
	//pCamera->setPosition(pos);
	//pCamera->setRotation(quat)

	m_CameraTransform.pos += m_MoveForward * GetForward() * m_MoveSpeed * deltaSeconds;
	m_CameraTransform.pos += m_MoveStrafe * GetRight() * m_MoveSpeed * deltaSeconds;
	// todo_yanxiongjian
	//m_CameraTransform.rot * Quaternionf::Euler(m_RotY * deltaSeconds, m_RotX * deltaSeconds, 0);
}

int TPSCamera::onInputEvent(const Rainbow::InputEvent &event)
{
	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);

	m_MoveStrafe = 0;
	m_MoveForward = 0;
	// todo ʹ�� event.delta ���� dx/y
	if (event.type == InputEvent::kMouseMove)
	{
		const int dx = event.mousePosition.x - w / 2;
		const int dy = event.mousePosition.y - h / 2;

		m_RotX = float(dx)*m_pCameraManager->m_Sensitive / w;
		m_RotY = float(dy)*m_pCameraManager->m_Sensitive / h;
	}
	else if (event.type == InputEvent::kKeyDown)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveStrafe = 1.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward = -1.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe = -1.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe = 1.0f;
		}
	}

	return Rainbow::INPUTMSG_PASS;
}

EditingCustomCamera::EditingCustomCamera(CameraManager* cameraManager) : TPSCamera(cameraManager), m_IsInit(false),
                                                                         m_PreRotateY(0),
                                                                         m_PreRotateX(0)
{
	m_RotY = 0.0f;
	m_RotX = 0.0f;
	m_MoveStrafe = 0.0f;
	m_MoveForward = 0.0f;
	m_MoveUp = 0.0f;

	m_RotateID = -1;
}

EditingCustomCamera::~EditingCustomCamera()
{

}

void EditingCustomCamera::update(float deltaSeconds)
{
	m_pCameraManager->m_GameCamera->update(deltaSeconds, GetIPlayerControl()->getIWorld());

	if (abs(m_MoveForward) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveForward(m_MoveForward);
		GetIPlayerControl()->setCameraConfigPosition();
	}
	if (abs(m_MoveStrafe) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveSide(m_MoveStrafe);
		GetIPlayerControl()->setCameraConfigPosition();
	}
	if (abs(m_MoveUp) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveUp(m_MoveUp);
		GetIPlayerControl()->setCameraConfigPosition();
	}

}

int EditingCustomCamera::onInputEvent(const Rainbow::InputEvent &event)
{
	if (GetClientInfoProxy()->isMobile())
	{
		onTouchInputEvent(event);
	}
	else
	{
		onPcInputEvent(event);
	}

	return INPUTMSG_HANDLED;
}

int EditingCustomCamera::onPcInputEvent(const Rainbow::InputEvent &event)
{
	m_RotX = m_RotY = 0.0f;

	if (event.type == InputEvent::kMouseMove)
	{
		// todo use event.delta replace with dx/dy
		int w, h = 0;
		GetClientInfoProxy()->getClientWindowSize(w, h);
		const int dx = event.mousePosition.x - m_PreMousePos.x;
		const int dy = event.mousePosition.y - m_PreMousePos.y;

		LOG_INFO("camera rotate: p1(%d, %d), p2(%d, %d)", event.mousePosition.x, event.mousePosition.y, m_PreMousePos.x, m_PreMousePos.y);

		float pcSensity = 1.0f;
		bool isSightMode = false;
		bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<bool&, float&>("PlayerCtr_MouseMove", isSightMode, pcSensity);
		Assert(result);
		if (isSightMode)
		{
			if (m_IsInit)
			{
				if (abs(dx) >= 2 || abs(dy) >= 2)
				{
					m_RotX = float(dx)* pcSensity *0.75f*0.75f*2.0f / w;
					m_RotY = float(dy)* pcSensity *0.75f*0.75f*2.0f / h;

					m_pCameraManager->m_GameCamera->rotate(m_RotX, m_RotY);
				}
			}
			m_PreMousePos.x = event.mousePosition.x;
			m_PreMousePos.y = event.mousePosition.y;

			//if (abs(m_PreMousePos.x - w) < 4)
			//{
			//	m_PreMousePos.x -= 50;
			//}
			//else if (abs(m_PreMousePos.x - 0) < 4)
			//{
			//	m_PreMousePos.x += 50;
			//}
			m_IsInit = true;
		}
	}
	else if (event.type == InputEvent::kKeyDown)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveForward += 1.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward += -1.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe += -1.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe += 1.0f;
		}
		else if (event.keycode == SDLK_SPACE)
		{
			m_MoveUp += 1.0f;
		}
		// todo �⴦�߼��Ͳ�Ӧ��д��if/else�У����ź����
		else if (event.keycode == SDLK_LSHIFT)
		{
			m_MoveUp += -1.0f;
		}
		else if (event.keycode == SDLK_BACKQUOTE)
		{
			//pccontrol中会处理~按键
			//GetIPlayerControl()->getPCControl()->setSightModel(!GetIPlayerControl()->getPCControl()->isSightMode());
			//if (GetClientGameManagerPtr()->getCurGame()->isInGame())
			//{
			//	((SurviveGame*)GetClientGameManagerPtr()->getCurGame())->ResetOperateState();
			//	bool isGuideState = true;
			//	MNSandbox::GetGlobalEvent().Emit<bool&, const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState, "guidekey");
			//	if (!isGuideState)
			//	{
			//		MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState", "guidekey", true);
			//		MINIW::ScriptVM::game()->callFunction("GuideKey_Finish", "");
			//	}
			//}
			m_IsInit = false;
		}
		else if (event.keycode == SDLK_ESCAPE)
		{
			int tmp = GetClientInfoProxy()->getGameData("hideui");
			MINIW::ScriptVM::game()->callFunction("AccelKey_Escape", "b", dynamic_cast<IClientActor*>(GetIPlayerControl())->isDead());
			if (tmp)
			{
				if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
				{
					GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
				}
			}
		}
	}
	else if (event.type == InputEvent::kKeyUp)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveForward -= 1.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward -= -1.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe -= -1.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe -= 1.0f;
		}
		else if (event.keycode == SDLK_SPACE)
		{
			m_MoveUp -= 1.0f;
		}
		else if (event.keycode == SDLK_LSHIFT)
		{
			m_MoveUp -= -1.0f;
		}
	}

	return true;
}

int EditingCustomCamera::onTouchInputEvent(const Rainbow::InputEvent &event)
{
	GetIPlayerControl()->onTouchInputEvent(event);


	int scale = UILib::GetScreenUIScale();

	if (event.type == InputEvent::kMouseDown)
	{
		int x = event.mousePosition.x;
		int y = event.mousePosition.y;
		int flyType = GetISandboxActorSubsystem()->TouchCtlCheckFlyArea(x, y, scale);
		if (flyType == 1)
		{
			m_MoveUp = 1.0f;
		}
		else if (flyType == -1)
		{
			m_MoveUp = -1.0f;
		}
		else
		{
			m_MoveUp = 0;
		}
	}
	else if (event.type == InputEvent::kMouseUp)
	{
		m_MoveUp = 0;
	}
	//m_MoveUp = 0.0f;
	//if (GetIPlayerControl()->getTouchControl()->GetButton(BUTTON_FLYUP))
	//{
	//	m_MoveUp = 1.0f;
	//}
	//else if (GetIPlayerControl()->getTouchControl()->GetButton(BUTTON_FLYDOWN))
	//{
	//	m_MoveUp = -1.0f;
	//}
	float moveforward = m_MoveForward;
	float moveStrafe = m_MoveStrafe;
	bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<float&, float&>("Playercontrol_onTouchInput", moveforward, moveStrafe);
	Assert(result);

	return true;
}

void EditingCustomCamera::onSwitchTo()
{
	m_IsInit = false;

	m_RotY = 0.0f;
	m_RotX = 0.0f;
	m_MoveStrafe = 0.0f;
	m_MoveForward = 0.0f;
	m_MoveUp = 0.0f;
	
	// ���˺������ǰλ�ü������
	//m_CameraTransform.pos = m_pCameraManager->m_GameCamera->getPosition();
	//m_CameraTransform.rot = m_pCameraManager->m_GameCamera->getRotation();

	bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<>("Playercontrol_Switch");
	Assert(result);
}