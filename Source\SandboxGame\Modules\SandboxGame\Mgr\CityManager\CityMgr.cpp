#include "CityMgr.h"
#include "world_types.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include "OgreUtils.h"
#include "json/jsonxx.h"
#include "world.h"
#include "EcosysBuildHelp.h"
#include "BuildCreater.h"
#include "BuildMgr.h"
#include "PlayerControl.h"
#include "ChunkGenerator.h"
#include "SandBoxManager.h"
#include "SandboxCoreDriver.h"
#include "CityBuildCreater.h"
#include "ClientPlayer.h"
#include "ActorManager.h"
#include "backpack.h"
#include "chunk.h"
#include "WorldManager.h"
#include "EcosysBuildHelp.h"
#include "EcosysManager.h"
#include "EcosysUnit_City.h"
#include "ChunkGenerator.h"
#include "EcosysUnit_RoadBuild.h"
#include "File/FileUtilities.h"
#include "Platforms/PlatformInterface.h"
#include "Play/gameplay/ChestMgr.h"

CityMgr::CityMgr(World* world):m_world(world)
{
	loadCityConfig();
}

void CityMgr::loadCityConfig()
{
	jsonxx::Object obj;
	core::string data;
	auto path = Rainbow::GetFileManager().ToFullPath("minigame/citybuild/cfg/CityConfig.json");
	if (Rainbow::LoadFileAsString(path.c_str(), data))
	{
		obj.parse(data.c_str());
	}

	// obj << "biomeConfig" << "biomeConfig.json";
	// obj << "otherConfig" << "otherConfig.json";
	// obj << "singleBuildConfig" << "singleBuildConfig.json";
	if (CityConfig::getSingletonPtr())
	{
		CityConfig::getSingletonPtr()->Load(obj, nullptr);
	}

	m_globalProtectedRange = CityConfig::getSingletonPtr()->GetCityProtectedRange();
}

bool CityMgr::addNewCity(const CityData& data)
{
	if (!m_world)
	{
		return false;
	}
	if (std::find_if(m_allData.begin(), m_allData.end(), [leftDown = data.leftDown](const CityData& creater) {
		return creater.leftDown == leftDown;
	}) != m_allData.end())
	{
		return false;
	}
	if (m_allData.size() >= CityConfig::getSingletonPtr()->GetMaxCityCount())
	{
		return false;
	}

	m_allData.push_back(data);
	m_datas.insert(m_datas.begin(), CityBuildPlace(data));
	m_isChanged = true;
	return true;
}

void CityMgr::tick()
{
	if (!m_world)
	{
		return;
	}
	if (m_datas.empty())
	{
		return;
	}
	auto pcreator = m_datas.end() - 1;
	pcreator->tick(m_world);
	if (pcreator->isEnd())
	{
		m_datas.erase(pcreator);
	}
	m_isChanged = true;
}

bool CityMgr::loadCityFile(long long owid, long long uin, int mapId, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(m_world->getMapSpecialType());

	char path[512] = { 0 };
	snprintf(path, sizeof(path), "%s/w%lld/cityData/city_%d.fb", rootpath.c_str(), owid, mapId);
	int buflen;
	void* buf = ReadWholeFile(path, buflen);
	if (buf != NULL)
	{
		flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
		if (!FBSave::VerifyCityFBDataBuffer(verifier))
		{
			LOG_WARNING("verify BuildMgr flatbuffer error: path=%s", path);
			return false;
		}

		const FBSave::CityFBData* ts = FBSave::GetCityFBData(buf);
		load(ts);
		GetEcosysUnitRoadBuild().load(m_world, ts);
		GetEcosysUnitCityBuild().onFileLoad(m_world);
		free(buf);

		GetEcosysUnitRoadBuild().addToWorld(m_world);
	}
	return true;
}

flatbuffers::Offset<FBSave::CityFBData> CityMgr::save(flatbuffers::FlatBufferBuilder& builder)
{
	std::vector<flatbuffers::Offset<FBSave::CitySaveData>> alldataFB;
	for (const auto& p : m_allData)
	{
		alldataFB.push_back(FBSave::CreateCitySaveData(builder, p.leftDown.x, p.leftDown.z, p.height, p.cityNum, p.range.x(), p.range.y(), p.configIndex));
	}
	std::vector<flatbuffers::Offset<FBSave::CityRunData>> rundataFB;
	for (const auto& p : m_datas)
	{
		rundataFB.push_back(p.save(builder));
	}
	std::vector<flatbuffers::Offset<FBSave::CityRoadNodeData>> roaddataFB;
	GetEcosysUnitRoadBuild().save(builder, roaddataFB);

	std::vector<flatbuffers::Offset<FBSave::CitySingleBuildData>> singleBuildDataFB;
	for (const auto& p : m_singleBuildData)
	{
		const auto& data = p.second;
		std::vector<flatbuffers::Offset<FBSave::SingleBuildData>> buildDataFB;
		for (const auto& p2 : data.buildData)
		{
			buildDataFB.push_back(FBSave::CreateSingleBuildData(builder, p2.x, p2.z, p2.rangeX, p2.rangeZ, p2.bRangeX, p2.bRangeZ));
		}
		singleBuildDataFB.push_back(FBSave::CreateCitySingleBuildData(builder, builder.CreateString(data.buildName), builder.CreateVector(buildDataFB)));
	}
	std::vector<int> cidxList;
	for (auto item : m_world->GetRespawnChunkList())
	{
		// 使用固定的映射码：10001 把SOC地图大小范围[-500,500]映射到[99501,10501]
		// 编码公式：encoded = original + 10001 (使用多一个1确保不会有0值冲突)
		// 避免了负值位保存时出现异常
		int encodedX = item.x + 10001; 
		int encodedZ = item.z + 10001;
		
		// 验证编码后的值在有效范围内
		if (encodedX < 1 || encodedX > 20001 || encodedZ < 1 || encodedZ > 20001) {
			continue; // 跳过无效数据
		}
		
		// 高16位存储z，低16位存储x
		cidxList.push_back((encodedZ << 16) | (encodedX & 0xffff));
	}

	return FBSave::CreateCityFBData(builder, builder.CreateVector(alldataFB), builder.CreateVector(rundataFB), 
		builder.CreateVector(roaddataFB), builder.CreateVector(singleBuildDataFB), builder.CreateVector(cidxList));
}

void CityMgr::load(const FBSave::CityFBData* src)
{
	if (!src) return;
	//获取所有地图数据
	{
		const auto* allDataFB = src->allData();
		if (allDataFB)
		{
			int size = allDataFB->size();
			m_allData.reserve(size);
			for (int i = 0; i < size; i++)
			{
				CityData data;
				const auto* dataFB = allDataFB->Get(i);
				if (dataFB)
				{
					data.leftDown.x = dataFB->leftDownX();
					data.leftDown.z = dataFB->leftDownZ();
					data.height = dataFB->height();
					data.cityNum = dataFB->cityNum();
					data.range.x() = dataFB->rangeX();
					data.range.y() = dataFB->rangeZ();
					data.configIndex = dataFB->configIndex();
				}
				m_allData.push_back(data);
			}
		}
	}
	//获取所有运行数据
	{
		const auto* runDataFB = src->runData();
		if (runDataFB)
		{
			int size = runDataFB->size();
			m_datas.resize(size);
			for (int i = 0; i < size; i++)
			{
				m_datas[i].load(runDataFB->Get(i));
			}
		}
	}
	{
		const auto* singleBuildDataFB = src->singleBuildData();
		if (singleBuildDataFB)
		{
			int size = singleBuildDataFB->size();
			for (int i = 0; i < size; i++)
			{
				SaveSingleBuildData singledata;
				const auto* singledataFB = singleBuildDataFB->Get(i);
				singledata.buildName = singledataFB->buildName()->c_str();
				singledata.buildData.reserve(singledataFB->buildData()->size());
				for (int j = 0; j < singledataFB->buildData()->size(); j++)
				{
					const auto* builddataFB = singledataFB->buildData()->Get(j);
					int x = builddataFB->leftDownX();
					int z = builddataFB->leftDownZ();
					int rangeX = builddataFB->rangeX();
					int rangeZ = builddataFB->rangeZ();
					int brangex = builddataFB->brangex();
					int brangez = builddataFB->brangez();
					singledata.buildData.push_back(SaveBuildData(x, z, rangeX, rangeZ, brangex, brangez));
				}
				m_singleBuildData[singledata.buildName] = singledata;
			}
		}
	}

	auto SOCRespawnCIdxs = src->spawnCidxList();
	if (SOCRespawnCIdxs)
	{
		for (size_t i = 0; i < SOCRespawnCIdxs->size(); i++)
		{
			auto cidxitem = SOCRespawnCIdxs->Get(i);
			// 解码坐标：使用固定的解码公式
			int encodedX = cidxitem & 0xffff;          // 提取低16位
			int encodedZ = (cidxitem >> 16) & 0xffff;  // 提取高16位
			
			// 解码公式：original = encoded - 10001
			int cx = encodedX - 10001;
			int cz = encodedZ - 10001;
			
			// 验证解码后的坐标在有效范围内
			if (cx >= -10000 && cx <= 10000 && cz >= -10000 && cz <= 10000) {
				m_world->AddRespawnChunkIdx(cx, cz);
			}
		}
	}
#ifdef DEDICATED_SERVER
	// TODO 上报城市保护区
#endif

	onCityGenEnd();
}

bool CityMgr::saveFile(bool force)
{
	if (!m_world)
	{
		return false;
	}

	// 没有变化就不做保存文件操作了，直接返回true
	if (!m_isChanged && !force)
	{
		return true;
	}

	if (m_world->isRemoteMode())
	{
		return true;
	}
	m_isChanged = false;
	std::string rootpath = GetWorldRootBySpecialType(m_world->getMapSpecialType());
	char path[256];
	flatbuffers::FlatBufferBuilder builder;
	auto td = save(builder);
	builder.Finish(td);
	sprintf(path, "%s/w%lld/cityData/city_%d.fb", rootpath.c_str(), m_world->getOWID(), m_world->getCurMapID());
	//return Rainbow::GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize(), true);
	m_thread.addSaveRequest(builder.GetBufferPointer(), builder.GetSize(), path);
	return true;
}

void CityMgr::leaveWorld()
{
	saveFile();
}

void CityMgr::buildBase(WCoord playerPos)
{
	if (!m_world)
	{
		return;
	}
	if (!(m_world->getBuildMgr()))
	{
		return;
	}
	if (!CityConfig::getSingletonPtr())
	{
		return;
	}
	const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().baseData;
	const auto& buildParam = buildData.m_paramter;
	//玩家要在基地的基地方块里, 我们设定玩家一定在基地方块的左下角
	//基地方块5x5, 
	BuildData data;
	if (isValidDir(buildData.m_dir))
	{
		data.dir = buildData.m_dir;
	}
	else
	{
		data.dir = DIR_NEG_Z;
	}
	//data.id_1 = "cityBuild";
	data.id_2 = BuildCreaterType_City;
	WCoord blockpos = CityConfig::getSingletonPtr()->getBaseBottomLeft();
	//{
	//	blockpos.x = playerPos.x - (buildData.m_size.x() / 2) + 2;
	//	blockpos.y = playerPos.y - buildData.m_size.y() - 1;
	//	blockpos.z = playerPos.z - (buildData.m_size.z() / 2) + 2;
	//}
	data.startPos = blockpos;
	data.fileName = buildData.m_fileName;
	data.paramter = buildParam;
	if (data.startPos.y < 63)
	{
		data.paramter.waterFill = false;
	}
	CityBuildCreater* creater = static_cast<CityBuildCreater*>(dynamic_cast<BuildMgr*>(m_world->getBuildMgr())->addBuild(data));
	if (creater)
	{
		creater->setBuildIndex(CityBuildBase);
	}
	//新需求: 在出生点周围刷个副本
	if (m_world->getChunkProvider() && m_world->getChunkProvider()->getBiomeManager())
	{
		//我们选择一个副本
		std::vector<ChunkSpecialBiomeData> cityDatas;
		//找到范围内的所有城市
		if (m_world->getChunkProvider()->getBiomeManager()->findSpecialBiomeOn(BlockDivSection(g_WorldMgr->getSpawnPoint().x), BlockDivSection(g_WorldMgr->getSpawnPoint().z), 50, 50, TerrainSpecialDataType::TerrainSpecialData_SpecialSubOrderBiome, cityDatas))
		{
			WCoord retPos;
			if (GetEcosysUnitCityBuild().addSpecialBuildToWorld(m_world->getWorldProxy(), g_WorldMgr->getSpawnPoint(), cityDatas, retPos))
			{
				if (creater)
				{
					creater->m_reportPos = retPos;
					creater->m_reportPos.y = 0;
				}
			}
		}
	}
}

void CityMgr::checkFindCity(const std::vector<ChunkSpecialBiomeData>& specialBiome, WORLD_ID uin)
{
	if (!m_world)
	{
		return;
	}
	if (m_world->isRemoteMode())
	{
		return;
	}
	if (m_world->getCurMapID() != 0)
	{
		return;
	}
	jsonxx::Object ret;
	{
		jsonxx::Array arr;
		for (const auto& data : specialBiome)
		{
			if (data.type == SpecialBiome_City)
			{
				jsonxx::Object obj;
				obj << "x" << (data.leftDown.x + data.range.x() / 2) * CHUNK_BLOCK_X;
				obj << "z" << (data.leftDown.z + data.range.y() / 2) * CHUNK_BLOCK_Z;
				arr << obj;
			}
		}
		ret << "data" << arr;
	}
	//是本机直接处理
	if (g_pPlayerCtrl && g_pPlayerCtrl->getUin() == uin)
	{
		//发事件
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_String("data_str", ret.json_nospace());
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_CITY_DATA_LOAD", sandboxContext);
		}
	}
	else
	{
		int size = 0;
		unsigned char* p = NULL;
		ret.saveBinary(p, size);
		SandBoxManager::getSingletonPtr()->sendToClient(uin, (char*)("CITY_SEARCH_CLIENT"), p, size);
		free(p);
	}
}

bool CityMgr::findNearCity(long long uin, WCoord pos, int range)
{
	if (!m_world) return false;
	if (m_world->isRemoteMode()) return false;
	if (TERRAIN_NORMAL != m_world->getTerrainType())
	{
		//直接报没找到
		std::vector<ChunkSpecialBiomeData> specialBiome;
		checkFindCity(specialBiome, uin);
		return true;
	}
	if (!m_world->getChunkProvider()) return false;
	m_world->getChunkProvider()->findCityTerrain(CoordDivSection(pos.x), CoordDivSection(pos.z), range, uin);
	return true;
}

WCoord CityMgr::genCityPrecious(long long uin)
{
	WCoord ret(0, -1, 0);
	if (!CityConfig::getSingletonPtr())
	{
		return ret;
	}
	if (!m_world) return ret;
	if (m_world->isRemoteMode()) return ret;
	if (!m_world->getBuildMgr()) return ret;
	if (m_world->getCurMapID() != 0)
	{
		return ret;
	}
	if (m_allData.empty())
	{
		return ret;
	}
	ClientPlayer* player = nullptr;
	if (m_world->getActorMgr())
	{
		player = m_world->getActorMgr()->ToCastMgr()->findPlayerByUin(uin);
	}
	if (!player) return ret;
	BackPackGrid* curGrid = nullptr;
	//如果已经有数据了, 那就不用新生成了
	{
		BackPack* back = player->getBackPack();
		if (back != NULL)
		{
			curGrid = back->index2Grid(player->getCurShortcut() + player->getShortcutStartIndex());
			if (curGrid && !curGrid->userdata_str.empty())
			{
				jsonxx::Object posObj;
				if (posObj.parse(curGrid->userdata_str))
				{
					if (posObj.has<jsonxx::Number>("x") && posObj.has<jsonxx::Number>("z") && posObj.has<jsonxx::Number>("y"))
					{
						ret.x = posObj.get<jsonxx::Number>("x");
						ret.y = posObj.get<jsonxx::Number>("y");
						ret.z = posObj.get<jsonxx::Number>("z");
						return ret;
					}
				}
			}
		}
	}
	m_useCount++;
	//要新生成
	const auto& config = CityConfig::getSingletonPtr()->getOtherConfig();
	ChunkRandGen randgen;
	randgen.setSeed((m_allData.size()) + (m_useCount >> 4) + MINIW::GetTimeStamp());
	int index = randgen.nextInt(m_allData.size());
	const auto& city = m_allData[index];
	ret.y = randgen.get(config.buildHeight.x(), config.buildHeight.y());
	ret.x = randgen.nextInt(city.range.x() * CHUNK_BLOCK_X) + city.leftDown.x * CHUNK_BLOCK_X;
	ret.z = randgen.nextInt(city.range.y() * CHUNK_BLOCK_Z) + city.leftDown.z * CHUNK_BLOCK_Z;
	BuildData data;
	data.dir = DIR_NEG_Z;
	//data.id_1 = "cityBuild";
	data.id_2 = BuildCreaterType_City;
	data.startPos = ret;
	data.fileName = config.preciousData.m_fileName;
	//data.paramter = buildParam;
	auto creater = static_cast<CityBuildCreater*>(dynamic_cast<BuildMgr*>(m_world->getBuildMgr())->addBuild(data));
	if (creater)
	{
		creater->setBuildIndex(CityBuildPrecious);
	}
	//这里写死了, 位置偏去中间
	ret.x += 1;
	ret.z += 1;
	//设置数据
	if (curGrid)
	{
		jsonxx::Object obj;
		obj << "x" << ret.x;
		obj << "y" << ret.y;
		obj << "z" << ret.z;
		curGrid->setUserdataStr(obj.json_nospace().c_str());
	}
	return ret;
}

void CityMgr::loadForceChunk()
{
	if (g_WorldMgr && CityConfig::getSingletonPtr() && m_world && m_world->getCurMapID() == 0)
	{
		int range = CityConfig::getSingletonPtr()->getOtherConfig().m_loadRange;
		auto centePos = BlockDivSection(CityConfig::getSingletonPtr()->getBaseCenter());
		int trunkX = 0;
		int trunkZ = 0;
		for (int z = -range; z <= range; z++)
		{
			for (int x = -range; x <= range; x++)
			{
				trunkX = x + centePos.x;
				trunkZ = z + centePos.z;
				Chunk* chunk = m_world->getChunk({ trunkX, trunkZ });
				if (!chunk)
					m_world->syncLoadChunk(trunkX, trunkZ);
			}
		}
	}
}

std::string CityMgr::GetAllSingleBuildDataToJson()
{
	jsonxx::Array json_arr;

	for (const auto& it : m_singleBuildData)
	{
		//<10的遗迹
		if (it.second.buildData.size() >= 10)
			continue;

		jsonxx::Object item_obj;
		item_obj << "buildName" << it.second.buildName;
		jsonxx::Array buildarr;
		for (const auto& itembuilddata : it.second.buildData)
		{
			jsonxx::Object builddata;
			builddata << "x" << (itembuilddata.posbegin.x + itembuilddata.posend.x) / 2;
			builddata << "z" << (itembuilddata.posbegin.z + itembuilddata.posend.z) / 2;
			buildarr.import(builddata);
		}

		item_obj << "builddata" << buildarr;
		json_arr.import(item_obj);
	}

	return json_arr.json();
}

void CityMgr::onAddSingleBuild(const std::string& buildName, int x, int z, int rangeX, int rangeZ, int bRangeX, int bRangeZ)
{
	// 查找是否已存在相同buildName的建筑数据
	auto it = m_singleBuildData.find(buildName);

	if (it != m_singleBuildData.end()) {
		// 找到相同buildName的建筑，直接添加新的建筑数据
		it->second.buildData.push_back(SaveBuildData(x, z, rangeX, rangeZ, bRangeX, bRangeZ));
	} else {
		// 没有找到相同buildName的建筑，创建新的数据
		SaveSingleBuildData data;
		data.buildName = buildName;
		data.buildData.push_back(SaveBuildData(x, z, rangeX, rangeZ, bRangeX, bRangeZ));
		m_singleBuildData[buildName] = data;
	}
	m_isChanged = true;
}

bool CityMgr::checkCanAddSingleBuild(const std::string& name)
{
	auto builddata = CityConfig::getSingletonPtr()->getSingleBuildDataByName(name);
	if (!builddata)
	{
		return false;
	}
	
	return GetSingleBuildCount(name) < builddata->m_maxCount;
}

int CityMgr::GetSingleBuildCount(const std::string& name)
{
	auto it = m_singleBuildData.find(name);
	if (it == m_singleBuildData.end())
	{
		return 0;
	}
	return it->second.buildData.size();
}
/**
 * 判断给定坐标点是否在保护区内
 * @param position 待检查的坐标点
 * @return true: 在保护区内 false: 不在保护区内
 * @note 保护区定义为CityData的范围向外扩展20格，及SaveSingleBuildData向外扩展20格
 */
bool CityMgr::isInProtectedArea(const WCoord& position) //之后使用4叉或8叉来保存建筑物位置与范围。提供判断保护区的效率（优化项） by charles xie
{
	//const int globalProtectedRange = CityConfig::getSingletonPtr()->GetCityProtectedRange(); // 保护区向外扩展的格数

	//SOC 全是单独建筑物。
	// 1. 检查是否在城市数据(CityData)的保护范围内
	//for (auto& cityData : m_allData)
	//{
	//	// 检查给定位置是否在扩展的保护范围内
	//	if (position.x >= cityData.posbegin.x && position.x <= cityData.posend.x &&
	//		position.z >= cityData.posbegin.z && position.z <= cityData.posend.z)
	//	{
	//		return true;
	//	}
	//}
	
	// 2. 检查是否在单独建筑(SaveSingleBuildData)的保护范围内
	for (auto& p : m_singleBuildData)
	{
		for (auto& buildData : p.second.buildData)
		{
			// 检查给定位置是否在扩展的保护范围内
			if (position.x >= buildData.posbegin.x && position.x <= buildData.posend.x &&
				position.z >= buildData.posbegin.z && position.z <= buildData.posend.z)
			{
				return buildData.bIsProtected;
			}
		}
	}
	
	// 不在任何保护区内
	return false;
}

bool CityMgr::isInProtectedChunkArea(const ChunkIndex& cidx)
{
	for (auto& p : m_singleBuildData)
	{
		for (auto& buildData : p.second.buildData)
		{
			// 检查给定位置是否在扩展的保护范围内
			if (cidx.x >= (buildData.x - 1) && cidx.x <= (buildData.x + buildData.rangeX - 1) &&
				cidx.z >= (buildData.z - 1) && cidx.z <= (buildData.z + buildData.rangeZ - 1))
			{
				return true;
			}
		}
	}
	// 不在任何保护区内
	return false;
}

void CityMgr::updateCityProtectedRange(CityData& data)
{
	int protectedRange = m_globalProtectedRange;
	const CityBuildConfig::CityDataConfig* pConfig = GetCityConfigInterface()->getCityDataByIndex(data.configIndex);
	if (pConfig)
		protectedRange = pConfig->m_protectedRange;

	data.posbegin.x -= protectedRange;
	data.posbegin.z -= protectedRange;
	data.posend.x += protectedRange;
	data.posend.z += protectedRange;
}

void CityMgr::updateSingleBuildProtectedRange(SaveSingleBuildData& data)
{
	int protectedRange = 0;//m_globalProtectedRange;
	const CityBuildConfig::SingleBuildData* pConfig = GetCityConfigInterface()->getSingleBuildDataByName(data.buildName);
	if (pConfig)
		protectedRange = pConfig->m_protectedRange;
	bool isprotected = protectedRange < 0 ? false : true;
	for (auto& buildData : data.buildData)
	{
		buildData.bIsProtected = isprotected;
		buildData.posbegin.x -= protectedRange;
		buildData.posbegin.z -= protectedRange;
		buildData.posend.x += protectedRange;
		buildData.posend.z += protectedRange;
	}
}

void CityMgr::onCityGenEnd()
{
	for (auto& data : m_allData)
	{
		updateCityProtectedRange(data);
	}
	for (auto& p : m_singleBuildData)
	{
		updateSingleBuildProtectedRange(p.second);
	}
}
int CreatedbigbuildCount = 0;
int CreateSmallBuildCount = 0;
void CityMgr::onBuildEnd(World* world, const BuildData& data)
{
	ChunkIndex buildCIdx(data.startPos.x / 16, data.startPos.z / 16);
	bool isBigBuild = false;
	int rangex = 0;
	int rangez = 0;
	for (auto& cityData : m_allData)
	{
		if (cityData.leftDown == buildCIdx)
		{
			m_CompleteCityIdxList.push_back(cityData.configIndex);
			isBigBuild = true;
			if (data.roadBeginPosList.size())
			{
				LOG_INFO_BUILD("#####Create Map record create build finish build name = %s, pos x = %d, z = %d, roadcrossing count = %d"
					, data.fileName.c_str(), data.startPos.x, data.startPos.z, data.roadBeginPosList.size());
				cityData.roadBeginPosList.clear();
				cityData.roadBeginPosList = data.roadBeginPosList;
			}
			rangex = cityData.range.x();
			rangez = cityData.range.y();
			CreatedbigbuildCount++;
			break;
		}
	}
	if (!isBigBuild)
	{
		const CityBuildConfig::SingleBuildData* pBuildData = GetCityConfigInterface()->getSingleBuildDataByName(data.fileName.c_str());
		rangex = pBuildData->m_range.x();
		rangez = pBuildData->m_range.y();
		if (data.dir == DIR_NEG_X || data.dir == DIR_POS_X)
		{
			rangex = pBuildData->m_range.y();
			rangez = pBuildData->m_range.x();
		}
		CreateSmallBuildCount++;
	}
	int recalculateExSize = 4;
	for (int x = -recalculateExSize; x <= rangex + recalculateExSize; x++)
	{
		for (int z = -recalculateExSize; z <= rangez + recalculateExSize; z++)
		{
			ChunkIndex cidx(buildCIdx.x + x, buildCIdx.z + z);
			//if (x >= 0 && z >= 0 && x < rangex && z < rangez)
			{
				if (GetIPlayerControl())
				{
					GetIPlayerControl()->OnLoadChunk(cidx);
				}
			}
			world->recalculateChunkLight(cidx);
		}
	}
	for (int x = 0; x < rangex; x++)
	{
		for (int z = 0; z < rangez; z++)
		{
			ChunkIndex cidx(buildCIdx.x + x, buildCIdx.z + z);
			world->removeChunk(cidx);
		}
	}
	LOG_INFO_BUILD("#####Create Map record create build finish build name = %s, is %s, now count = %d, pos x = %d, z = %d, rangex = %d, rangez = %d "
		, data.fileName.c_str(), isBigBuild ? "BigBuild" : "SmallBuild", isBigBuild ? CreatedbigbuildCount : CreateSmallBuildCount, data.startPos.x, data.startPos.z, rangex, rangez);
}

void CityMgr::onCityBuildAllEnd(World* world)
{
	if (m_CompleteCityIdxList.size() != m_allData.size())
	{
		assert(0);
	}
	//if (m_CompleteCityIdxList.size() == m_allData.size())
	if (m_nSOCCtiyBuildStep == 0)
	{
		GetEcosysUnitRoadBuild().onWorldLoaded(world);
		GetEcosysUnitCityBuild().GenSmallBuilding(world);
		
		m_nSOCCtiyBuildStep++;
	}
	else if (m_nSOCCtiyBuildStep == 1)
	{
		//world->unloadAllNoUseChunk();
		m_world->getActorMgr()->ToCastMgr()->saveMobPresetsToJson();
		m_world->GetChestMgr()->saveBuildingChestsToJson();

		//world->recalculateAllLight();
		world->setAllSOCRule();
		LOG_INFO_BUILD("#####Create Map record create ALL Build finish big count = %d, small count = %d " , CreatedbigbuildCount, CreateSmallBuildCount);
		world->createWholeMiniMap();
		m_nSOCCtiyBuildStep++;
	}
	else if (m_nSOCCtiyBuildStep == 2)
	{
		LOG_INFO_BUILD("#####Create Map record create ALL Build finish step = %d big count = %d, small count = %d ", m_nSOCCtiyBuildStep, CreatedbigbuildCount, CreateSmallBuildCount);
		world->createWholeMiniMap();
		m_nSOCCtiyBuildStep++;
	}
	else if (m_nSOCCtiyBuildStep > 2)
	{
		LOG_INFO_BUILD("#####Create Map record create ALL Build finish step = %d big count = %d, small count = %d ", m_nSOCCtiyBuildStep, CreatedbigbuildCount, CreateSmallBuildCount);
	}
}

const int MIN_CITY_DISTANCE = 5;  // 城市之间至少间隔5个区块
bool CityMgr::checkPosCanPlaceCity(ChunkIndex chunkIndex, int rangeX, int rangeZ, std::vector<int> canPlaceBiomeList, bool checkOtherCity, int dir)
{
	//int rangeMax = std::max(rangeX, rangeZ);
	int exRange = 1;// checkOtherCity ? 1 : 0;//为道路生成扩展1chunk
	int posminx = chunkIndex.x - exRange;
	int posmaxx = chunkIndex.x + /*rangeMax;*/rangeX + exRange;
	int posminz = chunkIndex.z - exRange;
	int posmaxz = chunkIndex.z + /*rangeMax;*/rangeZ + exRange;

	if (!canPlaceBiomeList.empty())
	{
		for (int cx = posminx; cx <= posmaxx; cx++)
		{
			for (int cz = posminz; cz <= posmaxz; cz++)
			{
				ChunkIndex cidx(cx, cz);
				auto chunk = m_world->getChunk(cidx);
				if (!chunk)
				{
					m_world->syncLoadChunk(cidx);
				}
			}
		}
		if (canPlaceBiomeList.size() >= 2)
		{
			if (canPlaceBiomeList.size() == 2)
			{
				if (canPlaceBiomeList[0] == BIOME_BEACH && canPlaceBiomeList[1] == BIOME_OCEAN)
				{
					int mainBiomeCount = 0;
					int firstBiomeCount = 0;

					int headPartStartX = posminx;
					int headPartEndX = posmaxx;
					int headPartStartZ = posminz;
					int headPartEndZ = posmaxz;

					int tailPartStartX = posminx;
					int tailPartEndX = posmaxx;
					int tailPartStartZ = posminz;
					int tailPartEndZ = posmaxz;

					if (dir == 0)
					{
						int xLong = posmaxx - posminx;
						headPartStartX = posminx;
						headPartEndX = (headPartStartX + xLong * 2 / 5);
						tailPartStartX = headPartEndX + 1;
						tailPartEndX = posmaxx;
					}
					else if (dir == 1)
					{
						int xLong = posmaxx - posminx;
						headPartStartX = (posminx + xLong * 3 / 5);
						headPartEndX = posmaxx;
						tailPartStartX = posminx;
						tailPartEndX = headPartStartX - 1;
					}
					else if (dir == 2)
					{
						int zLong = posmaxz - posminz;
						headPartStartZ = posminz;
						headPartEndZ = (headPartStartZ + zLong * 2 / 5);
						tailPartStartZ = headPartEndZ + 1;
						tailPartEndZ = posmaxz;
					}
					else if (dir == 3)
					{
						int zLong = posmaxz - posminz;
						headPartStartZ = (posminz + zLong * 3 / 5);
						headPartEndZ = posmaxz;
						tailPartStartZ = posminz;
						tailPartEndZ = headPartStartZ - 1;
					}

					auto checkPlaceDataFun = [&canPlaceBiomeList](int biomeId) -> int
						{
							if (biomeId == canPlaceBiomeList[0] || biomeId == BIOME_PLAINS || biomeId == BIOME_DESERT || biomeId == BIOME_ICE_PLAINS)
							{
								return 1;
							}
							else if (biomeId == canPlaceBiomeList[1])
							{
								return 2;
							}
							return 0;
						};
					int allBiomeCount = 0;

					for (int cx = headPartStartX; cx <= headPartEndX; cx++)
					{
						for (int cz = headPartStartZ; cz <= headPartEndZ; cz++)
						{
							ChunkIndex cidx(cx, cz);
							auto chunk = m_world->getChunk(cidx);
							if (chunk)
							{
								for (int bx = 0; bx < SECTION_BLOCK_DIM; bx++)
								{
									for (int bz = 0; bz < SECTION_BLOCK_DIM; bz++)
									{
										auto biomeId = chunk->getBiomeID(bx, bz);
										int ret = checkPlaceDataFun(biomeId);
										if (ret == 1) mainBiomeCount++;
										else if (ret == 2) firstBiomeCount++;
										allBiomeCount++;
									}
								}
							}
							
						}
					}
					//int al = allBiomeCount * 0.9;
					//if (firstBiomeCount < (allBiomeCount * 0.9)) return false;
					if (firstBiomeCount != allBiomeCount) return false;

					mainBiomeCount = 0;
					firstBiomeCount = 0;
					for (int cx = tailPartStartX; cx <= tailPartEndX; cx++)
					{
						for (int cz = tailPartStartZ; cz <= tailPartEndZ; cz++)
						{
							ChunkIndex cidx(cx, cz);
							auto chunk = m_world->getChunk(cidx);
							if (chunk)
							{
								for (int bx = 0; bx < SECTION_BLOCK_DIM; bx++)
								{
									for (int bz = 0; bz < SECTION_BLOCK_DIM; bz++)
									{
										auto biomeId = chunk->getBiomeID(bx, bz);
										int ret = checkPlaceDataFun(biomeId);
										if (ret == 1) mainBiomeCount++;
										else if (ret == 2) firstBiomeCount++;
									}
								}
							}
						}
					}
					int fc = firstBiomeCount * 2;
					if (mainBiomeCount == 0 || mainBiomeCount < (firstBiomeCount * 2)) return false;
				}
			}
		}
		else
		{
			std::vector<int> tmpCanList;
			if (canPlaceBiomeList[0] == -1)
			{
				tmpCanList = { BIOME_PLAINS , BIOME_DESERT , BIOME_ICE_PLAINS };
			}
			else
			{
				tmpCanList.push_back(canPlaceBiomeList[0]);
			}
			auto checkBiomeIdFun = [&tmpCanList](int biomeId) ->bool
				{
					for (auto item : tmpCanList)
					{
						if (item == biomeId) return true;
					}
					return false;
				};
			for (int cx = posminx; cx <= posmaxx; cx++)
			{
				for (int cz = posminz; cz <= posmaxz; cz++)
				{
					ChunkIndex cidx(cx, cz);
					auto chunk = m_world->getChunk(cidx);
					if (chunk)
					{
						for (int bx = 0; bx < SECTION_BLOCK_DIM; bx++)
						{
							for (int bz = 0; bz < SECTION_BLOCK_DIM; bz++)
							{
								auto biomeId = chunk->getBiomeID(bx, bz);
								if (!checkBiomeIdFun(biomeId)) return false;
							}
						}
					}
				}
			}
		}
	}
	// 检查给定位置是否在扩展的保护范围内
	if (checkOtherCity)
	{
		for (auto& cityData : m_allData)
		{
			int maxRange = std::max(cityData.range.x(), cityData.range.y());
			int minx = cityData.leftDown.x - MIN_CITY_DISTANCE;
			int maxx = cityData.leftDown.x + maxRange + MIN_CITY_DISTANCE;
			int minz = cityData.leftDown.z - MIN_CITY_DISTANCE;
			int maxz = cityData.leftDown.z + maxRange + MIN_CITY_DISTANCE;

			// 有交集则不能放置
			if ((posminx >= minx && posminx <= maxx || posmaxx >= minx && posmaxx <= maxx) &&
				(posminz >= minz && posminz <= maxz || posmaxz >= minz && posmaxz <= maxz))
			{
				return false;
			}
		}
	}
	return true;
}
