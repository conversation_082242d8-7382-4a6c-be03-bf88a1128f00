#include "MpActorTrackerEntry.h"
#include "MpActorManager.h"
#include "GameNetManager.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "IActorLiving.h"
#include "IActorAttrib.h"
#include "IPlayerControl.h"
#include "IActorLocoMotion.h"
#include "IBackpack.h"
#include "IActorBody.h"
#include "blocks/container.h"
#include "IRecordInterface.h"
#include "EffectManager.h"
#include "blocks/special_blockid.h"
#include "ActorManagerInterface.h"
#include "ClientActorDef.h"
#include "WorldManager.h"
#include "IClientActorFuncWrapper.h"
#include "Components/Transform.h"
#include "ActorBodySequence.h"
#include "PlayManagerInterface.h"
#include "ActorManagerInterface.h"

//#define ALL_NEW_SYNC 1
//�µ�ͬ����ʽ���Ʒ���Ч��
//#ifdef IWORLD_SERVER_BUILD 
//#define ALL_NEW_SYNC 1 
//#endif


const int DPSQ = 12 * 12;
const int ASQ = 4 * 4;
const float QUAT_SQ = Rainbow::CosByAngle(4.0f);
const float QUAT_SQ_VEHICLE = Rainbow::CosByAngle(2.0f);

using namespace Rainbow;
using namespace MINIW;

MpActorTrackerEntry::MpActorTrackerEntry(IClientActor *actor, int trackingDistance, int updateFrequency)
	: mTrackingPlayersTable(500), mEntryActor(actor), mInitialUpdated(false), mUpdateCount(0), mShouldRemoveThis(false), mLastStance(PB_STANCERUN),
	mTrackingDistance(trackingDistance), mUpdateFrequency(updateFrequency),	mbLastReverse(false),mEntryPlayer(nullptr)
{
	mLastPosition.x = mLastPosition.y = mLastPosition.z = 0;
	mLastOnPlatPos.x = mLastOnPlatPos.y = mLastOnPlatPos.z = 0;
	mLastRot = Rainbow::Quaternionf::identity;
	memset (mLastEquipment, 0, sizeof(mLastEquipment));
	m_SyncDataPre = NULL;
	resetAniSendState();
	mLastCustomScale = 0.0f;
	mLastOnRail = false;
	mLastHeadCar = false;
	mLastTailCar = false;
	mLastRailT = 0;
	mLastRailKnot = WCoord(0,-1,0);
	mLastRailOutIndex = -1;
	mLastAnimSeq = 0; //20210928 codeby:chenwei 初始化
	m_LastWeaponUserdata = "";
	auto attr = actor->GetIActorAttrib();
	if (attr != nullptr)
	{
		mLastHP = attr->getHP();
		mLastArmor = attr->getArmor();
		mLastExtraHP = attr->getExtraHP();
	}
	else
	{
		mLastHP = 1;
		mLastArmor = 0;
		mLastExtraHP = 0;
	}
	IClientPlayer *player = dynamic_cast<IClientPlayer *>(actor);
	if(player)
	{
		//PlayerAttrib *attr = player->getPlayerAttrib();
		auto attr = actor->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
		if (attr) {
			bool result = attr->Event2().Emit<float&, int&, int&>("PlayerAttrib_InitValue", mLastOxygen, mLastExp, mLastFoodLevel);
			Assert(result);
		}
		mEntryPlayer = player;
	}

	m_NeedSyncHandModel = false;
	m_SyncDataPre = new jsonxx::Value();
	m_SyncDataPre->object_value_ = new jsonxx::Object();
	m_SyncDataPre->type_ = jsonxx::Value::OBJECT_;

	m_SyncDataNow = new jsonxx::Value();
	m_SyncDataNow->object_value_ = new jsonxx::Object();
	m_SyncDataNow->type_ = jsonxx::Value::OBJECT_;
	
	m_SyncDataDiff = new jsonxx::Value();
	mLastAnimWeapon = -1;
	mLastUpBodyAnimSeqId = -1;
	mLastOffLine = false;
}

MpActorTrackerEntry::~MpActorTrackerEntry()
{
	if (m_SyncDataPre)
		m_SyncDataPre->release();
	if (m_SyncDataNow)
		m_SyncDataNow->release();
	if (m_SyncDataDiff)
		m_SyncDataDiff->release();
}

void MpActorTrackerEntry::removeEntryNextUpdate()
{
	auto iter = mTrackingPlayersTable.iterate(nullptr);
	for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
	{
		sendActorLeaveToClient(iter->key->getUin(), mEntryActor->getObjId());
	}
	mTrackingPlayersTable.clear();
	mEntryActor = nullptr;
	mEntryPlayer = nullptr;

	mShouldRemoveThis = true;
}

bool MpActorTrackerEntry::shouldRemoveEntry()
{
	return mShouldRemoveThis;
}

bool MpActorTrackerEntry::inTrackingDistance(const WCoord &pos1, const WCoord &pos2)
{
	//20211021 �������������˳� codyby:liushuxin
	int dx = CoordDivBlock(pos1.x) - CoordDivBlock(pos2.x);
	if (dx < -mTrackingDistance || dx > mTrackingDistance) 
	{
		return false;
	} 

	int dz = CoordDivBlock(pos1.z) - CoordDivBlock(pos2.z);
	if (dz < -mTrackingDistance || dz > mTrackingDistance)
	{
		return false;
	}

	return true;
}

void MpActorTrackerEntry::checkTrackingPlayersChange(bool bForceObjChild)
{
    OPTICK_EVENT();
	if (!bForceObjChild && mEntryActor && mEntryActor->IsObject() && mEntryActor->GetParentWID() != 0) {
		// 实体子节点都由父节点来调用 checkTrackingPlayersChange
		return;
	}
	static std::vector<int> enterList;
	enterList.clear();

	// update tracking player list;
	for (size_t i = 0; i != mEntryActor->GetActorMgrInterface()->getNumPlayer(); ++i)
	{
		IClientPlayer* player = mEntryActor->GetActorMgrInterface()->iGetIthPlayer(i);
		if (player == nullptr || player->CastToActor() == mEntryActor) continue;

		int uin = player->getUin();

		bool inDist = inTrackingDistance(mLastPosition, player->CastToActor()->getPosition());

		if (mTrackingPlayersTable.find(player) == nullptr)
		{
			if (inDist) //���뷶Χ
			{
				if (mEntryPlayer != nullptr)
				{
					if (mEntryPlayer->getUin() != uin) 
						enterList.push_back(uin);
				}
				else
				{
					enterList.push_back(uin);
				}
				mTrackingPlayersTable.insert(player, true);
				m_NeedSyncHandModel = true;
			}
		}
		else //��player
		{
			if (!inDist) //�뿪��Χ
			{
				mTrackingPlayersTable.erase(player);
				// ���뿪��Χ��say byebye
				sendActorLeaveToClient(uin, mEntryActor->getObjId());
			}
		}
	}
	//20211021 ����֪ͨenter��Ϣ codeby:liushuxin
	sendEnterToClient(enterList);

	mEntryActor->TriggerScriptComponent(enterList);
	
#ifdef MODULE_FUNCTION_ENABLE_GAMERECORD
	if (GAMERECORD_INTERFACE_EXEC(isRecordStarted(), 0) && mEntryActor->getObjType() == OBJ_TYPE_ROLE
	&& GAMERECORD_INTERFACE_EXEC(isRecordUin(int(mEntryActor->getObjId())), 0))
	{
		IClientPlayer *thisplayer = dynamic_cast<IClientPlayer*>(mEntryActor);
		if (thisplayer) mTrackingPlayersTable.insert(thisplayer);
	}
#endif	

	if (mEntryActor && mEntryActor->IsObject() && mEntryActor->GetChildrenCount() > 0)
	{
		// 实体根节点
		auto pWorld = mEntryActor->getWorld();
		if (pWorld && pWorld->getMpActorMgr()) {
			mEntryActor->GetChildrenList().for_each([&](MNSandbox::SandboxNode* t) -> void {
				IClientActor* child = dynamic_cast<IClientActor*>(t);
				if (child)
				{
					MpActorTrackerEntry* entry = pWorld->getMpActorMgr()->getTrackerEntry(child->getObjId());
					if (entry)
						entry->checkTrackingPlayersChange(true);
				}
			});
		}
	}
}

void MpActorTrackerEntry::sendEnterToClient(std::vector<int>& enterList)
{
    OPTICK_EVENT();
	if (enterList.empty())
	{
		return;
	}

	if (mEntryPlayer)
	{
		sendPlayerEnterToClient(&enterList[0], enterList.size(), mEntryPlayer);
	}
	else
	{
		if (mEntryActor->supportSaveToPB())
			sendActorEnterToClient_V2(&enterList[0], enterList.size());
		else
			sendActorEnterToClient(&enterList[0], enterList.size());

		if (mEntryActor->getObjType() == OBJ_TYPE_VEHICLE)
		{
			for (int i = 0; i < enterList.size(); i++)
			{
				if (g_WorldMgr)
				{
					IClientPlayer* player = g_WorldMgr->getPlayerByUin(enterList[i]);
					if (player)
					{
						auto FuncWrapper = mEntryActor->getActorFuncWrapper();
						if (FuncWrapper)
						{
							FuncWrapper->setMustSyncPos(true);
						}
						broadAttribChanges(player);
					}
				}
			}

		}
	}
}

void MpActorTrackerEntry::sendPlayerEnterToClient(int *uinList, int count, IClientPlayer *player)
{
    OPTICK_EVENT();
	assert(GetGameNetManagerPtr()->isHost() || GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false));

	PB_ActorEnterAOIHC actorEnterAOIHC;
	auto actor = player->CastToActor();
	actorEnterAOIHC.set_objid(actor->getObjId());
	actorEnterAOIHC.set_actortype(PB_ACTORTYPEROLE);
	PB_ActorInfo* actorInfo = actorEnterAOIHC.mutable_actorinfo();
	PB_ActorRoleInfo* actorRoleInfo = actorInfo->mutable_roleinfo();
	PB_PlayerInfo* playerInfo = actorRoleInfo->mutable_player();
	PB_RoleInfo* roleInfo = actorRoleInfo->mutable_info();

	playerInfo->set_anim(actor->GetIBody()->getCurAnim(0));
	playerInfo->set_anim1(actor->GetIBody()->getCurAnim(1));
	playerInfo->set_animweapon(actor->GetIBody()->getCurAnimWeapon());
	playerInfo->set_bodycolor(actor->GetIBody()->getBodyColor());
	playerInfo->set_customscale(actor->getCustomScale());
	playerInfo->set_actseqid(actor->GetIBody()->getActSeqID());
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	SANDBOX_ASSERT(actor->GetNodeid() != 0);
	playerInfo->set_nodeid(actor->GetNodeid());
	// LOG_INFO("[player_nodeid] send player nodeid: %d , objid:%d", actor->GetNodeid(), actor->getObjId());
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX

	// WorldManager::syncPlayerEnter 时同步过teamid但是没有player
	playerInfo->set_teamid((player)->iGetTeam());
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	SANDBOX_ASSERT(actor->GetNodeid() != 0);
	playerInfo->set_nodeid(actor->GetNodeid());
	// LOG_INFO("[player_nodeid] send player nodeid: %d , objid:%d", actor->GetNodeid(), actor->getObjId());
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX

	roleInfo->set_model(actor->GetIBody()->getModelID());
	roleInfo->set_skinid(actor->GetIBody()->getSkinID());
	IActorAttrib* actorAttr = actor->GetIActorAttrib();
	actorEnterAOIHC.set_offline(actorAttr->getOffLine());
	if (IClientActor*horse = player->GetCurAccountHorse())
	{
		playerInfo->set_curdisplayhorseobjid(horse->getObjId());
	}
	// 悦享赛事称号 ******** by wuyuwang
	string titlename = player->getBPTitle();

#ifndef IWORLD_SERVER_BUILD
	if (player->isHost()) {
		char mytitlename[64] = { 0 };
		MINIW::ScriptVM::game()->callFunction("GetCurUseTitle", ">s", &mytitlename);
		if (0 != strlen(mytitlename)) {
			titlename = string(mytitlename);
		}
	}
#endif

	if (!titlename.empty()) {
		roleInfo->set_bptitle(titlename);
	}
	/////////////////////////////////////////////////////////////////////////////
	
	 roleInfo->set_nickname(player->getNickname());
	auto sawtooth_ = playerInfo->mutable_sawtooth();
	
	auto thornComponent = actor->getActorComponent(ComponentType::COMPONENT_THORN_BALL);
	if (thornComponent != nullptr)
	{
		bool result = thornComponent->Event2().Emit<google::protobuf::RepeatedPtrField< ::game::common::PB_SawtoothInfo >*>("ThornBall_UpdateValue", sawtooth_);
		Assert(result);
	}

	auto fish = playerInfo->mutable_fishing();

	auto fishingCmp = actor->getActorComponent(ComponentType::COMPONENT_FISHING);
	if (fishingCmp)
	{
		bool result = fishingCmp->Event2().Emit<game::common::PB_FishingInfo*>("Fishing_UpdateValue", fish);
		Assert(result);
	}

	const char *customskins = actor->GetIBody()->getCustomSkins();
	if(customskins && customskins[0]) roleInfo->set_customjson(customskins);

	player->changeRoleData(playerInfo->mutable_roledata());

	actorEnterAOIHC.set_spectator_mode(player->getSpectatorMode());
	actorEnterAOIHC.set_spectator_type(player->getSpectatorType());

	//auto attr = player->getLivingAttrib();
	for (int i = 0; i < MAX_EQUIP_SLOTS; ++i) mLastEquipment[i] = player->getEquipItem(EQUIP_SLOT_TYPE(i));
	if (player->getEquipGrid(EQUIP_WEAPON)) m_LastWeaponUserdata = player->getEquipGrid(EQUIP_WEAPON)->userdata_str;

	mLastAnim[0] = playerInfo->anim();
	mLastAnim[1] = playerInfo->anim1();

	actorEnterAOIHC.set_hookid(player->getHookObj());
	if (player->getOPWay() != PLAYEROP_WAY_NORMAL)
	{
		actorEnterAOIHC.set_playmode(player->getOPWay());
	}
	if (player->getCurOperate() != PLAYEROP_NULL && player->getCurOperate() != PLAYEROP_DIG)
	{
		actorEnterAOIHC.set_playoperate(player->getCurOperate());
	}
	if (player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		if (GetISandboxActorSubsystem()->IsActorType(player->GetCatchBall(), ActorType::ACTOR_BASKETBALL))
		{
			actorEnterAOIHC.set_childuuid(actor->getObjId());
		}
	}
	//player:������Ч
	auto effectComponent = actor->getActorComponent(ComponentType::COMPONENT_EFFECT);
	if (effectComponent)
	{
		bool result = effectComponent->Event2().Emit<game::common::PB_PlayerInfo*>("Effect_UpdateValue", playerInfo);
		Assert(result);
	}
	//Player:������Ч
	auto sound = actor->getActorComponent(ComponentType::COMPONENT_SOUND);
	if (sound)
	{
		bool result = sound->Event2().Emit<game::common::PB_PlayerInfo*, IClientActor*>("Sound_UpdateValue", playerInfo, actor);
		Assert(result);
	}

	GetGameNetManagerPtr()->sendToClientMulti(uinList, count, PB_ACTOR_ENTER_AOI_HC, actorEnterAOIHC);
}

void MpActorTrackerEntry::sendActorEnterToClientAfter(int* uinList, int count)
{
	IActorBody* body = mEntryActor->GetIBody();
	if (body
#ifndef IWORLD_SERVER_BUILD
		&& (body->getCurAnim(0) != 0 || body->getCurAnim(1) != -1 || body->getActID() != -1 || mEntryActor->getCustomScale() != 1.0f || body->getActTriggerID() > 0)
#endif
		)
	{
		broadAnimChanges(uinList, count, false);
	}
	else if (mEntryActor->getObjType() == OBJ_TYPE_THROWABLE)
	{
		if (GetISandboxActorSubsystem()->IsActorType(mEntryActor, ActorType::ACTOR_PROJECTILE))
		{
			int actid, animode;
			bool result = mEntryActor->Event2().Emit<int&, int&>("ActorProjectile_GetValue", actid, animode);
			Assert(result);

			PB_ActorAnimHC actorAnimHC;
			actorAnimHC.set_actorid(mEntryActor->getObjId());
			actorAnimHC.set_changeflag(mEntryActor->getChangeFlag());
			actorAnimHC.set_anim(0);
			actorAnimHC.set_anim1(0);
			actorAnimHC.set_animweapon(0);
			actorAnimHC.set_actid(actid);
			actorAnimHC.set_customscale(0);
			actorAnimHC.set_actidtrigger(animode);
			GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_ACTOR_ANIM_HC, actorAnimHC);

			mLastAnim[0] = actorAnimHC.anim();
			mLastAnim[1] = actorAnimHC.anim1();
			mLastAnimWeapon = actorAnimHC.animweapon();
		}
	}
	else if(mEntryActor->getObjType() == OBJ_TYPE_DROPITEM)
	{
		if (GetISandboxActorSubsystem()->IsActorType(mEntryActor, ActorType::ACTOR_ITEM))
		{
			int animid, animode;
			bool result = mEntryActor->Event2().Emit<int&, int&>("ClientItem_GetValue", animid, animode);
			Assert(result);
			PB_ActorAnimHC actorAnimHC;
			actorAnimHC.set_actorid(mEntryActor->getObjId());
			actorAnimHC.set_changeflag(mEntryActor->getChangeFlag());
			actorAnimHC.set_anim(0);
			actorAnimHC.set_anim1(0);
			actorAnimHC.set_animweapon(0);
			actorAnimHC.set_actid(animid);
			actorAnimHC.set_customscale(0);
			actorAnimHC.set_actidtrigger(animode);
			GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_ACTOR_ANIM_HC, actorAnimHC);

			mLastAnim[0] = actorAnimHC.anim();
			mLastAnim[1] = actorAnimHC.anim1();
			mLastAnimWeapon = actorAnimHC.animweapon();
		}
	}
	if (mEntryActor->getReverse() != 0)
	{
		PB_ActorReverseHC actorReverseHC;
		actorReverseHC.set_actorid(mEntryActor->getObjId());
		actorReverseHC.set_reverse(mEntryActor->getReverse());

		GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_ACTOR_REVERSE_HC, actorReverseHC);
	}

	auto BindActorComp = mEntryActor->getActorComponent(ComponentType::COMPONENT_BIND_ACTOR);
	if (BindActorComp)
	{
		bool result = BindActorComp->Event2().Emit<int, int*>("BindActor_sendMsg", count, uinList);
		Assert(result);
	}

	WCoord railknot;
	int outindex;
	float t;
	int flags;
	if (mEntryActor->getOnRailState(railknot, outindex, t, flags))
	{
		//ActorTrainCar* car = static_cast<ActorTrainCar*>(mEntryActor);
		WORLD_ID headcar, tailcar;
		bool result = mEntryActor->Event2().Emit<WORLD_ID&, WORLD_ID&>("TrainCar_GetValue", headcar, tailcar);
		Assert(result);
		PB_TrainMoveHC trainMoveHC;
		trainMoveHC.set_objid(mEntryActor->getObjId());
		trainMoveHC.set_mapid(mEntryActor->getCurMapID());
		trainMoveHC.set_outindex(outindex);
		trainMoveHC.set_curvet(t);
		trainMoveHC.set_carreverse(flags & 1);
		trainMoveHC.set_headcar(headcar);
		trainMoveHC.set_tailcar(tailcar);

		PB_Vector3* railKnot = trainMoveHC.mutable_railknot();
		railKnot->set_x(railknot.x);
		railKnot->set_y(railknot.y);
		railKnot->set_z(railknot.z);

		GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_TRAIN_MOVE_HC, trainMoveHC);

		mLastOnRail = true;
		mLastHeadCar = headcar != 0;
		mLastTailCar = tailcar != 0;
		mLastRailKnot = railknot;
		mLastRailOutIndex = outindex;
		mLastRailT = t;
	}
}

void MpActorTrackerEntry::sendActorEnterToClient_V2(int* uinList, int count)
{
    OPTICK_EVENT();
	//子节点不track,只依赖父节点的同步
	if (mEntryActor->IsObject() && mEntryActor->GetParentWID() != 0)
		return;

	assert(GameNetManager::getInstance()->isHost() || GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false));

	PB_GeneralEnterAOIHC actorEnterAOIHC;
	actorEnterAOIHC.set_objid(mEntryActor->getObjId());
	actorEnterAOIHC.set_mapid(mEntryActor->getCurMapID());
	int ret = mEntryActor->saveToPB(&actorEnterAOIHC);
	if (ret != 0)
	{
		// TODO:
		return;
	}
	//actor:������Ч
	auto  effectComponent = mEntryActor->getActorComponent(ComponentType::COMPONENT_EFFECT);
	if (effectComponent)
	{
		bool result = effectComponent->Event2().Emit<game::hc::PB_GeneralEnterAOIHC*>("Effect_AOI_UpdateValue", &actorEnterAOIHC);
		Assert(result);
	}

	// Don't sync sound, let's implement it in client in future. 2024/11/12 by cloud.
	//Player:������Ч
	//auto sound = mEntryActor->getActorComponent(ComponentType::COMPONENT_SOUND);
	//if (sound)
	//{
	//	bool result = sound->Event2().Emit<game::hc::PB_GeneralEnterAOIHC*, IClientActor*>("Sound_AOI_UpdateValue", &actorEnterAOIHC, mEntryActor);
	//	Assert(result);
	//}
	
	GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_GENERAL_ENTER_AOI_HC, actorEnterAOIHC);
	sendActorEnterToClientAfter(uinList, count);
}
void MpActorTrackerEntry::sendActorEnterToClient(int *uinList, int count)
{
	assert(GetGameNetManagerPtr()->isHost() || GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false));

	PB_ActorEnterAOIHC actorEnterAOIHC;
	actorEnterAOIHC.set_objid(mEntryActor->getObjId());
	actorEnterAOIHC.set_actortype(PB_ACTORTYPEGENERAL);
	PB_ActorInfo* actorInfo = actorEnterAOIHC.mutable_actorinfo();
	PB_ActorGeneral* actorGeneral = actorInfo->mutable_actorgeneral();
	actorGeneral->set_mapid(mEntryActor->getCurMapID());

	if (mEntryActor->isSupportSaveExt())
	{
		jsonxx::Object obj;
		mEntryActor->save(obj);
		actorGeneral->set_infolen((int)obj.binLen());
		actorGeneral->set_actordetail((const char*)obj.bin(), obj.binLen());	
		actorGeneral->set_actortype(mEntryActor->getActorType());
	}
	else
	{
		size_t buflen;
		void *buf = mEntryActor->saveToBuffer(buflen);
		actorGeneral->set_infolen((int)buflen);
		actorGeneral->set_actordetail((const char*)buf, buflen);
		free(buf);
	}

	IActorLiving *living = dynamic_cast<IActorLiving *>(mEntryActor);
	if (living)
	{
		auto livingattrib = mEntryActor->getActorComponent(ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB);
		if (livingattrib)
		{
			bool result = livingattrib->Event2().Emit<int*, std::string&>("LivingAttrib_Equip", mLastEquipment, m_LastWeaponUserdata);
			Assert(result);
		}
	}

	//actor:������Ч
	auto effectComponent = mEntryActor->getActorComponent(ComponentType::COMPONENT_EFFECT);
	if (effectComponent)
	{
		bool result = effectComponent->Event2().Emit<game::common::PB_PlayerInfo*>("Effect_UpdateValue", actorInfo->mutable_roleinfo()->mutable_player());
		Assert(result);
	}

	//Player:������Ч
	auto sound = mEntryActor->getActorComponent(ComponentType::COMPONENT_SOUND);
	if (sound)
	{
		bool result = sound->Event2().Emit<game::common::PB_PlayerInfo*, IClientActor*>("Sound_UpdateValue", actorInfo->mutable_roleinfo()->mutable_player(), mEntryActor);
		Assert(result);
	}

	if (actorEnterAOIHC.ByteSize() >= 0xffff)
	{
#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
		MNSandbox::GetGlobalEvent().Emit<int,int>("StatisticRainforest_jsonObj", mEntryActor->getObjType(), actorEnterAOIHC.ByteSize());
#endif
		return;	
	}
	GetGameNetManagerPtr()->sendToClientMulti(uinList, count, PB_ACTOR_ENTER_AOI_HC, actorEnterAOIHC);
	sendActorEnterToClientAfter(uinList, count);
}

void MpActorTrackerEntry::sendActorLeaveToClient(int uin, long long objId)
{
	//子节点不track,只依赖父节点的同步
	if (mEntryActor->IsObject() && mEntryActor->GetParentWID() != 0)
		return;

	PB_ActorLeaveAOIHC actorLeaveAOIHC;
	actorLeaveAOIHC.set_objid(objId);

	GetGameNetManagerPtr()->sendToClient(uin, PB_ACTOR_LEAVE_AOI_HC, actorLeaveAOIHC);
}
#ifdef SERVER_NET_OPT
void MpActorTrackerEntry::sendActorMovementToClient(int uin, IClientActor* actor, float yaw, float pitch)
{
	PB_ActorMoveV2HC actorMoveHC;
	uint64_t objid = actor->getObjId();
	objid = GetISandboxActorSubsystem()->PackObjId(objid);
	actorMoveHC.set_objid(objid);

	uint32_t yaw_pitch = 0;
	yaw_pitch = (AngleFloat2Char(yaw) & 0xff) << 8;
	yaw_pitch |= AngleFloat2Char(pitch);
	actorMoveHC.set_yaw_pitch(yaw_pitch);

	if (!actor->getILocoMotion()->getOnGround())
	{
		actorMoveHC.set_changeflags(0);
	}

	actorMoveHC.add_position(actor->getPosition().x);
	actorMoveHC.add_position(actor->getPosition().y);
	actorMoveHC.add_position(actor->getPosition().z);

	GetGameNetManagerPtr()->cacheActorMove(uin, objid, actorMoveHC);
	//GameNetManager::getInstance()->sendToClient(uin, PB_ACTOR_MOVEV2_HC, actorMoveHC, 0, true, UNRELIABLE_SEQUENCED, HIGH_PRIORITY, 0, objid);
}
#else
void MpActorTrackerEntry::sendActorMovementToClient(int uin, IClientActor *actor, float yaw, float pitch)
{
	PB_ActorMoveHC actorMoveHC;
	actorMoveHC.set_objid(actor->getObjId());

	PB_MoveMotion* moveMotion = actorMoveHC.mutable_movemotion();
	moveMotion->set_pitch(AngleFloat2Char(pitch));
	moveMotion->set_yaw(AngleFloat2Char(yaw));
// 	if (actor->getLocoMotion()->m_OnGround)
// 	{
// 		moveMotion->set_changeflags(8);
// 	}
	moveMotion->set_changeflags(actor->getLocoMotion()->m_OnGround ? 8 : 0);
// 	moveMotion->set_mapid(actor->getCurMapID() + 1);

	PB_Vector3* pos = moveMotion->mutable_position();
	pos->set_x(actor->getPosition().x);
	pos->set_y(actor->getPosition().y);
	pos->set_z(actor->getPosition().z);

	GameNetManager::getInstance()->sendToClient(uin, PB_ACTOR_MOVE_HC, actorMoveHC, 0, true, UNRELIABLE_SEQUENCED);
}
#endif
void MpActorTrackerEntry::sendActorEquipUpdateToClient(int uin, IClientActor *actor, int equipType, int equipment)
{
	PB_ActorEquipItemHC actorEquipItemHC;
	actorEquipItemHC.set_objid(actor->getObjId());
	actorEquipItemHC.set_slottype(equipType);

	IActorLiving* living = dynamic_cast<IActorLiving*>(actor);
	if (living)
	{
		auto livingattrib = actor->getActorComponent(ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB);
		if (livingattrib)
		{
			BackPackGrid* grid = nullptr;
			int itemid;
			bool result = livingattrib->Event2().Emit<BackPackGrid*, int, int&>("LivingAttrib_GetGrid", grid, equipType, itemid);
			Assert(result);
			GetISandboxActorSubsystem()->StoreGridData(actorEquipItemHC.mutable_iteminfo(), grid);
		}
		else
			return;
	}
	else
		return;

	GetGameNetManagerPtr()->sendToClient(uin, PB_ACTOR_EQUIP_ITEM_HC, actorEquipItemHC);
}

void MpActorTrackerEntry::checkPlayerChangeToSelf(IClientPlayer *player)
{
	// update backpack grids
	std::set<int> dirtyIndex;
	std::set<int> userdataIndex;
	dirtyIndex.swap(player->GetDirtyGridIndex());

	IBackPack *backpack = player->getIBackPack();

	PB_BackPackGridUpdateHC gridUpdateHC;

	for (auto it = dirtyIndex.begin(); it != dirtyIndex.end(); ++it)
	{
		int curindex = *it;

		//����˽�˱����Ϳ������װ������Ҳ���ǵ�ǰ�򿪵�����/worldcontainer, ��֪ͨ
		int base = int(curindex / GRID_INDEX_BASIS) * GRID_INDEX_BASIS;
		if (base != BACKPACK_START_INDEX &&
			base != backpack->getShortcutStartIndex() &&
			base != MOUSE_PICKITEM_INDEX &&
			base != EQUIP_START_INDEX &&
			base != RUNE_INLAY_CONTAINER_INDEX && //������֪ͨ code by:tanzhenyu
			base != RUNE_MERGE_CONTAINER_INDEX && //
			base != RUNE_AUTH_CONTAINER_INDEX && //
			base != POT_START_INDEX &&
			base != EXT_BACKPACK_START_INDEX &&
			base != player->getOpenContainerBaseIndex()) continue;

		BackPackGrid *srcgrid = backpack->index2Grid(curindex);

		RepeatedPtrField<PB_ItemData>* itemInfos = gridUpdateHC.mutable_iteminfo();
		GetISandboxActorSubsystem()->StoreGridData(itemInfos->Add(), srcgrid, curindex);

		if (gridUpdateHC.iteminfo_size() >= MAX_UPDATE_GRID_COUNT)
		{
			// 10������һ��
			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_BACKPACK_GRID_UPDATE_HC, gridUpdateHC);
			gridUpdateHC.Clear();
		}

		// ��¼��UserDataStr��Grid
		if (srcgrid && !srcgrid->userdata_str.empty())
		{
			userdataIndex.insert(curindex);
		}
	}
	if (gridUpdateHC.iteminfo_size() > 0)
	{
		// �������ݣ���������
		GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_BACKPACK_GRID_UPDATE_HC, gridUpdateHC);
	}

	//update attribs
	//PlayerAttrib *attrib = player->getPlayerAttrib();
	auto attrib = player->CastToActor()->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
	if (attrib)
	{
		bool result = attrib->Event2().Emit<int&, bool&, float&, int&, IClientPlayer*,float, float>("PlayerAttrib_UpdateAttrib", 
			mLastExp, mHPSent, mLastOxygen, mLastFoodLevel, player, mLastHP, mLastArmor);
		Assert(result);
	}

	// update userdata_str
	for (auto it = userdataIndex.begin(); it != userdataIndex.end(); ++it)
	{
		int curindex = *it;
		BackPackGrid *srcgrid = backpack->index2Grid(curindex);

		// ͬ�����ͻ����Լ�
		PB_ItemGridUserData itemGridUserData;
		itemGridUserData.set_uin(player->getUin());
		itemGridUserData.set_gridindex(curindex);
		itemGridUserData.set_userdatastr(srcgrid->userdata_str);

		GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_SYNC_GRIDUSERDATA_HC, itemGridUserData);
	}
}

void MpActorTrackerEntry::broadAttribChanges(IClientPlayer *target)
{
    OPTICK_EVENT();
	IActorLiving *living = dynamic_cast<IActorLiving *>(mEntryActor);
	//auto RidComp = mEntryActor->getRiddenComponent();
	auto RidComp = mEntryActor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
	if (living)
	{
		//LivingAttrib *livingattr = living->getLivingAttrib();
		auto livingattr = mEntryActor->getActorComponent(ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB);
		if(livingattr == NULL) return;
		bool change = false;
		for (int i = 0; i < MAX_EQUIP_SLOTS; ++i)
		{
			int itemid;
			if (RidComp && i == EQUIP_WEAPON)
			{
				bool isvehicleController = false;
				bool result = RidComp->Event2().Emit<bool&>("Ridden_IsVehicleController", isvehicleController);
				Assert(result);
				if (isvehicleController)
				{
					itemid = 0;
				}
			}
			//染色方块userdata_str改变时需要同步 by：Jeff
			bool needSyncColorItem = false;
			BackPackGrid* grid = nullptr;
			bool result = livingattr->Event2().Emit<BackPackGrid* &, int, int&>("LivingAttrib_GetGrid", grid, i, itemid);
			Assert(result);
			if (i == EQUIP_WEAPON && IsDyeableBlock(itemid) && grid->userdata_str != m_LastWeaponUserdata)
			{
				needSyncColorItem = true;
			}
			if (itemid != mLastEquipment[i] || (mEntryActor->isVehicleController() && i == EQUIP_WEAPON && m_NeedSyncHandModel) || needSyncColorItem)
			{
				PB_ActorEquipItemHC actorEquipItemHC;
				actorEquipItemHC.set_objid(mEntryActor->getObjId());
				actorEquipItemHC.set_slottype(i);

				GetISandboxActorSubsystem()->StoreGridData(actorEquipItemHC.mutable_iteminfo(), grid);

				if (!actorEquipItemHC.IsInitialized())
				{
					living = NULL;
				}

				if(target) 	GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_ACTOR_EQUIP_ITEM_HC, actorEquipItemHC, false, true);
				else
				{
					sendMsgToTrackingPlayers(PB_ACTOR_EQUIP_ITEM_HC, actorEquipItemHC, false, true);
					mLastEquipment[i] = itemid;
					change = true;
				}
				m_NeedSyncHandModel = false;
			}
		}

		if (change)
		{
			bool result = livingattr->Event2().Emit<>("LivingAttrib_SyncWeaponEnchant");
			Assert(result);
		}
	}

	IActorAttrib* actorAttr = mEntryActor->GetIActorAttrib();
	if (actorAttr)
	{
		float curhp = actorAttr->getHP();
		float curArmor = actorAttr->getArmor();
		float curExtraHp = actorAttr->getExtraHP();
		bool curOffLien = actorAttr->getOffLine();
		if (curhp != mLastHP || curArmor != mLastArmor || curExtraHp != mLastExtraHP  || curOffLien != mLastOffLine)
		{
			PB_ActorAttrChangeHC actorAttrChangeHC;
			actorAttrChangeHC.set_objid(mEntryActor->getObjId());
			actorAttrChangeHC.set_extarhp(curExtraHp);
			actorAttrChangeHC.set_hp(curhp);
			actorAttrChangeHC.set_behurttarget(mEntryActor->getBeHurtTargetID());
			actorAttrChangeHC.set_armor(curArmor);
			actorAttrChangeHC.set_offline(curOffLien);
			//�����������������Ϊ�����Ѫ�������������Ȱ�Ϊ���⸴������·�ߴ������ŵ�����״̬�����Ѫ����Ϣʹ��RELIABLE_ORDERED(������Ҫ���)
			if (mEntryActor->getObjId() < 0x100000000LL)
			{
				if(target) 
				{
					GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_ACTOR_ATTR_CHANGE_HC, actorAttrChangeHC, false, true, RELIABLE_ORDERED);
				}
				else
				{
					sendMsgToTrackingPlayers(PB_ACTOR_ATTR_CHANGE_HC, actorAttrChangeHC, false, true, RELIABLE_ORDERED);
				}
			}
			else
			{
				if(target) 	
				{
					GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_ACTOR_ATTR_CHANGE_HC, actorAttrChangeHC, false, true, curhp <= 0 && mLastHP > 0 ? RELIABLE_ORDERED : UNRELIABLE_SEQUENCED);
				}
				else 
				{
					sendMsgToTrackingPlayers(PB_ACTOR_ATTR_CHANGE_HC, actorAttrChangeHC, false, true, curhp <= 0 && mLastHP > 0 ? RELIABLE_ORDERED : UNRELIABLE_SEQUENCED);
				}
			}
			mHPSent = true;
			mLastArmor = curArmor;
			mLastOffLine = curOffLien;
			//mLastExtraHP = curExtraHp;
		}
	}

	if (mEntryActor->getReverse() != mbLastReverse)
	{
		mbLastReverse = mEntryActor->getReverse();

		PB_ActorReverseHC actorReverseHC;
		actorReverseHC.set_actorid(mEntryActor->getObjId());
		actorReverseHC.set_reverse(mbLastReverse);

		if(target) 	GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_ACTOR_REVERSE_HC, actorReverseHC, false, true, UNRELIABLE_SEQUENCED);
		else sendMsgToTrackingPlayers(PB_ACTOR_REVERSE_HC, actorReverseHC, false, true, UNRELIABLE_SEQUENCED);
	}

	if (mEntryActor->GetIBody())
	{
		IActorBody *body = mEntryActor->GetIBody();
		int seqid0 = body->getCurSeqID(0);
		int seqid1 = body->getCurSeqID(1);
		bool animChange = mLastAnim[0] != seqid0
			|| mLastAnim[1] != seqid1
			|| mLastActID != body->getActID()
			|| mLastCustomScale != mEntryActor->getCustomScale()
			|| mLastActIDTrigger != body->getActTriggerID()
			|| mLastAnimSeq != body->getAnimSeq()  //20210923 codeby:chenwei �Ż���д��ʽ
			|| mLastAnimWeapon != body->getCurAnimWeapon();

		//枪IDLE  codeby: 李元星
		bool upBodyCheck = mLastAnim[1] == SEQ_GUN_IDLE && mLastAnim[1] == body->getCurAnim(1);
		//其他可能的坑 ……  TODO
		//上半身动画的精细化检测
		bool updateUpBodyAnimSeqId = false;
		if (upBodyCheck)
		{
			if (mLastUpBodyAnimSeqId != body->getCurSeqID(1))
			{
				updateUpBodyAnimSeqId = true;
				if (!animChange) animChange = true;
			}
		}

		if (animChange)	
		{
			PB_ActorAnimHC actorAnimHC;
			actorAnimHC.set_actorid(mEntryActor->getObjId());
			actorAnimHC.set_changeflag(mEntryActor->getChangeFlag());
			if (mLastAnim[0] != seqid0)
			{
				actorAnimHC.set_anim(body->getCurAnim(0));
			}
			if (mLastAnim[1] != seqid1 || updateUpBodyAnimSeqId)
			{
				actorAnimHC.set_anim1(body->getCurAnim(1));
			}
			if (mLastAnimWeapon != body->getCurAnimWeapon())
			{
				actorAnimHC.set_animweapon(body->getCurAnimWeapon());
			}
			if (mLastActID != body->getActID())
			{
				actorAnimHC.set_actid(body->getActID());
			}
			if (mLastCustomScale != mEntryActor->getCustomScale() && mEntryActor->getCustomScale() > 0.0001)
			{
				actorAnimHC.set_customscale(mEntryActor->getCustomScale());
			}
			if (mLastActIDTrigger != body->getActTriggerID())
			{
				actorAnimHC.set_actidtrigger(body->getActTriggerID());
			}
			//20210923 codeby:chenwei ���кŲ�ֱͬ��ͬ�����¶���״̬
			if (mLastAnimSeq != body->getAnimSeq())
			{
				actorAnimHC.set_anim(body->getCurAnim(0));
				actorAnimHC.set_anim1(body->getCurAnim(1));
				actorAnimHC.set_actid(body->getActID());		
				actorAnimHC.set_animseq(body->getAnimSeq());
			}
			//2021-09-14 codeby:chenwei ���ø�����״̬
			actorAnimHC.set_sideact(body->isSideAct());

			if (target)
			{
				GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_ACTOR_ANIM_HC, actorAnimHC, false, true, UNRELIABLE_SEQUENCED);
			}
			else
			{
				sendMsgToTrackingPlayers(PB_ACTOR_ANIM_HC, actorAnimHC, false, true, UNRELIABLE_SEQUENCED);
				mLastAnim[0] = seqid0;
				mLastAnim[1] = seqid1;
				mLastAnimWeapon = body->getCurAnimWeapon();
				mLastActID = body->getActID();
				mLastCustomScale = mEntryActor->getCustomScale();
				mLastActIDTrigger = body->getActTriggerID();
				mLastAnimSeq = body->getAnimSeq();
				mLastUpBodyAnimSeqId = body->getCurSeqID(1);
			}
		}
	}

	WCoord railknot;
	int outindex;
	float t;
	int flags;
	if (mEntryActor->getOnRailState(railknot, outindex, t, flags))
	{
		WORLD_ID headcar, tailcar;
		bool result = mEntryActor->Event2().Emit<WORLD_ID&, WORLD_ID&>("TrainCar_GetValue", headcar, tailcar);
		Assert(result);

		//ActorTrainCar *car = static_cast<ActorTrainCar *>(mEntryActor);
		bool synpos = true;
		bool curhead = headcar != 0;
		bool curtail = tailcar != 0;
		if (mLastOnRail && curhead == mLastHeadCar && curtail == mLastTailCar)
		{
			if (curhead)
			{
				synpos = false;
			}
			else if (mLastRailKnot == railknot && mLastRailOutIndex == outindex && Rainbow::Abs(t - mLastRailT) < 0.1f)
			{
				synpos = false;
			}
		}

		if (synpos)
		{
			PB_TrainMoveHC trainMoveHC;
			trainMoveHC.set_objid(mEntryActor->getObjId());
			trainMoveHC.set_mapid(mEntryActor->getCurMapID());
			trainMoveHC.set_outindex(outindex);
			trainMoveHC.set_curvet(t);
			trainMoveHC.set_carreverse(flags & 1);
			trainMoveHC.set_headcar(headcar);
			trainMoveHC.set_tailcar(tailcar);

			PB_Vector3* railKnot = trainMoveHC.mutable_railknot();
			railKnot->set_x(railknot.x);
			railKnot->set_y(railknot.y);
			railKnot->set_z(railknot.z);
			if(target)
			{
				GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_TRAIN_MOVE_HC, trainMoveHC, false, true, UNRELIABLE_SEQUENCED);
			}
			else
				sendMsgToTrackingPlayers(PB_TRAIN_MOVE_HC, trainMoveHC, false, true, UNRELIABLE_SEQUENCED);

			mLastOnRail = true;
			mLastHeadCar = headcar != 0;
			mLastTailCar = tailcar != 0;
			mLastRailKnot = railknot;
			mLastRailOutIndex = outindex;
			mLastRailT = t;
		}
		mLastPosition = mEntryActor->getPosition();
		return;
	}
	else mLastHeadCar = mLastTailCar = false;

	IActorLocoMotion* locmove = mEntryActor->getILocoMotion();
	IClientPlayer* entryPlayer = dynamic_cast<IClientPlayer*>(mEntryActor);

	if (entryPlayer && entryPlayer->IsOnPlatform())
	{
		IClientActor* plat = entryPlayer->GetPlatformIClientActor();
		auto playerT = mEntryActor->GetTransform();
		if (plat && playerT)
		{
			auto playerPT = playerT->GetParent();
			auto platT = plat->GetTransform();
			if (platT && playerPT == platT)
			{
				auto worldPosPlayer = mEntryActor->GetWorldPosition();
				auto localPos = platT->InverseTransformPoint(worldPosPlayer);
				WCoord curOnPlatLocalPos;
				curOnPlatLocalPos.x = (int)localPos.x;
				curOnPlatLocalPos.y = (int)localPos.y;
				curOnPlatLocalPos.z = (int)localPos.z;
				WCoord dpOnPlat = curOnPlatLocalPos - mLastOnPlatPos;
				int curpitch = AngleFloat2Char(locmove->GetRotationPitch());
				int curyaw = AngleFloat2Char(locmove->GetRotateYaw());
				int dyaw = (int)(curyaw - mLastRot.x);
				int dpitch = (int)(curpitch - mLastRot.y);
				bool rot_enough = (dyaw * dyaw >= ASQ || dpitch * dpitch >= ASQ);
				if (dpOnPlat.lengthSquared() > 16 || rot_enough)
				{
					PB_ActorMoveV2HC actorMoveHC;
					uint64_t  objid = mEntryActor->getObjId();
					objid = GetISandboxActorSubsystem()->PackObjId(objid);
					actorMoveHC.set_objid(objid);
					uint32_t yaw_pitch = 0;
					yaw_pitch = ((curyaw) & 0xff) << 8;
					yaw_pitch |= (curpitch & 0xff);
					actorMoveHC.set_yaw_pitch(yaw_pitch);
					actorMoveHC.add_position(curOnPlatLocalPos.x);
					actorMoveHC.add_position(curOnPlatLocalPos.y);
					actorMoveHC.add_position(curOnPlatLocalPos.z);
					mLastRot.x = (float)curyaw;
					mLastRot.y = (float)curpitch;
					mLastOnPlatPos = curOnPlatLocalPos;
					sendMsgToTrackingPlayers(PB_PHYSICS_COM_PLAT_LOCAL_POS, actorMoveHC);
				}
				return;
			}
		}
	}

	//实体
	if (mEntryActor->IsObject())
	{
		//子节点不track,只依赖父节点的同步
		if (mEntryActor->GetParentWID() != 0)
			return;

		//有运动器的节点，不track位置
		if (mEntryActor->IsControlByScript())
			return;
	}

	WCoord curpos = locmove->getPosition();
	WCoord dp = curpos - mLastPosition;

	int curpitch, curyaw;
	Rainbow::Quaternionf curquat;
	bool rot_enough = false;
	bool full_rot = locmove->needFullRotation();
	float rotdiff = 0;
	if(full_rot)
	{
		locmove->getRotation(curquat);
		rotdiff = Dot(curquat, mLastRot);
		rot_enough = rotdiff < (QUAT_SQ_VEHICLE);
	}
	else
	{
		curpitch = AngleFloat2Char(locmove->GetRotationPitch());
		curyaw = AngleFloat2Char(locmove->GetRotateYaw());
		int dyaw = (int)(curyaw - mLastRot.x);
		int dpitch = (int)(curpitch - mLastRot.y);
		rot_enough = (dyaw*dyaw>=ASQ || dpitch*dpitch>=ASQ);
	}


	bool syncpos = false;
	if (mLastOnRail) syncpos = true;
	mLastOnRail = false;
	
	auto FuncWrapper = mEntryActor->getActorFuncWrapper();
	bool mustSync = false;
	if (FuncWrapper)
	{
		mustSync = FuncWrapper->getMustSyncPos();
	}
	bool isriding = false;
	if (RidComp)
	{
		bool result = RidComp->Event2().Emit<bool&>("Ridden_IsRiding", isriding);
		Assert(result);
	}
	if (isriding)
	{
		if (rot_enough || (mUpdateCount % 60) == 0)
		{
			syncpos = true;
		}
	}
	else
	{
		int iDuration = 60;
		auto functionWrapper = mEntryActor->getActorFuncWrapper();
		if (full_rot)
		{
#ifdef SERVER_NET_OPT
			if (rot_enough || dp.lengthSquared() > (DPSQ/2) || (FuncWrapper && FuncWrapper->getMustSyncPos()))
#else
			if ((mUpdateCount % iDuration) == 0 || rot_enough || dp.lengthSquared() > (DPSQ/2) || (functionWrapper && functionWrapper->getMustSyncPos()))
#endif				
			{
				syncpos = true;

				if (functionWrapper)
				{
					functionWrapper->setMustSyncPos(false);
				}
			}
		}
		else
		{
#ifdef SERVER_NET_OPT
			if (rot_enough || dp.lengthSquared() > DPSQ || (FuncWrapper && FuncWrapper->getMustSyncPos()))
#else			
			if ((mUpdateCount % iDuration) == 0 || rot_enough || dp.lengthSquared() > DPSQ || (functionWrapper && functionWrapper->getMustSyncPos()))
#endif				
			{
				syncpos = true;

				if (functionWrapper)
				{
					functionWrapper->setMustSyncPos(false);
				}
			}	
		}
	}

	if (mEntryActor->getObjType() == OBJ_TYPE_VEHICLE)
	{
		//逻辑代码移动到VehicleAssembleLocoMotion中
		mEntryActor->getILocoMotion()->SetVehicleAssembleLocoMotionData(m_vLastPosition, syncpos, m_vLastRot, rot_enough, QUAT_SQ_VEHICLE, DPSQ);
	}

	if (syncpos)
	{
		mUpdateCount = mUpdateCount % 60 ? mUpdateCount : 0;
		if (mEntryActor->getObjType() == OBJ_TYPE_VEHICLE)
		{
			PB_VehicleMoveHC vehicleMoveHC;
			vehicleMoveHC.set_objid(mEntryActor->getObjId());

			//逻辑代码移动到VehicleAssembleLocoMotion中
			mEntryActor->getILocoMotion()->BroadVehicleAssembleLocoMotionData(&vehicleMoveHC, mEntryActor);
	
			if(target) 	
				GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_VEHICLE_MOVE_HC, vehicleMoveHC, false, true, UNRELIABLE_SEQUENCED);
			else
			{
				sendMsgToTrackingPlayers(PB_VEHICLE_MOVE_HC, vehicleMoveHC, false, true, UNRELIABLE_SEQUENCED);

				if(full_rot) mLastRot = curquat;
				else
				{
					mLastRot.x = (float)curyaw;
					mLastRot.y = (float)curpitch;
				}
				mLastPosition = curpos;
			}
		}
		else
		{
			if (full_rot)
			{
				bool send = false;
				PB_FullrotActorMoveHC actorMoveHC;
				actorMoveHC.set_objid(mEntryActor->getObjId());
				if (!target && !mustSync && rot_enough)
				{
					send = true;
					actorMoveHC.set_yaw(curquat.ToUInt32());
					mLastRot = curquat;
				}
				if (!locmove->getOnGround())
				{
					actorMoveHC.set_changeflags(0);
				}
				if (!target && !mustSync && dp.lengthSquared() > (DPSQ / 2))
				{
					send = true;
					PB_Vector3* pos = actorMoveHC.mutable_position();
					pos->set_x(curpos.x);
					pos->set_y(curpos.y);
					pos->set_z(curpos.z);
					mLastPosition = curpos;
				}
				if (mustSync)
				{
					send = true;
					actorMoveHC.set_yaw(curquat.ToUInt32());
					PB_Vector3* pos = actorMoveHC.mutable_position();
					pos->set_x(curpos.x);
					pos->set_y(curpos.y);
					pos->set_z(curpos.z);
				}

				if (target)
				{
					actorMoveHC.set_yaw(curquat.ToUInt32());
					PB_Vector3* pos = actorMoveHC.mutable_position();
					pos->set_x(curpos.x);
					pos->set_y(curpos.y);
					pos->set_z(curpos.z);
					GameNetManager::getInstance()->sendToClient(target->getUin(), PB_FULLROT_ACTOR_MOVE_HC, actorMoveHC, false, true, UNRELIABLE_SEQUENCED);
				}
				else if (send)
				{
					sendMsgToTrackingPlayers(PB_FULLROT_ACTOR_MOVE_HC, actorMoveHC, false, true, UNRELIABLE_SEQUENCED);
				}
			}
			else
			{
				bool send = false;
				PB_ActorMoveV2HC actorMoveHC;
				uint64_t         objid = mEntryActor->getObjId();
				objid = GetISandboxActorSubsystem()->PackObjId(objid);
				actorMoveHC.set_objid(objid);
				if (!target && !mustSync && rot_enough)
				{
					send = true;
					uint32_t yaw_pitch = 0;
					yaw_pitch = ((curyaw) & 0xff) << 8;
					yaw_pitch |= (curpitch & 0xff);
					actorMoveHC.set_yaw_pitch(yaw_pitch);
					mLastRot.x = (float)curyaw;
					mLastRot.y = (float)curpitch;
				}

				if (!locmove->getOnGround())
				{
					actorMoveHC.set_changeflags(0);
				}
				if (!target && !mustSync && dp.lengthSquared() > (DPSQ))
				{
					send = true;
					actorMoveHC.add_position(curpos.x);
					actorMoveHC.add_position(curpos.y);
					actorMoveHC.add_position(curpos.z);
					mLastPosition = curpos;
				}

				if (mustSync)
				{
					send = true;
					uint32_t yaw_pitch = 0;
					yaw_pitch = ((curyaw) & 0xff) << 8;
					yaw_pitch |= (curpitch & 0xff);
					actorMoveHC.set_yaw_pitch(yaw_pitch);
					actorMoveHC.add_position(curpos.x);
					actorMoveHC.add_position(curpos.y);
					actorMoveHC.add_position(curpos.z);
				}

				if (target)
				{
					uint32_t yaw_pitch = 0;
					yaw_pitch = ((curyaw) & 0xff) << 8;
					yaw_pitch |= (curpitch & 0xff);
					actorMoveHC.set_yaw_pitch(yaw_pitch);
					actorMoveHC.add_position(curpos.x);
					actorMoveHC.add_position(curpos.y);
					actorMoveHC.add_position(curpos.z);
					//GameNetManager::getInstance()->sendToClient(target->getUin(), PB_ACTOR_MOVEV2_HC, actorMoveHC, false, true, UNRELIABLE_SEQUENCED);
					GetGameNetManagerPtr()->cacheActorMove(target->getUin(), objid, actorMoveHC);
				}
				else if (send)
				{
					auto iter = mTrackingPlayersTable.iterate(nullptr);
					for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
					{
						IClientPlayer* pp = iter->key;
						if (pp != NULL) {
							GetGameNetManagerPtr()->cacheActorMove(pp->getUin(), objid, actorMoveHC);
						}
					}

					//sendMsgToTrackingPlayers(PB_ACTOR_MOVEV2_HC, actorMoveHC, false, true, UNRELIABLE_SEQUENCED);
				}
			}
		}

	}
}

void MpActorTrackerEntry::broadAnimChanges(bool checkChange)
{
	std::vector<int> uinList;
	auto iter = mTrackingPlayersTable.iterate(nullptr);
	for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
	{
		IClientPlayer* pp = iter->key;
		if (pp != NULL) {
			uinList.push_back(pp->getUin());
		}
	}

	if (uinList.empty())
	{
		return;
	}
	broadAnimChanges(uinList.data(), uinList.size(), checkChange);
}

void MpActorTrackerEntry::broadPlayAnim(int anim, int anim1, int loopmode)
{
	std::vector<int> uinList;
	auto iter = mTrackingPlayersTable.iterate(nullptr);
	for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
	{
		IClientPlayer* pp = iter->key;
		if (pp != NULL) {
			uinList.push_back(pp->getUin());
		}
	}

	if (uinList.empty())
	{
		return;
	}

	PB_ActorAnimHC actorAnimHC;
	actorAnimHC.set_actorid(mEntryActor->getObjId());
	actorAnimHC.set_changeflag(mEntryActor->getChangeFlag());
	actorAnimHC.set_anim(anim);
	actorAnimHC.set_anim1(anim1);
	actorAnimHC.set_isloop(loopmode);
	GameNetManager::getInstance()->sendToClientMulti(uinList.data(), uinList.size(), PB_ACTOR_ANIM_HC, actorAnimHC);

	mLastAnim[0] = anim;
	mLastAnim[1] = anim1;
}

void MpActorTrackerEntry::broadAnimChanges(int* uinList, int count, bool checkChange)
{
	IActorBody* body = mEntryActor->GetIBody();
	if (body)
	{
		PB_ActorAnimHC actorAnimHC;
		actorAnimHC.set_actorid(mEntryActor->getObjId());
		actorAnimHC.set_changeflag(mEntryActor->getChangeFlag());
		if (!checkChange)
		{
			actorAnimHC.set_anim(body->getCurAnim(0));
			actorAnimHC.set_anim1(body->getCurAnim(1));
			actorAnimHC.set_animweapon(body->getCurAnimWeapon());
			actorAnimHC.set_actid(body->getActID());
			actorAnimHC.set_customscale(mEntryActor->getCustomScale());
			actorAnimHC.set_actidtrigger(body->getActTriggerID());
		}
		else
		{
			if (mLastAnim[0] != body->getCurAnim(0))
			{
				actorAnimHC.set_anim(body->getCurAnim(0));
			}
			if (mLastAnim[1] != body->getCurAnim(1))
			{
				actorAnimHC.set_anim1(body->getCurAnim(1));
			}
			if (mLastAnimWeapon != body->getCurAnimWeapon())
			{
				actorAnimHC.set_animweapon(body->getCurAnimWeapon());
			}
			if (mLastActID != body->getActID())
			{
				actorAnimHC.set_actid(body->getActID());
			}
			if (mLastCustomScale != mEntryActor->getCustomScale() && mEntryActor->getCustomScale() > 0.0001)
			{
				actorAnimHC.set_customscale(mEntryActor->getCustomScale());
			}
			if (mLastActIDTrigger != body->getActTriggerID())
			{
				actorAnimHC.set_actidtrigger(body->getActTriggerID());
			}
			if (mLastAnimSeq != body->getAnimSeq())
			{
				actorAnimHC.set_anim(body->getCurAnim(0));
				actorAnimHC.set_anim1(body->getCurAnim(1));
				actorAnimHC.set_actid(body->getActID());		
				actorAnimHC.set_animseq(body->getAnimSeq());
			}
			actorAnimHC.set_sideact(body->isSideAct());
		}
		GameNetManager::getInstance()->sendToClientMulti(uinList, count, PB_ACTOR_ANIM_HC, actorAnimHC);

		mLastAnim[0] = body->getCurAnim(0);
		mLastAnim[1] = body->getCurAnim(1);
		mLastAnimWeapon = body->getCurAnimWeapon();
		mLastActID = body->getActID();
		mLastAnimSeq = body->getAnimSeq();
		mLastActIDTrigger = body->getActTriggerID();
	}
}


void MpActorTrackerEntry::gentAttribChanges()
{
	m_SyncDataDiff->setVolid(false);
	//m_SyncDataNow->setVolid(false);
	mEntryActor->getsyncData(*m_SyncDataNow->object_value_);

	cmpTwoJson(m_SyncDataPre, m_SyncDataNow, m_SyncDataDiff);
	//refreshVolid(*m_SyncDataDiff);
	jsonxx::Value *temp = m_SyncDataNow;
	m_SyncDataNow = m_SyncDataPre;
	m_SyncDataPre = temp;
}
void MpActorTrackerEntry::sendActorMotion2client(bool include_me)
{
	PB_ActorMotionV2HC actorMotionHC;

	uint64_t objid = mEntryActor->getObjId();
	objid = GetISandboxActorSubsystem()->PackObjId(objid);
	actorMotionHC.set_objid(objid);

	const Rainbow::Vector3f& motion = mEntryActor->getILocoMotion()->GetMotion();
	actorMotionHC.set_x(motion.x * 1000);
	actorMotionHC.set_y(motion.y * 1000);
	actorMotionHC.set_z(motion.z * 1000);
	if (mEntryActor->isChangePos())
	{
		actorMotionHC.set_ischangepos(true);
	}

	sendMsgToTrackingPlayers(PB_ACTOR_MOTIONV2_HC, actorMotionHC, include_me, UNRELIABLE_SEQUENCED);
	mEntryActor->clearMotionChange();
}
void MpActorTrackerEntry::updateTracker()
{
	if (mEntryActor == nullptr) return;
	//LOG_INFO("MpActorTrackerEntry::updateTracker(): mEmptyActor = %d", mEntryActor->getObjId());
	assert(!mShouldRemoveThis);

	/*原来的这段删去了。是沙盒层早已不用的代码屏蔽,影响阅读。*/
	/*xxxxxxx*/
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		if ((mUpdateCount % mUpdateFrequency) == 0)
		{
			IClientPlayer* curplayer = dynamic_cast<IClientPlayer*>(mEntryActor);
			if (curplayer && !curplayer->hasUIControl()
				)
			{
				checkPlayerChangeToSelf(curplayer);
			}
		}
		++mUpdateCount;
	}
	else 
	{
		IClientActor *actor = nullptr;
		//auto bindAComponent = mEntryActor->m_pBindActorComponent;
		auto bindAComponent = mEntryActor->getActorComponent(ComponentType::COMPONENT_BIND_ACTOR);
		if (bindAComponent)
		{
			bool result = bindAComponent->Event2().Emit<IClientActor*>("BindActor_getTarget", actor);
			Assert(result);
		}

		IClientActor* riddenactor = nullptr;
		bool isRidden = false;
		auto RidComp = mEntryActor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
		if (RidComp)
		{
			bool result = RidComp->Event2().Emit<bool&, IClientActor*&>("Ridden_getRiddenByActor", isRidden, riddenactor);
			Assert(result);
		}

		if (!mInitialUpdated)
		{
			mInitialUpdated = true;

			mLastPosition = mEntryActor->getPosition();
			mLastRot = Rainbow::Quaternionf::identity; //TODO get pitch and ya
			return;
		}
		else if ((mUpdateCount % mUpdateFrequency) == 0 || mEntryActor->getObjType() == OBJ_TYPE_ROCKET)
		{
			IClientPlayer* curplayer = dynamic_cast<IClientPlayer*>(mEntryActor);
			if (curplayer && !curplayer->hasUIControl()
				)
			{
				checkPlayerChangeToSelf(curplayer);
			}

			checkTrackingPlayersChange();

			if (mEntryActor->needSyncAttribChanges()) broadAttribChanges(NULL);
		}
		else if (isRidden)
		{
			IClientPlayer *curplayer = dynamic_cast<IClientPlayer *>(riddenactor);
			if (curplayer && (!curplayer->hasUIControl() || GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false)))
			{
				broadAttribChanges(curplayer);
			}
		}
		else if (actor)
		{
			IClientPlayer *curplayer = dynamic_cast<IClientPlayer *>(actor);
			if (curplayer && !curplayer->hasUIControl()) broadAttribChanges(curplayer);
		}

		++mUpdateCount;

		if (mEntryActor->isMotionChange())
		{
			// PB_ActorMotionHC actorMotionHC;
			// actorMotionHC.set_objid(mEntryActor->getObjId());

			// const Rainbow::Vector3f &motion = mEntryActor->getLocoMotion()->m_Motion;
			// actorMotionHC.set_x(motion.x);
			// actorMotionHC.set_y(motion.y);
			// actorMotionHC.set_z(motion.z);
			// if (mEntryActor->isChangePos())
			// {
			// 	actorMotionHC.set_ischangepos(true);
			// }

			// sendMsgToTrackingPlayers(PB_ACTOR_MOTION_HC, actorMotionHC, true, UNRELIABLE_SEQUENCED);

			// mEntryActor->clearMotionChange();
			bool include_me = true;
			if (mEntryActor->isPlayer())
			{
				// ClientPlayer* curplayer = dynamic_cast<ClientPlayer*>(mEntryActor);
				IClientPlayer* curplayer = dynamic_cast<IClientPlayer*>(mEntryActor);
				if (curplayer && !curplayer->hasUIControl() && curplayer->isMoveControlActive()
					 && curplayer->isMotionChangeSyncPos() && curplayer->getCurToolID()!=12006)  // TODO: 12006 临时处理
				{
					curplayer->syncPos2Client(true);
					include_me = false;
				}
			}
			sendActorMotion2client(include_me);
		}
		if (mHPSent) {
			auto attr = mEntryActor->GetIActorAttrib();
			if (attr) {
				mLastHP = attr->getHP();
				mLastExtraHP = attr->getExtraHP();
			}
			mHPSent = false;
		}
	}
}

void MpActorTrackerEntry::sendMsgToTrackingPlayers(ePBMsgCode pbCode, const Message &pbData, bool include_me, bool except_local, PacketReliability reliability /*= RELIABLE_ORDERED*/)
{
	auto iter = mTrackingPlayersTable.iterate(nullptr);

	// �Ż� �����ڴ���� codeby:liusijia 20210926
	static PB_PACKDATA packData(0, 1);
	packData.setInfo(pbCode, pbData.ByteSize());

	pbData.SerializeToArray(packData.MsgData, packData.ByteSize);
	int sendCnt = 0;

	for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
	{
		IClientPlayer *player = iter->key;
		if(player != NULL){
			sendCnt++;
			GetGameNetManagerPtr()->sendToClientByData(player->getUin(), &packData, 0, except_local, reliability);
		}
	}

	if (include_me)
	{
		IClientPlayer *player = dynamic_cast<IClientPlayer *>(mEntryActor);
		if (player) {
			sendCnt++;
			GetGameNetManagerPtr()->sendToClientByData(player->getUin(), &packData, 0, except_local, reliability);
		}
	}

	//--//==//--Net-SEND
	SDB_NETMONITOR_SEND((unsigned)(pbCode),(unsigned)(packData.ByteSize + PB_PROTO_HEAD_LEN+9), pbCode==7000?(((PB_Custom_Msg*)(&pbData))->msgname()):"", sendCnt)

	// ʹ�ú��ÿ�ʹ�õĲ��� 
	packData.resetMem();
}

void MpActorTrackerEntry::removeTrackingPlayer(IClientPlayer *player)
{
	mTrackingPlayersTable.erase(player);
}

void MpActorTrackerEntry::clearTrackingPlayer()
{
	mTrackingPlayersTable.clear();
}

void MpActorTrackerEntry::forceTrack()
{
	if (!mInitialUpdated)
	{
		mInitialUpdated = true;
		if(mEntryActor){
			mLastPosition = mEntryActor->getPosition();
		}
		mLastRot = Rainbow::Quaternionf::identity; //TODO get pitch and ya
	}

	updateTracker();

// 	mUpdateCount = 1;
}

//// 新增一个方法来处理玩家在线的逻辑
//void MpActorTrackerEntry::onPlayerOnline()
//{
//	 return;
//	clearTrackingPlayer();
//	// 强制更新
//	mInitialUpdated = false;
//	if (mEntryActor)
//	{
//		mLastPosition = mEntryActor->getPosition();
//	}
//
//	//  遍历所有其他玩家，找到在范围内的，添加到跟踪列表，并发送给自己
//	if (mEntryPlayer) {
//		for (size_t i = 0; i != mEntryActor->GetActorMgrInterface()->getNumPlayer(); ++i)
//		{
//			IClientPlayer* otherPlayer = mEntryActor->GetActorMgrInterface()->iGetIthPlayer(i);
//			if (otherPlayer == nullptr || otherPlayer == mEntryPlayer) continue;
//
//			if (inTrackingDistance(mLastPosition, otherPlayer->CastToActor()->getPosition()))
//			{
//				//  添加到跟踪列表
//				mTrackingPlayersTable.insert(otherPlayer, true);
//				// 发送 "其他玩家" 给 "自己" (mEntryPlayer)
//				int Uin = mEntryPlayer->getUin();
//				sendPlayerEnterToClient(&Uin, 1, otherPlayer);
//			}
//		}
//	}
//}

// ���ö���������Ϣ �Ա�ǿ�Ʒ��Ͷ����ֶ�
void MpActorTrackerEntry::resetAniSendState()
{
	mLastAnim[0] = 0;
	mLastAnim[1] = -1;
	mLastActID = -1;
	mLastActIDTrigger = -1;

	if (m_SyncDataPre && m_SyncDataPre->object_value_)
	{
		*m_SyncDataPre->object_value_ << "anim" << 0;
		*m_SyncDataPre->object_value_ << "anim1" << -1;
		*m_SyncDataPre->object_value_ << "actid" << -1;
		*m_SyncDataPre->object_value_ << "actidtrugger" << -1;
	}
}
