
#include "LightningMgr.h"
#include "BlockMeshVert.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "world.h"
#include "blocks/special_blockid.h"
#include "IClientActor.h"
#include "IActorAttrib.h"
#include "IClientMob.h"
#include "WorldManager.h"
#include "IPlayerControl.h"
#include "Optick/optick.h"

#include "EffectManager.h"
#include "IClientMob.h"
using namespace MINIW;

extern void AddVertToArray(std::vector<BlockGeomVert>&vertarray, const Rainbow::Vector3f &pos, float u, float v);

LightningMgr::LightningMgr(World *pworld) : m_pWorld(pworld)
{
	m_vecLightning.clear();
}

LightningMgr::~LightningMgr()
{

}

void LightningMgr::update(unsigned int dtick)
{
	OPTICK_EVENT();
	auto iter = m_vecLightning.begin();
	for (; iter != m_vecLightning.end(); )
	{
		WCoord startPos = iter->startPos;
		WCoord endPos = iter->endPos;

		endPos.y = startPos.y - LIGHTNING_WIDTH;  //闪电头长256
		if(iter->isShow)
			addRect(LIGHTNING_HEAD, startPos, endPos);

		float totalTime = 10;
		if (iter->curTick > 10)
		{
			startPos.y -= LIGHTNING_WIDTH;  //闪电中间部分的起点

			float length = iter->startPos.y - iter->endPos.y - 2*LIGHTNING_WIDTH;  //去掉闪电头尾的长度
			totalTime = length * 0.025f; // 闪电中间部分的总时长

			float ratio = (iter->curTick - 10) / totalTime;
			if (ratio > 1) ratio = 1;
			endPos.y = startPos.y - length* ratio;
			LOG_INFO("Lightning middle y:%d tick:%d", endPos.y, iter->curTick);
			if (iter->isShow)
				addRect(LIGHTNING_MIDDLE, startPos, endPos);

			if (iter->curTick > (totalTime+20))
			{
				startPos.y = iter->endPos.y + LIGHTNING_WIDTH;  //闪电尾部部分的起点
				endPos.y = iter->endPos.y;
				if (iter->isShow)
					addRect(LIGHTNING_TAIL, startPos, endPos);

				if (!m_pWorld->isRemoteMode() && !iter->createEffect)
				{
					iter->createEffect = true;

					if (m_pWorld->getEffectMgr())
					{
						int x = (startPos.x + endPos.x) / 2;
						int z = (startPos.z + endPos.z) / 2;
						m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/shandian.ent", WCoord(x, iter->endPos.y, z), 40);
					}


					WCoord blockPos = CoordDivBlock(iter->endPos);
					int blockid = m_pWorld->getBlockID(blockPos);
					if (blockid > 0)
					{
						//m_pWorld->setBlockAll(WCoord(blockPos.x, blockPos.y + 1, blockPos.z), BLOCK_FIRE, 0);

					}

					effectToActor(&iter->startPos, &iter->endPos);
				}
			}
		}


		if(iter->curTick >= (totalTime + 400))
			iter = m_vecLightning.erase(iter);
		else
		{
			if (iter->curTick >= (totalTime + 200) && iter->isShow)
			{
				float sound = 0.5f;
				if (GetIPlayerControl())
				{
					float length = (GetIPlayerControl()->GetPlayerControlPosition() - iter->startPos).length() / BLOCK_SIZE / SECTION_BLOCK_DIM;
					if (length < 2.0f)
						sound = 2.0f;
					else if (length < 5.0f)
					{
						sound = 2.0f - (length - 2.0f) / 2;
					}
				}
				if (m_pWorld->getEffectMgr())
					m_pWorld->getEffectMgr()->playSound(iter->startPos, "env.thunder", sound, 2.0f, PLAYSND_LONGDIST);

				iter->isShow = false;

			}

			iter->curTick += dtick;
			iter++;
		}
	}
}

void LightningMgr::addRect(int type, WCoord startPos, const WCoord &endPos)
{
	std::vector<BlockGeomVert> array;
	std::vector<unsigned short>indices;

	array.clear();
	indices.clear();

	float width = BLOCK_SIZE;
	float length = startPos.y - endPos.y;

	float maxU = 1.0f;
	//if(type == LIGHTNING_MIDDLE)
	//	maxU = 0.17;

	float maxV = 1.0f;
	int addRectNum = 1;
	if (type == LIGHTNING_MIDDLE)
	{
		addRectNum = (length / (7* LIGHTNING_WIDTH)) + 1;
	}

	int id = CURVEFACEMTL_LIGHTNING1;
	if (type == LIGHTNING_MIDDLE)
		id = CURVEFACEMTL_LIGHTNING2;
	else if (type == LIGHTNING_TAIL)
		id = CURVEFACEMTL_LIGHTNING3;

	int startY = startPos.y;
	for (int k = 0; k < addRectNum; k++)
	{
		array.clear();
		indices.clear();
		float uvMul = 1.0f;
		startPos.y = startY - k * (7 * LIGHTNING_WIDTH);
		if (type == LIGHTNING_MIDDLE)
		{
			if (k + 1 == addRectNum)
			{
				length = startY - endPos.y - k * 7 * LIGHTNING_WIDTH;
				maxV = length/ LIGHTNING_WIDTH;
			}	
			else
			{
				length = 7 * LIGHTNING_WIDTH;
				maxV = 7;
			}	
		}

		for (int i = 0; i <= 1; i++)
		{
			float v = (i % 2) == 0 ? 0 : maxV;
			Rainbow::Vector3f pos1;
			pos1.x = (float)startPos.x;
			pos1.y = startPos.y - i * length;

			pos1.z = (float)startPos.z;

			Rainbow::Vector3f pos2;
			pos2.x = (float)endPos.x;
			pos2.y = startPos.y - i * length;
			pos2.z = endPos.z;

			GetISandboxActorSubsystem()->RailAddVertToArray(array, pos1, 0, v);
			GetISandboxActorSubsystem()->RailAddVertToArray(array, pos2, maxU, v);
		}

		for (int j = 0; j < 1; j++)
		{
			short baseindex = j * 2;
			indices.push_back(baseindex + 0);
			indices.push_back(baseindex + 2);
			indices.push_back(baseindex + 1);

			indices.push_back(baseindex + 1);
			indices.push_back(baseindex + 2);
			indices.push_back(baseindex + 3);
		}
		if (m_pWorld->getRender() && m_pWorld->getRender()->getCurveRender())
		m_pWorld->getRender()->getCurveRender()->addRect(id, WCoord(0, 0, 0), array, indices);

	}

}

void LightningMgr::effectToActor(WCoord *startPos, WCoord *endPos)
{
	if (!m_pWorld)
		return;
	int x = (startPos->x + endPos->x) / 2;
	int z = (startPos->z + endPos->z) / 2;
	WCoord minPos = WCoord(x - 1, endPos->y, z - 1);
	WCoord maxPos = WCoord(x + 1, startPos->y, z + 1);

	CollideAABB box;
	box.setPoints(minPos, maxPos);
	box.expand(100, 0, 100);

	std::vector<IClientActor *>tmpactors;
	m_pWorld->getActorsInBox(tmpactors, box);

	for (size_t i = 0; i < tmpactors.size(); i++)
	{
		auto attr = tmpactors[i]->GetIActorAttrib();
		if (attr)
		{
			IClientMob* mob = dynamic_cast<IClientMob*>(tmpactors[i]);
			if (mob != nullptr &&
				mob->IsInvulnerable()) {
				continue;
			}

			if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
			{
				if (tmpactors[i]->getObjType() != OBJ_TYPE_ROLE)
					attr->addHP(-30);
			}
			else
				attr->addHP(-30);
		}
	}
}

void LightningMgr::addLightning(WCoord *startPos, WCoord *endPos)
{
	Lightning lighting;
	lighting.startPos = WCoord(startPos->x, startPos->y, startPos->z);
	lighting.endPos = WCoord(endPos->x, endPos->y, endPos->z);

	m_vecLightning.push_back(lighting);
}

int LightningMgr::getLightningNum()
{
	return m_vecLightning.size();
}

float LightningMgr::getLightningImpactFactor(WCoord pos)
{
	pos.y = 0;
	float curFactor = 0.0f;
	auto iter = m_vecLightning.begin();
	for (; iter != m_vecLightning.end(); iter++)
	{
		WCoord pos2 = WCoord(iter->startPos.x, 0, iter->startPos.z);

		float factor = 1 - pos.distanceTo(pos2) / 14000;
		if (factor > curFactor)
			curFactor = factor;
	}

	return curFactor;
}