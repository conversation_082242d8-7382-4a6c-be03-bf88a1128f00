#include "CustomModelMgr.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "IClientPlayer.h"
#include "WorldManager.h"
#include "blocks/BlockMaterialMgr.h"
#include "blocks/container_world.h"
#include "CustomModelMgr_generated.h"
#include "IPlayerControl.h"
#include "GameNetManager.h"
#include "genCustomModel.h"
#include "IBackpack.h"
#include "FullyCustomModelMgr.h"
#include "BlockGeom.h"
#include "CustomModelPacking.h"
#include "PackingFullyCustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "GlobalFunctions.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include "OgreCrc32.h"
#include "OgreUtils.h"
#include "Platforms/PlatformInterface.h"
#include "OgreScriptLuaVM.h"

#include "LuaInterfaceProxy.h"
#include <time.h>
#include "Global.h"
#include <chrono>

#include "ResourceCenter.h"
#include "Jobs/JobTypes.h"
#include "Jobs/JobBatchDispatcher.h"
#include "Jobs/BackStageJob.h"
#include "Jobs/MainThreadJob.h"

#include "ClientGameManager.h"
#include "File/FileManager.h"
#include "Texture/LegacyTextureUtils.h"
#include "ClientInfoProxy.h"
#include "Texture/TextureRenderGen.h"
#include "Graphics/GeneratedTextures.h"
#include "SandboxCoreDriver.h"
#include "OgreStringUtil.h"
#include "voxelmodel.h"
#include "SandboxBodyComponent.h"
#include <chrono>
#include "WorldStringManagerProxy.h"
#include "IActorBody.h"
#include "SandboxIdDef.h"
#include "CustomModel.h"
#include "IClientGameManagerInterface.h"
#include "ImageMesh.h"
#include "PlayManagerInterface.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace MINIW;
using namespace std::chrono;

const int BODYTEX_WIDTH = 160;
const int BODYTEX_HEIGHT = 200;

#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#define O_BINARY (0)
#endif


CustomModelMgr* CustomModelMgr::getSingletonPtr()
{
	return CustomModelMgr::GetInstancePtr();
}
CustomModelMgr& CustomModelMgr::getSingleton()
{
	return CustomModelMgr::GetInstance();
}

struct CustomModelBGLoadTask 
{
	CustomModelBGLoadTask(long long owid, std::string filename,
		int realOwnerUin, std::string modRoot,
		int type, int reType, CustomModel* pcostommodel,
		bool isLast, std::function<void(std::string)> fn, int specialType) : m_owid(owid), m_fileName(filename),
		m_realOwnerUin(realOwnerUin), m_modRoot(modRoot)
		, m_type(type), m_reType(reType), m_pCustomModel(pcostommodel),
		m_isLoadingResult(false), m_isLastTask(isLast), m_nSpecialType(specialType), m_pgeom(nullptr)
	{
		m_fn = std::move(fn);
	}
	~CustomModelBGLoadTask() 
	{
		m_fileName = "";
		m_modRoot = "";
	}
	void DoInJobThread()
	{
		if (m_type == RES_MODEL_CLASS)
		{
			if (m_pCustomModel && m_pCustomModel->loadInJobThread(-1, m_fileName, 0, "", false, 0, &m_pgeom))
			{
				m_isLoadingResult = true;
			}
		}
		else if (m_type == MAP_MODEL_CLASS)
		{
			if (m_pCustomModel && m_pCustomModel->loadInJobThread(m_owid, m_fileName, m_realOwnerUin, m_modRoot, false, m_nSpecialType, &m_pgeom))
			{
				m_isLoadingResult = true;
			}
		}
		else if (m_type == EQUIP_MODEL_CLASS)
		{
			if (m_pCustomModel && m_pCustomModel->loadInJobThread(-2, m_fileName, 0, "", false, 0, &m_pgeom))
			{
				m_isLoadingResult = true;
			}
		}
	}
	void DoInMainThread()
	{
        OPTICK_EVENT();
		if (!m_isLoadingResult || !mgr)
		{
			ENG_DELETE(m_pCustomModel);
		}
		if (mgr)
		{
			mgr->OnLoadAyncTaskCompleted(this);
		}
	}

	long long   m_owid;
	int m_nSpecialType;
	std::string m_fileName;
	int			m_realOwnerUin;
	std::string m_modRoot;
	int m_type;
	int m_reType;
	bool m_isLoadingResult;
	CustomModel* m_pCustomModel;
	bool m_isLastTask;
	std::function<void(std::string)> m_fn;
	void* m_pgeom;

	CustomModelMgr* mgr;
	Rainbow::BackJobFence jobFence;
};

struct CustomModelFromHostBGLoadTask
{
	CustomModelFromHostBGLoadTask(const PB_CustomModelHC& hc)
		:m_customModelHC(hc)
	{
	}

	~CustomModelFromHostBGLoadTask()
	{

	}

	static void staticFuncForJobThread(CustomModelFromHostBGLoadTask* pTask)
	{
		pTask->DoInJobThread();
		GetMainThreadJob().ScheduleMainThreadJob(CustomModelFromHostBGLoadTask::staticFuncForMainThread, pTask);
	}

	static void staticFuncForMainThread(CustomModelFromHostBGLoadTask* pTask)
	{
		pTask->DoInMainThread();
		ENG_DELETE_LABEL(pTask, kMemGame);
	}

	void DoInJobThread()
	{
		if (CustomModelMgr::GetInstancePtr())
		{
			CustomModelMgr::GetInstancePtr()->bgLoadCustomModelFromHostInJob((void*)this);
		}
	}
	void DoInMainThread()
	{
		if (CustomModelMgr::GetInstancePtr())
		{
			CustomModelMgr::GetInstancePtr()->bgLoadCustomModelFromHostInMain((void*)this);
		}
	}
	PB_CustomModelHC m_customModelHC;
	std::string filename;
	int type = BLOCK_MODEL;
	std::vector<BLOCK_DATA_TYPE> bolckDatas;
	std::vector<WCoord> relativePos;
	bool isDownload = false;
	int itemId = 0;
	std::string modelName;
	std::string modelDesc;
	int authUin = 0;
	int itemid = 0;
	bool bJobThreadRet = false;
};


static void LoadCustomModelAyncComplete(CustomModelBGLoadTask* task)
{
	task->DoInMainThread();
	ENG_DELETE_LABEL(task, kMemGame);
}

static void LoadCustomModelAsync(CustomModelBGLoadTask* task)
{
	OPTICK_EVENT();
	task->DoInJobThread();
	GetMainThreadJob().ScheduleMainThreadJob(LoadCustomModelAyncComplete, task);
}


CustomModelMgr::CustomModelMgr() : m_CurOWID(-1), m_LoadedRes(false), m_pCurEditActorBody(NULL),
								 m_bCustomItemDirty(false), m_nSpecialType(NORMAL_WORLD)
{
	m_IsInRelease = false;

#ifndef IWORLD_SERVER_BUILD
	
	m_TextureGen = ENG_NEW_LABEL(TextureRenderGen,kMemGame)();
	
	m_IconDescs.clear();
	m_MapIconDescs.clear();
#else
    m_TextureGen = NULL;	
#endif

	m_PreviewCustomModelsCache.clear();
	m_PreviewCustomModels.clear();
	m_PreviewResCustomActorModels.clear();
}

CustomModelMgr::~CustomModelMgr()
{
	m_IsInRelease = true;
	for (auto iter = m_LoadAsyncTasks.begin(); iter != m_LoadAsyncTasks.end(); ++iter)
	{
		CustomModelBGLoadTask* task = *iter;
		task->mgr = nullptr;
	}
	m_LoadAsyncTasks.clear();   //CustomModelBGLoadTask 在主线程job执行后会自己释放对象实例

	ClearMapCustomModels();
	ClearResCustomModels();
	ClearEquipResCustomModels();
	ClearPreviewResCustomModels();

	auto iter = m_IconDescs.begin();
	for (; iter != m_IconDescs.end(); iter++)
	{
		ENG_DELETE(iter->second);
	}
	m_IconDescs.clear();

	auto mapIter = m_MapIconDescs.begin();
	for (; mapIter != m_MapIconDescs.end(); mapIter++)
	{
		ENG_DELETE(mapIter->second);
	}
	m_MapIconDescs.clear();

	if (m_pCurEditActorBody)
		ENG_DELETE(m_pCurEditActorBody);

	ENG_DELETE_LABEL(m_TextureGen, kMemGame);

	m_PreviewResCustomActorModels.clear();
}

void CustomModelMgr::OnLoadAyncTaskCompleted(CustomModelBGLoadTask* task)
{
	if (m_IsInRelease) return;
	m_LoadAsyncTasks.remove(task);

	if (task->m_pgeom)
	{
		g_BlockMtlMgr.SetBlockGeomTemplate(task->m_fileName.c_str(), (BlockGeomTemplate*)task->m_pgeom);
	}

	onLoadCustomModel(task->m_owid, task->m_realOwnerUin, task->m_modRoot,
		task->m_type, task->m_reType, task->m_fileName, task->m_pCustomModel, task->m_isLastTask, task->m_fn);
}


void CustomModelMgr::ClearPreviewResCustomModels()
{
	for (size_t i = 0; i < m_PreviewCustomModels.size(); i++)
	{
		ENG_DELETE(m_PreviewCustomModels[i]);
	}
	m_PreviewCustomModels.clear();
	m_PreviewCustomModelsCache.clear();
}

void CustomModelMgr::update(float dtime)
{
	if (m_pCurEditActorBody)
		m_pCurEditActorBody->update(dtime);
}

void CustomModelMgr::tick()
{
	if (m_bCustomItemDirty)
	{
		saveCustomModelItem();
		m_bCustomItemDirty = false;
	}
		
}

void CustomModelMgr::addCustomModel(World *pworld, int type, WCoord start, WCoord dim, IClientPlayer *player, std::string modelname, std::string modeldesc, WorldContainer*container, int dirtype/* =DIR_NEG_Z */)
{
	if (!pworld)
		return;

	if (start.x > start.x + dim.x)
		start.x = start.x + dim.x;

	if (start.y > start.y + dim.y)
		start.y = start.y + dim.y;

	if (start.z > start.z + dim.z)
		start.z = start.z + dim.z;

	dim.x = Rainbow::Abs(dim.x);
	dim.y = Rainbow::Abs(dim.y);
	dim.z = Rainbow::Abs(dim.z);

	std::vector<Block> blocks;
	std::vector<WCoord> relatives;
	blocks.clear();
	relatives.clear();

	for (int y = 0; y <= Rainbow::Abs(dim.y); y++)
	{
		for (int z = 0; z <= Rainbow::Abs(dim.z); z++)
		{
			for (int x = 0; x <= Rainbow::Abs(dim.x); x++)
			{
				WCoord pos(start.x + x, start.y + y, start.z + z);
				if (pworld->getChunk(pos) == NULL)
					pworld->syncLoadChunk(pos, 1);

				Block srcBlock = pworld->getBlock(pos);

				int blockid = srcBlock.getResID();
				if (blockid > 0 && blockid != ITEM_BLOCKMODELCRAFT && blockid != ITEM_ITEMMODELCRAFT)
				{
					relatives.push_back(WCoord(x, y, z));
					blocks.push_back(srcBlock);
				}
			}
		}
	}

	if (blocks.size() <= 0)
		return;

	unsigned long blockCrc = MINIW::Crc32Calc(&blocks[0], sizeof(Block)* blocks.size());
	unsigned long relativePosCrc = MINIW::Crc32Calc(&relatives[0], sizeof(WCoord)* relatives.size());

	auto *custommodel = hasCustomModel(blockCrc, relativePosCrc, modelname, modeldesc, type);
	if (!custommodel)
	{
		int id = getFreeId(type);
		if (id <= 0)
		{
			//没有可分配的id了
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
			return;
		}

		if (!GetWorldManagerPtr())
			return;

		char filename[256];
#if defined(_WIN32)
		sprintf(filename, "%d%I64d", player->getUin(), time(NULL));
#else
		sprintf(filename, "%d%ld", player->getUin(), time(NULL));
#endif

		CustomModel* custommodel = ENG_NEW(CustomModel)(id, filename);
		custommodel->setData(blocks, relatives, blockCrc, relativePosCrc, modelname, modeldesc, dim, type, dirtype);

		GetWorldManagerPtr()->syncAllPlayersCustomModel();
		m_MapCustomModels.push_back(custommodel);

		//checkFile(GetWorldManagerPtr()->getWorldId());

		custommodel->save(GetWorldManagerPtr()->getWorldId(), filename, GetClientInfoProxy()->getNickName(), GetClientInfoProxy()->getUin(), GetWorldManagerPtr()->getSpecialType());
		custommodel->m_ProductContainerCoord = container->m_BlockPos;
		addCustomItemData(id, filename, "default", type);
		if (ResourceCenter::GetInstancePtr())
			ResourceCenter::GetInstancePtr()->addOneResToClass(filename, true);

		//GetGameEventQue().postAddCustomModel(id);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("id", id);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_ADD_CUSTOMMODEL", sandboxContext);
		}

		GetDefManagerProxy()->addDefByCustomModel(id, type, filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());

		std::string content = modelname + ";" + modeldesc;
		GetWorldStringManagerProxy()->insert(filename, content, SAVEFILETYPE::CUSTOM_MODEL);
		//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
		//	.SetData_Number("type", 4)
		//	.SetData_String("key", content)
		//	.SetData_String("content", filename));

		custommodel->syncCustomModelData(player, "default");
	}
	else
	{
		int id = getCustomItemID(custommodel->getFileName());
		if (id > 0 && container)
		{
			bool result = container->Event2().Emit<int>("ContainerModelCraft_ProductResult", id);
			Assert(result);
		}
	}
}

CustomModel *CustomModelMgr::hasCustomModel(unsigned long blockcrc, unsigned long relativePoscrc, std::string modelname, std::string modeldesc, int modeltype)
{
	for (size_t i = 0; i < m_MapCustomModels.size(); i++)
	{
		if (m_MapCustomModels[i]->getBlocksCrc() == blockcrc && m_MapCustomModels[i]->getRelativePosCrc() == relativePoscrc
			&& m_MapCustomModels[i]->getModelName() == modelname && m_MapCustomModels[i]->getModelDesc() == modeldesc
			&& m_MapCustomModels[i]->getModelType() == modeltype)
		{
			return m_MapCustomModels[i];
		}
	}

	return NULL;
}

int CustomModelMgr::getFreeId(int type)
{
	Rainbow::Mutex::AutoLock locker(m_Mutex);
	if (type == BLOCK_MODEL)
	{
		for (int i = 0; i < CustomBlockModelMinId-1; i++)
		{
			auto *def = GetDefManagerProxy()->getBlockDef(CustomBlockModelMaxId - i, false);
			if (!def && !GetDefManagerProxy()->getItemDef(CustomBlockModelMaxId - i))
				return CustomBlockModelMaxId - i;
		}
	}
	else if (type > BLOCK_MODEL && type <= VEHICLE_MODEL)
	{
		for (int i = 0; i < 5850; i++)
		{
			auto *def = GetDefManagerProxy()->getItemDef(9950 - i, false);
			if (!def)
				return 9950 - i;
		}
	}
	else if (type == ACTOR_MODEL)
	{
		for (int i = 0; i < 3000; i++)
		{
			auto *def = GetDefManagerProxy()->getMonsterDef(3000 - i, false);
			if (!def)
				return 3000 - i;
		}
	}

	return -1;
}

void CustomModelMgr::loadMapCustomModelData(long long owid, int realowneruin, std::string modroot/* ="" */,ResourceDefineType reType/*= CUSTOM_MODEL_TYPE*/, int specialType/* = NORMAL_WORLD*/, bool ignorecheck/* = false*/)
{
	m_CurOWID = owid;
	m_nSpecialType = specialType;
	m_DirRoot = modroot;
	loadCustomModelItem();
	if (!m_LoadedRes)
	{
		loadResCustomModel(true, reType, realowneruin, 0);
	}

#ifdef IWORLD_SERVER_BUILD
	std::string cmurl, mapmd5;
	long long mapID;
	if (GetIClientGameManagerInterface()->getCMURL(cmurl, mapmd5, mapID))
	{
		loadMapCustomModelDataImplCloudServer(owid, realowneruin, modroot, (ResourceDefineType)reType, specialType);
	}
	else
	{
		loadMapCustomModelDataImpl(owid, realowneruin, modroot, (ResourceDefineType)reType, specialType);
	}
#else
	loadMapCustomModelDataImpl(owid, realowneruin, modroot, (ResourceDefineType)reType, specialType, ignorecheck);
#endif
}

void CustomModelMgr::preLoadMapCustomModelData(long long owid, int realowneruin, std::string modroot/* ="" */,ResourceDefineType reType/*= CUSTOM_MODEL_TYPE*/, int specialType/* = NORMAL_WORLD*/)
{
	m_CurOWID = owid;
	m_nSpecialType = specialType;
	m_DirRoot = modroot;
	loadCustomModelItem();
	if (!m_LoadedRes)
	{
		// 这里是load总库？
		loadResCustomModel(true, reType, realowneruin, 0);
	}

	//扫描所有custommodel;
	ClearMapCustomModels();
}

void CustomModelMgr::afterLoadMapCustomModelData(long long owid, int realowneruin)
{
	loadCustomActor(MAP_MODEL_CLASS, realowneruin);
}

void CustomModelMgr::loadOneMapCustomModelDataImpl(long long owid, int realowneruin, std::string& modroot, std::string& filename, int specialType/* = NORMAL_WORLD*/)
{
	CustomModel* custommodel = ENG_NEW(CustomModel)();
	if (custommodel && custommodel->load(owid, filename, realowneruin, modroot, false, specialType))
	{
		m_MapCustomModels.push_back(custommodel);
		int id = getCustomItemID(filename);
		if (id > 0)
		{
			custommodel->setItemID(id);

			int modelType = custommodel->getModelType();
			GetDefManagerProxy()->addDefByCustomModel(id, modelType, filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());
		}
	}
	else
		ENG_DELETE(custommodel);
}

void CustomModelMgr::loadMapCustomModelDataImpl(long long owid, int realowneruin, std::string modroot, ResourceDefineType reType, int specialType, bool ignorecheck/* = false*/)
{
	std::string pathname = ResourceCenter::GetInstancePtr()->getFolderNameByResType(reType);
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/", rootpath.c_str(), owid);
	std::string realpath(dir);
	realpath.append(pathname).append("/");


	if (!GetFileManager().IsFileExistWritePath(realpath.c_str()))
	{
		GetFileManager().CreateWritePathDir(realpath.c_str());
	}
	

	if (reType == CUSTOM_MODEL_TYPE)
	{
		//扫描所有custommodel;
		ClearMapCustomModels();

		char path[256];
		sprintf(path, "%s/w%lld/custommodel", rootpath.c_str(), owid);

		OneLevelScaner scaner;
		scaner.setRoot(GetFileManager().GetWritePathRoot());
		scaner.scanTree(path, 1);
		int counter = 0;
		for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++, counter++)
		{
			std::string filename = it->c_str();
			CustomModel* custommodel = ENG_NEW(CustomModel)();

			if (custommodel && custommodel->load(owid, filename, realowneruin, modroot, ignorecheck, specialType))
			{
				m_MapCustomModels.push_back(custommodel);
				int id = getCustomItemID(filename);
				if (id > 0)
				{
					custommodel->setItemID(id);

					int modelType = custommodel->getModelType();
					GetDefManagerProxy()->addDefByCustomModel(id, modelType, filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());
				}
			}
			else
				ENG_DELETE(custommodel);
		}

	}

	if (reType == CUSTOM_MODEL_TYPE)
	{
		loadCustomActor(MAP_MODEL_CLASS, realowneruin);
	}
}

//云服情况下加载微缩
void CustomModelMgr::loadMapCustomModelDataImplCloudServer(long long owid, int realowneruin, std::string modroot, ResourceDefineType reType, int specialType)
{
	std::string pathname = ResourceCenter::GetInstancePtr()->getFolderNameByResType(reType);
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/", rootpath.c_str(), owid);
	std::string realpath(dir);
	realpath.append(pathname).append("/");

	if (!GetFileManager().IsFileExistWritePath(realpath.c_str()))
	{
		GetFileManager().CreateWritePathDir(realpath.c_str());
	}

	if (reType == CUSTOM_MODEL_TYPE)
	{
		ClearMapCustomModels();

		char path[256];
		sprintf(path, "%s/w%lld/custommodel", rootpath.c_str(), owid);

		OneLevelScaner scaner;
		scaner.setRoot(GetFileManager().GetWritePathRoot());
		scaner.scanTree(path, 1);
		int counter = 0;
		for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++, counter++)
		{
			std::string filename = it->c_str();
			int id = getCustomItemID(filename);
			if (id <= 0)
			{
				continue;
			}

			CustomModel* custommodel = ENG_NEW(CustomModel)();

			if (custommodel && custommodel->loadCloudServer(owid, filename, realowneruin, modroot, false, specialType))
			{
				GetDefManagerProxy()->addDefByCustomModel(id, custommodel->getModelType(), filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());
			}

			ENG_DELETE(custommodel);
		}
	}

	if (reType == CUSTOM_MODEL_TYPE)
	{
		loadCustomActor(MAP_MODEL_CLASS, realowneruin);
	}
}

void CustomModelMgr::ClearMapCustomModels()
{
	for (size_t i = 0; i < m_MapCustomModels.size(); i++)
	{
		ENG_DELETE(m_MapCustomModels[i]);
	}
	m_MapCustomModels.clear();
	m_MapCustomModelsCache.clear();
}

void CustomModelMgr::loadOneModCustomRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod, bool ignorecheck)
{
	if (CustomModelMgr::getCustomModel(MAP_MODEL_CLASS, modelkey))
		return;

	CustomModel* custommodel = ENG_NEW(CustomModel)();

	if (custommodel && custommodel->load(m_CurOWID, modelkey, realowneruin, modroot, ignorecheck, m_nSpecialType))
	{
		if (islibmod)
			m_ResCustomModels.push_back(custommodel); 
		else
			m_MapCustomModels.push_back(custommodel);
	}
	else
		ENG_DELETE(custommodel)
}

void CustomModelMgr::checkMapCustomItemByClassInfo()
{
	if (!ResourceCenter::GetInstancePtr())
		return;

	auto *pResFolderSetInfosVec = ResourceCenter::GetInstancePtr()->getResFolderSetInfos(MAP_LIB, CUSTOM_MODEL_TYPE);
	if (!pResFolderSetInfosVec)
		return;

	auto infoVec = (*pResFolderSetInfosVec);
	for (size_t i = 0; i < infoVec.size(); i++)
	{
		for (size_t j = 0; j < infoVec[i].m_Models.size(); j++)
		{
			auto skey = infoVec[i].m_Models[j]->itemName;
			CustomItemData *pCustomItem = getCustomItem(skey);
			if (!pCustomItem)  //如果没有分配道具id,重新分配一下
			{
				int type = -1;
				std::string modelName = "";
				std::string modelDesc = "";
				Rainbow::Vector3f box(0, 0, 0);
				auto* pCm = getCustomModel(MAP_MODEL_CLASS, skey);
				if (pCm)
				{
					type = pCm->getModelType();
					modelName = pCm->getModelName();
					modelDesc = pCm->getModelDesc();
					box = pCm->getBox();
				}
				else
				{
					auto* pActorCm = findCustomActorModelData(MAP_MODEL_CLASS, skey);
					if (pActorCm)
					{
						type = ACTOR_MODEL;
						modelName = pActorCm->modelname;
					}
					else
					{
						auto* pFcm = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(MAP_MODEL_CLASS, skey, true);
						if (pFcm)
						{
							type = pFcm->getModelType();
							modelName = pFcm->getModelName();
							modelDesc = pFcm->getModelDesc();
						}

						auto* pIcm = ImportCustomModelMgr::GetInstancePtr()->findImportCustomModel(MAP_MODEL_CLASS, skey);
						if (pIcm)
						{
							type = pIcm->getModelType();
							modelName = pIcm->getName();
							modelDesc = pIcm->getDesc();
						}
					}
				}
				if(type < 0)
					continue;

				int id = getFreeId(type);
				if (type >= FULLY_BLOCK_MODEL && type <= FULLY_PACKING_CUSTOM_MODEL)
					id = FullyCustomModelMgr::GetInstancePtr()->getFreeId(type);
				else if (type >= IMPORT_BLOCK_MODEL && type <= IMPORT_MODEL_MAX)
					id = ImportCustomModelMgr::GetInstancePtr()->getFreeId(type);

				int involvedId = 0;
				if (type == FULLY_ACTOR_MODEL)
					involvedId = FullyCustomModelMgr::GetInstancePtr()->getFreeId(type);
				else if (type == IMPORT_ACTOR_MODEL)
					involvedId = ImportCustomModelMgr::GetInstancePtr()->getFreeId(type);
				else if (type == ACTOR_MODEL)
					involvedId = getFreeId(type);


				if (id <= 0)
					continue;
				if ((type == FULLY_ACTOR_MODEL || type == IMPORT_ACTOR_MODEL || type == ACTOR_MODEL) && involvedId <= 0)
					continue;

				if (pCm)
				{
					pCm->setItemID(id);
					if (pCm->getModelType() == BLOCK_MODEL)
						pCm->genGeomTemplate();
				}
					

				addCustomItemData(id, skey, "default", type, involvedId);
				GetDefManagerProxy()->addDefByCustomModel(id, type, skey, modelName, modelDesc, box, involvedId);
			}
			else
			{
				int type = pCustomItem->type;
				int id = pCustomItem->itemid;
				int involvedId = pCustomItem->involvedid;

				if ((type == FULLY_ACTOR_MODEL || type == IMPORT_ACTOR_MODEL || type == ACTOR_MODEL) && !GetDefManagerProxy()->getItemDef(involvedId,false)) {
					if (type == FULLY_ACTOR_MODEL)
						involvedId = FullyCustomModelMgr::GetInstancePtr()->getFreeId(type);
					else if (type == IMPORT_ACTOR_MODEL)
						involvedId = ImportCustomModelMgr::GetInstancePtr()->getFreeId(type);
					else if (type == ACTOR_MODEL)
						involvedId = getFreeId(type);
					if (involvedId <= 0)
						continue;
					std::string modelName = "";
					auto* pActorCm = findCustomActorModelData(MAP_MODEL_CLASS, skey);
					if (pActorCm)
					{
						modelName = pActorCm->modelname;
					}
					else
					{
						auto* pFcm = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(MAP_MODEL_CLASS, skey, true);
						if (pFcm)
						{
							modelName = pFcm->getModelName();
						}

						auto* pIcm = ImportCustomModelMgr::GetInstancePtr()->findImportCustomModel(MAP_MODEL_CLASS, skey);
						if (pIcm)
						{
							modelName = pIcm->getName();
						}
					}
					addCustomItemData(id, skey, "default", type, involvedId);
					GetDefManagerProxy()->addItemDef(involvedId, type, skey, modelName, "", id);
				}
			}
		}
	}
}
bool CustomModelMgr::loadOneMapCustomModel(int type, std::string filename, std::string classname,int folderindex,int id, bool addcustomitem/* =true */, bool ignorecheck/* =false */)
{
	CustomModel *custommodel = ENG_NEW(CustomModel)();
	if (custommodel->load(m_CurOWID,filename,0,"",ignorecheck, m_nSpecialType))
	{
		if (GetWorldManagerPtr())
			GetWorldManagerPtr()->syncAllPlayersCustomModel();
		if(id > 0)
			custommodel->setItemID(id);
		m_MapCustomModels.push_back(custommodel);
		if (id > 0 && addcustomitem)
		{
			addCustomItemData(id, filename, classname,type, 0, folderindex);
			custommodel->genGeomTemplate();
		}

		GetDefManagerProxy()->addDefByCustomModel(id, type, filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());
		return true;
	}
	else
	{
		ENG_DELETE(custommodel);
		return false;
	}
}

bool CustomModelMgr::saveMapManifest(ResourceDefineType reType /*= CUSTOM_MODEL_TYPE*/)
{
	if (m_CurOWID < 0)
		return false;
	if (!ResourceCenter::GetInstancePtr())
		return false;

	return ResourceCenter::GetInstancePtr()->saveMapClassManifest(reType);
}

void CustomModelMgr::onLoadCustomModel(long long owid, int realOwnerUin, std::string& modRoot, int type, int resType, std::string& filename, CustomModel* pCostomModel, bool isLastTask, std::function<void(std::string)>& callback_func)
{
	if (pCostomModel)
	{
		if (type == RES_MODEL_CLASS)
		{
			m_ResCustomModels.push_back(pCostomModel);
		}
		else if (type == MAP_MODEL_CLASS)
		{
			m_MapCustomModels.push_back(pCostomModel);
			int id = getCustomItemID(filename);
			if (id > 0)
			{
				pCostomModel->setItemID(id);
				int modelType = pCostomModel->getModelType();
				GetDefManagerProxy()->addDefByCustomModel(id, modelType, filename, pCostomModel->getModelName(), pCostomModel->getModelDesc(), pCostomModel->getBox());
			}
		}
	}
	if (isLastTask)
	{
		callback_func(modRoot);
	}
}

void CustomModelMgr::loadResCustomModel(bool loadmanifest/* =true */,ResourceDefineType reType/* = CUSTOM_MODEL_TYPE*/, int realOwnerUin/* = 0*/, int from/* = 0*/)
{
	if (m_LoadedRes) //没load总库
		return;
	m_LoadedRes = true;
	std::string folderName = ResourceCenter::GetInstancePtr()->getFolderNameByResType(reType);
	char dir[32];
	sprintf(dir, "data/");
	std::string realdir(dir);
	realdir.append(folderName).append("/");
	if (!GetFileManager().IsFileExistWritePath(realdir.c_str()))
	{
		GetFileManager().CreateWritePathDir(realdir.c_str());
	}

	ClearResCustomModels();

	char path[32];
	sprintf(path, "data/");
	std::string realpath(path);
	realpath.append(folderName);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(realpath.c_str(), 1);

	int counter = 0;
	auto callback_fun = [=](std::string modRoot) {
		loadCustomActor(RES_MODEL_CLASS);
		if (from == 1)
		{
			loadMapCustomModelDataImpl(m_CurOWID, realOwnerUin, m_DirRoot, (ResourceDefineType)reType, m_nSpecialType);
		}
	};

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++, counter++)
	{
		std::string filename = it->c_str();
		bool isLastTask = false;
		if (counter == scaner.m_FileNamesVec.size() - 1)
		{
			isLastTask = true;
		}
		CustomModel* custommodel = ENG_NEW(CustomModel)();
		if(custommodel)
		{
			//MINIW::BGThreadTask::addTask(ENG_NEW(CustomModelBGLoadTask)(m_CurOWID,filename,realOwnerUin,m_DirRoot,RES_MODEL_CLASS,reType,custommodel, isLastTask,callback_fun, m_nSpecialType));
			
			CustomModelBGLoadTask* task = ENG_NEW_LABEL(CustomModelBGLoadTask, kMemGame)(m_CurOWID, filename, realOwnerUin, m_DirRoot, RES_MODEL_CLASS, reType, custommodel, isLastTask, callback_fun, m_nSpecialType);
			task->mgr = this;
			task->jobFence = GetBackStageJob().ScheduleJob(LoadCustomModelAsync, task);
			m_LoadAsyncTasks.push_back(task);
		}
	}
	if (scaner.m_FileNamesVec.empty())
	{
		loadCustomActor(RES_MODEL_CLASS);
	}
}

void CustomModelMgr::ClearResCustomModels()
{
	for (size_t i = 0; i < m_ResCustomModels.size(); i++)
	{
		ENG_DELETE(m_ResCustomModels[i]);
	}
	m_ResCustomModels.clear();
	m_ResCustomModelsCache.clear();
}

void CustomModelMgr::loadEquipCustomModel(bool isReload/* = false*/)
{
	if (!isReload)
	{
		static int isLoaded = false;
		if (isLoaded)
			return;
		isLoaded = true;
	}

	std::string realDir = "data/custommodel_equip/";
	std::string realpath = "data/custommodel_equip";
	
	if (GetFileManager().IsFileExistWritePath(realDir.c_str()))
	{
		//清空
		ClearEquipResCustomModels();

		int counter = 0;
		OneLevelScaner scaner;
		scaner.setRoot(GetFileManager().GetWritePathRoot());
		scaner.scanTree(realpath.c_str(), 1);

		for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++, counter++)
		{
			//同步
			std::string filename = it->c_str();
			bool isLastTask = false;
			CustomModel* custommodel = ENG_NEW(CustomModel)();

			if (custommodel->load(-2, filename))
			{
				m_EquipResCustomModels.push_back(custommodel);
			}
			else
			{
				ENG_DELETE(custommodel);
			}
		}
	}
}

bool CustomModelMgr::copyEquipCustomModel2StudioPath()
{
	static bool isReload = false;
	if (isReload)
		return true;
	isReload = true;

	//  res/custommodel_equip.zip --> data/custommodel_equip.zip --> data/custommodel_equip/
	
	std::string srcZip  = "custommodel_equip.zip";
	std::string dstZip  = "data/custommodel_equip.zip";
	std::string tempZip = "data/custommodel_equip_temp.zip";
	std::string dstDir  = "data/custommodel_equip/";
	std::string modUncompressDir = "data/";

	//-->temp
	if (!GetFileManager().CopyPKgFileToWritePath(srcZip.c_str(), tempZip.c_str()))
	{
		LOG_INFO("copy zip failed");
		return false;
	}

	int dstSize = 0;
	int tempSize = 0;

	if (GetFileManager().IsFileExistWritePath(dstZip.c_str()))
	{
		dstSize = GetFileManager().GetWritePathFileSize(dstZip.c_str());
		tempSize = GetFileManager().GetWritePathFileSize(tempZip.c_str());

		if (GetFileManager().IsFileExistWritePath(dstDir.c_str()))
		{
			if (dstSize == tempSize)
			{
				LOG_INFO("no change!");
				return true;
			}
		}
	}

	if (!GetFileManager().CopyPKgFileToWritePath(srcZip.c_str(), dstZip.c_str()))
	{
		LOG_INFO("copy zip failed");
		return false;
	}

	if (Rainbow::CompressTool::uncompressZipInPkg(dstZip.c_str(), modUncompressDir.c_str()) != 0)
	{
		LOG_INFO("uncompress zip failed");
		return false;
	}

	return true;
}

void CustomModelMgr::ClearEquipResCustomModels()
{
	for (size_t i = 0; i < m_EquipResCustomModels.size(); i++)
	{
		ENG_DELETE(m_EquipResCustomModels[i]);
	}
	m_EquipResCustomModels.clear();
	m_EquipResCustomModelsCache.clear();
}

void CustomModelMgr::onSwitchAccountSucceed(int uin)
{
	m_LoadedRes = false;
	ClearResCustomModels();
	for (size_t i = CUSTOM_MODEL_TYPE; i < MAX_TYPE; i++)
	{
		loadResCustomModel(true,static_cast<ResourceDefineType>(i));
	}

	loadEquipCustomModel(true);
}

void CustomModelMgr::loadOneResCustomModel(int type, std::string filename, bool ignorecheck /* = false */)
{
	auto cmodel = getCustomModel(type, filename);
	if (cmodel)
	{
		return;
	}

	CustomModel *custommodel = ENG_NEW(CustomModel)();
	if (custommodel && custommodel->load(type == PREVIEW_MODEL_CLASS ? -3 : -1, filename,0,"",ignorecheck))
	{
		if(type == PREVIEW_MODEL_CLASS)
			m_PreviewCustomModels.push_back(custommodel);
		else
			m_ResCustomModels.push_back(custommodel);
	}
	else
	{
		ENG_DELETE(custommodel);
	}
}


void CustomModelMgr::clearDownLoadResArchive()
{
	std::set<ItemInfo*>::iterator iter = m_DownloadResFileNameSet.begin();
	for (; iter != m_DownloadResFileNameSet.end(); ++iter)
	{
		ItemInfo* p = *iter;
		ENG_DELETE(p);
	}
	m_DownloadResFileNameSet.clear();
}
void CustomModelMgr::leaveWorld()
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	auto iter = m_MapCustomActorModels.begin();
	for (iter; iter != m_MapCustomActorModels.end(); iter++)
	{
		char path[256];
		if (iter->leaveworlddel)
		{
			sprintf(path, "%s/w%lld/custommodel/actormodelmf/%s.mf", rootpath.c_str(), m_CurOWID, iter->modelmark.c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}
	}

	clearCustomItemByLeaveWorld();

	for (size_t i = 0; i < m_MapCustomModels.size(); i++)
	{
		if (m_MapCustomModels[i]->isLeaveWorldDel())
		{
			char path[256];
			sprintf(path, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), m_CurOWID, m_MapCustomModels[i]->getFileName().c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}
		g_BlockMtlMgr.removeGenGeomTemplate(m_MapCustomModels[i]->getFileName().c_str());
		ENG_DELETE(m_MapCustomModels[i]);
	}

	ClearMapCustomModels();
	m_ResCustomModelsCache.clear();
	m_EquipResCustomModelsCache.clear();
	m_PreviewCustomModelsCache.clear();
	m_MapCustomActorModels.clear();
	m_CurOWID = -1;
	m_nSpecialType = NORMAL_WORLD;

	auto mapIter = m_MapIconDescs.begin();
	for (; mapIter != m_MapIconDescs.end(); mapIter++)
	{
		ENG_DELETE(mapIter->second);
	}
	m_MapIconDescs.clear();
	//clearDownLoadResArchive();

}

bool CustomModelMgr::syncCustomModelDataPre(IClientPlayer *player, int index,ResourceDefineType resType /*= CUSTOM_MODEL_TYPE*/)
{
	if (!ResourceCenter::GetInstancePtr())
		return false;

	if (index < 0 || index >= (int)m_MapCustomModels.size() || !m_MapCustomModels[index])
		return false;

	std::string className = ResourceCenter::GetInstancePtr()->getClassName(MAP_LIB, m_MapCustomModels[index]->getFileName(), resType);
	if (className.empty())
		className = "default";

	if (resType == CUSTOM_MODEL_TYPE)
	{
		m_MapCustomModels[index]->syncCustomModelDataPre(player, className, index);
	}
	return true;
}

bool CustomModelMgr::syncCustomModelData(IClientPlayer *player, int index,ResourceDefineType resType /*= CUSTOM_MODEL_TYPE*/, bool isSendData)
{
	if (!ResourceCenter::GetInstancePtr())
		return false;

	if (index < 0 || index >= (int)m_MapCustomModels.size() || !m_MapCustomModels[index])
		return false;

	std::string className = ResourceCenter::GetInstancePtr()->getClassName(MAP_LIB, m_MapCustomModels[index]->getFileName(), resType);
	if (className.empty())
		className = "default";
	if (resType == CUSTOM_MODEL_TYPE)
	{
		m_MapCustomModels[index]->syncCustomModelData(player, className, isSendData);
	}
	return true;
}

int CustomModelMgr::getCustomModelNum(int type)
{
	if(type == MAP_MODEL_CLASS)
		return (int)m_MapCustomModels.size();
	else if(type == RES_MODEL_CLASS)
		return (int)m_ResCustomModels.size();
	else if (type == EQUIP_MODEL_CLASS)
		return (int)m_EquipResCustomModels.size();
	else if (type == PREVIEW_MODEL_CLASS)
		return (int)m_PreviewCustomModels.size();

	return 0;
}

CustomModel *CustomModelMgr::getCustomModel(int cmModelClass, int index)
{
	if (index < 0)
		return NULL;

	if (cmModelClass == MAP_MODEL_CLASS)
	{
		if (index >= (int)m_MapCustomModels.size())
			return NULL;

		return m_MapCustomModels[index];
	}
	else if (cmModelClass == RES_MODEL_CLASS)
	{
		if (index >= (int)m_ResCustomModels.size())
			return NULL;

		return m_ResCustomModels[index];
	}
	else if (cmModelClass == EQUIP_MODEL_CLASS)
	{
		if (index >= (int)m_EquipResCustomModels.size())
			return NULL;

		return m_EquipResCustomModels[index];
	}
	else if (cmModelClass == PREVIEW_MODEL_CLASS)
	{
		if (index >= (int)m_PreviewCustomModels.size())
			return NULL;

		return m_PreviewCustomModels[index];
	}
	
	return NULL;
}

CustomModel* CustomModelMgr::getCustomModel(int eCmModelClass, const std::string& strKey)
{
	std::vector<CustomModel*>& vCm = getCmArray(eCmModelClass);
	std::unordered_map<std::string, int>& mCmCache = getCmCacheMap(eCmModelClass);

	if (mCmCache.find(strKey) != mCmCache.end())
	{
		unsigned i = mCmCache[strKey];
		if (i < vCm.size())
		{
			return vCm[i];
		}
	}
	for (size_t i = 0; i < vCm.size(); i++)
	{
		if (vCm[i]->getFileName() == strKey)
		{
			mCmCache[strKey] = i;
			return vCm[i];
		}
	}
	return nullptr;
}

CustomModel* CustomModelMgr::getCustomModel(const std::string& filename)
{
	if (filename.empty())
	{
		return nullptr;
	}

	CustomModel* custom = getCustomModel(MAP_MODEL_CLASS, filename);
	if (!custom)
		custom = getCustomModel(RES_MODEL_CLASS, filename);

	if (!custom)
		custom = getCustomModel(EQUIP_MODEL_CLASS, filename);

	if (!custom)
		custom = getCustomModel(PREVIEW_MODEL_CLASS, filename);

	return custom;
}

CustomModel* CustomModelMgr::getCustomModelByIndex(int type, int index)
{
	return getCustomModel(type, index);
} 

const int ITEM_UVEDGE = 1;
Rainbow::SharePtr<Rainbow::Texture2D>   CustomModelMgr::getModelIcon(std::string geomname, int modeltype, int &u, int &v, int &width, int &height, int &r, int &g, int &b)
{
	if (geomname.empty())
		return nullptr;

	auto iter = m_IconDescs.find(geomname);
	if (iter != m_IconDescs.end())
	{
		u = iter->second->u;
		v = iter->second->v;
		width = iter->second->width;
		height = iter->second->height;

		r = iter->second->color.r;
		g = iter->second->color.g;
		b = iter->second->color.b;

		return iter->second->tex;
	}
	else
	{
		auto mapIter = m_MapIconDescs.find(geomname);
		if (mapIter != m_MapIconDescs.end())
		{
			u = mapIter->second->u;
			v = mapIter->second->v;
			width = mapIter->second->width;
			height = mapIter->second->height;

			r = mapIter->second->color.r;
			g = mapIter->second->color.g;
			b = mapIter->second->color.b;

			return mapIter->second->tex;
		}
	}
		

	Rainbow::SharePtr<Rainbow::Texture2D>  ptex = nullptr;
	int modelLibType = RES_MODEL_CLASS;

	if (modeltype == -1) //不限类型
	{
		ptex = GetBlockMaterialMgr().genOneItemIconTex(geomname,true);
		
		if (!ptex)
			ptex = genActorTexture(geomname);
			
	}
	else
	{
		if (modeltype == BLOCK_MODEL)
		{
			ptex = GetBlockMaterialMgr().genOneItemIconTex(geomname,true);
		}
		else if (modeltype > BLOCK_MODEL&& modeltype <= BOW_MODEL)
		{
			ptex = GetBlockMaterialMgr().genOneItemIconTex(geomname,true);
		}
		else if (modeltype == ACTOR_MODEL)
		{
			ptex = genActorTexture(geomname);
		}
	}

	if (modeltype == ACTOR_MODEL && findCustomActorModelData(MAP_MODEL_CLASS, geomname))
	{
		modeltype = MAP_MODEL_CLASS;
	}
	else if (clientHasAvatarModel(geomname) || getCustomModel(MAP_MODEL_CLASS, geomname))
	{
		modeltype = MAP_MODEL_CLASS;
	}

	//TODO:这一块不知道翻译的对不对
	// if (!ptex)
	// {
	// 	char path[256];
	// 	sprintf(path, "$model:%s", geomname.c_str());
	// 	ptex = UILib::UIRenderer::GetInstance().CreateTexture(path);
	// }

	// if (!ptex)
	// {
	// 	ptex = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(GetWhiteTexture());
	// }

	if (ptex)
	{
		IconDesc *desc = ENG_NEW(IconDesc)();

		desc->tex = ptex;
		desc->color = ColorRGBA32::white;
		desc->u = desc->v = ITEM_UVEDGE;
		desc->width = ptex->GetOrginWidth() - 2 * ITEM_UVEDGE;
		desc->height = ptex->GetOrginHeight() - 2 * ITEM_UVEDGE;
		if (desc->width <= 0)
			desc->width = 1;
		if (desc->height <= 0)
			desc->height = 1;

		if (modeltype == BLOCK_MODEL) {
			desc->isblock = true;
		}else{
			desc->isblock = false;
		}

		u = desc->u;
		v = desc->v;
		width = desc->width;
		height = desc->height;
		r = desc->color.r;
		g = desc->color.g;
		b = desc->color.b;

		if (modelLibType == RES_MODEL_CLASS)
		{
			m_IconDescs[geomname] = desc;
			return m_IconDescs[geomname]->tex;
		}
		else
		{
			m_MapIconDescs[geomname] = desc;
			return m_MapIconDescs[geomname]->tex;
		}

	}

	return nullptr;
}

void CustomModelMgr::addCustomModelByHostSync(std::string filename, std::vector<BLOCK_DATA_TYPE> &blockDatas, std::vector<WCoord> &relativePos, int itype, int itemId, std::string modelName, std::string modelDesc, int authUin, bool needgengeom /*= false*/)
{
	if (getCustomModel(MAP_MODEL_CLASS, filename)) return;
	CustomModel* pModel = ENG_NEW(CustomModel)();
	CustomModelData data;
	data.blocks.clear();
	for (size_t i=0;i<blockDatas.size();i++)
	{
		data.blocks.push_back(Block(blockDatas[i]));
	}
	data.relativepos.clear();
	for (size_t i=0; i<relativePos.size(); i++)
	{
		data.relativepos.push_back(relativePos[i]);
	}
	pModel->setFileName(filename);
	pModel->setData(data.blocks, data.relativepos, needgengeom);
	pModel->setAuthUin(authUin);
	pModel->setModelType(itype);
	pModel->setItemID(itemId);
	pModel->setModelName(modelName);
	pModel->setModelDesc(modelDesc);
	m_MapCustomModels.push_back(pModel);
}

Rainbow::Model *CustomModelMgr::getItemModel(std::string filename, ITEM_MESH_TYPE meshtype)
{
	char str_meshtype[32];
	sprintf(str_meshtype, "_%d", meshtype);
	std::string filename_ = filename;
	filename_ += str_meshtype;

	CustomModel *custom = getCustomModel(MAP_MODEL_CLASS, filename);
	if (!custom)
		custom = getCustomModel(RES_MODEL_CLASS, filename);

	if (!custom)
		custom = getCustomModel(EQUIP_MODEL_CLASS, filename);

	if (custom)
	{
		return custom->getItemModel(meshtype);
	}
	
	return NULL;
}

Rainbow::Model *CustomModelMgr::getAvatarModel(std::string filename, ITEM_MESH_TYPE eMeshType)
{
	if (filename.empty())
	{
		return nullptr;
	}
	char str_meshtype[32];
	sprintf(str_meshtype, "_%d", eMeshType);
	std::string filename_ = filename;
	filename_ += str_meshtype;

	CustomModel *custom = getCustomModel(MAP_MODEL_CLASS, filename);
	if (!custom)
		custom = getCustomModel(RES_MODEL_CLASS, filename);

	if (!custom)
		custom = getCustomModel(EQUIP_MODEL_CLASS, filename);

	if (!custom)
		custom = getCustomModel(PREVIEW_MODEL_CLASS, filename);

	#if BUILD_MINI_EDITOR_APP
	{
		if (!custom)
			custom = loadEditorCm(filename);
	}
	#endif


	Rainbow::Model* model = nullptr;
	if (custom)
	{
		model = custom->getItemModel(eMeshType);
	}

	//if (!model)
	//	model = createCmModel(filename, eMeshType);

	return model;
}

bool CustomModelMgr::isAvatarCMItemModelInCache(std::string filename, ITEM_MESH_TYPE meshtype)
{
	CustomModel* custom = getCustomModel(filename);
	if (custom)
	{
		return custom->isItemModelInCache(meshtype);
	}
	return false;
}

void CustomModelMgr::loadAvatarCMItemModelToCache(std::string filename, ITEM_MESH_TYPE meshtype)
{
	CustomModel* custom = getCustomModel(filename);
	if (custom)
	{
		custom->genItemModelAsync(meshtype);
	}
}

bool CustomModelMgr::hasAvatarModel(std::string filename)
{
	if (clientHasAvatarModel(filename))
		return true;

	if (getCustomModel(MAP_MODEL_CLASS, filename))
		return true;

	if (getCustomModel(RES_MODEL_CLASS, filename))
		return true;

	return false;
}

bool CustomModelMgr::clientHasAvatarModel(std::string filename)
{
	/*
	auto iter = m_ItemModelDataByHostSync.find(filename);
	if (iter != m_ItemModelDataByHostSync.end())
	{
		return true;
	}
	
	auto blockIter = m_BlockModelDataByHostSync.find(filename);
	if (blockIter != m_BlockModelDataByHostSync.end())
	{
		return true;
	}*/

	return false;
}

bool CustomModelMgr::isBlockCM(std::string filename)
{
	/*
	auto iter = m_BlockModelDataByHostSync.find(filename);
	if (iter != m_BlockModelDataByHostSync.end())
	{
		return true;
	}*/

	for (size_t i = 0; i < m_MapCustomModels.size(); i++)
	{
		if (m_MapCustomModels[i]->getFileName() == filename)
		{
			return m_MapCustomModels[i]->getModelType() == BLOCK_MODEL;
		}	
	}

	return false;
}

std::string CustomModelMgr::createAndSaveActor(IClientPlayer *player, WCoord &containerpos, CustomActorModelData &modeldata, std::string modelname /* = "" */)
{
	int id = getFreeId(ACTOR_MODEL);
	if (id <= 0)
	{
		//没有可分配的id了
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
		return "";
	}

	int involvedId = getFreeId(WEAPON_MODEL);  //获取一个可用的道具id
	if (involvedId <= 0)
	{
		//没有可分配的id了
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
		return "";
	}

	if (!GetWorldManagerPtr())
		return "";

	char filename[256];
#if defined(_WIN32)
		sprintf(filename, "%d%I64d", player->getUin(), time(NULL));
#else
		sprintf(filename, "%d%ld", player->getUin(), time(NULL));
#endif
	CustomActorModelData data;
	data = modeldata;
	data.modelmark = filename;
	data.modelname = modelname;
	data.authuin = GetClientInfoProxy()->getUin();
	m_MapCustomActorModels.push_back(data);

	saveCustomActor(MAP_MODEL_CLASS, data);

	addCustomItemData(id, filename, "default",ACTOR_MODEL, involvedId, 0);
	GetDefManagerProxy()->addDefByCustomModel(id, ACTOR_MODEL, filename, modelname, "", Rainbow::Vector3f(0, 0, 0), involvedId);

	World* world = player->GetPlayerWorld();
	if (world && !world->isRemoteMode())
	{
		PB_CustomItemIDsHC customItemIDsHC;
		for (size_t i = 0; i < 1; i++)
		{
			customItemIDsHC.add_customitemids(id);
			customItemIDsHC.add_custommodelfilenames(filename);
			customItemIDsHC.add_custommodelclassnames("default");
			customItemIDsHC.add_custommodelfolderindexs(0);
			customItemIDsHC.add_customtypes(ACTOR_MODEL);
			customItemIDsHC.add_involvedids(involvedId);
		}
		GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_ITEMIDS_HC, customItemIDsHC, 0);
	}

	if (world)
	{
		ActorManagerInterface* actormgr = world->getActorMgr();
		if (actormgr)
		{
			if (containerpos.y > 0)
			{
				actormgr->iSpawnMob(containerpos*BLOCK_SIZE, id, false, false);
				//player->getWorld()->setBlockAll(containerpos, 0, 0);
			}
		}
	}

	syncCustomActorModelData(0, m_MapCustomActorModels.size() - 1);
	if (player->getIBackPack())
	{
		GridCopyData data;
		data.resid = involvedId;
		data.num = 1;
		player->getIBackPack()->tryAddItem_byGridCopyData(data);
		//player->getBackPack()->tryAddItem(involvedId, 1, -1, 0, NULL);
	}

	if (ResourceCenter::GetInstancePtr())
		ResourceCenter::GetInstancePtr()->addOneResToClass(filename, true);

	GetWorldStringManagerProxy()->insert(filename, modelname, SAVEFILETYPE::ACTOR_CUSTOM_MODEL);

	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
	//	.SetData_Number("type", 5)
	//	.SetData_String("content", modelname)
	//	.SetData_String("key", filename));

	if (GetIPlayerControl())
	{
		char sHasBind[10];
		const int hasBind = data.models.size() > 0 ? 1 : 0;
		sprintf(sHasBind, "%d", hasBind);

		char sModelType[10]; 
		sprintf(sModelType, "%d", data.type);

		//GetIPlayerControl()->statisticToWorld(player->getUin(), 61023, "", GetIPlayerControl()->getCurWorldType(), "param_to_str", sHasBind, sModelType);
	}

	return filename;
}

void CustomModelMgr::checkSyncCustomModelData(int itemindex, int actormodelindex, bool addtoclass/* =true */, int uin/* =0 */)
{
	syncCustomItemIDs(itemindex, addtoclass, uin);
	syncCustomActorModelData(uin, actormodelindex);
}

void CustomModelMgr::syncCustomItemIDs(int itemindex, bool addtoclass, int uin)
{
	size_t i = itemindex;
	PB_CustomItemIDsHC customItemIDsHC;
	for (; i < m_CustomItems.size(); i++)
	{
		if(i == itemindex+500)  //一次同步500个，避免同步的信息量太大，同步不过去
			break;

		customItemIDsHC.add_customitemids(m_CustomItems[i].itemid);
		customItemIDsHC.add_custommodelfilenames(m_CustomItems[i].modelfilename.c_str());
		customItemIDsHC.add_custommodelclassnames(m_CustomItems[i].classname.c_str());
		if (addtoclass)
			customItemIDsHC.add_custommodelfolderindexs(m_CustomItems[i].folderindex);
		else
			customItemIDsHC.add_custommodelfolderindexs(-1);

		customItemIDsHC.add_customtypes(m_CustomItems[i].type);
		customItemIDsHC.add_involvedids(m_CustomItems[i].involvedid);
	}
	if (uin == 0)
		GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_ITEMIDS_HC, customItemIDsHC, 0);
	else
		GetGameNetManagerPtr()->sendToClient(uin, PB_CUSTOM_ITEMIDS_HC, customItemIDsHC);

	if (i < m_CustomItems.size())
		syncCustomItemIDs(i, addtoclass, uin);
}

void CustomModelMgr::reEncryptCustomModelData(long long owid, int olduin, int newuin, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/", rootpath.c_str(), owid);
	if (!gFunc_isStdioDirExist(dir)) { return; }

	char path[256];
	sprintf(path, "%s/w%lld/custommodel", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();

		CustomModel *custommodel = ENG_NEW(CustomModel)();
		custommodel->reEncrypt(owid, filename, olduin, newuin, specialType);
		ENG_DELETE(custommodel);
	}
}

void CustomModelMgr::updateSyncModelData(Rainbow::Entity *entity, std::map<std::string, std::vector<CustomAvatarModelData>> &waitSyncModelDataList, bool ispacking/* =false */)
{
	if (waitSyncModelDataList.size() <= 0)
		return;

	if (!entity)return;

	bool packmodel = false;
	auto iter = waitSyncModelDataList.begin();
	for (; iter != waitSyncModelDataList.end();)
	{
		std::string boneName = iter->first;

		auto it = iter->second.begin();
		for (; it != iter->second.end(); )
		{
			ITEM_MESH_TYPE meshType = PROJECTILE_ACTOR_MESH;
			if (ispacking)
				meshType = NORMAL_MESH;

			if (it->show)  //按照原逻辑，调整条件判断次序
			{
				Rainbow::Model* pSubModel = nullptr;
				if (CustomModelMgr::GetInstancePtr()->isAvatarCMItemModelInCache(it->modelfilename, meshType))
				{
					pSubModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(it->modelfilename, meshType);
				}
				else
				{
					// 异步加载
					CustomModelMgr::GetInstancePtr()->loadAvatarCMItemModelToCache(it->modelfilename, meshType);
				}

				if (pSubModel)
				{
					//把之前绑定的默认模型解绑并释放!
					entity->UnbindCustomObject(boneName.c_str(), 1);

					pSubModel->SetPosition(Rainbow::Vector3f(it->offset_x, it->offset_y, it->offset_z));
					//TODO_wangshuai:it->newrotatemode这个参数没用到,判断角度还是弧度？
					pSubModel->SetRotation(it->yaw, it->pitch, it->roll);
					pSubModel->SetScale(it->scale);
					entity->BindCunstomObject(boneName.c_str(), pSubModel);

					packmodel = true;
					it = iter->second.erase(it);
				}
				else
					++it;
			}
			else
				++it;
		}

		if (iter->second.size() <= 0)
		{
			iter = waitSyncModelDataList.erase(iter);
		}
		else
			++iter;
	}
	if (packmodel)
	{
		if (waitSyncModelDataList.size() == 0)
		{
			GenCustomModelManager::GetInstance().bindCusntomObjs(entity);
		}
		entity->UpdateBindFather();
		entity->UpdateTick(0);
	}	
}

void CustomModelMgr::exportSTL(std::string filename, std::string exportdir)
{

	BlockGeomTemplate *geom = g_BlockMtlMgr.getGeomTemplate(filename.c_str());
	if (geom)
	{
		GeomRawVertex *verts;
		size_t nvert;
		size_t nindexs;
		unsigned short *pindices;
		if (geom->getMeshVerts(verts, pindices, nvert, nindexs, 0))
		{
			char path[256];
			sprintf(path, "%s/%s.stl", exportdir.c_str(), filename.c_str());

			FileAutoClose fp(path, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
			if (fp.isNull()) return;

			char fileHead[80];
			memset(fileHead, 0, 80);
			sprintf(fileHead, "%s.stl", filename.c_str());

			char desc[2];
			memset(desc, 0, 2);

			//写入文件头
			if (!fp.write(fileHead, sizeof(char) * 80))
			{
				fp.sync();
				fp.close();
				return;
			}

			//写入三角面片数量
			int triangleNum = nindexs / 3;
			if (!fp.write(&triangleNum, 4))
			{
				fp.sync();
				fp.close();
				return;
			}

			//写入三角面片数据
			float* dat = new float[12];
			for (size_t i = 0; i < nindexs;)
			{
				if (i + 2 >= nindexs)
					break;

				GeomRawVertex &src1 = verts[pindices[i]];
				GeomRawVertex &src2 = verts[pindices[i + 1]];
				GeomRawVertex &src3 = verts[pindices[i + 2]];


				dat[0] = src1.normal.x;
				dat[1] = src1.normal.y;
				dat[2] = src1.normal.z;

				dat[3] = src1.pos.x * 20;
				dat[4] = src1.pos.z * 20;
				dat[5] = src1.pos.y * 20;

				dat[6] = src2.pos.x * 20;
				dat[7] = src2.pos.z * 20;
				dat[8] = src2.pos.y * 20;

				dat[9] = src3.pos.x * 20;
				dat[10] = src3.pos.z * 20;
				dat[11] = src3.pos.y * 20;


				int nbytes = sizeof(float) * 12;
				if (!fp.write(dat, nbytes))
				{
					fp.sync();
					fp.close();
					OGRE_DELETE_ARRAY(dat);
					return;
				}
				if (!fp.write(desc, 2))
				{
					fp.sync();
					fp.close();
					OGRE_DELETE_ARRAY(dat);
					return;
				}

				i = i + 3;
			}

			fp.sync();
			fp.close();

			OGRE_DELETE_ARRAY(dat);
		}
	}
}

bool CustomModelMgr::saveOneCMByPacking(CustomModel *cm, std::string skey)
{
	if (!cm)
		return false;

	if (m_CurOWID <= 0 || !GetWorldManagerPtr())
		return false;

	cm->save(GetWorldManagerPtr()->getWorldId(), skey, GetClientInfoProxy()->getNickName(), GetClientInfoProxy()->getUin(), GetWorldManagerPtr()->getSpecialType());

	m_MapCustomModels.push_back(cm);

	return true;
}

bool CustomModelMgr::isDownloadCM(std::string filename)
{
	if (filename.empty())
		return false;

	CustomModel* cm = getCustomModel(MAP_MODEL_CLASS, filename);
	if (!cm)
		cm = getCustomModel(RES_MODEL_CLASS, filename);

	if (cm)
	{
		return cm->isDownload();
	}

	return false;
}

std::string CustomModelMgr::createResourceImageByFileName(std::string fileName,std::string imageName)
{
	if (!fileName.empty())
	{
		auto iter = m_IconDescs.find(fileName);
		if (iter != m_IconDescs.end())
		{
			size_t pos = imageName.find_last_of("/");
			std::string destPath = imageName.substr(0, pos + 1);
			if (!GetFileManager().IsFileExistWritePath(destPath.c_str()))
			{
				GetFileManager().CreateWritePathDir(destPath.c_str());
			}

			core::string savePath = GetFileManager().ToWriteableFullPath(imageName.c_str());
			Rainbow::Tex2DSaveToImage(iter->second->tex, savePath.c_str());
			return imageName;
		}

		auto createFullyCustomIconPng = [&]()->std::string
		{
			size_t pos = imageName.find_last_of("/");
			std::string destPath = imageName.substr(0, pos + 1);
			if (!GetFileManager().IsFileExistWritePath(destPath.c_str()))
			{
				GetFileManager().CreateWritePathDir(destPath.c_str());
			}
	
			core::string savePath = GetFileManager().ToWriteableFullPath(imageName.c_str());
			Rainbow::Tex2DSaveToImage(iter->second->tex, savePath.c_str());
			return imageName;
		};
		auto fullyIcon = FullyCustomModelMgr::GetInstancePtr()->getIconDescs();
		if (!fullyIcon.empty())
		{
			iter = fullyIcon.find(fileName);
			if (iter != fullyIcon.end())
			{
				return createFullyCustomIconPng();
			}
		}
		auto createImportCMIconPng = [&]()->std::string
		{
			size_t pos = imageName.find_last_of("/");
			std::string destPath = imageName.substr(0, pos + 1);
			if (!GetFileManager().IsFileExistWritePath(destPath.c_str()))
			{
				GetFileManager().CreateWritePathDir(destPath.c_str());
			}

			core::string savePath = GetFileManager().ToWriteableFullPath(imageName.c_str());
			Rainbow::Tex2DSaveToImage(iter->second->tex, savePath.c_str());
			return imageName;
		};

		if (!ImportCustomModelMgr::GetInstancePtr())
			return "";

		auto importCMIcon = ImportCustomModelMgr::GetInstancePtr()->getIconDescs();
		if (!importCMIcon.empty())
		{
			iter = importCMIcon.find(fileName);
			if (iter != importCMIcon.end())
			{
				return createImportCMIconPng();
			}
		}
		auto importMapIcons = FullyCustomModelMgr::GetInstancePtr()->getIconDescs();
		if (!importMapIcons.empty())
		{
			iter = importMapIcons.find(fileName);
			if (iter != importMapIcons.end())
			{
				return createImportCMIconPng();
			}
		}
	}
	return "";
}

std::string CustomModelMgr::createResourceImage(int type, std::string resId, std::string imageName, ResourceDefineType reType)
{
	if (!ResourceCenter::GetInstancePtr())
		return "";

	std::string fileName = ResourceCenter::GetInstancePtr()->getResNameByResId(type, resId);
	return createResourceImageByFileName(fileName, imageName);
}

int	convertLittleEndian4byte(int in)
{
	return ((in & 0xff000000) >> 24) | ((in & 0x00ff0000) >> 8) | ((in & 0x0000ff00) << 8) | ((in & 0x000000ff) << 24);
}

int CustomModelMgr::convertCMBySmallRoutine(std::string filename, std::string path)
{

	int buflen = 0;
	void *buf = ReadWholeFile(path.c_str(), buflen);
	if (buf == NULL)
		return -1;

	int posLen = 0;
	memcpy(&posLen, (const char *)buf, 4);
	posLen = convertLittleEndian4byte(posLen);

	short jsonLen = 0;
	memcpy(&jsonLen, (const char *)buf + 4, 2);
	jsonLen = ((jsonLen & 0xff00) >> 8) | ((jsonLen & 0x00ff) << 8);

	char *jsonStr = new char[jsonLen+1];
	memcpy(jsonStr, (const char *)buf + 6, jsonLen+1);
	jsonStr[jsonLen] = 0;

	int offset = 0;
	std::vector<unsigned int> colors;
	jsonxx::Object txtObj;
	if (txtObj.parse(jsonStr))
	{
		if (txtObj.has<jsonxx::Array>("colorArry"))
		{
			jsonxx::Array colorArray = txtObj.get<jsonxx::Array>("colorArry");
			for (size_t i = 0; i < colorArray.size(); i++)
			{
				unsigned int col = (unsigned int)colorArray.get<jsonxx::Number>(i);
				colors.push_back(col);
			}
		}

		if (txtObj.has<jsonxx::Number>("area"))
		{
			offset = (int)txtObj.get<jsonxx::Number>("area")/2;
		}
	}
	OGRE_DELETE_ARRAY(jsonStr);

	std::vector<WCoord> pos_vec;
	std::vector<Block> block_vec;
	std::map<int, CustomModelData> customModelDatas;
	customModelDatas.clear();
	int start = (jsonLen + 6);

	for (int i = 0; i < posLen; )
	{
		if (i + start + 3 >= buflen)
			break;

		signed char c_x;
		memcpy(&c_x, (const char *)buf +i+ start, 1);

		signed char c_y;
		memcpy(&c_y, (const char *)buf +i+ start+1, 1);

		signed char c_z;
		memcpy(&c_z, (const char *)buf + i + start+2, 1);

		char c_colorIndex;
		memcpy(&c_colorIndex, (const char *)buf+ i + start + 3, 1);

		char colorStr[12];
		if ((int)colors.size() <= c_colorIndex)
			c_colorIndex = 0;

		sprintf(colorStr, "%ld", colors[c_colorIndex]);

		int blockId, data;
		MINIW::ScriptVM::game()->callFunction("GetBlockInfoFromColor", "s>ii", colorStr, &blockId, &data);
		if(blockId <= 0)
		{
			i += 4;
			continue;
		}


		WCoord pos = WCoord(c_x, c_y, c_z) + WCoord(offset-1, -1, offset-1);
		WCoord packingblockpos = WCoord(pos.x / BLOCK_MODEL_SIZE, pos.y / BLOCK_MODEL_SIZE, pos.z / BLOCK_MODEL_SIZE);
		int mapKey = packingblockpos.x + packingblockpos .y* 100 + packingblockpos.z * 10000;
		auto iter = customModelDatas.find(mapKey);
		if (iter != customModelDatas.end())
		{
			Block block;
			block.setAll(blockId, data);
			iter->second.blocks.push_back(block);

			WCoord relativePos = pos - packingblockpos*BLOCK_MODEL_SIZE;
			iter->second.relativepos.push_back(relativePos);
		}
		else
		{
			CustomModelData modelData;

			Block block;
			block.setAll(blockId, data);
			modelData.blocks.push_back(block);
			WCoord relativePos = pos - packingblockpos*BLOCK_MODEL_SIZE;
			modelData.relativepos.push_back(relativePos);

			modelData.packingblockpos = packingblockpos;

			customModelDatas[mapKey] = modelData;
		}
		i += 4;
	}

	free(buf);

	if (offset == 30 || offset == 50) //微缩组合
	{
		if (!CustomModelPacking::GetInstancePtr())
			return -4;

		char skey[64] = { 0 };
		int idx = 1;
		std::vector<PackingCustomItemData> newCustomItems;
		FullyCustomModel *pFcm = ENG_NEW(FullyCustomModel)();
		auto iter = customModelDatas.begin();
		for (; iter != customModelDatas.end(); iter++)
		{
			//int id = CustomModelMgr::GetInstancePtr()->getFreeId(BLOCK_MODEL);
			//if (id <= 0)
			//{
			//	//没有可分配的id了
			//	GetIPlayerControl()->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
			//	break;
			//}

			memset(skey, 0, sizeof(skey) / sizeof(char));
			sprintf(skey, "%s%d", filename.c_str(), idx);
			CustomModel *pCm = ENG_NEW(CustomModel)(); //ENG_NEW(CustomModel)(id, skey);
			pCm->setData(iter->second.blocks, iter->second.relativepos, true);
			pCm->save(-1, skey, GetClientInfoProxy()->getNickName(), GetClientInfoProxy()->getUin());
			//ENG_DELETE(pCm);
			//CustomModelMgr::GetInstancePtr()->addCustomItemData(id, skey, "default", BLOCK_MODEL);
			//GetDefManagerProxy()->addDefByCustomModel(id, BLOCK_MODEL, skey, "", "", pCm->getBox());
			m_ResCustomModels.push_back(pCm);

			char subName[32];
			sprintf(subName, "%s%d", GetDefManagerProxy()->getStringDef(16014), idx);
			
			Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, 0, 0);
			pFcm->setPackingFCMData(iter->second.packingblockpos, subName, skey, quat);
			idx++;
		}


		char fcmPath[256] = { 0 };

		sprintf(fcmPath, "data/custommodel/fully/packing/%s.pfcm", filename.c_str());

		if (!pFcm->save(fcmPath, filename, GetIPlayerControl()->GetIUin(), GetIPlayerControl()->GetPlayerControlNickname(), "", "", FULLY_PACKING_CUSTOM_MODEL))
		{
			ENG_DELETE(pFcm);
			return -3;
		}
		else
		{
			if (FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
				FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->addResPackingFcm(pFcm);

			if (ResourceCenter::GetInstancePtr())
				ResourceCenter::GetInstancePtr()->addOneResToClass(filename, true, 0, PUBLIC_LIB);
		}
	}
	else
	{
		auto iter = customModelDatas.begin();
		if (iter == customModelDatas.end())
			return -2;

		CustomModel *pCm = ENG_NEW(CustomModel)();
		pCm->setData(iter->second.blocks, iter->second.relativepos, true);
		pCm->setCustFlag(true);
		pCm->save(-1, filename, GetClientInfoProxy()->getNickName(), GetClientInfoProxy()->getUin());
		m_ResCustomModels.push_back(pCm);
		if (ResourceCenter::GetInstancePtr())
			ResourceCenter::GetInstancePtr()->addOneResToClass(filename, true, 0, PUBLIC_LIB);

	}

	return 0;
}

bool CustomModelMgr::reEncryptDownloadModelRes(long long owid, int olduin, int newuin)
{
	if (olduin == newuin) { return true; }

	char path[256];
	sprintf(path, "data/w%lld/custommodel/manifest.mf", owid);
	if (!gFunc_isStdioFileExist(path)) { return true; }

	int buflen = 0;
	void* buf = ReadWholeFile(path, buflen);
	if (!buf) { return true; }

	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	if (!FBSave::VerifyCustomModelMgrDataBuffer(verifier))
	{
		free(buf);
		return false;
	}

	const FBSave::CustomModelMgrData *data = FBSave::GetCustomModelMgrData(buf);
	if (data == NULL)
	{
		free(buf);
		return false;
	}

	flatbuffers::FlatBufferBuilder builder;
	std::vector<flatbuffers::Offset<FBSave::CustomModelClass>> datas;
	datas.clear();

	auto modelclass = data->modelclass();
	if (modelclass)
	{
		std::vector<flatbuffers::Offset<flatbuffers::String>> modelnames;
		std::vector<flatbuffers::Offset<flatbuffers::String>> modelids;
		std::vector<int> modelstatus;
		std::vector<int> downloadUins;
		std::vector<flatbuffers::Offset<flatbuffers::String>> modelMd5s;
		int download_uin = 0;
		for (size_t i = 0; i < modelclass->size(); i++)
		{
			auto models = modelclass->Get(i);
			if (!models) { continue; }

			auto modelname_ptr = models->modelname();
			if (!modelname_ptr) { continue; }

			auto modelids_ptr = models->modelids();
			auto modelstatus_ptr = models->modelstatus();
			auto downloadUins_ptr = models->unlockeruin();
			auto modelMd5s_ptr = models->modelmd5();

			modelnames.clear();
			modelids.clear();
			modelstatus.clear();
			downloadUins.clear();
			modelMd5s.clear();

			for (size_t j = 0; j < modelname_ptr->size(); j++)
			{
				modelnames.push_back(builder.CreateString(modelname_ptr->Get(j)->c_str()));

				if(modelids_ptr && modelids_ptr->size() > j)
					modelids.push_back(builder.CreateString(modelids_ptr->Get(j)->c_str()));

				if (modelstatus_ptr && modelstatus_ptr->size() > j)
					modelstatus.push_back(modelstatus_ptr->Get(j));

				if (downloadUins_ptr && downloadUins_ptr->size() > j)
				{
					download_uin = downloadUins_ptr->Get(j);
					if (download_uin == olduin)
						downloadUins.push_back(newuin);
					else
						downloadUins.push_back(download_uin);
				}

				if (modelMd5s_ptr && modelMd5s_ptr->size() > j)
					modelMd5s.push_back(builder.CreateString(modelMd5s_ptr->Get(j)->c_str()));
			}

			datas.push_back(FBSave::CreateCustomModelClass(builder, builder.CreateString(models->classname()),
				builder.CreateVector(modelnames),
				modelids_ptr ? builder.CreateVector(modelids) : NULL,
				modelstatus_ptr ? builder.CreateVector(modelstatus) : NULL,
				downloadUins_ptr ? builder.CreateVector(downloadUins) : NULL,
				modelMd5s_ptr ? builder.CreateVector(modelMd5s) : NULL));
		}
	}

	auto cm = FBSave::CreateCustomModelMgrData(builder, builder.CreateVector(datas));
	builder.Finish(cm);
	free(buf);

	if (!GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize())) { return false; }

	return true;
}

bool CustomModelMgr::reEncryptDownloadModLibRes(long long owid, int olduin, int newuin)
{
	if (olduin == newuin) { return true; }

	char path[256];
	sprintf(path, "data/w%lld/achive", owid);
	if (!gFunc_isStdioFileExist(path)) { return true; }

	int len = 0;
	void* buf = ReadWholeFile(path, len);
	if (!buf) { return true; }

	std::string readBuf = b64_decode_my((char*)buf, len);
	jsonxx::Array downloadResFileNames;
	if (downloadResFileNames.parse(readBuf))
	{
		int download_uin = 0;
		for (size_t i = 0; i < downloadResFileNames.size(); i++)
		{
			if (downloadResFileNames.has<jsonxx::Object>(i))
			{
				jsonxx::Object& resObj = downloadResFileNames.get<jsonxx::Object>(i);
				const auto& objMap = resObj.kv_map();
				auto iter = objMap.begin();
				for (; iter != objMap.end(); ++iter)
				{
					download_uin = (int)iter->second->get<jsonxx::Number>();
					if (download_uin == olduin)
					{
						resObj.get<jsonxx::Number>(iter->first) = newuin;
					}
				}
			}
		}
	}

	std::string textData = downloadResFileNames.json();
	textData = b64_encode_my((const unsigned char*)textData.c_str(), textData.length());
	GetFileManager().SaveToWritePath(path, textData.c_str(), textData.length());

	free(buf);
	buf = NULL;
	return true;
}

bool CustomModelMgr::moveCustomModelRes(int destlib, std::string filename, ResourceFolderSetInfo *destclass)
{
	if (destlib == MAP_LIB && m_CurOWID <= 0)
		return false;

	int srclib = MAP_LIB;
	if (destlib == MAP_LIB)
		srclib = PUBLIC_LIB;

	auto resCustomModel = getCustomModel(srclib, filename);
	if (!resCustomModel)
		return false;

	auto *existCM = getCustomModel(destlib, filename);
	if (!existCM)
	{
		char srcPath[128] = { 0 };
		char destPath[128] = { 0 };
		if (destlib == MAP_LIB)
		{
			sprintf(srcPath, "data/custommodel/%s.cm", filename.c_str());
			sprintf(destPath, "data/w%lld/custommodel/%s.cm", m_CurOWID, filename.c_str());
		}
		else if (destlib == PUBLIC_LIB)
		{
			sprintf(srcPath, "data/w%lld/custommodel/%s.cm", m_CurOWID, filename.c_str());
			sprintf(destPath, "data/custommodel/%s.cm", filename.c_str());
		}
		
		if (GetFileManager().IsFileExistWritePath(destPath))
			return true;

		if (!GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath))
			return false;
	}

	if (destlib == MAP_LIB)
	{
		if (existCM && existCM->getItemID() > 0)
		{
			//原来地图库里已经有的模型，如果在待删除列表里，从列表里去掉	
			existCM->setLeaveWorldDel(false);
			checkRemoveWaitDelCustomItem(existCM->getItemID());
		}
		else
		{
			//加载到存档内

			int id = allocationDefId(resCustomModel->getModelType());
			if (id <= 0)
			{
				return false;
			}
		
			if (existCM)
			{
				addDefToMap(id, existCM->getModelType(), existCM);
				existCM->setItemID(id);
			}
			else
			{
				auto *newCM = loadOneCustomModelNew(destlib, filename, false);
				if (newCM)
				{
					newCM->setItemID(id);
					addDefToMap(id, newCM->getModelType(), newCM);
				}
				else
					return false;
			}

			//文字安全检测上报
			std::string content = resCustomModel->getModelName() + ";" + resCustomModel->getModelDesc();
			GetWorldStringManagerProxy()->insert(filename, content, SAVEFILETYPE::CUSTOM_MODEL);
			//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
			//	.SetData_Number("type", 4)
			//	.SetData_String("content", content)
			//	.SetData_String("key", filename));
		}
	}
	else if (destlib == PUBLIC_LIB && !existCM)
	{
		loadOneCustomModelNew(destlib, filename, false);
	}
	
	return true;
}

CustomModel *CustomModelMgr::loadOneCustomModelNew(int libtype, std::string filename, bool ignorecheck)
{
	int specialType = m_nSpecialType;
	long long owid = (libtype == PREVIEW_MODEL_CLASS ? -3 : -1);
	if (libtype == MAP_LIB)
	{
		if (m_CurOWID <= 0)
			return nullptr;

		owid = m_CurOWID;
		specialType = 0;
	}

	auto cmodel = getCustomModel(libtype, filename);
	if (cmodel)
	{
		return cmodel;
	}

	CustomModel *custommodel = ENG_NEW(CustomModel)();
	if (custommodel && custommodel->load(owid, filename, 0, "", ignorecheck, specialType))
	{
		//同步给客机
		if (GetWorldManagerPtr() && libtype == MAP_LIB)
			GetWorldManagerPtr()->syncAllPlayersCustomModel();

		if (libtype == PREVIEW_MODEL_CLASS)
			m_PreviewCustomModels.push_back(custommodel);
		else if (libtype == PUBLIC_LIB)
			m_ResCustomModels.push_back(custommodel);
		else if (libtype == MAP_LIB)
			m_MapCustomModels.push_back(custommodel);

		return custommodel;
	}
	else
	{
		ENG_DELETE(custommodel);
		return nullptr;
	}
	return nullptr;
}

bool CustomModelMgr::removeResByResourceCenter(int libtype, std::string filename)
{
	if (libtype == MAP_LIB)
	{
		std::vector<CustomModel*>::iterator iter = m_MapCustomModels.begin();
		for (; iter != m_MapCustomModels.end(); iter++)
		{
			if ((*iter)->getFileName() == filename)
			{
				(*iter)->setLeaveWorldDel(true);
				addWaitDelCustomItem(filename);
				return true;
			}
		}
	}
	else if (libtype == PUBLIC_LIB)
	{
		std::vector<CustomModel*>::iterator iter = m_ResCustomModels.begin();
		for (; iter != m_ResCustomModels.end();)
		{
			auto resFileName = (*iter)->getFileName();
			if (resFileName == filename)
			{
				char path[256] = { 0 };
				snprintf(path, sizeof(path), "data/custommodel/%s.cm", resFileName.c_str());
				if (GetFileManager().IsFileExistWritePath(path))
				{
					GetFileManager().DeleteWritePathFileOrDir(path);
				}

				auto customModel = getCustomModel(MAP_MODEL_CLASS, resFileName);
				if (!customModel && (*iter)->getModelType() == BLOCK_MODEL)
					g_BlockMtlMgr.removeGenGeomTemplate(resFileName.c_str());

				ENG_DELETE(*iter);
				iter = m_ResCustomModels.erase(iter);
				m_ResCustomModelsCache.erase(filename);
				return true;
			}
			else
				++iter;
		}
	}
	

	return false;
}

int CustomModelMgr::allocationDefId(int modeltype)
{
	int id = getFreeId(modeltype);
	if (id <= 0 && GetIPlayerControl()) //没有可分配的id了
	{
		GetIPlayerControl()->iNotifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
	}

	return id;
}

void CustomModelMgr::addDefToMap(int id, int modeltype, CustomModel *cm)
{
	if (!cm)
		return;

	addCustomItemData(id, cm->getFileName());
	GetDefManagerProxy()->addDefByCustomModel(id, modeltype, cm->getFileName(), cm->getModelName(), cm->getModelDesc(), cm->getBox());
}

#ifdef UGC_PROFILE_ENABLED
float CustomModelMgr::getMemStatForUGC()
{
	size_t iSize = 0;
	for (size_t i = 0; i < m_MapCustomModels.size(); i++)
	{
		CustomModel* custommodel = m_MapCustomModels[i];
		if (custommodel)
		{
			iSize += custommodel->getMemStatForUGC();
		}
	}

	for (size_t i = 0; i < m_ResCustomModels.size(); i++)
	{
		CustomModel* custommodel = m_ResCustomModels[i];
		if (custommodel)
		{
			iSize += custommodel->getMemStatForUGC();
		}
	}

	for (size_t i = 0; i < m_EquipResCustomModels.size(); i++)
	{
		CustomModel* custommodel = m_EquipResCustomModels[i];
		if (custommodel)
		{
			iSize += custommodel->getMemStatForUGC();
		}
	}

	for (size_t i = 0; i < m_PreviewCustomModels.size(); i++)
	{
		CustomModel* custommodel = m_PreviewCustomModels[i];
		if (custommodel)
		{
			iSize += custommodel->getMemStatForUGC();
		}
	}
	

	return iSize / 1024.0f / 1024.0f;
}
#endif // UGC_PROFILE_ENABLED

void CustomModelMgr::loadCustomModel(long long owid)
{
		ClearMapCustomModels();

		if (m_CurOWID <= 0) 
		{
			m_CurOWID = owid;
		}

		char path[256];
		sprintf(path, "data/w%lld/custommodel", owid);

		OneLevelScaner scaner;
		scaner.setRoot(GetFileManager().GetWritePathRoot());
		scaner.scanTree(path, 1);
		for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
		{
			std::string filename = it->c_str();
			CustomModel* custommodel = ENG_NEW(CustomModel)();
			if (custommodel && custommodel->load(owid, filename, 0, "", true, NORMAL_WORLD))
			{
				m_MapCustomModels.push_back(custommodel);
			}
			else
			{
				ENG_DELETE(custommodel);
			}
		}
}

//inline void ScheduleJobForEach(JobFence& fence, void concurrentJobFunc(T*, unsigned), T* jobData, int iterationCount, JobPriority priority)

void LoadCMJobFunc(LoadCMJobData* data, unsigned index) {
	size_t endIndex = data->m_BlockRanges[index].startIndex + data->m_BlockRanges[index].rangeSize;
	for (size_t idx = data->m_BlockRanges[index].startIndex; idx < endIndex; ++idx)
	{
		std::string& fileName = data->m_WorkDatas[idx].strCmName;
		CustomModel* custommodel = ENG_NEW(CustomModel)();
		void* pGeom = nullptr;
		if (custommodel && custommodel->loadInJobThread(data->m_CurOWID, fileName, 0, "", true, NORMAL_WORLD, &pGeom))
		{
			data->m_WorkDatas[idx].pRet = custommodel;
			data->m_WorkDatas[idx].pgeom = pGeom;
		}
		else
		{
			ENG_DELETE(custommodel);
		}
	}
}

int CustomModelMgr::loadCustomModelByTick()
{
	/*if (m_loadCMJobData.m_JobCount > 0)
	{
		JobFence fence;
		ScheduleJobForEach(fence, LoadCMJobFunc, &m_loadCMJobData, m_loadCMJobData.m_JobCount, kHighJobPriority);
		SyncFence(fence);

		for (auto ite = m_loadCMJobData.m_WorkDatas.begin(); ite != m_loadCMJobData.m_WorkDatas.end(); ++ite) {
			if (ite->pRet) {
				m_MapCustomModels.push_back(ite->pRet);
			}
			if (ite->pgeom)
			{
				g_BlockMtlMgr.SetBlockGeomTemplate(ite->strCmName.c_str(), (BlockGeomTemplate*)ite->pgeom);
			}
		}
		m_loadCMJobData.clear();
	}

	return 0;*/
	
	if (m_cmName.empty()) {
		return 0;
	}

	//LOG_INFO("load cm tick dtime:%f", dtime);

	auto start = high_resolution_clock::now();
	//int count = 0;
	auto tmp = duration_cast<microseconds>(high_resolution_clock::now() - start);

	std::string filename;
	for (auto it = m_cmName.begin(); it != m_cmName.end();)
	{
		CustomModel* custommodel = ENG_NEW(CustomModel)();
		filename = *it;
		if (custommodel && custommodel->load(m_CurOWID, filename, 0, "", true, NORMAL_WORLD))
		{
			m_MapCustomModels.push_back(custommodel);
		}
		else
		{
			ENG_DELETE(custommodel);
		}

		it = m_cmName.erase(it);
		//count++;
		tmp = duration_cast<microseconds>(high_resolution_clock::now() - start);
		//2022125 每次tick限制使用50000ms codeby:liushuxin
		if (tmp.count() > 50000) {
			break;
		}
	}

	//auto stop = high_resolution_clock::now();
	//auto duration = duration_cast<microseconds>(stop - start);
	//LOG_INFO("load cm tick ms:%lld, count:%d, left:%d", duration.count(), count, m_cmName.size());
	return int(m_cmName.size());
}

CustomModel* CustomModelMgr::loadEditorCm(const std::string& strFilePath)
{
	CustomModel* cm = getCustomModel(EDITOR_MODEL_CLASS, strFilePath);
	if (cm)
	{
		return cm;
	}
	cm = createEditorCustomModel(strFilePath);
	if (!cm->load(strFilePath.c_str()))
	{
		removeCm(cm, EDITOR_MODEL_CLASS);
		return NULL;
	}
	return cm;
}

CustomModel* CustomModelMgr::createEditorCustomModel(const std::string& strFilePath)
{
	CustomModel *cm = ENG_NEW(CustomModel);
	std::vector<CustomModel*>& vCm = getCmArray(EDITOR_MODEL_CLASS);
	vCm.push_back(cm);
	cm->setModelType(BLOCK_MODEL);
	int modelType = cm->getModelType();
	int id = getFreeId(modelType);
	if (id > 0)
	{
		cm->setItemID(id);
		GetDefManagerProxy()->addDefByCustomModel(id, modelType, strFilePath, cm->getModelName(),
			cm->getModelDesc(), cm->getBox());
	}
	return cm;
}

bool CustomModelMgr::removeCm(const std::string& strKey, const int eCmModelClass)
{
	CustomModel* cm = getCustomModel(eCmModelClass, strKey);
	if (!cm)
	{
		return true;
	}
	return removeCm(cm, eCmModelClass);
}

bool CustomModelMgr::removeCm(const CustomModel* cm, const int eCmModelClass)
{
	std::vector<CustomModel*>& vCm = getCmArray(eCmModelClass);
	std::unordered_map<std::string, int>& mCmCache = getCmCacheMap(eCmModelClass);
	std::string key = "";
	auto it = find(vCm.begin(), vCm.end(), cm);
	if (it == vCm.end())
	{
		return true;
	}
	key = (*it)->getFileName();
	ENG_DELETE(*it);
	vCm.erase(it);
	mCmCache.clear();
	return true;
}

bool CustomModelMgr::renameCm(std::string prevPath, std::string newPath, const int eCmModelClass)
{
	std::vector<CustomModel*>& vCm = getCmArray(eCmModelClass);
	auto itFind = std::find_if(vCm.begin(), vCm.end(), [&](CustomModel* pModel)->bool {
		return prevPath == pModel->getFileName();
	});

	if (itFind == vCm.end())
	{
		return false;
	}

	(*itFind)->setFileName(newPath);

	std::unordered_map<std::string, int >& mCmCache = getCmCacheMap(eCmModelClass);
	if (mCmCache.find(prevPath) != mCmCache.end())
	{
		mCmCache.insert(std::make_pair(newPath, mCmCache[prevPath]));
		mCmCache.erase(prevPath);
	}
	return true;
}

CustomModel* CustomModelMgr::copyEditorToMap(const std::string& strFilePath)
{
	CustomModel* cmEditor = getCustomModel(EDITOR_MODEL_CLASS, strFilePath);
	if (!cmEditor)
	{
		return nullptr;
	}
	int i;
	string str = strFilePath;
	i = str.find_last_of('/');
	if (i == string::npos)
	{
		i = str.find_last_of('\\');
	}
	if (i != string::npos)
	{
		str = str.substr(i + 1, str.size() - i);
	}
	i = str.find(".cm");
	if (i != string::npos)
	{
		str = str.substr(0, i);
	}
	string& strBaseName = str;
	CustomModel* cmMap = ENG_NEW(CustomModel);
	cmMap->setData(cmEditor->m_Blocks, cmEditor->m_RelativePos, false);
	bool ok = saveOneCMByPacking(cmMap, strBaseName);
	if (!ok)
	{
		return nullptr;
	}
	int modelType = cmMap->getModelType();
	const int itemId = getFreeId(modelType);
	if (itemId > 0)
	{
		cmMap->setItemID(itemId);
		GetDefManagerProxy()->addDefByCustomModel(itemId, modelType, strBaseName, cmMap->getModelName(),
			cmMap->getModelDesc(), cmMap->getBox());
	}
	addCustomItemData(itemId, strBaseName);
	return cmMap;
}
bool CustomModelMgr::loadVoxelToCustomModel(const char* path, const char* outPath) {
	VoxelModel vox;
	vox.loadVoxelFile(path);

	CustomModel* pModel = ENG_NEW(CustomModel);
	CustomModelData data;
	data.blocks.clear();
	data.relativepos.clear();
	std::vector<BlockCoord>blockDatas = vox.getVoxelDataToBlock();
	for (size_t i = 0; i < blockDatas.size(); i++)
	{
		BlockCoord blockCoord = blockDatas[i];
		const WCoord& w = blockCoord.GetCoord();
		Block block;
		block.setAllData(blockCoord.getAll());
		data.blocks.push_back(block);
		data.relativepos.push_back(w);
	}
	unsigned long blockCrc = MINIW::Crc32Calc(&data.blocks[0], sizeof(Block) * data.blocks.size());
	unsigned long relativePosCrc = MINIW::Crc32Calc(&data.relativepos[0], sizeof(WCoord) * data.relativepos.size());
	CustomModel* custommodel = ENG_NEW(CustomModel);
	WCoord dim = WCoord(20, 20, 20);
	custommodel->setData(data.blocks, data.relativepos, blockCrc, relativePosCrc, outPath, outPath, dim, BLOCK_MODEL, 0);
	custommodel->save(outPath);
	ENG_DELETE(custommodel);
	return true;
}

int CustomModelMgr::loadCustomModelName(long long owid)
{
	LOG_INFO("proc cm, load custom model name");
	ClearMapCustomModels();
	m_cmName.clear();

	if (m_CurOWID <= 0)
	{
		m_CurOWID = owid;
	}

	char path[256];
	sprintf(path, "data/w%lld/custommodel", owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);
	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		m_cmName.insert(filename);
	}

	return int(m_cmName.size());

	//LOG_INFO("proc cm, load custom model name");
	//ClearMapCustomModels();
	//m_loadCMJobData.clear();

	//if (m_CurOWID <= 0)
	//{
	//	m_CurOWID = owid;
	//}
	//m_loadCMJobData.m_CurOWID = m_CurOWID;

	//char path[256];
	//sprintf(path, "data/w%lld/custommodel", owid);

	//OneLevelScaner scaner;
	//scaner.setRoot(GetFileManager().GetWritePathRoot());
	//scaner.scanTree(path, 1);
	//for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	//{
	//	m_loadCMJobData.m_WorkDatas.emplace_back(std::move(*it));
	//}
	//m_loadCMJobData.Setup(m_loadCMJobData.m_WorkDatas.size());
	//return m_loadCMJobData.m_WorkDatas.size();
}

std::vector<CustomModel*>& CustomModelMgr::getCmArray(const int eCmModelClass)
{
#if 0
	std::vector<CustomModel*>* pvCm = nullptr;
	switch (eCmModelClass)
	{
	case MAP_MODEL_CLASS:
		pvCm = &m_MapCustomModels;
		break;
	case RES_MODEL_CLASS:
		pvCm = &m_ResCustomModels;
		break;
	case EQUIP_MODEL_CLASS:
		pvCm = &m_EquipResCustomModels;
		break;
	case PREVIEW_MODEL_CLASS:
		pvCm = &m_PreviewCustomModels;
		break;
	case EDITOR_MODEL_CLASS:
		pvCm = &m_EditorCustomModels;
		break;
	default:
		pvCm = &m_UnknownCustomModels;
		break;
	}
	return *pvCm;
#else
	switch (eCmModelClass)
	{
	case MAP_MODEL_CLASS:
		return m_MapCustomModels;
	case RES_MODEL_CLASS:
		return m_ResCustomModels;
	case EQUIP_MODEL_CLASS:
		return m_EquipResCustomModels;
	case PREVIEW_MODEL_CLASS:
		return m_PreviewCustomModels;
	case EDITOR_MODEL_CLASS:
		return m_EditorCustomModels;
	}

	return m_UnknownCustomModels;
#endif
}

std::unordered_map<std::string, int>& CustomModelMgr::getCmCacheMap(const int eCmModelClass)
{
#if 0
	std::unordered_map<std::string, int>* pmCmCache = nullptr;
	switch (eCmModelClass)
	{
	case MAP_MODEL_CLASS:
		pmCmCache = &m_MapCustomModelsCache;
		break;
	case RES_MODEL_CLASS:
		pmCmCache = &m_ResCustomModelsCache;
		break;
	case EQUIP_MODEL_CLASS:
		pmCmCache = &m_EquipResCustomModelsCache;
		break;
	case PREVIEW_MODEL_CLASS:
		pmCmCache = &m_PreviewCustomModelsCache;
		break;
	case EDITOR_MODEL_CLASS:
		pmCmCache = &m_EditorCustomModelsCache;
		break;
	default:
		pmCmCache = &m_UnknownCustomModelsCache;
		break;
	}
	return *pmCmCache;
#else
	switch (eCmModelClass)
	{
	case MAP_MODEL_CLASS:
		return m_MapCustomModelsCache;
	case RES_MODEL_CLASS:
		return m_ResCustomModelsCache;
	case EQUIP_MODEL_CLASS:
		return m_EquipResCustomModelsCache;
	case PREVIEW_MODEL_CLASS:
		return m_PreviewCustomModelsCache;
	case EDITOR_MODEL_CLASS:
		return m_EditorCustomModelsCache;
	}

	return m_UnknownCustomModelsCache;
#endif
}

// 在子线程中加载从服务器传来的cm
void CustomModelMgr::bgLoadCustomModelFromHost(const PB_CustomModelHC& hc)
{
	CustomModelFromHostBGLoadTask* task = ENG_NEW_LABEL(CustomModelFromHostBGLoadTask, kMemGame)(hc);
	GetBackStageJob().ScheduleJob(CustomModelFromHostBGLoadTask::staticFuncForJobThread, task);
}

void CustomModelMgr::bgLoadCustomModelFromHostInJob(void* pTask)
{
	CustomModelFromHostBGLoadTask* pTheTask = (CustomModelFromHostBGLoadTask*)pTask;
	PB_CustomModelHC& customModelHC = pTheTask->m_customModelHC;

	unsigned char blockIdVersion = 0;
	pTheTask->filename = customModelHC.filename().c_str();
	size_t destlen = customModelHC.unziplen();
	std::vector<SyncCustomModelData> datas;
	std::string cache_name = "data/http/modelcache/";
	cache_name += pTheTask->filename;
	if (destlen != 0)
	{
		blockIdVersion = customModelHC.blockidversion();
		int compresstype = destlen >> 28;
		destlen &= (1 << 28) - 1;

		Rainbow::CompressTool ctool(compresstype);

		int size = destlen / sizeof(SyncCustomModelData);
		if (size <= 0)
			return;
		datas.resize(size);
		if (!ctool.decompress(&datas[0], destlen, customModelHC.blobdetail().c_str(), customModelHC.bloblen()))
		{
			//assert(0);
			LOG_WARNING("uncompress custom model blob failed");
			return;
		}
		//写模型缓存
		//GetFileManager().CreateWritePathDir("data/http/modelcache/");
		int exsize = 0;
		char* cache_data = new char[sizeof(unsigned int) + customModelHC.bloblen() + 3 * sizeof(char)];
		*(unsigned int*)cache_data = customModelHC.unziplen();
		if (0 != blockIdVersion)
		{
			exsize = 3 * sizeof(char);
			char version[3];
			version[0] = 'e';
			version[1] = 'x';
			version[2] = blockIdVersion;
			memcpy(cache_data + sizeof(unsigned int), version, exsize);
		}
		memcpy(cache_data + sizeof(unsigned int) + exsize, customModelHC.blobdetail().c_str(), customModelHC.bloblen());
		GetFileManager().SaveToWritePath(cache_name.c_str(), cache_data, sizeof(unsigned int) + customModelHC.bloblen() + exsize);
		delete[] cache_data;
	}
	else
	{
		int buflen = 0;
		void* buf = ReadWholeFile(cache_name.c_str(), buflen);
		if (buf == NULL)
		{
			return;
		}
		else
		{
			if (buflen < sizeof(unsigned int))
			{
				free(buf);
			}
			else
			{
				int destlen = *(unsigned int*)buf;
				char version[3] = { 0 };
				memcpy(version, (char*)buf + sizeof(unsigned int), 3 * sizeof(char));
				int exsize = 0;
				if (version[0] == 'e' && version[1] == 'x')
				{
					exsize = 3 * sizeof(char);
					blockIdVersion = version[2];
				}
				char* cache_data = (char*)buf + sizeof(unsigned int) + exsize;
				int compresstype = destlen >> 28;
				destlen &= (1 << 28) - 1;
				Rainbow::CompressTool ctool(compresstype);

				int size = destlen / sizeof(SyncCustomModelData);
				if (size <= 0)
					return;
				datas.resize(size);
				if (!ctool.decompress(&datas[0], destlen, cache_data, buflen - sizeof(unsigned int) - exsize))
				{
					//assert(0);
					LOG_WARNING("uncompress custom model blob failed");
					free(buf);
					return;
				}
				free(buf);
			}
		}
	}

	//std::vector<unsigned int> colors;
	std::vector<BLOCK_DATA_TYPE>& bolckDatas = pTheTask->bolckDatas;
	std::vector<WCoord>& relativePos = pTheTask->relativePos;

	pTheTask->type = BLOCK_MODEL;
	if (customModelHC.type())
		pTheTask->type = customModelHC.type();

	for (size_t i = 0; i < datas.size(); i++)
	{
		//colors.push_back(datas[i].color);
		if (1 == blockIdVersion)
		{
			bolckDatas.push_back(datas[i].blockData);
		}
		else if (0 == blockIdVersion)
		{
			bolckDatas.push_back(Block::changeBlockNewFormatData(datas[i].blockData, 0));
		}
		int x = datas[i].pos & 0x000000ff;
		int y = (datas[i].pos >> 8) & 0x000000ff;
		int z = datas[i].pos >> 16;
		relativePos.push_back(WCoord(x, y, z));
	}
	if (relativePos.size() <= 0 || bolckDatas.size() <= 0 /*|| colors.size() <= 0*/)
		return;

	if (customModelHC.has_isdownload())
		pTheTask->isDownload = customModelHC.isdownload();
	if (customModelHC.has_itemid())
	{
		pTheTask->itemId = customModelHC.itemid();
	}
	if (customModelHC.has_modelname())
	{
		pTheTask->modelName = customModelHC.modelname();
	}
	if (customModelHC.has_modeldesc())
	{
		pTheTask->modelDesc = customModelHC.modeldesc();
	}
	if (customModelHC.has_authuin())
	{
		pTheTask->authUin = customModelHC.authuin();
	}
	
	// 子线程任务成功完成标志
	pTheTask->bJobThreadRet = true;
}

void CustomModelMgr::bgLoadCustomModelFromHostInMain(void* pTask)
{
	CustomModelFromHostBGLoadTask* pTheTask = (CustomModelFromHostBGLoadTask*)pTask;
	PB_CustomModelHC& customModelHC = pTheTask->m_customModelHC;

	if (!pTheTask->bJobThreadRet)
	{
		return;
	}
	if (pTheTask->type == BLOCK_MODEL)
	{
		if (CustomModelMgr::GetInstancePtr())
		{
			CustomModelMgr::GetInstancePtr()->addCustomModelByHostSync(pTheTask->filename, pTheTask->bolckDatas, pTheTask->relativePos, 
				pTheTask->type, pTheTask->itemId, pTheTask->modelName, pTheTask->modelDesc, pTheTask->authUin, true);
		}
	}
	else if (pTheTask->type > BLOCK_MODEL && pTheTask->type <= BOW_MODEL)
	{
		if (CustomModelMgr::GetInstancePtr())
		{
			CustomModelMgr::GetInstancePtr()->addCustomModelByHostSync(pTheTask->filename, pTheTask->bolckDatas, pTheTask->relativePos, 
				pTheTask->type, pTheTask->itemId, pTheTask->modelName, pTheTask->modelDesc, pTheTask->authUin);
		}
	}

	bool resetIcon = false;
	ItemDef* def = NULL;

	int itemid = customModelHC.itemid();
	if (itemid > 0)
	{
		def = GetDefManagerProxy()->getItemDef(itemid, false);

		Rainbow::Vector3f box((float)customModelHC.mutable_box()->x(), (float)customModelHC.mutable_box()->y(), (float)customModelHC.mutable_box()->z());
		if (!def)
		{
			GetDefManagerProxy()->addDefByCustomModel(itemid, pTheTask->type, pTheTask->filename, customModelHC.modelname().c_str(), customModelHC.modeldesc().c_str(), box);

			CustomModelMgr::GetInstancePtr()->addCustomItemData(itemid, pTheTask->filename, "default", pTheTask->type);
			//GetGameEventQue().postAddCustomModel(itemid);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("id", itemid);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_ADD_CUSTOMMODEL", sandboxContext);
			}

			if (CustomModelMgr::GetInstancePtr())
			{
				std::string className = "";
				int folderIndex = 0;
				if (customModelHC.has_classname())
					className = customModelHC.classname().c_str();

				if (customModelHC.has_folderindex())
					folderIndex = customModelHC.folderindex();

				if (className.empty())
					className = "default";

				if (ResourceCenter::GetInstancePtr())
					ResourceCenter::GetInstancePtr()->addOneResToClass(pTheTask->filename, false, folderIndex);
			}
		}
		else
		{
			if (pTheTask->type == BLOCK_MODEL)
			{
				auto* blockDef = GetDefManagerProxy()->getBlockDef(itemid, false);
				if (blockDef)
					blockDef->Texture2 = pTheTask->filename.c_str();
			}
			else if (pTheTask->type > BLOCK_MODEL && pTheTask->type <= BOW_MODEL)
			{
				def->Model = pTheTask->filename.c_str();
				def->MeshType = CUSTOM_GEN_MESH;
				if (pTheTask->type == PROJECTILE_MODEL)
				{
					auto* projectileDef = GetDefManagerProxy()->getProjectileDef(itemid);
					if (projectileDef)
						projectileDef->Model = pTheTask->filename.c_str();

					auto* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(itemid);

					if (physicsActorDef)
					{
						physicsActorDef->ShapeVal1 = (int)box.x;
						physicsActorDef->ShapeVal2 = (int)box.y;
						physicsActorDef->ShapeVal3 = (int)box.z;
					}
				}
			}

			if (customModelHC.modelname() != "")
				def->Name = customModelHC.modelname().c_str();
			if (customModelHC.modeldesc() != "")
				def->Desc = customModelHC.modeldesc().c_str();

			GetDefManagerProxy()->resetCrcCode(CRCCODE_ITEMS);

			resetIcon = true;
		}
	}

	if (def)
	{
		//刷新图标
		if (resetIcon)
		{
			GetItemIconManagerInterface()->resetItemIcon(def->ID);
		}

		//刷新投射物
		if (pTheTask->type == PROJECTILE_MODEL)
		{
			WorldManager* worldMgr = GetWorldManagerPtr();
			if (worldMgr && GetIPlayerControl())
			{
				World* pworld = worldMgr->getWorld(GetIPlayerControl()->GetPlayerControlCurMapID());
				if (pworld)
				{
					pworld->getActorMgr()->resetActorsByCustomModel(def->ID);
				}
			}
		}
	}

	if (pTheTask->type == BLOCK_MODEL)
	{
		g_BlockMtlMgr.refreshCustomBlockOne(itemid);
	}

	int refreshType = pTheTask->type == BLOCK_MODEL ? 2 : 1;

	if (GetIPlayerControl())
		GetIPlayerControl()->setRefreshType(refreshType);
}
