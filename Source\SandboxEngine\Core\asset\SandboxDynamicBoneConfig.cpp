#include "SandboxDynamicBoneConfig.h"
#include "SandboxStreamBuffer.h"
#include "SandboxReflexTypePolicy.h"

namespace MNSandbox
{
	AutoRef<Stream> SerializeDynamicBoneConfig(const DynamicBoneConfig& config)
	{
		auto& policy1 = ReflexType::GetSingleton<FloatCurve>().GetPolicy();
		auto& policy2 = ReflexType::GetSingleton<Rainbow::Vector3f>().GetPolicy();

		AutoRef<Stream> stream = SANDBOX_NEW(StreamBuffer);
		stream->WriteNumber(config.ver);
		stream->WriteNumber((uint32_t)(config.m_Roots.size()));
		for(auto& one : config.m_Roots)
		{
			stream->WriteNumber(one);
		}
		stream->WriteNumber(config.m_Damping);
		policy1.m_cbReflexToBinary((const void*)(&config.m_DampingDistrib), stream);
		stream->WriteNumber(config.m_Elasticity);
		policy1.m_cbReflexToBinary((const void*)(&config.m_ElasticityDistrib), stream);
		stream->WriteNumber(config.m_Stiffness);
		policy1.m_cbReflexToBinary((const void*)(&config.m_StiffnessDistrib), stream);
		stream->WriteNumber(config.m_Inert);
		policy1.m_cbReflexToBinary((const void*)(&config.m_InertDistrib), stream);
		stream->WriteNumber(config.m_Friction);
		policy1.m_cbReflexToBinary((const void*)(&config.m_FrictionDistrib), stream);
		stream->WriteNumber(config.m_Radius);
		policy1.m_cbReflexToBinary((const void*)(&config.m_RadiusDistrib), stream);
		stream->WriteNumber(config.m_EndLength);
		policy2.m_cbReflexToBinary((const void*)(&config.m_EndOffset), stream);
		policy2.m_cbReflexToBinary((const void*)(&config.m_Gravity), stream);
		policy2.m_cbReflexToBinary((const void*)(&config.m_Force), stream);
		stream->WriteNumber(config.m_BlendWeight);
		stream->WriteNumber((uint32_t)(config.m_Colliders.size()));
		for(auto& one : config.m_Colliders)
		{
			stream->WriteNumber(one.m_BoneId);
			stream->WriteNumber(static_cast<uint8_t>(one.m_Direction));
			stream->WriteNumber(static_cast<uint8_t>(one.m_Bound));
			stream->WriteNumber(one.m_Radius);
			stream->WriteNumber(one.m_Height);
			stream->WriteNumber(one.m_Radius2);
			policy2.m_cbReflexToBinary((const void*)(&one.m_Center), stream);
		}
		stream->WriteNumber((uint32_t)(config.m_Exclusions.size()));
		for(auto& one : config.m_Exclusions)
		{
			stream->WriteNumber(one);
		}
		stream->WriteNumber((uint8_t)(config.m_FreezeAxis));
		return stream;
	}

	bool UnserializeDynamicBoneConfig(DynamicBoneConfig& config,const AutoRef<Stream>& stream)
	{
		auto& policy1 = ReflexType::GetSingleton<FloatCurve>().GetPolicy();
		auto& policy2 = ReflexType::GetSingleton<Rainbow::Vector3f>().GetPolicy();
		
		uint32_t arrayCount = 0; 
		uint8_t tempInt8 = 0;

		config.m_Roots.clear();
		config.m_Colliders.clear();
		config.m_Exclusions.clear();

		stream->ReadNumber(config.ver);
		stream->ReadNumber(arrayCount);
		for(int i = 0; i < arrayCount; i++)
		{
			decltype(config.m_Roots)::value_type one;
			stream->ReadNumber(one);
			config.m_Roots.emplace_back(one);
		}
		stream->ReadNumber(config.m_Damping);
		policy1.m_cbReflexFromBinary((void*)(&config.m_DampingDistrib), stream);
		stream->ReadNumber(config.m_Elasticity);
		policy1.m_cbReflexFromBinary((void*)(&config.m_ElasticityDistrib), stream);
		stream->ReadNumber(config.m_Stiffness);
		policy1.m_cbReflexFromBinary((void*)(&config.m_StiffnessDistrib), stream);
		stream->ReadNumber(config.m_Inert);
		policy1.m_cbReflexFromBinary((void*)(&config.m_InertDistrib), stream);
		stream->ReadNumber(config.m_Friction);
		policy1.m_cbReflexFromBinary((void*)(&config.m_FrictionDistrib), stream);
		stream->ReadNumber(config.m_Radius);
		policy1.m_cbReflexFromBinary((void*)(&config.m_RadiusDistrib), stream);
		stream->ReadNumber(config.m_EndLength);
		policy2.m_cbReflexFromBinary((void*)(&config.m_EndOffset), stream);
		policy2.m_cbReflexFromBinary((void*)(&config.m_Gravity), stream);
		policy2.m_cbReflexFromBinary((void*)(&config.m_Force), stream);
		stream->ReadNumber(config.m_BlendWeight);
		stream->ReadNumber(arrayCount);
		for(int i = 0; i < arrayCount; i++)
		{
			decltype(config.m_Colliders)::value_type one;
			stream->WriteNumber(one.m_BoneId);
			stream->ReadNumber(tempInt8);
			one.m_Direction = static_cast<Rainbow::DynamicBoneManager::Direction>(tempInt8);
			stream->ReadNumber(tempInt8);
			one.m_Bound = static_cast<Rainbow::DynamicBoneManager::Bound>(tempInt8);
			policy2.m_cbReflexFromBinary((void*)(&one.m_Center), stream);
			stream->ReadNumber(one.m_Radius);
			stream->ReadNumber(one.m_Height);
			stream->ReadNumber(one.m_Radius2);
			config.m_Colliders.emplace_back(one);
		}
		stream->ReadNumber(arrayCount);
		for(int i = 0; i < arrayCount; i++)
		{
			decltype(config.m_Exclusions)::value_type one;
			stream->ReadNumber(one);
			config.m_Exclusions.emplace_back(one);
		}
		stream->ReadNumber(tempInt8);
		config.m_FreezeAxis = static_cast<Rainbow::DynamicBoneManager::FreezeAxis>(tempInt8);
		return true;
	}

	IMPLEMENT_REFCLASS(DynamicBoneRef)

	DynamicBoneRef::DynamicBoneRef()
	{

	}

	DynamicBoneRef::~DynamicBoneRef()
	{
		ReleaseDynamicBoneConfig();	
	}

	DynamicBoneConfig* DynamicBoneRef::GetDynamicBoneConfig()
	{
		if(!config)
		{
			config = SANDBOX_NEW(MNSandbox::DynamicBoneConfig);
		}
		return config;
	}

	void DynamicBoneRef::ReleaseDynamicBoneConfig()
	{
		if(config)
		{
			SANDBOX_DELETE(config);
			config = nullptr;
		}
	}

}