#pragma once
#include "Input/OgreInputManager.h"
#include "CameraInfo.h"
//tolua_begin
enum MoveType
{
	UniformSpeedMove = 0,
	LerpMove = 1,
};
//tolua_end

class FreeFlyCamera :public CameraBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	FreeFlyCamera(CameraManager* cameraManager);

	void update(float deltaSeconds) override;
	int onInputEvent(const Rainbow::InputEvent &event) override;
	void onSwitchTo();


	void translateCamera(Rainbow::Vector3f targetPos, Rainbow::Quaternionf targetRot, float moveSpeed, float rotSpeed);
	void lerpCamera(Rainbow::Vector3f targetPos, Rainbow::Quaternionf targetRot, float moveSpeed, float rotSpeed);

	float m_MoveSpeed;
	float m_MoveStrafe;
	float m_MoveForward;
	float m_RotX;
	float m_RotY;

	float m_RotSpeed;

	float m_RotateY;
	float m_RotateX;

	float lastMousePosX;
	float lastMousePosY;

	bool isInStoryMode;
	Rainbow::Vector3f m_TargetPos;
	Rainbow::Quaternionf m_TargetRot;
	float m_StoryMoveSpeed;
	float m_StoryRotSpeed;
	MoveType m_MoveType;
	//tolua_end
}; //tolua_exports