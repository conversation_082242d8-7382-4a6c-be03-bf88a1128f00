#pragma once

// 沙盒曲线资源

#include "SandboxMath.h"
#include "Animation/AnimationCurve.h"
#include "Utilities/dynamic_array.h"

namespace MNSandbox
{
    template<typename TType>
    struct EXPORT_SANDBOXDRIVERMODULE CurvePointTemplate
    {
        int ver = 1;

        float time = 0.0f;
		TType value = Rainbow::Zero<TType>();
		TType inSlope = Rainbow::Zero<TType>();
		TType outSlope = Rainbow::Zero<TType>();
		int tangentMode = 0;
		int weightedMode = 0;
		TType inWeight = Rainbow::DefaultWeight<TType>();
		TType outWeight = Rainbow::DefaultWeight<TType>();

        bool operator==(const CurvePointTemplate<TType>& other) const
		{
            return ver == other.ver &&
                time == other.time &&
                value == other.value &&
                inSlope == other.inSlope &&
                outSlope == other.outSlope &&
                tangentMode == other.tangentMode &&
                weightedMode == other.weightedMode &&
                inWeight == other.inWeight &&
                outWeight == other.outWeight;
		}

        void operator=(const CurvePointTemplate<TType>& other)
        {
            ver = other.ver;
            time = other.time;
            value = other.value;
            inSlope = other.inSlope;
            outSlope = other.outSlope;
            tangentMode = other.tangentMode;
            weightedMode = other.weightedMode;
            inWeight = other.inWeight;
            outWeight = other.outWeight;
        }
    };

    template<typename TType>
    struct EXPORT_SANDBOXDRIVERMODULE CurveTemplate
    {
        using CurvePoint = CurvePointTemplate<TType>;

        int ver = 1;
        std::vector<CurvePoint> m_Curve;
        Rainbow::InternalWrapMode m_PreInfinity = Rainbow::InternalWrapMode::kInternalWrapModeDefault;
        Rainbow::InternalWrapMode m_PostInfinity = Rainbow::InternalWrapMode::kInternalWrapModeDefault;
        math::RotationOrder m_RotationOrder = math::RotationOrder::kOrderDefault;

        bool operator==(const CurveTemplate<TType>& other) const
		{
            auto isSame = ver == other.ver &&
                m_Curve.size() == other.m_Curve.size() &&
                m_PreInfinity == other.m_PreInfinity &&
                m_PostInfinity == other.m_PostInfinity &&
                m_RotationOrder == other.m_RotationOrder;
            if (!isSame)
            {
                return false;
            }
            for (int i = 0; i < m_Curve.size(); i++)
            {
                if (m_Curve[i] == other.m_Curve[i])
                {
                }
                else
                {
                    return false;
                }
            }
            return true;
		}

        bool operator!=(const CurveTemplate<TType>& other) const
        {
            return !(*this == other);
        }

        void operator=(const CurveTemplate<TType>& other)
        {
            ver = other.ver;
            m_PreInfinity = other.m_PreInfinity;
            m_PostInfinity = other.m_PostInfinity;
            m_RotationOrder = other.m_RotationOrder;

            m_Curve.resize(other.m_Curve.size());

            for (int i = 0; i < other.m_Curve.size(); i++)
            {
                CurvePoint point;
                point = other.m_Curve[i];
                m_Curve[i] = point;
            }
        }
    };

    template<typename TType>
    void TransformToRainbowCurve(Rainbow::AnimationCurveTpl<TType>& rbCurve, const CurveTemplate<TType>& msCurve)
    {
        rbCurve.SetPreInfinityInternal(msCurve.m_PreInfinity);
        rbCurve.SetPostInfinityInternal(msCurve.m_PostInfinity);
        rbCurve.SetRotationOrder(msCurve.m_RotationOrder);

        rbCurve.ResizeUninitialized(0);
        for (auto& one : msCurve.m_Curve)
        {
            Rainbow::KeyframeTpl<TType> keyFrame;
            keyFrame.time = one.time;
            keyFrame.value = one.value;
            keyFrame.inSlope = one.inSlope;
            keyFrame.outSlope = one.outSlope;
#if RAINBOW_EDITOR
            keyFrame.tangentMode = one.tangentMode;
#endif
            keyFrame.weightedMode = one.weightedMode;
            keyFrame.inWeight = one.inWeight;
            keyFrame.outWeight = one.outWeight;

            rbCurve.AddKey(keyFrame);
        }
    }

    template<typename TType>
    void ResetToMNSandboxCurve(const Rainbow::AnimationCurveTpl<TType>& rbCurve, CurveTemplate<TType>& msCurve)
    {
        msCurve.m_PreInfinity = rbCurve.GetPreInfinityInternal();
        msCurve.m_PostInfinity = rbCurve.GetPostInfinityInternal();
        msCurve.m_RotationOrder = rbCurve.GetRotationOrder();
        msCurve.m_Curve.clear();

        for (int i = 0; i < rbCurve.GetKeyCount(); i++)
        {
            auto keyFrame = rbCurve.GetKey(i);
            MNSandbox::CurvePointTemplate<TType> point;
            point.time = keyFrame.time;
            point.value = keyFrame.value;
            point.inSlope = keyFrame.inSlope;
            point.outSlope = keyFrame.outSlope;
#if RAINBOW_EDITOR
            point.tangentMode = keyFrame.tangentMode;
#endif
            point.weightedMode = keyFrame.weightedMode;
            point.inWeight = keyFrame.inWeight;
            point.outWeight = keyFrame.outWeight;

            msCurve.m_Curve.push_back(point);
        }
    }

    using FloatCurvePoint = CurvePointTemplate<float>;
    using QuaternionCurvePoint = CurvePointTemplate<Rainbow::Quaternionf>;
    using Vec3CurvePoint = CurvePointTemplate<Rainbow::Vector3f>;
    using Vec4CurvePoint = CurvePointTemplate<Rainbow::Vector4f>;

    using FloatCurve = CurveTemplate<float>;
    using QuaternionCurve = CurveTemplate<Rainbow::Quaternionf>;
    using Vec3Curve = CurveTemplate<Rainbow::Vector3f>;
    using Vec4Curve = CurveTemplate<Rainbow::Vector4f>;



    // floatCurve范围区间
    struct EXPORT_SANDBOXDRIVERMODULE RangeFloatCurve
    {
        FloatCurve _min{};
        FloatCurve _max{};
        RangeFloatCurve();
        RangeFloatCurve(bool);
        RangeFloatCurve(FloatCurve val);
        RangeFloatCurve(FloatCurve min, FloatCurve max);

        void set(FloatCurve min, FloatCurve max)
        {
            _min = min;
            _max = max;
        }
        bool operator==(const RangeFloatCurve& v) const
        {
            return _min == v._min && _max == v._max;
        }
        bool operator!=(const RangeFloatCurve& v) const
        {
            return _min != v._min || _max != v._max;
        }
        void operator=(const RangeFloatCurve& v)
        {
            _min = v._min;
            _max = v._max;
        }
    };
}
