#pragma once


#include "Math/Vector2f.h"
#include "Math/Vector3f.h"
#include "Utilities/dynamic_array.h"

#if PLATFORM_WEBGL
#include <values.h> // FLT_MAX
#endif

int CalculateExpandedClippedBoxConvexHull(Rainbow::Vector2f* segments2d, Rainbow::Vector2f* corners2d, const Rainbow::Vector3f* boxVertices,
    float slabMinY, float slabMaxY, float radius);

int CalculateClippedBoxConvexHull(Rainbow::Vector3f* hullVertices, const Rainbow::Vector3f* boxVertices, float slabMinY, float slabMaxY);

// returns true if y-axis aligned cylinder overlaps oriented box
// in case of overlap the 'penetration' is positive.
// in case of no overlap the 'penetration' is set to 0.
bool AlignedCylinderOverlapsOrientedBox(
    float* penetration, const Rainbow::Vector3f* boxVertices, const Rainbow::Vector3f& cylinderPosition,
    float cylinderRadius, float cylinderMinY, float cylinderMaxY);

// returns true if y-axis aligned cylinder overlaps oriented capsule
// in case of overlap the 'penetration' is positive.
// in case of no overlap the 'penetration' is set to 0.
bool AlignedCylinderOverlapsOrientedCapsule(
    float* penetration, const Rainbow::Vector3f& capsuleCenter, const Rainbow::Vector3f& capsuleExtents, const Rainbow::Vector3f& capsuleAxis,
    const Rainbow::Vector3f& cylinderPosition, float cylinderRadius, float cylinderMinY, float cylinderMaxY);

int CalculateClippedCapsule(
    Rainbow::Vector2f* points, float* radius,
    const Rainbow::Vector3f& capsuleCenter, const Rainbow::Vector3f& capsuleExtents, const Rainbow::Vector3f& capsuleAxis,
    float slabMinY, float slabMaxY);


typedef dynamic_array<Rainbow::Vector2f> Vertex2Array;

void CalculatePointsFromClippedBox(Vertex2Array& points, const Rainbow::Vector3f* box, float slabMinY, float slabMaxY);

void CalculateConvexHull(Vertex2Array& hull, Vertex2Array& points);

bool CircleHullOverlap(float* penetration, const Vertex2Array& hull, const Rainbow::Vector2f& circlePosition, float circleRadius);

void FitCapsuleToExtents(float* radius, float* height, const Rainbow::Vector3f& capsuleExtents);
void CalcCapsuleWorldExtents(Rainbow::Vector3f* worldExtents, const Rainbow::Vector3f& localExtents, const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis);
void CalcBoxWorldExtents(Rainbow::Vector3f* worldExtents, const Rainbow::Vector3f& localExtents, const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis);

void CalculateOrientedBoxCorners(Rainbow::Vector3f* cornerVertices, const Rainbow::Vector3f& position, const Rainbow::Vector3f& extents,
    const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis);
