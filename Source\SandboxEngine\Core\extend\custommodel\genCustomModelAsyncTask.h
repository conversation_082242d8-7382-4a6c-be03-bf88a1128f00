#pragma once
#include <vector>
#include <unordered_map>
#include "world_types.h"
#include "CoreCommonDef.h"
#include "Common/SingletonDefinition.h"
#include "LegacyOgreVertexIndexData.h"

class GenCustomModelAsyncTaskMgr {
	class GenCustomModelAsyncTask
	{
	public:
		GenCustomModelAsyncTask():eMeshType(NORMAL_MESH), isBlock(false){}
		~GenCustomModelAsyncTask() {
			ENG_RELEASE(ib);
			ENG_RELEASE(vb);
		}

		void DoInJobThread();
		void DoInMainThread();

		std::vector<WCoord> vpos;
		std::vector<unsigned int> color;
		ITEM_MESH_TYPE eMeshType;
		bool isBlock;
		std::string hex;

		Rainbow::MinMaxAABB aabb = Rainbow::MinMaxAABB::invalid;
		Rainbow::IndexData* ib = nullptr;
		Rainbow::OgreVertexData* vb = nullptr;
	};
public:
	GenCustomModelAsyncTaskMgr() {}
	~GenCustomModelAsyncTaskMgr() {}

	static void genCustomModelInJobThread(GenCustomModelAsyncTask* pTask);
	static void genCustomModelRetInMainThread(GenCustomModelAsyncTask* pTask);

	void genCustomModelAsync(std::vector<WCoord>& vpos, std::vector<unsigned int>& color,
		ITEM_MESH_TYPE eMeshType, bool isBlock, const char* hex);

protected:
	std::unordered_map<std::string, int>  m_geningMap;
};

DECLARE_GETMETHOD(GenCustomModelAsyncTaskMgr)