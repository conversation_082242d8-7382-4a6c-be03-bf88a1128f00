#pragma once

#include "SandboxServiceNode.h"


namespace MNSandbox 
{
	class EXPORT_SANDBOXDRIVERMODULE ScriptContext : public ServiceNode
	{
		DECLARE_SCENEOBJECTCLASS(ScriptContext)
	public:
		ScriptContext();
		virtual ~ScriptContext();

		virtual void InitDefaultChildren() override;
	protected:
		virtual void OnBeginPlay() override;
		virtual void OnEndPlay() override;

		void CreateDefaultChildren();

		AutoRef<SandboxNode> addCoreScriptLocal(std::string nodename, std::string scriptpath, AutoRef<SandboxNode> parent);
	
		
		static ReflexClassMethodArgsRet<ScriptContext, AutoRef<SandboxNode>, std::string, std::string, AutoRef<SandboxNode>> R_AddCoreScriptLocal; // 
	};
};