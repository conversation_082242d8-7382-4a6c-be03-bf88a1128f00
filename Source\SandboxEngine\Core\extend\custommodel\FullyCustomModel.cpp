#include "FullyCustomModel.h"
#include "FullyCustomModelMgr.h"

#include "Common/OgreUtils.h"

#include "blocks/BlockMaterialMgr.h"
#include "custommodel/CustomModelMgr.h"
#include "custommodel/genCustomModel.h"
#include "custommotion/CustomMotionMgr.h"
#include "custommotion/CustomMotion.h"
#include "IPlayerControl.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "CustomModel.h"
#include "CustomModelWaitSyncListMgr.h"
#include "CommonUtil.h"

#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#define O_BINARY (0)
#endif

using namespace Rainbow;
using namespace MNSandbox;

extern void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype);

FullyCustomModel::FullyCustomModel(): m_eFcmSaveType(0)
{
	m_iAuthUin = 0;
	m_sKey = "";
	m_sName = "";
	m_sDesc = "";
	m_sAuthName = "";
	m_iVersion = 1;
	m_bNeedSave = false;
	m_sFileName = "";
	m_vFcbd.clear();
	m_bLeaveworlddel = false;
	m_bEditing = false;
	m_attachmentBoneNames.clear();
	m_splitSkeletonModel = false;
	m_showSkeletonModel = true;

	m_iUseDownloadCMNum = 0;

	m_CurSelectBoneEffect.effect = NULL;
	m_iPackingCMForwardDir = DIR_NEG_Z;
	m_Dim = WCoord(0, 0, 0);
	m_BoundBox.reset();

	m_ref = SANDBOX_NEW(MNSandbox::Ref);
}

FullyCustomModel::~FullyCustomModel()
{
	for (auto fcbd : m_vFcbd)
	{
		ENG_DELETE(fcbd);
	}
	m_vFcbd.clear();

	
	if (m_CurSelectBoneEffect.effect )
	{
		Model* model = m_CurSelectBoneEffect.effect;
		if (model&& !model->GetTransform()->GetParent())
			Model::Destory(m_CurSelectBoneEffect.effect);
	}

	m_attachmentBoneNames.clear();
	
	SANDBOX_RELEASE(m_ref);
}


bool FullyCustomModel::loadDownloadSubCMs(std::string path, std::map<std::string, bool> *subcustommodels, int iType /* = -1 */)
{
	if (!CustomModelMgr::GetInstancePtr())
		return false;

	auto startPos = path.find("w");
	auto endPos = path.find("/custommodel");

	long long owid = 0;
	if (startPos != std::string::npos && endPos != std::string::npos)
	{
		std::string sowid = path.substr(startPos+1, endPos);
		std::stringstream sstr;
		sstr << sowid;
		if (!(sstr >> owid))
			return false;
	}

	int libType = RES_MODEL_CLASS;
	if(iType == PREVIEW_MODEL_CLASS)
		libType = PREVIEW_MODEL_CLASS;
	else if (owid > 0)
		libType = MAP_MODEL_CLASS;

	auto iter = subcustommodels->begin();
	for (; iter != subcustommodels->end(); iter++)
	{
		//TODO 检查subcustommodels[i]是否load过
		if (!CustomModelMgr::GetInstancePtr()->getCustomModel(libType, iter->first))
		{
			if (iType == PREVIEW_MODEL_CLASS)
			{
				CustomModelMgr::GetInstancePtr()->loadOneResCustomModel(PREVIEW_MODEL_CLASS, iter->first, true);
			}
			else if (owid > 0)
			{
				int id = -1;
				int modelType = -1;
				bool addCustomItem = false;
				if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)  //微缩组合移动到地图库时要给子微缩分配方块id
				{
					id = CustomModelMgr::GetInstancePtr()->getCustomItemID(iter->first);
					if (id <= 0)
					{
						id = CustomModelMgr::GetInstancePtr()->getFreeId(BLOCK_MODEL);
						if (id <= 0)
						{
							//没有可分配的id了
							//GetIPlayerControl()->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
							CommonUtil::GetInstance().PostInfoTips(3729);
							return false;
						}

						addCustomItem = true;
					}

					modelType = BLOCK_MODEL;
				}

				if (!CustomModelMgr::GetInstancePtr()->loadOneMapCustomModel(modelType, iter->first, "default", 0, id, addCustomItem,true))
					continue;
			}
			else
			{
				CustomModelMgr::GetInstancePtr()->loadOneResCustomModel(RES_MODEL_CLASS, iter->first,true);
			}
		}
	}

	return true;
}


void FullyCustomModel::setPackingFCMData(WCoord& packingpos, std::string name, std::string modelfilename, Rainbow::Quaternionf& quat, int dir/* =-1 */)
{
	std::string boneName = GetDefManagerProxy()->getStringDef(16013);
	auto fatherBoneData = findFullyCustomBoneData(boneName);
	if (!fatherBoneData)
	{
		fatherBoneData = ENG_NEW(FullyCustomBoneData)(this);
		fatherBoneData->name = boneName;
		fatherBoneData->quat = quat;
		fatherBoneData->show = false;
		m_vFcbd.push_back(fatherBoneData);
	}

	FullyCustomBoneData *boneData = ENG_NEW(FullyCustomBoneData)(this);
	boneData->name = name;
	boneData->fathername = boneName;
	boneData->model = modelfilename;
	boneData->quat = quat;
	boneData->offsetpos.Set(
		(float)(packingpos.x*BLOCK_SIZE), 
		(float)(packingpos.y*BLOCK_SIZE), 
		(float)(packingpos.z*BLOCK_SIZE));

	fatherBoneData->vChildFcbds.push_back(boneData);

	if (!modelfilename.empty() && CustomModelMgr::GetInstancePtr()->isDownloadCM(modelfilename))
	{
		m_iUseDownloadCMNum++;
	}

	if (dir >= 0)
	{
		m_PackingCMForwardDirs[name] = dir;
	}
}

void FullyCustomModel::setPackingFCMInfoByHostSync(std::string name, std::string desc, std::string skey, short dir, WCoord minpos, WCoord maxpos, int authUin)
{
	m_sName = name;
	m_sDesc = desc;
	m_sKey = skey;
	m_iPackingCMForwardDir = dir;
	m_eFcmSaveType = FULLY_PACKING_CUSTOM_MODEL;
	m_BoundBox.setPoints(minpos, maxpos);
	m_iAuthUin = authUin;
}
WCoord FullyCustomModel::getPackingFcmDim()
{
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		if (m_Dim != WCoord(0, 0, 0))
			return m_Dim;

		auto *boneData = getBoneDataByIndex(0);
		if (!boneData)
			return m_Dim;

		for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
		{
			auto &childBone = boneData->vChildFcbds[i];

			if (childBone->offsetpos.x > m_Dim.x)
				m_Dim.x = (int)childBone->offsetpos.x;
			if (childBone->offsetpos.y > m_Dim.y)
				m_Dim.y = (int)childBone->offsetpos.y;
			if (childBone->offsetpos.z > m_Dim.z)
				m_Dim.z = (int)childBone->offsetpos.z;
		}
	}

	return m_Dim;
}

CollideAABB FullyCustomModel::getFcmBoundBox()
{	
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		if (m_BoundBox.pos != WCoord(MAX_INT, MAX_INT, MAX_INT))
			return m_BoundBox;

		auto *boneData = getBoneDataByIndex(0);
		if (!boneData)
			return m_BoundBox;

		WCoord minPos(BLOCK_SIZE*100, BLOCK_SIZE * 100, BLOCK_SIZE * 100);
		WCoord maxPos(0, 0, 0);
		for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
		{
			auto &childBone = boneData->vChildFcbds[i];

			auto tempMaxPos = getSubCMRealMaxPos(childBone->model, childBone->name);
			if(tempMaxPos.x < 0)
				continue;

			auto tempMinPos = getSubCMRealMinPos(childBone->model, childBone->name);

			int tempX = (int)tempMinPos.x;
			tempMinPos.x = (float)(BLOCK_MODEL_SIZE - 1 - tempMaxPos.x	);
			tempMaxPos.x = (float)(BLOCK_MODEL_SIZE - 1 - tempX			);

			if (childBone->offsetpos.x/5 + tempMinPos.x < minPos.x)
				minPos.x = (int)(childBone->offsetpos.x/5 + tempMinPos.x);
			if (childBone->offsetpos.y/5 + tempMinPos.y < minPos.y)
				minPos.y = (int)(childBone->offsetpos.y/5 + tempMinPos.y);
			if (childBone->offsetpos.z/5 + tempMinPos.z < minPos.z)
				minPos.z = (int)(childBone->offsetpos.z/5 + tempMinPos.z);

			if (childBone->offsetpos.x/5 + tempMaxPos.x > maxPos.x)
				maxPos.x = (int)(childBone->offsetpos.x/5 + tempMaxPos.x);
			if (childBone->offsetpos.y/5 + tempMaxPos.y > maxPos.y)
				maxPos.y = (int)(childBone->offsetpos.y/5 + tempMaxPos.y);
			if (childBone->offsetpos.z/5 + tempMaxPos.z > maxPos.z)
				maxPos.z = (int)(childBone->offsetpos.z/5 + tempMaxPos.z);
		}

		m_BoundBox.setPoints(minPos, maxPos + WCoord(1, 1, 1));
	}
	else
	{
		if (m_BoundBox.pos != WCoord(MAX_INT, MAX_INT, MAX_INT))
			return m_BoundBox;

		calBoundBox();
	}
	
	return m_BoundBox;
}

void FullyCustomModel::calBoundBox(FullyCustomBoneData * fcbdRoot /* = NULL */)
{
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
		return;

	auto* pvFcbds = &m_vFcbd;
	if (fcbdRoot)
	{
		pvFcbds = &fcbdRoot->vChildFcbds;
	}

	for (int i = 0; i < (int)pvFcbds->size(); i++)
	{
		FullyCustomBoneData* fcbd = (*pvFcbds)[i];

		calBoundBox(fcbd);

		if (fcbd->model.empty())
			continue;

		auto* pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, fcbd->model);
		if (!pCm)
			pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, fcbd->model);

		if(!pCm)
			continue;

		Rainbow::Matrix4x4f base_tm;
		Rainbow::makeSRTMatrix(base_tm, fcbd->submodelscale3, fcbd->submodelquat, fcbd->submodelpos);

		Rainbow::Matrix4x4f bone_tm;
		Rainbow::makeSRTMatrix(bone_tm, fcbd->scale3, fcbd->quat, fcbd->offsetpos);
		//base_tm *= bone_tm;
		Rainbow::Matrix4x4f temp_tm;
		temp_tm = Matrix4x4Mul(base_tm, bone_tm);   // MultiplyMatrices3x4(base_tm, bone_tm, temp_tm);
		base_tm = temp_tm;
		std::string fatherName = fcbd->fathername;
		while (!fatherName.empty())
		{
			auto fcbdParent = findFullyCustomBoneData(fatherName);
			if (fcbdParent)
			{
				Rainbow::Matrix4x4f father_tm;
				Rainbow::makeSRTMatrix(father_tm, fcbdParent->scale3, fcbdParent->quat, fcbdParent->offsetpos);
				//base_tm *= father_tm;
				temp_tm = Matrix4x4Mul(base_tm, father_tm); //MultiplyMatrices3x4(base_tm, father_tm, temp_tm);
				base_tm = temp_tm;

				fatherName = fcbdParent->fathername;
			}
			else
				fatherName = "";
		}

		calOneCMBoundBox(pCm, base_tm);
	}
}

void FullyCustomModel::calOneCMBoundBox(CustomModel *cm, Rainbow::Matrix4x4f basetm)
{
	Rainbow::Vector3f maxP = (cm->m_MaxPos + Rainbow::Vector3f::one - cm->m_MinPos) * 5.0f;
	Rainbow::Vector3f minP(0, 0, 0);
	Rainbow::Vector3f centerPos = (maxP - minP) / 2.0f;
	std::vector<Rainbow::Vector3f> zenithOffsets;
	zenithOffsets.push_back(minP - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(minP.x, minP.y, maxP.z) - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(minP.x, maxP.y, minP.z) - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(minP.x, maxP.y, maxP.z) - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(maxP.x, minP.y, minP.z) - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(maxP.x, minP.y, maxP.z) - centerPos);
	zenithOffsets.push_back(Rainbow::Vector3f(maxP.x, maxP.y, minP.z) - centerPos);
	zenithOffsets.push_back(maxP - centerPos);
	auto boxMin = m_BoundBox.minPos();
	auto boxMax = m_BoundBox.maxPos();

	for (size_t i = 0; i < zenithOffsets.size(); i++)
	{
		Rainbow::Vector3f &offset = zenithOffsets[i];
		Rainbow::Matrix4x4f temp_tm;
		Rainbow::makeSRTMatrix(temp_tm, Rainbow::Vector3f(1.0, 1.0, 1.0), Rainbow::Quaternionf::identity, offset);

		Rainbow::Matrix4x4f temp_tm_;
		temp_tm_ = Matrix4x4Mul(temp_tm, basetm); //MultiplyMatrices3x4(temp_tm, basetm, temp_tm_);

		Rainbow::Vector3f translate = temp_tm_.GetPosition();
		
		if (translate.x < boxMin.x)
			boxMin.x = (int)translate.x;
		if (translate.y < boxMin.y)
			boxMin.y = (int)translate.y;
		if (translate.z < boxMin.z)
			boxMin.z = (int)translate.z;

		if (translate.x > boxMax.x)
			boxMax.x = (int)translate.x;
		if (translate.y > boxMax.y)
			boxMax.y = (int)translate.y;
		if (translate.z > boxMax.z)
			boxMax.z = (int)translate.z;
	}

	m_BoundBox.setPoints(boxMin, boxMax);
}

bool FullyCustomModel::isAttachmentBoneName(std::string boneName)
{
	if (!findFullyCustomBoneData(boneName))
	{
		return false;
	}

	for (auto _boneName : m_attachmentBoneNames)
	{
		if (_boneName == boneName)
		{
			return true;
		}
	}

	return false;
}

void FullyCustomModel::setAttachmentBoneName(std::string boneName, bool isAttachement)
{
	if (!findFullyCustomBoneData(boneName))
	{
		return;
	}

	if (isAttachement)
	{
		if (!isAttachmentBoneName(boneName))
		{
			m_attachmentBoneNames.push_back(boneName);
		}
	}
	else
	{
		m_attachmentBoneNames.erase(std::remove(m_attachmentBoneNames.begin(), m_attachmentBoneNames.end(), boneName), m_attachmentBoneNames.end());
	}

	setNeedSave(true);
}

void FullyCustomModel::setAttachmentBoneNames(std::vector<std::string>& boneNames)
{
	m_attachmentBoneNames.clear();
	for (auto _boneName : boneNames)
	{
		setAttachmentBoneName(_boneName, true);
	}
}

void FullyCustomModel::getAttachmentBoneNames(std::vector<std::string>& boneNames)
{
	boneNames.clear();

	for (auto _boneName : m_attachmentBoneNames)
	{
		if (findFullyCustomBoneData(_boneName))
		{
			boneNames.push_back(_boneName);
		}
	}
}

void FullyCustomModel::setModelDataByPackingIcon(Entity* entity, FullyCustomBoneData* fcbdRoot /* = NULL */,
	bool bUpdateWhenCustomModelReady/* = false*/, WCoord offset/* =WCoord(MAX_INT, MAX_INT, MAX_INT) */)
{
	if (!entity)
		return;

	vector<FullyCustomBoneData*>* pvFcbds = &m_vFcbd;
	if (fcbdRoot)
	{
		pvFcbds = &fcbdRoot->vChildFcbds;
	}
	vector<FullyCustomBoneData*>& vFcbds = *pvFcbds;

	entity->PreRebuildSkeleton();

	float scale = 1.0;
	if (offset == WCoord(0, 0, 0))
	{
		WCoord dim = getPackingFcmDim() + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
		CollideAABB box = getFcmBoundBox();
		WCoord boxMinPos = box.minPos();
		WCoord distance = box.maxPos() - boxMinPos;
		Rainbow::Vector3f vDis((float)distance.x, (float)distance.y, (float)distance.z);
		offset = (Rainbow::Vector3f((float)boxMinPos.x, (float)boxMinPos.y, (float)boxMinPos.z) + vDis / 2.0f) * 5.0f;

		float width = (float)(box.maxPos().x - boxMinPos.x);
		float heith = (float)(box.maxPos().y - boxMinPos.y);
		float length = (float)(box.maxPos().z - boxMinPos.z);
		float maxLen = Max(width, heith);
		maxLen = Max(maxLen, length);
		scale = 1.8f * (1.0f / (maxLen / BLOCK_MODEL_SIZE));
	}

	setModelDataByPackingIconStep1(entity, fcbdRoot, offset);

	if (entity->GetMainModel())
		entity->GetMainModel()->BuildAllCunsomData();

	CustomModelWaitSyncListMgr::TCustomAvatarModelDataMap* datalist = nullptr;
	if (bUpdateWhenCustomModelReady) {
		if (GetCustomModelWaitSyncListMgrPtr()) {
			bool bIsPacking = getModelType() == FULLY_PACKING_CUSTOM_MODEL;
			datalist = GetCustomModelWaitSyncListMgrPtr()->createEntityWaitMap(entity, bIsPacking);
		}
	}

	setModelDataByPackingIconStep2(entity, fcbdRoot, datalist);
	int nWaiMapSize = datalist ? datalist->size() : 0;

	if (!fcbdRoot && entity)
	{
		if (!bUpdateWhenCustomModelReady || nWaiMapSize <= 0)  //???????????????????????????????????
		{
			entity->GetMainModel()->SetName(m_sKey.c_str());
			GenCustomModelManager::GetInstance().bindCusntomObjs(entity);
		}
	}
	if (!fcbdRoot && entity)
	{
		int a = 1;
		entity->SetPosition(Rainbow::Vector3f(0, 125, 0));
		entity->SetScale(scale);

		float rotateP = 15;
		float rotateR = 15;
		float rotateY = (float)(placeDirConverAngle() - 45);
		getPackingIconPitchAndRollByYaw(rotateY, rotateP, rotateR);

		entity->SetRotation(rotateY, rotateP, rotateR);
	}
}
void FullyCustomModel::setModelDataByPackingIconStep1(Rainbow::Entity* entity, FullyCustomBoneData* rootdata, WCoord offset)
{
	if (!entity)
		return;

	vector<FullyCustomBoneData*>* pvFcbd = &m_vFcbd;
	if (rootdata)
	{
		pvFcbd = &rootdata->vChildFcbds;
	}
	vector<FullyCustomBoneData*>& vFcbds = *pvFcbd;

	WCoord dim = getPackingFcmDim();
	for (int i = 0; i < (int)vFcbds.size(); i++)
	{
		FullyCustomBoneData* fcbd = vFcbds[i];
		WCoord offsetPos((int)fcbd->offsetpos.x, (int)fcbd->offsetpos.y, (int)fcbd->offsetpos.z);

		if (isPackingFCMValidBone(fcbd))
		{
			converBlockPosByPlaceDir(offsetPos, DIR_POS_X, dim, BLOCK_MODEL);
			entity->AddCustomBone(fcbd->name.c_str(), fcbd->fathername.c_str(), fcbd->scale3, fcbd->quat,
				Rainbow::Vector3f((float)(offsetPos.x - offset.z), (float)(offsetPos.y - offset.y), (float)(offsetPos.z - offset.x)), false);
		}
		else
			entity->AddCustomBone(fcbd->name.c_str(), fcbd->fathername.c_str(), fcbd->scale3, fcbd->quat,
				Rainbow::Vector3f((float)offsetPos.x, (float)offsetPos.y, (float)offsetPos.z), false);

		setModelDataByPackingIconStep1(entity, fcbd, offset);
	}
}

void FullyCustomModel::setModelDataByPackingIconStep2(Rainbow::Entity* entity, FullyCustomBoneData* rootdata, std::map<std::string, std::vector<CustomAvatarModelData>>* datalist)
{
	if (!entity)
		return;


	vector<FullyCustomBoneData*>* pvFcbd = &m_vFcbd;
	if (rootdata)
	{
		pvFcbd = &rootdata->vChildFcbds;
	}
	vector<FullyCustomBoneData*>& vFcbds = *pvFcbd;

	WCoord dim = getPackingFcmDim();
	for (int i = 0; i < (int)vFcbds.size(); i++)
	{
		FullyCustomBoneData* fcbd = vFcbds[i];
		WCoord offsetPos((int)fcbd->offsetpos.x, (int)fcbd->offsetpos.y, (int)fcbd->offsetpos.z);

		int objclass = 0;
		Rainbow::Model* pSubModel = nullptr;
		// 上层有传 datalist 才表示上层有能力处理异步加载的情况
		if (!datalist || CustomModelMgr::GetInstancePtr()->isAvatarCMItemModelInCache(fcbd->model, NORMAL_MESH))
		{
			pSubModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(fcbd->model, NORMAL_MESH);
		}
		else
		{
			// 异步加载, 让 modelBone 保持 null, 下面 ApplyModelDataCallFunc 会往 mCamds 中添加等待信息
			CustomModelMgr::GetInstancePtr()->loadAvatarCMItemModelToCache(fcbd->model, NORMAL_MESH);
		}

		if (!pSubModel)
		{
			if (datalist && !fcbd->model.empty() && !isPackingFCMRootBone(fcbd))
			{
				std::vector<CustomAvatarModelData> vCamds;
				vCamds.clear();
				CustomAvatarModelData camd;
				CamdFromFcbd(camd, fcbd);
				vCamds.push_back(camd);
				(*datalist)[fcbd->name] = vCamds;
			}
			if (FullyCustomModelMgr::GetInstancePtr())
			{
				pSubModel = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
			}
			objclass = 1;
			if (!pSubModel)
				continue;
		}

		pSubModel->SetPosition(fcbd->submodelpos);
		pSubModel->SetRotation(fcbd->submodelquat);
		pSubModel->SetScale(fcbd->submodelscale3);
		bool bind = false;
		if (m_bEditing || fcbd->show)
		{
			bind = true;
		}
		if (m_bEditing)
		{
			pSubModel->ShowSkins(fcbd->show);
		}
		if (bind)
		{
			if (fcbd->model2)
			{
				Model* oldModel = fcbd->model2;
				if (entity->UnbindObject(oldModel))
					Model::Destory(oldModel);
			}
			fcbd->model2 = nullptr;
			fcbd->setModel(pSubModel);
			fcbd->setTextureId(fcbd->texId);
			entity->BindCunstomObject(fcbd->name.c_str(), pSubModel, objclass);
		}

		if (!bind)
		{
			Model::Destory(pSubModel);
		}
		setModelDataByPackingIconStep2(entity, fcbd, datalist);
	}
}
void FullyCustomModel::getPackingIconPitchAndRollByYaw(float yaw, float &pitch, float &roll)
{
	if (yaw == -45)
	{
		pitch = -15;
		roll = 15;
	}
	else if(yaw == 45)
	{
		pitch = -15;
		roll = -15;
	}
	else if (yaw == 135)
	{
		pitch = 15;
		roll = -15;
	}
	else
	{
		pitch = 15;
		roll = 15;
	}
}

int FullyCustomModel::placeDirConverAngle()
{
	if (m_iPackingCMForwardDir == DIR_POS_X)
		return 90;
	else if (m_iPackingCMForwardDir == DIR_POS_Z)
		return 180;
	else if (m_iPackingCMForwardDir == DIR_NEG_X)
		return 270;

	return 0;
}

bool FullyCustomModel::isPackingFCMValidBone(FullyCustomBoneData *data)
{
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL && !data->fathername.empty())
		return true;

	return false;
}

bool FullyCustomModel::isPackingFCMRootBone(FullyCustomBoneData *data)
{
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL && data->fathername.empty())
		return true;

	return false;
}

bool FullyCustomModel::isPackingFCM()
{
	return m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL;
}

bool FullyCustomModel::moveResAvatarModelToMap(long long owid, std::string classname,int folderindex, FullyCustomBoneData *rootdata /* = NULL */, bool isdownloadfcm /* = false */, int specialType/* = NORMAL_WORLD*/)
{
	if (!CustomModelMgr::GetInstancePtr())
		return false;

	auto *bones = &m_vFcbd;
	if (rootdata)
	{
		bones = &rootdata->vChildFcbds;
	}

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	for (int i = 0; i < (int)bones->size(); i++)
	{
		auto *data = (*bones)[i];

		if (!data->model.empty())
		{
			auto customModel = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, data->model);
			if (!customModel)
			{
				char srcPath[256];
				char destPath[256];
				snprintf(srcPath, sizeof(srcPath)-1, "data/custommodel/%s.cm", data->model.c_str());
				snprintf(destPath, sizeof(destPath)-1, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, data->model.c_str());

				if (!GetFileManager().IsFileExistWritePath(destPath))
					GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

				if (isdownloadfcm) //下载的资源不在这里load 子cm
				{
					if (!moveResAvatarModelToMap(owid, classname, folderindex, data, isdownloadfcm, specialType))
						return false;

					continue;
				}

				int id = -1;
				int modelType = -1;
				if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)  //微缩组合移动到地图库时要给子微缩分配方块id
				{
					id = CustomModelMgr::GetInstancePtr()->getFreeId(BLOCK_MODEL);
					if (id <= 0)
					{
						//没有可分配的id了
						//GetIPlayerControl()->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
						CommonUtil::GetInstance().PostInfoTips(3729);
						return false;
					}
					
					modelType = BLOCK_MODEL;
				}
				
				if (CustomModelMgr::GetInstancePtr()->loadOneMapCustomModel(modelType, data->model, classname, folderindex, id))
				{
					if (!moveResAvatarModelToMap(owid, classname, folderindex, data, isdownloadfcm, specialType))
						return false;
				}
				else
					return false;
			}
			else
			{
				if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)  //微缩组合移动到地图库时要给子微缩分配方块id
				{
					auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(data->model, true);
					if (!customItem)
					{
						int id = CustomModelMgr::GetInstancePtr()->getFreeId(BLOCK_MODEL);
						if (id <= 0)
						{
							//没有可分配的id了
							//GetIPlayerControl()->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
							CommonUtil::GetInstance().PostInfoTips(3729);
							return false;
						}

						CustomModelMgr::GetInstancePtr()->addCustomItemData(id, data->model);
						GetDefManagerProxy()->addDefByCustomModel(id, BLOCK_MODEL, data->model);
					}
				}

				if (!moveResAvatarModelToMap(owid, classname, folderindex, data, isdownloadfcm, specialType))
					return false;
			}
			
		}
		else
		{
			if (!moveResAvatarModelToMap(owid, classname, folderindex, data, isdownloadfcm, specialType))
				return false;
		}
		
	}

	return true;
}

void FullyCustomModel::moveMapAvatarModelToRes(long long owid, FullyCustomBoneData *rootdata /* = NULL */, bool isdownloadfcm/* =false */, int specialType/* = NORMAL_WORLD*/)
{
	auto *bones = &m_vFcbd;
	if (rootdata)
	{
		bones = &rootdata->vChildFcbds;
	}

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	for (int i = 0; i < (int)bones->size(); i++)
	{
		auto *data = (*bones)[i];

		if (!data->model.empty())
		{
			char srcPath[256];
			char destPath[256];
			snprintf(srcPath, sizeof(srcPath)-1, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, data->model.c_str());
			snprintf(destPath, sizeof(destPath)-1, "data/custommodel/%s.cm", data->model.c_str());

			if (!GetFileManager().IsFileExistWritePath(destPath))
			{
				GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

				if (CustomModelMgr::GetInstancePtr() && !isdownloadfcm)
					CustomModelMgr::GetInstancePtr()->loadOneResCustomModel(RES_MODEL_CLASS, data->model);
			}
				
		}

		moveMapAvatarModelToRes(owid, data, isdownloadfcm, specialType);
	}
}

bool FullyCustomModel::moveSubModelRes(int destlibtype, long long owid, FullyCustomBoneData *rootdata /* = NULL */, bool ignorecheck /* = false */)
{
	if (!CustomModelMgr::GetInstancePtr())
		return false;

	auto *bones = &m_vFcbd;
	if (rootdata)
	{
		bones = &rootdata->vChildFcbds;
	}

	for (int i = 0; i < (int)bones->size(); i++)
	{
		auto *data = (*bones)[i];
		if (data->model.empty())   //没有子模型
		{
			if (moveSubModelRes(destlibtype, owid, data, ignorecheck))
				continue;
			else
				return false;
		}

		auto customModel = CustomModelMgr::GetInstancePtr()->getCustomModel(destlibtype, data->model);
		if (customModel)  //子模型已经在目标库存在了
		{
			if (moveSubModelRes(destlibtype, owid, data, ignorecheck))
				continue;
			else
				return false;
		}

		char srcPath[256] = { 0 };
		char destPath[256] = { 0 };
		if (destlibtype == MAP_LIB)
		{
			snprintf(srcPath, sizeof(srcPath)-1, "data/custommodel/%s.cm", data->model.c_str());
			snprintf(destPath, sizeof(destPath)-1, "data/w%lld/custommodel/%s.cm", owid, data->model.c_str());
		}
		else if (destlibtype == PUBLIC_LIB)
		{
			snprintf(srcPath, sizeof(srcPath)-1, "data/w%lld/custommodel/%s.cm", owid, data->model.c_str());
			snprintf(destPath, sizeof(destPath)-1, "data/custommodel/%s.cm", data->model.c_str());
		}


		if (!GetFileManager().IsFileExistWritePath(destPath))
		{
			int id = -1;
			if (destlibtype == MAP_LIB && m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL) //微缩组合移动到地图库时要给子微缩分配方块id
			{
				id = CustomModelMgr::GetInstancePtr()->allocationDefId(BLOCK_MODEL);
				if (id < 0)
					return false;
			}

			GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);
			auto *cm = CustomModelMgr::GetInstancePtr()->loadOneCustomModelNew(destlibtype, data->model, ignorecheck);
			if (cm)
			{
				if (destlibtype == MAP_LIB && m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
					CustomModelMgr::GetInstancePtr()->addDefToMap(id, BLOCK_MODEL, cm);
			}
			else
			{
				LOG_INFO("FullyCustomModel::moveSubModelRes load to %d failed name:%s", destlibtype, data->model.c_str());
			}
		}

		if (!moveSubModelRes(destlibtype, owid, data, ignorecheck))
			return false;
	}

	return true;
}



void FullyCustomModel::checkEditUseCMIsDownloaded(FullyCustomBoneData *rootdata /* = NULL */)
{
	auto *bones = &m_vFcbd;
	if (rootdata)
	{
		bones = &rootdata->vChildFcbds;
	}

	for (int i = 0; i < (int)bones->size(); i++)
	{
		if (!(*bones)[i]->model.empty() && CustomModelMgr::GetInstancePtr()->isDownloadCM((*bones)[i]->model))
		{
			(*bones)[i]->isdownload = true;
			m_iUseDownloadCMNum++;
		}

		checkEditUseCMIsDownloaded((*bones)[i]);
	}
}

int FullyCustomModel::getOneCMPlaceDir(std::string bonename)
{
	auto iter = m_PackingCMForwardDirs.find(bonename);
	if (iter != m_PackingCMForwardDirs.end())
		return iter->second;

	return -1;
}


void FullyCustomModel::oneBoneDataConvertToMotion(FullyCustomBoneData *bonedata, std::map<int, CustomMotion *> &custommotions, int motionid, int &idx)
{
	if (CustomMotionMgr::GetInstancePtr()->isCusMotionBone(bonedata->name))
	{
		for (size_t i = 0; i < bonedata->vCmds.size(); i++)
		{
			auto& cmd = bonedata->vCmds[i];
			if (-1 != motionid && cmd.id != motionid)
				continue;

			auto motionIter = custommotions.find(cmd.id);
			if (motionIter != custommotions.end())
			{
				for (size_t j = 0; j < cmd.ticks.size(); j++)
				{
					motionIter->second->insertCustomKeyFrame(bonedata->name, cmd.ticks[j], cmd.posoffsets[j], cmd.quats[j], cmd.scale3s[j]);
				}
			}
			else
			{
				CustomMotion *pMotion = ENG_NEW(CustomMotion)();
				for (size_t j = 0; j < cmd.ticks.size(); j++)
				{
					pMotion->insertCustomKeyFrame(bonedata->name, cmd.ticks[j], cmd.posoffsets[j], cmd.quats[j], cmd.scale3s[j]);
				}
				if (GetIPlayerControl())
				{
					char skey[64] = { 0 };
#if defined(_WIN32)
					sprintf(skey, "%d%d%I64d", GetIPlayerControl()->GetIUin(), idx, time(NULL));
#else
					sprintf(skey, "%d%d%ld", GetIPlayerControl()->GetIUin(), idx, time(NULL));
#endif
					idx++;
					pMotion->setMotionInfo(skey, GetIPlayerControl()->GetIUin(), GetIPlayerControl()->GetPlayerControlNickname(), cmd.time, cmd.id);
				}

				custommotions[cmd.id] = pMotion;
			}
		}
	}

	for (size_t j = 0; j < bonedata->vChildFcbds.size(); j++)
	{
		oneBoneDataConvertToMotion(bonedata->vChildFcbds[j], custommotions, motionid, idx);
	}
}

#ifdef UGC_PROFILE_ENABLED
size_t FullyCustomModel::getMemStatForUGC()
{
	size_t resSize = 0;

	resSize += (m_sName.size() + m_sAuthName.size() + m_sDesc.size() + m_sKey.size() + m_sFileName.size()) * sizeof(char);
	resSize += 4 * sizeof(int) + 3 * sizeof(bool) + sizeof(WCoord) + sizeof(CollideAABB);
	resSize += 3 * sizeof(bool);
	for (std::map<std::string, short>::iterator iter = m_PackingCMForwardDirs.begin();
		iter != m_PackingCMForwardDirs.end(); iter++)
	{
		resSize += iter->first.size() * sizeof(char) + sizeof(short);
	}

	resSize += getMemStatBoneData(m_vFcbd);

	return resSize;
}

size_t FullyCustomModel::getMemStatBoneData(const std::vector<FullyCustomBoneData*>& boneDatas)
{
	size_t resSize = 0;

	for (size_t i = 0; i < boneDatas.size(); i++)
	{
		if (!boneDatas[i])
		{
			continue;
		}

		size_t strSize = boneDatas[i]->name.size() + boneDatas[i]->fathername.size() + boneDatas[i]->model.size();
		resSize += strSize * sizeof(char);
		resSize += 2 * sizeof(Vector3f) + 2 * sizeof(Quaternionf) + 2 * sizeof(float) + 3 * sizeof(bool);

		for (size_t mi = 0; mi < boneDatas[i]->vCmds.size(); mi++)
		{
			CustomMotionData& moData = boneDatas[i]->vCmds[mi];

			resSize += sizeof(int) + sizeof(short);
			resSize += moData.ticks.size() * sizeof(unsigned int);
			resSize += moData.posoffsets.size() * sizeof(Vector3f);
			resSize += moData.quats.size() * sizeof(Quaternionf);
			resSize += moData.scales.size() * sizeof(float);
		}

		if (boneDatas[i]->vChildFcbds.size() > 0)
		{
			resSize += getMemStatBoneData(boneDatas[i]->vChildFcbds);
		}
	}

	return resSize;
}
#endif // UGC_PROFILE_ENABLED

void FullyCustomModel::packingFcmConvertFcm()
{
	if (m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		m_eFcmSaveType = CLOSE_EDIT_FCM_UI_TYPE::ONLY_SAVE;
		auto *boneData = getBoneDataByIndex(0);
		if (!boneData)
			return;

		for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
		{
			auto &childBone = boneData->vChildFcbds[i];
			auto *pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, childBone->model);
			if (!pCm)
				pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, childBone->model);

			if(!pCm)
				continue;

			auto offsetPos = pCm->getBlockCenterDrawOffsetPos();

			childBone->quat = Rainbow::XYZAngleToQuat(0, -90, 0);
			childBone->submodelpos = Rainbow::Vector3f(offsetPos.z - 50, offsetPos.y, offsetPos.x - 50);
		}
	}

	m_sKey = "";
}

void FullyCustomModel::getClientMissSubCMNum(int &num, FullyCustomBoneData *rootdata /* = NULL */)
{
	num = 0;
	auto* bones = &m_vFcbd;
	if (rootdata)
	{
		bones = &rootdata->vChildFcbds;
	}

	for (int i = 0; i < (int)bones->size(); i++)
	{
		if (!(*bones)[i]->model.empty() && !CustomModelMgr::GetInstancePtr()->clientHasAvatarModel((*bones)[i]->model))
			num++;

		getClientMissSubCMNum(num, (*bones)[i]);
	}
}

Rainbow::Vector3f FullyCustomModel::getSubCMRealMaxPos(std::string skey, std::string bonename)
{
	auto* pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, skey);
	if (!pCm)
		pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, skey);
	if (!pCm)
		return Rainbow::Vector3f(-1, -1, -1);

	int placeDir = getOneCMPlaceDir(bonename);

	auto tempVec = pCm->m_MaxPos;
	if (placeDir == DIR_NEG_X)
	{
		tempVec.x = BLOCK_MODEL_SIZE - 1 - pCm->m_MinPos.z;
		tempVec.z = pCm->m_MaxPos.x;
	}
	else if (placeDir == DIR_POS_Z)
	{
		tempVec.x = BLOCK_MODEL_SIZE - 1 - pCm->m_MinPos.x;
		tempVec.z = BLOCK_MODEL_SIZE - 1 - pCm->m_MinPos.z;
	}
	else if (placeDir == DIR_POS_X)
	{
		tempVec.x = pCm->m_MaxPos.z;
		tempVec.z = BLOCK_MODEL_SIZE - 1 - pCm->m_MinPos.x;
	}

	return tempVec;
}

Rainbow::Vector3f FullyCustomModel::getSubCMRealMinPos(std::string skey, std::string bonename)
{
	auto* pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, skey);
	if (!pCm)
		pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, skey);
	if (!pCm)
		return Rainbow::Vector3f(-1, -1, -1);

	auto tempVec = pCm->m_MinPos;
	int placeDir = getOneCMPlaceDir(bonename);
	if (placeDir == DIR_NEG_X)
	{
		tempVec.x = BLOCK_MODEL_SIZE - 1 - pCm->m_MaxPos.z;
		tempVec.z = pCm->m_MinPos.x;
	}
	else if (placeDir == DIR_POS_Z)
	{
		tempVec.x = BLOCK_MODEL_SIZE - 1 - pCm->m_MaxPos.x;
		tempVec.z = BLOCK_MODEL_SIZE - 1 - pCm->m_MaxPos.z;
	}
	else if (placeDir == DIR_POS_X)
	{
		tempVec.x = pCm->m_MinPos.z;
		tempVec.z = BLOCK_MODEL_SIZE - 1 - pCm->m_MaxPos.x;
	}

	return tempVec;
}

extern int convertLittleEndian4byte(int in);

int FullyCustomModel::convert2SmallRoutine(int type)
{
	if (m_eFcmSaveType != FULLY_PACKING_CUSTOM_MODEL)
	{
		return 5;
	}

	long long owid = -1;
	if (type == MAP_MODEL_CLASS)
	{
		if (!GetWorldManagerPtr())
			return 1;

		owid = GetWorldManagerPtr()->getWorldId();
	}

	char path[256];
	if (owid < 0)
		sprintf(path, "data/custommodel/%s.txt", m_sKey.c_str());
	else
	{
		std::string rootpath = GetWorldRootBySpecialType(GetWorldManagerPtr()->getSpecialType());

		sprintf(path, "%s/w%lld/custommodel/%s.txt", rootpath.c_str(), owid, m_sKey.c_str());
	}

	core::string fullpath;
	GetFileManager().ToWritePathFull(path, fullpath);
	
	FileAutoClose fp(fullpath, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
	if (fp.isNull())
		return 2;

	std::vector<char> modelDatas;
	modelDatas.clear();

	short colorIndex = 0;
	std::map<unsigned int, char> colorInfos;
	colorInfos.clear();
	std::vector<unsigned int> convertColors;
	convertColors.clear();

	auto *boneData = getBoneDataByIndex(0);
	if (!boneData)
		return 6;

	for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
	{
		auto &childBone = boneData->vChildFcbds[i];
		auto *pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(type, childBone->model);
		if (!pCm)
			continue;

		WCoord offset(-49, 1, -49);   //100*100*100大小的小程序资源的起点是(-9, 1, 9);
		WCoord relativePos = WCoord(childBone->offsetpos.x / BLOCK_SIZE, childBone->offsetpos.y / BLOCK_SIZE, childBone->offsetpos.z / BLOCK_SIZE);
		offset += relativePos * BLOCK_MODEL_SIZE;

		int cmDir = getOneCMPlaceDir(childBone->name);
		pCm->getModelDataBySmallRoutineFormat(modelDatas, colorInfos, convertColors, offset, cmDir);
	}

	int dataLen = convertLittleEndian4byte((int)modelDatas.size());

	if (!fp.write(&dataLen, sizeof(unsigned int)))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	int seekOffset = sizeof(unsigned int);
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	jsonxx::Array textContent;
	auto iter = colorInfos.begin();
	for (size_t i = 0; i < convertColors.size(); i++)
	{
		textContent << convertColors[i];
	}

	jsonxx::Object resObj;
	resObj << "colorArry" << textContent;
	resObj << "fromminiworld" << 1;
	resObj << "area" << 100;
	resObj << "mtype" << "3D";

	std::string jsonStr = resObj.json();
	//regex pattern(" |\n|\r|\t");
	//std::string fmt = "";

	//jsonStr = std::regex_replace(jsonStr, pattern, fmt);
	unsigned short jsonLen = static_cast<unsigned short>(jsonStr.length());
	unsigned short tempJsonLen = jsonLen;
	jsonLen = ((jsonLen & 0xff00) >> 8) | ((jsonLen & 0x00ff) << 8);

	if (!fp.write(&jsonLen, sizeof(unsigned short)))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	seekOffset += sizeof(unsigned short);
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	if (!fp.write(jsonStr.c_str(), tempJsonLen))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	seekOffset += tempJsonLen;
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	for (size_t i = 0; i < modelDatas.size(); i++)
	{
		if (!fp.write(&modelDatas[i], 1))
		{
			fp.sync();
			fp.close();
			return 3;
		}

		seekOffset += 1;
		if (!fp.seek(seekOffset, SEEK_SET))
		{
			fp.sync();
			fp.close();
			return 3;
		}
	}

	fp.sync();
	fp.close();

	return 0;
}
