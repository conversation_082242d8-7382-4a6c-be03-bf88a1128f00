# PB_SYNC_MOVE_CH 移动同步协议分析文档

## 概述

`PB_SYNC_MOVE_CH` 是客户端发送给服务器端的移动同步协议，用于实现多人游戏中玩家移动状态的实时同步。

- **客户端发送位置**: `MpPlayerControl.cpp` 中的 `void MpPlayerControl::syncMove2Host()`
- **服务端处理位置**: `MpGameSurviveHostHandler.cpp` 中的 `void MpGameSurviveNetHandler::handleSyncMove2Host(int uin, const PB_PACKDATA& pkg)`

## 客户端-服务器端完整时序图

```mermaid
sequenceDiagram
    participant Game as 游戏主循环
    participant MPC as MpPlayerControl
    participant MC as MoveControl
    participant Net as GameNetManager
    participant Host as 服务器网络层
    participant Handler as MpGameSurviveNetHandler
    participant Player as ClientPlayer
    participant Cheat as CheatHandler

    Game->>MPC: 每帧调用 ClientActor::tick()->afterMoveTick()
    MPC->>MPC: 检查 isNewMoveSyncSwitchOn()
    MPC->>MPC: 检查 m_pWorld->isRemoteMode()
    
    alt 条件满足
        MPC->>MPC: 调用 syncMove2Host()
        MPC->>MC: 检查 statusChanged() (按键变化)
        
        alt 按键状态变化
            MC-->>MPC: 返回 true
            MPC->>MPC: 设置 move_opera (yaw/pitch/opera)
            MPC->>MPC: need_sync = true, bits |= 1
        end
        
        MPC->>MC: 检查 getFlagChange() (状态变化)
        alt 移动标志变化
            MC-->>MPC: 返回变化列表
            MPC->>MPC: 添加 flag_change (飞行/潜行等)
            MPC->>MPC: need_sync = true, bits |= 4
        end
        
        MPC->>MPC: 检查定时同步条件
        alt 按键状态下定时同步
            MPC->>MPC: need_sync = true, bits |= 16
        end
        
        MPC->>MC: 检查 isNeedSync() (强制同步)
        alt 需要强制同步
            MC-->>MPC: 返回 true
            MPC->>MPC: need_sync = true, bits |= 32
        end
        
        MPC->>MPC: 检查位置变化
        alt 位置需要同步
            MPC->>MPC: 设置 pos (x/y/z)
            MPC->>MPC: need_sync = true, bits |= 2
        end
        
        alt need_sync == true
            MPC->>Net: sendToHost(PB_SYNC_MOVE_CH, pbMoveSync)
            Net->>Host: 发送协议数据
            
            Note over Host,Handler: 服务器端处理开始
            Host->>Handler: 路由到 handleSyncMove2Host(uin, pkg)
            Handler->>Handler: 通过 uin2Player(uin) 查找玩家
            
            alt 玩家不存在
                Handler-->>Host: 直接返回
            else 玩家存在
                Handler->>Handler: 解析 PB_MoveSyncCH 协议
                Handler->>Handler: 获取 id = pbCH.id()
                
                Note over Handler,Player: 处理移动标志变化
                loop 遍历 flag_change_size()
                    Handler->>Player: changeMoveFlag(type, on)
                    Player->>Player: 应用状态变化
                end
                
                Note over Handler,Cheat: 防作弊检测
                Handler->>Handler: 获取 tick = pbCH.tick()
                Handler->>Player: 获取 CheatHandler
                Player-->>Handler: 返回 CheatHandler
                Handler->>Cheat: checkClientTick(tick)
                Cheat-->>Handler: 返回 tick_valid
                
                Note over Handler,Player: 位置信息处理
                alt 包含位置信息 pbCH.has_pos()
                    Handler->>Handler: 转换位置 MPVEC2WCoord(pbCH.pos())
                    alt tick_valid == true
                        Handler->>Player: setCheckMoveResult(id, pos, tick)
                    else tick_valid == false
                        Handler->>Player: setCheckMoveResult(id, pos, 0)
                    end
                    Player->>Player: 设置位置检查结果
                end
                
                Note over Handler,Player: 移动操作处理
                alt 包含移动操作 pbCH.has_move_opera()
                    Handler->>Handler: 提取 move_info = pbCH.move_opera()
                    Handler->>Handler: 获取 opera/yaw/pitch
                    alt tick_valid == true
                        Handler->>Player: setMoveControl(opera, yaw/1000.0f, pitch/1000.0f, tick)
                    else tick_valid == false
                        Handler->>Player: setMoveControl(opera, yaw/1000.0f, pitch/1000.0f, 0)
                    end
                    Player->>Player: 设置移动控制状态
                end
                
                Handler-->>Host: 处理完成
            end
            
            Note over Host,Handler: 服务器端处理结束
        end
    end
```

## 服务端处理流程图

```mermaid
flowchart TD
    A[接收 PB_SYNC_MOVE_CH 协议] --> B[通过UIN查找玩家对象]
    B --> C{玩家对象是否存在?}
    C -->|否| D[直接返回]
    C -->|是| E[解析协议数据 PB_MoveSyncCH]
    
    E --> F[处理移动标志变化]
    F --> G[遍历 flag_change 列表]
    G --> H[调用 player->changeMoveFlag]
    
    H --> I[获取客户端时间戳]
    I --> J[防作弊检测 checkClientTick]
    J --> K{时间戳是否有效?}
    
    K -->|是| L[tick_valid = true]
    K -->|否| M[tick_valid = false]
    
    L --> N{是否包含位置信息?}
    M --> N
    N -->|是| O[设置位置检查结果]
    O --> P[调用 setCheckMoveResult 带有效时间戳]
    N -->|否| Q{是否包含移动操作?}
    
    P --> Q
    Q -->|是| R[提取移动操作信息]
    R --> S[获取 opera/yaw/pitch]
    S --> T[调用 setMoveControl]
    T --> U[处理完成]
    Q -->|否| U
    
    style A fill:#e1f5fe
    style U fill:#c8e6c9
    style D fill:#ffcdd2
    style J fill:#fff3e0
    style K fill:#fff3e0
```

## 客户端发送条件详细分析

### 1. 主要调用入口

**文件位置**: `MpPlayerControl.cpp:2305-2312`

```cpp
void MpPlayerControl::afterMoveTick()
{
    if (m_MoveControl->isMotionUpEnd())
        m_MoveControl->addEvent(IMT_UpEnd);
    if (isNewMoveSyncSwitchOn() && m_pWorld && m_pWorld->isRemoteMode())
        syncMove2Host();
    PlayerControl::afterMoveTick();
}
```

**发送前置条件**:
- `isNewMoveSyncSwitchOn()` 返回 true
- `m_pWorld->isRemoteMode()` 为 true (多人游戏客户端模式)

### 2. isNewMoveSyncSwitchOn() 条件

**文件位置**: `ClientPlayer_Base.cpp:1711`

```cpp
bool ClientPlayer::isNewMoveSyncSwitchOn()
{
    return hasUIControl() && m_MoveControl && m_MoveControl->isNewSyncSwitchOn();
}
```

**必须满足的三个条件**:
- `hasUIControl()` - 玩家拥有UI控制权
- `m_MoveControl` 对象存在
- `m_MoveControl->isNewSyncSwitchOn()` - 新同步开关开启

### 3. 具体同步触发情况

**文件位置**: `MpPlayerControl.cpp:513-582`

#### A. 按键状态变化 (bits |= 1)
```cpp
if (m_MoveControl->statusChanged())
{ // 按键变化, 前后左右移动跳跃等
    auto move_tick = pbMoveSync.mutable_move_opera();
    move_tick->set_yaw((int)(m_MoveControl->getYaw() * 1000));
    move_tick->set_pitch((int)(m_MoveControl->getPitch() * 1000));
    move_tick->set_opera(m_MoveControl->toBits());
    need_sync = true;
    bits |= 1;
}
```

**触发条件**: 前后左右移动、跳跃等按键状态发生变化

#### B. 移动标志变化 (bits |= 4)
```cpp
auto &flag_changed = m_MoveControl->getFlagChange();
if (!flag_changed.empty())
{ // 状态变化, 飞行/潜行/奔跑等
    for (auto it = flag_changed.begin(); it != flag_changed.end(); ++it)
    {
        auto change = pbMoveSync.add_flag_change();
        change->set_type(it->first);
        change->set_on(it->second);
    }
    need_sync = true;
    bits |= 4;
}
```

**触发条件**: 飞行、潜行、奔跑等状态标志发生变化

#### C. 定时同步 (bits |= 16)
```cpp
bool tick_match = tick % m_MoveControl->clientGetMoveSyncInterval() == 0;
if (!m_MoveControl->getCurrentStatus().empty() && tick_match)
{ // 按键状态下, 定时同步
    need_sync = true;
    bits |= 16;
}
```

**触发条件**: 在有按键状态的情况下，按照指定间隔进行定时同步

#### D. 强制同步 (bits |= 32)
```cpp
if (m_MoveControl->isNeedSync())
{
    need_sync = true;
    bits |= 32;
    m_MoveControl->clearNeedSync();
}
```

**触发条件**: 移动控制器标记需要强制同步

#### E. 位置变化同步 (bits |= 2)
```cpp
if (!not_sync_pos)
{
    const auto& pos = m_MoveControl->getPrePosition();
    if (need_sync || (tick_match && m_MoveControl->positionChanged(pos)))
    {
        auto pb_pos = pbMoveSync.mutable_pos();
        pb_pos->set_x(pos.x);
        pb_pos->set_y(pos.y);
        pb_pos->set_z(pos.z);
        need_sync = true;
        bits |= 2;
    }
}
```

**触发条件**: 玩家位置发生变化
**特殊限制**: 当玩家在载具上或有服务器插值组件时，不同步位置

## 服务器端处理流程详细分析

### handleSyncMove2Host() 处理逻辑

**文件位置**: `MpGameSurviveHostHandler.cpp:7424-7456`

```cpp
void MpGameSurviveNetHandler::handleSyncMove2Host(int uin, const PB_PACKDATA& pkg)
{
    // 1. 玩家验证
    ClientPlayer* player = uin2Player(uin);
    if (player == NULL)
        return;

    // 2. 协议解析
    game::ch::PB_MoveSyncCH pbCH;
    pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
    
    unsigned id = pbCH.id();

    // 3. 处理移动标志变化
    for (int i = 0; i < pbCH.flag_change_size(); ++i)
    {
        auto &flag_change = pbCH.flag_change(i);
        player->changeMoveFlag(flag_change.type(), flag_change.on());
    }

    // 4. 防作弊检测
    unsigned long long tick = pbCH.tick();
    bool tick_valid = player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(tick);
    
    // 5. 位置信息处理
    if (pbCH.has_pos())
    {
        if (tick_valid)
            player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), tick);
        else
            player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), 0);
    }

    // 6. 移动操作处理
    if (pbCH.has_move_opera())
    {
        auto move_info = pbCH.move_opera();
        player->setMoveControl(move_info.opera(), move_info.yaw() / 1000.0f, move_info.pitch() / 1000.0f, tick_valid ? tick: 0);
    }
}
```

### 处理步骤说明

1. **玩家验证**: 通过UIN查找对应的玩家对象，如果不存在则直接返回
2. **协议解析**: 将接收到的二进制数据解析为 `PB_MoveSyncCH` 结构
3. **标志处理**: 遍历所有移动标志变化，调用 `changeMoveFlag` 应用状态
4. **防作弊检测**: 通过 `checkClientTick` 验证客户端时间戳的有效性
5. **位置同步**: 如果包含位置信息，根据时间戳有效性设置位置检查结果
6. **操作同步**: 如果包含移动操作，设置玩家的移动控制状态和视角

### 防作弊机制

- **时间戳验证**: 通过 `player->GetCheatHandler()->checkClientTick(tick)` 验证时间戳
- **失效处理**: 当时间戳无效时，将tick设为0，触发相应的防作弊逻辑
- **位置校验**: 服务器会对位置变化进行合理性检查

## 协议数据结构

### PB_MoveSyncCH 主要字段

```protobuf
message PB_MoveSyncCH {
    optional uint32 id = 1;                    // 移动ID
    optional uint64 tick = 2;                  // 客户端时间戳
    optional PB_Vector3 pos = 3;               // 位置信息
    optional PB_MoveOpera move_opera = 4;      // 移动操作
    repeated PB_FlagChange flag_change = 5;    // 状态变化列表
}

message PB_MoveOpera {
    optional int32 yaw = 1;                    // 水平视角 (乘以1000)
    optional int32 pitch = 2;                 // 垂直视角 (乘以1000)  
    optional uint32 opera = 3;                // 操作位标志
}

message PB_FlagChange {
    optional uint32 type = 1;                 // 标志类型
    optional bool on = 2;                     // 开关状态
}
```

## 性能考虑

### 客户端优化
- 只在状态真正变化时才发送协议
- 使用位标志压缩操作信息
- 定时同步避免频繁发送

### 服务端优化  
- 快速的玩家查找机制
- 高效的协议解析
- 防作弊检测不影响正常流程

## 注意事项

1. **新旧协议兼容**: 主机同时支持新老移动同步协议
2. **载具状态**: 玩家在载具上时位置同步逻辑不同
3. **权限控制**: 需要检查玩家是否有UI控制权
4. **防作弊**: 服务器会验证所有移动相关的数据合理性

## 相关文件清单

### 客户端文件
- `SandboxGame/Play/gameplay/mpgameplay/MpPlayerControl.cpp` - 主要同步逻辑
- `SandboxGame/Play/player/ClientPlayer_Base.cpp` - 开关判断逻辑
- `SandboxGame/Play/player/ClientPlayer.h` - 接口定义

### 服务端文件  
- `MiniGame/iworld/gameStage/net_handler/MpGameSurviveHostHandler.cpp` - 协议处理

### 相关组件
- `MoveControl` - 移动控制组件
- `GameNetManager` - 网络管理器
- `CheatHandler` - 防作弊处理器

## 总结

PB_SYNC_MOVE_CH协议是游戏中移动同步的核心机制，通过精确的条件判断和高效的数据传输，确保多人游戏中玩家移动状态的实时同步。同时具备完善的防作弊检测机制，保证游戏的公平性和稳定性。 