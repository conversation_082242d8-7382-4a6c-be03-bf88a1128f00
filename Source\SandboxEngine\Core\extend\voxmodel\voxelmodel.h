﻿
#ifndef __VOXELMODEL_H__
#define __VOXELMODEL_H__

#include "world_types.h"
#include "OgreBlock.h"
#include "SandboxEngine.h"
class WorldProxy;

class EXPORT_SANDBOXENGINE VoxelModel;
class VoxelModel //tolua_exports
{ //tolua_exports
public:
    //tolua_begin
    VoxelModel();
    ~VoxelModel();

    WCoord getDim()
    {
        return WCoord(m_DimX, m_DimY, m_DimZ);
    }
    bool loadVoxelFile(const char* path);
    bool loadVoxelFile(const char *path, int blockid);
    bool loadVMOFile(const char *path);
    //bool loadVMOFileDir(const char* path, int dir);
    bool saveVMOFile(const char *path);

    void placeInWorld(WorldProxy *pworld, const WCoord &pos, bool completely, int placedir);
    void placeInWorld(World *pworld, const WCoord &pos, bool completely, int placedir);
    void removeInWorld(WorldProxy *pworld, const WCoord &pos, int placedir);
    void removeInWorld(World *pworld, const WCoord &pos, int placedir);
    void captureFromWorld(World *pworld, const WCoord &begin, const WCoord &end);


    int vox_xyz2Index(int x, int y, int z)const
    {
        return (y*m_DimZ + z)*m_DimX + x;
    };

    BLOCK_DATA_TYPE getData(int x,int y,int z)const
    {
        return m_VoxelData[vox_xyz2Index(x,y,z)];
    };

    WCoord vox_index2xyz(int index)const
    {
        WCoord ret;
        ret.x = index%m_DimX;
        int yz = (index - ret.x)/m_DimX;
        ret.z = yz%m_DimZ;
        ret.y = (yz - ret.z)/m_DimZ;
        return ret;
    };

    void* getContainersData(){
        return m_containersData;
    }

    bool captureFromWorldAndSaveV2(World *pworld, const WCoord &begin, const WCoord &end,const char *path);
    //tolua_end
    std::vector<BlockCoord>& getVoxelDataToBlock();
	static int loadVox(const char *path, int blockid, std::vector<VoxelModel*>& vModel);
private:
    std::vector<BlockCoord> m_Blocks;
	BLOCK_DATA_TYPE* m_VoxelData;
    void *m_containersData;
    int m_DimX;
    int m_DimY;
    int m_DimZ;
    int m_Dir;
}; //tolua_exports

#endif
