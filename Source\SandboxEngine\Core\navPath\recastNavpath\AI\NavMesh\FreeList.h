#pragma once

#include "Utilities/Logs/LogAssert.h"

#define FREELIST_EXPENSIVE_TEST 0

// Free list using index as next pointer.
// Note: Only calls constructor when new items are allocated from heap,
// and detructor is called only whwn the whole freelist is deleted.
// This is done intentionally so that we can retain some data (i.e. salt in off-mesh connection).
template<typename T>
class FreeList
{
public:
    enum { kNullLinkId = 0xffffffff };

    FreeList()
        : m_NextFree(kNullLinkId)
        , m_Capacity(0)
        , m_Data(0)
    {
    }

    ~FreeList()
    {
        for (int i = 0; i < m_Capacity; i++)
            m_Data[i].~T();
        ENG_DELETE_LABEL(m_Data, kMemAI);
    }

    T& operator[](int i)
    {
        DebugAssertFormatMsg(i < m_Capacity, "FreeList index %u out of range %u", i, m_Capacity);
        return m_Data[i];
    }

    const T& operator[](int i) const
    {
        DebugAssertFormatMsg(i < m_Capacity, "FreeList index %u out of range %u", i, m_Capacity);
        return m_Data[i];
    }

    unsigned int Alloc()
    {
        if (m_NextFree == kNullLinkId)
        {
            Grow(m_Capacity ? 2 * m_Capacity : 4);
            if (m_NextFree == kNullLinkId)
                return kNullLinkId;
        }
        unsigned int id = m_NextFree;
        m_NextFree = m_Data[id].next;
        m_Data[id].next = 0;
        return id;
    }

    void Release(unsigned int id)
    {
        DebugAssertFormatMsg(id < m_Capacity, "FreeList release %u out of range %u", id, m_Capacity);

        m_Data[id].next = m_NextFree;
        m_NextFree = id;
    }

    inline unsigned int Capacity() const { return m_Capacity; }

    void Clear()
    {
        for (int i = 0; i < m_Capacity; i++)
            m_Data[i].~T();
        ENG_DELETE_LABEL(m_Data, kMemAI);

        m_Data = NULL;
        m_NextFree = kNullLinkId;
        m_Capacity = 0;
    }

private:

    void Grow(unsigned int s)
    {
        if (s <= m_Capacity || m_NextFree != kNullLinkId)
            return;
        T* data = reinterpret_cast<T*>(ENG_REALLOC_LABEL(m_Data, sizeof(T) * s, kMemAI));
        if (!data)
            return;
        m_Data = data;
        for (unsigned int i = m_Capacity; i < s - 1; ++i)
        {
            new(&m_Data[i]) T;
            m_Data[i].next = i + 1;
        }
        new(&m_Data[s - 1]) T;
        m_Data[s - 1].next = kNullLinkId;
        m_NextFree = m_Capacity;
        m_Capacity = s;
    }

    unsigned int m_NextFree;
    unsigned int m_Capacity;
    T* m_Data;
};
