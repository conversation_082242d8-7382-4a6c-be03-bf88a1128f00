#pragma once
/**
* file : SandboxAttribute
* func : 属性
* by : chenzh
*/
#include "SandboxType.h"
#include "SandboxRef.h"
#include "SandboxAutoRef.h"
#include "SandboxReflexVariant.h"
#include "SandboxNotify.h"
#include "SandboxReflexEnum.h"
#include <string>



namespace MNSandbox {

	class Attribute;
	class AttributeContainer;
	class SandboxNode;


	/**
	* 属性对象
	*/
	class EXPORT_SANDBOXDRIVERMODULE Attribute : public Ref
	{
	public:

		/* 支持的属性类型 */
		enum class TYPE : unsigned short
		{
			IDLE, // 空
			Number, // 数值
			Bool, // 布尔
			String, // 字符串
			Vector3, // 矢量
			Vector2, 
			Vector4, 
			Color,
			Rect,
			NumberSequence,
			ColorSequence,
			NodeLinker
		};

	public:
		Attribute(AttributeContainer* container, const std::string& name, TYPE attrType);

		void setName(const std::string&name);
		/* 获取属性类型 */
		TYPE GetAttrType() const { return m_attrType; }

		/* 获取属性名称 */
		const std::string& GetName() const { return m_name; }

		/* 获取数据 */
		template<typename TType>
		bool GetValue(TType& val);

		/* 设置数据 */
		template<typename TType>
		bool SetValue(const TType& val);

		/* 获取通用值 */
		const ReflexVariant& GetVariant() const { return m_value; }

		/* 通用值设置 */
		bool SetVariant(const ReflexVariant& val);

		/* 同步容器 */
		void SyncContainer(AttributeContainer* linker) { m_link = linker; }

	public:
		/* 读档用 */
		ReflexVariant& GetVariant() { return m_value; }
		static TYPE GetAttrTypeByRefexType(const ReflexType* reflexType);

	public:
		/* 通知，属性改变 */
		//Notify<std::string> m_notifyChanged;

	private:

		friend class AttributeContainer;

		/* 容器指针 */
		AttributeContainer* m_link = nullptr;

		/* 属性类型 */
		TYPE m_attrType = TYPE::IDLE;
		/* 名称 */
		std::string m_name;
		/* 值 */
		ReflexVariant m_value;
		static ReflexEnumDesc<TYPE> AttributeEnum;
	};

	/**
	* 属性容器
	*/
	class EXPORT_SANDBOXDRIVERMODULE AttributeContainer
	{
	public:
		AttributeContainer() = default;
		AttributeContainer(SandboxNode* owner) : m_owner(owner) {}
		AttributeContainer(const AttributeContainer& v)
			: m_pool(v.m_pool)
		{}
		bool ModifyAttrName(const std::string&oldName,const std::string&newName);
		/* 新建属性，需要指定名称和类型 */
		AutoRef<Attribute> NewAttribute(const std::string& name, Attribute::TYPE type, const ReflexVariant& val = ReflexVariant());

		/* 删除属性 */
		void DeleteAttribute(const std::string& name);

		/* 清除全部属性 */
		void ClearAllAttributes();

		/* 获取属性 */
		AutoRef<Attribute> GetAttribute(const std::string& name) const;

		/* 获取属性数量 */
		unsigned GetSize() const { return (unsigned)m_pool.size(); }

		/* 获取属性数据 */
		const std::map<std::string, AutoRef<Attribute>>& GetDatas() const { return m_pool; }

		/* 遍历 */
		void ForEach(std::function<void(const std::pair<std::string, AutoRef<Attribute>>)> callback) const;
		void ForEach(std::function<void(std::pair<std::string, AutoRef<Attribute>>)> callback);

		/* 插入属性，加载用 */
		void InsertAttribute(AutoRef<Attribute> attr);

		/* 拷贝 */
		AttributeContainer& operator=(const AttributeContainer& v)
		{
			m_pool = v.m_pool;
			return *this;
		}
		/* 比较 */
		bool operator==(const AttributeContainer& v) const
		{
			return m_pool == v.m_pool;
		}

		/* 通知属性变化 */
		void OnCustomAttrChanged(Attribute* attr);

	public:
		/* 通知，新增属性 */
		//Notify<std::string> m_notifyAttrAdd;
		/* 通知，移除属性 */
		//Notify<std::string> m_notifyAttrRemove;
		/* 通知，属性改变 */
		Notify<std::string> m_notifyAttrChanged;

	private:
		/* 所属 */
		SandboxNode* m_owner = nullptr;
		/* 属性 */
		std::map<std::string, AutoRef<Attribute>> m_pool;
	};

	////////////////////////////////////////////////////////////////////////////////////

	template<typename TType>
	bool Attribute::GetValue(TType& val)
	{
		if (!m_value.IsType<TType>())
			return false;

		return m_value.GetValue<TType>(val);
	}

	template<typename TType>
	bool Attribute::SetValue(const TType& val)
	{
		if (!m_value.IsType<TType>())
			return false;

		if (m_value.CompareValue(val))
			return true;

		m_value.SetData<TType>(val);

		// 通知
		//m_notifyChanged.Emit(m_name);
		if (m_link)
		{
			m_link->m_notifyAttrChanged.Emit(m_name);
		}
		return true;
	}

}