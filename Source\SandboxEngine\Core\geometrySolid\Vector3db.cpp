#include "geometrySolid/Vector3db.h"
#include "geometrySolid/Quaterniondb.h"
#include <limits>
//#include "Math/FloatConversion.h"

namespace MNSandbox {
	namespace GeometrySolid {

		//REFLECT_BEGIN(Vector3db)
		//	REFLECT_FIELD(x)
		//	REFLECT_FIELD(y)
		//	REFLECT_FIELD(z)
		//REFLECT_END()

		//void Vector3db::Reflect() {
		//	using namespace Reflect;

		//	ReflectType* classType = ReflectContext::Class<Vector3db>(); 

		//	classType->Field()
		//	/*using namespace rttr;
		//	
		//	registration::class_<Vector3db>("Vector3db")
		//   (
		//		metadata(EditorRttrType::kEditorTitle, NoFreeFixedString("Vector3db"))
		//   )
		//	.constructor<>()
		//	.property("x", &Vector3db::x)
		//	(
		//		metadata(EditorRttrType::kEditorTitle, NoFreeFixedString("X")) ,
		//		metadata(EditorRttrType::kEditorAttribute, EditorAttributeType::kEditorFloat)

		//	)
		//	.property("y", &Vector3db::y)
		//	(
		//		metadata(EditorRttrType::kEditorTitle, NoFreeFixedString("Y")) ,
		//		metadata(EditorRttrType::kEditorAttribute, EditorAttributeType::kEditorFloat)
		//	)
		//	.property("z", &Vector3db::z)
		//	(
		//		metadata(EditorRttrType::kEditorTitle, NoFreeFixedString("Z")) ,
		//		metadata(EditorRttrType::kEditorAttribute, EditorAttributeType::kEditorFloat)
		//	);*/
		//};

		//const Vector3db Vector3db::infinity = Vector3db(std::numeric_limits<double>::infinity(), std::numeric_limits<double>::infinity(), std::numeric_limits<double>::infinity());
		const Vector3db Vector3db::one(1.0, 1.0, 1.0);
		const Vector3db Vector3db::zero(0.0, 0.0, 0.0);
		const Vector3db Vector3db::xAxis(1.0, 0.0, 0.0);
		const Vector3db Vector3db::yAxis(0.0, 1.0, 0.0);
		const Vector3db Vector3db::zAxis(0.0, 0.0, 1.0);
		const Vector3db Vector3db::neg_xAxis(-1, 0, 0);
		const Vector3db Vector3db::neg_yAxis(0, -1, 0);
		const Vector3db Vector3db::neg_zAxis(0, 0, -1);

		//static Vector3db NormalizeRobustInternal(const Vector3db& a, double &l, double &div, double eps)
		//{
		//	double a0, a1, a2, aa0, aa1, aa2;
		//	a0 = a[0];
		//	a1 = a[1];
		//	a2 = a[2];

		//	#if FPFIXES
		//	if (CompareApproximately(a0, 0.0F, eps))
		//		a0 = aa0 = 0;
		//	else
		//		#endif
		//	{
		//		aa0 = Rainbow::Abs(a0);
		//	}

		//	#if FPFIXES
		//	if (CompareApproximately(a1, 0.0F, eps))
		//		a1 = aa1 = 0;
		//	else
		//		#endif
		//	{
		//		aa1 = Rainbow::Abs(a1);
		//	}

		//	#if FPFIXES
		//	if (CompareApproximately(a2, 0.0F, eps))
		//		a2 = aa2 = 0;
		//	else
		//		#endif
		//	{
		//		aa2 = Rainbow::Abs(a2);
		//	}

		//	if (aa1 > aa0)
		//	{
		//		if (aa2 > aa1)
		//		{
		//			a0 /= aa2;
		//			a1 /= aa2;
		//			l = InvSqrt(a0 * a0 + a1 * a1 + 1.0F);
		//			div = aa2;
		//			return Vector3db(a0 * l, a1 * l, CopySignf(l, a2));
		//		}
		//		else
		//		{
		//			// aa1 is largest
		//			a0 /= aa1;
		//			a2 /= aa1;
		//			l = InvSqrt(a0 * a0 + a2 * a2 + 1.0F);
		//			div = aa1;
		//			return Vector3db(a0 * l, CopySignf(l, a1), a2 * l);
		//		}
		//	}
		//	else
		//	{
		//		if (aa2 > aa0)
		//		{
		//			// aa2 is largest
		//			a0 /= aa2;
		//			a1 /= aa2;
		//			l = InvSqrt(a0 * a0 + a1 * a1 + 1.0F);
		//			div = aa2;
		//			return Vector3db(a0 * l, a1 * l, CopySignf(l, a2));
		//		}
		//		else
		//		{
		//			// aa0 is largest
		//			if (aa0 <= 0)
		//			{
		//				l = 0;
		//				div = 1;
		//				return Vector3db(0.0F, 1.0F, 0.0F);
		//			}

		//			a1 /= aa0;
		//			a2 /= aa0;
		//			l = InvSqrt(a1 * a1 + a2 * a2 + 1.0F);
		//			div = aa0;
		//			return Vector3db(CopySignf(l, a0), a1 * l, a2 * l);
		//		}
		//	}
		//}

		//Vector3db NormalizeRobust(const Vector3db& a)
		//{
		//	double l, div;
		//	return NormalizeRobustInternal(a, l, div, kEpsilon);
		//}

		//Vector3db NormalizeRobust(const Vector3db& a, double &invOriginalLength, double eps /*= kEpsilon*/)
		//{
		//	double l, div;
		//	const Vector3db &n = NormalizeRobustInternal(a, l, div, eps);
		//	invOriginalLength = l / div;
		//	// guard for NaNs
		//	Assert(n == n);
		//	Assert(invOriginalLength == invOriginalLength);
		//	Assert(n.IsNormalized());
		//	return n;
		//}

		//#define k1OverSqrt2 double(0.7071067811865475244008443621048490)
		//Vector3db OrthoNormalVectorFast(const Vector3db& n)
		//{
		//	Vector3db res;
		//	if (Abs(n.z) > k1OverSqrt2)
		//	{
		//		// choose p in y-z plane
		//		double a = n.y * n.y + n.z * n.z;
		//		double k = 1.0F / Sqrt(a);
		//		res.x = 0;
		//		res.y = -n.z * k;
		//		res.z = n.y * k;
		//	}
		//	else
		//	{
		//		// choose p in x-y plane
		//		double a = n.x * n.x + n.y * n.y;
		//		double k = 1.0F / Sqrt(a);
		//		res.x = -n.y * k;
		//		res.y = n.x * k;
		//		res.z = 0;
		//	}
		//	return res;
		//}

		//void OrthoNormalize(Vector3db* inU, Vector3db* inV)
		//{
		//	// compute u0
		//	double mag = Magnitude(*inU);
		//	if (mag > kEpsilon)
		//		*inU /= mag;
		//	else
		//		*inU = Vector3db(1.0F, 0.0F, 0.0F);

		//	// compute u1
		//	double dot0 = DotProduct(*inU, *inV);
		//	*inV -= dot0 * *inU;
		//	mag = Magnitude(*inV);
		//	if (mag < kEpsilon)
		//		*inV = OrthoNormalVectorFast(*inU);
		//	else
		//		*inV /= mag;
		//}

		//void OrthoNormalize(Vector3db* inU, Vector3db* inV, Vector3db* inW)
		//{
		//	// compute u0
		//	double mag = Magnitude(*inU);
		//	if (mag > kEpsilon)
		//		*inU /= mag;
		//	else
		//		*inU = Vector3db(1.0F, 0.0F, 0.0F);

		//	// compute u1
		//	double dot0 = DotProduct(*inU, *inV);
		//	*inV -= dot0 * *inU;
		//	mag = Magnitude(*inV);
		//	if (mag > kEpsilon)
		//		*inV /= mag;
		//	else
		//		*inV = OrthoNormalVectorFast(*inU);

		//	// compute u2
		//	double dot1 = DotProduct(*inV, *inW);
		//	dot0 = DotProduct(*inU, *inW);
		//	*inW -= dot0 * *inU + dot1 * *inV;
		//	mag = Magnitude(*inW);
		//	if (mag > kEpsilon)
		//		*inW /= mag;
		//	else
		//		*inW = CrossProduct(*inU, *inV);
		//}

		//Vector3db Slerp(const Vector3db &lhs, const Vector3db &rhs, double t)
		//{
		//	double lhsMag = Magnitude(lhs);
		//	double rhsMag = Magnitude(rhs);

		//	if (lhsMag < kEpsilon || rhsMag < kEpsilon)
		//		return Lerp(lhs, rhs, t);

		//	double lerpedMagnitude = Lerp(lhsMag, rhsMag, t);

		//	double dot = DotProduct(lhs, rhs) / (lhsMag * rhsMag);
		//	// direction is almost the same
		//	if (dot > 1.0F - kEpsilon)
		//	{
		//		return Lerp(lhs, rhs, t);
		//	}
		//	// directions are almost opposite
		//	else if (dot < -1.0F + kEpsilon)
		//	{
		//		Vector3db lhsNorm = lhs / lhsMag;
		//		Vector3db axis = OrthoNormalVectorFast(lhsNorm);
		//		Matrix3x3f m;
		//		m.SetAxisAngle(axis, kPI * t);
		//		Vector3db slerped = m.MultiplyPoint3(lhsNorm);
		//		slerped *= lerpedMagnitude;
		//		return slerped;
		//	}
		//	// normal case
		//	else
		//	{
		//		Vector3db axis = CrossProduct(lhs, rhs);
		//		Vector3db lhsNorm = lhs / lhsMag;
		//		axis = Normalize(axis);
		//		double angle = std::acos(dot) * t;

		//		Matrix3x3f m;
		//		m.SetAxisAngle(axis, angle);
		//		Vector3db slerped = m.MultiplyPoint3(lhsNorm);
		//		slerped *= lerpedMagnitude;
		//		return slerped;
		//	}
		//}

		Vector3db DirectionToEuler(const Vector3db& direction)
		{
			Vector3db result;
			// Find pitch.
			result.x = atan2(direction.x, sqrtf(direction.x * direction.x + direction.y * direction.y));
			// Find yaw.
			result.y = atan2(direction.y, direction.x);
			// Find roll.
			result.z = 0;

			return result;
		}
	}
}
