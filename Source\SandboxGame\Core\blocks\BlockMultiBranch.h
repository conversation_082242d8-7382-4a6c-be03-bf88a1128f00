#ifndef __BLOCKMULTIBRANCH_H__
#define __BLOCKMULTIBRANCH_H__

#include "BlockBranch.h"
#include "BlockArchitecturalBase.h"

class BlockMultiBranch : public BlockBranch, public BlockArchitecturalBase
{
    DECLARE_BLOCKMATERIAL(BlockMultiBranch)
public:
    BlockMultiBranch();
    virtual ~BlockMultiBranch() = default;
    virtual void init(int resid) override;
    virtual int getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
    virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player);
    virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f) override;
    virtual bool onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount) override;
    virtual bool onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player) override;
    virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
    virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
    virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho) override;
    virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
    virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
    virtual bool canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
    virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
    virtual int getBlockHP(World* pworld, const WCoord& blockpos);
    virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;
    virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
    virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_Architecture; }
    virtual bool hasSolidTopSurface(int blockdata) { return true; };
private:
    virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
    bool isCoreBlock(int blockdata) { return (blockdata & 8) == 0; } // 位3（值8）标识扩展方块
};

#endif // __BLOCKMULTIBRANCH_H__ 