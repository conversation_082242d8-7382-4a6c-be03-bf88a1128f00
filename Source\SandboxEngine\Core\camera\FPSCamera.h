#pragma once
#include "Input/OgreInputManager.h"
#include "CameraInfo.h"

class FPSCamera :public CameraBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	FPSCamera(CameraManager* cameraManager);
	~FPSCamera();

	void update(float deltaSeconds) override;
	int onInputEvent(const Rainbow::InputEvent &event) override;

	float m_MoveSpeed;
	float m_MoveStrafe;
	float m_MoveForward;
	//tolua_end
}; //tolua_exports