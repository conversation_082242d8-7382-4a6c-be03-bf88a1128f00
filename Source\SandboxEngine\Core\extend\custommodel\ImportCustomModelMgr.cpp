#include "ImportCustomModelMgr.h"
#include "CoreCommonDef.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "DefManagerProxy.h"
#include "MpGameSurviveCdnResMgr.h"
#include "RoomSyncResMgr.h"
#include <time.h>
#include "GlobalFunctions.h"
#include "ImportCustomModel_generated.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include "Entity/OgreModel.h"
#include "Entity/OgreModelData.h"
#include "AssetPipeline/AssetManager.h"
#include "ResourceCenter.h"
#include "OgreUtils.h"
#include "Graphics/GeneratedTextures.h"
#include "Pkgs/PkgUtils.h"
#include "Pkgs/PackageZipFile.h"
#include "Texture/TextureRenderGen.h"
#include "Graphics/GeneratedTextures.h"
#include "GameNetManager.h"
#include "Components/Camera.h"
#include "extend/custommodel/CustomModelMgr.h"
#include "WorldManager.h"
#include "ClientInfoProxy.h"
#include "OgreEntity.h"
#include "ImageMesh.h"
#include "PlayManagerInterface.h"


using namespace MINIW;
using namespace Rainbow;

const std::string IMPORT_CM_GENERAL_LIB_PATH = "data/custommodel/import/";
const std::string IMPORT_CM_SYNC_PATH = "data/http/entity/";
const std::string IMPORT_CM_PREVIEW_PATH = "data/custommodel_pre/import/";

const int BODYTEX_WIDTH = 200;
const int BODYTEX_HEIGHT = 200;

//TODO:m_TextureGen
ImportCustomModelMgr::ImportCustomModelMgr() :/* m_TextureGen(NULL),*/ m_nSpecialType(NORMAL_WORLD)
{
	m_CurOWID = 0;
	m_CurOWRealUin = -1;
	m_bLoadedRes = false;
	m_PreImportCustomModels.clear();
	m_TextureGen = ENG_NEW_LABEL(Rainbow::TextureRenderGen, kMemGame)();

	m_IconDescs.clear();
	m_MapIconDescs.clear();
}

ImportCustomModelMgr::~ImportCustomModelMgr()
{
	clearMapImportModels();
	clearResImportModels();
	clearPreImportModels();

	auto iterIcon = m_IconDescs.begin();
	for (; iterIcon != m_IconDescs.end(); iterIcon++)
	{
		ENG_DELETE(iterIcon->second);
	}
	m_IconDescs.clear();

	auto iterMapIcon = m_MapIconDescs.begin();
	for (; iterMapIcon != m_MapIconDescs.end(); iterMapIcon++)
	{
		ENG_DELETE(iterMapIcon->second);
	}
	m_MapIconDescs.clear();

	ENG_DELETE_LABEL(m_TextureGen, kMemGame);

	// auto modeldataIter = m_ModelDataCache.begin();
	// for (; modeldataIter != m_ModelDataCache.end(); modeldataIter++)
	// {
	// 	OGRE_RELEASE(modeldataIter->second);
	// }
	m_ModelDataCache.clear();

	// auto textureIter = m_TextureCache.begin();
	// for (; textureIter != m_TextureCache.end(); textureIter++)
	// {
	// 	OGRE_RELEASE(textureIter->second);
	// }
	m_TextureCache.clear();

	// auto modmodeldataIter = m_ModModelDataCache.begin();
	// for (; modmodeldataIter != m_ModModelDataCache.end(); modmodeldataIter++)
	// {
	// 	OGRE_RELEASE(modmodeldataIter->second);
	// }
	m_ModModelDataCache.clear();

	// auto modtextureIter = m_ModTextureCache.begin();
	// for (; modtextureIter != m_ModTextureCache.end(); modtextureIter++)
	// {
	// 	OGRE_RELEASE(modtextureIter->second);
	// }
	m_ModTextureCache.clear();
}

void ImportCustomModelMgr::loadMapImportCustomModelData(long long owid, int realowneruin, int specialType/* = NORMAL_WORLD*/)
{
	m_CurOWID = owid;
	m_nSpecialType = specialType;
	m_CurOWRealUin = realowneruin;
	clearMapImportModels();

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/import/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/import", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath +=  "/" + filename + ".icm";

		//È¥ÖØ£¬icmºÍzipÍ¬Ãû
		ImportCustomModel *importcustommodel = findImportCustomModel(MAP_MODEL_CLASS, filename);
		if (importcustommodel)
		{
			continue;
		}

		importcustommodel = ENG_NEW(ImportCustomModel)();
		if (importcustommodel->load(filepath, realowneruin))
		{
			m_MapImportCustomModels.push_back(importcustommodel);

			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(importcustommodel->getKey());
			if (customItem && customItem->itemid > 0)
			{
				GetDefManagerProxy()->addDefByCustomModel(customItem->itemid, customItem->type, filename, importcustommodel->getName(), importcustommodel->getDesc(), Rainbow::Vector3f(0, 0, 0), customItem->involvedid);
			}
		}
		else
			ENG_DELETE(importcustommodel);
	}
}
void ImportCustomModelMgr::preLoadMapImportCustomModelData(long long owid, int realowneruin)
{
	m_CurOWID = owid;
	m_CurOWRealUin = realowneruin;
	clearMapImportModels();
}
void ImportCustomModelMgr::loadOneMapImportCustomModelData(std::string& filename)
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/import", rootpath.c_str(), m_CurOWID);

	ImportCustomModel *importcustommodel = findImportCustomModel(MAP_MODEL_CLASS, filename);
	if (importcustommodel)
	{
		return;
	}
	std::string filepath = path;
	filepath += "/" + filename + ".icm";

	importcustommodel = ENG_NEW(ImportCustomModel)();
	if (importcustommodel->load(filepath, m_CurOWRealUin))
	{
		m_MapImportCustomModels.push_back(importcustommodel);

		auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(importcustommodel->getKey());
		if (customItem && customItem->itemid > 0)
		{
			GetDefManagerProxy()->addDefByCustomModel(customItem->itemid, customItem->type, filename, importcustommodel->getName(), importcustommodel->getDesc(), Rainbow::Vector3f(0, 0, 0), customItem->involvedid);
		}
	}
	else
		ENG_DELETE(importcustommodel);
}

void ImportCustomModelMgr::loadOneModImportCustomRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod/* =false */, bool ignorecheck/* = false */)
{
	if (findImportCustomModel(MAP_MODEL_CLASS, modelkey))
		return;

	char path[256];
	sprintf(path, "%s/resource/custommodel/import/%s.icm", modroot.c_str(), modelkey.c_str());

	auto *importcustommodel = ENG_NEW(ImportCustomModel)();
	if (importcustommodel->load(path, realowneruin))
	{
		if (islibmod)
			m_ResImportCustomModels.push_back(importcustommodel);
		else
			m_MapImportCustomModels.push_back(importcustommodel);
		LoadModImportModel(modelkey, modroot);
	}
	else
		ENG_DELETE(importcustommodel);
}

void ImportCustomModelMgr::leaveWorld()
{
	m_nSpecialType = NORMAL_WORLD;
	m_CurOWRealUin = -1;
	clearMapImportModels();

	// auto modmodeldataIter = m_ModModelDataCache.begin();
	// for (; modmodeldataIter != m_ModModelDataCache.end(); modmodeldataIter++)
	// {
	// 	OGRE_RELEASE(modmodeldataIter->second);
	// }
	m_ModModelDataCache.clear();

	// auto modtextureIter = m_ModTextureCache.begin();
	// for (; modtextureIter != m_ModTextureCache.end(); modtextureIter++)
	// {
	// 	OGRE_RELEASE(modtextureIter->second);
	// }
	m_ModTextureCache.clear();
	m_CurOWID = 0;
}

bool ImportCustomModelMgr::moveResImportCustomModelToMap(std::string filename)
{
	ImportCustomModel * importCustomModel = findImportCustomModel(MAP_MODEL_CLASS, filename);
	if (importCustomModel)
	{
		importCustomModel->setLeaveWorldDel(false);
		return true;
	}

	char srcDir[256] = {0};
	sprintf(srcDir, "data/custommodel/import/");
	std::string srcIcm = srcDir + filename + ".icm";
	std::string srcZip = srcDir + filename + ".zip";

	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char dstDir[256];
	sprintf(dstDir, "%s/w%lld/custommodel/import/", rootpath.c_str(), m_CurOWID);
	if (!GetFileManager().IsFileExistWritePath(dstDir))
	{
		GetFileManager().CreateWritePathDir(dstDir);
	}
	std::string dstIcm = dstDir + filename + ".icm";
	std::string dstZip = dstDir + filename + ".zip";
	if (GetFileManager().CopyWritePathFileToWritePath(srcIcm.c_str(), dstIcm.c_str()) &&
		GetFileManager().CopyWritePathFileToWritePath(srcZip.c_str(), dstZip.c_str()))
	{
		loadMapImportCustomModel(filename);
		syncImportModel(0,m_MapImportCustomModels.size()-1); //¹ã²¥¿Í»ú
		return true;
	}

	return false;
}

bool ImportCustomModelMgr::moveMapImportCustomModelToRes(std::string filename)
{
	ImportCustomModel * importCustomModel = findImportCustomModel(RES_MODEL_CLASS, filename);
	if (importCustomModel)
	{
		return false;
	}

	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char srcDir[256];
	sprintf(srcDir, "%s/w%lld/custommodel/import/", rootpath.c_str(), m_CurOWID);
	std::string srcIcm = srcDir + filename + ".icm";
	std::string srcZip = srcDir + filename + ".zip";

	char dstDir[256];
	sprintf(dstDir, "data/custommodel/import/");
	std::string dstIcm = dstDir + filename + ".icm";
	std::string dstZip = dstDir + filename + ".zip";
	if (GetFileManager().CopyWritePathFileToWritePath(srcIcm.c_str(), dstIcm.c_str()) &&
		GetFileManager().CopyWritePathFileToWritePath(srcZip.c_str(), dstZip.c_str()))
	{
		loadResImportCustomModel(filename);
		return true;
	}

	return false;
}

bool ImportCustomModelMgr::moveIcmRes(int destlibtype, std::string filename, ResourceFolderSetInfo *destclass)
{
	if (!CustomModelMgr::GetInstancePtr())
		return false;

	bool ret = false;
	if (destlibtype == MAP_LIB)
	{
		ImportCustomModel * icm = findImportCustomModel(PUBLIC_LIB, filename);
		if (!CustomModelMgr::GetInstancePtr()->getCustomItem(filename, true) && icm)   //µØÍ¼ÀïÓÐÕâ¸öµÀ¾ß
		{
			int type = icm->getModelType();
			if (type == -1) type = IMPORT_ACTOR_MODEL;

			int id = ImportCustomModelMgr::GetInstancePtr()->getFreeId(type);
			int involvedId = 0;
			if (type == IMPORT_ACTOR_MODEL)
				involvedId = ImportCustomModelMgr::GetInstancePtr()->getFreeId(IMPORT_ITEM_MODEL);

			if ((id <= 0 || (type == IMPORT_ACTOR_MODEL && involvedId <= 0)) && GetIPlayerControl())
			{
				//Ã»ÓÐ¿É·ÖÅäµÄidÁË
				GetIPlayerControl()->iNotifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
				return false;
			}
			else
			{
				CustomModelMgr::GetInstancePtr()->addCustomItemData(id, filename, destclass->classname, type, involvedId, destclass->classindex);
				GetDefManagerProxy()->addDefByCustomModel(id, type, filename, icm->getName(), icm->getDesc(), Rainbow::Vector3f(0, 0, 0), involvedId);
			}

			ret = moveResImportCustomModelToMap(filename);
		}
	}
	else if (destlibtype == PUBLIC_LIB)
	{
		ret = moveMapImportCustomModelToRes(filename);
	}

	return ret;
}

bool ImportCustomModelMgr::removeResByResourceCenter(int libtype, std::string filename)
{
	if (libtype == MAP_LIB)
	{
		std::vector<ImportCustomModel*>::iterator iter = m_MapImportCustomModels.begin();
		while (iter != m_MapImportCustomModels.end())
		{
			if ((*iter)->getKey() == filename)
			{
				(*iter)->setLeaveWorldDel(true);
				if (CustomModelMgr::GetInstancePtr())
					CustomModelMgr::GetInstancePtr()->addWaitDelCustomItem(filename);
				return true;
			}
			iter++;
		}
	}
	else if (libtype == PUBLIC_LIB)
	{
		char srcDir[128] = {0};
		sprintf(srcDir, "data/custommodel/import/");
		std::string srcIcm = srcDir + filename + ".icm";
		std::string srcZip = srcDir + filename + ".zip";
		if (GetFileManager().IsFileExistWritePath(srcIcm.c_str()))
		{
			GetFileManager().DeleteWritePathFileOrDir(srcIcm.c_str());
		}
		if (GetFileManager().IsFileExistWritePath(srcZip.c_str()))
		{
			GetFileManager().DeleteWritePathFileOrDir(srcZip.c_str());
		}

		std::vector<ImportCustomModel*>::iterator iter = m_ResImportCustomModels.begin();
		while (iter != m_ResImportCustomModels.end())
		{
			if ((*iter)->getKey() == filename)
			{
				ENG_DELETE(*iter);
				m_ResImportCustomModels.erase(iter);
				return true;
			}
			iter++;
		}

	}
	
	return false;
}

bool ImportCustomModelMgr::removeMapImportCustomModel(std::string filename)	//ÒÆ³ýµØÍ¼Ä£ÐÍ
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char srcDir[256];
	sprintf(srcDir, "%s/w%lld/custommodel/import/", rootpath.c_str(), m_CurOWID);
	std::string srcIcm = srcDir + filename + ".icm";
	std::string srcZip = srcDir + filename + ".zip";
	if (GetFileManager().IsFileExistWritePath(srcIcm.c_str()))
	{
		GetFileManager().DeleteWritePathFileOrDir(srcIcm.c_str());
	}
	if (GetFileManager().IsFileExistWritePath(srcZip.c_str()))
	{
		GetFileManager().DeleteWritePathFileOrDir(srcZip.c_str());
	}

	std::vector<ImportCustomModel*>::iterator iter = m_MapImportCustomModels.begin();
	while(iter!=m_MapImportCustomModels.end())
	{
		if ((*iter)->getKey() == filename)
		{
			ENG_DELETE(*iter);
			m_MapImportCustomModels.erase(iter);
			return true;
		}
		iter++;
	}

	return false;
}

void ImportCustomModelMgr::loadResImportCustomModelData() //¼ÓÔØ×Ü¿âÄ£ÐÍ
{
	if (m_bLoadedRes) //Ã»load×Ü¿â
		return;

	m_bLoadedRes = true;

	char dir[256];
	sprintf(dir, "data/custommodel/import/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	clearResImportModels();
	char path[256];
	sprintf(path, "data/custommodel/import");
	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath += "/" + filename + ".icm";

		//È¥ÖØ£¬icmºÍzipÍ¬Ãû
		ImportCustomModel *importcustommodel = findImportCustomModel(RES_MODEL_CLASS, filename);
		if (importcustommodel)
		{
			continue;
		}

		importcustommodel = ENG_NEW(ImportCustomModel)();
		if (importcustommodel->load(filepath, GetClientInfoProxy()->getUin()))
		{
			m_ResImportCustomModels.push_back(importcustommodel);
		}
		else
			ENG_DELETE(importcustommodel);
	}
}

void ImportCustomModelMgr::onSwitchAccountSucceed(int uin) //ÇÐ»»ÕËºÅµÄ´¦Àí
{
	m_bLoadedRes = false;
	clearResImportModels();
	clearPreImportModels();
	loadResImportCustomModelData();
}

void ImportCustomModelMgr::loadMapImportCustomModel(std::string filename) //¼ÓÔØ×Ü¿âµÄµ¥¸öÄ£ÐÍ
{
	if (m_CurOWID<=0)
	{
		return;
	}

	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/import/", rootpath.c_str(), m_CurOWID);
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	std::string filepath(dir);
	filepath += filename + ".icm";
	auto iter = m_MapImportCustomModels.begin();
	for (; iter != m_MapImportCustomModels.end(); iter++)
	{
		if ((*iter)->getKey() == filename)
		{
			return;
		}
	}

	ImportCustomModel* importcustommodel = ENG_NEW(ImportCustomModel)();
	if (importcustommodel->load(filepath, GetClientInfoProxy()->getUin()))
	{
		m_MapImportCustomModels.push_back(importcustommodel);
	}
	else
		ENG_DELETE(importcustommodel);
}

void ImportCustomModelMgr::loadResImportCustomModel(std::string filename) //¼ÓÔØ×Ü¿âµÄµ¥¸öÄ£ÐÍ
{
	char dir[256] = {0};
	sprintf(dir, "data/custommodel/import/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	std::string filepath(dir);
	filepath += filename + ".icm";
	auto iter = m_ResImportCustomModels.begin();
	for (; iter != m_ResImportCustomModels.end(); iter++)
	{
		if ((*iter)->getKey() == filename)
		{
			return;
		}
	}

	ImportCustomModel* importcustommodel = ENG_NEW(ImportCustomModel)();
	if (importcustommodel->load(filepath, GetClientInfoProxy()->getUin()))
	{
		m_ResImportCustomModels.push_back(importcustommodel);
	}
	else
		ENG_DELETE(importcustommodel);
}

void ImportCustomModelMgr::loadPreImportCustomModel(std::string filename) //¼ÓÔØ×Ü¿âµÄµ¥¸öÄ£ÐÍ
{
	char dir[256] = {0};
	sprintf(dir, "data/custommodel_pre/import/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	std::string filepath(dir);
	filepath += filename + ".icm";
	auto it = m_PreImportCustomModels.find(filename);
	if (it != m_PreImportCustomModels.end())
		return;

	ImportCustomModel* importcustommodel = ENG_NEW(ImportCustomModel)();
	if (importcustommodel->load(filepath, -1))
	{
		m_PreImportCustomModels[filename] = importcustommodel;
	}
	else
		ENG_DELETE(importcustommodel);
}

ImportCustomModel * ImportCustomModelMgr::findImportCustomModel(int type, std::string skey)
{
	if (type == MAP_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_MapImportCustomModels.size(); i++)
		{
			if (m_MapImportCustomModels[i] != NULL && m_MapImportCustomModels[i]->getKey()==skey)
			{
				return m_MapImportCustomModels[i];
			}
		}
	}
	else if (type == RES_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_ResImportCustomModels.size(); i++)
		{
			if (m_ResImportCustomModels[i] != NULL && m_ResImportCustomModels[i]->getKey() == skey)
			{
				return m_ResImportCustomModels[i];
			}
		}
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		auto it = m_PreImportCustomModels.find(skey);
		if (it != m_PreImportCustomModels.end())
		{
			return it->second;
		}
	}

	return NULL;
}

std::string ImportCustomModelMgr::importResModelZip(std::string sname, std::string sdesc, int itype, std::string sfilename) //µ¼ÈëÄ£ÐÍÎÄ¼þ£¬·µ»ØmodelµÄkeyÖµ
{
	ImportCustomModel *importcustommodel = ENG_NEW(ImportCustomModel)();
	int authuin = GetClientInfoProxy()->getUin();
	std::string authname = GetClientInfoProxy()->getNickName();
	std::string fkey = sfilename; //±£³ÖzipºÍicmÃû×ÖÒ»ÖÂ
	char dir[256];
	sprintf(dir, "data/custommodel/import/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	std::string filepath(dir);
	filepath += fkey + ".icm";

	if ( importcustommodel->save(filepath, fkey, sname, sdesc, itype, authuin, authname, sfilename) )
	{
		m_ResImportCustomModels.push_back(importcustommodel);
		return fkey;
	}
	else
	{
		ENG_DELETE(importcustommodel);
	}

	return "";
}

std::string ImportCustomModelMgr::copyModelZipFromStudioToRes(char * srcPath) //¿½±´Ä£ÐÍzip°üµ½×Ü¿â
{
	char dir[256];
	sprintf(dir, "data/custommodel/import/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	char filename[256];
#if defined(_WIN32)
	sprintf(filename, "%d%I64d", GetClientInfoProxy()->getUin(), time(NULL));
#else
	sprintf(filename, "%d%ld", GetClientInfoProxy()->getUin(), time(NULL));
#endif
	std::string filepath(dir);
	filepath += std::string(filename) + ".zip";
	if ( GetFileManager().CopyWritePathFileToWritePath(srcPath, filepath.c_str()) )
	{
		return std::string(filename);
	}
	return "";
}

void ImportCustomModelMgr::clearMapImportModels()
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);


	char path[256] = {0};

	for (size_t i = 0; i < m_MapImportCustomModels.size(); i++)
	{
		if (m_MapImportCustomModels[i] == NULL)
			continue;
		if (m_MapImportCustomModels[i]->isLeaveWorldDel() && m_CurOWID > 0)
		{
			sprintf(path, "%s/w%lld/custommodel/import/%s.icm", rootpath.c_str(), m_CurOWID, m_MapImportCustomModels[i]->getFileName().c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
			sprintf(path, "%s/w%lld/custommodel/import/%s.zip", rootpath.c_str(), m_CurOWID, m_MapImportCustomModels[i]->getFileName().c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}

		ENG_DELETE(m_MapImportCustomModels[i]);
	}
	m_MapImportCustomModels.clear();
}

void ImportCustomModelMgr::clearResImportModels()
{
	for (size_t i = 0; i < m_ResImportCustomModels.size(); i++)
	{
		if (m_ResImportCustomModels[i] != NULL)
		{
			ENG_DELETE(m_ResImportCustomModels[i]);
		}
	}
	m_ResImportCustomModels.clear();
}

void ImportCustomModelMgr::clearPreImportModels()
{
	auto it = m_PreImportCustomModels.begin();
	for (; it != m_PreImportCustomModels.end(); it++)
	{
		ENG_DELETE(it->second);
	}
	m_PreImportCustomModels.clear();
}

bool ImportCustomModelMgr::addMapImportModels(ImportCustomModel * pModel)
{
	if (!pModel || findImportCustomModel(MAP_MODEL_CLASS, pModel->getKey()))
	{
		return false;
	}
	m_MapImportCustomModels.push_back(pModel);
	return true;
}

void ImportCustomModelMgr::syncImportModel(int uin, unsigned int startIndex)
{
	size_t i = (size_t)startIndex;
	PB_ImportModelHC importModelHC;
	for (; i < m_MapImportCustomModels.size(); i++)
	{
		if (i == startIndex + 60)
			break;

		ImportCustomModel *pSrcModel = m_MapImportCustomModels[i];

		PB_ImportModelData *pModelData = importModelHC.mutable_models()->Add();
		pModelData->set_key(pSrcModel->getKey());
		pModelData->set_name(pSrcModel->getName());
		pModelData->set_desc(pSrcModel->getDesc());
		pModelData->set_type(pSrcModel->getModelType());
		pModelData->set_authuin(pSrcModel->getAuthUin());
		pModelData->set_authname(pSrcModel->getAuthName());
		pModelData->set_filename(pSrcModel->getFileName());
	}

	if (uin == 0)
		GetGameNetManagerPtr()->sendBroadCast(PB_IMPORT_MODEL_HC, importModelHC, 0);
	else
		GetGameNetManagerPtr()->sendToClient(uin, PB_IMPORT_MODEL_HC, importModelHC);

	if (i < m_MapImportCustomModels.size())
	{
		syncImportModel(uin, i);
	}
}

Rainbow::SharePtr<Rainbow::Texture2D>  ImportCustomModelMgr::genModelTexture(std::string skey, int* libtype /* = nullptr */)
{
	Rainbow::SharePtr<Rainbow::Texture2D> icon = nullptr;
	
	if (skey.find("s_") == 0)
	{
		//ugc导入模型
		icon = GetISandboxActorSubsystem()->GenObjModelTexture(skey);

		if (!icon)
			icon = GetISandboxActorSubsystem()->GenOmodModelTexture(skey);
	}
	else
	{
		icon = createModelTexture(skey, libtype);
	}

	return icon;
}

Rainbow::SharePtr<Rainbow::Texture2D>  ImportCustomModelMgr::createModelTexture(std::string skey, int *libtype /* = nullptr */)
{
	Rainbow::Model *pModel = createImportModel(skey, libtype);

	if (!pModel)
	{
		return nullptr;
	}

	auto *pEntity = Entity::Create();
	pEntity->Load(pModel);

	float scale = 1.0f;
	if (pModel)
	{
		BoxSphereBound bound;
		bound.reset();
		pModel->getLocalBounds(bound);
		if (bound.m_Radius != 0)
		{
			Rainbow::Vector3f maxPos = bound.getMax();
			Rainbow::Vector3f minPos = bound.getMin();

			WCoord box = maxPos - minPos;
			int longest = max(max(box.x, box.y), box.z);
			scale = 300.0f / longest;

		}
	}

	pEntity->SetScale(scale);
	pEntity->UpdateTick(0);
	pEntity->SetInstanceData(Vector4f(1, 1, 0, 0));

	Rainbow::UIModelView3D* modelView = nullptr;
	auto ptex = m_TextureGen->GenerateEx(pEntity->GetGameObject(), BODYTEX_WIDTH, BODYTEX_HEIGHT, &modelView,true);//true 不自动释放纹理信息否则不能生成微缩纹理图片
	if (modelView)
	{
		Rainbow::Camera* camera = modelView->GetCamera();
		{
			float w = 1.0f / tan((Deg2Rad(20 / 2.f)));
			float h = w * WIN_DEF_SCRH / WIN_DEF_SCRW;
			float fovy = Rad2Deg(atan(1.0f / h));
			camera->SetVerticalFieldOfView(fovy);
		}
		camera->LookAt(Rainbow::Vector3f(0, 140.0f, -1000.0f), Rainbow::Vector3f(0, 128.0f, 0.0f));
	}

	return ptex;
}

const int ITEM_UVEDGE = 1;
Rainbow::SharePtr<Rainbow::Texture2D>   ImportCustomModelMgr::getModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b)
{
	Rainbow::SharePtr<Rainbow::Texture2D> icon = nullptr;
	
	if (skey.find("s_") == 0)
	{
		//ugc导入模型
		icon = GetISandboxActorSubsystem()->GetObjModelIcon(skey, u, v, width, height, r, g, b);

		if (!icon)
			icon = GetISandboxActorSubsystem()->GetOmodModelIcon(skey, u, v, width, height, r, g, b);

		/*if (!icon)
		{
			std::string path = UgcAssetMgr::GetInstance().GetAssetFilePath(skey);
			path.append("/body");

			SharePtr<Rainbow::Asset> pres = Rainbow::Model::LoadModelAssetByPathRule(path.c_str());
			if (pres)
			{
				auto model = Rainbow::Model::CreateInstanceFromAsset(pres);
				if (model)
				{
					model->GetModelIcon(u, v, width, height, r, g, b);
				}
			}
		}*/
	}
	else
	{
		icon = createModelIcon(skey, u, v, width, height, r, g, b);
	}

	return icon;
}

Rainbow::SharePtr<Rainbow::Texture2D>   ImportCustomModelMgr::createModelIcon(std::string skey, int &u, int &v, int &width, int &height, int &r, int &g, int &b)
{
	if (skey.empty())
		return nullptr;

	auto iter = m_IconDescs.find(skey);
	if (iter != m_IconDescs.end())
	{
		u = iter->second->u;
		v = iter->second->v;
		width = iter->second->width;
		height = iter->second->height;

		r = iter->second->color.r;
		g = iter->second->color.g;
		b = iter->second->color.b;

		return iter->second->tex;
	}
	else
	{
		auto mapIter = m_MapIconDescs.find(skey);
		if (mapIter != m_MapIconDescs.end())
		{
			u = mapIter->second->u;
			v = mapIter->second->v;
			width = mapIter->second->width;
			height = mapIter->second->height;

			r = mapIter->second->color.r;
			g = mapIter->second->color.g;
			b = mapIter->second->color.b;

			return mapIter->second->tex;
		}
	}

	int libType = PUBLIC_LIB;
	Rainbow::SharePtr<Rainbow::Texture2D>  ptex = genModelTexture(skey, &libType);
	if (ptex)
	{
		IconDesc *desc = ENG_NEW(IconDesc)();

		desc->tex = ptex;
		desc->color = ColorRGBA32::white;

		TextureDesc texdesc;
		desc->u = desc->v = ITEM_UVEDGE;
		desc->width = ptex->GetOrginWidth() - 2 * ITEM_UVEDGE;
		desc->height = ptex->GetOrginHeight() - 2 * ITEM_UVEDGE;
		if (desc->width <= 0)
			desc->width = 1;
		if (desc->height <= 0)
			desc->height = 1;

		desc->isblock = false;

		u = desc->u;
		v = desc->v;
		width = desc->width;
		height = desc->height;
		r = desc->color.r;
		g = desc->color.g;
		b = desc->color.b;

		if (libType == PUBLIC_LIB)
		{
			m_IconDescs[skey] = desc;
			return m_IconDescs[skey]->tex;
		}
		else
		{
			m_MapIconDescs[skey] = desc;
			return m_MapIconDescs[skey]->tex;
		}

	}

	return nullptr;
}

Rainbow::Model* ImportCustomModelMgr::getImportModel(std::string skey, int* libtype/* =nullptr */, int iType /* = -1 */)
{
	Rainbow::Model* model = NULL;
	
	if (skey.find("s_") == 0)
	{
		//ugc导入模型
		model = GetISandboxActorSubsystem()->CreateObjModel(skey);

		if (!model)
			model = GetISandboxActorSubsystem()->CreateOmodModel(skey);

		if (!model)
		{
			std::string path = GetUgcAssetMgrInterface()->GetAssetFilePath(skey);
			path.append("/body");

			SharePtr<Rainbow::Asset> pres = Rainbow::Model::LoadModelAssetByPathRule(path.c_str());
			if (pres)
			{
				model = Rainbow::Model::CreateInstanceFromAsset(pres);
			}
		}
	}
	else
	{
		model = createImportModel(skey, libtype, iType);
	}

	return model;
}

Rainbow::Model *ImportCustomModelMgr::createImportModel(std::string skey, int *libtype/* =nullptr */, int iType /* = -1 */)
{
	SharePtr<Rainbow::ModelData> modelData = nullptr;
	if (m_ModelDataCache.find(skey) != m_ModelDataCache.end())
	{
		modelData = m_ModelDataCache[skey];
	}
	else if (m_ModModelDataCache.find(skey) != m_ModModelDataCache.end())
	{
		modelData = m_ModModelDataCache[skey];
	}
	
	if (!modelData && !checkModelLegitimacy(skey, iType))
	{
		return NULL;
	}

	std::string dataResPath("");
	if (iType == PREVIEW_MODEL_CLASS)
	{
		dataResPath = IMPORT_CM_PREVIEW_PATH + skey + ".zip";
	}
	else
	{
		dataResPath = IMPORT_CM_GENERAL_LIB_PATH + skey + ".zip";

		if (!GetFileManager().IsFileExistWritePath(dataResPath.c_str()))
		{
			if (libtype)
			{
				*libtype = MAP_LIB;
			}

			dataResPath = IMPORT_CM_SYNC_PATH + skey + ".zip";
			if (!GetFileManager().IsFileExistWritePath(dataResPath.c_str()))
			{
				dataResPath.clear();
				char sOwid[64] = { 0 };
				sprintf(sOwid, "%lld", m_CurOWID);
				dataResPath.append("data/w").append(sOwid).append("/custommodel/import/").append(skey).append(".zip");
			}
		}
		else if (libtype)
		{
			*libtype = PUBLIC_LIB;
		}

	}

	core::string zipPath = GetFileManager().ToWriteableFullPath(dataResPath.c_str());

	if (!modelData)
	{
		modelData = PkgUtils::ZipFileResLoad<ModelData>(zipPath.c_str(), "body.omod");
		if (modelData)
			m_ModelDataCache[skey] = modelData;
	}
		
	//¿Í»úÇëÇóÏÂÔØ
	if (!modelData && GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode())
	{
		if (MpGameSurviveCdnResMgr::GetInstancePtr())
			MpGameSurviveCdnResMgr::GetInstancePtr()->addWaitDownloadRes(skey, RoomResType::IMPORT_MODEL);
	}
	
	if (!modelData)
		return nullptr;


	Rainbow::Model* pModel = Rainbow::Model::CreateInstanceLegacy(modelData);
	pModel->PlayAnim(100100);

	//ÉèÖÃbodyµÄÌùÍ¼
	for (int i = 0; i < 10; i++)
	{
		char textureKey[40] = { 0 };
		char meshname[16] = { 0 };
		sprintf(textureKey, "%s_body%d.png", skey.c_str(), i);
		sprintf(meshname, "body%d", i);

		Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
		if (m_TextureCache.find(textureKey) != m_TextureCache.end())
		{
			tex = m_TextureCache[textureKey];
		}
		else if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
		{
			tex = m_ModTextureCache[textureKey];
		}
		else
		{
			char pngPath[16] = { 0 };

			sprintf(pngPath, "body%d.png", i);
			tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath.c_str(), pngPath);
			if(tex)
				m_TextureCache[textureKey] = tex;
		}
			
		if (tex)
		{
			pModel->SetTexture("g_DiffuseTex", tex, meshname);
		}
		else
			break;
	}
	
	//ÉèÖÃfaceµÄÌùÍ¼
	for (int j = 0; j < 10; j++)
	{
		char textureKey[40] = { 0 };
		char meshname[16] = { 0 };
		sprintf(textureKey, "%s_face%d.png", skey.c_str(), j);
		sprintf(meshname, "face%d", j);

		Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
		if (m_TextureCache.find(textureKey) != m_TextureCache.end())
		{
			tex = m_TextureCache[textureKey];
		}
		else if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
		{
			tex = m_ModTextureCache[textureKey];
		}
		else
		{
			char pngPath[16] = { 0 };

			sprintf(pngPath, "face%d.png", j);
			tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath.c_str(), pngPath);
		
			if(tex)
				m_TextureCache[textureKey] = tex;
		}

		if (tex)
		{
			pModel->SetTexture("g_DiffuseTex", tex, meshname);
		}
		else
			break;
	}

	return pModel;
}

Rainbow::Model* ImportCustomModelMgr::getImportHandModel(std::string skey)
{
	Rainbow::Model* model = NULL;
	
	if (skey.find("s_") == 0)
	{
		//ugc导入模型
		model = GetISandboxActorSubsystem()->CreateObjModelHand(skey);

		if (!model)
			model = GetISandboxActorSubsystem()->CreateOmodModelHand(skey);
	}
	else
	{
		model = createImportHandModel(skey);
	}

	return model;

}

Rainbow::Model *ImportCustomModelMgr::createImportHandModel(std::string skey)
{
	char handKey[40] = { 0 };
	sprintf(handKey, "%s_hand", skey.c_str());
	SharePtr<Rainbow::ModelData> modelData = nullptr;
	if (m_ModelDataCache.find(handKey) != m_ModelDataCache.end())
	{
		modelData = m_ModelDataCache[handKey];
	}
	else if (m_ModModelDataCache.find(skey) != m_ModModelDataCache.end())
	{
		modelData = m_ModModelDataCache[skey];
	}

	if (!modelData && !checkModelLegitimacy(skey))
	{
		return NULL;
	}

	std::string dataResPath("");
	dataResPath = IMPORT_CM_GENERAL_LIB_PATH + skey + ".zip";

	if (!GetFileManager().IsFileExistWritePath(dataResPath.c_str()))
	{
		dataResPath = IMPORT_CM_SYNC_PATH + skey + ".zip";
		if (!GetFileManager().IsFileExistWritePath(dataResPath.c_str()))
		{
			dataResPath.clear();
			char sOwid[64] = { 0 };
			sprintf(sOwid, "%lld", m_CurOWID);
			dataResPath.append("data/w").append(sOwid).append("/custommodel/import/").append(skey).append(".zip");
		}
	}

	std::string zipPath = GetFileManager().ToWriteableFullPath(dataResPath.c_str());

	if (!modelData)
	{
		modelData = PkgUtils::ZipFileResLoad<ModelData>(zipPath.c_str(), "hand.omod");

		if (modelData)
		{
			SharePtr<AnimationData> panim = PkgUtils::ZipFileResLoad<AnimationData>(zipPath.c_str(), "hand.oanim");
			if (panim)
			{
				modelData->AddAnimation(panim);
				// panim->Release();
			}
		if(modelData)
			m_ModelDataCache[handKey] = modelData;
		}
	}

	if (!modelData)
		return NULL;



	Rainbow::Model* pModel = Rainbow::Model::CreateInstanceLegacy(modelData);



	//ÉèÖÃhandµÄÌùÍ¼
	char textureKey[40] = { 0 };
	sprintf(textureKey, "%s_body0.png", skey.c_str());

	Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
	if (m_TextureCache.find(textureKey) != m_TextureCache.end())
	{
		tex = m_TextureCache[textureKey];
	}
	else if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
	{
		tex = m_ModTextureCache[textureKey];
	}
	else
	{
		SharePtr<Texture2D> panim = PkgUtils::ZipFileResLoad<Texture2D>(zipPath.c_str(), "body0.png");
		
		if(tex)
			m_TextureCache[textureKey] = tex;
	}

	if (tex)
	{
		pModel->SetTexture("g_DiffuseTex", tex, "hand01");
		pModel->SetTexture("g_DiffuseTex", tex, "hand02");
	}

	return pModel;
}

void ImportCustomModelMgr::LoadModImportModel(std::string skey, std::string modroot)
{
	char zipPath[256] = { 0 };
	sprintf(zipPath, "%s/resource/custommodel/fully/%s.zip", modroot.c_str(), skey.c_str());

	SharePtr<ModelData> modelData = PkgUtils::ZipFileResLoad<ModelData>(zipPath, "body.omod");
	if (!modelData)
		return;

	m_ModModelDataCache[skey] = modelData;

	//ÉèÖÃbodyµÄÌùÍ¼
	for (int i = 0; i < 10; i++)
	{
		char textureKey[40] = { 0 };
		char meshname[16] = { 0 };
		sprintf(textureKey, "%s_body%d.png", skey.c_str(), i);
		sprintf(meshname, "body%d", i);

		Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
		if (m_TextureCache.find(textureKey) != m_TextureCache.end())
			continue;

		if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
			continue;

	
		char pngPath[16] = { 0 };

		sprintf(pngPath, "body%d.png", i);
		tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath, pngPath);

		if(tex)
			m_ModTextureCache[textureKey] = tex;
		else
			break;
	}

	//ÉèÖÃfaceµÄÌùÍ¼
	for (int j = 0; j < 10; j++)
	{
		char textureKey[40] = { 0 };
		char meshname[16] = { 0 };
		sprintf(textureKey, "%s_face%d.png", skey.c_str(), j);
		sprintf(meshname, "face%d", j);

		Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
		if (m_TextureCache.find(textureKey) != m_TextureCache.end())
			continue;

		if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
			continue;

		char pngPath[16] = { 0 };

		sprintf(pngPath, "face%d.png", j);
		tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath, pngPath);
		if(tex)
			m_ModTextureCache[textureKey] = tex;
		else
			break;
	}


	char handKey[40] = { 0 };
	sprintf(handKey, "%s_hand", skey.c_str());
	SharePtr<ModelData> handModelData = PkgUtils::ZipFileResLoad<ModelData>(zipPath, "hand.omod");
	
	SharePtr<AnimationData> panim = PkgUtils::ZipFileResLoad<AnimationData>(zipPath, "hand.oanim");
	if (panim)
	{
		handModelData->AddAnimation(panim);
		// panim->Release();
	}
	if (handModelData)
		m_ModModelDataCache[handKey] = handModelData;
	else
		return;

	//ÉèÖÃhandµÄÌùÍ¼
	char textureKey[40] = { 0 };
	sprintf(textureKey, "%s_body0.png", skey.c_str());

	Rainbow::SharePtr<Rainbow::Texture2D>  tex = nullptr;
	if (m_TextureCache.find(textureKey) != m_TextureCache.end())
		return;

	if (m_ModTextureCache.find(textureKey) != m_ModTextureCache.end())
		return;

	tex = PkgUtils::ZipFileResLoad<Texture2D>(zipPath, "body0.png");
	if (tex)
		m_ModTextureCache[textureKey] = tex;
	
}

bool ImportCustomModelMgr::checkModelLegitimacy(std::string skey, int iType/* =-1 */)
{
	if (iType == PREVIEW_MODEL_CLASS || iType == UI_SHOW_MODEL_CLASS)  //Ô¤ÀÀµÄ »òÕßUIÕ¹Ê¾µÄ ºöÂÔ¼ì²â
		return true;

	
	bool isRemoteMode = (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode());
	if (!isRemoteMode)  //·Ç¿Í»úÊ±Òª¼ì²éuin
	{
		int checkuin = -1;
		if (m_CurOWID > 0)
		{
			checkuin = m_CurOWRealUin;
		}
		else
		{
			checkuin = GetClientInfoProxy()->getUin();
		}

		if (checkuin >= 0)
		{
			auto *importModel = findImportCustomModel(MAP_MODEL_CLASS, skey);
			if (!importModel)
				importModel = findImportCustomModel(RES_MODEL_CLASS, skey);

			if (!importModel)
				return false;

			auto resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(skey);
			if (resInfo)
			{
				//²»ÊÇµØÍ¼×÷ÕßÏÂÔØµÄ×ÊÔ´,Ò²²»ÊÇ×Ô¼ºÏÂÔØµÄ×ÊÔ´£¬Ò²²»ÔÚÄ£°åµØÍ¼ÄÚ ²»ÔÊÐíÊ¹ÓÃ
				if (resInfo->download_uin != checkuin &&
					resInfo->download_uin != GetClientInfoProxy()->getUin() &&
					(GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false))
				{
					return false;
				}
			}
			else
			{
				//²»ÊÇµØÍ¼×÷ÕßµÄ×ÊÔ´,Ò²²»ÊÇ×Ô¼ºµÄ×ÊÔ´£¬Ò²²»ÔÚÄ£°åµØÍ¼ÄÚ ²»ÔÊÐíÊ¹ÓÃ
				if (importModel->getAuthUin() != checkuin &&
					importModel->getAuthUin() != GetClientInfoProxy()->getUin() &&
					(GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), importModel->getAuthUin()) == false))
				{
					return false;
				}
			}

			return true;
		}
		else
		{
			return false;
		}
	}
	else
		return true;
}

int ImportCustomModelMgr::getFreeId(int type)
{
	if (type == IMPORT_BLOCK_MODEL)
	{
		for (int i = 0; i < CustomBlockModelMinId-1; i++)
		{
			auto* def = GetDefManagerProxy()->getBlockDef(CustomBlockModelMaxId - i, false);
			if (!def && !GetDefManagerProxy()->getItemDef(CustomBlockModelMaxId - i))
				return CustomBlockModelMaxId - i;
		}
	}
	else if (type == IMPORT_ITEM_MODEL)
	{
		for (int i = 0; i < 5850; i++)
		{
			auto* def = GetDefManagerProxy()->getItemDef(9950 - i, false);
			if (!def)
				return 9950 - i;
		}
	}
	else if (type == IMPORT_ACTOR_MODEL)
	{
		for (int i = 0; i < 3000; i++)
		{
			auto* def = GetDefManagerProxy()->getMonsterDef(3000 - i, false);
			if (!def)
				return 3000 - i;
		}
	}

	return -1;
}


bool ImportCustomModelMgr::reEncryptImportCustomModel(long long owid, int olduin, int newuin, const std::string& sAuthName)
{
	char dir[256];
	sprintf(dir, "data/w%lld/custommodel/import/", owid);
	if (!gFunc_isStdioDirExist(dir)) { return true; }

	char path[256];
	sprintf(path, "data/w%lld/custommodel/import", owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string fullname = std::string(path) + "/" + *it + ".icm";
		int buflen = 0;
		void* buf = ReadWholeFile(fullname.c_str(), buflen);
		if (buf == NULL)
			continue;

		flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
		if (!FBSave::VerifyImportCustomModelBuffer(verifier))
		{
			free(buf);
			continue;
		}

		const FBSave::ImportCustomModel *importModel = FBSave::GetImportCustomModel(buf);
		if (!importModel || importModel->authuin() != olduin || importModel->authuin() == newuin)
		{
			free(buf);
			continue;
		}

		flatbuffers::FlatBufferBuilder builder;

		auto icm = FBSave::CreateImportCustomModel(builder,
			builder.CreateString(importModel->key()),
			builder.CreateString(importModel->name()),
			builder.CreateString(importModel->desc()),
			importModel->type(),
			newuin,
			builder.CreateString(sAuthName),
			builder.CreateString(importModel->filename()));
		builder.Finish(icm);

		free(buf);
		if(!GetFileManager().SaveToWritePath(fullname.c_str(), builder.GetBufferPointer(), builder.GetSize())) { return false; }
	}

	return true;
}