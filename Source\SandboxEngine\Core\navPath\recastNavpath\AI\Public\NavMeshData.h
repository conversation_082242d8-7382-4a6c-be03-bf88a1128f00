#pragma once
#include "Public/NavMeshBuildSettings.h"
#include "Geometry/AABB.h"
#include "Math/Quaternionf.h"
#include "Utilities/Hash128.h"
#include "Utilities/dynamic_array.h"

// OffMeshLinkData is a scripting API type.
struct AutoOffMeshLinkData
{
    Rainbow::Vector3f m_Start;
    Rainbow::Vector3f m_End;
    float m_Radius;
    unsigned short m_LinkType;      // Off-mesh poly flags.
    unsigned char m_Area;           // Off-mesh poly  area ids.
    unsigned char m_LinkDirection;  // Off-mesh connection direction flags (NavMeshLinkDirectionFlags)
};

typedef std::vector<AutoOffMeshLinkData> OffMeshLinkDataVector;

struct NavMeshTileData
{
    dynamic_array<UInt8> m_MeshData;
    Rainbow::Hash128 m_Hash;
};

typedef std::vector<NavMeshTileData> NavMeshTileDataVector;

class NavMeshData
{
public:
    NavMeshData();
    // ~NavMeshData - declared by macro

    static void InitializeClass();
    static void CleanupClass();

    inline const NavMeshTileDataVector& GetNavMeshTiles() const;
    inline void SetNavMeshTiles(const NavMeshTileDataVector& tiles);
    // Updates NavMeshData vector, removes tiles, compacts and adds new tiles.
    //   removeTileIDs - indices tiles to remove (current tile vector)
    //   newTiles - new tiles to add, ownership is transferred to this and array is cleared
    //   newTileIDs - IDs of the newly added tiles (new tile vector)
    void UpdateTiles(const dynamic_array<int>& removeTileIDs, NavMeshTileDataVector& newTiles, dynamic_array<int>& newTileIDs);

    
    inline const NavMeshBuildSettings& GetNavMeshBuildSettings() const { return m_NavMeshBuildSettings; }
    inline void SetNavMeshBuildSettings(const NavMeshBuildSettings& settings) { m_NavMeshBuildSettings = settings; }


    inline int GetAgentTypeID() const { return m_AgentTypeID; }
    inline void SetAgentTypeID(int agentTypeID) { m_AgentTypeID = agentTypeID; }

    inline void SetSourceBounds(const Rainbow::AABB& bounds) { m_SourceBounds = bounds; }
    inline const Rainbow::AABB& GetSourceBounds() const { return m_SourceBounds; }

    inline void SetPosition(const Rainbow::Vector3f& position) { m_Position = position; }
    inline const Rainbow::Vector3f& GetPosition() const { return m_Position; }

    inline const Rainbow::Quaternionf& GetRotation() const { return m_Rotation; }
    inline void SetRotation(const Rainbow::Quaternionf& rotation) { m_Rotation = rotation; }

private:
    NavMeshBuildSettings m_NavMeshBuildSettings;
    NavMeshTileDataVector m_NavMeshTiles;
   
    Rainbow::AABB m_SourceBounds;
    Rainbow::Quaternionf m_Rotation;
    Rainbow::Vector3f m_Position;
    int m_AgentTypeID;
};

inline const NavMeshTileDataVector& NavMeshData::GetNavMeshTiles() const
{
    return m_NavMeshTiles;
}

inline void NavMeshData::SetNavMeshTiles(const NavMeshTileDataVector& tiles)
{
    m_NavMeshTiles = tiles;
}

