#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"
#include "CustomModelMgr.h"

#include "Math/Color.h"
#include "UICommon/UIModelView3D.h"

#include "Texture/TextureRenderGen.h"

#include "IActorBody.h"
#include "IBackpack.h"
#include "gamenet/GameNetManager.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "blocks/container_world.h"
#include "ActorManagerInterface.h"
#include "DefManagerProxy.h"
#include "ClientInfoProxy.h"
#include "WorldStringManagerProxy.h"
#include "WorldManager.h"
#include "CustomModelWaitSyncListMgr.h"
#include "ImageMesh.h"

using namespace MNSandbox;
using namespace Rainbow;

const int ITEM_UVEDGE = 1;

const int BODYTEX_WIDTH = 200;
const int BODYTEX_HEIGHT = 200;

void FullyCustomModelMgr::update(float dtime)
{
	if (m_pCurEditActorBody)
		m_pCurEditActorBody->update(dtime);
}

void FullyCustomModelMgr::tick()
{
	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->tick();

	m_iRefreshIconTick++;
	if (m_iRefreshIconTick >= 20)
	{
		m_iRefreshIconTick = 0;
		refreshIcon();
	}

	if (m_pCurEditFcm && GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_NOT_INIT && GetIPlayerControl())
	{	
		m_iAutoSaveTick++;
		if (m_iAutoSaveTick >= 20 * 10)  //10s保存一次
		{
			m_iAutoSaveTick = 0;
			if (!m_pCurEditFcm->needSave())
				return;

			WorldContainer *container = GetIPlayerControl()->GetPlayerControlCurOpenedContainer();
			if (!container)
				return;

			saveCurEditModel(dynamic_cast<IClientPlayer*>(GetIPlayerControl()), HOST_SAVE, "", "", ONLY_SAVE);
			std::string skey = m_pCurEditFcm->getKey();
			container->updateFullyCustomModelData(skey);
		}
	}
}

void FullyCustomModelMgr::refreshIcon()
{
	//bool notifyUI = false;
	auto iter = m_IncompleteIcons.begin();
	for (; iter != m_IncompleteIcons.end();)
	{
		iter->second.tryrefreshtime--;
		int missmodelnum = 0;
		int modelLibType = MAP_MODEL_CLASS;		// 这个参数 genModelTexture 只是用来和FULLY_PACKING_CUSTOM_MODEL做了比较， 并不用来找fcm
		
		auto icondescIter = m_IconDescs.find(iter->first);
		Rainbow::SharePtr<Rainbow::Texture2D> pOldTex = nullptr;
		if (icondescIter != m_IconDescs.end())
		{
			pOldTex = icondescIter->second->tex;
		}
		else
		{
			// 不可能出现不存在的情况
			iter = m_IncompleteIcons.erase(iter);
			continue;
		}
		
		Rainbow::SharePtr<Rainbow::Texture2D>  ptex = genModelTexture(iter->first, missmodelnum, FULLY_ITEM_MODEL, 
			modelLibType, iter->second.missmodelnum, pOldTex, iter->second.tryrefreshtime % 10 == 0);
		if (missmodelnum >= iter->second.missmodelnum)
		{
			if (iter->second.tryrefreshtime <= 0)
			{
				iter = m_IncompleteIcons.erase(iter);
			}
			else
				++iter;
			continue;
		}
		else
			iter->second.missmodelnum = missmodelnum;
			
		//TODO:这一块不知道翻译的???不??
		// if (!ptex)
		// {
		// 	char path[256];
		// 	sprintf(path, "$model:%s", iter->first.c_str());
		// 	ptex = UILib::UIRenderer::GetInstance().CreateTexture(path);
		// }
		
		// if (!ptex)
		// {
		// 	ptex = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(GetWhiteTexture());
		// }

		//if (ptex)
		//{
		//	notifyUI = true;
		//	IconDesc*desc = ENG_NEW(IconDesc)();
		//	desc->tex = ptex;
		//	desc->color = ColorRGBA32::white;
		//	desc->u = desc->v = ITEM_UVEDGE;
		//	desc->width = ptex->GetOrginWidth() - 2 * ITEM_UVEDGE;
		//	desc->height = ptex->GetOrginHeight() - 2 * ITEM_UVEDGE;
		//	if (desc->width <= 0)
		//		desc->width = 1;
		//	if (desc->height <= 0)
		//		desc->height = 1;

		//	if (icondescIter != m_IconDescs.end()) {
		//		desc->isblock = icondescIter->second->isblock;
		//	}
		//	else
		//		desc->isblock = false;

		//	if (icondescIter != m_IconDescs.end())
		//	{
		//		ENG_DELETE(icondescIter->second);
		//		m_IconDescs.erase(icondescIter);
		//	}
		//	
		//	m_IconDescs[iter->first] = desc;
		//}

		if (missmodelnum <= 0 || iter->second.tryrefreshtime <= 0) {
			iter = m_IncompleteIcons.erase(iter);
		}
		else
			++iter;
	}

	//if (notifyUI)   //刷新界面
	//{
	//	GetGameEventQue().postBackpackChange(-1);
	//}
}

int FullyCustomModelMgr::getFreeId(int eFcmSaveType)
{
	if (eFcmSaveType == FULLY_BLOCK_MODEL)
	{
		for (int i = 0; i < CustomBlockModelMinId-1; i++)
		{
			auto *def = GetDefManagerProxy()->getBlockDef(CustomBlockModelMaxId - i, false);
			if (!def && !GetDefManagerProxy()->getItemDef(CustomBlockModelMaxId - i))
				return CustomBlockModelMaxId - i;
		}
	}
	else if (eFcmSaveType == FULLY_ITEM_MODEL || eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		for (int i = 0; i < 5850; i++)
		{
			auto *def = GetDefManagerProxy()->getItemDef(9950 - i, false);
			if (!def)
				return 9950 - i;
		}
	}
	else if (eFcmSaveType == FULLY_ACTOR_MODEL)
	{
		for (int i = 0; i < 3000; i++)
		{
			auto *def = GetDefManagerProxy()->getMonsterDef(3000 - i, false);
			if (!def)
				return 3000 - i;
		}
	}

	return -1;
}

void FullyCustomModelMgr::closeEditModelUI(IClientPlayer* player, int eFcmSaveType,
	WCoord containerpos /* = WCoord(0, -1, 0) */, std::string name/* ="" */, std::string desc/* ="" */)
{
	if (!player)
		return;

	//if (!m_pCurEditCustomModel)
		//return;

	if (player->GetPlayerWorld()->isRemoteMode())
	{
		if ((m_pCurEditFcm && m_pCurEditFcm->needSave()) || eFcmSaveType > CLOSE_EDIT_FCM_UI_TYPE::ONLY_SAVE)
		{
			if (!m_pCurEditFcm)
				return;

			// 客机保存文件
			IClientPlayer *player = NULL;
			int hostUin = 0;
			if (GetGameNetManagerPtr() && GetWorldManagerPtr())
			{
				hostUin = GetGameNetManagerPtr()->getHostUin();
				player = GetWorldManagerPtr()->getPlayerByUin(hostUin);
			}
				
			if (!player)
				return;

			std::string makeKey = saveCurEditModel(player, CLIENT_SAVE, name, desc, eFcmSaveType);
			if (makeKey.empty())
				return;

			// 客机上传
			std::string skey = m_pCurEditFcm->getKey();
			jsonxx::Object externdataObj;
			externdataObj << "operatetype" << eFcmSaveType;
			externdataObj << "name" << name;
			externdataObj << "desc" << desc;
			externdataObj << "makekey" << makeKey;
			reqUploadFullyCustomModel(skey, m_pCurEditFcm->getFileName(), hostUin, FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH, externdataObj.json());
			// 通知主机下载，并返回结果
		}
		else
		{
			std::string skey = "";
			if (m_pCurEditFcm)
				skey = m_pCurEditFcm->getKey();

			syncCloseUI2Host(CLOSE_EDIT_FCM_UI_TYPE::NORMAL_CLOSE, "", 1, skey);
		}
			
	}
	else
	{
		WorldContainer *container = player->GetPlayerWorld()->getContainerMgr()->getContainerExt(containerpos);
		if (m_pCurEditFcm)
		{
			std::string makeKey = "";
			if (eFcmSaveType > CLOSE_EDIT_FCM_UI_TYPE::ONLY_SAVE || m_pCurEditFcm->needSave())
			{
				makeKey = saveCurEditModel(dynamic_cast<IClientPlayer*>(GetIPlayerControl()), HOST_SAVE, name, desc, eFcmSaveType);
				m_pCurEditFcm->setNeedSave(false);
			}

			std::string skey = m_pCurEditFcm->getKey();
			m_pCurEditFcm->addVersion(1);

			if (container && eFcmSaveType > CLOSE_EDIT_FCM_UI_TYPE::NORMAL_CLOSE)
			{
				container->updateFullyCustomModelData(skey);
			}

			if (!makeKey.empty() && eFcmSaveType >= CLOSE_EDIT_FCM_UI_TYPE::SAVE_AND_CREATE_BLOCK) //生成相应的方块operatetype=BLOCK_MODEL、道具operatetype=WEAPON_MODEL、生物operatetype=ACTOR_MODEL
			{
				createObjectByCurEditModel(player, containerpos, eFcmSaveType, makeKey, name, desc);
			}

			m_pCurEditFcm->setEditing(false);
			m_pCurEditFcm = NULL;
		}
		
		if (container)
		{
			container->removeOpenUIN(player->getUin());
		}
		//GetGameEventQue().postCloseEditFullyCustomModel(SAVE_CLOSE_FCM_UI_SUCCESS);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("result", SAVE_CLOSE_FCM_UI_SUCCESS);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_CLOSE_EDIT_FULLYCUSTOMMODEL", sandboxContext);

		if (m_pCurEditActorBody)
			SANDBOX_DELETE(m_pCurEditActorBody);
	}
}

void FullyCustomModelMgr::syncCloseUI2Host(int operatetype, std::string url/* ="" */, int version, std::string skey/* ="" */, std::string name/* ="" */, std::string desc/* ="" */)
{
	if (GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT)
	{
		PB_CloseFullyCustomModelUICH closeFullyCustomModelUICH;
		closeFullyCustomModelUICH.set_operatetype(operatetype);
		closeFullyCustomModelUICH.set_url(url);
		closeFullyCustomModelUICH.set_version(version);
		closeFullyCustomModelUICH.set_skey(skey);
		closeFullyCustomModelUICH.set_name(name);
		closeFullyCustomModelUICH.set_desc(desc);

		GetGameNetManagerPtr()->sendToHost(PB_CLOSE_FULLYCUSTOMMODEL_UI_CH, closeFullyCustomModelUICH);
	}
}

void FullyCustomModelMgr::syncCloseUI2Client(int uin, int result, WCoord pos/* =WCoord(0, -1, 0) */, int mapid/* =0 */, std::string skey/* ="" */)
{
	if (uin <= 0)
		return;

	PB_CloseFullyCustomModelUIHC closeFullyCustomModelUIHC;
	closeFullyCustomModelUIHC.set_result(result);
	PB_Vector3* containerPos = closeFullyCustomModelUIHC.mutable_containerpos();
	containerPos->set_x(pos.x);
	containerPos->set_y(pos.y);
	containerPos->set_z(pos.z);
	closeFullyCustomModelUIHC.set_mapid(mapid);
	closeFullyCustomModelUIHC.set_skey(skey);


	GetGameNetManagerPtr()->sendToClient(uin, PB_CLOSE_FULLYCUSTOMMODEL_UI_HC, closeFullyCustomModelUIHC);
}

void FullyCustomModelMgr::createObjectByCurEditModel(IClientPlayer* player, const WCoord& containerpos, int eFcmSaveType, std::string skey, std::string name, std::string desc)
{
	int id = getFreeId(eFcmSaveType);
	int involvedId = 0;
	if (id <= 0)
	{
		//没有??分配的id??
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
		return;
	}

	if (eFcmSaveType == SAVE_AND_CREATE_ACTOR)
	{
		involvedId = getFreeId(SAVE_AND_CREATE_ITEM);//获取一????用的道具id

		if (involvedId <= 0)
		{
			//没有??分配的id??
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
			return;
		}
	}
		

	CustomModelMgr::GetInstancePtr()->addCustomItemData(id, skey, "default", eFcmSaveType, involvedId, 0);
	GetDefManagerProxy()->addDefByCustomModel(id, eFcmSaveType, skey, name, desc, Rainbow::Vector3f(0, 0, 0), involvedId);

	std::string content = name + ";" + desc;

	GetWorldStringManagerProxy()->insert(skey, content, SAVEFILETYPE::FULLY_CUSTOM_MODEL);
	//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
	//	.SetData_Number("type", 6)
	//	.SetData_String("content", content)
	//	.SetData_String("key", skey));

	if (!player->GetPlayerWorld()->isRemoteMode())
	{
		PB_CustomItemIDsHC customItemIDsHC;
		for (size_t i = 0; i < 1; i++)
		{
			customItemIDsHC.add_customitemids(id);
			customItemIDsHC.add_custommodelfilenames(skey);
			customItemIDsHC.add_custommodelclassnames("default");
			customItemIDsHC.add_custommodelfolderindexs(0);
			customItemIDsHC.add_customtypes(eFcmSaveType);
			customItemIDsHC.add_involvedids(involvedId);
		}
		GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_ITEMIDS_HC, customItemIDsHC, 0);
	}


	if (eFcmSaveType == FULLY_ACTOR_MODEL)
	{
		ActorManagerInterface*actormgr = player->GetPlayerWorld()->getActorMgr();
		if (actormgr)
		{
			if (containerpos.y > 0)
			{
				actormgr->iSpawnMob(containerpos*BLOCK_SIZE, id, false, false);
			}
		}

		//player->getBackPack()->tryAddItem(involvedId, 1, -1, 0, NULL);
		GridCopyData data;
		data.resid = involvedId;
		data.num = 1;
		player->getIBackPack()->tryAddItem_byGridCopyData(data);
	}
	else
	{
		//player->getBackPack()->tryAddItem(id, 1, -1, 0, NULL);
		GridCopyData data;
		data.resid = id;
		data.num = 1;
		player->getIBackPack()->tryAddItem_byGridCopyData(data);
	}

	if (CustomModelMgr::GetInstancePtr())
	{
		if(ResourceCenter::GetInstancePtr())
			ResourceCenter::GetInstancePtr()->addOneResToClass(skey, true);
	}
		
}

void FullyCustomModelMgr::openEditUI2Client(WorldContainer *container, std::string url, bool edited, int version, int result)
{
	LOG_INFO("openEditUI2Client result : %d url:%s version:%d", result, url.c_str(), version);
	bool canOpen = false;
	if (result == 0 && !url.empty() && container)
	{
		std::string skey = "";

		bool result = container->Event2().Emit<std::string&>("ContainerFullyCustomModel_getKey", skey);
		Assert(result);
		if (skey.empty())  //空数??，无需下载同???资源，直接打开界面
			canOpen = true;
		else
		{
			auto *fullyCustomModel = findFullyCustomModel(MAP_MODEL_CLASS, skey);
			if (fullyCustomModel && fullyCustomModel->getVersion() >= version)  //??地有数据，且版本号大于等于服务器上的
			{
				canOpen = true;
			}	
		}

		if (canOpen)
		{
			bool result = container->Event2().Emit<>("ContainerFullyCustomModel_syncData");
			Assert(result);
			//GetGameEventQue().postOpenEditFullyCustomModel(edited);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Bool("edited", edited);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_OPEN_EDIT_FULLYCUSTOMMODEL", sandboxContext);
		}
		else
		{
			//TODO 下载??定义模型资源
			reqDownloadFile(url, FCM_CLIENT_DOWNLOAD_BY_CLIENT_EDIT, version);
		}
	}
	else  //打开编辑界面失败
	{
		//GetGameEventQue().postPreOpenEditFCMUI(PRE_OPEN_UPLOAD_FAIL);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("state", PRE_OPEN_UPLOAD_FAIL);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PRE_OPEN_EDIT_FCM_UI", sandboxContext);
	}	
}

void FullyCustomModelMgr::syncPreOpenEditUI2Client(int uin, int state)
{
	if (uin <= 0)
		return;

	PB_PreOpenEditFCMUIHC preOpenEditFCMUIHC;
	preOpenEditFCMUIHC.set_state(state);

	GetGameNetManagerPtr()->sendToClient(uin, PB_PRE_OPEN_EDIT_FCM_UI, preOpenEditFCMUIHC);
}

Rainbow::SharePtr<Rainbow::Texture2D>  FullyCustomModelMgr::getModelIcon(std::string skey, int modeltype, int &u, int &v, int &width, int &height, int &r, int &g, int &b)
{
	if (skey.empty())
		return nullptr;

	auto iter = m_IconDescs.find(skey);
	if (iter != m_IconDescs.end())
	{
		u = iter->second->u;
		v = iter->second->v;
		width = iter->second->width;
		height = iter->second->height;

		r = iter->second->color.r;
		g = iter->second->color.g;
		b = iter->second->color.b;

		return iter->second->tex;
	}

		
	int missmodelnum;
	int modelLibType;
	Rainbow::SharePtr<Rainbow::Texture2D>  ptex = genModelTexture(skey, missmodelnum, modeltype, modelLibType);

	//TODO:这一块不知道翻译的???不??
	//if (!ptex)
	//{
	//	char path[256];
	//	sprintf(path, "$model:%s", skey.c_str());
	//	ptex = UILib::UIRenderer::GetInstance().CreateTexture(path);
	//}

	//if (!ptex)
	//{
	//	ptex = Rainbow::NativeToSharePtr<Rainbow::Texture2D>(GetWhiteTexture());
	//}

	if (ptex)
	{
		IconDesc*desc = ENG_NEW(IconDesc)();
		desc->tex = ptex;
		desc->color = ColorRGBA32::white;

		desc->u = desc->v = ITEM_UVEDGE;
		desc->width = ptex->GetOrginWidth() - 2 * ITEM_UVEDGE;
		desc->height = ptex->GetOrginHeight() - 2 * ITEM_UVEDGE;
		if (desc->width <= 0)
			desc->width = 1;
		if (desc->height <= 0)
			desc->height = 1;

		if (&desc)
		{
			if (modeltype == BLOCK_MODEL)
				desc->isblock = true;
			else
				desc->isblock = false;
		}

		u = desc->u;
		v = desc->v;
		width = desc->width;
		height = desc->height;
		r = desc->color.r;
		g = desc->color.g;
		b = desc->color.b;

		if (missmodelnum > 0)
		{
			InComplete_Icon iconinfo;
			iconinfo.missmodelnum = missmodelnum;
			iconinfo.tryrefreshtime = 100;
			m_IncompleteIcons[skey] = iconinfo;
		}

		m_IconDescs[skey] = desc;
		return m_IconDescs[skey]->tex;
	}

	return nullptr;
}

Rainbow::SharePtr<Rainbow::Texture2D>  FullyCustomModelMgr::genModelTexture(std::string skey, int &missmodelnum, int modeltype, int &modellibtype, int lastmissmodelnum/* =0 */, Rainbow::SharePtr<Rainbow::Texture2D> result, bool bForceRegen)
{

	missmodelnum = 0;
	FullyCustomModel *fcm = findFullyCustomModel(MAP_MODEL_CLASS, skey, true);
	if (!fcm)
	{		
		fcm = findFullyCustomModel(RES_MODEL_CLASS, skey, true);
		modellibtype = RES_MODEL_CLASS;

		if (!fcm)
		{
			fcm = findFullyCustomModel(EQUIP_MODEL_CLASS, skey, true);
		}
	}
	else
		modellibtype = MAP_MODEL_CLASS;
		

	if (!fcm)
	{
		if (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode() && modeltype != FULLY_PACKING_CUSTOM_MODEL)
		{
			
			Rainbow::SharePtr<Rainbow::Texture2D> destTexture = Rainbow::MakeSharePtrWithLabel<Rainbow::Texture2D>(kMemTexture);
			destTexture->InitTexture(BODYTEX_WIDTH, BODYTEX_HEIGHT, kTexFormatRGBA32, TextureCreationFlags::kTextureInitNone, 1, 1);

			addDownload(skey);
			return destTexture;
		}

		return nullptr;
	}

	//getClientMissSubCMNum这个接口没有用。
	// if (lastmissmodelnum > 0)
	// {
	// 	fcm->getClientMissSubCMNum(missmodelnum);
	// 	if (missmodelnum >= lastmissmodelnum)
	// 		return nullptr;
	// }
	auto *pEntity = Entity::Create();
	SharePtr< ModelData> modeldata = MakeSharePtr<ModelData>();

	Rainbow::Model* pModel = Model::Model::CreateInstanceLegacy(modeldata, true);


	pModel->SetCustomPrepareFinish(false);
	pEntity->Load(pModel);

	//if (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode())
	// 不管单机 联机 都设置bUpdateWhenCustomModelReady为true， 让底层可以异步计算加载cm的模型
	bool bUpdateWhenCustomModelReady = false;
	if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		bUpdateWhenCustomModelReady = true;

	if (fcm->getModelType() == FULLY_PACKING_CUSTOM_MODEL)
		fcm->setModelDataByPackingIcon(pEntity, NULL, bUpdateWhenCustomModelReady);
	else
		fcm->setModelData(pEntity, nullptr, NULL, bUpdateWhenCustomModelReady);

	pModel->SetCustomPrepareFinish(true);
	pEntity->UpdateBindFather();

	if (bUpdateWhenCustomModelReady)
	{
		missmodelnum = GetCustomModelWaitSyncListMgrPtr()->getWaitMapSize(pEntity);
		GetCustomModelWaitSyncListMgrPtr()->setNoNeedUpdate(pEntity);
	}
	
	if (!(bForceRegen || lastmissmodelnum == 0 || (missmodelnum == 0 && missmodelnum < lastmissmodelnum))){
		DESTORY_GAMEOBJECT_BY_COMPOENT(pEntity);
		return nullptr;
	}

	Rainbow::SharePtr<Rainbow::Texture2D>  ptex = nullptr;

	if (fcm->getModelType() != FULLY_PACKING_CUSTOM_MODEL)
	{
		pEntity->SetRotation(45, 0, 0);
		pEntity->SetPosition(Rainbow::Vector3f(-50, 0, 0));
		pEntity->SetScale(1.15f);
	}

	pEntity->UpdateTick(0);
	pEntity->SetInstanceData(Vector4f(1, 1, 0, 0));

	Rainbow::UIModelView3D* modelView = nullptr;
	ptex = m_TextureGen->GenerateEx(pEntity->GetGameObject(), BODYTEX_WIDTH, BODYTEX_HEIGHT, &modelView,true, result);//true 不自动释放纹理信??否则不能生成??缩纹理图??
	if (modelView)
	{
		Rainbow::Camera* camera = modelView->GetCamera();
		{
			float w = 1.0f / tan((Deg2Rad(20 / 2.f)));
			float h = w * WIN_DEF_SCRH / WIN_DEF_SCRW;
			float fovy = Rad2Deg(atan(1.0f / h));
			camera->SetVerticalFieldOfView(fovy);
		}
		camera->LookAt(Rainbow::Vector3f(0, 140.0f, -1000.0f), Rainbow::Vector3f(0, 128.0f, 0.0f));
	}
	return ptex;
}