//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Math/Vector2f.h"
#include "Math/Vector3f.h"
#include "Math/Quaternionf.h"
#include "Utilities/Utility.h"

struct CrowdAgentDebugInfo;

struct ObstacleAvoidanceParams
{
    float velBias;
    float weightDesVel;
    float weightCurVel;
    float weightToi;
    unsigned char adaptiveDivs;
    unsigned char adaptiveRings;
    unsigned char adaptiveDepth;
};

class ObstacleAvoidanceQuery
{
public:

    ObstacleAvoidanceQuery();
    ~ObstacleAvoidanceQuery();

    void Reset(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& rot, float rad, float height);
    void AddCircle(const Rainbow::Vector3f& pos, float rad, float weight, const Rainbow::Vector3f& vel, const Rainbow::Vector3f& dvel);
    void AddBoundarySegment(const Rainbow::Vector3f& start, const Rainbow::Vector3f& end);
    void AddBox(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& extents, const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis, const Rainbow::Vector3f& vel);
    void AddCapsule(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& extents, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& vel);

    int SampleVelocityAdaptive(Rainbow::Vector3f& nvel, const Rainbow::Vector3f& vel, const Rainbow::Vector3f& dvel, const float rad, const float height,
        const float vmax, const float horizTime, const ObstacleAvoidanceParams* params, CrowdAgentDebugInfo* debugInfo);

private:


    enum { kPatternMaxDivs = 7 };   // Max numver of adaptive divs.
    enum { kPatternMaxRings = 3 };  // Max number of adaptive rings.

    struct Circle
    {
        Rainbow::Vector2f pos;           // Position of the obstacle
        Rainbow::Vector2f vel;           // Velocity of the obstacle
        float radius;           // Radius of the obstacle
        float weight;           // Weight of obstacle avoidance term
    };

    struct Segment
    {
        Rainbow::Vector2f start, end;
        float priority;
    };

    struct Slab
    {
        Rainbow::Vector2f start, end;
        Rainbow::Vector2f vel;           // Velocity of the obstacle
        Rainbow::Vector2f center, dir;
        float length;
        float radius;
        float weight;
    };


    enum { kCircleCapacity = 8 * 12 + 8 };
    enum { kSegmentCapacity = 8 * 12 + 8 };
    enum { kSlabCapacity = 8 * 12 + 8 };
    enum { kMaxSamples = kPatternMaxDivs * kPatternMaxRings * 4 + 1 }; // Strore samples up to depth 4.

    void Prepare(float vmax, float horizTime, const ObstacleAvoidanceParams* params);
    float ProcessSample(const Rainbow::Vector2f& vcand, const Rainbow::Vector2f& pos, const Rainbow::Vector2f& vel, const Rainbow::Vector2f& dvel, float rad) const;
    float ProcessDynamicObstacles(const Rainbow::Vector2f& pos, const Rainbow::Vector2f& vcand, float rad) const;

    Circle m_Circles[kCircleCapacity];
    Segment m_Segments[kSegmentCapacity];
    Slab m_Slabs[kSlabCapacity];

    ObstacleAvoidanceParams m_Parameters;

    Rainbow::Matrix4x4f m_WorldToLocal;

    float m_Radius;
    float m_Height;

    float m_HorizTime;
    float m_InvHorizTime;
    float m_InvVmax;

    int m_CircleCount;
    int m_SegmentCount;
    int m_SlabCount;
};
