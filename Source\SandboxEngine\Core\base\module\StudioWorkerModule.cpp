/**
* file : StuidoWorkerModule
* func : studio工作模块（studio代码嵌入sandbox 太深，平移过来）
*/
#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
#include "base/module/StudioWorkerModule.h"

#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
#include "SandboxDebuggerDispatcher.h"
#endif

namespace MINIW {

	void StudioWorker::Init()
	{
#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
		MNSandbox::Debugger::DebuggerCreate();
#endif
	}

	void StudioWorker::Release()
	{
#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
		MNSandbox::Debugger::DebuggerDestroy();
#endif
	}
}

#endif