#ifndef __MECHA_SECTION_MESH_H__
#define __MECHA_SECTION_MESH_H__

#include "SectionMesh.h"
namespace Rainbow
{
	class VertexLayout;
}

class EXPORT_SANDBOXENGINE MechaSectionMesh : public SectionMesh //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	MechaSectionMesh(bool dynamic_vb = true);
	~MechaSectionMesh();
	//virtual void render(MINIW::SceneRenderer* pRenderer, const MINIW::ShaderEnvData &envdata);
	void setLight(float _sunlight, float _blocklight);
	virtual void onCreate();
	//tolua_end

	virtual Rainbow::VertexLayout* GetVertexLayout(bool isPackable) override;
private:
	float sunlight;
	float blocklight;
	//std::vector<MergeSubMesh> m_SubMergeMeshes;
}; //tolua_exports

#endif