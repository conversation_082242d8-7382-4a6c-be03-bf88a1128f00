/**
* file : SandboxDebugAction
* func : 沙盒调试能力
* by : chenzh
*/
#include "SandboxDebugAction.h"
#include "SandboxCoreDriver.h"
#include "SandboxGameInput.h"
#include "SandboxGameMapHost.h"
#include "SandboxConfig.h"
#include "SandboxMapSave.h"
#include "SandboxServiceNode.h"
#include "SandboxRemoteMsg.h"
#include "File/FileUtilities.h"
#include "File/FileManager.h"
#include "SandboxCustomBuffer.h"
#include "base/stream/SandboxStreamBuffer.h"
#include "scene/SandboxLoadSave.h"


namespace MNSandbox {


	DebugAction::DebugAction()
		: m_listenThread(this, &DebugAction::OnThreadRun)

	{

		m_thread.Start(m_listenThread);
	}

	DebugAction::~DebugAction()
	{
	}

	void DebugAction::DoSaveDecode()
	{
		//modified by dongjianan 2023.8.25
		Info info;
		info._opt = Opt::SAVE;
		OnSaveDecode(info);

		m_thread.PushTask(info);
	}

	void DebugAction::CloudSaveDecode()
	{
		// 发送到云服执行
		if (Config::GetSingleton().IsRemote())
		{
			RemoteMsg::GetSingleton().SendToHost(SdbSceneManager::ms_globalServiceNodeid, "DevSaveDecode", Config::GetSingleton().GetLocalUin());
		}
	}

	void DebugAction::DoPrintNodeTree()
	{
		//modified by dongjianan 2023.8.25
		Info info;
		info._opt = Opt::CURRENT;
		OnPrintNodeTree("game", info);

		m_thread.PushTask(info);
	}

	void DebugAction::CloudPrintNodeTree()
	{
		// 发送到云服执行
		if (Config::GetSingleton().IsRemote())
		{
			RemoteMsg::GetSingleton().SendToHost(SdbSceneManager::ms_globalServiceNodeid, "DevPrintNodeTree", Config::GetSingleton().GetLocalUin());
		}
	}

	std::string DebugAction::GetReportServiceName(const std::string& path)
	{
		std::string::size_type ret = path.find_first_of('.', 0);
		if (ret != std::string::npos) {
			return path.substr(0, ret - 1 + 1);
		}
		return path;
	}

	void DebugAction::DoReportNodeTree(MINIW::DebugCmdData& cmdData)
	{
		std::string path = "";
		if (cmdData.cmdParam.has<MNJsonStr>("path")) {
			path = cmdData.cmdParam.get<MNJsonStr>("path");
		}

		Info info;
		info._opt = Opt::REPORT;
		info._extended = cmdData;

		if (path == "") { //默认当前场景
			OnPrintNodeTree("workspace", info);
		}
		else {
			OnPrintNodeTree(GetReportServiceName(path), info);
		}
		m_thread.PushTask(info);
	}

	void DebugAction::OnSaveDecode(Info& OutInfo)
	{
		if (!Config::GetSingleton().IsHost())
			return;

		auto gamemap = GetCurrentGameMap().ToCast<GameMapHost>();
		if (!gamemap)
			return;

		const SAVEMODE modes[] = { SAVEMODE::CONFIG, SAVEMODE::NORMAL, SAVEMODE::ASSETCFG, SAVEMODE::LOADING, SAVEMODE::WORKSPACE_BEG };
		const int cnt = sizeof(modes) / sizeof(modes[0]);
		for (int i = 0; i < cnt; ++i)
		{
			std::string fullpath_bin;
			auto mapsave = gamemap->GetMapSave(modes[i]);
			if (mapsave && mapsave->HasStreamData(fullpath_bin))
			{
				AutoRef<Stream> OutStream = SANDBOX_NEW(StreamBuffer);
				if (OutStream->LoadFromFile(fullpath_bin) == SANDBOXERR::OK)
				{
					std::string content;
					Stream2String(OutStream.get(), content);
					if (content.length() > 0)
					{
						OutInfo._print2.insert(std::make_pair(mapsave->GetSaveFilepath(), content));
					}
				}
			}
		}
	}

	void DebugAction::OnSaveDecode(std::map<std::string, MNJsonObject>& out)
	{
		if (!Config::GetSingleton().IsHost())
			return;

#if 0 // todo by chenzh  现在使用二进制了，这个json的已经没有用了
		SAVEMODE modes[] = {
			SAVEMODE::CONFIG,
			SAVEMODE::NORMAL,
			SAVEMODE::ASSETCFG,
			SAVEMODE::LOADING,
			SAVEMODE::WORKSPACE_BEG,
		};
		const int cnt = sizeof(modes) / sizeof(modes[0]);

		auto gamemap = GetCurrentGameMap().ToCast<GameMapHost>();
		if (!gamemap)
			return;

		for (int i = 0; i < cnt; ++i)
		{
			auto mapsave = gamemap->GetMapSave(modes[i]);
			if (!mapsave)
				continue;

			bool hasSaveData = false;
			std::string savepath = mapsave->GetSaveFilepath();
			auto ret = out.insert(std::make_pair(savepath, MNJsonObject()));
			if (ret.second)
			{
				if (!mapsave->LoadToJson(ret.first->second, hasSaveData) || !hasSaveData)
				{
					out.erase(ret.first);
				}
			}
		}
#endif
	}

	void DebugAction::Stream2String(Stream* InStream, std::string& OutString)
	{
		if (InStream)
		{
			long long mapid = 0;
			if (!InStream->ReadNumber<long long>(mapid))
			{
				return;
			}

			if (mapid != 0 && !Config::GetSingleton().IsDevelopGameEnv(false)) // 不是开发环境，需要校验存档
			{
				// 校验地图id
				if (mapid != Config::GetSingleton().GetMapId() && mapid != Config::GetSingleton().GetOwnerMapId()) // 当前地图id 和 原始地图id都可以
				{
					return;
				}
			}

			unsigned serviceCnt = 0;
			if (!InStream->ReadLength<unsigned>(serviceCnt))
			{
				return;
			}

			std::string key;
			AutoRef<Stream> cellStream;
			for (unsigned i = 0; i < serviceCnt; ++i)
			{
				cellStream = SANDBOX_NEW(StreamBuffer);

				SDBSTREAM_LINE(InStream);
				InStream->ReadString(key);
				SDBSTREAM_PUSHLEVEL(InStream);
				InStream->ReadStream(cellStream);
				SDBSTREAM_POPLEVEL(InStream);

				AutoRef<SandboxNode> self = SandboxNode::NewInstance();
				NodeSerialize::ParseNodeFromStream(self, cellStream, ParseFlags(false, false));
			}
#ifdef SDBSTREAM_DEV_OPEN
			InStream->Dev_SaveToString(OutString);
#endif
		}
	}

	void DebugAction::OnThreadSaveDecode(const Info& info)
	{
		//modified by dongjianan 2023.8.25
		for (auto& v : info._print2)
		{
			std::string savepath = v.first + ".debug";
			Rainbow::GetFileManager().SaveToWritePath(savepath, v.second.c_str(), v.second.length());
		}

		/*
		for (auto& v : info._print)
		{
			std::string savepath = v.first + ".debug";
			std::string json = v.second.json();
			Rainbow::GetFileManager().SaveToWritePath(savepath, json.c_str(), json.length());
		}
		*/
	}

	void DebugAction::OnPrintNodeTree(const std::string& service, Info& OutInfo)
	{
		auto gameroot = GetCurrentGameRoot();
		if (!gameroot)
			return;

		if (service == "game")
		{
			// 全部
			auto& services = gameroot->GetAllServices();
			for (auto& v : services)
			{
				OnPrintNodeTree(v.second.StaticToCast<ServiceNode>(), OutInfo);
			}
		}
		else
		{
			auto serviceNode = gameroot->GetService(service);
			if (serviceNode)
			{
				OnPrintNodeTree(serviceNode, OutInfo);
			}
		}
	}
	void DebugAction::OnPrintNodeTree(ServiceNode* service, Info& OutInfo)
	{
		if (!service->IsSerializable())
			return;

		if (OutInfo._opt == Opt::REPORT) {
			MNJsonObject jsonObj;
			if (service->SerializeToJson(jsonObj))
			{
				OutInfo._print.insert(std::make_pair(service->GetName(), jsonObj));
			}
		} else {
			AutoRef<Stream> OutStream = SANDBOX_NEW(StreamBuffer);
			if (service->SerializeToStream(OutStream))
			{
				std::string content;
#ifdef SDBSTREAM_DEV_OPEN
				OutStream->Dev_SaveToString(content);
#endif
				if (content.length() > 0)
				{
					OutInfo._print2.insert(std::make_pair(service->GetName(), content));
				}
			}
		}		
	}

	void DebugAction::OnPrintNodeTree(const std::string& service, std::map<std::string, MNJsonObject>& out)
	{
		auto gameroot = GetCurrentGameRoot();
		if (!gameroot)
			return;

		if (service == "game")
		{
			// 全部
			auto& services = gameroot->GetAllServices();
			for (auto& v : services)
			{
				OnPrintNodeTree(v.second.StaticToCast<ServiceNode>(), out);
			}
		}
		else
		{
			auto serviceNode = gameroot->GetService(service);
			if (serviceNode)
			{
				OnPrintNodeTree(serviceNode, out);
			}
		}
	}

	void DebugAction::OnPrintNodeTree(ServiceNode* service, std::map<std::string, MNJsonObject>& out)
	{
		if (!service->IsSerializable())
			return;

		auto ret = out.insert(std::make_pair(service->GetName(), MNJsonObject()));
		if (ret.second)
		{
			service->SerializeToJson(ret.first->second);
		}
	}

	void DebugAction::OnThreadPrintNodeTree(const Info& info)
	{
		std::string logpath = Config::GetSingleton().GetLogPath() + "/sandbox_debug";
		Rainbow::GetFileManager().DeleteWritePathFileOrDir(logpath.c_str());
		
		//modified by dongjianan 2023.8.25
		for (auto& v : info._print2)
		{
			std::string filepath = ToString(logpath, "/", v.first, "_nodes.log");
			Rainbow::GetFileManager().SaveToWritePath(filepath, v.second.c_str(), v.second.length());
		}
		
		/*
		for (auto& v : info._print)
		{
			std::string json = v.second.json();
			std::string filepath = ToString(logpath, "/", v.first, "_nodes.log");
			Rainbow::GetFileManager().SaveToWritePath(filepath, json.c_str(), json.length());
		}
		*/
	}

	void DebugAction::GetNodeTreePath(MNJsonObject& nodes, MNJsonObject& node, std::string& prefix, std::string& relPath)
	{
		if (!node.has<MNJsonNumber>("SaveIndex")) return;

		int iSaveIndex = (int)node.get<MNJsonNumber>("SaveIndex");

		const std::string& sSaveIndex = ToString(iSaveIndex);
		if(!nodes.has<MNJsonObject>(sSaveIndex)) return;

		const MNJsonObject& reflex = nodes.get<MNJsonObject>(sSaveIndex);
		if (!reflex.has<MNJsonObject>("reflexattrs")) return;
		
		const MNJsonObject& attr = reflex.get<MNJsonObject>("reflexattrs");
		if (!attr.has<MNJsonStr>("Name")) return;

		if (prefix != "") {
			relPath = ToString(prefix, ".", attr.get<MNJsonStr>("Name"));
		} else {
			relPath = attr.get<MNJsonStr>("Name");
		}
	}

	void DebugAction::ExportNodeTreeByPath(MNJsonObject& nodes, MNJsonObject& nodetree, const std::string& targetPath, std::string prefix, bool& retStatus, MNJsonObject& outNode)
	{
		std::string relPath;
		GetNodeTreePath(nodes, nodetree, prefix, relPath);

		if (relPath == targetPath) {
			retStatus = true;
			outNode << "nodetree" << nodetree;
		} else if ((targetPath.find(relPath, 0) == 0) && nodetree.has<MNJsonArray>("children")) {
			auto children = nodetree.get<MNJsonArray>("children");

			for (size_t i = 0; i < children.size(); i++) {
				if (retStatus) return;

				ExportNodeTreeByPath(nodes, children.get<MNJsonObject>((int)i), targetPath, relPath, retStatus, outNode);
			}
		}
	}

	void DebugAction::ExportNodeTreeAttrs(MNJsonObject& nodes, MNJsonObject& nodetree, MNJsonObject& outNode)
	{
		if (!nodetree.has<MNJsonNumber>("SaveIndex")) return;

		int iSaveIndex = (int)nodetree.get<MNJsonNumber>("SaveIndex");
		
		const std::string& sSaveIndex = ToString(iSaveIndex);
		if(!nodes.has<MNJsonObject>(sSaveIndex)) return;
		
		outNode << sSaveIndex << nodes.get<MNJsonObject>(sSaveIndex);

		if (nodetree.has<MNJsonArray>("children")) {
			auto children = nodetree.get<MNJsonArray>("children");

			for (size_t i = 0; i < children.size(); i++) {
				ExportNodeTreeAttrs(nodes, children.get<MNJsonObject>((int)i), outNode);
			}
		}
	}

	void* DebugAction::encodeCmdDataAllocated(MINIW::DebugCmdData& data, unsigned long& dataLen)
	{
		if ((int)data.cmdType < 0) return NULL;

		jsonxx::Object jsonData;
		jsonData << jsonxx::String("cmdFrom") << jsonxx::Number(data.cmdFrom);
		jsonData << jsonxx::String("cmdId") << jsonxx::Number(data.cmdId);
		jsonData << jsonxx::String("cmdRet") << jsonxx::Number(data.cmdRet);
		jsonData << jsonxx::String("cmdType") << jsonxx::Number(data.cmdType);
		jsonData << jsonxx::String("cmdParam") << data.cmdParam;

		std::string jsonStr = jsonData.json_nospace();
		dataLen = jsonStr.length();

		char* point = (char*)malloc(dataLen + 1);
		if (point == NULL) {
			dataLen = 0;
		}
		else {
			strcpy(point, jsonStr.c_str());
		}
		return point;
	}

	void DebugAction::OnThreadReportNodeTree(const Info& info)
	{
		std::string path = "";
		if (info._extended.cmdParam.has<MNJsonStr>("path")) {
			path = info._extended.cmdParam.get<MNJsonStr>("path");
		}
		std::string serviceName = GetReportServiceName(path);

		MINIW::DebugCmdData& cmdData = const_cast<MINIW::DebugCmdData&>(info._extended);

		cmdData.cmdParam << "tree" << MNJsonObject();
		MNJsonObject& treeNode = cmdData.cmdParam.get<MNJsonObject>("tree");
		
		for (auto& v : info._print)
		{
			if (serviceName != "" && v.first != serviceName) continue;

			MNJsonObject& root = const_cast<MNJsonObject&>(v.second);
				
			if(!root.has<MNJsonObject>("nodes")) break;
			if(!root.has<MNJsonStr>("version")) break;
			if(!root.has<MNJsonObject>("nodetree")) break;

			treeNode << "version" << root.get<MNJsonStr>("version");

			bool retStatus = false;
			MNJsonObject& nodes = root.get<MNJsonObject>("nodes");
			MNJsonObject& nodetree = root.get<MNJsonObject>("nodetree");

			if (path == "") {
				GetNodeTreePath(nodes, nodetree, path, path);
				ExportNodeTreeByPath(nodes, nodetree, path, "", retStatus, treeNode);
			}
			else {
				ExportNodeTreeByPath(nodes, nodetree, path, "", retStatus, treeNode);
			}

			if (retStatus && treeNode.has<MNJsonObject>("nodetree")) {
				cmdData.cmdRet = 0;
				ExportNodeTreeAttrs(nodes, treeNode.get<MNJsonObject>("nodetree"), treeNode);
				break;
			}
		}
		std::string fpath = ToString("game.", path);
		treeNode << "path" << fpath;
		cmdData.cmdId++;

		unsigned long len = 0;
		void* pData = encodeCmdDataAllocated(cmdData, len);

		if (pData == NULL) {
			GetGlobalEvent().Emit<std::string, char*, unsigned long>("HttpReportMgr_reportNodeTree", fpath, NULL, 0);
		} else {
			GetGlobalEvent().Emit<std::string, char*, unsigned long>("HttpReportMgr_reportNodeTree", fpath, (char*) pData, len);
		}
	}

	void DebugAction::OnThreadRun(Info& info)
	{
		switch (info._opt)
		{
			case Opt::SAVE:
				OnThreadSaveDecode(info);
				break;
			case Opt::CURRENT:
				OnThreadPrintNodeTree(info);
				break;
			case Opt::REPORT:
				OnThreadReportNodeTree(info);
				break;
		}
	}

	///////////////////////////////////////////////////////////

	AutoRef<DebugAction> GetDebugAction()
	{
		auto coreDriver = SandboxCoreDriver::GetInstancePtr();
		if (coreDriver)
		{
			auto debugaction = coreDriver->GetDebugAction();
			if (debugaction)
				return debugaction;
		}

		return nullptr;
	}

}