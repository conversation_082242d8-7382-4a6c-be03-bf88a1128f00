#pragma once

#include "FrameworkPrefix.h"
#include "geometrySolid/Vector3db.h"
#include "Math/LegacyBounding.h"
//#include "Geometry/AABB.h"
//#include "LegacyModule.h"

namespace MNSandbox {
	namespace GeometrySolid {
		//保留这些注释，需要时再启用
		class BoxBounddb
		{
		public:
			BoxBounddb() : m_isValid(false) {}
			BoxBounddb(const Vector3db &minpos, const Vector3db &maxpos) : m_MinPos(minpos), m_MaxPos(maxpos), m_isValid(true) {}
			BoxBounddb(const BoxBounddb &rhs) : m_MinPos(rhs.m_MinPos), m_MaxPos(rhs.m_MaxPos), m_isValid(rhs.m_isValid) {}

			inline Rainbow::BoxBound ToFloat() const
			{
				return Rainbow::BoxBound(m_MinPos.ToFloat(), m_MaxPos.ToFloat());
			}

			BoxBounddb& operator+=(const Vector3db &Other)
			{
				if (m_isValid)
				{
					#ifdef ASM_X86
					__asm
					{
						mov		eax, [Other]
						mov		ecx, [this]

						movss	xmm3, [eax]Vector3db.x
						movss	xmm4, [eax]Vector3db.y
						movss	xmm5, [eax]Vector3db.z

						movss	xmm0, [ecx]BoxBounddb.m_MinPos.x
						movss	xmm1, [ecx]BoxBounddb.m_MinPos.y
						movss	xmm2, [ecx]BoxBounddb.m_MinPos.z
						minss	xmm0, xmm3
						minss	xmm1, xmm4
						minss	xmm2, xmm5
						movss[ecx]BoxBounddb.m_MinPos.x, xmm0
						movss[ecx]BoxBounddb.m_MinPos.y, xmm1
						movss[ecx]BoxBounddb.m_MinPos.z, xmm2

						movss	xmm0, [ecx]BoxBounddb.m_MaxPos.x
						movss	xmm1, [ecx]BoxBounddb.m_MaxPos.y
						movss	xmm2, [ecx]BoxBounddb.m_MaxPos.z
						maxss	xmm0, xmm3
						maxss	xmm1, xmm4
						maxss	xmm2, xmm5
						movss[ecx]BoxBounddb.m_MaxPos.x, xmm0
						movss[ecx]BoxBounddb.m_MaxPos.y, xmm1
						movss[ecx]BoxBounddb.m_MaxPos.z, xmm2
					}
					#else
					m_MinPos.x = Rainbow::Min(m_MinPos.x, Other.x);
					m_MinPos.y = Rainbow::Min(m_MinPos.y, Other.y);
					m_MinPos.z = Rainbow::Min(m_MinPos.z, Other.z);

					m_MaxPos.x = Rainbow::Max(m_MaxPos.x, Other.x);
					m_MaxPos.y = Rainbow::Max(m_MaxPos.y, Other.y);
					m_MaxPos.z = Rainbow::Max(m_MaxPos.z, Other.z);
					#endif
				}
				else
				{
					m_MinPos = m_MaxPos = Other;
					m_isValid = true;
				}
				return *this;
			}

			BoxBounddb& operator+=(const BoxBounddb& Other)
			{
				if (m_isValid && Other.m_isValid)
				{
					#ifdef ASM_X86
					__asm
					{
						mov		eax, [Other]
						mov		ecx, [this]

						movss	xmm0, [ecx]BoxBounddb.m_MinPos.x
						movss	xmm1, [ecx]BoxBounddb.m_MinPos.y
						movss	xmm2, [ecx]BoxBounddb.m_MinPos.z
						minss	xmm0, [eax]BoxBounddb.m_MinPos.x
						minss	xmm1, [eax]BoxBounddb.m_MinPos.y
						minss	xmm2, [eax]BoxBounddb.m_MinPos.z
						movss[ecx]BoxBounddb.m_MinPos.x, xmm0
						movss[ecx]BoxBounddb.m_MinPos.y, xmm1
						movss[ecx]BoxBounddb.m_MinPos.z, xmm2

						movss	xmm0, [ecx]BoxBounddb.m_MaxPos.x
						movss	xmm1, [ecx]BoxBounddb.m_MaxPos.y
						movss	xmm2, [ecx]BoxBounddb.m_MaxPos.z
						maxss	xmm0, [eax]BoxBounddb.m_MaxPos.x
						maxss	xmm1, [eax]BoxBounddb.m_MaxPos.y
						maxss	xmm2, [eax]BoxBounddb.m_MaxPos.z
						movss[ecx]BoxBounddb.m_MaxPos.x, xmm0
						movss[ecx]BoxBounddb.m_MaxPos.y, xmm1
						movss[ecx]BoxBounddb.m_MaxPos.z, xmm2
					}
					#else
					m_MinPos.x = Rainbow::Min(m_MinPos.x, Other.m_MinPos.x);
					m_MinPos.y = Rainbow::Min(m_MinPos.y, Other.m_MinPos.y);
					m_MinPos.z = Rainbow::Min(m_MinPos.z, Other.m_MinPos.z);

					m_MaxPos.x = Rainbow::Max(m_MaxPos.x, Other.m_MaxPos.x);
					m_MaxPos.y = Rainbow::Max(m_MaxPos.y, Other.m_MaxPos.y);
					m_MaxPos.z = Rainbow::Max(m_MaxPos.z, Other.m_MaxPos.z);
					#endif
				}
				else if (Other.m_isValid)
				{
					*this = Other;
				}
				return *this;
			}

			void setRange(const Vector3db &minpt, const Vector3db &maxpt)
			{
				m_MinPos = minpt;
				m_MaxPos = maxpt;
				m_isValid = true;
			}

			void setCenterExtension(const Vector3db &center, const Vector3db &exten)
			{
				m_MinPos = center - exten;
				m_MaxPos = center + exten;
				m_isValid = true;
			}

			//BoxBounddb transformBy(const Matrix4x4f& M) const;
			//BoxBounddb transformProjectBy(const Matrix4x4f& ProjM) const;
			BoxBounddb expandBy(float w) const
			{
				return BoxBounddb(m_MinPos - Vector3db(w, w, w), m_MaxPos + Vector3db(w, w, w));
			}

			/**
			* compute the bound volume according to input vertex data
			* pvert[0] = x, pvert[1] = y,  pvert[2] = z
			* vertsize ---- in bytes
			*/
			//void setVertexBuffer(const float *vert, size_t vertsize, size_t num);
			Vector3db getCenter() const { return (m_MinPos + m_MaxPos)*0.5; }
			Vector3db getExtension() const { return (m_MaxPos - m_MinPos)*0.5; }
			const Vector3db &getMinPos() const { return m_MinPos; }
			const Vector3db &getMaxPos() const { return m_MaxPos; }

			float getRadiusInDirection(const Vector3db &dir) const
			{
				Vector3db extent = getExtension();
				return Rainbow::Abs(dir.x*extent.x) + Rainbow::Abs(dir.y*extent.y) + Rainbow::Abs(dir.z*extent.z);
			}

			bool isPointIn(const Vector3db& vPos) const
			{
				return (vPos.x > m_MinPos.x && vPos.x<m_MaxPos.x
					&&vPos.y>m_MinPos.y && vPos.y<m_MaxPos.y
					&&vPos.z>m_MinPos.z && vPos.z < m_MaxPos.z);
			}

			bool isPointInXZ(const Vector3db &vPos) const
			{
				return (vPos.x > m_MinPos.x && vPos.x<m_MaxPos.x
					&&vPos.z>m_MinPos.z && vPos.z < m_MaxPos.z);
			}

			bool intersectBoxBound(const BoxBounddb& other) const
			{
				if (m_MinPos.x > other.m_MaxPos.x || other.m_MinPos.x > m_MaxPos.x)
					return false;
				if (m_MinPos.y > other.m_MaxPos.y || other.m_MinPos.y > m_MaxPos.y)
					return false;
				if (m_MinPos.z > other.m_MaxPos.z || other.m_MinPos.z > m_MaxPos.z)
					return false;
				return true;
			}

			//
			bool isBoxBoundIn(const BoxBounddb& box)
			{
				if (box.m_MaxPos.x > m_MaxPos.x ||
					box.m_MinPos.x < m_MinPos.x)
					return false;
				if (box.m_MaxPos.y > m_MaxPos.y ||
					box.m_MinPos.y < m_MinPos.y)
					return false;
				if (box.m_MaxPos.z > m_MaxPos.z ||
					box.m_MinPos.z < m_MinPos.z)
					return false;

				return true;
			}

			//
			bool isBoxBoundOut(const BoxBounddb& box) const
			{
				Vector3db vDis = Abs(box.getCenter() - getCenter());
				Vector3db vRadius = box.getExtension() + getExtension();

				if (vDis.x > vRadius.x || vDis.y > vRadius.y || vDis.z > vRadius.z)
					return true;

				return false;
			}



		public:
			Vector3db m_MinPos;
			Vector3db m_MaxPos;
			bool m_isValid;
		};
	}
}

