#include "ObjFileParseManager.h"
#if ASYNC_OPJ_FILE_PARSE
#include "BlockGeom.h"
#include "GameStatic.h"
#include "MeshCacheManager.h"
#include "SandboxAssetObject.h"
#include "SandboxBodyComponent.h"
#include "SandboxGlobalNotify.h"
#include "SandboxMaterialService.h"
#include "2d/CCNode.h"
#include "File/FileManager.h"

#if PARSE_OBJ_NEW
#include "fast_obj.h"
#else
#include "obj_parser.h"
#endif

using namespace Rainbow;

namespace MNSandbox 
{
	ObjFileParseManager::ObjFileParseManager()
		: ThreadObject(true, true)
		, m_listenGameToClose(this, &ObjFileParseManager::OnGameToClose) 
	{
		GlobalNotify::GetInstance().m_GameToClose.Subscribe(m_listenGameToClose);
	}

	ObjFileParseManager::~ObjFileParseManager() 
	{

	}

	void ObjFileParseManager::OnGameToClose() 
	{
		Finish();
	}

	void ObjFileParseManager::AsyncParse(ObjFileParseJobData jobData) 
	{
		MAIN_THREAD_CHECK_ENABLE;
		if (GetMeshCacheService()) 
		{
			jobData.m_pOutputNonScaledMesh = GetMeshCacheService()->GetMeshCache(jobData.objFilePath);
		}
		PushThreadTask(jobData);
	}

	static MINIW::GameStatic<ObjFileParseManager> sObjFileParseManager(MINIW::kInitManual);
	ObjFileParseManager & ObjFileParseManager::GetInstance() 
	{
		return *sObjFileParseManager.EnsureInitialized();
	}

	#if PARSE_OBJ_NEW
	static void* OpenStream(const char* path, void* user_data)
	{
		if (!user_data)
		{
			return nullptr;
		}
		DataStream* ds = (DataStream*)user_data;
		ds->Seek(0);
		return ds;
	}

	static void CloseStream(void* file, void* user_data)
	{
	}

	static size_t ReadStream(void* file, void* dst, size_t bytes, void* user_data)
	{
		if (!user_data)
		{
			return 0;
		}
		DataStream* ds = (DataStream*)user_data;
		return ds->Read(dst, bytes);
	}

	static unsigned long GetStreamSize(void* file, void* user_data)
	{
		if (!user_data)
		{
			return 0;
		}
		DataStream* ds = (DataStream*)user_data;
		return ds->Size();
	}
	#endif

	void ObjFileParseManager::OnThreadRun(ObjFileParseJobData &job) 
	{
		if (job.m_pOutputNonScaledMesh.IsValid()) 
		{
			return;
		}

		const char* szPath = job.objFilePath.c_str();
		if (!job.ds.IsValid()) 
		{
			job.ds = Rainbow::GetFileManager().OpenFile(szPath, true, Rainbow::kFileOpAll);
		}
		if (!job.ds.IsValid()) 
		{
			Assert(false);
			return;
		}

		#if PARSE_OBJ_NEW
		fastObjCallbacks callbacks;
		callbacks.file_open = OpenStream;
		callbacks.file_close = CloseStream;
		callbacks.file_read = ReadStream;
		callbacks.file_size = GetStreamSize;
		fastObjMesh* fom = fast_obj_read_with_callbacks(szPath, &callbacks, job.ds.Get());
		job.m_meshNeedUpload = fom != nullptr;
		#else
		OPTICK_EVENT();
		MINIW::obj_scene_data objSceneData = {};
		MINIW::ObjParser objParser = {};
		objParser.m_Fopen = OpenStream;
		objParser.m_Fclose = CloseStream;
		objParser.m_Fgets = ReadStream;
		objParser.data_out = &objSceneData;
		objParser.filename = reinterpret_cast<const char *>(job.ds.Get());
		job.m_meshNeedUpload = parse_obj_scene_new(&objParser);
		#endif
		SANDBOX_ASSERT(job.m_meshNeedUpload && "parse obj failed!");
		if (job.m_meshNeedUpload) 
		{
			job.m_pOutputNonScaledMesh = Rainbow::MakeSharePtrWithLabel<Rainbow::Mesh>(kMemMesh);
			#if PARSE_OBJ_NEW
			BodyComponent::ParseMesh(job.m_pOutputNonScaledMesh, fom);
			#else
			BodyComponent::ParseMesh(job.m_pOutputNonScaledMesh, objSceneData);
			#endif
			job.m_pOutputNonScaledMesh->SetKeepVertices(true);
			job.m_pOutputNonScaledMesh->SetKeepIndices(true);
		} else {
			Assert(false);
		}
	}

	void ObjFileParseManager::OnThreadMainCallback(ObjFileParseJobData &data)
	{
		MAIN_THREAD_CHECK_ENABLE;
		OPTICK_EVENT();
		if (data.m_meshNeedUpload) 
		{
			// 上传GPU数据只能在主线程
			data.m_pOutputNonScaledMesh->UploadMeshData(false);
		}

		if (!data.m_pOutputNonScaledMesh.IsValid()){
			Assert(false);
			if (data.callback != nullptr) 
			{
				data.callback(nullptr);
			}
			return;
		}
		if (GetMeshCacheService()) 
		{
			GetMeshCacheService()->SetMeshCache(data.objFilePath, data.m_pOutputNonScaledMesh);	
		}
		if (data.callback != nullptr) 
		{
			data.callback(data.m_pOutputNonScaledMesh);
		}
	}

}

#endif