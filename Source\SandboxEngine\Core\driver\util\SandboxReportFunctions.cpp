#include "SandboxReportFunctions.h"

#include "OgreScriptLuaVM.h"

#include "SandboxStatistics.h"
#include "SandboxStatisticsNode.h"
#include "SandboxAssetObject.h"
#include "SandboxGameMap.h"
#include "SandboxGameStage.h"

namespace MNSandbox
{
	namespace MiniReport
	{
		/*
		* Studio的上报数据
		*/
		static StudioReportData g_StudioReportData;

		void StudioReportEnterSubstage(int stage, int substage, bool isfinish)
		{
			//非studio地图 不上报
			if (!MNSandbox::Config::GetSingleton().IsSandboxMode())
			{
				SANDBOX_LOG("StudioReportStage not SandboxMode stage=", stage, ", substage=", substage);
				return;
			}

			MINIW::ScriptVM::game()->callFunction("StudioReportEnterSubstage", "ii", stage, substage);

			if (isfinish)
			{
				//埋点
				MINIW::ScriptVM::game()->callFunction("StudioReportEnterStage", "i", stage);
			}
		}

		void StudioReportSandbox(int loadingstage, int substage, bool isfinish)
		{
			MINIW::ScriptVM::game()->callFunction("StudioReportLoading", "iib", loadingstage, substage, isfinish);
		}

		void SetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType, int value, bool directValue/* = false*/)
		{
			if (GetCurrentGameMap() && GetCurrentGameMap()->GetGameStage()->GetStage() == GAMESTAGE::RUN)
			{
				if (!directValue)
					g_StudioReportData.Data[DataType] += value;
				else
					g_StudioReportData.Data[DataType] = value;
			}
		}

		int GetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType)
		{
			int value = 0;
			switch (DataType)
			{
			case MNSandbox::MiniReport::ReportDataType::StudioLoadQueueNum:
				{
					auto container = AssetObjectContainer::GetInstance();
					value = container ? container->GetAssetLoadingNum() : 0;
					break;
				}
			default:
				{
					value = g_StudioReportData.Data[DataType];
					break;
				}
			}
			if (DataType != MNSandbox::MiniReport::ReportDataType::StudioPlayerNum && DataType != MNSandbox::MiniReport::ReportDataType::StudioNodeNum)
			{
				ResetStudioReportData(DataType);
			}
			return value;
		}

		void ResetAllStudioReportData()
		{
			memset(g_StudioReportData.Data, 0, sizeof(g_StudioReportData.Data));
		}

		void ResetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType)
		{
			g_StudioReportData.Data[DataType] = 0;
		}
	}
}