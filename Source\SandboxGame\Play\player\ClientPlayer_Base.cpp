#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "PlayerAttrib.h"
#include "VehicleMgr.h"	
#include "ActorBasketBall.h"
#include "ActorBall.h"
#include "PlayerLocoMotion.h"
#include "GameCamera.h"
#include "GameMode.h"
#include "GameNetManager.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "FallComponent.h"
#include "BindActorComponent.h"
#include "ObserverEventManager.h"
#include "LuaInterfaceProxy.h"
#include "BlockBed.h"
#include "DefManagerProxy.h"
#include "SoundComponent.h"
#include "ActorPushSnowBall.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "EffectManager.h"
#include "WorldManager.h"
#include "ClientInfoProxy.h"
#include "ImageMesh.h"
#include "InputInfo.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerCheat.h"
#include "ClientAppProxy.h"
#include "MoveControl.h"
#include "IRecordInterface.h"
#include "SkillComponent.h"
#include "ActorBomb.h"
#include "chunk.h"
#include "SandboxMacros.h"
#include "ChargeJumpComponent.h"
#include "ActorBodySafeHandle.h"
#include "ICloudProxy.h"
#include "IClientGameManagerInterface.h"
#include "EntryGridDataComponent.h"
#include "ModEntryMgr.h"
#include "Utils/thinkingdata/GameAnalytics.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

float ClientPlayer::getFallHurtSubtract()
{
	float extvalues[4];
	getGeniusValue(GENIUS_TWOJUMP, extvalues);

	float value2 = 0;
	float value1 = getLivingAttrib()->getEquipEnchantValue(EQUIP_LEGGING, ENCHANT_FALL_SPEED, ATTACK_ALL, ATTACK_TARGET_ALL, &value2);
	if (value1 > 0)
	{
		//消耗1点耐久度
		getLivingAttrib()->damageEquipItemWithType(EQUIP_LEGGING, 1);
	}

	return extvalues[1] + getLivingAttrib()->getEquipEnchantValue(EQUIP_PIFENG, ENCHANT_FALL_PROTECT)
		+ getLivingAttrib()->getEquipEnchantValue(EQUIP_SHOE, ENCHANT_FALL_PROTECT) + value2;
}

void ClientPlayer::fall(float fall_dist)
{
 	if (fall_dist >= BLOCK_SIZE * GetLuaInterfaceProxy().get_lua_const()->fall_hurt_ext) //这里改成按配置的大小来判断 code_by:huangfubin
	{
		m_pWorld->getEffectMgr()->playBlockDestroyEffect(0, getPosition() - WCoord(0, 5, 0), DIR_POS_Y, 20);
		{//落地造成范围伤害：考虑走爆炸伤害，爆炸ID，指向projectiledef表，高度关联，通过高度计算独立加成值 renjie
			
			if (getLivingAttrib())
			{
				bool haveEn = true; 
				haveEn = getLivingAttrib()->checkEnchant(ENCHANT_FALL_DAMAGE);
				if (haveEn == true)
				{
					float hightTmp = pow( ( fall_dist /  BLOCK_SIZE ) , 0.4 ) - 1 ;//  爆炸伤害计算半径=FLOOR(跌落高度^0.4,0.5)-1，当高度小于6格时不造成任何伤害
					if (fall_dist > 600)
					{
						m_pWorld->createExplosion(this, getPosition(), hightTmp);
					}	
				}
			}
		}
		if (getBody()->getCurShowEquipItemId(EQUIP_PIFENG) == 11591)
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("item.11591.flap", 1.0f, 1.0f);
			}
		}
			
	}
	
	FallComponent::fall_Base(this, fall_dist);
	
	if (fall_dist >= 3 * BLOCK_SIZE && getCatchBall())
	{
		ActorPushSnowBall* ball = dynamic_cast<ActorPushSnowBall*>(getCatchBall());
		if (ball)
		{
			ball->snowCovered();
			ball->playDieEffect();
		}
	}
}


int ClientPlayer::getUin() const
{
	return int(m_ObjId);
}
//for minicode
void ClientPlayer::setUin(long long uin)
{
	GetClientInfoProxy()->getAccountInfo()->Uin = (int)uin;
	SetObjId(uin);
}

const char* ClientPlayer::getNickname()
{
	return m_Nickname.c_str();
}

void ClientPlayer::setVipInfo(const VipInfo& vipinfo)
{
	memcpy(&m_VipInfo, &vipinfo, sizeof(m_VipInfo));

	applyDisplayName();
}

const VipInfo& ClientPlayer::getVipInfo()
{
	return m_VipInfo;
}

ActorBody* ClientPlayer::newActorBody()
{
	ActorBody* actorBody = ENG_NEW(ActorBody)(this);
	actorBody->initPlayer(getBody()->getPlayerIndex(), 0, getCustomjson());
	if (m_strCustomjson.size())
	{
		actorBody->setBodyType(3);
		//LOG_INFO("addAvatarPartModel loc7:%p ", actorBody);
		actorBody->addDefaultAvatar();
		MINIW::ScriptVM::game()->callFunction("ClientGetRoleAvatarInfo", "is", getUin(), getCustomjson());
	}

	return actorBody;
}

bool ClientPlayer::isComboWeaponInHand()
{
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
	return tooldef && tooldef->SubType > 0 && tooldef->SubType != 2; //非盾
}

int ClientPlayer::getCurToolID()
{
#ifdef BUILD_MINI_EDITOR_APP 
	if (m_nCurToolId > 0)
	{
		return m_nCurToolId;
	}
#endif
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isVehicleController()) return 0;
	if (g_pPlayerCtrl == this && isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		ClientPlayer* target = g_pPlayerCtrl->getToSpectatorPlayer();
		if (target)
		{
			PlayerAttrib* attrib = target->getPlayerAttrib();
			return attrib->getEquipItem(EQUIP_WEAPON);
		}
	}

	PlayerAttrib* attrib = getPlayerAttrib();
	return attrib->getEquipItem(EQUIP_WEAPON);
}

int ClientPlayer::getCurDorsumID()
{
	return getPlayerAttrib()->getEquipItemWithType(EQUIP_PIFENG);
}

bool ClientPlayer::canControl()
{
	if (!GetWorldManagerPtr())
		return true;

	return m_CanControl;
}

void ClientPlayer::setCanControl(bool b)
{
	if (!GetWorldManagerPtr())
		return;
	if (hasUIControl())
		m_CanControl = b;

	if (m_SyncCtrl !=  b && m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_PlayerCanControlHC playerCanControlHC;
		playerCanControlHC.set_cancontrol(b);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_CANCONTROL_HC, playerCanControlHC);
	}
	m_SyncCtrl = b;
}

bool ClientPlayer::isSleepAndNeedHide()
{
	if (isSleeping() || isRestInBed())
	{
		WCoord blockCoor = CoordDivBlock(getPosition());
		return BedLogicHandle::IsSleepNeedHide(m_pWorld, blockCoor);
	}
	return false;
}

void ClientPlayer::setScale(float s)
{
	if (!getBody())
		return;

	if (s <= 0.00001f) // С��0��ʾˢ�� EPSINON
	{
		s = getScale();
	}

	float scale = 1.0f + getPlayerAttrib()->getModAttrib(MODATTR_ACTOR_SCALE);
	getBody()->setScale(s);
	getBody()->setRealScale(scale*s*getCustomScale());

#ifdef _UPDATE_BOUND_BY_SCALE_
	// �޸���ײ��͹����ж���
	updateBound();
	updateAttackBound();
#endif
}

void ClientPlayer::syncCustomScale()
{
	// ������ͬ�����ͻ�
	if (m_pWorld)
	{
		PB_PlayerScaleHC playerScaleHC;
		playerScaleHC.set_scale(getCustomScale());
		playerScaleHC.set_uin(getUin());

		if (!m_pWorld->isRemoteMode()) {
			GetGameNetManagerPtr()->sendBroadCast(PB_PLAYER_ATTR_SCALE_HC, playerScaleHC);
		}
		else {
			GetGameNetManagerPtr()->sendToHost(PB_PLAYER_ATTR_SCALE_CH, playerScaleHC);
		}
	}
}

float ClientPlayer::getScale()
{
	if (getBody())
		return getBody()->getBodyScale();

	return 1.0f;
}

int ClientPlayer::getObjType() const
{
	return OBJ_TYPE_ROLE;
}

int ClientPlayer::getEyeHeight()
{
	auto RidComp = getRiddenComponent();
	if (isSleeping() || isRestInBed()) return 0;
	else if (getSitting()) return 120;
	else if (RidComp && RidComp->getRidingActorObjId() > 0) return 120;
	//else return 162;
	else return 142;
}

int ClientPlayer::getChestHeight()
{
	auto RidComp = getRiddenComponent();
	if (isSleeping() || isRestInBed())
		return 10;
	else if (getSitting())
		return 90;
	else if (RidComp && RidComp->getRidingActorObjId() > 0)
		return 100;
	else
		return 120;
}

bool ClientPlayer::isInvulnerable(ClientActor *attacker)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode() || isInSpectatorMode() || GetClientInfoProxy()->IsCurrentUserOuterChecker())
	{
		return true;
	}

	ActorLiving *living = dynamic_cast<ActorLiving *>(attacker);
	if (living && living->isSameTeam(this)) return false;
	else
	{
		return getInvulnerable();
	}
}

int ClientPlayer::getOxygenUseInterval()
{
	float extvalue[4];
	getGeniusValue(GENIUS_SURVIVE, extvalue);

	return int(60 * (1.0f + extvalue[0]));
}

float ClientPlayer::getOxygenUseRate()
{
#if 0
	if (getPlayerAttrib()->isNewStatus())
	{
		if (getPlayerAttrib()->hasBuff(65) || getPlayerAttrib()->hasBuff(66) || getPlayerAttrib()->hasBuff(67))
		{
			return 2.0f;
		}
		else
		{
			return 1.0f;
		}
	}
	else
	{
		int flags = (int)getPlayerAttrib()->getModAttrib(MODATTR_OXYGEN_SUPPLY);

		if (flags / 10000 > 0) return 2.0f;

		if (m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR)
		{
			return (flags / 100) != 0 ? 0 : 1.0f;
		}
		else return (flags % 100) != 0 ? 0 : 1.0f;
	}
#else
	if (getPlayerAttrib()->hasBuff(65) || getPlayerAttrib()->hasBuff(66) || getPlayerAttrib()->hasBuff(67))
	{
		return 2.0f;
	}
	else
	{
		return 1.0f;
	}
#endif
}

void ClientPlayer::newAdNpcAddExp(int op, int starNum)
{
	if (hasUIControl() && MINIW::ScriptVM::game())
	{
		int result = 0;
		int exp = starNum * EXP_STAR_RATIO;
		PlayerAttrib *playerAttr = getPlayerAttrib();
		if (playerAttr)
		{
			int expBefore = playerAttr->getExp();
			if (exp > 0 || (exp < 0 && expBefore >= abs(exp)))
			{
				playerAttr->addExp(exp);
				if ((expBefore + exp) == playerAttr->getExp())
				{
					result = 1;
				}
			}
		}
		// �������Ǳ�����/�۳����
		MINIW::ScriptVM::game()->callFunction("AddExpResult", "ii", op, result);
	}
}

void ClientPlayer::AddStar(int starNum)
{
	int exp = starNum * EXP_STAR_RATIO;
	PlayerAttrib *playerAttr = getPlayerAttrib();
	if (!playerAttr)
		return;
	playerAttr->addExp(exp);
}

void ClientPlayer::starConvert(int num)
{
	if (!m_pWorld->isRemoteMode())
	{
		getPlayerAttrib()->addExp(EXP_STAR_RATIO*num);

		if (!hasUIControl())
		{
			PB_SpecialItemUseHC specialItemUseHC;
			specialItemUseHC.set_itemid(-num);
			specialItemUseHC.set_itemnum(num);

			GetGameNetManagerPtr()->sendToClient(getUin(), PB_SPECIALITEM_USE_HC, specialItemUseHC);
		}
	}
}

void ClientPlayer::applyEquips(EQUIP_SLOT_TYPE t)
{
	if (m_UIViewBody && !GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		m_UIViewBody = nullptr;
	}

	getLivingAttrib()->applyEquips(getBody(), t);

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (m_UIViewBody && m_UIViewBody->getIsAttachModelView())
#else
	if (m_UIViewBody && m_UIViewBody->getAttachedModelView())
#endif
	{
		getLivingAttrib()->applyEquips(m_UIViewBody, t);
	}

	if (t == EQUIP_WEAPON || t == MAX_EQUIP_SLOTS)
	{
		changeOPWay();
	}

	// 埋点上报
	int itemid = getLivingAttrib()->getEquipItemWithType(t);
	GameAnalytics::TrackEvent("player_equip", {
		{"item_id",itemid},
		{"equip_pos",t},
		{"op", "set"}
	});

	if (g_pPlayerCtrl == this && EQUIP_WEAPON == t)
	{
		if (ITEM_VEHICLE_LINK_TOOL != itemid)
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_clearVehicleEndLink",
				SandboxContext(nullptr));
		}
	}
	//添加技能
	//技能组件没有的话,看装备配置里是否有技能信息,有的话就创建一个,
	if (!getSkillComponent())
	{
		auto def = GetDefManagerProxy()->getItemDef(itemid);
		if (def && !def->Addskills.empty())
		{
			CreateComponent<SkillComponent>("SkillComponent");
		}
	}
	if (getSkillComponent())
	{
		getSkillComponent()->addSkillByEquip(itemid, t);
	}
}

void ClientPlayer::applyEquipsOnShortcut(EQUIP_SLOT_TYPE t, bool takeoffAble)
{
	if (m_UIViewBody && !GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		m_UIViewBody = nullptr;
	}

	getLivingAttrib()->applyEquips(getBody(), t, takeoffAble);

#if MODELVIEW_DECOUPLE_FROM_ACTORBODY
	if (m_UIViewBody && m_UIViewBody->getIsAttachModelView())
#else
	if (m_UIViewBody && m_UIViewBody->getAttachedModelView())
#endif
	{
		getLivingAttrib()->applyEquips(m_UIViewBody, t, takeoffAble);
	}

	if (t == EQUIP_WEAPON || t == MAX_EQUIP_SLOTS)
	{
		changeOPWay();
	}

	int itemid = getLivingAttrib()->getEquipItemWithType(t);
	if (g_pPlayerCtrl == this && EQUIP_WEAPON == t)
	{
		if (ITEM_VEHICLE_LINK_TOOL != itemid)
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_clearVehicleEndLink",
				SandboxContext(nullptr));
		}
	}
	//添加技能
	//技能组件没有的话,看装备配置里是否有技能信息,有的话就创建一个,
	if (!getSkillComponent())
	{
		auto def = GetDefManagerProxy()->getItemDef(itemid);
		if (def && !def->Addskills.empty())
		{
			CreateComponent<SkillComponent>("SkillComponent");
		}
	}
	if (getSkillComponent())
	{
		getSkillComponent()->addSkillByEquip(itemid, t);
	}
}

void ClientPlayer::onDisApplyEquips(int fromid, int toindex, int itemid, int num)
{
	gridChangeOnTrigger(fromid, itemid, num); //通知事件 装备栏改变
	// 观察者事件接口:对应功能:卸载装备
	ObserverEvent_PlayerItem obevent(getUin(), itemid, 1, -1, fromid);
	GetObserverEventManager().OnTriggerEvent("Player.EquipOff", &obevent);

	// 埋点上报
	GameAnalytics::TrackEvent("player_equip", {
		{"item_id",itemid},
		{"equip_pos",toindex},
		{"op", "remove"}
	});

	onDisApplyEquips(itemid);
}

void ClientPlayer::onDisApplyEquips(int itemid)
{
	if (GetWorldManagerPtr())
	{
		LivingAttrib* pLivingAttr = getLivingAttrib();

		if (pLivingAttr)
		{
			//卸载装备时, 卸载对应的状态
			pLivingAttr->removeEquipBuff(itemid);
			pLivingAttr->removeToolBuff(itemid);
			pLivingAttr->removeEquipEntryBuff(itemid);
			//防火服
			//if (itemid == ITEM_FIRESAFETY_PACK && m_pWorld && m_pWorld->getCurMapID() == MAPID_LIEYANSTAR)
			//{
			//	pLivingAttr->removeBuff(HOT_BUFF, false);
			//	pLivingAttr->addBuff(HOT_BUFF, 1);
			//}
		}
	}
	////移除技能
	//auto skillComp = getSkillComponent();
	//if (skillComp)
	//{
	//	skillComp->removeSkillByEquip(itemid);
	//}
}

void ClientPlayer::onApplyEquips(int itemid, int grid_index)
{
	if (GetWorldManagerPtr())
	{
		const ToolDef *def = GetDefManagerProxy()->getToolDef(itemid);
		if (def)
		{
			int type = def->Type; // 装备类型(8->头,9->胸甲,10->腿,11->鞋子,16->背部)
			//int item_grid_index = ToolType2EquipIndex(type);
			//if (item_grid_index == 0 || item_grid_index != grid_index)  // 防止穿戴与栏位类型不匹配的装备 2022.10.10 by huanglin
			//	return;
			bool isEquid = false;
			auto slotType = def->getSlotType();
			if (type == 16 || type == 30) { slotType = EQUIP_PIFENG; }  // 背部对应EQUIP_PIFENG
			if (slotType != EQUIP_NONE)
			{
				isEquid = true;
				gridChangeOnTrigger(grid_index/*EQUIP_START_INDEX + (type - 8)*/, itemid, 1);  //通知事件 装备栏改变
				applyEquipsOnTrigger(itemid, slotType/*static_cast<EQUIP_SLOT_TYPE>(type - 8)*/);
			}

			LivingAttrib* pLivingAttr = getLivingAttrib();

			if (pLivingAttr)
			{
				//装载装备时, 装载对应的状态
				if (!pLivingAttr->hasEquipBuff(itemid)) // 检测当前身上是否已经有这个buff了，如果没有才加上 codeby chenzihang
					pLivingAttr->addEquipBuff(itemid);

				int grid_slot_index = -1;
				//装在tool时
				if (!pLivingAttr->hasToolBuff(itemid)) // 检测当前身上是否已经有这个buff了，如果没有才加上 冒险0623 codeby zhangyusong
				{
					if (isEquid)
					{
						grid_slot_index = grid_index - EQUIP_START_INDEX;
						if (getEquipItemDuration((EQUIP_SLOT_TYPE)(grid_slot_index)/*static_cast<EQUIP_SLOT_TYPE>(type - 8)*/) > 0 || def->Duration == -1) //添加逻辑,装备耐久-1时，不需要耐久即可生效 20240718 codeby yangjia
							pLivingAttr->addToolBuff(itemid);
					}
					else
						pLivingAttr->addToolBuff(itemid);
				}

				//装备词条状态
				doEquipModEntry(itemid, true);
				pLivingAttr->addEquipEntryBuff(itemid, 1);

				////防火服
				//if (itemid == ITEM_FIRESAFETY_PACK && m_pWorld && m_pWorld->getCurMapID() == MAPID_LIEYANSTAR)
				//{
				//	pLivingAttr->removeBuff(HOT_BUFF, false);
				//	pLivingAttr->addBuff(HOT_BUFF, 2);
				//}
			}
			updateTaskSysProcess(TASKSYS_WEAR_EQUIP, itemid);
		}
	}
}


void ClientPlayer::doEquipModEntry(int itemid, bool enable)
{
	std::vector<ModEntryCreate> entrys;
	const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(itemid);
	if (!toolDef) return;

	int type = toolDef->Type; // 装备类型(8->头,9->胸甲,10->腿,11->鞋子,16->背部)
	BackPackGrid* grid = nullptr;
	auto slotType = toolDef->getSlotType();
	if (slotType != EQUIP_NONE)
	{
		grid = getEquipGridWithType(slotType);
	}
	// if (type == 16 || type == 30) { type = 12; }  // 背部对应EQUIP_PIFENG
	// if (type >= 8 && type <= 12)
	// {
	// 	grid = getEquipGrid(static_cast<EQUIP_SLOT_TYPE>(type - 8));
	// }
	if (!grid) return;

	EntryGridDataComponent* entryGridDataComponent = dynamic_cast<EntryGridDataComponent*>(grid->getEntryDataComponent());
	if (!entryGridDataComponent) return;
	entrys = entryGridDataComponent->GetModEntryCreateVector();

	if (entrys.empty()) return;

	for (auto& entry : entrys)
	{
		GetModEntryMgr().SetModEntryTrig(enable, getObjId(), entry.entryID);
	}
}

void ClientPlayer::updateEquips()
{
	for (int i = 0; i < EQUIP_WEAPON; ++i)
	{
		int itemid = getPlayerAttrib()->getEquipItem((EQUIP_SLOT_TYPE)i);

		if (itemid > 0)
		{
			applyEquips((EQUIP_SLOT_TYPE)i);
			onApplyEquips(itemid, getPlayerAttrib()->equipSlot2Index((EQUIP_SLOT_TYPE)i));
		}
	}
}

//等级经验
bool ClientPlayer::SetLevelMode(int nSumExp, int nCurExp, int nCurLevel)
{
	PlayerAttrib* playerAttr = this->getPlayerAttrib();

	if (playerAttr)
	{
		PlayerLevelMode* pLevelMode = playerAttr->m_pLevelMode;
		if (pLevelMode)
		{
			pLevelMode->SetSumExp(nSumExp);
			pLevelMode->SetCurExp(nCurExp);
			pLevelMode->SetCurLevel(nCurLevel);
		}
	}

	return true;
}

void ClientPlayer::syncLevelMode()
{
	PlayerAttrib* playerAttr = this->getPlayerAttrib();

	if (playerAttr)
	{
		PlayerLevelMode* pLevelMode = playerAttr->m_pLevelMode;
		if (pLevelMode)
		{
			if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				PB_PlayerLevelModeHC playerLevelModeHC;
				playerLevelModeHC.set_sumexp(pLevelMode->GetSumExp());
				playerLevelModeHC.set_curexp(pLevelMode->GetCurExp());
				playerLevelModeHC.set_curlevel(pLevelMode->GetCurLevel());

				GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_LEVELMODE_HC, playerLevelModeHC);
			}
		}
	}
}

int ClientPlayer::getSurviveDay(int mapid/* =-1 */)
{
	if (mapid < -1 && mapid >= MAX_MAP) return 1;

	if (mapid == -1 && GetWorldManagerPtr())
	{
		return GetWorldManagerPtr()->getWorldTime() / TICKS_ONEDAY + 1;
	}
	else
		return m_WorldTimes[mapid] / TICKS_ONEDAY + 1;

}

void ClientPlayer::setWorldTime(int mapid, int t)
{
	m_WorldTimes[mapid] = t;
}

void ClientPlayer::sendWorldTimesUpdate()
{
	PB_WorldTimesHC worldTimesHC;
	for (int i = 0; i < MAX_MAP; i++)
	{
		worldTimesHC.add_times(m_WorldTimes[i]);
	}
	GetGameNetManagerPtr()->sendToClient(getUin(), PB_WORLD_TIMES_HC, worldTimesHC);
}

bool ClientPlayer::isHost()
{
	int mp = GetClientInfoProxy()->getMultiPlayer();

	if (mp == GAME_NET_MP_GAME_NOT_INIT) return true;
	else if (mp == GAME_NET_MP_GAME_HOST_AND_CLIENT && hasUIControl())
	{
		return true;
	}
	else return false;
}

bool ClientPlayer::isRemote()
{
	return m_pWorld && m_pWorld->isRemoteMode();
}

bool ClientPlayer::isPlayerControl()
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(this);
	return player != nullptr ? true : false;
}

void ClientPlayer::setSpectatorMode(PLAYER_SPECTATOR_MODE spectmod)
{
	m_nSpectatormode = spectmod;
	if (m_nSpectatormode > SPECTATOR_MODE_NONE)
	{
		static_cast<PlayerLocoMotion*>(getLocoMotion())->detachPhysActor();
	}
	else
	{
		static_cast<PlayerLocoMotion*>(getLocoMotion())->attachPhysActor();
		if (this == g_pPlayerCtrl)
		{
			setFlying(false);
		}
	}
	if (g_WorldMgr)
		g_WorldMgr->signChangedToSync(getObjId(), BIS_INSPECTATOR);
}

bool ClientPlayer::canBePushed()
{
	if (isInSpectatorMode())
		return false;
	return ActorLiving::canBePushed();
}

void ClientPlayer::setOPWay(int way)
{
	if (m_OPWay == way) return;

	m_OPWay = way;
	if (way == PLAYEROP_WAY_FOOTBALLER)
	{
		//if (nullptr != m_StateController)
		//{
		//	toActionState("ToFootball");
		//	if (g_pPlayerCtrl->getViewMode() < CAMERA_TPS_OVERLOOK)
		//	{
		//		g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
		//	}
		//}
		if (!m_pWorld->isRemoteMode())
		{
			//切换成足球模式 解绑
			auto* ball = getCatchBall();
			if (ball && !dynamic_cast<ActorBall*>(ball))
			{
				auto bindAComponent = ball->getBindActorCom();
				if (bindAComponent)
				{
					bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
				}
			}
			getLivingAttrib()->removeBuff(PUSHSNOWBALLWAY_BUFF);
			getLivingAttrib()->removeBuff(BASKETBALLWAY_BUFF);
			getLivingAttrib()->addBuff(FOOTBALLWAY_BUFF, 1);
		}
	}
	else if (way == PLAYEROP_WAY_BASKETBALLER) 
	{
		//if (nullptr != m_StateController)
		//{
		//	toActionState("ToBasketball");
		//	if (g_pPlayerCtrl->getViewMode() < CAMERA_CUSTOM_VIEW)
		//	{
		//		g_pPlayerCtrl->setViewMode(CAMERA_TPS_BACK_2);
		//	}
		//	if (!g_pPlayerCtrl->checkIfDataUpload(Upload_BasketBallWear_Use))
		//	{
		//		std::stringstream sParam1;
		//		sParam1 << m_pWorld->getOWID();

		//		g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30031, "", g_pPlayerCtrl->getCurWorldType(), "param_to_str", sParam1.str().c_str());
		//		g_pPlayerCtrl->setUploadStatus(Upload_BasketBallWear_Use);
		//	}
		//}

		if (!m_pWorld->isRemoteMode()) {
			//切换成非足球模式 解绑
			auto* ball = getCatchBall();
			if (ball && !dynamic_cast<ActorBasketBall*>(ball))
			{
				auto bindAComponent = ball->getBindActorCom();
				if (bindAComponent)
				{
					bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
				}
			}
			getLivingAttrib()->removeBuff(PUSHSNOWBALLWAY_BUFF);
			getLivingAttrib()->removeBuff(FOOTBALLWAY_BUFF);
			getLivingAttrib()->addBuff(BASKETBALLWAY_BUFF, 1);
		}
	}
	else if (way == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		if (!m_pWorld->isRemoteMode())
		{
			auto* ball = getCatchBall();
			if (ball && !dynamic_cast<ActorPushSnowBall*>(ball))
			{
				{
					auto bindAComponent = ball->getBindActorCom();
					if (bindAComponent)
					{
						bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
					}
				}
			}
			getLivingAttrib()->removeBuff(FOOTBALLWAY_BUFF);
			getLivingAttrib()->removeBuff(BASKETBALLWAY_BUFF);
			getLivingAttrib()->addBuff(PUSHSNOWBALLWAY_BUFF, 1);

			//测试生成雪球	
		/*	Rainbow::Vector3f dir = Yaw2FowardDir(getLocoMotion()->m_RotateYaw);
			float distance = getLocoMotion()->m_BoundSize + BLOCK_SIZE;
			WCoord pos = getLocoMotion()->getPosition() + WCoord(dir * (distance));
			ActorPushSnowBall::create(m_pWorld, pos.x, pos.y, pos.z, 0, 0, 0, 2);*/
		}
	}
	else
	{
		if (!m_pWorld->isRemoteMode())
		{
			//切换成非足球模式 解绑
			auto* ball = getCatchBall();
			if (ball)
			{
				{
					auto bindAComponent = ball->getBindActorCom();
					if (bindAComponent)
					{
						bindAComponent->setBindInfo(-getObjId(), WCoord(0, 0, 0));
					}
				}
			}
			getLivingAttrib()->removeBuff(PUSHSNOWBALLWAY_BUFF);
			getLivingAttrib()->removeBuff(FOOTBALLWAY_BUFF);
			getLivingAttrib()->removeBuff(BASKETBALLWAY_BUFF);

			//切换成非佩戴重力手套模式,解绑,清除动作和特效
			ClientActor* actor = getCatchGravityActor();
			if (actor)
			{
				doPutGravityActor(actor);
				onOperateEnded();
				stopMotion(30);
			}
		}

		//if (hasUIControl())
		//{
		//	if (g_pPlayerCtrl->getViewMode() < CAMERA_TPS_OVERLOOK && !isShapeShift())
		//	{
		//		if (g_pPlayerCtrl->m_pCamera)
		//		{
		//			g_pPlayerCtrl->m_pCamera->setDistMultiply(1.0f);
		//		}
		//		XMLNode node = Root::getSingleton().m_Config.getRootNode().getChild("GameData");
		//		if (!node.isNull())
		//		{
		//			XMLNode child = node.getChild("Settinig");
		//			if (!child.isNull())
		//			{
		//				int mode = child.attribToInt("view") - 1;
		//				切换视角时进行地图设置判断 by：Jeff
		//				if (g_WorldMgr && g_WorldMgr->isGameMakerRunMode())
		//				{
		//					int defcam = (int)g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA);
		//					if (9 == defcam || 8 == defcam)
		//					{
		//						mode = CAMERA_TPS_BACK_2;
		//					}
		//					else if (defcam >= 3 && defcam <= 5)
		//					{
		//						mode = defcam - 3;
		//					}
		//					else if (6 == defcam || 7 == defcam)
		//					{
		//						mode = defcam - 2;
		//					}
		//					//else if (10 == defcam || 11 == defcam)
		//					//{
		//					//	mode = defcam - 6;
		//					//}
		//				}
		//				g_pPlayerCtrl->setViewMode(mode);
		//			}
		//		}
		//	}
		//}
	}
	//if (hasUIControl())
	//{
	//	MINIW::ScriptVM::game()->callFunction("UpdateUIBtnByOPWayChange", "");
	//}
	updateBallEffect();
}

void ClientPlayer::changeOPWay()
{
	if (!m_pWorld) return;
	if (!getLivingAttrib()) return;
	int iToolId = getLivingAttrib()->getEquipItem(EQUIP_WEAPON);
	auto RidComp = getRiddenComponent();
	if (iToolId == ITEM_FOOTBALLWEAR && !(RidComp && RidComp->isRiding()))
		setOPWay(PLAYEROP_WAY_FOOTBALLER);
	else if (iToolId == ITEM_GRAVITYGUN && !(RidComp && RidComp->isRiding()))
		setOPWay(PLAYEROP_WAY_GRAVITYGUN);
	else if (iToolId == ITEM_BASKETBALLWEAR && !(RidComp && RidComp->isRiding()))
		setOPWay(PLAYEROP_WAY_BASKETBALLER);
	else if (iToolId == ITEM_WINTER_GLOVES && !(RidComp && RidComp->isRiding()))
		setOPWay(PLAYEROP_WAY_PUSHSNOWBALL);
	else
		setOPWay(PLAYEROP_WAY_NORMAL);

	if (!m_pWorld->isRemoteMode())
	{
		const ToolDef* def = GetDefManagerProxy()->getToolDef(iToolId);
		if (def && def->isFishingRod())
		{
			getLivingAttrib()->addBuff(FISHING_BUFFER, 1);
		}
		else
		{
			getLivingAttrib()->removeBuff(FISHING_BUFFER);
		}
	}
}

bool ClientPlayer::isVisible()
{
	if (g_pPlayerCtrl)
	{
		if ((!g_pPlayerCtrl->isInSpectatorMode()) && isInSpectatorMode())
			return false;
		if (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FOLLW)
			return false;
	}

	return true;
}

bool ClientPlayer::isDead()
{
	if (isInSpectatorMode())
	{
		return true;
	}
	else if (getAttrib())
	{
		return static_cast<PlayerAttrib*>(getAttrib())->isDead();
	}
	else return false;
}

// 判断是否装备了某个具体装备
bool ClientPlayer::isEquipByResID(int resid)
{
	if (resid <= 0) { return false; }
	for (int i = 0; i < EQUIP_WEAPON; ++i)
	{
		int itemid = getPlayerAttrib()->getEquipItem(static_cast<EQUIP_SLOT_TYPE>(i));
		if (itemid == resid)
		{
			return true;
		}
	}
	return false;
}

void ClientPlayer::rotateCamera(float yaw, float pitch)
{
	if (hasUIControl())
	{
		g_pPlayerCtrl->m_pCamera->setRotate(yaw, pitch);
	}
	else if (!m_pWorld->isRemoteMode())
	{
		PB_PlayerCameraRotateHC playerCameraRotateHC;
		playerCameraRotateHC.set_pitch(pitch);
		playerCameraRotateHC.set_yaw(yaw);

		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_CAMERAROTATE_HC, playerCameraRotateHC);
	}
}

void ClientPlayer::changeViewMode(int mode, bool lock, bool clientMode)
{
	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->m_RuleMgr)
		return;

	if (hasUIControl())
	{
		g_pPlayerCtrl->setViewMode(mode);
		GetWorldManagerPtr()->m_RuleMgr->setLockViewMode(lock);
	}
	else if (!m_pWorld->isRemoteMode())
	{
		PB_PlayerChangeViewModeHC playerChangeViewModeHC;
		playerChangeViewModeHC.set_viewmode(mode);
		playerChangeViewModeHC.set_lock(lock);
		playerChangeViewModeHC.set_clientmode(clientMode);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_CHANGEVIEWMODE_HC, playerChangeViewModeHC);

	}
}

void ClientPlayer::syncAttr(int attrType, float val)
{
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		return;
	}
	LOG_INFO("syncAttr to client: uin = %d | attrType = %d | value = %.2f", getUin(), attrType, val);
	PB_PlayerSetAttrHC playerSetAttrHC;
	playerSetAttrHC.set_attrtype(attrType);
	playerSetAttrHC.set_val(val);
	if (!IsOffline())
	{
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_SETATTR_HC, playerSetAttrHC);
	}
}

void ClientPlayer::setFreezing(int freezingFlag)
{
	m_nFreezingFlag = freezingFlag;
	if (m_pWorld && !m_pWorld->isRemoteMode() && !hasUIControl())
	{
		PB_PlayerFreezingHC playerFreezingHC;
		playerFreezingHC.set_freezingflag(freezingFlag);
		GetGameNetManagerPtr()->sendToClient(getUin(), PB_PLAYER_FREEZING_HC, playerFreezingHC);
	}
}

bool ClientPlayer::isPlayerMove()
{
	LivingLocoMotion *loc = static_cast<LivingLocoMotion *>(getLocoMotion());
	if (loc->m_MoveForward != 0 || loc->m_MoveStrafing != 0 || loc->m_TickPosition.m_LastTickPos.x != loc->m_Position.x || loc->m_TickPosition.m_LastTickPos.z != loc->m_Position.z)
	{
		return true;
	}
	return false;
}

void ClientPlayer::ReviveCostExp()
{
	if (getPlayerAttrib()->getExp() < 1 * EXP_STAR_RATIO)
	{
		return;
	}

	//消耗星星
	getPlayerAttrib()->addExp(-1 * EXP_STAR_RATIO);
}

void ClientPlayer::createUIViewActorBody()
{
//	if (m_UIViewBody)
	if (GetActorBodySafeHandle()->IsVaild(m_UIViewBody))
	{
		m_UIViewBody->onLeaveWorld();
		OGRE_DELETE(m_UIViewBody);
	}
	if (!m_UIViewBody)
	{
		m_UIViewBody = ENG_NEW(ActorBody)(this);
	}
}

long long ClientPlayer::getOWID()
{
	if (m_pWorld)
		return m_pWorld->m_OWID;
	else
		return 0;
}

void ClientPlayer::resetRound()
{
	if (isDead())
	{
		revive(3);
	}
	else
	{
		auto RidComp = getRiddenComponent();
		if (RidComp && RidComp->isRiding())
			mountActor(NULL);
		if (m_pWorld->getCurMapID() == 1)
			teleportMap(0);
		getLocoMotion()->m_Motion.x = 0;
		getLocoMotion()->m_Motion.y = 0;
		getLocoMotion()->m_Motion.z = 0;

		getLivingAttrib()->clearrevive(2);
		//GetWorldManagerPtr()->m_RuleMgr->onPlayerInit(this, false);
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
			GetWorldManagerPtr()->m_RuleMgr->initPlayerDir(this);
	}

	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
		getLivingAttrib()->addBuff(FOOTBALLWAY_BUFF, 1);
	else if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
		getLivingAttrib()->addBuff(BASKETBALLWAY_BUFF, 1);
	else if (getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
		getLivingAttrib()->addBuff(PUSHSNOWBALLWAY_BUFF, 1);
}

void ClientPlayer::OnGainedExp(int nExp)
{
	// 无经验模式，不能获取经验
	if (g_WorldMgr && g_WorldMgr->getBaseSettingManager())
	{
		if (g_WorldMgr->getBaseSettingManager()->isNoExpMode())
		{
			return;
		}
	}

	if (m_PlayerAttrib)
	{
		LivingAttrib* pAttrib = getLivingAttrib();
		if (pAttrib) {
			nExp += pAttrib->getActorAttValueWithStatus(BUFFATTRT_EXP_GAIN_SPEED, nExp);
		}

		m_PlayerAttrib->addExp(nExp);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("exp_xisou");
		}
	}
}

void ClientPlayer::updatePlayer()
{
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		return;
	if (!m_MoveControl)
		return;
	if (!hasUIControl())  // 客机需要跑
	{
		if (!m_MoveControl->isActive())
			return;
	}
	if (isDead())
		return;	
	PlayerLocoMotion *player_loco = static_cast<PlayerLocoMotion *> (getLocoMotion());
	if (!player_loco)
		return;
	
	player_loco->m_RotateYaw = m_MoveControl->getYaw();
	player_loco->m_RotationPitch = m_MoveControl->getPitch();
	const auto opera = m_MoveControl->getCurrentStatus();

	if (getFlying())
	{
		if (opera.count(IMT_Up))
		{
			player_loco->setMoveUp(1);
			m_MoveUp = 1;
		}
		else if (opera.count(IMT_Down))
		{
			player_loco->setMoveUp(-1);
			m_MoveUp = -1;
		}
		else
		{
			player_loco->setMoveUp(0);
			m_MoveUp = 0;
		}
	}
	if (getSitting())
	{
		player_loco->setMoveForward(0);
		player_loco->setMoveStrafing(0);
	}
	else
	{
		bool reverse = ((!getPlayerAttrib()->isNewStatus() && getPlayerAttrib()->hasBuff(MOVEREVERSE_BUFF)) || getPlayerAttrib()->hasStatusEffect(STATUS_EFFECT_MOVEREVERSE));
		if (opera.count(IMT_Forward))
		{
			player_loco->setMoveForward(reverse ? -1 : 1);			
		}
		else if (opera.count(IMT_Back))
		{
			player_loco->setMoveForward(reverse ? 1 : -1);			
		}
		else
		{
			player_loco->setMoveForward(0);
		}
		
		if (opera.count(IMT_Right))
		{
			player_loco->setMoveStrafing(reverse ? -1 : 1);
		}
		else if (opera.count(IMT_Left))
		{
			player_loco->setMoveStrafing(reverse ? 1 : -1);
		}
		else
		{
			player_loco->setMoveStrafing(0);
		}
		if (opera.count(IMT_UpEnd))
		{
			const auto &motion = player_loco->getMotion();
			player_loco->setMotion(Rainbow::Vector3f(motion.x, 0, motion.z));
		}
	}
	if (opera.count(IMT_Jump))
	{
		if (!player_loco->getJumping())
			setJumping(true);
	}
	else
	{
		if (player_loco->getJumping())
			setJumping(false);
	}
	updateJetFly(opera.count(IMT_Jet));
	int move_forward = 0;
	if (opera.count(IMT_Forward))
		move_forward = 1;
	else if (opera.count(IMT_Back))
		move_forward = -1;

	updateFireRocket(opera.count(IMT_Jump));
	updateChargeJump(opera.count(IMT_Jump));
	updateSnakeGodWing(m_MoveControl->isTriggerJump(), move_forward);
	m_MoveControl->clearDisposableEvent();
}

void ClientPlayer::checkMoveResult()
{
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		return;
	if (!m_MoveControl || !m_MoveControl->isActive() ||! m_MoveControl->needCheck())
		return;
	if (hasUIControl())
		return;
	if (isDead())
		return;
	if (GetCheatHandler())
	{
		if (!GetCheatHandler()->checkRotation(m_MoveControl->getYaw(), m_MoveControl->getPitch()))
		{
			m_MoveControl->setLastTickYawPitch();
		}
	}
	if (getRiddenComponent() && getRiddenComponent()->isRiding())
		return;
	if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isRemote())
	{// 主机跑的逻辑
		PlayerLocoMotion *player_loco = static_cast<PlayerLocoMotion *> (getLocoMotion());
		if (!player_loco)
			return;
		int ret = CRT_Ingore;
		auto &check_pos = m_MoveControl->getPrePosition();
		WCoord wc_check_pos(check_pos.x, check_pos.y, check_pos.z);
		Rainbow::Vector3f motion = player_loco->m_Motion;		
		if (preCheckMoveControl(ret, wc_check_pos))
		{
    		LOG_DEBUG("[%llu]preCheckMoveControl ret=%d", GetIClientGameManagerInterface()->getICurGame()->getGameTick(), ret);
			// empty
		}
		else
			ret = m_MoveControl->checkMoveStatus(*player_loco);
		if (ret == CRT_RollBack)
		{  // 客机需要重置
			game::hc::PB_MoveSyncHC pbMoveSync;
			pbMoveSync.set_id(m_MoveControl->getCheckID());
			pbMoveSync.set_accept(false);
			auto pb_pos = pbMoveSync.mutable_pos();
			auto &pos = player_loco->getPosition();
			pb_pos->set_x(pos.x);
			pb_pos->set_y(pos.y);
			pb_pos->set_z(pos.z);
			
			auto pb_motion = pbMoveSync.mutable_motion();
			auto &motion = player_loco->getMotion();
			pb_motion->set_x(motion.x);
			pb_motion->set_y(motion.y);
			pb_motion->set_z(motion.z);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_SYNC_MOVE_HC, pbMoveSync);

			m_MoveControl->onSyncToPos(pos, motion);
		}
		else if (ret == CRT_Accept)
		{ // 主机接受客机位置
			// 在运动的平台上接受可客机位置再setPosition 的话，客机位置会不停偏移 抖动
			if (!IsOnPlatform())
			{
				auto& pos = m_MoveControl->getPrePosition();
				if (!pos.isZero())
				{
					player_loco->setPosition(pos.x, pos.y, pos.z);
					player_loco->setMotion(motion);
					WCoord mvec(0, -1, 0);
					CollideAABB box;
					player_loco->getCollideBoxMatchPhysBox(box);
					WCoord dest = m_pWorld->moveBox(box, mvec);
					if (dest.y == 0)
					{
						player_loco->setOnGround(true);
						player_loco->m_JumpingTicks = 0;
						// LOG_INFO("client set onground uin=%d y=%d", getUin(), dest.y);
					}
				}
			}
		}
		else if (ret == CRT_Ingore) 
		{
		}
	}
}

/**
 * @brief 主机前置检测移动条件
 * @param result 返回值, 函数返回true时才会设置
 * @param dest_pos 客机上传, 移动到目标位置
 * @return true result已被设置
*/
bool ClientPlayer::preCheckMoveControl(int &result, const WCoord& dest_pos)
{
	const WCoord& host_pos = getPosition();
	if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchNewNoCollision) && GetCheatHandler()->isCollideInteract(this, WCoord(dest_pos.x, dest_pos.y, dest_pos.z)))
	{
#ifdef IWORLD_SERVER_BUILD
		jsonxx::Object log;
		jsonxx::Array srv, client;
		client << dest_pos.x << dest_pos.y << dest_pos.z;
		log << "client_pos" << client;

		const WCoord& host_pos = getPosition();
		srv << host_pos.x << host_pos.y << host_pos.z;
		log << "server_pos" << srv;

		Rainbow::GetICloudProxyPtr()->InfoLog(getUin(), 0, "move_sync_collision", log);
#endif
		result = CRT_RollBack;
		return true;
	}
	else if (GetCheatHandler() && (GetCheatHandler()->onErrorHalfBlock(dest_pos) || GetCheatHandler()->onErrorHalfBlock(host_pos)))
	{
		result = CRT_Accept;
		return true;
	}
	return false;
}

void ClientPlayer::updateJetFly(bool jet_flying)
{
	auto functionWrapper = getFuncWrapper();
	if (!functionWrapper)
		return;
	bool old = functionWrapper->getJetpackFlying();
	auto effectComponent = getEffectComponent();
	if (jet_flying)
	{
		if (getCurDorsumID() != ITEM_JETPACK) //火箭背包
		{
			return;
		}
		PlayerLocoMotion* loc = static_cast<PlayerLocoMotion*> (getLocoMotion());
		if (!loc)
		{
			return;
		}
		functionWrapper->setJetpackFlying(true);
		loc->addMotion(0.0f,  210 * 1.0f / 20.0f, 0.0f);
		if (effectComponent && !old)
			effectComponent->playBodyEffect(TOOLFX_JETPACK2);
	}
	else
	{
		functionWrapper->setJetpackFlying(false);
		if (effectComponent && old)
			effectComponent->stopBodyEffect(TOOLFX_JETPACK2);
	}

}

void ClientPlayer::updateSnakeGodWing(bool jump, int move_forward)
{
	if (getCurDorsumID() != ITEM_SNAKEGOD_WING) //蛇神之翼
	{
		return;
	}
	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *> (getLocoMotion());
	if (!loc)
	{
		return;
	}
	int displayoxygen = 0;
	if(!isDead())
	{
		displayoxygen = (int)getLivingAttrib()->getOxygen();
		float oxygen_userate = getOxygenUseRate();
		if (m_pWorld->getCurMapID() >= MAPID_MENGYANSTAR) oxygen_userate *= GetLuaInterfaceProxyPtr()->get_lua_const()->planet_oxygenuse_beilv;
		if(loc && (!loc->isInsideNoOxygenBlock() || oxygen_userate == 0 || oxygen_userate >=2 || !isInWater()) && displayoxygen>=START_OXYGEN)
		{
			displayoxygen = -1;
		}
	}
	if (jump && displayoxygen < 0 && !getFlying())
	{
		if (m_SnakeGodWingState.wingState >= 1 && !loc->onGround() && m_SnakeGodWingState.wingFlyTime <= -1.0f)//阶段1下落阶段可以进入阶段2
		{
			m_SnakeGodWingState.wingState = 2;
			m_SnakeGodWingState.wingForwardSpeed = 1.2f;
			m_SnakeGodWingState.wingFlyTime = 3.0f;
			loc->addMotion(0.0f, 10.0f, 0.0f);
		}
		else if (m_SnakeGodWingState.wingState == 0)
		{
			if (getRun() && loc->onGround() && move_forward > 0)//地面跑步起飞进入阶段1
			{
				m_SnakeGodWingState.wingState = 1;
				m_SnakeGodWingState.wingForwardSpeed = 1.2f;
				m_SnakeGodWingState.wingFlyTime = 3.0f;
				loc->setOnGround(false);
			}
			else if (!loc->onGround() && m_SnakeGodWingState.wingFlyTime <= -1.0f) //空中坠落进入阶段1
			{
				m_SnakeGodWingState.wingState = 1;
				m_SnakeGodWingState.wingForwardSpeed = 1.2f;
				m_SnakeGodWingState.wingFlyTime = 3.0f;
				loc->addMotion(0.0f, 10.0f, 0.0f);
				loc->setOnGround(false);
			}
		}
	}

	Rainbow::Vector3f lookdirToGo;
	PitchYaw2Direction(lookdirToGo, getFaceYaw(), getFacePitch());
	lookdirToGo.y = 0.0f;

	float dtime = 1.0f / 20.0f;
	if (m_SnakeGodWingState.wingState > 0)
	{
		if (getFlying() || displayoxygen > 0)
			m_SnakeGodWingState.wingState = 0;
		
		loc->setMoveForward(0);
		loc->setMoveStrafing(0);
		if (m_SnakeGodWingState.wingForwardSpeed > 0.8f) 
		{
			m_SnakeGodWingState.wingForwardSpeed -= 0.2f * dtime;
			if (m_SnakeGodWingState.wingForwardSpeed < 0.8f)
				m_SnakeGodWingState.wingForwardSpeed = 0.8f;
		}

		m_SnakeGodWingState.wingFlyTime -= dtime;
		if (m_SnakeGodWingState.wingFlyTime > 0.0f) //上升
		{
			loc->m_Motion.y += 20 * dtime;
		}
		float r = (m_SnakeGodWingState.wingForwardSpeed * BLOCK_SIZE * dtime) / Sqrt(lookdirToGo.x*lookdirToGo.x + lookdirToGo.z * lookdirToGo.z);
		lookdirToGo *= r;
		loc->addMotion(lookdirToGo.x, lookdirToGo.y, lookdirToGo.z);
		m_SnakeGodWingFlying = true;
	}
	else
	{
		if (getFlying())
			m_SnakeGodWingState.wingFlyTime = 0.0f;
		else if (!loc->onGround() && displayoxygen < 0)
			m_SnakeGodWingState.wingFlyTime -= dtime;
		m_SnakeGodWingFlying = false;
	}

	if (loc->onGround())
	{
		if (m_SnakeGodWingState.wingState == 0)
		{
			m_SnakeGodWingState.wingFlyTime = 0.0f;
		}
		if (m_SnakeGodWingState.wingFlyTime <= 2.5f && m_SnakeGodWingState.wingState > 0)
		{
			m_SnakeGodWingState.wingState = 0;
			lookdirToGo *= 6.0f;
			loc->addMotion(lookdirToGo.x, lookdirToGo.y, lookdirToGo.z);
		}
		auto functionWrapper = getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setImmuneFall(1);
		}
	}
}


void ClientPlayer::updateFireRocket(bool jump)
{
	if (getCurDorsumID() != ITEM_FIRE_ROCKET)
		return;

	auto loc = getLocoMotion();
	if (!loc)
		return;
	float dtime = 1.0f / 20;
	Rainbow::Vector3f lookdirToGo;
	PitchYaw2Direction(lookdirToGo, loc->m_RotateYaw, loc->m_RotationPitch);

	lookdirToGo *= 130.0f*dtime;
	loc->addMotion(lookdirToGo.x, lookdirToGo.y, lookdirToGo.z);

	if(jump)
	{
		loc->addMotion(0, 100.0f*dtime, 0);
	}

	if(loc->getOnGround())
	{
		loc->addMotion(0, 25, 0);
		auto functionWrapper = getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setImmuneFall(1);
		}
		loc->setOnGround(false);
	}
}

void ClientPlayer::updateChargeJump(bool jump)
{
	//蓄力弹跳组件
	if (m_pChargeJumpComponent)
	{
		m_pChargeJumpComponent->updatePlayerChargeJump(jump);
	}
}

void ClientPlayer::_updateJump()
{
	setJumping(true);
}

void ClientPlayer::addMoveStatus(unsigned key)
{
	static std::map<unsigned, unsigned> release_status_pairs = {
		{IMT_Forward, IMT_Back},
		{IMT_Back, IMT_Forward},
		{IMT_Left, IMT_Right},
		{IMT_Right, IMT_Left},
		{IMT_Down, IMT_Up},
		{IMT_Up, IMT_Down},
	};
	if (!m_MoveControl)
		return;
	m_MoveControl->addEvent(key);
	auto it = release_status_pairs.find(key);
	if (it != release_status_pairs.end())
		m_MoveControl->removeEvent(it->second);
}

void ClientPlayer::removeMoveStatus(unsigned key)
{
	if (m_MoveControl)
		m_MoveControl->removeEvent(key);
}

void ClientPlayer::setMoveControl(const std::set<unsigned> &operators, float yaw, float pitch, unsigned long long tick)
{
	if (m_MoveControl)
		m_MoveControl->setClientOperators(operators, yaw, pitch, tick);
}
void ClientPlayer::setMoveControl(unsigned operators, float yaw, float pitch, unsigned long long tick)
{
	if (m_MoveControl)
		m_MoveControl->setClientOperators(m_MoveControl->getOperaFromBits(operators), yaw, pitch, tick);
}

void ClientPlayer::setCheckMoveResult(unsigned long long id, const WCoord &pos, unsigned long long tick)
{
	if (m_MoveControl)
		m_MoveControl->setMoveCheck(id, pos, tick);
}

void ClientPlayer::setLastSyncPosition(const WCoord &pos)
{
	if (m_MoveControl)
		m_MoveControl->setLastPosition(pos);
}
void ClientPlayer::setMoveControlPitch(float pitch)
{
	if (m_MoveControl)
		m_MoveControl->setPitch(pitch);
}

void ClientPlayer::setMoveControlYaw(float yaw)
{
	if (m_MoveControl)
		m_MoveControl->setYaw(yaw);
}

void ClientPlayer::COpenWorkbench(int Uin, int Level)
{
#ifndef IWORLD_SERVER_BUILD
	PB_OpenWorkbenchCH info;
	info.set_uin(Uin);
	info.set_level(Level);

	GetGameNetManagerPtr()->sendToHost(PB_OPEN_WORKBENCH_CH ,info);
#endif
}

void ClientPlayer::CUnlockTechNode(int Level, int RootTreeId, int NodeId)
{
#ifndef IWORLD_SERVER_BUILD
	PB_UnlockTechNodeCH info;
	info.set_level(Level);
	info.set_roottreeid(RootTreeId);
	info.set_nodeid(NodeId);

	GetGameNetManagerPtr()->sendToHost(PB_UNLOCK_TECH_NODE_CH, info);
#endif
}

void ClientPlayer::changeMoveFlag(unsigned flag_id, bool on)
{
	switch(flag_id)
	{
		case IFC_Fly:
		{
			if (!on)
				setFlyingAndSync(on);
			else
			{
				if ((GetCheatHandler() && GetCheatHandler()->isTestEnv())
					|| (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
					|| (isInSpectatorMode() && getSpectatorType() == SPECTATOR_TYPE_FREE)
					|| GetClientInfoProxy()->IsUserOuterChecker(getUin())
					|| GetClientAppProxy()->checkIsGMWhiteListMember()
					)
				{
					// setFlyingAndSync(true);
				}
			}
			break;
		}
		
		case IFC_Sneak:
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->getPlayerPermit(ENABLE_SNEAK))
				setSneaking(on);
			break;
		case IFC_Run:
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->getPlayerPermit(ENABLE_RUN))
				setRun(on);
			break;
		case IFC_Sit:
			setSitting(on);
			break;
		default:
			LOG_INFO("%s unknown flag_id=%u", __FUNCTION__, flag_id);
	}
}

/**
 * @brief 返回是否开启了新的位移同步
 * 主机同时支持新老位移同步协议, 调用此函数无意义
 * 
 * @return true 支持
 */
bool ClientPlayer::isNewMoveSyncSwitchOn()
{
	return hasUIControl() && m_MoveControl && m_MoveControl->isNewSyncSwitchOn();
}

bool ClientPlayer::isMoveControlActive() const
{
	return m_MoveControl && m_MoveControl->isActive();
}

void ClientPlayer::updateClientMoveSyncInterval(bool nega)
{
	m_MoveControl && m_MoveControl->updateClientMoveSyncInterval(nega);
}

void ClientPlayer::resetAllFlags(unsigned int flags)
{
	ActorLiving::resetAllFlags(flags);
	if (ActorLiving::getFlying())
	{
		if (GetCheatHandler())
			GetCheatHandler()->onFlyModeChange(true);
	}
}

void ClientPlayer::setNeedSyncPosition()
{
	if (m_MoveControl)
		m_MoveControl->addSync();
}

void ClientPlayer::setCameraLerpSpeed(float speed)
{
	if (hasUIControl() && g_pPlayerCtrl && g_pPlayerCtrl->m_pCamera && speed > 0)
	{
		g_pPlayerCtrl->m_pCamera->setCameraLerpSpeed(speed);
	}
}