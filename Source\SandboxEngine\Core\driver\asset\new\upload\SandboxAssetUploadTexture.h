#pragma once

#include "SandboxAssetUploadRef.h"
#include "Utilities/String.h"
#include "AssetPipeline/BuildTarget.h"

namespace MNSandbox {

	class /*EXPORT_SANDBOXENGINE*/ AssetUploadTexture : public AssetUploadRef
	{
		DECLARE_REF_NEW_INSTANCE(AssetUploadTexture)
	public:
		AssetUploadTexture();
		virtual ~AssetUploadTexture();

		virtual void OnGetRemoteResIdFinish()override;
	protected:
		virtual void OnInit() override;
		virtual void OnPrepareUploadFile() override;
		virtual void OnPrepareSetMeta() override;
		virtual std::string GetCloudIdByAssetDetailRsp(AutoRef<AssetDetailRsp> rspData) override;
	private:
		//virtual bool SetUploadIntranetPathHash() override;

	private:
		std::unordered_map<int, SInt64> m_platformRemoteIdMap;
		
		
	};

}