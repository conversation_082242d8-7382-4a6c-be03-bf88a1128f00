#pragma once
//#include "OgrePrerequisites.h"
//#include "Utilities/Singleton.h"
#include "Math/Quaternionf.h"
#include "Vehicle/VehiclePhysics/OgreVehicleManager.h"
#include "Vehicle/VehiclePhysics/OgreVehicleArticulationManager.h"
//#include "OgreShared.h"
#include "RigidStaticActor.h"
#include "RigidDynamicActor.h"
#include "Physics/RawMesh.h"
#include "Physics/BoxMesh.h"
#include "Components/VehicleWheelBase.h"
#include "Core/Component.h"
#include "Physics/PhysicMaterial.h"
#include "Components/CharacterControllerBase.h"
#include "Common/SingletonDefinition.h"
#include "Physics/RawMesh.h"
#include "Vehicle/VehiclePhysics/OgreVehicleTypes.h"
#include "Physics/JointDescriptions.h"
#include "Physics/IRaycast.h"
#include "Physics/PhysicsQuery.h"
#include "Physics/JointSpring.h"
//#include "SandboxAutoRef.h"
#include "SandboxSceneObject.h"
#include "Components/SpringJoint.h"
#include "SandboxWeakRef.h"
#include "BaseClass/SharePtr.h"
#include "SandboxEngine.h"
//#include "SandboxNotify.h"
#include <list>

namespace Rainbow
{
	class Joint;
	class CharacterController;
	class GameScene;
	class ArticulationBody;
	class VehicleDrive4W;
	class VehicleCollisionScript;
	class HingeJoint;
	class ConfigurableJoint;
}
class World;

namespace MINIW //tolua_exports
{ //tolua_exports
	using namespace physx;

	EXPORT_SANDBOXENGINE extern const int SIMULATE_TIME_DIVISOR;

	
	enum class CollisionEvent {
		TOUCH = 0,
		TOUCHEND = 1,
	};

	//tolua_begin
	//Joint类型
	enum JointType
	{
		HFIXED=0,
		SPHERICAL,
		REVOLUTE,			  //转轴
		PRISMATIC,			  //滑动方块
		DISTANCE,
		SPRING,               //弹簧
		D6,
		ROPE,                 //绞绳头部
		ROPENODE,             //绞绳节点
		ROPETAIL,             //绞绳尾部
		SUSPENSION,           //悬挂
		ARM_PRISMATIC,		  //液压臂
		T_REVOLUTE,			  //T铰链
		MAX_NUM_JOINT_TYPES
	};

	//Joint 滑动关节轴向设置
	enum JointAxis
	{
		AXIS_NOT_INIT = -1,
		AXIS_NEG_X = 0,
		AXIS_POS_X,
		AXIS_NEG_Z,
		AXIS_POS_Z,
		AXIS_NEG_Y,
		AXIS_POS_Y
	};
	//tolua_end

	class EXPORT_SANDBOXENGINE JointCreateParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		JointCreateParam()
			: mType(REVOLUTE),
			mGroupId0(0),//当前关节连接到的组id
			mGroupId1(0),//连接到当前关节的方块的新组id
			mPosBase(Rainbow::Vector3f::zero),
			mPos0(Rainbow::Vector3f::zero),
			mQuat0(Rainbow::Quaternionf::identity),
			mPos1(Rainbow::Vector3f::zero),
			mQuat1(Rainbow::Quaternionf::identity),
			mContainerPos(Rainbow::Vector3f::zero),
			mDir(0),
			mSlideLen(0),
			mSlideAxis(AXIS_NOT_INIT),
			mSlideReverse(false),
			mInvInertiaScale(1),
			mInvMassScale(1),
			mSuspensionLevel(1)   //悬挂的等级
		{
		}
		//tolua_end
		JointType mType;
		int mGroupId0;//当前关节连接到的组id
		int mGroupId1;//连接到当前关节的方块的新组id
		Rainbow::Vector3f mPosBase;
		Rainbow::Vector3f mPos0;
		Rainbow::Quaternionf mQuat0;
		Rainbow::Vector3f mPos1;
		Rainbow::Quaternionf mQuat1;
		Rainbow::Vector3f mContainerPos;
		int mDir;
		int mSlideLen;
		JointAxis mSlideAxis;
		bool mSlideReverse;
		float mInvInertiaScale;
		float mInvMassScale;
		int x;
		int y;
		int z;
		int mSuspensionLevel;
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE Joint //tolua_exports
	{ //tolua_exports
	public:
		//设置刚体1相对坐标
		void                                                      SetActor0Pose(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q);
		void													  GetActor0Pose(Rainbow::Vector3f& pos, Rainbow::Quaternionf& q);
		void                                                      GetActor0WorldPos(Rainbow::Vector3f& pos);


		//设置刚体2相对坐标
		void                                                      SetActor1Pose(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q);
		void													  GetActor1Pose(Rainbow::Vector3f& pos, Rainbow::Quaternionf& q);
		void                                                      GetActor1WorldPos(Rainbow::Vector3f& pos);


		//设置断开的作用力和扭力
		void                                                      SetBreakForce(float force, float torque);

		void                                                      GetBreakForce(float& force, float& torque);

		//设置能否断开
		void                                                      SetBreakable(bool data);

		bool                                                      GetBreakable();

		//连接的2个刚体之间是否产生碰撞
		void                                                      SetCollisionEnable(bool data);

		bool                                                      GetCollisionEnable();

		/*PxJoint*											      GetPxJoint();*/

		void                                                      SetJointType(JointType tp);
		JointType                                                 GetJointType();
		void													  SetEnableProjection(const bool& b);

		
		void                                                      UpdateMassScale(const float& mass0,const float& mass1);

		virtual ~Joint();
	protected:
		Joint(Rainbow::Joint* joint);
		
		JointType m_type;
		Rainbow::Joint*                                                  m_pJoint;
		friend class PhysXScene;
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE RevoluteJoint : public Joint
	{
	public:
		void SetEnableLimit(const bool& b);
		void SetEnableDrive(const bool& b);
		void SetEnableDRIVE_FREESPIN(const bool& b);

		void SetDriveVelocity(const float& f);
		void SetDriveForceLimit(const float& f);

		void SetProjectionLinearTolerance(const float& f);
		void SetProjectionAngularTolerance(const float& f);
		//PxJointAngularLimitPair 的限制参数
		void SetLimitAngle(const float& upper, const float& lower);
		void SetRestitution(const float& f);
		void SetBounceThreshold(const float& f);
		void SetContactDistance(const float& f);
		void SetLimitSpring(const float& f);
		void SetLimitDamping(const float& f);
		void SetLimitTargetAngle(const float& f);
		


		//f为弧度
		void SetTargetAngle(const float& f);

		void SetDriveAreForces(const bool& b);

		virtual ~RevoluteJoint();
	protected:
		RevoluteJoint(Rainbow::HingeJoint* joint);
		
	protected:
		struct Rainbow::JointMotor m_motor;
		struct Rainbow::JointLimits m_limit;
		struct Rainbow::JointSpring m_spring;
	private:
		Rainbow::HingeJoint* m_pRevoluteJoint;
		friend class PhysXScene;
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE SpringJoint : public Joint
	{
	public:
		virtual ~SpringJoint();
	protected:
		SpringJoint(Rainbow::SpringJoint* joint);
	public:
		float GetSpring() const;
		void SetSpring(float spring);

		float GetDamper() const;
		void SetDamper(float damper);

		float GetMinDistance() const;
		void SetMinDistance(float distance);

		float GetMaxDistance() const;
		void SetMaxDistance(float distance);

		float GetTolerance() const;
		void SetTolerance(float tolerance);
		void SetIsSpring(const bool& b);
	private:
		Rainbow::SpringJoint * m_pSpringJoint;
		friend class PhysXScene;
	};

	class EXPORT_SANDBOXENGINE StickJoint : public Joint
	{
	public:
		virtual ~StickJoint();
	protected:
		StickJoint(Rainbow::ConfigurableJoint* joint);
	public:
		void SetDistance(const float& f);
		void SetLimitAngle0(const float& f);
	private:
		void UpdateInfo();
	private:
		Rainbow::ConfigurableJoint* m_pStickJoint;

		float m_fDistance{100};
		float m_fLimitAngle0{ 0 };
		

		friend class PhysXScene;
	};

	class EXPORT_SANDBOXENGINE CFGJoint : public Joint
	{
	public:
		virtual ~CFGJoint();
	protected:
		CFGJoint(Rainbow::ConfigurableJoint* joint);
	public:
		Rainbow::ConfigurableJoint* GetJoint();
	private:
		Rainbow::ConfigurableJoint* m_pD6Joint;
		friend class PhysXScene;
	};

	class EXPORT_SANDBOXENGINE ArticulationJoint //tolua_exports
	{ //tolua_exports
	public:

		Rainbow::ArticulationBody*                     GetPxArticulationJoint();
		virtual void                                              update(float timestep) = 0;
	protected:
		ArticulationJoint();
		ArticulationJoint(Rainbow::ArticulationBody* joint);
		virtual ~ArticulationJoint();
		Rainbow::ArticulationBody* m_pArticulationJoint;
	private:
		friend class PhysXScene;
	}; //tolua_exports

	//tolua_begin
	//动力部件类型
	enum DynamicType
	{
		WING=0,
		EMPENNAGE,

		MAX_NUM_DYNAMIC_TYPES
	};
	//tolua_end

	class EXPORT_SANDBOXENGINE DynamicParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		DynamicParam() : 
		  mBlockPos(Rainbow::Vector3f::zero),
			  mBlockDir(0), 
			  mGroupId(0)
		  {
		  }
		//tolua_end
		Rainbow::Vector3f mBlockPos;
		int mBlockDir;
		int mGroupId;
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE WingParam : public DynamicParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		WingParam()
		{
		}
		//tolua_end
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE EmpParam : public DynamicParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		EmpParam()
		{
		}
		//tolua_end
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE SThrusterParam : public DynamicParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		SThrusterParam()
			:m_fPowerMax(0)
			, m_fPowerChangeVal(0)
			, m_nPowerChangeInterval(0)
			, m_nPowerChangeIntervalCounter(0)
			, m_nCostInterval(0)
			, m_nCostIntervalCounter(0)
			, m_fCurPower(0)
			, m_nCurPowerLevel(0)
			, m_bIsControl(false)
			, m_nChangeState(0)
			, m_fThrusterForce2(0)
			, m_fThrusterForce1(0)
		{
			m_vTopLimitList.resize(3);
			m_vCostValList.resize(3);
		}
		float m_fPowerMax;
		float m_fPowerChangeVal;
		int m_nPowerChangeInterval;
		int m_nPowerChangeIntervalCounter;
		int m_nCostInterval;
		int m_nCostIntervalCounter;

		std::vector<int> m_vTopLimitList;
		std::vector<int> m_vCostValList;

		float m_fCurPower;
		int m_nCurPowerLevel;
		bool m_bIsControl;
		int m_nChangeState; // 0为保持状态，1为提速，2为减速
		float m_fThrusterForce2;
		float m_fThrusterForce1;
		//tolua_end
	}; //tolua_exports
	//浮桶参数
	class _OgreExport FloatbucketParam : public DynamicParam //tolua_exports
	{//tolua_exports
	public:
		//tolua_begin

		FloatbucketParam() :m_fFloatForce(0.0f)
		{

		};
		float m_fFloatForce;
		//tolua_end
	};//tolua_exports
	//船只推进器参数
	class _OgreExport BoatThrusterParam : public DynamicParam //tolua_exports
	{//tolua_exports
	public:
		//tolua_begin
		BoatThrusterParam() :m_fThrusterForce(0.0f), m_fUpAngle(90.0f), m_fDownAngle(-90.0f)
		{

		};
		float m_fThrusterForce;
		float m_fUpAngle;
		float m_fDownAngle;
		//tolua_end
	};//tolua_exports
	class EXPORT_SANDBOXENGINE ThrusterParam : public DynamicParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		ThrusterParam()
			:m_fThrusterForce(0)
			, m_fRigidForce(0)
			, m_nThrusterType(0)
		{
		}
		float m_fThrusterForce;
		float m_fRigidForce;
		int m_nThrusterType;
		//tolua_end
	}; //tolua_exports

	//tolua_begin
	struct OPos
	{
		OPos(int _x, int _y, int _z)
		{
			x = _x;
			y = _y;
			z = _z;
		}
		int x;
		int y;
		int z;
	};
	//tolua_end

	class EXPORT_SANDBOXENGINE DynamicCreateParam //tolua_exports
	{ //tolua_exports
	public:
		//tolua_begin
		DynamicCreateParam()
		{
		}

		//机翼参数
		std::vector<WingParam> mWingParams;
		//尾翼参数
		std::vector<EmpParam> mEmpParams;
		//航天推进器参数
		std::vector<SThrusterParam> mSThrusterParams;
		//普通推进器参数
		std::vector<ThrusterParam> mThrusterParams;
		//船只的浮桶参数
		std::vector<FloatbucketParam> mFloatbucketParams;
		//船只的推进器参数
		std::vector<BoatThrusterParam> mBoatThrusterParams;
		//tolua_end
	}; //tolua_exports

	enum class RoleControllerType
	{
		kRoleControllerType_Box,
		kRoleControllerType_Capsule,

	};

	class EXPORT_SANDBOXENGINE RoleControllerCallback : public Rainbow::ControllerBehaviorCallback
	{
		virtual Rainbow::ControllerBehaviorFlag getBehaviorFlags(Rainbow::EControllerBehaviorType type);
	};
	
	//角色控制器接口封装
	class EXPORT_SANDBOXENGINE RoleController : public Rainbow::Component{ //tolua_exports
		//DECLARE_CLASS(RoleController);
	public:
		RoleController(RoleControllerType type)
			: Rainbow::Component(kMemDefault) , m_pActor(nullptr)
		{
			mType = type;
			m_pController = nullptr;
		}


		void virtual OnAddToScene(Rainbow::GameScene* scene) override;
		
		void OnAddToGameObject() override;
		virtual void OnRemoveFromGameObject() override;

		void SetHalfHeight(float height);
		void SetCenterPos(const Rainbow::Vector3f& pos);

		//初始化
		//void                                                                    init(PxController* ctrl, void* userdata = NULL);

		//获取坐标，不提供旋转参数，因为不需要
		Rainbow::Vector3f                                                                 GetPosition();

		//设置坐标，此函数不是安全的，如果把角色控制器设置到静态刚体物件里面或者穿插的话会出现错误，所以坐标需要保证不与其他物件穿插
		void                                                                    SetPosition(const Rainbow::Vector3f& pos);

		float                                                                   GetHalfHeight();

		float                                                                   GetRadius();

		int                                                                    Move(const Rainbow::Vector3f& vec, float elapsedtime, bool filterStatic = true);

		void																	SetMassLimit(int massLimit);

		void																	SetUserData(void* userdata);

		void setIsClient(bool client) { m_bClient = client; }

		Rainbow::Vector3f													GetVelocity() const;

		RoleControllerCallback	m_Callback;

		void																			SetGroup(UInt32 id);

		UInt32																			GetGroup();
		Rainbow::RigidDynamicActor* getActor() { return m_pActor; }


		float GetStepOffset();
		void SetStepOffset(const float& value);

		float GetSlopeLimit();
		void SetSlopeLimit(const float& value);

		void InvalidateCache();
		Rainbow::Collider* GetColliderUnderFoot();

		void SetEnableDrawCollider(bool enableDraw);
		bool GetEnableDrawCollider();
		void SetSkinWidth(const float& f);
		float GetSkinWidth();
#if	GIZMO_DRAW_ENGABLE
	public:
	protected:
		virtual void OnDrawGizmo();
		void* m_pvListener = nullptr;
		//bool m_GizmoEnable{ true };
#endif

	protected:
		virtual ~RoleController();
		
	private:

		//void defaultCCTInteraction(const PxControllerShapeHit& hit);
		RoleControllerType mType;
		friend class PhysXScene;
		Rainbow::CharacterControllerBase*				                                            m_pController;
		Rainbow::PPtr<Rainbow::CharacterControllerBase>												m_pControllerPPtr;
		//PxMaterial*                                                             m_pMaterial;
		int																		m_MassLimit;
		bool																	m_bClient;
		Rainbow::RigidDynamicActor* m_pActor;
		bool m_EnableDrawCollider{ false };
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE PhysXScene //tolua_exports
	{ //tolua_exports
	public:

		void                 CreateScene(float gravity, const VehicleManagerParam& vehicleManagerParam, bool reportwhenpenetrate = false);

		RoleController*      CreateRoleController( float halfheight, float forward, float side, const Rainbow::Vector3f& pos, void* userdata = NULL);

		RoleController*      CreateRoleCapsuleController( float halfheight, float radius, const Rainbow::Vector3f& pos, void* userdata = NULL);

		RoleController* CreateRoleControllerWithGameobj(float halfheight, float forward, float side, const Rainbow::Vector3f& pos,Rainbow::GameObject* go ,Rainbow::Vector3f center ,void* userdata = NULL, bool useBehaviorCallback = true);

		RoleController* CreateRoleCapsuleControllerWithGameobj(float halfheight, float radius, const Rainbow::Vector3f& pos, Rainbow::GameObject* go, void* userdata = NULL, bool useBehaviorCallback = true);


		bool				DeleteRoleControllerComponent(RoleController* controller);

		bool                 DeleteRoleController(RoleController* controller);

		//box形状
		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, bool isTrigger, void* userdata = NULL);

		//sphere形状
		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL);

		//capsule形状
		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL);

		Rainbow::RigidStaticActor*    AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 indecesNum, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL);


		//box形状 不创建新的go
		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, bool isTrigger, Rainbow::GameObject* go, void* userdata = NULL);

		//sphere形状 不创建新的go
		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL);

		//capsule形状 不创建新的go
		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL);

		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL);

		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 indecesNum, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL);

		Rainbow::RigidStaticActor* AddRigidStaticActorWithGameobject(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, Rainbow::SharePtr<Rainbow::Mesh> mesh, float staticFriction, float dynamicFriction, float restitution, Rainbow::GameObject* go, void* userdata = NULL);



	private:
		//box形状
		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL , Rainbow::GameObject* go = NULL);

		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, bool isTrigger, void* userdata = NULL, Rainbow::GameObject* go = NULL);

		//sphere形状
		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, Rainbow::GameObject* go = NULL);

		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, Rainbow::GameObject* go = NULL);

		//capsule形状
		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, Rainbow::GameObject* go = NULL);

		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, Rainbow::GameObject* go = NULL);

		//三角形面片
		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 indecesNum, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, void* userdata = NULL, Rainbow::GameObject* go = NULL);

		Rainbow::RigidStaticActor* _AddRigidStaticActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, Rainbow::SharePtr<Rainbow::Mesh> mesh, float staticFriction, float dynamicFriction, float restitution, void* userdata, Rainbow::GameObject* go = NULL);
		

	public:	
		
		//连同gameobj一起删掉，比较暴力，适用单独生成一个go来管理物理部分组件
		bool                 DeleteRigidActor(Rainbow::RigidBaseActor* actor);
		//只删除rigid 和在其中生成的 组件,不删除go和其他组件
		bool				 DeleteRigidComponent(Rainbow::RigidBaseActor* actor);

		//box形状
		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL);

		//sphere形状
		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL);

		//capsule形状
		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, int relativePoseFlag = 0, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL);

		//三角形面片
		Rainbow::RigidDynamicActor* AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 nbTriangles, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata = NULL, bool enableGravity = true);


		//box形状 不创建新的go
		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go , void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL);

		//sphere形状
		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go , void* userdata = NULL);

		//capsule形状
		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL, const Rainbow::Vector3f* relativePose = NULL, int relativePoseFlag = 0, bool enableGravity = true);

		Rainbow::RigidDynamicActor*   AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL);

		//三角形面片
		Rainbow::RigidDynamicActor* AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 nbTriangles, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL, bool enableGravity = true, bool convex = false);

		//三角形面(share)
		Rainbow::RigidDynamicActor* AddRigidDynamicActorWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, Rainbow::SharePtr<Rainbow::Mesh> mesh, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, Rainbow::GameObject* go, void* userdata = NULL, bool enableGravity = true, bool convex = false);


		//创建简单4驱车辆
		Rainbow::RigidDynamicActor* AddRigidDynamicSimple4WVehicleWithGameobj(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, std::vector<Rainbow::RAWMesh>& data , Rainbow::GameObject* go);

		//创建车辆
		Rainbow::RigidDynamicActor*	 AddRigidDynamicVehicle(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, std::vector<Rainbow::RAWMesh>& data, Rainbow::VehicleDrive4W*& car, Rainbow::VehicleCollisionScript*& collisionScript);
		//创建简单4驱车辆
		Rainbow::RigidDynamicActor* AddRigidDynamicSimple4WVehicle(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, std::vector<Rainbow::RAWMesh>& data, Rainbow::GameObject* go);

		//创建组装车辆
		Rainbow::RigidDynamicActor*	 AddRigidDynamicVehicleAssemble(bool b, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, const std::vector<Rainbow::BoxMesh>& boxMeshesChassis, const std::vector<Rainbow::RAWMesh>& rawMeshesWheel, const Rainbow::VehicleCreateParam& vehicleCreateParam, Rainbow::VehicleDriveCustom*& car, Rainbow::VehicleCollisionScript*& collisionScript);

		//创建机械骨骼车辆
		Rainbow::RigidDynamicActor*	 AddRigidDynamicVehicleArticulation(bool b, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, const MINIW::VehicleCreateMesh& vehicleCreateMesh, const Rainbow::VehicleCreateParam& vehicleCreateParam, Rainbow::VehicleDriveCustom*& car, VehicleArticulation*& vehicleArticulation, Rainbow::RigidDynamicActor* parent, Rainbow::VehicleCollisionScript*& collisionScript);

		////删除组装车辆
		//void DeleteRigidDynamicVehicle(PxVehicleWheels* vehicle);

		void DeleteRigidDynamicVehicleArticulation(VehicleArticulation* vehicleArticulation);

		void ResetRigidDynamicVehicle(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, Rainbow::VehicleWheelsBase* vehicle);

		void ResetRigidDynamicVehicleArticulation(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, VehicleArticulation* vehicleArticulation);

		//创建静态车身(客机使用)
		Rainbow::RigidStaticActor*	 AddRigidStaticChassis(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, void* userdata, const Rainbow::Vector3f* relativePose, const Rainbow::BoxMesh& data);

		//创建机械骨骼车身
		Rainbow::RigidDynamicActor*	 AddArticulationLinkChassis(VehicleArticulation* vehicleArticulation, Rainbow::RigidDynamicActor* parent, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, const Rainbow::VehicleCreateParam& vehicleCreateParam, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, const Rainbow::BoxMesh& data, float jointMass, bool isRope = false);

		//创建动态车身
		Rainbow::RigidDynamicActor*	 AddRigidDynamicChassis(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, const Rainbow::BoxMesh& data);

		//创建车轮
		Rainbow::RigidDynamicActor*	 AddRigidDynamicWheel(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, const Rainbow::Vector3f* relativePose, bool enableGravity, const Rainbow::RAWMesh& data, float wheel_width, float wheel_radius);

		//
		bool				 attachShapesToRigidActor(Rainbow::RigidBaseActor& actor, const Rainbow::SharePtr<Rainbow::PhysicMaterial>& mtl, const Rainbow::BoxMesh& data, void* userdata, bool kinematic, std::vector<Rainbow::Collider*> &list);
		bool				 detachShapeFromRigidActor(Rainbow::RigidBaseActor& actor, Rainbow::Collider& shape);
		bool                 DeleteJoint(Joint* joint);
		void                 AddArticulationJoint(ArticulationJoint* articulationJoint);
		bool                 DeleteArticulationJoint(ArticulationJoint* articulationJoint);

		//创建Fix类型链接控制器 localpos0单位
		Joint*               CreateFixJoint(Rainbow::RigidDynamicActor* actor0, const Rainbow::Vector3f& localpos0, const Rainbow::Quaternionf& localq0, Rainbow::RigidDynamicActor* actor1, const Rainbow::Vector3f& localpos1, const Rainbow::Quaternionf& localq1);

		//创建Revolute类型关节
		RevoluteJoint* CreateRevoluteJoint(Rainbow::RigidDynamicActor* actor0, const Rainbow::Vector3f& localpos0, const Rainbow::Quaternionf& localq0, Rainbow::RigidDynamicActor* actor1, const Rainbow::Vector3f& localpos1, const Rainbow::Quaternionf& localq1);

		//创建Spring类型关节
		SpringJoint* CreateSpringJoint(Rainbow::RigidDynamicActor* actor0, const Rainbow::Vector3f& localpos0, const Rainbow::Quaternionf& localq0, Rainbow::RigidDynamicActor* actor1, const Rainbow::Vector3f& localpos1, const Rainbow::Quaternionf& localq1);
		
		//创建Stick类型关节
		StickJoint* CreateStickJoint(Rainbow::RigidDynamicActor* actor0, const Rainbow::Vector3f& localpos0, const Rainbow::Quaternionf& localq0, Rainbow::RigidDynamicActor* actor1, const Rainbow::Vector3f& localpos1, const Rainbow::Quaternionf& localq1);




		//创建D6类型链接控制器
		CFGJoint* CreateD6Joint(Rainbow::RigidDynamicActor* actor0, Rainbow::RigidDynamicActor* actor1, const JointCreateParam& jointCreateParam0, const JointCreateParam& jointCreateParam1, float speed = 0);
		CFGJoint* CreateD6Joint(Rainbow::RigidDynamicActor* actor0, const Rainbow::Vector3f& localpos0, const Rainbow::Quaternionf& localq0, Rainbow::RigidDynamicActor* actor1, const Rainbow::Vector3f& localpos1, const Rainbow::Quaternionf& localq1);

		//创建机械骨骼链接控制器
		Rainbow::ArticulationBody* CreateArticulationJoint(Rainbow::RigidDynamicActor* actor, const JointCreateParam& jointCreateParam, long long stiffness, int damping);

		//增加Shape
		void				 AddShape(Rainbow::RigidDynamicActor* actor, const Rainbow::Vector3f& localpos, const Rainbow::Quaternionf& localq, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, float mass = 0);

		//创建刚体碰撞线框, 方便调试
		//void				 DebugRender(DebugRenderer* debugRenderer);
		////调试载具逻辑
		//void				 DebugVehicle(UInt32 channelEngine, UInt32 channelWheel, UIRenderer *uiRenderer, const MINIW::HHFONT& debugInfoFont, DebugRenderer* debugRenderer);

		VehicleArticulationManager* GetVehicleManager();

		virtual ~PhysXScene();

		bool GetNeedUpdateState() const {
			return m_bNeedUpdate;
		}

		void SetNeedUpdateState(bool state)
		{
			m_bNeedUpdate = state;
		}

		void UpdateArticulationJoint(float dtime);

	public:

		bool RayCast(const Rainbow::Vector3f& origin, const Rainbow::Vector3f& unitDir, const float  distance, Rainbow::RaycastHit& hitInfo, unsigned int mask, bool isIgnoreTrigger);

		Rainbow::RaycastHits RayCastAll(const Rainbow::Vector3f& origin, const Rainbow::Vector3f& unitDir, const float  distance, unsigned int mask, bool isIgnoreTrigger);


		Rainbow::ColliderCache OverlapBox(const Rainbow::Vector3f shape, const Rainbow::Vector3f pos, const Rainbow::Vector3f angle ,unsigned int mask , bool isIgnoreTrigger);

		Rainbow::ColliderCache OverlapCapsule(const float radius, const Rainbow::Vector3f p0, const Rainbow::Vector3f p1 , unsigned int mask , bool isIgnoreTrigger);

		Rainbow::ColliderCache OverlapSphere(const float radius, const Rainbow::Vector3f pos , unsigned int mask, bool isIgnoreTrigger);



		bool SweepBox(const Rainbow::Vector3f& center, const Rainbow::Vector3f& shape, const Rainbow::Vector3f& direction, const Rainbow::Vector3f angle, const float distance, Rainbow::RaycastHit& hitInfo,unsigned int mask, bool isIgnoreTrigger);


		Rainbow::RaycastHits SweepBoxAll(const Rainbow::Vector3f& center, const Rainbow::Vector3f& shape, const Rainbow::Vector3f& direction, const Rainbow::Vector3f angle, const float distance,  unsigned int mask , bool isIgnoreTrigger);


		bool SweepCapsule(const float radius, const Rainbow::Vector3f& p0, const Rainbow::Vector3f& p1, const Rainbow::Vector3f& dir, const float distance, Rainbow::RaycastHit& hitInfo , unsigned int mask , bool isIgnoreTrigger);

	
		Rainbow::RaycastHits SweepCapsuleAll(const float radius, const Rainbow::Vector3f& p0, const Rainbow::Vector3f& p1, const Rainbow::Vector3f& dir, const float distance, unsigned int mask , bool isIgnoreTrigger);

		
		bool SweepSphere(const float radius, const Rainbow::Vector3f& center, const Rainbow::Vector3f& direction , const float distance,Rainbow::RaycastHit& hitInfo,unsigned int mask , bool isIgnoreTrigger);

		
		Rainbow::RaycastHits SweepSphereAll(const float radius, const Rainbow::Vector3f& center, const Rainbow::Vector3f& direction , const float distance, unsigned int mask , bool isIgnoreTrigger);

		bool EnableCCD() { return m_bEnableCCD; };

		void IgnoreCollision(int layer1, int layer2, bool ignore);
		void SetWorld(World* pWorld) { m_pWorld = pWorld; }
	private:
		//box形状
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& halfextents, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , const Rainbow::Vector3f* relativePose , bool enableGravity , Rainbow::GameObject* go);

		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const Rainbow::Vector3f* halfextents, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , Rainbow::GameObject* go);

		//sphere形状
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , const Rainbow::Vector3f* relativePose , bool enableGravity , Rainbow::GameObject* go);

		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , Rainbow::GameObject* go);

		//capsule形状
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float halfheight, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , const Rainbow::Vector3f* relativePose , int relativePoseFlag , bool enableGravity , Rainbow::GameObject* go);

		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, const Rainbow::Vector3f* localpos, const float* halfheight, const float* radius, UInt32 count, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , Rainbow::GameObject* go);

		//三角形面片
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, void* verts, UInt32 vertexnum, void* indices, UInt32 nbTriangles, bool _32bitsindex, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata , bool enableGravity , Rainbow::GameObject* go, bool convex = false);

		//三角形面片(直接用SharePtr<Mesh>)
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, Rainbow::SharePtr<Rainbow::Mesh> mesh, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, bool enableGravity, Rainbow::GameObject* go, bool convex = false);
	private:
		template<typename RigidType>
		RigidType* _AddRigidActor(Rainbow::RigidBaseActor::RigidActor_Type type, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float staticFriction, float dynamicFriction, float restitution, void* userdata,Rainbow::GameObject* go);

		template<typename ActorType>
		Rainbow::RigidDynamicActor* _AddRigidDynamicActor(Rainbow::RigidBaseActor::RigidActor_Type type, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float staticFriction, float dynamicFriction, float restitution, void* userdata, float mass, bool useGravity, bool kinemaitc, bool enableCCD, bool b, Rainbow::GameObject* go);

		Rainbow::RigidDynamicActor* _CreateVehicleActor(VehicleArticulation* vehicleArticulation, Rainbow::RigidDynamicActor* parent, const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& q, float radius, float staticFriction, float dynamicFriction, float restitution, float mass, bool kinematic, void* userdata, Rainbow::GameObject* go);
	protected:
		PhysXScene();
	private:
		friend class PhysXManager;
		bool                                              m_bNeedUpdate;
		core::hash_map<RoleController*, RoleController*>        m_mapController;
		Rainbow::SharePtr<Rainbow::PhysicMaterial>                                       m_pControllerMaterial;
		core::hash_map<Rainbow::RigidBaseActor*, Rainbow::PPtr<Rainbow::RigidBaseActor> >        m_mapRigidActor;
		core::hash_map<MINIW::Joint*, MINIW::Joint*>                          m_mapJoint;
		core::hash_map<ArticulationJoint*, ArticulationJoint*>  m_mapArticulationJoint;
		bool                                              m_bEnableCCD;
		VehicleArticulationManager						  m_VehicleManager;
		World* m_pWorld = NULL;
	public:
		static const UInt32 g_MaxGroupID = 32;
	}; //tolua_exports

	class EXPORT_SANDBOXENGINE PhysXManager //tolua_exports
	{ //tolua_exports
	public:
		enum {
			TOUCH_COLLIDER = 0,
			TOUCH_TRIGGER = 1
		};

		enum {
			TOUCH_EXIT = 1,
			TOUCH_ENTER = 1 << 1,
			TOUCH_ENTER_FINISH = 1 << 2,
			TOUCH_STAY = 1 << 3,
		};

		struct TouchInfo
		{
			TouchInfo()
			{
			}

			TouchInfo(Rainbow::Vector3f inPos, Rainbow::Vector3f inNormal, MNSandbox::WeakRef<MNSandbox::SandboxNode> src, MNSandbox::WeakRef<MNSandbox::SandboxNode> dst, unsigned int inState, unsigned int touchType, bool isRoleTouch, unsigned int step_)
			{
				pos = inPos;
				normal = inNormal;
				srcObj = src;
				dstObj = dst;
				state = inState;
				touch_type = touchType;
				this->isRoleTouch = isRoleTouch;
				step = step_;
			}

			TouchInfo(const TouchInfo& touchInfo)
			{
				pos = touchInfo.pos;
				normal = touchInfo.normal;
				srcObj = touchInfo.srcObj;
				dstObj = touchInfo.dstObj;
				state = touchInfo.state;
				isRoleTouch = touchInfo.isRoleTouch;
				step = touchInfo.step;
			}

			Rainbow::Vector3f pos;
			Rainbow::Vector3f normal;
			MNSandbox::WeakRef<MNSandbox::SandboxNode> srcObj;
			MNSandbox::WeakRef<MNSandbox::SandboxNode> dstObj;
			unsigned int state{ 0 };
			unsigned int touch_type{ TOUCH_COLLIDER };
			unsigned int step{ 0 };
			bool isRoleTouch{ false };
		};
	private:
		struct TouchPairID
		{
			TouchPairID(unsigned long long& firstID, unsigned long long& secondID)
			{
				if (firstID >= secondID)
				{
					this->firstID = firstID;
					this->secondID = secondID;
				}
				else {
					this->secondID = firstID;
					this->firstID = secondID;
				}
			}

			bool operator ==(const TouchPairID& dst) const
			{
				return (firstID == dst.firstID && secondID == dst.secondID);
			}

			bool operator < (const TouchPairID& dst) const
			{
				if (this->firstID < dst.firstID)
				{
					return true;
				}
				else if (this->firstID == dst.firstID) {
					return this->secondID < dst.secondID;
				}

				return false;
			}


			unsigned long long firstID;
			unsigned long long secondID;

		};

	public:
		
		static PhysXManager& GetInstance();
		static PhysXManager* GetInstancePtr();
		PhysXManager();
		virtual ~PhysXManager();

		Rainbow::SharePtr<Rainbow::PhysicMaterial> GetOrCreatePhysicMaterial(float staticFriction, float dynamicFriction, float restitution);


		//tolua_begin
		//初始化物理引擎
		bool                 Init();

		//更新
		void                 FrameMove(float elapsedtime);
		//tolua_end

		//创建物理场景
		bool                 CreatePhysXScene(const std::string& name);

		//获取物理场景
		PhysXScene*          GetPhysXScene(const std::string& name);

		//删除物理场景
		void                 DeletePhysXScene(const std::string& name);

		//删除所有物理场景
		void                 DeleteAllPhysXScene();

		//tolua_begin
		//载具管理器更新
		void UpdateVehicleManager(float dtime);

		void SwitchChannelEngine();

		void SwitchChannelWheel();
		//tolua_end

		//void DebugVehicle(UIRenderer *uiRenderer, const MINIW::HHFONT& debugInfoFont, DebugRenderer* debugRenderer);

		void IgnoreCollision(int layer1, int layer2, bool ignore);
		bool GetIgnoreCollision(int layer1, int layer2);


		void OnFixedUpdate();

	public:

		void OnEnterTouchRecard( Rainbow::Collider* src , Rainbow::Collider* dst , const Rainbow::Vector3f& pos , const Rainbow::Vector3f& normal , unsigned int touchType = TOUCH_COLLIDER);
		void OnStayTouchRecard( Rainbow::Collider* src, Rainbow::Collider* dst, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& normal, unsigned int step = 0, unsigned int touchType = TOUCH_COLLIDER);
		void OnExitTouchRecard(Rainbow::Collider* src, Rainbow::Collider* dst, unsigned int touchType = TOUCH_COLLIDER);
		void ProcessTouchRecard();
		std::list<TouchInfo>& GetTouchSendOut();
		void ClearTouchSendOut();
		virtual void PostTick();
		void PreUpdate(float dt);
	public:
		void setProcessTouch()
		{
			m_CanProcessTouch = true;
		}
		void InitCollisionMatrix();

	private:
		core::hash_map<core::string,Rainbow::PPtr<Rainbow::PhysicMaterial>> m_mapPxMaterials;
		std::list<TouchInfo> m_listTouchRecardInfoNormal;
		std::map<TouchPairID, TouchInfo > m_mapTouchRecardInfo;
		std::map<std::string, PhysXScene*>                                      m_mapPhysXScene;
		UInt32																	m_DebugVehicleChannelEngine;
		UInt32																	m_DebugVehicleChannelWheel;
		bool                                                                    m_CanProcessTouch;
		MNSandbox::ListenerClass<PhysXManager> m_listenPostTick; // 游戏刻

		MNSandbox::ListenerClass<PhysXManager,float> m_listenPreUpdate; // 游戏刻

		std::list<TouchInfo> m_SendOutList;
	}; //tolua_exports

	DECLARE_GETMETHOD(PhysXManager)
	EXPORT_SANDBOXENGINE void SafelyDestroyPhysXManager();

} //tolua_exports





