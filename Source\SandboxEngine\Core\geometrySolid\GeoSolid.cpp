#include "GeoSolid.h"

#include "geometrySolid/GeometryMath.h"
#include "geometrySolid/Cuboid.h"
#include "geometrySolid/TriangularPrism.h"
#include "geometrySolid/RectangularPyramid.h"
#include "geometrySolid/Cylinder.h"
#include "geometrySolid/Cone.h"
#include "geometrySolid/Sphere.h"
#include "geometrySolid/CompositeGeoSolid.h"
#include "geometrySolid/Rectangle.h"
#include "nodes/SandboxSurfaceObject.h"
#include "nodes/SandboxGeoSolid.h"

#include "GeoSolid_generated.h"
#include "actors/helper/ClientActorHelper.h"

#include "Components/MeshRenderer.h"
#include "Render/ShaderMaterial/MaterialManager.h"

#include <limits>
#include <functional>

namespace MNSandbox { namespace GeometrySolid {

	using namespace Rainbow;
	using namespace GeometryMath;

	GeoSolidArray<PrimitiveVertex> GeoSolid::s_daPvsEmpty(0);
	GeoSolidArray<UInt32> GeoSolid::s_daPisEmpty(0);

	ShaderChannelMask GeoSolid::s_ShaderChannelMask = kShaderChannelMaskVertex | kShaderChannelMaskNormal | kShaderChannelMaskColor | kShaderChannelMaskTexCoord0;

	GeoSolid::GeoSolid() : m_eGss(GeoSolidShape::NONE)
	{
	}

	GeoSolid::~GeoSolid()
	{
		OPTICK_EVENT();
		m_daVertices.clear();
		m_daPrimitiveVertices.clear();
		m_daPrimitiveIndices.clear();
		m_daBoxBounds.clear();
		m_daUvs.clear();
		#if COMPOSITE_ELEMENT_USE_POINTER
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle* gst = m_daTriangles[it];
			GeoSolidTriangle::recycle(gst);
			gst = m_daTriangles[it] = nullptr;
		}
		const GsIe ce = GetEdgeCount();
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge* gse = m_daEdges[ie];
			GeoSolidEdge::recycle(gse);
			gse = m_daEdges[ie] = nullptr;
		}
		const GsIv cv = GetVertexCount();
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			GeoSolidVertex* gsv = m_daGeoSolidVertices[iv];
			GeoSolidVertex::recycle(gsv);
			gsv = m_daGeoSolidVertices[iv] = nullptr;
		}
		m_daTriangles.clear();
		m_daEdges.clear();
		m_daGeoSolidVertices.clear();
		#else
		m_daTriangles.clear();
		m_daEdges.clear();
		m_daGeoSolidVertices.clear();
		#endif
		for (auto it = m_daMeshes.begin(); it != m_daMeshes.end(); ) { it = m_daMeshes.erase(it); }
		m_mesh = nullptr;
	}

	void GeoSolid::LeaveWorld()
	{
		for (auto p : Cylinder::s_mapSizeToInstances)
		{
			GeoSolid* gs = p.second;
			if (gs == Cylinder::getSingletonPtr())
			{
				continue;
			}
			SANDBOX_DELETE(gs);
			p.second = nullptr;
		}
		Cylinder::s_mapSizeToInstances.clear();

		for (auto p : Cone::s_mapSizeToInstances)
		{
			GeoSolid* gs = p.second;
			if (gs == Cone::getSingletonPtr())
			{
				continue;
			}
			SANDBOX_DELETE(gs);
			p.second = nullptr;
		}
		Cone::s_mapSizeToInstances.clear();

		for (auto p : Sphere::s_mapSizeToInstances)
		{
			GeoSolid* gs = p.second;
			if (gs == Sphere::getSingletonPtr())
			{
				continue;
			}
			SANDBOX_DELETE(gs);
			p.second = nullptr;
		}
		Sphere::s_mapSizeToInstances.clear();

		Cuboid::destroySingleton();
		TriangularPrism::destroySingleton();
		RectangularPyramid::destroySingleton();
		Cylinder::destroySingleton();
		Cone::destroySingleton();
		Sphere::destroySingleton();

		LogStringMsg("GeoSolidVertex::s_PoolSize = %d", GeoSolidVertex::s_PoolSize);
		LogStringMsg("GeoSolidEdge::s_PoolSize = %d", GeoSolidEdge::s_PoolSize);
		LogStringMsg("GeoSolidTriangle::s_PoolSize = %d", GeoSolidTriangle::s_PoolSize);
		LogStringMsg("GeoSolidSubMesh::s_PoolSize = %d", GeoSolidSubMesh::s_PoolSize);
		GeoSolidVertex::releaseAll();
		GeoSolidEdge::releaseAll();
		GeoSolidTriangle::releaseAll();
		GeoSolidSubMesh::releaseAll();
	}

	void GeoSolid::UpdateVerticesEdgesTriangles(
		GeoSolidVertexArray& daGeoSolidVertices,
		GeoSolidTriangleArray& daTriangles,
		GeoSolidEdgeArray& daEdges,
		GeoSolidArray<GsVector3>& daVs)
	{
		const GsIndex cv = daVs.size();
		for (GsIndex iv = 0; iv < cv; ++iv)
		{
			//GeoSolidVertex* gsv = SANDBOX_NEW(GeoSolidVertex);
			GeoSolidVertex* gsv = GeoSolidVertex::obtain();
			gsv->v = gsv->vInSrc = gsv->vInCut = daVs[iv];
			daGeoSolidVertices.emplace_back(gsv);
		}
		const GsIndex ct = daTriangles.size();
		for (GsIndex it = 0; it < ct; ++it)
		{
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidTriangle& gst = *daTriangles[it];
			GeoSolidVertex& gsv0 = *daGeoSolidVertices[(GsIndex)gst.iv0];
			GeoSolidVertex& gsv1 = *daGeoSolidVertices[(GsIndex)gst.iv1];
			GeoSolidVertex& gsv2 = *daGeoSolidVertices[(GsIndex)gst.iv2];
			#else
			GeoSolidTriangle& gst = daTriangles[it];
			GeoSolidVertex& gsv0 = daGeoSolidVertices[(GsIndex)gst.iv0];
			GeoSolidVertex& gsv1 = daGeoSolidVertices[(GsIndex)gst.iv1];
			GeoSolidVertex& gsv2 = daGeoSolidVertices[(GsIndex)gst.iv2];
			#endif
			GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(gst.iv0, gst.iv1);
			GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(gst.iv0, gst.iv2);
			GeoSolidEdge* gse2 = GeoSolid::obtain<GeoSolidEdge>(gst.iv1, gst.iv2);
			GsIe ie0 = AddEdge(daEdges, gse0);
			GsIe ie1 = AddEdge(daEdges, gse1);
			GsIe ie2 = AddEdge(daEdges, gse2);
			gst.ie0 = ie0;
			gst.ie1 = ie1;
			gst.ie2 = ie2;
		}
	}

	GsIe GeoSolid::AddEdge(GeoSolidEdgeArray& daEdges, const GeoSolidEdge* gse)
	{
		int index = FindEdge(daEdges, gse->iv0, gse->iv1);
		if (index >= 0 && index < daEdges.size())
		{
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidEdge& gse0 = *daEdges[index];
			#else
			GeoSolidEdge& gse0 = daEdges[index];
			#endif
			gse0.intersectSrc = gse->intersectSrc;
			gse0.intersectCut = gse->intersectCut;
			return (GsIe)index;
		}
		GeoSolidEdge* gseNew = GeoSolid::obtain<GeoSolidEdge>(gse);
		daEdges.emplace_back(gseNew);
		const GsIndex ieNew = daEdges.size() - 1;
		return (GsIe)ieNew;
	}

	int GeoSolid::FindEdge(GeoSolidEdgeArray& daEdges, const GsIv iv0, const GsIv iv1)
	{
		int index = -1;
		for (int i = daEdges.size() - 1; i >= 0; --i)
		{
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidEdge& gse = *daEdges[i];
			#else
			GeoSolidEdge& gse = daEdges[i];
			#endif
			if ((gse.iv0 == iv0 && gse.iv1 == iv1) || 
				(gse.iv0 == iv1 && gse.iv1 == iv0))
			{
				index = i;
				break;
			}
		}
		return index;
	}

	GeoSolidVertex& GeoSolid::GetVertex(const GsIv& iv)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		return *m_daGeoSolidVertices[(GsIndex)iv];
		#else
		return *m_daGeoSolidVertices[iv];
		#endif
		#else
		#if COMPOSITE_GEO_SOLID_DEBUG
		return m_daGeoSolidVertices[(GsIndex)iv];
		#else
		return m_daGeoSolidVertices[iv];
		#endif
		#endif
	}

	const GeoSolidVertex& GeoSolid::GetVertex(const GsIv& iv) const
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		return *m_daGeoSolidVertices[(GsIndex)iv];
		#else
		return *m_daGeoSolidVertices[iv];
		#endif
		#else
		#if COMPOSITE_GEO_SOLID_DEBUG
		return m_daGeoSolidVertices[(GsIndex)iv];
		#else
		return m_daGeoSolidVertices[iv];
		#endif
		#endif
	}

	bool GeoSolid::RemoveVertex(const GsIv& iv)
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		GsIndex i = (GsIndex)iv;
		#else
		GsIndex i = iv;
		#endif
		//GeoSolidVertex* gsv = m_daGeoSolidVertices[i];
		//SANDBOX_DELETE(gsv);
		//m_daGeoSolidVertices[i] = nullptr;
		m_daGeoSolidVertices.erase(m_daGeoSolidVertices.begin() + i);
		return true;
	}

	GsIv GeoSolid::GetVertexCount()
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		return (GsIv)m_daGeoSolidVertices.size();
		#else
		return m_daGeoSolidVertices.size();
		#endif
	}

	GsIv GeoSolid::GetVertexCount() const
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		return (GsIv)m_daGeoSolidVertices.size();
		#else
		return m_daGeoSolidVertices.size();
		#endif
	}

	void GeoSolid::SetNullVertex(const GsIv& iv)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		m_daGeoSolidVertices[(GsIndex)iv] = nullptr;
		#else
		m_daGeoSolidVertices[iv] = nullptr;
		#endif
		#endif
	}

	int GeoSolid::FindVertex(const GeoSolidVertex& gsvMain, const GsDigit errorThreshold)
	{
		const GsIv cv = GetVertexCount();
		GeoSolidArray<GsIv> daIvsNear;
		GeoSolidArray<GsDigit> daDs;
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			GeoSolidVertex& gsv = GetVertex(iv);
			const GsDigit d = Distance(gsv.v, gsvMain.v);
			//if (IsVertexEqual(gsv.v, gsvMain.v, errorThreshold))
			if (d <= errorThreshold)
			{
				daIvsNear.emplace_back(iv);
				daDs.emplace_back(d);
			}
		}
		if (daIvsNear.empty())
		{
			return -1;
		}
		GsIv ivNear = daIvsNear[0];
		GsDigit min = daDs[0];
		const UInt32 c = daIvsNear.size();
		for (int i = 1; i < c; ++i)
		{
			if (daDs[i] < min)
			{
				//找距离最近的坐标
				ivNear = daIvsNear[i];
				min = daDs[i];
			}
		}
		return (int)ivNear;
	}

	GeoSolidEdge& GeoSolid::GetEdge(const GsIe& ie)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		return *m_daEdges[(GsIndex)ie];
		#else
		return *m_daEdges[ie];
		#endif
		#else
		#if COMPOSITE_GEO_SOLID_DEBUG
		return m_daEdges[(GsIndex)ie];
		#else
		return m_daEdges[ie];
		#endif
		#endif
	}

	bool GeoSolid::RemoveEdge(const GsIe& ie)
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		const GsIndex i = (GsIndex)ie;
		#else
		const GsIndex i = ie;
		#endif
		#if COMPOSITE_ELEMENT_USE_POINTER
		GeoSolidEdge* gse = m_daEdges[i];
		SANDBOX_DELETE(gse);
		m_daEdges[i] = nullptr;
		#endif
		m_daEdges.erase(m_daEdges.begin() + i);
		return true;
	}

	GsIe GeoSolid::GetEdgeCount()
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		return (GsIe)m_daEdges.size();
		#else
		return m_daEdges.size();
		#endif
	}

	void GeoSolid::SetNullEdge(const GsIe& ie)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		GsIndex i = (GsIndex)ie;
		#else
		GsIndex i = ie;
		#endif
		m_daEdges[i] = nullptr;
		#endif
	}

	GeoSolidTriangle& GeoSolid::GetTriangle(const GsIt& it)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		return *m_daTriangles[(GsIndex)it];
		#else
		return *m_daTriangles[it];
		#endif
		#else
		#if COMPOSITE_GEO_SOLID_DEBUG
		return m_daTriangles[(GsIndex)it];
		#else
		return m_daTriangles[it];
		#endif
		#endif
	}

	const GeoSolidTriangle& GeoSolid::GetTriangle(const GsIt& it) const
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		return *m_daTriangles[(GsIndex)it];
		#else
		return *m_daTriangles[it];
		#endif
		#else
		#if COMPOSITE_GEO_SOLID_DEBUG
		return m_daTriangles[(GsIndex)it];
		#else
		return m_daTriangles[it];
		#endif
		#endif
	}

	bool GeoSolid::RemoveTriangle(const GsIt& it)
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		GsIndex i = (GsIndex)it;
		#else
		GsIndex i = it;
		#endif
		#if COMPOSITE_ELEMENT_USE_POINTER
		GeoSolidTriangle* gst = m_daTriangles[i];
		SANDBOX_DELETE(gst);
		m_daTriangles[i] = nullptr;
		#endif
		m_daTriangles.erase(m_daTriangles.begin() + i);
		return true;
	}

	GsIt GeoSolid::GetTriangleCount()
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		return (GsIt)m_daTriangles.size();
		#else
		return m_daTriangles.size();
		#endif
	}

	GsIt GeoSolid::GetTriangleCount() const
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		return (GsIt)m_daTriangles.size();
		#else
		return m_daTriangles.size();
		#endif
	}

	void GeoSolid::SetNullTriangle(const GsIt& it)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		#if COMPOSITE_GEO_SOLID_DEBUG
		GsIndex i = (GsIndex)it;
		#else
		GsIndex i = it;
		#endif
		m_daTriangles[i] = nullptr;
		#endif
	}

	int GeoSolid::FindTriangle(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2)
	{
		//TODO: 2023-04-14 16:52:07: 三角形较多时的优化
		int index = -1;
		for (int i = m_daTriangles.size() - 1; i >= 0; --i)
		{
			GeoSolidTriangle& gst = GetTriangle((GsIt)i);
			//有顺序
			if ((gst.iv0 == iv0 && gst.iv1 == iv1 && gst.iv2 == iv2) ||
				(gst.iv0 == iv1 && gst.iv1 == iv2 && gst.iv2 == iv0) ||
				(gst.iv0 == iv2 && gst.iv1 == iv0 && gst.iv2 == iv1))
			{
				index = i;
				break;
			}
		}
		return index;
	}

	SharePtr<Mesh> GeoSolid::CreateMesh(const GeoSolidArray<PrimitiveVertex>& daPvs, const GeoSolidArray<UInt32>& daPis)
	{
		return CreateMesh(daPvs, daPis, daPvs.size(), daPis.size(), 0, 0);
	}

	SharePtr<Mesh> GeoSolid::CreateMesh(const GeoSolidArray<PrimitiveVertex>& daPvs, const GeoSolidArray<UInt32>& daPis,
		const int cpv, const int cpi, const int opv, const int opi)
	{
		if (daPvs.empty() || daPis.empty())
		{
			return nullptr;
		}
		if (cpv < 0 || cpi < 0)
		{
			return nullptr;
		}
		if (opv < 0 || opi < 0)
		{
			return nullptr;
		}

		SharePtr<Mesh> mesh = MoveToSharePtr(CreateObjectWithLabel<Mesh>(kMemMesh));
		mesh->GetWritableIndexFormat() = kIndexFormat32;
		mesh->GetSharedMeshData()->AllocMemory(cpv, cpi, s_ShaderChannelMask);

		VertexData& vd = mesh->GetSharedMeshData()->GetVertexData();
		memcpy(vd.GetDataPtr(), &daPvs[opv], cpv * sizeof(PrimitiveVertex));
		SharedMeshData::IndexContainer& ic = mesh->GetSharedMeshData()->GetIndexBuffer();
		if (opi == 0)
		{
			memcpy(ic.data(), daPis.data(), cpi * sizeof(UInt32));
		}
		else
		{
			dynamic_array<UInt32> daPisSingle;
			daPisSingle.resize_uninitialized(cpi);
			memcpy(daPisSingle.data(), &daPis[opi], cpi * sizeof(UInt32));
			for (int i = 0; i < cpi; ++i)
			{
				daPisSingle[i] -= opv;
			}
			memcpy(ic.data(), daPisSingle.data(), cpi * sizeof(UInt32));
		}
		SharedMeshData::SubMeshContainer& container = mesh->GetSharedMeshData()->GetSubMeshes();
		container.resize_uninitialized(1);
		SubMesh& subMesh = container[0];
		subMesh.trianglesFirstIndexByte = 0;
		subMesh.trianglesIndexCount = 3;
		subMesh.firstIndexByte = 0;
		subMesh.indexCount = cpi;
		//TODO: 2023-10-10 10:37:38: 优化？更改为Triangle Strip
		subMesh.topology = GfxPrimitiveType::kPrimitiveTriangles;
		subMesh.baseVertex = 0;
		subMesh.firstVertex = 0;
		subMesh.vertexCount = cpv;
		mesh->MarkDirty();
		mesh->UploadMeshData(false);
		mesh->RecalculateBounds();
		return mesh;
	}

	GeoSolidArray<PrimitiveVertex> GeoSolid::GetPvs(Rainbow::SharePtr<Rainbow::Mesh> mesh, GeoSolidArray<PrimitiveVertex>& daPvsOrigin)
	{
		if (!daPvsOrigin.empty())
		{
			return daPvsOrigin;
		}
		if (!mesh)
		{
			return s_daPvsEmpty;
		}
		Rainbow::SharedMeshData* smd = mesh->GetSharedMeshData();
		if (!smd)
		{
			return s_daPvsEmpty;
		}
		Rainbow::VertexData& vd = smd->GetVertexData();
		GeoSolidArray<PrimitiveVertex> daPvs;
		const UInt32 cpv = vd.GetVertexCount();
		daPvs.resize(cpv);
		memcpy(daPvs.data(), vd.GetDataPtr(), cpv * sizeof(PrimitiveVertex));
		return daPvs;
	}

	GeoSolidArray<UInt32> GeoSolid::GetPis(Rainbow::SharePtr<Rainbow::Mesh> mesh, GeoSolidArray<UInt32>& daPisOrigin)
	{
		if (!daPisOrigin.empty())
		{
			return daPisOrigin;
		}
		if (!mesh)
		{
			return s_daPisEmpty;
		}
		Rainbow::SharedMeshData* smd = mesh->GetSharedMeshData();
		if (!smd)
		{
			return s_daPisEmpty;
		}
		dynamic_array<UInt8>& ib = smd->GetIndexBuffer();
		GeoSolidArray<UInt32> daPis;
		const UInt32 cpi = ib.size() * sizeof(UInt8) / sizeof(UInt32);
		daPis.resize(cpi);
		memcpy(daPis.data(), ib.data(), cpi * sizeof(UInt32));
		return daPis;
	}

	SharePtr<Mesh> GeoSolid::CopySurfaceMesh(SharePtr<Mesh> meshOrigin)
	{
		OPTICK_EVENT();
		const GfxPrimitiveType topology = GfxPrimitiveType::kPrimitiveTriangles;
		const VertexData& vdOrigin = meshOrigin->GetVertexData();
		const SharedMeshData::IndexContainer& icOrigin = meshOrigin->GetIndexBuffer();
		const int cv = vdOrigin.GetVertexCount();
		const int ci = meshOrigin->GetIndexCount();
		SharePtr<Mesh> mesh = MakeSharePtrWithLabel<Mesh>(kMemPhysics);
		mesh->GetWritableIndexFormat() = kIndexFormat32;
		mesh->GetSharedMeshData()->AllocMemory(cv, ci, s_ShaderChannelMask);

		VertexData& vd = mesh->GetSharedMeshData()->GetVertexData();
		memcpy(vd.GetDataPtr(), vdOrigin.GetDataPtr(), cv * sizeof(PrimitiveVertex));
		SharedMeshData::IndexContainer& ic = mesh->GetSharedMeshData()->GetIndexBuffer();
		memcpy(ic.data(), icOrigin.data(), ci * sizeof(UInt32));

		SharedMeshData::SubMeshContainer& container = mesh->GetSharedMeshData()->GetSubMeshes();
		container.resize_uninitialized(1);
		SubMesh& subMesh = container[0];
		subMesh.trianglesFirstIndexByte = 0;
		subMesh.trianglesIndexCount = 3;
		subMesh.firstIndexByte = 0;
		subMesh.indexCount = ci;
		subMesh.topology = topology;
		subMesh.baseVertex = 0;
		subMesh.firstVertex = 0;
		subMesh.vertexCount = cv;
		return mesh;
	}

	SharePtr<Mesh> GeoSolid::GetCopySurfaceMesh(const int& i)
	{
		SharePtr<Mesh> meshOrigin = GetSurfaceMesh(i);
		if (!meshOrigin)
		{
			return nullptr;
		}
		return CopySurfaceMesh(meshOrigin);
	}

	void GeoSolid::UpdateBound(GeoSolidVertexArray& daGeoSolidVertices, GsBoxBound& bb)
	{
		OPTICK_EVENT();
		bb.m_MinPos.Set(0, 0, 0);
		bb.m_MaxPos.Set(1, 1, 1);
		const GsIv cv = daGeoSolidVertices.size();
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			#if COMPOSITE_ELEMENT_USE_POINTER
			const GeoSolidVertex& gsv = *daGeoSolidVertices[iv];
			#else
			const GeoSolidVertex& gsv = daGeoSolidVertices[iv];
			#endif
			bb += gsv.v;
		}
	}

	Rainbow::SharePtr<Rainbow::Mesh> GeoSolid::GetMesh()
	{
		if (m_mesh) 
		{
			return m_mesh;
		}
		OPTICK_EVENT();
		{
			OPTICK_EVENT("CreateDynamic");
			CreateDynamic();
		}
		m_mesh = GeoSolid::CreateMesh(m_daPrimitiveVertices, m_daPrimitiveIndices);
		{
			OPTICK_EVENT("SeparateSurfaces")
			SeparateSurfaces();
		}
		m_daPrimitiveVertices.clear();
		m_daPrimitiveIndices.clear();
		char name_[128];
		sprintf(name_, "_%s_%u", GetCacheKey().c_str(), 0xffffffff);
		if (GetMeshCacheService())
		{
			GetMeshCacheService()->SetMeshCache(name_, m_mesh);
		}
		return m_mesh;
	}

	UInt32 GeoSolid::AddUv(GeoSolidArray<Vector2db>& daUvs, const Vector2db& uv)
	{
		const UInt32 cuv = daUvs.size();
		for (int i = cuv - 1; i >= 0; --i)
		{
			const Vector2db& uv0 = daUvs[i];
			if (IsZero(uv0.x - uv.x) && IsZero(uv0.y - uv.y))
			{
				return i;
			}
		}
		daUvs.emplace_back(uv);
		return cuv;
	}

	GeoSolid* GeoSolid::CreateGeoSolid(const GeoSolidShape& gss)
	{
		OPTICK_EVENT();
		GeoSolid* gs = nullptr;
		switch (gss)
		{
			case GeoSolidShape::CUBOID:
				gs = Cuboid::getSingletonPtr();
				break;
			case GeoSolidShape::WEDGE:
				gs = TriangularPrism::getSingletonPtr();
				break;
			case GeoSolidShape::PYRAMID:
				gs = RectangularPyramid::getSingletonPtr();
				break;
			case GeoSolidShape::CYLINDER:
				gs = Cylinder::getSingletonPtr();
				break;
			case GeoSolidShape::CONE:
				gs = Cone::getSingletonPtr();
				break;
			case GeoSolidShape::SPHERE:
				gs = Sphere::getSingletonPtr();
				break;
			case GeoSolidShape::COMPOSITE:
				gs = SANDBOX_NEW(CompositeGeoSolid);
				break;
			case GeoSolidShape::RECTANGLE:
				gs = Rectangle::getSingletonPtr();
				break;
			//case GeoSolidShape::DICE:
			//	gs = SANDBOX_NEW(Dice);
			//	break;
			//case GeoSolidShape::TRIANGLE:
			//	gs = SANDBOX_NEW(Triangle);
			//	break;
		}
		
		return gs;
	}

	SharePtr<Mesh> GeoSolid::GetCacheMesh(const ColorQuad& cq)
	{
		#ifdef IWORLD_SERVER_BUILD
		return GetMesh();
		#else
		OPTICK_EVENT();
		if (!GetMeshCacheService())
		{
			return GetMesh();
		}
		char szCacheKey[256] = { 0 };
		sprintf(szCacheKey, "_%s_%u", GetCacheKey().c_str(), cq.c);
		//std::string strCacheKey("_");
		//strCacheKey += GetCacheKey();
		//strCacheKey += '_';
		//strCacheKey += std::to_string(cq.c);
		SharePtr<Mesh> mesh = GetMeshCacheService()->GetMeshCache(szCacheKey);
		if (mesh)
		{
			return mesh;
		}
		mesh = GetMesh();
		if (!mesh)
		{
			//还没有创建好
			return nullptr;
		}
		if (cq == 0xffffffff)
		{
			return mesh;
		}
		mesh = GeoSolid::CopySurfaceMesh(mesh);
		VertexData& vd = mesh->GetVertexData();
		const int cv = vd.GetVertexCount();
		GeometrySolid::PrimitiveVertex* aPvs = (GeometrySolid::PrimitiveVertex*)vd.GetDataPtr();
		{
			OPTICK_EVENT("GeoSolid::GetCacheMesh(): set colors");
			for (int iv = 0; iv < cv; ++iv)
			{
				aPvs[iv].color.Set(cq.r, cq.g, cq.b, cq.a);
			}
		}
		mesh->MarkDirty();
		mesh->UploadMeshData(false);
		mesh->RecalculateBounds();
		GetMeshCacheService()->SetMeshCache(szCacheKey, mesh);
		return mesh;
		#endif
	}

	#if COMPOSITE_GEO_SOLID_DEBUG
	const char* GeoSolid::GetFaceName(const GeoSolidFace& eGsf)
	{
		switch (eGsf)
		{
			case GeoSolidFace::FRONT:
				return "Front";
			case GeoSolidFace::BACK:
				return "Back";
			case GeoSolidFace::LEFT:
				return "Left";
			case GeoSolidFace::RIGHT:
				return "Right";
			case GeoSolidFace::TOP:
				return "Top";
			case GeoSolidFace::BOTTOM:
				return "Bottom";
			case GeoSolidFace::SLOPE:
				return "Slope";
			case GeoSolidFace::SIDE:
				return "Side";
			default:
				return "Unknown";
		}
	}

	const char* GeoSolid::GetShapeName(const GeoSolidShape& eGss)
	{
		switch (eGss)
		{
			case GeoSolidShape::CUBOID:
				return "Cuboid";
			case GeoSolidShape::WEDGE:
				return "Wedge";
			case GeoSolidShape::PYRAMID:
				return "Pyramid";
			case GeoSolidShape::CYLINDER:
				return "Cylinder";
			case GeoSolidShape::CONE:
				return "Cone";
			case GeoSolidShape::SPHERE:
				return "Sphere";
			case GeoSolidShape::COMPOSITE:
				return "Composite";
			case GeoSolidShape::RECTANGLE:
				return "Rectangle";
			default:
				return "Unknown";
		}
	}
	#endif

	const char* GeoSolid::GetSurfaceName(const SceneModelObject::Surface& sf)
	{
		static std::map<SceneModelObject::Surface, const char*> g_mSurfaceNames = {
			{SceneModelObject::Surface::FACE1, "Face1"},
			{SceneModelObject::Surface::FACE2, "Face2"},
			{SceneModelObject::Surface::FACE3, "Face3"},
			{SceneModelObject::Surface::FACE4, "Face4"},
			{SceneModelObject::Surface::FACE5, "Face5"},
			{SceneModelObject::Surface::FACE6, "Face6"},
			{SceneModelObject::Surface::FACE7, "Face7"},
			{SceneModelObject::Surface::FACE8, "Face8"},
			{SceneModelObject::Surface::FACE9, "Face9"},
		};
		return g_mSurfaceNames[sf];
	}

	//=====================================================================================================//

	GeoSolidInteractState::GeoSolidInteractState()
		: from(false)
		, inside(false)
		, coincide(false)
		, hasCheckedInside(false)
		, hasCheckedCoincide(false)
	{

	}

	//=====================================================================================================//


	#if GEO_SOLID_RECYCLE
	IMPLEMENT_THREAD_FLYWEIGHT_PATTERN(GeoSolidVertex)
	#else
	IMPLEMENT_NON_FLYWEIGHT_PATTERN(GeoSolidVertex)
	#endif
	int GeoSolidVertex::s_MaxPoolSize = 99999;
	GeoSolidVertex::GeoSolidVertex()
		: isDrawn(false)
	{ 
	}

	GeoSolidVertex::~GeoSolidVertex()
	{
	}

	void GeoSolidVertex::Copy(const GeoSolidVertex& gsv)
	{
		v = gsv.v;
		vInSrc = gsv.vInSrc;
		vInCut = gsv.vInCut;
		intersectSrc = gsv.intersectSrc;
		intersectCut = gsv.intersectCut;
		isDrawn = gsv.isDrawn;
		listNeighbors = gsv.listNeighbors;
	}

	void GeoSolidVertex::Set(const GsVector3& v0, const bool fromSrc, const bool fromCut)
	{
		v = v0;
		isDrawn = false;
		intersectSrc.from = fromSrc;
		intersectCut.from = fromCut;
	}

	void GeoSolidVertex::Set(const GeoSolidVertex* gsv)
	{
		Copy(*gsv);
	}

	void GeoSolidVertex::Set(const GeoSolidVertex& gsv)
	{
		Copy(gsv);
	}

	GeoSolidVertex& GeoSolidVertex::operator=(const GeoSolidVertex& gsv)
	{
		Copy(gsv);
		return *this;
	}

	void GeoSolidVertex::onRecycle()
	{
		v.SetZero();
		vInSrc.SetZero();
		vInCut.SetZero();
		isDrawn = false;
		intersectSrc.reset();
		intersectCut.reset();
		std::list<GeoSolidVertexNeighbor> listNs;
		listNeighbors.swap(listNs);
	}

	//=====================================================================================================//

	#if GEO_SOLID_RECYCLE
	IMPLEMENT_THREAD_FLYWEIGHT_PATTERN(GeoSolidEdge)
	#else
	IMPLEMENT_NON_FLYWEIGHT_PATTERN(GeoSolidEdge)
	#endif
	int GeoSolidEdge::s_MaxPoolSize = 99999;
	GeoSolidEdge::GeoSolidEdge() 
		: isDrawn(false)
	{

	}

	GeoSolidEdge::~GeoSolidEdge()
	{
	}

	bool GeoSolidEdge::Is(const GsIv& iv0, const GsIv& iv1) const
	{
		return
			(this->iv0 == iv0 && this->iv1 == iv1) ||
			(this->iv0 == iv1 && this->iv1 == iv0)
		;
	}

	void GeoSolidEdge::Copy(const GeoSolidEdge& gse)
	{
		iv0 = gse.iv0;
		iv1 = gse.iv1;
		intersectSrc = gse.intersectSrc;
		intersectCut = gse.intersectCut;
		isDrawn = gse.isDrawn;
	}

	void GeoSolidEdge::Set(const GsIv& iv0, const GsIv& iv1, const bool src, const bool cut)
	{
		this->iv0 = iv0;
		this->iv1 = iv1;
		isDrawn = false;
		intersectSrc.from = src;
		intersectCut.from = cut;
	}

	void GeoSolidEdge::Set(const GsIv& iv0, const GsIv& iv1)
	{
		Set(iv0, iv1, false, false);
	}

	void GeoSolidEdge::Set(const GeoSolidEdge* gse)
	{
		Copy(*gse);
	}

	void GeoSolidEdge::Set(const GeoSolidEdge& gse)
	{
		Copy(gse);
	}

	GeoSolidEdge& GeoSolidEdge::operator=(const GeoSolidEdge& gse)
	{
		Copy(gse);
		return *this;
	}

	void GeoSolidEdge::onRecycle()
	{
		isDrawn = false;
		intersectSrc.reset();
		intersectCut.reset();
	}

	//=====================================================================================================//

	#if GEO_SOLID_RECYCLE
	IMPLEMENT_THREAD_FLYWEIGHT_PATTERN(GeoSolidTriangle)
	#else
	IMPLEMENT_NON_FLYWEIGHT_PATTERN(GeoSolidTriangle)
	#endif
	int GeoSolidTriangle::s_MaxPoolSize = 99999;
	//int GeoSolidTriangle::s_PoolSize = 0;
	//GeoSolidTriangle* GeoSolidTriangle::head = 0;
	//GeoSolidTriangle* GeoSolidTriangle::tail = 0;
	//Rainbow::ReadWriteLock* GeoSolidTriangle::s_lock = ENG_NEW_LABEL(Rainbow::ReadWriteLock, kMemScene);
	//void GeoSolidTriangle::recycle(GeoSolidTriangle*& instance) {
	//	Rainbow::ReadWriteLock::AutoWriteLock lock(*s_lock);
	//	if (!instance) {
	//		return;
	//	}
	//	if (s_PoolSize >= s_MaxPoolSize) {
	//		SANDBOX_DELETE(instance);
	//		return;
	//	}
	//	++s_PoolSize;
	//	if (tail) {
	//		tail->next = instance;
	//	} 
	//	tail = instance; 
	//	if (!head) {
	//		head = tail;
	//	}
	//	instance = 0;
	//	//LogStringMsg("GST::recycle(): pool = %d", s_PoolSize);
	//}

	//GeoSolidTriangle* GeoSolidTriangle::obtain() {
	//	Rainbow::ReadWriteLock::AutoWriteLock lock(*s_lock);
	//	GeoSolidTriangle* cur = head;
	//	if (cur != 0) {
	//		head = cur->next;
	//		cur->next = 0;
	//		--s_PoolSize;
	//		cur->onRecycle();
	//		if (!head) {
	//			tail = 0;
	//		}
	//		//LogStringMsg("GST::obtain():  pool = %d", s_PoolSize);
	//		return cur;
	//	}
	//	cur = SANDBOX_NEW(GeoSolidTriangle);
	//	cur->next = 0;
	//	return cur;
	//}
	//void GeoSolidTriangle::releaseAll() {
	//	Rainbow::ReadWriteLock::AutoWriteLock lock(*s_lock);
	//	GeoSolidTriangle* cur = head; 
	//	while (cur) {
	//		GeoSolidTriangle* old = cur;
	//		cur = cur->next;
	//		old->next = 0;
	//		SANDBOX_DELETE(old);
	//	} 
	//	s_PoolSize = 0;
	//	head = 0;
	//	tail = 0;
	//	//LogString("GST::releaseAll(): pool = 0");
	//}

	GeoSolidTriangle::GeoSolidTriangle()
	{
	}

	GeoSolidTriangle::~GeoSolidTriangle()
	{
		#if COMPOSITE_STD_USE_POINTER
		SANDBOX_DELETE(setNewIvs);
		SANDBOX_DELETE(setIesDivider);
		SANDBOX_DELETE(setCoincidentIts);
		#endif
	}

	void GeoSolidTriangle::Copy(const GeoSolidTriangle& gst)
	{
		iv0 = gst.iv0;
		iv1 = gst.iv1;
		iv2 = gst.iv2;
		ie0 = gst.ie0;
		ie1 = gst.ie1;
		ie2 = gst.ie2;
		ibb = gst.ibb;
		ivc = gst.ivc;
		ic = gst.ic;
		im = gst.im;
		ivn = gst.ivn;
		area = gst.area;
		iuv0 = gst.iuv0;
		iuv1 = gst.iuv1;
		iuv2 = gst.iuv2;
		uIndex = gst.uIndex;
		vIndex = gst.vIndex;
		gss = gst.gss;
		gsf = gst.gsf;
		intersectSrc = gst.intersectSrc;
		intersectCut = gst.intersectCut;
		markRemoval = gst.markRemoval;
		turnover = gst.turnover;
		#if COMPOSITE_STD_USE_POINTER
		if (!setNewIvs)
		{
			setNewIvs = SANDBOX_NEW(GeoSolidSet<GsIv>, gst.GetNewIvs());
		}
		#else
		setNewIvs = gst.setNewIvs;
		#endif
		setCoincidentIts = gst.setCoincidentIts;
	}

	void GeoSolidTriangle::Set(const GsIndex iv0, const GsIndex iv1, const GsIndex iv2)
	{
		Set(GsIv(iv0), GsIv(iv1), GsIv(iv2), 0, 0, 0, 0, 1, 0, 0, gss, gsf, 0, false, false, 0);
	}

	void GeoSolidTriangle::Set(const GsIndex iv0, const GsIndex iv1, const GsIndex iv2,
		const UInt32& iuv0, const UInt32& iuv1, const UInt32& iuv2,
		const UInt8& uIndex, const UInt8& vIndex, const GeoSolidShape& gss, const GeoSolidFace& gsf)
	{
		//ivn在基础模型中不使用。在组合模型中，会按旋转重新计算一次法线来赋值。
		//ibb、im、ic在基础模型中不使用。在组合模型中，会根据外部节点各自的值来赋值。
		Set(GsIv(iv0), GsIv(iv1), GsIv(iv2), iuv0, iuv1, iuv2, uIndex, vIndex, 0, 0, gss, gsf, 0, false, false, 0);
	}

	void GeoSolidTriangle::Set(const GsIv iv0, const GsIv iv1, const GsIv iv2, const UInt32& ibb, const UInt32& im,
		const GeoSolidShape& gss, const GeoSolidFace& gsf, const UInt32& ivn,
		const bool src, const bool cut, const UInt32 ic)
	{
		Set(iv0, iv1, iv2, 0, 0, 0, 0, 1, ibb, im, gss, gsf, ivn, src, cut, ic);
	}

	void GeoSolidTriangle::Set(const GsIv iv0, const GsIv iv1, const GsIv iv2, const GeoSolidTriangle& gst)
	{
		this->iv0 = iv0;
		this->iv1 = iv1;
		this->iv2 = iv2;
		this->ie0 = 0;
		this->ie1 = 0;
		this->ie2 = 0;
		this->iuv0 = 0;
		this->iuv1 = 0;
		this->iuv2 = 0;
		uIndex = gst.uIndex;
		vIndex = gst.vIndex;
		ibb = gst.ibb;
		ivc = gst.ivc;
		im = gst.im;
		gss = gst.gss;
		gsf = gst.gsf;
		ivn = gst.ivn;
		ic = gst.ic;
		markRemoval = false;
		turnover = gst.turnover;
		#if COMPOSITE_STD_USE_POINTER
		setNewIvs = nullptr;
		setIesDivider = nullptr;
		setCoincidentIts = nullptr;
		#endif
		//intersectSrc.from = gst.intersectSrc.from;
		//intersectSrc.hasCheckedInside = gst.intersectSrc.hasCheckedInside;
		//intersectSrc.inside = gst.intersectSrc.inside;
		//intersectCut.from = gst.intersectCut.from;
		//intersectCut.hasCheckedInside = gst.intersectCut.hasCheckedInside;
		//intersectCut.inside = gst.intersectCut.inside;
		intersectSrc |= gst.intersectSrc;
		intersectCut |= gst.intersectCut;
	}

	void GeoSolidTriangle::Set(const GsIv iv0, const GsIv iv1, const GsIv iv2,
		const UInt32& iuv0, const UInt32& iuv1, const UInt32& iuv2,
		const UInt8& uIndex, const UInt8& vIndex, const UInt32& ibb, const UInt32& im,
		const GeoSolidShape& gss, const GeoSolidFace& gsf, const UInt32& ivn,
		const bool src, const bool cut, const UInt32 ic)
	{
		this->iv0 = iv0;
		this->iv1 = iv1;
		this->iv2 = iv2;
		this->ie0 = 0;
		this->ie1 = 0;
		this->ie2 = 0;
		this->iuv0 = iuv0;
		this->iuv1 = iuv1;
		this->iuv2 = iuv2;
		this->uIndex = uIndex;
		this->vIndex = vIndex;
		this->ibb = ibb;
		this->ivc = 0;
		this->im = im;
		this->gss = gss;
		this->gsf = gsf;
		this->ivn = ivn;
		this->ic = ic;
		area = -1.0;
		markRemoval = false;
		turnover = false;
		#if COMPOSITE_STD_USE_POINTER
		setNewIvs = nullptr;
		setIesDivider = nullptr;
		setCoincidentIts = nullptr;
		#endif
		intersectSrc.from = src;
		intersectCut.from = cut;
	}

	void GeoSolidTriangle::Set(const FBSave::GeoSolidTriangle* fbsGst)
	{
		area = -1.f;
		turnover = false;
		FromFbs(fbsGst);
		intersectSrc.from = true;
		intersectCut.from = false;
	}

	void GeoSolidTriangle::Set(const FBSave::GeoSolidTriangle& fbsGst)
	{
		area = -1.f;
		turnover = false;
		iv0 = (GsIv)fbsGst.iv0();
		iv1 = (GsIv)fbsGst.iv1();
		iv2 = (GsIv)fbsGst.iv2();
		iuv0 = fbsGst.iuv0();
		iuv1 = fbsGst.iuv1();
		iuv2 = fbsGst.iuv2();
		ivn = fbsGst.ivn();
		ic = fbsGst.ic();
		uIndex = fbsGst.uIndex();
		vIndex = fbsGst.vIndex();
		#if COMPOSITE_STD_USE_POINTER
		setNewIvs = nullptr;
		setIesDivider = nullptr;
		setCoincidentIts = nullptr;
		#endif
		intersectSrc.from = true;
		intersectCut.from = false;
	}

	void GeoSolidTriangle::Set(const GeoSolidTriangle* gst)
	{
		Copy(*gst);
	}

	void GeoSolidTriangle::Set(const GeoSolidTriangle& gst)
	{
		Copy(gst);
	}

	GeoSolidTriangle& GeoSolidTriangle::operator=(const GeoSolidTriangle& gst)
	{
		Copy(gst); 
		return *this;
	}

	bool GeoSolidTriangle::IsValid() const
	{
		return iv0 != iv1 && iv0 != iv2 && iv1 != iv2;
	}

	bool GeoSolidTriangle::Neighbor(const GeoSolidTriangle& gst) const
	{
		return
			ie0 == gst.ie0 || ie0 == gst.ie1 || ie0 == gst.ie2 ||
			ie1 == gst.ie0 || ie1 == gst.ie1 || ie1 == gst.ie2 ||
			ie2 == gst.ie0 || ie2 == gst.ie1 || ie2 == gst.ie2
		;
	}

	flatbuffers::Offset<FBSave::GeoSolidTriangle> GeoSolidTriangle::ToFbs(flatbuffers::FlatBufferBuilder& builder) const
	{
		return FBSave::CreateGeoSolidTriangle(builder,
			(GsIndex)iv0, (GsIndex)iv1, (GsIndex)iv2,
			iuv0, iuv1, iuv2,
			ic, ivn,
			uIndex, vIndex
		);
	}

	void GeoSolidTriangle::FromFbs(const FBSave::GeoSolidTriangle* fbsGst)
	{
		if (!fbsGst)
		{
			return;
		}
		iv0 = (GsIv)fbsGst->iv0();
		iv1 = (GsIv)fbsGst->iv1();
		iv2 = (GsIv)fbsGst->iv2();
		iuv0 = fbsGst->iuv0();
		iuv1 = fbsGst->iuv1();
		iuv2 = fbsGst->iuv2();
		ivn = fbsGst->ivn();
		//ibb = 0;
		//im = 0;
		//2023-11-23：无法删除，后续由GeoSolidSubMesh记录，暂未刷新外网文件
		ic = fbsGst->ic();
		uIndex = fbsGst->uIndex();
		vIndex = fbsGst->vIndex();
	}

	const GeoSolidSet<GsIv>& GeoSolidTriangle::GetNewIvs() const
	{
		#if COMPOSITE_STD_USE_POINTER
		if (!setNewIvs)
		{
			setNewIvs = SANDBOX_NEW(GeoSolidSet<GsIv>);
		}
		return *setNewIvs;
		#else
		return setNewIvs;
		#endif
	}

	GeoSolidSet<GsIv>& GeoSolidTriangle::GetNewIvs()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (!setNewIvs)
		{
			setNewIvs = SANDBOX_NEW(GeoSolidSet<GsIv>);
		}
		return *setNewIvs;
		#else
		return setNewIvs;
		#endif
	}

	void GeoSolidTriangle::AddNewIv(const GsIv& iv)
	{
		GeoSolidSet<GsIv> setNewIvs = GetNewIvs();
		setNewIvs.emplace(iv);
	}

	void GeoSolidTriangle::ClearNewIvs()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (setNewIvs)
		{
			setNewIvs->clear();
		}
		#else
		setNewIvs.clear();
		#endif
	}

	GeoSolidSet<GsIv>& GeoSolidTriangle::GetIesDivider()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (!setIesDivider)
		{
			setIesDivider = SANDBOX_NEW(GeoSolidSet<GsIe>);
		}
		return *setIesDivider;
		#else
		return setIesDivider;
		#endif
	}

	void GeoSolidTriangle::AddIeDivider(const GsIe& ie)
	{
		GeoSolidSet<GsIe> setIesDivider = GetIesDivider();
		setIesDivider.emplace(ie);
	}

	void GeoSolidTriangle::ClearIesDivider()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (setIesDivider)
		{
			setIesDivider->clear();
		}
		#else
		setIesDivider.clear();
		#endif
	}

	GeoSolidSet<GsIv>& GeoSolidTriangle::GetCoincidentIts()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (!setCoincidentIts)
		{
			setCoincidentIts = SANDBOX_NEW(GeoSolidSet<GsIt>);
		}
		return *setCoincidentIts;
		#else
		return setCoincidentIts;
		#endif
	}

	void GeoSolidTriangle::AddCoincidentIts(const GsIt& it)
	{
		GeoSolidSet<GsIt> setCoincidentIts = GetCoincidentIts();
		setCoincidentIts.emplace(it);
	}

	void GeoSolidTriangle::ClearCoincidentIts()
	{
		#if COMPOSITE_STD_USE_POINTER
		if (setCoincidentIts)
		{
			setCoincidentIts->clear();
		}
		#else
		setCoincidentIts.clear();
		#endif
	}

	void GeoSolidTriangle::onRecycle()
	{
		this->iv0 = 0;
		this->iv1 = 0;
		this->iv2 = 0;
		this->ie0 = 0;
		this->ie1 = 0;
		this->ie2 = 0;
		this->iuv0 = 0;
		this->iuv1 = 0;
		this->iuv2 = 0;
		uIndex = 0;
		vIndex = 0;
		ibb = 0;
		ivc = 0;
		im = 0;
		ivn = 0;
		ic = 0;
		area = -1.0;
		gss = GeoSolidShape::NONE;
		gsf = GeoSolidFace::UNKNOWN;
		markRemoval = false;
		turnover = false;
		#if COMPOSITE_STD_USE_POINTER
		setNewIvs = nullptr;
		setIesDivider = nullptr;
		setCoincidentIts = nullptr;
		#endif
		//intersectSrc.from = gst.intersectSrc.from;
		//intersectSrc.hasCheckedInside = gst.intersectSrc.hasCheckedInside;
		//intersectSrc.inside = gst.intersectSrc.inside;
		//intersectCut.from = gst.intersectCut.from;
		//intersectCut.hasCheckedInside = gst.intersectCut.hasCheckedInside;
		//intersectCut.inside = gst.intersectCut.inside;
		intersectSrc.reset();
		intersectCut.reset();
		setNewIvs.clear();
		setIesDivider.clear();
		setCoincidentIts.clear();
	}

}}