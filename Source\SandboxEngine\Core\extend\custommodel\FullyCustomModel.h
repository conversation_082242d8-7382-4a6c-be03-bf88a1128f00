#ifndef __FULLYCUSTOMMODEL_H__
#define __FULLYCUSTOMMODEL_H__

#include <string>
#include "BaseClass/PPtr.h"
#include "CustomModelData.h"
#include "FullyCustomModel_generated.h"
#include "world_types.h"
#include "FullyCustomBoneData.h"
#include "SandboxEngine.h"
#include "SandboxAssetObject.h"
#include "CorePrerequisites.h"
#include "Mesh/LegacyOgreAnimationData.h"

namespace Rainbow
{
	class Entity;
}

class CustomModel;
class CustomMotion;

//tolua_begin
struct FCMSelectBoneEffect
{
	std::string bonename;
	//废弃，不用
	Rainbow::PPtr<Rainbow::Model> effect;

	FCMSelectBoneEffect() : bonename(""), effect(nullptr)
	{

	}
};
//tolua_end

//tolua_begin
struct FCMMoveFileLoadData
{
	int moveFromLibType;
	long long owid;
	std::string classname;
	int folderindex;
};
//tolua_end

//tolua_begin
struct LoadMapModData
{
	int realowneruin;  //地图作者
	std::string modroot;	//插件包路径
	bool isLibMod;			//总库插件包
	LoadMapModData() :realowneruin(0), modroot(""), isLibMod(false) {}
};
//tolua_end

#define FCM_EXAMPLE_TYPE (99)
#define FCM_LOAD_IGNORE_CHECK_UIN (-999)
#define FCM_PREVIEW_IGNORE_CHECK_UIN (-998)//预览资源不检测

//TODO: 2022-04-24 16:05:51: 部分接口在使用欧拉角时有误导性，使用时需看清参数顺序
//TODO: 2023-02-02 15:44:34: 单个骨骼相关的函数，可移动到骨骼类
class EXPORT_SANDBOXENGINE FullyCustomModel;
class FullyCustomModel //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	FullyCustomModel();
	~FullyCustomModel();
	//tolua_end

	//---------------------------------------文件操作start---------------------------------------//
public:
	//tolua_begin
	bool load(std::string path, std::string filename, int checkuin,
		FCMMoveFileLoadData* movedata = NULL, bool input = false,
		LoadMapModData* moddata = NULL, int specialType = 0, bool ignorecheck = false);
	bool loadDownloadSubCMs(std::string path, std::map<std::string, bool>* subcustommodels, int iType = -1);
	bool save(std::string path, std::string skey, int uin, std::string authname,
		std::string name, std::string desc, int eFcmSaveType = -1);
	//tolua_end
	bool save(const std::string& path, bool relativePath = true);
	bool cloneToSave(FullyCustomModel* fcm, std::string path, bool relativePath = true);
	//tolua_begin
	std::string saveCusMotion(int motionid, long long owid, std::string motionname, int specialType = 0);
	void reEncrypt(long long owid, int newuin, std::string authnickname, int specialType = 0);
	//TODO: 2022-04-27 18:23:18: 下方有重复的getter接口
	std::string getDesc();
	void setDesc(std::string desc);
	std::string getName();
	void setName(std::string name);
	int getModelType();
	void setModelType(int eFcmSaveType);
	bool isSecondaryCreation();
	std::string getKey();
	int getAuthUin();
	void setAuthUin(int uin);
	bool isDownload();
	bool isLeaveWorldDel();
	std::string getAuthName();
	void setAuthName(std::string authname);
	std::string getModelName();
	std::string getModelDesc();
	void setKey(std::string skey);
	void setEditing(bool editing);
	bool isEdited();
	void addVersion(int v);
	void setVersion(int v);
	int getVersion();
	void setNeedSave(bool b);
	bool needSave();
	std::string& getFileName();
	void setFileName(std::string filename);
	void setLeaveWorldDel(bool b);
	//tolua_end
	//---------------------------------------文件操作end-----------------------------------------//

	//---------------------------------------骨骼操作start---------------------------------------//
public:
    //tolua_begin
	bool addCustomBone(Rainbow::Entity* entity, std::string name, std::string fathername, std::string modelfilename);
    bool addCustomBone(Rainbow::Entity* entity, std::string name, std::string fathername, std::string modelfilename, Rainbow::Quaternionf r);
    bool addCustomBone(Rainbow::Entity* entity, std::string name, std::string fathername, std::string modelfilename,
        Rainbow::Quaternionf r, Rainbow::Vector3f t, float s = 1.0f);
	// 资源异步加载回调处理
	bool addCustomBoneCallFunc(Rainbow::Entity* entity, std::string name, FullyCustomBoneData* fcbd, Rainbow::Model* model);
	bool setCustomBone(Rainbow::Entity* entity, std::string name, float scale, short offsetx, short offsety, short offsetz, Rainbow::Quaternionf& rotate);
	void updateKeyFrameByBoneChange(Rainbow::Entity* entity, FullyCustomBoneData* bonedata, Rainbow::Matrix4x4f& tm_bone_old, Rainbow::Matrix4x4f& tm_bone_new);
	bool changeBindModel(Rainbow::Entity* entity, std::string name, std::string modelfilename);
	// 资源异步加载回调处理
	bool changeBindModelCallFunc(Rainbow::Entity* entity, std::string boneName, FullyCustomBoneData* fcbd, Rainbow::Model* model);
	//tolua_end
	bool setCustomBone(Rainbow::Entity* entity, std::string name,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	bool setCustomBone(Rainbow::Entity* entity, std::string name, MINIW::Transform_& trs);
	void updateBindModel(Rainbow::Entity* entity);
	// 资源异步加载回调处理
	void updateBindModelCallFunc(Rainbow::Entity* entity, Rainbow::Model* model, FullyCustomBoneData& fcbd);
	//static Rainbow::Model* createObjModel(std::string& strModelId);
	//bool loadObjModelbyAssetid(MNSandbox::AssetObject& asset, std::string& strModelId, std::function<void(Rainbow::Entity*)> loadFinish);
	//tolua_begin
	void setBindModelOffset(std::string name, short offsetx, short offsety, short offsetz);
	void setBindModelRotate(std::string name, float yaw, float pitch, float roll);
	void setBindModelScale(std::string name, float scale);
	void setModelOverlayColor(std::string name, bool show);
	bool setModelShow(std::string strBoneName, bool visible);
	void setCurSelectBoneEffect(Rainbow::Entity* entity, std::string name);
	bool delCustomBone(Rainbow::Entity* entity, std::string name, FullyCustomBoneData* rootdata = NULL);
	bool changeCustomBoneName(std::string oldname, std::string newname);
	void changeCustomBoneStandard(std::string name, bool isstandard = false);
	bool changeCustomBoneFather(std::string name, std::string fathername);
	//tolua_end
	void setBindModelScale(std::string name, Rainbow::Vector3f& scale3);
	void setBindModel(std::string name,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	void setBindModel(std::string name, MINIW::Transform_& trs);
	bool setModelTexture(std::string modelName, std::string texturePath);
	bool setTextureId(std::string boneName, std::string texturePath);
	//---------------------------------------骨骼操作end---------------------------------------//

	//---------------------------------------关键帧与动画操作start---------------------------------------//
public:
	//tolua_begin
	void setCustomSequenceEndtime(int seqId, int endtime, FullyCustomBoneData* rootdata = NULL);
	bool insertCustomKeyFrame(int seqId, std::string bonename, const int tick,
		short offsetx, short offsety, short offsetz, Rainbow::Quaternionf& rotate, float scale);
	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick,
		short offsetx, short offsety, short offsetz, Rainbow::Quaternionf& rotate, float scale);
	bool delCustomKeyFrame(int seqId, std::string bonename, const int tick);
	//tolua_end
	bool insertCustomKeyFrame(int seqId, std::string bonename, const int tick,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3);

	bool delCustomKeyframes(int seqId, const int tick);
	void delAllCustomKeyframes(int seqId);

	bool getCustomMotionDataTRS(int seqId, std::string strBoneName, int tick, MINIW::Transform_& trs);
	std::set<unsigned> getTicks(int seqId);
	int getEndtime(int seqId);
	bool hasTick(int seqId, int tick);
	bool hasBoneTick(int seqId, std::string strBoneName, int tick);
	bool hasTickExceptBone(int seqId, int tick, std::string strBoneName);
	bool isTickValid(int seqId, int tick);

	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3);
	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool copyCustomKeyframes(int seqIdSrc, int seqIdDst);
	int getTickPerKf(int seqId);
	void transformTickPerKf(int seqId, int tickPerKf);
	//tolua_begin
	void oneBoneDataConvertToMotion(FullyCustomBoneData* bonedata, std::map<int, CustomMotion*>& custommotions, int motionid, int& idx);
	//tolua_end
	//---------------------------------------关键帧与动画操作end---------------------------------------//

	//---------------------------------------骨架start---------------------------------------//
public:
	void iterate(std::function<bool(FullyCustomBoneData&)>& func);
	bool iterateCheck(std::function<bool(FullyCustomBoneData&)>& func, bool bIteAll);
	//tolua_begin
	FullyCustomBoneData* findFullyCustomBoneData(std::string name, FullyCustomBoneData* fcbdRoot = NULL);
	int getBoneDataNum();
	FullyCustomBoneData* getBoneDataByIndex(int index);
	//tolua_end
	FullyCustomBoneData* findFcbdUnder(std::string strBoneName, std::string strParent);
	const std::vector<FullyCustomBoneData*>* getFullyModelBone();
	void deepCopyChildren(std::vector<FullyCustomBoneData*>& vFcbds, int animId);
	Rainbow::Model* findModelWithName(std::string modelName, const std::vector<FullyCustomBoneData*>& list);
	void BuildModelData(Rainbow::Entity* entity, Rainbow::SharePtr<Rainbow::ModelData> modelData,
		FullyCustomBoneData* fcbdRoot, bool bindingPack, bool skipPacking);
	bool checkGenItemModelCache();
	void ApplyModelData(Rainbow::Entity* entity, std::map<std::string, std::vector<CustomAvatarModelData>>* mCamds, bool bindingPack, bool skipPacking);
	// 资源异步加载回调处理
	void ApplyModelDataCallFunc(Rainbow::PPtr<Rainbow::Entity> spEntity, FullyCustomBoneData* fcbd, std::map<std::string, std::vector<CustomAvatarModelData>>* mCamds, 
		bool bindingPack, bool skipPacking, Rainbow::Model* modelBone, int objclass);
	void setModelDataByPackingIconStep1(Rainbow::Entity* entity, FullyCustomBoneData* fcbdRoot, WCoord offset);
	void setModelDataByPackingIconStep2(Rainbow::Entity* entity, FullyCustomBoneData* fcbdRoot,
		std::map<std::string, std::vector<CustomAvatarModelData>>* mCamds);
	//tolua_begin
	//skipPacking 忽略微缩打包的差异
	void setModelData(Rainbow::Entity* entity, Rainbow::SharePtr<Rainbow::ModelData> modeldata, 
		FullyCustomBoneData* fcbdRoot = NULL, bool bUpdateWhenCustomModelReady = false, 
		bool bindingPack = true, bool skipPacking = false);
	void setModelDataByPackingIcon(Rainbow::Entity* entity, FullyCustomBoneData* fcbdRoot = NULL,
		bool bUpdateWhenCustomModelReady = false, WCoord offset = WCoord(0, 0, 0));
	//tolua_end
	void clearCloudUploadState();
	bool checkAllUploadCloud();
	bool needUploadBones();
	//---------------------------------------骨架end---------------------------------------//

	//tolua_begin
	bool moveResAvatarModelToMap(long long owid, std::string classname, int folderindex, FullyCustomBoneData* rootdata = NULL, bool isdownloadfcm = false, int specialType = 0);
	void moveMapAvatarModelToRes(long long owid, FullyCustomBoneData* rootdata = NULL, bool isdownloadfcm = false, int specialType = 0);

	bool moveSubModelRes(int destlibtype, long long owid, FullyCustomBoneData* rootdata = NULL, bool ignorecheck = false);

	void packingFcmConvertFcm();
	void getClientMissSubCMNum(int& num, FullyCustomBoneData* rootdata = NULL);

	Rainbow::Vector3f getSubCMRealMaxPos(std::string skey, std::string bonename);
	Rainbow::Vector3f getSubCMRealMinPos(std::string skey, std::string bonename);
	int convert2SmallRoutine(int type);
	void checkEditUseCMIsDownloaded(FullyCustomBoneData* rootdata = NULL);
	//tolua_end
public:
	//tolua_begin

	void setPackingCMForwardDir(short dir)
	{
		m_iPackingCMForwardDir = dir;
	}

	int getPackingFCMPlaceDir()
	{
		return m_iPackingCMForwardDir;
	}

	int getOneCMPlaceDir(std::string bonename);
	//tolua_end
public:
	//tolua_begin
	bool isPackingFCM();
	void setPackingFCMData(WCoord& packingpos, std::string name, std::string modelfilename, Rainbow::Quaternionf& rotate, int dir = -1);  //?è??×é????????????
	void setPackingFCMInfoByHostSync(std::string name, std::string desc, std::string skey, short dir, WCoord minpos, WCoord maxpos, int authUin);
	WCoord getPackingFcmDim();
	CollideAABB getFcmBoundBox();
	void calBoundBox(FullyCustomBoneData* rootdata = NULL);
	void calOneCMBoundBox(CustomModel* cm, Rainbow::Matrix4x4f basetm);

	bool isAttachmentBoneName(std::string boneName);
	void setAttachmentBoneName(std::string boneName, bool isAttachement);
	void setAttachmentBoneNames(std::vector<std::string>& boneNames);
	void getAttachmentBoneNames(std::vector<std::string>& boneNames);
	//tolua_end

#ifdef UGC_PROFILE_ENABLED
	size_t getMemStatForUGC();
#endif // UGC_PROFILE_ENABLED

	bool getFrameEvent(int id, UInt32 beginTick, UInt32 endTick, std::vector<Rainbow::LegacyAnimationFrameEventData*>* ret = nullptr);
	void addFrameEvent(int id, UInt32 tick, const std::string& event, UInt32 count);
	void removeFrameEvent(int id, UInt32 tick);
	void updateFrameEvent(UInt32 maxTick);
private:

#ifdef UGC_PROFILE_ENABLED
	size_t getMemStatBoneData(const std::vector<FullyCustomBoneData*>& boneDatas);
#endif // UGC_PROFILE_ENABLED

	int placeDirConverAngle();
	bool isPackingFCMValidBone(FullyCustomBoneData* data);  //微缩组合有效的骨骼数据（根骨骼无效）
	bool isPackingFCMRootBone(FullyCustomBoneData* data);  //微缩组合有效的根骨骼数据

	void getPackingIconPitchAndRollByYaw(float yaw, float& pitch, float& roll); 
private:
	void initDefaultSkeletonModel(Rainbow::Entity* entity, FullyCustomBoneData* fcbd, int objclass);
public:
	void setSplitSkeletonModel(bool isSplit);
	bool isSplitSkeletonModel();
	void setSkeletonModelShowState(bool isShow);
	bool getSkeletonModelShowState();
	void setSkeletonModelOverlayColor(std::string name, bool show);
private:
	std::string m_sName;
	std::string m_sAuthName;
	std::string m_sDesc;
	/**
	@brief	1. 游戏：纯名字
			2. 编辑器：路径
	 */
	std::string m_sKey;
	int m_iAuthUin;
	//多棵骨骼树的根节点
	std::vector<FullyCustomBoneData*> m_vFcbd;
	int m_iVersion;
	bool m_bNeedSave;
	/**
	@brief	base name
	 */
	std::string m_sFileName;
	int m_eFcmSaveType;
	bool m_bLeaveworlddel;
	bool m_bEditing;
	FCMSelectBoneEffect m_CurSelectBoneEffect;

	//骨骼绑点信息
	std::vector<std::string> m_attachmentBoneNames;

	//是否骨骼模型分离
	bool m_splitSkeletonModel;
	bool m_showSkeletonModel;

	int m_nloadModelAsyncRet = 0;
private:
	short m_iUseDownloadCMNum;  //使用下载微缩组合的数量
	short m_iPackingCMForwardDir;  //整体微缩组合的正向
	std::map<std::string, short> m_PackingCMForwardDirs; //微缩组合里的单个微缩的正向
	WCoord m_Dim; //微缩组合的范围
	CollideAABB m_BoundBox;//微缩组合的盒子

	MNSandbox::AutoRef<MNSandbox::Ref> m_ref;	// 用做lamda表达式中判断对象是否存在
	std::vector<Rainbow::LegacyAnimationFrameEventData> m_frameEventData;

	friend class FullyCustomBoneData;
}; //tolua_exports


#endif
