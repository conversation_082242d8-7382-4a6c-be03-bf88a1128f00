/**
* file : SandboxGameObject
* func : 游戏对象
* by : chenzh
*/
#include "SandboxGameObject.h"
#include "math/SandboxSafeContainer.h"


namespace MNSandbox {

	IMPLEMENT_REFCLASS(GameObject);

	GameObject::GameObject()
	{
		m_flagAutoRelease = 1; // 计数减为 0 时，自动释放
	}
	GameObject::~GameObject()
	{
		if (m_tickComps)
		{
			SafeContainer<Component*>* ptr = static_cast<SafeContainer<Component*>*>(m_tickComps);
			SANDBOX_DELETE(ptr);
			m_tickComps = nullptr;
		}
		m_components.for_each([this](Component* comp) { // 将owner 设置成null，避免其他地方缓存了component 指针
			comp->SetOwner(nullptr);
		});
		m_components.clear();
	}

	bool GameObject::DestroyComponent(MNSandbox::Component* comp)
	{
		//SANDBOX_ASSERT(comp);
		if (comp)
		{
			auto node = comp->GetListNode();
			if (node->IsInContainer(&m_components))
			{
				RemoveComponent(comp);
				return true;
			}
		}
		return false;
	}

	MNSandbox::Component* GameObject::GetComponentByName(const std::string& name) const
	{
		auto curNode = m_components.find([&name](Component* node) -> bool {
			return node->GetName() == name;
		});
		return curNode ? curNode->Get() : nullptr;
	}

	void GameObject::AddComponent(MNSandbox::Component* comp)
	{
		SANDBOX_ASSERT(comp && "add component is nullptr");
		if (comp)
		{
			m_components.push_back(comp->GetListNode());
			comp->SetOwner(this);
			OnAddComponent(comp); // 通知新增
		}
	}

	void GameObject::RemoveComponent(MNSandbox::Component* comp)
	{
		// SANDBOX_ASSERT(comp && "remove component is nullptr");
		if (comp)
		{
			OnRemoveComponent(comp); // 通知移除
			comp->SetOwner(nullptr);
			m_components.erase(comp->GetListNode());
		}
	}

	void GameObject::RegComponentTick(Component* comp)
	{
		if (!comp)
			return;

		if (!m_tickComps)
		{
			m_tickComps = (void*)SANDBOX_NEW(SafeContainer<Component*>);
			BindTick();
		}

		static_cast<SafeContainer<Component*>*>(m_tickComps)->Insert(comp);
	}

	void GameObject::UnregComponentTick(Component* comp)
	{
		if (!m_tickComps)
			return;

		static_cast<SafeContainer<Component*>*>(m_tickComps)->Erase(comp);
	}

	void GameObject::ComponentPreTick()
	{
		if (m_tickComps)
			static_cast<SafeContainer<Component*>*>(m_tickComps)->For_each(this, &GameObject::PreTickCallback);
	}

	void GameObject::ComponentTick()
	{
		if (m_tickComps)
			static_cast<SafeContainer<Component*>*>(m_tickComps)->For_each(this, &GameObject::TickCallback);
	}

	/////////////////////////////////////////////////////////

	void GameObject::OnAddComponent(MNSandbox::Component* comp)
	{
		m_notifyAddComponent(this, comp); // 通知新增
	}

	void GameObject::OnRemoveComponent(MNSandbox::Component* comp)
	{
		m_notifyRemoveComponent(this, comp); // 通知移除
	}

}