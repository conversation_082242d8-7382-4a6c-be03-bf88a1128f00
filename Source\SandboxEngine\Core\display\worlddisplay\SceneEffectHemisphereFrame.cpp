/**
* file : SceneEffectHemisphereFrame
* func : 场景效果 （上半球框）
* by : pengdapu
*/
#include "SceneEffectHemisphereFrame.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "SceneEffectEllipse.h"
#include "WorldRender.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectHemisphereFrame::SceneEffectHemisphereFrame()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;

	m_ellipseY = SANDBOX_NEW(SceneEffectEllipse);
	m_ellipseZ = SANDBOX_NEW(SceneEffectEllipse);
	m_ellipseX = SANDBOX_NEW(SceneEffectEllipse);
	m_eSes = SceneEffectShape::HEMISPHERE_FRAME;
}

SceneEffectHemisphereFrame::~SceneEffectHemisphereFrame()
{
}

void SceneEffectHemisphereFrame::SetRadius(float radius)
{
	if (m_radius == radius)
		return;

	m_radius = radius;
	m_originRadius = radius;
}

void SceneEffectHemisphereFrame::OnClear()
{
	SANDBOX_DELETE(m_ellipseZ);
	SANDBOX_DELETE(m_ellipseX);
	SANDBOX_DELETE(m_ellipseY);
}

void SceneEffectHemisphereFrame::Refresh()
{
	OnClear();
	SceneEffectEllipse* aEllipses[3] = {
		SANDBOX_NEW(SceneEffectEllipse),
		SANDBOX_NEW(SceneEffectEllipse),
		SANDBOX_NEW(SceneEffectEllipse),
	};
	m_ellipseZ = aEllipses[0];
	m_ellipseX = aEllipses[1];
	m_ellipseY = aEllipses[2];

	m_ellipseZ->SetRotationAxis(Vector3f::neg_zAxis, Vector3f::neg_zAxis);
	m_ellipseX->SetRotationAxis(Vector3f::neg_xAxis, Vector3f::neg_xAxis);
	m_ellipseY->SetRotationAxis(Vector3f::yAxis, Vector3f::yAxis);

	m_ellipseX->SetStartRadian(kHalfPI);
	m_ellipseX->SetEndRadian(kHalfPI + kOnePI);
	m_ellipseY->SetEndRadian(kOnePI);
	m_ellipseX->SetSector(16);
	m_ellipseY->SetSector(16);

	for (int i = 0; i < 3; ++i)
	{
		SceneEffectEllipse* ellipse = aEllipses[i];
		ellipse->SetCenter(m_vCenter);
		ellipse->SetRadius(m_radius);
		ellipse->SetColor(m_Color, m_Color);
		ellipse->SetStroke(m_iStroke);
		ellipse->SetMtlType(m_MtlType);
	}
	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectHemisphereFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	if (m_ellipseZ) m_ellipseZ->OnDraw(pWorld);
	if (m_ellipseX) m_ellipseX->OnDraw(pWorld);
	if (m_ellipseY) m_ellipseY->OnDraw(pWorld);
}

bool SceneEffectHemisphereFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectHemisphereFrame::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;
	if (m_ellipseZ)
	{
		m_ellipseZ->RefreshEllipseFrame(vc, q, m_radius * vs.x, m_radius * vs.y);
	}
	if (m_ellipseX)
	{
		m_ellipseX->RefreshEllipseFrame(vc, q, m_radius * vs.z, m_radius * vs.y);
	}
	if (m_ellipseY)
	{
		m_ellipseY->RefreshEllipseFrame(vc, q, m_radius * vs.x, m_radius * vs.z);
	}
}
