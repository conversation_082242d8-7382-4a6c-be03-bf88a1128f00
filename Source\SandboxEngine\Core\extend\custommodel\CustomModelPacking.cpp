
#include "CustomModelPacking.h"
#include "CustomModelPackingSelectArea.h"
#include "CustomModelData.h"
#include "IPlayerControl.h"
#include "DefManagerProxy.h"
#include "IBackpack.h"
#include "FullyCustomModel.h"
#include "CustomModel.h"
#include "FullyCustomModelMgr.h"

#include "blocks/BlockMaterialMgr.h"
#include "IActorBody.h"
#include "PackingFullyCustomModelMgr.h"
#include "ResourceCenter.h"

#include "OgreScriptLuaVM.h"
#include "OgreCrc32.h"
#include "Platforms/PlatformInterface.h"
#include "ClientInfoProxy.h"
#include "Jobs/JobBatchDispatcher.h"
#include <time.h>
#include "GameNetManager.h"
#include "WorldStringManagerProxy.h"
#include "Optick/optick.h"
#include "CustomModelMgr.h"
#include "IClientPlayer.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
#pragma region Packing Thread
namespace JOBNS
{


	static void CreatePackingFcm(CustomModelPacking::PackingJob* jobData)
	{
		CreatePackingFcmResult& ret = jobData->result;
		CreatePackingFcmInfo& packinginfo = jobData->info;
		CustomModelPacking* modlePacking = jobData->modelPacking;
		ret.uin = packinginfo.uin;
		if (!FullyCustomModelMgr::GetInstancePtr())
		{
			ret.result = -1;
			return;
		}

		if (!GetWorldManagerPtr())
		{
			ret.result = -2;
			return;
		}


		IClientPlayer* player = GetWorldManagerPtr()->getPlayerByUin(packinginfo.uin);
		if (!player)
		{
			ret.result = -3;
			return;
		}

		World* pWorld = player->GetPlayerWorld();
		if (!pWorld)
		{
			ret.result = -4;
			return;
		}

		int dir = modlePacking->rotateModeConverPlaceDir(packinginfo.mode);
		FullyCustomModel* packingBlockFCM = ENG_NEW(FullyCustomModel)();
		packingBlockFCM->setPackingCMForwardDir(dir);

		int idx = 0;
		if (packinginfo.packingMode == PACKING_CM_MODE_BLOCK)
		{
			idx = modlePacking->setFCMByPackingBlock(packingBlockFCM, pWorld, packinginfo.offsetPos, packinginfo.startPos, packinginfo.endPos);
		}
		else if (packinginfo.packingMode == PACKING_CM_MODE_CM)
		{
			idx = modlePacking->setFCMByPackingCM(packingBlockFCM, pWorld, packinginfo.startPos, packinginfo.endPos);
		}

		ret.skey = "";
		if (FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
			ret.skey = FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->saveFcmByPackingCM(packingBlockFCM, pWorld->getOWID(), idx, packinginfo.name, packinginfo.desc, player, pWorld->getMapSpecialType());
		if (ret.skey.empty())
		{
			ENG_DELETE(packingBlockFCM);
			ret.result = -5;
			return;
		}

		ret.id = packinginfo.id;
		ret.result = 1;
		//GetWorldStringManagerProxy()->insert(ret.skey, content, SAVEFILETYPE::PACKING_FCM);
	}

}

#pragma endregion




extern void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype);

CustomModelPacking::CustomModelPacking() : m_bStartPacking(false), m_EditStartPos(WCoord(0, -1, 0)), m_EditCenterPos(WCoord(0, -1, 0)), m_EditEndPos(WCoord(0, -1, 0)),
	m_EditMaxOffset(0, 0, 0), m_pEditActorBody(NULL), m_iCheckNotifyCreateResultTick(10), m_bSecondaryCreation(false)
{
	m_pSelectArea = ENG_NEW(CustomModelPackingSelectArea)();
}

CustomModelPacking::~CustomModelPacking()
{
	ENG_DELETE(m_pSelectArea);
	SANDBOX_DELETE(m_pEditActorBody);

	m_FrameModels.clear();

	if (m_JobsArray.size() > 0)
	{
		PackingJob* jobdata = &m_JobsArray.front();
		SyncFence(jobdata->jobfence);
	}
}

void CustomModelPacking::tick()
{
	OPTICK_EVENT();
	if (m_bStartPacking)
	{
		if (m_pSelectArea)
			m_pSelectArea->tick();
	}
	//job�����
	if (m_JobsArray.size()>0)
	{
		PackingJob* jobdata = &m_JobsArray.front();
		if (!jobdata->isRuning) {
			JobBatchDispatcher dispatcher;
			
			dispatcher.ScheduleJob(jobdata->jobfence, JOBNS::CreatePackingFcm, jobdata);
			dispatcher.KickJobs();
			jobdata->isRuning = true;
		}
		else {
			if (IsFenceDone(jobdata->jobfence)) {
				if (jobdata->result.uin > 0) {
					std::string content = jobdata->info.name + ";" + jobdata->info.desc;
					GetWorldStringManagerProxy()->insert(jobdata->result.skey, content, SAVEFILETYPE::PACKING_FCM);
					//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
					//.SetData_Number("type", 7)
					//.SetData_String("content", content)
					//.SetData_String("key", jobdata->result.skey));

					createPackingFcmResultHandle(jobdata->result);
				}
				m_JobsArray.pop_front();
			}
		}
	}
}

void CustomModelPacking::update(float dtime)
{
	OPTICK_EVENT();
	if (m_pEditActorBody)
		m_pEditActorBody->update(dtime);
}

int CustomModelPacking::onInputEvent(const Rainbow::InputEvent &event)
{
	if (!GetClientInfoProxy()->isMobile())
	{
		std::string key = "";
		if (event.keycode == SDLK_ESCAPE)
		{
			key = "escape";
		}
		else if (event.keycode == SDLK_r)
		{
			key = "r";
		}
		else if (event.keycode == SDLK_F3)
		{
			key = "f3";
		}
		else if (event.keycode == SDLK_g)
		{
			key = "G";
		}
		else if (event.keycode == SDLK_b)
		{
			key = "B";
		}
		else if (event.keycode == SDLK_c)
		{
			key = "C";
		}
		else if (event.keycode == SDLK_x)
		{
			key = "X";
		}
		else if (event.keycode == SDLK_f)
		{
			key = "F";
		}
		else if (event.keycode == SDLK_h)
		{
			key = "H";
		}
		else if (event.keycode == SDLK_m)
		{
			key = "M";
		}
		else if (event.keycode == SDLK_y)
		{
			key = "Y";
		}
		else if (event.keycode == SDLK_z)
		{
			key = "Z";
		}
		else if (event.keycode == SDLK_i)
		{
			key = "I";
		}
		else if (event.keycode == SDLK_0)
		{
			key = "O";
		}
		else if (event.keycode == SDLK_p)
		{
			key = "P";
		}
		else if (event.type == InputEvent::kScrollWheel)
		{
			if (event.delta.y > 0)
				key = "+mousewheel";
			else
				key = "-mousewheel";
		}
		else
		{
			// todo check this hardcode (keycode 1~9)
			for (int i = 1; i < 9; i++)
			{
				if (event.keycode == i + SDLK_0)
				{
					char ch[10];
					sprintf(ch, "%d", i);
					std::string str(ch, ch + strlen(ch));
					key = str;
				}
			}
		}

		if (key != "")
		{
			if ((event.type == InputEvent::kKeyDown || event.type == InputEvent::kScrollWheel) && MINIW::ScriptVM::game())
				MINIW::ScriptVM::game()->callFunction("PackingCMAccelKeyHandle", "s", key.c_str());

			return INPUTMSG_HANDLED;
		}
	}
	return INPUTMSG_PASS;
}

bool CustomModelPacking::isStartPacking()
{
	return m_bStartPacking;
}

void CustomModelPacking::startPacking()
{
	m_bStartPacking = true;
	if (m_pSelectArea)
		m_pSelectArea->init();
}

void CustomModelPacking::endPacking()
{
	m_bStartPacking = false;
	SANDBOX_DELETE(m_pEditActorBody);
	m_EditStartPos = WCoord(0, -1, 0);
	m_EditEndPos = WCoord(0, -1, 0);
	m_EditCenterPos = WCoord(0, -1, 0);
	m_EditMaxOffset = WCoord(0, 0, 0);
	m_EditMinOffset = WCoord(0, 0, 0);
	m_bSecondaryCreation = false;
	if (m_pSelectArea)
		m_pSelectArea->clearPreBlocksMesh();

	m_FrameModels.clear();
	//clearEditCustomModel();
}

void CustomModelPacking::excuteCmdWithRBClicked()
{	
	if (m_bStartPacking && m_pSelectArea)
	{
		if (m_pSelectArea->confirmArea()) //确定区域后，生成微缩组合
		{
			auto mode = m_pSelectArea->getOperateMode();
			int idx = createFcmToPackEdit(mode);

			if (MINIW::ScriptVM::game())
				MINIW::ScriptVM::game()->callFunction("OpenPackingCMAJustFrame", "ii", mode, idx);
		}
	}
		
}

void CustomModelPacking::setOperateDistance(int distance)
{
	if (m_pSelectArea)
		m_pSelectArea->setOperateDistance(distance);
}

void CustomModelPacking::setOperateMode(PACKING_CM_MODE mode)
{
	if (m_pSelectArea)
		m_pSelectArea->setOperateMode(mode);
}

Rainbow::IActorBody *CustomModelPacking::getEditActorBody()
{
	return m_pEditActorBody;
}

void CustomModelPacking::setEditData(int x, int y, int z, PACKING_CM_RORATE_MODE mode)
{
	if (!m_pEditActorBody)
		return;

	auto *entity = m_pEditActorBody->getEntity();
	if (!entity)
		return;
	Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, (float)(mode-90), 0);
	entity->SetCustomBone(PACKING_ROOT_NAME, Rainbow::Vector3f(1.0f, 1.0f, 1.0f), quat, Rainbow::Vector3f(0, 0, (float)m_EditCenterPos.z));

	quat = Rainbow::XYZAngleToQuat(0, 0, 0);
	entity->SetCustomBone(PACKING_CM_ROOT_NAME, Rainbow::Vector3f(1.0f, 1.0f, 1.0f), quat, Rainbow::Vector3f((float)x, (float)y, (float)z));
}

void CustomModelPacking::getOffsetRange(int &minx, int &miny, int &minz, int &maxx, int &maxy, int &maxz)
{
	minx = m_EditMinOffset.x;
	miny = m_EditMinOffset.y;
	minz = m_EditMinOffset.z;
	maxx = m_EditMaxOffset.x;
	maxy = m_EditMaxOffset.y;
	maxz = m_EditMaxOffset.z;
}

void CustomModelPacking::requestCreatePackingFcm(std::string name, std::string desc, int x, int y, int z, PACKING_CM_RORATE_MODE mode, int packingmode/* =-1 */, IClientPlayer *player/* =NULL */)
{
	int uin = 0;
	World* pWorld = nullptr;

	if (player == nullptr && GetIPlayerControl() == nullptr)
	{
		notifyCreateResult(-6);
		return;
	}
	if (player == nullptr)
	{
		uin = GetIPlayerControl()->GetIUin();
		pWorld = GetIPlayerControl()->getIWorld();
	}
	else
	{
		uin = player->getUin();
		pWorld = player->GetPlayerWorld();
	}

	if (!pWorld)
	{
		notifyCreateResult(-7, uin);
		return;
	}	

	if (pWorld->isRemoteMode())
	{
		if (!m_pSelectArea)
		{
			notifyCreateResult(-8);
			return;
		}

		PB_CreatePackingCMCH createPackingCMCH;

		PB_Vector3* pos = createPackingCMCH.mutable_offsetpos();
		pos->set_x(x);
		pos->set_y(y);
		pos->set_z(z);

		createPackingCMCH.set_rotatetype(mode);
		createPackingCMCH.set_name(name);
		createPackingCMCH.set_desc(desc);
		createPackingCMCH.set_createtype(m_pSelectArea->getOperateMode());

		PB_Vector3* startPos = createPackingCMCH.mutable_startpos();
		startPos->set_x(m_EditStartPos.x);
		startPos->set_y(m_EditStartPos.y);
		startPos->set_z(m_EditStartPos.z);

		PB_Vector3* endPos = createPackingCMCH.mutable_endpos();
		endPos->set_x(m_EditEndPos.x);
		endPos->set_y(m_EditEndPos.y);
		endPos->set_z(m_EditEndPos.z);

		GetGameNetManagerPtr()->sendToHost(PB_CREATE_PACKINGCM_CH, createPackingCMCH);
		return;
	}

	CreatePackingFcmInfo info;
	info.name = name;
	info.desc = desc;
	info.mode = mode;
	if(packingmode < 0)
		info.packingMode = m_pSelectArea->getOperateMode();
	else
		info.packingMode = packingmode;

	info.offsetPos = WCoord(x, y, z);
	info.startPos = m_EditStartPos;
	info.endPos = m_EditEndPos;
	info.uin = uin;

	createPackingFcm(info);
	
}

void CustomModelPacking::createPackingFcm(CreatePackingFcmInfo info)
{
	IClientPlayer *player = GetWorldManagerPtr()->getPlayerByUin(info.uin);
	if (!player)
	{
		notifyCreateResult(-9);
		return;
	}

	if (!FullyCustomModelMgr::GetInstancePtr())
	{
		notifyCreateResult(-10, player->getUin());
		return;
	}

	int id = FullyCustomModelMgr::GetInstancePtr()->getFreeId(FULLY_PACKING_CUSTOM_MODEL);
	if (id <= 0)
	{
		//没有可分配的id了
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
		notifyCreateResult(-11, player->getUin());
		return;
	}

	info.id = id;
	GetDefManagerProxy()->addDefByCustomModel(id, FULLY_PACKING_CUSTOM_MODEL, "", info.name, info.desc);

	PackingJob jobData;
	jobData.info = info;
	jobData.modelPacking = this;
	m_JobsArray.push_back(jobData);
	
}


void CustomModelPacking::createPackingFcmResultHandle(CreatePackingFcmResult ret)
{
	if (ret.result == 1)
	{
		World *pWorld = NULL;

		IClientPlayer *player = NULL;
		if (GetWorldManagerPtr())
		{
			player = GetWorldManagerPtr()->getPlayerByUin(ret.uin);
			// GetIPlayerControl() 在云服是null
			if (!player && GetIPlayerControl() == nullptr)
				return;

			if (player == nullptr)
				pWorld = GetIPlayerControl()->getIWorld();
			else
				pWorld = player->GetPlayerWorld();
		}

		CustomModelMgr::GetInstancePtr()->addCustomItemData(ret.id, ret.skey, "default", FULLY_PACKING_CUSTOM_MODEL);
		auto *itemDef = GetDefManagerProxy()->getItemDef(ret.id);
		if (itemDef)
		{
			itemDef->Model = ret.skey.c_str();
			itemDef->MeshType = FULLY_CUSTOM_GEN_MESH;
			itemDef->Icon = "fullycustompacking";
		}

		if (!pWorld->isRemoteMode())
		{
			PB_CustomItemIDsHC customItemIDsHC;
			for (size_t i = 0; i < 1; i++)
			{
				customItemIDsHC.add_customitemids(ret.id);
				customItemIDsHC.add_custommodelfilenames(ret.skey);
				customItemIDsHC.add_custommodelclassnames("default");
				customItemIDsHC.add_custommodelfolderindexs(0);
				customItemIDsHC.add_customtypes(FULLY_PACKING_CUSTOM_MODEL);
				customItemIDsHC.add_involvedids(0);
			}
			GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_ITEMIDS_HC, customItemIDsHC, 0);
		}

		if (CustomModelMgr::GetInstancePtr())
		{
			if (ResourceCenter::GetInstancePtr())
				ResourceCenter::GetInstancePtr()->addOneResToClass(ret.skey, true);
		}

		if (FullyCustomModelMgr::GetInstancePtr() && FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr())
			FullyCustomModelMgr::GetInstancePtr()->getPackingFCMMgr()->syncPackingFcmByCreateNew();

		GridCopyData data;
		data.resid = ret.id;
		data.num = 1;
		if (player)
		{
			player->getIBackPack()->tryAddItem_byGridCopyData(data);
			//player->getBackPack()->tryAddItem(ret.id, 1, -1, 0, NULL);
		}
		else
			GetIPlayerControl()->getIPlayerControlBackPack()->tryAddItem_byGridCopyData(data);
	}
	else
	{
		GetDefManagerProxy()->removeCustom(ret.id, FULLY_PACKING_CUSTOM_MODEL, true);
	}

	notifyCreateResult(ret.result, ret.uin);
}

void CustomModelPacking::notifyCreateResult(int result, int uin/* =0 */)
{
	if (uin == 0 )
	{
		if(MINIW::ScriptVM::game())
			MINIW::ScriptVM::game()->callFunction("CreatePackingCMResultHandle", "i", result);

		return;
	}
		

	IClientPlayer *player = NULL;
	if (GetWorldManagerPtr())
		player = GetWorldManagerPtr()->getPlayerByUin(uin);

	if (player && player->hasUIControl())
	{
		if(MINIW::ScriptVM::game())
			MINIW::ScriptVM::game()->callFunction("CreatePackingCMResultHandle", "i", result);
	}
	else
	{
		//LOG_INFO("kekeke notifyCreateResult 2");
		PB_CreatePackingCMHC createPackingCMHC;
		createPackingCMHC.set_result(result);

		GetGameNetManagerPtr()->sendToClient(uin, PB_CREATE_PACKINGCM_HC, createPackingCMHC);
	}
}

int CustomModelPacking::setFCMByPackingBlock(FullyCustomModel *fcm, World *pworld, WCoord editoffsetpos, WCoord startpos, WCoord endpos)
{
	WCoord realStartPos = startpos - editoffsetpos;

	std::map<int, CustomModelData> tempCustomModelDatas;
	WCoord dim = WCoord(0, 0, 0);
	extractCustomModelDatas(tempCustomModelDatas, pworld, false, dim, realStartPos, startpos, endpos);

	int idx = 1;
	std::vector<PackingCustomItemData> newCustomItems;

	auto iter = tempCustomModelDatas.begin();
	for (; iter != tempCustomModelDatas.end(); iter++)
	{
		if (iter->second.blocks.size() <= 0 || iter->second.blocks.size() == 1 && iter->second.blocks[0].getResID() <= 0)
			continue;

		PackingCustomItemData itemData;
		std::string filename = createOneCMByPacking(iter->second, idx, &itemData);
		if (filename.empty())
			continue;

		char subName[32];
		sprintf(subName, "%s%d", GetDefManagerProxy()->getStringDef(16014), idx);
		Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, 0, 0);
		fcm->setPackingFCMData(iter->second.packingblockpos, subName, filename, quat);
		idx++;

		
		newCustomItems.push_back(itemData);
	}

	PB_CustomItemIDsHC customItemIDsHC;
	for (size_t i = 0; i < newCustomItems.size(); i++)
	{
		customItemIDsHC.add_customitemids(newCustomItems[i].itemid);
		customItemIDsHC.add_custommodelfilenames(newCustomItems[i].model);
		customItemIDsHC.add_custommodelclassnames("");
		customItemIDsHC.add_customtypes(BLOCK_MODEL);
		customItemIDsHC.add_involvedids(0);
		customItemIDsHC.add_custommodelfolderindexs(0);
	}
	GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOM_ITEMIDS_HC, customItemIDsHC);

	return idx;
}

std::string CustomModelPacking::createOneCMByPacking(CustomModelData data, int idx, PackingCustomItemData *itemdata)
{
	if (!CustomModelMgr::GetInstancePtr() || !GetIPlayerControl())
		return "";

	int id = CustomModelMgr::GetInstancePtr()->getFreeId(BLOCK_MODEL);
	if (id <= 0)
	{
		//没有可分配的id了
		GetIPlayerControl()->iNotifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
		return "";
	}

	if (data.blocks.size() <= 0)
		return "";

	unsigned long blockCrc = MINIW::Crc32Calc(&data.blocks[0], sizeof(Block)* data.blocks.size());
	unsigned long relativePosCrc = MINIW::Crc32Calc(&data.relativepos[0], sizeof(WCoord)* data.relativepos.size());

	auto *custommodel = CustomModelMgr::GetInstancePtr()->hasCustomModel(blockCrc, relativePosCrc, "", "", BLOCK_MODEL);
	if (custommodel)
		return custommodel->getFileName();

	char filename[256];
#if defined(_WIN32)
	sprintf(filename, "%d%d%I64d", GetIPlayerControl()->GetIUin(), idx, time(NULL));
#else
	sprintf(filename, "%d%d%ld", GetIPlayerControl()->GetIUin(), idx, time(NULL));
#endif


	custommodel = ENG_NEW(CustomModel)(id, filename);
	custommodel->setData(data.blocks, data.relativepos, true);
	if (!CustomModelMgr::GetInstancePtr()->saveOneCMByPacking(custommodel, filename))
	{
		ENG_DELETE(custommodel);
		return  "";
	}

	CustomModelMgr::GetInstancePtr()->addCustomItemData(id, filename, "default", BLOCK_MODEL);
	GetDefManagerProxy()->addDefByCustomModel(id, BLOCK_MODEL, filename, custommodel->getModelName(), custommodel->getModelDesc(), custommodel->getBox());	

	itemdata->itemid = id;
	itemdata->model = filename;
	return filename;
}

int CustomModelPacking::setFCMByPackingCM(FullyCustomModel *fcm, World *pworld, WCoord startpos, WCoord endpos)
{
	int idx = 1;
	//WCoord dim = m_EditEndPos - m_EditStartPos;
	for (int y = startpos.y; y <= endpos.y; y++)
	{
		for (int z = startpos.z; z <= endpos.z; z++)
		{
			for (int x = startpos.x; x <= endpos.x; x++)
			{
				WCoord blockPos = WCoord(x, y, z);
				Block srcBlock = pworld->getBlock(WCoord(x, y, z));
				if (srcBlock.isEmpty())
					continue;

				int blockId = srcBlock.getResID();
				if (blockId <=0)
					continue;

				std::string skey = "";
				auto *blockDef = GetDefManagerProxy()->getBlockDef(blockId);
				if (blockDef && blockDef->Type == "custombasic") //微缩数据
					skey = blockDef->Texture2;

				if(skey.empty())
					continue;

				WCoord packingBlockPos = WCoord(x - startpos.x, y - startpos.y, z - startpos.z);

				if (CustomModelMgr::GetInstancePtr()->hasAvatarModel(skey)) //有数据的位置
				{
					idx++;

					char subName[32] = { 0 };
					sprintf(subName, "%sg%d", GetDefManagerProxy()->getStringDef(16014), idx);

					int placeDir = pworld->getBlockData(blockPos);

					Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, 0, 0);
					fcm->setPackingFCMData(packingBlockPos, subName, skey, quat, placeDir);
				}
			}
		}
	}

	return idx;
}

int CustomModelPacking::rotateModeConverPlaceDir(PACKING_CM_RORATE_MODE mode)
{
	if (mode == PACKING_CM_RORATE_90)
		return DIR_POS_X;
	else if (mode == PACKING_CM_RORATE_180)
		return DIR_POS_Z;
	else if (mode == PACKING_CM_RORATE_270)
		return DIR_NEG_X;

	return DIR_NEG_Z;
}

PACKING_CM_RORATE_MODE CustomModelPacking::placeDirConverRotateMode(int placedir)
{
	if (placedir == DIR_POS_X)
		return PACKING_CM_RORATE_270;
	else if (placedir == DIR_NEG_Z)
		return PACKING_CM_RORATE_0;
	else if (placedir == DIR_NEG_X)
		return PACKING_CM_RORATE_90;

	return PACKING_CM_RORATE_180;
}

void CustomModelPacking::converRelativePosByPlaceDir(WCoord &pos, int placedir)
{
	if (placedir == DIR_NEG_X)
	{
		int tempx = pos.x;
		pos.x = pos.z;
		pos.z = -tempx;
	}
	else if (placedir == DIR_POS_Z)
	{
		int tempx = pos.x;
		pos.x = -pos.x;
		pos.z = -pos.z;
	}
	else if (placedir == DIR_POS_X)
	{
		int tempx = pos.x;
		pos.x = -pos.z;
		pos.z = tempx;
	}
}


int CustomModelPacking::getRealPlaceDir(int placedir, int forwarddir)
{
	if (forwarddir == DIR_NEG_X)
	{
		return RotateDir90(placedir);
	}
	else if (forwarddir == DIR_POS_Z)
	{
		return ReverseDirection(placedir);
	}
	else if (forwarddir == DIR_POS_X)
	{
		return ReverseDirection(RotateDir90(placedir));
	}

	return placedir;
}

void CustomModelPacking::setEditPos(PACKING_CM_MODE mode)
{
	m_pSelectArea->getAreaSize(m_EditStartPos, m_EditEndPos);

	if (m_EditStartPos.x > m_EditEndPos.x)
	{
		int temp = m_EditStartPos.x;
		m_EditStartPos.x = m_EditEndPos.x;
		m_EditEndPos.x = temp;
	}
	if (m_EditStartPos.y > m_EditEndPos.y)
	{
		int temp = m_EditStartPos.y;
		m_EditStartPos.y = m_EditEndPos.y;
		m_EditEndPos.y = temp;
	}
	if (m_EditStartPos.z > m_EditEndPos.z)
	{
		int temp = m_EditStartPos.z;
		m_EditStartPos.z = m_EditEndPos.z;
		m_EditEndPos.z = temp;
	}

	
	int centerX = 0;
	int centerZ = 0;

	if (mode == PACKING_CM_MODE_BLOCK)
	{
		centerX = ((m_EditEndPos.x - m_EditStartPos.x) / BLOCK_MODEL_SIZE + 1) * BLOCK_SIZE / 2;
		centerZ = ((m_EditEndPos.z - m_EditStartPos.z) / BLOCK_MODEL_SIZE + 1) * BLOCK_SIZE / 2;
	}
	else if (mode == PACKING_CM_MODE_CM)
	{
		centerX = ((m_EditEndPos.x - m_EditStartPos.x)+ 1) * BLOCK_SIZE / 2;
		centerZ = ((m_EditEndPos.z - m_EditStartPos.z)+ 1) * BLOCK_SIZE / 2;
	}

	m_EditCenterPos = WCoord(centerZ, 0, centerX);
}

int CustomModelPacking::createFcmToPackEdit(PACKING_CM_MODE mode)
{
	if (!GetIPlayerControl())
		return -1;

	World *pWorld = GetIPlayerControl()->getIWorld();
	if (!pWorld)
		return -1;

	//设置编辑的起点和终点以及中心点
	setEditPos(mode);
	
	int idx = 0;
	if (mode == PACKING_CM_MODE_BLOCK)
	{
		//提取微缩模型数据
		std::map<int, CustomModelData> tempCustomModelDatas;
		tempCustomModelDatas.clear();

		WCoord dim = WCoord(0, 0, 0);
		extractCustomModelDatas(tempCustomModelDatas, pWorld, true, dim, WCoord(0, 0, 0), m_EditStartPos, m_EditEndPos);

		//设置界面编辑的actorbody
		idx = setEditActorBodyByPackingBlock(tempCustomModelDatas, dim);
	}
	else if (mode == PACKING_CM_MODE_CM)
	{
		//设置界面编辑的actorbody
		idx = setEditActorBodyByPackingCM(pWorld);
	}
	
	return idx;
}


void CustomModelPacking::extractCustomModelDatas(std::map<int, CustomModelData> &custommodeldatas, World *pworld, bool isedit, WCoord &dim, WCoord realstartpos, WCoord startpos, WCoord endpos)
{
	WCoord relativeStartPos = WCoord(0, 0, 0);
	WCoord startPos = startpos;
	if (!isedit)
		startPos = realstartpos;

	WCoord maxpos = WCoord(0, 0, 0);
	WCoord minpos = WCoord(BLOCK_MODEL_SIZE, BLOCK_MODEL_SIZE, BLOCK_MODEL_SIZE);
	for (int y = startPos.y; y <= endpos.y; y++)
	{
		for (int z = startPos.z; z <= endpos.z; z++)
		{
			for (int x = startPos.x; x <= endpos.x; x++)
			{
				if(!isedit && (x < startpos.x || y < startpos.y || z < startpos.z))
					continue;

				relativeStartPos.x = ((x - startPos.x) / BLOCK_MODEL_SIZE) * BLOCK_MODEL_SIZE;
				relativeStartPos.y = ((y - startPos.y) / BLOCK_MODEL_SIZE) * BLOCK_MODEL_SIZE;
				relativeStartPos.z = ((z - startPos.z) / BLOCK_MODEL_SIZE) * BLOCK_MODEL_SIZE;
				WCoord pos(x, y, z);
				if (pworld->getChunk(pos) == NULL)
					continue;
					//pworld->syncLoadChunk(pos, 1);

				Block srcBlock = pworld->getBlock(pos);
				if (srcBlock.isEmpty())
					continue;

				if (!isedit)
				{
					int blockId = srcBlock.getResID();
					if (blockId <= 0)    //空数据就不需要继续添加进去了
						continue;

				}

				int xLength = x - startPos.x;
				int yLength = y - startPos.y;
				int zLength = z - startPos.z;
				WCoord relativePos = WCoord(xLength - relativeStartPos.x, yLength - relativeStartPos.y, zLength - relativeStartPos.z);

				int mapKey = relativeStartPos.x + relativeStartPos.y * 100 + relativeStartPos.z * 10000;
				auto iter = custommodeldatas.find(mapKey);
				if (iter != custommodeldatas.end())
				{
					int blockId = srcBlock.getResID();
					if (blockId <= 0)    //空数据就不需要继续添加进去了
						continue;

					iter->second.blocks.push_back(srcBlock);
					iter->second.relativepos.push_back(relativePos);

					if (isedit)
					{
						if (xLength > maxpos.x)
							maxpos.x = xLength;
						if (yLength > maxpos.y)
							maxpos.y = yLength;
						if (zLength > maxpos.z)
							maxpos.z = zLength;

						if (xLength < minpos.x)
							minpos.x = xLength;
						if (yLength < minpos.y)
							minpos.y = yLength;
						if (zLength < minpos.z)
							minpos.z = zLength;

					}
				}
				else
				{
					CustomModelData data;
					data.blocks.push_back(srcBlock);
					data.relativepos.push_back(relativePos);
					data.packingblockpos = WCoord(relativeStartPos.x / BLOCK_MODEL_SIZE, relativeStartPos.y / BLOCK_MODEL_SIZE, relativeStartPos.z / BLOCK_MODEL_SIZE);

					custommodeldatas[mapKey] = data;

					if (isedit)
					{
						if (data.packingblockpos.x > dim.x)
							dim.x = data.packingblockpos.x;
						if (data.packingblockpos.y > dim.y)
							dim.y = data.packingblockpos.y;
						if (data.packingblockpos.z > dim.z)
							dim.z = data.packingblockpos.z;
					}
				}
			}
		}
	}

	//能编辑的最大位移
	if (isedit)
	{
		m_EditMaxOffset = WCoord(relativeStartPos.x + BLOCK_MODEL_SIZE - maxpos.x - 1, relativeStartPos.y + BLOCK_MODEL_SIZE - maxpos.y - 1, relativeStartPos.z + BLOCK_MODEL_SIZE - maxpos.z - 1);
		m_EditMinOffset = WCoord(0, 0, 0) - minpos;
	}
		
}

int CustomModelPacking::setEditActorBodyByPackingBlock(std::map<int, CustomModelData> &custommodeldatas, WCoord &dim)
{
	if (m_pEditActorBody)
		SANDBOX_DELETE(m_pEditActorBody);

	m_FrameModels.clear();

	m_pEditActorBody = GetISandboxActorSubsystem()->CreateNewIActorBody();
	m_pEditActorBody->initEditingPakcingCMActor();
	auto *pEntity = m_pEditActorBody->getEntity();

	//添加整个打包编辑模型的根节点
	Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, -90, 0);
	pEntity->AddCustomBone(PACKING_ROOT_NAME, "", Rainbow::Vector3f(1.0, 1.0, 1.0), quat, Rainbow::Vector3f(0, 0, (float)m_EditCenterPos.z));

	////添加微缩模型的根节点
	pEntity->AddCustomBone(PACKING_CM_ROOT_NAME, PACKING_ROOT_NAME, Rainbow::Vector3f(1.0, 1.0, 1.0), Rainbow::Quaternionf::identity, Rainbow::Vector3f(0, 0, 0));

	Rainbow::Model* pSubModel = nullptr;
	if (FullyCustomModelMgr::GetInstancePtr())
	{
		pSubModel = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
	}
	if (pSubModel)
	{
		pEntity->BindCunstomObject(PACKING_ROOT_NAME, pSubModel);
		pSubModel->ShowSkins(false);
	}

	int idx1 = 0;
	int idx2 = 0;
	auto iter = custommodeldatas.begin();
	for (; iter != custommodeldatas.end(); iter++)
	{
		WCoord packingBlockPos = iter->second.packingblockpos;
		converBlockPosByPlaceDir(packingBlockPos, DIR_POS_X, dim, BLOCK_MODEL);
		if (iter->second.blocks.size() > 1 || iter->second.blocks[0].getResID() > 0)  //有数据的位置
		{
			/*long name = time(NULL) + idx1;
			char filename[256];
			sprintf(filename, "%d%ld", GetIPlayerControl()->GetIUin(), name);*/

			CustomModel *custommodel = ENG_NEW(CustomModel)();
			custommodel->setData(iter->second.blocks, iter->second.relativepos);

			idx1++;

			char subName[32] = { 0 };
			sprintf(subName, "weisuomoxing%d", idx1);

			//绑定子模型
			auto *pSubModel = custommodel->getItemModel(NORMAL_MESH);
			if (pSubModel)
			{

				Rainbow::Vector3f offsetpos = Rainbow::Vector3f(
					(float)(packingBlockPos.x*BLOCK_SIZE - m_EditCenterPos.x), 
					(float)(packingBlockPos.y*BLOCK_SIZE					), 
					(float)(packingBlockPos.z*BLOCK_SIZE - m_EditCenterPos.z));
				pEntity->AddCustomBone(subName, PACKING_CM_ROOT_NAME, Rainbow::Vector3f(1.0, 1.0, 1.0), Rainbow::Quaternionf::identity, offsetpos);
				pEntity->BindCunstomObject(subName, pSubModel);
			}

			ENG_DELETE(custommodel);
		}


		//不管有没有数据的位置都绑定一个方块框模型
		auto *pFrameModel = g_BlockMtlMgr.getModel("entity/custommodel/frame1.omod");
		if (pFrameModel)
		{
			char frameName[32] = { 0 };
			sprintf(frameName, "frameName%d", idx2);
			Rainbow::Vector3f offsetpos = Rainbow::Vector3f(
				(float)(packingBlockPos.x*BLOCK_SIZE - m_EditCenterPos.x), 
				(float)(packingBlockPos.y*BLOCK_SIZE					), 
				(float)(packingBlockPos.z*BLOCK_SIZE - m_EditCenterPos.z));
			auto pTexture = GetAssetManager().LoadAsset<Texture2D>("entity/custommodel/frame1.png");
			if (pTexture)
			{
				pFrameModel->SetTexture(ShaderParamNames::g_DiffuseTex, pTexture);
			}
			pEntity->AddCustomBone(frameName, PACKING_ROOT_NAME, Rainbow::Vector3f(1, 1, 1), Rainbow::Quaternionf::identity, offsetpos);
			pEntity->BindCunstomObject(frameName, pFrameModel);

			m_FrameModels.push_back(pFrameModel);
			idx2++;
		}
	}

	return idx1;
}

int CustomModelPacking::setEditActorBodyByPackingCM(World *pworld)
{
	if (m_pEditActorBody)
		SANDBOX_DELETE(m_pEditActorBody);

	m_FrameModels.clear();

	m_pEditActorBody = GetISandboxActorSubsystem()->CreateNewIActorBody();
	m_pEditActorBody->initEditingPakcingCMActor();
	auto *pEntity = m_pEditActorBody->getEntity();

	//添加整个打包编辑模型的根节点
	Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0,-90,0);
	pEntity->AddCustomBone(PACKING_ROOT_NAME, "", Rainbow::Vector3f(1.0, 1.0, 1.0), quat, Rainbow::Vector3f(0, 0, (float)m_EditCenterPos.z));

	////添加微缩模型的根节点
	pEntity->AddCustomBone(PACKING_CM_ROOT_NAME, PACKING_ROOT_NAME, Rainbow::Vector3f(1.0, 1.0, 1.0), Rainbow::Quaternionf::identity, Rainbow::Vector3f(0, 0, 0));

	Rainbow::Model* pSubModel = nullptr;
	if (FullyCustomModelMgr::GetInstancePtr())
	{
		pSubModel = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
	}
	if (pSubModel)
	{
		pEntity->BindCunstomObject(PACKING_ROOT_NAME, pSubModel);
		pSubModel->ShowSkins(false);
	}

	WCoord dim = m_EditEndPos - m_EditStartPos;

	int idx1 = 0;
	int idx2 = 0;
	for (int y = m_EditStartPos.y; y <= m_EditEndPos.y; y++)
	{
		for (int z = m_EditStartPos.z; z <= m_EditEndPos.z; z++)
		{
			for (int x = m_EditStartPos.x; x <= m_EditEndPos.x; x++)
			{
				WCoord blockPos = WCoord(x, y, z);
				Block srcBlock = pworld->getBlock(WCoord(x, y, z));
				if (srcBlock.isEmpty())
					continue;

				int blockId = srcBlock.getResID();
				std::string skey = "";
				auto *blockDef = GetDefManagerProxy()->getBlockDef(blockId);
				if (blockDef && blockDef->Type == "custombasic") //微缩数据
					skey = blockDef->Texture2;
				
				WCoord packingBlockPos = WCoord(x - m_EditStartPos.x, y - m_EditStartPos.y, z - m_EditStartPos.z);
				converBlockPosByPlaceDir(packingBlockPos, DIR_POS_X, dim, BLOCK_MODEL);

				Rainbow::Model *pSubModel = NULL;
				if(!skey.empty())
					pSubModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(skey, EDIT_BLOCK_MESH);

				if (pSubModel)  //有数据的位置
				{
					idx1++;

					char subName[32] = { 0 };
					sprintf(subName, "weisuomoxing%d", idx1);

					int placeDir = pworld->getBlockData(blockPos);
					auto mode = placeDirConverRotateMode(placeDir);
					Rainbow::Vector3f offsetpos = Rainbow::Vector3f(
						(float)(packingBlockPos.x*BLOCK_SIZE - m_EditCenterPos.x+50	), 
						(float)(packingBlockPos.y*BLOCK_SIZE+50						), 
						(float)(packingBlockPos.z*BLOCK_SIZE - m_EditCenterPos.z+50	));
					Rainbow::Quaternionf quat = Rainbow::XYZAngleToQuat(0, mode, 0);
					pEntity->AddCustomBone(subName, PACKING_CM_ROOT_NAME, Rainbow::Vector3f(1.0, 1.0, 1.0), quat, offsetpos);
					pEntity->BindCunstomObject(subName, pSubModel);

					if (!m_bSecondaryCreation && CustomModelMgr::GetInstancePtr()->isDownloadCM(skey))
						m_bSecondaryCreation = true;
				}


				///不管有没有数据的位置都绑定一个方块框模型
				auto *pFrameModel = g_BlockMtlMgr.getModel("entity/custommodel/frame1.omod");
				if (pFrameModel)
				{
					char frameName[32] = { 0 };
					sprintf(frameName, "frameName%d", idx2);
					Rainbow::Vector3f offsetpos = Rainbow::Vector3f(
						(float)(packingBlockPos.x*BLOCK_SIZE - m_EditCenterPos.x), 
						(float)(packingBlockPos.y*BLOCK_SIZE					), 
						(float)(packingBlockPos.z*BLOCK_SIZE - m_EditCenterPos.z));
					auto pTexture = GetAssetManager().LoadAsset<Texture2D>("entity/custommodel/frame1.png");
					if (pTexture)
					{
						pFrameModel->SetTexture(ShaderParamNames::g_DiffuseTex, pTexture);
					}
					pEntity->AddCustomBone(frameName, PACKING_ROOT_NAME, Rainbow::Vector3f(1, 1, 1), Rainbow::Quaternionf::identity, offsetpos);
					pEntity->BindCunstomObject(frameName, pFrameModel);

					m_FrameModels.push_back(pFrameModel);

					idx2++;
				}
			}
		}
	}

	return idx1;
}

void CustomModelPacking::lineFrameModelShow(bool b)
{
	for (size_t i = 0; i < m_FrameModels.size(); i++)
	{
		m_FrameModels[i]->ShowSkins(b);
	}
}
