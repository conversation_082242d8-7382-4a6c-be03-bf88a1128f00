#pragma once

#include "SandboxSingleton.h"
#include "SandboxAutoRef.h"
#include "SandboxListener.h"
#include <vector>
#include <unordered_map>
#include <functional>
#include "SandboxAssetHttpType.h"
#include "SandboxAssetBaseHttp.h"
namespace Rainbow {
	namespace Http {
		class WebRequest;
	};
};

namespace MNSandbox {
	class SandboxAssetData;
	class AssetHttp;
	class EXPORT_SANDBOXDRIVERMODULE AssetReqMgrNew :public Ref, public MNSandbox::Singleton<AssetReqMgrNew>
	{
	public:
		enum class ReqStatus
		{
			ReqBegin = 0,
			Reqing = 1,
			ReqEnd = 2
		};

		enum class UploadState
		{
			None,
			Loading,
			Remove,
		};
	private:
		struct ReqItem
		{
			ReqStatus state = ReqStatus::Reqing;
			AutoRef<AssetHttp> ref;
			ReqItem() = default;
		};
	public:
		AssetReqMgrNew();
		virtual ~AssetReqMgrNew();
	public:
		bool IsNewShortId(const std::string& cloudid);
		AutoRef<SandboxAssetData> GetSandboxAssetData(const std::string& remoteid, int version);
		std::string GetResV3HostUrl(bool isUseHttp, bool havePort);

		void RequestAsset(AutoRef<HttpReq> reqData, AssetBaseHttp::RspCallback cb);
		void PushGetResSingleRequest(AutoRef<SingleReqByIdData> reqData, AssetBaseHttp::RspCallback cb);
		void Gc();
		void Save(long long mapid);
		void Load(long long mapid);
	private:
		void OnTick();
		void HandleGetResByIdsRsp(AutoRef<BackpackGetResByIdsRsp> rsp);
		void HandleGetDownloadUrlByIdsRsp(AutoRef<GetDownloadUrlByIdsRsp> rsp);
		void HandleAssetDetailListRsp(const std::vector<AutoRef<AssetDetailRsp>>& list);
		
	private:
		struct ReqMergeItem
		{
			std::vector<AssetBaseHttp::RspCallback> callbacks;
			ReqStatus reqStatus = ReqStatus::Reqing;
			AutoRef<SingleReqByIdData> reqData;
			std::string req_key;
			ReqMergeItem() = default;
		};
		static int m_mergeReqCacheKeyIdx;
		ListenerClass<AssetReqMgrNew> m_listenGlobalTick;
		std::vector<ReqItem> m_reqList;

		std::unordered_map<std::string, ReqMergeItem> m_mergeReqList;
		std::unordered_map<int, std::vector<ReqMergeItem>> m_mergeReqCache;
		std::unordered_map<std::string, AutoRef<HttpRsp>> m_rspCache;
	private:
		void PushGetResSingle(const std::string& key, AutoRef<SingleReqByIdData> reqData, AssetBaseHttp::RspCallback cb);
		void HandelMergeRequest(const std::vector<ReqMergeItem>& v);
		void DoMergeRequest(AutoRef<HttpReq>mergeReqData, const std::vector<ReqMergeItem>& v);
		void HandleMergeRsp(const std::string& residkey, AutoRef<HttpRsp> rsp);
		void HandleMergeRspEx(const std::string& residkey, AutoRef<HttpRsp> rsp);
	};

}