/*
* studio上报数据专用
*/
#ifndef __STAND_REPORT_FUNCTION_H__
#define __STAND_REPORT_FUNCTION_H__

#include "SandboxDriverModule.h"

namespace MNSandbox
{
	namespace MiniReport
	{
		enum ReportDataType
		{
			StudioMoveLength = 0,		//移动距离
			StudioCameraDegree,			//镜头转动总角度
			StudioUiClickNum,			//UI点击次数
			StudioLoadQueueNum,			//加载队列长度
			StudioPlayerNum,			//场景玩家数
			StudioNodeNum,				//场景节点数

			StudioDataMax
		};

		struct StudioReportData
		{
			int Data[StudioDataMax] = {0};
		};
		/*
		* studio地图进入stage\substage阶段埋点
		*/
		void StudioReportEnterSubstage(int stage, int substage, bool isfinish);

		/*
		* studio地图进入Loading阶段CLOAD_WAIT_SANDBOX、CLOAD_WAIT_CUSTOM的相关埋点
		* isfinish：true表示结束、false表示开始
		*/
		EXPORT_SANDBOXDRIVERMODULE extern void StudioReportSandbox(int loadingstage, int substage, bool isfinish);

		/*
		* 更新StudioReportData数据
		* 默认是累加方式，上报一次之后重置为0
		*/
		EXPORT_SANDBOXDRIVERMODULE extern void SetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType, int value, bool directValue = false);

		/*
		* 获取StudioReportData单条数据 
		*/
		EXPORT_SANDBOXDRIVERMODULE extern int GetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType);

		/*
		* 重置所有StudioReportData数据
		*/
		EXPORT_SANDBOXDRIVERMODULE void ResetAllStudioReportData();

		/*
		* 重置单个StudioReportData数据
		* DataType: ReportDataType
		*/
		EXPORT_SANDBOXDRIVERMODULE void ResetStudioReportData(MNSandbox::MiniReport::ReportDataType DataType);
	}
}

#endif//__STAND_REPORT_FUNCTION_H__