#pragma once
/*
*	file: SceneEffectBox
*	func: 在世界场景中绘制盒
*	by: chenzh
*	time: 2022.8.1
*/
#include "SceneEffectGeom.h"
#include "world_types.h"

class World;

class SceneEffectBox : public SceneEffectGeom //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	SceneEffectBox();
	SceneEffectBox(const WCoord startpos, const WCoord endpos, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE, CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectBox();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;
	//tolua_end

	/* 设置绘制起始位置(角色坐标cm) */
	void SetDrawPos(const WCoord pos) { m_drawpos = pos; }

	// 设置颜色（alpha 可用）
	void SetFaceColor(BlockVector color) { m_faceColor = color; }
	void SetFaceColor(MNSandbox::MNColor color) { SetFaceColor(BlockVector(color._r, color._g, color._b, color._a)); }

	void SetFace(DirectionType face) { m_face = face; }

	// 设置半透。接口封装的不方便，暂时利用纹理填空时的效果，作为半透使用。
	void SetTranslucent(bool enable) { m_Translucent = enable; }
	bool GetTranslucent() const { return m_Translucent; }

protected:

	void CalcVertexsAndIndices(const Rainbow::Vector3f dim);

private:

	bool m_Translucent = false;
	DirectionType m_face{ DIR_NOT_INIT };
	BlockVector m_faceColor{ 255, 0, 0, 255 };
	
	// 缓存
	WCoord m_drawpos;
	std::vector<BlockGeomVert> m_Vertices1; // 顶点缓存
	std::vector<BlockGeomVert> m_Vertices2;
	std::vector<BlockGeomVert> m_Vertices3;
	std::vector<BlockGeomVert> m_Vertices4;
	std::vector<BlockGeomVert> m_Vertices5;
	std::vector<BlockGeomVert> m_Vertices6;
	std::vector<unsigned short> m_Indices; // 索引缓存
}; //tolua_exports
