﻿#include "WaterPressureManager.h"
#include "world.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "ActorManagerInterface.h"
#include "DefManagerProxy.h"
#include "Optick/optick.h"
#include "EffectManager.h"
#include "SandBoxManager.h"
#include "blocks/BlockMaterialBase.h"
using namespace MNSandbox;

inline int xzIndex(int x, int z)
{
	assert(x >= 0 && x < 8);
	assert(z >= 0 && z < 8);
	return (x << 3) | z;
}

/*
*	是否液体方块，仅用于水压系统，减少计算
*/
inline bool isLiquidBlock(int blockid)
{
	/*
		1. 水：id = 3，4
		2. 岩浆：id = 5，6
		3. 蜂蜜：id = 11、12
		4. 流沙：13、14
		5. 剧毒液：17、18
	*/

	if (blockid > 2 && blockid < 7 || blockid > 10 && blockid < 15 || blockid== 17 || blockid == 18 || (blockid > 313 && blockid < 321)
		|| blockid == 245 || blockid == 246 || blockid > 493 && blockid < 498)
		return true;

	return false;
}

WaterPressureManager::WaterPressureManager() : m_World(NULL), m_BlockAreaRefresh(true), m_Tick(0), m_IsOpen(true)
,m_BlockTickRate(16), m_BlockAreaRefreshTick(-5), m_ListenBlockChanged(this, &WaterPressureManager::onBlockChanged)
, m_BlockAreaStartPos(WCoord()), m_WaterPresureStartPos(WCoord()),m_ListenStackTick(0)
{
	m_WaterPressureCoefficient = GetLuaInterfaceProxy().get_lua_const()->water_pressure_coefficient;
	m_BlockHardnessVal = GetLuaInterfaceProxy().get_lua_const()->block_hardness_value;
	m_BlockPressureVal = GetLuaInterfaceProxy().get_lua_const()->block_pressure_value;
	m_BlockScanAllRange = GetLuaInterfaceProxy().get_lua_const()->block_scan_all_range;
	m_BlockScanLiquidRange = GetLuaInterfaceProxy().get_lua_const()->block_scan_liquid_range;

	m_BlockDestoryList.clear();
	m_WaterPresureBuffer.clear();
	m_BlockAreaBuffer.clear();
	m_ListenStack.clear();

	changeBlockScanAllRange(m_BlockScanAllRange);

	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("Client_Player_enterWorld");
	SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("Client_Player_enterWorld", nullptr, [&](SandboxContext context) -> SandboxResult {
		IClientPlayer* player = (IClientPlayer*)context.GetData_Userdata("player");
		if (player && GetIPlayerControl() && player->getUin() == GetIPlayerControl()->GetIUin())// 本地玩家才更新
		{
			m_BlockAreaRefresh = true;
			m_BlockAreaRefreshTick = -2;
			//if (g_WorldMgr && (g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isGameMakerMode())) m_IsOpen = false;// 创造模式没有水压
		}
		return SandboxResult(nullptr, true);
	});
}

WaterPressureManager::~WaterPressureManager()
{
	m_ListenBlockChanged.ClearBindNotify();
	m_BlockDestoryList.clear();
	m_WaterPresureBuffer.clear();
	m_BlockAreaBuffer.clear();

	SandboxEventDispatcherManager::GetGlobalInstance().DestroyEventDispatcher("Client_Player_enterWorld");
}

void WaterPressureManager::changeBlockScanAllRange(int value)
{
	m_BlockScanAllRange = value;
	if (m_BlockScanAllRange % 2 == 1) m_BlockScanAllRange++;// 以中心对称左右两边相等，需要偶数
	if (m_BlockScanAllRange > 126) m_BlockScanAllRange = 126;		// 性能限制
	std::vector<short> bufferX(m_BlockScanAllRange, 0);
	std::vector<std::vector<short>> bufferY(m_BlockScanAllRange, bufferX);
	m_BlockAreaBuffer.clear();
	m_BlockAreaBuffer.resize(m_BlockScanAllRange + 1, bufferY);
	m_BlockAreaRefresh = true;
	m_BlockTickRate = m_BlockScanAllRange / 2;
	if (m_BlockTickRate % 2 == 1) m_BlockTickRate++;// 后面分帧逻辑需要偶数
}

void WaterPressureManager::tick()
{
	OPTICK_EVENT();
	if (!m_IsOpen || !GetIPlayerControl() || !GetIPlayerControl()->getIWorld()) return;
	if (m_World != GetIPlayerControl()->getIWorld())
	{
		m_ListenBlockChanged.ClearBindNotify();
		m_World = GetIPlayerControl()->getIWorld();
		m_World->m_notifyBlockChanged.Subscribe(m_ListenBlockChanged);
	}

	if (m_Tick >= 999) m_Tick = 0;			// 防止一直计算下去，超过长度
	m_Tick++;


	// 玩家水压都在本地计算。
	playerTick();
	blockTick();
	updateBlockAreaBuffer();
	destoryBlockTick();

	if (m_ListenStackTick < 0)
	{
		m_ListenStackTick++;
	}
	else
	{
		listenStackTick();
	}
}

void WaterPressureManager::playerTick()
{
	if (!GetIPlayerControl() || !m_World) return;

	if (m_Tick % 60 == 0) // 每3秒校验一下玩家水压值
		updatePlayerRoundLiquidDepth();

	int depth = 0;
	int lowerDepth = 0;
	auto clientactor = dynamic_cast<IClientActor*>(GetIPlayerControl());
	if (isLiquidBlock(m_World->getBlockID(CoordDivBlock(clientactor->getEyePosition()))))
	{
		depth = getLiquidDepthBufferByPos(clientactor->getEyePosition()) + 1;
	}
	if (isLiquidBlock(m_World->getBlockID(CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition()))))
	{
		lowerDepth = getLiquidDepthBufferByPos(GetIPlayerControl()->GetPlayerControlPosition()) + 1;
	}

	auto attr = clientactor->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
	if (attr)
	{
		bool result = attr->Event2().Emit<int, int, int, World*>("PlayerAttrib_waterPress",
			depth, lowerDepth, m_WaterPressureCoefficient, m_World);
		Assert(result);
	}
}

int WaterPressureManager::getWaterPressureByPos(const WCoord& pos,int range /* = 5 */)
{
	WCoord blockPos = CoordDivBlock(pos);
	int h = getUnitedLiquidMaxHeight(blockPos.x, blockPos.y, blockPos.z, range);
	// 水压等级=（（扫描高度-1）/换算系数+1）（不进位取整）
	// h - y 是实际高度 - 1
	int press = (h - blockPos.y) / m_WaterPressureCoefficient + 1;
	if (press > 10) press = 10;
	return press;
}

int WaterPressureManager::getWaterPressureBufferByPos(const WCoord& pos)
{
	int press = getLiquidDepthBufferByPos(pos) / m_WaterPressureCoefficient + 1;
	if (press > 10) press = 10;
	return press;
}

int WaterPressureManager::getLiquidDepthBufferByPos(const WCoord& pos)
{
	return getLiquidDepthBufferByBlockPos(CoordDivBlock(pos));
}

int WaterPressureManager::getLiquidDepthBufferByBlockPos(const WCoord& pos)
{
	int offY = pos.y - m_BlockAreaStartPos.y;
	int offX = pos.x - m_BlockAreaStartPos.x;
	int offZ = pos.z - m_BlockAreaStartPos.z;
	if (offY > -1 && offY < m_BlockScanAllRange + 1 && offX > -1 && offX < m_BlockScanAllRange && offZ > -1 && offZ < m_BlockScanAllRange)
	{
		return m_BlockAreaBuffer[offY][offX][offZ];
	}
	return 0;
}

void WaterPressureManager::blockTick()
{
	if (!m_BlockAreaRefresh || !GetIPlayerControl() || !m_World) return;
	if (!m_World->getChunk(CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition())))return;
	if (m_World->getBlockID(CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition())) == 4095) return;

	if (m_BlockAreaRefresh)
	{
		m_BlockAreaRefreshTick++;
		if (m_BlockAreaRefreshTick == 0)
		{
			m_BlockAreaStartPos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition()) - m_BlockScanAllRange / 2;
			if (m_BlockAreaStartPos.y < 0) m_BlockAreaStartPos.y = 0;
		}
		if (m_BlockAreaRefreshTick >= m_BlockTickRate)
		{
			m_BlockAreaRefreshTick = -1;
			m_BlockAreaRefresh = false;
			return;
		}
	}

	int interval = m_BlockTickRate -6;// 以下需要分帧处理
	int interval_2 = 4;
	int internal_3 = 2;
	if (m_BlockAreaRefreshTick < interval)		// 找出第一个m_BlockScanAllRange高度平面，算出全部方块深度值
	{
		if (m_BlockAreaRefreshTick < 0) 
			return;
		int s_x = (m_BlockAreaRefreshTick % interval) * m_BlockScanAllRange / interval;
		int e_x = ((m_BlockAreaRefreshTick + 1) % (interval + 1)) * m_BlockScanAllRange / interval;
		int blockPosY = m_BlockAreaStartPos.y + m_BlockScanAllRange;
		int blockPosX = m_BlockAreaStartPos.x;
		int blockPosZ = m_BlockAreaStartPos.z;
		for (int x = s_x; x < e_x; x++)
		{
			blockPosX = m_BlockAreaStartPos.x + x;
			auto& bufferX = m_BlockAreaBuffer[m_BlockScanAllRange][x];
			/*for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				blockPosZ = m_BlockAreaStartPos.z + z;
				if (isLiquidBlock(m_World->getBlockID(blockPosX, blockPosY, blockPosZ)))
					bufferX[z] = getUnitedLiquidMaxHeight(blockPosX, blockPosY, blockPosZ, m_BlockScanLiquidRange) - blockPosY;
				else
					bufferX[z] = -1;
			}*/
			int range = m_BlockScanLiquidRange;
			int num = (m_BlockScanAllRange + range - 1) / range;
			int half = range / 2;
			for (int i = 0; i < num; i++)
			{
				int value = half + i * range;
				blockPosZ = m_BlockAreaStartPos.z + value;
				std::vector<short> list(xzIndex(range, range) + 1, 0);
				m_WaterPresureBuffer.clear();
				m_WaterPresureBuffer.resize(CHUNK_BLOCK_Y, list);
				m_WaterPresureStartPos = WCoord(blockPosX - half, blockPosY, blockPosZ - half);
				int depth = findRoundUnitedLiquid(blockPosX, blockPosY, blockPosZ, range) - blockPosY;
				m_WaterPresureBuffer.clear();
				for (int k = 0; k < range; k++)
				{
					int idx = k + i * range;
					if (idx < m_BlockScanAllRange)
					{
						if (isLiquidBlock(m_World->getBlockID(blockPosX, blockPosY, m_BlockAreaStartPos.z + idx)))
							bufferX[idx] = depth;
						else
							bufferX[idx] = -1;
					}
				}
			}
		}
	}
	else if(m_BlockAreaRefreshTick < interval + interval_2)
	{
		// 计算m_BlockScanAllRange高度平面之下得每个平面
		// 以算出的最高读平面的压强往下算即可，减少重复遍历block计算。
		int s_y = ((interval + interval_2 - m_BlockAreaRefreshTick) % (interval + 1)) * m_BlockScanAllRange / interval_2;
		int e_y = ((interval + interval_2 - m_BlockAreaRefreshTick - 1) % interval) * m_BlockScanAllRange / interval_2;
		if (e_y == 0) e_y = -1;
		int blockPosY = m_BlockAreaStartPos.y;
		int blockPosX = m_BlockAreaStartPos.x;
		int blockPosZ = m_BlockAreaStartPos.z;
		std::vector<short> listX(m_BlockScanAllRange, 0);
		for (int y = s_y; y > e_y; y--)
		{
			blockPosY = m_BlockAreaStartPos.y + y;
			auto& bufferY = m_BlockAreaBuffer[y];
			if (y - 1 > 0)
			{
				m_BlockAreaBuffer[y - 1].clear();
				m_BlockAreaBuffer[y - 1].resize(m_BlockScanAllRange, listX);
			}
			for (int x = 0; x < m_BlockScanAllRange; x++)
			{
				blockPosX = m_BlockAreaStartPos.x + x;
				auto& bufferX = bufferY[x];
				for (int z = 0; z < m_BlockScanAllRange; z++)
				{
					blockPosZ = m_BlockAreaStartPos.z + z;
					if (isLiquidBlock(m_World->getBlockID(WCoord(blockPosX, blockPosY, blockPosZ))))
						bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
					else
						bufferX[z] = -1;
				}
			}
		}
	}
	else
	{
		// 以最低平面，往上再次计算，此举保证竖直向下遇到固体方块阻挡会不准确得问题
		int s_y = ((m_BlockAreaRefreshTick + internal_3 - m_BlockTickRate) % internal_3) * m_BlockScanAllRange / internal_3;
		int e_y = ((m_BlockAreaRefreshTick + internal_3 - m_BlockTickRate + 1) % (internal_3 + 1)) * m_BlockScanAllRange / internal_3;

		int blockPosY = m_BlockAreaStartPos.y;
		int blockPosX = m_BlockAreaStartPos.x;
		int blockPosZ = m_BlockAreaStartPos.z;
		for (int y = s_y; y < e_y; y++)
		{
			blockPosY = m_BlockAreaStartPos.y + y;
			auto& bufferY = m_BlockAreaBuffer[y];
			for (int x = m_BlockScanAllRange - 1; x > -1; x--)
			{
				blockPosX = m_BlockAreaStartPos.x + x;
				auto& bufferX = bufferY[x];
				for (int z = m_BlockScanAllRange - 1; z > -1; z--)
				{
					blockPosZ = m_BlockAreaStartPos.z + z;
					if (bufferX[z] > -1)
					{
						bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
					}
					else
					{
						//bufferX[z] = -1;
						isSatifyDestorySolidBlock(WCoord(blockPosX, blockPosY, blockPosZ));
					}
				}
			}
		}
	}
}

int WaterPressureManager::findRoundUnitedLiquidBuffer(int x, int y, int z)
{
	int h = 0;
	if (y > -1 && y <= m_BlockScanAllRange && x > -1 && x < m_BlockScanAllRange && z > -1 && z < m_BlockScanAllRange)
	{
		if (y < m_BlockScanAllRange)
		{
			int val = m_BlockAreaBuffer[y + 1][x][z] + 1;
			if (h < val) h = val;
		}

		if (y > 1)
		{
			int val = m_BlockAreaBuffer[y - 1][x][z] - 1;
			if (h < val) h = val;
		}

		auto& list = m_BlockAreaBuffer[y];
		if (x - 1 > -1)
		{
			int val = list[x - 1][z];
			if (h < val) h = val;
		}
		if (x + 1 < m_BlockScanAllRange)
		{
			int val = list[x + 1][z];
			if (h < val) h = val;
		}
		if (z + 1 < m_BlockScanAllRange)
		{
			int val = list[x][z + 1];
			if (h < val) h = val;
		}
		if (z - 1 > -1)
		{
			int val = list[x][z - 1];
			if (h < val) h = val;
		}
	}
	return h;
}

void WaterPressureManager::updateBlockAreaBuffer()
{
	if (!m_World || !GetIPlayerControl()) return;
	WCoord curPos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition()) - m_BlockScanAllRange / 2;
	WCoord pos = curPos - m_BlockAreaStartPos;
	updateBlockAreaBufferX(pos.x);
	if (curPos.y > 0)
		updateBlockAreaBufferY(pos.y);
	updateBlockAreaBufferZ(pos.z);
}

void WaterPressureManager::updateBlockAreaBufferX(int val)
{
	if (val == 0) return;
	int absX = abs(val);

	int sign = 1;
	if (val < 0)
		sign = -1;
	if (absX > 1) absX = 1; // 只允许每tick计算几次

	for (int x = 0; x < absX; x++)
	{
		// 先操作数组队列增删，初始化新增的区域数据
		for (int y = m_BlockScanAllRange; y > -1; y--)
		{
			auto& bufferY = m_BlockAreaBuffer[y];
			std::vector<short> bufferX(m_BlockScanAllRange, 0);
			if (sign > 0)
			{
				bufferY.erase(bufferY.begin());
				bufferY.push_back(bufferX);
			}
			else
			{
				bufferY.pop_back();
				bufferY.emplace(bufferY.begin(), bufferX);
			}
		}
		int blockPosY = m_BlockAreaStartPos.y + m_BlockScanAllRange;
		int xIndex = m_BlockScanAllRange - 1;
		int blockPosX = m_BlockAreaStartPos.x + xIndex + x + 1;
		if (sign < 0)
		{
			xIndex = 0;
			blockPosX = m_BlockAreaStartPos.x - xIndex - (x + 1);
		}
		// 计算出最顶层得水压数据
		{
			auto& bufferX_ = m_BlockAreaBuffer[m_BlockScanAllRange][xIndex];
			/*for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				int blockPosZ = m_BlockAreaStartPos.z + z;
				if (isLiquidBlock(m_World->getBlockID(blockPosX, blockPosY, blockPosZ)))
				{
					bufferX_[z] = getUnitedLiquidMaxHeight(blockPosX, blockPosY, blockPosZ, m_BlockScanLiquidRange) - blockPosY;
				}
				else
					bufferX_[z] = -1;
			}*/
			int range = m_BlockScanLiquidRange;
			int num = (m_BlockScanAllRange + range - 1) / range;
			int half = range / 2;
			for (int i = 0; i < num; i++)
			{
				int value = half + i * range;
				int blockPosZ = m_BlockAreaStartPos.z + value;
				std::vector<short> list(xzIndex(range, range) + 1, 0);
				m_WaterPresureBuffer.clear();
				m_WaterPresureBuffer.resize(CHUNK_BLOCK_Y, list);
				m_WaterPresureStartPos = WCoord(blockPosX - half, blockPosY, blockPosZ - half);
				int depth = findRoundUnitedLiquid(blockPosX, blockPosY, blockPosZ, range) - blockPosY;
				m_WaterPresureBuffer.clear();
				for (int k = 0; k < range; k++)
				{
					int idx = k + i * range;
					if (idx < m_BlockScanAllRange)
					{
						if (isLiquidBlock(m_World->getBlockID(blockPosX, blockPosY, m_BlockAreaStartPos.z + idx)))
							bufferX_[idx] = depth;
						else
							bufferX_[idx] = -1;
					}
				}
			}
		}
		// 再计算顶层之下得水压数据
		for (int y = m_BlockScanAllRange - 1; y > -1; y--)
		{
			blockPosY = m_BlockAreaStartPos.y + y;
			auto& bufferX = m_BlockAreaBuffer[y][xIndex];
			for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				if (isLiquidBlock(m_World->getBlockID(WCoord(blockPosX, blockPosY, m_BlockAreaStartPos.z + z))))
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(xIndex, y, z);
				}
				else
				{
					bufferX[z] = -1;
				}
			}
		}
		// 反向计算一次
		int maxVal = 0;
		for (int y = 0; y <= m_BlockScanAllRange; y++)
		{
			blockPosY = m_BlockAreaStartPos.y + y;
			auto& bufferX = m_BlockAreaBuffer[y][xIndex];
			for (int z = m_BlockScanAllRange - 1; z > -1; z--)
			{
				if (bufferX[z] > -1)
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(xIndex, y, z);
					int value = 0;
					int depth = m_BlockAreaBuffer[y][xIndex - sign][z];
					if (depth > 0)
						value = bufferX[z] - depth;
					if (maxVal < value) maxVal = value;
				}
				else
				{
					bufferX[z] = -1;
					isSatifyDestorySolidBlock(WCoord(blockPosX, blockPosY, m_BlockAreaStartPos.z + z));
				}
			}
		}
		m_BlockAreaStartPos.x += sign;

		if (maxVal > 0)
		{
			int maxNum = m_BlockScanAllRange / 2;
			if (sign > 0)
				refreshBlockAreaBufferX(m_BlockScanAllRange - absX - maxNum, m_BlockScanAllRange - 1 - absX);
			else
				refreshBlockAreaBufferX(absX, absX - 1 + maxNum);
		}
	}
}

void WaterPressureManager::updateBlockAreaBufferY(int val)
{
	if (val == 0) return;
	int absY = abs(val);

	int sign = 1;
	if (val < 0)
		sign = -1;
	//if (absY > 1) absY = 1;

	int maxVal = 0;
	for (int y = 0; y < absY; y++)
	{
		std::vector<short> bufferX(m_BlockScanAllRange, 0);
		std::vector<std::vector<short>> bufferY(m_BlockScanAllRange, bufferX);
		if (sign > 0)
		{
			m_BlockAreaBuffer.erase(m_BlockAreaBuffer.begin());
			m_BlockAreaBuffer.push_back(bufferY);
		}
		else
		{
			m_BlockAreaBuffer.pop_back();
			m_BlockAreaBuffer.emplace(m_BlockAreaBuffer.begin(), bufferY);
		}

		int blockPosY = m_BlockAreaStartPos.y + m_BlockScanAllRange + 1 + y;
		if (sign < 0)
			blockPosY = m_BlockAreaStartPos.y - 1 - y;

		auto& lastBufferY = m_BlockAreaBuffer[m_BlockScanAllRange];
		auto& firstBufferY = m_BlockAreaBuffer[0];

		for (int x = 0; x < m_BlockScanAllRange; x++)
		{
			int blockPosX = m_BlockAreaStartPos.x + x;
			for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				int blockPosZ = m_BlockAreaStartPos.z + z;
				if (isLiquidBlock(m_World->getBlockID(WCoord(blockPosX, blockPosY, blockPosZ))))
				{
					if (sign > 0)
					{
						int depth = m_BlockAreaBuffer[m_BlockScanAllRange - 1][x][z];
						if (depth > 0)
						{
							// 正下方水压大于1
							lastBufferY[x][z] = depth - 1;
						}
						else
						{
							lastBufferY[x][z] = findRoundUnitedLiquidBuffer(x, m_BlockScanAllRange, z);
						}
						if (lastBufferY[x][z] < 0)
							lastBufferY[x][z] = 0;
					}
					else
						firstBufferY[x][z] = findRoundUnitedLiquidBuffer(x, 0, z);
				}
				else
				{
					if (sign > 0)
						lastBufferY[x][z] = -1;
					else
						firstBufferY[x][z] = -1;
				}
			}
		}
		// 做一次反向计算
		for (int x = m_BlockScanAllRange - 1; x > -1; x--)
		{
			int blockPosX = m_BlockAreaStartPos.x + x;
			for (int z = m_BlockScanAllRange - 1; z > -1; z--)
			{
				int blockPosZ = m_BlockAreaStartPos.z + z;
				WCoord blockPos = WCoord(blockPosX, blockPosY, blockPosZ);
				if (sign > 0)
				{
					if (lastBufferY[x][z] > -1)
					{
						lastBufferY[x][z] = findRoundUnitedLiquidBuffer(x, m_BlockScanAllRange, z);
					}
					else
						isSatifyDestorySolidBlock(blockPos);
				}
				else
				{
					if (firstBufferY[x][z] > -1)
					{
						firstBufferY[x][z] = findRoundUnitedLiquidBuffer(x, 0, z);
						//int depth = m_BlockAreaBuffer[1][x][z];
						//int value = 0;
						//if (depth > 0)
						//	value = firstBufferY[x][z] - depth * 2;
						//if (maxVal < value) maxVal = value;
					}
					else
						isSatifyDestorySolidBlock(blockPos);
				}
			}
		}
	}
	m_BlockAreaStartPos.y += absY * sign;

	//int maxNum = 0;
	//while (maxVal > 0)
	//{
	//	maxVal *= 0.5f;
	//	if (maxVal < 1.2f) break;
	//	maxNum++;
	//}
	//if (maxNum > 0)
	//{
	//	//if (maxNum > 6) maxNum = 6;// 限制不计算太多，因为后续误差不大
	//	if (sign > 0)
	//		refreshBlockAreaBufferY(m_BlockScanAllRange + 1 - absY - maxNum, m_BlockScanAllRange - absY);
	//	else
	//		refreshBlockAreaBufferY(absY, absY - 1 + maxNum);
	//}
}

void WaterPressureManager::updateBlockAreaBufferZ(int val)
{
	if (val == 0) return;
	// 因为三维数组的原因，z 维度的 计算量最大
	int absZ = abs(val);
	int sign = 1;
	if (val < 0)
		sign = -1;
	if (absZ > 1) absZ = 1; // 只允许每tick计算几次

	for (int z = 0; z < absZ; z++)
	{
		// 先操作数组队列增删，初始化新增的区域数据
		for (int y = m_BlockScanAllRange; y > -1; y--)
		{
			auto& bufferY = m_BlockAreaBuffer[y];
			int blockPosY = m_BlockAreaStartPos.y + m_BlockScanAllRange;
			for (int x = 0; x < m_BlockScanAllRange; x++)
			{
				auto& bufferX = bufferY[x];
				if (sign > 0)
				{
					bufferX.erase(bufferX.begin());
					bufferX.push_back(0);
				}
				else
				{
					bufferX.pop_back();
					bufferX.emplace(bufferX.begin(), 0);
				}
			}
		}
		int blockPosY = m_BlockAreaStartPos.y + m_BlockScanAllRange;
		int zIdx = m_BlockScanAllRange - 1;
		int blockPosZ = m_BlockAreaStartPos.z + zIdx + z + 1;
		if (sign < 0)
		{
			zIdx = 0;
			blockPosZ = m_BlockAreaStartPos.z - zIdx - (z + 1);
		}
		// 计算出最顶层得水压数据
		{
			/*for (int x = 0; x < m_BlockScanAllRange; x++)
			{
				int blockPosX = m_BlockAreaStartPos.x + x;
				auto& bufferX = m_BlockAreaBuffer[m_BlockScanAllRange][x];
				if (isLiquidBlock(m_World->getBlockID(blockPosX, blockPosY, blockPosZ)))
				{
					bufferX[zIdx] = getUnitedLiquidMaxHeight(blockPosX, blockPosY, blockPosZ, m_BlockScanLiquidRange) - blockPosY;
				}
				else
					bufferX[zIdx] = -1;
			}*/
			int range = m_BlockScanLiquidRange;
			int num = (m_BlockScanAllRange + range - 1) / range;
			int half = range / 2;
			for (int i = 0; i < num; i++)
			{
				int value = half + i * range;
				int blockPosX = m_BlockAreaStartPos.x + value;
				std::vector<short> list(xzIndex(range, range) + 1, 0);
				m_WaterPresureBuffer.clear();
				m_WaterPresureBuffer.resize(CHUNK_BLOCK_Y, list);
				m_WaterPresureStartPos = WCoord(blockPosX - half, blockPosY, blockPosZ - half);
				int depth = findRoundUnitedLiquid(blockPosX, blockPosY, blockPosZ, range) - blockPosY;
				m_WaterPresureBuffer.clear();
				for (int k = 0; k < range; k++)
				{
					int idx = k + i * range;
					auto& bufferX = m_BlockAreaBuffer[m_BlockScanAllRange][idx];
					if (idx < m_BlockScanAllRange)
					{
						if (isLiquidBlock(m_World->getBlockID(m_BlockAreaStartPos.x + idx, blockPosY, blockPosZ)))
							bufferX[zIdx] = depth;
						else
							bufferX[zIdx] = -1;
					}
				}
			}
		}
		// 再计算顶层之下得水压数据
		for (int y = m_BlockScanAllRange - 1; y > -1; y--)
		{
			blockPosY = m_BlockAreaStartPos.y + y;
			for (int x = 0; x < m_BlockScanAllRange; x++)
			{
				auto& bufferX = m_BlockAreaBuffer[y][x];
				if (isLiquidBlock(m_World->getBlockID(m_BlockAreaStartPos.x + x, blockPosY, blockPosZ)))
				{
					bufferX[zIdx] = findRoundUnitedLiquidBuffer(x, y, zIdx);
				}
				else
					bufferX[zIdx] = -1;
			}
		}
		int maxVal = 0;
		// 反向计算一次
		for (int y = 0; y <= m_BlockScanAllRange; y++)
		{
			for (int x = m_BlockScanAllRange - 1; x > -1; x--)
			{
				auto& bufferX = m_BlockAreaBuffer[y][x];
				if (bufferX[zIdx] > -1)
				{
					bufferX[zIdx] = findRoundUnitedLiquidBuffer(x, y, zIdx);
					int value = 0;
					int depth = bufferX[zIdx - sign];
					if (depth > 0)
						value = bufferX[zIdx] - depth;
					if (maxVal < value) maxVal = value;
				}
				else
				{
					//bufferX[zIdx] = -1;
					isSatifyDestorySolidBlock(WCoord(m_BlockAreaStartPos.x + x, blockPosY, blockPosZ));
				}
			}
		}
		m_BlockAreaStartPos.z += sign;

		if (maxVal > 0)
		{
			int maxNum = m_BlockScanAllRange / 2;
			if (sign > 0)
				refreshBlockAreaBufferZ(m_BlockScanAllRange - absZ - maxNum, m_BlockScanAllRange - 1 - absZ);
			else
				refreshBlockAreaBufferZ(absZ, absZ - 1 + maxNum);
		}
	}
}

int WaterPressureManager::getUnitedLiquidMaxHeight(int x, int y, int z, int range /* = 5 */)
{
	if (!m_World) return y - 1;
	if (!isLiquidBlock(m_World->getBlockID(x, y, z))) return y - 1;
	if (range % 2 == 0) range++;//保证为奇数，才有中心对称点方块
	int half = (range - 1) / 2;
	std::vector<short> list(xzIndex(range, range) + 1, 0);
	m_WaterPresureBuffer.clear();
	m_WaterPresureBuffer.resize(CHUNK_BLOCK_Y, list);

	// 初始第一层，找出所有的与中心的联通水
	m_WaterPresureStartPos = WCoord(x - half, y, z - half);
	int h = findRoundUnitedLiquid(x, y, z, range);
	m_WaterPresureBuffer.clear();
	return h;
}

int WaterPressureManager::findRoundUnitedLiquid(int x, int y, int z, int range, int flag /* = 0 */)
{
	if (y < 0 || y >= CHUNK_BLOCK_Y) return 0;
	std::vector<short>& list = m_WaterPresureBuffer[y];
	int offX = x - m_WaterPresureStartPos.x;
	int offZ = z - m_WaterPresureStartPos.z;
	int height = y;
	int h = 0;
	if (offX + 1 < range && (flag == 0 || flag == 1 || flag == 3))
	{
		int idx = xzIndex(offX + 1, offZ);
		if (list[idx] == 0)
		{
			int blockid = m_World->getBlockID(x + 1, y, z);
			if (isLiquidBlock(blockid))
			{
				list[idx] = 1;
				h = findRoundUnitedLiquid(x + 1, y, z, range, 1);
				height = height < h ? h : height;
			}
			else
				list[idx] = -1;
		}
	}

	if (offX - 1 > -1 && (flag == 0 || flag == 2 || flag == 4))
	{
		int idx = xzIndex(offX - 1, offZ);
		if (list[idx] == 0)
		{
			int blockid = m_World->getBlockID(x - 1, y, z);
			if (isLiquidBlock(blockid))
			{
				list[idx] = 1;
				h = findRoundUnitedLiquid(x - 1, y, z, range, 2);
				height = height < h ? h : height;
			}
			else
				list[idx] = -1;
		}
	}

	if (offZ + 1 < range && (flag == 0 || flag == 3 || flag == 2))
	{
		int idx = xzIndex(offX, offZ + 1);
		if (list[idx] == 0)
		{
			int blockid = m_World->getBlockID(x, y, z + 1);
			if (isLiquidBlock(blockid))
			{
				list[idx] = 1;
				h = findRoundUnitedLiquid(x, y, z + 1, range, 3);
				height = height < h ? h : height;
			}
			else
				list[idx] = -1;
		}
	}

	if (offZ - 1 > -1 && (flag == 0 || flag == 4 || flag == 1))
	{
		int idx = xzIndex(offX, offZ - 1);
		if (list[idx] == 0)
		{
			int blockid = m_World->getBlockID(x, y, z - 1);
			if (isLiquidBlock(blockid))
			{
				list[idx] = 1;
				h = findRoundUnitedLiquid(x, y, z - 1, range, 4);
				height = height < h ? h : height;
			}
			else
				list[idx] = -1;
			
		}
	}
	
	if (y + 1 < m_WaterPresureBuffer.size())
	{
		int idx = xzIndex(offX, offZ);
		std::vector<short>& list_ = m_WaterPresureBuffer[y + 1];
		if (list_[idx] == 0)
		{
			int blockid = m_World->getBlockID(x, y + 1, z);
			if (isLiquidBlock(blockid))
			{
				list_[idx] = 1;
				h = findRoundUnitedLiquid(x, y + 1, z, range);
				height = height < h ? h : height;
			}
			else
				list_[idx] = -1;
		}
	}
	return height;
}

void WaterPressureManager::destoryBlockTick()
{
	if (!m_World || !GetIPlayerControl()) return;
	
	for (auto iter = m_BlockDestoryList.begin(); iter != m_BlockDestoryList.end();)
	{
		WCoord pos = iter->first;
		DestoryBlockData& data = iter->second;
		if (m_World->getBlockID(pos) == data.blockid)
		{
			data.val++;
			//更新裂纹，有10个阶段
			if (data.val % 10 == 0)
			{
				auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
				if (isSatifyRoundLiquidMinNum(pos))
				{
					m_World->getEffectMgr()->playBlockCrackEffect(pos, data.val / 10, actor->getObjId());
				}
				else
				{
					m_World->getEffectMgr()->playBlockCrackEffect(pos, -1, actor->getObjId());
					iter = m_BlockDestoryList.erase(iter);
					continue;
				}
			}
			if (data.val > 90)
			{
				m_World->getEffectMgr()->playBlockDestroyEffect(0, pos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
				if (m_World->isRemoteMode())
				{
					jsonxx::Object context;
					context << "uin" << GetIPlayerControl()->GetIUin();
					context << "blockid" << data.blockid;
					context << "posX" << pos.x;
					context << "posY" << pos.y;
					context << "posZ" << pos.z;
					context << "dropItem" << 0;
					SandBoxManager::getSingleton().sendToHost("PB_DESTORY_BLOCK_CH", context.bin(), context.binLen());
				}
				else
					m_World->destroyBlock(pos.x, pos.y, pos.z, 0);

				iter = m_BlockDestoryList.erase(iter);
			}
			else
			{
				iter++;
			}
		}
		else
		{
			auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
			m_World->getEffectMgr()->playBlockCrackEffect(pos, -1, actor->getObjId());
			iter = m_BlockDestoryList.erase(iter);
		}
	}
}

void WaterPressureManager::onBlockChanged(WCoord pos, int dstid, int srcid)
{
	if (!m_IsOpen) return;
	WCoord tempPos = CoordDivBlock(pos);
	if (m_World->getChunkBySCoord(tempPos.x, tempPos.z) == NULL) return;
	m_ListenStack.push_back(ListenStackData(pos.x, pos.y, pos.z, dstid, srcid));
	if (m_ListenStack.size() > 100)
		m_ListenStackTick = 1;
	else
		m_ListenStackTick = -1;
}

void WaterPressureManager::isSatifyDestorySolidBlock(const WCoord& pos)
{
	Block block = m_World->getBlock(pos);
	if (!block.GetBlockMaterial()) return;
	const BlockDef* def = block.GetBlockMaterial()->GetBlockDef();
	if (def && def->Hardness != -1 && def->Hardness < m_BlockHardnessVal && def->MoveCollide == 1)
	{
		if (isSatifyRoundLiquidMinNum(pos))
		{
			auto iter = m_BlockDestoryList.find(pos);
			if (iter == m_BlockDestoryList.end())
			{
				m_BlockDestoryList.insert(make_pair(pos, DestoryBlockData(0, block.getResID())));
			}
		}
	}
}

bool WaterPressureManager::isSatifyRoundLiquidMinNum(const WCoord& pos, int num /*=3*/)
{
	int count = 0;

	WCoord blockpos = WCoord(pos.x, pos.y + 1, pos.z);
	int val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	blockpos = WCoord(pos.x, pos.y - 1, pos.z);
	val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	blockpos = WCoord(pos.x + 1, pos.y, pos.z);
	val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	blockpos = WCoord(pos.x - 1, pos.y, pos.z);
	val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	blockpos = WCoord(pos.x, pos.y, pos.z + 1);
	val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	blockpos = WCoord(pos.x, pos.y, pos.z - 1);
	val = getLiquidDepthBufferByBlockPos(blockpos) / m_WaterPressureCoefficient + 1;
	if (val > m_BlockPressureVal) count++;

	return count > num - 1;
}

void WaterPressureManager::listenStackTick()
{
	int size = m_ListenStack.size();

	for (int i = 0; i < size; i++)
	{
		ListenStackData data = m_ListenStack[i];
		WCoord pos = WCoord(data.x, data.y, data.z);
		int dstid = data.dstid;
		int srcid = data.srcid;
		int offY = pos.y - m_BlockAreaStartPos.y;
		int offX = pos.x - m_BlockAreaStartPos.x;
		int offZ = pos.z - m_BlockAreaStartPos.z;
		if (offX > -1 && offX < m_BlockScanAllRange && offZ > -1 && offZ < m_BlockScanAllRange)
		{
			if (offY > -1 && offY < m_BlockScanAllRange + 1)
			{
				if (isLiquidBlock(srcid))		//原本是液体
				{
					if (!isLiquidBlock(dstid))		// 变换后是非液体
					{
						isSatifyDestorySolidBlock(pos);
						m_BlockAreaBuffer[offY][offX][offZ] = -1;
					}
				}
				else  //原本是非液体
				{
					if (isLiquidBlock(dstid)) // 变为液体
					{
						m_BlockAreaBuffer[offY][offX][offZ] = findRoundUnitedLiquidBuffer(offX, offY, offZ);

						
						WCoord blockPos = WCoord(pos.x, pos.y + 1, pos.z);
						isSatifyDestorySolidBlock(blockPos);
						blockPos = WCoord(pos.x - 1, pos.y, pos.z);
						isSatifyDestorySolidBlock(blockPos);
						blockPos = WCoord(pos.x + 1, pos.y, pos.z);
						isSatifyDestorySolidBlock(blockPos);
						blockPos = WCoord(pos.x, pos.y, pos.z - 1);
						isSatifyDestorySolidBlock(blockPos);
						blockPos = WCoord(pos.x, pos.y, pos.z + 1);
						isSatifyDestorySolidBlock(blockPos);
						blockPos = WCoord(pos.x, pos.y - 1, pos.z);
						isSatifyDestorySolidBlock(blockPos);
					}
				}
			}
			else if (offY > m_BlockScanAllRange)
			{
				//TODO
			}
		}
	}

	m_ListenStack.clear();
}

void WaterPressureManager::updatePlayerRoundLiquidDepth()
{
	if (m_BlockAreaRefresh || !GetIPlayerControl()) return;
	// 取玩家眼睛位置校验
	auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
	WCoord eyePos = CoordDivBlock(actor->getEyePosition());
	int realDepth = getUnitedLiquidMaxHeight(eyePos.x, eyePos.y, eyePos.z) - eyePos.y;
	int bufferDepth = getLiquidDepthBufferByBlockPos(eyePos);
	bool ret = abs(realDepth - bufferDepth) > 1;
	int blockPosY = eyePos.y;
	if (!ret)
	{
		// 下半身校验
		WCoord pos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
		realDepth = getUnitedLiquidMaxHeight(pos.x, pos.y, pos.z) - pos.y;
		bufferDepth = getLiquidDepthBufferByBlockPos(pos);
		ret = abs(realDepth - bufferDepth) > 1;
		blockPosY = pos.y;
	}
	if (ret)
	{
		int blockPosX = m_BlockAreaStartPos.x;
		int blockPosZ = m_BlockAreaStartPos.z;
		int max = m_BlockScanAllRange;
		int rem = max % 5;
		if (rem % 5 > 0) max += 5 - rem;
		int maxDepth = 0;
		for (int x = 2; x < m_BlockScanAllRange; x += 5)
		{
			blockPosX = m_BlockAreaStartPos.x + x;
			for (int z = 2; z < m_BlockScanAllRange; z += 5)
			{
				blockPosZ = m_BlockAreaStartPos.z + z;
				int depth = getUnitedLiquidMaxHeight(blockPosX, blockPosY, blockPosZ) - blockPosY;
				if (realDepth < depth) 
					return;
			}
		}

		m_BlockAreaRefresh = true;
		m_BlockAreaRefreshTick = -1;
	}
}

void WaterPressureManager::refreshBlockAreaBufferX(int min /* = -1 */, int max /* = -1 */)
{
	if (min < 0) min = 0;
	if (min >= m_BlockScanAllRange) min = m_BlockScanAllRange - 1;
	if (max < 0) max = 0;
	if (max >= m_BlockScanAllRange) max = m_BlockScanAllRange - 1;

	for (int y = m_BlockScanAllRange - 1; y > -1; y--)
	{
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = min; x <= max; x++)
		{
			auto& bufferX = bufferY[x];
			for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				if (bufferX[z] > -1)
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
				}
			}
		}
	}
	// y 方向由下往上 计算一次
	for (int y = 0; y < m_BlockScanAllRange; y++)
	{
		int blockPosY = m_BlockAreaStartPos.y + y;
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = max; x >= min; x--)
		{
			int blockPosX = m_BlockAreaStartPos.x + x;
			auto& bufferX = bufferY[x];
			for (int z = m_BlockScanAllRange - 1; z > -1; z--)
			{
				if (bufferX[z] > -1)
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
				}
				else
					isSatifyDestorySolidBlock(WCoord(blockPosX, blockPosY, m_BlockAreaStartPos.z + z));
			}
		}
	}
}

void WaterPressureManager::refreshBlockAreaBufferY(int min /* = -1 */, int max /* = -1 */)
{
	if (min < 0) min = 0;
	if (min >= m_BlockScanAllRange) min = m_BlockScanAllRange - 1;
	if (max < 0) max = 0;
	if (max >= m_BlockScanAllRange) max = m_BlockScanAllRange - 1;

	for (int y = min; y <= max; y++)
	{
		int blockPosY = m_BlockAreaStartPos.y + y;
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = 0; x < m_BlockScanAllRange; x++)
		{
			auto& bufferX = bufferY[x];
			for (int z = 0; z < m_BlockScanAllRange; z++)
			{
				if (bufferX[z] > -1)
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
			}
		}
	}
	// y 方向由下往上 计算一次
	for (int y = max; y >= min; y--)
	{
		int blockPosY = m_BlockAreaStartPos.y + y;
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = m_BlockScanAllRange - 1; x > -1; x--)
		{
			int blockPosX = m_BlockAreaStartPos.x + x;
			auto& bufferX = bufferY[x];
			for (int z = m_BlockScanAllRange - 1; z > -1; z--)
			{
				if (bufferX[z] > -1)
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
				else
					isSatifyDestorySolidBlock(WCoord(blockPosX, blockPosY, m_BlockAreaStartPos.z + z));
			}
		}
	}
}

void WaterPressureManager::refreshBlockAreaBufferZ(int min /* = -1 */, int max /* = -1 */)
{
	if (min < 0) min = 0;
	if (min >= m_BlockScanAllRange) min = m_BlockScanAllRange - 1;
	if (max < 0) max = 0;
	if (max >= m_BlockScanAllRange) max = m_BlockScanAllRange - 1;

	// 以计算到的最顶部深度值，往下做加减运算
	for (int y = m_BlockScanAllRange - 1; y > -1; y--)
	{
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = 0; x < m_BlockScanAllRange; x++)
		{
			auto& bufferX = bufferY[x];
			for (int z = min; z <= max; z++)
			{
				if (bufferX[z] > -1)
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
				}
			}
		}
	}
	// y 方向由下往上 计算一次
	for (int y = 0; y < m_BlockScanAllRange; y++)
	{
		int blockPosY = m_BlockAreaStartPos.y + y;
		auto& bufferY = m_BlockAreaBuffer[y];
		for (int x = m_BlockScanAllRange - 1; x > -1; x--)
		{
			int blockPosX = m_BlockAreaStartPos.x + x;
			auto& bufferX = bufferY[x];
			for (int z = max; z >= min; z--)
			{
				if (bufferX[z] > -1)
				{
					bufferX[z] = findRoundUnitedLiquidBuffer(x, y, z);
				}
				else
					isSatifyDestorySolidBlock(WCoord(blockPosX, blockPosY, m_BlockAreaStartPos.z + z));
			}
		}
	}
}
