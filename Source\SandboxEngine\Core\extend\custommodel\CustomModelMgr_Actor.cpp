

#include "IPlayerControl.h"
#include "IClientPlayer.h"

#include "CustomActorModel_generated.h"
#include "GlobalFunctions.h"

#include "File/DirVisitor.h"
#include "OgreUtils.h"

#include "GameNetManager.h"
#include "genCustomModel.h"
#include "Texture/TextureRenderGen.h"
#include "ClientActorHelper.h"
#include "WorldManager.h"
#include "WorldStringManagerProxy.h"
#include "IActorBody.h"
#include "blocks/BlockMaterialMgr.h"
#include "File/FileManager.h"
#include "CustomModelMgr.h"
#include "blocks/container_world.h"
#include "OgreEntity.h"
#include "OgreModel.h"
#include "ClientInfoProxy.h"
#include "CustomModelWaitSyncListMgr.h"
#include "DefManagerProxy.h"
#include "PlayManagerInterface.h"

using namespace MNSandbox;
using namespace MINIW;
using namespace Rainbow;
const int BODYTEX_WIDTH = 200;
const int BODYTEX_HEIGHT = 200;

//TODO:m_TextureGen
Rainbow::SharePtr<Rainbow::Texture2D>  CustomModelMgr::genActorTexture(std::string markname)
{
	//RenderSystem::GetInstance().m_DisableTexFilter = true;

	auto *data = findCustomActorModelData(MAP_MODEL_CLASS, markname);
	if (!data)
		data = findCustomActorModelData(RES_MODEL_CLASS, markname);

	if (!data)
	{
		//RenderSystem::GetInstance().m_DisableTexFilter = false;
		return nullptr;
	}


	auto *pEntity = Entity::Create();
	Rainbow::Model *pModel = NULL;

	if (data->type == HUMAN_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body1.omod");
	else if (data->type == QUADRUPED_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body2.omod");
	else if (data->type == SINGLE_BONE_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body3.omod");

	bool pack = true;
	if (pModel)
	{
		pEntity->Load(pModel);
		pModel->SetName(markname.c_str());

		if (!data->skindisplay)
			pEntity->GetMainModel()->ShowSkins(false);

		auto iter = data->models.begin();
		for (; iter != data->models.end(); iter++)
		{
			std::string boneName = iter->first;

			if (iter->second.size() > 0)
			{
				std::string subModelName = boneName;
				transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);

				pEntity->GetMainModel()->ShowSkin(subModelName.c_str(), false);
			}

			for (size_t i = 0; i < iter->second.size(); i++)
			{
				auto *pSubModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
				if (!pSubModel)
				{
					pack = false;
					continue;
				}
				pSubModel->SetPosition(Rainbow::Vector3f(iter->second[i].offset_x, iter->second[i].offset_y, iter->second[i].offset_z));
				pSubModel->SetRotation(iter->second[i].newrotatemode, iter->second[i].yaw, iter->second[i].pitch, iter->second[i].roll);
				pSubModel->SetScale(iter->second[i].scale3);
				pEntity->BindCunstomObject(boneName.c_str(), pSubModel);
			}
		}

		if (data->models.size() && pack)
		{
			GenCustomModelManager::GetInstance().bindCusntomObjs(pEntity);
		}
	}

	if (data->type == HUMAN_MODEL)
		pEntity->SetScale(1.3f);
	else if (data->type == QUADRUPED_MODEL)
		pEntity->SetScale(1.5f);
	else if (data->type == SINGLE_BONE_MODEL)
		pEntity->SetScale(1.7f);

	pEntity->SetRotation(45, 0, 0);
	pEntity->SetPosition(WorldPos(-50, 0, 0));
	pEntity->UpdateTick(0);
	pEntity->SetInstanceData(Vector4f(1, 1, 0, 0));
	
	Rainbow::UIModelView3D* modelView = nullptr;
	Rainbow::SharePtr<Rainbow::Texture2D>  ptex = m_TextureGen->GenerateEx(pEntity->GetGameObject(), BODYTEX_WIDTH, BODYTEX_HEIGHT, &modelView, true);//true 不自动释放纹理信息否则不能生成微缩纹理图片
	if (modelView)
	{
		Rainbow::Camera* camera = modelView->GetCamera();
		{
			float w = 1.0f/tan((Deg2Rad(20/2.f)));
			float h = w * WIN_DEF_SCRH/WIN_DEF_SCRW ;
			float fovy = Rad2Deg(atan(1.0f / h));
			camera->SetVerticalFieldOfView(fovy);
		}
		camera->LookAt(Rainbow::Vector3f(0, 140.0f, -1000.0f), Rainbow::Vector3f(0, 128.0f, 0.0f));
		//Rainbow::Vector3f dir(1.0f, 1.0f, 1.0f);
		//dir  = Rainbow::Normalize(dir);
		//Light *plight = m_TextureGen->addLight(LT_DIRECT);
		//plight->setDirection(dir);
		//plight->m_AmbientColor = ColourValue(1.0f, 1.0f, 1.0f);
		//plight->m_DiffuseColor = ColourValue(1.0f, 1.0f, 1.0f);
		//plight->enableAmbient();
		//plight->enableShadow();
	}

	
	//DESTORY_GAMEOBJECT_BY_COMPOENT(pEntity);  // 托管给 m_TextureGen 里绘制完毕后释放

	
	return ptex;


}

void CustomModelMgr::getCurEditActorTemplateInfo(bool &needSel, int &modeltype)
{
	needSel = m_CurEditActorModel.models.size() <= 0;
	modeltype = m_CurEditActorModel.type;
}

Rainbow::IActorBody* CustomModelMgr::getOrCreateCurEditActorBody(ACTOR_MODEL_TYPE type/* =DEFAUT_MODEL */)
{
	if (m_pCurEditActorBody && (m_CurEditActorModel.type == type && type != DEFAUT_MODEL))
		return m_pCurEditActorBody;

	if (m_pCurEditActorBody)
		SANDBOX_DELETE(m_pCurEditActorBody);

	if (type == DEFAUT_MODEL)
		type = m_CurEditActorModel.type;

	m_pCurEditActorBody = GetISandboxActorSubsystem()->CreateCustomModelActorBody(type);// ENG_NEW(ActorBody)(NULL);
	//m_pCurEditActorBody->initCustomActor(type);
	auto *entity = m_pCurEditActorBody->getEntity();
	if (!entity)
	{
		SANDBOX_DELETE(m_pCurEditActorBody);
		return NULL;
	}

	m_CurEditActorModel.type = type;
	if (!m_CurEditActorModel.skindisplay && m_pCurEditActorBody->getEntity()->GetMainModel())
		m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkins(false);

	auto iter = m_CurEditActorModel.models.begin();
	for (; iter != m_CurEditActorModel.models.end(); iter++)
	{
		std::string boneName = iter->first;

		if (iter->second.size() > 0)
		{
			std::string subModelName = boneName;
			transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);

			if (m_pCurEditActorBody->getEntity()->GetMainModel())
				m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkin(subModelName.c_str(), false);
		}

		for (size_t i = 0; i < iter->second.size(); i++)
		{
			auto *pSubModel = getAvatarModel(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
			if (!pSubModel)
				continue;

			pSubModel->SetPosition(Rainbow::Vector3f(iter->second[i].offset_x, iter->second[i].offset_y, iter->second[i].offset_z));
			pSubModel->SetRotation(iter->second[i].newrotatemode, iter->second[i].yaw, iter->second[i].pitch, iter->second[i].roll);
			pSubModel->SetScale(iter->second[i].scale3);
			entity->BindCunstomObject(boneName.c_str(), pSubModel);
			iter->second[i].model = pSubModel;
		}
	}

	return m_pCurEditActorBody;
}

Rainbow::IActorBody *CustomModelMgr::getActorBody()
{
	if (m_pCurEditActorBody)
		return m_pCurEditActorBody;

	return NULL;
}

bool CustomModelMgr::bindCustomAvatar(std::string bonename, std::string modelfilename)
{
	if (!m_pCurEditActorBody || !m_pCurEditActorBody->getEntity())
		return false;

	auto *pModel = getAvatarModel(modelfilename, PROJECTILE_ACTOR_MESH);
	if (!pModel)
		return false;

	m_pCurEditActorBody->getEntity()->BindCunstomObject(bonename.c_str(), pModel);

	if (m_pCurEditActorBody->getEntity()->GetMainModel())
	{
		std::string subModelName = bonename;
		transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);
		m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkin(subModelName.c_str(), false);
	}

	CustomAvatarModelData data;
	data.modelfilename = modelfilename;
	data.model = pModel;
	if (!modelfilename.empty() && isDownloadCM(modelfilename))
	{
		m_CurEditActorModel.usedownloadcmnum++;
		data.isdownload = true;
	}
		

	std::string keyname("");
	if (m_CurEditActorModel.type == HUMAN_MODEL)
		keyname = "human_" + bonename;
	else if (m_CurEditActorModel.type == QUADRUPED_MODEL)
		keyname = "quadruped_" + bonename;
	else if (m_CurEditActorModel.type == SINGLE_BONE_MODEL)
		keyname = "single_" + bonename;

	auto *cfg = GetWorldManagerPtr()->m_SurviveGameConfig->editactormodelcfg.getEditActorModelConfigInfo(keyname);
	if (cfg)
	{
		data.setModelScale(cfg->scale);
		data.setPosition(cfg->offset_y, cfg->offset_x, cfg->offset_z);
		data.setRotation(cfg->yaw, cfg->pitch, cfg->roll);
	}

	auto iter = m_CurEditActorModel.models.find(bonename);
	if (iter != m_CurEditActorModel.models.end())
	{
		m_CurEditActorModel.models[bonename].push_back(data);
	}
	else
	{
		std::vector<CustomAvatarModelData> datas;
		datas.push_back(data);

		m_CurEditActorModel.models[bonename] = datas;
	}

	return true;
}

bool CustomModelMgr::delCustomAvatar(std::string bonename, int index)
{
	if (!m_pCurEditActorBody || !m_pCurEditActorBody->getEntity())
		return false;

	auto iter = m_CurEditActorModel.models.find(bonename);
	if (iter == m_CurEditActorModel.models.end())
		return false;

	if (index < 0 && index >= (int)iter->second.size())
		return false;

	if(iter->second[index].isdownload || (!iter->second[index].modelfilename.empty() && isDownloadCM(iter->second[index].modelfilename)))
	{
		m_CurEditActorModel.usedownloadcmnum--;
	}
	m_pCurEditActorBody->getEntity()->UnbindObject(iter->second[index].model);
	DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second[index].model);
	iter->second.erase(iter->second.begin() + index);
	if (iter->second.size() <= 0 && m_CurEditActorModel.skindisplay && m_pCurEditActorBody->getEntity()->GetMainModel())
	{
		std::string subModelName = bonename;
		transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);
		m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkin(subModelName.c_str(), true);
	}

	return true;
}

bool CustomModelMgr::replaceCustomAvatar(std::string bonename, int index, std::string replacename)
{
	if (!m_pCurEditActorBody || !m_pCurEditActorBody->getEntity())
		return false;

	auto iter = m_CurEditActorModel.models.find(bonename);
	if (iter == m_CurEditActorModel.models.end())
		return false;

	auto *pModel = getAvatarModel(replacename, PROJECTILE_ACTOR_MESH);
	if (!pModel)
		return false;

	if (index < 0 && index >= (int)iter->second.size())
		return false;

	m_pCurEditActorBody->getEntity()->UnbindObject(iter->second[index].model);
	DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second[index].model);
	m_pCurEditActorBody->getEntity()->BindCunstomObject(bonename.c_str(), pModel);
	pModel->SetScale(iter->second[index].scale3);
	pModel->SetPosition(Rainbow::Vector3f(iter->second[index].offset_x, iter->second[index].offset_y, iter->second[index].offset_z));
	pModel->SetRotation(iter->second[index].newrotatemode, iter->second[index].yaw, iter->second[index].pitch, iter->second[index].roll);
	iter->second[index].modelfilename = replacename;
	iter->second[index].model = pModel;

	if (!replacename.empty() && isDownloadCM(replacename))
	{
		m_CurEditActorModel.usedownloadcmnum++;
		iter->second[index].isdownload = true;
	}
	else if(iter->second[index].isdownload)
	{
		m_CurEditActorModel.usedownloadcmnum--;
		iter->second[index].isdownload = false;
	}

	return true;
}
CustomActorModelData* CustomModelMgr::findCamd(std::string modelmark)
{
	vector<CustomActorModelData>* pvCamds = nullptr;
	vector<CustomActorModelData>& vCamds = *pvCamds;

	vCamds = m_MapCustomActorModels;
	for (size_t i = 0; i < vCamds.size(); i++)
	{
		if (vCamds[i].modelmark == modelmark)
		{
			return &vCamds[i];
		}
	}
	vCamds = m_ResCustomActorModels;
	for (size_t i = 0; i < vCamds.size(); i++)
	{
		if (vCamds[i].modelmark == modelmark)
		{
			return &vCamds[i];
		}
	}
	vCamds = m_PreviewResCustomActorModels;
	for (size_t i = 0; i < vCamds.size(); i++)
	{
		if (vCamds[i].modelmark == modelmark)
		{
			return &vCamds[i];
		}
	}
	return nullptr;
}

bool CustomModelMgr::loadEditableCustomActorModel(std::string modelmark)
{
	auto data = findCustomActorModelData(MAP_MODEL_CLASS, modelmark);
	if (!data)
		data = findCustomActorModelData(RES_MODEL_CLASS, modelmark);

	if (data)
	{
		if (ResourceCenter::GetInstancePtr()->findDownloadItemInfo(modelmark))
		{
			data->usedownloadcmnum = (short)data->models.size();
		}
		setCurEditActorModelData(*data);

		return true;
	}

	return false;
}

void CustomModelMgr::setCurEditActorSkinDisplay(bool isshow)
{
	if (!m_pCurEditActorBody)
		return;

	if (m_pCurEditActorBody->getEntity()->GetMainModel())
		m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkins(isshow);

	if (isshow && m_pCurEditActorBody->getEntity()->GetMainModel())
	{
		auto iter = m_CurEditActorModel.models.begin();
		for (; iter != m_CurEditActorModel.models.end(); iter++)
		{
			if (iter->second.size() <= 0)
				continue;

			std::string subModelName = iter->first;
			transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);
			m_pCurEditActorBody->getEntity()->GetMainModel()->ShowSkin(subModelName.c_str(), false);
		}
	}

	m_CurEditActorModel.skindisplay = isshow;
}


int CustomModelMgr::getCurEditActorModelType()
{
	return m_CurEditActorModel.type;
}

bool CustomModelMgr::getCurEditActorSkinDisplay()
{
	return m_CurEditActorModel.skindisplay;
}

bool CustomModelMgr::isSecondaryCreationByEditActor()
{
	return m_CurEditActorModel.isSecondaryCreation();
}

CustomAvatarModelData *CustomModelMgr::getCustomAvatarModelData(std::string bonename, int index)
{
	auto iter = m_CurEditActorModel.models.find(bonename);
	if (iter == m_CurEditActorModel.models.end())
		return NULL;

	if (index >= 0 && index < (int)iter->second.size())
		return &iter->second[index];

	return NULL;

}

CustomActorModelData *CustomModelMgr::findCustomActorModelData(int type, std::string modelmark)
{
	if (type == MAP_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_MapCustomActorModels.size(); i++)
		{
			if (m_MapCustomActorModels[i].modelmark == modelmark)
			{
				return &m_MapCustomActorModels[i];
			}
		}
	}
	else if (type == RES_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_ResCustomActorModels.size(); i++)
		{
			if (m_ResCustomActorModels[i].modelmark == modelmark)
			{
				return &m_ResCustomActorModels[i];
			}
		}
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_PreviewResCustomActorModels.size(); i++)
		{
			if (m_PreviewResCustomActorModels[i].modelmark == modelmark)
			{
				return &m_PreviewResCustomActorModels[i];
			}
		}
	}

	return NULL;
}

CustomActorModelData *CustomModelMgr::getCustomActorModelData(int type, int index)
{
	if (type == MAP_MODEL_CLASS)
	{
		if (index < 0 || index >= (int)m_MapCustomActorModels.size())
			return NULL;

		return &m_MapCustomActorModels[index];
	}
	else if (type == RES_MODEL_CLASS)
	{
		if (index < 0 || index >= (int)m_ResCustomActorModels.size())
			return NULL;

		return &m_ResCustomActorModels[index];
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		if (index < 0 || index >= (int)m_PreviewResCustomActorModels.size())
			return NULL;

		return &m_PreviewResCustomActorModels[index];
	}

	return NULL;
}

int CustomModelMgr::getCustomActorSubModelNum(CustomActorModelData* pcustomactordata)
{
	if (!pcustomactordata)
	{
		return 0;
	}
	return pcustomactordata->models.size();
}

int CustomModelMgr::getCustomActorSubModelCostomModelNum(CustomActorModelData* pcustomactordata, int index)
{
	if (pcustomactordata && index >= 0 && index < (int)pcustomactordata->models.size())
	{
		std::map<std::string, std::vector<CustomAvatarModelData>>::iterator iter = pcustomactordata->models.begin();
		for (int i = 0; iter != pcustomactordata->models.end(); ++iter, ++i)
		{
			if (i == index)
			{
				return iter->second.size();
			}
		}
	}
	return 0;
}

std::string CustomModelMgr::getCustomActorSubModelCostomModelNameByIndex(CustomActorModelData* pcustomactordata,int index,int modelIndex)
{
	std::string filename = "";
	if (pcustomactordata && index >= 0 && index < (int)pcustomactordata->models.size())
	{
		std::map<std::string, std::vector<CustomAvatarModelData>>::iterator iter = pcustomactordata->models.begin();
		for (int i = 0; iter != pcustomactordata->models.end(); ++iter,++i)
		{
			if (i == index && (modelIndex >= 0 && modelIndex < (int)iter->second.size()))
			{
				return iter->second[modelIndex].modelfilename;
			}
		}
	}
	return filename;
}

int CustomModelMgr::getCustomActorModelNum(int type)
{
	if (type == MAP_MODEL_CLASS)
		return (int)m_MapCustomActorModels.size();
	else if (type == RES_MODEL_CLASS)
		return (int)m_ResCustomActorModels.size();
	else if (type == PREVIEW_MODEL_CLASS)
		return (int)m_PreviewResCustomActorModels.size();

	return 0;
}

void CustomModelMgr::setCurEditActorOverlayColor(std::string bonename, bool b)
{
	if (m_pCurEditActorBody && m_pCurEditActorBody->getEntity()->GetMainModel())
	{
		std::string subModelName = bonename;
		transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);

		if (b)
		{
			Rainbow::ColourValue color(0, 0, 1.0f);
			m_pCurEditActorBody->getEntity()->GetMainModel()->SetSubMeshOverlayColor(subModelName.c_str(), &color);
		}
		else
			m_pCurEditActorBody->getEntity()->GetMainModel()->SetSubMeshOverlayColor(subModelName.c_str(), NULL);
	}
}

Rainbow::Entity *CustomModelMgr::getActorEntity(std::string markname, bool bUpdateWhenCustomModelReady/* = false*/)
{
	auto pEntity = Rainbow::Entity::Create();
	Rainbow::Model *pModel = NULL;

	//bool needWaitSync = (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode()) ? true : false;
	bool needWaitSync = (GetIPlayerControl() && GetIPlayerControl()->getIWorld()) ? true : false;

	CustomModelWaitSyncListMgr::TCustomAvatarModelDataMap* datalist = nullptr;
	if (bUpdateWhenCustomModelReady && needWaitSync) {
		if (GetCustomModelWaitSyncListMgrPtr()) {
			datalist = GetCustomModelWaitSyncListMgrPtr()->createEntityWaitMap(pEntity, false);
		}
	}

	auto *data = findCustomActorModelData(MAP_MODEL_CLASS, markname);
	if (!data)
		return NULL;

	if (data->type == HUMAN_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body1.omod");
	else if (data->type == QUADRUPED_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body2.omod");
	else if (data->type == SINGLE_BONE_MODEL)
		pModel = g_BlockMtlMgr.getModel("entity/custommodel/body3.omod");

	if (pModel)
	{
		pEntity->Load(pModel);
		pModel->SetName(markname.c_str());
		if (!data->skindisplay)
			pEntity->GetMainModel()->ShowSkins(false);

		auto iter = data->models.begin();
		for (; iter != data->models.end(); iter++)
		{
			std::string boneName = iter->first;

			if (iter->second.size() > 0)
			{
				std::string subModelName = boneName;
				transform(subModelName.begin(), subModelName.end(), subModelName.begin(), ::tolower);

				pEntity->GetMainModel()->ShowSkin(subModelName.c_str(), false);
			}

			std::vector<CustomAvatarModelData> avatarModelDatas;
			avatarModelDatas.clear();
			for (size_t i = 0; i < iter->second.size(); i++)
			{
				Rainbow::Model* pSubModel = nullptr;
				// 上层有传 datalist 才表示上层有能力处理异步加载的情况
				if (!datalist || CustomModelMgr::GetInstancePtr()->isAvatarCMItemModelInCache(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH))
				{
					pSubModel = getAvatarModel(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
				}
				else
				{
					// 异步加载
					CustomModelMgr::GetInstancePtr()->loadAvatarCMItemModelToCache(iter->second[i].modelfilename, PROJECTILE_ACTOR_MESH);
				}

				if (!pSubModel)
				{
					if (datalist && !iter->second[i].modelfilename.empty())
						avatarModelDatas.push_back(iter->second[i]);
					continue;
				}

				pSubModel->SetPosition(Rainbow::Vector3f(iter->second[i].offset_x, iter->second[i].offset_y, iter->second[i].offset_z));
				pSubModel->SetRotation(iter->second[i].newrotatemode, iter->second[i].yaw, iter->second[i].pitch, iter->second[i].roll);
				pSubModel->SetScale(iter->second[i].scale3);
				pEntity->BindCunstomObject(boneName.c_str(), pSubModel);
			}

			if (datalist && avatarModelDatas.size() > 0)
			{
				(*datalist)[boneName] = avatarModelDatas;
			}
		}
		if (data->models.size() && (!datalist || datalist->size() == 0))
		{
			GenCustomModelManager::GetInstance().bindCusntomObjs(pEntity);
		}
	}

	pEntity->SetScale(0.5);
	return pEntity;
}

void CustomModelMgr::setCurEditActorModelData(CustomActorModelData &data)
{
	m_CurEditActorModel = data;

	m_CurEditActorModel.usedownloadcmnum = 0;
	auto iter = m_CurEditActorModel.models.begin();
	for (; iter != m_CurEditActorModel.models.end(); iter++)
	{
		for (size_t i = 0; i < iter->second.size(); i++)
		{
			std::string modelname = iter->second[i].modelfilename;
			if (!modelname.empty() && isDownloadCM(modelname))
			{
				iter->second[i].isdownload = true;
				m_CurEditActorModel.usedownloadcmnum++;
			}
		}
	}

}

void CustomModelMgr::closeEditActorModel(IClientPlayer *player, int operatetype, std::string modelname/* ="" */, WCoord containerpos/* =WCoord(0, -1, 0) */)
{
	if (!player)
		return;

	if (player->GetPlayerWorld()->isRemoteMode())
	{
		PB_CloseEditActorModelCH closeEditActorModelCH;
		if (operatetype != -1)
		{
			for (auto it = m_CurEditActorModel.models.begin(); it != m_CurEditActorModel.models.end(); it++)
			{
				PB_ActorOneBoneModelData *oneBoneModel = closeEditActorModelCH.mutable_bonemodels()->Add();//.mutable_onerecord()->Add();
				oneBoneModel->set_bonename(it->first);

				for (size_t i = 0; i < it->second.size(); i++)
				{
					CustomAvatarModelData& camd = it->second[i];
					PB_ActorOneAvatarModelData* pPbAoamd = oneBoneModel->mutable_avatarmodels()->Add();
					CamdToPb(camd, pPbAoamd);
				}
			}
			closeEditActorModelCH.set_modeltype(m_CurEditActorModel.type);
			closeEditActorModelCH.set_modelname(modelname);
			closeEditActorModelCH.set_skindisplay(m_CurEditActorModel.skindisplay);
		}

		closeEditActorModelCH.set_operatetype(operatetype);
		GetGameNetManagerPtr()->sendToHost(PB_CLOSE_EDIT_ACTORMODEL_CH, closeEditActorModelCH);
	}
	else
	{
		WorldContainer *container = player->GetPlayerWorld()->getContainerMgr()->getContainerExt(containerpos);
		if (operatetype == -1) //普通的关闭
		{
			if (container)
			{
				container->removeOpenUIN(player->getUin());
			}
		}
		else
		{
			PB_CloseEditActorModelHC closeEditActorModelHC;

			for (auto it = m_CurEditActorModel.models.begin(); it != m_CurEditActorModel.models.end(); it++)
			{
				PB_ActorOneBoneModelData *oneBoneModel = closeEditActorModelHC.mutable_bonemodels()->Add();//.mutable_onerecord()->Add();
				oneBoneModel->set_bonename(it->first);

				for (size_t i = 0; i < it->second.size(); i++)
				{
					CustomAvatarModelData& camd = it->second[i];
					PB_ActorOneAvatarModelData* pPbAoamd = oneBoneModel->mutable_avatarmodels()->Add();
					CamdToPb(camd, pPbAoamd);
				}
			}
			closeEditActorModelHC.set_mapid(player->iGetCurMapID());

			if (container)
			{
				container->updateCustomActorModelData(m_CurEditActorModel);
				container->removeOpenUIN(player->getUin());
			}

			PB_Vector3* containerPos = closeEditActorModelHC.mutable_containerpos();
			containerPos->set_x(containerpos.x);
			containerPos->set_y(containerpos.y);
			containerPos->set_z(containerpos.z);
			closeEditActorModelHC.set_modeltype(m_CurEditActorModel.type);

			if (operatetype == 1) //保存模型
			{
				std::string modelMark = createAndSaveActor(player, containerpos, m_CurEditActorModel, modelname);
				if (!modelMark.empty())
				{
					closeEditActorModelHC.set_modelmark(modelMark);
				}
			}

			closeEditActorModelHC.set_skindisplay(m_CurEditActorModel.skindisplay);

			GetGameNetManagerPtr()->sendBroadCast(PB_CLOSE_EDIT_ACTORMODEL_HC, closeEditActorModelHC, 0);
		}
	}

	auto iter = m_CurEditActorModel.models.begin();
	for (; iter != m_CurEditActorModel.models.end(); iter++)
	{
		std::string boneName = iter->first;
		for (size_t i = 0; i < iter->second.size(); i++)
		{
			//不知道要不要释放
			if (iter->second[i].model)
				DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second[i].model);
		}
	}
	m_CurEditActorModel.models.clear();

	if (m_pCurEditActorBody)
		SANDBOX_DELETE(m_pCurEditActorBody);
}

void CustomModelMgr::loadCustomActor(int type, std::string filename)
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char dir[256];
	char path[256];
	if (type == MAP_MODEL_CLASS)
	{
		sprintf(path, "%s/w%lld/custommodel/actormodelmf", rootpath.c_str(), m_CurOWID);
		sprintf(dir, "%s/w%lld/custommodel/actormodelmf/", rootpath.c_str(), m_CurOWID);
	}
	else if (type == PREVIEW_MODEL_CLASS) {
		sprintf(path, "data/custommodel_pre/actormodelmf");
		sprintf(dir, "data/custommodel_pre/actormodelmf/");
	}
	else
	{
		sprintf(path, "data/custommodel/actormodelmf");
		sprintf(dir, "data/custommodel/actormodelmf/");
	}

	if (!Rainbow::GetFileManager().IsFileExistWritePath(dir))
	{
		Rainbow::GetFileManager().CreateWritePathDir(dir);
	}
	std::string rootPath = path;
	sprintf(path, "%s/%s.mf", rootPath.c_str(), filename.c_str());

	int buflen = 0;
	void* buf = ReadWholeFile(path, buflen);
	if (buf == NULL)
		return;

	flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
	if (!FBSave::VerifyCustomActorModelDataBuffer(verifier))
	{
		free(buf);
		return;
	}

	const FBSave::CustomActorModelData* actormodel = FBSave::GetCustomActorModelData(buf);
	if (actormodel == NULL)
	{
		free(buf);
		return;
	}

	auto fitem = findCustomActorModelData(type, actormodel->modelmark()->c_str());
	if (fitem)
	{
		free(buf);
		return;
	}
	
	CustomActorModelData actorModelData;
	actorModelData.models.clear();

	if (actormodel->authuin())
		actorModelData.authuin = actormodel->authuin();
	typedef std::map<std::string, bool> downloadType;
	std::map<std::string, bool> *downloadSubCMs = ENG_NEW(downloadType);
	for (size_t i = 0; i < actormodel->bonemodels()->size(); i++)
	{
		std::vector<CustomAvatarModelData> vCamds;
		vCamds.clear();

		auto* fbsBmd = actormodel->bonemodels()->Get(i);
		for (size_t j = 0; j < fbsBmd->avatarmodels()->size(); j++)
		{
			auto* fbsAmd = fbsBmd->avatarmodels()->Get(j);
			CustomAvatarModelData camd;
			CamdFromFbs(camd, fbsAmd);

			vCamds.push_back(camd);

			if (downloadSubCMs && !camd.modelfilename.empty())
				(*downloadSubCMs)[camd.modelfilename] = true;
		}

		actorModelData.models[fbsBmd->bonename()->c_str()] = vCamds;
	}

	actorModelData.modelmark = actormodel->modelmark()->c_str();
	if (actormodel->modelname())
	{
		string modelName = actormodel->modelname()->c_str();
		std::string nameKey(filename);
		nameKey.append("_name");
		if (!modelName.empty())
		{
			//生物微缩模型  TRANSLATE_TYPE
			modelName = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)3, nameKey, modelName);
		}
		actorModelData.modelname = modelName;
	}

	actorModelData.type = (ACTOR_MODEL_TYPE)actormodel->type();

	actorModelData.skindisplay = actormodel->skindisplay();
	actorModelData.usedownloadcmnum = actormodel->downloadcmnum();

	auto t = actormodel->time();

	if (type == MAP_MODEL_CLASS)
	{
		m_MapCustomActorModels.push_back(actorModelData);
		auto* pCustomItem = getCustomItem(actorModelData.modelmark);
		if (pCustomItem && pCustomItem->itemid > 0)
		{
			GetDefManagerProxy()->addDefByCustomModel(pCustomItem->itemid, ACTOR_MODEL, actorModelData.modelmark, actorModelData.modelname, "", Rainbow::Vector3f(0, 0, 0), pCustomItem->involvedid);
		}
	}
	else if (type == PREVIEW_MODEL_CLASS) {
		m_PreviewResCustomActorModels.push_back(actorModelData);
	}
	else
		m_ResCustomActorModels.push_back(actorModelData);

	if (downloadSubCMs)
	{
		loadDownloadSubCMs(type, downloadSubCMs);
		ENG_DELETE(downloadSubCMs);
	}
	
	free(buf);
}

void CustomModelMgr::loadCustomActor(int type, int realowneruin/* =0 */)
{
	if (realowneruin == 0)
		realowneruin = GetClientInfoProxy()->getUin();

	char dir[256];
	char path[256];

	if (type == MAP_MODEL_CLASS)
	{
		m_MapCustomActorModels.clear();
		std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);


		sprintf(path, "%s/w%lld/custommodel/actormodelmf", rootpath.c_str(), m_CurOWID);
		sprintf(dir, "%s/w%lld/custommodel/actormodelmf/", rootpath.c_str(), m_CurOWID);
	}
	else
	{
		m_ResCustomActorModels.clear();
		sprintf(path, "data/custommodel/actormodelmf");
		sprintf(dir, "data/custommodel/actormodelmf/");
	}

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	std::string rootPath = path;
	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		sprintf(path, "%s/%s.mf", rootPath.c_str(), filename.c_str());

		int buflen = 0;
		void *buf = ReadWholeFile(path, buflen);
		if (buf == NULL)
			continue;

		flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
		if (!FBSave::VerifyCustomActorModelDataBuffer(verifier))
		{
			free(buf);
			continue;
		}

		const FBSave::CustomActorModelData *actormodel = FBSave::GetCustomActorModelData(buf);
		if (actormodel == NULL)
		{
			free(buf);
			continue;
		}

		int authUin = 0;
		if (actormodel->authuin())
			authUin = actormodel->authuin();

		auto resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename);
		if (!resInfo)
		{
			
			if (authUin == 0 || (realowneruin != authUin  && (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), authUin) == false )))
			{
				free(buf);
				removeInvalidCustomItemData(filename);
				continue;
			}

		}
		else 
		{
			//不是地图作者下载的资源，不允许加载
			if (realowneruin != resInfo->download_uin && (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false))
			{
				free(buf);
				continue;
			}
		}


		CustomActorModelData actorModelData;
		actorModelData.models.clear();
		actorModelData.authuin = authUin;

		std::map<std::string, bool> *downloadSubCMs = NULL;
		if (resInfo && (resInfo->download_uin == realowneruin  || (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin)))  )
		{
			typedef std::map<std::string, bool> downloadType;
			downloadSubCMs = ENG_NEW(downloadType);
		}
		for (size_t i = 0; i < actormodel->bonemodels()->size(); i++)
		{
			std::vector<CustomAvatarModelData> vCamds;
			vCamds.clear();

			auto* fbsBmd = actormodel->bonemodels()->Get(i);
			for (size_t j = 0; j < fbsBmd->avatarmodels()->size(); j++)
			{
				auto* fbsAmd = fbsBmd->avatarmodels()->Get(j);
				CustomAvatarModelData camd;
				CamdFromFbs(camd, fbsAmd);
				if (camd.modelfilename != "") // 微缩生物的子模型需要跳过加载 原因是下载的子模型不会纳入下载列表记录中
				{
					CustomModel* submodel =  this->getCustomModel(type,camd.modelfilename);
					if (!submodel && m_CurOWID > 0)
					{
						this->loadOneModCustomRes(camd.modelfilename,realowneruin, "", false, true);
					}
				}
				vCamds.push_back(camd);
			}

			actorModelData.models[fbsBmd->bonename()->c_str()] = vCamds;
		}

		actorModelData.modelmark = actormodel->modelmark()->c_str();
		if (actormodel->modelname())
		{
			string modelName = actormodel->modelname()->c_str();
			std::string nameKey(filename);
			nameKey.append("_name");
			if (!modelName.empty())
			{
				//生物微缩模型  TRANSLATE_TYPE
				modelName = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)3, nameKey, modelName);
			}
			actorModelData.modelname = modelName;
		}

		actorModelData.type = (ACTOR_MODEL_TYPE)actormodel->type();

		actorModelData.skindisplay = actormodel->skindisplay();
		actorModelData.usedownloadcmnum = actormodel->downloadcmnum();

		auto t = actormodel->time();

		if (type == MAP_MODEL_CLASS)
		{
			m_MapCustomActorModels.push_back(actorModelData);
			auto *pCustomItem = getCustomItem(actorModelData.modelmark);
			if (pCustomItem && pCustomItem->itemid > 0)
			{
				GetDefManagerProxy()->addDefByCustomModel(pCustomItem->itemid, ACTOR_MODEL, actorModelData.modelmark, actorModelData.modelname, "", Rainbow::Vector3f(0, 0, 0), pCustomItem->involvedid);
			}
		}
		else
			m_ResCustomActorModels.push_back(actorModelData);


		if (downloadSubCMs)
		{
			if (downloadSubCMs->size() > 0)
			{
				loadDownloadSubCMs(type, downloadSubCMs);
			}
			ENG_DELETE(downloadSubCMs)
		}

		free(buf);
	}
}

void CustomModelMgr::loadDownloadSubCMs(int type, std::map<std::string, bool> *subcustommodels)
{
	auto iter = subcustommodels->begin();
	for (; iter != subcustommodels->end(); iter++)
	{
		//TODO 检查subcustommodels[i]是否load过
		if (!CustomModelMgr::GetInstancePtr()->getCustomModel(type, iter->first))
		{
			if (type == MAP_MODEL_CLASS)
			{
				CustomModelMgr::GetInstancePtr()->loadOneMapCustomModel(-1, iter->first, "default", 0, -1, true, true);
			}
			else if (type == PREVIEW_MODEL_CLASS) {
				CustomModelMgr::GetInstancePtr()->loadOneResCustomModel(PREVIEW_MODEL_CLASS, iter->first, true);
			}
			else
			{
				CustomModelMgr::GetInstancePtr()->loadOneResCustomModel(RES_MODEL_CLASS, iter->first, true);
			}
		}
	}
}

void CustomModelMgr::loadOneModCustomActorRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod/* =false */)
{
	if (findCustomActorModelData(MAP_MODEL_CLASS, modelkey))
		return;

	char path[256];
	sprintf(path, "%s/resource/custommodel/actormodelmf/%s.mf", modroot.c_str(), modelkey.c_str());

	int buflen = 0;
	void *buf = ReadWholeFile(path, buflen);
	if (buf == NULL)
		return;

	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	if (!FBSave::VerifyCustomActorModelDataBuffer(verifier))
	{
		free(buf);
		return;
	}

	const FBSave::CustomActorModelData *actormodel = FBSave::GetCustomActorModelData(buf);
	if (actormodel == NULL)
	{
		free(buf);
		return;
	}

	int authUin = 0;
	auto resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(modelkey);
	if (!resInfo)
	{
		if (actormodel->authuin())
			authUin = actormodel->authuin();

		if (authUin == 0 || (realowneruin != authUin && (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), authUin) == false)))
		{
			free(buf);
			return;
		}
	}
	else
	{
		//不是地图作者下载的资源，不允许加载
		if (realowneruin != resInfo->download_uin && (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false))
		{
			free(buf);
			return;
		}
	}

	CustomActorModelData actorModelData;
	actorModelData.models.clear();
	actorModelData.authuin = authUin;

	for (size_t i = 0; i < actormodel->bonemodels()->size(); i++)
	{
		std::vector<CustomAvatarModelData> vCamds;
		vCamds.clear();

		auto* fbsBmd = actormodel->bonemodels()->Get(i);
		for (size_t j = 0; j < fbsBmd->avatarmodels()->size(); j++)
		{
			auto* fbsAmd = fbsBmd->avatarmodels()->Get(j);
			CustomAvatarModelData camd;
			CamdFromFbs(camd, fbsAmd);

			vCamds.push_back(camd);

			loadOneModCustomRes(camd.modelfilename, realowneruin, modroot, islibmod);
		}

		actorModelData.models[fbsBmd->bonename()->c_str()] = vCamds;
	}

	actorModelData.modelmark = actormodel->modelmark()->c_str();
	if (actormodel->modelname())
		actorModelData.modelname = actormodel->modelname()->c_str();

	actorModelData.type = (ACTOR_MODEL_TYPE)actormodel->type();

	actorModelData.skindisplay = actormodel->skindisplay();
	actorModelData.usedownloadcmnum = actormodel->downloadcmnum();

	auto t = actormodel->time();

	if(islibmod)
		m_ResCustomActorModels.push_back(actorModelData);
	else
		m_MapCustomActorModels.push_back(actorModelData);

	free(buf);
}

bool CustomModelMgr::saveCustomActor(int type, CustomActorModelData &data)
{
	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char dir[256];
	if (type == MAP_MODEL_CLASS)
	{
		if (m_CurOWID < 0)
			return false;

		sprintf(dir, "%s/w%lld/custommodel/actormodelmf/", rootpath.c_str(), m_CurOWID);
	}
	else
	{
		sprintf(dir, "data/custommodel/actormodelmf/");
	}

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	if (type == MAP_MODEL_CLASS)
	{
		sprintf(path, "%s/w%lld/custommodel/actormodelmf/%s.mf", rootpath.c_str(), m_CurOWID, data.modelmark.c_str());
	}
	else
	{
		sprintf(path, "data/custommodel/actormodelmf/%s.mf", data.modelmark.c_str());
	}

	flatbuffers::FlatBufferBuilder builder;
	std::vector<flatbuffers::Offset<FBSave::CustomActorModelData>> actorModelDatas;

	std::vector<CustomActorModelData> *srcModels = NULL;
	if (type == MAP_MODEL_CLASS)
		srcModels = &m_MapCustomActorModels;
	else
		srcModels = &m_ResCustomActorModels;


	auto &srcModel = data;

	std::vector<flatbuffers::Offset<FBSave::BoneModelData>> boneModelDatas;
	for (auto boneIter = srcModel.models.begin(); boneIter != srcModel.models.end(); boneIter++)
	{
		std::vector<flatbuffers::Offset<FBSave::AvatarModelData>> avatarModelDatas;
		for (size_t i = 0; i < boneIter->second.size(); i++)
		{
			auto &src = boneIter->second[i];
			auto offsetPos = WCoordToCoord3(WCoord(src.offset_x, src.offset_y, src.offset_z));
			avatarModelDatas.push_back(FBSave::CreateAvatarModelData(builder, builder.CreateString(src.modelfilename), src.scale, src.yaw, src.pitch, &offsetPos, src.roll, src.newrotatemode));
		}


		boneModelDatas.push_back(FBSave::CreateBoneModelData(builder, builder.CreateString(boneIter->first), builder.CreateVector(avatarModelDatas)));
	}


	unsigned long t = (unsigned long)time(NULL);
	auto cm = FBSave::CreateCustomActorModelData(builder, builder.CreateVector(boneModelDatas), builder.CreateString(data.modelmark), data.type, builder.CreateString(data.modelname), data.authuin, srcModel.skindisplay, t, srcModel.usedownloadcmnum);

	builder.Finish(cm);
	return GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize());
}

void CustomModelMgr::syncCustomActorModelData(int uin, int index)
{
	size_t i = index;
	PB_CustomActorModelDataHC customActorModelDataHC;
	for (; i < m_MapCustomActorModels.size(); i++)
	{
		if (i == index + 30)
			break;

		PB_OneCustomActorModelDataHC *oneModel = customActorModelDataHC.mutable_modeldatas()->Add();

		auto iter = m_MapCustomActorModels[i].models.begin();
		for (; iter != m_MapCustomActorModels[i].models.end(); iter++)
		{
			PB_ActorOneBoneModelData* pbAobmd = oneModel->mutable_bonemodels()->Add();
			for (size_t j = 0; j < iter->second.size(); j++)
			{
				PB_ActorOneAvatarModelData* pbAoamd = pbAobmd->mutable_avatarmodels()->Add();
				CustomAvatarModelData& camd = iter->second[j];
				CamdToPb(camd, pbAoamd);
			}

			pbAobmd->set_bonename(iter->first);
		}

		oneModel->set_modelmark(m_MapCustomActorModels[i].modelmark);
		oneModel->set_type(m_MapCustomActorModels[i].type);
		oneModel->set_modelname(m_MapCustomActorModels[i].modelname);
		oneModel->set_skindisplay(m_MapCustomActorModels[i].skindisplay);
		oneModel->set_authuin(m_MapCustomActorModels[i].authuin);
	}

	if (uin == 0)
		GetGameNetManagerPtr()->sendBroadCast(PB_CUSTOMACTOR_MODELDATA_HC, customActorModelDataHC, 0);
	else
		GetGameNetManagerPtr()->sendToClient(uin, PB_CUSTOMACTOR_MODELDATA_HC, customActorModelDataHC);

	if (i < m_MapCustomActorModels.size())
	{
		syncCustomActorModelData(uin, i);
	}
}

void CustomModelMgr::addCustomActorBySync(CustomActorModelData data)
{
	m_MapCustomActorModels.push_back(data);
	//addOneModelToClass(data.modelmark, false);
}

bool CustomModelMgr::reEncryptCustomActor(long long owid, int olduin, int newuin)
{
	char dir[256];
	sprintf(dir, "data/w%lld/custommodel/actormodelmf/", owid);
	if (!gFunc_isStdioDirExist(dir)) { return true; }

	char path[256];
	sprintf(path, "data/w%lld/custommodel/actormodelmf", owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string fullname = std::string(path) + "/" + *it + ".mf";

		int buflen = 0;
		void* buf = ReadWholeFile(fullname.c_str(), buflen);
		if (buf == NULL)
			continue;

		flatbuffers::Verifier verifier((const uint8_t*)buf, buflen);
		if (!FBSave::VerifyCustomActorModelDataBuffer(verifier))
		{
			free(buf);
			continue;
		}

		const FBSave::CustomActorModelData* actormodel = FBSave::GetCustomActorModelData(buf);
		if (!actormodel || actormodel->authuin() != olduin || actormodel->authuin() == newuin)
		{
			free(buf);
			continue;
		}

		flatbuffers::FlatBufferBuilder builder;
		unsigned long t = (unsigned long)time(NULL);
		std::vector<flatbuffers::Offset<FBSave::BoneModelData>> boneModelDatas;
		boneModelDatas.clear();

		auto bonemodels = actormodel->bonemodels();
		if (bonemodels)
		{
			for (unsigned int i = 0; i < bonemodels->size(); i++)
			{
				auto bones = bonemodels->Get(i);
				if (!bones) { continue; }

				auto fbsvFbsAmds = bones->avatarmodels();
				std::vector<flatbuffers::Offset<FBSave::AvatarModelData>> vFbsAmds;
				vFbsAmds.clear();

				if (fbsvFbsAmds)
				{
					for (unsigned int j = 0; j < fbsvFbsAmds->size(); j++)
					{
						auto fbsAmd = fbsvFbsAmds->Get(j);
						if (!fbsAmd)
						{
							continue;
						}
						vFbsAmds.push_back(FBSave::CreateAvatarModelData(builder,
							builder.CreateString(fbsAmd->modelfilename()), fbsAmd->scale(), fbsAmd->yaw(), fbsAmd->pitch(),
							fbsAmd->offsetpos(), fbsAmd->roll(), fbsAmd->newrotatemode(), fbsAmd->scale3()
						));
					}
				}

				boneModelDatas.push_back(FBSave::CreateBoneModelData(builder, builder.CreateString(bones->bonename()), fbsvFbsAmds ? builder.CreateVector(vFbsAmds) : NULL));
			}
		}

		auto cm = FBSave::CreateCustomActorModelData(builder, builder.CreateVector(boneModelDatas), builder.CreateString(actormodel->modelmark()), actormodel->type(),
			builder.CreateString(actormodel->modelname()), newuin, actormodel->skindisplay(), t, actormodel->downloadcmnum());

		builder.Finish(cm);
		free(buf);
		if (!GetFileManager().SaveToWritePath(fullname.c_str(), builder.GetBufferPointer(), builder.GetSize())) { return false; }
	}

	return true;
}

bool CustomModelMgr::moveActorModelRes(int destlib, CustomActorModelData *data, ResourceFolderSetInfo *destclass)
{
	if (!data || !destclass  || !ResourceCenter::GetInstancePtr() || m_CurOWID < 0)
		return false;

	auto actorModeldata = findCustomActorModelData(destlib, data->modelmark);
	if (!actorModeldata)
	{
		bool ignorecheck = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(data->modelmark) != NULL;  //下载的生物微缩，其子部件加载忽略检测
		char srcPath[128] = { 0 };
		char destPath[128] = { 0 };

		auto iter = data->models.begin();
		for (; iter != data->models.end(); iter++)
		{
			for (size_t i = 0; i < iter->second.size(); i++)
			{
				auto custoModel = getCustomModel(destlib, iter->second[i].modelfilename);
				if (custoModel)
					continue;

				//子模型的资源文件拷贝到相对应的资源库里
				if (destlib == MAP_LIB)
				{
					sprintf(srcPath, "data/custommodel/%s.cm", iter->second[i].modelfilename.c_str());
					sprintf(destPath, "data/w%lld/custommodel/%s.cm", m_CurOWID, iter->second[i].modelfilename.c_str());
				}
				else if (destlib == PUBLIC_LIB)
				{
					sprintf(srcPath, "data/w%lld/custommodel/%s.cm", m_CurOWID, iter->second[i].modelfilename.c_str());
					sprintf(destPath, "data/custommodel/%s.cm", iter->second[i].modelfilename.c_str());
				}

				if (GetFileManager().IsFileExistWritePath(destPath))
					 continue;

				if (!GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath))
					continue;

				//子模型加载到相对应的资源库里
				loadOneCustomModelNew(destlib, iter->second[i].modelfilename, ignorecheck);
			}
		}

		if (destlib == MAP_LIB)
		{
			m_MapCustomActorModels.push_back(*data);
			syncCustomActorModelData(0, m_MapCustomActorModels.size() - 1);    //同步给客机
		}
		else
			m_ResCustomActorModels.push_back(*data);

		saveCustomActor(destlib, *data);
	}

	if (destlib == MAP_LIB)
	{
		bool needAddDef = true;
		if (actorModeldata)   //地图库里已经有这个模型数据
		{
			/**************************************************************************************************************/
			//	这里处理一下地图资源删除的状态，原因是地图资源删除时因为地图本身很多地方已经用了这个资源，所以并不会立即删除，
			//	而是放到一个列表里和记录一个删除状态，等退出地图的时候才删除	
			actorModeldata->leaveworlddel = false;			//删除状态置否
			auto customItem = getCustomItem(data->modelmark, true);
			if (customItem)   //地图里有这个生物蛋道具
			{
				//检测一下 如果已经在删除列表里，从列表清除
				checkRemoveWaitDelCustomItem(customItem->itemid);
				needAddDef = false;
			}
			/**************************************************************************************************************/
		}

		//增加monsterdef和生物蛋的itemdef,以便于在地图内使用，(ps后续考虑将这块的逻辑挪到拿资源到快捷栏时再添加 而不是资源移动到地图库就添加)
		if (needAddDef)
		{
			int id = getFreeId(ACTOR_MODEL);
			int involvedId = getFreeId(WEAPON_MODEL);
			if ((id <= 0 || involvedId <= 0))	//没有可分配的id了
			{
				if(GetIPlayerControl())
					GetIPlayerControl()->iNotifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 3729);
				return false;
			}
			
			addCustomItemData(id, data->modelmark, destclass->classname, ACTOR_MODEL, involvedId, destclass->classindex);
			GetDefManagerProxy()->addDefByCustomModel(id, ACTOR_MODEL, data->modelmark, data->modelname, "", Rainbow::Vector3f(0, 0, 0), involvedId);

			//文字安全检测上报
			GetWorldStringManagerProxy()->insert(data->modelmark, data->modelname, SAVEFILETYPE::ACTOR_CUSTOM_MODEL);
			//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
			//	.SetData_Number("type", 5)
			//	.SetData_String("content", data->modelname)
			//	.SetData_String("key", data->modelmark));
		}
	}

	return true;
}

bool CustomModelMgr::removeActorResByResourceCenter(int libtype, std::string filename)
{
	if (libtype == MAP_LIB)
	{
		auto iter = m_MapCustomActorModels.begin();
		for (; iter != m_MapCustomActorModels.end(); iter++)
		{
			if (iter->modelmark == filename)
			{
				iter->leaveworlddel = true;
				addWaitDelCustomItem(filename);
				return true;
			}
		}
	}
	else if (libtype == PUBLIC_LIB)
	{
		auto iter = m_ResCustomActorModels.begin();
		for (; iter != m_ResCustomActorModels.end(); iter++)
		{
			if (iter->modelmark == filename)
			{
				char path[128] = {0};

				sprintf(path, "data/custommodel/actormodelmf/%s.mf", iter->modelmark.c_str());
				if (GetFileManager().IsFileExistWritePath(path))
				{
					GetFileManager().DeleteWritePathFileOrDir(path);
				}

				m_ResCustomActorModels.erase(iter);
				return true;
			}
		}
	}
	return false;
}