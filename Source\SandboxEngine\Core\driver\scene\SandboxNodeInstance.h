#pragma once
/**
* file : SandboxNodeIntance
* func : 节点实例
* by : chenzh
*/
#include "base/SandboxAutoRef.h"
#include "scene/SandboxSceneObject.h"
#include "event/notify/SandboxTCallback.h"
#include <type_traits>


namespace MNSandbox {

	class SandboxNode;

	struct EXPORT_SANDBOXDRIVERMODULE NodeInstanceReflex
	{
		NodeInstanceReflex() {}
		NodeInstanceReflex(AutoRef<SandboxNode> node, SandboxNodeID id) : _node(node), _id(id) {}
		AutoRef<SandboxNode> _node;
		SandboxNodeID _id=0;

		SandboxNodeID Getid() const { return _id; }

		inline bool operator==(const NodeInstanceReflex& v) const { return _id == v._id; }

		// id 加载node 注册监听和注销监听
		static void RegNodeReady(SandboxNodeID id, TCallback<SandboxNodeID, SandboxNode*>* cb);
		static void UnregNodeReady(SandboxNodeID id, TCallback<SandboxNodeID, SandboxNode*>* cb);
		AutoRef<SandboxNode> TryGetReadyNode() const;
	};

	// node instance
	// 必须继承自 SandboxNode ,如果想前置申明，可以把 Base 设置为 SanedboxNode
	template<typename T, typename Base = T, typename std::enable_if<std::is_base_of<SandboxNode, Base>::value>::type* = nullptr>
	class NodeInstance
	{
	public:
		NodeInstance()
			: _node(nullptr), _id(0)
		{}
		NodeInstance(const NodeInstanceReflex& ins)
			: _node(ins.TryGetReadyNode().ToCast<T>()), _id(ins._id)
		{
			RegListenNodeReady();
		}
		NodeInstance(T* node)
			: _node(node), _id(node ? static_cast<SandboxNode*>(node)->GetNodeid() : 0)
		{}
		NodeInstance(const AutoRef<T>& node)
			: _node(node.template ToCast<T>()), _id(node ? node.template StaticToCast<SandboxNode>()->GetNodeid() : 0)
		{}
		NodeInstance(const NodeInstance& ins)
			: _node(ins._node), _id(ins._id)
		{	RegListenNodeReady();
		}
		~NodeInstance()
		{
			UnregListenNodeReady();
		}

		template<typename T2>
		NodeInstance& operator=(const NodeInstance<T2>& ins)
		{
			UnregListenNodeReady();
			_node = ins._node;
			_id = ins._id;
			RegListenNodeReady();
			return *this;
		}
		NodeInstance& operator=(const NodeInstance& ins)
		{
			UnregListenNodeReady();
			_node = ins._node;
			_id = ins._id;
			RegListenNodeReady();
			return *this;
		}
		NodeInstance& operator=(const NodeInstanceReflex& ins)
		{
			UnregListenNodeReady();
			_node = ins.TryGetReadyNode().ToCast<T>();
			_id = ins._id;
			RegListenNodeReady();
			return *this;
		}

		inline const AutoRef<T>& operator->() const { return _node; }
		inline const AutoRef<T>& operator *() const { return _node; }
		inline operator T*() const { return _node.get(); }
		inline operator bool() const { return _node; }
		inline operator AutoRef<T>() const { return get(); }
		inline operator NodeInstanceReflex() const { return NodeInstanceReflex{_node, _id}; }

		inline bool operator==(const NodeInstance& v) const { return _id == v._id; }
		inline bool operator!=(const NodeInstance& v) const { return _id != v._id; }
		inline bool operator==(const T* v) const { return v ? _id == static_cast<const SandboxNode*>(v)->GetNodeid() : _id == 0; }
		inline bool operator!=(const T* v) const { return !(*this == v); }

		bool IsValid() const { return _id != 0; }

		template<typename T2>
		operator AutoRef<T2>() const { return _node ? _node->template ToCast<T2>() : nullptr; }
		template<typename T2>
		AutoRef<T2> ToCast() const { return _node ? _node->template ToCast<T2>() : nullptr; }
		template<typename T2>
		AutoRef<T2> StaticToCast() const { SANDBOX_ASSERT(!_node || IsKindOf<T2>()); return static_cast<T2*>(_node.get()); }
		template<typename T2>
		bool IsKindOf() const { return _node ? _node->template IsKindOf<T2>() : false; }

		// get
		inline const AutoRef<T>& get() const { return _node; }

		// set by id
		void SetNodeById(SandboxNodeID id)
		{
			if (id == _id)
				return;
			UnregListenNodeReady();
			_node = nullptr;
			_id = id;
			RegListenNodeReady();
		}

		// 设置节点准备完成的回调
		void SetNodeFinish(TCallback<NodeInstance*, T*>* cb) { m_cbFinish = cb; }

	private:
		// 回调
		void OnListenNodeReady(SandboxNodeID id, SandboxNode* node)
		{
			if (id == _id && node && static_cast< SandboxNode*>(node)->IsKindOf<T>())
			{
				_node = static_cast<SandboxNode*>(node)->StaticToCast<T>();
				if (m_cbFinish)
					m_cbFinish->Emit(this, _node.get());
			}
			else
				SANDBOX_ASSERT(false);
		}
		// 注册监听
		void RegListenNodeReady()
		{
			if (_id == 0 || _node)
				return;
			if (!m_cbNodeReady)
			{
				using CB = TCallbackIns<NodeInstance, SandboxNodeID, SandboxNode*>;
				m_cbNodeReady = SANDBOX_NEW(CB, this, &NodeInstance::OnListenNodeReady);
			}
			NodeInstanceReflex::RegNodeReady(_id, m_cbNodeReady);
		}
		// 注销监听
		void UnregListenNodeReady()
		{
			if (_id != 0 && !_node && m_cbNodeReady)
			{
				NodeInstanceReflex::UnregNodeReady(_id, m_cbNodeReady);
			}
			SANDBOX_DELETE(m_cbNodeReady);
		}

	private:
		AutoRef<T> _node;
		SandboxNodeID _id = 0;
		TCallback<SandboxNodeID, SandboxNode*>* m_cbNodeReady = nullptr; // 回调用于更新节点
		TCallback<NodeInstance*, T*>* m_cbFinish = nullptr; // 完成通知
	};

}
