#pragma once
/**
* file : SceneEffectFan
* func : 场景效果 （扇形）
* by : yangzy
*/
#include "SceneEffectLine.h"
#include "SceneEffectSphere.h"

typedef uint16_t UINT16;
namespace Rainbow {
	class Model;
}
class SceneEffectFan : public SceneEffectGeom
{
	struct vertex
	{
		vertex()
		{
			mNormal = { 0, 1, 0 };
			mUV = { 0, 0 };
		}
		Rainbow::Vector3f mPosition;
		Rainbow::Vector3f mNormal;
		Rainbow::Vector2f mUV;
	};
public:
	SceneEffectFan(CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectFan();
	virtual void Refresh() final;
	virtual void OnDraw(World* pWorld) final;
	void setPosition(const Rainbow::Vector3f& pos);
	void setSmooth(float level);
	void draw(const Rainbow::Vector3f& vOrigin, const Rainbow::Vector3f& vBeginPos, const Rainbow::Vector3f& vEndPos, const std::string& szTexture = "blocks/voxelBrush.png");

private:
	std::vector<vertex> mDrawVertices;
	std::vector<uint16_t> mDrawIndices;
	Rainbow::Model* mpModel = nullptr;
	Rainbow::Vector3f	mPostion;
	float mRadisu;
	float mSmoothLevel = 1.0f / 360;
	Rainbow::Vector3f m_vOrigin;
	Rainbow::Vector3f m_vBeginPos;
	Rainbow::Vector3f m_vEndPos;
	std::string m_szTexture;
};
