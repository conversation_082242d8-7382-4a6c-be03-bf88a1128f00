/**
* file : SandboxReflexTypePolicy
* func : 反射类型处理方案
* by : chenzh
*/
#include "SandboxReflexTypePolicy.h"
#include "SandboxReflexTypePolicyEx.h"
#include "SandboxLuaScriptState.h"
#include "SandboxCommonBridge.h"
#include "SandboxSceneObjectBridge.h"
#include "SandboxReflexVariant.h"
#include "SandboxLua.h"
#include "Math/Vector4f.h"
#include "Math/Vector3f.h"
#include "OgreWCoord.h"
#include "OgreRect.h"
#include "Common/LegacyOgreColourValue.h"//#include "OgreColourValue.h"
#include "OgreRay.h"
#include "SandboxReflexMap.h"
#include "SandboxCustomBuffer.h"
#include "SandboxCommonInfo.h"
#include "base/stream/SandboxStream.h"
#include "script/bridge/SandboxReflexReferenceBridge.h"
#include "EnginePrefix.h"
#include "Math/Vector.h"


namespace MNSandbox {

	// #define RECORD_EFFECTOBJECT_REFLEX_LOG
	#define USE_SPECIAL_TYPE_REFLEX_VALUE

#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
	static ParticleEmitterColorGradient defaultParticleEmitterColorGradient1;
	static ParticleEmitterColorGradient defaultParticleEmitterColorGradient2(true);
	static ParticleEmitterColorGradient& GetDefaultParticleEmitterColorGradient3()
	{
		static ParticleEmitterColorGradient defaultParticleEmitterColorGradient1;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			for (int i = 0; i < 20; i++)
			{
				defaultParticleEmitterColorGradient1._colors[i] = Rainbow::ColorQuad(0, 0, 0, 0);
			}
		}
		return defaultParticleEmitterColorGradient1;
	}
	static int isSpecialDefaultParticleEmitterColorGradient(const ParticleEmitterColorGradient& origin)
	{
		if(origin == defaultParticleEmitterColorGradient1)
		{
			return 1;
		}
		else if(origin == defaultParticleEmitterColorGradient2)
		{
			return 2;
		}
		else if (origin == GetDefaultParticleEmitterColorGradient3())
		{
			return 3;
		}
		return 0;
	}

#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
	#define QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(T)	\
		static std::vector<T> all##T; 					\
		static std::unordered_map<int,int> map##T; 		\
		static void Add##T(const T& other){				\
			bool isFind = false;						\
			for(int i = 0; i < all##T.size(); i++)		\
			{											\
				if(all##T[i] == other)					\
				{										\
					isFind = true;						\
					map##T[i] = map##T[i] + 1;			\
					break;								\
				}										\
			}											\
			if(!isFind)									\
			{											\
				int idx = all##T.size();				\
				map##T[idx] = 1;						\
				all##T.push_back(other);				\
			}											\
		}

	QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(ParticleEmissionBurst)

	QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(ParticleEmitterColorGradient)

	QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(PairVector3fInfo)

	static void PrintTop100ParticleEmissionBurst()
	{
		std::multimap<int, int> decentMaps;
		for(const auto& one : mapParticleEmissionBurst)
		{
			decentMaps.emplace(one.second, one.first);
		}
		int count = 100;
		std::vector<ParticleEmissionBurst> curve1; 
		for(auto it = decentMaps.crbegin(); it != decentMaps.crend(); ++it)
		{
			count = count - 1;
			curve1.push_back(allParticleEmissionBurst[it->second]);
			if(count < 0)
			{
				break;
			}
		}
		auto d2 = 10;
	}

	static void PrintTop100ParticleEmitterColorGradient()
	{
		std::multimap<int, int> decentMaps;
		for(const auto& one : mapParticleEmitterColorGradient)
		{
			decentMaps.emplace(one.second, one.first);
		}
		int count = 100;
		std::vector<ParticleEmitterColorGradient> curve1; 
		for(auto it = decentMaps.crbegin(); it != decentMaps.crend(); ++it)
		{
			count = count - 1;
			curve1.push_back(allParticleEmitterColorGradient[it->second]);
			if(count < 0)
			{
				break;
			}
		}
		auto d2 = 10;
	}

	static void PrintTop100PairVector3Info()
	{
		std::multimap<int, int> decentMaps;
		for(const auto& one : mapPairVector3fInfo)
		{
			decentMaps.emplace(one.second, one.first);
		}
		int count = 100;
		std::vector<PairVector3fInfo> curve1; 
		for(auto it = decentMaps.crbegin(); it != decentMaps.crend(); ++it)
		{
			count = count - 1;
			curve1.push_back(allPairVector3fInfo[it->second]);
			if(count < 0)
			{
				break;
			}
		}
		auto d2 = 10;
	}
#endif

	static ParticleEmissionBurst& GetDefaultParticleEmissionBurst1()
	{
		static ParticleEmissionBurst ret;
		return ret;
	}
	static ParticleEmissionBurst& GetDefaultParticleEmissionBurst2()
	{
		static ParticleEmissionBurst ret;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._curCount = 1;
			ret._minCounts[0] = 1;
			ret._maxCounts[0] = 1;
			ret._cycles[0] = 1;
			ret._intervals[0] = 0.01f;
			ret._probabilitys[0] = 1.0f;
		}
		return ret;
	}
	static int isSpecialParticleEmissionBurst(const ParticleEmissionBurst& origin)
	{
		if(origin == GetDefaultParticleEmissionBurst1())
		{
			return 1;
		}
		else if(origin == GetDefaultParticleEmissionBurst2())
		{
			return 2;
		}
		return 0;
	}

	static ParticleVertexStreams defaultParticleVertexStreams1;
	static ParticleVertexStreams defaultParticleVertexStreams2(true);
	static ParticleVertexStreams& GetDefaultParticleVertexStreams3()
	{
		static ParticleVertexStreams ret(true);
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._values[0] = 0;
			ret._values[1] = 1;
			ret._values[2] = 3;
			ret._values[3] = 4;
			ret._values[4] = 0;
		}
		return ret;
	}
	static int isSpecialDefaultParticleVertexStreams(const ParticleVertexStreams& origin)
	{
		if(origin == defaultParticleVertexStreams1)
		{
			return 1;
		}
		else if(origin == defaultParticleVertexStreams2)
		{
			return 2;
		}
		else if (origin == GetDefaultParticleVertexStreams3())
		{
			return 3;
		}
		return 0;
	}

	static ParticleSubEmitterData defaultParticleSubEmitterData;
	static int isSpecialDefaultParticleSubEmitterData(const ParticleSubEmitterData& origin)
	{
		if(origin == defaultParticleSubEmitterData)
		{
			return 1;
		}
		return 0;
	}
	static PairVector3fInfo& GetDefaultPairVector3fInfo1()
	{
		static PairVector3fInfo ret;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._valuefrist = Rainbow::Vector3f::zero;
			ret._valuesecond = Rainbow::Vector3f::zero;
		}
		return ret;
	}
	static PairVector3fInfo& GetDefaultPairVector3fInfo2()
	{
		static PairVector3fInfo ret;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._valuefrist = Rainbow::Vector3f(100.0f);
			ret._valuesecond = Rainbow::Vector3f(100.0f);
		}
		return ret;
	}
	static PairVector3fInfo& GetDefaultPairVector3fInfo3()
	{
		static PairVector3fInfo ret;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._valuefrist = Rainbow::Vector3f(1.0f);
			ret._valuesecond = Rainbow::Vector3f(1.0f);
		}
		return ret;
	}
	static PairVector3fInfo& GetDefaultPairVector3fInfo4()
	{
		static PairVector3fInfo ret = GetDefaultPairVector3fInfo1();
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._valuefrist.z = 0.79f;
			ret._valuesecond.z = 0.79f;
		}
		return ret;
	}
	static int isSpecialDefaultPairVector3fInfo(const PairVector3fInfo& origin)
	{
		if(origin == GetDefaultPairVector3fInfo1())
		{
			return 1;
		}
		else if(origin == GetDefaultPairVector3fInfo2())
		{
			return 2;
		}
		else if(origin == GetDefaultPairVector3fInfo3())
		{
			return 3;
		}
		else if(origin == GetDefaultPairVector3fInfo4())
		{
			return 4;
		}
		return 0;
	}
#endif

#if LOG_FOR_REFLEXPOLICY
	void ReflexPolicyRegLog::PushLog(const std::string& log)
	{
		GetLog().push_back(log);
	}
	std::vector<std::string>& ReflexPolicyRegLog::GetLog()
	{
		static std::vector<std::string> s_logs;
		return s_logs;
	}
#endif

	ReflexTypePolicyRegister::ReflexTypePolicyRegister(ReflexType& type, unsigned eType, const std::string& name, FillPolicyFunc fillFunc, ReflexType::TYPE category)
	{
		Register(type, eType, name, fillFunc, category);
	}

	//ReflexTypePolicyRegister::ReflexTypePolicyRegister(ReflexType& type, unsigned eType, const std::string& name, const ReflexTypePolicy& policy, ReflexType::TYPE category)
	//{
	//	Register(type, eType, name, policy, category);
	//}

	void ReflexTypePolicyRegister::Register(ReflexType& type, unsigned eType, const std::string& name, FillPolicyFunc fillFunc, ReflexType::TYPE category)
	{
		type.SetReflexTypeEnum((REFLEXTYPEENUM)eType);
		type.SetName(name);

#if LOG_FOR_REFLEXPOLICY
		char szTemp[1024];
		sprintf(szTemp, "TType : %s, TPolicyFill : %p, Category : %d", type.GetName().c_str(), fillFunc, (int)category);
		ReflexPolicyRegLog::PushLog(szTemp);
#endif

		ReflexTypePolicy& policy = type.GetPolicy();
		type.SetCategoryType(category); // 设置分类
		fillFunc(policy);
	}

//	void ReflexTypePolicyRegister::Register(ReflexType& type, unsigned eType, const std::string& name, const ReflexTypePolicy& policy, ReflexType::TYPE category)
//	{
//		type.SetReflexTypeEnum((REFLEXTYPEENUM)eType);
//		type.SetName(name);
//
//#if LOG_FOR_REFLEXPOLICY
//		char szTemp[1024];
//		sprintf(szTemp, "TType : %s, TPolicyFill : %p, Category : %d", type.GetName().c_str(), (void*)&policy, (int)category);
//		ReflexPolicyRegLog::PushLog(szTemp);
//#endif
//
//		ReflexTypePolicy& mypolicy = type.GetPolicy();
//		type.SetCategoryType(category); // 设置分类
//		mypolicy = policy;
//	}

	// 基类
	void ReflexPolicyFuncBase::CallbackSerialize(const void*, MNJsonVal&)
	{
		SANDBOX_ASSERT(false && "undefined [Serialize] function for this type");
	}
	bool ReflexPolicyFuncBase::CallbackUnserialize(void*, const MNJsonVal&)
	{
		SANDBOX_ASSERT(false && "undefined [Unserialize] function for this type");
		return false;
	}
	bool ReflexPolicyFuncBase::CallbackLuaToC(void*, lua_State*, int)
	{
		SANDBOX_ASSERT(false && "undefined [LuaToC] function for this type");
		return false;
	}
	int ReflexPolicyFuncBase::CallbackLuaPushC(const void*, lua_State*)
	{
		SANDBOX_ASSERT(false && "undefined [LuaPushC] function for this type");
		return 0;
	}
	std::string ReflexPolicyFuncBase::CallbackToString(const void*)
	{
		//SANDBOX_ASSERT(false && "undefined [ToString] function for this type");
		return std::string("[unknown]");
	}
	unsigned ReflexPolicyFuncBase::SizeOf()
	{
		SANDBOX_ASSERT(false && "undefined [SizeOf] function for this type");
		return 0;
	}
	void ReflexPolicyFuncBase::Structure(void*)
	{
		SANDBOX_ASSERT(false && "undefined [Structure] function for this type");
	}
	void ReflexPolicyFuncBase::Destructor(void*)
	{
		SANDBOX_ASSERT(false && "undefined [Destructor] function for this type");
	}
	void ReflexPolicyFuncBase::Copy(const void*, void*)
	{
		SANDBOX_ASSERT(false && "undefined [Copy] function for this type");
	}
	bool ReflexPolicyFuncBase::IsSame(const void* src, const void* dst)
	{
		//SANDBOX_ASSERT(false && "undefined [IsSame] function for this type");
		return false;
	}
	bool ReflexPolicyFuncBase::ToEnumVal(const void*, EnumValType&)
	{
		SANDBOX_ASSERT(false && "undefined [ToEnumVal] function for this type");
		return false;
	}
	bool ReflexPolicyFuncBase::GetEnumVal(void*, EnumValType)
	{
		SANDBOX_ASSERT(false && "undefined [GetEnumVal] function for this type");
		return false;
	}
	size_t ReflexPolicyFuncBase::ReflexToBinary(const void*, const AutoRef<Stream>&)
	{
		SANDBOX_ASSERT(false && "undefined [ReflexToBinary] function for this type");
		return Stream::Error;
	}
	bool ReflexPolicyFuncBase::ReflexFromBinary(void*, const AutoRef<Stream>&)
	{
		SANDBOX_ASSERT(false && "undefined [ReflexFromBinary] function for this type");
		return false;
	}

	//////////////////////////////////////////////////////////////////////////////////

	// void
	class ReflexPolicyVoid
	{
	public:
		// 定义方案
		static void CallbackSerialize(const void*, MNJsonVal&);
		static bool CallbackUnserialize(void*, const MNJsonVal&);
		static bool CallbackLuaToC(void*, lua_State*, int);
		static int CallbackLuaPushC(const void*, lua_State*);
		static std::string CallbackToString(const void*);
		static unsigned SizeOf(); // 类型所占字节数
		static void Structure(void*); // 构造
		static void Destructor(void*); // 析构
		static void Copy(const void*, void*); // 拷贝
		static size_t ReflexToBinary(const void*, const AutoRef<Stream>&); // 转成二进制数据
		static bool ReflexFromBinary(void*, const AutoRef<Stream>&); // 从二进制数据提取

		static void FillPolicy(ReflexTypePolicy& policy)
		{
			policy.m_cbSerialize = &CallbackSerialize;
			policy.m_cbUnserialize = &CallbackUnserialize;
			policy.m_cbLuaToC = &CallbackLuaToC;
			policy.m_cbLuaPushC = &CallbackLuaPushC;
			policy.m_cbToString = &CallbackToString;
			policy.m_fSizeOf = &SizeOf;
			policy.m_fStructure = &Structure;
			policy.m_fDestructor = &Destructor;
			policy.m_fCopy = &Copy;
			policy.m_cbReflexToBinary = &ReflexToBinary;
			policy.m_cbReflexFromBinary = &ReflexFromBinary;
		}
	};

	// void
	void ReflexPolicyVoid::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		//out.import(MNJsonNull());
	}
	bool ReflexPolicyVoid::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		//return in.is<MNJsonNull>();
		//return false;
		return true;
	}
	bool ReflexPolicyVoid::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		//return lua_isnil(L, objindex);
		return false;
	}
	int ReflexPolicyVoid::CallbackLuaPushC(const void* data, lua_State* L)
	{
		//lua_pushnil(L);
		//return 1;
		return 0;
	}
	std::string ReflexPolicyVoid::CallbackToString(const void* data)
	{
		return std::string("void");
	}
	unsigned ReflexPolicyVoid::SizeOf()
	{
		return 0;
	}
	void ReflexPolicyVoid::Structure(void*)
	{
	}
	void ReflexPolicyVoid::Destructor(void*)
	{
	}
	void ReflexPolicyVoid::Copy(const void*, void*)
	{
	}
	size_t ReflexPolicyVoid::ReflexToBinary(const void*, const AutoRef<Stream>&)
	{
		return 0;
	}
	bool ReflexPolicyVoid::ReflexFromBinary(void*, const AutoRef<Stream>&)
	{
		return true;
	}
	RegisterReflexTypePolicy(void, REFLEXTYPEENUM_VOID, ReflexPolicyVoid, ReflexType::TYPE::NIL);

	// number
#define REFLEXTYPE_NUMBER_POLICY(NumType, eType) \
	template<> unsigned ReflexPolicyDataCustom<NumType>::SizeOf() { return ReflexPolicyFuncBase::template TSizeOf<NumType>(); } \
	template<> void ReflexPolicyDataCustom<NumType>::Structure(void* data) { \
		NumType* p = new(data)NumType; \
		*p = 0; \
	} \
	template<> void ReflexPolicyDataCustom<NumType>::Destructor(void* data) { ReflexPolicyFuncBase::template TDestructor<NumType>(data); } \
	template<> void ReflexPolicyDataCustom<NumType>::Copy(const void* src, void* dst) { ReflexPolicyFuncBase::template TCopy<NumType>(src, dst); } \
	template<> bool ReflexPolicyDataCustom<NumType>::IsSame(const void* src, const void* dst) \
	{ \
		auto& dataSrc = Data(src); \
		auto& dataDst = Data(dst); \
		return NumberIsSame(dataSrc, dataDst); \
	} \
	template<> \
	void ReflexPolicySerialize<NumType>::CallbackSerialize(const void* data, MNJsonVal& out) \
	{ \
		NumType v = Data(data); \
		MNJsonNumber n = static_cast<MNJsonNumber>(v); \
		out.import(n); \
	} \
	template<> \
	bool ReflexPolicySerialize<NumType>::CallbackUnserialize(void* data, const MNJsonVal& in) \
	{ \
		if (in.is<MNJsonNumber>()) \
		{ \
			MNJsonNumber v = in.get<MNJsonNumber>(); \
			NumType n = static_cast<NumType>(v); \
			Data(data) = n; \
			return true; \
		} \
		return false; \
	} \
	template<> \
	bool ReflexPolicyLua<NumType>::CallbackLuaToC(void* data, lua_State* L, int objindex) \
	{ \
		int luatype = lua_type(L, objindex); \
		lua_Number v; \
		switch (luatype) \
		{ \
		case LUA_TNUMBER: \
			v = lua_tonumber(L, objindex); \
			Data(data) = (NumType)v; \
			return true; \
		default: \
			SANDBOX_ASSERT(luatype <= 0 && "nonsupport type!"); \
			return false; \
		} \
		return false; \
	} \
	template<> \
	int ReflexPolicyLua<NumType>::CallbackLuaPushC(const void* data, lua_State* L) \
	{ \
		NumType v = Data(data); \
		lua_pushnumber(L, (lua_Number)v); \
		return 1; \
	} \
	template<> \
	std::string ReflexPolicyToString<NumType>::CallbackToString(const void* data) \
	{ \
		return std::to_string(Data(data)); \
	} \
	template<> \
	size_t ReflexPolicySerialize<NumType>::ReflexToBinary(const void* data, const AutoRef<Stream>& out) \
	{ \
		return out->WriteNumber<NumType>(Data(data)); \
	} \
	template<> \
	bool ReflexPolicySerialize<NumType>::ReflexFromBinary(void* data, const AutoRef<Stream>& in) \
	{ \
		return in->ReadNumber<NumType>(Data(data)); \
	} \
	static MNSandbox::ReflexTypePolicyRegister SDB_MACROSSPLICE(s_policyReg_, __LINE__)( \
		MNSandbox::ReflexType::GetSingleton<NumType>(), \
		eType, \
		#NumType, \
		&ReflexPolicyFuncReg<NumType, ReflexPolicyDataCustom<NumType>>::FillPolicy, \
		ReflexType::TYPE::NUMBER \
	)
	/*RegisterReflexTypePolicy(NumType, eType, (ReflexPolicyFuncReg<NumType, ReflexPolicyDataCustom<NumType>>), ReflexType::TYPE::NUMBER)*/
//end REFLEXTYPE_NUMBER_POLICY

	REFLEXTYPE_NUMBER_POLICY(char, REFLEXTYPEENUM_CHAR);
	REFLEXTYPE_NUMBER_POLICY(unsigned char, REFLEXTYPEENUM_UNCHAR);
	REFLEXTYPE_NUMBER_POLICY(short, REFLEXTYPEENUM_SHORT);
	REFLEXTYPE_NUMBER_POLICY(unsigned short, REFLEXTYPEENUM_UNSHORT);
	REFLEXTYPE_NUMBER_POLICY(int, REFLEXTYPEENUM_INT);
	REFLEXTYPE_NUMBER_POLICY(unsigned int, REFLEXTYPEENUM_UNINT);
	REFLEXTYPE_NUMBER_POLICY(long, REFLEXTYPEENUM_LONG); // 64位为主
	REFLEXTYPE_NUMBER_POLICY(unsigned long, REFLEXTYPEENUM_UNLONG); // 64位为主
	REFLEXTYPE_NUMBER_POLICY(long long, REFLEXTYPEENUM_LONGLONG);
	REFLEXTYPE_NUMBER_POLICY(unsigned long long, REFLEXTYPEENUM_UNLONGLONG);
	REFLEXTYPE_NUMBER_POLICY(float, REFLEXTYPEENUM_FLOAT);
	REFLEXTYPE_NUMBER_POLICY(double, REFLEXTYPEENUM_DOUBLE);
	//REFLEXTYPE_NUMBER_POLICY(long double, REFLEXTYPEENUM_LONGDOUBLE); // 按最大的

	// bool
	template<>
	void ReflexPolicyFunc<bool>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		out.import((MNJsonBool)Data(data));
	}
	template<>
	bool ReflexPolicyFunc<bool>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (in.is<MNJsonBool>())
		{
			Data(data) = (bool)in.get<MNJsonBool>();
			return true;
		}
		return false;
	}
	template<>
	bool ReflexPolicyFunc<bool>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		int luatype = lua_type(L, objindex);
		switch (luatype)
		{
		case LUA_TNIL:
			Data(data) = false;
			return true;
		case LUA_TBOOLEAN:
			Data(data) = lua_toboolean(L, objindex);
			return true;
		default:
			SANDBOX_ASSERT("nonsupport type!");
			return false;
		}
	}
	template<>
	int ReflexPolicyFunc<bool>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		lua_pushboolean(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<bool>::CallbackToString(const void* data)
	{
		return Data(data) ? "true" : "false";
	}
	template<>
	size_t ReflexPolicyFunc<bool>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		return out->WriteBool(Data(data));
	}
	template<>
	bool ReflexPolicyFunc<bool>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		return in->ReadBool(Data(data));
	}
	RegisterReflexTypePolicy(bool, REFLEXTYPEENUM_BOOL, ReflexPolicyFunc<bool>, ReflexType::TYPE::BOOL);

	// std::string
	template<>
	void ReflexPolicyFunc<std::string>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		out.import(Data(data));
	}
	template<>
	bool ReflexPolicyFunc<std::string>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (in.is<MNJsonStr>())
		{
			Data(data) = in.get<MNJsonStr>();
			return true;
		}
		return false;
	}
	template<>
	bool ReflexPolicyFunc<std::string>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		int luatype = lua_type(L, objindex);
		switch (luatype)
		{
		case LUA_TSTRING:
			{
				size_t len = 0;
				const char* str = lua_tolstring(L, objindex, &len);
				Data(data).assign(str, len);
			}
			return true;
		default:
			return false; // "nonsupport type!"
		}
	}
	template<>
	int ReflexPolicyFunc<std::string>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		auto& v = Data(data);
		lua_pushlstring(L, v.c_str(), v.size());
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<std::string>::CallbackToString(const void* data)
	{
		return Data(data);
	}
	template<>
	size_t ReflexPolicyFunc<std::string>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		return out->WriteString(Data(data));
	}
	template<>
	bool ReflexPolicyFunc<std::string>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		return in->ReadString(Data(data));
	}
	template<>
	bool ReflexPolicyTypeCast<const char*, std::string>::CastTo(const void* src, void* dst)
	{
		auto& vsrc = DataSrc(src);
		auto& vdst = DataDst(dst);
		if (!vsrc) return false;
		vdst = vsrc;
		return true;
	}
	template<>
	bool ReflexPolicyTypeCast<char*, std::string>::CastTo(const void* src, void* dst)
	{
		auto& vsrc = DataSrc(src);
		auto& vdst = DataDst(dst);
		if (!vsrc) return false;
		vdst = vsrc;
		return true;
	}
	RegisterReflexTypePolicy(std::string, REFLEXTYPEENUM_STRING, ReflexPolicyFunc<std::string>, ReflexType::TYPE::STRING);
	RegisterReflexTypePolicyCastFrom(std::string, const char*, char*);

	// const char*
	typedef const char* const_char_ptr;
	template<>
	bool ReflexPolicyLua<const_char_ptr>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		int luatype = lua_type(L, objindex);
		switch (luatype)
		{
		case LUA_TSTRING:
			Data(data) = lua_tolstring(L, objindex, nullptr);
			return true;
		default:
			SANDBOX_ASSERT("nonsupport type!");
			return false;
		}
	}
	template<>
	int ReflexPolicyLua<const_char_ptr>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		lua_pushstring(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyToString<const_char_ptr>::CallbackToString(const void* data)
	{
		const_char_ptr v = Data(data);
		return v ? std::string(v) : std::string();
	}
	template<>
	bool ReflexPolicyTypeCast<std::string, const_char_ptr>::CastTo(const void* src, void* dst)
	{
		auto& vsrc = DataSrc(src);
		auto& vdst = DataDst(dst);
		vdst = vsrc.c_str();
		return true;
	}
	template<>
	const ReflexType* ReflexPolicyVersionCompatibility<const_char_ptr>::CallbackVersionCompatibility(unsigned version)
	{
		return &ReflexType::GetSingleton<std::string>(); // 使用 std::string 存档
	}
	RegisterReflexTypePolicy(const_char_ptr, REFLEXTYPEENUM_CONSTCHARPTR, ReflexPolicyLuaRegCombi<const_char_ptr>, ReflexType::TYPE::STRING);
	RegisterReflexTypePolicyCastFrom(const_char_ptr, std::string);

	// char*
	typedef char* char_ptr;
	template<>
	bool ReflexPolicyLua<char_ptr>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return ReflexPolicyLua<const_char_ptr>::CallbackLuaToC(data, L, objindex);
	}
	template<>
	int ReflexPolicyLua<char_ptr>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return ReflexPolicyLua<const_char_ptr>::CallbackLuaPushC(data, L);
	}
	template<>
	std::string ReflexPolicyToString<char_ptr>::CallbackToString(const void* data)
	{
		return ReflexPolicyToString<const_char_ptr>::CallbackToString(data);
	}
	template<>
	const ReflexType* ReflexPolicyVersionCompatibility<char_ptr>::CallbackVersionCompatibility(unsigned version)
	{
		return &ReflexType::GetSingleton<std::string>(); // 使用 std::string 存档
	}
	RegisterReflexTypePolicy(char_ptr, REFLEXTYPEENUM_CHARPTR, ReflexPolicyLuaRegCombi<char_ptr>, ReflexType::TYPE::STRING);

	// void*
	using ReflexPolicyFunc_Ptr = ReflexPolicyFunc<void*>;
	template<>
	void ReflexPolicyFunc_Ptr::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		SANDBOX_ASSERTEX(false, "ptr json serialize");
	}
	template<>
	bool ReflexPolicyFunc_Ptr::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		SANDBOX_ASSERTEX(false, "ptr json unserialize");
		return false;
	}
	template<>
	bool ReflexPolicyFunc_Ptr::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (!lua_isuserdata(L, objindex))
			return false;
		Data(data) = lua_touserdata(L, objindex);;
		return true;
	}
	template<>
	int ReflexPolicyFunc_Ptr::CallbackLuaPushC(const void* data, lua_State* L)
	{
		lua_pushlightuserdata(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc_Ptr::CallbackToString(const void* data)
	{
		char tmp[32];
		sprintf(tmp, "%p", Data(data));
		return tmp;
	}
	template<>
	size_t ReflexPolicyFunc_Ptr::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		SANDBOX_ASSERTEX(false, "ptr to binary");
		return Stream::Error;
	}
	template<>
	bool ReflexPolicyFunc_Ptr::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		SANDBOX_ASSERTEX(false, "ptr from binary");
		return false;
	}
	RegisterReflexTypePolicyClass(void*, REFLEXTYPEENUM_VOIDPTR, ReflexPolicyFunc_Ptr);

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	// MINIW::Rainbow::Vector3f
	typedef Rainbow::Vector3f vector3;
	template<>
	void ReflexPolicyFunc<vector3>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x << (MNJsonNumber)v.y << (MNJsonNumber)v.z;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<vector3>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2))
		{
			v.x = jsona.get<MNJsonNumber>(0);
			v.y = jsona.get<MNJsonNumber>(1);
			v.z = jsona.get<MNJsonNumber>(2);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<vector3>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Vector3Bridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<vector3>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		Vector3Bridge::PushVector3(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<vector3>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f, %f}", v.x, v.y, v.z);
		//return szTemp;
		return ToString("{", v.x, ", ", v.y, ", ", v.z, "}");
	}
	template<>
	size_t ReflexPolicyFunc<vector3>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.x);
		len += out->WriteNumber<float>(v.y);
		len += out->WriteNumber<float>(v.z);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<vector3>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.x);
		ret &= in->ReadNumber<float>(v.y);
		ret &= in->ReadNumber<float>(v.z);
		return ret;
	}
	RegisterReflexTypePolicyClass(vector3, REFLEXTYPEENUM_VECTOR3, ReflexPolicyFunc<vector3>);

	// MINIW::Rainbow::Vector3i
	typedef Rainbow::Vector3i vector3i;
	template<>
	void ReflexPolicySerialize<vector3i>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x() << (MNJsonNumber)v.y() << (MNJsonNumber)v.z();
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicySerialize<vector3i>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2))
		{
			v.x() = (int)jsona.get<MNJsonNumber>(0);
			v.y() = (int)jsona.get<MNJsonNumber>(1);
			v.z() = (int)jsona.get<MNJsonNumber>(2);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	std::string ReflexPolicyToString<vector3i>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f, %f}", v.x, v.y, v.z);
		//return szTemp;
		return ToString("{", v.x(), ", ", v.y(), ", ", v.z(), "}");
	}
	template<>
	size_t ReflexPolicySerialize<vector3i>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<int>(v.x());
		len += out->WriteNumber<int>(v.y());
		len += out->WriteNumber<int>(v.z());
		return len;
	}
	template<>
	bool ReflexPolicySerialize<vector3i>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<int>(v.x());
		ret &= in->ReadNumber<int>(v.y());
		ret &= in->ReadNumber<int>(v.z());
		return ret;
	}
	RegisterReflexTypePolicyClass(vector3i, REFLEXTYPEENUM_VECTOR3_INT, ReflexPolicySerializeReg<vector3i>);

	// Rainbow::Vector2f
	typedef Rainbow::Vector2f vector2;
	template<>
	void ReflexPolicyFunc<vector2>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x << (MNJsonNumber)v.y ;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<vector2>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1))
		{
			v.x = jsona.get<MNJsonNumber>(0);
			v.y = jsona.get<MNJsonNumber>(1);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<vector2>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Bridge<vector2>::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<vector2>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		Vector2Bridge::PushVector2(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<vector2>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		/*	char szTemp[128];
			sprintf(szTemp, "{%f, %f}", v.x, v.y);
			return szTemp;*/
		return ToString("{", v.x, ", ", v.y, "}");
	}
	template<>
	size_t ReflexPolicyFunc<vector2>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.x);
		len += out->WriteNumber<float>(v.y);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<vector2>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.x);
		ret &= in->ReadNumber<float>(v.y);
		return ret;
	}
	RegisterReflexTypePolicyClass(vector2, REFLEXTYPEENUM_VECTOR2, ReflexPolicyFunc<vector2>);

	// Rainbow::Vector4f
	typedef Rainbow::Vector4f vector4;
	template<>
	void ReflexPolicyFunc<vector4>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x << (MNJsonNumber)v.y << (MNJsonNumber)v.z << (MNJsonNumber)v.w;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<vector4>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3))
		{
			v.x = jsona.get<MNJsonNumber>(0);
			v.y = jsona.get<MNJsonNumber>(1);
			v.z = jsona.get<MNJsonNumber>(2);
			v.w = jsona.get<MNJsonNumber>(3);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<vector4>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Bridge<vector4>::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<vector4>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		Vector4Bridge::PushVector4(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<vector4>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f, %f, %f}", v.x, v.y, v.z, v.w);
		//return szTemp;
		return ToString("{", v.x, ", ", v.y, ", ", v.z, ", ", v.w, "}");
	}
	template<>
	size_t ReflexPolicyFunc<vector4>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.x);
		len += out->WriteNumber<float>(v.y);
		len += out->WriteNumber<float>(v.z);
		len += out->WriteNumber<float>(v.w);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<vector4>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.x);
		ret &= in->ReadNumber<float>(v.y);
		ret &= in->ReadNumber<float>(v.z);
		ret &= in->ReadNumber<float>(v.w);
		return ret;
	}
	RegisterReflexTypePolicyClass(vector4, REFLEXTYPEENUM_VECTOR4, ReflexPolicyFunc<vector4>);


	template<>
	void ReflexPolicyFunc<MINIW::RectFloat>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.m_Left << (MNJsonNumber)v.m_Top << (MNJsonNumber)v.m_Right << (MNJsonNumber)v.m_Bottom;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<MINIW::RectFloat>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3))
		{
			v.m_Left = jsona.get<MNJsonNumber>(0);
			v.m_Top = jsona.get<MNJsonNumber>(1);
			v.m_Right = jsona.get<MNJsonNumber>(2);
			v.m_Bottom = jsona.get<MNJsonNumber>(3);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<MINIW::RectFloat>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Bridge<MINIW::RectFloat>::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<MINIW::RectFloat>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		RectFloatBridge::PushRectFloat(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<MINIW::RectFloat>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f, %f, %f}", v.m_Left, v.m_Top, v.m_Right, v.m_Bottom);
		//return szTemp;
		return ToString("{", v.m_Left, ", ", v.m_Top, ", ", v.m_Right, ", ", v.m_Bottom, "}");
	}
	template<>
	size_t ReflexPolicyFunc<MINIW::RectFloat>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.m_Left);
		len += out->WriteNumber<float>(v.m_Top);
		len += out->WriteNumber<float>(v.m_Right);
		len += out->WriteNumber<float>(v.m_Bottom);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<MINIW::RectFloat>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.m_Left);
		ret &= in->ReadNumber<float>(v.m_Top);
		ret &= in->ReadNumber<float>(v.m_Right);
		ret &= in->ReadNumber<float>(v.m_Bottom);
		return ret;
	}
	RegisterReflexTypePolicyClass(MINIW::RectFloat, REFLEXTYPEENUM_RECTFLOAT, ReflexPolicyFunc<MINIW::RectFloat>);

	//-------------

	// MINIW::Ray
	//typedef MINIW::Ray Ray;
	template<> unsigned ReflexPolicyDataCustom<MINIW::Ray>::SizeOf() { return ReflexPolicyFuncBase::template TSizeOf<MINIW::Ray>(); }
	template<> void ReflexPolicyDataCustom<MINIW::Ray>::Structure(void* data) { ReflexPolicyFuncBase::template TStructure<MINIW::Ray>(data); }
	template<> void ReflexPolicyDataCustom<MINIW::Ray>::Destructor(void* data) { ReflexPolicyFuncBase::template TDestructor<MINIW::Ray>(data); }
	template<> void ReflexPolicyDataCustom<MINIW::Ray>::Copy(const void* src, void* dst) { ReflexPolicyFuncBase::template TCopy<MINIW::Ray>(src, dst); }
	template<> bool ReflexPolicyDataCustom<MINIW::Ray>::IsSame(const void* src, const void* dst) {
		auto& dataSrc = Data(src);
		auto& dataDst = Data(dst);
		return dataSrc.m_Dir == dataDst.m_Dir && dataSrc.m_Origin == dataDst.m_Origin && dataSrc.m_Range == dataDst.m_Range;
	}
	template<>
	void ReflexPolicySerialize<MINIW::Ray>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.m_Origin.x << (MNJsonNumber)v.m_Origin.x << (MNJsonNumber)v.m_Origin.z
		<<(MNJsonNumber)v.m_Dir.x << (MNJsonNumber)v.m_Dir.x << (MNJsonNumber)v.m_Dir.x;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicySerialize<MINIW::Ray>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) 
			&& jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3) 
			&& jsona.has<MNJsonNumber>(4) && jsona.has<MNJsonNumber>(5))
		{
			v.m_Origin.x = jsona.get<MNJsonNumber>(0);
			v.m_Origin.y = jsona.get<MNJsonNumber>(1);
			v.m_Origin.z = jsona.get<MNJsonNumber>(2);

			v.m_Dir.x = jsona.get<MNJsonNumber>(3);
			v.m_Dir.y = jsona.get<MNJsonNumber>(4);
			v.m_Dir.z = jsona.get<MNJsonNumber>(5);
			v.m_Range = Rainbow::MAX_FLOAT;
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyLua<MINIW::Ray>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Bridge<MINIW::Ray>::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyLua<MINIW::Ray>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		RayBridge::PushRay(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyToString<MINIW::Ray>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		/*	char szTemp[128];
			sprintf(szTemp, "{%f, %f, %f, %f, %f, %f}", v.m_Origin.x, v.m_Origin.y, v.m_Origin.z, v.m_Dir.x, v.m_Dir.y, v.m_Dir.z);
			return szTemp;*/
		return ToString("{", v.m_Origin.x, ", ", v.m_Origin.y, ", ", v.m_Origin.z, ", ", v.m_Dir.x, ", ", v.m_Dir.y, ", ", v.m_Dir.z, "}");
	}
	template<>
	size_t ReflexPolicySerialize<MINIW::Ray>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		size_t vLen1 = out->WriteReflex<Rainbow::Vector3f>(v.m_Origin);
		size_t vLen2 = out->WriteReflex<Rainbow::Vector3f>(v.m_Dir);
		if (vLen1 == Stream::Error || vLen2 == Stream::Error)
		{
			SANDBOX_ASSERT(false);
			return Stream::Error;
		}

		len += vLen1 + vLen2;
		len += out->WriteNumber<float>(v.m_Range);
		return len;
	}
	template<>
	bool ReflexPolicySerialize<MINIW::Ray>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadReflex<Rainbow::Vector3f>(v.m_Origin);
		ret &= in->ReadReflex<Rainbow::Vector3f>(v.m_Dir);
		ret &= in->ReadNumber<float>(v.m_Range);
		return ret;
	}
	using ReflexPolicyFuncReg_MNRay = ReflexPolicyFuncReg<MINIW::Ray, ReflexPolicyDataCustom<MINIW::Ray>>;
	RegisterReflexTypePolicyClass(MINIW::Ray, REFLEXTYPEENUM_RAY, ReflexPolicyFuncReg_MNRay);

	// Rainbow::Quaternionf
	typedef Rainbow::Quaternionf Quaternion;
	template<>
	void ReflexPolicyFunc<Quaternion>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x << (MNJsonNumber)v.y << (MNJsonNumber)v.z << (MNJsonNumber)v.w;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<Quaternion>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3))
		{
			v.x = jsona.get<MNJsonNumber>(0);
			v.y = jsona.get<MNJsonNumber>(1);
			v.z = jsona.get<MNJsonNumber>(2);
			v.w = jsona.get<MNJsonNumber>(3);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<Quaternion>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *Bridge<Rainbow::Quaternionf>::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<Quaternion>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		QuaternionBridge::PushQuaternion(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<Quaternion>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		/*char szTemp[128];
		sprintf(szTemp, "{%f, %f, %f, %f}", v.x, v.y, v.z, v.w);
		return szTemp;*/
		return ToString("{", v.x, ", ", v.y, ", ", v.z, ", ", v.w, "}");
	}
	template<>
	size_t ReflexPolicyFunc<Quaternion>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.x);
		len += out->WriteNumber<float>(v.y);
		len += out->WriteNumber<float>(v.z);
		len += out->WriteNumber<float>(v.w);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<Quaternion>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.x);
		ret &= in->ReadNumber<float>(v.y);
		ret &= in->ReadNumber<float>(v.z);
		ret &= in->ReadNumber<float>(v.w);
		return ret;
	}
	RegisterReflexTypePolicyClass(Quaternion, REFLEXTYPEENUM_QUATERNION, ReflexPolicyFunc<Quaternion>);

	// WCoord
	template<>
	void ReflexPolicyFunc<WCoord>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.x << (MNJsonNumber)v.y << (MNJsonNumber)v.z;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<WCoord>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2))
		{
			v.x = (int)jsona.get<MNJsonNumber>(0);
			v.y = (int)jsona.get<MNJsonNumber>(1);
			v.z = (int)jsona.get<MNJsonNumber>(2);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	bool ReflexPolicyFunc<WCoord>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *WCoordBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<WCoord>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		WCoordBridge::PushWCoord(L, Data(data));
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<WCoord>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%d, %d, %d}", v.x, v.y, v.z);
		//return szTemp;
		return ToString("{", v.x, ", ", v.y, ", ", v.z,"}");
	}
	template<>
	size_t ReflexPolicyFunc<WCoord>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<int>(v.x);
		len += out->WriteNumber<int>(v.y);
		len += out->WriteNumber<int>(v.z);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<WCoord>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<int>(v.x);
		ret &= in->ReadNumber<int>(v.y);
		ret &= in->ReadNumber<int>(v.z);
		return ret;
	}
	RegisterReflexTypePolicyClass(WCoord, REFLEXTYPEENUM_WCOORD, ReflexPolicyFunc<WCoord>);

	// MINIW::ColourValue
		// MINIW::ColourValue
	typedef Rainbow::ColourValue colour_value;
	template<>
	void ReflexPolicyFunc<colour_value>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.r << (MNJsonNumber)v.g << (MNJsonNumber)v.b << (MNJsonNumber)v.a;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<colour_value>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1)
			&& jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3))
		{
			v.r = jsona.get<MNJsonNumber>(0);
			v.g = jsona.get<MNJsonNumber>(1);
			v.b = jsona.get<MNJsonNumber>(2);
			v.a = jsona.get<MNJsonNumber>(3);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<colour_value>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f, %f, %f}", v.r, v.g, v.b, v.a);
		//return szTemp;
		return ToString("{", v.r, ", ", v.g, ", ", v.b, ", ", v.a, "}");
	}
	template<>
	bool ReflexPolicyFunc<colour_value>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *ColorValueBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<colour_value>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		ColorValueBridge::PushColorValue(L, Data(data));
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<colour_value>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v.r);
		len += out->WriteNumber<float>(v.g);
		len += out->WriteNumber<float>(v.b);
		len += out->WriteNumber<float>(v.a);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<colour_value>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v.r);
		ret &= in->ReadNumber<float>(v.g);
		ret &= in->ReadNumber<float>(v.b);
		ret &= in->ReadNumber<float>(v.a);
		return ret;
	}
	RegisterReflexTypePolicyClass(colour_value, REFLEXTYPEENUM_COLOURVAL, ReflexPolicyFunc<colour_value>);
	
	// Rainbow::ColorQuad
	typedef Rainbow::ColorQuad colour_quad;
	template<>
	void ReflexPolicyFunc<colour_quad>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.r << (MNJsonNumber)v.g << (MNJsonNumber)v.b << (MNJsonNumber)v.a;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<colour_quad>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1)
			&& jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3))
		{
			v.r = jsona.get<MNJsonNumber>(0);
			v.g = jsona.get<MNJsonNumber>(1);
			v.b = jsona.get<MNJsonNumber>(2);
			v.a = jsona.get<MNJsonNumber>(3);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<colour_quad>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%c, %c, %c, %c}", v.r, v.g, v.b, v.a);
		//return szTemp;
		return ToString("{", v.r, ", ", v.g, ", ", v.b, ", ", v.a, "}");
	}
	template<>
	bool ReflexPolicyFunc<colour_quad>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *ColorQuadBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<colour_quad>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		ColorQuadBridge::PushColorValue(L, Data(data));
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<colour_quad>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<unsigned char>(v.r);
		len += out->WriteNumber<unsigned char>(v.g);
		len += out->WriteNumber<unsigned char>(v.b);
		len += out->WriteNumber<unsigned char>(v.a);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<colour_quad>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<unsigned char>(v.r);
		ret &= in->ReadNumber<unsigned char>(v.g);
		ret &= in->ReadNumber<unsigned char>(v.b);
		ret &= in->ReadNumber<unsigned char>(v.a);
		return ret;
	}
	RegisterReflexTypePolicyClass(colour_quad, REFLEXTYPEENUM_COLOURQUAD, ReflexPolicyFunc<colour_quad>);

	// MINIW::TweenInfo
	typedef MINIW::TweenInfo tweeninfo;
	using ReflexPolicyFunc_tweeninfo = ReflexPolicyFunc<tweeninfo>; //ReflexPolicyFunc<tweeninfo, ReflexPolicyDataCustom<tweeninfo>>;
	template<>
	void ReflexPolicyFunc_tweeninfo::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.m_nEasingDirection << (MNJsonNumber)v.m_fTime << (MNJsonNumber)v.m_fDelayTime << (MNJsonNumber)v.m_nRepeatCount << (MNJsonNumber)v.m_nEasingStyle<< (MNJsonNumber)(v.m_bReverses?1:0);
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc_tweeninfo::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1)
			&& jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3) 
			&& jsona.has<MNJsonNumber>(4) && jsona.has<MNJsonNumber>(5))
		{
			v.m_nEasingDirection = jsona.get<MNJsonNumber>(0);
			v.m_fTime = jsona.get<MNJsonNumber>(1);
			v.m_fDelayTime = jsona.get<MNJsonNumber>(2);
			v.m_nRepeatCount = jsona.get<MNJsonNumber>(3);
			v.m_nEasingStyle = jsona.get<MNJsonNumber>(4);
			v.m_bReverses = (bool)jsona.get<MNJsonNumber>(5);
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc_tweeninfo::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%d, %f, %f, %d, %d, %d}", v.m_nEasingDirection, v.m_fTime, v.m_fDelayTime, v.m_nRepeatCount, v.m_nEasingStyle, v.m_bReverses);
		//return szTemp;
		return ToString("{", v.m_nEasingDirection, ", ", v.m_fTime, ", ", v.m_fDelayTime, ", ", v.m_nRepeatCount, ", ", v.m_nEasingStyle, ", ", v.m_bReverses, "}");
	}
	template<>
	bool ReflexPolicyFunc_tweeninfo::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *TweenInfoBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc_tweeninfo::CallbackLuaPushC(const void* data, lua_State* L)
	{
		TweenInfoBridge::PushTweenInfo(L, Data(data));
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc_tweeninfo::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<int>(v.m_nEasingDirection);
		len += out->WriteNumber<float>(v.m_fTime);
		len += out->WriteNumber<float>(v.m_fDelayTime);
		len += out->WriteNumber<int>(v.m_nRepeatCount);
		len += out->WriteNumber<int>(v.m_nEasingStyle);
		len += out->WriteBool(v.m_bReverses);
		return len;
	}
	template<>
	bool ReflexPolicyFunc_tweeninfo::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<int>(v.m_nEasingDirection);
		ret &= in->ReadNumber<float>(v.m_fTime);
		ret &= in->ReadNumber<float>(v.m_fDelayTime);
		ret &= in->ReadNumber<int>(v.m_nRepeatCount);
		ret &= in->ReadNumber<int>(v.m_nEasingStyle);
		ret &= in->ReadBool(v.m_bReverses);
		return ret;
	}
	RegisterReflexTypePolicyClass(tweeninfo, REFLEXTYPEENUM_TWEENINFO, ReflexPolicyFunc_tweeninfo);

	// RangeInfo
	typedef MNSandbox::RangeInfo rangeinfo;
	template<>
	void ReflexPolicyFunc<rangeinfo>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._min << (MNJsonNumber)v._max;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<rangeinfo>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1))
		{
			v._min = jsona.get<MNJsonNumber>(0);
			v._max = jsona.get<MNJsonNumber>(1);
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<rangeinfo>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		//char szTemp[128];
		//sprintf(szTemp, "{%f, %f}", v._min, v._max);
		//return szTemp;
		return ToString("{", v._min, ", ", v._max, "}");
	}
	template<>
	bool ReflexPolicyFunc<rangeinfo>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *RangeInfoBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<rangeinfo>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		RangeInfoBridge::PushRangeInfo(L, Data(data));
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<rangeinfo>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<float>(v._min);
		len += out->WriteNumber<float>(v._max);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<rangeinfo>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<float>(v._min);
		ret &= in->ReadNumber<float>(v._max);
		return ret;
	}
	RegisterReflexTypePolicyClass(rangeinfo, REFLEXTYPEENUM_RANGEINFO, ReflexPolicyFunc<rangeinfo>);


	// PairVector3fInfo
	typedef MNSandbox::PairVector3fInfo pairvector3finfo;
	template<>
	void ReflexPolicyFunc<pairvector3finfo>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._valuefrist.x << (MNJsonNumber)v._valuefrist.y << (MNJsonNumber)v._valuefrist.z
			<< (MNJsonNumber)v._valuesecond.x << (MNJsonNumber)v._valuesecond.y << (MNJsonNumber)v._valuesecond.z;
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<pairvector3finfo>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) && jsona.has<MNJsonNumber>(2) && jsona.has<MNJsonNumber>(3) && jsona.has<MNJsonNumber>(4) && jsona.has<MNJsonNumber>(5))
		{
			v._valuefrist.x = jsona.get<MNJsonNumber>(0);
			v._valuefrist.y = jsona.get<MNJsonNumber>(1);
			v._valuefrist.z = jsona.get<MNJsonNumber>(2);
			v._valuesecond.x = jsona.get<MNJsonNumber>(3);
			v._valuesecond.y = jsona.get<MNJsonNumber>(4);
			v._valuesecond.z = jsona.get<MNJsonNumber>(5);
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<pairvector3finfo>::CallbackToString(const void* data)
	{
		auto& v = Data(data);

		return ToString("{", v._valuefrist.x, ", ", v._valuefrist.y, ", ", v._valuefrist.z, ", ", v._valuesecond.x, ", ", v._valuesecond.y, ", ", v._valuesecond.z, "}");
	}
	template<>
	bool ReflexPolicyFunc<pairvector3finfo>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<pairvector3finfo>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 0;
	}
	template<>
	size_t ReflexPolicyFunc<pairvector3finfo>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		AddPairVector3fInfo(v);
		static int count = 0;
		count++;
		if(count >= 1000)
		{
			PrintTop100PairVector3Info();
			count = 0;
		}
#endif
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		int specialType = isSpecialDefaultPairVector3fInfo(v);
		len += out->WriteNumber<int>(specialType);
		if (specialType == 0)
		{
#endif
			len += out->WriteNumber<float>(v._valuefrist.x);
			len += out->WriteNumber<float>(v._valuefrist.y);
			len += out->WriteNumber<float>(v._valuefrist.z);
			len += out->WriteNumber<float>(v._valuesecond.x);
			len += out->WriteNumber<float>(v._valuesecond.y);
			len += out->WriteNumber<float>(v._valuesecond.z);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: Pairvector3finfo ReflexToBinary ", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<pairvector3finfo>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		int specialType = 0;
		if (in->GetVersion() >= Config::ToVersion(0, 0, 22))
		{
			ret &= in->ReadNumber<int>(specialType);
		}
#endif
		if(specialType == 0)
		{
			ret &= in->ReadNumber<float>(v._valuefrist.x);
			ret &= in->ReadNumber<float>(v._valuefrist.y);
			ret &= in->ReadNumber<float>(v._valuefrist.z);
			ret &= in->ReadNumber<float>(v._valuesecond.x);
			ret &= in->ReadNumber<float>(v._valuesecond.y);
			ret &= in->ReadNumber<float>(v._valuesecond.z);
		}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		if(specialType == 1)
		{
			v = GetDefaultPairVector3fInfo1();
		}
		else if(specialType == 2)
		{
			v = GetDefaultPairVector3fInfo2();
		}
		else if(specialType == 3)
		{
			v = GetDefaultPairVector3fInfo3();
		}
		else if(specialType == 4)
		{
			v = GetDefaultPairVector3fInfo4();
		}
#endif

#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: RangeFloatCurve ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return ret;
	}
	RegisterReflexTypePolicyClass(pairvector3finfo, REFLEXTYPEENUM_PAIRVECTOR3FINFO, ReflexPolicyFunc<pairvector3finfo>);


	typedef EDITORUI::Button autoref_Button;
	template<>
	void ReflexPolicyFunc<autoref_Button>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		out.import(MNJsonNull());
	}
	template<>
	bool ReflexPolicyFunc<autoref_Button>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		return in.is<MNJsonNull>();
	}
	template<>
	bool ReflexPolicyFunc<autoref_Button>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return lua_isnil(L, objindex);
	}
	template<>
	int ReflexPolicyFunc<autoref_Button>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		lua_pushnil(L);
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<autoref_Button>::CallbackToString(const void* data)
	{
		return "void";
	}
	template<>
	size_t ReflexPolicyFunc<autoref_Button>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		return 0;
	}
	template<>
	bool ReflexPolicyFunc<autoref_Button>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		return true;
	}
	RegisterReflexTypePolicyClass(autoref_Button, REFLEXTYPEENUM_AUTOREF_BUTTON, ReflexPolicyFunc<autoref_Button>);



	typedef EDITORUI::ButtonToggle autoref_ButtonToggle;
	template<>
	void ReflexPolicyFunc<autoref_ButtonToggle>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		out.import((MNJsonBool)Data(data).m_toggle);
	}
	template<>
	bool ReflexPolicyFunc<autoref_ButtonToggle>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (in.is<MNJsonBool>())
		{
			Data(data).m_toggle = in.get<MNJsonBool>();
			return true;
		}
		return false;
	}
	template<>
	bool ReflexPolicyFunc<autoref_ButtonToggle>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		int luatype = lua_type(L, objindex);
		switch (luatype)
		{
		case LUA_TNIL:
			Data(data).m_toggle = false;
			return true;
		case LUA_TBOOLEAN:
			Data(data).m_toggle = lua_toboolean(L, objindex);
			return true;
		default:
			SANDBOX_ASSERT("nonsupport type!");
			return false;
		}
	}
	template<>
	int ReflexPolicyFunc<autoref_ButtonToggle>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		lua_pushboolean(L, Data(data).m_toggle);
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<autoref_ButtonToggle>::CallbackToString(const void* data)
	{
		return Data(data).m_toggle ? "true" : "false";
	}
	template<>
	size_t ReflexPolicyFunc<autoref_ButtonToggle>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteBool(v.m_toggle);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<autoref_ButtonToggle>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadBool(v.m_toggle);
		return ret;
	}
	RegisterReflexTypePolicyClass(autoref_ButtonToggle, REFLEXTYPEENUM_AUTOREF_BUTTONTOGGLE, ReflexPolicyFunc<autoref_ButtonToggle>);

	RegisterExtendDataReflex(std::string, REFLEXTYPEENUM_STRING_FILEMODEL, EDITORUI::ReflexSpecialType, EDITORUI::ReflexSpecialType::FILE_MODEL);
	RegisterExtendDataReflex(std::string, REFLEXTYPEENUM_STRING_FILETEX, EDITORUI::ReflexSpecialType, EDITORUI::ReflexSpecialType::FILE_TEXTRUE);

	
	typedef AutoRef<CustomBuffer> autoref_cb;
	template<>
	void ReflexPolicySerialize<autoref_cb>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		if (v)
			out.import(v->Data<unsigned char>(), (int)v->Size());
	}
	template<>
	bool ReflexPolicySerialize<autoref_cb>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& v = Data(data);
		if (in.isBinary())
		{
			v = CustomBuffer::CreateGlobal(in.bin(), in.binLen());
			return true;
		}
		else if (in.type_ == MNJsonVal::NULL_ || in.type_ == MNJsonVal::INVALID_)
		{
			v = nullptr;
			return true;
		}
		return false;
	}
	template<>
	size_t ReflexPolicySerialize<autoref_cb>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		return out->WriteBuffer(v);
	}
	template<>
	bool ReflexPolicySerialize<autoref_cb>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		return in->ReadBuffer(v);
	}
	RegisterReflexTypePolicyClass(autoref_cb, REFLEXTYPEENUM_AUTOREF_CUSTOMBUFFER, ReflexPolicySerializeOnlyReg<autoref_cb>);

	// ParticleEmitterColorGradient
	typedef MNSandbox::ParticleEmitterColorGradient colorGradient;
	template<>
	void ReflexPolicyFunc<colorGradient>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._mode << (MNJsonNumber)v._numColorKeys << (MNJsonNumber)v._numAlphaKeys;
		for (int i = 0; i < 20; i++)
		{
			jsonarray << (MNJsonNumber)v._colors[i].r << (MNJsonNumber)v._colors[i].g << (MNJsonNumber)v._colors[i].b << (MNJsonNumber)v._colors[i].a
				<< (MNJsonNumber)v._colorTimes[i] << (MNJsonNumber)v._alphaTimes[i];
		}
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<colorGradient>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0))
		{
			v._mode = jsona.get<MNJsonNumber>(0);
			v._numColorKeys = jsona.get<MNJsonNumber>(1);
			v._numAlphaKeys = jsona.get<MNJsonNumber>(2);
			int index = 2;
			for (int i = 0; i < 20; i++)
			{
				v._colors[i].r = jsona.get<MNJsonNumber>(++index);
				v._colors[i].g = jsona.get<MNJsonNumber>(++index);
				v._colors[i].b = jsona.get<MNJsonNumber>(++index);
				v._colors[i].a = jsona.get<MNJsonNumber>(++index);
				v._colorTimes[i] = jsona.get<MNJsonNumber>(++index);
				v._alphaTimes[i] = jsona.get<MNJsonNumber>(++index);
			}
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<colorGradient>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		std::string str("{");
		str += ToString(v._mode, ", ", v._numColorKeys, ", ", v._numAlphaKeys);
		for (int i = 0; i < 20; i++)
		{
			str += ToString(", ", v._colors[i].r, ", ", v._colors[i].g, ", ", v._colors[i].b, ", ", v._colors[i].a, ", ", v._colorTimes[i], ", ", v._alphaTimes[i]);
		}
		str += "}";
		return str;
	}
	template<>
	bool ReflexPolicyFunc<colorGradient>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<colorGradient>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<colorGradient>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		AddParticleEmitterColorGradient(v);
		static int count = 0;
		count++;
		if(count >= 1000)
		{
			PrintTop100ParticleEmitterColorGradient();
			count = 0;
		}
#endif
		
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		auto specialType = isSpecialDefaultParticleEmitterColorGradient(v);
		if(specialType == 0)
		{
#endif
			len += out->WriteNumber<UInt8>(v._mode);
			len += out->WriteNumber<UInt8>(v._numColorKeys);
			len += out->WriteNumber<UInt8>(v._numAlphaKeys);
			for (int i = 0; i < 20; i++)
			{
				len += out->WriteNumber<UInt8>(v._colors[i].r);
				len += out->WriteNumber<UInt8>(v._colors[i].g);
				len += out->WriteNumber<UInt8>(v._colors[i].b);
				len += out->WriteNumber<UInt8>(v._colors[i].a);
				len += out->WriteNumber<UInt16>(v._colorTimes[i]);
				len += out->WriteNumber<UInt16>(v._alphaTimes[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			len += out->WriteNumber<UInt8>(0xF + specialType);
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: ColorGradient ReflexToBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<colorGradient>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<UInt8>(v._mode);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		bool isSpecial = v._mode > 0xF;
#endif
		if(v._mode <= 0xF)
		{
#endif
			ret &= in->ReadNumber<UInt8>(v._numColorKeys);
			ret &= in->ReadNumber<UInt8>(v._numAlphaKeys);
			for (int i = 0; i < 20; i++)
			{
				ret &= in->ReadNumber<UInt8>(v._colors[i].r);
				ret &= in->ReadNumber<UInt8>(v._colors[i].g);
				ret &= in->ReadNumber<UInt8>(v._colors[i].b);
				ret &= in->ReadNumber<UInt8>(v._colors[i].a);
				ret &= in->ReadNumber<UInt16>(v._colorTimes[i]);
				ret &= in->ReadNumber<UInt16>(v._alphaTimes[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			auto specialType = v._mode - 0xF;
			if(specialType == 1)
			{
				v = defaultParticleEmitterColorGradient1;
			}
			else if(specialType == 2)
			{
				v = defaultParticleEmitterColorGradient2;
			}
			else if (specialType == 3)
			{
				v = GetDefaultParticleEmitterColorGradient3();
			}
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = isSpecial ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: ColorGradient ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return ret;
	}
	RegisterReflexTypePolicyClass(colorGradient, REFLEXTYPEENUM_PARTICLEEMITTER_COLORGRADIENT, ReflexPolicyFunc<colorGradient>);

	
	// ParticleEmissionBurst
	typedef MNSandbox::ParticleEmissionBurst emissionBurst;
	template<>
	void ReflexPolicyFunc<emissionBurst>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._curCount;
		for (int i = 0; i < 8; i++)
		{
			jsonarray << (MNJsonNumber)v._times[i] << (MNJsonNumber)v._minCounts[i] << (MNJsonNumber)v._maxCounts[i] << (MNJsonNumber)v._cycles[i]
				<< (MNJsonNumber)v._intervals[i] << (MNJsonNumber)v._probabilitys[i];
		}
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<emissionBurst>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0))
		{
			v._curCount = jsona.get<MNJsonNumber>(0);
			int index = 0;
			for (int i = 0; i < 8; i++)
			{
				v._times[i] = jsona.get<MNJsonNumber>(++index);
				v._minCounts[i] = jsona.get<MNJsonNumber>(++index);
				v._maxCounts[i] = jsona.get<MNJsonNumber>(++index);
				v._cycles[i] = jsona.get<MNJsonNumber>(++index);
				v._intervals[i] = jsona.get<MNJsonNumber>(++index);
				v._probabilitys[i] = jsona.get<MNJsonNumber>(++index);
			}
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<emissionBurst>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		std::string str("{");
		for (int i = 0; i < v._curCount; i++)
		{
			str += ToString("[", v._times[i], ", ", v._minCounts[i], ", ", v._maxCounts[i], ", ", v._cycles[i], ", ", v._intervals[i], ", ", v._probabilitys[i], "]");
		}
		str += "}";
		return str;
	}
	template<>
	bool ReflexPolicyFunc<emissionBurst>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<emissionBurst>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<emissionBurst>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		AddParticleEmissionBurst(v);
		static int count = 0;
		count++;
		if(count >= 100)
		{
			PrintTop100ParticleEmissionBurst();
			count = 0;
		}
#endif
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		auto specialType = isSpecialParticleEmissionBurst(v);
		if(specialType == 0)
		{
#endif
			len += out->WriteNumber<UInt8>(v._curCount);
			for (int i = 0; i < 8; i++)
			{
				len += out->WriteNumber<float>(v._times[i]);
				len += out->WriteNumber<UInt16>(v._minCounts[i]);
				len += out->WriteNumber<UInt16>(v._maxCounts[i]);
				len += out->WriteNumber<UInt16>(v._cycles[i]);
				len += out->WriteNumber<float>(v._intervals[i]);
				len += out->WriteNumber<float>(v._probabilitys[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			len += out->WriteNumber<UInt8>(0xF0 + specialType);
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: EmissionBurst ReflexToBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<emissionBurst>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<UInt8>(v._curCount);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		bool isSpecial = v._curCount > 0xF0;
#endif
		if(v._curCount <= 0xF0)
		{
#endif
			for (int i = 0; i < 8; i++)
			{
				ret &= in->ReadNumber<float>(v._times[i]);
				ret &= in->ReadNumber<UInt16>(v._minCounts[i]);
				ret &= in->ReadNumber<UInt16>(v._maxCounts[i]);
				ret &= in->ReadNumber<UInt16>(v._cycles[i]);
				ret &= in->ReadNumber<float>(v._intervals[i]);
				ret &= in->ReadNumber<float>(v._probabilitys[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			int specialType = v._curCount - 0xF0;
			if(specialType == 1 || specialType == 0xF)
			{
				v = GetDefaultParticleEmissionBurst1();
			}
			else if(specialType == 2)
			{
				v = GetDefaultParticleEmissionBurst2();
			}
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = isSpecial ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: EmissionBurst ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return ret;
	}
	RegisterReflexTypePolicyClass(emissionBurst, REFLEXTYPEENUM_PARTICLE_EMISSIONBURST, ReflexPolicyFunc<emissionBurst>);


	// ParticleVertexStreams
	typedef MNSandbox::ParticleVertexStreams vertexStreams;
	template<>
	void ReflexPolicyFunc<vertexStreams>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._numSize;
		for (int i = 0; i < 46; i++)
		{
			jsonarray << (MNJsonNumber)v._values[i];
		}
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<vertexStreams>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0))
		{
			v._numSize = jsona.get<MNJsonNumber>(0);
			int index = 0;
			for (int i = 0; i < 46; i++)
			{
				v._values[i] = jsona.get<MNJsonNumber>(++index);
			}
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<vertexStreams>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		std::string str("{");
		for (int i = 0; i < v._numSize; i++)
		{
			str += ToString(v._values[i]);
			if (i < v._numSize - 1)
			{
				str += ",";
			}
		}
		str += "}";
		return str;
	}
	template<>
	bool ReflexPolicyFunc<vertexStreams>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<vertexStreams>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<vertexStreams>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		auto specialType = isSpecialDefaultParticleVertexStreams(v);
		if(specialType == 0)
		{
#endif
			len += out->WriteNumber<UInt8>(v._numSize);
			for (int i = 0; i < 46; i++)
			{
				len += out->WriteNumber<UInt16>(v._values[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			len += out->WriteNumber<UInt8>(46 + specialType);
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: VertexStreams ReflexToBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<vertexStreams>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<UInt8>(v._numSize);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		bool isSpecial = v._numSize > 46;
#endif
		if(v._numSize <= 46)
		{
#endif
			for (int i = 0; i < 46; i++)
			{
				ret &= in->ReadNumber<UInt16>(v._values[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			int specialType = v._numSize - 46;
			if(specialType == 1)
			{
				v = defaultParticleVertexStreams1;
			}
			else if(specialType == 2)
			{
				v = defaultParticleVertexStreams2;
			}
			else if (specialType == 3)
			{
				v = GetDefaultParticleVertexStreams3();
			}
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = isSpecial ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: VertexStreams ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		
		return ret;
	}
	RegisterReflexTypePolicyClass(vertexStreams, REFLEXTYPEENUM_PARTICLE_VERTEXSTREAMS, ReflexPolicyFunc<vertexStreams>);


	// ParticleSubEmitterData
	typedef MNSandbox::ParticleSubEmitterData subEmitterData;
	template<>
	void ReflexPolicyFunc<subEmitterData>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v._numSize;
		for (int i = 0; i < v._numSize; i++)
		{
			jsonarray << (MNJsonNumber)v._conditions[i] << (MNJsonNumber)v._childIndexs[i] << (MNJsonNumber)v._inherits[i] << (MNJsonNumber)v._emitProbabilitys[i];
		}
		out.import(jsonarray);
	}
	template<>
	bool ReflexPolicyFunc<subEmitterData>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0))
		{
			v._numSize = jsona.get<MNJsonNumber>(0);

			v._conditions.resize(v._numSize);
			v._childIndexs.resize(v._numSize);
			v._inherits.resize(v._numSize);
			v._emitProbabilitys.resize(v._numSize);

			int index = 0;
			for (int i = 0; i < v._numSize; i++)
			{
				v._conditions[i] = jsona.get<MNJsonNumber>(++index);
				v._childIndexs[i] = jsona.get<MNJsonNumber>(++index);
				v._inherits[i] = jsona.get<MNJsonNumber>(++index);
				v._emitProbabilitys[i] = jsona.get<MNJsonNumber>(++index);
			}
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<subEmitterData>::CallbackToString(const void* data)
	{
		return "";
	}
	template<>
	bool ReflexPolicyFunc<subEmitterData>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<subEmitterData>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<subEmitterData>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		int specialType = isSpecialDefaultParticleSubEmitterData(v);
		if(specialType == 0)
		{
#endif
			len += out->WriteNumber<UInt8>(v._numSize);
			for (int i = 0; i < v._numSize; i++)
			{
				len += out->WriteNumber<UInt8>(v._conditions[i]);
				len += out->WriteReflex<int>(v._childIndexs[i]);
				len += out->WriteNumber<int>(v._inherits[i]);
				len += out->WriteNumber<float>(v._emitProbabilitys[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			len += out->WriteNumber<UInt8>(0xFF);
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: SubEmitterData ReflexToBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<subEmitterData>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<UInt8>(v._numSize);
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		bool isSpecial = v._numSize != 0xFF;
#endif
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		if(v._numSize != 0xFF)
		{
#endif
			v._conditions.resize(v._numSize);
			v._childIndexs.resize(v._numSize);
			v._inherits.resize(v._numSize);
			v._emitProbabilitys.resize(v._numSize);

			for (int i = 0; i < v._numSize; i++)
			{
				ret &= in->ReadNumber<UInt8>(v._conditions[i]);
				ret &= in->ReadReflex<int>(v._childIndexs[i]);
				ret &= in->ReadNumber<int>(v._inherits[i]);
				ret &= in->ReadNumber<float>(v._emitProbabilitys[i]);
			}
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			v = defaultParticleSubEmitterData;
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = isSpecial ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: SubEmitterData ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return ret;
	}
	RegisterReflexTypePolicyClass(subEmitterData, REFLEXTYPEENUM_PARTICLE_SUBEMITTERDATA, ReflexPolicyFunc<subEmitterData>);
	
	//SceneConfig
	template<>
	void ReflexPolicyFunc<SceneConfig>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray jsonarray;
		jsonarray << (MNJsonNumber)v.sceneid << (MNJsonStr)v.name;
		out.import(jsonarray);
	}

	template<>
	bool ReflexPolicyFunc<SceneConfig>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonStr>(1))
		{
			v.sceneid = jsona.get<MNJsonNumber>(0);
			v.name = jsona.get<MNJsonStr>(1);
			return true;
		}
		SANDBOX_ASSERT(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<SceneConfig>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		return ToString("{", v.sceneid, ", ", v.name,  "}");
	}
	template<>
	bool ReflexPolicyFunc<SceneConfig>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		if (lua_isnil(L, objindex))
			return false;
		Data(data) = *SceneConfigBridge::GetObject(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<SceneConfig>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		SceneConfigBridge::PushSceneConfig(L, Data(data));
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<SceneConfig>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<unsigned short>(v.sceneid);
		len += out->WriteString(v.name);
		return len;
	}
	template<>
	bool ReflexPolicyFunc<SceneConfig>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;
		ret &= in->ReadNumber<unsigned short>(v.sceneid);
		ret &= in->ReadString(v.name);
		return ret;
	}
	RegisterReflexTypePolicyClass(SceneConfig, REFLEXTYPEENUM_ENUM_SCENECONFIG, ReflexPolicyFunc<SceneConfig>);

	//Coord3<int>
#define REG_REFLEXTYPEPOLICY_COORD3(Type, TEnum) \
	template<> \
	void ReflexPolicySerialize<Coord3<Type>>::CallbackSerialize(const void* data, MNJsonVal& out) \
	{ \
		auto& v = Data(data); \
		MNJsonArray jsonarray; \
		jsonarray << (MNJsonNumber)v.GetX() << (MNJsonNumber)v.GetY() << (MNJsonNumber)v.GetZ(); \
		out.import(jsonarray); \
	} \
	template<> \
	bool ReflexPolicySerialize<Coord3<Type>>::CallbackUnserialize(void* data, const MNJsonVal& in) \
	{ \
		if (!in.is<MNJsonArray>()) \
			return false; \
		auto& jsona = in.get<MNJsonArray>(); \
		auto& v = Data(data); \
		if (jsona.has<MNJsonNumber>(0) && jsona.has<MNJsonNumber>(1) \
			&& jsona.has<MNJsonNumber>(2)) \
		{ \
			v.SetX((Type)jsona.get<MNJsonNumber>(0)); \
			v.SetY((Type)jsona.get<MNJsonNumber>(1)); \
			v.SetZ((Type)jsona.get<MNJsonNumber>(2)); \
			return true; \
		} \
		SANDBOX_ASSERT(false); \
		return false; \
	} \
	template<> \
	std::string ReflexPolicyToString<Coord3<Type>>::CallbackToString(const void* data) \
	{ \
		auto& v = Data(data); \
		return ToString("{", v.GetX(), ", ", v.GetY(), ", ", v.GetZ(), "}"); \
	} \
	template<> \
	size_t ReflexPolicySerialize<Coord3<Type>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out) \
	{ \
		auto& v = Data(data); \
		size_t len = 0; \
		len += out->WriteNumber<Type>(v.GetX()); \
		len += out->WriteNumber<Type>(v.GetY()); \
		len += out->WriteNumber<Type>(v.GetZ()); \
		return len; \
	} \
	template<> \
	bool ReflexPolicySerialize<Coord3<Type>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in) \
	{ \
		auto& v = Data(data); \
		bool ret = true; \
		Type num; \
		ret &= in->ReadNumber<Type>(num); v.SetX(num); \
		ret &= in->ReadNumber<Type>(num); v.SetY(num); \
		ret &= in->ReadNumber<Type>(num); v.SetZ(num); \
		return ret; \
	} \
	RegisterReflexTypePolicyClass(Coord3<Type>, TEnum, ReflexPolicySerializeReg<Coord3<Type>>) \
// end REG_REFLEXTYPEPOLICY_COORD3

	REG_REFLEXTYPEPOLICY_COORD3(int, REFLEXTYPEENUM_COORD3_INT);
	REG_REFLEXTYPEPOLICY_COORD3(float, REFLEXTYPEENUM_COORD3_FLOAT);

	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


	// number
	REFLEXTYPE_CONTAINER_POLICY(std::vector<char>, REFLEXTYPEENUM_VECTOR_CHAR, REFLEXTYPEENUM_VECTOR_CHAR_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<unsigned char>, REFLEXTYPEENUM_VECTOR_UNCHAR, REFLEXTYPEENUM_VECTOR_UNCHAR_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<short>, REFLEXTYPEENUM_VECTOR_SHORT, REFLEXTYPEENUM_VECTOR_SHORT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<unsigned short>, REFLEXTYPEENUM_VECTOR_UNSHORT, REFLEXTYPEENUM_VECTOR_UNSHORT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<int>, REFLEXTYPEENUM_VECTOR_INT, REFLEXTYPEENUM_VECTOR_INT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<unsigned int>, REFLEXTYPEENUM_VECTOR_UNINT, REFLEXTYPEENUM_VECTOR_UNINT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<long>, REFLEXTYPEENUM_VECTOR_LONG, REFLEXTYPEENUM_VECTOR_LONG_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<unsigned long>, REFLEXTYPEENUM_VECTOR_UNLONG, REFLEXTYPEENUM_VECTOR_UNLONG_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<long long>, REFLEXTYPEENUM_VECTOR_LONGLONG, REFLEXTYPEENUM_VECTOR_LONGLONG_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<unsigned long long>, REFLEXTYPEENUM_VECTOR_UNLONGLONG, REFLEXTYPEENUM_VECTOR_UNLONGLONG_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<float>, REFLEXTYPEENUM_VECTOR_FLOAT, REFLEXTYPEENUM_VECTOR_FLOAT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<double>, REFLEXTYPEENUM_VECTOR_DOUBLE, REFLEXTYPEENUM_VECTOR_DOUBLE_PTR);
	//REFLEXTYPE_CONTAINER_POLICY(std::vector<long double>, REFLEXTYPEENUM_VECTOR_LONGDOUBLE_PTR);

	REFLEXTYPE_CONTAINER_POLICY(std::vector<std::string>, REFLEXTYPEENUM_VECTOR_STRING, REFLEXTYPEENUM_VECTOR_STRING_PTR);
	//REFLEXTYPE_CONTAINER_POLICY(std::vector<bool>, REFLEXTYPEENUM_VECTOR_BOOL, REFLEXTYPEENUM_VECTOR_BOOL_PTR);

	// TIP: 以上是默认的一系列类型定义，其他子工程拓展的类型，可以直接定义在子工程
	REFLEXTYPE_CONTAINER_POLICY(std::vector<ReflexVariant>, REFLEXTYPEENUM_VECTOR_VARIANT, REFLEXTYPEENUM_VECTOR_VARIANT_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<colour_quad>, REFLEXTYPEENUM_VECTOR_COLOURQUAD, REFLEXTYPEENUM_VECTOR_COLOURQUAD_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<colour_value>, REFLEXTYPEENUM_VECTOR_COLOURVALUE, REFLEXTYPEENUM_VECTOR_COLOURVALUE_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<AutoRef<SandboxNode>>, REFLEXTYPEENUM_VECTOR_NODEREF, REFLEXTYPEENUM_VECTOR_NODEREF_PTR);

	REFLEXTYPE_CONTAINER_POLICY(std::vector<Rainbow::Vector3f>, REFLEXTYPEENUM_VECTOR_VECTOR3, REFLEXTYPEENUM_VECTOR_VECTOR3_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<AutoRef<ReflexMap>>, REFLEXTYPEENUM_VECTOR_REFLEXMAPREF, REFLEXTYPEENUM_VECTOR_REFLEXMAPREF_PTR);
	REFLEXTYPE_CONTAINER_POLICY(std::vector<AutoRef<ReflexTuple>>, REFLEXTYPEENUM_VECTOR_REFLEXTUPLE, REFLEXTYPEENUM_VECTOR_REFLEXTUPLE_PTR);

	REFLEXTYPE_CONTAINER_POLICY(std::vector<SceneConfig>, REFLEXTYPEENUM_VECTOR_SCENECONFIG, REFLEXTYPEENUM_VECTOR_SCENECONFIG_PTR);
	// 注册Map 反射，如 std::map, std::unordered_map 等
	// 示例：
	// using TestMap = std::map<int, std::string>;
	// REFLEXTYPE_MAP_POLICY(TestMap, REFLEXTYPEENUM_MAP_XXXXXX, REFLEXTYPEENUM_MAP_XXXXXX_PTR);


}