#pragma once
#include <Math/Color.h>

#include "Serialize/SerializeUtility.h"
#include "Utilities/dynamic_array.h"

namespace Rainbow
{
	struct NodeColor 
	{
		DECLARE_SERIALIZE(NodeColor)
		float r, g, b, a;
	};

	struct NodeInfo
	{
		DECLARE_SERIALIZE(NodeInfo)
		//<!--softness-->
		float nodeCoverage;
		float nodeSoftness;
		float nodeCloudAlpha;
		//<!--skyColor-->
		NodeColor nodeTopColor;
		NodeColor nodeMidColor;
		NodeColor nodesBottomColor;
		float nodeYPosScale1;
		float nodeYPosScale2;
		//<!--SunLight-->
		float nodeSunLightIntensity;
		float nodeSunLightSize;
		float nodeSunLightAlpha;
		float nodeSunLightPower;
		float nodeSunRimPower;
		float nodeSunRimRange;
		//<!--MoonLight-->
		float nodeMoonLightIntensity;
		float nodemoonLightSize;
		float nodemoonLightAlpha;
		float nodemoonLightPower;
		float nodemoonRimPower;
		float nodemoonRimRange;
		//<!--SHADOWS-->
		float nodeShadowSoftness;
		float nodeShadowIntensity;
		float nodeShadowAlpha;
		NodeColor nodeShadowColor;
		NodeColor nodeShadowDarkColor;
		//<!--Stars-->
		float nodeStarry;
	};

	class DyParamentsSetting
	{
	public:
		DyParamentsSetting();
		DECLARE_SERIALIZE(DyParamentsSetting)
		bool LoadConfig(const char* path);
		dynamic_array<NodeInfo> NodeInfos;
	};
	struct EnvData
	{
		float windStrength = 1.0f;
		float shadowIntensity = 1.0f;
		ColorRGBAf waterColor = ColorRGBAf(1.0f,1.0f,1.0f,1.0f);
		float waterWaveSpeed = 1.0f;
		float isWaterGlow = 0;
	};
	//struct DyFogStartEnd
	//{
	//	DECLARE_SERIALIZE(DyFogStartEnd)
	//	float start;
	//	float end;
	//};

	struct DyFogRangeInfo
	{
		DECLARE_SERIALIZE(DyFogRangeInfo)
		float start;
		float end;
	};

	struct DyEnvInfo
	{
		DECLARE_SERIALIZE(DyFogInfo)
		int skyboxType;
		dynamic_array<DyFogRangeInfo> rangeInfos;
		float windStrength;
		float shadowIntensity;
		ColorRGBAf waterColor;
		float waterWaveSpeed;
		float isWaterGlow;
	};

	class DyEnvParamentsSetting
	{
	public:
		DyEnvParamentsSetting();
		DECLARE_SERIALIZE(DyEnvParamentsSetting)
		bool LoadConfig(const char* path);
		dynamic_array<DyEnvInfo> EnvInfos;
	};

	//class DyParamentsSetting_Sun : public DyParamentsSetting
	//{
	//public:
	//	DyParamentsSetting_Sun();
	//	virtual void LoadConfig();
	//};

	//class DyParamentsSetting_Rain : public DyParamentsSetting
	//{
	//public:
	//	DyParamentsSetting_Rain();
	//	virtual void LoadConfig();
	//};

	////DyParamentsSetting& GetDyParamentsSetting();
	//DyParamentsSetting_Sun& GetDyParamentsSetting_Sun();
	//DyParamentsSetting_Rain& GetDyParamentsSetting_Rain();
}
