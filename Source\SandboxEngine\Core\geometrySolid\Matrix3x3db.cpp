﻿#include "geometrySolid/Matrix3x3db.h"

namespace MNSandbox {
	namespace GeometrySolid {
		namespace
		{
			Matrix3x3db CreateIdentityMatrix3x3f()
			{
				Matrix3x3db temp;
				temp.SetIdentity();
				return temp;
			}

			Matrix3x3db CreateZeroMatrix3x3f()
			{
				Matrix3x3db temp;
				temp.SetZero();
				return temp;
			}
		}

		const Matrix3x3db Matrix3x3db::identity = CreateIdentityMatrix3x3f();
		const Matrix3x3db Matrix3x3db::zero = CreateZeroMatrix3x3f();


		Matrix3x3db::Matrix3x3db(const Rainbow::Matrix4x4f& other)
		{
			m_Data[0] = other.m_Data[0];
			m_Data[1] = other.m_Data[1];
			m_Data[2] = other.m_Data[2];

			m_Data[3] = other.m_Data[4];
			m_Data[4] = other.m_Data[5];
			m_Data[5] = other.m_Data[6];

			m_Data[6] = other.m_Data[8];
			m_Data[7] = other.m_Data[9];
			m_Data[8] = other.m_Data[10];
		}

		//void GetRotMatrixNormVec(double* out, const double* inVec, double radians);

		//Matrix3x3db::Matrix3x3db(const Matrix4x4f& other)
		//{
		//	m_Data[0] = other.m_Data[0];
		//	m_Data[1] = other.m_Data[1];
		//	m_Data[2] = other.m_Data[2];

		//	m_Data[3] = other.m_Data[4];
		//	m_Data[4] = other.m_Data[5];
		//	m_Data[5] = other.m_Data[6];

		//	m_Data[6] = other.m_Data[8];
		//	m_Data[7] = other.m_Data[9];
		//	m_Data[8] = other.m_Data[10];
		//}

		Matrix3x3db& Matrix3x3db::SetIdentity()
		{
			Get(0, 0) = 1.0;  Get(0, 1) = 0.0;  Get(0, 2) = 0.0;
			Get(1, 0) = 0.0;  Get(1, 1) = 1.0;  Get(1, 2) = 0.0;
			Get(2, 0) = 0.0;  Get(2, 1) = 0.0;  Get(2, 2) = 1.0;
			return *this;
		}

		Matrix3x3db& Matrix3x3db::SetZero()
		{
			Get(0, 0) = 0.0;  Get(0, 1) = 0.0;  Get(0, 2) = 0.0;
			Get(1, 0) = 0.0;  Get(1, 1) = 0.0;  Get(1, 2) = 0.0;
			Get(2, 0) = 0.0;  Get(2, 1) = 0.0;  Get(2, 2) = 0.0;
			return *this;
		}

		Matrix3x3db& Matrix3x3db::SetBasis(const Vector3db& inX, const Vector3db& inY, const Vector3db& inZ)
		{
			Get(0, 0) = inX[0];    Get(0, 1) = inY[0];    Get(0, 2) = inZ[0];
			Get(1, 0) = inX[1];    Get(1, 1) = inY[1];    Get(1, 2) = inZ[1];
			Get(2, 0) = inX[2];    Get(2, 1) = inY[2];    Get(2, 2) = inZ[2];
			return *this;
		}

		Matrix3x3db& Matrix3x3db::SetBasisTransposed(const Vector3db& inX, const Vector3db& inY, const Vector3db& inZ)
		{
			Get(0, 0) = inX[0];    Get(1, 0) = inY[0];    Get(2, 0) = inZ[0];
			Get(0, 1) = inX[1];    Get(1, 1) = inY[1];    Get(2, 1) = inZ[1];
			Get(0, 2) = inX[2];    Get(1, 2) = inY[2];    Get(2, 2) = inZ[2];
			return *this;
		}

		Matrix3x3db& Matrix3x3db::SetScale(const Vector3db& inScale)
		{
			Get(0, 0) = inScale[0];    Get(0, 1) = 0.0;          Get(0, 2) = 0.0;
			Get(1, 0) = 0.0;          Get(1, 1) = inScale[1];    Get(1, 2) = 0.0;
			Get(2, 0) = 0.0;          Get(2, 1) = 0.0;          Get(2, 2) = inScale[2];
			return *this;
		}

		//bool Matrix3x3db::IsIdentity(double threshold)
		//{
		//	if (CompareApproximatelyD(Get(0, 0), 1.0, threshold) && CompareApproximatelyD(Get(0, 1), 0.0, threshold) && CompareApproximatelyD(Get(0, 2), 0.0, threshold) &&
		//		CompareApproximatelyD(Get(1, 0), 0.0, threshold) && CompareApproximatelyD(Get(1, 1), 1.0, threshold) && CompareApproximatelyD(Get(1, 2), 0.0, threshold) &&
		//		CompareApproximatelyD(Get(2, 0), 0.0, threshold) && CompareApproximatelyD(Get(2, 1), 0.0, threshold) && CompareApproximatelyD(Get(2, 2), 1.0, threshold))
		//	{
		//		return true;
		//	}
		//	return false;
		//}

		Matrix3x3db& Matrix3x3db::Scale(const Vector3db& inScale)
		{
			Get(0, 0) *= inScale[0];
			Get(1, 0) *= inScale[0];
			Get(2, 0) *= inScale[0];

			Get(0, 1) *= inScale[1];
			Get(1, 1) *= inScale[1];
			Get(2, 1) *= inScale[1];

			Get(0, 2) *= inScale[2];
			Get(1, 2) *= inScale[2];
			Get(2, 2) *= inScale[2];
			return *this;
		}

		double Matrix3x3db::GetDeterminant() const
		{
			double fCofactor0 = Get(0, 0) * Get(1, 1) * Get(2, 2);
			double fCofactor1 = Get(0, 1) * Get(1, 2) * Get(2, 0);
			double fCofactor2 = Get(0, 2) * Get(1, 0) * Get(2, 1);

			double fCofactor3 = Get(0, 2) * Get(1, 1) * Get(2, 0);
			double fCofactor4 = Get(0, 1) * Get(1, 0) * Get(2, 2);
			double fCofactor5 = Get(0, 0) * Get(1, 2) * Get(2, 1);

			return fCofactor0 + fCofactor1 + fCofactor2 - fCofactor3 - fCofactor4 - fCofactor5;
		}

		Matrix3x3db& Matrix3x3db::Transpose()
		{
			std::swap(Get(0, 1), Get(1, 0));
			std::swap(Get(0, 2), Get(2, 0));
			std::swap(Get(2, 1), Get(1, 2));
			return *this;
		}

		Matrix3x3db& Matrix3x3db::Transpose (const Matrix3x3db& inMat)
		{
			int i;
			for (i=0;i<3;i++)
			{
				Get (i, 0) = inMat.Get (0, i);
				Get (i, 1) = inMat.Get (1, i);
				Get (i, 2) = inMat.Get (2, i);
			}
			return *this;
		}
		
		bool Matrix3x3db::Invert()
		{
			///@TODO make a fast but robust inverse matrix 3x3
			Rainbow::Matrix4x4f m(
				m_Data33[0][0], m_Data33[0][1], m_Data33[0][2], 0,
				m_Data33[1][0], m_Data33[1][1], m_Data33[1][2], 0,
				m_Data33[2][0], m_Data33[2][1], m_Data33[2][2], 0,
				0, 0, 0, 1
			);
			bool success = Rainbow::InvertMatrix4x4_Full(m.GetPtr(), m.GetPtr());
			*this = m;
			return success;
		}

		Matrix3x3db& Matrix3x3db::operator=(const Rainbow::Matrix4x4f& other)
		{
			m_Data[0] = other.m_Data[0];
			m_Data[1] = other.m_Data[1];
			m_Data[2] = other.m_Data[2];

			m_Data[3] = other.m_Data[4];
			m_Data[4] = other.m_Data[5];
			m_Data[5] = other.m_Data[6];

			m_Data[6] = other.m_Data[8];
			m_Data[7] = other.m_Data[9];
			m_Data[8] = other.m_Data[10];
			return *this;
		}

		//void Matrix3x3db::InvertTranspose()
		//{
		//	Invert();
		//	Transpose();
		//}

		Matrix3x3db& Matrix3x3db::operator*=(double f)
		{
			for (int i = 0; i < 9; i++)
				m_Data[i] *= f;
			return *this;
		}

		Matrix3x3db& Matrix3x3db::operator*=(const Matrix3x3db& inM)
		{
			int i;
			for (i = 0; i < 3; i++)
			{
				double v[3] = { Get(i, 0), Get(i, 1), Get(i, 2) };
				Get(i, 0) = v[0] * inM.Get(0, 0) + v[1] * inM.Get(1, 0) + v[2] * inM.Get(2, 0);
				Get(i, 1) = v[0] * inM.Get(0, 1) + v[1] * inM.Get(1, 1) + v[2] * inM.Get(2, 1);
				Get(i, 2) = v[0] * inM.Get(0, 2) + v[1] * inM.Get(1, 2) + v[2] * inM.Get(2, 2);
			}
			return *this;
		}

		//Matrix3x3db& Matrix3x3db::operator*=(const Matrix4x4f& inM)
		//{
		//	int i;
		//	for (i = 0; i < 3; i++)
		//	{
		//		double v[3] = { Get(i, 0), Get(i, 1), Get(i, 2) };
		//		Get(i, 0) = v[0] * inM.Get(0, 0) + v[1] * inM.Get(1, 0) + v[2] * inM.Get(2, 0);
		//		Get(i, 1) = v[0] * inM.Get(0, 1) + v[1] * inM.Get(1, 1) + v[2] * inM.Get(2, 1);
		//		Get(i, 2) = v[0] * inM.Get(0, 2) + v[1] * inM.Get(1, 2) + v[2] * inM.Get(2, 2);
		//	}
		//	return *this;
		//}

		//Matrix3x3db& Matrix3x3db::SetAxisAngle(const Vector3db& rotationAxis, double radians)
		//{
		//	GetRotMatrixNormVec(m_Data, rotationAxis.GetPtr(), radians);
		//	return *this;
		//}

		void EulerToMatrix(const Vector3db& v, Matrix3x3db& matrix)
		{
			double cx = cos(v.x);
			double sx = sin(v.x);
			double cy = cos(v.y);
			double sy = sin(v.y);
			double cz = cos(v.z);
			double sz = sin(v.z);

			matrix.Get(0, 0) = cy * cz + sx * sy * sz;
			matrix.Get(0, 1) = cz * sx * sy - cy * sz;
			matrix.Get(0, 2) = cx * sy;

			matrix.Get(1, 0) = cx * sz;
			matrix.Get(1, 1) = cx * cz;
			matrix.Get(1, 2) = -sx;

			matrix.Get(2, 0) = -cz * sy + cy * sx * sz;
			matrix.Get(2, 1) = cy * cz * sx + sy * sz;
			matrix.Get(2, 2) = cx * cy;
		}

		/*
		 * A function for creating a rotation matrix that rotates a vector called
		 * "from" into another vector called "to".
		 * Input: from, to which both must be *normalized* non-zero vectors
		 * Output: a 3x3 matrix in colum-major form
		 * Author: Tomas Möller, 1999
		 */
		Matrix3x3db& Matrix3x3db::SetFromToRotation(const Vector3db& from, const Vector3db& to)
		{
			Vector3db v = CrossProduct(from, to);
			double e = DotProduct(from, to);
			if (e > 1.0 - kEpsilon)     /* "from" almost or equal to "to"-vector? */
			{
				/* return identity */
				Get(0, 0) = 1.0; Get(0, 1) = 0.0; Get(0, 2) = 0.0;
				Get(1, 0) = 0.0; Get(1, 1) = 1.0; Get(1, 2) = 0.0;
				Get(2, 0) = 0.0; Get(2, 1) = 0.0; Get(2, 2) = 1.0;
			}
			else if (e < -1.0 + kEpsilon) /* "from" almost or equal to negated "to"? */
			{
				double invlen;
				double fxx, fyy, fzz, fxy, fxz, fyz;
				double uxx, uyy, uzz, uxy, uxz, uyz;
				double lxx, lyy, lzz, lxy, lxz, lyz;
				/* left=CROSS(from, (1,0,0)) */
				Vector3db left(0.0, from[2], -from[1]);
				if (DotProduct(left, left) < kEpsilon) /* was left=CROSS(from,(1,0,0)) a good choice? */
				{
					/* here we now that left = CROSS(from, (1,0,0)) will be a good choice */
					left[0] = -from[2]; left[1] = 0.0; left[2] = from[0];
				}
				/* normalize "left" */
				invlen = 1.0 / std::sqrt(DotProduct(left, left));
				left[0] *= invlen;
				left[1] *= invlen;
				left[2] *= invlen;
				Vector3db up = CrossProduct(left, from);
				/* now we have a coordinate system, i.e., a basis;    */
				/* M=(from, up, left), and we want to rotate to:      */
				/* N=(-from, up, -left). This is done with the matrix:*/
				/* N*M^T where M^T is the transpose of M              */
				fxx = -from[0] * from[0]; fyy = -from[1] * from[1]; fzz = -from[2] * from[2];
				fxy = -from[0] * from[1]; fxz = -from[0] * from[2]; fyz = -from[1] * from[2];

				uxx = up[0] * up[0]; uyy = up[1] * up[1]; uzz = up[2] * up[2];
				uxy = up[0] * up[1]; uxz = up[0] * up[2]; uyz = up[1] * up[2];

				lxx = -left[0] * left[0]; lyy = -left[1] * left[1]; lzz = -left[2] * left[2];
				lxy = -left[0] * left[1]; lxz = -left[0] * left[2]; lyz = -left[1] * left[2];
				/* symmetric matrix */
				Get(0, 0) = fxx + uxx + lxx; Get(0, 1) = fxy + uxy + lxy; Get(0, 2) = fxz + uxz + lxz;
				Get(1, 0) = Get(0, 1);   Get(1, 1) = fyy + uyy + lyy; Get(1, 2) = fyz + uyz + lyz;
				Get(2, 0) = Get(0, 2);   Get(2, 1) = Get(1, 2);   Get(2, 2) = fzz + uzz + lzz;
			}
			else  /* the most common case, unless "from"="to", or "from"=-"to" */
			{
				/* ...otherwise use this hand optimized version (9 mults less) */
				double hvx, hvz, hvxy, hvxz, hvyz;
				double h = (1.0f - e) / DotProduct(v, v);
				hvx = h * v[0];
				hvz = h * v[2];
				hvxy = hvx * v[1];
				hvxz = hvx * v[2];
				hvyz = hvz * v[1];
				Get(0, 0) = e + hvx * v[0]; Get(0, 1) = hvxy - v[2];     Get(0, 2) = hvxz + v[1];
				Get(1, 0) = hvxy + v[2];  Get(1, 1) = e + h * v[1] * v[1]; Get(1, 2) = hvyz - v[0];
				Get(2, 0) = hvxz - v[1];  Get(2, 1) = hvyz + v[0];     Get(2, 2) = e + hvz * v[2];
			}
			return *this;
		}

		// Right handed
		bool LookRotationToMatrix(const Vector3db& viewVec, const Vector3db& upVec, Matrix3x3db* m)
		{
			Vector3db z = viewVec;
			// compute u0
			double mag = Magnitude(z);
			if (mag < kEpsilon)
			{
				m->SetIdentity();
				return false;
			}
			z /= mag;

			Vector3db x = CrossProduct(upVec, z);
			mag = Magnitude(x);
			if (mag < kEpsilon)
			{
				m->SetIdentity();
				return false;
			}
			x /= mag;

			Vector3db y(CrossProduct(z, x));
			if (!Rainbow::CompareApproximatelyD(SqrMagnitude(y), 1.0))
				return false;

			m->SetBasis(x, y, z);
			return true;
		}

		/*
		//Left handed
		bool LookRotationToMatrixLeftHanded (const Vector3db& viewVec, const Vector3db& upVec, Matrix3x3db* m)
		{
			Vector3db z = viewVec;
			// compute u0
			double mag = Magnitude (z);
			if (mag < kEpsilon)
				return false;
			z /= mag;

			Vector3db x = Cross (z, upVec);
			mag = Magnitude (x);
			if (mag < kEpsilon)
				return false;
			x /= mag;

			Vector3db y (Cross (x, z));
			if (!CompareApproximately (SqrMagnitude (y), 1.0F))
				return false;

			m->SetBasis (x, y, z);
			return true;
		}
		*/

		//void GetRotMatrixNormVec(double* out, const double* inVec, double radians)
		//{
		//	/* This function contributed by Erich Boleyn (<EMAIL>) */
		//	/* This function used from the Mesa OpenGL code (matrix.c)  */
		//	double s, c;
		//	double vx, vy, vz, xx, yy, zz, xy, yz, zx, xs, ys, zs, one_c;

		//	s = sin(radians);
		//	c = cos(radians);

		//	vx = inVec[0];
		//	vy = inVec[1];
		//	vz = inVec[2];

		//	#define M(row, col)  out[row*3 + col]
		//	/*
		//	*     Arbitrary axis rotation matrix.
		//	*
		//	*  This is composed of 5 matrices, Rz, Ry, T, Ry', Rz', multiplied
		//	*  like so:  Rz * Ry * T * Ry' * Rz'.  T is the final rotation
		//	*  (which is about the X-axis), and the two composite transforms
		//	*  Ry' * Rz' and Rz * Ry are (respectively) the rotations necessary
		//	*  from the arbitrary axis to the X-axis then back.  They are
		//	*  all elementary rotations.
		//	*
		//	*  Rz' is a rotation about the Z-axis, to bring the axis vector
		//	*  into the x-z plane.  Then Ry' is applied, rotating about the
		//	*  Y-axis to bring the axis vector parallel with the X-axis.  The
		//	*  rotation about the X-axis is then performed.  Ry and Rz are
		//	*  simply the respective inverse transforms to bring the arbitrary
		//	*  axis back to its original orientation.  The first transforms
		//	*  Rz' and Ry' are considered inverses, since the data from the
		//	*  arbitrary axis gives you info on how to get to it, not how
		//	*  to get away from it, and an inverse must be applied.
		//	*
		//	*  The basic calculation used is to recognize that the arbitrary
		//	*  axis vector (x, y, z), since it is of unit length, actually
		//	*  represents the sines and cosines of the angles to rotate the
		//	*  X-axis to the same orientation, with theta being the angle about
		//	*  Z and phi the angle about Y (in the order described above)
		//	*  as follows:
		//	*
		//	*  cos ( theta ) = x / sqrt ( 1 - z^2 )
		//	*  sin ( theta ) = y / sqrt ( 1 - z^2 )
		//	*
		//	*  cos ( phi ) = sqrt ( 1 - z^2 )
		//	*  sin ( phi ) = z
		//	*
		//	*  Note that cos ( phi ) can further be inserted to the above
		//	*  formulas:
		//	*
		//	*  cos ( theta ) = x / cos ( phi )
		//	*  sin ( theta ) = y / cos ( phi )
		//	*
		//	*  ...etc.  Because of those relations and the standard trigonometric
		//	*  relations, it is pssible to reduce the transforms down to what
		//	*  is used below.  It may be that any primary axis chosen will give the
		//	*  same results (modulo a sign convention) using thie method.
		//	*
		//	*  Particularly nice is to notice that all divisions that might
		//	*  have caused trouble when parallel to certain planes or
		//	*  axis go away with care paid to reducing the expressions.
		//	*  After checking, it does perform correctly under all cases, since
		//	*  in all the cases of division where the denominator would have
		//	*  been zero, the numerator would have been zero as well, giving
		//	*  the expected result.
		//	*/

		//	xx = vx * vx;
		//	yy = vy * vy;
		//	zz = vz * vz;
		//	xy = vx * vy;
		//	yz = vy * vz;
		//	zx = vz * vx;
		//	xs = vx * s;
		//	ys = vy * s;
		//	zs = vz * s;
		//	one_c = 1.0 - c;

		//	M(0, 0) = (one_c * xx) + c;
		//	M(1, 0) = (one_c * xy) - zs;
		//	M(2, 0) = (one_c * zx) + ys;

		//	M(0, 1) = (one_c * xy) + zs;
		//	M(1, 1) = (one_c * yy) + c;
		//	M(2, 1) = (one_c * yz) - xs;

		//	M(0, 2) = (one_c * zx) - ys;
		//	M(1, 2) = (one_c * yz) + xs;
		//	M(2, 2) = (one_c * zz) + c;

		//	#undef M
		//}

		//void OrthoNormalize(Matrix3x3db& matrix)
		//{
		//	Vector3db* c0 = (Vector3db*)matrix.GetPtr() + 0;
		//	Vector3db* c1 = (Vector3db*)matrix.GetPtr() + 3;
		//	Vector3db* c2 = (Vector3db*)matrix.GetPtr() + 6;
		//	OrthoNormalize(c0, c1, c2);
		//}

	}
}