#include "SandstormEffect.h"
#include "world_types.h"
#include "worldData/WorldManager.h"
#include "display/WorldRender.h"
#include "display/worlddisplay/SkyPlane.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "AssetPipeline/AssetManager.h"
#include "display/worlddisplay/SandstormRenderObject.h"
#include "Misc/FrameTempMemoryManager.h"
#include "EffectParticle.h"


namespace Rainbow
{
	const static int lowestY = -10000;
	const static int TopestY = 10000;

	IMPLEMENT_CLASS(SandstormEffect)

		SandstormEntity::SandstormEntity()
		: m_UVTile(1, 1)
		, m_VB(nullptr)
		, m_IB(nullptr)
		, m_NumVerts(0)
		, m_NumIndices(0)
		, m_IBBufferSize(0)
		, m_VBBufferSize(0)
		, m_Mask(kShaderChannelMaskVertex | kShaderChannelMaskColor | kShaderChannelMaskTexCoord0 | kShaderChannelMaskTexCoord1)
		, m_LocalBound(Vector3f::zero, Vector3f::one)
	{
		m_VertStride = sizeof(SandstormVertex);
		//MaterialManager& materialManager = GetMaterialManager();
		//SharePtr<Texture2D> ptex = GetAssetManager().LoadAsset<Texture2D>("particles/texture/dust_storm.png");
		//m_SingleSandstormMaterial = MoveToSharePtr(materialManager.LoadFromFile("Materials/MiniGame/legacy/legacy_sandstorm.xml")->CreateInstance());
		//m_SingleSandstormMaterial->SetTexture(ShaderParamNames::g_DiffuseTex, ptex.Get());
		//m_SingleSandstormMaterial->SetStencilCompFunc(kFuncNotEqual);
		//m_SingleSandstormMaterial->SetStencilOpPass(kStencilOpReplace);
		//m_SingleSandstormMaterial->SetStencilReadMask(1 << 0);
		//m_SingleSandstormMaterial->SetStencilWriteMask(1 << 0);
		//m_SingleSandstormMaterial->SetStencilRef(1 << 0);
		//m_SingleSandstormMaterial->SetDepthBias(-1);
		//m_SingleSandstormMaterial->SetSlopeScaledDepthBias(-0.05f);
	}

	void SandstormEntity::RemoveAllSections()
	{
		m_SectionLists.clear();
	}

	void SandstormEntity::RemoveAllBlocks()
	{
		m_BlockLists.clear();
	}

	void SandstormEntity::RemoveSections(const std::vector<ChunkIndex>& sectionLists)
	{
	}

	void SandstormEntity::AddSections(const std::vector<ChunkIndex>& sectionLists, Vector3f dir)
	{
		//for (int i = 0; i < sectionLists.size(); i++)
		//{
		//	m_SectionLists.push_back(sectionLists[i]);
		//}
		//m_Dir = dir;
		//m_SingleSandstormMaterial->SetVector(ShaderParamNames::g_Dir, m_Dir);
	}

	void SandstormEntity::AddBlockList(const std::vector<WCoord>& blockList, const Vector3f& dir)
	{
		//m_BlockLists.insert(m_BlockLists.begin(), blockList.begin(), blockList.end());
		//m_Dir = dir;
		//m_SingleSandstormMaterial->SetVector(ShaderParamNames::g_Dir, m_Dir);
	}


	void SandstormEntity::PreInit()
	{
		////todo 合并顶点
		//m_Sections.clear();

		//for (int i = 0; i < m_SectionLists.size(); i++)
		//{
		//	m_Sections.emplace(m_SectionLists[i]);
		//}

		//m_Blocks.clear();
		//for (int i = 0; i < m_BlockLists.size(); i++)
		//{
		//	m_Blocks.emplace(BlockFindKey(m_BlockLists[i]));
		//}

		//m_BlockCoords.clear();
		//for (int i = 0; i < m_BlockLists.size(); ++i)
		//{
		//	m_BlockCoords.emplace(m_BlockLists[i]);
		//}

		//std::vector<Line> m_Lines;
		////处理 section
		//for (int i = 0; i < m_SectionLists.size(); i++)
		//{
		//	ChunkIndex& section = m_SectionLists[i];
		//	ChunkIndex leftSection = ChunkIndex(section.x - 1, section.z);
		//	ChunkIndex rightSection = ChunkIndex(section.x + 1, section.z);
		//	ChunkIndex bottomSection = ChunkIndex(section.x, section.z - 1);
		//	ChunkIndex topSection = ChunkIndex(section.x, section.z + 1);
		//	if (m_Sections.find(leftSection) == m_Sections.end())
		//	{
		//		m_Lines.push_back(Line(
		//			WCoord(section.x * SECTION_SIZE, lowestY, section.z * SECTION_SIZE),
		//			WCoord(section.x * SECTION_SIZE, lowestY, section.z * SECTION_SIZE + SECTION_SIZE), 0, Vector2f(0, 1))
		//		);
		//	}
		//	if (m_Sections.find(rightSection) == m_Sections.end())
		//	{
		//		m_Lines.push_back(Line(
		//			WCoord(section.x * SECTION_SIZE + SECTION_SIZE, lowestY, section.z * SECTION_SIZE + SECTION_SIZE),
		//			WCoord(section.x * SECTION_SIZE + SECTION_SIZE, lowestY, section.z * SECTION_SIZE), 2, Vector2f(0, 1))
		//		);
		//	}
		//	if (m_Sections.find(topSection) == m_Sections.end())
		//	{
		//		m_Lines.push_back(Line(
		//			WCoord(section.x * SECTION_SIZE, lowestY, section.z * SECTION_SIZE + SECTION_SIZE),
		//			WCoord(section.x * SECTION_SIZE + SECTION_SIZE, lowestY, section.z * SECTION_SIZE + SECTION_SIZE), 1, Vector2f(0, 1))
		//		);
		//	}
		//	if (m_Sections.find(bottomSection) == m_Sections.end())
		//	{
		//		m_Lines.push_back(Line(
		//			WCoord(section.x * SECTION_SIZE + SECTION_SIZE, lowestY, section.z * SECTION_SIZE),
		//			WCoord(section.x * SECTION_SIZE, lowestY, section.z * SECTION_SIZE), 3, Vector2f(0, 1))
		//		);
		//	}
		//}




		//for (int i = 0; i < m_BlockLists.size(); ++i)
		//{
		//	WCoord blockPos = m_BlockLists[i];
		//	WCoord leftBlock = WCoord(blockPos.x - 1, blockPos.y, blockPos.z);
		//	WCoord rightBlock = WCoord(blockPos.x + 1, blockPos.y, blockPos.z);
		//	WCoord bottomBlock = WCoord(blockPos.x, blockPos.y, blockPos.z - 1);
		//	WCoord topBlock = WCoord(blockPos.x, blockPos.y, blockPos.z + 1);
		//	WCoord positiveYBlock = WCoord(blockPos.x, blockPos.y + 1, blockPos.z);
		//	bool isPositiveYExists = false;
		//	//todo 检查y方向上断点，消耗大，试考虑另一种方式
		//	// std::set<BlockFindKey>::iterator it = m_Blocks.find(BlockFindKey(blockPos.x, blockPos.y, blockPos.z));
		//	//if(it == m_Blocks.end())
		//	//{
		//	//	isPositiveYExists = false;
		//	//}
		//	//else if(blockPos.y < it->max_Y - 1)
		//	//{
		//	//	isPositiveYExists = true;
		//	//}
		//	//!(m_Blocks.find(BlockFindKey(blockPos.x, blockPos.y, blockPos.z)) == m_Blocks.end());
		//	if (m_BlockCoords.find(leftBlock) == m_BlockCoords.end())
		//	{
		//		float startU = (blockPos.z % SECTION_BLOCK_DIM) / (float)(SECTION_BLOCK_DIM);
		//		Line tempLine(
		//			WCoord(blockPos.x * BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE),
		//			WCoord(blockPos.x * BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE + BLOCK_SIZE), 0, Vector2f(startU, startU + 1.0f / SECTION_BLOCK_DIM));
		//		tempLine.SetLineUpBlock(isPositiveYExists);
		//		m_Lines.push_back(tempLine);
		//	}
		//	if (m_BlockCoords.find(rightBlock) == m_BlockCoords.end())
		//	{
		//		float startU = (blockPos.z % SECTION_BLOCK_DIM) / (float)(SECTION_BLOCK_DIM);
		//		Line tempLine(
		//			WCoord(blockPos.x * BLOCK_SIZE + BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE + BLOCK_SIZE),
		//			WCoord(blockPos.x * BLOCK_SIZE + BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE), 2, Vector2f(startU + 1.0f / SECTION_BLOCK_DIM, startU));
		//		tempLine.SetLineUpBlock(isPositiveYExists);
		//		m_Lines.push_back(tempLine);
		//	}
		//	if (m_BlockCoords.find(topBlock) == m_BlockCoords.end())
		//	{
		//		float startU = (blockPos.x % SECTION_BLOCK_DIM) / (float)(SECTION_BLOCK_DIM);
		//		Line tempLine(
		//			WCoord(blockPos.x * BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE + BLOCK_SIZE),
		//			WCoord(blockPos.x * BLOCK_SIZE + BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE + BLOCK_SIZE), 1, Vector2f(startU, startU + 1.0f / SECTION_BLOCK_DIM));
		//		tempLine.SetLineUpBlock(isPositiveYExists);
		//		m_Lines.push_back(tempLine);
		//	}
		//	if (m_BlockCoords.find(bottomBlock) == m_BlockCoords.end())
		//	{
		//		float startU = (blockPos.x % SECTION_BLOCK_DIM) / (float)(SECTION_BLOCK_DIM);
		//		Line tempLine(
		//			WCoord(blockPos.x * BLOCK_SIZE + BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE),
		//			WCoord(blockPos.x * BLOCK_SIZE, blockPos.y * BLOCK_SIZE, blockPos.z * BLOCK_SIZE), 3, Vector2f(startU + 1.0f / SECTION_BLOCK_DIM, startU));
		//		tempLine.SetLineUpBlock(isPositiveYExists);
		//		m_Lines.push_back(tempLine);
		//	}
		//}

		//GenerateMesh(m_Lines);
	}

	void SandstormEntity::GenerateMesh(const std::vector<Line>& lines)
	{
		//m_Vertexes.clear_dealloc();
		//m_Indices.clear_dealloc();
		//int vertNum = lines.size() * 4;
		//if (vertNum == 0) {
		//	m_LocalBound.setRange(Vector3f::zero, Vector3f::one);
		//	m_NumVerts = 0;
		//	m_NumIndices = 0;
		//	return;
		//}
		//m_NumVerts = vertNum;
		//m_NumIndices = vertNum * 3;

		//m_Vertexes.resize_uninitialized(m_NumVerts);
		//m_Indices.resize_uninitialized(m_NumIndices);

		////m_VB = GetFrameTempMemoryManager().Alloc(m_VBBufferSize, EFrameTempMemoryType::FTMT_Render);
		////m_IB = GetFrameTempMemoryManager().Alloc(m_IBBufferSize, EFrameTempMemoryType::FTMT_Render);

		//SandstormVertex* vert = m_Vertexes.data();
		//SandstormVertex* p = vert;
		//
		//UInt16* index = m_Indices.data();
		//UInt16* indices = index;

		//Vector2f uvspeed[4];
		//uvspeed[0] = { 0, 1 };
		//uvspeed[1] = { 1, 0 };
		//uvspeed[2] = { 0, -1 };
		//uvspeed[3] = { -1, 0 };

		//int lineIndex = 0;

		//MinMaxAABB aabb = MinMaxAABB::invalid;
		//for (int j = 0; j < lines.size(); j++)
		//{
		//	Line line = lines[j];
		//	//point 0 //todo 检查y方向上断点，消耗大，试考虑另一种方式
		//	if (!line.GetLineUpBlock())
		//	{
		//		p->pos = Vector3f(line.m_Start.x, TopestY, line.m_Start.z);
		//		p->uv = Vector2f(line.m_UV.x, TopestY / SECTION_SIZE);
		//	}
		//	else
		//	{
		//		//上方存在方块的时候直接用自己所在位置 +BLOCK_SIZE就好了
		//		p->pos = Vector3f(line.m_Start.x, line.m_Start.y + BLOCK_SIZE, line.m_Start.z);
		//		p->uv = Vector2f(line.m_UV.x, (line.m_Start.y + BLOCK_SIZE) / SECTION_SIZE);//? 左上的是这样？
		//	}
		//	p->uv2 = Vector2f(uvspeed[line.m_Normal].x, uvspeed[line.m_Normal].y);
		//	aabb.m_Min = Minimize(aabb.m_Min, p->pos);
		//	aabb.m_Max = Maximize(aabb.m_Max, p->pos);

		//	p++;
		//	// point 1
		//	if (!line.GetLineUpBlock())
		//	{
		//		p->pos = line.m_Start.toVector3();
		//		p->uv = Vector2f(line.m_UV.x, line.m_Start.y / SECTION_SIZE);
		//		
		//	}
		//	else
		//	{
		//		p->pos = line.m_Start.toVector3();
		//		p->uv = Vector2f(line.m_UV.x, line.m_Start.y / SECTION_SIZE);
		//	}
		//	p->uv2 = Vector2f(uvspeed[line.m_Normal].x, uvspeed[line.m_Normal].y);
		//	aabb.m_Min = Minimize(aabb.m_Min, p->pos);
		//	aabb.m_Max = Maximize(aabb.m_Max, p->pos);
		//	p++;

		//	//point 2
		//	if (!line.GetLineUpBlock())
		//	{
		//		p->pos = Vector3f(line.m_End.x, TopestY, line.m_End.z);
		//		p->uv = Vector2f(line.m_UV.y, TopestY / SECTION_SIZE);
		//	}
		//	else
		//	{
		//		p->pos = Vector3f(line.m_End.x, line.m_End.y + BLOCK_SIZE, line.m_End.z);
		//		p->uv = Vector2f(line.m_UV.y, (line.m_End.y + BLOCK_SIZE) / SECTION_SIZE );
		//	}
		//	p->uv2 = Vector2f(uvspeed[line.m_Normal].x, uvspeed[line.m_Normal].y);
		//	aabb.m_Min = Minimize(aabb.m_Min, p->pos);
		//	aabb.m_Max = Maximize(aabb.m_Max, p->pos);
		//	p++;

		//	// point 3
		//	if (!line.GetLineUpBlock())
		//	{
		//		p->pos = line.m_End.toVector3();
		//		p->uv = Vector2f(line.m_UV.y, line.m_Start.y / SECTION_SIZE);
		//	}
		//	else
		//	{
		//		p->pos = line.m_End.toVector3();
		//		p->uv = Vector2f(line.m_UV.y, line.m_Start.y / SECTION_SIZE);
		//	}
		//	p->uv2 = Vector2f(uvspeed[line.m_Normal].x, uvspeed[line.m_Normal].y);
		//	aabb.m_Min = Minimize(aabb.m_Min, p->pos);
		//	aabb.m_Max = Maximize(aabb.m_Max, p->pos);
		//	p++;

		//	*indices = lineIndex * 4;
		//	indices++;
		//	*indices = lineIndex * 4 + 1;
		//	indices++;
		//	*indices = lineIndex * 4 + 2;
		//	indices++;
		//	*indices = lineIndex * 4 + 2;
		//	indices++;
		//	*indices = lineIndex * 4 + 1;
		//	indices++;
		//	*indices = lineIndex * 4 + 3;
		//	indices++;

		//	*indices = lineIndex * 4;
		//	indices++;
		//	*indices = lineIndex * 4 + 2;
		//	indices++;
		//	*indices = lineIndex * 4 + 1;
		//	indices++;
		//	*indices = lineIndex * 4 + 1;
		//	indices++;
		//	*indices = lineIndex * 4 + 2;
		//	indices++;
		//	*indices = lineIndex * 4 + 3;
		//	indices++;

		//	lineIndex++;
		//}
		//m_LocalBound.setRange(aabb.GetMin(), aabb.GetMax());
	}

	void SandstormEntity::SetUVTile(Vector2f tile)
	{
		//m_UVTile = tile;
		//m_SingleSandstormMaterial->SetVector(ShaderParamNames::g_UVTile, m_UVTile);
	}

	void SandstormEntity::GenerateVBBuffer()
	{
		//if (m_NumVerts > 0)
		//{
		//	m_VBBufferSize = m_NumVerts * m_VertStride;
		//	m_IBBufferSize = m_NumIndices * sizeof(UInt16);
		//	m_VB = GetFrameTempMemoryManager().Alloc(m_VBBufferSize, EFrameTempMemoryType::FTMT_Render);
		//	m_IB = GetFrameTempMemoryManager().Alloc(m_IBBufferSize, EFrameTempMemoryType::FTMT_Render);			
		//	SandstormVertex* pVert = (SandstormVertex*)m_VB;
		//	memcpy(pVert, &m_Vertexes[0], m_Vertexes.size() * sizeof(SandstormVertex));
		//	UInt16* pIndex = (UInt16*)m_IB;
		//	memcpy(pIndex, &m_Indices[0], m_Indices.size() * sizeof(UInt16));
		//}
		//else 
		//{
		//	m_VB = nullptr;
		//	m_IB = nullptr;
		//}


	}

	bool SandstormEntity::HasMeshRenderData()
	{
		//if (m_NumVerts > 0 && m_VBBufferSize > 0 && !m_Inner) return true;
		return false;
	}

	/*=================================================SandstormEffect=================================================*/

	

	SandstormEffect* SandstormEffect::Create(Rainbow::Camera* sceneCamera)
	{
		GameObject* obj = GameObject::Create();
		obj->CreateComponent<MovableObject>();
		return obj->CreateComponent<SandstormEffect>(sceneCamera);
	}


	SandstormEffect::SandstormEffect()
		: Super(kMemDefault)
		, m_UVTile(1, 1)
		, m_SceneCamera(nullptr)
		, m_FullScreenEffect(nullptr)
	{
		m_PrimitiveRenderData.m_CastShadow = false;
		m_PrimitiveRenderData.m_ReceiveShadow = false;
		m_PrimitiveRenderData.m_IsAffectedByFog = false;
		m_PrimitiveRenderData.m_IsAffectedByLight = false;
	}

	SandstormEffect::SandstormEffect(Rainbow::Camera* sceneCamera)
		: Super(kMemDefault)
		, m_UVTile(1, 1)
		, m_SceneCamera(sceneCamera)
	{
		m_PrimitiveRenderData.m_CastShadow = false;
		m_PrimitiveRenderData.m_ReceiveShadow = false;
		m_PrimitiveRenderData.m_IsAffectedByFog = false;
		m_PrimitiveRenderData.m_IsAffectedByLight = false;

		SandstormEntity* entity = ENG_NEW(SandstormEntity)();
		m_ActiveSandstorms.push_back(entity);

		m_FullScreenEffect = SimpleFullScreenEffect::Create("particles/texture/dust_storm.png");
		m_FullScreenEffect->SetHide(true);

	}
	SandstormEffect::~SandstormEffect()
	{
		for (size_t sandstormIdx = 0; sandstormIdx < m_ActiveSandstorms.size(); sandstormIdx++)
		{
			ENG_DELETE(m_ActiveSandstorms[sandstormIdx]);
		}
		m_SceneCamera = nullptr;
	}

	void SandstormEffect::update(UInt32 dtick)
	{
		//m_DeltaTime = TickToTime(dtick);

		//if (m_InnerSandstorm)
		//{
		//	m_fFadingProgress = 1.0f;// += TickToTime(dtick) * 1.0f / m_fFadingSpeed;
		//}
		//else
		//{
		//	m_fFadingProgress -= m_DeltaTime * 1.0f / m_fFadingSpeed;;
		//}
		//m_fFadingProgress = Clamp(m_fFadingProgress, 0.0f, 1.0f);
		//m_Time += m_DeltaTime;
		////效果已交给雾效了
		//UpdateSandstormEntity();

		//if (m_FullScreenEffect != nullptr) 
		//{
		//	m_FullScreenEffect->SetHide(!m_DrawFullScreenQuad);
		//}
	}

	void SandstormEffect::RemoveAllSection()
	{
		//m_AllSections.clear();
		//if (!m_ActiveSandstorms.empty())
		//{
		//	SandstormEntity* entity = m_ActiveSandstorms[0];
		//	if (entity)
		//	{
		//		entity->RemoveAllSections();
		//		entity->PreInit();
		//	}
		//}
		//SetHide(m_AllBlockList.empty());
		//RefreshLocalBound();
		//show(!m_AllBlockList.empty());
	}

	void SandstormEffect::RemoveAllBlocks()
	{
		//m_AllBlockList.clear();
		//if (!m_ActiveSandstorms.empty())
		//{
		//	SandstormEntity* entity = m_ActiveSandstorms[0];
		//	if (entity)
		//	{
		//		entity->RemoveAllBlocks();
		//		entity->PreInit();
		//	}
		//}
		//SetHide(m_AllBlockList.empty());
		//RefreshLocalBound();
		//show(!m_AllSections.empty());
	}

	void SandstormEffect::RemoveAll()
	{
		//m_AllBlockList.clear();
		//m_AllSections.clear();
		//if (!m_ActiveSandstorms.empty())
		//{
		//	SandstormEntity* entity = m_ActiveSandstorms[0];
		//	if (entity)
		//	{
		//		entity->RemoveAllBlocks();
		//		entity->RemoveAllSections();
		//		entity->PreInit();
		//	}
		//}
		//SetHide(true);

		//show(false);
	}

	void SandstormEffect::SetEnableUVOffset(bool isEnableUVOffset)
	{
		//m_EnableUVOffset = isEnableUVOffset;
	}

	void SandstormEffect::RemoveSection(const std::vector<ChunkIndex>& originList)
	{
		//static int InnerId = 0;
		//std::vector<ChunkIndex> sectionList;
		//int length = m_AllSections.size();
		//for (int i = originList.size() - 1; i >= 0; i--)
		//{
		//	auto found = m_AllSections.find(originList[i]);
		//	if (found != m_AllSections.end())
		//	{
		//		m_AllSections.erase(found);
		//	}
		//}
		//if (length == m_AllSections.size()) return;

		//for (auto it = m_AllSections.begin(); it != m_AllSections.end(); it++)
		//{
		//	sectionList.push_back(*it);
		//}
		//SandstormEntity* entity = m_ActiveSandstorms[0];
		//entity->RemoveAllSections();
		//entity->AddSections(sectionList, entity->m_Dir);
		//entity->PreInit();
		//SetHide(m_AllSections.size() > 0 ? false : true);
		//RefreshLocalBound();
		//show(m_AllSections.size() > 0);
	}

	void SandstormEffect::AddBlocks(const std::vector<WCoord>& originList, Vector3f dir)
	{
		//static int InnerId = 0;
		//std::vector<WCoord> blockList;
		//for (int i = originList.size() - 1; i >= 0; i--)
		//{
		//	if (m_AllBlockList.find(BlockFindKey(originList[i].x, originList[i].y, originList[i].z)) == m_AllBlockList.end())
		//	{
		//		blockList.push_back(originList[i]);
		//	}
		//}
		//if (blockList.size() <= 0) return;
		//for (int i = 0; i < blockList.size(); i++)
		//{
		//	m_AllBlockList.emplace(blockList[i]);
		//}
		//SandstormEntity* entity = m_ActiveSandstorms[0];
		//entity->AddBlockList(blockList, dir);
		//entity->PreInit();
		//SetHide(false);
		//RefreshLocalBound();
		//show(true);
	}

	void SandstormEffect::RefreshBlocks(const std::vector<WCoord>& originList, Vector3f dir)
	{
		//static int InnerId = 0;
		//SandstormEntity* entity = m_ActiveSandstorms[0];
		//entity->RemoveAllBlocks();
		//entity->AddBlockList(originList, dir);
		//entity->PreInit();
		//SetHide(false);
		//RefreshLocalBound();
		//show(true);
	}
	void SandstormEffect::RemoveBlocks(const std::vector<WCoord>& originList)
	{
		//static int InnerId = 0;
		//std::vector<WCoord> sectionList;
		//int length = m_AllBlockList.size();
		//for (int i = originList.size() - 1; i >= 0; i--)
		//{
		//	auto found = m_AllBlockList.find(BlockFindKey(originList[i]));
		//	if (found != m_AllBlockList.end())
		//	{
		//		m_AllBlockList.erase(found);
		//	}
		//}
		//if (length == m_AllBlockList.size()) return;
		//for (auto it = m_AllBlockList.begin(); it != m_AllBlockList.end(); it++)
		//{
		//	sectionList.push_back(WCoord(it->m_X, it->m_Y, it->m_Z));
		//}
		//SandstormEntity* entity = m_ActiveSandstorms[0];
		//entity->RemoveAllBlocks();
		//entity->AddBlockList(sectionList, entity->m_Dir);
		//entity->PreInit();
		//SetHide(m_AllSections.size() > 0 ? false : true);
		//RefreshLocalBound();
		//show(m_AllSections.size() > 0);
	}

	void SandstormEffect::AddSandstorm(const std::vector<ChunkIndex>& originList, Vector3f dir)
	{
		//static int InnerId = 0;
		//std::vector<ChunkIndex> sectionList;
		//for (int i = originList.size() - 1; i >= 0; i--)
		//{
		//	if (m_AllSections.find(originList[i]) == m_AllSections.end())
		//	{
		//		sectionList.push_back(originList[i]);
		//	}
		//}
		//if (sectionList.size() <= 0) return;
		//for (int i = 0; i < sectionList.size(); i++)
		//{
		//	m_AllSections.emplace(sectionList[i]);
		//}
		//SandstormEntity* entity = m_ActiveSandstorms[0];
		//entity->AddSections(sectionList, dir);
		//entity->PreInit();
		//SetHide(false);
		//RefreshLocalBound();
		//show(true);
	}

	void SandstormEffect::RemoveSandstorm(SandstormEntity* entity)
	{
		//for (auto it = m_ActiveSandstorms.begin(); it != m_ActiveSandstorms.end(); it++)
		//{
		//	if (*it == entity)
		//	{
		//		for (auto it : entity->m_Sections)
		//		{
		//			m_AllSections.erase(it);
		//		}
		//		m_ActiveSandstorms.erase(it);
		//		return;
		//	}
		//}
	}

	Vector3f clipSpaceNearPlant[4]{
		{-1.0f, -1.0f, 0.02f},
		{-1.0f, 1.0f, 0.02f},
		{1.0f, -1.0f, 0.02f},
		{1.0f, 1.0f, 0.02f}
	};

	void SandstormEffect::SetPotency(float potency)
	{
		m_Potency = potency;
	}

	RenderObject* SandstormEffect::CreateSceneObject()
	{
		RenderObject* renderObj = ENG_NEW_LABEL(SandstormRenderObject, kMemObject)(this);
		return renderObj;
	}

	void SandstormEffect::UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld)
	{
		if (IsInScene() && m_ActiveSandstorms[0])
		{
			const BoxBound& localBounds = m_ActiveSandstorms[0]->GetLocalBound();
			AABB bounds(localBounds.getCenter(), localBounds.getExtension());
			TransformAABB(bounds, localToWorld, m_WorldBounds);
		}
	}

	void SandstormEffect::UpdateSandstormEntity()
	{
		//update camera is on sandstormentity range?
		m_InnerSandstorm = false;
		m_DrawFullScreenQuad = false;
		if (m_SceneCamera == nullptr)
		{
			return;
		}

		Vector3f viewPos = m_SceneCamera->GetWorldPosition();
		//Vector3 right, up;

		ChunkIndex eyeSection(CoordDivSection(viewPos.x), CoordDivSection(viewPos.z));
		WCoord eyePos = WCoord(CoordDivBlock(viewPos.x), CoordDivBlock(viewPos.y), CoordDivBlock(viewPos.z));
		Matrix4x4f vpMatrix = Matrix4x4Mul(m_SceneCamera->GetWorldToCameraMatrix(), m_SceneCamera->GetProjectionMatrix());
		Matrix4x4f inversVPMatrix = vpMatrix.GetInvert_Full();
		Vector3f nearPlanePoint[4];
		ChunkIndex chunkIndices[4];
		for (int i = 0; i < 4; i++)
		{
			//cullpoint to worldposition
			inversVPMatrix.PerspectiveMultiplyPoint3(clipSpaceNearPlant[i], nearPlanePoint[i]);
			//inversVPMatrix.apply4x4(nearPlanePoint[i], clipSpaceNearPlant[i]);
			/*worldPos[i] = WCoord(floorf(nearPlanePoint[i].x / 100.0f), floorf(nearPlanePoint[i].y / 100.0f), floorf(nearPlanePoint[i].z / 100.0f));*/
			chunkIndices[i] = ChunkIndex(CoordDivSection(nearPlanePoint[i].x), CoordDivSection(nearPlanePoint[i].z));
		}

		SandstormEntity* inEntity = nullptr;
		for (int i = 0; i < m_ActiveSandstorms.size(); i++)
		{
			SandstormEntity* entity = m_ActiveSandstorms[i];
			//check all sandstormentity is on cullpoint range,just draw near plane 
			bool inSandstorm = entity->m_Sections.find(eyeSection) != entity->m_Sections.end() ||
				entity->m_Sections.find(chunkIndices[0]) != entity->m_Sections.end() ||
				entity->m_Sections.find(chunkIndices[1]) != entity->m_Sections.end() ||
				entity->m_Sections.find(chunkIndices[2]) != entity->m_Sections.end() ||
				entity->m_Sections.find(chunkIndices[3]) != entity->m_Sections.end();
			if (!inSandstorm)
			{
				auto it = entity->m_BlockCoords.find(WCoord(eyePos.x, eyePos.y, eyePos.z));
				if (it != entity->m_BlockCoords.end())
				{
					inSandstorm = true;
				}
			}
			//在沙暴里面
			if (inSandstorm)
			{
				inEntity = entity;
				entity->m_Inner = true;
				if (!m_InnerSandstorm)
				{
					m_InnerSandstorm = true;
				}
			}
			else
			{
				entity->m_Inner = false;
			}
		}
		if (m_InnerSandstorm)
		{
			m_fFadingProgress = 1;
		}

		m_FinalPotency = m_fFadingProgress * m_Potency;
		float hue = 0.0f;
		float saturation = 0.0f;
		float brightNess = 0.0f;
		if (g_WorldMgr && g_WorldMgr->getWorld(0))
			g_WorldMgr->getWorld(0)->getRender()->getSky()->getSkyLight().CalculateHSV(hue, saturation, brightNess);// getHSB(hue, saturation, brightNess);
		m_FinalPotency *= brightNess;

		if (m_FinalPotency > 0.001f)
		{
			m_DrawFullScreenQuad = true;
		}

		//if (inEntity)
		//{
		//	Vector3f viewSpaceDir = m_SceneCamera->GetWorldToCameraMatrix().MultiplyVector3(inEntity->m_Dir);
		//	//envdata.view.transformNormal(viewSpaceDir, inEntity->m_Dir);
		//	//m_FullScreenUVOffsetScale.x -= viewSpaceDir.x / (nearP.x * 2.0f) * m_DeltaTime;
		//	//m_FullScreenUVOffsetScale.y -= viewSpaceDir.y / (nearP.y * 2.0f) * m_DeltaTime;
		//}
		//if (m_FinalPotency > 0.001f)
		//{
		//	m_DrawFullScreenQuad = true;
		//	//render full screen

		//	//RenderFullScreenEffect(pRenderer, envdata);
		//}
		//if (m_EnableUVOffset)
		//{
		//	m_SingleSandstormMaterial->setParamValue("g_Time", &m_Time);
		//}

		//for (int i = 0; i < m_ActiveSandstorms.size(); i++)
		//{
		//	SandstormEntity* entity = m_ActiveSandstorms[i];
		//	//在沙暴里面
		//	if (!entity->m_Inner)
		//	{
		//		RenderOneSandstorm(pRenderer, envdata, entity);
		//	}
		//}
		//m_ActiveSandstorms[0]->GenerateMesh();

	}

	void SandstormEffect::GenerateMeshData()
	{
		m_ActiveSandstorms[0]->GenerateVBBuffer();
	}

	bool SandstormEffect::HasMeshData()
	{
		return m_ActiveSandstorms[0]->HasMeshRenderData();
	}

	void SandstormEffect::RefreshLocalBound()
	{
		this->GetGameObject()->SendLocalBoundsChanged(m_WorldBounds);
	}

	SandstormEntity* SandstormEffect::GetRenderSandstormEntity()
	{
		return m_ActiveSandstorms[0];
	}

	void SandstormEffect::SetUVTile(Vector2f tile)
	{
		for (int i = 0; i < m_ActiveSandstorms.size(); i++)
		{
			m_ActiveSandstorms[i]->SetUVTile(tile);
		}
		m_UVTile = tile;
	}

	void SandstormEffect::SetInnerUVTile(Vector2f tile)
	{
		m_FullScreenUVOffsetScale.z = tile.x;
		m_FullScreenUVOffsetScale.w = tile.y;
	}

}



