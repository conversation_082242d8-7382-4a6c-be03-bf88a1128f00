
#include "SandboxAssetBaseHttp.h"
#include "SandboxAssetHttpMgr.h"
#include "SandboxGlobalNotify.h"
#include "RapidJSON/document.h"
#include "SandboxLog.h"
#include "Network/HttpManager.h"
#include "Network/Http/SyncRequest.h"
#include "CloudAsset/CloudAssetUser.h"
#include "SandboxAssetReqMgr.h"
#include "BaseClass/RefCounter.h"
#include "SandboxAssetLogMgr.h"

namespace MNSandbox {

	static const char* sandbox_http = "[sandbox http]";

	/*******************************************************************AssetBaseHttp************************************************/

	IMPLEMENT_REFCLASS(AssetBaseHttp)

	AssetBaseHttp::AssetBaseHttp()
	{
		STATISTICS_COUNT_INFO(4, 1);
	}


	AssetBaseHttp::~AssetBaseHttp()
	{
		STATISTICS_COUNT_INFO(4, 2);
	}

	bool AssetBaseHttp::IsReqInValid(AssetHttpReqType reqType)
	{
		return m_callbackMap.find(reqType) == m_callbackMap.end();
	}

	void AssetBaseHttp::Excute()
	{
		if (!IsValid())
		{
			SANDBOX_ASSERTEX(false, "please us NewInstance.");
			return;
		}
		do{
			if (!m_reqData){
				break;
			}
			auto reqType = m_reqData->GetReqType();
			if (IsReqInValid(reqType)){
				break;
			}
			auto& funcReq = m_callbackMap[reqType].reqCb;
			if (funcReq){
				funcReq(this, m_reqData);
				return;
			}
		} while (false);

		SetDirty();
	}

	void AssetBaseHttp::RegisterReqCallback(AssetHttpReqType reqType, ReqCallback reqCb)
	{
		RegisterItem item;
		item.reqType = reqType;
		item.reqCb = reqCb;

		m_callbackMap[reqType] = item;
	}

	void AssetBaseHttp::SetDirty()
	{
		AssetHttpMgr::GetSingleton().SetHttpDirty(_ID);
	}

	void AssetBaseHttp::Log(const std::string& log)
	{
		SANDBOX_LOG(sandbox_http, "[id:", _ID, "]:", log.c_str());
	}

	AutoRef<HttpRsp> AssetBaseHttp::CreateRspDataPtr(AssetHttpReqType reqType)
	{
		AutoRef<HttpRsp> ptr = nullptr;
		switch (reqType)
		{
		case MNSandbox::AssetHttpReqType::None:
			break;
		case MNSandbox::AssetHttpReqType::Logic_UploadAsset:
			ptr = UploadAssetRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::GetAndCheckResId:
			ptr = GetAndCheckResIdRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::UpdateFile:
		case MNSandbox::AssetHttpReqType::UploadFile:
			ptr = UploadFileRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::CacheUpload:
			ptr = CacheUploadRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::CacheDownload:
			ptr = CacheDownloadRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::SetMetaData:
			ptr = SetMetaRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::BackpackAddRes:
			ptr = BackpackAddResRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::BackpackGetResByUin:
			ptr = BackpackGetResByUinRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::BackpackGetResByIds:
			ptr = BackpackGetResByIdsRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::GetResIdByVers:
			break;
		case MNSandbox::AssetHttpReqType::QueryResMeta:
			break;
		case MNSandbox::AssetHttpReqType::GetDownloadUrlByIds:
			ptr = GetDownloadUrlByIdsRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::BackpackUpdateRes:
			ptr = BackpackUpdateResRsp::NewInstance();
			break;
		case MNSandbox::AssetHttpReqType::DeleteShopRes:
			break;
		default:
			ptr = EmptyRsp::NewInstance();;
			break;
		}
		return ptr;
	}

	void AssetBaseHttp::HandleJsonResponse(const core::string& rsp, const char* reqUrl)
	{
		SetDirty();
		if (!m_reqData) {
			return;
		}
		int code = -1;
		HandleCode(code, rsp, reqUrl);
		HandleRspCallback(code, rsp);
	}
	void AssetBaseHttp::HandleCode(int& code, const core::string& rsp, const char* reqUrl)
	{
		rapidjson::Document jsonDoc;
		SANDBOX_LOG(sandbox_http, "[id:", _ID, "] \nreq:", reqUrl, "\nrsp:", rsp.c_str());
		bool isSuccess = false;
		jsonDoc.Parse(rsp.c_str());
		if (!jsonDoc.HasParseError()) {
			if (jsonDoc.HasMember("code")) {
				code = jsonDoc["code"].GetInt();
				if (code == 0)
				{
					isSuccess = true;
				}
				else {
					SANDBOX_WARNINGLOG(sandbox_http, "[id:", _ID, "]", "code error:", code);
				}
			}
			else {
				SANDBOX_WARNINGLOG(sandbox_http, "[id:", _ID, "]", "no code:");
			}
		}
		else {
			SANDBOX_WARNINGLOG(sandbox_http, "[id:", _ID, "]", "parse error:");
		}
	}

#if PLATFORM_WIN && !WINDOWS_SERVER
	void  AssetBaseHttp::SyncRequestRsp(Rainbow::Http::SyncRequest* request)
	{
		SetDirty();
		if (!m_reqData) {
			return;
		}
		int code = -1;
		core::string response;
		CURLcode ret = request->FetchResponse(response);
		HandleCode(code, response, "");
		if (ret == CURLcode::CURLE_OK && code <=0 && response.size() > 0){
			code = 0;
		}
		HandleRspCallback(code, response);
	}

#endif
	void AssetBaseHttp::WebRequestRsp(bool success, Rainbow::Http::WebRequest* request)
	{
		HandleJsonResponse(request->GetResponse(), request->GetUrl());
	}
	void AssetBaseHttp::UpLoadFileTaskRsp(bool success, Rainbow::Http::UpLoadFileTask* request)
	{
		HandleJsonResponse(request->GetResponse(), request->GetUrl());
	}

	void AssetBaseHttp::HandleRspCallback(int code, const core::string& rsp)
	{
		AutoRef<HttpRsp> rspDataPtr = CreateRspDataPtr(m_reqData->GetReqType());
		if (rspDataPtr) {
			if (!rspDataPtr->IsValid()) {
				SANDBOX_ASSERTEX(false, "please use NewInstance.");
			}
			rspDataPtr->FillCommon(code, rsp);
			//fill data
			rspDataPtr->Parse(rsp);
			//do callback
			if (m_rspCb) {
				m_rspCb(rspDataPtr);
			}
		}else {
			SANDBOX_WARNINGLOG(sandbox_http, "[id:", _ID, "]", "[ReqType:", (int)m_reqData->GetReqType(), "] rsp parse data is nil");
		}
	}


	void AssetBaseHttp::GenerateV3QueryHeader(const char* act, AutoRef<HttpReq>req, core::string& out)
	{
		Rainbow::CloudAssetUser& cuser = Rainbow::GetCloudAssetUser();
		time_t t;
		time(&t);
		unsigned int curtimestamp = (unsigned int)t;
		std::ostringstream urlOss;
		urlOss << AssetReqMgrNew::GetSingleton().GetResV3HostUrl(true, false).c_str()
			<< "/miniw/res/v3"
			<< "?act=" << act
			<< "&uin=" << cuser.GetMiniId()
			<< "&s2t=" << cuser.GetServerToken()
			<< "&time=" << curtimestamp
			<< "&from=" << (int)req->m_from
			<< "&language=" << req->m_language
			<< "&auth=" << cuser.GetCloudAssetUinAuth(curtimestamp);

		out = urlOss.str();
	}

/*******************************************************************AssetBaseHttpRef************************************************/

IMPLEMENT_REFCLASS(AssetBaseHttpRef)

//void AssetBaseHttpRef::Register(AssetHttpReqType t, RspCallback cb)
//{
//	m_callbackMap[t] = cb;
//}
//
//
//void AssetBaseHttpRef::HandleResponse(AutoRef<HttpRsp> rsp)
//{
//	if (!m_reqData)
//	{
//		SANDBOX_WARNINGEX(false, "HttpReq is nil.");
//		return;
//	}
//	const auto& find = m_callbackMap.find(m_reqData->GetReqType());
//	if (find != m_callbackMap.end())
//	{
//		find->second(rsp);
//	}
//}

}
