#ifndef __TriangularPrism_h__
#define __TriangularPrism_h__ 1

#include "GeoSolid.h"
#include "Common/LazySingleton.h"

#include "BaseClass/SharePtr.h"
#include "Graphics/Mesh/Mesh.h"

namespace MNSandbox { 
	class SceneGeoSolid;
	namespace GeometrySolid {
		class TriangularPrism : public GeoSolid
		{
		public:
			TriangularPrism();
			~TriangularPrism();
			void InitGeoSolidMeshData() override;
			void CreateDynamic() override;
			void SeparateSurfaces() override;
			int GetSurfaceCount() override { return 5; }
			GeoSolidFace GetGeoSolidFace(int ism) override
			{
				GeoSolidFace gsf = GeoSolidFace::UNKNOWN;
				switch (ism)
				{
					case 0:
						gsf = GeoSolidFace::SLOPE;
						break;
					case 1:
						gsf = GeoSolidFace::LEFT;
						break;
					case 2:
						gsf = GeoSolidFace::BOTTOM;
						break;
					case 3:
						gsf = GeoSolidFace::FRONT;
						break;
					case 4:
						gsf = GeoSolidFace::BACK;
						break;
				}
				return gsf;
			}
			const char* GetName() const override { return "Wedge"; }
			const char* GetSurfaceName(const SceneModelObject::Surface& sf) override;
			DECLARE_GET_SINGLETON(TriangularPrism)
		};
	}
}
#endif//__TriangularPrism_h__