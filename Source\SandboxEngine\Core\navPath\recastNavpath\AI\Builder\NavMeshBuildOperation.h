#pragma once
#include "NavMeshBuildManager.h"
#include "RuntimeNavMeshBuilder.h"
#include "NavMeshManager.h"
#include "Public/NavMeshBuildSettings.h"
#include "Public/NavMeshData.h"
#include "Jobs/Jobs.h"
#include "Threads/CurrentThread.h"
#include "NavMeshManager.h"
#include "Graphics/Mesh/SharedMeshData.h"
#include "BaseClass/SharePtr.h"
#include "Geometry/Intersection.h"
#include "AssetPipeline/AsyncOperation/AsyncAutoRefPtr.h"
struct SharedMeshBuildSource
{
    // All: Out
    Rainbow::MinMaxAABB bounds;
    // All: In-Out
    Rainbow::Matrix4x4f transform;

    // All: In
    UInt8 areaTag;
    UInt8 shape;          // 'NavMeshBuildSourceShape' in a byte

    // Mesh: In
    Rainbow::AABB localBounds;
    Rainbow::SharedMeshData* sharedMeshData;
    // 
    // Primitives: In
    Rainbow::Vector3f size;
};
class NavMeshBuildOperation : public Rainbow::AsyncAutoRefPtr
{
public:
    NavMeshBuildOperation(MemLabelId label, NavMeshData* data, const NavMeshBuildSettings& buildSettings,
                          const SharedMeshBuildSource* sources, size_t nsources, const Rainbow::AABB& localBounds)
        : m_Data(data), m_LocalBounds(localBounds), m_IsScheduled(false), m_IntegratedToMainThread(false)
    {
        AssertMsg(Rainbow::CurrentThread::IsMainThread(), "Construction must happen on the main thread");
        AssertMsg(data, "NavMeshBuildOperation must have data");

        m_BuildSettings = buildSettings;
        m_Info = CreateBuildNavMeshInfo();
        AcquireSharedMeshData(m_Info, sources, nsources, data->GetPosition(), data->GetRotation(), localBounds);
    }

    virtual ~NavMeshBuildOperation()
    {
        DestroyBuildNavMeshInfo(m_Info);
        m_Info = NULL;
    }

    virtual float GetProgress()
    {
        return m_IntegratedToMainThread ? 1.0f : Progress(m_Info);
    }

    bool IsReadyToIntegrateToMainThread()
    {
        return m_IsScheduled && Done(m_Info);
    }

    virtual bool IsDone()
    {
        return m_IntegratedToMainThread;
    }

    const NavMeshData* GetData() const
    {
        return m_Data;
    }

    void AddSchedulingDependency(Rainbow::JobGroupID group)
    {
        GetNavMeshManager().GetNavMeshBuildManager()->SyncOperationFence(m_SchedulingFence);
        m_SchedulingFence = group;
    }

    // Cancel pre-schedule or in-flight for current operation
    void Purge()
    {
        AssertMsg(Rainbow::CurrentThread::IsMainThread(), "Purge must happen on the main thread");
        if (m_Data != NULL)
        {
            // Cancel build operations (computation jobs won't be scheduled or will early abort)
            Cancel(m_Info);

            // Wait for async scheduling to be done
            GetNavMeshManager().GetNavMeshBuildManager()->SyncOperationFence(m_SchedulingFence);

            // Wait for async computations to be done
            SyncComputationFence(m_Info);

            m_Data = NULL;
        }
    }

    // Cancel pre-schedule or in-flight if data matches
    void Purge(const NavMeshData* removedData)
    {
        if (m_Data == removedData)
        {
            Purge();
        }
    }

    void Schedule()
    {
        // Can be called on any thread
        AssertMsg(!m_IsScheduled, "Attempt to doubly schedule an operation.");

        // Handle the case where data is purged before being scheduled
        if (!m_Data)
        {
            DestroyBuildNavMeshInfo(m_Info);
            m_Info = NULL;
            m_IsScheduled = true;
            m_IntegratedToMainThread = true;
            return;
        }

        NavMeshBuildSettings validatedSettings;
        ValidateNavMeshBuildSettings(validatedSettings, NULL, m_BuildSettings, m_LocalBounds);
        ScheduleNavMeshDataUpdate(m_Data, m_Info, validatedSettings, m_LocalBounds);

        m_IsScheduled = true;
    }

    void Integrate()
    {
        AssertMsg(Rainbow::CurrentThread::IsMainThread(), "Integrate must happen on the main thread");
        AssertMsg(IsReadyToIntegrateToMainThread(), "The operation must be done when integrating.");

        if (m_Data)
            IntegrateNavMeshDataUpdate(m_Data, m_Info, m_LocalBounds);
        // Consider warning here - currently we don't want to warn after cancel, but after destroying it might make sense.
        //      else
        //          ErrorString ("UpdateNavMeshData failed: the source NavMeshData has been modified while being updated");

        DestroyBuildNavMeshInfo(m_Info);
        m_Info = NULL;
        m_IntegratedToMainThread = true;
    }

    bool IsAsyncOperationing(Rainbow::AABB& inBounds)
    {
        if (IntersectAABBAABB(m_LocalBounds, inBounds))
            return true;
        else
            return false;
    }
private:
    virtual void SetObject(Rainbow::Object* obj) {}
private:
    NavMeshData* m_Data;
    NavMeshBuildSettings m_BuildSettings;
    struct BuildNavMeshInfo* m_Info;
    Rainbow::AABB m_LocalBounds;
    Rainbow::JobGroupID m_SchedulingFence;
    bool m_IsScheduled;
    bool m_IntegratedToMainThread;
};
