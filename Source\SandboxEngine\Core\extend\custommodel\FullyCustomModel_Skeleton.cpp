#include "FullyCustomModel.h"
#include "FullyCustomBoneData.h"
#include "CoreCommonDef.h"
#include "CustomModelMgr.h"
#include "blocks/BlockMaterialMgr.h"
#include "genCustomModel.h"
#include "CustomModel.h"
#include "CustomModelWaitSyncListMgr.h"
#include "FullyCustomModelMgr.h"
#include <vector>
#include <functional>

using namespace Rainbow;
using namespace MNSandbox;
using std::vector;
using std::function;

extern void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype);

void FullyCustomModel::iterate(std::function<bool(FullyCustomBoneData&)>& func)
{
	FullyCustomBoneData::iterate(m_vFcbd, func);
}

bool FullyCustomModel::iterateCheck(std::function<bool(FullyCustomBoneData&)>& func, bool bIteAll)
{
	return FullyCustomBoneData::iterateCheck(m_vFcbd, func, bIteAll);
}

FullyCustomBoneData *FullyCustomModel::findFullyCustomBoneData(std::string name, FullyCustomBoneData *fcbdRoot /* = NULL */)
{
	if (name.empty())
	{
		return NULL;
	}
	if (m_vFcbd.empty())
	{
		return nullptr;
	}
	vector<FullyCustomBoneData*>* pvFcbd = &m_vFcbd;
	if (fcbdRoot)
	{
		pvFcbd = &fcbdRoot->vChildFcbds;
	}
	vector<FullyCustomBoneData*>& vFcbd = *pvFcbd;

	for (unsigned i = 0; i < vFcbd.size(); i++)
	{
		if (vFcbd[i]->name == name)
		{
			return vFcbd[i];
		}
		FullyCustomBoneData* fcbd = findFullyCustomBoneData(name, vFcbd[i]);
		if (fcbd)
		{
			return fcbd;
		}
	}

	return NULL;
}

FullyCustomBoneData* FullyCustomModel::findFcbdUnder(std::string strBoneName, std::string strParent)
{
	FullyCustomBoneData* fcbdParent = findFullyCustomBoneData(strParent);
	if (!fcbdParent)
	{
		return nullptr;
	}
	return findFullyCustomBoneData(strBoneName, fcbdParent);
}

const std::vector<FullyCustomBoneData*>* FullyCustomModel::getFullyModelBone()
{
	return &m_vFcbd;
}

void FullyCustomModel::deepCopyChildren(std::vector<FullyCustomBoneData*>& vFcbds, int animId)
{
	for (FullyCustomBoneData* fcbdChild : m_vFcbd)
	{
		FullyCustomBoneData* fcbd = ENG_NEW(FullyCustomBoneData)(this);
		fcbd->deepCopy(fcbdChild);
		vFcbds.push_back(fcbd);
	}
}

void FullyCustomModel::clearCloudUploadState()
{
	function<bool(FullyCustomBoneData&)> func = [this](FullyCustomBoneData& fcbd) -> bool {
		fcbd.hasUploadedModel = false;
		fcbd.hasUploadedTexture = false;
		return true;
	};
	iterate(func);
}

bool FullyCustomModel::checkAllUploadCloud()
{
	bool all = true;
	function<bool(FullyCustomBoneData&)> func = [this, &all](FullyCustomBoneData& fcbd) -> bool {
		if (!fcbd.hasUploadedModel || !fcbd.hasUploadedTexture)
		{
			all = false;
			return false;
		}
		return true;
	};
	iterate(func);
	return all;
}

bool FullyCustomModel::needUploadBones()
{
	bool need = false;
	function<bool(FullyCustomBoneData&)> func = [this, &need](FullyCustomBoneData& fcbd) -> bool {
		switch (fcbd.m_aoModel.GetAssetIdInfo().m_subCategory)
		{
			case AssetSubCategory::CLOUD:
				return true;
			case AssetSubCategory::LOCAL:
			case AssetSubCategory::SYS:
				need = true;
				return false;
			default:
				break;
		}
		switch (fcbd.m_aoTexture.GetAssetIdInfo().m_subCategory)
		{
			case AssetSubCategory::CLOUD:
				return true;
			case AssetSubCategory::LOCAL:
			case AssetSubCategory::SYS:
				need = true;
				return false;
			default:
				break;
		}
		return true;
	};
	iterate(func);
	return need;
}


Rainbow::Model* FullyCustomModel::findModelWithName(std::string modelName, const std::vector<FullyCustomBoneData*>& list)
{
	for (auto iter : list)
	{
		if (iter->model == modelName) return iter->model2;
		else
		{
			findModelWithName(modelName, iter->vChildFcbds);
		}
	}
	return NULL;
}

int FullyCustomModel::getBoneDataNum()
{
	return (int)m_vFcbd.size();
}

FullyCustomBoneData *FullyCustomModel::getBoneDataByIndex(int index)
{
	if (index >= 0 && index < (int)m_vFcbd.size())
		return m_vFcbd[index];

	return NULL;
}


void FullyCustomModel::BuildModelData(Rainbow::Entity* entity, Rainbow::SharePtr<Rainbow::ModelData> modelData, 
	FullyCustomBoneData* fcbdRoot , bool bindingPack, bool skipPacking)
{
	auto* pvFcbd = &m_vFcbd;
	if (fcbdRoot)
	{
		pvFcbd = &fcbdRoot->vChildFcbds;
	}

	WCoord dim = getPackingFcmDim();
	for (int i = 0; i < (int)pvFcbd->size(); i++)
	{
		auto* fcbd = (*pvFcbd)[i];
		WCoord offsetPos = WCoord(fcbd->offsetpos.x, fcbd->offsetpos.y, fcbd->offsetpos.z);
		ITEM_MESH_TYPE mesh_type = PROJECTILE_ACTOR_MESH;
		if (!m_bEditing)
		{
			if (isPackingFCMValidBone(fcbd))
			{
				converBlockPosByPlaceDir(offsetPos, DIR_POS_X, dim, BLOCK_MODEL);
				mesh_type = NORMAL_MESH;
				if (!skipPacking)
					offsetPos.z -= (dim.x / 2) + 50;
				else
				{
					offsetPos.x -= 50;
					offsetPos.z -= 50;
				}
			}
		}
		entity->AddCustomBone(
			fcbd->name.c_str(), fcbd->fathername.c_str(), 
			fcbd->scale3, fcbd->quat, Rainbow::Vector3f((float)offsetPos.x, (float)offsetPos.y, (float)offsetPos.z), 
			false
		);
		if (modelData)
		{
			size_t len = fcbd->vCmds.size();
			for (size_t i = 0; i < len; i++)
			{
				auto& cmd = fcbd->vCmds[i];
				modelData->setCustomSequenceEndtime(cmd.id, cmd.time);
				int nkey = cmd.ticks.size();
				if (nkey > 0)
				{
					modelData->setCustomKeyFrames(cmd.id, fcbd->name.c_str(), nkey, &cmd.ticks[0], &cmd.posoffsets[0], &cmd.quats[0], &cmd.scale3s[0]);
				}
			}
		}
		BuildModelData(entity, modelData, fcbd, bindingPack, skipPacking);
	}
}

bool FullyCustomModel::checkGenItemModelCache()
{
	std::function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		ITEM_MESH_TYPE eItemMeshType = PROJECTILE_ACTOR_MESH;
		int objclass = 0;

		if (!m_bEditing)
		{
			if (isPackingFCMValidBone(&fcbd))
			{
				eItemMeshType = NORMAL_MESH;
			}
		}

		if (CustomModelMgr::GetInstancePtr())
		{
			CustomModel* pCM = CustomModelMgr::GetInstancePtr()->getCustomModel(fcbd.model);
			if (pCM)
			{
				return pCM->isItemModelInCache(eItemMeshType);
			}
		}

		return false;

	};
	return iterateCheck(func, false);
}

void FullyCustomModel::ApplyModelData(Rainbow::Entity* entity, std::map<std::string, std::vector<CustomAvatarModelData>>* mCamds, bool bindingPack, bool skipPacking)
{
    OPTICK_EVENT();
	std::function<bool(FullyCustomBoneData&)> func = [this, entity, mCamds, bindingPack, skipPacking](FullyCustomBoneData& fcbd) -> bool {
		ITEM_MESH_TYPE eItemMeshType = PROJECTILE_ACTOR_MESH;
		int objclass = 0;

		if (!m_bEditing)
		{
			if (isPackingFCMValidBone(&fcbd))
			{
				eItemMeshType = NORMAL_MESH;
			}
		}
		if (m_splitSkeletonModel)
		{
			initDefaultSkeletonModel(entity, &fcbd, objclass);
		}

		Model* modelBone = nullptr;
		if (CustomModelMgr::GetInstancePtr())
		{
			// 上层有传 mCamds 才表示上层有能力处理异步加载的情况
			if (!mCamds || CustomModelMgr::GetInstancePtr()->isAvatarCMItemModelInCache(fcbd.model, eItemMeshType))
			{
				modelBone = CustomModelMgr::GetInstancePtr()->getAvatarModel(fcbd.model, eItemMeshType);
			}
			else
			{
				// 异步加载, 让 modelBone 保持 null, 下面 ApplyModelDataCallFunc 会往 mCamds 中添加等待信息
				CustomModelMgr::GetInstancePtr()->loadAvatarCMItemModelToCache(fcbd.model, eItemMeshType);
				PPtr<Rainbow::Entity> ppEntity(entity);
				//loadModelAsync 没有同步调用cb, modelBone是null也需要执行(构建默认骨骼，和往mCamds填充等待信息)
				ApplyModelDataCallFunc(ppEntity, &fcbd, mCamds, bindingPack, skipPacking, modelBone, objclass);
				return true;
			}
		}
		PPtr<Rainbow::Entity> ppEntity(entity);

		if (!modelBone)
		{
			//以下是studio资源加载
			MNSandbox::WeakRef<MNSandbox::Ref> self = m_ref.get();
			m_nloadModelAsyncRet = 0;
			auto cb = [self, this, ppEntity, bindingPack, skipPacking, objclass](Rainbow::Entity* assetEntity, FullyCustomBoneData& fcbd) -> void {
				if (!self)
				{
					return;
				}
				Model* modelBone = nullptr;
				if (assetEntity)
				{
					m_nloadModelAsyncRet = 1;
					modelBone = assetEntity->GetMainModel();
					ApplyModelDataCallFunc(ppEntity, &fcbd, NULL, bindingPack, skipPacking, modelBone, objclass);
				}
				else
				{
					// 异步加载失败的回调
					m_nloadModelAsyncRet = 2;
				}
				
			};
			fcbd.loadModelAsync(cb);
			if (m_nloadModelAsyncRet == 0)
			{
				//loadModelAsync 没有同步调用cb, modelBone是null也需要执行(构建默认骨骼，和往mCamds填充等待信息)
				ApplyModelDataCallFunc(ppEntity, &fcbd, mCamds, bindingPack, skipPacking, modelBone, objclass);
			}
			else
			{
				if (m_nloadModelAsyncRet == 2)
				{
					// 同步调用了cb, 但是结果是失败的，modelBone是null也需要执行(构建默认骨骼，和往mCamds填充等待信息)
					ApplyModelDataCallFunc(ppEntity, &fcbd, mCamds, bindingPack, skipPacking, modelBone, objclass);
				}
			}
		}
		else
		{
			ApplyModelDataCallFunc(ppEntity, &fcbd, mCamds, bindingPack, skipPacking, modelBone, objclass);
		}
		return true;
		
	};
	iterate(func);
}

void FullyCustomModel::ApplyModelDataCallFunc(PPtr<Rainbow::Entity> ppEntity, FullyCustomBoneData* fcbd, std::map<std::string, std::vector<CustomAvatarModelData>>* mCamds, bool bindingPack, bool skipPacking, 
	Model* modelBone, int objclass)
{
	Rainbow::Entity* entity = ppEntity.Get();
	if (!fcbd || !entity)
	{
		return;
	}

	if (!modelBone)
	{
		// 要加载的是默认的骨骼模型
		bool isBoneModel = false;
		bool isRoot = fcbd->fathername.empty();
		if (fcbd->model.size() > 0 && fcbd->model.find("entity/custommodel/bone.omod", 0) != std::string::npos)
		{
			isBoneModel = true;
		}

		if (mCamds && !fcbd->model.empty() && !isBoneModel && !isPackingFCMRootBone(fcbd))
		{
			//WarningStringMsg("ApplyModelDataCallFunc(): fcbd = %lld", fcbd);
			//WarningStringMsg("ApplyModelDataCallFunc(): fcbd->name = %s", fcbd->name.c_str());
			//WarningStringMsg("ApplyModelDataCallFunc(): mCamds->size() = %d", mCamds->size());
			//用此优化，减少拷贝
			//(*mCamds)[fcbd->name] = std::vector<CustomAvatarModelData>();
			//std::vector<CustomAvatarModelData>& vCamds = (*mCamds)[fcbd->name];
			std::vector<CustomAvatarModelData> vCamds;
			vCamds.clear();
			CustomAvatarModelData camd;
			CamdFromFcbd(camd, fcbd);
			vCamds.push_back(camd);
			//WarningStringMsg("ApplyModelDataCallFunc(): vCamds.size() = %d", vCamds.size());
			(*mCamds)[fcbd->name] = vCamds;
		}
		if (FullyCustomModelMgr::GetInstancePtr())
		{
			modelBone = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
		}
		objclass = 1;
		if (!modelBone)
			return;
	}

	modelBone->SetPosition(fcbd->submodelpos);
	modelBone->SetRotation(fcbd->submodelquat);
	modelBone->SetScale(fcbd->submodelscale3);

	bool destroy = true;
	FixedString fsName = fcbd->name.c_str();
	if ((m_bEditing && !bindingPack) || fcbd->show)
	{
		entity->UnbindCustomObject(fsName, 0);
		entity->UnbindCustomObject(fsName, 1);
		entity->BindCunstomObject(fsName, modelBone, objclass);
		destroy = false;
	}

	if (m_bEditing && !bindingPack)
	{
		modelBone->ShowSkins(fcbd->show);
		fcbd->setModel(modelBone);
		fcbd->setTextureId(fcbd->texId);
		destroy = false;
	}

	if (destroy)
	{
		Model::Destory(modelBone);
	}
}

void FullyCustomModel::setModelData(Rainbow::Entity* entity, SharePtr<ModelData> modelData /* = NULL */, FullyCustomBoneData* fcbdRoot /* = NULL */,
	bool bUpdateWhenCustomModelReady/* = false*/, bool bindPacking/* =true */, bool skipPacking/* =false */)
{
		OPTICK_EVENT();

	CustomModelWaitSyncListMgr::TCustomAvatarModelDataMap* pCMWaitSyncList = nullptr;
	if (bUpdateWhenCustomModelReady) {
		if (GetCustomModelWaitSyncListMgrPtr()) {
			bool bIsPacking = getModelType() == FULLY_PACKING_CUSTOM_MODEL;
			pCMWaitSyncList = GetCustomModelWaitSyncListMgrPtr()->createEntityWaitMap(entity, bIsPacking);
		}
	}

	if (!entity)
		return;
	
	if (entity->GetMainModel())
		entity->GetMainModel()->SetName(m_sKey.c_str());

	BuildModelData(entity, modelData, fcbdRoot, bindPacking, skipPacking);

	entity->PreRebuildSkeleton();

	// bind custom frame event
	if(modelData && !modelData->m_Anims.empty())
	{
		// clear animation frame event
		for (const auto& one : modelData->m_Anims)
		{
			if (one.anim)
			{
				one.anim->m_FrameEvents.clear();
			}
		}
		if (m_frameEventData.size() > 0)
		{
			std::unordered_map<int, std::vector<Rainbow::LegacyAnimationFrameEventData*>> seqFrameEventMap;
			for (const auto& one : m_frameEventData)
			{
				auto& frameEvents = seqFrameEventMap[one.id];
				frameEvents.emplace_back(const_cast<Rainbow::LegacyAnimationFrameEventData*>(&one));
			}
			for (const auto& modelAnim : modelData->m_Anims)
			{
				if (modelAnim.anim)
				{
					auto anim = modelAnim.anim;
					for (auto& seq : anim->m_Sequences)
					{
						auto& frameEvents = seqFrameEventMap[seq.id];
						for (const auto& frame : frameEvents)
						{
							anim->m_FrameEvents.emplace_back(*frame);
						}
						frameEvents.clear();
					}
				}
			}
		}
	}


	if (entity->GetMainModel())
		entity->GetMainModel()->BuildAllCunsomData();

	bool bUseBindCustomObjCache = false;
	if (!fcbdRoot && entity && bindPacking && !m_bEditing && entity->GetMainModel())
	{
		if (GenCustomModelManager::GetInstance().getFullyModelData(entity->GetMainModel()->GetName()))
		{
			if (GenCustomModelManager::GetInstance().bindCusntomObjs(entity))
			{
				//打包后模型销毁了,所以fcbd必须清理一下
				function<bool(FullyCustomBoneData&)> func = [this](FullyCustomBoneData& fcbd) -> bool {
					fcbd.model2 = nullptr;
					return true;
				};
				iterate(func);

				bUseBindCustomObjCache = true;
			}
		}
	}

	if (!bUseBindCustomObjCache)
	{
		ApplyModelData(entity, pCMWaitSyncList, bindPacking, skipPacking);

		if (!fcbdRoot && entity && bindPacking)
		{
			//子模型不完全的（客机等待模型同步时），先不打包
			if ((!pCMWaitSyncList || pCMWaitSyncList->size() <= 0) && !m_bEditing)
			{
				if (GenCustomModelManager::GetInstance().bindCusntomObjs(entity))
				{
					//打包后模型销毁了,所以fcbd必须清理一下
					function<bool(FullyCustomBoneData&)> func = [this](FullyCustomBoneData& fcbd) -> bool {
						fcbd.model2 = nullptr;
						return true;
					};
					iterate(func);
				}
			}
		}
	}
	
	if (!fcbdRoot && entity && !m_bEditing && m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
	{
		entity->SetRotation((float)(placeDirConverAngle() - 90), 0, 0);
	}
}

void FullyCustomModel::initDefaultSkeletonModel(Rainbow::Entity* entity, FullyCustomBoneData* fcbd, int objclass)
{
	if (!m_splitSkeletonModel)
	{
		return;
	}

	if (!fcbd || !entity)
	{
		return;
	}

	if (!FullyCustomModelMgr::GetInstancePtr()) 
	{
		return;
	}
	auto skeletonModel = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
	objclass = 1;

	if (!skeletonModel)
	{
		return;
	}

	/*skeletonModel->SetPosition(fcbd->offsetpos);
	skeletonModel->SetRotation(fcbd->quat);
	skeletonModel->SetScale(fcbd->scale3);*/

	skeletonModel->ShowSkins(m_showSkeletonModel && fcbd->show);
	if (skeletonModel->GetGameObject())
	{
		skeletonModel->GetGameObject()->SetLayer(kLayerIndexCustom_FRONT_SCENE);
	}

	FixedString fsName = fcbd->name.c_str();
	entity->UnbindCustomObject(fsName);
	fcbd->defaultSkeletonModel = nullptr;
	fcbd->setDefaultSkeletonModel(skeletonModel);
	entity->BindCunstomObject(fsName, skeletonModel, objclass);
}

void FullyCustomModel::setSkeletonModelShowState(bool isShow)
{
	if (!m_splitSkeletonModel || m_showSkeletonModel == isShow)
	{
		return;
	}

	m_showSkeletonModel = isShow;

	function<bool(FullyCustomBoneData&)> func = [this](FullyCustomBoneData& fcbd) -> bool {
		if (fcbd.defaultSkeletonModel)
		{
			fcbd.defaultSkeletonModel->ShowSkins(m_showSkeletonModel && fcbd.show);
		}
		return true;
	};
	iterate(func);
}

bool FullyCustomModel::getSkeletonModelShowState()
{
	return m_showSkeletonModel;
}

void FullyCustomModel::setSkeletonModelOverlayColor(std::string name, bool show)
{
	if (!m_splitSkeletonModel)
	{
		return;
	}

	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd || !fcbd->defaultSkeletonModel)
	{
		return;
	}

	if (show)
	{
		ColourValue color(0, 0, 1.0f);
		fcbd->defaultSkeletonModel->SetOverlayColor(&color);
	}
	else
	{
		fcbd->defaultSkeletonModel->SetOverlayColor(NULL);
	}
}

