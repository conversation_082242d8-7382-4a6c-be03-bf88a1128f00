
#include "loose_aabbtree.h"

BinaryTreeNode::BinaryTreeNode(LooseBinaryTree *tree, BinaryTreeNode *parent, int level, const WCoord &center, const WCoord &radius)
	:m_pTree(tree), m_pFather(parent), m_Center(center), m_Radius(radius), m_Level(level)
{
	//m_Zone = radius*0.5f;
	m_MinPos = m_Center - m_Radius - m_Radius/2;
	m_MaxPos = m_Center + m_Radius + m_Radius/2;

	tree->m_CurNodeNum++;
	assert(tree->m_CurNodeNum < 10000);

	m_pChild[0] = NULL;
	m_pChild[1] = NULL;
	const int Y_FACTOR = 2;
	if(radius.x >= radius.y/Y_FACTOR)
	{
		if(radius.x >= radius.z) m_SplitDir = 0;
		else m_SplitDir = 2;
	}
	else
	{
		if(radius.y/Y_FACTOR > radius.z) m_SplitDir = 1;
		else m_SplitDir = 2;
	}
}

BinaryTreeNode::~BinaryTreeNode()
{
	m_pTree->m_CurNodeNum--;

	ENG_DELETE(m_pChild[0]);
	ENG_DELETE(m_pChild[1]);
}

void BinaryTreeNode::addObject(void *userdata)
{
	m_Objects.push_back(userdata);
}

void BinaryTreeNode::removeObject(void *userdata)
{
	for(size_t i=0; i<m_Objects.size(); i++)
	{
		if(m_Objects[i] == userdata)
		{
			m_Objects[i] = m_Objects.back();
			m_Objects.resize(m_Objects.size()-1);
			break;
		}
	}
}

static WCoord OffsetTable[3][2] = 
{
	{WCoord(-1, 0, 0), WCoord(1, 0, 0)},
	{WCoord(0, -1, 0), WCoord(0, 1, 0)},
	{WCoord(0, 0, -1), WCoord(0, 0, 1)}
};
inline bool IsBoxSmallThanQuater(const WCoord &radius, const WCoord &bigradius)
{
	return radius.x<=bigradius.x/4 && radius.y<=bigradius.y/4 && radius.z<=bigradius.z/4;
}

BinaryTreeNode *BinaryTreeNode::getContainNode(const WCoord &center, const WCoord &radius)
{
	assert(center.x>=m_Center.x-m_Radius.x && center.x<=m_Center.x+m_Radius.x);
	assert(center.z>=m_Center.z-m_Radius.z && center.z<=m_Center.z+m_Radius.z);

	if(m_Level<m_pTree->m_MaxLevel && IsBoxSmallThanQuater(radius, m_Radius)) //推进到下一级的octree node
	{
		int index;
		if(m_SplitDir == 0)
		{
			index = center.x>m_Center.x?1:0;
		}
		else if(m_SplitDir == 1)
		{
			index = center.y>m_Center.y?1:0;
		}
		else index = center.z>m_Center.z?1:0;

		if(m_pChild[index] == NULL)
		{
			WCoord newradius = m_Radius + m_Radius*OffsetTable[m_SplitDir][0]/2;
			m_pChild[index] = ENG_NEW(BinaryTreeNode)(m_pTree, this, m_Level+1, m_Center+m_Radius*OffsetTable[m_SplitDir][index]/2, newradius);
		}

		return m_pChild[index]->getContainNode(center, radius);
	}
	else return this;
}

bool BinaryTreeNode::isInNode(const WCoord &minpos, const WCoord &maxpos)
{
	if(minpos.x>=m_MinPos.x && maxpos.x<=m_MaxPos.x && minpos.y>=m_MinPos.y && maxpos.y<=m_MaxPos.y && minpos.z>=m_MinPos.z && maxpos.z<=m_MaxPos.z)
	{
		return true;
	}
	else return false;
}

bool BinaryTreeNode::isOutNode(const WCoord &minpos, const WCoord &maxpos)
{
	if(minpos.x>m_MaxPos.x || maxpos.x<m_MinPos.x || minpos.y>m_MaxPos.y || maxpos.y<m_MinPos.y || minpos.z>m_MaxPos.z || maxpos.z<m_MinPos.z)
	{
		return true;
	}
	else return false;
}

void BinaryTreeNode::pickObjects(std::vector<void *>&objs, const Rainbow::Ray &ray)
{
//todo check
	// TODO 需要对齐
	/*if(ray.intersectBox(m_MinPos.toVector3(), m_MaxPos.toVector3()) >= 0)
	{
		objs.insert(objs.end(), m_Objects.begin(), m_Objects.end());

		if(m_pChild[0]) m_pChild[0]->pickObjects(objs, ray);
		if(m_pChild[1]) m_pChild[1]->pickObjects(objs, ray);
	}*/
}

void BinaryTreeNode::getObjectsInBox(std::vector<void *>&objs, const WCoord &minpos, const WCoord &maxpos)
{
	if(isOutNode(minpos, maxpos)) return;

	objs.insert(objs.end(), m_Objects.begin(), m_Objects.end());

	if(m_pChild[0]) m_pChild[0]->getObjectsInBox(objs, minpos, maxpos);
	if(m_pChild[1]) m_pChild[1]->getObjectsInBox(objs, minpos, maxpos);
}

//--------------------------------------------------------------------------------------------------------------------------
LooseBinaryTree::LooseBinaryTree(size_t maxlevel, const WCoord &minpos, const WCoord &maxpos)
	: m_MaxLevel(maxlevel), m_CurNodeNum(0)
{
	m_Origin = minpos;

	WCoord halfpos = (maxpos-minpos)/2;
	m_pRoot = ENG_NEW(BinaryTreeNode)(this, NULL, 0, halfpos, halfpos);
}

LooseBinaryTree::~LooseBinaryTree()
{
	ENG_DELETE(m_pRoot);
}

BinaryTreeNode *LooseBinaryTree::attachObject(const WCoord &minpos, const WCoord &maxpos, void *userdata)
{
	WCoord center = (minpos + maxpos)/2 - m_Origin;
	WCoord radius = (maxpos - minpos)/2;

	BinaryTreeNode *pnode = m_pRoot->getContainNode(center, radius);
	pnode->addObject(userdata);

	return pnode;
}

void LooseBinaryTree::detachObject(BinaryTreeNode *node, void *userdata)
{
	node->removeObject(userdata);
}

BinaryTreeNode *LooseBinaryTree::updateObject(BinaryTreeNode *node, const WCoord &minpos2, const WCoord &maxpos2, void *userdata)
{
	WCoord minpos = minpos2 - m_Origin;
	WCoord maxpos = maxpos2 - m_Origin;

	bool detached = false;
	if(!node->isInNode(minpos, maxpos))
	{
		detached = true;
		detachObject(node, userdata);
		node = attachObject(minpos, maxpos, userdata);
	}

	assert(minpos.x>=node->m_Center.x-node->m_Radius.x*3/2 && minpos.x<=node->m_Center.x+node->m_Radius.x*3/2 &&
		maxpos.x>=node->m_Center.x-node->m_Radius.x*3/2 && maxpos.x<=node->m_Center.x+node->m_Radius.x*3/2 );

	assert(minpos.y>=node->m_Center.y-node->m_Radius.y*3/2 && minpos.y<=node->m_Center.y+node->m_Radius.y*3/2 &&
		maxpos.y>=node->m_Center.y-node->m_Radius.y*3/2 && maxpos.y<=node->m_Center.y+node->m_Radius.y*3/2 );

	assert(minpos.z>=node->m_Center.z-node->m_Radius.z*3/2 && minpos.z<=node->m_Center.z+node->m_Radius.z*3/2 &&
		maxpos.z>=node->m_Center.z-node->m_Radius.z*3/2 && maxpos.z<=node->m_Center.z+node->m_Radius.z*3/2 );
	return node;
}

void LooseBinaryTree::pickObjects(std::vector<void *>&objs, const Rainbow::Ray &worldray)
{
//todo check
	// TODO 需要对齐
	/*MINIW::Ray ray;
	worldray.getRelativeRay(ray, m_Origin.toWorldPos());

	m_pRoot->pickObjects(objs, ray);*/
}

void LooseBinaryTree::getObjectsInBox(std::vector<void *>&objs, const WCoord &minpos2, const WCoord &maxpos2)
{
	WCoord minpos = minpos2 - m_Origin;
	WCoord maxpos = maxpos2 - m_Origin;

	m_pRoot->getObjectsInBox(objs, minpos, maxpos);
}

/*
#include "loose_aabbtree.h"
#include "world.h"
#include "IClientActor.h"
#include "ActorLocoMotion.h"
#include "chunk.h"

CollisionTree::CollisionTree(const WCoord &origin) : m_Origin(origin)
{
	memset(m_Nodes, 0, sizeof(m_Nodes));
}

CollisionTree::~CollisionTree()
{
	for(size_t i=0; i<TREE_NODES_TOTAL; i++)
	{
		IClientActor *pcur = static_cast<IClientActor *>(m_Nodes[i]);
		while(pcur)
		{
			IClientActor *pnext = static_cast<IClientActor *>(pcur->m_pNextObject);

			pcur->clearCollideInfo();
			pcur->release();

			pcur = pnext;
		}
	}
}

void CollisionTree::pos2xyz(const WCoord &pos, int &x, int &y, int &z)
{
	x = pos.x/TREE_NODESIZE_X;
	y = pos.y/TREE_NODESIZE_Y;
	z = pos.z/TREE_NODESIZE_Z;

	if(y < 0) y = 0;
	if(y >= TREE_NODES_Y) y = TREE_NODES_Y-1;
}

void CollisionTree::addActor(IClientActor *pactor)
{
	WCoord pos = pactor->getLocoMotion()->getPosition() - m_Origin;
	assert(pos.x>=0 && pos.z>=0 && pos.x<CHUNK_SIZE_X && pos.z<CHUNK_SIZE_Z);

	int index = pos2Index(pos);

	pactor->m_pNextObject = m_Nodes[index];
	m_Nodes[index] = pactor;

	pactor->addRef();
	pactor->m_CollideTree = this;
	pactor->m_CollideNodeIndex = index;
}

void CollisionTree::removeActor(IClientActor *pactor)
{
	assert(pactor->m_CollideNodeIndex>=0 && pactor->m_CollideNodeIndex<TREE_NODES_TOTAL);
	CollideObject **ppcur = &m_Nodes[pactor->m_CollideNodeIndex];
	while(*ppcur != NULL)
	{
		if(*ppcur == pactor)
		{
			*ppcur = pactor->m_pNextObject;
			pactor->release();
			pactor->clearCollideInfo();
			return;
		}
		ppcur = &((*ppcur)->m_pNextObject);
	}
}

void CollisionTree::onActorMoved(IClientActor *pactor)
{
	WCoord actorpos = pactor->getLocoMotion()->getPosition();
	WCoord pos = actorpos - m_Origin;
	if(pos.x>=0 && pos.z>=0 && pos.x<CHUNK_SIZE_X && pos.z<CHUNK_SIZE_Z)
	{
		if(pos2Index(pos) == pactor->m_CollideNodeIndex) return;
	}

	removeActor(pactor);

	Chunk *pchunk = ClientWorld::GetInstance().getChunk(CoordDivBlock(actorpos));
	if(pchunk)
	{
		pchunk->m_CollideTree->addActor(pactor);
	}
}

bool CollisionTree::isCollide(const WCoord &pos, const WCoord &dim, IClientActor *exclude)
{
	CollideAABB box;
	box.pos = pos;
	box.dim = dim;
	WCoord cpos = box.pos - m_Origin;

	int x1, y1, z1;
	int x2, y2, z2;
	pos2xyz(cpos, x1, y1, z1);
	if(x1 < 0) x1 = 0;
	if(z1 < 0) z1 = 0;

	pos2xyz(cpos+box.dim, x2, y2, z2);
	if(x2 >= TREE_NODES_X) x2 = TREE_NODES_X-1;
	if(z2 >= TREE_NODES_Z) z2 = TREE_NODES_Z-1;

	for(int y=y1; y<=y2; y++)
	{
		for(int z=z1; z<=z2; z++)
		{
			for(int x=x1; x<=x2; x++)
			{
				IClientActor *pcur = static_cast<IClientActor *>(m_Nodes[xyz2Index(x,y,z)]);
				while(pcur != NULL)
				{
					if(pcur != exclude)
					{
						CollideAABB actorbox;
						pcur->getCollideBox(actorbox);
						if(actorbox.intersect(box))
						{
							return true;
						}
					}

					pcur = static_cast<IClientActor *>(pcur->m_pNextObject);
				}
			}
		}
	}
	return false;
}

void CollisionTree::getActorsInBoxExclude(std::vector<IClientActor *>&actors, const CollideAABB &box, IClientActor *exclude)
{
	WCoord cpos = box.pos - m_Origin;

	int x1, y1, z1;
	int x2, y2, z2;
	pos2xyz(cpos, x1, y1, z1);
	if(x1 < 0) x1 = 0;
	if(z1 < 0) z1 = 0;

	pos2xyz(cpos+box.dim, x2, y2, z2);
	if(x2 >= TREE_NODES_X) x2 = TREE_NODES_X-1;
	if(z2 >= TREE_NODES_Z) z2 = TREE_NODES_Z-1;

	for(int y=y1; y<=y2; y++)
	{
		for(int z=z1; z<=z2; z++)
		{
			for(int x=x1; x<=x2; x++)
			{
				IClientActor *pcur = static_cast<IClientActor *>(m_Nodes[xyz2Index(x,y,z)]);
				while(pcur != NULL)
				{
					if(pcur != exclude)
					{
						CollideAABB actorbox;
						pcur->getCollideBox(actorbox);
						if(actorbox.intersect(box))
						{
							actors.push_back(pcur);
						}
					}

					pcur = static_cast<IClientActor *>(pcur->m_pNextObject);
				}
			}
		}
	}
}

void CollisionTree::getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype)
{
	WCoord cpos = box.pos - m_Origin;

	int x1, y1, z1;
	int x2, y2, z2;
	pos2xyz(cpos, x1, y1, z1);
	if(x1 < 0) x1 = 0;
	if(z1 < 0) z1 = 0;

	pos2xyz(cpos+box.dim, x2, y2, z2);
	if(x2 >= TREE_NODES_X) x2 = TREE_NODES_X-1;
	if(z2 >= TREE_NODES_Z) z2 = TREE_NODES_Z-1;

	for(int y=y1; y<=y2; y++)
	{
		for(int z=z1; z<=z2; z++)
		{
			for(int x=x1; x<=x2; x++)
			{
				IClientActor *pcur = static_cast<IClientActor *>(m_Nodes[xyz2Index(x,y,z)]);
				while(pcur != NULL)
				{
					if(pcur->getObjType() == actortype)
					{
						CollideAABB actorbox;
						pcur->getCollideBox(actorbox);
						if(actorbox.intersect(box))
						{
							actors.push_back(pcur);
						}
					}

					pcur = static_cast<IClientActor *>(pcur->m_pNextObject);
				}
			}
		}
	}
}

IClientActor *CollisionTree::pickActor(const WCoord &pos, const Rainbow::Vector3f &dir, IClientActor *exclude)
{
	MINIW::Ray ray;
	ray.m_Origin = (pos-m_Origin).toVector3();
	ray.m_Dir = dir;

	for(int y=0; y<TREE_NODES_Y; y++)
	{
		for(int z=0; z<TREE_NODES_Z; z++)
		{
			for(int x=0; x<TREE_NODES_X; x++)
			{
				IClientActor *pcur = static_cast<IClientActor *>(m_Nodes[xyz2Index(x,y,z)]);
				if(pcur == NULL) continue;

				ray.intersectBox()

			}
		}
	}
}
*/