#pragma once

#include "LegacyModule.h"
#include <map>

namespace Rainbow
{
	class EXPORT_LEGACYMODULE SequenceMap
	{
	public:
		struct SeqDesc
		{
			int seqid;
			int loopmode;
			int priority;
			int subseq[2];
			int replay;
			int halfbody;
			int freeView;
			float speed;
			int nobreak;
		};

	public:

		SequenceMap();
		~SequenceMap();

		void Init(const char* path);

		SeqDesc* findSequenceDesc(int seq);
		SeqDesc* getOrCreateSequenceDesc(int seq);
		bool CanBreak(int seq);
		void copySequenceDesc(int srcSeq, int desSeq);

		//static SequenceMap* GetInstancePtr();

	private:
		std::map<int, SeqDesc>m_SeqMap;
	};

	EXPORT_LEGACYMODULE SequenceMap& GetSequenceMap();
}
