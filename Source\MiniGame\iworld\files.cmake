﻿set(CUR_DIR "iworld")

add_filtered_std(${CUR_DIR})
add_filtered_std("${CUR_DIR}/commondef")
add_filtered_std("${CUR_DIR}/gameFunction")
add_filtered_std("${CUR_DIR}/gameInfo")
add_filtered_std("${CUR_DIR}/gamePlatAbility")
add_filtered_std("${CUR_DIR}/gamePlatSDKUtils")
add_filtered_std("${CUR_DIR}/gameRunTime")
add_filtered_std("${CUR_DIR}/gameEvent")
add_filtered_std("${CUR_DIR}/iworldsystem")
add_filtered_std("${CUR_DIR}/login")

if (ANDROID OR APPLE_IOS OR WIN32 OR LINUX_SERVER)
    add_filtered_std("${CUR_DIR}/ARInterface")
endif()

add_filtered_std("${CUR_DIR}/login/appupdate")
add_filtered_std("${CUR_DIR}/login/appupdate/coldupdate")
add_filtered_std("${CUR_DIR}/login/appupdate/hotfix")

add_filtered_std("${CUR_DIR}/luaInterface")
add_filtered_std("${CUR_DIR}/platform")
add_filtered_std("${CUR_DIR}/uicontrol")
add_filtered_std("${CUR_DIR}/YouMe")
add_filtered_std("${CUR_DIR}/commonparam")
add_filtered_std("${CUR_DIR}/sign")
add_filtered_std("${CUR_DIR}/gameStage")
add_filtered_std("${CUR_DIR}/gameStage/ui_handler")
add_filtered_std("${CUR_DIR}/gameStage/net_handler")
add_filtered_std("${CUR_DIR}/gameStage/load_handler")

if(WINDOWS)
    set(VERSIONINFO_RC "${CMAKE_CURRENT_SOURCE_DIR}/libiworld.rc")

    add_filtered_std("${CUR_DIR}/platform/win32")
    add_filtered_std("${CUR_DIR}/platform/win32/sentry")

    include_directories(${MINI_GAME_SDK_DIR}/qqrailsdk)
    include_directories(${MINI_GAME_SDK_DIR}/qqrailsdk/public)
    include_directories(${MINI_GAME_SDK_DIR}/qqrailsdk/common)
    add_definitions(-DUNICODE -D_UNICODE) # unicode
    # include_directories(${MINI_GAME_SDK_DIR}/YouMeTalkSdk/include)
    add_definitions(-DWIN32_LEAN_AND_MEAN -D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS -DTSF4G_STATIC)
    link_directories(${PROJECT_BINARY_DIR}/lib)

elseif(ANDROID)
    add_filtered_std("${CUR_DIR}/platform/android")

elseif(OHOS)
    add_filtered_std("${CUR_DIR}/platform/ohos")

elseif(APPLE_IOS)
    add_filtered_std("${CUR_DIR}/platform/ios")

elseif(LINUX_SERVER)
    add_filtered_std("${CUR_DIR}/platform/linux")

endif()


if(RAINBOW_SERVER)
    add_filtered_std("${CUR_DIR}/platform/server")
endif()

add_definitions(-DOGRE_STATIC_LIB)

if(WIN_SERVER)
    list(REMOVE_ITEM SOURCE_FILES "${CMAKE_CURRENT_SOURCE_DIR}/platform/win32/ClientToLua.cpp")
else()
    list(REMOVE_ITEM SOURCE_FILES "${CMAKE_CURRENT_SOURCE_DIR}/platform/win32/ClientToLua_server.cpp")
endif()
