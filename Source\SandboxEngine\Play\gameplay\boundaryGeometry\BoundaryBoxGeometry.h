//#pragma once
#ifndef __BOUNDARY_BOX_GEOMETRY__
#define __BOUNDARY_BOX_GEOMETRY__

#include <iostream>
#include "BoundaryGeometry.h"
#include "OgreUtils.h"
#include "world_types.h"

class IClientActor;

#define DECLARE_GET_POINT(CLASS, XDIS, YDIS, ZDIS) \
		Rainbow::Vector3f x##XDIS##y##YDIS##z##ZDIS () const\
		{\
			auto& rotate = getRotate();\
			return getCenter() + (XDIS - 0.5f) * getDimX() * rotate.GetColumn(0) + getDimY() * (YDIS - 0.5f) * rotate.GetColumn(1) + getDimZ() * (ZDIS - 0.5f) * rotate.GetColumn(2); \
		};
#define DECLARE_GET_AXIES(CLASS, DIR, COLUME) \
		Rainbow::Vector3f vector##DIR () const\
		{\
			return getRotate().GetColumn(COLUME);\
		};


class EXPORT_SANDBOXENGINE BoundaryBoxGeometry : public BoundaryGeometry
{
public:
	BoundaryBoxGeometry();
	BoundaryBoxGeometry(const CollideAABB& aabb);
	BoundaryBoxGeometry(const Rainbow::Vector3f& centerPos, const Rainbow::Vector3f& dim, const Rainbow::Matrix3x3f tm);
	Rainbow::Vector3f centerAABB() const //不考虑旋转的情况下
	{
		return m_centerPos;
	};

	Rainbow::Vector3f getCenter() const
	{
		return m_centerPos;
	};

	Rainbow::Vector3f support(Rainbow::Vector3f dir) const;
	//< 0的情况?
	float getDimX() const { return m_dim.x; }
	float getDimY() const { return m_dim.y; }
	float getDimZ() const { return m_dim.z; }
	Rainbow::Vector3f getMinPos() const { return m_centerPos - vectorX() * getDimX() * 0.5 - vectorY() * getDimY() * 0.5 - vectorZ() * getDimZ() * 0.5; }
	Rainbow::Vector3f getMaxPos() const { return m_centerPos + vectorX() * getDimX() * 0.5 + vectorY() * getDimY() * 0.5 + vectorZ() * getDimZ() * 0.5; }
	const Rainbow::Matrix3x3f& getRotate() const { return m_worldRotate; }
	//获取顶点
	DECLARE_GET_POINT(BoundaryBoxGeometry, 0, 0, 0);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 0, 0, 1);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 1, 0, 1);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 1, 0, 0);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 0, 1, 0);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 0, 1, 1);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 1, 1, 1);
	DECLARE_GET_POINT(BoundaryBoxGeometry, 1, 1, 0);
	//获取轴方向的边旋转后的向量.单位向量
	DECLARE_GET_AXIES(BoundaryBoxGeometry, X, 0);
	DECLARE_GET_AXIES(BoundaryBoxGeometry, Y, 1);
	DECLARE_GET_AXIES(BoundaryBoxGeometry, Z, 2);
	bool isValid()const;
	bool isAABB() const;
	bool isOBB() const{ return !isAABB(); }
	//设置轴 
	void setRotate(const Rainbow::Matrix3x3f& tm);
	/// <summary>
	/// 盒子移动, normAxies表示是沿着标准轴移动还是沿着自身的轴移动
	/// </summary>
	/// <param name="to"></param>
	/// <param name="normAxies"></param>
	void move(const Rainbow::Vector3f to, bool normAxies = true);
	void rotation(const Rainbow::Vector3f angles);
	void resetRotation(const Rainbow::Vector3f angles);
	void setSize(int x, int y, int z);
	void reset(const Rainbow::Vector3f size, const Rainbow::Vector3f angles,const Rainbow::Vector3f centerPos);
	//void transform(Rainbow::Transform* t);
private:
	void updateModelWorldMat();
private:
	Rainbow::Vector3f m_dim; //长度
};

//enum WrapBoxCollideType
//{
//	WrapBoxCollideType_AABB,
//	WrapBoxCollideType_OBB
//};


//class WrapBoxExtend
//{
//public:
//	static bool checkBoxBoxCollide(const WrapBoxBody& box1, const WrapBoxBody& box2);
///***********************************AABB*********************************************/
//#ifdef ENABLE_PLAYER_CMD_COMMAND
//	static void drawBox(const WrapBoxBody& box, World* pworld, WrapBoxExtendColor color = WrapBoxExtendColor_Yellow);
//	static void drawAABBBox(const WrapBoxBody& box, World* pworld, WrapBoxExtendColor color = WrapBoxExtendColor_Yellow);
//	static void drawOBBBox(const WrapBoxBody& box, World* pworld, WrapBoxExtendColor color = WrapBoxExtendColor_Yellow);
//	static void drawLine(const Rainbow::Vector3f& point, const Rainbow::Vector3f& point2, World* pworld, WrapBoxExtendColor color = WrapBoxExtendColor_Yellow);
//#endif
///************************************OBB**********************************************/
//	static bool checkBoxBoxCollideAABB(const WrapBoxBody& box1, const WrapBoxBody& box2);
//	static bool checkBoxBoxCollideOBB(const WrapBoxBody& box1, const WrapBoxBody& box2);
//};
#endif