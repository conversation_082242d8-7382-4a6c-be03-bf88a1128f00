#pragma once

#include "base/SandboxRef.h"
#include "SandboxSingleton.h"
#include "base/SandboxWeakRef.h"
#include "SandboxListener.h"
#include "base/SandboxModule.h"

namespace MNSandbox {

	class AssetReqMgrNew;
	class AssetHttpMgr;
	class AssetUploadMgr;
	class AssetAutoCheckMgr;
	class AssetLogMgr;
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
	class AssetDetailMgr;
#endif
	class EXPORT_SANDBOXENGINE AssetMgr :public /*Ref*/MNSandbox::Module<AssetMgr>, public MNSandbox::Singleton<AssetMgr>
	{
	public:
		AssetMgr();
		virtual ~AssetMgr();
		void Gc();
		virtual void Init();
		virtual void Release();
	private:
		void OnTick();
		void OnEnterMap();
		void OnLeaveMap();
	private:
		ListenerClass<AssetMgr> m_listenGlobalTick;
		ListenerClass<AssetMgr> m_listenEnterMap;
		ListenerClass<AssetMgr> m_listenLeaveMap;

		AutoRef<AssetHttpMgr>  m_httpMgr;
		AutoRef<AssetUploadMgr> m_uploadMgr;
		AutoRef<AssetReqMgrNew> m_asseetReqMgr;
		AutoRef<AssetAutoCheckMgr> m_autoCheckMgr;
		AutoRef<AssetLogMgr> m_assetLogMgr;
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
		AutoRef<AssetDetailMgr> m_assetDetailMgr;
#endif
	};

}