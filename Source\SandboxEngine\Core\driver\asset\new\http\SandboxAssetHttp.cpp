#include "SandboxAssetHttp.h"
//#include "SandboxAssetLoderRef.h"
////#include "Utilities/Hash256.h"
#include "SandboxLog.h"
#include "RapidJSON/document.h"
#include "Network/HttpManager.h"
#include "CloudAsset/CloudAssetUser.h"
#include "SandboxAssetRequestMgr.h"

namespace MNSandbox {

	IMPLEMENT_REFCLASS(AssetHttp)

	AssetHttp::AssetHttp()
	{

	}

	AssetHttp::AssetHttp(HttpReq* ptr)
	{
	}

	AssetHttp::~AssetHttp()
	{

	}

	void AssetHttp::RegisterByReqData(HttpReq* ptr)
	{
		SetReqData(ptr);
		RegisterReqCallback(AssetHttpReqType::BackpackGetResByIds, std::bind(&AssetHttp::BackpackGetResByIdsRequst, this, GetReqData()));
		RegisterReqCallback(AssetHttpReqType::BackpackGetResByUin, std::bind(&AssetHttp::BackpackGetResByUinRequst, this, GetReqData()));
		RegisterReqCallback(AssetHttpReqType::GetDownloadUrlByIds, std::bind(&AssetHttp::GetDownloadUrlByIdsRequst, this, GetReqData()));
		
	}

	void AssetHttp::BackpackGetResByIdsRequst(AutoRef<HttpReq> reqData)
	{
		auto data = reqData.ToCast<BackpackGetResByIdsReq>();
		if (!data){
			SANDBOX_WARNING("BackpackGetResByIdsReq is nil");
			return;
		}

		core::string fullUrl;
		GenerateV3QueryHeader("get_res_by_ids", reqData, fullUrl);
#ifndef DEDICATED_SERVER
		//GenerateV3QueryHeader("get_res_by_ids", reqData, fullUrl);
#else
//		auto user = Rainbow::GetCloudAssetUser();
//		std::uint64_t appid = 0;
//		std::string appkey = "";
//#if defined(STUDIO_SERVER)
//		appid = 1024;
//		appkey = "Cufm4KjCy3$aE4s@3bEmBvjHm@nX2qes";
//#else
//		appid = 1018;
//		appkey = "5a$QuG7M$zgF2QzTEQSULuQt2rH5uwfM";
//#endif
//		SInt64 ts = MINIW::GetTimeStamp();
//		char t[128];
//		sprintf(t, "%lli", ts);
//		std::string tim = t;
//		std::string auth = CalcMD5(std::to_string(appid) + appkey + tim);
//		ret = Format("%s/v1/res/download?mini_id=%d&s2t=%d&auth=%s&ts=%lld&appid=%lld&sign_type=server&res_id=%s&http2=0"
//			, user.GetCloudUrl()
//			, 0
//			, 0
//			, auth.c_str()
//			, ts
//			, appid
//			, resid.c_str()
//		);
#endif
		std::ostringstream urlOss;
		urlOss << fullUrl.c_str() << "&ids=" << data->m_ids;
		fullUrl = urlOss.str();
		Rainbow::Http::GetHttpManager().Request(fullUrl, "", (void*)this, 0, nullptr, std::bind(&AssetHttp::WebRequestRsp, this, std::placeholders::_1, std::placeholders::_2));
	}

	void AssetHttp::BackpackGetResByUinRequst(AutoRef<HttpReq> reqData)
	{
		auto data = reqData.ToCast<BackpackGetResByUinReq>();
		if (!data){
			SANDBOX_ASSERTEX(false, "not BackpackGetResByUinReq");
			return;
		}

		core::string fullUrl;
		GenerateV3QueryHeader("get_res_by_uin", reqData, fullUrl);

		std::ostringstream urlOss;
		urlOss << fullUrl.c_str()
			<< "&rtype=" << data->m_uiType
			<< "&folder=" << data->m_folder
			<< "&page=" << data->m_page
			<< "&page_size=" << data->m_pageSize
			<< "&op_uin=" << data->m_optUin;
		fullUrl = urlOss.str();
		Rainbow::Http::GetHttpManager().Request(fullUrl, "", (void*)this, 0, nullptr, std::bind(&AssetHttp::WebRequestRsp, this, std::placeholders::_1, std::placeholders::_2));
	}


	void AssetHttp::GetDownloadUrlByIdsRequst(AutoRef<HttpReq> reqData)
	{
		SANDBOX_ASSERTEX(false, "v3?");
//		auto user = Rainbow::GetCloudAssetUser();
//#ifndef DEDICATED_SERVER
//		int uin = user.GetMiniId();
//		std::string auth = user.GetCloudAssetAuth();
//		long long curTime = user.GetTime();
//		long long s2t = user.GetServerToken();
//		ret = Format("%s/v1/res/download?mini_id=%d&s2t=%lld&auth=%s&ts=%lld&res_id=%s&http2=0"
//			, user.GetCloudUrl()
//			, uin
//			, s2t
//			, auth.c_str()
//			, curTime
//			, resid.c_str()// m_provenance.GetDownloadKey().c_str()
//		);
//#else
//		//GenerateRequestServerUrl(resid, ret);
//#endif //#ifndef DEDICATED_SERVER
//#ifdef DEDICATED_SERVER
//		auto user = Rainbow::GetCloudAssetUser();
//		std::uint64_t appid = 0;
//		std::string appkey = "";
//#if defined(STUDIO_SERVER)
//		appid = 1024;
//		appkey = "Cufm4KjCy3$aE4s@3bEmBvjHm@nX2qes";
//#else
//		appid = 1018;
//		appkey = "5a$QuG7M$zgF2QzTEQSULuQt2rH5uwfM";
//#endif
//		SInt64 ts = MINIW::GetTimeStamp();
//		char t[128];
//		sprintf(t, "%lli", ts);
//		std::string tim = t;
//		std::string auth = CalcMD5(std::to_string(appid) + appkey + tim);
//		ret = Format("%s/v1/res/download?mini_id=%d&s2t=%d&auth=%s&ts=%lld&appid=%lld&sign_type=server&res_id=%s&http2=0"
//			, user.GetCloudUrl()
//			, 0
//			, 0
//			, auth.c_str()
//			, ts
//			, appid
//			, resid.c_str()
//		);
//#endif
	}
}