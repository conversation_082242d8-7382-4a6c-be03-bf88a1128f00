#pragma once
/**
* file : StuidoWorkerModule
* func : studio工作模块（studio代码嵌入sandbox 太深，平移过来）
*/
#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
#include "base/SandboxType.h"
#include "base/SandboxModule.h"

namespace MINIW {

	class StudioWorker : public MNSandbox::Module<StudioWorker>
	{
	public:
		StudioWorker() = default;
		virtual ~StudioWorker() = default;

		// 初始化
		virtual void Init() override;
		// 释放
		virtual void Release() override;

	};

}

#endif