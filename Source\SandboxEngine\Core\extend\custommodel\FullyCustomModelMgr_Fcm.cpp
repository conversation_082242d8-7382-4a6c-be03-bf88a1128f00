#include "FullyCustomModelMgr.h"
#include "FullyCustomModel.h"
#include "FullyCustomBoneData.h"
#include "PackingFullyCustomModelMgr.h"

#include "File/FileManager.h"
#include "File/DirVisitor.h"

#include "IPlayerControl.h"
#include "CustomModelMgr.h"
#include "DefManagerProxy.h"
#include "ClientInfoProxy.h"
#include "SandboxGFunc.h"
#include "IActorBody.h"
#include "PlayManagerInterface.h"
#include "WorldManager.h"

using namespace MNSandbox;
using namespace Rainbow;

void FullyCustomModelMgr::loadOneMapFullyCustomModelData(long long owid, int realowneruin, std::string& filename, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/fully", rootpath.c_str(), owid);
	std::string filepath = path;
	filepath +=  "/" + filename + ".fcm";

	FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
	if (fullycustommodel->load(filepath, filename, realowneruin, NULL, false, NULL, specialType))
	{
		m_vMapFcms.push_back(fullycustommodel);

		auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(fullycustommodel->getKey());
		if (customItem && customItem->itemid > 0)
		{
			GetDefManagerProxy()->addDefByCustomModel(customItem->itemid, customItem->type, filename, fullycustommodel->getName(), fullycustommodel->getDesc(), Rainbow::Vector3f(0, 0, 0), customItem->involvedid);
		}
	}
	else
		ENG_DELETE(fullycustommodel);
}

void FullyCustomModelMgr::preMapFullyCustomModelData(long long owid, int specialType/* = NORMAL_WORLD*/)
{
	loadResFullyCustomModel();

	m_CurOWID = owid;
	m_nSpecialType = specialType;
	clearMapFullyModels();

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/fully/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
}

void FullyCustomModelMgr::afterLoadMapPackingFcm(long long owid, int realowneruin, int specialType)
{
	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->loadMapPackingFcm(owid, realowneruin, specialType);
}

void FullyCustomModelMgr::loadMapFullyCustomModelData(long long owid, int realowneruin, int specialType/* = NORMAL_WORLD*/)
{
	loadResFullyCustomModel();

	m_CurOWID = owid;
	m_nSpecialType = specialType;
	clearMapFullyModels();

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/fully/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/fully", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath +=  "/" + filename + ".fcm";


		FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, realowneruin, NULL, false, NULL, specialType))
		{
			m_vMapFcms.push_back(fullycustommodel);

			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(fullycustommodel->getKey());
			if (customItem && customItem->itemid > 0)
			{
				GetDefManagerProxy()->addDefByCustomModel(customItem->itemid, customItem->type, filename, fullycustommodel->getName(), fullycustommodel->getDesc(), Rainbow::Vector3f(0, 0, 0), customItem->involvedid);
			}
		}
		else
			ENG_DELETE(fullycustommodel);
	}

	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->loadMapPackingFcm(owid, realowneruin, specialType);
}

void FullyCustomModelMgr::loadOneModFullyCustomRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod/* =false */, bool ignorecheck/* =false */)
{
	if (findFullyCustomModel(MAP_MODEL_CLASS, modelkey))
		return;

	char path[256];
	sprintf(path, "%s/resource/custommodel/fully/%s.fcm", modroot.c_str(), modelkey.c_str());
	if (!GetFileManager().IsFileExistWritePath(path))
	{
		sprintf(path, "%s/resource/custommodel/fully/packing/%s.pfcm", modroot.c_str(), modelkey.c_str());
	}

	FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
	LoadMapModData modData;
	modData.realowneruin = realowneruin;
	modData.modroot = modroot;
	modData.isLibMod = islibmod;


	if (fullycustommodel->load(path, modelkey, realowneruin, NULL, false, &modData, 0, ignorecheck))
	{
		if (islibmod)
			m_vResFcms.push_back(fullycustommodel);
		else
			m_vMapFcms.push_back(fullycustommodel);
	}
	else
		ENG_DELETE(fullycustommodel);
}

void FullyCustomModelMgr::loadOneModFullyCustomRes(long long owid, std::string modelkey, int realowneruin, bool ignorecheck /*= false*/) // 资源整合加载单独的一个文件资源
{
	if (findFullyCustomModel(MAP_MODEL_CLASS, modelkey))
		return;

	char path[256];
	sprintf(path, "data/w%lld/custommodel/fully/%s.fcm", owid, modelkey.c_str());
	if (!GetFileManager().IsFileExistWritePath(path))
	{
		sprintf(path, "data/w%lld/custommodel/fully/packing/%s.pfcm", owid, modelkey.c_str());
	}

	FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
	LoadMapModData modData;
	modData.realowneruin = realowneruin;
	modData.modroot = "";
	modData.isLibMod = false;


	if (fullycustommodel->load(path, modelkey, realowneruin, NULL, false, &modData, 0, ignorecheck))
	{
		m_vMapFcms.push_back(fullycustommodel);
	}
	else
		ENG_DELETE(fullycustommodel);
}

bool FullyCustomModelMgr::moveResFullyCustomModelToMap(std::string filename, std::string classname,int folderindex, bool ispacking/* =false */)
{
	if (ispacking)
	{
		if (m_pPackingFCMMgr)
			return m_pPackingFCMMgr->moveResPackingFcmToMap(filename, classname, folderindex, m_CurOWID, m_nSpecialType);

		return false;
	}
	else
	{
		std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

		char srcPath[128];
		char destPath[128];
		sprintf(srcPath, "data/custommodel/fully/%s.fcm", filename.c_str());
		sprintf(destPath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, filename.c_str());
		GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

		FCMMoveFileLoadData data;
		data.moveFromLibType = RES_MODEL_CLASS;
		data.owid = m_CurOWID;
		data.classname = classname;
		data.folderindex = folderindex;

		FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(destPath, filename, GetClientInfoProxy()->getUin(), &data, false, NULL, m_nSpecialType))
		{	
			m_vMapFcms.push_back(fullycustommodel);		
			return true;
		}
		else
		{
			ENG_DELETE(fullycustommodel);
			return false;
		}
	}	
}

void FullyCustomModelMgr::moveMapFullyCustomModelToRes(std::string filename, bool ispacking/* =false */)
{
	if (ispacking)
	{
		if (m_pPackingFCMMgr)
			m_pPackingFCMMgr->moveMapPackingFcmToRes(filename, m_CurOWID, m_nSpecialType);
	}
	else
	{
		std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

		char srcPath[128];
		char destPath[128];
		sprintf(srcPath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, filename.c_str());
		sprintf(destPath, "data/custommodel/fully/%s.fcm", filename.c_str());
		GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

		FCMMoveFileLoadData data;
		data.moveFromLibType = MAP_MODEL_CLASS;
		data.owid = m_CurOWID;
		FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(destPath, filename, GetClientInfoProxy()->getUin(), &data, false, NULL, m_nSpecialType))
		{
			m_vResFcms.push_back(fullycustommodel);
			//fullycustommodel->moveMapAvatarModelToRes(m_CurOWID);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
}

void FullyCustomModelMgr::loadResFullyCustomModel()
{
	if (m_bLoadedRes) //�?load总库
		return;

	m_bLoadedRes = true;

	char dir[32];
	sprintf(dir, "data/custommodel/fully/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	clearResFullyModels();
	char path[32];
	sprintf(path, "data/custommodel/fully");
	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath += "/" + filename + ".fcm";

		FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_vResFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}

	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->loadResPackingFcm();
}


void FullyCustomModelMgr::loadResFullyCustomModel(int eFcmModelClass, std::string filename)
{
	char dir[32];
	std::string filepath;
	switch (eFcmModelClass)
	{
	default:
		sprintf(dir, "data/custommodel/fully/");
		if (!GetFileManager().IsFileExistWritePath(dir))
		{
			GetFileManager().CreateWritePathDir(dir);
		}
		filepath = dir + filename + ".fcm";
		break;
	case PREVIEW_MODEL_CLASS:
		sprintf(dir, "data/custommodel_pre/fully/");
		if (!GetFileManager().IsFileExistWritePath(dir))
		{
			GetFileManager().CreateWritePathDir(dir);
		}
		filepath = dir + filename + ".fcm";
		break;
	case EDITOR_MODEL_CLASS:
		filepath = filename;
		break;
	}
	if (eFcmModelClass == RES_MODEL_CLASS)
	{
		auto iter = m_vResFcms.begin();
		for (; iter != m_vResFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_vResFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
	else if (eFcmModelClass == MAP_MODEL_CLASS)
	{
		auto iter = m_vMapFcms.begin();
		for (; iter != m_vMapFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_vMapFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
	else if (eFcmModelClass == PREVIEW_MODEL_CLASS)
	{
		auto iter = m_vPreviewResFcms.begin();
		for (; iter != m_vPreviewResFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, FCM_PREVIEW_IGNORE_CHECK_UIN))
		{
			m_vPreviewResFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
	else if (eFcmModelClass == EDITOR_MODEL_CLASS)
	{
		auto iter = m_vEditorFcms.begin();
		for (; iter != m_vEditorFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, FCM_PREVIEW_IGNORE_CHECK_UIN))
		{
			m_vEditorFcms.push_back(fullycustommodel);
		}
		else
			OGRE_DELETE(fullycustommodel);
	}
}


void FullyCustomModelMgr::loadResFullyCustomModelByPath(int type, std::string filename, std::string filepath)
{
	if (type == RES_MODEL_CLASS)
	{
		auto iter = m_vResFcms.begin();
		for (; iter != m_vResFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_vResFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
	else if (type == MAP_MODEL_CLASS)
	{
		auto iter = m_vMapFcms.begin();
		for (; iter != m_vMapFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_vMapFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		auto iter = m_vPreviewResFcms.begin();
		for (; iter != m_vPreviewResFcms.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
		if (fullycustommodel->load(filepath, filename, FCM_PREVIEW_IGNORE_CHECK_UIN))
		{
			m_vPreviewResFcms.push_back(fullycustommodel);
		}
		else
			ENG_DELETE(fullycustommodel);
	}
}

void FullyCustomModelMgr::loadEquipFullyCustomModel(bool isReload/* = false*/)
{
	if (!isReload)
	{
		static bool isLoaded = false;
		if (isLoaded)
			return;
		isLoaded = true;
	}

	char dir[256];
	char path[256] = "data/custommodel_equip/fully";

	sprintf(dir, "%s/", path);

	if (GetFileManager().IsFileExistWritePath(dir))
	{
		clearEquipResFullyModels();
		OneLevelScaner scaner;
		scaner.setRoot(GetFileManager().GetWritePathRoot());
		scaner.scanTree(path, 1);

		for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
		{
			std::string filename = it->c_str();
			std::string filepath = path;
			filepath += "/" + filename + ".fcm";

			FullyCustomModel* fullycustommodel = ENG_NEW(FullyCustomModel)();
			int checkuin = FCM_LOAD_IGNORE_CHECK_UIN;	//不校�?
			if (fullycustommodel->load(filepath, filename, checkuin))
			{
				m_vEquipResFcms.push_back(fullycustommodel);
			}
			else
				ENG_DELETE(fullycustommodel);
		}

		if (m_pPackingFCMMgr)
			m_pPackingFCMMgr->loadResPackingFcm();
	}
}

FullyCustomModel* FullyCustomModelMgr::getFullyCustomModelByIndex(int type, int index)
{
	if (type == EQUIP_MODEL_CLASS)
	{
		assert(index >= 0 && index < (int)m_vEquipResFcms.size());
		if (m_vEquipResFcms.size() > 0)
			return m_vEquipResFcms[index];
	}

	return NULL;
}

int FullyCustomModelMgr::getFullyCustomModelNum(int type)
{
	if (type == MAP_MODEL_CLASS)
		return m_vMapFcms.size();
	else if (type == RES_MODEL_CLASS)
		return m_vResFcms.size();
	else if (type == EQUIP_MODEL_CLASS)
		return m_vEquipResFcms.size();
	else if (type == PREVIEW_MODEL_CLASS)
		return m_vPreviewResFcms.size();

	return 0;
}

Rainbow::IActorBody* FullyCustomModelMgr::getCurEditActorBody(bool needcreate/* =true */)
{
	if (!needcreate)
		return m_pCurEditActorBody;

	if (m_pCurEditActorBody)
		SANDBOX_DELETE(m_pCurEditActorBody);

	m_pCurEditActorBody = GetISandboxActorSubsystem()->CreateFullyCustomModelActorBody(MAP_MODEL_CLASS, m_pCurEditFcm); // ENG_NEW(ActorBody)(NULL);
	//m_pCurEditActorBody->initFullyCustomActor(MAP_MODEL_CLASS, m_pCurEditFcm, "", true);
#if defined(BUILD_MINI_EDITOR_APP)
	m_pCurEditFcm->updateBindModel(m_pCurEditActorBody->getEntity());
#endif

	return m_pCurEditActorBody;
}


FullyCustomModel *FullyCustomModelMgr::getCurEditFullyCustomModel()
{
	return m_pCurEditFcm;
}


FullyCustomModel *FullyCustomModelMgr::findFullyCustomModel(int eFcmModelClass, std::string skey, bool toFindPackingFcm/* =false */)
{
		OPTICK_EVENT();
	const std::vector<FullyCustomModel*>* pvFcms = getVectorFcms(eFcmModelClass);
	if (!pvFcms)
	{
		return nullptr;
	}
	const std::vector<FullyCustomModel*>& vFcms = *pvFcms;
	for (int i = 0; i < (int)vFcms.size(); i++)
	{
		if (vFcms[i]->getKey() == skey)
		{
			return vFcms[i];
		}
	}
	switch (eFcmModelClass)
	{
		case MAP_MODEL_CLASS:
		case RES_MODEL_CLASS:
		case PREVIEW_MODEL_CLASS:
			if (toFindPackingFcm && m_pPackingFCMMgr)
			{
				return m_pPackingFCMMgr->findPackingFcm(eFcmModelClass, skey);
			}
			break;
	}

	return NULL;
}

std::vector<FullyCustomModel*>* FullyCustomModelMgr::getVectorFcms(int eFcmModelClass)
{
	std::vector<FullyCustomModel*>* pvFcms = nullptr;
	switch (eFcmModelClass)
	{
		case MAP_MODEL_CLASS:
			pvFcms = &m_vMapFcms;
			break;
		case RES_MODEL_CLASS:
			pvFcms = &m_vResFcms;
			break;
		case EQUIP_MODEL_CLASS:
			pvFcms = &m_vEquipResFcms;
			break;
		case PREVIEW_MODEL_CLASS:
			pvFcms = &m_vPreviewResFcms;
			break;
		case EDITOR_MODEL_CLASS:
			pvFcms = &m_vEditorFcms;
			break;
	}
	return pvFcms;
}



bool FullyCustomModelMgr::removeOldFullyCustomModel(int eFcmImportType, std::string skey)
{
	vector<FullyCustomModel*>* p = NULL;
	switch (eFcmImportType)
	{
		case MAP_MODEL_CLASS:
			p = &m_vMapFcms;
			break;
		case EDITOR_MODEL_CLASS:
			p = &m_vEditorFcms;
			break;
		default:
			return false;
	}
	vector<FullyCustomModel*>& v = *p;
	auto it = v.begin();
	for (; it != v.end(); it++)
	{
		if ((*it)->getKey() == skey)
		{
			OGRE_DELETE(*it);
			v.erase(it);
			return true;
		}
	}
	return false;
}

void FullyCustomModelMgr::removeEditFailModel()
{
	auto iter = m_vMapFcms.begin();
	for (; iter != m_vMapFcms.end(); iter++)
	{
		if ((*iter) == m_pCurEditFcm)
		{
			ENG_DELETE(*iter);
			m_vMapFcms.erase(iter);
			return;
		}
	}
}

bool FullyCustomModelMgr::setCurEditModel(const std::string& skey, int eFcmImportType /* = NEW_NORMALE_FCM_MODEL */)
{
	return (m_pCurEditFcm = getOrCreate(skey, eFcmImportType))!= nullptr;
}

void FullyCustomModelMgr::setCurEditModel(FullyCustomModel* fcm)
{
	m_pCurEditFcm = fcm;
}

void FullyCustomModelMgr::addFcm(FullyCustomModel* fcm, int eFcmImportType)
{
	if (!fcm)
	{
		return;
	}
	std::vector<FullyCustomModel*>* pvFcms = nullptr;
	if (eFcmImportType == EDITOR_FCM_MODEL)
	{
		pvFcms = &m_vEditorFcms;
	}
	else
	{
		pvFcms = &m_vMapFcms;
	}
	std::vector<FullyCustomModel*>& vFcms = *pvFcms;
	std::vector<FullyCustomModel*>::iterator it;
	it = std::find(vFcms.begin(), vFcms.end(), fcm);
	if (it != vFcms.end())
	{
		it = vFcms.erase(it);
	}
	vFcms.emplace_back(fcm);
}

bool FullyCustomModelMgr::isEditorFcm(FullyCustomModel* fcm)
{
	std::vector<FullyCustomModel*>::iterator it;
	it = std::find(m_vEditorFcms.begin(), m_vEditorFcms.end(), fcm);
	return it != m_vEditorFcms.end();
}

Rainbow::Entity* FullyCustomModelMgr::getEntity(std::string skey, bool bUpdateWhenCustomModelReady/* = false*/, bool ispacking/* =false */)
{
	return getEntityByResClass(MAP_MODEL_CLASS, skey, bUpdateWhenCustomModelReady, ispacking);
}

Rainbow::Entity *FullyCustomModelMgr::getEntityByResClass(int resClass, std::string skey, bool bUpdateWhenCustomModelReady/* = false*/, bool ispacking/* =false */)
{	
	FullyCustomModel *fcm = findFullyCustomModel((int)resClass, skey, true);

	if(!fcm)
		fcm = findFullyCustomModel(EQUIP_MODEL_CLASS, skey, true);

	if (!fcm)
	{
		if (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->isRemoteMode() && !ispacking)
		{
			addDownload(skey);
		}
		return NULL;
	}

	Entity* pEntity = Entity::Create();

	SharePtr<ModelData> modeldata = MakeSharePtr<ModelData>();
	modeldata->addCustomSequenceID(0, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100100, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100101, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100102, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100103, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100104, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100105, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100106, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100107, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100108, ANIM_MODE_LOOP);
	modeldata->addCustomSequenceID(100109, ANIM_MODE_LOOP);

	Model* pModel = Model::Model::CreateInstanceLegacy(modeldata, true);
	pModel->SetCustomPrepareFinish(false);
	pEntity->Load(pModel);

	fcm->setModelData(pEntity, modeldata, NULL, bUpdateWhenCustomModelReady);

	pModel->SetCustomPrepareFinish(true);
	pModel->CalculateBoundFromSkinMesh();
	pEntity->UpdateBindFather();

	if (fcm->getModelType() == FULLY_PACKING_CUSTOM_MODEL)
	{
		WCoord dim = fcm->getPackingFcmDim() + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
		int maxLen = Max(dim.x, dim.y);
		maxLen = Max(maxLen, dim.z);
		if (maxLen >= BLOCK_SIZE)
		{
			float scale = 1.0f / (maxLen / BLOCK_SIZE);
			pEntity->SetScale(scale);
		}

	}

	return pEntity;
}

PackingFullyCustomModelMgr *FullyCustomModelMgr::getPackingFCMMgr()
{
	return m_pPackingFCMMgr;
}

void FullyCustomModelMgr::clearMapFullyModels()
{
	for (int i = 0; i < (int)m_vMapFcms.size(); i++)
	{
		if (m_vMapFcms[i] != NULL)
		{
			ENG_DELETE(m_vMapFcms[i]);
		}
	}
	m_vMapFcms.clear();
}

void FullyCustomModelMgr::clearResFullyModels()
{
	for (int i = 0; i < (int)m_vResFcms.size(); i++)
	{
		if (m_vResFcms[i] != NULL)
		{
			ENG_DELETE(m_vResFcms[i]);
		}
	}
	m_vResFcms.clear();
}

void FullyCustomModelMgr::clearEquipResFullyModels()
{
	for (int i = 0; i < (int)m_vEquipResFcms.size(); i++)
	{
		if (m_vEquipResFcms[i] != NULL)
		{
			ENG_DELETE(m_vEquipResFcms[i]);
		}
	}
	m_vEquipResFcms.clear();
}

void FullyCustomModelMgr::clearPreviewResFullyModels()
{
	for (int i = 0; i < (int)m_vPreviewResFcms.size(); i++)
	{
		if (m_vPreviewResFcms[i] != NULL)
		{
			ENG_DELETE(m_vPreviewResFcms[i]);
		}
	}
	m_vPreviewResFcms.clear();
}

Rainbow::Entity* FullyCustomModelMgr::getCurEntity()
{
	if (!m_pCurEditFcm)
		return nullptr;

	if (!m_pCurEditActorBody)
		return nullptr;

	return m_pCurEditActorBody->getEntity();
}

Rainbow::Model* FullyCustomModelMgr::getCurModel()
{
	Rainbow::Entity* entity = getCurEntity();
	if (!entity)
		return nullptr;

	return entity->GetMainModel();
}

Rainbow::SharePtr<Rainbow::ModelData> FullyCustomModelMgr::getCurModelData()
{
	Rainbow::Model* model = getCurModel();
	if (!model)
		return nullptr;

	if (model->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(model);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			return legacymodel->GetModelData();
		}
	}

	return nullptr;
}

bool FullyCustomModelMgr::isDownloadFCM(std::string filename)
{
	auto *fcm = findFullyCustomModel(MAP_MODEL_CLASS, filename, true);
	if(!fcm)
		fcm = findFullyCustomModel(RES_MODEL_CLASS, filename, true);

	if (fcm)
		return fcm->isDownload();

	return false;
}