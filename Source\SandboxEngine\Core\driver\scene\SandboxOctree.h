#pragma  once
#include "SandboxSingleton.h"
#include "SceneManagement/GenericOctree.h"
#include "SceneManagement/GenericOctreePublic.h"
#include "SceneManagement/GenericOctreeMGT.hpp"
#include "SceneManagement/SceneManagement.hpp"
#include "SceneManagement/SceneMGTNode.hpp"

namespace MNSandbox
{
	class SceneOctreeSemantics
	{
	public:
		static const int MaxNodeDepth = 10;
		static const int MaxElementsPerLeaf = 32;
		static const int MinInclusiveElementsPerNode = 7;

		static Rainbow::AABB GetBoundingBox(Rainbow::SceneMGTNode* Obj)
		{
			return Obj->GetAABB();
		}
		static const Rainbow::Algorithm::OctreeObjectHandle& GetElementHandle(Rainbow::SceneMGTNode* Obj)
		{
			return Obj->GetMGTHandle();
		}
		static void SetElementHandle(Rainbow::SceneMGTNode* Obj, const Rainbow::Algorithm::OctreeObjectHandle& handle)
		{
			Obj->SetMGTHandle(handle);
		}
	};
}
