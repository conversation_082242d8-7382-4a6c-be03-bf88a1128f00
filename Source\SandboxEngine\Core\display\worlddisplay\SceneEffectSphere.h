#pragma once
/**
* file : SceneEffectSphere
* func : 场景效果 （球体）
* by : chenzh
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectSphere : public SceneEffectGeom
{
public:
	SceneEffectSphere();
	SceneEffectSphere(const MNSandbox::MNCoord3f& pos, const MNSandbox::MNCoord3f& sphereDir, float radius, 
		//int accuracyGrade = 5, float ratio = 1.0, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, CurveFace* curveFaces = nullptr);
		int accuracyGrade, float ratio, CURVEFACEMTLTYPE mtltype);
	SceneEffectSphere(const MNSandbox::MNCoord3f& center, float radius);
	virtual ~SceneEffectSphere();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;

public:
	// 设置比率
	void SetRatio(float ratio);
	// 设置中心位置
	void SetSphere(MNSandbox::MNCoord3f pos, MNSandbox::MNCoord3f sphereDir);

	// 射线选中
	bool RaySphere(MNSandbox::Ray& ray, MNSandbox::MNCoord3f& targetPos);

	void SetRadius(float radius) override;

	// 设置球面精度级别
	void SetAccuracyGrade(int accuracyGrade);
	int GetAccuracyGrade() const { return m_accuracyGrade; }
	
	void SetColor(BlockVector color) override;

	void SetDefaultOffset(int defaultOffset);
	
protected:
	// 计算VB 和 IB
	void CalcVertexsAndIndices(float radius, int accuracyGrade);

private:
	std::vector<BlockGeomVert> m_Vertexs; // 顶点缓存
	std::vector<unsigned short> m_Indices; // 索引缓存

	MNSandbox::MNCoord3f m_sphereDir;
	MNSandbox::MNCoord3f m_drawpos;
	// 属性
	int m_accuracyGrade = 5; // 球面精度级别
	int m_defaultOffset = 80;				//默认偏移
	int m_lenOffset = m_defaultOffset;

	bool m_Translucent = false;
	// 缓存
	bool m_needRefreshFlag = true;
};
