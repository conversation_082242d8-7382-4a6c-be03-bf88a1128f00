
#ifndef __I_CLIENT_ACTOR_H__
#define __I_CLIENT_ACTOR_H__

#include "OgreWCoord.h"
#include "SceneManagement/SceneMGTNode.hpp"
#include "misc/Collision.h"
#include "json/jsonxx.h"
#include "ActorTypes.h"
//#include "blocks/container_world.h"
#include "actors/helper/Actor_Base.h"
#include "ChunkSave_generated.h"

#ifndef SAVE_BUFFER_BUILDER
typedef flatbuffers::FlatBufferBuilder SAVE_BUFFER_BUILDER;
#endif

class World;
class ActorManagerInterface;
class IClientActorFuncWrapper;
class IActorLocoMotion;
class IActorAttrib;
class IClientPlayer;
class NavigationPath;

enum ComponentType
{
	COMPONENT_ACTOR_LIVING_ATTRIB,
	COMPONENT_RIDDEN,				//RiddenComponent
	COMPONENT_FISHING,				//FishingComponent
	COMPONENT_TO_ATTACK_TARGET,		//ToAttackTargetComponent
	COMPONENT_PLAYER_ATTRIB,		//PlayerAttrib
	COMPONENT_ACTOR_IN_PORTAL,		//ActorInPortal
	COMPONENT_PHYSICS,				//PhysicsComponent
	COMPONENT_SOUND,				//SoundComponent
	COMPONENT_ATTACK,				//AttackedComponent

	COMPONENT_THORN_BALL,			// ThornBallComponent
	COMPONENT_EFFECT,				// EffectComponent

	COMPONENT_BIND_ACTOR,			// BindActorComponent
	COMPONENT_DIE,					//DieComponent
};

namespace Rainbow
{
	class IActorBody;
	class Transform;
}
namespace FBSave
{
	class SectionActor;
}
namespace MNSandbox 
{
	class EventObjectManager;
}
namespace game
{
	namespace hc
	{
		class PB_GeneralEnterAOIHC;
	}
}
class Section;
class ClientActor;
/* ClientActor 基类 */
class EXPORT_SANDBOXENGINE IClientActor : public ActorBase, public Rainbow::SceneMGTNode // tolua_export
{// tolua_export
public:
	IClientActor() {};
	virtual ~IClientActor() {};
	virtual IClientPlayer* CastToPlayer() = 0;
	virtual ClientActor* GetActor() = 0;
	virtual void release() = 0;
	virtual void addRef() = 0;
	virtual IClientActor* IClone() = 0;
	virtual void prepareTick() = 0;
	virtual void tick() = 0;
	virtual void update(float dtime) = 0;
	virtual void setPosition(const WCoord& pos) = 0;
	virtual bool isSupportSaveExt() = 0;
	virtual void getCollideBox(CollideAABB& box) = 0;
	virtual void getMultiCollideBox(std::vector<CollideAABB>& boxs) = 0;
	virtual void getHitCollideBox(CollideAABB& box) = 0;
	virtual int getEyeHeight() = 0;
	virtual Rainbow::AABB GetAABB() = 0;

	virtual int getObjType() const = 0;
	virtual int getNetSyncPeriod() = 0;
	virtual std::string getActorType() = 0;
	virtual float getFacePitch() = 0;
	virtual float getNavigationFacePitch() = 0;
	virtual long long getObjId() = 0;
	virtual Rainbow::IActorBody* GetIBody() = 0;
	virtual WCoord& getPosition() = 0;
	virtual WCoord getEyePosition() = 0;
	virtual void getPosition(int& x, int& y, int& z) = 0;
	virtual bool isSupportSyncExt() = 0;
	virtual bool supportSaveToPB() = 0;
	virtual void legacyLoad(bool& find, bool& ret, const FBSave::SectionActor* s, int version) = 0;
	virtual float getCustomScale() = 0;
	// 这个函数导致了#include "ChunkSave_generated.h"  可以考虑优化一下
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) = 0;
	virtual bool load(const void* srcdata, int version) = 0;
	virtual void save(jsonxx::Object& obj) = 0;
	virtual bool load(const jsonxx::Object& obj, int version) = 0;
	virtual void* saveToBuffer(size_t& buflen) = 0;
	virtual World* getWorld() = 0;
	virtual bool needSaveInChunk() = 0;
	virtual bool getUsingEmitter() = 0;
	virtual	int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb) = 0;
	virtual bool needClear() = 0;
	virtual bool canBeCollidedWith() = 0;
	virtual int getMass() { return 60000 ; }
	virtual bool getOnRailState(WCoord& railknot, int& outindex, float& t, int& flags) = 0;
	virtual bool preventActorSpawning() = 0;

	virtual bool intersectBox(const CollideAABB& box) = 0;

	virtual void addCollisionDetect(CollisionDetect& cd, const CollideAABB& rangebox) = 0;

	virtual double getSquareDistToPos(double x, double y, double z) = 0; //距离的平方
	virtual unsigned short getCurMapID() = 0;
	virtual ActorManagerInterface* GetActorMgrInterface() = 0;

	virtual bool getReverse() = 0;

	virtual bool getClimbing() = 0;
	virtual bool isVehicleController() = 0;
	virtual bool isInWater() = 0;
	virtual WORLD_ID getBeHurtTargetID() = 0;
	virtual int GetJumpHighest() = 0;
	virtual int GetJumpLongest() = 0;
	virtual int GetPathHideRange() = 0;
	virtual bool CanAttackByItemSkill(int skillId, IClientActor* player) = 0;
	virtual void stopMotion(const char* name) = 0;
	virtual bool getSitting() = 0;
	virtual int getViewDist() = 0;
	virtual bool isMotionChange() = 0;
	virtual bool CanNavigate() = 0;

	virtual bool canNavigation() = 0;

	virtual int getDefID() = 0;
	virtual void SetXrayEffectEnable(bool enable) = 0;

	virtual void playAnim(int seq, bool include_me = false, int loop = -1) = 0;
	virtual bool playAnimById(int id, int inputloopmode = -1, int playLayer = -1) = 0;
	virtual float getFaceYaw() = 0;

	virtual void BindNavigationPath(MNSandbox::SceneComponent* pComponent) = 0;

	virtual void ClearMoveForward() = 0;

	virtual IClientActorFuncWrapper* getActorFuncWrapper() = 0;

	virtual WCoord GetActorPosition() = 0;

	virtual int GetPathableYPos() = 0;

	virtual bool HasLivingLocoMotion() = 0;

	virtual bool HasTrixenieLocomotion() = 0;

	virtual void HandleLocomotionForNavigation( int type, bool noPath) = 0;

	virtual void SetJumpToTarget(const WCoord& target) = 0;

	virtual void SetTarget(const WCoord& target, float speed) = 0;

	virtual IActorLocoMotion* getILocoMotion() = 0;
	//获取引擎Transform
	virtual Rainbow::Transform* GetTransform() = 0;
	virtual MNSandbox::Component* getLocoMotionComponent() = 0;

	virtual bool isPlayer() = 0;

	virtual bool getRun() = 0;

	virtual void setNeedClear(int delay_ticks = 0) = 0;

	virtual bool IsLoadModelFinished() = 0;
	virtual bool IsObject() = 0;
	virtual bool isExistInCollide(const WCoord& blockpos) = 0;
	virtual long long GetParentWID() = 0;
	virtual int GetItemId() = 0;
	virtual bool AttackedFrom(OneAttackData& atkdata, IClientActor* inputattacker) = 0;
	virtual bool HasPhysActor() = 0;
	virtual bool IsClientActorProjectile() = 0;
	virtual bool IsActorVehicleAssemble() = 0;
	virtual void setMotionChange(float x, float y, float z, bool addmotion = false, bool changepos = false) = 0;
	virtual void onEnterSection(Section* section) = 0;
	virtual void onLeaveSection(Section* section) = 0;
	virtual void updateSectionIsDisplay(Section* section) = 0;
	virtual MNSandbox::Component* getActorAttackedComponent() = 0;
	virtual MNSandbox::Component* getActorSoundComponent() = 0;
	virtual MNSandbox::Component* getAttribComponent() = 0;
	virtual MNSandbox::Component* getActorComponent(ComponentType type) = 0;

	virtual IActorAttrib* GetIActorAttrib() = 0;
	virtual bool isSleeping() = 0;
	virtual bool isRestInBed() = 0;
	virtual bool IsEmpty() = 0;
	virtual bool IsInWorld() = 0;
	virtual void SetLastAcotrTick(int count) = 0;
	virtual int GetLastAcotrTick() = 0;
	virtual int GetSpaceTickPriority() = 0;
	virtual bool isActorVehicleAssemble() = 0;
	virtual void SetLastAcotrSelect(int count) = 0;
	virtual int GetLastAcotrSelect() = 0;
	virtual void SetLastAcotrUpdate(int count) = 0;
	virtual inline int GetLastAcotrUpdate() = 0;
	virtual float GetAccumulateDtime() = 0;
	virtual void SetAccumulateDtime(float value) = 0;
	virtual int getChangeFlag() = 0;
	virtual void setFlagBit(int ibit, bool b) = 0;
	virtual bool managedByChunk() = 0;
	virtual void setCanControl(bool b) = 0;
	virtual const Rainbow::Vector3f GetWorldPosition() = 0;
	virtual bool IsControlByScript() = 0;
	virtual void getsyncData(jsonxx::Object& data) = 0;
	virtual bool isChangePos() = 0;
	virtual void clearMotionChange() = 0;
	virtual bool needSyncAttribChanges() = 0;
	virtual void jumpOnce() = 0;
	virtual void kill() = 0;
	virtual bool getFlying() = 0;
	virtual bool isDead() = 0;
	virtual bool canControl() = 0;
	virtual void SyncPosition(const WCoord& pos, int yaw, int pitch, float angle = 30.0f) = 0;
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	virtual bool NeedSupportExSandboxsInfo() const = 0;
#endif

	virtual NavigationPath* getNavigator() = 0;
	virtual void getBodySize(float& width, float& height) = 0;
	//设置实体的位置
	virtual void SetWorldPosition(float x, float y, float z, bool igoreLerp = false) = 0;

	virtual void TriggerScriptComponent(const std::vector<int>& enterList) = 0;
};// tolua_export

#endif
