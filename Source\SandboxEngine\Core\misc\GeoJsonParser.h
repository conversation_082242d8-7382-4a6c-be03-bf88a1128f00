#pragma once
#include "utils/json.hpp"
#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <limits>

#include "SandboxEngine.h"

using json = nlohmann::json;

// 几何类型枚举
enum class GeometryType {
    Point,
    MultiPoint,
    LineString,
    MultiLineString,
    Polygon,
    MultiPolygon,
    GeometryCollection,
    Unknown
};

// 添加CRS相关的枚举和结构
enum class CRSType {
    Name,
    Link,
    Default,  // WGS84
    Unknown
};

struct CRS {
    CRSType type;
    std::string name;     // 用于name类型
    std::string href;     // 用于link类型
    std::string linkType; // 用于link类型的媒体类型

    // 默认构造函数 - WGS84
    CRS() : type(CRSType::Default), name("urn:ogc:def:crs:OGC::CRS84") {}
    
    // Name类型构造函数
    static CRS createName(const std::string& crsName) {
        CRS crs;
        crs.type = CRSType::Name;
        crs.name = crsName;
        return crs;
    }

    // Link类型构造函数
    static CRS createLink(const std::string& href, const std::string& type = "") {
        CRS crs;
        crs.type = CRSType::Link;
        crs.href = href;
        crs.linkType = type;
        return crs;
    }
};

// 坐标点结构
struct GeoPoint {
    double longitude;
    double latitude;
    double altitude;  // 可选
    
    GeoPoint(double lon, double lat, double alt = 0.0)
        : longitude(lon), latitude(lat), altitude(alt) {}
};

// 基础几何对象
struct Geometry {
    GeometryType type;
    virtual ~Geometry() = default;
};

// 具体几何类型
struct PointGeometry : Geometry {
    GeoPoint point;
    PointGeometry(const GeoPoint& p) : point(p) {
        type = GeometryType::Point;
    }
};

struct LineStringGeometry : Geometry {
    std::vector<GeoPoint> points;
    LineStringGeometry(const std::vector<GeoPoint>& pts) : points(pts) {
        type = GeometryType::LineString;
    }
};

struct PolygonGeometry : Geometry {
    std::vector<std::vector<GeoPoint>> rings;  // 第一个是外环，其余是内环
    PolygonGeometry(const std::vector<std::vector<GeoPoint>>& r) : rings(r) {
        type = GeometryType::Polygon;
    }
};

// 添加新的几何类型结构
struct MultiPointGeometry : Geometry {
    std::vector<GeoPoint> points;
    MultiPointGeometry(const std::vector<GeoPoint>& pts) : points(pts) {
        type = GeometryType::MultiPoint;
    }
};

struct MultiLineStringGeometry : Geometry {
    std::vector<std::vector<GeoPoint>> lineStrings;
    MultiLineStringGeometry(const std::vector<std::vector<GeoPoint>>& lines) 
        : lineStrings(lines) {
        type = GeometryType::MultiLineString;
    }
};

struct MultiPolygonGeometry : Geometry {
    std::vector<std::vector<std::vector<GeoPoint>>> polygons;
    MultiPolygonGeometry(const std::vector<std::vector<std::vector<GeoPoint>>>& polys) 
        : polygons(polys) {
        type = GeometryType::MultiPolygon;
    }
};

struct GeometryCollectionGeometry : Geometry {
    std::vector<std::shared_ptr<Geometry>> geometries;
    GeometryCollectionGeometry(const std::vector<std::shared_ptr<Geometry>>& geoms) 
        : geometries(geoms) {
        type = GeometryType::GeometryCollection;
    }
};

// Feature结构
struct Feature {
    std::shared_ptr<Geometry> geometry;
    json properties;
    std::string id;  // 可选
    CRS crs;  // 新增CRS支持
};

struct ValidationResult {
    bool isValid;
    std::string error;
    std::vector<std::string> warnings;
    
    ValidationResult() : isValid(true) {}
    
    void addError(const std::string& msg) {
        isValid = false;
        error = msg;
    }
    
    void addWarning(const std::string& msg) {
        warnings.push_back(msg);
    }
};

class EXPORT_SANDBOXENGINE GeoJsonParser {
public:
    // 几何计算常量
    static constexpr double EARTH_RADIUS = 6371008.8; // 地球平均半径（米）
    static constexpr double PI = 3.14159265358979323846;

    // 几何计算类
    class GeometryCalculator {
    public:
        // 距离计算
        static double distance(const GeoPoint& p1, const GeoPoint& p2);
        static double length(const std::vector<GeoPoint>& points);
        static double length(const std::shared_ptr<Geometry>& geometry);

        // 面积计算
        static double area(const std::vector<GeoPoint>& ring);
        static double area(const std::vector<std::vector<GeoPoint>>& rings);
        static double area(const std::shared_ptr<Geometry>& geometry);

        // 包围盒计算
        struct BoundingBox {
            double minLon, minLat, maxLon, maxLat;
            BoundingBox() : minLon(180), minLat(90), maxLon(-180), maxLat(-90) {}
        };
        static BoundingBox getBoundingBox(const std::shared_ptr<Geometry>& geometry);

        // 点是否在多边形内
        static bool pointInPolygon(const GeoPoint& point, const std::vector<GeoPoint>& ring);
        static bool pointInPolygon(const GeoPoint& point, const std::vector<std::vector<GeoPoint>>& rings);

    private:
        static double toRadians(double degrees);
        static double haversineDistance(const GeoPoint& p1, const GeoPoint& p2);
        static double signedArea(const std::vector<GeoPoint>& ring);
        static void updateBoundingBox(BoundingBox& box, const GeoPoint& point);
    };

    struct BoundingBox {
        double west;   // 最小经度
        double south;  // 最小纬度
        double east;   // 最大经度
        double north;  // 最大纬度
        double minAlt; // 最小高度（可选）
        double maxAlt; // 最大高度（可选）
        bool has3D;    // 是否包含高度信息

        BoundingBox() : west(180), south(90), east(-180), north(-90),
                       minAlt(std::numeric_limits<double>::max()),
                       maxAlt(std::numeric_limits<double>::lowest()),
                       has3D(false) {}

        std::vector<double> toVector() const;
        static BoundingBox fromVector(const std::vector<double>& bbox);
        bool isValid() const;
        bool crossesAntimeridian() const;
    };    

    static Feature parseFeature(const json& featureJson);
    static std::shared_ptr<Geometry> parseGeometry(const json& geometryJson);
    static std::vector<Feature> parseFeatureCollection(const std::string& geojsonStr);

    static json geometryToJson(const std::shared_ptr<Geometry>& geometry);
    static json featureToJson(const Feature& feature);
    static json featureCollectionToJson(const std::vector<Feature>& features);
    static std::string stringify(const json& j, bool pretty = false);

    static CRS parseCRS(const json& crsJson);
    static json crsToJson(const CRS& crs);

    static ValidationResult validate(const json& geojson);
    static ValidationResult validateGeometry(const std::shared_ptr<Geometry>& geometry);
    static ValidationResult validateFeature(const Feature& feature);
    static ValidationResult validateFeatureCollection(const std::vector<Feature>& features);


    static BoundingBox calculateBoundingBox(const std::shared_ptr<Geometry>& geometry);
    static BoundingBox calculateBoundingBox(const Feature& feature);
    static BoundingBox calculateBoundingBox(const std::vector<Feature>& features);
    static BoundingBox mergeBoundingBoxes(const BoundingBox& box1, const BoundingBox& box2);

    static GeoPoint parsePoint(const json& coordinates);
    static std::vector<GeoPoint> parseLineString(const json& coordinates);
    static std::vector<std::vector<GeoPoint>> parsePolygon(const json& coordinates);
    static GeometryType getGeometryType(const std::string& typeStr);

    static json pointToJson(const GeoPoint& point);
    static json lineStringToJson(const std::vector<GeoPoint>& points);
    static json polygonToJson(const std::vector<std::vector<GeoPoint>>& rings);

    static std::vector<GeoPoint> parseMultiPoint(const json& coordinates);
    static std::vector<std::vector<GeoPoint>> parseMultiLineString(const json& coordinates);
    static std::vector<std::vector<std::vector<GeoPoint>>> parseMultiPolygon(const json& coordinates);
    static std::vector<std::shared_ptr<Geometry>> parseGeometryCollection(const json& geometries);

    static bool validateType(const json& obj, ValidationResult& result);
    static bool validateCoordinates(double lon, double lat, double alt, ValidationResult& result);
    static bool validatePosition(const GeoPoint& point, ValidationResult& result);
    static bool validateLinearRing(const std::vector<GeoPoint>& ring, ValidationResult& result);
    static bool validatePolygonRings(const std::vector<std::vector<GeoPoint>>& rings, ValidationResult& result);
    static bool validateBoundingBox(const json& bbox, ValidationResult& result);
    static bool validateGeometryType(const std::string& type, ValidationResult& result);

    static void updateBoundingBox(BoundingBox& box, const GeoPoint& point);
    static bool validateBoundingBox(const std::vector<double>& bbox);
    static bool calculateRingOrientation(const std::vector<GeoPoint>& ring);
    static std::string geometryTypeToString(GeometryType type);
};
