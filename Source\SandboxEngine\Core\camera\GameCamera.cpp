#include "GameCamera.h"
#include "Geometry/AABB.h"
#include "IPlayerControl.h"
#include "IClientActor.h"
#include "IClientPlayer.h"
#include "CameraModel.h"
#include "OgreRay.h"
#include "OgreScriptLuaVM.h"
#include "IActorLocoMotion.h"
#include "world.h"
#include "proto_ch.pb.h"
#include "GameNetManager.h"
#include "IRecordInterface.h"
#include "CameraManager.h"
#include "DebugDataMgr.h"
#include "BlockScene.h"
#include "Debug/DebugMgr.h"
#include "OgreCameraAnim.h"
#include "IWorldConfigProxy.h"
#if GIZMO_DRAW_ENGABLE
#include "Gizmo/GizmoManager.h"
#endif

#if PLATFORM_ANDROID || PLATFORM_OHOS
#include "Platforms/ChannelList.h"
#endif//OGRE_PLATFORM == OGRE_PLATFORM_ANDROID

#include "SandboxContext.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxLuaPluginManager.h"
#include "Misc/TimeManager.h"
#include "WorldRender.h"
#include "ClientInfoProxy.h"
#include "Misc/FrameTimeManager.h"
#include "OgreUtils.h"
#include "ModelItemMesh.h"
#include "Entity/OgreEntity.h"

#include "gamemode/GameModeDef.h"
#include "IMiniDeveloperProxy.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;


scriptCamera::scriptCamera()
: m_fRotateY(0)
, m_fRotateX(0)
, m_pCamera(NULL)
, m_vLookDir(Rainbow::Vector3f(0, 0, 1.0f))
, m_wPosition(Rainbow::WorldPos(0, 0, 0))
, m_nWinWidth(0)
, m_nWinHeight(0)
, m_fFov(0)
, m_fCurrentFov(0)
, m_strScriptName("")
{
    
}

scriptCamera::~scriptCamera()
{
    DESTORY_GAMEOBJECT_BY_COMPOENT(m_pCamera);
}

void scriptCamera::initScriptCamera(const char* scriptName, float fov, float fwidth, float fheight, float fnear, float ffar, const Rainbow::WorldPos& pos)
{
    //m_pCamera = new Camera;

    AssertMsg(fnear >= ffar, "Incorrect near plane [%f] or far plane [%f] for camera!", fnear, ffar);

    if (GetIPlayerControl() && GetIPlayerControl()->getIWorld() && GetIPlayerControl()->getIWorld()->getRender())
    {
        BlockScene* scene = GetIPlayerControl()->getIWorld()->getRender()->getScene();

        Rainbow::GameObject* cameraGo = Rainbow::GameObject::Create();
        m_pCamera = cameraGo->CreateComponent<Rainbow::Camera>();
        m_pCamera->SetRenderFeatureFlags(kRenderFeatureDefault);
        scene->AddGameObject(cameraGo);


#if GIZMO_DRAW_ENGABLE  && !DEDICATED_SERVER
        GetGizmoManager().AttachedCamera(m_pCamera);
#endif
    }
    else
    {
        assert(false);
        m_pCamera = NULL;
    }

    m_nWinHeight = fheight;
    m_nWinWidth = fwidth;
    m_fCurrentFov = fov;
    m_fFov = fov;
    m_wPosition = pos;
    m_pCamera->SetAspect(float(fwidth) / (float)fheight);
    m_pCamera->SetNear(fnear);
    m_pCamera->SetFar(ffar);
    m_pCamera->SetVerticalFieldOfView(fov);
    m_pCamera->SetWorldPosition(m_wPosition.toVector3());
    m_strScriptName = scriptName;

    m_MoveSpeed = 10;
    m_RotSpeed = 3;
    m_MoveForward = 0;
    m_MoveStrafe = 0;
    m_RotY = 0;
    m_RotX = 0;
    _pos = m_wPosition.toVector3();
    _rot = m_pCamera->GetWorldRotation();
    if (!m_strScriptName.empty())
    {
        this->LuaPluginMgr().BindLuaPluginByFileT<scriptCamera>(scriptName);
    }
}

void scriptCamera::update(float dtime)
{
    if (m_strScriptName.length())
    {
        //��ʱ����  ���Ӻ��ű����д���� ���滻д��
        char buff[128];
        snprintf(buff, sizeof(buff) - 1, "%s_update", m_strScriptName.c_str());
        MINIW::ScriptVM::game()->callFunction(buff, "f", dtime);
    }
    else
    {
// 		_pos += m_MoveForward * GetForward() * m_MoveSpeed * dtime;
// 		_pos += m_MoveStrafe * GetRight() * m_MoveSpeed * dtime;
// 		m_pCamera->setPosition(WorldPos::initVector3(_pos));
// 
// 		_rot *= Rainbow::Quaternionf::Euler(m_RotY * dtime, m_RotX * dtime, 0);
// 		m_pCamera->setRotation(_rot);
 
// 		_pos += m_MoveForward * GetForward() * m_MoveSpeed;
// 		_pos += m_MoveStrafe * GetRight() * m_MoveSpeed;
// 		m_pCamera->setPosition(WorldPos::initVector3(_pos));
// 		m_pCamera->setRotation(_rot);
// 		LOG_INFO("~!~!~~~!!!!!!~~~~~~!@!!!!!!!~!~!~ pos x = %f, y = %f, z = %f, rot x = %f, y = %f, z = %f", _pos.x, _pos.y, _pos.z, _rot.x, _rot.y, _rot.z);
    }
}

// int scriptCamera::onInputEvent(const Rainbow::LegacyInputEvent& event)
// {
// 	int w = Root::GetInstance().getClientWidth();
// 	int h = Root::GetInstance().getClientHeight();
// 
// 	if (event.msg == GIE_MOUSEMOVE)
// 	{
// 		int dx = event.mouse.x - w / 2;
// 		int dy = event.mouse.y - h / 2;
// 
// 		m_RotX = float(dx) * 3.f / w;
// 		m_RotY = float(dy) * 3.f / h;
// 
// 		_rot *= Rainbow::Quaternionf::Euler(m_RotY, m_RotX, 0);
// // 		m_pCamera->setRotation(_rot);
// 
// 	}
// 	else if (event.msg == GIE_KEYDOWN)
// 	{
// 		if (event.key.vkey == 97)
// 		{
// 			m_MoveStrafe = 1.0f;
// 		}
// 		else if (event.key.vkey == 98)
// 		{
// 			m_MoveForward = -1.0f;
// 		}
// 		else if (event.key.vkey == 99)
// 		{
// 			m_MoveStrafe = -1.0f;
// 		}
// 		else if (event.key.vkey == 101)
// 		{
// 			m_MoveForward = 1.0f;
// 		}
// // 		_pos += m_MoveForward * GetForward() * m_MoveSpeed;
// // 		_pos += m_MoveStrafe * GetRight() * m_MoveSpeed;
// // 		m_pCamera->setPosition(WorldPos::initVector3(_pos));
// 	}
// 	else if (event.msg == GIE_KEYUP)
// 	{
// 		if (event.key.vkey == 97 || event.key.vkey == 99)
// 		{
// 			m_MoveStrafe = 0.0f;
// 		}
// 		else if (event.key.vkey == 98 || event.key.vkey == 101)
// 		{
// 			m_MoveForward = 0.0f;
// 		}
// 	}
// 	return 1;
// }

void scriptCamera::applyToEngine(World* pworld)
{
    if (m_strScriptName.length())
    {
        SandboxResult ret = Event().Emit("applyToEngine", SandboxContext(this).SetData_Userdata("World", pworld));
        if (ret.IsExecSuccessed())
        {
            Quaternionf quat;
            WorldPos position;
            CameraData* pData = ret.GetData_Usertype<CameraData>();
            quat = Quaternionf(pData->getQuatX(), pData->getQuatY(), pData->getQuatZ(), pData->getQuatW());
            position = WorldPos(pData->getPosX(), pData->getPosY(), pData->getPosZ());
            m_pCamera->SetWorldPosition(position.toVector3());
            m_pCamera->SetWorldRotation(quat);
        }
        else
        {
            
        }
    }
    else
    {
    }
}


void scriptCamera::setRotation(Rainbow::Quaternionf quat)
{
    m_vLookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
    m_pCamera->SetWorldRotation(quat);
}

void scriptCamera::setPosition(Rainbow::WorldPos pos)
{
    m_pCamera->SetWorldPosition(pos.toVector3());
}

void scriptCamera::setLookAt(Rainbow::WorldPos eye, Rainbow::Vector3f dir, Rainbow::Vector3f up)
{
    m_pCamera->LookAt(eye.toVector3(), eye.toVector3()+ dir, up);
}

void scriptCamera::setFov(float fov)
{
    m_pCamera->SetVerticalFieldOfView(fov);
}

Rainbow::WorldPos scriptCamera::getPosition()
{
    return Rainbow::WorldPos::fromVector3(m_pCamera->GetPosition());
}

Rainbow::Quaternionf scriptCamera::getRotation()
{
    return m_pCamera->GetWorldRotation();
}

GameCamera::GameCamera() : m_RotateYaw(0), m_RotatePitch(0), m_CurrentShakeTime(0), m_Mode(CameraControlMode::CAMERA_FPS), m_LookDir(0, 0, 1.0f),
                           m_WinWidth(800), m_WinHeight(600), m_Fov(60.0f), m_ZoomIn(false),
                           m_NeedZoom(false), m_CurZoomTime(0), m_ZoomBeginTime(1.0f), m_bInterpRot(true), m_CameraLerpSpeed(20.0f), m_vInterpCamPos(0, 0, 0),
                           m_ZoomRecoverTime(1.0f), m_DistMultiply(1.0f), m_ShakeRotZ(0), m_CamAnim(nullptr)
{
    m_CameraModel = NULL;
    m_Player = GetIPlayerControl();
    
    InitEngineCamera();

    m_CurrentLocalPosY = 0;
    m_EnableCameraBob = false;

    m_IsNeedHideHand = false;
    m_IsNeedShowHand = false;

    m_SneakOffset = 0.5f * Rainbow::Vector3f(0, -1, 0);
    m_CurrentFov = m_Fov;
    m_StartZoomFov = m_Fov;
    m_DeltaFov = 0;
    m_TriggerZoom = false;
    m_TargetFov = 0;

    m_ShowHandDuration = 0.2f;
    m_HideHandDuration = 0.15f;
    m_LerpStartMark = -1;
    m_SwithThingHandlowBound = -25.0f;
    m_Position.x = 0;
    m_Position.y = 0;
    m_Position.z = 0;
    m_MoveForward = m_MoveUp = m_MoveRight = 0.0f;
    
    
    //20210724: ��������API  codeby:wangshuai
    m_ShakingPower.x = 0;
    m_ShakingPower.y = 0;
    m_ShakingPower.z = 0;
    m_ShakingDuration = 0;
        //20210824: ��������API  codeby:wangshuai
    m_ShakingOriginDuration = 0;
}

void GameCamera::InitEngineCamera(World* world)
{
    


    if(world == nullptr)
    {
        if(m_Player && m_Player->getIWorld())
        {
            world = m_Player->getIWorld();
        }
    }

    if (world && world->getRender())
    {
        m_pCamera = world->getRender()->GetCamera();
        m_pCamera->SetAspect(float(m_WinWidth) / m_WinHeight);
        m_pCamera->SetNear(5.0f);
        m_pCamera->SetFar(30000.0f);
        m_pCamera->SetWorldPosition(m_Player->GetPlayerControlPosition().toVector3());

        /*this->m_FrontCamera = world->getRender()->GetFrontCamera();
        this->m_FrontCamera->SetAspect(this->m_pCamera->GetAspect());
        this->m_FrontCamera->SetNear(this->m_pCamera->GetNear());
        this->m_FrontCamera->SetFar(this->m_pCamera->GetFar());
        this->m_FrontCamera->SetWorldPosition(m_Player->getPosition().toVector3());*/
    }
    else
    {
        m_pCamera = nullptr;
        //m_FrontCamera = nullptr;
    }
}

GameCamera::~GameCamera()
{
	m_pCamera = nullptr; // DESTORY_GAMEOBJECT_BY_COMPOENT(m_pCamera);
	//this->m_FrontCamera = nullptr;
    if (m_CameraModel)
    {
        m_CameraModel->m_GameCamera = NULL;
        m_CameraModel = NULL;
    }
    
    for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
    {
        ENG_DELETE(iter->second);
    }
    m_mScriptCameras.clear();
}

template<typename T>
T __value_initialization() {
    return T();
}
template<> Rainbow::Quaternionf __value_initialization() {
    return Rainbow::Quaternionf::identity;
}
template<> Rainbow::WorldPos __value_initialization() {
	return Rainbow::WorldPos(0,0,0);
}
template<typename T>
T GameCamera::InternalInterp(const T& target, T& last, float dt, float lerpSpeed)
{
	if (!m_bInterpRot)
	{
		return target;
	}

	if (last == __value_initialization<T>())
	{
		last = target;
		return target;
	}

	last = Lerpto(last, target, dt, lerpSpeed);
	return last;
}
void GameCamera::update(float dtime, World *pworld)
{
    if (m_Player == NULL)
    {
        m_Player = GetIPlayerControl();
        return;
    }
    //if (m_Player == NULL) return;
    auto actor = dynamic_cast<IClientActor*>(m_Player);
    auto player = dynamic_cast<IClientPlayer*>(m_Player);
    if ((actor->isSleeping() || actor->isRestInBed() || player->isUseHearth()) && m_Mode == CAMERA_FPS)
    {
        return;
    }

    if(m_pCamera.Get() == nullptr) return;

    bool bOpenSightingTeleScope = player->GetGunZoom();

	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);
	if (m_WinWidth != w || m_WinHeight != h)
	{
		setScreenSize(w, h);
	}

    if (bOpenSightingTeleScope)
    {
        m_IsNeedHideHand = true;
    }

    //Start switch thing in hand
    if (m_IsNeedHideHand)
    {
        m_IsNeedShowHand = false;

        if (bOpenSightingTeleScope || GetTimeSinceStartup() - m_LerpStartMark < m_HideHandDuration)
        {
            m_CurrentLocalPosY = Lerp(0, m_SwithThingHandlowBound, clamp01((GetTimeSinceStartup() - m_LerpStartMark) / m_HideHandDuration));
        }
        else
        {
            m_IsNeedShowHand = true;
            m_IsNeedHideHand = false;
            m_LerpStartMark = GetTimeSinceStartup();
            m_Player->switchCurrentItem();
        }
        /*

        m_CurrentLocalPosY = Lerp(m_CurrentLocalPosY, lowBound, switchSpeed * dtime);
        if (Abs(lowBound - m_CurrentLocalPosY) < 20.0f)
        {
            m_IsNeedHideHand = false;
            m_IsNeedShowHand = true;
            m_Player->switchCurrentItem();
        }*/
    }

    //After switched thing in hand
    if (m_IsNeedShowHand)
    {
        //float emptyHandTargetY = -20.0f;
        float targetPosY = 0;

        //Hand
        //if (m_Player->getCurObjId() == 0)
        //	targetPosY = emptyHandTargetY;

        if (GetTimeSinceStartup() - m_LerpStartMark < m_ShowHandDuration)
        {
            m_CurrentLocalPosY = Lerp(m_SwithThingHandlowBound, targetPosY, clamp01((GetTimeSinceStartup() - m_LerpStartMark) / m_ShowHandDuration));
        }
        else
        {
            m_LerpStartMark = -1;
            m_IsNeedShowHand = false;
        }
    }

    if (m_TriggerZoom)
    {
        if (m_ZoomBeginTime == 0.f)
            m_CurZoomTime = kMaxFloat;
        else
            m_CurZoomTime += dtime / m_ZoomBeginTime;
    }
    else
    {
        if (m_ZoomRecoverTime == 0.f)
            m_CurZoomTime = kMaxFloat;
        else
            m_CurZoomTime += dtime / m_ZoomRecoverTime;
    }
    if (m_CurZoomTime > 1.0f) m_CurZoomTime = 1.0f;
    
    // 如果是跑沙盒系統由沙盒摄像机节点控制 fieldview
    auto playercontrol = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
    if (!playercontrol->IsRunSandboxPlayer())
    {
        float t = (m_CurZoomTime * m_CurZoomTime + m_CurZoomTime * 2.0f) / 3.0f;
        //m_CurrentFov = Lerp(targetFov, m_CurrentFov, 3* )
        m_CurrentFov = m_StartZoomFov + t * (m_DeltaFov);
        m_pCamera->SetVerticalFieldOfView(m_CurrentFov);
    }
    if (GetIPlayerControl() && GetIPlayerControl()->getBobbingByRocket())
    {
        m_CurrentShakeTime += dtime;
    }

    applyToEngine(pworld);

    //20210724: ��������API  codeby:wangshuai
    if (GetIPlayerControl() && (GetIPlayerControl()->isShakingCamera()))
    {
        m_ShakingDuration -= dtime;
        if (m_ShakingDuration < 0 ){
            GetIPlayerControl()->setShakeCamera(false);
        }
    }


#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
    // TODO OgreArRenderer.h ͷ�ļ���ʱδͬ���������棬��ʱע����δ���   --by joker
    /*MINIW::ArRenderer* arRenderer = ArRenderer::GetInstancePtr();
    if( arRenderer&&arRenderer->GetArCameraEnable())
    {
        GetIPlayerControl()->getIWorld()->setRenderTrunk(false);
        WorldPos posOffset(0.0f, 0.0f, 0.0f);
        arRenderer->ApplyArCamera(m_pCamera, posOffset);
    }*/
#endif
    //����mvp����
    // no use m_pCamera->update(dtick);
    for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
    {
        // no use iter->second->m_pCamera->update(dtick);
        iter->second->update(dtime);
    }

    if (m_IsNeedShowHand || m_IsNeedHideHand)
    {
        //���ֵ�λ�Ʋ�
        const Rainbow::Vector3f localDelta = RotateVectorByQuat(m_pCamera->GetWorldRotation(), m_CurrentLocalPosY* Rainbow::Vector3f::yAxis);

        if (m_CameraModel && m_CameraModel->isShow())
        {
            m_CameraModel->update(dtime, getPosition() + localDelta, m_pCamera->GetWorldRotation());
        }
    }
    else
    {
        if (m_CameraModel && m_CameraModel->isShow())
        {
            m_CameraModel->update(dtime, getPosition(), m_pCamera->GetWorldRotation());
        }
    }

    if (m_CameraModel)
    {
        if (m_CameraModel->isShowHomeTaskDir())
        {
            m_CameraModel->updateHomeTask(dtime, getPosition(), m_pCamera->GetWorldRotation());
        }
    }

    CollideAABB aabb;
    m_Player->getPlayerControlLocoMotion()->getCollideBox(aabb);
    DebugDataMgr::GetInstance().drawBounds(&aabb);
}

void GameCamera::AttachCameralModeToScene(Rainbow::GameScene* scene)
{
    if (m_CameraModel == nullptr) return;

    m_CameraModel->AttachToScene(scene);
}

void GameCamera::tick()
{
    if (m_CameraModel != NULL)
    {
        m_CameraModel->tick();
    }
}

void GameCamera::setBobbing(bool b)
{
    if (m_CameraModel == NULL) return;
    m_EnableCameraBob = b && GetIWorldConfigProxy()->getGameData("camerashake");
    m_CameraModel->setBobbing(m_EnableCameraBob);
    if (!b)
    {
        m_CameraModel->resetShakeTime();
    }
    auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
    if (player && player->getSpectatorUin())
    {
        if (GetIPlayerControl()->getIWorld()->isRemoteMode())
        {
            //��ս��Ϣͬ��������
            PB_SetBobbingToSpectatorCH setBobbingCH;
            setBobbingCH.set_spectatoruin(player->getSpectatorUin());
            setBobbingCH.set_tospectatoruin(player->getUin());
            setBobbingCH.set_bobbing(b);

            GetGameNetManagerPtr()->sendToHost(PB_SET_BOBBING_SPECTATOR_CH, setBobbingCH);
        }
        else
        {
            PB_SetBobbingToSpectatorHC setBobbingHC;
            setBobbingHC.set_spectatoruin(player->getSpectatorUin());
            setBobbingHC.set_tospectatoruin(player->getUin());
            setBobbingHC.set_bobbing(b);

            GetGameNetManagerPtr()->sendToClient(player->getSpectatorUin(), PB_SET_BOBBING_SPECTATOR_HC, setBobbingHC);
        }

    }
}

bool GameCamera::getBobbing()
{
    return m_EnableCameraBob;
}

void GameCamera::setZoomInOut(float targetFov, int zoomticks /*= 0*/, int recoverticks /*= 6*/)
{
    m_TargetFov = targetFov;
    m_StartZoomFov = m_CurrentFov;
    m_NeedZoom = true;
    m_ZoomBeginTime = zoomticks * GAME_TICK_TIME;
    m_ZoomRecoverTime = recoverticks * GAME_TICK_TIME;
    m_DeltaFov = targetFov - m_CurrentFov;
    m_TriggerZoom = true;
    m_CurZoomTime = 0;
}

void GameCamera::resetZoom()
{
    m_NeedZoom = false;
    m_StartZoomFov = m_Fov;
    m_CurrentFov = m_Fov;
    m_DeltaFov = 0;
    m_TriggerZoom = false;
    m_CurZoomTime = 0;
    m_TargetFov = 0;
}

void GameCamera::setFov(float fov)
{
    // LOG_INFO("setFov(): fov = %f", fov);
    m_StartZoomFov = m_CurrentFov = m_Fov = fov;
    if (m_TargetFov == 0) m_DeltaFov = 0;
}

Rainbow::Camera* GameCamera::getEngineCamera()
{
    if (m_pCamera.Get() == nullptr)
    {
        InitEngineCamera();
    }
    AssertMsg(m_pCamera, "Camera is NULL!");
    return m_pCamera;
}
void GameCamera::disableZoom()
{
    m_NeedZoom = false;
    m_StartZoomFov = m_CurrentFov;
    m_DeltaFov = m_Fov - m_CurrentFov;
    m_TriggerZoom = false;
    m_CurZoomTime = 0;
    m_TargetFov = 0;
}

void GameCamera::setMode(CameraControlMode mode)
{
    if (m_Mode == mode) return;
    m_qInterpRotate = Rainbow::Quaternionf::identity;
    m_vInterpCamPos = WorldPos(0,0,0);
    m_Mode = mode;
    if (!GetIPlayerControl() || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
    {
        return;
    }
    onChangeViewMode();
    if (mode == CAMERA_CUSTOM_VIEW)
    {
        GetIPlayerControl()->resetCurCameraConfig(true);
    }
    else
    {
        GetIPlayerControl()->resetCurCameraConfig(false);

        if (CameraManager::GetInstance().getCameraEditState() != CAMERA_EDIT_STATE_NULL)
        {
            MINIW::ScriptVM::game()->callFunction("CameraFrameCloseBtn_OnClick", "");
        }
    }
    GetIPlayerControl()->applyCurCameraConfig();

    //Warning !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! delete in release
/*
    DebugDataMgr::GetInstance().toggleRenderInfo();
    SurviveGame *pgame = dynamic_cast<SurviveGame *>(GetClientGameManagerPtr()->getCurGame());
    if (pgame)
    {
        pgame->m_OpenCmd = true;
    }*/
}


void GameCamera::setScreenSize(int width, int height)
{
    m_WinWidth = width;
    m_WinHeight = height;

    if(m_pCamera.Get() == nullptr) return;
    if (m_WinHeight == 0)
    {
        m_pCamera->SetAspect(1.0f);
        for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
        {
            iter->second->m_pCamera->SetAspect(1.0f);
            iter->second->m_nWinWidth = width;
            iter->second->m_nWinHeight = height;
        }
    }
    else
    {
        m_pCamera->SetAspect(float(m_WinWidth) / m_WinHeight);
        for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
        {
            iter->second->m_pCamera->SetAspect(float(m_WinWidth) / m_WinHeight);
            iter->second->m_nWinWidth = width;
            iter->second->m_nWinHeight = height;
        }
    }

	//this->m_FrontCamera->SetAspect(this->m_pCamera->GetAspect());
}

void GameCamera::setCameraMode(CameraModel* cameraModel)
{
    m_CameraModel = cameraModel;
    if (cameraModel) cameraModel->m_GameCamera = this;
}

Rainbow::Quaternionf GameCamera::getRotation()
{
    Quaternionf quat = AngleEulerToQuaternionf(Vector3f(m_RotatePitch, m_RotateYaw, 0));
    return quat;
}

Rainbow::Quaternionf GameCamera::getRotation(float pitch, float yaw, float roll)
{
	return AngleEulerToQuaternionf(Vector3f(pitch, yaw, roll));
}

WCoord GameCamera::getEyePos()
{
    if(m_pCamera) return WCoord(m_pCamera->GetPosition().x, m_pCamera->GetPosition().y, m_pCamera->GetPosition().z);
    return WCoord(0, 0, 0);
}

WorldPos GameCamera::getPosition()
{
    if(m_pCamera) return Rainbow::WorldPos::fromVector3(m_pCamera->GetPosition());
    return Rainbow::WorldPos(0, 0, 0);
}

void GameCamera::trigerFPSHandAnimation()
{
    m_IsNeedHideHand = true;
    
    m_LerpStartMark = GetTimeSinceStartup();
}

Rainbow::WorldPos GameCamera::CalCollidedEyePos(World *pworld, const Rainbow::WorldPos &pos, const Rainbow::Vector3f &dir, float dist)
{
	CollideAABB box;
	box.dim = WCoord(20, 20, 20);
	box.pos = WCoord(pos) - box.dim / 2;

	WCoord mvec = dir * dist;
	Rainbow::Vector3f colnormal;
	float mdist = pworld->moveBox(box, mvec, colnormal);

	return pos + dir * (mdist * dist);
}

bool GameCamera::isShelteredByBlock(World *pworld, const Rainbow::AABB& box)
{
    //盒子的八个点都被挡住才算挡住
    Rainbow::Vector3f cameraPos = getPosition().toVector3();
    Rainbow::Vector3f boxPos = box.GetCenter();
    Rainbow::Vector3f boxExtent = box.GetExtent();
    Rainbow::Vector3f dir;
    Rainbow::Vector3f colnormal;
    Vector3f collisionPos(cameraPos);
    WorldRay wRay;
    WCoord src_pos = cameraPos;
    wRay.m_Origin = src_pos.toWorldPos();
    ActorExcludes excludes;
    IntersectResult presult;
    Vector3f normal(0.0, 0.0, 0.0);
    float dotResult = 0;
    int visiblePoint = 0;
    for (int i = -1; i < 2; i += 2)
    {
        for (int j = -1; j < 2; j += 2)
        {
            for (int k = -1; k < 2; k += 2)
            {
                Vector3f startPos;
                startPos.x = boxPos.x + boxExtent.x * i;
                startPos.y = boxPos.y + boxExtent.y * j;
                startPos.z = boxPos.z + boxExtent.z * k;
                dir = startPos - cameraPos;
                wRay.m_Dir = dir;
                wRay.m_Range = dir.Length();
                wRay.m_Dir = wRay.m_Dir / wRay.m_Range;
                presult.intersect_block = pworld->pickGround(wRay, &presult, PICK_METHOD_SOLID);
                if (presult.intersect_block)
                {
                    Vector3f blockPos = ((presult.bound_maxpos + presult.bound_minpos) / 2).toVector3();
                    switch (presult.face)
                    {
                    case DIR_NOT_INIT:
                        break;
                    case DIR_NEG_X:
                        normal.x = -1;
                        break;
                    case DIR_POS_X:
                        normal.x = 1;
                        break;
                    case DIR_NEG_Y:
                        normal.y = -1;
                        break;
                    case DIR_POS_Y:
                        normal.y = 1;
                        break;
                    case DIR_NEG_Z:
                        normal.z = -1;
                        break;
                    case DIR_POS_Z:
                        normal.z = 1;
                        break;
                    }
                    //求解现在要用哪个法向平面的中点
                    Vector3f blockFacePos = blockPos + BLOCK_SIZE * normal * 0.5;
                    Vector3f tempV1 = startPos - blockFacePos;
                    //过视点和平面垂直的向量和该平面法向一致。大于0代表在同一侧，小于零代表在不同侧
                    dotResult = DotProduct(tempV1, normal);
                    if (dotResult > 0)
                    {
                        //代表在同一侧 可见
                        return false;
                    }
                    else {
                        ++visiblePoint;
                    }
                }
            }
        }
    }
    if (visiblePoint == 8)
    {
        //八个点都被挡住才算挡住
        return true;
    }
    else
    {
        return false;
    }
}

void GameCamera::applyToEngine(World* pworld)
{
    bool newGun = m_Player && m_Player->IsGunHoldState(GunHoldState::CUSTOMGUN); //m_GunHoleState == GunHoldState::CUSTOMGUN;
    applyToEngineWithLerpOrNot(pworld, newGun ? false : true);
}

void GameCamera::applyToEngineWithLerpOrNot(World *pworld, bool bLerp)
{
	auto playercontrol = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
    //如果走新节点系统摄像机位置属性由新节点系统接管
    if (playercontrol->IsRunSandboxPlayer()) return;

    if (m_Player == NULL) return;
    if (m_pCamera.Get() == nullptr)
    {
        InitEngineCamera();
    }
    if (m_pCamera.Get() == nullptr) return;

    float dtime = GetFrameTimeManager().GetDeltaTime();

    //ar模式渲染
    if (GetIPlayerControl() && !GetIPlayerControl()->getIWorld()->getRenderTrunk())
    {	
        Matrix4x4f cameraView;
        Matrix4x4f cameraProjection;
        Matrix4x4f mModelMatrixs;
        Rainbow::Vector3f cameraViewPos = cameraView.GetPosition();
        Rainbow::Vector3f mModelMatrixsPos = mModelMatrixs.GetPosition();
        //暂定15倍
        Rainbow::Vector3f dir = cameraViewPos - mModelMatrixsPos;
        dir *= 15.0f;
        cameraViewPos += dir;
        Quaternionf cameraViewRotate;
        MatrixToQuaternionf(cameraView, cameraViewRotate);

        // old code cameraView.makeViewMatrix(cameraViewPos, cameraViewRotate);
        QuaternionfToMatrix(cameraViewRotate, cameraView);
        cameraView.SetTranslate(-cameraViewPos);
        // or cameraView.SetTranslate(cameraViewPos); cameraView.Invert_Full/*Invert_General3D*/();

        //m_pCamera->setPosition(cameraViewPos);
        //m_pCamera->setRotation(cameraViewRotate);
        // todo temp comment 
        //m_pCamera->setCustomizeCamera(true);
        //m_pCamera->setViewTransform(cameraView);
        //m_pCamera->setProjectionTransform(cameraProjection);
        return;
        //float[] cameraView, float[] cameraProjection ��mModelMatrixs ���Ի�Ϊ�����ԭʼ����
    }

    if (GetIPlayerControl() && GetIPlayerControl()->GetPlayerControlSneaking())
    {
        m_SneakOffset = 20 * Rainbow::Vector3f::neg_yAxis;
    }
    else
    {
        m_SneakOffset = Rainbow::Vector3f::zero;
    }

    Quaternionf quat = getRotation(m_RotatePitch, m_RotateYaw, 0);

    m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));

    if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false) && GAMERECORD_INTERFACE_EXEC(isPause(), false))
        return;

    Quaternionf riderot;
    bool has_riderot = false;
    auto riddencomponent = dynamic_cast<IClientActor*>(m_Player)->getActorComponent(ComponentType::COMPONENT_RIDDEN);
    if (riddencomponent)
    {
        bool result = riddencomponent->Event2().Emit<bool&, Rainbow::Quaternionf, IPlayerControl*>("Ridden_GetBindRot",
            has_riderot, riderot, m_Player);
        Assert(result);
    }

    if (m_Mode == CAMERA_FPS)
    {
        WorldPos position = m_Position + m_SneakOffset;
        // ���������Ԫ���˷���Ҫ����˳��
        if (has_riderot) quat = riderot * quat;

        if (m_EnableCameraBob && !has_riderot)
        {
            float oscillationAx = 5;
            float oscillationAy = 5;
            float rotaionPower = 0.15f;

            if (GetIPlayerControl()->GetPlayerControlSneaking())
            {
                oscillationAx = 5 * 0.3f;
                oscillationAy = 5 * 0.3f;
                rotaionPower = 0.15f * 0.3f;
            }
            if (m_CameraModel == NULL) return;
            float currentTime = m_CameraModel->getCurrentShakeTime();
            m_ShakeVector.x = oscillationAx * Rainbow::Sin(0.5f * ONE_PI + 2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle());
            m_ShakeVector.y = oscillationAy * Abs(Rainbow::Sin(2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle()));
            m_ShakeVector.z = 0;
            m_ShakeRotZ = -rotaionPower * Rainbow::Sin(2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle());

            const Quaternionf tmpRot = AxisAngleToQuaternionf(Vector3f(0, 0, 1), Deg2Rad(m_ShakeRotZ));

            const Rainbow::Vector3f tmp = RotateVectorByQuat(quat, m_ShakeVector);
            position += tmp;

            // ���������Ԫ���˷���Ҫ����˳��
            quat = quat * tmpRot;
        }
        if (bLerp)
            quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        else
            m_qInterpRotate = quat;

        m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        m_pCamera->SetWorldPosition(position.toVector3());
        m_pCamera->SetWorldRotation(quat);
    }
    else if (m_Mode == CAMERA_TPS_BACK || m_Mode == CAMERA_TPS_BACK_2)
    {
        Rainbow::Vector3f offset(0, 60.0f, 0);
        float dist = (getRotatePitch() / -80.0f);
        if (dist < 0) dist = 0;
        dist = (500.0f - dist * 450.0f) * m_DistMultiply;

        if (bLerp)
            quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        else
            m_qInterpRotate = quat;

        m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -m_LookDir, dist);
        //pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);

        if (has_riderot)
        {
            riderot = riderot.Inverse();
            riderot = riderot * quat;
            m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
        }
        m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
        m_pCamera->SetWorldRotation(quat);
    }
    else if (m_Mode == CAMERA_TPS_BACK_SHOULDER)
    {
        Rainbow::Vector3f offset(0, 60.0f, 0);
        float dist = (getRotatePitch() / -80.0f);
        if (dist < 0) dist = 0;
        dist = (500.0f - dist * 450.0f) * m_DistMultiply;

        if (bLerp)
            quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        else
            m_qInterpRotate = quat;

        m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -m_LookDir, dist);
        //pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);

        if (has_riderot)
        {
            riderot = riderot.Inverse();
            riderot = riderot * quat;
            m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
        }
        m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
        m_pCamera->SetWorldRotation(quat);
    }
    else if (m_Mode == CAMERA_TPS_FRONT)
    {
        Rainbow::Vector3f offset(0, 60.0f, 0);
        float dist = (-getRotatePitch() / -80.0f);
        if (dist < 0) dist = 0;
        dist = (500.0f - dist * 450.0f) * m_DistMultiply;

        if (bLerp)
            quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        else
            m_qInterpRotate = quat;

        m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
        Rainbow::WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, m_LookDir, dist);
		//pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);
        m_pCamera->LookAt(pos.toVector3() + m_SneakOffset, pos.toVector3() + m_SneakOffset - m_LookDir * 100.0f, Rainbow::Vector3f::yAxis);
        //auto camera_pos = m_pCamera->GetWorldPosition();
        //auto camera_rotation = m_pCamera->GetWorldRotation();

        if (has_riderot)
        {
            riderot = riderot.Inverse();
            // ���������Ԫ���˷���Ҫ����˳��
            riderot = riderot * quat;
            m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
        }
    }
    else if (m_Mode == CAMERA_TPS_OVERLOOK)
    {
        Rainbow::Vector3f offset(0, 60.0f, 0);
        float dist = 500.0f;
        //m_RotationStabilizer.AddValue(Vector3f(70.0f, 0.0f, 0.0f));
        quat = getRotation(70.0f, 0.0f, 0.0f);
        Rainbow::Vector3f dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
        WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
        //pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);
        m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
        m_pCamera->SetWorldRotation(quat);
    }
    else if (m_Mode == CAMERA_THIRD_SHOULDER)
    {
        float yaw, pitch;
        Rainbow::Vector3f offset(0.0f, -80.0f, -80.0f);
        yaw = m_Player->getPlayerControlLocoMotion()->GetRotateYaw() + 135.0f;
        pitch = 5.0f;
        this->setFov(45);
        Quaternionf quat;
        quat = getRotation(pitch, yaw, 0.0f);
        float dist = (m_RotatePitch / -80.0f);
        if (dist < 0) dist = 0;
        dist = (500.0f - dist * 450.0f) * m_DistMultiply;
        float RotateYaw = round(m_Player->getPlayerControlLocoMotion()->GetRotateYaw() * 10) / 10.0;
        if (RotateYaw == 90.0)
        {
            offset.y = -80.0f;
            offset.z = 50.0f;
        }
        else if (RotateYaw == -90.0)
        {
            offset.y = -80.0f;
            offset.z = -20.0f;
        }
        else if (RotateYaw == -180.0)
        {
            offset.z = 80.0f;
        }
        //quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        //m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        Rainbow::Vector3f dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
        WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
        //pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);
        m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
        m_pCamera->SetWorldRotation(quat);
    }
    else if (m_Mode == CAMERA_TPS_SIDE)
    {
        float yaw, pitch;
        Rainbow::Vector3f offset(0.0f, -80.0f, -80.0f);
        yaw = m_Player->getPlayerControlLocoMotion()->GetRotateYaw() +  80.0f;
        pitch = 5.0f;
        this->setFov(45);
        Quaternionf quat;
        quat = getRotation(pitch, yaw, 0.0f);
        float dist = (getRotatePitch() / -80.0f);
        if (dist < 0) dist = 0;
        dist = (500.0f - dist * 450.0f) * m_DistMultiply;
        float RotateYaw = round(m_Player->getPlayerControlLocoMotion()->GetRotateYaw() * 10) / 10.0;
        if (RotateYaw == 90.0)
        {
            offset.x = -80.0f;
            offset.y = -80.0f;
            offset.z = 50.0f;
        }
        else if (RotateYaw == -90.0)
        {
            offset.x = 70.0f;
            offset.y = -80.0f;
            offset.y =  -80.0f;
        }
        else if (RotateYaw == -180.0)
        {
            offset.z = 80.0f;
        }
		//quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
		//m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        Rainbow::Vector3f dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
        WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
        //pos = InternalInterp(pos, m_vInterpCamPos, dtime, m_CameraLerpSpeed);
        m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
        m_pCamera->SetWorldRotation(quat);

    }
    else if (m_Mode == CAMERA_CUSTOM_VIEW)
    {
        if (bLerp)
            quat = InternalInterp(quat, m_qInterpRotate, dtime, m_CameraLerpSpeed);
        else
            m_qInterpRotate = quat;

        m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
        if (CameraManager::GetInstance().getCameraEditState() == CAMERA_EDIT_STATE_EDIT)
        {
            m_pCamera->SetWorldRotation(quat);
        }
        else
        {
            // �������Ҫת��
            if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA
                && (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_CAMERA_AND_BODY
                    || m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_ONLY_CAMERA))
            {
                // �������Ҫ��������
                if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
                {
                    m_pCamera->SetWorldPosition(m_Player->getCameraConfigPosition(m_RotateYaw, m_RotatePitch).toVector3());
                }
                m_pCamera->SetWorldRotation(quat);
            }
            else
            {
                // �������Ҫ��������
                if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
                {
                    if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_BACKVIEW)// && m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_CAMERA_AND_BODY)
                    {
                        m_pCamera->SetWorldPosition(m_Player->getCameraConfigPosition(m_RotateYaw, m_RotatePitch).toVector3());
                    }
                    else if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW && m_Player->HasReversed())
                    {
                        m_pCamera->SetWorldPosition(m_Player->getCameraConfigPosition(m_Player->getPlayerControlLocoMotion()->GetRotateYaw(), m_Player->getPlayerControlLocoMotion()->GetRotationPitch()).toVector3());
                    }
                    else
                    {
                        m_pCamera->SetWorldPosition(m_Player->getCameraConfigPosition(m_Player->getPlayerControlLocoMotion()->GetRotateYaw() + 180.0f, m_Player->getPlayerControlLocoMotion()->GetRotationPitch()).toVector3());
                    }
                }
            }
        }
    }

    if (GetIPlayerControl() && m_CameraModel && GetIPlayerControl()->getBobbingByRocket())
    {
        WorldPos position1 = WorldPos::fromVector3(m_pCamera->GetWorldPosition());

        float oscillationAx = 2;

        m_ShakeVector.x = oscillationAx * Rainbow::Sin(0.5f * ONE_PI + 2 * m_CurrentShakeTime * ONE_PI / (0.2f * m_CameraModel->getCurrentShakeCycle()));
        m_ShakeVector.y = 0;
        m_ShakeVector.z = 0;

        Quaternionf quat1 = m_pCamera->GetWorldRotation();
        Rainbow::Vector3f tmp = RotateVectorByQuat(quat1, m_ShakeVector);
        m_pCamera->SetWorldPosition(m_pCamera->GetPosition() + tmp);
    }

    if (GetIPlayerControl() && GetIPlayerControl()->isShakingCamera() && m_ShakingDuration > 0)
    {
        // updateShaking();
    }


    for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
    {
        iter->second->applyToEngine(pworld);
    }
}

void GameCamera::setPosition(const Rainbow::WorldPos &pos)
{
    m_Position = pos;
}

void GameCamera::SetEngineCameraPos(const Rainbow::WorldPos &pos)
{
	if (m_pCamera)
	{
		m_pCamera->SetWorldPosition(pos.toVector3());
		//this->m_FrontCamera->SetWorldPosition(this->m_pCamera->GetWorldPosition());
	}
}

void GameCamera::SetEngineCameraRot(const Rainbow::Quaternionf& rot)
{
	//setRotate(rot.EulerAngle().x, rot.EulerAngle().y);
	if (m_pCamera)
	{
		m_pCamera->SetWorldRotation(rot);
		//this->m_FrontCamera->SetWorldRotation(this->m_pCamera->GetWorldRotation());
	}
}

Rainbow::Quaternionf GameCamera::GetEngineCameraRot()
{
	if (m_pCamera)
	{
        return m_pCamera->GetWorldRotation();
	}
    else
    {
        return Quaternionf();
    }
}

void GameCamera::UpdateEngineCamera(float dtime)
{  
	if(m_pCamera){
		Quaternionf quat = AngleEulerToQuaternionf( Vector3f( m_RotatePitch, m_RotateYaw, 0 ));
		m_pCamera->SetWorldRotation(quat);
		//this->m_FrontCamera->SetWorldRotation(this->m_pCamera->GetWorldRotation());
		// int dtick = TimeToTick(dtime);
		// no use m_pCamera->update(dtick);
	}
}


void GameCamera::moveForward(float dist)
{
    Rainbow::Vector3f move = m_LookDir;
    move.y = 0;
    move  = MINIW::Normalize(move);

	if (GetClientInfoProxy()->isMobile()) dist *= 10;
	move *= dist;
	//m_Position += move;
	if (m_pCamera)
	{
		m_pCamera->SetWorldPosition(m_pCamera->GetWorldPosition() + move);
		//this->m_FrontCamera->SetWorldPosition(m_pCamera->GetWorldPosition());
	}
}

void GameCamera::moveSide(float dist)
{
    Rainbow::Vector3f move = m_LookDir;
    move.y = 0;
    move  = MINIW::Normalize(move);

	Rainbow::Vector3f side = CrossProduct(Rainbow::Vector3f(0,1.0f,0), move);
	if (GetClientInfoProxy()->isMobile()) dist *= 10;
	side *= dist;
	//m_Position += side;
	if (m_pCamera)
	{
		m_pCamera->SetWorldPosition(m_pCamera->GetWorldPosition() + side);
		//this->m_FrontCamera->SetWorldPosition(m_pCamera->GetWorldPosition());
	}
}

void GameCamera::moveUp(float dist)
{
	//m_Position += Rainbow::Vector3f(0, dist, 0);
	if (GetClientInfoProxy()->isMobile()) dist *= 10;
	if (m_pCamera)
	{
		m_pCamera->SetWorldPosition(m_pCamera->GetWorldPosition() + Rainbow::Vector3f(0, dist, 0));
		//this->m_FrontCamera->SetWorldPosition(m_pCamera->GetWorldPosition());
	}
}

void GameCamera::rotateOnScreen(int xpixels, int ypixels)
{
    float xdist = float(xpixels)/m_WinWidth;
    float ydist = float(ypixels)/m_WinHeight;

    rotate(xdist, ydist);
}

void GameCamera::rotate(float yaw, float pitch)
{
    //WarningStringMsg("yaw:%f,pitch:%f", yaw, pitch);
    auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
    if ((GetIPlayerControl() && player && player->isInSpectatorMode() && player->getSpectatorType() == SPECTATOR_TYPE_FOLLW) ||
        (GetIPlayerControl() && player->getSpectatorType() == SPECTATOR_TYPE_OUTCONTROL))
        return;

    if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->GetTriggerOperateAttr(player->getUin(), 2)) //是否禁止旋转摄像头 
    {
        return;
    }
    if (m_Mode == CAMERA_TPS_OVERLOOK)
    {
        Rainbow::Vector3f dir(-yaw, 0, pitch);
        dir  = MINIW::Normalize(dir);

        Direction2PitchYaw(&m_RotateYaw, NULL, dir);
    }
    else
    {
        if (m_Mode == CAMERA_CUSTOM_VIEW
            && CameraManager::GetInstance().getCameraEditState() != CAMERA_EDIT_STATE_EDIT)
        {
            switch (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING_LIMIT))
            {
            case CRL_ONLY_X:
                pitch = 0.0f;
                break;
            case CRL_ONLY_Y:
                yaw = 0.0f;
                break;
            }
        }
        m_RotateYaw += yaw * 180.0f;
    }

    bool isFishingOpAndFPSMode = m_Mode == CameraControlMode::CAMERA_FPS && player->getOPWay() == PLAYEROP_WAY_FISHING;

    if (m_RotateYaw > 360.0f)
    {
        m_RotateYaw -= 360.0f;
        if (m_CameraModel != NULL && !isFishingOpAndFPSMode) {
            m_CameraModel->m_ArmYaw -= 360.0f;
            m_CameraModel->m_RenderYaw -= 360.0f;
            m_CameraModel->m_PreYaw -= 360.0f;
        }
    }
    if (m_RotateYaw < 0)
    {
        m_RotateYaw += 360.0f;
        if (m_CameraModel != NULL && !isFishingOpAndFPSMode) {
            m_CameraModel->m_ArmYaw += 360.0f;
            m_CameraModel->m_RenderYaw += 360.0f;
            m_CameraModel->m_PreYaw += 360.0f;
        }
    }

    float anglex = 88.0f;
    m_RotatePitch += pitch * 90.0f;
    if (GetIPlayerControl() && dynamic_cast<IClientActor*>(GetIPlayerControl())->getUsingEmitter())		// 手动发射器上控制上下摆动幅度
    {
        anglex = 15.0f;
    }
    if (m_RotatePitch < -anglex) m_RotatePitch = -anglex;
    if (m_RotatePitch > anglex) m_RotatePitch = anglex;
}

void GameCamera::setRotate(float yaw, float pitch)
{
    m_RotateYaw = yaw;
    if(m_RotateYaw > 360.0f) m_RotateYaw -= 360.0f;
    if(m_RotateYaw < 0) m_RotateYaw += 360.0f;

    m_RotatePitch = pitch;
    float anglex = 88.0f;
    if(m_RotatePitch < -anglex) m_RotatePitch = -anglex;
    if(m_RotatePitch > anglex) m_RotatePitch = anglex;
}

void GameCamera::beginCaptureAnim(int seq)
{
    m_CamAnim->beginCapture(seq, m_pCamera);
}

void GameCamera::endCaptureAnim()
{
    m_CamAnim->endCapture();
}

void GameCamera::saveCaptureAnim()
{

}

void GameCamera::loadCaptureAnim()
{

}

void GameCamera::playAnim(int seq)
{
    m_CamAnim->beginPlay(seq, m_pCamera);
}

void GameCamera::stopAnim()
{
    m_CamAnim->endPlay();
}

//#ifdef TestScriptCamera
void GameCamera::addScriptCamera(char* name, const char* scriptName, float fov, float fnear, float ffar)
{
    auto iter = m_mScriptCameras.find(name);
    if (iter == m_mScriptCameras.end())
    {
        scriptCamera *data = ENG_NEW(scriptCamera)();
        data->initScriptCamera(scriptName, fov, m_WinWidth, m_WinHeight, fnear, ffar, m_Position);
        m_mScriptCameras[name] = data;
    }
}

void GameCamera::removeScriptCamera(char* name)
{
    auto iter = m_mScriptCameras.find(name);
    if (iter != m_mScriptCameras.end())
    {
        OGRE_DELETE(iter->second);
        m_mScriptCameras.erase(iter);
    }
}

scriptCamera* GameCamera::getScriptCameraWithName(char* name)
{
    auto iter = m_mScriptCameras.find(name);
    if (iter != m_mScriptCameras.end())
    {
        return iter->second;
    }
    return nullptr;
}

int GameCamera::getScriptCameraCount()
{
    return m_mScriptCameras.size();
}

bool GameCamera::setSpCameraPosRot(char* name, Rainbow::WorldPos pos, Rainbow::Quaternionf quat/*float yaw, float pitch*/)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pData->setPosition(pos);
        pData->setRotation(quat);
        return true;
    }
    return false;
}
bool GameCamera::setSpCameraPos(char* name, Rainbow::WorldPos pos)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pData->setPosition(pos);
        return true;
    }
    return false;
}
bool GameCamera::setSpCameraRot(char* name, Rainbow::Quaternionf qut)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pData->setRotation(qut);
        return true;
    }
    return false;
}
bool GameCamera::setSpCameraLookAt(char* name, Rainbow::WorldPos eye, Rainbow::Vector3f dir, Rainbow::Vector3f up)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pData->setLookAt(eye, dir, up);
        return true;
    }
    return false;
}
bool GameCamera::setSpCameraFov(char* name, float fov)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pData->setFov(fov);
        return true;
    }
    return false;
}
bool GameCamera::getSpCameraPos(char* name, Rainbow::WorldPos& pos)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        pos = pData->getPosition();
        return true;
    }
    return false;
}
bool GameCamera::getSpCameraRot(char* name, Rainbow::Quaternionf& rot)
{
    scriptCamera* pData = getScriptCameraWithName(name);
    if (pData)
    {
        rot = pData->getRotation();
        return true;
    }
    return false;
}

void GameCamera::getViewRayByScreenPt(MINIW::WorldRay* pRay, float x, float y)
{
    Camera* camear = m_pCamera.Get();
    if (camear != nullptr && camear->GetGameObjectSafe() != nullptr)
    {
        const Rainbow::Ray rainbowRay = camear->ViewportPointToRay(Vector2f(x, 1.0 - y));
        pRay->m_Origin = Rainbow::WorldPos::fromVector3(rainbowRay.GetOrigin());
        pRay->m_Dir = rainbowRay.GetDirection();
    }
    else {
        assert(false); //pRay 值不是预期的，会影响后续计算
    }
}


CameraModel* GameCamera::getCameraModel()
{
    return m_CameraModel;
}

void GameCamera::onChangeViewMode()
{
    InitEngineCamera();
    //if (m_Mode == CAMERA_FPS)
    //{
    //    if (m_FrontCamera.Get() != nullptr && m_FrontCamera.IsValid())
    //    {
    //        m_FrontCamera->SetEnable(true);
    //    }
    //}
    //else
    //{
    //    if (m_FrontCamera.Get() != nullptr && m_FrontCamera.IsValid())
    //    {
    //        m_FrontCamera->SetEnable(false);
    //    }
    //}

}

bool GameCamera::GetScreenPointFromeWeapon(int anchorId, Rainbow::Vector2f& screenPos)
{
    return GetPointFromeWeapon(anchorId, screenPos, 0);
}

bool GameCamera::GetViewportPointFromeWeapon(int anchorId, Rainbow::Vector2f& viewportPos)
{
    return GetPointFromeWeapon(anchorId, viewportPos, 1);
}

bool GameCamera::GetPointFromeWeapon(int anchorId, Rainbow::Vector2f& pos, int pointType)
{
    if (m_pCamera && m_CameraModel && m_CameraModel->m_ToolModel)
    {
        ModelItemMesh* modelMesh = dynamic_cast<ModelItemMesh*>(m_CameraModel->m_ToolModel);
        if (modelMesh && modelMesh->GetModel())
        {
            Rainbow::BoneNode* boneNode = modelMesh->GetModel()->GetBoneNode(anchorId);
            if (boneNode)
            {
                Rainbow::Vector3f boneWorldPos = boneNode->GetWorldPosition();

                Rainbow::Vector3f bonePointPos;
                if (pointType == 0)
                    bonePointPos = m_pCamera->WorldToScreenPoint(boneWorldPos);
                else
                    bonePointPos = m_pCamera->WorldToViewportPoint(boneWorldPos);

                pos = { bonePointPos.x, bonePointPos.y };
                return true;
            }
        }
    }
    return false;
}

bool GameCamera::GetScreenPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& screenPos)
{
    return GetPointFromeWeaponPart(weaponAnchorId, partAnchorId, screenPos, 0);
}

bool GameCamera::GetViewportPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& viewportPos)
{
    return GetPointFromeWeaponPart(weaponAnchorId, partAnchorId, viewportPos, 1);
}

bool GameCamera::GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos)
{
    if (m_CameraModel && m_CameraModel->m_ToolModel)
    {
        ModelItemMesh* modelMesh = dynamic_cast<ModelItemMesh*>(m_CameraModel->m_ToolModel);
        if (modelMesh && modelMesh->GetModel())
        {
            Rainbow::BoneNode* boneNode = modelMesh->GetModel()->GetBoneNode(anchorId);
            if (boneNode)
            {
                boneWorldPos = boneNode->GetWorldPosition();
                return true;
            }
        }
    }
    return false;
}

void GameCamera::updateShaking()
{
    if (m_ShakingDuration <= 0 || m_ShakingOriginDuration <= 0)
        return;

    auto EaseOutQuad = [](float t) { return t * t; };

    std::random_device rd;
    std::mt19937 gen(rd());
    auto rand_float = [&gen](float min, float max) -> float {
        std::uniform_real_distribution<float> dis(min, max);
        return dis(gen);
    };

    Vector3f attackDir = GetIPlayerControl()->getLookDir();
    float attackStrength = 1.0f;

    float t = m_ShakingDuration / m_ShakingOriginDuration;
    float k = EaseOutQuad(t);

    float rx = rand_float(-0.7f, 0.7f) * m_ShakingPower.x * k * attackStrength;
    float ry = rand_float(-0.7f, 0.7f) * m_ShakingPower.y * k * attackStrength;
    float rz = rand_float(-0.25f, 0.25f) * m_ShakingPower.z * k * attackStrength;

    m_ShakeVector = Vector3f(rx, ry, -rz);

    float rotStrength = 0.1f * k * attackStrength;
    Quaternionf rotShake = Rainbow::EulerToQuaternionf(
        rand_float(-rotStrength, rotStrength),
        rand_float(-rotStrength, rotStrength),
        rand_float(-rotStrength, rotStrength)
    );

    Quaternionf quat1 = m_pCamera->GetWorldRotation();
    Rainbow::Vector3f tmp = RotateVectorByQuat(quat1, m_ShakeVector);

    quat1 = quat1 * rotShake;
    m_pCamera->SetWorldPosition(m_pCamera->GetWorldPosition() + tmp);
    m_pCamera->SetWorldRotation(quat1);
}

bool GameCamera::GetPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& pos, int pointType)
{
    if (m_pCamera && m_CameraModel && m_CameraModel->m_ToolModel)
    {
        ModelItemMesh* modelMesh = dynamic_cast<ModelItemMesh*>(m_CameraModel->m_ToolModel);
        if (modelMesh && modelMesh->getEntity())
        {
            MovableObject* part = modelMesh->getEntity()->GetBindObjectByAid(weaponAnchorId);
            if (part)
            {
                Rainbow::Model* partModel{ nullptr };
                ModelItemMesh* partModelMesh = dynamic_cast<ModelItemMesh*>(part);
                if (partModelMesh && partModelMesh->GetModel())
                {
                    partModel = partModelMesh->GetModel();
                }
                else
                {
                    partModel = dynamic_cast<Rainbow::Model*>(part);
                }
                if (partModel)
                {
                    Rainbow::BoneNode* boneNode = partModel->GetBoneNode(partAnchorId);
                    if (boneNode)
                    {
                        Rainbow::Vector3f boneWorldPos = boneNode->GetWorldPosition();
                        Rainbow::Vector3f bonePointPos;
                        m_pCamera->SetVerticalFieldOfView(57.f);
                        if (pointType == 0)
                        {
                            bonePointPos = m_pCamera->WorldToScreenPoint(boneWorldPos);
                        }
                        else
                        {
                            bonePointPos = m_pCamera->WorldToViewportPoint(boneWorldPos);
                        }
                        m_pCamera->SetVerticalFieldOfView(m_CurrentFov);
                        pos = { bonePointPos.x, bonePointPos.y };
                        return true;
                    }
                }
            }
        }
    }
    return false;
}