/*
*	File:	ObserverEventManager
*	Func:	观察者事件管理器
*	Desc:	利用观察者模式，使功能模块和监听模块去耦合。
*			同时因为功能模块逻辑已经很成熟了，所以这里使用单独的观察者类来管理。
*/

#include <map>
#include "ObserverEventManager.h"
#include "WorldManager.h"
#include "IClientGameManagerInterface.h"
#include "OgreScriptLuaVM.h"
#include "GameStatic.h"
#include "SandboxCoreDriver.h"
#include "ClientInfoProxy.h"
#include "ICloudProxy.h"
#include "Optick/optick.h"
//#include "SandboxGameDef.h"
#include "blocks/BlockMaterialMgr.h"
using namespace MINIW;
using namespace Rainbow;
IMPLEMENT_GETMETHOD_MANUAL_INIT(ObserverEventManager)

extern "C"
{
	#include "lua.h"
}
using namespace MNSandbox;


ObserverEventManager& ObserverEventManager::getSingleton()
{
	return GetObserverEventManager();
}

ObserverEventManager::ObserverEventManager()
{
	lua_State* L = MINIW::ScriptVM::game()->getLuaState();
	if (L)
		ObserverEvent::RegObserverEventParse(L);
	m_staticsEventCount = 0;
}

ObserverEventManager::~ObserverEventManager()
{
	m_triggerEventList.clear();
	m_triggerEventListBuffer.clear();
}

void ObserverEventManager::tick()
{
	OPTICK_EVENT();
	int triggered = 0;
	// 缓存队列
	
	int num = 100;		// 每帧处理num个
#ifdef IWORLD_SERVER_BUILD
	num = 1000;
#endif	
	auto iter = m_triggerEventList.begin();
	bool begin = false;

	std::map<std::string, int> eventCount;
	int listSize = m_triggerEventList.size();
	while (iter != m_triggerEventList.end() && num-- > 0)
	{
#ifdef IWORLD_SERVER_BUILD		
		if (listSize > 200)
		{
			eventCount[iter->first] += 1;
		}
#endif		
		auto iterListen = m_mapListens.find(iter->first);
		if (iterListen != m_mapListens.end())
		{
#ifdef ENABLE_SS_TICKCALLONCE
			if (!begin)
			{
				begin = true;
				EVENT_TRIGGER_BEGIN();
			}
#endif
			//SERVER_PERF_NAME(iter->first);
			iterListen->second.OnTrigger(iter->first.c_str(), &iter->second);
			triggered++;
		}
		iter++;
	}
#ifdef ENABLE_SS_TICKCALLONCE
	if (begin)
	{
		EVENT_TRIGGER_END();
	}
#endif
#ifdef IWORLD_SERVER_BUILD
	if (listSize > 200)
	{
		auto count_itr = eventCount.begin();
		int max1 = 0;
		std::string eventname;
		while (count_itr != eventCount.end())
		{
			if (count_itr->second > max1)
			{
				max1 = count_itr->second;
				eventname = count_itr->first;
			}
			++count_itr;
		}
		GetICloudProxyPtr()->SimpleSLOG("ObserverEventManager::tick too much event [%d] %s[%d]", listSize, eventname.c_str(), max1);
	}
	m_staticsEventCount += triggered;
#endif
	m_triggerEventList.erase(m_triggerEventList.begin(), iter);

	// 插入
	m_triggerEventList.insert(m_triggerEventList.end(), m_triggerEventListBuffer.begin(), m_triggerEventListBuffer.end());
	m_triggerEventListBuffer.clear();
	OPTICK_TAG("triggered", triggered);
}
#ifdef IWORLD_SERVER_BUILD
int ObserverEventManager::GetStatics(int &eventCount, int &curSize)
{
	curSize = m_triggerEventList.size();
	eventCount = m_staticsEventCount;
	return 0;
}
int ObserverEventManager::ResetStatics()
{
	m_staticsEventCount = 0;
	return 0;	
}
#endif

bool ObserverEventManager::IsEnable()
{
	WorldManager* pWorldMgr = GetWorldManagerPtr();
	if (pWorldMgr == NULL) return false;
	
	if (pWorldMgr->isRemote()) // 客机不需要触发
		return false;

	if (!pWorldMgr->isGameMakerRunMode() && !pWorldMgr->isOriginAdventureMode())
		return false;

	if (m_triggerEventList.size() + m_triggerEventListBuffer.size() > 100000) // 保护
		return false;

	return true;
}

bool ObserverEventManager::CheckEventRegistered(const char* name)
{
	auto iter = m_mapListens.find(std::string(name));
	if (iter != m_mapListens.end() && !iter->second.IsEmpty())
	{
		return true;
	}
	return false;
}

void ObserverEventManager::RegisterEvent(const char* name, ObserverEventListen* pListen, const FuncTrigger& func)
{
	m_mapListens[std::string(name)] += ObserverEventListenData(pListen, func);
}

void ObserverEventManager::UnregisterEvent(const char* name, ObserverEventListen* pListen)
{
	auto iter = m_mapListens.find(std::string(name));
	if (iter != m_mapListens.end())
	{
		iter->second -= pListen;//ObserverEventListenData(pListen, NULL);
		if (iter->second.IsEmpty())
		{
			iter = m_mapListens.erase(iter);
		}
	}
}

void ObserverEventManager::UnregisterEvent(const char* name)
{
	auto iter = m_mapListens.find(std::string(name));
	if (iter != m_mapListens.end())
		m_mapListens.erase(iter);
}

void ObserverEventManager::UnregisterEvent(ObserverEventListen* pListen)
{
	auto iter = m_mapListens.begin();
	while (iter != m_mapListens.end())
	{
		iter->second -= pListen;//ObserverEventListenData(pListen, NULL);
		if (iter->second.IsEmpty())
		{
			iter = m_mapListens.erase(iter);
		}
		else
		{
			iter++;
		}
	}
}

void ObserverEventManager::OnTriggerEvent(const char* name, const ObserverEvent* pobevent)
{
	if (!IsEnable())
		return;

	// 屏蔽裁判和旁观者
	if (pobevent)
	{
		int uin = pobevent->getUin();
		if (uin != 0 && GetIClientGameManagerInterface()->isJudgeOrSpectator(uin,0))
			return;
	}

	// 缓存,下一个update执行
	if (CheckEventRegistered(name))
	{
		m_triggerEventListBuffer.push_back(make_pair(name, pobevent ? *pobevent : ObserverEvent()));
	}

	//方块组件事件
	GetISandboxActorSubsystem()->OnBlockEvent(name, pobevent, 1);
	/*auto it = m_BlockEventMap.find(name);
	if (it != m_BlockEventMap.end())
	{
		this->OnBlockEvent(it->second, pobevent, 1);
	}*/
}

bool ObserverEventManager::OnBlockEvent(unsigned int eEventType, const ObserverEvent* pobevent, int nImmediate)
{
	//if (eEventType == Resgist_OnBlockClick)
	//{
	//	//const ObserverEvent_PlayerBlock* event = static_cast<const ObserverEvent_PlayerBlock*>(pobevent);
	//}
	if (!pobevent)
		return false;

	return TriggerBlockEvent(eEventType, pobevent, nImmediate);
}

bool ObserverEventManager::TriggerBlockEvent(unsigned int eEventType, const ObserverEvent* pobevent, int nImmediate)
{
	int blockid = 0;
	pobevent->GetData_Block(blockid);
	if (blockid == 0)
		return false;

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl)
		return false;


	return GetISandboxActorSubsystem()->BSComponentOnEventFromTrigger(pmtl, eEventType, pobevent, nImmediate);
}

//=======================================================================//
ObserverEventRegisterIns::ObserverEventRegisterIns(ObserverEventListen* pOEListen)
	: m_pInstance(pOEListen)
{
}

ObserverEventRegisterIns::~ObserverEventRegisterIns()
{
	UnregisterAllEvent();
}

bool ObserverEventRegisterIns::RegisterEvent(const std::string& eventname, const FuncTrigger& func)
{
	if (eventname.empty())
		return false;

	// 已经存在了，不能再次注册
	if (m_RegedEvents.find(eventname) != m_RegedEvents.end())
		return false;

	m_RegedEvents.insert(eventname);
	GetObserverEventManager().RegisterEvent(eventname.c_str(), m_pInstance, func);
	return true;
}

bool ObserverEventRegisterIns::UnregisterEvent(const std::string& eventname)
{
	if (eventname.empty())
		return false;

	// 不存在，不能注销
	if (m_RegedEvents.find(eventname) == m_RegedEvents.end())
		return false;

	m_RegedEvents.erase(eventname);
	GetObserverEventManager().UnregisterEvent(eventname.c_str(), m_pInstance);
	return true;
}

void ObserverEventRegisterIns::UnregisterAllEvent()
{
	if (m_RegedEvents.empty())
		return;

	m_RegedEvents.clear();
	GetObserverEventManager().UnregisterEvent(m_pInstance);
}


