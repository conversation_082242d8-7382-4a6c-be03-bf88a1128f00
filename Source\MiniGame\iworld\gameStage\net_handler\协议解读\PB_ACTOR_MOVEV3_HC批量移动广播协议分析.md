# PB_ACTOR_MOVEV3_HC 批量移动广播协议深度分析

## 概述

PB_ACTOR_MOVEV3_HC 是目前**唯一在运行**的 Actor 移动广播协议，用于批量向客户端同步其他玩家的移动信息。PB_ACTOR_MOVE_HC 和 PB_ACTOR_MOVEV2_HC 已被注释掉，不再使用。

**协议 ID**: 2018 (PB_ACTOR_MOVEV3_HC)

## 协议演进历史

### 协议版本对比

| 协议版本           | 状态            | 特点                          | 使用场景 |
| ------------------ | --------------- | ----------------------------- | -------- |
| PB_ACTOR_MOVE_HC   | ❌ **已废弃**   | 单个移动，包含完整 MoveMotion | 早期版本 |
| PB_ACTOR_MOVEV2_HC | ❌ **已废弃**   | 单个移动，压缩位置数据        | 优化版本 |
| PB_ACTOR_MOVEV3_HC | ✅ **当前使用** | 批量移动，性能最优            | 当前版本 |

### 废弃原因分析

**代码证据**: `Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp`

```cpp
// 第642行 - PB_ACTOR_MOVEV2_HC 被注释
//GameNetManager::getInstance()->sendToClient(uin, PB_ACTOR_MOVEV2_HC, actorMoveHC, 0, true, UNRELIABLE_SEQUENCED, HIGH_PRIORITY, 0, objid);

// 第665行 - PB_ACTOR_MOVE_HC 仍有少量使用但已被批量机制替代
GameNetManager::getInstance()->sendToClient(uin, PB_ACTOR_MOVE_HC, actorMoveHC, 0, true, UNRELIABLE_SEQUENCED);

// 第1322行和1331行 - 统一使用缓存机制
GetGameNetManagerPtr()->cacheActorMove(target->getUin(), objid, actorMoveHC);
```

**废弃原因**:

1. **网络性能**: 单个发送造成大量网络调用
2. **带宽浪费**: 每个协议都有独立的包头开销
3. **处理效率**: 客户端需要处理大量小包

## 协议定义

### PB_ActorMoveV3HC_Batch 结构

**文件位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:149-152`

```protobuf
message PB_ActorMoveV3HC_Batch
{
    repeated PB_ActorMoveV2HC MoveBatch = 1;
}
```

### PB_ActorMoveV2HC 子结构

**文件位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:141-147`

```protobuf
message PB_ActorMoveV2HC
{
    optional uint64 ObjID = 1;                    // Actor对象ID
    repeated sint32 Position = 2 [packed = true]; // 位置信息(x,y,z)
    optional uint32 Yaw_Pitch = 3;               // 朝向信息(压缩)
    optional int32 ChangeFlags = 4;              // 变化标志
}
```

## 服务器端发送机制

### 1. 缓存阶段 (cacheActorMove)

**代码位置**: `Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:1528-1567`

```cpp
void GameNetManager::cacheActorMove(int targetUin, int64_t objid, const PB_ActorMoveV2HC& am)
{
    // 移动同步包优化
    if (targetUin > 0 && objid >= 0)
    {
        // 更新obj的数据 - 覆盖式更新，保证最新状态
        auto it = m_ActorMoveCache.find(objid);
        if (it != m_ActorMoveCache.end())
        {
            auto& old = it->second;
            old = am;  // 直接覆盖，不是合并
        }
        else
        {
            m_ActorMoveCache.emplace(objid, am);
        }

        // 更新target - 记录哪些玩家需要接收此Actor的移动信息
        auto itt = m_ActorMoveTargetCache.find(targetUin);
        if (itt != m_ActorMoveTargetCache.end())
        {
            auto& tVec = itt->second;
            bool found = false;
            for (size_t i = 0; i < tVec.size(); i++)
            {
                if (tVec[i] == objid)
                {
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                tVec.push_back(objid);
            }
        }
        else
        {
            std::vector<int64_t> tVec;
            tVec.push_back(objid);
            m_ActorMoveTargetCache.emplace(targetUin, tVec);
        }
    }
}
```

### 2. 批量发送阶段 (tickActorMoveSync)

**调用时机**: 每帧调用 - `Source\SandboxEngine\Core\worldData\WorldManager.cpp:1844`

```cpp
GetGameNetManagerPtr()->tickActorMoveSync();
```

**发送逻辑**: `Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:460-486`

```cpp
void GameNetManager::tickActorMoveSync()
{
    if (m_ActorMoveTargetCache.empty())
        return;

    for (auto& data : m_ActorMoveTargetCache)
    {
        // 从对象池获取批量协议对象
        std::shared_ptr<PB_ActorMoveV3HC_Batch> batch = m_ProtoMoveV3HCPool.Acquire();
        auto target = data.first;      // 目标玩家UIN
        auto& ovec = data.second;      // 需要同步的Actor列表

        // 将缓存的移动数据打包到批量协议中
        for (auto oid : ovec)
        {
            auto it = m_ActorMoveCache.find(oid);
            if (it != m_ActorMoveCache.end())
            {
                PB_ActorMoveV2HC& am = it->second;
                auto mo = batch->add_movebatch();
                *mo = am;  // 复制移动数据到批量包中
            }
        }

        // 发送批量移动协议
        sendToClient(target, PB_ACTOR_MOVEV3_HC, *batch, 0, true,
                    UNRELIABLE_SEQUENCED, HIGH_PRIORITY, 0);

        // 释放对象池资源
        m_ProtoMoveV3HCPool.Release(batch);
    }

    // 清空缓存，准备下一帧
    m_ActorMoveCache.clear();
    m_ActorMoveTargetCache.clear();
}
```

### 3. 发送触发条件

**代码位置**: `Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:1269-1337`

**触发条件**:

1. **位置变化**: `DPSQ > 0.01f` (位置变化超过阈值)
2. **朝向变化**: `ASQ > 0.01f` (朝向变化超过阈值)
3. **强制同步**: `mustSync = true` (特殊情况)

```cpp
// 位置变化检测
float DPSQ = (curpos - mLastPosition).lengthSq();
bool posChange = DPSQ > 0.01f;

// 朝向变化检测
float ASQ = (curyaw - mLastRot.x) * (curyaw - mLastRot.x) +
            (curpitch - mLastRot.y) * (curpitch - mLastRot.y);
bool yawChange = ASQ > 0.01f;

// 判断是否需要同步
bool mustSync = posChange || yawChange || forceSync;
```

## 客户端处理机制

### handleActorMoveV32Client 处理流程

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:1174-1220`

```cpp
void MpGameSurviveNetHandler::handleActorMoveV32Client(const PB_PACKDATA_CLIENT& pkg)
{
    // 1. 解析批量协议
    PB_ActorMoveV3HC_Batch batch;
    batch.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    // 2. 遍历批量移动数据
    for (size_t i = 0; i < batch.movebatch_size(); i++)
    {
        const PB_ActorMoveV2HC& actorMoveHC = batch.movebatch(i);

        // 3. 获取Actor对象
        long long ObjId = actorMoveHC.objid();
        ObjId = ClientActor::UnpackObjId(ObjId);
        ClientActor* actor = objId2ActorOnClient(ObjId);

        if (actor)
        {
            int flag = 0;  // 移动标志
            WCoord cur_pos = actor->getPosition();

            // 4. 处理位置信息
            if (actorMoveHC.position_size() == 3)
            {
                cur_pos.x = actorMoveHC.position(0);
                cur_pos.y = actorMoveHC.position(1);
                cur_pos.z = actorMoveHC.position(2);
                flag |= MF_POSITION;
            }

            // 5. 处理朝向信息
            float yaw = actor->getFaceYaw();
            float pitch = actor->getFacePitch();
            if (actorMoveHC.has_yaw_pitch())
            {
                flag |= MF_YAW_PITCH;
                uint32_t yaw_pitch = actorMoveHC.yaw_pitch();
                yaw = AngleChar2Float((yaw_pitch & 0xff00) >> 8);
                pitch = AngleChar2Float(yaw_pitch & 0xff);
            }

            // 6. 应用移动数据
            actor->setNetMoveFlag(flag);
            actor->moveToPosition(cur_pos, yaw, pitch, 3);  // 3帧插值
            actor->clearNetMoveFlag();

            // 7. 设置地面状态
            ActorLocoMotion* locmove = actor->getLocoMotion();
            if (locmove)
            {
                locmove->setOnGround(!actorMoveHC.has_changeflags());
            }
        }
    }
}
```

### 🔍 **本地玩家 vs 远程玩家处理差异**

#### **关键发现**: handleActorMoveV32Client **不处理本地玩家移动**

**服务器端过滤机制**:

**代码位置**: `Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:160`

```cpp
// 在构建追踪玩家列表时，排除自己
for (size_t i = 0; i != mEntryActor->GetActorMgrInterface()->getNumPlayer(); ++i)
{
    IClientPlayer* player = mEntryActor->GetActorMgrInterface()->iGetIthPlayer(i);
    if (player == nullptr || player->CastToActor() == mEntryActor) continue;  // 🔑 关键过滤
    // ... 后续处理
}
```

**过滤逻辑说明**:

- `player->CastToActor() == mEntryActor` 确保玩家不会收到自己的移动广播
- 只有**其他玩家**的移动信息会被添加到追踪列表中
- 本地玩家的移动由客户端本地处理，不需要服务器同步

#### **moveToPosition 处理差异**

**代码位置**: `Source\SandboxGame\Core\actors\clientActor\ClientActor.cpp:2891-2906`

```cpp
void ClientActor::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
    if (getLocoMotion() == NULL) return;
    getLocoMotion()->m_RotateYaw = yaw;
    getLocoMotion()->m_RotationPitch = pitch;

    // 🔑 关键区分: 远程模式 vs 本地模式
    if (IsObject() && m_pWorld && m_pWorld->isRemoteMode())
    {
        // 远程玩家: 使用插值平滑
        getLocoMotion()->m_PosRotationIncrements = interpol_ticks;  // 3帧插值
        getLocoMotion()->m_ServerPos = pos;                        // 服务器位置
    }
    else
    {
        // 本地玩家: 直接设置位置
        getLocoMotion()->setPosition(pos);
    }
}
```

#### **本地玩家判断机制**

**代码位置**: `Source\SandboxEngine\Core\nodes\SandboxActorObject.cpp:474-483`

```cpp
bool SceneActorObject::IsLocalPlayerActor()
{
    if (m_UserId == 0)
        return false;
    if (m_UserId == MNSandbox::Config::GetSingleton().GetLocalUin())  // 🔑 UIN比较
    {
        return true;
    }
    return false;
}
```

#### **isRemoteMode 判断**

**代码位置**: `Source\SandboxEngine\Core\worldData\world_gen.cpp:957-960`

```cpp
/**
 * @return true 客机, false 主机
*/
bool World::isRemoteMode() const
{
    return m_isRemote || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false);
}
```

## 性能优化机制

### 1. 对象池优化

```cpp
std::shared_ptr<PB_ActorMoveV3HC_Batch> batch = m_ProtoMoveV3HCPool.Acquire();
// ... 使用批量对象
m_ProtoMoveV3HCPool.Release(batch);
```

**优势**: 避免频繁的内存分配和释放

### 2. 数据压缩

- **位置压缩**: 使用 `sint32` 存储位置，减少数据量
- **朝向压缩**: 将 yaw 和 pitch 压缩到一个 `uint32` 中
- **批量传输**: 多个移动信息合并为一个网络包

### 3. 网络传输优化

```cpp
sendToClient(target, PB_ACTOR_MOVEV3_HC, *batch, 0, true,
            UNRELIABLE_SEQUENCED, HIGH_PRIORITY, 0);
```

- **UNRELIABLE_SEQUENCED**: 不保证可靠性，但保证顺序，适合高频移动数据
- **HIGH_PRIORITY**: 高优先级传输，保证移动同步的实时性

## 📋 **处理流程总结**

### **本地玩家移动处理**:

1. **客户端输入** → **本地移动计算** → **PB_ROLE_MOVE_CH 发送给服务器**
2. **不会收到自己的 PB_ACTOR_MOVEV3_HC 广播**
3. **moveToPosition 直接设置位置**，无插值

### **远程玩家移动处理**:

1. **服务器接收其他玩家移动** → **缓存到批量协议** → **PB_ACTOR_MOVEV3_HC 广播**
2. **客户端接收批量移动数据** → **解析每个玩家移动** → **3 帧插值平滑**
3. **moveToPosition 使用插值**，保证流畅性

### **关键过滤机制**:

- **服务器端**: `player->CastToActor() == mEntryActor` 排除自己
- **客户端**: 只处理其他玩家的移动信息
- **本地玩家**: 通过 `IsLocalPlayerActor()` 和 `isRemoteMode()` 区分

## 总结

PB_ACTOR_MOVEV3_HC 是经过多次迭代优化的移动广播协议，通过以下机制实现了最佳性能：

1. **批量处理**: 将多个移动信息合并发送，减少网络调用
2. **缓存机制**: 先缓存后批量发送，避免重复数据
3. **对象池**: 复用协议对象，减少内存分配
4. **数据压缩**: 优化数据结构，减少网络带宽
5. **插值平滑**: 客户端 3 帧插值，保证移动流畅性
6. **智能过滤**: 服务器端自动排除本地玩家，避免重复处理

这套机制确保了多人游戏中移动同步的高效性和流畅性，是目前游戏中移动广播的核心协议。
