<!DOCTYPE html><html><head>
      <title>特殊开采协议-PB_BLOCK_EXPLOIT</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      <script type="text/javascript" src="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\mermaid\mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="特殊开采协议---pb_block_exploit">特殊开采协议 - PB_BLOCK_EXPLOIT </h1>
<h2 id="协议概述">协议概述 </h2>
<p>PB_BLOCK_EXPLOIT 协议是沙盒游戏中处理特殊开采操作的网络协议。该协议主要用于使用特定工具进行的开采操作，与普通挖掘(PB_BLOCK_PUNCH)不同，它更专注于精确的开采行为。</p>
<p><strong>注意</strong>: 在当前代码版本中，该协议的大部分客户端发送逻辑已被注释，但服务器端和客户端的处理逻辑仍然保留。</p>
<ul>
<li><strong>PB_BLOCK_EXPLOIT_CH</strong>: 客户端发送给服务器</li>
<li><strong>PB_BLOCK_EXPLOIT_HC</strong>: 服务器发送给客户端</li>
</ul>
<h2 id="协议定义">协议定义 </h2>
<h3 id="pb_blockexploitch-客户端--服务器">PB_BlockExploitCH (客户端 → 服务器) </h3>
<pre data-role="codeBlock" data-info="protobuf" class="language-protobuf protobuf"><code><span class="token keyword keyword-message">message</span> <span class="token class-name">PB_BlockExploitCH</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> status <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>                  <span class="token comment">// 开采状态</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> face <span class="token operator">=</span> <span class="token number">2</span><span class="token punctuation">;</span>                    <span class="token comment">// 开采面方向</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3</span> blockpos <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>  <span class="token comment">// 目标方块位置</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> picktype <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span>               <span class="token comment">// 开采工具类型</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>协议定义位置</strong>: <code>Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:957-963</code></p>
<h3 id="pb_blockexploithc-服务器--客户端">PB_BlockExploitHC (服务器 → 客户端) </h3>
<pre data-role="codeBlock" data-info="protobuf" class="language-protobuf protobuf"><code><span class="token keyword keyword-message">message</span> <span class="token class-name">PB_BlockExploitHC</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">uint64</span> objid <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>                  <span class="token comment">// 玩家对象ID</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> status <span class="token operator">=</span> <span class="token number">2</span><span class="token punctuation">;</span>                  <span class="token comment">// 开采状态</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> face <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>                    <span class="token comment">// 开采面方向</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3</span> blockpos <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span>  <span class="token comment">// 方块位置</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> picktype <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>               <span class="token comment">// 开采工具类型</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="协议流程图">协议流程图 </h2>
<div class="mermaid">sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant OtherClients as 其他客户端

    Note over Client: 玩家使用特殊工具开采
    Client-&gt;&gt;Client: ExploitState::exploitBlock()
    Note over Client: (当前版本已注释)
    Client--&gt;&gt;Server: PB_BLOCK_EXPLOIT_CH

    Note over Server: 服务器处理
    Server-&gt;&gt;Server: handleBlockExploit2Host()
    Server-&gt;&gt;Server: 验证玩家状态
    Server-&gt;&gt;Server: 获取ExploitState
    Server-&gt;&gt;Server: exploitState-&gt;exploitBlock()

    Note over Server: 广播给其他玩家
    Server-&gt;&gt;Server: notifyExploitBlock2Tracking()
    Note over Server: (当前版本已注释)
    Server--&gt;&gt;OtherClients: PB_BLOCK_EXPLOIT_HC

    Note over OtherClients: 客户端同步
    OtherClients-&gt;&gt;OtherClients: handleBlockExploit2Client()
    OtherClients-&gt;&gt;OtherClients: 执行开采逻辑
</div><h2 id="详细实现流程">详细实现流程 </h2>
<h3 id="1-客户端发送协议-已注释">1. 客户端发送协议 (已注释) </h3>
<p><strong>原始触发位置</strong>: <code>Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:854-873</code> (已注释)</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 注释的客户端发送代码</span>
<span class="token comment">//bool MpPlayerControl::exploitBlock(const WCoord &amp;targetblock, DirectionType targetface, int status, int pickType)</span>
<span class="token comment">//{</span>
<span class="token comment">//    if (!PlayerControl::exploitBlock(targetblock, targetface, status, pickType)) return false;</span>
<span class="token comment">//</span>
<span class="token comment">//    if (m_pWorld-&gt;isRemoteMode())</span>
<span class="token comment">//    {</span>
<span class="token comment">//        PB_BlockExploitCH blockExploitCH;</span>
<span class="token comment">//        blockExploitCH.set_face(targetface);</span>
<span class="token comment">//        blockExploitCH.set_status(status);</span>
<span class="token comment">//</span>
<span class="token comment">//        PB_Vector3* blockPos = blockExploitCH.mutable_blockpos();</span>
<span class="token comment">//        blockPos-&gt;set_x(targetblock.x);</span>
<span class="token comment">//        blockPos-&gt;set_y(targetblock.y);</span>
<span class="token comment">//        blockPos-&gt;set_z(targetblock.z);</span>
<span class="token comment">//</span>
<span class="token comment">//        blockExploitCH.set_picktype(pickType);</span>
<span class="token comment">//</span>
<span class="token comment">//        GameNetManager::getInstance()-&gt;sendToHost(PB_BLOCK_EXPLOIT_CH, blockExploitCH);</span>
<span class="token comment">//    }</span>
<span class="token comment">//    return true;</span>
<span class="token comment">//}</span>
</code></pre><p><strong>协议参数说明</strong>:</p>
<ul>
<li><code>status</code>: 开采状态（开始/进行中/结束）</li>
<li><code>face</code>: 开采面方向</li>
<li><code>blockpos</code>: 目标方块坐标</li>
<li><code>picktype</code>: 开采工具类型标识</li>
</ul>
<h3 id="2-服务器处理协议">2. 服务器处理协议 </h3>
<p><strong>处理函数</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1703-1722</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleBlockExploit2Host</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> uin<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> PB_PACKDATA <span class="token operator">&amp;</span>pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    ClientPlayer <span class="token operator">*</span>player <span class="token operator">=</span> <span class="token function">checkDownedPlayerByMsg2Host</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    PB_BlockExploitCH blockExploitCH<span class="token punctuation">;</span>
    blockExploitCH<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    WCoord blockpos <span class="token operator">=</span> <span class="token function">MPVEC2WCoord</span><span class="token punctuation">(</span>blockExploitCH<span class="token punctuation">.</span><span class="token function">blockpos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-auto">auto</span> pState <span class="token operator">=</span> player<span class="token operator">-&gt;</span><span class="token function">getActionStatePtr</span><span class="token punctuation">(</span><span class="token string">"Exploit"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-nullptr">nullptr</span> <span class="token operator">!=</span> pState<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-auto">auto</span> pExploitState <span class="token operator">=</span> <span class="token generic-function"><span class="token function">dynamic_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>ExploitState<span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>pState<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-nullptr">nullptr</span> <span class="token operator">!=</span> pExploitState<span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            pExploitState<span class="token operator">-&gt;</span><span class="token function">exploitBlock</span><span class="token punctuation">(</span>blockpos<span class="token punctuation">,</span> <span class="token punctuation">(</span>DirectionType<span class="token punctuation">)</span>blockExploitCH<span class="token punctuation">.</span><span class="token function">face</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
                                      blockExploitCH<span class="token punctuation">.</span><span class="token function">status</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> blockExploitCH<span class="token punctuation">.</span><span class="token function">picktype</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 注释的旧版本调用</span>
    <span class="token comment">//player-&gt;exploitBlock(blockpos, (DirectionType)blockExploitCH.face(), blockExploitCH.status(), blockExploitCH.picktype());</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>处理步骤</strong>:</p>
<ol>
<li>验证玩家状态 (<code>checkDownedPlayerByMsg2Host</code>)</li>
<li>解析协议数据</li>
<li>获取玩家的 ExploitState 状态对象</li>
<li>调用 ExploitState 的 exploitBlock 方法执行开采逻辑</li>
</ol>
<h3 id="3-服务器响应广播-已注释">3. 服务器响应广播 (已注释) </h3>
<p><strong>原始响应函数</strong>: <code>Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:131-146</code> (已注释)</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 注释的服务器响应代码</span>
<span class="token comment">//void ClientPlayer::notifyExploitBlock2Tracking(const WCoord &amp;blockpos, DirectionType face, int status, int pickType)</span>
<span class="token comment">//{</span>
<span class="token comment">//    if (!m_pWorld-&gt;isRemoteMode())</span>
<span class="token comment">//    {</span>
<span class="token comment">//        PB_BlockExploitHC blockExploitHC;</span>
<span class="token comment">//        blockExploitHC.set_objid(getObjId());</span>
<span class="token comment">//        blockExploitHC.set_status(status);</span>
<span class="token comment">//        blockExploitHC.set_face(face);</span>
<span class="token comment">//        blockExploitHC.mutable_blockpos()-&gt;set_x(blockpos.x);</span>
<span class="token comment">//        blockExploitHC.mutable_blockpos()-&gt;set_y(blockpos.y);</span>
<span class="token comment">//        blockExploitHC.mutable_blockpos()-&gt;set_z(blockpos.z);</span>
<span class="token comment">//        blockExploitHC.set_picktype(pickType);</span>
<span class="token comment">//</span>
<span class="token comment">//        m_pWorld-&gt;getMpActorMgr()-&gt;sendMsgToTrackingPlayers(PB_BLOCK_EXPLOIT_HC, blockExploitHC, this);</span>
<span class="token comment">//    }</span>
<span class="token comment">//}</span>
</code></pre><h3 id="4-客户端接收响应">4. 客户端接收响应 </h3>
<p><strong>处理函数</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:3825-3840</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleBlockExploit2Client</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> PB_PACKDATA_CLIENT <span class="token operator">&amp;</span>pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    PB_BlockExploitHC blockExploitHC<span class="token punctuation">;</span>
    blockExploitHC<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    ClientPlayer <span class="token operator">*</span>player <span class="token operator">=</span> <span class="token generic-function"><span class="token function">dynamic_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>ClientPlayer <span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token function">objId2ActorOnClient</span><span class="token punctuation">(</span>blockExploitHC<span class="token punctuation">.</span><span class="token function">objid</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-auto">auto</span> pState <span class="token operator">=</span> player<span class="token operator">-&gt;</span><span class="token function">getCurrentActionStatePtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-auto">auto</span> pExploitState <span class="token operator">=</span> <span class="token generic-function"><span class="token function">dynamic_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>ExploitState<span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>pState<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token keyword keyword-nullptr">nullptr</span> <span class="token operator">!=</span> pExploitState<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        pExploitState<span class="token operator">-&gt;</span><span class="token function">exploitBlock</span><span class="token punctuation">(</span><span class="token function">MPVEC2WCoord</span><span class="token punctuation">(</span>blockExploitHC<span class="token punctuation">.</span><span class="token function">blockpos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
                                  <span class="token punctuation">(</span>DirectionType<span class="token punctuation">)</span>blockExploitHC<span class="token punctuation">.</span><span class="token function">face</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
                                  blockExploitHC<span class="token punctuation">.</span><span class="token function">status</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
                                  blockExploitHC<span class="token punctuation">.</span><span class="token function">picktype</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 注释的旧版本调用</span>
    <span class="token comment">//player-&gt;exploitBlock(MPVEC2WCoord(blockExploitHC.blockpos()), (DirectionType)blockExploitHC.face(), blockExploitHC.status(), blockExploitHC.picktype());</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>处理步骤</strong>:</p>
<ol>
<li>解析协议数据</li>
<li>根据对象 ID 找到对应玩家</li>
<li>获取当前的 ExploitState 状态</li>
<li>执行开采逻辑</li>
</ol>
<h2 id="协议注册">协议注册 </h2>
<p><strong>注册位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 服务器端处理器注册 (构造函数中)</span>
<span class="token function">REGIS_HOST_HANDLER</span><span class="token punctuation">(</span>PB_BLOCK_EXPLOIT_CH<span class="token punctuation">,</span> handleBlockExploit2Host<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 客户端处理器注册 (行号482)</span>
<span class="token function">REGIS_CLIENT_HANDLER</span><span class="token punctuation">(</span>PB_BLOCK_EXPLOIT_HC<span class="token punctuation">,</span> handleBlockExploit2Client<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h2 id="exploitstate-状态管理">ExploitState 状态管理 </h2>
<p>ExploitState 是专门处理开采操作的状态类，与 DigState 不同：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">ExploitState</span> <span class="token operator">:</span> <span class="token base-clause"><span class="token keyword keyword-public">public</span> <span class="token class-name">ActionState</span></span>
<span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">exploitBlock</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> WCoord<span class="token operator">&amp;</span> blockpos<span class="token punctuation">,</span> DirectionType face<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> status<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> picktype<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// ... 其他方法</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><p><strong>主要特点</strong>:</p>
<ul>
<li>专门处理特殊工具的开采操作</li>
<li>支持不同的开采工具类型 (<code>picktype</code>)</li>
<li>独立的状态管理机制</li>
</ul>
<h2 id="协议状态分析">协议状态分析 </h2>
<h3 id="当前状态">当前状态 </h3>
<ol>
<li><strong>服务器端处理</strong>: ✅ 完全可用</li>
<li><strong>客户端处理</strong>: ✅ 完全可用</li>
<li><strong>客户端发送</strong>: ❌ 已注释禁用</li>
<li><strong>服务器广播</strong>: ❌ 已注释禁用</li>
</ol>
<h3 id="可能的使用场景">可能的使用场景 </h3>
<ol>
<li><strong>特殊工具开采</strong>: 使用特定工具进行精确开采</li>
<li><strong>资源收集</strong>: 针对特定资源的开采操作</li>
<li><strong>技能系统</strong>: 与玩家技能相关的开采行为</li>
</ol>
<h2 id="与其他协议的区别">与其他协议的区别 </h2>
<table>
<thead>
<tr>
<th>协议</th>
<th>用途</th>
<th>状态</th>
<th>特点</th>
</tr>
</thead>
<tbody>
<tr>
<td>PB_BLOCK_PUNCH</td>
<td>普通挖掘</td>
<td>✅ 活跃使用</td>
<td>支持载具、多种挖掘模式</td>
</tr>
<tr>
<td>PB_BLOCK_EXPLOIT</td>
<td>特殊开采</td>
<td>⚠️ 部分禁用</td>
<td>专用工具、精确开采</td>
</tr>
<tr>
<td>PB_BLOCK_INTERACT</td>
<td>方块放置</td>
<td>✅ 活跃使用</td>
<td>方块放置和交互</td>
</tr>
</tbody>
</table>
<h2 id="协议特点">协议特点 </h2>
<ol>
<li><strong>专业化</strong>: 专门用于特殊工具的开采操作</li>
<li><strong>工具类型</strong>: 支持不同开采工具的区分 (<code>picktype</code>)</li>
<li><strong>状态驱动</strong>: 基于 ExploitState 状态机</li>
<li><strong>部分禁用</strong>: 客户端发送和服务器广播功能已被注释</li>
<li><strong>向后兼容</strong>: 保留完整的处理逻辑以备将来启用</li>
</ol>
<h2 id="潜在重启方案">潜在重启方案 </h2>
<p>如需重新启用该协议，需要：</p>
<ol>
<li><strong>取消注释客户端发送逻辑</strong></li>
<li><strong>取消注释服务器广播逻辑</strong></li>
<li><strong>添加适当的触发条件</strong></li>
<li><strong>完善 ExploitState 状态管理</strong></li>
<li><strong>添加权限验证机制</strong></li>
</ol>
<h2 id="相关协议">相关协议 </h2>
<ul>
<li><strong>PB_BLOCK_PUNCH</strong>: 普通方块挖掘协议</li>
<li><strong>PB_BLOCK_INTERACT</strong>: 方块交互协议</li>
<li><strong>PB_ITEM_USE</strong>: 道具使用协议</li>
</ul>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>