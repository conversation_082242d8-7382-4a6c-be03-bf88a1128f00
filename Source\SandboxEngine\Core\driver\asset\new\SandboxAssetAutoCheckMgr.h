#pragma once

#include "SandboxType.h"
#include "SandboxSingleton.h"
#include "SandboxListener.h"
#include "SandboxSingleton.h"

namespace MNSandbox {

	class AssetUploadRef;

	class EXPORT_SANDBOXDRIVERMODULE AssetAutoCheckMgr :public Ref, public MNSandbox::Singleton<AssetAutoCheckMgr>
	{
	private:
		enum class CheckState
		{
			None,
			Checking,
			Remove,
		};
		struct CheckItem
		{
			CheckState state;
			AutoRef<AssetUploadRef> ref;
			std::function<bool()> checkCallback;
			std::function<void()> endCallback;
		};
	public:
		AssetAutoCheckMgr();
		virtual ~AssetAutoCheckMgr();

		void Push(AutoRef<AssetUploadRef> checkOwner, std::function<bool()> checkCallback, std::function<void()>endCallback);
		std::vector<CheckItem> m_waitList;
		std::vector<CheckItem> m_checkList;

	private:
		void OnUpdate(float f);
		ListenerClass<AssetAutoCheckMgr, float> m_listenGlobalUpdate;
	};

}