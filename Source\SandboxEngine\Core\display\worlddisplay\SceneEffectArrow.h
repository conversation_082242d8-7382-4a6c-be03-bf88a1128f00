#pragma once
/**
* file : SceneEffectArrow
* func : 场景效果（箭头）
* by : yangzy
*/
#include "SceneEffectGeom.h"
#include "SceneEffectLine.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectArrow : public SceneEffectGeom
{
public:
	SceneEffectArrow();
	SceneEffectArrow(MNSandbox::MNCoord3f arrowPos, MNSandbox::MNCoord3f arrowDir, DirectionType dir, float ratio = 1.0, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectArrow();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;

public:
	// 设置箭头位置方向
	void SetArrow(MNSandbox::MNCoord3f arrowPos, MNSandbox::MNCoord3f arrowDir, DirectionType dir);
	//设置箭头宽度大小
	void SetArrowRange(int arrowWidth, int arrowLength);
	// 设置比率
	void SetRatio(float ratio);
	void RefreshArrowFrame();
	// 射线选中
	bool RayArrow(MNSandbox::Ray& ray, MNSandbox::MNCoord3f& targetPos);
	//
	Rainbow::Quaternionf GetQua();

	//
	enum class ARROWTYPE
	{
		NORMAL,		//普通箭头
		SIMPLIFY,	//精简箭头
	};
	// 设置箭头类型
	void SetArrowType(ARROWTYPE arrowType);

private:
	// 属性
	MNSandbox::MNCoord3f m_arrowPos = MNSandbox::MNCoord3f(0.0);
	MNSandbox::MNCoord3f m_arrowDir = MNSandbox::MNCoord3f(0.0);
	DirectionType m_dir = DIR_NOT_INIT;
	ARROWTYPE m_arrowType = ARROWTYPE::NORMAL;

	static const unsigned  ms_lineWidth = 4;	
	static const unsigned  ms_lineLength = 150;	
	static const unsigned  ms_lineOffsetSimplify = 100;
	static const unsigned  ms_lineWidthSimplify = 4;
	static const unsigned  ms_lineLengthSimplify = 80;

	unsigned  ms_arrowWidth = 8;
	unsigned  ms_arrowLength = 50;

	int m_lineWidth = ms_lineWidth;
	int m_lineLength = ms_lineLength;

	int m_lineOffsetSimplify = ms_lineOffsetSimplify;
	int m_lineWidthSimplify = ms_lineWidthSimplify;
	int m_lineLengthSimplify = ms_lineLengthSimplify;
	int m_arrowWidth = ms_arrowWidth;
	int m_arrowLength = ms_arrowLength;

	struct DrawLine
	{
		SceneEffectLine* _line = nullptr; // 绘制线条
		MNSandbox::MNCoord3f _startDrawPos = MNSandbox::MNCoord3f();
		MNSandbox::MNCoord3f _endDrawPos = MNSandbox::MNCoord3f();

		DrawLine() = default;
		DrawLine(MNSandbox::MNCoord3f start, MNSandbox::MNCoord3f end) :_startDrawPos(start), _endDrawPos(end)
		{
		}
	};

	DrawLine m_arrowLine;
	std::vector<DrawLine> m_vecArrowLineSimplify = {};
	std::vector<BlockGeomVert> m_vecVertexs1 = {};
	std::vector<BlockGeomVert> m_vecVertexs2 = {};
	std::vector<BlockGeomVert> m_vecVertexs3 = {};
	std::vector<BlockGeomVert> m_vecVertexs4 = {};
	std::vector<unsigned short> m_vecIndices = {};
	std::vector<DrawLine> m_vecRayPickLine = {};

public:
	static std::map<DirectionType, MNSandbox::MNCoord3f> m_mapArrowOriginDir;		//箭头方向
};