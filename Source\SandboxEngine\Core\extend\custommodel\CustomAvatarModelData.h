#ifndef __CustomAvatarModelData_h__
#define __CustomAvatarModelData_h__ 1

#include "Math/Vector3f.h"

#include <string>
#include "SandboxEngine.h"

namespace Rainbow
{
	class Model;
}

class FullyCustomBoneData;

namespace FBSave
{
	struct AvatarModelData;
}
namespace game
{
	namespace common
	{
		class PB_ActorOneAvatarModelData;
	}
}
struct EXPORT_SANDBOXENGINE CustomAvatarModelData;
//tolua_begin
struct CustomAvatarModelData
{
	std::string modelfilename;
	float scale;
	float yaw;
	float pitch;
	short offset_x;
	short offset_y;
	short offset_z;
	float roll;
	bool show;
	//是否用了新的欧拉角转四元素的接口
	bool newrotatemode;
	bool isdownload;
	//旧版开发者仅开发了相同尺寸缩放，现扩展到各自的缩放
	Rainbow::Vector3f scale3;
	Rainbow::Model* model;

	CustomAvatarModelData();
	void setModelScale(float scale);
	void setPosition(short x, short y, short z);
	void setRotation(float yaw, float pitch, float roll);
	void setOverlayColor(bool show);
};
//tolua_end

EXPORT_SANDBOXENGINE extern void CamdFromFbs(CustomAvatarModelData& camd, const FBSave::AvatarModelData* fbsAmd);
EXPORT_SANDBOXENGINE extern void CamdFromPb(CustomAvatarModelData& camd, const game::common::PB_ActorOneAvatarModelData* pbAoamd);
void CamdFromFcbd(CustomAvatarModelData& camd, const FullyCustomBoneData* fcbd);
EXPORT_SANDBOXENGINE extern void CamdToPb(const CustomAvatarModelData& camd, game::common::PB_ActorOneAvatarModelData* pbAoamd);
#endif//__CustomAvatarModelData_h__