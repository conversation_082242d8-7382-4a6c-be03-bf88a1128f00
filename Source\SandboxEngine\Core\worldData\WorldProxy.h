
#ifndef __POPULATE_WORLD_H__
#define __POPULATE_WORLD_H__

#include <set>
#include "world_types.h"
#include "Common/OgreShared.h"
#include "OgreBlock.h"
#include "chunkrandom.h"
#include "Threads/Mutex.h"
#include "OgreHashTable.h"
#include "SandboxEngine.h"

class CityMgrInterface;
class ChunkGenerator;
class EcosystemManager;
class Ecosystem;
class BlockMaterial;
struct BiomeDef;

class EXPORT_SANDBOXENGINE WorldProxy;
class WorldProxy //tolua_exports
{ //tolua_exports
public:
	virtual ~WorldProxy() = default;

	//tolua_begin
	virtual bool isRemoteMode() = 0;

	virtual EcosystemManager *getBiomeManager() = 0;
	virtual Ecosystem *getBiomeGen(int x, int z) = 0;
	virtual const BiomeDef *getBiome(int x, int z) = 0;
	virtual int getBiomeType(int x, int z) = 0;

	virtual Block getBlock(const WCoord &grid)= 0;
	virtual bool setBlockAll(const WCoord &pos, int blockid, int data, int flags, int dataEx = 0) = 0;
	virtual bool hasChunk(CHUNK_INDEX chunkindex) = 0;

	virtual int getBlockSunIllum(const WCoord &blockpos) = 0;
	virtual int getFullBlockLightValue(const WCoord &pos) = 0;
	
	virtual int getTopHeight(int x, int z) = 0;
	virtual int getTopSolidOrLiquidBlock(int x, int z) = 0;
	virtual int getPrecipitationHeight(int x, int z) = 0;
	/*
		获取一定范围的方块高度
	*/
	virtual int getLimitHeight(int x, int z, int minHeight, int maxHeight) = 0;
	virtual WORLD_SEED getRandomSeed() = 0;
	virtual unsigned short getCurMapID() = 0;

	virtual void cacheChunks(int sx, int sz, int ex, int ez) = 0;
	virtual void cancelCacheChunks() = 0;
	virtual void clearCacheBlock() = 0;

	virtual void scheduleImmediateBlockTick(int blockResId, const WCoord &blockpos) = 0;
	virtual void addDungeonChest(const WCoord &pos, int blockid, int blockdata, const ChunkRandGen& randgen) = 0;
	virtual void addRandomItemToChest(const WCoord &pos, int blockid, const ChunkRandGen& randgen) = 0;
	virtual void addStorageBox(int x, int y, int z, int blockIdToCheck) = 0;
	virtual void spawnMob(const WCoord &pos, int monsterid, bool mobtype_check, bool mob_check) = 0;
	
	virtual void setChunkDungeonPos(const WCoord &blockInChunk, const WCoord &dungeonPos) = 0;

	virtual int getChunkActualHeight() = 0;
	
	BlockMaterial *getBlockMaterial(const WCoord &grid) ;

	Block getBlock(int x, int y, int z) 
	{
		return getBlock(WCoord(x, y, z));
	}
	bool setBlockAll(int x, int y, int z, int blockid, int data, int flags, int dataEx = 0)
	{
		return setBlockAll(WCoord(x, y, z), blockid, data, flags, dataEx);
	}

	int getBlockID(const WCoord &pos) 
	{
		return getBlock(pos).getResID();
	}
	int getBlockID(int x, int y, int z) 
	{
		return getBlock(x, y, z).getResID();
	}
	int getBlockData(const WCoord &pos) 
	{
		return getBlock(pos).getData();
	}
	int getBlockData(int x, int y, int z) 
	{
		return getBlock(x, y, z).getData();
	}
	int getBlockDataEx(const WCoord &pos) 
	{
		return getBlock(pos).getDataEx();
	}
	int getBlockDataEx(int x, int y, int z) 
	{
		return getBlock(x, y, z).getDataEx();
	}
	bool isAirBlock(const WCoord &pos) 
	{
		return getBlockID(pos) == 0;
	}
	bool isAirBlock(int x, int y, int z) 
	{
		return getBlockID(x, y, z) == 0;
	}
	bool isBlockSolid(const WCoord& pos);
	bool isBlockSolid(int x, int y, int z);
	bool isBlockLiquid(const WCoord& pos);

	bool isBlockLiquid(int x, int y, int z);
	bool isBlockNormalCube(const WCoord& pos);
	bool isBlockNormalCube(int x, int y, int z);
	bool isBlockOpaqueCube(const WCoord& pos);
	bool isBlockOpaqueCube(int x, int y, int z);
	bool getBlockIcing(const WCoord& blockpos, bool onlyedge = false);
	bool getBlockSnowing(const WCoord& pos);
	bool doesBlockHaveSolidTopSurface(const WCoord &pos);

	virtual World* getWorld() = 0;
	//tolua_end
	virtual bool isSpecialBiome(int x, int z) { return false; };
}; //tolua_exports

// used for main thread, access client world directly
class EXPORT_SANDBOXENGINE MainWorldProxy;
class MainWorldProxy : public WorldProxy //tolua_exports
{ //tolua_exports
	World* pworld;
public:
	//tolua_begin
	MainWorldProxy(World* world):pworld(world) {}
	virtual ~MainWorldProxy(){}

	virtual bool isRemoteMode();

	virtual Block getBlock(const WCoord &grid) ;
	virtual bool setBlockAll(const WCoord &pos, int blockid, int data, int flags, int dataEx = 0);
	virtual bool hasChunk(CHUNK_INDEX chunkindex);

	virtual int getBlockSunIllum(const WCoord &blockpos);
	virtual int getFullBlockLightValue(const WCoord &pos);

	virtual int getTopHeight(int x, int z);
	virtual int getTopSolidOrLiquidBlock(int x, int z);
	virtual int getPrecipitationHeight(int x, int z);

	/*
		获取一定范围的方块高度
	*/
	virtual int getLimitHeight(int x, int z, int minHeight, int maxHeight);
	virtual EcosystemManager *getBiomeManager();
	virtual Ecosystem *getBiomeGen(int x, int z);
	virtual const BiomeDef *getBiome(int x, int z);
	virtual int getBiomeType(int x, int z);

	virtual WORLD_SEED getRandomSeed();
	virtual unsigned short getCurMapID();

	virtual void cacheChunks(int sx, int sz, int ex, int ez);
	virtual void cancelCacheChunks();
	virtual void clearCacheBlock();

	virtual void scheduleImmediateBlockTick(int blockResId, const WCoord &blockpos);
	virtual void addDungeonChest(const WCoord &pos, int blockid, int blockdata, const ChunkRandGen& randgen);
	virtual void addRandomItemToChest(const WCoord &pos, int blockid, const ChunkRandGen& randgen);
	virtual void addStorageBox(int x, int y, int z, int blockIdToCheck);
	virtual void spawnMob(const WCoord &pos, int monsterid, bool mobtype_check, bool mob_check);
	
	virtual void setChunkDungeonPos(const WCoord &blockInChunk, const WCoord &dungeonPos);

	virtual int getChunkActualHeight();

	virtual World* getWorld() {
		return pworld;
	}
	//tolua_end
	virtual bool isSpecialBiome(int x, int z) override;
}; //tolua_exports

#endif