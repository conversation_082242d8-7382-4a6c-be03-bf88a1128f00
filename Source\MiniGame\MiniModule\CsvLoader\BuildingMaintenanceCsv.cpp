#include "BuildingMaintenanceCsv.h"
#include "OgreUtils.h"
#include "OgreStringUtil.h"
#include "defmanager.h"
#include "ModManager.h"

using MINIW::CSVParser;
using namespace MINIW;

IMPLEMENT_LAZY_SINGLETON(BuildingMaintenanceCsv)

BuildingMaintenanceCsv::BuildingMaintenanceCsv()
{
}

BuildingMaintenanceCsv::~BuildingMaintenanceCsv()
{
    onClear();
}

const char* BuildingMaintenanceCsv::getName()
{
    return "BuildingBaintenance_new";
}

const char* BuildingMaintenanceCsv::getClassName()
{
    return "BuildingMaintenanceCsv";
}

void BuildingMaintenanceCsv::onClear()
{
    m_Table.clear();
}

void BuildingMaintenanceCsv::onParse(CSVParser& parser)
{
    onClear();
    
    parser.SetTitleLine(1);
    int numLines = (int)parser.GetNumLines();
    
    for (int i = 2; i < numLines; ++i)
    {
        BuildingMaintenanceCsvDef def;
        def.num = parser[i]["num"].Int();
        
        // 跳过num为0的行
        if (def.num == 0) continue;
        
        def.proportion = parser[i]["proportion"].Int();
        
        // 添加到表格，使用num作为键
        m_Table.AddRecord(def.num, def);
    }
}

int BuildingMaintenanceCsv::getNum()
{
    load();
    return m_Table.GetRecordSize();
}

const BuildingMaintenanceCsvDef* BuildingMaintenanceCsv::get(int num)
{
    load();
    return m_Table.GetRecord(num);
}

std::vector<const BuildingMaintenanceCsvDef*> BuildingMaintenanceCsv::getAll()
{
    load();
    std::vector<const BuildingMaintenanceCsvDef*> result;
    
    int size = m_Table.GetRecordSize();
    for (int i = 0; i < size; ++i)
    {
        const BuildingMaintenanceCsvDef* record = m_Table.GetRecordByIndex(i);
        if (record) {
            result.push_back(record);
        }
    }
    
    return result;
} 