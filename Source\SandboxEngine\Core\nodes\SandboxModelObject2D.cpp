﻿/** @file
 * @name  SandboxTransObject2D.cpp
 * @parent  SandboxNode
 * @brief  位移，旋转等操作的基类节点
 * <AUTHOR> @date 
 * @version
 */

#include  "SandboxTransObject2D.h"
#include  "SandboxModelObject2D.h"
#include "SandboxSceneObject.h"
#include "SandboxListener.h"
#include "SandboxScene.h"
#include "SandboxActorObject.h"
#include "SandboxScene.h"
#include "WorldScene.h"

#include "GeometryObject/GeometryObject.h"
#include "world.h"
#include"Core/GameObject.h"
#include "Core/GameScene.h"
#include "Core/ComponentEvents.h"
#include "BlockScene.h"
#include "Math/LegacyBounding.h"
#include "SandboxNodeBoundingBox.h"
#include "SandboxUIModelView.h"

#include "SandboxSceneChunk.h"
#include "SandboxSceneRoot.h"
#include "SandboxGameSetNode.h"
#include "WorldRender.h"
#include "GetTime.h"
#include "SandboxBindAttachment.h"
#include "SandboxPlayerObject.h"
#include "scene/SandboxSceneManager.h"
#include "worldData/WorldManager.h"
#include "Render/TransformManager.h"
#include "SandboxAssetInstanceRainbowObject.h"
#include "2d/CCCamera.h"

namespace MNSandbox {

	IMPLEMENT_SCENEOBJECTCLASS_SHORTNAME(SceneModelObject2D, "Model2D", "SceneModelObject2D");


	/** @par
	* @name  Size
	* @brief  模型的包围盒大小
	*/
	ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> SceneModelObject2D::RCP_Size(1, "Size", "Boundbox", &SceneModelObject2D::GetSize, &SceneModelObject2D::SetSize);
	/** @par
	 * @name  Center
	 * @brief  模型的中心点所在世界坐标
	 */
	ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> SceneModelObject2D::RCP_Center(2, "Center", "Boundbox", &SceneModelObject2D::GetCenter, &SceneModelObject2D::SetCenter);


	/** @par
	* @name  TextureId
	* @brief  设置模型的材质，即资源id
	*/
	ReflexClassParam<SceneModelObject2D, SceneModelObject::ModelAssetType> SceneModelObject2D::RCP_MainTextureId(3, "TextureId", "model", &SceneModelObject2D::SetMainTextureIdR, &SceneModelObject2D::GetMainTextureIdR);


	/** @par
 * @name  Color
 * @brief  模型的颜色
 */
	ReflexClassParam<SceneModelObject2D, Rainbow::ColorQuad> SceneModelObject2D::RCP_Color(4, "Color", "model", &SceneModelObject2D::SetColor, &SceneModelObject2D::GetColor);



	/** @par
 * @name  EnableGravity
 * @brief  是否开启重力
 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_EnableGravity(5,"EnableGravity","physx",&SceneModelObject2D::SetEnableGravity,&SceneModelObject2D::GetEnableGravity);
	/** @par
 * @name  Anchored
 * @brief  是否锚定
 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_Anchor(6,"Anchored","physx",&SceneModelObject2D::SetAnchored,&SceneModelObject2D::GetAnchored);
	/** @par
 * @name  Gravity
 * @brief  重力
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_Gravity(7,"Gravity","physx",&SceneModelObject2D::SetGravity,&SceneModelObject2D::GetGravity);
	/** @par
 * @name  Mass
 * @brief  质量
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_Mass(8,"Mass","physx",&SceneModelObject2D::GetMass , ReflexConfig::REG_ONLY_SHOWEX);
	/** @par
 * @name  Mass
 * @brief  密度
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_Density(9, "Density", "physx", &SceneModelObject2D::SetDensity, &SceneModelObject2D::GetDensity); 

	/** @par
 * @name  Restitution
 * @brief  弹性
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_Restitution(10,"Restitution","physx",&SceneModelObject2D::SetRestitution,&SceneModelObject2D::GetRestitution);
	/** @par
 * @name  Friction
 * @brief  马擦力
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_Friction(11, "Friction", "physx", &SceneModelObject2D::SetFriction, &SceneModelObject2D::GetFriction);
	/** @par
 * @name  Velocity
 * @brief  速度
 */
	ReflexClassParam<SceneModelObject2D, Rainbow::Vector2f> SceneModelObject2D::RCP_Velocity(12, "Velocity", "physx", &SceneModelObject2D::SetVelocity, &SceneModelObject2D::GetVelocity);
	/** @par
 * @name  AngleVelocity
 * @brief 角速度
 */
	ReflexClassParam<SceneModelObject2D, float> SceneModelObject2D::RCP_AngleVelocity(13, "AngleVelocity", "physx", &SceneModelObject2D::SetAngleVelocity, &SceneModelObject2D::GetAngleVelocity);
	/** @par
	 * @name  CanCollide
	 * @brief 是否可以产生物理碰撞
	 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_CanCollide(14, "CanCollide", "physx", &SceneModelObject2D::SetCanCollide, &SceneModelObject2D::GetCanCollide);
	/** @par
	 * @name  CanTouch
	 * @brief 是否可以碰撞回调
	 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_CanTouch(15, "CanTouch", "physx", &SceneModelObject2D::SetCanTouch, &SceneModelObject2D::GetCanTouch);
	
	/** @par
	 * @name  GroupID
	 * @brief 设置碰撞组
	 */
	ReflexClassParam<SceneModelObject2D, unsigned int> SceneModelObject2D::RCP_GroupID(16, "GroupID", "physx", &SceneModelObject2D::SetGroupID, &SceneModelObject2D::GetGroupID);
	/** @par
	 * @name  EnablePhysics
	 * @brief 是否生效
	 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_Enable(17, "EnablePhysics", "physx", &SceneModelObject2D::SetEnable, &SceneModelObject2D::GetEnable);
	/** @par
	 * @name  EnableCCD
	 * @brief 是否开启CCD检测，可以防止物体快速移动的情况下发生穿透  会影响性能，所以默认不开启
	 */
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_EnableCCD(18, "EnableCCD", "physx", &SceneModelObject2D::SetEnableCCD, &SceneModelObject2D::GetEnableCCD);
	
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_EnableDrawCollider(19,"EnableDrawCollider","physx",&SceneModelObject2D::SetEnableDrawCollider,&SceneModelObject2D::GetEnableDrawCollider, ReflexConfig::REG_LOCAL);
	//ReflexClassParam<SceneModelObject2D, LocomotionComponent2D::PHYSX2D_TYPE> SceneModelObject2D::RCP_PhysXType(20,"PhysXType","physx",&SceneModelObject2D::SetPhysx2DType,&SceneModelObject2D::GetPhysx2DType);

	ReflexClassParam<SceneModelObject2D, std::vector<std::string> > SceneModelObject2D::RCP_PhysxVerts(21,"PhysxVerts","physx",&SceneModelObject2D::SetPhysxVerts,&SceneModelObject2D::GetPhysxVerts, ReflexConfig::NO_SCRIPT | ReflexConfig::NO_PUBLIC | ReflexConfig::SYNC_ONLYHOST);
	//图片原始大小
	ReflexClassParam<SceneModelObject2D, std::vector<int> > SceneModelObject2D::RCP_SrcSize(22,"SrcSize","model",&SceneModelObject2D::SetSrcSize,&SceneModelObject2D::GetSrcSize, ReflexConfig::NO_SCRIPT | ReflexConfig::NO_PUBLIC | ReflexConfig::SYNC_ONLYHOST);
	
	
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_FlippedX(23, "FlippedX", "model", &SceneModelObject2D::SetFlippedX, &SceneModelObject2D::GetFlippedX);
	ReflexClassParam<SceneModelObject2D, bool> SceneModelObject2D::RCP_FlippedY(24, "FlippedY", "model", &SceneModelObject2D::SetFlippedY, &SceneModelObject2D::GetFlippedY);
	/** @event
	 * @name  Touched
	 * @brief  模型被其他模型碰撞时，会触发一个Touched通知
	 * @param  node  < 被碰撞的模型节点对象
	 * @param  pos  < 世界坐标
	 * @param  normal  < 世界坐标
	 */
	ReflexClassNotify<SceneModelObject2D, AutoRef<SandboxNode>, Rainbow::Vector2f, Rainbow::Vector2f> SceneModelObject2D::Touched("Touched", "touch", &SceneModelObject2D::GetTouched);
	/** @event
	 * @name  TouchEnded
	 * @brief  模型被其他模型碰撞结束时，会触发一个TouchEnded通知
	 * @param  node  < 被碰撞的模型节点对象
	 */
	ReflexClassNotify<SceneModelObject2D, AutoRef<SandboxNode>> SceneModelObject2D::TouchEnded("TouchEnded", "touch", &SceneModelObject2D::GetTouchEnded);

	


	/** @enum
	 * @name  PhysicsType
	 * @brief  物理类型
	 */
	// ReflexEnumDesc<LocomotionComponent2D::PHYSX2D_TYPE> SceneModelObject2D::R_PhysicsEnumType("PHYSX2D_TYPE", (int)REFLEXTYPEENUM_ENUM_PHYSICSTYPE2D, {
	// 	{LocomotionComponent2D::PHYSX2D_TYPE::POLYGON,"BOX"},                      /**< 方形 */
	// 	{LocomotionComponent2D::PHYSX2D_TYPE::CIRCLE,"CIRCLE"},                      /**< 圆形 */
	// 	{LocomotionComponent2D::PHYSX2D_TYPE::CONCAVEPOLYGON,"CONCAVEPOLYGON"},                      /**< 多边形*/
	// 	});


	SceneModelObject2D::SceneModelObject2D() : m_nPropSetWaiting(0),
		m_bFlippedX(false), m_bFlippedY(false)
	{
		m_pAssetTexture = SANDBOX_NEW(AssetObject, this);
		m_cq = Rainbow::ColorQuad(255, 255, 255);
		//m_pLocomotion = nullptr;
		m_bEnterScene = false;
		m_srcSize = Rainbow::Vector2i(10, 10);
	}
	SceneModelObject2D::~SceneModelObject2D()
	{
		SANDBOX_DELETE(m_pAssetTexture);
		//SANDBOX_DELETE(m_pLocomotion);
		//m_pTransform->release();
	}
	

	void SceneModelObject2D::OnHandleInit()
	{
		m_pTransform = cocos2d::Sprite::create();
		m_pTransform->retain();
		m_pTransform->setCameraMask(1 << 1, false);
		m_pTransform->setContentSize(cocos2d::Size(m_vSize.x, m_vSize.y));

		createLocomotion();
	}

	void SceneModelObject2D::createLocomotion()
	{
//		assert(!m_pLocomotion && "locomotion不能 重复创建");
// 		m_pLocomotion = CreateComponent<LocomotionComponent2D>("LocomotionComponent2D");
// #ifdef NODEACTIVE_BY_TRIGGER
// 		m_pLocomotion->SetEnableGravity(false);
// #endif
	}

	void SceneModelObject2D::SetMainTextureId(const std::string& strTexId)
	{
		std::string now;
		GetMainTextureId(now);
		if (now != strTexId)
		{
			m_strTexId = strTexId;
			SetPropSetWaiting(PropFlag2D::PropMesh2D, true);
			SetPropSetWaiting(PropFlag2D::PropLocalSize2D, true);
			m_pAssetTexture->SetAssetId(strTexId);
			auto cb = [this](AssetObject* loader, bool success) -> void { // m_assetTex 存在，this 就必定存在
				OPTICK_EVENT("SceneModelObject2D::SetMainTextureId load asset callback");
				if (!success)
				{
					return;
				}
				AssetObject& ao = *loader;
				const auto& aii = ao.GetAssetIdInfo();
				switch (aii.m_fileType)
				{
				case AssetFileType::OMOD:
				case AssetFileType::OBJ:
				case AssetFileType::FCM:
				case AssetFileType::CM:
				case AssetFileType::BLUEPRINT:
				case AssetFileType::CGS:
					return;
				}
				auto assetins = ao.GetAssetInstance().ToCast<AssetInstanceRainbowObject>();
				if (!assetins || !assetins->CheckTypeT<Rainbow::Texture2D>())
					return;

				m_tex2d = assetins->GetDataT<Rainbow::Texture2D>();
				((cocos2d::Sprite*)m_pTransform)->setTexture(m_tex2d.CastTo<Rainbow::Texture>());
				if (GetPropSetWaiting(PropFlag2D::PropMesh2D))
				{
					cocos2d::Rect rect = cocos2d::Rect(0, 0, m_tex2d->GetOrginWidth(), m_tex2d->GetOrginHeight());
					((cocos2d::Sprite*)m_pTransform)->setTextureRect(rect, false, rect.size);
					//((cocos2d::Sprite*)m_pTransform)->setContentSize(cocos2d::Size(m_tex2d->GetOrginWidth(), m_tex2d->GetOrginHeight()));
					if (GetPropSetWaiting(PropFlag2D::PropLocalSize2D))
					SetSize(Rainbow::Vector2f(m_tex2d->GetOrginWidth(), m_tex2d->GetOrginHeight()));
				}
				else
				{
					cocos2d::Rect rect = cocos2d::Rect(0, 0, m_tex2d->GetOrginWidth(), m_tex2d->GetOrginHeight());
					((cocos2d::Sprite*)m_pTransform)->setTextureRect(rect, false, rect.size);
					((cocos2d::Sprite*)m_pTransform)->setContentSize(cocos2d::Size(m_vSize.x, m_vSize.y));
				}
				m_pTransform->setOpacity(m_cq.a);
				m_pTransform->setColor(cocos2d::Color3B(m_cq.r, m_cq.g, m_cq.b));
				//m_pTransform->setAnchorPoint(m_vCenter);
				m_nPropSetWaiting = 0;
				if (!MNSandbox::Config::GetSingleton().IsPlayMode())
				{
					std::vector<int> v;
					v.push_back(m_tex2d->GetDataWidth());
					v.push_back(m_tex2d->GetDataHeight());
					SetSrcSize(v);
					//m_pLocomotion->DirtyTexture();
					//if (m_pLocomotion && m_bEnterScene)
					//{
					//	m_pLocomotion->ResetPhysicsActor();
					//}
				}
					
			};
			m_tex2d = nullptr;
			m_pAssetTexture->Load(AssetResType::TEXTURE, cb);
		}
		OnAttributeChanged(this, &RCP_MainTextureId);
	}

	void SceneModelObject2D::SetColor(const Rainbow::ColorQuad& cq)
	{
		m_pTransform->setOpacity(cq.a);
		m_pTransform->setColor(cocos2d::Color3B(cq.r, cq.g, cq.b));
	}

	bool SceneModelObject2D::GetMainTextureId(std::string& strTexId) const
	{
		strTexId = m_strTexId;
		return true;
	}

	Rainbow::Vector2f SceneModelObject2D::GetSize() const
	{
		return m_vSize;
	}
	void SceneModelObject2D::SetSize(const Rainbow::Vector2f& size)
	{
		m_vSize = size;
		m_pTransform->setContentSize(cocos2d::Size(size.x, size.y));
		this->m_DirtyFlag.size = true;
		this->OnDirty();
		SetPropSetWaiting(PropFlag2D::PropLocalSize2D, false);
		OnAttributeChanged(this, &RCP_Size);
	}
	/*
	Rainbow::Vector2f SceneModelObject2D::GetCenter() const
	{
		return m_vCenter;
	}*/

	void SceneModelObject2D::SetCenter(const Rainbow::Vector2f& center)
	{
		m_vCenter = center;
		m_pTransform->setAnchorPoint(center);
		this->m_DirtyFlag.center = true;
		this->OnDirty();
		OnAttributeChanged(this, &RCP_Center);
	}

	void SceneModelObject2D::OnLoadReflexEnd()
	{
		SetPropSetWaiting(PropFlag2D::PropMesh2D, false);
		//if (m_pLocomotion && m_bEnterScene)
		//{
		//	m_pLocomotion->ResetPhysicsActor();
		//}
	}

	void SceneModelObject2D::SetPropSetWaiting(PropFlag2D prop, bool set)
	{
		if (set)
		{
			m_nPropSetWaiting |= (1 << (unsigned char)prop);
		}
		else
		{
			m_nPropSetWaiting &= (~(1 << (unsigned char)prop));
		}
	}

	void SceneModelObject2D::OnEnterScene(Scene* scene)
	{
		SceneTransObject2D::OnEnterScene(scene);
		m_bEnterScene = true;
		// if (m_pLocomotion)
		// {
		// 	m_pLocomotion->OnEnterScene(scene);
		// }
	}
	void SceneModelObject2D::OnLeaveScene(Scene* scene)
	{
		SceneTransObject2D::OnLeaveScene(scene);
		m_bEnterScene = false;
		// if (m_pLocomotion)
		// {
		// 	m_pLocomotion->OnLeaveScene(scene);
		// }
	}


	void SceneModelObject2D::onPhysXTouch2D(SceneModelObject2D* obj, const Rainbow::Vector2f& pos, const Rainbow::Vector2f& normal)
	{
		//todo;
		// if (!m_pLocomotion) return;
		// m_pLocomotion->m_NotifyTouch.Emit(obj, pos, normal);
	}
	void SceneModelObject2D::onPhysXTouchEnd2D(SceneModelObject2D* obj, const Rainbow::Vector2f& pos, const Rainbow::Vector2f& noraml)
	{
		//todo
		// if (!m_pLocomotion) return;
		// m_pLocomotion->m_NotifyTouchEnd.Emit(obj);
	}

	void SceneModelObject2D::SetWorldPositionPhysx(const Rainbow::Vector2f& pos)
	{
		SetWorldPosition_(pos,true);
	}
	void SceneModelObject2D::SetWorldRotationPhysx(const float& angle)
	{
		SetWorldRotation(angle, true);
	}


	void SceneModelObject2D::SetEnableGravity(const bool& b)
	{
		//if (!m_pLocomotion || m_pLocomotion->GetEnableGravity() == b) return;
		//m_pLocomotion->SetEnableGravity(b);
		OnAttributeChanged(this, &RCP_EnableGravity);
	}
	bool SceneModelObject2D::GetEnableGravity(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetEnableGravity();
		return true;
	}

	void SceneModelObject2D::SetAnchored(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetAnchored() == b) return;
		// m_pLocomotion->SetAnchored(b);
		OnAttributeChanged(this, &RCP_Anchor);
	}
	bool SceneModelObject2D::GetAnchored(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetAnchored();
		return true;
	}

	void SceneModelObject2D::SetIsStatic(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetIsStatic() == b) return;
		// m_pLocomotion->SetIsStatic(b);
	}
	bool SceneModelObject2D::GetIsStatic(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetIsStatic();
		return true;
	}

	void SceneModelObject2D::SetGravity(const float& f)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetGravity() == f) return;
		// m_pLocomotion->SetGravity(f);
		OnAttributeChanged(this, &RCP_Gravity);
	}
	bool SceneModelObject2D::GetGravity(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetGravity();
		return true;
	}


	bool SceneModelObject2D::GetMass(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetMass();
		return true;
	}


	void SceneModelObject2D::SetDensity(const float& f)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetDensity() == f) return;
		// m_pLocomotion->SetDensity(f);
		OnAttributeChanged(this, &RCP_Density);
		OnAttributeChanged(this, &RCP_Mass);
	}
	bool SceneModelObject2D::GetDensity(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetDensity();
		return true;
	}

	void SceneModelObject2D::SetRestitution(const float& f)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetRestitution() == f) return;
		// m_pLocomotion->SetRestitution(f);
		OnAttributeChanged(this, &RCP_Restitution);
	}
	bool SceneModelObject2D::GetRestitution(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetRestitution();
		return true;
	}

	void SceneModelObject2D::SetFriction(const float& f)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetFriction() == f) return;
		// m_pLocomotion->SetFriction(f);
		OnAttributeChanged(this, &RCP_Friction);
	}
	bool SceneModelObject2D::GetFriction(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetFriction();
		return true;
	}

	void SceneModelObject2D::SetVelocity(const Rainbow::Vector2f& v)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetVelocity() == v) return;
		// m_pLocomotion->SetVelocity(v);
		OnAttributeChanged(this, &RCP_Velocity);
	}
	bool  SceneModelObject2D::GetVelocity(Rainbow::Vector2f& v) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// v = m_pLocomotion->GetVelocity();
		return true;
	}

	void SceneModelObject2D::SetAngleVelocity(const float& f)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetAngleVelocity() == f) return;
		// m_pLocomotion->SetAngleVelocity(f);
		OnAttributeChanged(this, &RCP_AngleVelocity);
	}
	bool SceneModelObject2D::GetAngleVelocity(float& f) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// f = m_pLocomotion->GetAngleVelocity();
		return true;
	}

	void SceneModelObject2D::SetCanCollide(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetCanCollide() == b) return;
		// m_pLocomotion->SetCanCollide(b);
		OnAttributeChanged(this, &RCP_CanCollide);
	}
	bool SceneModelObject2D::GetCanCollide(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetCanCollide();
		return true;
	}

	void SceneModelObject2D::SetCanTouch(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetCantouch() == b) return;
		// m_pLocomotion->SetCanTouch(b);
		OnAttributeChanged(this, &RCP_CanTouch);
	}
	bool SceneModelObject2D::GetCanTouch(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetCantouch();
		return true;
	}

	void SceneModelObject2D::SetGroupID(const unsigned int& id)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetGroupID() == id) return;
		// m_pLocomotion->SetGroupID(id);
		OnAttributeChanged(this, &RCP_GroupID);
	}
	bool SceneModelObject2D::GetGroupID(unsigned int& id) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// id = m_pLocomotion->GetGroupID();
		return true;
	}

	void SceneModelObject2D::SetEnable(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetEnable() == b) return;
		// m_pLocomotion->SetEnable(b);
		OnAttributeChanged(this, &RCP_Enable);
	}
	bool SceneModelObject2D::GetEnable(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetEnable();
		return true;
	}

	void SceneModelObject2D::SetEnableCCD(const  bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetEnableCCD() == b) return;
		// m_pLocomotion->SetEnableCCD(b);
		OnAttributeChanged(this, &RCP_EnableCCD);
	}
	bool SceneModelObject2D::GetEnableCCD(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetEnableCCD();
		return true;
	}

	void SceneModelObject2D::SetEnableDrawCollider(const bool& b)
	{
		// if (!m_pLocomotion || m_pLocomotion->GetEnableDrawCollider() == b) return;
		// m_pLocomotion->SetEnableDrawCollider(b);
	}
	bool SceneModelObject2D::GetEnableDrawCollider(bool& b) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// b = m_pLocomotion->GetEnableDrawCollider();
		return true;
	}
	void SceneModelObject2D::SetPhysxVerts(const std::vector<std::string>& v)
	{
		if (!v.empty())
		{
			const char magicString[8] = "@##@!22";
			struct Head
			{
				const char magicString[8] = "@##@!22";
				unsigned int nameSize = 0;
				unsigned int size = 0;
			};
			unsigned char* data = nullptr;
			std::vector<std::vector<Rainbow::Vector2f>> verts;
			verts.resize(v.size());
			for (int i = 0; i < v.size(); i++)
			{
				Head head;
				memcpy(&(head), (void*)(v[i].data()), sizeof(Head));
				if (strcmp(head.magicString, magicString) != 0) {
					assert(false);
					return;
				}
				data = (unsigned char*)malloc(head.nameSize + head.size);
				memcpy(data, v[i].data() + sizeof(Head), head.nameSize + head.size);
				std::string name;
				name.assign((const char*)data, head.nameSize);
				verts[i].resize(head.size / sizeof(Rainbow::Vector2f));
				memcpy(verts[i].data(), data + head.nameSize, head.size);
				free(data);
				data = nullptr;
			}
			//m_pLocomotion->SetPhysxVerts(verts);
		}
	}
	bool SceneModelObject2D::GetPhysxVerts(std::vector<std::string>& verts) const
	{
		// if (!m_pLocomotion)
		// 	return false;
		// auto src = m_pLocomotion->GetPhysxVerts();
		//verts.resize(src.size());
		//struct Head
		//{
		//	const  char magicString[8] = "@##@!22";
		//	unsigned int nameSize = 0;
		//	unsigned int size = 0;
		//};
		//unsigned char* data;
		//for (int i = 0; i < verts.size(); i++)
		//{ 
		//	Head head;
		//	head.nameSize = m_strTexId.length();
		//	head.size = src[i].size() * sizeof(Rainbow::Vector2f);
		//	if (head.nameSize + head.size == 0) return true;
		//	data = (unsigned char*)malloc((unsigned long long)head.nameSize + head.size + sizeof(Head));
		//	if (data == nullptr)
		//	{
		//		assert(false);
		//		return true;
		//	}
		//	memset(data , 0, (unsigned long long)head.nameSize + head.size + sizeof(Head));
		//	unsigned char* data_ = data;
		//	memcpy(data_, &head, sizeof(Head));
		//	data_ += sizeof(Head);
		//	memcpy(data_, m_strTexId.data(), head.nameSize);
		//	data_ += head.nameSize;
		//	memcpy(data_, (void*)src[i].data(), head.size);
		//	verts[i].assign((const char* const)data, (std::size_t)(sizeof(Head) + head.nameSize + head.size));
		//	free(data);
		//	data = nullptr;
		//}
		return true;
	}

	void SceneModelObject2D::OnSetLocalRotation(const float& rot)
	{
		this->DirtyRot();
	}
	void SceneModelObject2D::OnSetLocalPosition(const Rainbow::Vector2f& pos)
	{
		this->DirtyPos();
	}
	void SceneModelObject2D::OnSetLocalScale(const Rainbow::Vector2f& scale)
	{
		this->DirtyScale();
	}

	void SceneModelObject2D::OnSetWorldPosition(const Rainbow::Vector2f& pos)
	{
		this->DirtyPos();
	}
	void SceneModelObject2D::OnSetWorldRotation(const float& rot)
	{
		this->DirtyRot();
	}
	void SceneModelObject2D::OnSetWorldScale(const Rainbow::Vector2f& scale)
	{
		this->DirtyScale();
	}
	void SceneModelObject2D::OnSetParent(SandboxNode* parent)
	{
		this->DirtyParent();
	}

	// void SceneModelObject2D::SetPhysx2DType(const LocomotionComponent2D::PHYSX2D_TYPE& tp)
	// {
	// 	if (!m_pLocomotion || m_pLocomotion->GetType() == tp) return;
	// 	m_pLocomotion->SetType(tp);
	// }
	// bool SceneModelObject2D::GetPhysx2DType(LocomotionComponent2D::PHYSX2D_TYPE& tp) const
	// {
	// 	if (!m_pLocomotion)
	// 	{
	// 		return false;
	// 	}
	// 	tp = m_pLocomotion->GetType();
	// 	return true;
	// }

	bool SceneModelObject2D::IsPhysicsSyncAtrr2D(ReflexValue* descriptor)
	{
		return descriptor == &SceneTransObject2D::LocalPosition
			|| descriptor == &SceneTransObject2D::LocalScale
			|| descriptor == &SceneTransObject2D::LocalRotation
			|| descriptor == &SceneModelObject2D::RCP_Velocity
			|| descriptor == &SceneModelObject2D::RCP_AngleVelocity;
	}

	void SceneModelObject2D::OnUpdateTransform()
	{
		// if (!m_pLocomotion) return;
		// if (m_DirtyFlag.pos)
		// {
		// 	m_pLocomotion->SetPosition(GetWorldPosition());
		// 	m_DirtyFlag.pos = false;
		// }

		// if (m_DirtyFlag.rot)
		// {
		// 	m_pLocomotion->SetAngle(GetWorldRotation());
		// 	m_DirtyFlag.rot = false;
		// }
		// if (m_DirtyFlag.scale || m_DirtyFlag.center || m_DirtyFlag.size)
		// {
		// 	m_pLocomotion->UpdateSize();
		// 	m_DirtyFlag.scale = false;
		// 	m_DirtyFlag.center = false;
		// 	m_DirtyFlag.size = false;
		// }

	}

	bool SceneModelObject2D::IsEnterScene() const
	{
		return m_bEnterScene;
	}

	void SceneModelObject2D::SetSrcSize(const std::vector<int>& s)
	{
		if (s.size() < 2) return;
		int& x = m_srcSize.x();
		x = s[0];
		int& y = m_srcSize.y();
		y = s[1];
		OnAttributeChanged(this, &RCP_SrcSize);
	}
	bool SceneModelObject2D::GetSrcSize(std::vector<int>& s) const
	{
		s.resize(2);
		s[0] = m_srcSize.x();
		s[1] = m_srcSize.y();
		return true;
	}

	void SceneModelObject2D::SetFlippedX(const  bool& b)
	{
		if (b != m_bFlippedX)
		{
			m_bFlippedX = b;
			((cocos2d::Sprite*)m_pTransform)->setFlippedX(b);
			OnAttributeChanged(this, &RCP_FlippedX);
		}
	}

	bool SceneModelObject2D::GetFlippedX() const
	{
		return m_bFlippedX;
	}

	void SceneModelObject2D::SetFlippedY(const  bool& b)
	{
		if (b != m_bFlippedY)
		{
			m_bFlippedY = b;
			((cocos2d::Sprite*)m_pTransform)->setFlippedY(b);
			OnAttributeChanged(this, &RCP_FlippedY);
		}
	}

	bool SceneModelObject2D::GetFlippedY() const
	{
		return m_bFlippedY;
	}


	Notify<AutoRef<SandboxNode>, Rainbow::Vector2f, Rainbow::Vector2f>* SceneModelObject2D::GetTouched()
	{
		//return m_pLocomotion ? &m_pLocomotion->m_NotifyTouch : nullptr;
		return nullptr;
	}

	Notify<AutoRef<SandboxNode>>* SceneModelObject2D::GetTouchEnded()
	{
		//return m_pLocomotion ? &m_pLocomotion->m_NotifyTouchEnd : nullptr;
		return nullptr;
	}

}