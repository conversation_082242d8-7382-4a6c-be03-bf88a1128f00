#ifndef __GeometryMath_h__
#define __GeometryMath_h__ 1

#include "geometrySolid/GeoSolidEnum.h"

#define COMPOSITE_GEO_SOLID_DEBUG 0

#define COMPOSITE_ELEMENT_USE_POINTER 1

#define COMPOSITE_STD_USE_POINTER !COMPOSITE_ELEMENT_USE_POINTER

#if defined(IWORLD_SERVER_BUILD) || defined(__ANDROID__) || defined(__OHOS__) || defined(__APPLE__)
#define GEO_SOLID_INLINE
#else
#define GEO_SOLID_INLINE inline
#endif

#if COMPOSITE_USE_DOUBLE
#include "geometrySolid/Vector2db.h"
#include "geometrySolid/Vector3db.h"
#include "geometrySolid/Matrix3x3db.h"
#include "geometrySolid/Quaterniondb.h"
#include "geometrySolid/BoxBounddb.h"
#include "geometrySolid/Planedb.h"
#include "geometrySolid/Raydb.h"
#else
#include "Math/Vector2f.h"
#include "Math/Vector3f.h"
#include "Math/Matrix3x3f.h"
#include "Math/Quaternionf.h"
#include "Math/LegacyBounding.h"
#include "Geometry/Plane.h"
#include "Geometry/Ray.h"
#endif

namespace MNSandbox {
	namespace GeometrySolid {

		template<class T>
		using GeoSolidArray = std::vector<T>;
		//#if COMPOSITE_GEO_SOLID_DEBUG
		//template<class T>
		//using GeoSolidArray = std::vector<T>;
		//#else
		//template<class T>
		//using GeoSolidArray = dynamic_array<T>;
		//#endif

		template<class T>
		using GeoSolidSet = std::unordered_set<T>;

		#if COMPOSITE_USE_DOUBLE
		using GsVector2 = Vector2db;
		using GsVector3 = Vector3db;
		using GsQuaternion = Quaterniondb;
		using GsMatrix3x3 = Matrix3x3db;
		using GsBoxBound = BoxBounddb;
		using GsPlane = Planedb;
		using GsRay = Raydb;
		#else
		using GsVector2 = Rainbow::Vector2f;
		using GsVector3 = Rainbow::Vector3f;
		using GsQuaternion = Rainbow::Quaternionf;
		using GsMatrix3x3 = Rainbow::Matrix3x3f;
		using GsBoxBound = Rainbow::BoxBound;
		using GsPlane = Rainbow::Planef;
		using GsRay = Rainbow::Ray;
		#endif

		using GsIndex = unsigned int;
		//using GsIndex8 = unsigned char;

		//让编译器检测三种下标是否有混用的情况
		//缺点：增加开发量
		//不推荐，无法使用负数
		//#define GS_DECLARE_INDEX(name) struct name##__ { char unused; }; typedef struct name##__ *name
		//GS_DECLARE_INDEX(GsIv);
		//GS_DECLARE_INDEX(GsIe);
		//GS_DECLARE_INDEX(GsIt);
		using GsIv = GsIndex;
		using GsIe = GsIndex;
		using GsIt = GsIndex;

		#if COMPOSITE_USE_DOUBLE
		const static GsDigit epZero = 1e-6;
		const static GsDigit epDotProduct = 1e-6;
		const static GsDigit epIdentityNormal = 3e-5;
		const static GsDigit epIdentityDotProductLength = 2e-5;
		#else
		const static GsDigit epZero = 1e-6f;
		const static GsDigit epDotProduct = 1e-3f;
		const static GsDigit epIdentityNormal = 3e-5f;
		const static GsDigit epIdentityDotProductLength = 2e-5f;
		#endif

		namespace GeometryMath
		{
			/*====================轻型判断====================*/
			bool IsZero(const GsDigit& gd, const GsDigit& epsilon = epZero);
			#if COMPOSITE_USE_DOUBLE
			bool IsVertexEqual(const GsVector3& va, const GsVector3& vb, const GsDigit& epsilon);
			bool IsVertexEqual(const GsVector3& va, const GsVector3& vb);
			#else
			bool IsVertexEqual(const GsVector3& va, const GsVector3& vb, const GsDigit& epsilon = epZero);
			#endif
			bool IsIdentityEqual(const GsVector3& via, const GsVector3& vib, const GsDigit& epsilon = epZero);
			//GsDigit ScalarProduct(const GsVector3& v1, const GsVector3& v2);
			//GsVector3 VectorProduct(const GsVector3& v1, const GsVector3& v2);


			/*====================常用空间关系判断====================*/
			bool IsVertexInSegment(const GsVector3& vp, const GsVector3& va, const GsVector3& vb, const GsDigit& ep = epZero);
			bool IsParalleled(const GsVector3& va, const GsVector3& vb, const GsVector3& vc, const GsDigit& ep = epZero);
			/**
			@brief	同向法确定点P是否落到△ABC内部或边上
			 */
			bool CheckVertexInsideTriangle(const GsVector3& vp, const GsVector3& va, const GsVector3& vb, const GsVector3& vc);
			/**
			@brief	不包括顶点重合与在边上
			 */
			bool IsVertexInsideTriangle(const GsVector3& vp, const GsVector3& va, const GsVector3& vb, const GsVector3& vc, bool* pbInEdge = nullptr);
			/**
			@brief	包括顶点重合与在边上
				*/
			bool IsVertexOnTriangle(const GsVector3& vp, const GsVector3& va, const GsVector3& vb, const GsVector3& vc);
			GsVector3 CastVertexOnLine(const GsVector3& vp, const GsVector3& va, const GsVector3& vb);
			/**
			@brief	左手坐标系下，三角形顶点顺时针是否与vn相同
			 */
			bool IsTriangleClockwise(const GsVector3& va, const GsVector3& vb, const GsVector3& vc, const GsVector3& vn);
			/**
			@brief	判断b点是否为凸点。包括并线情况。
			 */
			bool IsConvex(const GsVector3& va, const GsVector3& vb, const GsVector3& vc, const GsVector3& vn);

			bool IsVertexInBoxBound(const GsVector3& v, const GsBoxBound& bb);

			GsDigit Cosine(const GsVector3& va, const GsVector3& vb);

			/**
			@brief 获取角B的弧度
			*/
			GsDigit GetRadian(const GsVector3& va, const GsVector3& vb, const GsVector3& vc);

			GsDigit GetArea(const GsVector3& va, const GsVector3& vb, const GsVector3& vc);

			/**
			@brief 带符号的平行六面体体积
			*/
			GsDigit GetSignedVolumeOfParallelepiped(const GsVector3& va, const GsVector3& vb, const GsVector3& vc, const GsVector3& vd);
			/**
			@brief 判断空间线段是否穿过三角形，不判断共面情况
			*/
			bool WhetherLineCrossTriangle(const GsVector3& vla, const GsVector3& vlb, const GsVector3& vta, const GsVector3& vtb, const GsVector3& vtc);
			/**
			@brief 线段AB与线段CD是否相交，不包括共线情况
			*/
			bool WhetherLineIntersectLine(const GsVector3& va, const GsVector3& vb, const GsVector3& vc, const GsVector3& vd, GsVector3* vIntersect = nullptr);


			/*====================复杂空间关系判断====================*/
			bool IntersectRayPlane(const GsRay& ray, const GsPlane& plane, GsDigit* enter);
			/**
			@deprecated
			@brief https://blog.csdn.net/csxiaoshui/article/details/73614352
			 */
			bool IntersectRayLineSegment(const GsRay& ray,
				const GsVector3& va, const GsVector3& vb, GsDigit& time);
			/**
			@brief 参考：https://blog.csdn.net/qq_45735851/article/details/114434281
			 */
			bool IntersectRayLineSegment2(const GsRay& ray,
				const GsVector3& va, const GsVector3& vb, GsDigit& time);
			/**
			@brief	参考：IntersectRayTriangle(Intersection.cpp)
			 */
			bool IntersectRayTriangle(const GsRay& line,
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc, GsDigit* outT);
			/**
			@brief	参考：IntersectRayTriangle(Intersection.cpp)
					直线版本。对误差进行了调整。
			 */
			bool IntersectLineTriangle(const GsRay& line,
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc, GsDigit* outT);
			/**
			@deprecated
			 */
			bool IntersectRayTriangleEdges(const GsRay& ray,
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc, GsDigit& time);
			bool IntersectRayTriangleEdges2(const GsRay& ray,
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc, GsDigit& time);

			/**
			@brief	用DEF的射线与ABC进行相交
			 */
			int TriangleIntersectedByTriangle(
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc,
				const GsVector3& vd, const GsVector3& ve, const GsVector3& vf,
				GsVector3* pvi0, GsVector3* pvi1, bool* pCoplanar
			);
			bool IntersectTriangleTriangle(
				const GsVector3& va, const GsVector3& vb, const GsVector3& vc,
				const GsVector3& vd, const GsVector3& ve, const GsVector3& vf,
				GsVector3* pvi0, GsVector3* pvi1, bool* pCoplanar
			);

		};
	}
}

#endif//__GeometryMath_h__