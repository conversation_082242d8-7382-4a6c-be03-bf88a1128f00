/**
* file : SandboxCoroutineRewrite
* func : 沙盒lua脚本协程使用重写
* by : zhangyusong
*/
#include "SandboxCoroutineRewrite.h"
#include "SandboxLuaFunction.h"
#include "SandboxLuaScriptState.h"
#include "script/SandboxLuaCoroutineJob.h"
#include "script/SandboxLuaCoroutinePool.h"
#include "script/linker/SandboxLuaLinkerData.h"
#include "script/linker/SandboxLuaLinkerManager.h"

namespace MNSandbox {

	CoroutineRewrite::CoroutineRewrite()
		: m_listenRunFinish(this, &CoroutineRewrite::OnCoroutineFinish), m_isYield(false), m_WorkMark(false)
	{
		
	}

	CoroutineRewrite::~CoroutineRewrite()
	{
		SANDBOX_RELEASE(m_luaCo);
		SANDBOX_RELEASE(m_linkerData);
	}

	void CoroutineRewrite::Create(lua_State* L)
	{
		m_linkerData = SANDBOX_NEW(Lua::LinkerData);
		m_linkerData->BindDatas(L, 1, 1);
	}

	int CoroutineRewrite::Resume(lua_State* L)
	{
		m_luaMain = GetSandboxScriptVM().GetLuaStateRef(L).get();
		if (!m_luaMain)
		{
			SANDBOX_ASSERT(false && "lua_state not same");
			return 0;
		}

		if (!m_luaCo)
		{
			AutoRef<Lua::CoJobData_LuaFunction> cojob = SANDBOX_NEW(Lua::CoJobData_LuaFunction);
			cojob->SetFunction(m_linkerData);
			cojob->AddParams(L, 1, lua_gettop(L));
			// 需要把使用的参数清理，不然会多出返回值
			lua_settop(L, 0);
			m_luaCo = GetSandboxScriptVM().CreateCoroutine(cojob, nullptr);

			m_luaCo->m_notifyReturn.Subscribe(m_listenRunFinish);
			m_luaCo->BindCoroutineRewrite(this);
			m_luaCo->Play();
		}
		else
		{
			if (!m_luaCo->IsValid())
			{
				// 协程已经结束了，但还是调用了resume
				lua_pushboolean(L, 0);
				lua_pushliteral(L, "cannot resume dead coroutine");
				return 2;
			}
			else
			{
				if (m_isYield)
				{
					m_isYield = false;
					lua_State* coL = m_luaCo->GetLuaState();
					if (!coL)
					{
						lua_pushboolean(L, 0);
						lua_pushliteral(L, "cannot resume dead coroutine");
						return 2;
					}
					// 需要将L里的参数转移到co协程里
					int top = lua_gettop(L);
					lua_xmove(L, coL, top);
					m_luaCo->Resume(top);
				}
				else
				{
					SANDBOX_LOG("CoroutineRewrite::Resume call is not a state = yield");
					lua_pushboolean(L, 0);
					lua_pushliteral(L, "cannot resume not a yield coroutine");
					return 2;
				}
			}
		}

		// 返回结果数量来自 OnCoroutineFinish
		if (!m_isYield && m_luaCo->IsPause())
			return m_luaMain->Pause();
		else
			return lua_gettop(L);
	}

	int CoroutineRewrite::Yields(lua_State* L)
	{
		if (!m_luaMain || !m_luaMain->IsValid())
		{
			SANDBOX_ASSERT(false && "main rewrite yield failed");
			return 0;
		}
		if (!m_luaCo || !m_luaCo->IsValid())
		{
			SANDBOX_ASSERT(false && "co rewrite yield failed");
			return 0;
		}
		if (m_WorkMark)
		{
			ScriptState::LuaThrowError(L, "co yield is not allowed to be executed within the work function");
			return 0;
		}
		if (m_isYield)
		{
			SANDBOX_ASSERT(false && "co rewrite was yielded");
			ScriptState::LuaThrowError(L, "co was yielded");
			return 0;
		}
		m_isYield = true;
		int len = lua_gettop(L);
		auto mainL = m_luaMain->GetLuaState();
		lua_pushboolean(mainL, 1);
		lua_xmove(L, mainL, len);

		int ret = m_luaCo->Pause(len + 1);
		if (m_luaMain->IsPause())
			m_luaMain->Resume(len + 1);
		return ret;
	}

	int CoroutineRewrite::Status(lua_State* L)
	{
		if (m_luaCo)
		{
			if (!m_luaCo->IsValid())
			{
				lua_pushliteral(L, "dead");
			}
			else
			{
				lua_State* co = m_luaCo->GetLuaState();
				luaL_argcheck(L, co, 1, "coroutine expected");
				if (L == co)
					lua_pushliteral(L, "running");
				else
				{
					switch (lua_status(co))
					{
					case LUA_YIELD:
						lua_pushliteral(L, "suspended");
						break;
					case 0: {
						lua_Debug ar;
						if (lua_getstack(co, 0, &ar) > 0)  /* does it have frames? */
							lua_pushliteral(L, "normal");  /* it is running */
						else if (lua_gettop(co) == 0)
							lua_pushliteral(L, "dead");
						else
							lua_pushliteral(L, "suspended");  /* initial state */
						break;
					}
					default:  /* some error occured */
						lua_pushliteral(L, "dead");
						break;
					}
				}
			}
			/*Lua::JOBSTATE state = m_luaCo->GetRunningState();
			switch (state)
			{
			case Lua::JOBSTATE::IDLE:
				lua_pushstring(L, "suspended");
				break;
			case Lua::JOBSTATE::RUNNING:
				lua_pushstring(L, "running");
				break;
			case Lua::JOBSTATE::PAUSE:
				lua_pushstring(L, "suspended");
				break;
			case Lua::JOBSTATE::SUCCESS:
				lua_pushstring(L, "dead");
				break;
			case Lua::JOBSTATE::FAILED:
				lua_pushstring(L, "dead");
				break;
			default:
				lua_pushstring(L, "normal");
				break;
			}*/
		}
		else
		{
			lua_pushliteral(L, "suspended");
		}
		return 1;
	}

	int CoroutineRewrite::Running(lua_State* L)
	{
		lua_pushlightuserdata(L, (void*)this);
		return 1;
	}

	void CoroutineRewrite::Work(lua_State* L)
	{
		m_WorkMark = true;
		m_linkerData = SANDBOX_NEW(Lua::LinkerData);
		m_linkerData->BindDatas(L, 1, 1);

		AutoRef<Lua::CoJobData_LuaFunction> cojob = SANDBOX_NEW(Lua::CoJobData_LuaFunction);
		cojob->SetFunction(m_linkerData);
		cojob->AddParams(L, 2, lua_gettop(L));
		// 需要把使用的参数清理，不然会多出返回值
		lua_settop(L, 0);
		m_luaCo = GetSandboxScriptVM().CreateCoroutine(cojob, nullptr);
		m_luaCo->BindCoroutineRewrite(this);
		m_luaCo->Play();
	}

	void CoroutineRewrite::OnCoroutineFinish(LuaCoroutine* co, const AutoRef<Lua::LinkerData>& data)
	{
		if (!m_luaMain || !m_luaMain->IsValid()) return;
		auto L = m_luaMain->GetLuaState();
		lua_pushboolean(L, 1);
		// 这里将函数返回结果压入 
		int len = 0;
		if (data && data->GetSize() > 0)
		{
			len = data->GetSize();
			data->GetDatas(L);
		}

		if (m_luaMain->IsPause())
			m_luaMain->Resume(len + 1);
	}

}