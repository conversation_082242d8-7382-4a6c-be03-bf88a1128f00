#pragma once

#include "OgreBlock.h"
#include "section.h"
#include "coreMisc.h"
//#include "BlockMaterial.h"
#include "blocks/BlockMaterialBase.h"
#include "proto_common.h"
#include "proto_define.h"
#include "ChunkSave_generated.h"
#include "BiomeRegionGenType.h"
#include <set>
//#include "GameScene/MovableObject.h"
#include "SandboxEngine.h"
#include "SharedChunkData.h"
//#include "worldMesh/ChunkRenderer.h"
#include "SandboxAutoRef.h"

struct BiomeDef;
struct tagChunkSaveDB;
struct tagChunkSave;
class Ecosystem;
class SectionMesh;
class WorldContainer;
class IClientItem;
class IClientActor;
struct GenTerrResult;

namespace Rainbow {
	class MovableObject;
	class ChunkRenderer;
}
//namespace MNSandbox {
//	class SceneChunk;
//}

extern MNSandbox::SandboxNode *CreateActorFromUnionType(FBSave::SectionActorUnion actortype);
EXPORT_SANDBOXENGINE WorldContainer *CreateWorldContainerFromChunkContainer(const FBSave::ChunkContainer *pChunkContainer);
EXPORT_SANDBOXENGINE extern flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ChunkContainer>>> CreateContainerVec(flatbuffers::FlatBufferBuilder& builder, std::vector<WorldContainer*>& chunkcontainers);
class SearchBlocks //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	unsigned int m_BlockID;
	std::vector<unsigned short> m_PosIndices;
	//tolua_end
}; //tolua_exports
//tolua_begin
struct BossPos
{
	WCoord pos;
	World *pWorld;
};
//tolua_end

class EXPORT_SANDBOXENGINE Chunk //tolua_exports
{ //tolua_exports
public:
	Chunk(World *pworld, int ox, int oz, BLOCK_DATA_TYPE*chunkdata = NULL);

	//bool loadFromBuffer_sb(const void* data, const int len);//沙盒节点数据
	//void getAllActors(std::map<long long,MNSandbox::SandboxNode*>& out) const;
	//void LoadNodes(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& nodes, std::vector<MNSandbox::SandboxNodeID>& parentids);
	//tolua_begin
	virtual ~Chunk();
	virtual size_t getMemStat(MemStat& ms);
	virtual size_t printMemStat(FILE* f);

	bool loadFromBuffer(const char* chunkBuffer, size_t desLen, size_t srcLen, World* pworld, bool isinit = true, int sectionflags = 0xffff, bool loadActors = true);
	bool loadFromBufferWithDeompressed(unsigned char* rawdata, size_t desLen, World* pworld, bool isinit = true, int sectionflags = 0xffff, bool loadActors = true);
	bool savePBBuffer(PB_ChunkSaveDB *pbChunkBuffer, bool onlyblocks, bool isinit, int sectionflags);
	bool savePBBufferUncompress(PB_ChunkSaveDB *pbChunkBuffer, bool onlyblocks, bool isinit, int sectionflags);
	CHUNKSAVEDB* saveDBBuffer(bool isOnlyBlock = false);
	void* GetActorFBS(int& length);
	void LoadActorFBS(void* data, int length);
	
	bool needSave(bool forcesave);
	bool needSaveActor(bool forcesave);

	inline void SetCurForEachCount(int count) {
		m_CurForEachCount = count;
	}

	inline int GetCurForEachCount() {
		return m_CurForEachCount;
	}

	void generateSkylightMap();
	void generateHeightMap();
	void relightBlock(int x, int y, int z);
	bool updateSkylight();

	int setBlockAll(int x, int y, int z, int resid, int data, int flag = 0, int dataEx = 0);

	bool setBlockData(int x, int y, int z, int data);

	bool setBlockDateEx(int x, int y, int z, int dataEx);

	void addActor(IClientActor *actor, int ithSection);
	void removeActor(IClientActor *actor, int ithSection);
	bool hasActors()
	{
		return m_ActorNum > 0;
	}
	void addContainer(WorldContainer *container);
	void removeContainer(WorldContainer *container, bool needdel=true, bool delboard = true);

	void onEnterWorld(World *pworld);
	void onLeaveWorld(bool bReload = false);
	void clearPhysActor();

	void onNeighbourChunkEnterWorld(int dx, int dz, Chunk *neighbourChunk);
	void onNeighbourChunkLeaveWorld(int dx, int dz, Chunk *neighbourChunk);

	int getTopFilledSegment();


	int getBlockLightOpacity(int x, int y, int z)
	{
		return BlockMaterial::getLightOpacity(getBlockID(x, y, z));
	}
	int getBlockID(int x, int y, int z) const
	{
		return getBlock(x, y, z).getResID();
	}
	int getBlockID(const WCoord& pos) const
	{
		return getBlock(pos.x, pos.y, pos.z).getResID();
	}

	IClientItem *findNearestItem(const WCoord &center, int itemid);

	int getPrecipitationHeight(int x, int z);
	void setPrecipitationHeight(int x, int z, int h)
	{
		m_PrecipitationHeight[xz2Index(x,z)] = (short)h;
	}
	unsigned char getTopHeight(int x, int z) //返回最高非空block的高度 + 1
	{
		return m_TopHeight[xz2Index(x,z)];
	}
	void setTopHeight(int x, int z, unsigned char h)
	{
		m_TopHeight[xz2Index(x,z)] = h;
	}

	unsigned char getBiomeID(int x, int z) const
	{
		return GetSharedChunkData().getBiomeID(x, z);
	}

	void setBiomeID(int x, int z, unsigned char biome)
	{
		GetWritableSharedChunkData().setBiomeId(x, z, biome);
	}
	
	Section *getIthSection(int i) const
	{
		assert(i>=0 && i<CHUNK_SECTION_DIM);
		return m_Sections[i];
	}

	Section *getSectionByY(int y) const
	{
		int sect = BlockDivSection(y);
		if(sect<0 || sect>=CHUNK_SECTION_DIM)
		{
			return NULL;
		}
		return getIthSection(sect);
	}

	World *getWorld()
	{
		return m_pWorld;
	}
	const BiomeDef *getBiome(int x, int z) const;
	const BiomeDef *getAirLandBiome(int x, int z) const;
	Ecosystem *getBiomeGen(int x, int z);

	bool isBlockInChunk(const WCoord &grid)
	{
		return BlockDivSection(grid.x)*CHUNK_BLOCK_X==m_Origin.x && BlockDivSection(grid.z)*CHUNK_BLOCK_Z==m_Origin.z;
	}

	Block getBlock(int x, int y, int z) const;
	Block getBlock(const WCoord& grid) const
	{
		return getBlock(grid.x, grid.y, grid.z);
	}

	BlockLight getBlockLight(int x, int y, int z) const
	{
		assert(x >= 0 && x < CHUNK_BLOCK_X);
		assert(y >= 0 && y < CHUNK_BLOCK_Y);
		assert(z >= 0 && z < CHUNK_BLOCK_Z);

		return m_Sections[y >> 4]->getBlockLight(x, y & 15, z);
	}
	BlockLight getBlockLight(const WCoord& grid) const
	{
		return getBlockLight(grid.x, grid.y, grid.z);
	}

	void setBlockLight(int lttype, int x, int y, int z, int value);

	int getBlockTemperature(int x, int y, int z) const
	{
		assert(x >= 0 && x < CHUNK_BLOCK_X);
		assert(y >= 0 && y < CHUNK_BLOCK_Y);
		assert(z >= 0 && z < CHUNK_BLOCK_Z);

		return m_Sections[y >> 4]->getBlockTemperature(x, y, z);
	}

	int getBlockTemperature(const WCoord& grid) const
	{
		return getBlockTemperature(grid.x, grid.y, grid.z);
	}

	void setBlockTemperature(int x, int y, int z,int val)
	{
		assert(x >= 0 && x < CHUNK_BLOCK_X);
		assert(y >= 0 && y < CHUNK_BLOCK_Y);
		assert(z >= 0 && z < CHUNK_BLOCK_Z);

		m_Sections[y >> 4]->setBlockTemperature(x, y, z, val);
	}

	const std::unordered_map<short, char>* getBlockTemperatureMap(int y) const
	{
		assert(y >= 0 && y < CHUNK_BLOCK_Y);
		return m_Sections[y >> 4]->getBlockTemperatureMap();
	}

	Chunk *getNeighbourChunk(int deltax, int deltaz)
	{
		assert(deltax >= -1 && deltax <= 1);
		assert(deltaz >= -1 && deltaz <= 1);
		return m_NeighbourChunks[(deltaz + 1) * 3 + (deltax + 1)];
	}
	void setNeighbourChunk(int deltax, int deltaz, Chunk *pchunk)
	{
		assert(deltax >= -1 && deltax <= 1);
		assert(deltaz >= -1 && deltaz <= 1);
		m_NeighbourChunks[(deltaz + 1) * 3 + (deltax + 1)] = pchunk;
	}
	Chunk *getNeighbourChunkFast(int relativex, int relativez)  //x,z can be 0,1,2
	{
		return m_NeighbourChunks[relativez * 3 + relativex];
	}
	bool isEdgeChunk() const;
	//const
	//{
	//	for (int i = 0; i < 9; ++i)
	//	{
	//		if (m_NeighbourChunks[i] == nullptr)
	//			return true;
	//	}
	//	return false;
	//}

	bool NeighbourChunkHasData();
	void resetRelightChecks();
	void updateRelightChecks();

	int calBlockNum(int blockid);
	bool canGenerateMob()
	{
		return !m_isGapLightingUpdated;
	}

	void markLightDirty(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z);
	void markLightDirtySuspended(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z);
	void markNeighbourLightDirty(int lttype, int rx, int y, int rz);

	void markRemoteLightDirty(UInt8 lttype, UInt8 x, UInt8 y, UInt8 z);

	int calBlockLightValue(int lttype, int rx, int rz, int y, int* outNeibourLights);
	void checkGenMesh() {}
	int genMesh(const WCoord& camerapos);
	static void exportChunksToObj(Chunk **chunks, size_t nchunk, const char *dir, int mode, const WCoord &offset);
	static void exportStudioMapChunksToObj(World* pworld, Chunk **chunks, size_t nchunk, const char *dir, int mode, const WCoord& origin, const int &size);

	void propagateSkylightOcclusion(int x, int z)
	{
		m_updateSkylightColumns[z*CHUNK_BLOCK_X+x] = true;
		m_isGapLightingUpdated = true;
	}
	void setUpdateSkyLight();
	void updateSkylight_do();
	void generateDirtyLights();
	void addSearchBlock(int x, int y, int z, int blockid);
	void removeSearchBlock(int x, int y, int z, int blockid);
	SearchBlocks *getSearchBlocks(int blockid);

	// 设置区块特殊点
	void SetChunkSpecialPos(const std::vector<TerrSpecialPos>& datas, const std::vector<ChunkSpecialBiomeData>& biomes, const std::vector<ChunkSpecialBiomeData>& chunkBiomes);
	WCoord getOrigin(){return m_Origin;}
	//tolua_end

#ifdef BUILD_MINI_EDITOR_APP
	void ResetChunkData(GenTerrResult& ret);
#endif

	const SharedChunkData& GetSharedChunkData() const;
	const SharedChunkData& AcquireSharedChunkData() const;
	SharedChunkData& GetWritableSharedChunkData();
	void waitBuildMeshCompleted();
	bool IsVaild();

	void FreeRenderSection();
	void CreateRenderSection();
	Rainbow::ChunkRenderer* GetChunkRenderer() { return m_ChunkRenderer; }

	void SetHide(bool value);
	bool IsHide();
public:
	//tolua_begin
	bool m_isGapLightingUpdated;
	bool m_updateSkylightColumns[CHUNK_BLOCK_Z*CHUNK_BLOCK_X];
	//tolua_end
private:
	void _init(World *pworld, int section_x, int section_z);
	void checkSkylightNeighborHeight(int x, int z, int h);
	void updateSkylightNeighborHeight(int x, int z, int miny, int maxy);
	SearchBlocks *findSearchBlockIndex(int x, int y, int z, int blockid, int &offset);

public:
	//tolua_begin
	static bool ms_SetBlockCreateContainer;
	static bool ms_RelightBlock;
	ChunkRandGen m_RandGen;
	bool m_Populated;
	bool m_DirtyLightsGenerated;

	WCoord m_Origin;
	int m_MinTopHeight;
	unsigned char m_TopHeight[CHUNK_BLOCK_Z*CHUNK_BLOCK_X];
	short m_PrecipitationHeight[CHUNK_BLOCK_Z*CHUNK_BLOCK_X];
	//tolua_end
	//tolua_begin
	Chunk *m_NeighbourChunks[9];

	unsigned int m_LastSaveTick;
	unsigned int m_LastActorSaveTick;
	int m_ActorNum;
	bool m_ActorDirty;
	std::vector<WorldContainer *> m_ChunkContainers;

	unsigned short m_MapID;
	bool m_Dirty;
	bool m_NeedGenImmediate; //机甲方块移动需要立即生成或者会闪烁
	unsigned int  m_SaveIntegralCounter; //机甲方块保存的时候， 一次受影响的所有区块都需要保存,  0表示不需要

	int m_MeshFlags;
	int m_GenMeshFlags;
	int m_NeedGenWorldTick;
	Rainbow::MovableObject * m_pMesh;

	int m_RelightCheckCount;

	std::vector<int> m_SuppendLightSrc;
	//刷怪房位置
	WCoord m_DungeonPos;
	Block m_EmptyBlock;

	std::set<UInt32> m_suspendDirtyLights;
	std::vector<SearchBlocks *> m_SearchBlocks;
	bool m_IsSquareBlock;
	bool m_bNeedGenMesh;
	bool m_hasNoSaveData; //其作用是在退出地图时判断是否有没保存数据而进行保存
	int  m_CurSaveTick;
	//tolua_end
	int  m_delayResetGenMesh = 0;
	std::vector<WCoord> m_perspectivePos;//审核透视功能，道具位置
	int m_perspectiveResid[28] = { 719,828,883,884,885,965,967,1034,1550,1574,1598,1622,1646,1691,1722,688,892,893,894,895,896,897,815,818,886,888,890,681 };//审核透视功能，道具id
	std::vector<TerrSpecialPos> m_specialPos;
	std::vector<ChunkSpecialBiomeData> m_specialBiome; //这个是附近的specialBiome;
	std::vector<ChunkSpecialBiomeData> m_chunkSpecialBiome; //这个是当前chunk所在的biome, 一般只有一个
private:
	Section *m_Sections[CHUNK_SECTION_DIM];
	World *m_pWorld;
	//Section *m_SectionsRender[16];
	//SectionMergeObject *m_pMergeobj;

	SharedChunkData* m_SharedChunkData;

	Rainbow::ChunkRenderer* m_ChunkRenderer;

	int m_CurForEachCount;
	//MNSandbox::AutoRef<MNSandbox::SceneChunk> m_sceneChunk; //场景区块，用于场景树
}; //tolua_exports

EXPORT_SANDBOXENGINE extern bool g_EnableReLighting;

inline const SharedChunkData& Chunk::GetSharedChunkData() const
{
	return *m_SharedChunkData;
}

inline const SharedChunkData& Chunk::AcquireSharedChunkData() const
{
	m_SharedChunkData->AddRef();
	return *m_SharedChunkData;
}

inline SharedChunkData& Chunk::GetWritableSharedChunkData()
{
	m_SharedChunkData = GetWritableSharedObject(m_SharedChunkData);
	return *m_SharedChunkData;
}
