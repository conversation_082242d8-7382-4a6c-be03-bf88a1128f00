#ifndef __CustomMotionData_h__
#define __CustomMotionData_h__ 1

#include "Math/LegacyTransform.h"

#include <vector>
#include "SandboxEngine.h"

namespace FBSave
{
	struct KeyFrameData;
	struct MotionData;
}

struct EXPORT_SANDBOXENGINE CustomMotionData;
struct CustomMotionData {//tolua_exports
	//tolua_begin
	CustomMotionData();
	~CustomMotionData();
	//tolua_end
	CustomMotionData(const CustomMotionData& cmd);
	CustomMotionData(const CustomMotionData* cmd);
	CustomMotionData& operator= (const CustomMotionData& cmd);
	void clear();
	void add(int tick, const Rainbow::Vector3f& translate, const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3);
	bool setIndex(int i, const int tick, const Rainbow::Vector3f& translate, const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3);
	void removeIndex(int i);
	void insert(int i, int tick, const Rainbow::Vector3f& translate, const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3);
	void resetTicks();
	void copy(const CustomMotionData& cmd);
	int getIndexByTick(int tick);
	bool getTRS(int tick, MINIW::Transform_& trs);
	bool getTRSByIndex(int i, MINIW::Transform_& trs);
	bool hasTicks();
	void addFbs(const FBSave::KeyFrameData* fbsKfd);
	void fromFbs(const FBSave::MotionData* fbsMd);
	void validateTick(int& tick);
	void transformTickPerKf(int tickPerKf);
	void limitScale(Rainbow::Vector3f& scale3);
	void limitScale(float& s);
	//tolua_begin
	int getFrameKeyNum();
	int getTick(int index);
	void getOffsetPos(int index, float &x, float &y, float &z);
	void getRotate(int index, float &pitch, float &yaw, float &roll);
	float getScale(int index);
	//tolua_end
	//tolua_begin
	/**
	@brief	动作序列ID
	 */
	int id;
	/**
	@brief	时长
	 */
	short time;
	//tolua_end
	int tickPerKf = 100;
	/**
	@brief	关键帧数据
	 */
	std::vector<unsigned int> ticks;
	std::vector<Rainbow::Vector3f> posoffsets;
	std::vector<Rainbow::Quaternionf> quats;
	/**
	@brief	旧版使用一维开发，现扩展到三维缩放。
	 */
	std::vector<Rainbow::Vector3f> scale3s;
	std::vector<float> scales;
};//tolua_exports
#endif//__CustomMotionData_h__