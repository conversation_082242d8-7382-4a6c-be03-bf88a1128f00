/**
* file : SandboxModule
* func : 沙盒模块
* by : chenzh
*/
#include "base/SandboxModule.h"
#include "base/type/SandboxLuaTypeRegister.h"
#include "base/module/StudioWorkerModule.h"
#include "SandboxAssetMgr.h"

namespace MNSandbox {

	std::vector<ModuleInterface*> ModuleManager::ms_modules;

	void ModuleManager::CreateAllModules()
	{
		ms_modules.push_back(SANDBOX_NEW(AssetMgr));
		ms_modules.push_back(SANDBOX_NEW(LuaTypeRegister));
#if defined(BUILD_MINI_EDITOR_APP) || defined(STUDIO_SERVER)
		ms_modules.push_back(SANDBOX_NEW(MINIW::StudioWorker));
#endif
	}

	void ModuleManager::DestroyAllModules()
	{
		int cnt = (int)ms_modules.size();
		for (int i = cnt - 1; i >= 0; --i)
		{
			SANDBOX_DELETE(ms_modules[i]);
		}
		ms_modules.clear();
	}

	void ModuleManager::InitAllModules()
	{
		int cnt = (int)ms_modules.size();
		for (int i = 0; i < cnt; ++i)
		{
			ms_modules[i]->Init();
		}
	}

	void ModuleManager::ReleaseAllModules()
	{
		int cnt = (int)ms_modules.size();
		for (int i = cnt - 1; i >= 0; --i)
		{
			ms_modules[i]->Release();
		}
	}

}