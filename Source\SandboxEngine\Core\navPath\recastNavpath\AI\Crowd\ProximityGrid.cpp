//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//

#include "ProximityGrid.h"
#include "Utilities/BitUtility.h"

#include <math.h>

static inline int HashPos2(int x, int y, int n)
{
    return ((x * 73856093) ^ (y * 19349663)) & (n - 1);
}

ProximityGrid::ProximityGrid() :
    m_PoolHead(0),
    m_Pool(kMemAI),
    m_Buckets(kMemAI),
    m_InvCellSize(0.0f, 0.0f)
{
}

ProximityGrid::~ProximityGrid()
{
}

bool ProximityGrid::Init(const int poolSize)
{
    Assert(poolSize > 0);
    Assert(m_Buckets.empty());
    Assert(m_Pool.empty());
    m_PoolHead = 0;


    // Allocate pool of items.
    m_Pool.resize_uninitialized(poolSize);

    // Allocate hash buckets
    const int bucketsSize = std::max(4, (int)NextPowerOfTwo(poolSize) / 2);
    m_Buckets.resize_uninitialized(bucketsSize);

    ResetCellSize(Rainbow::Vector2f::one);
    return true;
}

void ProximityGrid::ResetCellSize(const Rainbow::Vector2f& cellSize)
{
    Clear();
    m_InvCellSize = Rainbow::Vector2f(1.0F / cellSize.x, 1.0F / cellSize.y);
}

void ProximityGrid::Clear()
{
    std::fill(m_Buckets.begin(), m_Buckets.end(), -1);
    m_PoolHead = 0;
    m_Bounds[0] = 0xffff;
    m_Bounds[1] = 0xffff;
    m_Bounds[2] = -0xffff;
    m_Bounds[3] = -0xffff;
}

void ProximityGrid::AddItem(CrowdRef ref, const float bounds[4])
{
    Assert(!m_Buckets.empty());

    const int iminx = (int)floorf(bounds[0] * m_InvCellSize.x);
    const int iminy = (int)floorf(bounds[1] * m_InvCellSize.y);
    const int imaxx = (int)floorf(bounds[2] * m_InvCellSize.x);
    const int imaxy = (int)floorf(bounds[3] * m_InvCellSize.y);

    m_Bounds[0] = std::min(m_Bounds[0], iminx);
    m_Bounds[1] = std::min(m_Bounds[1], iminy);
    m_Bounds[2] = std::max(m_Bounds[2], imaxx);
    m_Bounds[3] = std::max(m_Bounds[3], imaxy);

    const int poolSize = m_Pool.size();
    const int bucketsSize = m_Buckets.size();
    for (int y = iminy; y <= imaxy; ++y)
    {
        for (int x = iminx; x <= imaxx; ++x)
        {
            if (m_PoolHead >= poolSize)
                return;

            const int h = HashPos2(x, y, bucketsSize);
            const int idx = m_PoolHead;
            m_PoolHead++;
            Item& item = m_Pool[idx];
            item.ref = ref;
            item.next = m_Buckets[h];
#if RESOLVE_HASH_COLLISIONS
            item.x = (short)x;
            item.y = (short)y;
#endif
            m_Buckets[h] = idx;
        }
    }
}

int ProximityGrid::QueryItems(const float bounds[4], CrowdRef* refs, const int maxRefs) const
{
    Assert(maxRefs > 0);
    Assert(!m_Buckets.empty());

    const int iminx = std::max(m_Bounds[0], (int)floorf(bounds[0] * m_InvCellSize.x));
    const int iminy = std::max(m_Bounds[1], (int)floorf(bounds[1] * m_InvCellSize.y));
    const int imaxx = std::min(m_Bounds[2], (int)floorf(bounds[2] * m_InvCellSize.x));
    const int imaxy = std::min(m_Bounds[3], (int)floorf(bounds[3] * m_InvCellSize.y));

    int n = 0;

    const int bucketsSize = m_Buckets.size();

    // Use optimized version for the simple case of only one cell.
    if (iminx == imaxx && iminy == imaxy)
    {
        const short x = (short)iminx;
        const short y = (short)iminy;
        const int h = HashPos2(x, y, bucketsSize);
        int idx = m_Buckets[h];
        while (idx != -1)
        {
            const Item& item = m_Pool[idx];
            idx = item.next;
#if RESOLVE_HASH_COLLISIONS
            if (item.x != x || item.y != y)
                continue;
#endif
            refs[n++] = item.ref;
            if (n == maxRefs)
                return maxRefs;
        }
        return n;
    }

    // Search in a spiral, starting from the center of the box.
    const int cx = (iminx + imaxx) / 2;
    const int cy = (iminy + imaxy) / 2;
    const int w = (imaxx - iminx + 1) | 1; // Max inclusive, always odd number.
    const int h = (imaxy - iminy + 1) | 1;
    int x = 0, y = 0, dx = 0, dy = -1;

    int maxSize = std::max(w, h);
    int maxIter = maxSize * maxSize;

    for (int i = 0; i < maxIter; i++)
    {
        const int lx = cx + x;
        const int ly = cy + y;
        if (lx >= iminx && lx <= imaxx && ly >= iminy && ly <= imaxy)
        {
            const int h = HashPos2(lx, ly, bucketsSize);
            int idx = m_Buckets[h];
            const int ncomp = n;
            while (idx != -1)
            {
                const Item& item = m_Pool[idx];
                idx = item.next;

#if RESOLVE_HASH_COLLISIONS
                if ((int)item.x != lx || (int)item.y != ly)
                    continue;
#endif
                const CrowdRef ref = item.ref;

                // By assuming that each item is retrieved only once from each bucket (each bucket is a set)
                // we need only to test for doubles against previously collected buckets.
                //
                // Note: that removing the hash collision check _can_ break this assumption
                // e.g. if an item spanning several cells and two or more of these hash to same bucket.
                int i = 0;
                for (; i < ncomp && refs[i] != ref; ++i)
                {
                }
                if (i == ncomp)
                {
                    refs[n++] = ref;
                    if (n == maxRefs)
                        return maxRefs;
                }
            }
        }
        if ((x == y) || ((x < 0) && (x == -y)) || ((x > 0) && (x == 1 - y)))
        {
            int tmp = dx;
            dx = -dy;
            dy = tmp;
        }
        x += dx;
        y += dy;
    }

    return n;
}

Rainbow::Vector2f ProximityGrid::GetCellSize() const
{
    //return Inverse(m_InvCellSize);
    return Rainbow::Vector2f(1.0F / m_InvCellSize.x, 1.0F / m_InvCellSize.y);
}

void ProximityGrid::GetBounds(int bounds[4]) const
{
    bounds[0] = m_Bounds[0];
    bounds[1] = m_Bounds[1];
    bounds[2] = m_Bounds[2];
    bounds[3] = m_Bounds[3];
}

int ProximityGrid::GetItemCountAt(int x, int y) const
{
    int n = 0;
    const int h = HashPos2(x, y, m_Buckets.size());
    int idx = m_Buckets[h];
    while (idx != -1)
    {
        const Item& item = m_Pool[idx];
        idx = item.next;
#if RESOLVE_HASH_COLLISIONS
        if (item.x != x || item.y != y)
            continue;
#endif
        n++;
    }
    return n;
}
