#pragma once

#if RAINBOW_EDITOR_
#define AI_NAVIGATION_ANALYTICS 1
#else
#define AI_NAVIGATION_ANALYTICS 0
#endif

namespace BuildReporting { class BuildReport; }

class NavigationAnalytics
{
public:
    static void Initialize() {}
    static void ExitPlayMode() {}

    static void SendNavMeshBakingEvent(bool, int, int) {}
    static void SendNavigationGameBuildEvent(const BuildReporting::BuildReport&) {}
    static void SendNavMeshProjectSettingsInformation() {}
};

