
#include "PackingFullyCustomModelMgr.h"
#include "CoreCommonDef.h"

#include "FullyCustomModelMgr.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "BlockScene.h"
#include "WorldRender.h"
#include "DefManagerProxy.h"
#include "ClientInfoProxy.h"
#include "blocks/BlockMaterialMgr.h"
#include "FullyCustomModel.h"
#include "MechaMesh.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include <time.h>
#include "GameNetManager.h"
#include "Core/extend/custommodel/CustomModelMgr.h"
#include "WorldManager.h"
#include "section.h"

using namespace MINIW;
using namespace Rainbow;

extern void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype);

PackingFullyCustomModelMgr::PackingFullyCustomModelMgr()
{
	m_PreviewResPackingFCMs.clear();
}

PackingFullyCustomModelMgr::~PackingFullyCustomModelMgr()
{
	for (int i = 0; i < (int)m_MapPackingFCMs.size(); i++)
	{
		ENG_DELETE(m_MapPackingFCMs[i]);
	}
	m_MapPackingFCMs.clear();

	for (int i = 0; i < (int)m_ResPackingFCMs.size(); i++)
	{
		ENG_DELETE(m_ResPackingFCMs[i]);
	}
	m_ResPackingFCMs.clear();

	for (size_t i = 0; i < m_PreviewResPackingFCMs.size(); i++)
	{
		ENG_DELETE(m_PreviewResPackingFCMs[i]);
	}
	m_PreviewResPackingFCMs.clear();

	clearReviewMesh();
}

void PackingFullyCustomModelMgr::tick()
{
	if (m_ReviewData.blocks.size() > 0)
	{
		updatePackingFcmReview();
	}
}

void PackingFullyCustomModelMgr::leaveWorld(long long owid, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	for (int i = 0; i < (int)m_MapPackingFCMs.size(); i++)
	{
		char path[256];
		if (m_MapPackingFCMs[i]->isLeaveWorldDel())
		{
			sprintf(path, "%s/w%lld/custommodel/fully/packing/%s.pfcm", rootpath.c_str(), owid, m_MapPackingFCMs[i]->getFileName().c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}
		ENG_DELETE(m_MapPackingFCMs[i]);
	}
	m_MapPackingFCMs.clear();

	clearReviewMesh();
}

void PackingFullyCustomModelMgr::loadMapPackingFcm(long long owid, int realowneruin, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/fully/packing/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	m_MapPackingFCMs.clear();

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/fully/packing", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath += "/" + filename + ".pfcm";

		FullyCustomModel *pfcm = ENG_NEW(FullyCustomModel)();
		if (pfcm->load(filepath, filename, realowneruin, NULL, false, NULL, specialType))
		{
			m_MapPackingFCMs.push_back(pfcm);

			auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(pfcm->getKey());
			if (customItem && customItem->itemid > 0)
			{
				GetDefManagerProxy()->addDefByCustomModel(customItem->itemid, customItem->type, filename, pfcm->getName(), pfcm->getDesc(), Rainbow::Vector3f(0, 0, 0), customItem->involvedid);
			}
		}
		else
			ENG_DELETE(pfcm);
	}
}

void PackingFullyCustomModelMgr::loadResPackingFcm()
{
	char dir[64];
	sprintf(dir, "data/custommodel/fully/packing/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	for (int i = 0; i < (int)m_ResPackingFCMs.size(); i++)
	{
		ENG_DELETE(m_ResPackingFCMs[i]);
	}
	m_ResPackingFCMs.clear();
	char path[64];
	sprintf(path, "data/custommodel/fully/packing");
	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);


	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath += "/" + filename + ".pfcm";

		FullyCustomModel *pfcm = ENG_NEW(FullyCustomModel)();
		if (pfcm->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_ResPackingFCMs.push_back(pfcm);
		}
		else
			ENG_DELETE(pfcm);
	}
}

void PackingFullyCustomModelMgr::loadPackingFcm(int type, std::string filename)
{
	char dir[64];
	if (type == PREVIEW_MODEL_CLASS) 
		sprintf(dir, "data/custommodel_pre/fully/packing/");
	else
		sprintf(dir, "data/custommodel/fully/packing/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}
	std::string filepath(dir);
	filepath += filename + ".pfcm";
	if (type == RES_MODEL_CLASS)
	{
		auto iter = m_ResPackingFCMs.begin();
		for (; iter != m_ResPackingFCMs.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* pfcm = ENG_NEW(FullyCustomModel)();
		if (pfcm->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_ResPackingFCMs.push_back(pfcm);
		}
		else
			ENG_DELETE(pfcm);
	}
	else if (type == MAP_MODEL_CLASS)
	{
		auto iter = m_MapPackingFCMs.begin();
		for (; iter != m_MapPackingFCMs.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* pfcm = ENG_NEW(FullyCustomModel)();
		if (pfcm->load(filepath, filename, GetClientInfoProxy()->getUin()))
		{
			m_MapPackingFCMs.push_back(pfcm);
		}
		else
			ENG_DELETE(pfcm);
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		auto iter = m_PreviewResPackingFCMs.begin();
		for (; iter != m_PreviewResPackingFCMs.end(); iter++)
		{
			if ((*iter)->getKey() == filename)
			{
				return;
			}
		}

		FullyCustomModel* pfcm = ENG_NEW(FullyCustomModel)();
		if (pfcm->load(filepath, filename, FCM_PREVIEW_IGNORE_CHECK_UIN))
		{
			m_PreviewResPackingFCMs.push_back(pfcm);
		}
		else
			ENG_DELETE(pfcm);
	}
}

bool PackingFullyCustomModelMgr::moveResPackingFcmToMap(std::string filename, std::string classname, int folderindex, long long owid, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char srcPath[128];
	char destPath[128];
	sprintf(srcPath, "data/custommodel/fully/packing/%s.pfcm", filename.c_str());
	sprintf(destPath, "%s/w%lld/custommodel/fully/packing/%s.pfcm", rootpath.c_str(), owid, filename.c_str());
	GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

	FCMMoveFileLoadData data;
	data.moveFromLibType = RES_MODEL_CLASS;
	data.owid = owid;
	data.classname = classname;
	data.folderindex = folderindex;

	FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
	if (fullycustommodel->load(destPath, filename, GetClientInfoProxy()->getUin(), &data, false, NULL, specialType))
	{
		/*if (!fullycustommodel->moveResAvatarModelToMap(owid, classname, folderindex))
		{
			ENG_DELETE(fullycustommodel);
			return false;
		}*/

		m_MapPackingFCMs.push_back(fullycustommodel);
		return true;
	}
	else
	{
		ENG_DELETE(fullycustommodel);
		return false;
	}
}

void PackingFullyCustomModelMgr::moveMapPackingFcmToRes(std::string filename, long long owid, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char srcPath[128];
	char destPath[128];
	sprintf(srcPath, "%s/w%lld/custommodel/fully/packing/%s.pfcm", rootpath.c_str(), owid, filename.c_str());
	sprintf(destPath, "data/custommodel/fully/packing/%s.pfcm", filename.c_str());
	GetFileManager().CopyWritePathFileToWritePath(srcPath, destPath);

	FCMMoveFileLoadData data;
	data.moveFromLibType = MAP_MODEL_CLASS;
	data.owid = owid;

	FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
	if (fullycustommodel->load(destPath, filename, GetClientInfoProxy()->getUin(), &data, false, NULL, specialType))
	{
		m_ResPackingFCMs.push_back(fullycustommodel);
		//fullycustommodel->moveMapAvatarModelToRes(owid);
	}
	else
		ENG_DELETE(fullycustommodel);
}

void PackingFullyCustomModelMgr::addPackingFcm(int libtype, FullyCustomModel *pfcm)
{
	if (libtype == PUBLIC_LIB)
	{
		m_ResPackingFCMs.push_back(pfcm);
	}
	else if (libtype == MAP_LIB)
	{
		m_MapPackingFCMs.push_back(pfcm);
	}
}

bool PackingFullyCustomModelMgr::removeResByResourceCenter(int libtype, std::string skey)
{
	if (libtype == MAP_LIB)
	{
		auto iter = m_MapPackingFCMs.begin();
		for (; iter != m_MapPackingFCMs.end(); iter++)
		{
			if ((*iter)->getKey() == skey)
			{
				(*iter)->setLeaveWorldDel(true);
				if(CustomModelMgr::GetInstancePtr())
					CustomModelMgr::GetInstancePtr()->addWaitDelCustomItem(skey);
				return true;
			}
		}
	}
	else if(libtype == PUBLIC_LIB)
	{
		auto iter = m_ResPackingFCMs.begin();
		for (; iter != m_ResPackingFCMs.end(); iter++)
		{
			if ((*iter)->getKey() != skey)
				continue;

			char path[256];
			sprintf(path, "data/custommodel/fully/packing/%s.pfcm", skey.c_str());
			if (GetFileManager().IsFileExistWritePath(path))
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
			ENG_DELETE(*iter);
			m_ResPackingFCMs.erase(iter);
			return true;
		}
	}
	

	return false;
}

void PackingFullyCustomModelMgr::reEncryptFullyCustomModelData(long long owid, int olduin, int newuin, std::string authnickname, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/fully/packing/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		return;
	}


	char path[256];
	sprintf(path, "%s/w%lld/custommodel/fully/packing", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();

		FullyCustomModel *pfcm = ENG_NEW(FullyCustomModel)();
		std::string  filepath = path;
		filepath += "/" + filename + ".pfcm";
		if (pfcm->load(filepath, filename, olduin, NULL, false, NULL, specialType))
		{
			pfcm->reEncrypt(owid, newuin, authnickname, specialType);
		}

		ENG_DELETE(pfcm);
	}
}

FullyCustomModel *PackingFullyCustomModelMgr::findPackingFcm(int type, std::string skey)
{
	if (type == MAP_MODEL_CLASS)
	{
		for (int i = 0; i < (int)m_MapPackingFCMs.size(); i++)
		{
			if (m_MapPackingFCMs[i]->getKey() == skey)
			{
				return m_MapPackingFCMs[i];
			}
		}
	}
	else if (type == RES_MODEL_CLASS)
	{
		for (int i = 0; i < (int)m_ResPackingFCMs.size(); i++)
		{
			if (m_ResPackingFCMs[i]->getKey() == skey)
			{
				return m_ResPackingFCMs[i];
			}
		}
	}
	else if (type == PREVIEW_MODEL_CLASS)
	{
		for (size_t i = 0; i < m_PreviewResPackingFCMs.size(); i++)
		{
			if (m_PreviewResPackingFCMs[i]->getKey() == skey)
			{
				return m_PreviewResPackingFCMs[i];
			}
		}
	}

	return NULL;
}

void PackingFullyCustomModelMgr::addPackingFcmBySync(FullyCustomModel *fcm)
{
	m_MapPackingFCMs.push_back(fcm);
	//addOneModelToClass(fcm->getKey(), false);
}

void PackingFullyCustomModelMgr::onShortcutChange(int itemid)
{
	if (m_ReviewData.blocks.size() > 0)
	{
		m_ReviewData.clearData();
		clearReviewMesh();
	}

	ItemDef* itemDef = GetDefManagerProxy()->getItemDef(itemid);
	if (itemDef && itemDef->UseTarget == ITEM_USE_PACKING_FCM_ITEM)
	{
		updatePackingFcmReview(itemDef->Model.c_str());
	}
}

void PackingFullyCustomModelMgr::updatePackingFcmReview(std::string skey /* = "" */)
{
	if (!GetIPlayerControl())
		return;

	World *pWorld = GetIPlayerControl()->getIWorld();
	if (!pWorld)
		return;

	bool needUpdateMesh = false;

	if (m_ReviewData.m_startPos.y < 0 || m_ReviewData.m_startPos != GetIPlayerControl()->GetPlayerControlCurOperatePos())
	{
		needUpdateMesh = true;
	}

	if (m_ReviewData.blocks.size() <= 0)
	{
		auto *pFcm = findPackingFcm(MAP_MODEL_CLASS, skey);
		if (!pFcm)
			return;

		auto *boneData = pFcm->getBoneDataByIndex(0);
		if (!boneData)
			return;

		m_ReviewData.fcmDir = pFcm->getPackingFCMPlaceDir();

		for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
		{
			auto &childBone = boneData->vChildFcbds[i];

			WCoord relativePos = WCoord(childBone->offsetpos.x / BLOCK_SIZE, childBone->offsetpos.y / BLOCK_SIZE, childBone->offsetpos.z / BLOCK_SIZE);
			if (relativePos.x > m_ReviewData.dim.x)
				m_ReviewData.dim.x = relativePos.x;
			if (relativePos.y > m_ReviewData.dim.y)
				m_ReviewData.dim.y = relativePos.y;
			if (relativePos.z > m_ReviewData.dim.z)
				m_ReviewData.dim.z = relativePos.z;
		}

		for (int y = 0; y <= m_ReviewData.dim.y; y++)
		{
			for (int z = 0; z <= m_ReviewData.dim.z; z++)
			{
				for (int x = 0; x <= m_ReviewData.dim.x; x++)
				{
					m_ReviewData.blocks[WCoord(x, y, z)] = false;
				}
			}
		}
	}

	m_ReviewData.m_startPos= GetIPlayerControl()->GetPlayerControlCurOperatePos();
	if (m_ReviewData.m_startPos.y < 0)
		return;

	if (m_ReviewData.blocks.size() <= 0)
		return;

	m_ReviewData.limit = false;
	std::map<WCoord, std::vector<WCoord>> sectionPreBlocks;
	std::map<WCoord, std::vector<WCoord>> sectionLimitPreBlocks;

	auto blockIter = m_ReviewData.blocks.begin();
	for (; blockIter != m_ReviewData.blocks.end(); blockIter++)
	{
		int placeDir = GetIPlayerControl()->GetPlayerControlCurPlaceDir();
		WCoord relativePos = WCoord(blockIter->first);
		converBlockPosByPlaceDir(relativePos, m_ReviewData.fcmDir, m_ReviewData.dim, BLOCK_MODEL);
		converRelativePosByPlaceDir(relativePos, placeDir);

		WCoord pos = m_ReviewData.m_startPos + relativePos;
		WCoord ogigin = BlockDivSection(pos)*CHUNK_BLOCK_X;
		//Section *section = pWorld->getSection(pos);
		//if (section)
		//{
			Block srcBlock = pWorld->getBlock(pos);
			if (srcBlock.isEmpty() == false && srcBlock.getResID() > 0)
			{
				std::map<WCoord, std::vector<WCoord>>::iterator iter = sectionLimitPreBlocks.find(ogigin);

				if (sectionLimitPreBlocks.end() != iter)
					iter->second.push_back(pos);
				else
					sectionLimitPreBlocks[ogigin].push_back(pos);

				if (!blockIter->second)
				{
					needUpdateMesh = true;
					blockIter->second = true;
				}

				m_ReviewData.limit = true;
			}
			else
			{
				std::map<WCoord, std::vector<WCoord>>::iterator iter = sectionPreBlocks.find(ogigin);

				if (sectionPreBlocks.end() != iter)
					iter->second.push_back(pos);
				else
					sectionPreBlocks[ogigin].push_back(pos);

				if (blockIter->second)
				{
					needUpdateMesh = true;
					blockIter->second = false;
				}
			}
			
		/*}
		else
		{
			assert("updatePackingFcmReview section is null");
		}*/
	}

	if(needUpdateMesh)
		createReviewMesh(sectionPreBlocks, sectionLimitPreBlocks);
}

void PackingFullyCustomModelMgr::createReviewMesh(std::map<WCoord, std::vector<WCoord>> &sectionpreblocks, std::map<WCoord, std::vector<WCoord>> &sectionlimitpreblocks)
{
	World *pWorld = GetIPlayerControl()->getIWorld();
	if (!pWorld)
		return;

	clearReviewMesh();

	std::map<WCoord, std::vector<WCoord>>::iterator iter = sectionpreblocks.begin();
	for (; iter != sectionpreblocks.end(); ++iter)
	{
		Chunk *chunk = pWorld->getChunk(iter->second[0]);
		if (!chunk)
			pWorld->syncLoadChunk(iter->second[0], 1);

		Section *section = ENG_NEW(Section)(chunk, 0);
		section->initialize();
		section->m_Origin = iter->first;
		section->allocBlocks();
		for (int i = 0; i < (int)iter->second.size(); i++)
		{
			WCoord pos = iter->second[i] - section->m_Origin;
			section->setBlock(pos.x, pos.y, pos.z, 103, 0);
		}


		FixedString filename = FixedString("blocks/map_edit_pre_mesh.png");
		//if (m_bIsExceedLimit) filename = FixedString("blocks/map_edit_pre_mesh2.png");

		MechaMeshObject* mesh = section->createMapEditPreMesh(filename);

		if (mesh) {
			m_SectionMeshs.push_back(mesh);
		}
		section->destroy();
		ENG_DELETE(section);
	}

	std::map<WCoord, std::vector<WCoord>>::iterator limitIter = sectionlimitpreblocks.begin();
	for (; limitIter != sectionlimitpreblocks.end(); ++limitIter)
	{
		Chunk *chunk = pWorld->getChunk(limitIter->second[0]);
		if (!chunk)
			pWorld->syncLoadChunk(limitIter->second[0], 1);

		Section *section = ENG_NEW(Section)(chunk, 0);
		section->initialize();
		section->m_Origin = limitIter->first;
		section->allocBlocks();
		for (int i = 0; i < (int)limitIter->second.size(); i++)
		{
			WCoord pos = limitIter->second[i] - section->m_Origin;
			section->setBlock(pos.x, pos.y, pos.z, 103, 0);
		}


		FixedString filename = FixedString("blocks/map_edit_pre_mesh2.png");
		MechaMeshObject* mesh = section->createMapEditPreMesh(filename);

		if (mesh) {
			m_SectionMeshs.push_back(mesh);
		}
		section->destroy();
		ENG_DELETE(section);
	}

	if (pWorld && pWorld->getRenderer())
	{
		BlockScene *scene = pWorld->getRenderer()->getScene();
		if (scene)
		{
			for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
			{
				m_SectionMeshs[i]->AttachToScene(scene);
			}
		}
	}
}

void PackingFullyCustomModelMgr::clearReviewMesh()
{
	for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_SectionMeshs[i]);
	}

	m_SectionMeshs.clear();
}

void PackingFullyCustomModelMgr::converRelativePosByPlaceDir(WCoord &pos, int placedir)
{
	if (placedir == DIR_NEG_X)
	{
		int tempx = pos.x;
		pos.x = pos.z;
		pos.z = -tempx;
	}
	else if (placedir == DIR_POS_Z)
	{
		int tempx = pos.x;
		pos.x = -pos.x;
		pos.z = -pos.z;
	}
	else if (placedir == DIR_POS_X)
	{
		int tempx = pos.x;
		pos.x = -pos.z;
		pos.z = tempx;
	}
}

int PackingFullyCustomModelMgr::getRealPlaceDir(int placedir, int forwarddir)
{
	if (forwarddir == DIR_NEG_X)
	{
		return RotateDir90(placedir);
	}
	else if (forwarddir == DIR_POS_Z)
	{
		return ReverseDirection(placedir);
	}
	else if (forwarddir == DIR_POS_X)
	{
		return ReverseDirection(RotateDir90(placedir));
	}

	return placedir;
}

int PackingFullyCustomModelMgr::packingFcmItem_OnUse(IClientPlayer *player, World *pworld, std::string skey)
{
	if (!player)
		return 0;

	if (!CustomModelMgr::GetInstancePtr())
		return 0;

	auto *pFcm = findPackingFcm(MAP_MODEL_CLASS, skey);
	if (!pFcm)
		return 0;

	auto *boneData = pFcm->getBoneDataByIndex(0);
	if (!boneData)
		return 0;

	WCoord pos = player->getCurOperatePos();
	if (pos.y <= 0)
		return 0;

	int forwardDir = pFcm->getPackingFCMPlaceDir();
	WCoord dim = WCoord(0, 0, 0);

	for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
	{
		auto &childBone = boneData->vChildFcbds[i];

		WCoord relativePos = WCoord(childBone->offsetpos.x / BLOCK_SIZE, childBone->offsetpos.y / BLOCK_SIZE, childBone->offsetpos.z / BLOCK_SIZE);
		if (relativePos.x > dim.x)
			dim.x = relativePos.x;
		if (relativePos.y > dim.y)
			dim.y = relativePos.y;
		if (relativePos.z > dim.z)
			dim.z = relativePos.z;
	}

	for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
	{
		auto &childBone = boneData->vChildFcbds[i];
		/*	auto *pCm = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, childBone->model);
			if (!pCm)
				continue;*/
		auto customItem = CustomModelMgr::GetInstancePtr()->getCustomItem(childBone->model, true);
		if (!customItem)
			continue;

		int blockId = customItem->itemid;
		BlockMaterial *newmtl = g_BlockMtlMgr.getMaterial(blockId);
		if (newmtl == NULL)
			continue;

		int placeDir = player->GetPlayerCurPlaceDir();
		WCoord relativePos = WCoord(childBone->offsetpos.x / BLOCK_SIZE, childBone->offsetpos.y / BLOCK_SIZE, childBone->offsetpos.z / BLOCK_SIZE);
		converBlockPosByPlaceDir(relativePos, forwardDir, dim, BLOCK_MODEL);
		converRelativePosByPlaceDir(relativePos, placeDir);


		WCoord placepos = pos + relativePos;
		int cmDir = pFcm->getOneCMPlaceDir(childBone->name);
		if (cmDir >= 0)
			cmDir = getRealPlaceDir(forwardDir, cmDir);  //微缩本身有朝向
		else
			cmDir = forwardDir;

		int blockData = getRealPlaceDir(placeDir, cmDir);
		pworld->setBlockAll(placepos, blockId, blockData, 3);
	}

	player->shortcutItemUsed();

	return 1;
}

void PackingFullyCustomModelMgr::handlePackingFcmItemUseResult(int result)
{
	if (result == 1)
	{
		int itemid = 0;
		if(GetIPlayerControl())
			itemid = GetIPlayerControl()->GetPlayerControlCurToolID();

		ItemDef* itemDef = GetDefManagerProxy()->getItemDef(itemid);
		if (itemDef && itemDef->UseTarget == ITEM_USE_PACKING_FCM_ITEM)
		{
			updatePackingFcmReview(itemDef->Model.c_str());
		}
		else
		{
			m_ReviewData.clearData();
			clearReviewMesh();
		}
	}
}

bool PackingFullyCustomModelMgr::isLimit()
{
	return m_ReviewData.limit;
}

void PackingFullyCustomModelMgr::addResPackingFcm(FullyCustomModel *fcm)
{
	m_ResPackingFCMs.push_back(fcm);
}

std::string PackingFullyCustomModelMgr::saveFcmByPackingCM(FullyCustomModel *fcm, long long owid, int idx, std::string name /* = "" */, std::string desc /* = "" */, IClientPlayer *player/* =NULL */, int specialType/* = NORMAL_WORLD*/)
{
#ifndef IWORLD_SERVER_BUILD
	player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
#endif 

	if (!player)
		return "";

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char ckey[64] = { 0 };
	char path[256] = { 0 };

	if (owid <= 0)
		return "";

#if defined(_WIN32)
	sprintf(ckey, "%d%d%I64d", player->getUin(), idx, time(NULL));
#else
	sprintf(ckey, "%d%d%ld", player->getUin(), idx, time(NULL));
#endif
	sprintf(path, "%s/w%lld/custommodel/fully/packing/%s.pfcm", rootpath.c_str(), owid, ckey);

	if (fcm->save(path, ckey, player->getUin(), player->getNickname(), name, desc, FULLY_PACKING_CUSTOM_MODEL))
	{
		fcm->setFileName(ckey);
		m_MapPackingFCMs.push_back(fcm);
		return ckey;
	}

	return "";
}

void PackingFullyCustomModelMgr::syncPackingFcmByCreateNew()
{
	syncPackingFcm(0, m_MapPackingFCMs.size()-1);
}

void PackingFullyCustomModelMgr::syncPackingFcm(int uin, int index)
{
	size_t i = index;
	PB_PackingFCMDataHC packingFCMDataHC;
	for (; i < m_MapPackingFCMs.size(); i++)
	{
		if (i == index + 20)
			break;

		auto *pSrcFcm = m_MapPackingFCMs[i];

		auto *boneData = pSrcFcm->getBoneDataByIndex(0);
		if (!boneData)
			continue;;

		PB_OnePackingFCMDataHC *onePackingFCM = packingFCMDataHC.mutable_packingfcms()->Add();

		int forwardDir = pSrcFcm->getPackingFCMPlaceDir();
		WCoord dim = WCoord(0, 0, 0);

		for (size_t i = 0; i < boneData->vChildFcbds.size(); i++)
		{
			auto &childBone = boneData->vChildFcbds[i];

			WCoord pos = WCoord(childBone->offsetpos.x / BLOCK_SIZE, childBone->offsetpos.y / BLOCK_SIZE, childBone->offsetpos.z / BLOCK_SIZE);

			PB_OnePackingCMDataHC *onePackingCM = onePackingFCM->mutable_packingcms()->Add();

			PB_Vector3 *packingPos = onePackingCM->mutable_packingpos();
			packingPos->set_x(pos.x);
			packingPos->set_y(pos.y);
			packingPos->set_z(pos.z);

			PB_Quaternion *quat = onePackingCM->mutable_quat();
			quat->set_x(childBone->quat.x);
			quat->set_y(childBone->quat.y);
			quat->set_z(childBone->quat.z);
			quat->set_w(childBone->quat.w);

			onePackingCM->set_name(childBone->name);
			onePackingCM->set_model(childBone->model);
			onePackingCM->set_dir(pSrcFcm->getOneCMPlaceDir(childBone->name));
		}

		onePackingFCM->set_name(pSrcFcm->getName());
		onePackingFCM->set_desc(pSrcFcm->getDesc());
		onePackingFCM->set_skey(pSrcFcm->getKey());
		onePackingFCM->set_dir(pSrcFcm->getPackingFCMPlaceDir());
		onePackingFCM->set_authuin(pSrcFcm->getAuthUin());

		auto box = pSrcFcm->getFcmBoundBox();
		PB_Vector3 *minPos = onePackingFCM->mutable_minpos();
		minPos->set_x(box.minPos().x);
		minPos->set_y(box.minPos().y);
		minPos->set_z(box.minPos().z);

		PB_Vector3 *maxPos = onePackingFCM->mutable_maxpos();
		maxPos->set_x(box.maxPos().x);
		maxPos->set_y(box.maxPos().y);
		maxPos->set_z(box.maxPos().z);
	}

	if (uin == 0)
		GetGameNetManagerPtr()->sendBroadCast(PB_PACKING_FCMDATA_HC, packingFCMDataHC, 0);
	else
		GetGameNetManagerPtr()->sendToClient(uin, PB_PACKING_FCMDATA_HC, packingFCMDataHC);

	if (i < m_MapPackingFCMs.size())
	{
		syncPackingFcm(uin, i);
	}
}
