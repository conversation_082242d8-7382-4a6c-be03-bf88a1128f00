#include "SimpleFullScreenEffectRenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Render/RenderUtils.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "Render/RenderScene.h"
#include "Render/VertexLayouts/MeshVertexLayout.h"
#include "display/worlddisplay/SimpleFullScreenEffect.h"

namespace Rainbow 
{
	SimpleFullScreenEffectRenderObject::SimpleFullScreenEffectRenderObject(SimpleFullScreenEffect* component) : RenderObject(component)
	{
		m_VertexLayout = GetMeshVertexLayout(0);
	}

	SimpleFullScreenEffectRenderObject::~SimpleFullScreenEffectRenderObject()
	{

	}

	void SimpleFullScreenEffectRenderObject::ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator)
	{
        OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Rendering);
		auto* fullScreenEffect = GetComponentAs<SimpleFullScreenEffect>();
		Mesh* mesh = fullScreenEffect->GetMesh();
		MaterialInstance* mat = fullScreenEffect->GetMaterial();

		MeshPrimitiveRenderData& meshPrimitiveData = extractor.AddMeshPrimitiveRenderData(viewNode, allocator);
		meshPrimitiveData.m_MaterialRenderDatas.add(allocator);
		mat->CollectSharedMaterialDataList(meshPrimitiveData.m_MaterialRenderDatas->m_SharedMaterialDataList);
		meshPrimitiveData.m_SharedMeshRenderingData = mesh->AcquireSharedMeshRenderingData();
		meshPrimitiveData.m_VertexLayout = m_VertexLayout;

		const SharedMeshData::SubMeshContainer& subMeshes = mesh->GetSubMeshes();

		auto& meshPrim = extractor.AllocateMeshPrimitive(allocator);
		for (int i = 0; i < subMeshes.size(); ++i)
		{
			auto& elem = meshPrim.m_DrawBuffersRanges.emplace_back();
			elem = subMeshes[i].ToDrawBuffersRange(false);
		}
		extractor.AddMeshPrimitive(meshPrim, viewNode);

	}
}


