/**
* file : SceneEffectSphereFrame
* func : 场景效果 （球体）
* by : pengdapu
*/
#include "SceneEffectSphereFrame.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "SceneEffectEllipse.h"
#include "WorldRender.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectSphereFrame::SceneEffectSphereFrame()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_iStroke = 1;
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_eSes = SceneEffectShape::SPHERE_FRAME;
}

SceneEffectSphereFrame::~SceneEffectSphereFrame()
{
}

void SceneEffectSphereFrame::SetRadius(float radius)
{
	if (m_radius == radius)
		return;

	m_radius = radius;
	m_originRadius = radius;
}

void SceneEffectSphereFrame::SetSector(int sector)
{
	m_iSector = sector;
}

void SceneEffectSphereFrame::OnClear()
{
	SANDBOX_DELETE(m_ellipseY);
	SANDBOX_DELETE(m_ellipseZ);
	SANDBOX_DELETE(m_ellipseX);
}

void SceneEffectSphereFrame::Refresh()
{
	OnClear();

	SceneEffectEllipse* aEllipses[3] = {
		SANDBOX_NEW(SceneEffectEllipse),
		SANDBOX_NEW(SceneEffectEllipse),
		SANDBOX_NEW(SceneEffectEllipse),
	};

	m_ellipseY = aEllipses[0];
	m_ellipseZ = aEllipses[1];
	m_ellipseX = aEllipses[2];
	m_ellipseY->SetRotationAxis(Vector3f::yAxis, Vector3f::yAxis);
	m_ellipseZ->SetRotationAxis(Vector3f::zAxis, Vector3f::zAxis);
	m_ellipseX->SetRotationAxis(Vector3f::xAxis, Vector3f::xAxis);

	for (int i = 0; i < 3; ++i)
	{
		SceneEffectEllipse* ellipse = aEllipses[i];
		ellipse->SetCenter(m_vCenter);
		ellipse->SetRadius(m_radius);
		ellipse->SetColor(m_Color, m_Color);
		ellipse->SetStroke(m_iStroke);
		ellipse->SetSector(m_iSector);
	}
	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectSphereFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	if (m_ellipseY) m_ellipseY->OnDraw(pWorld);
	if (m_ellipseZ) m_ellipseZ->OnDraw(pWorld);
	if (m_ellipseX) m_ellipseX->OnDraw(pWorld);
}

bool SceneEffectSphereFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectSphereFrame::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;
	if (m_ellipseY)
	{
		m_ellipseY->RefreshEllipseFrame(vc, q, m_radius * vs.x, m_radius * vs.z);
	}
	if (m_ellipseZ)
	{
		m_ellipseZ->RefreshEllipseFrame(vc, q, m_radius * vs.x, m_radius * vs.y);
	}
	if (m_ellipseX)
	{
		m_ellipseX->RefreshEllipseFrame(vc, q, m_radius * vs.z, m_radius * vs.y);
	}
}
