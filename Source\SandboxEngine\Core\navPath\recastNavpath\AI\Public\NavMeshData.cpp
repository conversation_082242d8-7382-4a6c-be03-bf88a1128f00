#include "Public/NavMeshData.h"
#include "NavMeshManager.h"
#include "NavMesh/NavMeshSwapEndian.h"

// Legacy baked agent settings - added in 5.0 - replaced by storing NavMeshBuildSettings
struct NavMeshParams
{
    float walkableHeight;
    float walkableRadius;
    float walkableClimb;
    float cellSize;
};

NavMeshData::NavMeshData()
{
    m_SourceBounds = Rainbow::AABB::zero;
    m_Rotation = Rainbow::Quaternionf::identity;
    m_Position = Rainbow::Vector3f(0, 0, 0);
    m_AgentTypeID = 0;
}

void NavMeshData::InitializeClass()
{
#if RAINBOW_EDITOR_
    GetPersistentManager().AddNonTextSerializedType(TypeOf<NavMeshData>());
#endif
}

void NavMeshData::CleanupClass()
{

}


void NavMeshData::UpdateTiles(const dynamic_array<int>& removeTileIDs, NavMeshTileDataVector& newTiles, dynamic_array<int>& newTileIDs)
{
    newTileIDs.clear_dealloc();

    // We do all this dance so that the data pointers of NavMeshTileData does not change.

    // Transfer ownership of the tile data to a temp array.
    NavMeshTileDataVector stash;
    stash.resize(m_NavMeshTiles.size());
    for (int i = 0, size = m_NavMeshTiles.size(); i < size; i++)
    {
        stash[i].m_MeshData.swap(m_NavMeshTiles[i].m_MeshData);
        stash[i].m_Hash = m_NavMeshTiles[i].m_Hash;
    }

    // Clear out the removed tiles.
    for (int idx : removeTileIDs)
    {
        NavMeshTileData& data = stash[idx];
        data.m_Hash = Rainbow::Hash128();
        data.m_MeshData.clear_dealloc();

    }

    // Reallocate new tile data array, we do this only once (after transferring the ownership),
    // so that the tile data pointers don't change.
    int newCount = m_NavMeshTiles.size() - removeTileIDs.size() + newTiles.size();
    Assert(newCount >= 0);
    m_NavMeshTiles.resize(newCount);

    // Transfer ownership of data from stash, and compact array as we go.
    int idx = 0;
    for (NavMeshTileData& stashItem : stash)
    {
        if (stashItem.m_MeshData.empty())
            continue;
        m_NavMeshTiles[idx].m_MeshData.swap(stashItem.m_MeshData);
        m_NavMeshTiles[idx].m_Hash = stashItem.m_Hash;
        idx++;
    }

    // Transfer ownership of the new tile data
    // Add new tiles (not using m_NavMeshTiles.emplace_back_uninitialized() here to make sure pointers don't change).
    for (auto& tile : newTiles)
    {
        m_NavMeshTiles[idx].m_MeshData.swap(tile.m_MeshData);
        m_NavMeshTiles[idx].m_Hash = tile.m_Hash;


        newTileIDs.push_back(idx);
        idx++;
    }
    Assert(idx <= newCount);

    m_NavMeshTiles.resize(idx);
    newTiles.clear();
}
