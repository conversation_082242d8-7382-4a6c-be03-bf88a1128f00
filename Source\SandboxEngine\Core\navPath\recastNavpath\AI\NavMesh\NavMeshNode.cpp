//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//


#include "NavMeshNode.h"
#include "Utilities/BitUtility.h"

static inline unsigned int HashRef(NavMeshPolyRef a)
{
    // <PERSON>'s 64bit to 32bit.
    a = (~a) + (a << 18); // key = (key << 18) - key - 1;
    a = a ^ (a >> 31);
    a = a * 21; // key = (key + (key << 2)) + (key << 4);
    a = a ^ (a >> 11);
    a = a + (a << 6);
    a = a ^ (a >> 22);
    return (unsigned int)a;
}

//////////////////////////////////////////////////////////////////////////////////////////
NavMeshNodePool::NavMeshNodePool(int maxNavMeshNodes, int hashSize, MemLabelId label)
    : m_MaxNavMeshNodes(maxNavMeshNodes)
    , m_HashSize(hashSize)
    , m_NavMeshNodeCount(0)
    , m_NavMeshNodes(maxNavMeshNodes, label)
    , m_First(hashSize, kNavMeshNodeNullIndex, label)
    , m_Next(maxNavMeshNodes, kNavMeshNodeNullIndex, label)
{
    DebugAssert(0 < maxNavMeshNodes && maxNavMeshNodes <= kMaxNavMeshNodePoolSize);
    Assert(hashSize != 0);
    Assert(m_HashSize != 0);
    Assert(IsPowerOfTwo(hashSize));
}

NavMeshNodePool::~NavMeshNodePool()
{
}

void NavMeshNodePool::Clear()
{
    std::fill(m_First.begin(), m_First.end(), kNavMeshNodeNullIndex);
    m_NavMeshNodeCount = 0;
}

NavMeshNode* NavMeshNodePool::FindNavMeshNode(NavMeshPolyRef id)
{
    unsigned int bucket = HashRef(id) & (m_HashSize - 1);
    NavMeshNodeIndex i = m_First[bucket];
    while (i != kNavMeshNodeNullIndex)
    {
        if (m_NavMeshNodes[i].id == id)
            return &m_NavMeshNodes[i];
        i = m_Next[i];
    }
    return 0;
}

NavMeshNode* NavMeshNodePool::GetNode(NavMeshPolyRef id)
{
    unsigned int bucket = HashRef(id) & (m_HashSize - 1);
    NavMeshNodeIndex i = m_First[bucket];
    NavMeshNode* node = 0;
    while (i != kNavMeshNodeNullIndex)
    {
        if (m_NavMeshNodes[i].id == id)
            return &m_NavMeshNodes[i];
        i = m_Next[i];
    }

    if (m_NavMeshNodeCount >= m_MaxNavMeshNodes)
        return 0;

    i = (NavMeshNodeIndex)m_NavMeshNodeCount;
    m_NavMeshNodeCount++;
    DebugAssert(i < kMaxNavMeshNodePoolSize);

    // Init node
    node = &m_NavMeshNodes[i];
    node->pidx = 0;
    node->cost = 0;
    node->total = 0;
    node->id = id;
    node->flags = NavMeshNode::kNew;

    m_Next[i] = m_First[bucket];
    m_First[bucket] = i;

    return node;
}

//////////////////////////////////////////////////////////////////////////////////////////
NavMeshNodeQueue::NavMeshNodeQueue(int n)
    : m_Heap(n + 1, kMemAI)
    , m_Size(0)
{
}

NavMeshNodeQueue::~NavMeshNodeQueue()
{
}

void NavMeshNodeQueue::BubbleUp(int i, NavMeshNode* node)
{
    int parent = (i - 1) / 2;
    // note: (index > 0) means there is a parent
    while ((i > 0) && (m_Heap[parent]->total > node->total))
    {
        m_Heap[i] = m_Heap[parent];
        i = parent;
        parent = (i - 1) / 2;
    }
    m_Heap[i] = node;
}

void NavMeshNodeQueue::TrickleDown(int i, NavMeshNode* node)
{
    int child = (i * 2) + 1;
    while (child < m_Size)
    {
        if (((child + 1) < m_Size) &&
            (m_Heap[child]->total > m_Heap[child + 1]->total))
        {
            child++;
        }
        m_Heap[i] = m_Heap[child];
        i = child;
        child = (i * 2) + 1;
    }
    BubbleUp(i, node);
}
