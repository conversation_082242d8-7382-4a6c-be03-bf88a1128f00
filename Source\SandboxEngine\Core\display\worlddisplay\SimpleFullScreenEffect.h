#pragma once
#include "OgreWCoord.h"
#include "world_types.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "GameScene/MovableObject.h"
#include "Components/Camera.h"
#include "Components/Renderer.h"

namespace Rainbow 
{
	struct FullScreenVertex
	{
		Vector3f pos;
		Vector2f uv;
	};
	class Mesh;

	class EXPORT_SANDBOXENGINE SimpleFullScreenEffect : public Rainbow::Renderer
	{
		DECLARE_CLASS(SimpleFullScreenEffect);
	public:

		static SimpleFullScreenEffect* Create(const char* blendTexPath);
		static SimpleFullScreenEffect* Create(SharePtr<Texture2D> texture);

		SimpleFullScreenEffect(const char* blendTexPath);
		SimpleFullScreenEffect(SharePtr<Texture2D> texture);
		SimpleFullScreenEffect();
		virtual ~SimpleFullScreenEffect();

		Mesh* GetMesh() { return m_pMesh; }
		MaterialInstance* GetMaterial() { return m_Mat.Get(); }


		void SetAlpha(float alpha);

	protected:
		virtual RenderObject* CreateSceneObject() override;
		virtual void UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld) override {} 

	private:
		void CreateFullScreenQuad();
		void Init(const char* blendTexPath);

	private:
		Mesh* m_pMesh;
		SharePtr<MaterialInstance> m_Mat;
	};

}

