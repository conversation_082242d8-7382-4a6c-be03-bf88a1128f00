﻿#include "SocBedMgr.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "IClientPlayer.h"
#include "proto_gs2ds.pb.h"

#define PlayerMax 15

SocBedMgr::SocBedMgr()
{
}

SocBedMgr::~SocBedMgr()
{
	for (auto it : m_bed_map)
	{
		ENG_DELETE(it.second);
	}
	m_bed_map.clear();
}

SocBedData* SocBedMgr::GetSocBedDataByPos(const WCoord& pos)
{
	auto target = m_bed_map.find(pos);
	if (target == m_bed_map.end()) return nullptr;

	return target->second;
}

SocBedData* SocBedMgr::AddBed(const WCoord& pos, int itemid)
{
	SocBedData* ret = ENG_NEW(SocBedData);

	ret->owner = 0;
	ret->pos = pos;
	ret->itemid = itemid;
	ret->now = std::chrono::system_clock::now();

	m_bed_map[pos] = ret;

	return ret;
}

bool SocBedMgr::RemoveBed(const WCoord& pos)
{
	auto target = m_bed_map.find(pos);
	if (target == m_bed_map.end()) return false;
	//删除 owner 对应的坐标
	auto player = m_player_bed.find(target->second->owner);
	if (player != m_player_bed.end())
	{
		auto item = std::find_if(player->second.begin(), player->second.end(), 
			[pos](WCoord c1) 
			{
				return c1 == pos;
			}
		);

		if (player->second.end() != item)
			player->second.erase(item);
	}

	m_bed_map.erase(target);

	return true;
}

bool SocBedMgr::SetSocBedDataOwner(const WCoord& pos, int owner)
{
	auto target = m_bed_map.find(pos);
	if (target == m_bed_map.end()) return false;

	//取消占领
	if (target->second->owner != 0 && owner == 0)
	{
		auto bed_count_it = m_player_bed.find(target->second->owner);
		if (bed_count_it == m_player_bed.end())
		{
			LOG_WARNING("m_player_bed find error");
			return false;
		}

		auto item = std::find_if(bed_count_it->second.begin(), bed_count_it->second.end(),
			[pos](WCoord c1)
			{
				return c1 == pos;
			}
		);

		if (item != bed_count_it->second.end())
			bed_count_it->second.erase(item);

		target->second->owner = owner;

		return true;
	}

	if (target->second->owner == 0 && owner == 0)
	{
		LOG_WARNING("target->second->owner == 0 && owner == 0");
		return false;
	}

	auto bed_count_it = m_player_bed.find(owner);
	if (bed_count_it == m_player_bed.end())
	{
		m_player_bed[owner] = std::vector<WCoord>();
		bed_count_it = m_player_bed.find(owner);
	}

	if (bed_count_it->second.size() > PlayerMax) return false;

	target->second->owner = owner;
	bed_count_it->second.push_back(pos);

	return true;
}

bool SocBedMgr::IsUseBed(const WCoord& pos)
{
	float time = GetBedLeftTime(pos);
	if (time < 0.f) return false;

	auto target = m_bed_map.find(pos);
	ItemDef* item = GetDefManagerProxy()->getItemDef(target->second->itemid);
	if (!item) return false;

	if (item->para == "") return time > 20.f;

	try {
		int CD = std::stoi(item->para.c_str());
		return time > CD;
	}
	catch (const std::exception& e) {
		LOG_WARNING("ItemDef para to int error %s", item->para.c_str());
		return time > 20.f;
	}

	return time > 20.f;
}

float SocBedMgr::GetBedLeftTime(const WCoord& pos)
{
	auto target = m_bed_map.find(pos);
	if (target == m_bed_map.end()) return -1.0f;

	std::chrono::time_point<std::chrono::system_clock> current_time = std::chrono::system_clock::now();
	std::chrono::duration<float> time_diff = current_time - target->second->now;

	return time_diff.count();
}

bool SocBedMgr::IsBedMax(int uin)
{
	auto bed_count_it = m_player_bed.find(uin);
	if (bed_count_it == m_player_bed.end()) return false;

	return bed_count_it->second.size() >= PlayerMax;
}

//只有服务器启动会使用db数据
void SocBedMgr::OnLoadDataBase(const void* data, int len)
{
	m_bed_map.clear();
	m_player_bed.clear();

	miniw::bed_data_list beddata;
	if (!beddata.ParseFromArray(data, len))
	{
		LOG_WARNING("onLoadUserData pb ParseFromArray failed");
		return;
	}

	for (int i = 0; i < beddata.list_size(); i++)
	{
		const miniw::bed_data item_bed = beddata.list().Get(i);

		WCoord pos(item_bed.x(), item_bed.y(), item_bed.z());
		AddBed(pos, item_bed.itemid());
		if (item_bed.owner() != 0)
			SetSocBedDataOwner(pos, item_bed.owner());
	}
}