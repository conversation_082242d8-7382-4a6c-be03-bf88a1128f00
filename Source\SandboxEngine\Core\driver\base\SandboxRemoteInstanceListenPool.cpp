#include "SandboxRemoteInstanceListenPool.h"
#include "SandboxSceneObject.h"

namespace MNSandbox {

	void RemoteInstanceListenPool::Clear()
	{
		m_listenPool.clear();
	}

	void RemoteInstanceListenPool::Clear(SandboxNodeID nodeid)
	{
		m_listenPool.erase(nodeid);
	}

	void RemoteInstanceListenPool::RegisterListen(SandboxNodeID id, Callback* cb)
	{
		auto& target = m_listenPool[id];
		target.emplace_back(cb);
	}

	void RemoteInstanceListenPool::UnRegisterListen(SandboxNodeID id, Callback* cb)
	{
		auto it = m_listenPool.find(id);
		if (it != m_listenPool.end())
		{
			for (auto itList = it->second.begin(); itList != it->second.end(); itList++)
			{
				if (cb == (*itList)) {
					it->second.erase(itList);
					if (it->second.size() == 0) {
						Clear(id);
					}
					return;
				}
			}
		}
		SANDBOX_ASSERT(false);
	}


	void RemoteInstanceListenPool::OnRecvRemoteInstance(SandboxNodeID nid, SandboxNode* instance)
	{
		OPTICK_EVENT();
		SANDBOX_ASSERT(nid != 0 && instance != nullptr && nid == instance->GetNodeid());

		if (m_listenPool.size() > 0)
		{
			auto it = m_listenPool.find(nid);
			if (it != m_listenPool.end())
			{
				for (auto itList = it->second.begin(); itList != it->second.end(); itList++)
				{
					(*itList)->Emit(nid, instance);
				}

				Clear(nid);
			}
		}
	}

	bool RemoteInstanceListenPool::HadListener() const
	{
		return !m_listenPool.empty();
	}

} //namespace MNSandbox
