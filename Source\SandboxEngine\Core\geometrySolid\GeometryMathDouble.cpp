#include "GeometryMath.h"

#if COMPOSITE_USE_DOUBLE
namespace MNSandbox { namespace GeometrySolid { 
	namespace GeometryMath {

		bool Is<PERSON>ero(const GsDigit& gd, const GsDigit& epsilon)
		{
			return (gd > -epsilon && gd < epsilon);
		}

		bool IsVertexEqual(const GsVector3& va, const GsVector3& vb, const GsDigit& epsilon)
		{
			return
				IsZero(vb.x - va.x, epsilon) &&
				IsZero(vb.y - va.y, epsilon) &&
				IsZero(vb.z - va.z, epsilon);
		}

		bool IsVertexEqual(const GsVector3& va, const GsVector3& vb)
		{
			return
				va.x == vb.x &&
				va.y == vb.y &&
				va.z == vb.z;
		}

		bool IsIdentityEqual(const GsVector3& via, const GsVector3& vib, const GsDigit& epsilon)
		{
			const GsDigit dp = DotProduct(via, vib);
			return IsZero(dp - 1.0, epsilon);
		}

	}
}}
#endif