#pragma once

#include "geometrySolid/Vector3db.h"
//#include "Math/Vector4f.h"
//#include "Utilities/Logs/LogAssert.h"
//#include "Serialize/SerializeUtility.h"

namespace MNSandbox {
	namespace GeometrySolid {
		// Calculates the unnormalized normal from 3 points
		//Vector3db CalcRawNormalFromTriangle(const Vector3db& a, const Vector3db& b, const Vector3db& c);

		//enum PlaneFrustum
		//{
		//	kPlaneFrustumLeft,
		//	kPlaneFrustumRight,
		//	kPlaneFrustumBottom,
		//	kPlaneFrustumTop,
		//	kPlaneFrustumNear,
		//	kPlaneFrustumFar,
		//	kPlaneFrustumNum,
		//};

		class Planedb
		{
		public:

			Vector3db normal;
			double distance;

			const double& a() const { return normal.x; }
			const double& b() const { return normal.y; }
			const double& c() const { return normal.z; }

			const double& d() const { return distance; }
			double& d() { return distance; }

			//template<class TransferFunction>
			//inline void Transfer(TransferFunction& transfer);

			const Vector3db& GetNormal() const { return normal; }

			Planedb() {}
			Planedb(double a, double b, double c, double d) { normal.x = a; normal.y = b; normal.z = c; distance = d; }

			Planedb& operator*=(double scale);
			bool operator==(const Planedb& p) const { return normal == p.normal && distance == p.distance; }
			//bool operator!=(const Rainbow::Planedb& p) const { return normal != p.normal || distance != p.distance; }

			void SetInvalid() { normal = Vector3db::zero; distance = 0.0; }

			// Just sets the coefficients. Does NOT normalize!
			void SetABCD(const double a, const double b, const double c, const double d);

			void Set3Points(const Vector3db& a, const Vector3db& b, const Vector3db& c);
			bool Set3PointsSafe(const Vector3db& a, const Vector3db& b, const Vector3db& c);
			void Set3PointsUnnormalized(const Vector3db& a, const Vector3db& b, const Vector3db& c);

			void SetNormalAndPosition(const Vector3db& inNormal, const Vector3db& inPoint);

			double GetDistanceToPoint(const Vector3db& inPt) const;
			//double GetDistanceToPoint(const Vector4f& inPt) const;
			bool GetSide(const Vector3db& inPt) const;
			bool SameSide(const Vector3db& inPt0, const Vector3db& inPt1);

			//void NormalizeRobust(double eps = 0.00001);
			//void NormalizeUnsafe();

		};

		//template<class TransferFunction>
		//inline void Planedb::Transfer(TransferFunction& transfer)
		//{
		//	TRANSFER(normal);
		//	TRANSFER(distance);
		//}

		inline double Planedb::GetDistanceToPoint(const Vector3db& inPt) const
		{
			DebugAssert(normal.IsNormalized());
			return DotProduct(normal, inPt) + distance;
		}

		// inPt w component is ignored from distance computations
		//inline double Planedb::GetDistanceToPoint(const Vector4f& inPt) const
		//{
		//	DebugAssert(normal.IsNormalized());
		//	//Dot3 (normal, inPt) + distance;
		//	return normal.x * inPt.x + normal.y * inPt.y + normal.z * inPt.z + distance;
		//}

		// Returns true if we are on the front side (same as: GetDistanceToPoint () > 0.0)
		inline bool Planedb::GetSide(const Vector3db& inPt) const
		{
			return DotProduct(normal, inPt) + distance > 0.0F;
		}

		// Calculates the normal from 3 points unnormalized
		inline Vector3db CalcRawNormalFromTriangle(const Vector3db& a, const Vector3db& b, const Vector3db& c)
		{
			return CrossProduct(b - a, c - a);
		}

		//inline Vector3db RobustNormalFromTriangle(const Vector3db& v0, const Vector3db& v1, const Vector3db& v2)
		//{
		//	Vector3db normal = CalcRawNormalFromTriangle(v0, v1, v2);
		//	return NormalizeRobust(normal);
		//}

		inline void Planedb::SetABCD(double a, double b, double c, double d)
		{
			normal.Set(a, b, c);
			distance = d;
		}

		inline void Planedb::Set3Points(const Vector3db& a, const Vector3db& b, const Vector3db& c)
		{
			normal = CalcRawNormalFromTriangle(a, b, c);
			normal.Normalize();
			distance = -DotProduct(normal, a);
			Assert(normal.IsNormalized());
		}

		inline bool Planedb::Set3PointsSafe(const Vector3db& a, const Vector3db& b, const Vector3db& c)
		{
			normal = CalcRawNormalFromTriangle(a, b, c);

			double mag = Magnitude(normal);
			if (mag < kEpsilon)
			{
				return false;
			}
			normal /= mag;
			distance = -DotProduct(normal, a);
			Assert(normal.IsNormalized());
			return true;
		}

		inline void Planedb::Set3PointsUnnormalized(const Vector3db& a, const Vector3db& b, const Vector3db& c)
		{
			normal = CalcRawNormalFromTriangle(a, b, c);
			distance = -DotProduct(normal, a);
		}

		inline void Planedb::SetNormalAndPosition(const Vector3db& inNormal, const Vector3db& inPoint)
		{
			normal = inNormal;
			Assert(normal.IsNormalized(0.001f));
			distance = -DotProduct(inNormal, inPoint);
		}

		inline bool Planedb::SameSide(const Vector3db& inPt0, const Vector3db& inPt1)
		{
			double d0 = GetDistanceToPoint(inPt0);
			double d1 = GetDistanceToPoint(inPt1);
			if (d0 > 0.0f && d1 > 0.0f)
				return true;
			else if (d0 <= 0.0f && d1 <= 0.0f)
				return true;
			else
				return false;
		}

		inline Planedb& Planedb::operator*=(double scale)
		{
			normal *= scale;
			distance *= scale;
			return *this;
		}

		//inline void Planedb::NormalizeUnsafe()
		//{
		//	double invMag = 1.0f / Magnitude(normal);
		//	normal *= invMag;
		//	distance *= invMag;
		//}

		//// It uses NormalizeRobust(), so it handles zero and extremely small vectors,
		//// but can be slow. Another option would be to use plain normalize, but
		//// always remember to check for division by zero with zero vectors.
		//inline void Planedb::NormalizeRobust(double eps)
		//{
		//	double invMag;
		//	normal = Rainbow::NormalizeRobust(normal, invMag, eps);
		//	distance *= invMag;
		//}
	}
}

