#pragma once
/**
* file : SceneEffectSpotLightFrame
* func : 场景效果 （聚光灯用曲面底的双圆锥）
* by : pengdapu
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectEllipse;
class SceneEffectLine;

class SceneEffectSpotLightFrame : public SceneEffectGeom
{
public:
	SceneEffectSpotLightFrame();
	virtual ~SceneEffectSpotLightFrame();
	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;
	bool IsActive(World* pWorld) const override;
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
public:
	void SetRadius(float radius) override;
	void SetOuterAngle(float angle);
	void SetInnerAngle(float angle);
private:
	SceneEffectLine* m_aGeneratrices[4] = {nullptr, nullptr, nullptr, nullptr, };
	
	SceneEffectEllipse* m_aEllipses[2] = {nullptr, nullptr, };
	
	float m_fInnerRadius;

	float m_fOuterRadius;
	/**
	@brief	圆锥母线与高的夹角
	 */
	float m_fInnerAngle;
	
	float m_fOuterAngle;
};
