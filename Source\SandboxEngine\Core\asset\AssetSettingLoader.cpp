

#include "AssetSettingLoader.h"

#include "File/Packages/FilePkgBase.h"
#include "AssetPipeline/AssetManager.h"
//#include "AssetPipeline/Asset.h"
#include "AssetPipeline/Meta/MetaLoader.h"

//using namespace Rainbow;


namespace MNSandbox {

/***********************************************DefaultSettingLoader********************************************/
	DefaultSettingLoader::~DefaultSettingLoader()
	{
		ENG_RELEASE(m_AssetPtr);
	}

	bool DefaultSettingLoader::CheckAsset(Rainbow::FileInfoResult*& fileInfo, const char* realResPath)
	{
		return CheckAssetByPath(Rainbow::FileOpType::kFileOpAll, fileInfo, realResPath);
	}

	bool DefaultSettingLoader::CheckAssetByPath(Rainbow::FileOpType opType, Rainbow::FileInfoResult*& fileInfo, const char* realResPath)
	{
		if (realResPath == nullptr)
		{
			return false;
		}
		return Rainbow::GetFileManager().CheckAndGetInfo(realResPath, fileInfo, opType);
	}

	void DefaultSettingLoader::OnAssetBegin(Rainbow::Asset* res)
	{
		ENG_RELEASE(m_AssetPtr);
		m_AssetPtr = res;
		m_AssetPtr->AddRef();
	}

	void DefaultSettingLoader::OnLoadMeta(Rainbow::Asset* res)
	{
		Rainbow::GetMetaLoader().SetAssetMeta(res); ;
	}

	void DefaultSettingLoader::OnModifyAsset(Rainbow::Asset* asset)
	{
		//ModelData* data = static_cast<ModelData*>(asset);
		//data->m_RetainMeshData = true;
	}

	bool DefaultSettingLoader::NeedReload(Rainbow::Asset* res)
	{

		//ModelData* data = static_cast<ModelData*>(res);
		//if (!data->m_RetainMeshData)
		//	return true;

		return false;
	}
/***********************************************DefaultSettingLoader********************************************/


/***********************************************TextureSettingLoader********************************************/
	template<bool TextureReadable, bool MipMapEnable>
	AssetTextureSettingLoader<TextureReadable, MipMapEnable>::~AssetTextureSettingLoader()
	{
		ENG_RELEASE(m_Asset);
	}

	template<bool TextureReadable, bool MipMapEnable>
	bool AssetTextureSettingLoader<TextureReadable, MipMapEnable>::CheckAsset(Rainbow::FileInfoResult*& fileInfo, const char* realResPath, Rainbow::DataDecryptFunc func, Rainbow::TextureFormat targetTexFormat)
	{
		return CheckAssetByPath(Rainbow::FileOpType::kFileOpAll, fileInfo, realResPath, func, targetTexFormat);
	}

	template<bool TextureReadable, bool MipMapEnable>
	bool AssetTextureSettingLoader<TextureReadable, MipMapEnable>::CheckAssetByPath(Rainbow::FileOpType opType, Rainbow::FileInfoResult*& fileInfo, const char* realResPath, Rainbow::DataDecryptFunc func, Rainbow::TextureFormat targetTexFormat)
	{
		if (realResPath == nullptr)
		{
			return false;
		}

		m_TargetTexFormat = targetTexFormat;
		m_DercyptFunc = func;
		return Rainbow::GetFileManager().CheckAndGetInfo(realResPath, fileInfo, opType);
	}

	template<bool TextureReadable, bool MipMapEnable>
	void AssetTextureSettingLoader<TextureReadable, MipMapEnable>::OnAssetBegin(Rainbow::Asset* res)
	{
		ENG_RELEASE(m_Asset);
		m_Asset = res;
		m_Asset->AddRef();

		static_cast<Rainbow::Texture2D*>(res)->SetTargetTexFormat(m_TargetTexFormat);
		res->SetDataDecryptFunc(m_DercyptFunc);
		res->SetModifyAssetCall(AssetTextureSettingLoader<TextureReadable, MipMapEnable>::OnModifyAsset);
	}

	template<bool TextureReadable, bool MipMapEnable>
	void AssetTextureSettingLoader<TextureReadable, MipMapEnable>::OnLoadMeta(Rainbow::Asset* res)
	{
		Rainbow::GetMetaLoader().SetAssetMeta(res);
	}

	template<bool TextureReadable, bool MipMapEnable>
	bool AssetTextureSettingLoader<TextureReadable, MipMapEnable>::NeedReload(Rainbow::Asset* res)
	{
		Rainbow::Texture2D* tex = static_cast<Rainbow::Texture2D*>(res);
		//if (TextureReadable && !tex->GetImageData())
		if (TextureReadable && false == tex->GetSettings().m_IsReadable)
		{
			return true;
		}
		return false;
	}

	template<bool TextureReadable, bool MipMapEnable>
	void AssetTextureSettingLoader<TextureReadable, MipMapEnable>::OnModifyAsset(Rainbow::Asset* asset)
	{
		Rainbow::Texture2D* tex = static_cast<Rainbow::Texture2D*>(asset);
		Rainbow::TextureSettings& setting = tex->GetSettings();
		setting.m_IsReadable = TextureReadable;
		setting.m_EnableMipMap = MipMapEnable;
	}

	template class EXPORT_SANDBOXENGINE AssetTextureSettingLoader<false, false>;
	template class EXPORT_SANDBOXENGINE AssetTextureSettingLoader<false, true>;
	template class EXPORT_SANDBOXENGINE AssetTextureSettingLoader<true, true>;
	template class EXPORT_SANDBOXENGINE AssetTextureSettingLoader<true, false>;
/***********************************************TextureSettingLoader********************************************/

/***********************************************PrefabSettingLoader********************************************/
PrefabSettingLoader::~PrefabSettingLoader()
{
	ENG_RELEASE(m_AssetPtr);
}

bool PrefabSettingLoader::CheckAsset(Rainbow::FileInfoResult*& fileInfo, const char* realResPath)
{
	return CheckAssetByPath(Rainbow::FileOpType::kFileOpAll, fileInfo, realResPath);
}

bool PrefabSettingLoader::CheckAssetByPath(Rainbow::FileOpType opType, Rainbow::FileInfoResult*& fileInfo, const char* realResPath)
{
	if (realResPath == nullptr)
	{
		return false;
	}
	return Rainbow::GetFileManager().CheckAndGetInfo(realResPath, fileInfo, opType);
}

void PrefabSettingLoader::OnAssetBegin(Rainbow::Asset* res)
{
	ENG_RELEASE(m_AssetPtr);
	m_AssetPtr = res;
	m_AssetPtr->AddRef();
	//res->SetModifyAssetCall(PrefabSettingLoader::OnModifyAsset);
}

void PrefabSettingLoader::OnLoadMeta(Rainbow::Asset* res)
{

	Rainbow::GetMetaLoader().SetAssetMeta(res); ;
}

void PrefabSettingLoader::OnModifyAsset(Rainbow::Asset* asset)
{
	//ModelData* data = static_cast<ModelData*>(asset);
	//data->m_RetainMeshData = true;
}

bool PrefabSettingLoader::NeedReload(Rainbow::Asset* res)
{

	//ModelData* data = static_cast<ModelData*>(res);
	//if (!data->m_RetainMeshData)
	//	return true;

	return false;
}
/***********************************************PrefabSettingLoader********************************************/

}