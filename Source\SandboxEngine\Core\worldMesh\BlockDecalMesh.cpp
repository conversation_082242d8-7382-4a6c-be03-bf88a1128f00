#include "OgrePrerequisites.h"
#include "BlockDecalMesh.h"
#include "blocks/BlockMaterialMgr.h"
#include "BlockGeom.h"
#include "world.h"
#include "section.h"
#include "Core/GameScene.h"
#include "Core/GameEngine.h"
#include "SandboxRenderSetting.h"

#include "worldMesh/RenderSection.h"
#include "worldMesh/ChunkRenderer.h"
#include "chunk.h"
#include "PlayManagerInterface.h"

using namespace Rainbow;
using namespace MINIW;

IMPLEMENT_CLASS_GLOBAL(BlockDecalMesh, kRuntimeClassFlagAbstract)

BlockDecalRenderObject::BlockDecalRenderObject(BlockDecalMesh* obj)
		: SectionRenderObject(obj->getSectionMesh()), m_Object(obj)
{
	m_PrimitiveRenderData.m_CastShadow = false;
	m_PrimitiveRenderData.m_ReceiveShadow = false;
	m_PrimitiveRenderData.m_IsAffectedByFog = false;
	m_PrimitiveRenderData.m_IsAffectedByLight = false;
}

//Matrix4x4f GetLocalToWorld() const override
//{
//	return Rainbow::SectionRenderObject::GetLocalToWorld();

//	//WCoord pos = m_Object->GetBlock() * BLOCK_SIZE;
//	//Matrix4x4f mat = Matrix4x4f::identity;
//	//mat.SetPosition(pos.toVector3());
//	//return mat;
//}

AABB BlockDecalRenderObject::CalculateWorldBounds() const
{

	if ( m_SectionMesh->GetSubMeshCount() > 0 && m_SectionMesh->GetSubMeshAt(0)->HasRenderData())
	{
		Matrix4x4f localToWorld = CalculateLocalToWorld();
		AABB worldAABB;
		TransformAABB(m_SectionMesh->GetLocalAABB(), localToWorld, worldAABB);
		return worldAABB;
	}
	else
	{
		return Rainbow::SectionRenderObject::CalculateWorldBounds();
	}
	//return Rainbow::SectionRenderObject::GetWorldBounds();

	//AABB aabb(GetLocalToWorld().GetPosition(), BLOCK_SIZE);
	//return AABB(GetLocalToWorld().GetPosition(), BLOCK_SIZE);
}

	bool BlockDecalRenderObject::IsEnabled() const 
{
	return m_Object->IsShow();
}

	bool BlockDecalRenderObject::PrepareRender(const Rainbow::PrimitiveFrameNode& node)
{
	return IsEnabled();
}

void BlockDecalRenderObject::ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator) 
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Rendering);
	const auto& meshPrimitives = m_SectionMesh->GetMeshPrimitives();
	size_t count = meshPrimitives.size();
	if (count <= 0)
	{
		return;
	}

	MeshPrimitiveRenderData* meshPrimitiveDatas = extractor.AddMeshPrimitiveRenderData(viewNode, allocator, count);
	for (size_t idx = 0; idx < count; ++idx)
	{
		MeshPrimitiveRenderData& meshPrimitiveRenderData = meshPrimitiveDatas[idx];
		const SectionMeshPrimitive& meshPrim = meshPrimitives[idx];
		meshPrimitiveRenderData.m_VertexLayout = meshPrim.m_VertexLayout;
		meshPrimitiveRenderData.m_SharedMeshRenderingData = meshPrim.m_SharedMeshRenderingData;
		meshPrimitiveRenderData.m_SharedMeshRenderingData->AddRef();
		meshPrimitiveRenderData.m_MaterialRenderDatas.add(allocator);
		m_Object->GetRenderMaterial(idx)->CollectSharedMaterialDataList(meshPrimitiveRenderData.m_MaterialRenderDatas->m_SharedMaterialDataList);

		MeshPrimitive& meshPrimtive = extractor.AllocateMeshPrimitive(allocator, idx, 0);
		meshPrimtive.m_DrawBuffersRanges.emplace_back() = meshPrim.m_DrawBuffersRange;
		extractor.AddMeshPrimitive(meshPrimtive, viewNode);
	}

	//for (size_t idx = 0; idx < m_SectionMesh->GetSubMeshCount(); ++idx)
	//{
	//	SectionSubMesh* subMesh = m_SectionMesh->GetSubMeshAt(idx);
	//	if (subMesh->HasRenderData())
	//	{
	//		for (size_t meshIdx = 0; meshIdx < subMesh->GetMeshInfoCount(); ++meshIdx)
	//		{
	//			auto meshInfo = subMesh->GetMeshInfo(meshIdx);

	//			//if (meshInfo->m_BatchSubmeshIndex != kInvalidIndex)
	//			//{
	//			//	ViewMeshPrimitive* viewMeshPrimitive = extractor->AllocateViewMeshPrimitive(node, view, m_Object->GetRenderMaterial()->GetRenderData());
	//			//	if (viewMeshPrimitive != nullptr)
	//			//	{
	//			//		meshPrim.m_MaterialRenderData = m_Object->GetRenderMaterial()->GetRenderData();
	//			//		meshPrim.m_VertexLayout = meshInfo->m_BatchMesh->GetSharedRenderingData()->GetVertexLayout();
	//			//		auto& elem = meshPrim.m_DrawBuffersRanges.emplace_back();
	//			//		elem.m_SharedIndexBuffer = meshInfo->m_BatchMesh->GetSharedIndexBuffer();
	//			//		elem = meshInfo->m_BatchMesh->GetSubMeshes()[meshInfo->m_BatchSubmeshIndex].ToDrawBuffersRange(false);
	//			//	}
	//			//}
	//			//else
	//			{
	//				auto& meshPrim = extractor.AllocateMeshPrimitive();
	//				{
	//					meshPrim.m_MaterialRenderData = m_Object->GetRenderMaterial()->GetRenderData();
	//					meshPrim.m_SharedMeshRenderingData = meshInfo->m_Mesh->GetSharedRenderingData();
	//					meshPrim.m_VertexLayout = meshInfo->m_VertexLayout;
	//					auto& elem = meshPrim.m_DrawBuffersRanges.emplace_back();
	//					elem = meshInfo->m_Mesh->GetSubMeshes()[0].ToDrawBuffersRange(false);
	//				}
	//				extractor.AddMeshPrimitive(meshPrim);
	//			}
	//		}
	//	}
	//}
}




BlockDecalMesh::BlockDecalMesh(const char *texname, int textype) : MovableObject(),
                                                                   m_CurBlock(INT_MAX, INT_MAX, INT_MAX),
                                                                   m_LastStage(-1), isShow(false),
                                                                   m_RenderObject(nullptr), m_OverlayColor(Rainbow::ColorRGBAf::black)
{
	m_Mtl = g_BlockMtlMgr.createRenderMaterial(texname, nullptr, textype, BLOCKDRAW_DECAL, 0);
	m_DecalSectionMesh = ENG_NEW(BlockDecalSectionMesh)(m_Mtl);

	//testcode
	//m_OverlayColor = ColorRGBAf::red;

	
}

BlockDecalMesh::~BlockDecalMesh()
{
	// todo 正确释放m_Mtl
	ENG_RELEASE(m_Mtl);
	ENG_RELEASE(m_DecalSectionMesh);
	//ENG_RELEASE(m_Mat);
	//ENG_RELEASE(m_MatPacked);

	//ENG_DELETE_LABEL(m_SectionMesh, kMemGame);
}

BlockDecalSectionMesh* BlockDecalMesh::getSectionMesh()
{
	return m_DecalSectionMesh;
}

void BlockDecalMesh::Show(bool b)
{
	this->isShow = b;
}


//SectionSubMesh *BlockDecalMesh::getSubMesh()
//{
//	return m_SubMesh;
//}

//void BlockDecalMesh::updateWorldCache()
//{
	//SectionMesh::updateWorldCache();

	//WCoord pos = m_CurBlock*BLOCK_SIZE;
	//m_WorldBounds.fromBox(pos.toVector3(), (pos+WCoord(BLOCK_SIZE,BLOCK_SIZE,BLOCK_SIZE)).toVector3());
//}

void BlockDecalMesh::setBlock(World *pworld, const WCoord &grid, int stage)
{
	assert(stage >= 0);
	AssertMsg(m_RenderObject, "Render Object is NULL!");
	bool immediateUpdate = false;
	bool waitForBuild = false;
	if (stage == 0 && grid == m_CurBlock)
	{
		Section* psection = pworld->getSection(grid);
		if (psection != nullptr) 
		{
			WCoord offset = grid - psection->m_Origin;
			int curData = psection->getBlockData(offset);
			immediateUpdate = curData != m_CacheBlockData;

			if (immediateUpdate)
			{
				BlockMaterial* pBlockmtl = pworld->getBlockMaterial(grid);
				if (pBlockmtl) {
					bool isDoor = false;
					GetISandboxActorSubsystem()->IsBlockType(isDoor, pBlockmtl, BlockCheckType::DOOR_MATERIAL);
					if (isDoor)
					{
						Chunk* pChunk = psection->GetChunk();
						if (pChunk != nullptr)
						{
							ChunkRenderer* chunkRenderer = pChunk->GetChunkRenderer();
							if (chunkRenderer != nullptr && !chunkRenderer->IsPendingDestroy())
							{
								//快速点击的时候会有残影，先清理掉
								m_DecalSectionMesh->CleanUp();
								//如果是门就等渲染完实体门再去渲染高亮，不然高亮先于实体门渲染
								waitForBuild = chunkRenderer->IsUpdating();
							}
						}
					}
				}
			}
		}
	} 
	if (m_DecalSectionMesh->IsSelectHight() && grid != m_CurBlock)
	{
		//if chunk is building wait 
		Section* psection = pworld->getSection(grid);
		if (psection != nullptr)
		{
			RenderSection* renderSection = psection->GetRenderSection();
			if (renderSection != nullptr && !renderSection->IsPendingDestroy())
			{
				waitForBuild = renderSection->NeedUpdate();
			}
			if (!waitForBuild) 
			{
				Chunk* pChunk = psection->GetChunk();
				if (pChunk != nullptr)
				{
					ChunkRenderer* chunkRenderer = pChunk->GetChunkRenderer();
					if (chunkRenderer != nullptr && !chunkRenderer->IsPendingDestroy())
					{
						waitForBuild = chunkRenderer->IsUpdating();
					}
				}
			}
		}
		//地形在构建过程,隐藏高亮显示
		Show(!waitForBuild);
	}
	if (waitForBuild) 
	{
		return;
	}

	if(grid != m_CurBlock || immediateUpdate)
	{
		//reset(false);	

		Section *psection = pworld->getSection(grid);
		if (psection == NULL) 
		{
			Show(false);
			return;
		}

		//超一格的方块敲掉后残留虚影
		m_DecalSectionMesh->CleanUp();
		
		WCoord offset = grid - psection->m_Origin;
		m_DecalSectionMesh->OnOriginChanged(psection->m_Origin);
		BlockMaterial *blockmtl = pworld->getBlockMaterial(grid);
		if(blockmtl){
			m_DecalSectionMesh->SetCurBlockId(blockmtl->getBlockResID());
			BuildSectionMeshData data;
			data.m_World = pworld;
			data.m_SharedSectionData = psection;
			data.m_SharedChunkData = &psection->GetChunk()->GetSharedChunkData();
			int blockid = pworld->getBlockID(grid);
			if (blockid == 224 || blockid==763 || blockid == 761 || blockid == 392)
			{
				data.m_SectionMesh = NULL;//data.m_SectionMesh这个值在这里为0xCCCC，所以手动设置为NULL,使覆雪草块高亮正常显示
			}
			if (blockid == 150023 && (pworld->getBlockData(grid) & 4) != 0)
				blockmtl->createBlockMesh(data, NeighborCoord( offset, DIR_NEG_Y), m_DecalSectionMesh);
			else
				blockmtl->createBlockMesh(data, offset, m_DecalSectionMesh);
		}

		// todo check useless
		//int bs = BLOCK_SIZE*WorldPos::UNIT;
		//m_SectionMesh->OnOriginChanged((psection->m_Origin * BLOCK_SIZE));
		//m_ChunkOrigin = (psection->m_Origin*BLOCK_SIZE).toWorldPos().toVector3();
		//setPosition(WorldPos(psection->m_Origin.x*bs, psection->m_Origin.y*bs, psection->m_Origin.z*bs));
		m_DecalSectionMesh->UploadMeshData();
		//onCreate();
		m_CurBlock = grid;
		m_CacheBlockData = psection->getBlockData(offset);
		m_LastStage = -1;
		//updateWorldCache();

		if (m_RenderObject != nullptr)
		{
			m_RenderObject->SetDirtyFlags(SceneObject::kDirtyFlagsWorldTransform | SceneObject::kDirtyFlagsBounds);
		}
	}

	//if(m_LastStage != stage && !m_DecalSectionMesh->IsSelectHight())
	//{
	//	// todo to implement
	//	m_Mtl->setTextureStage(stage, 10);
	//	m_LastStage = stage;
	//}

	if (m_LastStage != stage && !m_DecalSectionMesh->IsSelectHight())
	{
		BlockTexElement* textureElement = m_Mtl->getTexElement();
		if (textureElement != nullptr)
		{
			size_t nframe = textureElement->getNumFrames();
			if (nframe == 0) nframe = 1;
			size_t iframe = stage * nframe / 10;
			if (iframe >= nframe) iframe = nframe - 1;
			MaterialInstance* mat = m_DecalSectionMesh->GetRenderMaterial(0);
			mat->SetTexture(ShaderParamNames::g_DiffuseTex, textureElement->getTexture(iframe).Get());
		}
		// todo to implement
		//m_Mtl->setTextureStage(stage, 10);
		m_LastStage = stage;
	}
}

Rainbow::MaterialInstance* BlockDecalMesh::GetRenderMaterial(size_t idx)
{
	return m_DecalSectionMesh->GetRenderMaterial(idx);
}

void BlockDecalMesh::SetIsSelectHightLight()
{
	m_DecalSectionMesh->SetIsSelectHightLight();
	UpdateOverlayColorInternal();
}

void BlockDecalMesh::SetOverlayColor(const Rainbow::ColorRGBAf& color)
{
	m_OverlayColor = color;
	UpdateOverlayColorInternal();
}

void BlockDecalMesh::SetOverrideColor(const Rainbow::ColorRGBAf& color)
{
	m_OverrideColor = color;
	UpdateOverrideColorInternal();
}

void BlockDecalMesh::UpdateVertexAnimationEnable()
{
	m_DecalSectionMesh->UpdateVertexAnimationEnable();
}

void BlockDecalMesh::SetCustomMaterial()
{
	m_DecalSectionMesh->SetCustomMaterial();
}

void BlockDecalMesh::UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld)
{
}

void BlockDecalMesh::OnAddToScene(Rainbow::GameScene* scene)
{
	Super::OnAddToScene(scene);
	m_RenderObject = ENG_NEW(BlockDecalRenderObject)(this);	// todo opt new
	GetRenderScene().GetRenderObjectSceneAccessor().AddRenderObject(m_RenderObject, scene->GetGameSceneId());
}

void BlockDecalMesh::OnRemoveFromScene(Rainbow::GameScene* scene) 
{
	Super::OnRemoveFromScene(scene);
	if (m_RenderObject)
	{
		GetRenderScene().GetRenderObjectSceneAccessor().RemoveRenderObject(m_RenderObject);
		ENG_DELETE(m_RenderObject);
	}
}

void BlockDecalMesh::UpdateOverlayColorInternal()
{
	MaterialInstance* mat = m_DecalSectionMesh->GetRenderMaterial(0);
	if (mat)
	{
		mat->SetColor("g_OverlayColor", m_OverlayColor);
	}
	m_DecalSectionMesh->UpdateOverlayColor(m_OverlayColor);
}

void BlockDecalMesh::UpdateOverrideColorInternal()
{
	MaterialInstance* mat = m_DecalSectionMesh->GetRenderMaterial(0);
	if (mat)
	{
		mat->SetColor("g_OverrideColor", m_OverrideColor);
	}
	m_DecalSectionMesh->UpdateOverrideColor(m_OverrideColor);
}

//void BlockDecalMesh::render(SceneRenderer* pRenderer, const ShaderEnvData &envdata)
//{
//	if(m_CurBlock.x != INT_MAX) SectionMesh::render(pRenderer, envdata);
//}

BlockDecalSectionMesh::BlockDecalSectionMesh(RenderBlockMaterial* mtl)
	: m_Mtl(mtl)
	//, m_MatPacked(nullptr)
	//, m_Mat(nullptr)
	, m_CustomMat(nullptr)
{
	m_SubMesh = SectionMesh::getSubMesh(m_Mtl);
	m_SubMesh->SetIgnoreTileUV(true);
	m_HasNormal = false;
}

BlockDecalSectionMesh::~BlockDecalSectionMesh()
{
	m_Mtl = nullptr;

	for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
	{
		ENG_DELETE(m_MaterialList[idx]);
	}
	m_MaterialList.clear_dealloc();
	//ENG_RELEASE(m_Mat);
	//ENG_RELEASE(m_MatPacked);
	ENG_RELEASE(m_CustomMat);
}

SectionSubMesh* BlockDecalSectionMesh::getSubMesh(RenderBlockMaterial* mtl, bool foritem, int lodLevel)
{
	Assert(mtl != nullptr);
	if (nullptr == mtl)
	{
		return nullptr;
	}
	mtl->TryAllocatePackableMaterialId();
	SectionSubMesh* subMesh = nullptr;
	if (m_MaterialList.size() > 0) 
	{
		size_t index = GetMaterialInfoIndex(mtl);
		size_t subMeshCount = GetSubMeshCount();
		if (index >= subMeshCount) 
		{
			GenerateSubMesh(mtl, mtl->GetPackableMaterialId());
			subMeshCount = GetSubMeshCount();
			if (index >= subMeshCount) return m_SubMesh;
		}
		subMesh = GetSubMeshAt(index);
		subMesh->SetIgnoreTileUV(false);
		//需要将贴图传给材质那边，用于透明的裁剪
		Rainbow::MaterialInstance* mat = foritem ? mtl->getItemMaterial() : mtl->getMaterial();
		SharePtr<Texture> itemTex = NativeToSharePtr<Texture>(mat->GetTexture("g_DiffuseTex"));
		BlockDecalMaterialInfo* info = m_MaterialList[index];
		info->m_IsPacked = IsPackableMaterialId(mtl->GetPackableMaterialId());
		MaterialInstance* useMtl = info->m_IsPacked ? info->m_MatPacked : info->m_Mat;
		useMtl->SetTexture("g_MapPackedTex", itemTex.Get());
		UpdateVertexAnimationEnable();
	}
	else 
	{
		subMesh = m_SubMesh;
	}

	//if (m_Mat != nullptr)
	//{
	//	m_IsPacked = IsPackableMaterialId(mtl->GetPackableMaterialId());
	//	MaterialInstance* useMtl = m_IsPacked ? m_MatPacked : m_Mat;
	//	useMtl->SetTexture("g_MapPackedTex", itemTex.Get());
	//	UpdateVertexAnimationEnable();
	//}
	//else
	//{
	//	m_Mtl->getMaterial()->SetTexture("g_MapPackedTex", itemTex.Get());
	//}

	return subMesh;
}

//SectionSubMesh* BlockDecalSectionMesh::GetOrCreateSubMesh(RenderBlockMaterial* mtl, int materialId)
//{
//	Assert(mtl != nullptr);
//	for (size_t i = 0; i < m_SubMeshes.size(); i++)
//	{
//		if (IsPackableMaterialId(materialId))
//		{
//			if (m_SubMeshes[i]->GetMaterialId() == materialId)
//			{
//				return m_SubMeshes[i];
//			}
//		}
//		else
//		{
//			if (m_SubMeshes[i]->GetRenderBlockMaterial() == mtl && m_SubMeshes[i]->GetMaterialId() == materialId)
//			{
//				return m_SubMeshes[i];
//			}
//		}
//	}
//	SectionSubMesh* subMesh = ENG_NEW(SectionSubMesh)(this, mtl, materialId);
//	subMesh->m_SectionOffsetZero = m_SectionOffsetZero;
//	subMesh->m_SectionOffset = m_SectionOffset;
//	m_SubMeshes.push_back(subMesh);
//	return subMesh;
//}

Rainbow::MaterialInstance* BlockDecalSectionMesh::GetRenderMaterial(size_t index)
{
	if(m_MaterialList.size() > 0 && index < m_MaterialList.size())
	{
		BlockDecalMaterialInfo* info = m_MaterialList[index];
		return info->m_IsPacked ? info->m_MatPacked : info->m_Mat;
	}
	else
	{
		if (m_CustomMat != nullptr) return m_CustomMat;
		else return m_Mtl->getMaterial();
	}
}

void BlockDecalSectionMesh::SetCustomMaterial()
{
	if (m_CustomMat != nullptr) return;
	MaterialInstance* mat = m_Mtl->getMaterial();
	if (mat != nullptr)
	{
		m_CustomMat = MaterialInstance::CreateInstanceFromParent(mat);
		m_CustomMat->SetDepthBias(-10);
		m_CustomMat->SetSlopeScaledDepthBias(-0.01f);
		m_CustomMat->SetRenderGroup(kRenderGroupDefault - 1);
	}
}

void BlockDecalSectionMesh::SetIsSelectHightLight()
{
	if (m_MaterialList.size() == 0) 
	{
		AppendMaterialInfo();
	}

}

bool BlockDecalSectionMesh::IsSelectHight()
{
	return m_MaterialList.size() > 0;
}

void BlockDecalSectionMesh::UpdateOverlayColor(const Rainbow::ColorRGBAf& color)
{
	for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
	{
		BlockDecalMaterialInfo* info = m_MaterialList[idx];
		if (info) 
		{
			if (info->m_Mat) 
			{
				info->m_Mat->SetColor("g_OverlayColor", color);
			}
			if (info->m_MatPacked) 
			{
				info->m_MatPacked->SetColor("g_OverlayColor", color);
			}
		}
	}
	//if (m_Mat) m_Mat->SetColor("g_OverlayColor", color);
	//if (m_MatPacked) m_MatPacked->SetColor("g_OverlayColor", color);
}

void BlockDecalSectionMesh::SetCurBlockId(int id)
{
	m_CurBlockResId = id;
	m_IsRebuildMesh = false;

	if (m_MaterialList.size() > 0) 
	{
		for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
		{
			m_MaterialList[idx]->m_RenderBlockMtl = nullptr;
		}
	}
}

void BlockDecalSectionMesh::UpdateVertexAnimationEnable()
{
	for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
	{
		BlockDecalMaterialInfo* info = m_MaterialList[idx];
		if (info == nullptr) continue;

		MaterialInstance* useMtl = info->m_IsPacked ? info->m_MatPacked : info->m_Mat;
		if (!useMtl) return;
		bool openVertexAnimation = false;

		if (Rainbow::GetSandboxRenderSetting().m_Data.m_UseGrassAnimation)
		{
			openVertexAnimation = g_BlockMtlMgr.IsUseVertexAnimationEffect(m_CurBlockResId);
		}
		if (openVertexAnimation) useMtl->EnableKeyword("VERTEX_ANIMATION");
		else useMtl->DisableKeyword("VERTEX_ANIMATION");
	}


}

void BlockDecalSectionMesh::AppendMaterialInfo()
{
	BlockDecalMaterialInfo* info = ENG_NEW(BlockDecalMaterialInfo)();
	m_MaterialList.push_back(info);

	MaterialManager& materialManager = GetMaterialManager();
	SharePtr<Texture2D> ptex = GetAssetManager().LoadAsset<Texture2D>("blocks/highlight.png");
	info->m_Mat = materialManager.LoadFromFile("Materials/MiniGame/Block/block_modulate2x.templatemat")->CreateInstance();
	info->m_Mat->SetTexture("g_DiffuseTex", ptex.Get());
	info->m_MatPacked = materialManager.LoadFromFile("Materials/MiniGame/Block/block_atlas_modulate2x.templatemat")->CreateInstance();
	info->m_MatPacked->SetTexture("g_DiffuseTex", ptex.Get());
	//m_SubMesh->SetIgnoreTileUV(false);

	info->m_Mat->SetDepthBias(-10);
	info->m_Mat->SetSlopeScaledDepthBias(-0.01f);
	info->m_Mat->SetCullMode(CullMode::kCullOff);
	info->m_Mat->SetRenderGroup(kRenderGroupDefault + 1);

	info->m_MatPacked->SetDepthBias(-10);
	info->m_MatPacked->SetSlopeScaledDepthBias(-0.01f);
	info->m_MatPacked->SetCullMode(CullMode::kCullOff);
	info->m_MatPacked->SetRenderGroup(kRenderGroupDefault + 1);
}

size_t BlockDecalSectionMesh::GetMaterialInfoIndex(RenderBlockMaterial* mtl)
{
	for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
	{
		if (m_MaterialList[idx]->m_RenderBlockMtl == nullptr) 
		{
			m_MaterialList[idx]->m_RenderBlockMtl = mtl;
			return idx;
		}
		else if (m_MaterialList[idx]->m_RenderBlockMtl == mtl) 
		{
			return idx;
		}
	}
	AppendMaterialInfo();
	size_t index = m_MaterialList.size() - 1;
	m_MaterialList[index]->m_RenderBlockMtl = mtl;
	return index;
}

void BlockDecalSectionMesh::UpdateOverrideColor(const Rainbow::ColorRGBAf& color)
{
	for (size_t idx = 0; idx < m_MaterialList.size(); idx++) 
	{
		BlockDecalMaterialInfo* info = m_MaterialList[idx];
		if (info) 
		{
			if (info->m_Mat) 
			{
				info->m_Mat->SetColor("g_OverrideColor", color);
			}
			if (info->m_MatPacked) 
			{
				info->m_MatPacked->SetColor("g_OverrideColor", color);
			}
		}
	}
}
