#pragma once
/**
* file : SandboxTimer
* func : ɳ�ж�ʱ��
* by : chenzh
*/
#include "SandboxType.h"
#include "SandboxRef.h"
#include "SandboxNotify.h"
#include "SandboxAutoRef.h"
#include "SandboxWeakRef.h"
#include "SandboxBindRefUserdata.h"


namespace MNSandbox {


	class TimerManager;

	/* ��ʱ�� */
	class EXPORT_SANDBOXDRIVERMODULE MNTimer : public Ref
	{
		DECLARE_REFCLASS(MNTimer)
		friend class TimerManager;
	public:
		enum class PLAYSTATE
		{
			IDLE = 0, // ����
			PLAY, // ����״̬
			PAUSE, // ��ͣ״̬
		};

		using CallbackListener = AutoRef<Listener<AutoRef<MNTimer>>>;
		using CallbackMethod = void(*)(AutoRef<MNTimer>);
		using CallbackFunction = std::function<void(AutoRef<MNTimer>)>;
	public:
		MNTimer(double delay, bool loop = false, double interval = -1.0);
		virtual ~MNTimer();

		// �ͷ�
		virtual void Release() override { Destroy(); }

		/**
		* ������ʱ��
		* @param delay �ӳ�ִ��ʱ��
		* @param loop �Ƿ�ѭ��ִ��
		* @param interval ���ʱ�䣬< 0 ��ʾ ���delayʱ��
		* @param defPlay Ĭ�Ͽ�ʼ��������������ʼ���С�Ĭ��ֵtrue
		*/
		static AutoRef<MNTimer> CreateTimer(double delay, bool loop, double interval, bool defPlay = true);
		static AutoRef<MNTimer> CreateTimer(double delay) { return CreateTimer(delay, false, -1.0); }
		static AutoRef<MNTimer> CreateTimer(CallbackListener callback, double delay, bool loop, double interval, bool defPlay = true);
		static AutoRef<MNTimer> CreateTimer(CallbackListener callback, double delay) { return CreateTimer(callback, delay, false, -1.0); }
		static AutoRef<MNTimer> CreateTimer(CallbackMethod callback, double delay, bool loop, double interval, bool defPlay = true);
		static AutoRef<MNTimer> CreateTimer(CallbackMethod callback, double delay) { return CreateTimer(callback, delay, false, -1.0); }
		static AutoRef<MNTimer> CreateTimer(CallbackFunction callback, double delay, bool loop, double interval, bool defPlay = true);
		static AutoRef<MNTimer> CreateTimer(CallbackFunction callback, double delay) { return CreateTimer(callback, delay, false, -1.0); }

		/*
		* ���ٶ�ʱ��
		* @param timer ��ʱ��
		*/
		static void DestroyTimer(AutoRef<MNTimer> timer);

		// ����
		void Destroy();

		// �Ƿ���Ч
		bool IsValid() { return m_notifyTrigger.IsValid(); }

		// ִ�У�����userdata
		void Emit() { m_notifyTrigger.Emit(this); }

		// ���ķ���
		void Subscribe(AutoRef<Listener<AutoRef<MNTimer>>> listener);

		// ����ʣ��ʱ��
		double CalcLeftTime();

		// �Ƿ���������
		inline bool IsPlaying() const { return m_state == PLAYSTATE::PLAY; }

		// ��ʼ
		void Play();
		// ��ͣ
		void Pause();
		// �ָ�
		void Resume();
		// ֹͣ
		void Stop();

		// ��ȡ��ǰʹ�õ�timer ������
		TimerManager* GetTimerMgr() const { return m_timerMgr; }

	private:
		// ���ù�����Ƭ����
		void SetMgrPieceIdx(int pieceidx, TimerManager* timermgr);
		int GetMgrPieceIdx() const { return m_MgrPieceIdx; }
		void ClearMgrPieceIdx();
		void Clear();

		// ע�ᡢע����ʱ��
		void RegisterTimer(double delay);
		void UnregisterTimer();

	public:
		// ���û�����
		BindRefUserdata BindUserdata;

	private:
		// ֪ͨ������ 
		Notify<AutoRef<MNTimer>> m_notifyTrigger;

	private:
		// �ӳ�ִ��ʱ��
		double m_delay = 0.0;
		// ѭ��ִ�м��
		double m_interval = 0.0;
		// �Ƿ�ѭ��ִ��
		bool m_loop = false;
		// ��ʱ��������Ƭ����
		int m_MgrPieceIdx = -1;

		// ״̬
		PLAYSTATE m_state = PLAYSTATE::IDLE;
		// ʣ��ʱ��
		double m_leftTime = 0.0;

		// �󶨵�timer ������
		TimerManager* m_timerMgr = nullptr;
	};
}
