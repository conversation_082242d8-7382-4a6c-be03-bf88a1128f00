/**
* file : SandboxMultiThread
* func : 沙盒多线程
* by : chenzh
*/
#include "platform/SandboxMultiThread.h"
#include "platform/SandboxThreadApi.h"
#include "SandboxGlobalNotify.h"
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include <windows.h>
#include <process.h>
#else
#include <unistd.h>
#if _USE_PTHREAD_
#include <pthread.h>
#endif
#endif


namespace MNSandbox { namespace Thread {


#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	static unsigned WINAPI ThreadPoolRun(void* p)
#else
	static void* ThreadPoolRun(void* p)
#endif
	{
		MultiThread* ins = (MultiThread*)p;
		{
			char threadName[64];
			sprintf(threadName, "Sandbox.Job.%d", ins->GetThreadId() - MultiThreadPool::ms_threadIdOffset);
			SetThreadName(threadName);
		}
		MultiThreadPool* pool = MultiThreadPool::GetSingletonPtr();
		pool->GetThreadJob().ThreadRun();
		return NULL;
	}

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
	static unsigned WINAPI ThreadPrivateRun(void* p)
#else
	static void* ThreadPrivateRun(void* p)
#endif
	{
		MultiPrivateThread* ins = (MultiPrivateThread*)p;
		{
			switch ((USEDTHREAD)(ins->GetThreadId() - 1))
			{
			case USEDTHREAD::LOG:
				SetThreadName("Sandbox.Log");
				break;
			default:
				SetThreadName("Sandbox.Unknown");
				break;
			}
		}
		ins->GetThreadJob().ThreadRun();
		return NULL;
	}

	//////////////////////////////////////////////////////////////////

	ThreadJobTask::ThreadJobTask(TCallback<void*>* cb, void* param, bool needDelete)
		: m_cb(cb), m_param(param)
		, m_needDelFlag(needDelete ? 1 : 0), m_runningFlag(0), m_waitingFlag(0)
	{
	}

	ThreadJobTask::~ThreadJobTask()
	{
		SyncFinish(); // µÈ´ý½áÊø

		if (m_needDelFlag != 0 && m_cb)
			SANDBOX_DELETE(m_cb);

		// »Øµ÷²¢É¾³ý»Øµ÷
		for (auto& v : m_callbackDestruct)
		{
			v->Emit(this);
			SANDBOX_DELETE(v);
		}
		m_callbackDestruct.clear();
	}

	void ThreadJobTask::Exe()
	{
		m_cb->Emit(m_param);
	}

	void ThreadJobTask::SyncFinish()
	{
		while (IsActive()) // µÈ´ý½áÊø
		{
			Thread::Sleep(1);
		}
	}

	void ThreadJobTask::EraseDestructCallback(TCallback<ThreadJobTask*>* cb)
	{
		for (auto iter = m_callbackDestruct.begin(); iter != m_callbackDestruct.end(); ++iter)
		{
			if (cb == *iter)
			{
				SANDBOX_DELETE(cb);
				m_callbackDestruct.erase(iter);
				break;
			}
		}
	}

	//////////////////////////////////////////////////////////////////

	MultiThread::MultiThread(MultiThreadPool* pool, int id)
		: m_pool(pool), m_id(id)
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		m_threadHandle = nullptr;
#elif _USE_PTHREAD_
		m_threadHandle = (void*)SANDBOX_NEW(pthread_t);
#endif
	}

	MultiThread::~MultiThread()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#elif _USE_PTHREAD_
		pthread_t* ptr = (pthread_t*)m_threadHandle;
		SANDBOX_DELETE(ptr);
#endif
	}

	bool MultiThread::CreateThread()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		UInt32 threadid;
		m_threadHandle = (HANDLE)_beginthreadex(nullptr, 0, ThreadPoolRun, (void*)this, 0, &threadid);
		if (!m_threadHandle)
		{
			SANDBOX_ASSERTEX(false, "win32 create thread failed!");
			return false;
	}
#elif _USE_PTHREAD_
		SANDBOX_ASSERT(m_threadHandle);
		int ret = pthread_create((pthread_t*)m_threadHandle, nullptr, ThreadPoolRun, (void*)this);
		if (ret != 0)
		{
			SANDBOX_ASSERTEX(false, "create pthread failed!");
			return false;
		}
#endif
		return true;
	}

	void MultiThread::DestroyThread()
	{
		if (!m_threadHandle)
			return;

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		::WaitForSingleObject((HANDLE)m_threadHandle, INFINITE);
#elif _USE_PTHREAD_
		pthread_join(*(pthread_t*)m_threadHandle, nullptr);
#endif
	}

	//////////////////////////////////////////////////////////////////

	MultiThreadJob::MultiThreadJob()
		: m_stopFlag(0), m_activeFlag(0)
	{}

	void MultiThreadJob::ThreadRun()
	{
        OPTICK_THREAD("MultiThreadJob");
		ThreadJobTask* task = nullptr;
		while (true)
		{
			while (m_activeFlag != 0) // 处理任务
			{
				task = nullptr;
				{
					ThreadAutoLock lock(m_mutex);
					if (m_tasks.empty())
					{
						m_activeFlag = 0; // 不活跃
						break;
					}

					// ÕÒ³öÃ»ÓÐÔÚÔËÐÐµÄ
					for (auto iter = m_tasks.begin(); iter != m_tasks.end(); ++iter)
					{
						if (!(*iter)->IsRunning())
						{
							task = (*iter);
							m_tasks.erase(iter);
							break;
						}
					}
					if (!task)
						break;

					task->SetRunning(true); // ÔËÐÐÖÐ
					task->SetWaiting(false); // 不再等待了
				}

				task->Exe(); // 执行
				{
					ThreadAutoLock lock(m_mutex);
					task->SetRunning(false); // 运行结束
				}
			}

			if (m_stopFlag != 0) // 结束了
				break;
#ifdef DEDICATED_SERVER
			Thread::Sleep(2);
#else
			Thread::Sleep(10);
#endif
		}
	}

	void MultiThreadJob::PushTask(ThreadJobTask* task)
	{
		if (task->IsWaiting()) // 已经在等待队列，不需要继续压任务
			return;

		ThreadAutoLock lock(m_mutex);
		task->SetWaiting(true); // 进入等待列表
		m_tasks.push_back(task);
		m_activeFlag = 1;
	}

	//////////////////////////////////////////////////////////////////

	MultiPrivateThread::MultiPrivateThread(MultiThreadPool* pool, int id)
		: Super(pool, id)
	{
	}

	bool MultiPrivateThread::CreateThread()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		UInt32 threadid;
		m_threadHandle = (HANDLE)_beginthreadex(nullptr, 0, ThreadPrivateRun, (void*)this, 0, &threadid);
		if (!m_threadHandle)
		{
			SANDBOX_ASSERTEX(false, "win32 create thread failed!");
			return false;
		}
#elif _USE_PTHREAD_
		SANDBOX_ASSERT(m_threadHandle);
		int ret = pthread_create((pthread_t*)m_threadHandle, nullptr, ThreadPrivateRun, (void*)this);
		if (ret != 0)
		{
			SANDBOX_ASSERTEX(false, "create pthread failed!");
			return false;
		}
#endif
		return true;
	}

	void MultiPrivateThread::DestroyThread()
	{
		m_job.Stop();
		Super::DestroyThread();
	}

	//////////////////////////////////////////////////////////////////

#if DEDICATED_SERVER
	const int MultiThreadPool::ms_defaultCount = 2;
#else
	const int MultiThreadPool::ms_defaultCount = 6; // 6¸öÏß³Ì
#endif
	const int MultiThreadPool::ms_threadIdOffset = 1000; // 线程id偏移

	MultiThreadPool::MultiThreadPool()
	{
		CreatePrivateThreads((int)USEDTHREAD::MAX); // 私有
		CreateThreads(ms_defaultCount); // 共有
	}

	MultiThreadPool::~MultiThreadPool()
	{
		DestroyThreads();
		DestroyPrivateThreads();
	}

	void MultiThreadPool::CreateThreads(int cnt)
	{
		for (int i = 0; i < cnt; ++i)
		{
			MultiThread* thread = SANDBOX_NEW(MultiThread, this, ms_threadIdOffset + 1 + i);
			thread->CreateThread();
			m_threads.push_back(thread);
		}
	}

	void MultiThreadPool::CreatePrivateThreads(int cnt)
	{
		for (int i = 0; i < cnt; ++i)
		{
			MultiPrivateThread* thread = SANDBOX_NEW(MultiPrivateThread, this, 1 + i);
			thread->CreateThread();
			m_privateThreads.push_back(thread);
		}
	}

	void MultiThreadPool::DestroyThreads()
	{
		m_job.Stop();
		for (auto& v : m_threads)
		{
			v->DestroyThread();
			SANDBOX_DELETE(v);
		}
		m_threads.clear();
	}

	void MultiThreadPool::DestroyPrivateThreads()
	{
		for (auto& v : m_privateThreads)
		{
			v->DestroyThread();
			SANDBOX_DELETE(v);
		}
		m_privateThreads.clear();
	}

	void MultiThreadPool::PushTask(USEDTHREAD usedThread, ThreadJobTask* task)
	{
		int threadidx = (int)usedThread;
		if (threadidx < 0) // 使用默认指派
		{
			PushTask(task);
			return;
		}

		if (threadidx >= m_privateThreads.size())
		{
			SANDBOX_ASSERT(false);
			return;
		}

		m_privateThreads.at(threadidx)->GetThreadJob().PushTask(task);
	}

}}