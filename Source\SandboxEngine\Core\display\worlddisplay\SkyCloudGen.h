//
//#include "OgreThread.h"
//#include "Math/Vector3f.h"
//#include "LegacyOgreColourValue.h"
//
//const int CLOUD_SIZE0 = 32;
//const int CLOUD_OPLEVEL = 6; //6
//const int CLOUD_SHALEVEL = 4; //4
//
//class SkyCloudGen : public MINIW::OSThread //tolua_exports
//{ //tolua_exports
//public:
//	//tolua_begin
//	SkyCloudGen(int lmapsize, int cloudsize);
//	~SkyCloudGen();
//	//tolua_end
//	enum
//	{
//		GEN_WAITCMD = 0,
//		GEN_CMD,
//		GEN_WORKING,
//		GEN_COMPLETE
//	};
//	virtual RUN_RETTYPE _run();
//	//tolua_begin
//	virtual const char* getClassName()
//	{
//		return "SkyCloudGen";
//	}
//	void genCloud();
//	void shaderingCloud(Rainbow::ColorQuad *presult, unsigned char *pnoise, int w, int h);
//	//tolua_end
//public:
//	//tolua_begin
//	int genstate; //0:init, 1:gen,  2:gen ok
//
//	int seed1;
//	int seed2;
//	float t;
//	float m_LastDayTime;
//	Rainbow::Vector3f sunpos;
//	Rainbow::ColourValue suncolor;
//	Rainbow::ColourValue scattercolor;
//	float cloudsharpness;
//	int cloudcover;
//	int lmapsize;
//	int cloudmapsize;
//
//	unsigned char *pshadernoise;
//	unsigned char *plmapbits;
//	unsigned char *popnoise;
//
//	bool fullspeed;
//	//tolua_end
//}; //tolua_exports