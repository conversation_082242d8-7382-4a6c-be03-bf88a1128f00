
#include "SandboxAssetHttpMgr.h"
#include "SandboxGlobalNotify.h"
#include "SandboxAssetBaseHttp.h"
#include "Misc/TimeManager.h"

namespace MNSandbox {
	

	AssetHttpMgr::AssetHttpMgr():m_listenGlobalUpdate(this, &AssetHttpMgr::OnUpdate)
	{
	}
	AssetHttpMgr::~AssetHttpMgr()
	{

	}
	void AssetHttpMgr::Init()
	{

	}

	void AssetHttpMgr::Request(AssetHttpReqType reqType, AutoRef<AssetBaseHttp> http)
	{
		if (!http->IsValid())
		{
			SANDBOX_ASSERTEX(false, "please us NewInstance.");
			return;
		}
		AssetHttpItem item;
		item.state = AssetHttpState::Reqing;
		item.http = http;
		m_reqMap.insert(std::make_pair<>(http->_ID, item));
		http->Excute();

		if (!m_listenGlobalUpdate.GetListener()->IsBindNotify())
		{
			GlobalNotify::GetInstance().m_Update.Subscribe(m_listenGlobalUpdate);
		}
	}

	void AssetHttpMgr::OnUpdate(float f)
	{
		if (m_reqMap.empty())
		{
			m_listenGlobalUpdate.ClearBindNotify();
			return;
		}
		Gc();
	}

	void AssetHttpMgr::SetHttpDirty(unsigned id)
	{
		auto find = m_reqMap.find(id);
		if (find != m_reqMap.end())
		{
			find->second.state = AssetHttpState::Remove;
		}
	}

	void AssetHttpMgr::Gc()
	{
		//auto t1 = Rainbow::GetTimeUS();
		for (auto iter = m_reqMap.begin(); iter != m_reqMap.end();) {
			auto& item = iter->second;
			if (item.state == AssetHttpState::Remove) {
				iter = m_reqMap.erase(iter);
			}
			else {
				iter++;
			}
		}
		//SANDBOX_LOG("AssetHttpMgr::Gc:", std::to_string(Rainbow::GetTimeUS() - t1));
	}
}
