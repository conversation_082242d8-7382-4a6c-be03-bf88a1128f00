<?xml version="1.0" encoding="GBK" standalone="yes" ?>

<metalib name="db" tagsetversion="1" version="2">

	<struct name="AccOption" primarykey="Uin" version="1" >
		<entry name="Uin" type="int" />
		<entry name="AccOptionDetail" type="AccOptionDetail" />
	</struct>
	
	<struct name="MachineConfig"  version="1" >
		<entry name="MachineInnerIP" type="string" size="IP_STR_LEN" />
		<entry name="OWMax" type="smallint" desc="���Ķ�̬��������" />
	</struct>
	
	<struct name="Machine" primarykey="MachineUrl"  version="1" >
		<entry name="MachineUrl" type="string" size="URL_STR_LEN" desc="����URL" />
		<entry name="MachineConfig" type="MachineConfig" extendtotable="true"  />
	</struct>
	
	<macro name="MAX_ROOM"  value="2048" />
	<macro name="ROOM_ONLINE_STR"  value="20480" />
	
	<struct name="OnlineReport"  version="1" >
		<entry name="LogTime" type="datetime" index="0" />
		<entry name="Online" type="uint" />
		<entry name="NoRoomOnline" type="uint" />
		<entry name="RoomOnline" type="string" size="ROOM_ONLINE_STR" />
	</struct>

</metalib>
