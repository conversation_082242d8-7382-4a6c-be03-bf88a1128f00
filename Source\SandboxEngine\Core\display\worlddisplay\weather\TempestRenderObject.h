#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

using namespace Rainbow;
class TempestRenderable;
class TempestMeshData;

class TempestRenderObject : public Rainbow::RenderObject
{
public:
	explicit TempestRenderObject(TempestRenderable* component);
	~TempestRenderObject();

	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;


private:
	TempestRenderable* m_TempestEffect;
	MeshRenderData m_MeshRenderData;
	TempestMeshData* m_RenderBuffer;

	dynamic_array<DrawBuffersRange> m_DrawBuffersRanges;
};

