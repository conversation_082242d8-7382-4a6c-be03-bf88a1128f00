#include "BlizzardRenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Render/RenderUtils.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "Render/RenderScene.h"
#include "Render/VertexLayouts/MeshVertexLayout.h"
#include "display/worlddisplay/weather/BlizzardRenderable.h"

using namespace Rainbow;

BlizzardRenderObject::BlizzardRenderObject(BlizzardRenderable* component): RenderObject(component)
{
	m_MeshRenderData.SetVertexLayout(GetMeshVertexLayout(0));
	m_BlizzardEffect = static_cast<BlizzardRenderable*>(m_Component);
}

BlizzardRenderObject::~BlizzardRenderObject()
{
}

bool BlizzardRenderObject::PrepareRender(const PrimitiveFrameNode& node)
{
	m_BlizzardEffect->GenerateMesh();
	m_RenderBuffer = &m_BlizzardEffect->GetMeshData();
	m_DrawBuffersRanges.resize_uninitialized(0);

	if (m_RenderBuffer->m_BlizzardVertNum == 0) return false;

	size_t vertexStride = m_RenderBuffer->m_VertStride;
	size_t vertexOffset = 0;
	if (m_RenderBuffer->m_BlizzardVertNum > 0) 
	{
		m_DrawBuffersRanges.emplace_back(DrawBuffersRange::CreateIndexed(
			vertexStride, kPrimitiveTriangles, m_RenderBuffer->m_BlizzardVertNum, vertexOffset, 0, 0));
		vertexOffset += m_RenderBuffer->m_BlizzardVertNum * vertexStride;
	}

	GfxDevice& device = GetGfxDevice();
	DynamicVBOBuffer vertexBuffer = DynamicVBOBufferManager::AcquireExclusive(device, kGfxBufferTargetVertex, m_RenderBuffer->m_VBBufferSize);
	DynamicVBOBuffer indexBuffer;// = DynamicVBOBufferManager::AcquireExclusive(device, kGfxBufferTargetIndex, 0);
	m_MeshRenderData.PrepareDynamicVBOBuffer(device, vertexStride, vertexBuffer, indexBuffer, m_RenderBuffer->m_Mask, m_DrawBuffersRanges);
	UploadDynamicVBOBufferOnce(vertexBuffer, m_RenderBuffer->m_VB, m_RenderBuffer->m_VBBufferSize);
	//UploadDynamicVBOBufferOnce(indexBuffer, nullptr, 0);
	return true;
}

void BlizzardRenderObject::ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator)
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Rendering);
	if (m_DrawBuffersRanges.size() == 0) return;
	MeshPrimitiveRenderData& meshPrimitiveData = extractor.AddMeshPrimitiveRenderData(viewNode, allocator);

	meshPrimitiveData.m_MaterialRenderDatas.add(allocator, m_DrawBuffersRanges.size());
	meshPrimitiveData.m_SharedMeshRenderingData = m_MeshRenderData.AcquireSharedMeshRenderingData();
	meshPrimitiveData.m_VertexLayout = m_MeshRenderData.GetVertexLayout();

	for (size_t i = 0; i < m_DrawBuffersRanges.size(); i++) 
	{
		m_RenderBuffer->m_Mats[i]->CollectSharedMaterialDataList(meshPrimitiveData.m_MaterialRenderDatas->m_SharedMaterialDataList);
		auto& meshPrim = extractor.AllocateMeshPrimitive(allocator, 0, i);
		meshPrim.m_DrawBuffersRanges.emplace_back() = m_DrawBuffersRanges[i];
		extractor.AddMeshPrimitive(meshPrim, viewNode);

		//auto& meshPrim = extractor.AllocateMeshPrimitive();
		//meshPrim.m_SharedMeshRenderingData = m_MeshRenderData.GetSharedMeshRenderingData();
		//meshPrim.m_MaterialRenderData = m_RenderBuffer->m_Mats[i]->GetRenderData();
		//meshPrim.m_VertexLayout = m_MeshRenderData.GetVertexLayout();
		//auto& elem = meshPrim.m_DrawBuffersRanges.emplace_back();
		//elem = m_DrawBuffersRanges[i];
		//extractor.AddMeshPrimitive(meshPrim);
	}
}
