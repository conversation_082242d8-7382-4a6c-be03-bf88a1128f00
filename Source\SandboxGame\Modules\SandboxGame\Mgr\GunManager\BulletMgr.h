#ifndef __BulletMgr__
#define __BulletMgr__

#include <string>
#include "OgreHashTable.h"
#include "world_types.h"
#include "proto_common.h"
#include "proto_define.h"
#include "SandboxMgrBase.h"
#include "SandboxGame.h"
#include "OgreRay.h"
namespace Rainbow {
    class EventContent;
	class Vector3f;
	class GameObject;
}

namespace MINIW {
	class WorldRay;
}

struct BulletHolePaintData
{
	Rainbow::Vector3f pos;	//喷漆位置
	WCoord blockPos;	//喷漆方块位置
	int showtime;	//剩余显示时间
	std::string texname;	//喷漆贴图名
	int mapid;				//喷漆所在世界ID
	DirectionType dir;//喷漆面
	DirectionType secondDir;//面向（影响y面）
};

struct BulletHoleActorData
{
	Rainbow::GameObject* obj;
	int showtime;
};

struct BulletEffectData
{
	int effectId;//特效id
	int worldid;//世界id
	float bulletspeed;
	long long timeMs;//时间戳
	Rainbow::Vector3f origin;
	Rainbow::Vector3f dir;
};

class ClientPlayer;
class RenderBlockMaterial;
struct CustomGunDef;

class EXPORT_SANDBOXGAME BulletMgr;
class BulletMgr : public SandboxMgrBase//tolua_exports
{//tolua_exports
public:
	BulletMgr();
	~BulletMgr();
	static BulletMgr* create();
	virtual void onDestroy();
	virtual void onTick();
	virtual void initData();

	void DecalBuletHoleToBlock(ClientPlayer* player, const WCoord& blockPos, const Rainbow::Vector3f& pos, DirectionType dirtype, string buletholetype = "");
	RenderBlockMaterial* GetPaintMtlByBulletHolePaintData(const BulletHolePaintData& paintData, const float** texuv);
	void addBulletholeInfoByHostSync(PB_AddBulletholeInfoHC& msg); //由主机同步过来的弹孔数据
	static void Destroy();
	void removePaintData(const WCoord& blockpos, World* pworld);

	void DecalBuletHoleToActor(IClientActor* actor, Rainbow::Vector3f& pos, Rainbow::Vector3f& normal);
	void DoDecalBuletHoleToActor(IClientActor* actor, Rainbow::Vector3f& pos, Rainbow::Vector3f& normal, int showtime);

	//子弹轨迹特效，主机调用
	void PlayBulletEffect(int worldid, Rainbow::Vector3f& origin, Rainbow::Vector3f& end, const CustomGunDef* def);
	//击中特效，主机调用
	void PlayBulletHitEffect(int worldid, int particleId, float size, Rainbow::Vector3f& point, float yaw, float pitch);
	//更新运行中的子弹轨迹特效
	bool UpdateBulletEffect(BulletEffectData& data, int dt);
	//根据id获取特效路径
	std::string& GetValidEffectPathByParticleId(int particleId);
private:
	void updatePaintBlock(std::vector<WCoord> &needupdateblocks, World *pworld);
	void getTexUV(const float **texuv, DirectionType dirtype, DirectionType extenddirtype);
	void checkBulletHolePainedState();
	void generatePaintRenderMtl(const char* texname);
	void OnBuildSectionMesh(const Rainbow::EventContent* evt);
	
	std::map<std::string, RenderBlockMaterial*> m_PaintMtlMap;	//喷漆的贴图材质

	short m_iCheckPaintedCD;			//检测喷漆状态时间间隔
	Rainbow::Mutex m_Mutex;
	std::list<BulletHolePaintData> m_bulletDatas;//弹孔贴图
	std::unordered_map<long long, std::list<BulletHoleActorData>> m_ObjData;//实体模型的弹孔贴图
	PB_AddBulletholeInfoHC m_bulletHoleMsg;//弹孔待同步的数据

	std::list<BulletEffectData> m_bulletEffectDatas;//弹道轨迹
	std::unordered_map<int, std::string> m_particleMap;//id：path
	std::string m_emptyStr = "";
	float m_showTime = 3.f;//弹孔时间
	float m_bulletFlyTime = 0.1f;//子弹飞行时间
};//tolua_exports


#endif
