/*
*	file: SceneEffectManager
*	func: 在世界场景中绘制管理器
*	by: chenzh
*	time: 2021.1.22
*/
#include "SceneEffectManager.h"
#include "SceneEffectLine.h"
#include "SceneEffectCircle.h"
#include "SceneEffectRectangle.h"
#include "SceneEffectEllipse.h"
#include "SceneEffectArrow.h"
#include "SceneEffectBox.h"
#include "SceneEffectSelectFrame.h"
#include "SceneEffectSphere.h"
#include "SceneEffectSphereFrame.h"
#include "SceneEffectHemisphereFrame.h"
#include "SceneEffectConeFrame.h"
#include "SceneEffectDonutFrame.h"
#include "SceneEffectSpotLightFrame.h"
#include "SandboxMacros.h"
#include "Optick/optick.h"

using namespace MNSandbox;
using namespace Rainbow;

int SceneEffectManager::ms_SceneEffectId = 1;

SceneEffectManager::SceneEffectManager(World* pWorld)
	: m_pWorld(pWorld)
{
}

SceneEffectManager::~SceneEffectManager()
{
	for (auto iter = m_mapIdToEffect.begin(); iter != m_mapIdToEffect.end(); iter++)
	{
		SANDBOX_DELETE(iter->second);
	}
	m_mapIdToEffect.clear();
}

void SceneEffectManager::update(float dtime)
{
	OPTICK_EVENT();
	OnDraw();
}

SceneEffectID SceneEffectManager::CreateSceneEffectLine(const WCoord& startpos, const WCoord& endpos, int stroke, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectLine, startpos, endpos, stroke, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectLine()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectLine));
}

SceneEffectID SceneEffectManager::CreateSceneEffectCircle(const MNCoord3f& center, float radius, float ratio, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectCircle, center, radius, ratio, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectCircle()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectCircle));
}

SceneEffectID SceneEffectManager::CreateSceneEffectRectangle()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectRectangle));
}

SceneEffectID SceneEffectManager::CreateSceneEffectEllipse()
{
	auto effect = SANDBOX_NEW(SceneEffectEllipse);
	effect->SetRotationAxis(Vector3f::neg_zAxis, Vector3f::neg_zAxis);
	return SpawnEffect(effect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectArrow(MNCoord3f arrowPos, MNCoord3f arrowDir, DirectionType dir, float ratio, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectArrow, arrowPos, arrowDir, dir, ratio, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectArrow()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectArrow));
}

SceneEffectID SceneEffectManager::CreateSceneEffectBox(const WCoord& startpos, const WCoord& endpos, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectBox, startpos, endpos, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectBox()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectBox));
}

SceneEffectID SceneEffectManager::CreateSceneEffectFrame(const WCoord& startpos, const WCoord& endpos, 
	int stroke, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectFrame, startpos, endpos, stroke, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectSelectFrame(const WCoord& startpos, const WCoord& endpos, int stroke, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectSelectFrame, startpos, endpos, stroke, mtltype);
	if (refreshFlag)
	{
		pSceneEffect->Refresh();
	}

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectSelectFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectSelectFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectSphere(const MNCoord3f& pos, 
	const MNCoord3f& sphereDir, float radius, int accuracyGrade, float ratio, CURVEFACEMTLTYPE mtltype, bool refreshFlag)
{
	auto pSceneEffect = SANDBOX_NEW(SceneEffectSphere, pos, sphereDir, radius, accuracyGrade, ratio, mtltype);
	if (refreshFlag)
		pSceneEffect->Refresh();

	return SpawnEffect(pSceneEffect);
}

SceneEffectID SceneEffectManager::CreateSceneEffectSphere()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectSphere));
}

SceneEffectID SceneEffectManager::CreateSceneEffectSphereFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectSphereFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectHemisphereFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectHemisphereFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectConeFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectConeFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectDonutFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectDonutFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffectSpotLightFrame()
{
	return SpawnEffect(SANDBOX_NEW(SceneEffectSpotLightFrame));
}

SceneEffectID SceneEffectManager::CreateSceneEffect(const SceneEffectShape& eSes)
{
	SceneEffectID eid;
	switch (eSes)
	{
		case SceneEffectShape::EDGE:
			eid = CreateSceneEffectLine();
			break;
		case SceneEffectShape::ELLIPSE:
			eid = CreateSceneEffectEllipse();
			break;
		case SceneEffectShape::RECTANGLE:
			eid = CreateSceneEffectRectangle();
			break;
		case SceneEffectShape::BOX:
			eid = CreateSceneEffectBox();
			break;
		default:
		case SceneEffectShape::BOX_FRAME:
			eid = CreateSceneEffectFrame();
			break;
		case SceneEffectShape::SPHERE:
			eid = CreateSceneEffectSphere();
			break;
		case SceneEffectShape::SPHERE_FRAME:
			eid = CreateSceneEffectSphereFrame();
			break;
		case SceneEffectShape::HEMISPHERE_FRAME:
			eid = CreateSceneEffectHemisphereFrame();
			break;
		case SceneEffectShape::CONE_FRAME:
			eid = CreateSceneEffectConeFrame();
			break;
		case SceneEffectShape::DONUT_FRAME:
			eid = CreateSceneEffectDonutFrame();
			break;
		case SceneEffectShape::SPOT_LIGHT_FRAME:
			eid = CreateSceneEffectSpotLightFrame();
			break;
	}
	SceneEffectGeom* effect = m_mapIdToEffect[eid];
	if (effect)
	{
		effect->SetEffectShape(eSes);
	}
	return eid;
}

void SceneEffectManager::DestroySceneEffect(SceneEffectID id)
{
	auto iter = m_mapIdToEffect.find(id);
	if (iter != m_mapIdToEffect.end())
	{
		SANDBOX_DELETE(iter->second);
		m_mapIdToEffect.erase(iter);
	}
}

SceneEffectID SceneEffectManager::SpawnEffect(SceneEffectGeom* effect)
{
	SceneEffectID curId = ms_SceneEffectId++;
	m_mapIdToEffect.insert(std::make_pair(curId, effect));
	return curId;
}

void SceneEffectManager::MoveSceneEffect(SceneEffectID id, const WCoord& topos)
{
	// todo...
}

void SceneEffectManager::OnDraw()
{
	OPTICK_EVENT();
	int draw = 0;
	for (auto iter = m_mapIdToEffect.begin(); iter != m_mapIdToEffect.end(); iter++)
	{
		auto pSceneEffect = iter->second;
		if (pSceneEffect->IsActive(m_pWorld))
		{
			draw++;
			pSceneEffect->OnDraw(m_pWorld);
		}
	}
	OPTICK_TAG("draw", draw);
}