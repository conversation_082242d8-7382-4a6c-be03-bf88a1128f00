#include "WeatherPartBase.h"
#include "world.h"

LegacyWeatherPartBase::LegacyWeatherPartBase(World* pworld) :m_WeatherID(SANDDUSTSTORM_WEATHER), m_pWorld(pworld), m_PreStrength(0), m_Strength(0), m_UpdateTime(0), m_IdleTick(0), m_IsCome(false)
{
}

LegacyWeatherPartBase::~LegacyWeatherPartBase()
{

}

WeatherPartBase::WeatherPartBase(World *pworld) :WeatherBase(pworld),m_IdleTick(0),m_IsCome(false)
{
	m_WeatherID = SANDDUSTSTORM_WEATHER;
}

WeatherPartBase::~WeatherPartBase()
{

}
