#pragma once

#ifdef BUILD_SANDBOX_DLL

#if PLATFORM_WIN || PLATFORM_WINRT

#if defined(BUILD_SANDBOXDRIVER_DLL)
#define EXPORT_SANDBOXDRIVERMODULE __declspec(dllexport)
#else
#define EXPORT_SANDBOXDRIVERMODULE __declspec(dllimport)
#endif

#elif PLATFORM_OSX

#if defined(BUILD_SANDBOXDRIVER_DLL)
#define EXPORT_SANDBOXDRIVERMODULE __attribute__((visibility("default")))
#else
#define EXPORT_SANDBOXDRIVERMODULE
#endif

#else

#if defined(BUILD_SANDBOXDRIVER_DLL)
#define EXPORT_SANDBOXDRIVERMODULE  __attribute__((visibility("default")))
#else
#define EXPORT_SANDBOXDRIVERMODULE
#endif

#endif

#else

#define EXPORT_SANDBOXDRIVERMODULE  

#endif // BUILDING_DYNAMICLIB