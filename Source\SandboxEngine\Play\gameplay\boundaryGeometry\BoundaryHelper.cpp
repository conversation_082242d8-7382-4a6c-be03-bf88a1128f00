#include "BoundaryHelper.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "world.h"

static const int vertsNum = 8;

void BoundaryHelper::drawLine(const Rainbow::Vector3f& point1, const Rainbow::Vector3f& point2, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	//我们先求出2点之间的向量
	//我们认为这个是x向量
	Rainbow::Vector3f localX = point2 - point1;
	Rainbow::Vector3f normalX;
	Rainbow::Vector3f normalY;
	Rainbow::Vector3f normalZ;
	if (localX.x <= FLT_MIN && localX.z <= FLT_MIN && localX.y > FLT_MIN)
	{
		normalY = Rainbow::Vector3f::yAxis;
		normalX = Rainbow::Vector3f::xAxis;
		normalZ = Rainbow::Vector3f::zAxis;
	}
	else
	{
		normalX = localX;
		normalX.NormalizeSafe();
		//我们整个y向量
		normalY.x = -normalX.z;
		normalY.y = 0;
		normalY.z = normalX.x;
		//整个z向量
		normalZ = Rainbow::Vector3f::Cross(normalX, normalY);
		normalZ.NormalizeSafe();
	}
	//我们只求周围点,所以不用管轴方向
	////我们需要一个世界矩阵
	//Rainbow::Matrix4x4f worldTrans;
	//worldTrans.SetIdentity();
	//worldTrans.SetPosition(-point);
	////模型矩阵
	//Rainbow::Matrix3x3f model;
	//model.SetIdentity();
	//model.SetBasisTransposed(normalX, normalY, normalZ);;
	//
	//Rainbow::Vector3f localPoint2 = model.MultiplyVector3(worldTrans.MultiplyVector3(point2));

	//我们找到要绘制的8个点
	//0: x0y0z0, //1: x1y0z0, //2: x1y0z1  //3: x0y0z1//4: x0y1z0//5: x1y1z0//6: x1y1z1//7: x0y1z1
	float range = lineWidth;
	Rainbow::Vector3f renderPoint[vertsNum];
	renderPoint[0] = point1 - normalY * range - normalZ * range;
	renderPoint[1] = point2 - normalY * range - normalZ * range;
	renderPoint[2] = point2 - normalY * range + normalZ * range;
	renderPoint[3] = point1 - normalY * range + normalZ * range;
	renderPoint[4] = point1 + normalY * range - normalZ * range;
	renderPoint[5] = point2 + normalY * range - normalZ * range;
	renderPoint[6] = point2 + normalY * range + normalZ * range;
	renderPoint[7] = point1 + normalY * range + normalZ * range;
	std::vector<BlockGeomVert> vert1;
	vert1.resize(8);
	for (int j = 0; j < vertsNum; j++)
	{
		auto& vert = vert1[j];
		vert.pos.w = (0x7f << 8) | 0x7f;
		vert.color = BlockColor(255, 255, 255, 255);
		vert.normal = BlockVector(127, 127, 127, 127);// Vector3f::zero;
		vert.uv.x = 0;
		vert.uv.y = 0;
		vert.pos.x = renderPoint[j].x;
		vert.pos.y = renderPoint[j].y;
		vert.pos.z = renderPoint[j].z;
	}
	std::vector<unsigned short>indices;
	indices.reserve(36);
	indices.push_back(2);
	indices.push_back(1);
	indices.push_back(0);
	indices.push_back(3);
	indices.push_back(2);
	indices.push_back(1);

	indices.push_back(7);
	indices.push_back(5);
	indices.push_back(4);
	indices.push_back(7);
	indices.push_back(6);
	indices.push_back(5);

	indices.push_back(1);
	indices.push_back(4);
	indices.push_back(0);
	indices.push_back(1);
	indices.push_back(5);
	indices.push_back(4);

	indices.push_back(2);
	indices.push_back(5);
	indices.push_back(1);
	indices.push_back(2);
	indices.push_back(6);
	indices.push_back(5);

	indices.push_back(2);
	indices.push_back(7);
	indices.push_back(3);
	indices.push_back(2);
	indices.push_back(6);
	indices.push_back(7);

	indices.push_back(7);
	indices.push_back(4);
	indices.push_back(0);
	indices.push_back(3);
	indices.push_back(7);
	indices.push_back(4);
	pworld->getRender()->getCurveRender()->addRect(color, Rainbow::Vector3f(0, 0, 0), vert1, indices);
}

static void _drawAABBBox(World* const pworld, const BoundaryBoxGeometry& box, BoundaryGeDrawColor color, int lineWidth)
{
	if (!(pworld && pworld->getRender() && pworld->getRender()->getCurveRender()))
	{
		return;
	}
	std::vector<BlockGeomVert>verts1;
	verts1.resize(vertsNum);
	std::vector<unsigned short>indices;
	indices.reserve(36);
	for (auto& vert : verts1)
	{
		vert.pos.w = (0x7f << 8) | 0x7f;
		vert.color = BlockColor(255, 255, 255, 255);
		vert.normal = BlockVector(127, 127, 127, 127);// Vector3f::zero;
		vert.uv.x = 0;
		vert.uv.y = 0;
	}
	int extendRange[vertsNum][3] = { {-1, -1, -1}, {-1, -1, 1}, {1, -1, 1}, {1, -1, -1},
									 {-1,  1, -1}, {-1,  1, 1}, {1,  1, 1}, {1,  1, -1} };
	//我们以中心点作为origin, 获取8个顶点
	//0: x0y0z0, //1: x1y0z0, //2: x1y0z1  //3: x0y0z1//4: x0y1z0//5: x1y1z0//6: x1y1z1//7: x0y1z1
	WCoord origin = box.centerAABB();
	float halfx = box.getDimX() * 1.0f / 2;
	float halfz = box.getDimZ() * 1.0f / 2;
	float halfy = box.getDimY() * 1.0f / 2;
	for (int i = 0; i < vertsNum; i++)
	{
		auto& point = verts1[i];
		point.pos.x = extendRange[i][0] * halfx;
		point.pos.y = extendRange[i][1] * halfy;
		point.pos.z = extendRange[i][2] * halfz;
	}
	//现在绘制立方体的边, 8个小立方体
	//每个点扩充到8个点,我们把这个点作为中心点
	std::vector<BlockGeomVert>verts2;
	verts2.resize(vertsNum * 8);
	float range = lineWidth;
	for (int i = 0; i < vertsNum; i++)
	{
		auto& center = verts1[i];
		for (int j = 0; j < vertsNum; j++)
		{
			auto& vert = verts2[i * 8 + j];
			vert.pos.w = (0x7f << 8) | 0x7f;
			vert.color = BlockColor(255, 255, 255, 255);
			vert.normal = BlockVector(127, 127, 127, 127);// Vector3f::zero;
			vert.uv.x = 0;
			vert.uv.y = 0;
			vert.pos.x = extendRange[j][0] * range + center.pos.x;
			vert.pos.y = extendRange[j][1] * range + center.pos.y;
			vert.pos.z = extendRange[j][2] * range + center.pos.z;
		}
	}
	{
		int baseIndex = 0;
		int baseIndex2 = 0;
		int d0 = baseIndex;
		int d1 = baseIndex + 1;
		int d2 = baseIndex + 2;
		int d3 = baseIndex + 3;
		int d4 = baseIndex2;
		int d5 = baseIndex2 + 1;
		int d6 = baseIndex2 + 2;
		int d7 = baseIndex2 + 3;
		auto drawOutLine = [&]()
		{
			indices.push_back(d0);
			indices.push_back(d1);
			indices.push_back(d2);
			indices.push_back(d1);
			indices.push_back(d2);
			indices.push_back(d3);

			indices.push_back(d4);
			indices.push_back(d5);
			indices.push_back(d7);
			indices.push_back(d5);
			indices.push_back(d6);
			indices.push_back(d7);

			indices.push_back(d0);
			indices.push_back(d4);
			indices.push_back(d1);
			indices.push_back(d4);
			indices.push_back(d5);
			indices.push_back(d1);

			indices.push_back(d1);
			indices.push_back(d5);
			indices.push_back(d2);
			indices.push_back(d5);
			indices.push_back(d6);
			indices.push_back(d2);

			indices.push_back(d3);
			indices.push_back(d7);
			indices.push_back(d2);
			indices.push_back(d7);
			indices.push_back(d6);
			indices.push_back(d2);

			indices.push_back(d0);
			indices.push_back(d4);
			indices.push_back(d7);
			indices.push_back(d4);
			indices.push_back(d7);
			indices.push_back(d3);
		};
		//四个y轴方向的
		{
			int need[4] = { 0, 1, 2, 3 };
			for (int i = 0; i < 4; i++)
			{
				int cur = need[i];
				baseIndex = cur * 8;
				baseIndex2 = (cur + 4) * 8;
				d0 = baseIndex;
				d1 = baseIndex + 1;
				d2 = baseIndex + 2;
				d3 = baseIndex + 3;
				d4 = baseIndex2 + 4;
				d5 = baseIndex2 + 5;
				d6 = baseIndex2 + 6;
				d7 = baseIndex2 + 7;
				drawOutLine();
			}
		}
		//四个z轴的
		{
			int need[4] = { 0, 3, 4, 7 };
			for (int i = 0; i < 4; i++)
			{
				int cur = need[i];
				baseIndex = cur * 8;
				if (cur % 2 == 1) baseIndex2 = (cur - 1) * 8;
				else baseIndex2 = (cur + 1) * 8;
				d0 = baseIndex;
				d1 = baseIndex2 + 1;
				d2 = baseIndex2 + 2;
				d3 = baseIndex + 3;
				d4 = baseIndex + 4;
				d5 = baseIndex2 + 5;
				d6 = baseIndex2 + 6;
				d7 = baseIndex + 7;
				drawOutLine();
			}
		}
		//四个x轴的
		{
			int need[4] = { 0, 1, 4, 5 };
			for (int i = 0; i < 4; i++)
			{
				int cur = need[i];
				baseIndex = cur * 8;
				if (cur % 2 == 0) baseIndex2 = (cur + 3) * 8;
				else baseIndex2 = (cur + 1) * 8;
				d0 = baseIndex;
				d1 = baseIndex + 1;
				d2 = baseIndex2 + 2;
				d3 = baseIndex2 + 3;
				d4 = baseIndex + 4;
				d5 = baseIndex + 5;
				d6 = baseIndex2 + 6;
				d7 = baseIndex2 + 7;
				drawOutLine();
			}
		}

	}
	pworld->getRender()->getCurveRender()->addRect(color, origin, verts2, indices);
}

void drawAABBBox(const BoundaryBoxGeometry& box, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	_drawAABBBox(pworld, box, color, lineWidth);
}

void drawOBBBox(const BoundaryBoxGeometry& box, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	BoundaryHelper::drawLine(box.x0y0z0(), box.x0y0z1(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x0y0z1(), box.x1y0z1(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y0z1(), box.x1y0z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y0z0(), box.x0y0z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x0y1z0(), box.x0y1z1(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x0y1z1(), box.x1y1z1(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y1z1(), box.x1y1z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y1z0(), box.x0y1z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x0y0z0(), box.x0y1z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y0z0(), box.x1y1z0(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x1y0z1(), box.x1y1z1(), pworld, color, lineWidth);
	BoundaryHelper::drawLine(box.x0y0z1(), box.x0y1z1(), pworld, color, lineWidth);
}

#define CIRCLE_ANGLE_NUM 36
void BoundaryHelper::drawCircle2D(World* pworld, int r, const Rainbow::Matrix4x4f& localToWorld, BoundaryGeDrawColor color, int lineWidth)
{
	std::vector<Rainbow::Vector3f> posGather;
	posGather.reserve(CIRCLE_ANGLE_NUM);
	//360度, 我们画36个点
	for (int angle = 0, step = (360 / CIRCLE_ANGLE_NUM); angle < 360; angle += step)
	{
		Rainbow::Vector3f pos;
		pos.y = 0;
		pos.x = 1 * Rainbow::CosByAngle(angle);
		pos.y = 1 * Rainbow::SinByAngle(angle);
		pos = localToWorld.MultiplyPoint3(angle);
		posGather.push_back(pos);
	}
	for (int index = 1; index < CIRCLE_ANGLE_NUM; index++)
	{
		drawLine(posGather[index - 1], posGather[index], pworld, color, lineWidth);
	}
}

void BoundaryHelper::drawBox(const BoundaryBoxGeometry& box, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	if (box.isAABB())
	{
		drawAABBBox(box, pworld, color, lineWidth);
	}
	else
	{
		drawOBBBox(box, pworld, color, lineWidth);
	}
}

void BoundaryHelper::drawSphere(const BoundarySphereGeometry& sphere, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	//我们画三个圆
	Rainbow::Matrix4x4f mat = Rainbow::Matrix4x4f::identity;
	mat.SetPosition(sphere.center());
	//xz
	{
		drawCircle2D(pworld, sphere.radius(), mat, color, lineWidth);
	}
	//xy
	{
		drawCircle2D(pworld, sphere.radius(), mat * Rainbow::Matrix4RotateX(90), color, lineWidth);
	}
	//zy
	{
		drawCircle2D(pworld, sphere.radius(), mat * Rainbow::Matrix4RotateZ(90), color, lineWidth);
	}
}

void BoundaryHelper::drawCylinder(const BoundaryCylinderGeometry& cylinder, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	Rainbow::Matrix4x4f mat;
	mat.SetPosition(cylinder.center());
	mat = mat * cylinder.getRotate();
	//先画上下2个圆
	{
		Rainbow::Matrix4x4f offset;
		offset.SetPosition(Rainbow::Vector3f(0, 0, cylinder.height() / 2.0f));
		drawCircle2D(pworld, cylinder.radius(), mat* offset, color, lineWidth);
	}
	{
		Rainbow::Matrix4x4f offset;
		offset.SetPosition(Rainbow::Vector3f(0, 0, -cylinder.height() / 2.0f));
		drawCircle2D(pworld, cylinder.radius(), mat * offset, color, lineWidth);
	}
	//在画四条线
	float x = cylinder.radius();
	float y = cylinder.height() / 2.0f;
	//+x
	{
		Rainbow::Vector3f point1(x, y, 0);
		Rainbow::Vector3f point2(x, -y, 0);
		drawLine(mat.MultiplyPoint3(point1), mat.MultiplyPoint3(point2), pworld, color, lineWidth);
	}
	//+z
	{
		Rainbow::Vector3f point1(0, y, x);
		Rainbow::Vector3f point2(0, -y, x);
		drawLine(mat.MultiplyPoint3(point1), mat.MultiplyPoint3(point2), pworld, color);
	}
	//-x
	{
		Rainbow::Vector3f point1(-x, y, 0);
		Rainbow::Vector3f point2(-x, -y, 0);
		drawLine(mat.MultiplyPoint3(point1), mat.MultiplyPoint3(point2), pworld, color, lineWidth);
	}
	//-z
	{
		Rainbow::Vector3f point1(0, y, -x);
		Rainbow::Vector3f point2(0, -y, -x);
		drawLine(mat.MultiplyPoint3(point1), mat.MultiplyPoint3(point2), pworld, color, lineWidth);
	}
}

void BoundaryHelper::drawGeometry(const BoundaryGeometryHolder& holder, World* pworld, BoundaryGeDrawColor color, int lineWidth)
{
	switch (holder.getType())
	{
	case BoundaryGeometryType::eBOX:
		drawBox(holder.box(), pworld, color, lineWidth);
		break;
	case BoundaryGeometryType::eSPHERE:
		drawSphere(holder.sphere(), pworld, color, lineWidth);
		break;
	case BoundaryGeometryType::eCylinder:
		drawCylinder(holder.cylinder(), pworld, color, lineWidth);
		break;
	default:
		break;
	}
}