
#ifndef __BLOCKBASIC_H__
#define __BLOCKBASIC_H__

#include "BlockMaterial.h"
#include "SandboxEngine.h"
namespace Rainbow {
    class Texture;
}

class EXPORT_SANDBOXENGINE BasicBlockMaterial;
class BasicBlockMaterial : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BasicBlockMaterial)
public:
	virtual void initDefaultMtl() override;
	//tolua_begin
	BasicBlockMaterial();
	virtual ~BasicBlockMaterial();

	virtual void init(int resid);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	//int getBlockGeomID(int *idbuf, int *dirbuf, const SharedSectionData *sectionData, const WCoord &blockpos, World* world);
	//int getProtoBlockGeomID(int *idbuf, int *dirbuf);
	//tolua_end
private:
	virtual void initDrawType() override;
protected:
	virtual int getTextureType();
	//virtual BlockDrawType getDrawType();

	//RenderBlockMaterial *m_Mtl;

//#ifdef IWORLD_EXPOBJ_TOOLS
	virtual RenderBlockMaterial *getFaceMtl(const BiomeDef *biome, DirectionType dir, int blockdata, BlockColor &facecolor) override;

	//std::map<unsigned int, RenderBlockMaterial *>m_ColoredMtls;
	Rainbow::SharePtr<Rainbow::Texture2D> m_BaseTex;
//#endif
private:
	std::map<unsigned int, unsigned int>m_coloredMtlsIndex;
}; //tolua_exports

class EXPORT_SANDBOXENGINE BlockPowerBasic : public BasicBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockPowerBasic)
public:


	virtual void init(int resid) override;

	//tolua_begin
	//virtual bool canProvidePower()
	//{
	//	return true;
	//}

	virtual int outputWeakEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
	{
		return 15;
	}

	virtual int outputStrongEnergy(World *pworld, const WCoord &blockpos, const WCoord &blockrelativepos)
	{
		return 15;
	}

	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);

	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	//tolua_end
}; //tolua_exports

#endif
