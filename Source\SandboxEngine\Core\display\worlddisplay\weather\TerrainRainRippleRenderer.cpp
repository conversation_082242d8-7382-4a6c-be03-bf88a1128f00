#include "TerrainRainRippleRenderer.h"
#include "TerrainRainRippleRenderObject.h"
#include "Misc/FrameTempMemoryManager.h"
#include "blocks/BlockMaterialMgr.h"
#include "SandboxIdDef.h"
#include "WorldManager.h"

using namespace Rainbow;
using namespace MINIW;

//bool TerrainRainRippleRenderer::Open = true;
inline bool CanGenerateRippleMesh(int blockid) 
{
	// return blockid == BLOCK_GRASS ||
	// 	blockid == BLOCK_DIRT ||
	// 	blockid == BLOCK_FARMLAND ||
	// 	blockid == BLOCK_BURYLAND ||
	// 	blockid == BLOCK_STONE ||
	// 	blockid == BLOCK_LICHEN_STONE ||
	// 	blockid == BLOCK_SAND ||
	// 	blockid == BLOCK_GRAVEL ||
	// 	blockid == BLOCK_SANDSTONE ||
	// 	blockid == BLOCK_SAND ||
	// 	blockid == BLOCK_STILL_WATER ||
	// 	blockid == 247||
	auto material = g_BlockMtlMgr.getMaterial(blockid);
	return (material != nullptr && material->isOpaqueCube()) || blockid == BLOCK_STILL_WATER;
}


RainRippleMeshData::RainRippleMeshData()
	: m_VB(nullptr)
	, m_IB(nullptr)
	, m_VertNum(0)
	, m_VBBufferSize(0)
	, m_VertStride(0)
	, m_Mask(kShaderChannelMaskVertex | kShaderChannelMaskTexCoord0)
{
}

IMPLEMENT_CLASS_GLOBAL(TerrainRainRippleRenderer, kRuntimeClassFlagAbstract)
TerrainRainRippleRenderer* TerrainRainRippleRenderer::Create(Rainbow::Camera* sceneCamera, World* pworld)
{
	Rainbow::GameObject* obj = Rainbow::GameObject::Create();
	return obj->CreateComponent<TerrainRainRippleRenderer>(sceneCamera, pworld);
}

TerrainRainRippleRenderer::TerrainRainRippleRenderer(Rainbow::Camera* sceneCamera, World* pworld)
	: Super(kMemDefault),
	m_World(pworld),
	m_SceneCamera(sceneCamera)
{

	m_PrimitiveRenderData.m_CastShadow = false;
	m_PrimitiveRenderData.m_ReceiveShadow = false;
	m_PrimitiveRenderData.m_IsAffectedByFog = false;
	m_PrimitiveRenderData.m_IsAffectedByLight = false;

	MaterialManager& materialManager = GetMaterialManager();
	SharePtr<Texture2D> ptex = GetAssetManager().LoadAsset<Texture2D>("particles/texture/rain_ripple.png");
	m_Mat = MoveToSharePtr(materialManager.LoadFromFile("Materials/MiniGame/LegacyParticle/rain_ripple.templatemat")->CreateInstance());
	m_Mat->SetTexture(ShaderParamNames::_MainTex, ptex.Get(), Rainbow::InlineSamplerType::bilinear_repeat_sampler);

	m_WorldBounds = AABB(Vector3f::zero, Vector3f(std::numeric_limits<float>::max(), std::numeric_limits<float>::max(), std::numeric_limits<float>::max()));

}

TerrainRainRippleRenderer::~TerrainRainRippleRenderer()
{
}


RainRippleMeshData& TerrainRainRippleRenderer::GetMeshData()
{
	if (m_Vertexes.size() > 0)
	{
		m_MeshData.m_VertNum = m_Vertexes.size();
		m_MeshData.m_VertStride = sizeof(RainPippleVertex);
		m_MeshData.m_IndexNum = m_Indices.size();

		//copy vertex
		m_MeshData.m_VBBufferSize = (m_MeshData.m_VertNum) * m_MeshData.m_VertStride;
		m_MeshData.m_VB = GetFrameTempMemoryManager().Alloc(m_MeshData.m_VBBufferSize, EFrameTempMemoryType::FTMT_Render);
		RainPippleVertex* pVert = (RainPippleVertex*)m_MeshData.m_VB;
		memcpy(pVert, m_Vertexes.data(), m_MeshData.m_VBBufferSize);
		//copy index
		m_MeshData.m_IBBuffserSize = m_Indices.size() * sizeof(UInt16);
		m_MeshData.m_IB = GetFrameTempMemoryManager().Alloc(m_MeshData.m_IBBuffserSize, EFrameTempMemoryType::FTMT_Render);
		UInt16* pIndex = (UInt16*)m_MeshData.m_IB;
		memcpy(pIndex, m_Indices.data(), m_MeshData.m_IBBuffserSize);
	}
	else
	{
		m_MeshData.m_VertNum = 0;
		m_MeshData.m_VB = nullptr;
		m_MeshData.m_IndexNum = 0;
		m_MeshData.m_IB = nullptr;
	}
	return m_MeshData;
}

void TerrainRainRippleRenderer::OnTick(const WCoord& center)
{
	//if (!Open) return;
	float radius = 10;
	WCoord blockpos = center;
	int minx = center.x - radius;
	int maxx = center.x + radius;
	int minz = center.z - radius;
	int maxz = center.z + radius;

	WCoord curPos = blockpos;

	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			curPos.x = x;
			curPos.z = z;

			for (int offsety = radius; offsety >= -radius; offsety--)
			{
				curPos.y = blockpos.y + offsety;
				if (curPos.y < 0) continue;
				int blockid = m_World->getBlockID(curPos);
				if (blockid != 0)
				{
					if (CanGenerateRippleMesh(blockid)) 
					{
						AddMesh(TopCoord(curPos));
					}
					break;
				}
			}
		}
	}
}

void TerrainRainRippleRenderer::ResetMesh()
{
	m_Vertexes.resize_uninitialized(0);
	m_Indices.resize_uninitialized(0);
}

void TerrainRainRippleRenderer::AddMesh(const WCoord& origin)
{

	if (g_WorldMgr == NULL) return;
	if (m_World == NULL) return;
	if (m_World->m_Environ == NULL) return;
	if (m_World->getWeatherMgr() == NULL) return;

	WCoord p1 = WCoord(origin.x * BLOCK_SIZE, origin.y * BLOCK_SIZE, origin.z * BLOCK_SIZE);
	WCoord p2 = p1 + WCoord(0, 0, BLOCK_SIZE);
	WCoord p3 = p1 + WCoord(BLOCK_SIZE, 0, BLOCK_SIZE);
	WCoord p4 = p1 + WCoord(BLOCK_SIZE, 0, 0);
	float yOffset = 1.0f;

	RainPippleVertex v1;
	v1.pos.x = p1.x;
	v1.pos.y = p1.y + yOffset;
	v1.pos.z = p1.z;
	v1.uv = Vector2f(0, 0);

	RainPippleVertex v2;
	v2.pos.x = p2.x;
	v2.pos.y = p2.y + yOffset;
	v2.pos.z = p2.z;
	v2.uv = Vector2f(0, 1);

	RainPippleVertex v3;
	v3.pos.x = p3.x;
	v3.pos.y = p3.y + yOffset;
	v3.pos.z = p3.z;
	v3.uv = Vector2f(1, 1);

	RainPippleVertex v4;
	v4.pos.x = p4.x;
	v4.pos.y = p4.y + yOffset;
	v4.pos.z = p4.z;
	v4.uv = Vector2f(1, 0);

	int startIndex = m_Vertexes.size();

	m_Vertexes.push_back(v1);
	m_Vertexes.push_back(v2);
	m_Vertexes.push_back(v3);
	m_Vertexes.push_back(v4);

	m_Indices.push_back(startIndex + 0);
	m_Indices.push_back(startIndex + 1);
	m_Indices.push_back(startIndex + 2);

	m_Indices.push_back(startIndex + 0);
	m_Indices.push_back(startIndex + 2);
	m_Indices.push_back(startIndex + 3);

	//Vector3f min = Minimize(v1.pos, m_LocalBounds.CalculateMin());
	//Vector3f max = Maximize(v3.pos, m_LocalBounds.CalculateMax());
	//m_LocalBounds.FromMinMax(min, max);
}

Rainbow::RenderObject* TerrainRainRippleRenderer::CreateSceneObject()
{
	RenderObject* renderObj = ENG_NEW_LABEL(TerrainRainRippleRenderObject, kMemObject)(this);
	return renderObj;
}

void TerrainRainRippleRenderer::UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld)
{
}
