#pragma once
/**
* file : SandboxRef
* func : 沙盒基类，引用次数
* by ; chenzihang
*/
#include "SandboxType.h"
#include "SandboxFlags.h"
#include "SandboxRuntimeClass.h"
#include "SandboxLinkerRef.h"



namespace MNSandbox { //tolua_export


	class EXPORT_SANDBOXDRIVERMODULE Ref;
	class Ref : MNSandbox::noncopyable //tolua_export
	{ //tolua_export
	private:
		static const ::MNSandbox::RuntimeClass m_RTTI;
	public:
		typedef Ref ThisType;
		static const ::MNSandbox::RuntimeClass* RTTI() { return &m_RTTI; }
		virtual const ::MNSandbox::RuntimeClass* GetRTTI() const { return &m_RTTI; }
		static const std::string& ClassType() { return m_RTTI.GetType(); }
		const std::string& GetClassType() const { return GetRTTI()->GetType(); }
	public:
		template<typename T>
		inline T* ToCast()
		{
			return IsKindOf<T>() ? static_cast<T*>(this) : nullptr;
		}
		template<typename T>
		inline const T* ToCast() const
		{
			return IsKindOf<T>() ? static_cast<const T*>(this) : nullptr;
		}
		template<typename T>
		inline T* StaticToCast()
		{
			SANDBOX_ASSERT(IsKindOf<T>());
			return static_cast<T*>(this);
		}
		template<typename T>
		inline const T* StaticToCast() const
		{
			SANDBOX_ASSERT(IsKindOf<T>());
			return static_cast<const T*>(this);
		}
		template<typename T>
		inline bool IsKindOf() const
		{
			return GetRTTI()->IsKindOf(T::RTTI());
		}
		inline bool IsKindOf(const RuntimeClass* dst) const
		{
			return GetRTTI()->IsKindOf(dst);
		}
		inline bool IsKindOf(const ReflexType* reftype) const
		{
			if (!reftype || !reftype->GetRuntimeClass()) return false;
			return IsKindOf(reftype->GetRuntimeClass());
		}
		template<typename T>
		inline bool IsSameClass() const
		{
			return GetRTTI()->IsSame(T::RTTI());
		}
	public:
		Ref();
		virtual ~Ref();

		/**
		* 增加计数
		*/
#ifdef SANDBOX_USE_PROFILE_REFCNT
		inline void IncrementRef(void* key = nullptr)
#else
		inline void IncrementRef()
#endif
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			Profile::Manager::GetInstance().BindData(key, this);
#endif
			//SANDBOX_ASSERT(!m_flagIsDirty); // 自动释放的对象脏了后，不能再加计数
			++m_refCount;
		}

		/**
		* 减少引用计数，<= 0 释放
		*/
#ifdef SANDBOX_USE_PROFILE_REFCNT
		inline void DecrementRef(void* key = nullptr)
#else
		inline void DecrementRef()
#endif
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			Profile::Manager::GetInstance().UnbindData(key, this);
#endif
			SANDBOX_ASSERT(m_refCount > 0);
			--m_refCount;

			if (m_refCount == 0)
			{
				OnDirty(); // 脏处理，内存回收
			}
		}

		/**
		* 获取引用计数
		*/
		unsigned GetRefCount() const { return m_refCount; }

		/**
		* 重置
		*/
		virtual void ResetSelf();

		/**
		* 释放
		*/
		virtual void Release() {}

	public:
		/* 获取链接器 */
		LinkerRef* GetLinker();

	private:
		/* 清理链接器 */
		void ClearLinker();
		/* 删除链接器 */
		void DeleateLinker();

	protected:

		friend class SandboxAutoreleasePool;

		/**
		* 脏处理
		* 计数为0
		*/
		virtual void OnDirty();

		/**
		* 释放
		* 内存回收，回收到内存池，或者释放内存等
		*/
		virtual void ReleaseSelf();

	protected:
		/* flags */
		// 由于很多外部类依赖了Ref，但是没有使用 AutoRef 维护计数，会导致以外释放。
		// 所以新增 m_flagAutoRelease，默认为 1 表示释放。不需要释放的，手动改为 0

		// 是否自动释放
		unsigned m_flagAutoRelease : 1; // 0 表示不会因为计数释放，1 表示计数减到0自动释放
		// 是否已经脏了
		unsigned m_flagIsDirty : 1; // 0 表示还没有脏，1 表示已经脏了，正在释放

	private:
		/* 引用次数，计数归0执行死亡或复活逻辑 */
		alignas(16) std::atomic<unsigned> m_refCount; // android 需要对齐到16

	public:
		// object id, ScriptSupport need public _ID
		unsigned _ID; //tolua_export

	private:
		// 链接器
		LinkerRef* m_linker = nullptr;

	public:
		// id count, for new _ID
		static unsigned ms_uObjectCount;

	}; //tolua_export

	///////////////////////////////////////////////////////////////////////////
	// AutoRef

	template<typename T>
	FGetRef AutoRef<T>::ms_getRefFunc = nullptr;

	template<typename T>
	AutoRef<T>::AutoRef(T* data/*=nullptr*/)
		: m_data(data)
	{
		if (m_data)
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			GetRef(m_data)->IncrementRef(this);
#else
			GetRef(m_data)->IncrementRef();
#endif
		}
	}

	template<typename T>
	AutoRef<T>::AutoRef(const AutoRef& v)
		: m_data(v.m_data)
	{
		if (m_data)
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			GetRef(m_data)->IncrementRef(this);
#else
			GetRef(m_data)->IncrementRef();
#endif
		}
	}

	template<typename T>
	AutoRef<T>::~AutoRef()
	{
		if (m_data)
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			GetRef(m_data)->DecrementRef(this);
#else
			GetRef(m_data)->DecrementRef();
#endif
		}
	}

	template<typename T>
	Ref* AutoRef<T>::GetRef(T* ptr)
	{
#if 1
		if (!ms_getRefFunc)
		{
			auto reftype = ReflexType::GetSingletonPtr<T>();
			auto rtti = reftype ? reftype->GetRuntimeClass() : nullptr;
			ms_getRefFunc = rtti ? rtti->GetRefFunction() : &RuntimeClass::GetRefDefault;
		}
		return ms_getRefFunc(ptr);
#else
		return reinterpret_cast<Ref*>(ptr);
#endif
	}

	template<typename T>
	void AutoRef<T>::SetData(T* data)
	{
		if (m_data == data)
			return;

		if (m_data)
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			GetRef(m_data)->DecrementRef(this);
#else
			GetRef(m_data)->DecrementRef();
#endif
		}

		m_data = data;

		if (m_data)
		{
#ifdef SANDBOX_USE_PROFILE_REFCNT
			GetRef(m_data)->IncrementRef(this);
#else
			GetRef(m_data)->IncrementRef();
#endif
		}
	}

	template<typename T>
	void AutoRef<T>::Release()
	{
		if (m_data)
		{
			GetRef(m_data)->Release();
			SetData(nullptr);
		}
	}

} //tolua_export //namespace MNSandbox
