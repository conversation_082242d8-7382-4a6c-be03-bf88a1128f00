#pragma once

#include "SandboxAutoRef.h"
#include "SandboxSceneObject.h"
#include "SandboxReflexValue.h"
#include "SandboxReflexTuple.h"

namespace MNSandbox {

	class EventInvocation
	{
	public:
		EventInvocation(AutoRef<SandboxNode> instance, ReflexValue* desc, AutoRef<ReflexTuple> args)
			: m_instance(instance)
			, m_descriptor(desc)
			, m_arguments(args)
		{}

		EventInvocation(AutoRef<SandboxNode> instance, ReflexValue* desc)
			: m_instance(instance)
			, m_descriptor(desc)
		{}

		void CallEvent()
		{
			//m_descriptor->CallEvent(m_instance, m_arguments);
			if (!m_descriptor->TypeIsRemoteNotify()) return;
			m_descriptor->Call(m_instance, m_arguments);
		}

		void SendEvent()
		{
			//m_descriptor->SendEvent(m_instance, m_arguments);
			if (!m_descriptor->TypeIsRemoteNotify()) return;
			m_instance->SendEventInvocation(m_descriptor, m_arguments);
		}

		bool operator==(const EventInvocation& other) const {
			return this->m_descriptor == other.m_descriptor && this->m_instance == other.m_instance;
		}
		bool operator!=(const EventInvocation& other) const {
			return !operator==(other);
		}


	protected:
		ReflexValue* m_descriptor;
		AutoRef<SandboxNode> m_instance;
		AutoRef<ReflexTuple> m_arguments;
	};

}