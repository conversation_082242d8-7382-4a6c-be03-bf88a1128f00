
#include "OgreCameraAnim.h"
#include "Components/Camera.h"
#include "OgreTimer.h"
#include "Core/GameObject.h"

using namespace Rainbow;
namespace MINIW
{
	const int FRAME_INTERVAL = 20;
	CameraAnimation::CameraAnimation() : m_CurCamera(NULL), m_CaptureSeqID(-1), m_PlaySeqID(-1), m_CurTick(0)
	{
	}

	CameraAnimation::~CameraAnimation()
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_CurCamera);
	}

	void CameraAnimation::beginCapture(int seqid, Camera *pcamera)
	{
		m_CurCamera = pcamera;
		m_LastFrameTick = Timer::getSystemTick();
		m_CurTick = 0;
		m_CaptureSeqID = seqid;
		
		addFrameData();
	}

	int CameraAnimation::endCapture()
	{
		/*size_t nrawkeys = m_CurPosKeys.size();
		std::vector<Vector3f>newpos(nrawkeys);
		std::vector<Quaternionf>newrot(nrawkeys);
		std::vector<float>newfov(nrawkeys);
		std::vector<unsigned int>newpostimes(nrawkeys);
		std::vector<unsigned int>newrottimes(nrawkeys);
		std::vector<unsigned int>newfovtimes(nrawkeys);

		size_t nposkey = SampleReduction(nrawkeys, &m_CurPosKeys[0], &m_CurTimeKeys[0], &newpos[0], &newpostimes[0], 5.0f);
		size_t nrotkey = SampleReduction(nrawkeys, &m_CurRotKeys[0], &m_CurTimeKeys[0], &newrot[0], &newrottimes[0], Rainbow::SinByAngle(5.0f));
		size_t nfovkey = SampleReduction(nrawkeys, &m_CurFovKeys[0], &m_CurTimeKeys[0], &newfov[0], &newfovtimes[0], 5.0f);

		m_TranslateKeys.setSeqFrames(m_CaptureSeqID, nposkey, &newpos[0], &newpostimes[0]);
		m_RotateKeys.setSeqFrames(m_CaptureSeqID, nrotkey, &newrot[0], &newrottimes[0]);
		m_FovKeys.setSeqFrames(m_CaptureSeqID, nfovkey, &newfov[0], &newfovtimes[0]);

		OGRE_RELEASE(m_CurCamera);
		int ret = m_CaptureSeqID;
		m_CaptureSeqID = -1;

		m_CurTimeKeys.clear();
		m_CurPosKeys.clear();
		m_CurRotKeys.clear();
		m_CurFovKeys.clear();
		return ret;*/
		return 0;
	}

	void CameraAnimation::beginPlay(int seqid, Camera *pcamera)
	{
		if(!m_TranslateKeys.hasSeq(seqid)) return;

		m_CurCamera = pcamera;
		m_PlaySeqID = seqid;
		m_LastFrameTick = Timer::getSystemTick();
		m_CurTick = 0;
	}

	void CameraAnimation::endPlay()
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_CurCamera);
		m_PlaySeqID = -1;
	}

	bool CameraAnimation::isPlaying()
	{
		return m_CurCamera!=NULL && m_PlaySeqID>=0;
	}

	void CameraAnimation::update()
	{
		/*if(m_CurCamera == NULL) return;

		int systick = Timer::getSystemTick();
		int dt = systick - m_LastFrameTick;
		if(dt > 500) dt = 500;

		if(m_CaptureSeqID >= 0)
		{
			if(dt >= FRAME_INTERVAL)
			{
				m_CurTick += dt;
				m_LastFrameTick = systick;
				addFrameData();
			}
		}

		if(m_PlaySeqID >= 0)
		{
			m_CurTick += dt;
			m_LastFrameTick = systick;
			if(m_CurTick >= m_TranslateKeys.getDuration(m_PlaySeqID))
			{
				endPlay();
				return;
			}

			Rainbow::Vector3f pos;
			Rainbow::Quaternionf rot;
			float fov;

			m_TranslateKeys.getValue(m_PlaySeqID, m_CurTick, pos, false);
			m_RotateKeys.getValue(m_PlaySeqID, m_CurTick, rot, false);
			m_FovKeys.getValue(m_PlaySeqID, m_CurTick, fov, false);

			m_CurCamera->setOffsetPosition(pos);
			m_CurCamera->setRotation(rot);
			m_CurCamera->setFov(fov);
		}*/
	}

	void CameraAnimation::addFrameData()
	{
		/*Matrix4 tm = m_CurCamera->getWorldMatrix();
		Rainbow::Vector3f pos = tm.getTranslate();
		Rainbow::Quaternionf rot;
		rot.setMatrix(tm);
		float fov = m_CurCamera->getFov();

		m_CurTimeKeys.push_back(m_CurTick);
		m_CurPosKeys.push_back(pos);
		m_CurRotKeys.push_back(rot);
		m_CurFovKeys.push_back(fov);*/
	}
}