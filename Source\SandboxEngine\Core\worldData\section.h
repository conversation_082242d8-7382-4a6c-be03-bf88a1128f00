
#ifndef __SECTION_H__
#define __SECTION_H__

#include "OgrePrerequisites.h"
#include "basesection.h"
#include <vector>
//#include "worldMesh/RenderSection.h"
#include "worldData/SharedSectionData.h"
#include "SandboxEngine.h"
class MemStat;

struct PaneGrid;
class SectionMesh;
class SectionSubMesh;
class MechaSectionMesh;
class BlockGeomTemplate;
class BlockMesh;
class RenderBlockMaterial;
struct CacheBlock
{
	BlockMaterial* mtl = NULL;
	int waterpass = 0;
	int blockdata = 0;
}; 

inline CacheBlock& GetCacheBlock(CacheBlock blocks[], int x, int y, int z)
{
	assert((y) * (SECTION_BLOCK_DIM) * (SECTION_BLOCK_DIM) + (z) * (SECTION_BLOCK_DIM) + x < (SECTION_BLOCK_DIM) * (SECTION_BLOCK_DIM) * (SECTION_BLOCK_DIM));
	return blocks[(y) * (SECTION_BLOCK_DIM) * (SECTION_BLOCK_DIM) + (z) * (SECTION_BLOCK_DIM) + x];
}
namespace Rainbow
{
	class RigidStaticActor;
	class MechaMeshObject;
	class RenderSection;
};

const int NUM_DIRTYFLAGS_INT = 16*16*16/32;

//优化为一个整体的渲染对象
class EXPORT_SANDBOXENGINE Section;
class Section : public BaseSection //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	Section(Chunk *pchunk, int isect);
	virtual ~Section();
	virtual void destroy() override;
	virtual SharedSectionData* createSharedSectionData() const override;
	virtual size_t getMemStat(MemStat& ms);
	//tolua_end

	void FreeRenderSection();
	void CreateRenderSection();


	void SetHide(bool value);
	bool IsHide();
public:
	//tolua_begin
	void allocBlocks();
	void clearBlocks();
	int getBlockID(int x, int y, int z) const
	{
		return getBlock(x, y, z).getResID();
	}
	int getBlockData(const WCoord& blockpos) const
	{
		return getBlockData(blockpos.x, blockpos.y, blockpos.z);
	}
	int getBlockData(int x, int y, int z) const
	{
		return getBlock(x, y, z).getData();
	}
	int getBlockDataEx(const WCoord& blockpos) const
	{
		return getBlock(blockpos).getDataEx();
	}
	int getBlockDataEx(int x, int y, int z) const
	{
		return getBlock(x, y, z).getDataEx();
	}
	virtual World *getWorld() override;
	virtual const WCoord& getOrigin() const override
	{
		return m_Origin;
	}

	Chunk* GetChunk() const { return m_pChunk; }
	Rainbow::RenderSection* GetRenderSection() const { return m_RenderSection; }
	Rainbow::RenderSection* GetMinimapRenderSection() const { return m_MinimapSection; }

	void setLightDirty(int x, int y, int z, int lttype)
	{
		if (m_dirtyLightBits[lttype].empty())
		{
			m_dirtyLightBits[lttype].resize(NUM_DIRTYFLAGS_INT, 0);
		}
		int index = (x << 8) | (z << 4) | y;
		unsigned int& flags = m_dirtyLightBits[lttype][index >> 5]; //除以32

		unsigned int bit = 1 << (index & 31);
		if ((flags & bit) == 0)
		{
			flags = flags | bit;
			m_dirtyLightNum[lttype]++;
		}
	}

	void setRemoteLightDirty(int x, int y, int z, int lttype);


	void addActor(IClientActor *actor);
	void removeActor(IClientActor *actor);

	Section *getNeighbourSection(int deltax, int deltay, int deltaz);

	//create mesh
	void createPhysMesh(std::vector<Rainbow::Vector3f>&verts, std::vector<unsigned short>&indices);
	void createMinimapMesh();
	void clearMinimapMesh();
	BlockMesh*createBluePrintPreMesh(const std::map<WCoord, int> &specialBlockColorMap = std::map<WCoord, int>());
	Rainbow::MechaMeshObject*createMapEditPreMesh(const Rainbow::FixedString &filename);
	Rainbow::MechaMeshObject* createMapEditPreObject(const Rainbow::FixedString& filename);

	//void calVertexLights(Block *pcur, const WCoord &blockpos, DirectionType dir, int cover, float *vertlights, bool ao=true);
	void updateViewPos(const WCoord &viewpos);
	void setMeshInvalid(bool include_minimesh);
	void setNeighborMeshInvalid();
	void setPhyInvalid();
	void setNeedUpdateVisibility();
	void setWaterHeight(int waterHeight, int waterNumber);
	int getWaterHeight() const;
	int getWaterNumber() const;
	void loadBlockMaterials();
	Block getEmptyBlock() const { return m_EmptyBlock; }

	//tolua_end

	virtual const SectionDataHandler* getNeighborSectionData(const WCoord& blockPos, DirectionType dir) const override;

	virtual const SectionDataHandler* getNeighborSectionData(const WCoord& blockPos, const WCoord& offset) const override;

	virtual bool getNeighborCover(const WCoord& blockpos, SolidBlockMaterial* curmtl, int curblockdata, DirectionType dir) const override;

	virtual Block getNeighborBlock(const WCoord& blockPos, DirectionType dir) const override;

	virtual Block getNeighborBlock(const WCoord& blockPos, const WCoord& offset) const override;

	virtual Block getNeighborBlockLogic(const WCoord& grid, DirectionType dir) const override;

	virtual Block getNeighborBlockLogic(const WCoord& grid, const WCoord& offset) const override;


	void UpdateActorsCull();


protected:
	virtual BlockLight getNeighborBlockLightInternal(WCoord neighborBlockPos) const override;
	virtual Block getNeighborBlockInternal(WCoord neighborBlockPos) const override;
private:
	const SectionDataHandler* getNeighborSectionInternal(const WCoord& worldBlockPos) const;
	//void createOneBlockMesh(int x, int y, int z);
	void createOneBlockMinimapMesh(unsigned int *miniblocks, int x, int y, int z, SectionSubMesh *psubmesh, BlockGeomTemplate *geom);
	int calViewDirFaceMask(const WCoord &viewpos);

	void createPhysPaneMesh(std::vector<Rainbow::Vector3f>&verts, std::vector<unsigned short>&indices, PaneGrid *grids, int origin[3], int du[3], int dv[3], DirectionType dir);
	void createOneBlockBluePrintPreMesh(unsigned int *miniblocks, int x, int y, int z, SectionSubMesh *psubmesh, BlockGeomTemplate *geom);
	void createMapEditPreMesh(int x, int y, int z, SectionSubMesh *psubmesh, BlockGeomTemplate *geom);
	unsigned int getSpecialBlockMiniColor(int blockid, int x, int y, int z);


public:
	//tolua_begin
	Chunk *m_pChunk;
	WCoord m_Origin;

	unsigned short m_RandomTickBlocks; //需要随机tick的block个数
	unsigned short m_VoidRandomTickBlocks; //需要虚空之夜随机tick的block个数
	bool m_PhyMeshInvalid;
	int  m_PhyMeshCount;

	std::vector<IClientActor *> m_Actors;
	
	std::vector<UInt32> m_dirtyLights;

	int m_NeedGenWorldTick;
	WCoord m_BoundMinOffset;
	WCoord m_BoundMaxOffset;

	Rainbow::RigidStaticActor *m_PhysActor;
	Rainbow::RigidStaticActor* m_TriggerActor;
	Rainbow::RigidStaticActor* m_TriangleActor;

	bool m_RefreshedEmptySection;     //空Section需刷新一次，防止刚变成空Section时，被cull掉了，mesh不刷新
	//tolua_end
//#ifdef IWORLD_REALTIME_SHADOW
//	int m_WaterNum;  //在createMesh的时候计算
//#endif
	int m_WaterNum;
	int m_WaterHeight; //-1表示没有水

	std::vector<UInt32> m_dirtyLightBits[2];
	int m_dirtyLightNum[2];


	std::vector<UInt32> m_RemoteDirtyLightBits[2];
	int m_RemoteDirtyLightNum[2];

	Rainbow::RenderSection* m_RenderSection;
	Rainbow::RenderSection* m_MinimapSection;
	RenderBlockMaterial* m_MinimapMaterial{ nullptr };
	bool m_MiniMeshInvalid{ true };
	bool m_MeshInvalidLowPrio{ true };

protected:
	Block m_EmptyBlock;

}; //tolua_exports

#endif