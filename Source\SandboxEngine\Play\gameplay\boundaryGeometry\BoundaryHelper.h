#ifndef __BOUNDARY_HELPER__
#define __BOUNDARY_HELPER__

#include "WorldRender.h"
#include "BoundaryBoxGeometry.h"
#include "BoundarySphereGeometry.h"
#include "BoundaryCylinderGeometry.h"
#include "BoundaryHolder.h"
#include "Math/Matrix4x4f.h"
#include "SandboxEngine.h"
enum BoundaryGeDrawColor
{
	BoundaryGeDrawColor_Green = CURVEFACEMTL_GREENLINE,
	BoundaryGeDrawColor_Blue = CURVEFACEMTL_1BLUE,
	BoundaryGeDrawColor_Yellow = CURVEFACEMTL_1YELLOW,
	BoundaryGeDrawColor_Red = CURVEFACEMTL_1RED,
};


class EXPORT_SANDBOXENGINE BoundaryHelper
{
public:
	static void drawLine(const Rainbow::Vector3f& point1, const Rainbow::Vector3f& point2, World* pworld, BoundaryGeDrawColor color, int lineWidth = 5);
	static void drawBox(const BoundaryBoxGeometry& box, World* pworld, BoundaryGeDrawColor color, int lineWidth = 5);
	static void drawGeometry(const BoundaryGeometryHolder& holder, World* pworld, BoundaryGeDrawColor color, int lineWidth = 5);
	static void drawCircle2D(World* pworld, int r, const Rainbow::Matrix4x4f& localToWorld, BoundaryGeDrawColor color, int lineWidth = 5); //这个localToWorld只包含旋转, 移动
	static void drawSphere(const BoundarySphereGeometry& sphere, World* pworld, BoundaryGeDrawColor color, int lineWidth = 5);
	static void drawCylinder(const BoundaryCylinderGeometry& cylinder, World* pworld, BoundaryGeDrawColor color, int lineWidth = 5);
};

#endif
