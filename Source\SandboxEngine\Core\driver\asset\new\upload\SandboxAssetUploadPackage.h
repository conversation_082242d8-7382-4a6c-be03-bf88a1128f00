#pragma once


#include "SandboxAssetUploadRef.h"

namespace MNSandbox {

	class AssetInstancePacket;
	class /*EXPORT_SANDBOXENGINE*/ AssetUploadPackage : public AssetUploadRef
	{
		DECLARE_REF_NEW_INSTANCE(AssetUploadPackage)
	public:
		AssetUploadPackage();
		virtual ~AssetUploadPackage();

		virtual void OnGetRemoteResIdFinish()override;
	protected:
		virtual void OnInit() override;
		virtual void OnPrepareUploadFile() override;
	private:
		AutoRef<AssetInstancePacket> m_packetInstance;
		std::map<std::string, AutoRef<AssetUploadRef>> m_packageDepends;
	};

}