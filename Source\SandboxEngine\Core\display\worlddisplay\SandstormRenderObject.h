#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

namespace Rainbow 
{
	class SandstormEffect;

	class SandstormRenderObject : public Rainbow::RenderObject
	{
	public:
		explicit SandstormRenderObject(SandstormEffect* component);
		~SandstormRenderObject();

		virtual bool PrepareRender(const Rainbow::PrimitiveFrameNode& node) override;
		virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;

	private:
		SandstormEffect* m_SandstormEffect;
		MeshRenderData m_MeshRenderData;
		dynamic_array<int> m_SandstormEntityIndex;

	};
}




