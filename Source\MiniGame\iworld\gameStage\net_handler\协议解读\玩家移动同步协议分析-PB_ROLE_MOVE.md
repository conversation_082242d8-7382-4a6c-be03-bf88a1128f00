# 沙盒游戏玩家移动同步协议深度分析

## 概述

本文档深入分析沙盒创造类游戏中玩家移动同步的完整流程，包括 PB_ROLE_MOVE_CH 以及新版移动同步协议 PB_SYNC_MOVE_CH、PB_SYNC_MOVE_HC 的工作机制。

**重要说明**: 经过代码验证，**PB_ROLE_MOVE_HC 协议并不存在**。服务器向客户端广播其他玩家移动信息使用的是 PB_ACTOR_MOVEV2_HC 等协议。

## 移动同步完整时序图

```mermaid
sequenceDiagram
    participant C1 as 客户端A
    participant S as 服务器
    participant C2 as 客户端B
    participant C3 as 客户端C

    Note over C1,C3: 玩家A移动同步流程

    C1->>S: PB_ROLE_MOVE_CH (玩家A移动)
    Note over S: handleRoleMove2Host()
    S->>S: 验证玩家状态
    S->>S: 解析移动数据
    S->>S: 检查位置合法性

    alt 位置合法
        S->>S: 更新玩家A位置
        S->>C2: PB_ACTOR_MOVEV2_HC (广播给B)
        S->>C3: PB_ACTOR_MOVEV2_HC (广播给C)
        Note over C2,C3: 其他客户端更新玩家A位置
    else 位置不合法
        S->>C1: PB_SYNC_MOVE_HC (强制校正)
        Note over C1: handleSyncMove2Client()
        C1->>C1: 强制设置位置
        C1->>S: PB_SYNC_PLAYER_POS_CH (确认)
    end

    Note over C1,C3: 新版移动同步流程

    C1->>S: PB_SYNC_MOVE_CH (新版移动)
    Note over S: handleSyncMove2Host()
    S->>S: 验证时间戳
    S->>S: 处理移动标志
    S->>S: 检查移动合法性

    alt 移动合法
        S->>S: 接受客户端位置
        Note over S: CRT_Accept
    else 移动不合法
        S->>C1: PB_SYNC_MOVE_HC (回滚)
        Note over S: CRT_RollBack
        C1->>S: PB_SYNC_PLAYER_POS_CH (确认)
    end

    Note over C1,C3: 批量移动广播优化

    S->>S: 缓存多个玩家移动
    S->>S: tickActorMoveSync()
    S->>C2: PB_ACTOR_MOVEV3_HC (批量广播)
    S->>C3: PB_ACTOR_MOVEV3_HC (批量广播)
```

## 协议体系架构

### 1. 旧版移动协议

- **PB_ROLE_MOVE_CH**: 客户端向服务器发送移动信息
- **PB_ROLE_MOVE_HC**: 服务器向客户端广播其他玩家移动信息（没有这个协议）

### 2. 新版移动同步协议

- **PB_SYNC_MOVE_CH**: 客户端向服务器发送新版移动同步
- **PB_SYNC_MOVE_HC**: 服务器向客户端发送位置校正

### 3. Actor 移动广播协议

- **PB_ACTOR_MOVE_HC**: 广播其他玩家移动信息（不执行）
- **PB_ACTOR_MOVEV2_HC**: 优化版本的移动广播（已注释）
- **PB_ACTOR_MOVEV3_HC**: 批量移动广播

## 协议定义

### PB_RoleMoveCH (客户端 → 服务器)

**文件位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:43-50`

```protobuf
message PB_RoleMoveCH
{
    optional game.common.PB_MoveMotion MoveMotion = 1;
    optional game.common.PB_Vector3 AddMotion = 2;
    optional int32 rentToken = 3;
    optional float speed = 4;
    optional game.common.PB_Vector3 VehiclePos = 5;
}
```

### PB_MoveSyncHC (服务器 → 客户端)

**文件位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:2032-2038`

```protobuf
message PB_MoveSyncHC
{
    required uint32 id = 1;                    // 移动检查ID
    optional bool accept = 2;                  // 是否接受客机位置
    optional game.common.PB_Vector3 pos = 3;   // 位置信息
    optional game.common.PB_Vector3f motion = 4; // 动量信息
}
```

### PB_ActorMoveV2HC (服务器 → 客户端广播)

**文件位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:141-147`

```protobuf
message PB_ActorMoveV2HC
{
    optional uint64 ObjID = 1;
    repeated sint32 Position = 2 [packed = true];
    optional uint32 Yaw_Pitch = 3;
    optional int32 ChangeFlags = 4;
}
```

## 1. 客户端发送移动协议的情况

### 1.1 旧版协议发送条件

**代码位置**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:215-235`

```cpp
void MpPlayerControl::sendMoveToHost()
{
    m_nMoveTick++;
    WCoord pos = CoordDivBlock(getPosition());
    bool mustSync = false;
    auto functionWrapper = getFuncWrapper();
    if (functionWrapper)
    {
        mustSync = functionWrapper->getMustSyncPos();
    }
    if (m_pWorld->getChunk(pos) == NULL && !mustSync)
    {
        // 避免长时间不发送移动协议
        if (m_nMoveTick > 100)
        {
            m_nMoveTick = 0;
            PB_RoleMoveCH roleMoveCH;
            PB_MoveMotion* moveMotion = roleMoveCH.mutable_movemotion();
            moveMotion->set_changeflags(0);
            GameNetManager::getInstance()->sendToHost(PB_ROLE_MOVE_CH, roleMoveCH);
        }
        return;
    }
    // ... 继续处理正常移动同步
}
```

**发送触发条件**:

1. **心跳保活**: 当玩家在未加载区块时，每 100 帧发送一次心跳协议
2. **位置变化**: 玩家位置发生变化时
3. **朝向变化**: 玩家视角朝向发生变化时
4. **强制同步**: 特殊功能要求强制同步位置时

### 1.2 新版协议发送条件

**代码位置**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:574-605`

```cpp
void MpPlayerControl::sendNewMoveToHost()
{
    // ... 省略前置检查代码

    bool not_sync_pos = false;
    auto sit = GetComponent<ServerInterpolTick>();
    if ((sit && sit->isMoving()) || (getRiddenComponent() && getRiddenComponent()->isRiding()))
        not_sync_pos = true;

    if (!not_sync_pos)
    {
        const auto& pos = m_MoveControl->getPrePosition();
        if (need_sync || (tick_match && m_MoveControl->positionChanged(pos)))
        {
            auto pb_pos = pbMoveSync.mutable_pos();
            pb_pos->set_x(pos.x);
            pb_pos->set_y(pos.y);
            pb_pos->set_z(pos.z);

            need_sync = true;
            bits |= 2;
            m_MoveControl->setLastPosition(pos);
        }
    }

    if (need_sync)
    {
        pbMoveSync.set_id(0);
        pbMoveSync.set_tick(tick);
        GetGameNetManagerPtr()->sendToHost(PB_SYNC_MOVE_CH, pbMoveSync);
    }
}
```

**新版协议优势**:

- 包含时间戳验证，防止作弊
- 支持移动标志变化
- 更精确的位置同步

## 2. 服务器处理移动协议

### 2.1 旧版协议处理

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1083-1105`

```cpp
void MpGameSurviveNetHandler::handleRoleMove2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = uin2Player(uin);
    if (player == nullptr)
    {
        sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
        return;
    }
    if (!player->isSimPlayer() && AntiSetting::forceUseNewSync(uin))
    {
        ActionLogger::SimpleErrLog(uin, 0, "cheat_use_old_move", "");
        return;
    }

    PB_RoleMoveCH roleMoveCH;
    roleMoveCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    const PB_MoveMotion &motion = roleMoveCH.movemotion();
    bool posChange = motion.changeflags() & 1;
    bool yawChange = motion.changeflags() & 2;
    // 仅作为移动上报心跳的协议 不实际作用
    if (!posChange && !yawChange)
        return;

    // ... 继续处理位置验证和同步
}
```

**处理步骤**:

1. **玩家验证**: 检查玩家是否存在
2. **反作弊检查**: 强制使用新版同步的玩家不能使用旧协议
3. **协议解析**: 解析移动数据
4. **变化检测**: 检查位置或朝向是否变化
5. **心跳过滤**: 纯心跳协议直接返回

### 2.2 新版协议处理

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:7439-7472`

```cpp
void MpGameSurviveNetHandler::handleSyncMove2Host(int uin, const PB_PACKDATA& pkg)
{
    ClientPlayer* player = uin2Player(uin);
    if (player == NULL)
        return;

    game::ch::PB_MoveSyncCH pbCH;
    pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    unsigned id = pbCH.id();

    // 处理移动标志变化
    for (int i = 0; i < pbCH.flag_change_size(); ++i)
    {
        auto &flag_change = pbCH.flag_change(i);
        player->changeMoveFlag(flag_change.type(), flag_change.on());
    }

    unsigned long long tick = pbCH.tick();
    bool tick_valid = player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(tick);
    if (pbCH.has_pos())
    {
        if (tick_valid)
            player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), tick);
        else
            player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), 0);
    }

    if (pbCH.has_move_opera())
    {
        auto move_info = pbCH.move_opera();
        player->setMoveControl(move_info.opera(), move_info.yaw() / 1000.0f, move_info.pitch() / 1000.0f, tick_valid ? tick: 0);
    }
}
```

**新版处理特点**:

- **时间戳验证**: 通过 CheatHandler 验证客户端时间戳
- **移动标志**: 处理跳跃、下蹲等移动状态变化
- **精确控制**: 支持操作类型和视角信息

### 2.3 本机客户端与第三方客户端处理差异

**关键差异点**:

1. **本机客户端**: 服务器直接处理移动逻辑，不需要额外验证
2. **第三方客户端**: 需要进行反作弊检查、位置合法性验证

**代码体现**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1091-1095`

```cpp
if (!player->isSimPlayer() && AntiSetting::forceUseNewSync(uin))
{
    ActionLogger::SimpleErrLog(uin, 0, "cheat_use_old_move", "");
    return;
}
```

## 3. 服务器响应协议

### 3.1 位置校正协议 (PB_SYNC_MOVE_HC)

**发送位置**: `Source\SandboxGame\Play\player\ClientPlayer_Base.cpp` 中的 `checkMoveResult()` 方法

**发送情况**:

#### A. 回滚情况 (CRT_RollBack)

当客户端移动不合法时，服务器强制回滚：

```cpp
game::hc::PB_MoveSyncHC pbMoveSync;
pbMoveSync.set_id(m_MoveControl->getCheckID());
pbMoveSync.set_accept(false);  // 拒绝客机位置

// 设置服务器当前位置
auto pb_pos = pbMoveSync.mutable_pos();
auto &pos = player_loco->getPosition();
pb_pos->set_x(pos.x);
pb_pos->set_y(pos.y);
pb_pos->set_z(pos.z);

// 设置服务器当前动量
auto pb_motion = pbMoveSync.mutable_motion();
auto &motion = player_loco->getMotion();
pb_motion->set_x(motion.x);
pb_motion->set_y(motion.y);
pb_motion->set_z(motion.z);

GetGameNetManagerPtr()->sendToClient(getUin(), PB_SYNC_MOVE_HC, pbMoveSync);
```

#### B. 接受情况 (CRT_Accept)

当客户端移动合法时，服务器接受位置：

```cpp
if (!IsOnPlatform())  // 不在运动平台上才更新位置
{
    auto& pos = m_MoveControl->getPrePosition();
    if (!pos.isZero())
    {
        player_loco->setPosition(pos.x, pos.y, pos.z);
        player_loco->setMotion(motion);

        // 地面检测
        WCoord dest = m_pWorld->moveBox(box, WCoord(0, -1, 0));
        if (dest.y == 0)
        {
            player_loco->setOnGround(true);
            player_loco->m_JumpingTicks = 0;
        }
    }
}
```

### 3.2 广播其他玩家移动信息

**代码位置**: `Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:1269-1337`

服务器使用 `PB_ACTOR_MOVEV2_HC` 协议广播其他玩家的移动信息：

```cpp
PB_ActorMoveV2HC actorMoveHC;
uint64_t objid = mEntryActor->getObjId();
objid = GetISandboxActorSubsystem()->PackObjId(objid);
actorMoveHC.set_objid(objid);

// 设置朝向信息
uint32_t yaw_pitch = 0;
yaw_pitch = ((curyaw) & 0xff) << 8;
yaw_pitch |= (curpitch & 0xff);
actorMoveHC.set_yaw_pitch(yaw_pitch);

// 设置位置信息
actorMoveHC.add_position(curpos.x);
actorMoveHC.add_position(curpos.y);
actorMoveHC.add_position(curpos.z);

// 广播给追踪该玩家的所有客户端
auto iter = mTrackingPlayersTable.iterate(nullptr);
for (; iter != nullptr; iter = mTrackingPlayersTable.iterate(iter))
{
    IClientPlayer* pp = iter->key;
    if (pp != NULL) {
        GetGameNetManagerPtr()->cacheActorMove(pp->getUin(), objid, actorMoveHC);
    }
}
```

**广播触发条件**:

1. **位置变化**: 位置变化超过阈值 (DPSQ)
2. **朝向变化**: 朝向变化超过阈值 (ASQ)
3. **强制同步**: 特殊情况需要强制同步

## 4. 客户端处理响应协议

### 4.1 处理位置校正 (PB_SYNC_MOVE_HC)

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:8608-8627`

```cpp
void MpGameSurviveNetHandler::handleSyncMove2Client(const PB_PACKDATA_CLIENT& pkg)
{
    PlayerControl* playerCtrl = m_root->getPlayerControl();
    if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

    game::hc::PB_MoveSyncHC pbHC;
    pbHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    if (pbHC.has_pos())
    {
        WCoord pos = MPVEC2WCoord(pbHC.pos());
        if (playerCtrl->getLocoMotion())
            playerCtrl->getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
        playerCtrl->setLastSyncPosition(pos);
        playerCtrl->updateClientMoveSyncInterval(true);

        // 发送位置确认响应
        PB_ResetPosResponeCH msg;
        msg.set_tick(m_root? m_root->getGameTick(): 0);
        GetGameNetManagerPtr()->sendToHost(PB_SYNC_PLAYER_POS_CH, msg);
    }

    if (pbHC.has_motion())
    {
        auto &motion = pbHC.motion();
        playerCtrl->setMotionChange(Rainbow::Vector3f(motion.x(), motion.y(), motion.z()));
    }
}
```

**处理步骤**:

1. **强制设置位置**: 直接设置玩家位置到服务器指定位置
2. **记录同步位置**: 更新最后同步位置记录
3. **调整同步间隔**: 更新客户端移动同步频率
4. **发送确认**: 向服务器确认位置接收

### 4.2 处理其他玩家移动 (PB_ACTOR_MOVEV2_HC)

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:1113-1173`

```cpp
void MpGameSurviveNetHandler::handleActorMoveV22Client(const PB_PACKDATA_CLIENT& pkg)
{
    PB_ActorMoveV2HC actorMoveHC;
    actorMoveHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    long long ObjId = actorMoveHC.objid();
    ObjId = ClientActor::UnpackObjId(ObjId);
    ClientActor* actor = objId2ActorOnClient(ObjId);

    if (actor != nullptr)
    {
        // 解析位置信息
        if (actorMoveHC.position_size() >= 3)
        {
            WCoord pos;
            pos.x = actorMoveHC.position(0);
            pos.y = actorMoveHC.position(1);
            pos.z = actorMoveHC.position(2);

            // 解析朝向信息
            if (actorMoveHC.has_yaw_pitch())
            {
                uint32_t yaw_pitch = actorMoveHC.yaw_pitch();
                float yaw = AngleChar2Float((yaw_pitch >> 8) & 0xff);
                float pitch = AngleChar2Float(yaw_pitch & 0xff);

                Rainbow::Quaternionf rot = EulerAngleToQuaternion(Rainbow::Vector3f(pitch, yaw, 0));
                actor->moveToPosition(pos, rot, INTERPOL_TICKS);
            }
        }
    }
}
```

**本机客户端与第三方客户端处理差异**:

- **本机客户端**: 不处理自己的移动广播，避免位置冲突
- **第三方客户端**: 接收并应用其他玩家的移动信息，进行插值平滑

## 5. 协议注册机制

**注册位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp`

```cpp
// 服务器端处理器注册
REGIS_HOST_HANDLER(PB_ROLE_MOVE_CH, handleRoleMove2Host);
REGIS_HOST_HANDLER(PB_SYNC_MOVE_CH, handleSyncMove2Host);

// 客户端处理器注册
REGIS_CLIENT_HANDLER(PB_SYNC_MOVE_HC, handleSyncMove2Client);
REGIS_CLIENT_HANDLER(PB_ACTOR_MOVE_HC, handleActorMove2Client);
REGIS_CLIENT_HANDLER(PB_ACTOR_MOVEV2_HC, handleActorMoveV22Client);
REGIS_CLIENT_HANDLER(PB_ACTOR_MOVEV3_HC, handleActorMoveV32Client);
```

## 6. 反作弊机制

### 6.1 时间戳验证

**代码位置**: `Source\SandboxGame\Play\player\PlayerCheat.cpp`

```cpp
bool tick_valid = player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(tick);
```

### 6.2 移动协议监控

**代码位置**: `Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:2178-2203`

```cpp
void GameNetManager::onRecvClientMsg(int uin, int msgCode)
{
    if (!GetAntiCheatMgrInterface()->CheckMove(msgCode))
        return;

    unsigned now = Rainbow::Timer::getSystemTick();
    // 客户端可能存在协议阻塞的情况 把移动的数据校正一下
    if (now - m_RoleLastMsgTimes[uin] > 5000)
    {
        m_RoleMoveTimes[uin] = now;
    }

    m_RoleLastMsgTimes[uin] = now;
    if (msgCode == PB_ROLE_MOVE_CH)
        m_RoleMoveTimes[uin] = now;
}
```

## 7. 移动同步优化机制

### 7.1 批量移动广播

**代码位置**: `Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:460-486`

为了优化网络性能，系统使用批量移动广播：

```cpp
void GameNetManager::tickActorMoveSync()
{
    if (m_ActorMoveTargetCache.empty())
        return;
    for (auto& data : m_ActorMoveTargetCache)
    {
        std::shared_ptr<PB_ActorMoveV3HC_Batch> batch = m_ProtoMoveV3HCPool.Acquire();
        auto target = data.first;
        auto& ovec = data.second;
        for (auto oid : ovec)
        {
            auto it = m_ActorMoveCache.find(oid);
            if (it != m_ActorMoveCache.end())
            {
                PB_ActorMoveV2HC& am = it->second;
                auto mo = batch->add_movebatch();
                *mo = am;
            }
        }

        sendToClient(target, PB_ACTOR_MOVEV3_HC, *batch, 0, true, UNRELIABLE_SEQUENCED, HIGH_PRIORITY, 0);
        m_ProtoMoveV3HCPool.Release(batch);
    }
}
```

**优化特点**:

- **批量处理**: 将多个玩家的移动信息打包发送
- **对象池**: 使用对象池减少内存分配
- **不可靠传输**: 使用 UNRELIABLE_SEQUENCED 提高性能
- **高优先级**: 移动信息具有高传输优先级

### 7.2 移动缓存机制

**代码位置**: `Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:1322-1336`

```cpp
GetGameNetManagerPtr()->cacheActorMove(target->getUin(), objid, actorMoveHC);
```

系统将移动信息先缓存，然后批量发送，避免频繁的网络调用。

### 7.3 插值平滑处理

**代码位置**: `Source\SandboxGame\Core\actors\clientActor\ClientActor.cpp:2908-2926`

```cpp
void ClientActor::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
    if (getLocoMotion() == NULL) return;

    if (IsObject() && m_pWorld && m_pWorld->isRemoteMode())
    {
        getLocoMotion()->m_PosRotationIncrements = interpol_ticks;
        getLocoMotion()->m_ServerPos = pos;
        getLocoMotion()->m_ServerRot = rot;
    }
    else
    {
        getLocoMotion()->setPosition(pos);
        Rainbow::Vector3f euler = QuaternionToEulerAngle(rot);
        getLocoMotion()->m_RotateYaw = euler.x;
        getLocoMotion()->m_RotationPitch = euler.y;
    }
}
```

**插值机制**:

- **远程模式**: 设置目标位置和旋转，进行平滑插值
- **本地模式**: 直接设置位置和旋转
- **插值帧数**: 通过 INTERPOL_TICKS 控制插值时间

## 8. 网络可靠性保障

### 8.1 协议可靠性级别

- **PB_ROLE_MOVE_CH**: RELIABLE_ORDERED (可靠有序)
- **PB_SYNC_MOVE_CH**: RELIABLE_ORDERED (可靠有序)
- **PB_ACTOR_MOVEV2_HC**: UNRELIABLE_SEQUENCED (不可靠有序)
- **PB_SYNC_MOVE_HC**: RELIABLE_ORDERED (可靠有序)

### 8.2 位置确认机制

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:7474-7486`

```cpp
void MpGameSurviveNetHandler::handleRespSyncMove2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer* player = uin2Player(uin);
    if (player == NULL)
        return;

    PB_ResetPosResponeCH pbCH;
    pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
    if (player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(pbCH.tick()))
        player->onClientRspSyncPos(pbCH.tick());
    else
        player->onClientRspSyncPos(0);
}
```

客户端收到位置校正后，必须发送确认响应，服务器验证时间戳确保同步的有效性。

## 9. 移动同步决策流程图

```mermaid
flowchart TD
    A[客户端移动输入] --> B{是否在未加载区块?}
    B -->|是| C{移动计数>100?}
    C -->|是| D[发送心跳协议 PB_ROLE_MOVE_CH]
    C -->|否| E[跳过发送]
    B -->|否| F{使用新版同步?}

    F -->|是| G[发送 PB_SYNC_MOVE_CH]
    F -->|否| H[发送 PB_ROLE_MOVE_CH]

    G --> I[服务器验证时间戳]
    H --> J[服务器处理旧版协议]

    I --> K{时间戳有效?}
    K -->|是| L[处理移动数据]
    K -->|否| M[忽略时间戳，继续处理]

    J --> N{位置/朝向变化?}
    N -->|否| O[仅心跳，直接返回]
    N -->|是| L

    L --> P{移动合法性检查}
    P -->|合法| Q[CRT_Accept - 接受位置]
    P -->|不合法| R[CRT_RollBack - 强制回滚]
    P -->|忽略| S[CRT_Ignore - 无操作]

    Q --> T[更新服务器玩家位置]
    R --> U[发送 PB_SYNC_MOVE_HC 校正]

    T --> V[广播给其他玩家]
    U --> W[客户端强制设置位置]
    W --> X[发送位置确认]

    V --> Y{使用批量广播?}
    Y -->|是| Z[缓存到批量队列]
    Y -->|否| AA[直接发送 PB_ACTOR_MOVEV2_HC]

    Z --> BB[定时批量发送 PB_ACTOR_MOVEV3_HC]
```

## 10. 协议选择决策表

| 场景         | 协议类型              | 可靠性     | 用途            |
| ------------ | --------------------- | ---------- | --------------- |
| 玩家主动移动 | PB_ROLE_MOVE_CH       | 可靠有序   | 基础移动同步    |
| 新版移动同步 | PB_SYNC_MOVE_CH       | 可靠有序   | 精确同步+反作弊 |
| 位置强制校正 | PB_SYNC_MOVE_HC       | 可靠有序   | 服务器权威校正  |
| 其他玩家移动 | PB_ACTOR_MOVEV2_HC    | 不可靠有序 | 高频移动广播    |
| 批量移动广播 | PB_ACTOR_MOVEV3_HC    | 不可靠有序 | 性能优化        |
| 载具移动     | PB_TrainMoveCH        | 可靠有序   | 特殊载具同步    |
| 位置确认     | PB_SYNC_PLAYER_POS_CH | 可靠有序   | 确认机制        |

## 总结

沙盒游戏的移动同步系统采用了多层协议设计：

1. **旧版协议** (PB_ROLE_MOVE_CH) 主要用于兼容性和心跳保活
2. **新版协议** (PB_SYNC_MOVE_CH/HC) 提供更精确的同步和反作弊能力
3. **广播协议** (PB_ACTOR_MOVE 系列) 负责向其他玩家同步移动信息

整个系统通过客户端预测、服务器验证、强制校正的机制，确保了多人游戏中移动的流畅性和一致性，同时具备完善的反作弊保护。

**关键技术特点**:

- **分层协议设计**: 不同场景使用不同协议
- **批量优化**: 减少网络调用次数
- **插值平滑**: 提供流畅的视觉体验
- **AOI 管理**: 只向相关玩家发送信息
- **反作弊机制**: 时间戳验证和移动监控
- **可靠性保障**: 关键协议使用可靠传输
- **特殊场景支持**: 载具、平台等特殊移动场景

这套移动同步系统在保证游戏体验流畅性的同时，通过多层次的验证和优化机制，确保了多人在线游戏的稳定性和公平性。
