#include "SandboxUtilMath.h"
#include "Math/Simd/vec-transform.h"
#include "Math/DeprecatedConversion.h"
// void QuaternionLookAt(Rainbow::Quaternionf& quaternionf, const Rainbow::Vector3f& target, Rainbow::Vector3f up)
// {
// 	Rainbow::Vector3f vF, vU;
// 	vF = target;
// 	vU = up;

// 	vF.Normalize();
// 	vU.Normalize();

// 	Rainbow::Vector3f vTempUp = vU;
// 	float fDot = DotProduct(vF, vU);

// 	if (fDot > 0.995f || fDot < -0.995f)
// 	{
// 		// Generate a new up vector
// 		vTempUp.x = vF.y;
// 		vTempUp.y = vF.z;
// 		vTempUp.z = vF.x;
// 	}

// 	Rainbow::Matrix4x4f mTemp;
// 	Rainbow::Vector3f vRight = CrossProduct(vTempUp, vF);
// 	vRight.Normalize();
// 	Rainbow::Vector3f vTrueUp = CrossProduct(vF, vRight);
// 	mTemp.SetRow(0, Rainbow::Vector4f(vRight.x, vRight.y, vRight.z, 0.0f));
// 	mTemp.SetRow(1, Rainbow::Vector4f(vTrueUp.x, vTrueUp.y, vTrueUp.z, 0.0f));
// 	mTemp.SetRow(2, Rainbow::Vector4f(vF.x, vF.y, vF.z, 0.0f));
// 	mTemp.SetRow(3, Rainbow::Vector4f(0.0f, 0.0f, 0.0f, 1.0f));

// 	//setMatrix(mTemp);
// 	Rainbow::MatrixToQuaternionf(mTemp, quaternionf);
// }

float inversesqrt(float x)
{
	return 1.0f / std::sqrt(x);
}

#define QUICK_SET_MAT3_COLOMN(m,i,v) 	\
m.m_Data33[i][0] = v.x;					\
m.m_Data33[i][1] = v.y;					\
m.m_Data33[i][2] = v.z;					\


// copy from GLM::quatLookAtLH
void QuaternionLookAt(Rainbow::Quaternionf& quaternionf,const Rainbow::Vector3f& direction,Rainbow::Vector3f up)
{
	/*Rainbow::Matrix3x3f Result;
	QUICK_SET_MAT3_COLOMN(Result,2,direction)
	Rainbow::Vector3f Right = CrossProduct(up, direction);
	auto temp1 = Right * inversesqrt(std::max(0.00001f, DotProduct(Right, Right)));
	QUICK_SET_MAT3_COLOMN(Result,0,temp1)
	auto temp2 = CrossProduct(direction, temp1);
	QUICK_SET_MAT3_COLOMN(Result,1,temp2)

	math::float3x3 rotMat = Matrix3x3fTofloat3x3(Result);
	rotMat = math::rotation(rotMat);

	Rainbow::MatrixToQuaternionf(Rainbow::float3x3ToMatrix3x3f(rotMat), quaternionf);
	*/
	Rainbow::Matrix3x3f rot;
	LookRotationToMatrix(direction, up, &rot);
	Rainbow::MatrixToQuaternionf(rot, quaternionf);
}