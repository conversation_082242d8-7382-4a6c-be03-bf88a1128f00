//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON>@inside.org
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#include "NavMesh.h"
#include "NavMeshNode.h"
#include "MathUtil.h"
#include "Public/NavMeshProjectSettings.h"
#include "Geometry/AABB.h"
#include "Geometry/Intersection.h"
#include "Jobs/Jobs.h"
#include "Jobs/JobsDebugger.h"
#include "Math/Vector3f.h"
#include "Math/Vector2f.h"
#include "Allocator/MemoryMacros.h"
#include "Utilities/Align.h"

#include <float.h>
#include <algorithm>

unsigned long long NavMesh::s_SurfaceIDGen = 1;

struct PortalArea
{
    float min, max;
};

static Rainbow::MinMaxAABB GetWorldTileBounds(const NavMeshTile& tile)
{
    Assert(tile.header);

    Rainbow::MinMaxAABB tileBounds(tile.header->bmin, tile.header->bmax);
    if (tile.transformed)
    {
        Rainbow::Matrix4x4f mat;
        mat.SetTR(tile.position, tile.rotation);
        TransformAABBSlow(tileBounds, mat, tileBounds);
    }
    return tileBounds;
}

static bool OverlapSlabs(const Rainbow::Vector2f& amin, const Rainbow::Vector2f& amax,
    const Rainbow::Vector2f& bmin, const Rainbow::Vector2f& bmax,
    const float px, const float py)
{
    // Check for horizontal overlap.
    // The segment is shrunken a little so that slabs which touch
    // at end points are not connected.
    const float minLengthShare = 0.1f * std::min(amax.x - amin.x, bmax.x - bmin.x);
    const float margin = std::min(px, minLengthShare);
    const float minx = std::max(amin.x + margin, bmin.x + margin);
    const float maxx = std::min(amax.x - margin, bmax.x - margin);
    if (minx > maxx)
        return false;

    // Check vertical overlap.
    const float ad = (amax.y - amin.y) / (amax.x - amin.x);
    const float ak = amin.y - ad * amin.x;
    const float bd = (bmax.y - bmin.y) / (bmax.x - bmin.x);
    const float bk = bmin.y - bd * bmin.x;
    const float aminy = ad * minx + ak;
    const float amaxy = ad * maxx + ak;
    const float bminy = bd * minx + bk;
    const float bmaxy = bd * maxx + bk;
    const float dmin = bminy - aminy;
    const float dmax = bmaxy - amaxy;

    // Crossing segments always overlap.
    if (dmin * dmax < 0)
        return true;

    // Check for overlap at endpoints.
    const float thr = Rainbow::Sqr(py * 2);
    if (dmin * dmin <= thr || dmax * dmax <= thr)
        return true;

    return false;
}

static bool OverlapDetailSlabs(const Rainbow::Vector2f* aslabs, const int acount,
    const Rainbow::Vector2f* bslabs, const int bcount,
    const float px, const float py)
{
    for (int i = 0; i < acount - 1; i++)
    {
        const Rainbow::Vector2f& amin = aslabs[i];
        const Rainbow::Vector2f& amax = aslabs[i + 1];
        for (int j = 0; j < bcount - 1; j++)
        {
            const Rainbow::Vector2f& bmin = bslabs[j];
            const Rainbow::Vector2f& bmax = bslabs[j + 1];
            if (OverlapSlabs(amin, amax, bmin, bmax, px, py))
                return true;
        }
    }
    return false;
}

static float GetSlabCoord(const Rainbow::Vector3f& va, const int side)
{
    if (side == 0 || side == 4)
        return va.x;
    else if (side == 2 || side == 6)
        return va.z;
    return 0;
}

static void MakeDetailEdgeSlabs(Rainbow::Vector2f* slabs, const Rainbow::Vector3f* pts, const int npts, const int side)
{
    const Rainbow::Vector3f& va = pts[0];
    const Rainbow::Vector3f& vb = pts[npts - 1];
    if (side == 0 || side == 4)
    {
        if (va.z < vb.z)
        {
            for (int i = 0; i < npts; i++)
            {
                slabs[i].x = pts[i].z;
                slabs[i].y = pts[i].y;
            }
        }
        else
        {
            for (int i = 0; i < npts; i++)
            {
                slabs[i].x = pts[npts - 1 - i].z;
                slabs[i].y = pts[npts - 1 - i].y;
            }
        }
    }
    else if (side == 2 || side == 6)
    {
        if (va.x < vb.x)
        {
            for (int i = 0; i < npts; i++)
            {
                slabs[i].x = pts[i].x;
                slabs[i].y = pts[i].y;
            }
        }
        else
        {
            for (int i = 0; i < npts; i++)
            {
                slabs[i].x = pts[npts - 1 - i].x;
                slabs[i].y = pts[npts - 1 - i].y;
            }
        }
    }
    else
    {
        // fix warning of uninitialized bmin, bmax
        Assert(false);
        for (int i = 0; i < npts; i++)
        {
            slabs[i].x = 0;
            slabs[i].y = 0;
        }
    }
}

inline int OppositeTile(int side) { return (side + 4) & 0x7; }

inline void NeighbourLocation(int* x, int* y, int side)
{
    int nx = *x, ny = *y;
    switch (side)
    {
        case 0: nx++; break;
        case 1: nx++; ny++; break;
        case 2: ny++; break;
        case 3: nx--; ny++; break;
        case 4: nx--; break;
        case 5: nx--; ny--; break;
        case 6: ny--; break;
        case 7: nx++; ny--; break;
    }
    *x = nx;
    *y = ny;
}

// It's assumed the triangle (a,b,c) is not degenerate - otherwise division by zero might occur
static Rainbow::Vector3f ClosestPtPointTriangle(const Rainbow::Vector3f& p, const Rainbow::Vector3f& a, const Rainbow::Vector3f& b, const Rainbow::Vector3f& c)
{
    // Check if P in vertex region outside A
    Rainbow::Vector3f ab = b - a;
    Rainbow::Vector3f ac = c - a;
    Rainbow::Vector3f ap = p - a;

    float d1 = Dot(ab, ap);
    float d2 = Dot(ac, ap);
    if (d1 <= 0.0f && d2 <= 0.0f)
    {
        // barycentric coordinates (1,0,0)
        return a;
    }

    // Check if P in vertex region outside B
    Rainbow::Vector3f bp = p - b;
    float d3 = Dot(ab, bp);
    float d4 = Dot(ac, bp);
    if (d3 >= 0.0f && d4 <= d3)
    {
        // barycentric coordinates (0,1,0)
        return b;
    }

    // Check if P in edge region of AB, if so return projection of P onto AB
    float vc = d1 * d4 - d3 * d2;
    if (vc <= 0.0f && d1 >= 0.0f && d3 <= 0.0f)
    {
        // barycentric coordinates (1-v,v,0)
        float v = d1 / (d1 - d3);
        return a + v * ab;
    }

    // Check if P in vertex region outside C
    Rainbow::Vector3f cp = p - c;
    float d5 = Dot(ab, cp);
    float d6 = Dot(ac, cp);
    if (d6 >= 0.0f && d5 <= d6)
    {
        // barycentric coordinates (0,0,1)
        return c;
    }

    // Check if P in edge region of AC, if so return projection of P onto AC
    float vb = d5 * d2 - d1 * d6;
    if (vb <= 0.0f && d2 >= 0.0f && d6 <= 0.0f)
    {
        // barycentric coordinates (1-w,0,w)
        float w = d2 / (d2 - d6);
        return a + w * ac;
    }

    // Check if P in edge region of BC, if so return projection of P onto BC
    float va = d3 * d6 - d5 * d4;
    if (va <= 0.0f && (d4 - d3) >= 0.0f && (d5 - d6) >= 0.0f)
    {
        // barycentric coordinates (0,1-w,w)
        float w = (d4 - d3) / ((d4 - d3) + (d5 - d6));
        return Lerp(b, c, w);
    }

    // P inside face region. Compute Q through its barycentric coordinates (u,v,w)
    float denom = 1.0f / (va + vb + vc);
    float v = vb * denom;
    float w = vc * denom;
    return a + (v * ab) + (w * ac);
}

inline bool OverlapQuantBounds(const unsigned short amin[3], const unsigned short amax[3],
    const unsigned short bmin[3], const unsigned short bmax[3])
{
    bool overlap = true;
    overlap = (amin[0] > bmax[0] || amax[0] < bmin[0]) ? false : overlap;
    overlap = (amin[1] > bmax[1] || amax[1] < bmin[1]) ? false : overlap;
    overlap = (amin[2] > bmax[2] || amax[2] < bmin[2]) ? false : overlap;
    return overlap;
}

//////////////////////////////////////////////////////////////////////////////////////////

NavMesh::NavMesh()
    : m_firstOffMeshConnection(kNavMeshNullLink)
    , m_timeStamp(1)
{
}

NavMesh::~NavMesh()
{
    SyncWriteDependencies();

    for (int i = 0; i < m_tiles.Capacity(); ++i)
    {
        NavMeshTile* tile = &m_tiles[i];
        if (!tile->header)
            continue;
        if (tile->flags &  kTileFreeData)
        {
            ENG_FREE_LABEL(tile->data, kMemAI);
            tile->data = NULL;
            tile->dataSize = 0;
        }
        ENG_FREE_LABEL(tile->polyLinks, kMemAI);
        tile->polyLinks = NULL;
    }
}

int NavMesh::CreateSurface(int reserveTileCount, const NavMeshBuildSettings& settings,
    const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& rot)
{
    SyncWriteDependencies();

    // Find an unused and non-zero id
    int id;
    do
    {
        id = s_SurfaceIDGen++;
    }
    while (id == 0 || m_SurfaceIDToData.find(id) != m_SurfaceIDToData.end());

    SurfaceData& data = m_SurfaceIDToData[id];
    data.m_Settings = settings;
    data.m_Position = pos;
    data.m_Rotation = rot;
    data.m_TileLUT.reserve(reserveTileCount);

    return id;
}

unsigned long long NavMesh::GetSurfaceId(int x, int z, int agentid)
{
    unsigned long long xx = (unsigned long long)(x + 32000);
    unsigned long long zz = (unsigned long long)(z + 32000);
    unsigned long long yy = (unsigned long long)(agentid + 1);
    return  (yy + (xx << 32) + (zz << 48));
}

unsigned long long NavMesh::CreateSurfaceById(int x, int z, const NavMeshBuildSettings& settings)
{
    SyncWriteDependencies();

    // Find an unused and non-zero id
    /*int id;
    do
    {
        id = s_SurfaceIDGen++;
    } while (id == 0 || m_SurfaceIDToData.find(id) != m_SurfaceIDToData.end());*/
    /*unsigned long long xx = (unsigned long long)(x + 65000);
    unsigned long long zz = (unsigned long long)(z + 65000);
    unsigned long long yy = (unsigned long long)(settings.agentTypeID + 65000);
    int id = xx + (zz << 20) + (yy << 40)*/
    unsigned long long id = GetSurfaceId(x, z, settings.agentTypeID);
    SurfaceData& data = m_SurfaceIDToData[id];
    data.m_Settings = settings;
    data.m_Position = Rainbow::Vector3f(0, 0, 0);
    data.m_Rotation = Rainbow::Quaternionf::identity;
    data.m_TileLUT.reserve(1);

    return id;
}

void NavMesh::RemoveSurface(unsigned long long surfaceID)
{
    SyncWriteDependencies();
    AssertFormatMsg(m_SurfaceIDToData.find(surfaceID) != m_SurfaceIDToData.end(), "Trying to remove NavMesh Surface with unknown id: %i", surfaceID);
    m_SurfaceIDToData.erase(surfaceID);
}

const NavMeshBuildSettings* NavMesh::GetSurfaceSettings(int surfaceID) const
{
    SurfaceIDMap::const_iterator found = m_SurfaceIDToData.find(surfaceID);
    if (found == m_SurfaceIDToData.end())
        return NULL;
    return &found->second.m_Settings;
}

void NavMesh::SetSurfaceSettings(unsigned long long surfaceID, const NavMeshBuildSettings& settings)
{
    SyncWriteDependencies();

    AssertFormatMsg(m_SurfaceIDToData.find(surfaceID) != m_SurfaceIDToData.end(), "Trying to change settings for NavMesh Surface with unknown id: %i", surfaceID);

    m_SurfaceIDToData[surfaceID].m_Settings = settings;
}

NavMeshStatus NavMesh::GetSurfaceTransform(int surfaceID, Rainbow::Vector3f* pos, Rainbow::Quaternionf* rot) const
{
    SurfaceIDMap::const_iterator found = m_SurfaceIDToData.find(surfaceID);
    if (found == m_SurfaceIDToData.end())
        return kNavMeshFailure;

    *pos = found->second.m_Position;
    *rot = found->second.m_Rotation;
    return kNavMeshSuccess;
}

//////////////////////////////////////////////////////////////////////////////////////////
int NavMesh::FindConnectingPolys(const Rainbow::Vector3f* fromPoints, const int fromPointCount,
    const NavMeshTile* tile, int side,
    NavMeshPolyRef* con, struct PortalArea* conarea, int maxcon, float portalHeight) const
{
    if (!tile)
        return 0;
    if (fromPointCount < 2)
        return 0;

    const float portalHalfHeight = 0.5f * portalHeight; // Slab check is symmetrical (kinda like radius).
    static const int kMaxPoints = 16;

    Rainbow::Vector2f fromSlabs[kMaxPoints];
    MakeDetailEdgeSlabs(fromSlabs, fromPoints, fromPointCount, side);

    const Rainbow::Vector2f& fromMin = fromSlabs[0];
    const Rainbow::Vector2f& fromMax = fromSlabs[fromPointCount - 1];
    const float fromCoord = GetSlabCoord(fromPoints[0], side);

    Rainbow::Vector3f toPoints[kMaxPoints];
    Rainbow::Vector2f toSlabs[kMaxPoints];
    int toPointCount = 0;

    // Remove links pointing to 'side' and compact the links array.
    unsigned short m = kNavMeshExtLink | (unsigned short)side;
    int n = 0;

    NavMeshPolyRef base = GetPolyRefBase(tile);

    for (int i = 0; i < tile->header->polyCount; ++i)
    {
        NavMeshPoly* poly = &tile->polys[i];
        const int nv = poly->vertCount;
        for (int j = 0; j < nv; ++j)
        {
            // Skip edges which do not point to the right side.
            if (poly->neis[j] != m)
                continue;

            // Segments are not close enough along the border.
            const float toCoord = GetSlabCoord(tile->verts[poly->verts[j]], side);
            if (Rainbow::Abs(fromCoord - toCoord) > 1.0f)
                continue;

            GetPolyEdgeDetailPoints(tile, i, j, toPoints, &toPointCount, kMaxPoints);
            if (!toPointCount)
                continue;

            MakeDetailEdgeSlabs(toSlabs, toPoints, toPointCount, side);
            const Rainbow::Vector2f& toMin = toSlabs[0];
            const Rainbow::Vector2f& toMax = toSlabs[toPointCount - 1];

            // Check if the segments touch.
            if (!OverlapDetailSlabs(fromSlabs, fromPointCount, toSlabs, toPointCount, 1.0f, portalHalfHeight))
                continue;

            // Add return value.
            if (n < maxcon)
            {
                conarea[n].min = std::max(fromMin.x, toMin.x);
                conarea[n].max = std::min(fromMax.x, toMax.x);
                con[n] = base | EncodeBasePolyId(kPolyTypeGround, i);
                n++;
            }
            break;
        }
    }
    return n;
}

void NavMesh::RemoveLinkBetween(NavMeshPolyRef from, NavMeshPolyRef to)
{
    SyncWriteDependencies();

    if (DecodePolyIdType(from) == kPolyTypeOffMeshConnection)
    {
        OffMeshConnection* con = GetOffMeshConnectionUnsafe(from);
        if (con != NULL)
        {
            // Remove link from connect polygon.
            unsigned int k = con->firstLink;
            unsigned int pk = kNavMeshNullLink;
            while (k != kNavMeshNullLink)
            {
                if (m_links[k].ref == to)
                {
                    unsigned int nk = m_links[k].next;
                    if (pk == kNavMeshNullLink)
                    {
                        con->firstLink = nk;
                    }
                    else
                    {
                        Assert(pk != nk);
                        m_links[pk].next = nk;
                    }
                    con->endPoints[m_links[k].edge].tileRef = 0;
                    m_links.Release(k);
                    break;
                }
                pk = k;
                k = m_links[k].next;
            }
        }
    }
    else
    {
        NavMeshTile* neiTile = NULL;
        const NavMeshPoly* neiPoly = NULL;
        if (NavMeshStatusSucceed(GetTileAndPolyByRef(from, &neiTile, &neiPoly)))
        {
            // Remove link from connect polygon.
            const unsigned int ip = GetPolyIndex(neiTile, neiPoly);
            unsigned int k = neiTile->polyLinks[ip];
            unsigned int pk = kNavMeshNullLink;
            while (k != kNavMeshNullLink)
            {
                if (m_links[k].ref == to)
                {
                    unsigned int nk = m_links[k].next;
                    if (pk == kNavMeshNullLink)
                    {
                        neiTile->polyLinks[ip] = nk;
                    }
                    else
                    {
                        Assert(pk != nk);
                        m_links[pk].next = nk;
                    }
                    m_links.Release(k);
                    break;
                }
                pk = k;
                k = m_links[k].next;
            }
        }
    }
}

void NavMesh::UnconnectLinks(NavMeshTile* tile)
{
    Assert(tile);
    Assert(tile->header);
    Assert(tile->polyLinks);

    SyncWriteDependencies();

    NavMeshPolyRef base = GetPolyRefBase(tile);
    for (int i = 0; i < tile->header->polyCount; ++i)
    {
        NavMeshPolyRef polyRef = base | EncodeBasePolyId(kPolyTypeGround, i);

        unsigned int j = tile->polyLinks[i];
        while (j != kNavMeshNullLink)
        {
            unsigned int next = m_links[j].next;

            // Remove link to this polygon
            RemoveLinkBetween(m_links[j].ref, polyRef);

            m_links.Release(j);
            j = next;
        }
        tile->polyLinks[i] = kNavMeshNullLink;
    }
}

struct EdgePointSample
{
    float t;
    Rainbow::Vector3f pt;

    friend bool operator<(const EdgePointSample& lhs, const EdgePointSample& rhs)
    {
        return lhs.t < rhs.t;
    }
};

NavMeshStatus NavMesh::GetPolyEdgeDetailPoints(const NavMeshTile* tile, int p, int edge,
    Rainbow::Vector3f* points, int* pointCount, const int maxPoints) const
{
    Assert(maxPoints > 2);

    const NavMeshPoly* poly = &tile->polys[p];
    const NavMeshPolyDetail* pd = &tile->detailMeshes[p];

    const int ndv = pd->vertCount;
    const int vertCount = poly->vertCount;
    const Rainbow::Vector3f& v0 = tile->verts[poly->verts[edge]];
    const Rainbow::Vector3f& v1 = tile->verts[poly->verts[NextIndex(edge, vertCount)]];

    static const int kMaxSamples = 64;
    EdgePointSample samples[kMaxSamples];

    // Add first edge vertex
    samples[0].t = 0.0f;
    samples[0].pt = v0;
    int sampleCount = 1;

    // Collect detail vertices close to the edge.
    // adds up to kMaxSamples-2 detail vertices.
    if (ndv)
    {
        const float EPS = 0.01f;

        const float dvx = v1.x - v0.x;
        const float dvz = v1.z - v0.z;
        const float lenSq = dvx * dvx + dvz * dvz;
        if (lenSq < 4 * EPS * EPS)
        {
            *pointCount = 0;
            return kNavMeshFailure;
        }

        const float distanceThr = Rainbow::Sqr(1.0f);
        const float nrm = 1.0f / lenSq;
        const Rainbow::Vector3f* dverts = &tile->detailVerts[pd->vertBase];
        for (int i = 0; i < ndv; i++)
        {
            const Rainbow::Vector3f& dv = dverts[i];
            const float dpx = dv.x - v0.x;
            const float dpz = dv.z - v0.z;
            const float t = Rainbow::FloatClamp((dvx * dpx + dvz * dpz) * nrm, 0.0f , 1.0f);

            // Skip points projected to end points of edge
            if (t < EPS || t > (1.0f - EPS))
                continue;

            // Skip points not close to edge
            const float x = t * dvx - dpx;
            const float z = t * dvz - dpz;
            float distSqr = x * x + z * z;
            if (distSqr > distanceThr)
                continue;

            // Accept and add point.
            samples[sampleCount].t = t;
            samples[sampleCount].pt = dv;
            sampleCount++;

            // Leave room for last vertex
            if (sampleCount == kMaxSamples - 1)
                break;
        }

        // Order the collected detail vertices
        std::sort(samples + 1, samples + sampleCount);
    }

    // Add last edge vertex
    samples[sampleCount].t = 1.0f;
    samples[sampleCount].pt = v1;
    sampleCount++;

    // Simplify if needed.
    while (sampleCount > maxPoints)
    {
        // Find
        int removeIdx = -1;
        float minDist = FLT_MAX;
        for (int i = 1; i < sampleCount - 1; i++)
        {
            float t;
            const float d = SqrDistancePointSegment(&t, samples[i].pt, samples[i - 1].pt, samples[i + 1].pt);
            if (d < minDist)
            {
                minDist = d;
                removeIdx = i;
            }
        }
        // Remove removeIdx
        for (int i = removeIdx; i < sampleCount - 1; i++)
            samples[i] = samples[i + 1];
        sampleCount--;
    }

    for (int i = 0; i < sampleCount; i++)
        points[i] = samples[i].pt;

    *pointCount = sampleCount;

    return kNavMeshSuccess;
}

void NavMesh::ConnectExtLinks(NavMeshTile* tile, NavMeshTile* target, int side, float portalHeight)
{
    if (!tile)
        return;

    //specific: Do not connect tiles from different surfaces.
    // We expect each navmesh surface to be separate and connected to each other only via off-mesh connections.
    if ((tile->surfaceID & 0xfff) != (target->surfaceID & 0xfff))
        return;

    SyncWriteDependencies();

    // Connect border links.
    for (int i = 0; i < tile->header->polyCount; ++i)
    {
        NavMeshPoly* poly = &tile->polys[i];

        const int nv = poly->vertCount;
        for (int j = 0; j < nv; ++j)
        {
            // Skip non-portal edges.
            if ((poly->neis[j] & kNavMeshExtLink) == 0)
                continue;

            const int dir = (int)(poly->neis[j] & 0xff);
            if (side != -1 && dir != side)
                continue;

            // Create new links
            const Rainbow::Vector3f& va = tile->verts[poly->verts[j]];
            const Rainbow::Vector3f& vb = tile->verts[poly->verts[NextIndex(j, nv)]];

            static const int kMaxPoints = 16;
            Rainbow::Vector3f points[kMaxPoints];
            int pointCount = 0;
            GetPolyEdgeDetailPoints(tile, i, j, points, &pointCount, kMaxPoints);

            NavMeshPolyRef nei[4];
            PortalArea neia[4];
            int nnei = FindConnectingPolys(points, pointCount, target, OppositeTile(dir), nei, neia, 4, portalHeight);
            for (int k = 0; k < nnei; ++k)
            {
                unsigned int idx = m_links.Alloc();
                if (idx != kNavMeshNullLink)
                {
                    NavMeshLink* link = &m_links[idx];
                    link->ref = nei[k];
                    link->edge = (unsigned char)j;
                    link->side = (unsigned char)dir;

                    Assert(idx != tile->polyLinks[i]);
                    link->next = tile->polyLinks[i];
                    tile->polyLinks[i] = idx;

                    // Compress portal limits to a byte value.
                    if (dir == 0 || dir == 4)
                    {
                        float tmin = (neia[k].min - va.z) / (vb.z - va.z);
                        float tmax = (neia[k].max - va.z) / (vb.z - va.z);
                        if (tmin > tmax)
                            std::swap(tmin, tmax);
                        link->bmin = (unsigned char)(Rainbow::FloatClamp(tmin, 0.0f, 1.0f) * 255.0f);
                        link->bmax = (unsigned char)(Rainbow::FloatClamp(tmax, 0.0f, 1.0f) * 255.0f);
                    }
                    else if (dir == 2 || dir == 6)
                    {
                        float tmin = (neia[k].min - va.x) / (vb.x - va.x);
                        float tmax = (neia[k].max - va.x) / (vb.x - va.x);
                        if (tmin > tmax)
                            std::swap(tmin, tmax);
                        link->bmin = (unsigned char)(Rainbow::FloatClamp(tmin, 0.0f, 1.0f) * 255.0f);
                        link->bmax = (unsigned char)(Rainbow::FloatClamp(tmax, 0.0f, 1.0f) * 255.0f);
                    }
                }
            }
        }
    }
}

void NavMesh::ConnectIntLinks(NavMeshTile* tile)
{
    Assert(tile);
    Assert(tile->header);

    SyncWriteDependencies();

    const int polyCount = tile->header->polyCount;

    // Allocate and nullify per poly links
    Assert(!tile->polyLinks);
    tile->polyLinks = (unsigned int*)ENG_MALLOC_LABEL(polyCount * sizeof(unsigned int), kMemAI);
    for (int i = 0; i < polyCount; ++i)
        tile->polyLinks[i] = kNavMeshNullLink;

    NavMeshPolyRef base = GetPolyRefBase(tile);

    for (int i = 0; i < polyCount; ++i)
    {
        NavMeshPoly* poly = &tile->polys[i];

        // Build edge links backwards so that the links will be
        // in the linked list from lowest index to highest.
        for (int j = poly->vertCount - 1; j >= 0; --j)
        {
            // Skip hard and non-internal edges.
            if (poly->neis[j] == 0 || (poly->neis[j] & kNavMeshExtLink))
                continue;

            unsigned int idx = m_links.Alloc();
            if (idx != kNavMeshNullLink)
            {
                NavMeshLink* link = &m_links[idx];
                link->ref = base | EncodeBasePolyId(kPolyTypeGround, poly->neis[j] - 1);
                link->edge = (unsigned char)j;
                link->side = 0xff;
                link->bmin = link->bmax = 0;
                // Add to linked list.
                Assert(idx != tile->polyLinks[i]);
                link->next = tile->polyLinks[i];
                tile->polyLinks[i] = idx;
            }
        }
    }
}

void NavMesh::ClosestPointOnPolyInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly,
    const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const
{
    const unsigned int ip = GetPolyIndex(tile, poly);
    const NavMeshPolyDetail* pd = &tile->detailMeshes[ip];

    float closestDistSqr = FLT_MAX;
    for (int j = 0; j < pd->triCount; ++j)
    {
        const NavMeshPolyDetailIndex* t = &tile->detailTris[(pd->triBase + j) * 4];
        Rainbow::Vector3f v[3];
        for (int k = 0; k < 3; ++k)
        {
            if (t[k] < poly->vertCount)
                v[k] = tile->verts[poly->verts[t[k]]];
            else
                v[k] = tile->detailVerts[pd->vertBase + (t[k] - poly->vertCount)];
        }

        const Rainbow::Vector3f pt = ClosestPtPointTriangle(pos, v[0], v[1], v[2]);
        const float d = SqrDistance(pos, pt);
        if (d < closestDistSqr)
        {
            *closest = pt;
            closestDistSqr = d;
        }
    }
}

// It's assumed the line (a,b) is not degenerate - otherwise division by zero might occur
static Rainbow::Vector3f ClosestPtPointLine(const Rainbow::Vector3f& p, const Rainbow::Vector3f& a, const Rainbow::Vector3f& b)
{
    // Check if P in vertex region outside A
    Rainbow::Vector3f ab = b - a;
    Rainbow::Vector3f ap = p - a;

    float d1 = Dot(ab, ap);

    if (d1 <= 0.0f)
    {
        // barycentric coordinates (1,0,0)
        return a;
    }

    // Check if P in vertex region outside B
    Rainbow::Vector3f bp = p - b;
    float d3 = Dot(ab, bp);

    if (d3 >= 0.0f)
    {
        // barycentric coordinates (0,1,0)
        return b;
    }

    // barycentric coordinates (1-v,v,0)
    float v = d1 / (d1 - d3);

    return a + v * ab;
}

void NavMesh::ClosestPointOnPolyBoundaryInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly,
    const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const
{
    const unsigned int ip = GetPolyIndex(tile, poly);
    const NavMeshPolyDetail* pd = &tile->detailMeshes[ip];

    Rainbow::Vector3f pt;
    float d;
    float closestDistSqr = FLT_MAX;

    for (int j = 0; j < pd->triCount; ++j)
    {
        const NavMeshPolyDetailIndex* t = &tile->detailTris[(pd->triBase + j) * 4];
        Rainbow::Vector3f v[3];
        for (int k = 0; k < 3; ++k)
        {
            if (t[k] < poly->vertCount)
                v[k] = tile->verts[poly->verts[t[k]]];
            else
                v[k] = tile->detailVerts[pd->vertBase + (t[k] - poly->vertCount)];
        }

        if ((t[3] & 0x3F) == 0)
            continue;

        for (size_t m = 0, n = 2; m < 3; n = m++)
        {
            // Skip inner detail edges.
            if (((t[3] >> (n * 2)) & 0x3) == 0)
                continue;

            pt = ClosestPtPointLine(pos, v[n], v[m]);
            d = SqrDistance(pos, pt);

            if (d < closestDistSqr)
            {
                *closest = pt;
                closestDistSqr = d;
            }
        }
    }
}

NavMeshPolyRef NavMesh::FindNearestPoly(int typeID, const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, Rainbow::Vector3f* nearestPt) const
{
    struct NearestQuery : public NavMeshProcessCallback
    {
        const NavMesh* m_NavMesh;
        NavMeshPolyRef m_PolyRef;
        float m_DistanceSqr;
        Rainbow::Vector3f m_Center;
        Rainbow::Vector3f m_Point;

        inline NearestQuery(const NavMesh* navmesh, const Rainbow::Vector3f& center)
            : m_NavMesh(navmesh) , m_PolyRef(0) , m_DistanceSqr(FLT_MAX)
            , m_Center(center) , m_Point(0.0f, 0.0f, 0.0f)
        {
        }

        virtual void ProcessPolygons(const NavMeshTile* tile, const NavMeshPolyRef* polyRefs, const NavMeshPoly** polys, const int itemCount)
        {
            const Rainbow::Vector3f localPosition = WorldToTile(*tile, m_Center);
            // Find nearest polygon amongst the nearby polygons.
            for (int i = 0; i < itemCount; ++i)
            {
                NavMeshPolyRef ref = polyRefs[i];
                const NavMeshPoly* poly = polys[i];
                Rainbow::Vector3f closestPtPoly;
                m_NavMesh->ClosestPointOnPolyInTileLocal(tile, poly, localPosition, &closestPtPoly);
                const float d = SqrDistance(localPosition, closestPtPoly);
                if (d < m_DistanceSqr)
                {
                    m_Point = closestPtPoly;
                    m_DistanceSqr = d;
                    m_PolyRef = ref;
                }
            }
        }
    };

    // Get nearby polygons from proximity grid.
    NearestQuery nearest(this, center);
    QueryPolygons(typeID, center, extents, &nearest);

    if (nearest.m_PolyRef == 0)
        return 0;

    if (nearestPt)
    {
        const NavMeshTile* tile = GetTileByRef(nearest.m_PolyRef);
        if (tile)
            *nearestPt = TileToWorld(*tile, nearest.m_Point);
    }

    return nearest.m_PolyRef;
}

void NavMesh::QueryPolygons(int typeID, const Rainbow::Vector3f& cen, const Rainbow::Vector3f& ext,
    NavMeshProcessCallback* callback) const
{
    const Rainbow::MinMaxAABB bounds = Rainbow::MinMaxAABB(cen - ext, cen + ext);

    SurfaceIDMap::const_iterator it = m_SurfaceIDToData.begin();
    SurfaceIDMap::const_iterator end = m_SurfaceIDToData.end();

    for (; it != end; ++it)
    {
        const SurfaceData& surf = it->second;
        if (typeID != -1 && typeID != surf.m_Settings.agentTypeID)
            continue;

        Rainbow::MinMaxAABB overlap;
        if (!IntersectionAABBAABB(bounds, surf.m_WorldBounds, &overlap))
            continue;

        Rainbow::AABB localBounds;
        InverseTransformAABB(Rainbow::AABB(overlap), surf.m_Position, surf.m_Rotation, localBounds);

        const float invTileSize = 1.0f / (surf.m_Settings.tileSize * surf.m_Settings.cellSize);

        int iminx = Rainbow::FloorfToInt((localBounds.m_Center.x - localBounds.m_Extent.x) * invTileSize);
        int imaxx = Rainbow::FloorfToInt((localBounds.m_Center.x + localBounds.m_Extent.x) * invTileSize);

        int jminz = Rainbow::FloorfToInt((localBounds.m_Center.z - localBounds.m_Extent.z) * invTileSize);
        int jmaxz = Rainbow::FloorfToInt((localBounds.m_Center.z + localBounds.m_Extent.z) * invTileSize);

        for (int i = iminx; i <= imaxx; ++i)
        {
            for (int j = jminz; j <= jmaxz; ++j)
            {
                TileLUT::const_iterator found = surf.m_TileLUT.find(std::make_pair(i, j));
                if (found == surf.m_TileLUT.end())
                    continue;

                const int tileIndex = found->second;
                QueryPolygonsInTile(&m_tiles[tileIndex], localBounds.m_Center, localBounds.m_Extent, callback);
            }
        }
    }
}

void NavMesh::QueryPolygonsInTile(const NavMeshTile* tile, const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, NavMeshProcessCallback* callback) const
{
    static const int kBufferSize = 32;
    NavMeshPolyRef polyRefs[kBufferSize];
    const NavMeshPoly* polys[kBufferSize];

    const Rainbow::Vector3f qmin = center - extents;
    const Rainbow::Vector3f qmax = center + extents;

    if (tile->bvTree)
    {
        const NavMeshBVNode* node = &tile->bvTree[0];
        // The final node is garbage as we allocate 2*polyCount but need one less for the tree.
        // Here we make sure the end node is not reached by subtracting 1
        const NavMeshBVNode* end = &tile->bvTree[tile->header->bvNodeCount - 1];
        const Rainbow::Vector3f tbmin(tile->header->bmin);
        const Rainbow::Vector3f tbmax(tile->header->bmax);
        const float qfac = tile->header->bvQuantFactor;

        // Calculate quantized box
        unsigned short bmin[3], bmax[3];
        // FloatClamp query box to world box.
        float minx = Rainbow::FloatClamp(qmin.x, tbmin.x, tbmax.x) - tbmin.x;
        float miny = Rainbow::FloatClamp(qmin.y, tbmin.y, tbmax.y) - tbmin.y;
        float minz = Rainbow::FloatClamp(qmin.z, tbmin.z, tbmax.z) - tbmin.z;
        float maxx = Rainbow::FloatClamp(qmax.x, tbmin.x, tbmax.x) - tbmin.x;
        float maxy = Rainbow::FloatClamp(qmax.y, tbmin.y, tbmax.y) - tbmin.y;
        float maxz = Rainbow::FloatClamp(qmax.z, tbmin.z, tbmax.z) - tbmin.z;
        // Quantize
        bmin[0] = (unsigned short)(qfac * minx) & 0xfffe;
        bmin[1] = (unsigned short)(qfac * miny) & 0xfffe;
        bmin[2] = (unsigned short)(qfac * minz) & 0xfffe;
        bmax[0] = (unsigned short)(qfac * maxx + 1) | 1;
        bmax[1] = (unsigned short)(qfac * maxy + 1) | 1;
        bmax[2] = (unsigned short)(qfac * maxz + 1) | 1;

        // Traverse tree
        NavMeshPolyRef base = GetPolyRefBase(tile);
        int n = 0;
        while (node < end)
        {
            const bool overlap = OverlapQuantBounds(bmin, bmax, node->bmin, node->bmax);
            const bool isLeafNode = node->i >= 0;

            if (isLeafNode && overlap)
            {
                if (n >= kBufferSize)
                {
                    callback->ProcessPolygons(tile, polyRefs, polys, n);
                    n = 0;
                }
                polyRefs[n] = base | EncodeBasePolyId(kPolyTypeGround, node->i);
                polys[n] = &tile->polys[node->i];
                n++;
            }

            if (overlap || isLeafNode)
                node++;
            else
            {
                const int escapeIndex = -node->i;
                node += escapeIndex;
            }
        }
        if (n > 0)
            callback->ProcessPolygons(tile, polyRefs, polys, n);
    }
    else
    {
        Rainbow::Vector3f bmin, bmax;
        int n = 0;
        NavMeshPolyRef base = GetPolyRefBase(tile);
        for (int i = 0; i < tile->header->polyCount; ++i)
        {
            // Calc polygon bounds.
            NavMeshPoly* p = &tile->polys[i];
            bmin = bmax = tile->verts[p->verts[0]];
            for (int j = 1; j < p->vertCount; ++j)
            {
                const Rainbow::Vector3f& v = tile->verts[p->verts[j]];
                bmin = Rainbow::Minimize(bmin, v);
                bmax = Rainbow::Maximize(bmax, v);
            }
            if (OverlapBounds(qmin, qmax, bmin, bmax))
            {
                if (n >= kBufferSize)
                {
                    callback->ProcessPolygons(tile, polyRefs, polys, n);
                    n = 0;
                }
                polyRefs[n] = base | EncodeBasePolyId(kPolyTypeGround, i);
                polys[n] = p;
                n++;
            }
        }
        if (n > 0)
            callback->ProcessPolygons(tile, polyRefs, polys, n);
    }
}

NavMeshStatus NavMesh::AddTile(const unsigned char* data, int dataSize, NavMeshTileFlags flags,
    unsigned long long surfaceID, NavMeshTileRef* result)
{
    // Make sure the data is in right format.
    NavMeshDataHeader * header = (NavMeshDataHeader*)data;
    if (header->magic != kNavMeshMagic)
        return kNavMeshFailure | kNavMeshWrongMagic;
    if (header->version != kNavMeshVersion)
        return kNavMeshFailure | kNavMeshWrongVersion;

    // Patch header pointers.
    const int headerSize = Align4(sizeof(NavMeshDataHeader));
    const int vertsSize = Align4(sizeof(Rainbow::Vector3f) * header->vertCount);
    const int polysSize = Align4(sizeof(NavMeshPoly) * header->polyCount);
    const int detailMeshesSize = Align4(sizeof(NavMeshPolyDetail) * header->detailMeshCount);
    const int detailVertsSize = Align4(sizeof(Rainbow::Vector3f) * header->detailVertCount);
    const int detailTrisSize = Align4(sizeof(NavMeshPolyDetailIndex) * 4 * header->detailTriCount);
    const int bvtreeSize = Align4(sizeof(NavMeshBVNode) * header->bvNodeCount);

    const unsigned char* d = data + headerSize;
    const Rainbow::Vector3f* verts = (const Rainbow::Vector3f*)d; d += vertsSize;
    NavMeshPoly* polys = (NavMeshPoly*)d; d += polysSize;
    const NavMeshPolyDetail* detailMeshes = (NavMeshPolyDetail*)d; d += detailMeshesSize;
    const Rainbow::Vector3f* detailVerts = (const Rainbow::Vector3f*)d; d += detailVertsSize;
    const NavMeshPolyDetailIndex* detailTris = (const NavMeshPolyDetailIndex*)d; d += detailTrisSize;
    const NavMeshBVNode* bvTree = (const NavMeshBVNode*)d; d += bvtreeSize;

    if ((d - data) != dataSize)
        return kNavMeshFailure;

    SyncWriteDependencies();

    // Allocate a tile.
    const unsigned int newTileIndex = m_tiles.Alloc();

    // Make sure we could allocate a tile.
    if (newTileIndex == kNavMeshNullLink)
        return kNavMeshFailure | kNavMeshOutOfMemory;

    AssertMsg(m_SurfaceIDToData.find(surfaceID) != m_SurfaceIDToData.end(), "Invalid surface ID");

    NavMeshTile* tile = &m_tiles[newTileIndex];

    // Insert tile into the position lut.
    SurfaceData& surfaceData = m_SurfaceIDToData[surfaceID];
    TileLUT& lut = surfaceData.m_TileLUT;
    lut[std::make_pair(header->x, header->y)] = newTileIndex;

    const Rainbow::Vector3f position = surfaceData.m_Position;
    const Rainbow::Quaternionf rotation = surfaceData.m_Rotation;

    // Consider: Post process all these
    Rainbow::AABB tileWorldBounds;
    Rainbow::MinMaxAABB tilebounds(header->bmin, header->bmax);
    TransformAABB(Rainbow::AABB(tilebounds), position, rotation, tileWorldBounds);
    surfaceData.m_WorldBounds.Encapsulate(Rainbow::MinMaxAABB(tileWorldBounds));

    // Patch header pointers.
    tile->verts = verts;
    tile->polys = polys;
    tile->detailMeshes = detailMeshes;
    tile->detailVerts = detailVerts;
    tile->detailTris = detailTris;
    tile->bvTree = bvTree;

    // If there are no items in the bvtree, reset the tree pointer.
    if (!bvtreeSize)
        tile->bvTree = 0;

    // Init tile.
    tile->surfaceID = surfaceID;
    tile->header = header;
    tile->polyLinks = 0;
    tile->data = data;
    tile->dataSize = dataSize;
    tile->flags = flags;

    if (CompareApproximately(Rainbow::Vector3f(0, 0, 0), position) && CompareApproximately(Rainbow::Quaternionf::identity, rotation))
    {
        tile->rotation = Rainbow::Quaternionf::identity;
        tile->position = Rainbow::Vector3f(0, 0, 0);
        tile->transformed = 0;
    }
    else
    {
        tile->rotation = rotation;
        tile->position = position;
        tile->transformed = 1;
    }

    ConnectIntLinks(tile);
   
    int agentid = surfaceData.m_Settings.agentTypeID;
    // Create connections to the 4 nearest neighbour tiles.
    const float portalHeight = surfaceData.m_Settings.cellSize;
    for (int i = 0; i < 8; i += 2)
    {
        int x = tile->header->x;
        int y = tile->header->y;
        NeighbourLocation(&x, &y, i);

        auto surface_it = m_SurfaceIDToData.find(GetSurfaceId(x, y, agentid));
        if (surface_it == m_SurfaceIDToData.end())
        {
            continue;
        }
        TileLUT::const_iterator it = surface_it->second.m_TileLUT.find(std::make_pair(x, y));
        if (it == surface_it->second.m_TileLUT.end())
            continue;
        NavMeshTile* ntile = &m_tiles[it->second];

        ConnectExtLinks(tile, ntile, i, portalHeight);
        ConnectExtLinks(ntile, tile, OppositeTile(i), portalHeight);
    }

    ConnectOffMeshConnectionsToTile(tile);

    if (result)
        *result = GetTileRef(tile);

    return kNavMeshSuccess;
}

const NavMeshTile* NavMesh::GetTileByRef(NavMeshTileRef ref) const
{
    if (!ref)
        return 0;
    unsigned int tileIndex = DecodePolyIdTile((NavMeshPolyRef)ref);
    unsigned int tileSalt = DecodePolyIdSalt((NavMeshPolyRef)ref);
    if ((int)tileIndex >= m_tiles.Capacity())
        return 0;
    const NavMeshTile* tile = &m_tiles[tileIndex];
    if (tile->salt != tileSalt)
        return 0;
    return tile;
}

NavMeshStatus NavMesh::GetTileAndPolyByRef(const NavMeshPolyRef ref, NavMeshTile** tile, const NavMeshPoly** poly)
{
    if (!ref)
        return kNavMeshFailure;
    unsigned int salt, it, type, ip;
    DecodePolyId(&salt, &it, &type, &ip, ref);
    if (it >= (unsigned int)m_tiles.Capacity())
        return kNavMeshFailure | kNavMeshInvalidParam;
    if (m_tiles[it].salt != salt || m_tiles[it].header == 0)
        return kNavMeshFailure | kNavMeshInvalidParam;
    SyncWriteDependencies();
    if (type == kPolyTypeOffMeshConnection)
    {
        *tile = 0;
        *poly = 0;
    }
    else
    {
        if (ip >= (unsigned int)m_tiles[it].header->polyCount)
            return kNavMeshFailure | kNavMeshInvalidParam;
        *tile = &m_tiles[it];
        *poly = &m_tiles[it].polys[ip];
    }
    return kNavMeshSuccess;
}

NavMeshStatus NavMesh::GetTileAndPolyByRef(const NavMeshPolyRef ref, const NavMeshTile** tile, const NavMeshPoly** poly) const
{
    if (!ref)
        return kNavMeshFailure;
    unsigned int salt, it, type, ip;
    DecodePolyId(&salt, &it, &type, &ip, ref);
    if (it >= (unsigned int)m_tiles.Capacity())
        return kNavMeshFailure | kNavMeshInvalidParam;
    if (m_tiles[it].salt != salt || m_tiles[it].header == 0)
        return kNavMeshFailure | kNavMeshInvalidParam;
    if (type == kPolyTypeOffMeshConnection)
    {
        *tile = 0;
        *poly = 0;
    }
    else
    {
        if (ip >= (unsigned int)m_tiles[it].header->polyCount)
            return kNavMeshFailure | kNavMeshInvalidParam;
        *tile = &m_tiles[it];
        *poly = &m_tiles[it].polys[ip];
    }
    return kNavMeshSuccess;
}

void NavMesh::GetTileAndPolyByRefUnsafe(const NavMeshPolyRef ref, const NavMeshTile** tile, const NavMeshPoly** poly) const
{
    unsigned int salt, it, type, ip;
    DecodePolyId(&salt, &it, &type, &ip, ref);
    if (type == kPolyTypeOffMeshConnection)
    {
        *tile = 0;
        *poly = 0;
    }
    else
    {
        *tile = &m_tiles[it];
        *poly = &m_tiles[it].polys[ip];
    }
}

bool NavMesh::IsValidPolyRef(NavMeshPolyRef ref) const
{
    if (!ref)
        return false;
    unsigned int salt, it, type, ip;
    DecodePolyId(&salt, &it, &type, &ip, ref);
    if (type == kPolyTypeOffMeshConnection)
    {
        if (ip >= m_offMeshConnections.Capacity())
            return false;
        if (m_offMeshConnections[ip].salt != salt)
            return false;
    }
    else
    {
        if (it >= (unsigned int)m_tiles.Capacity())
            return false;
        if (m_tiles[it].salt != salt || m_tiles[it].header == 0)
            return false;
        if (ip >= (unsigned int)m_tiles[it].header->polyCount)
            return false;
    }
    return true;
}

int NavMesh::GetAgentTypeIdForPolyRef(NavMeshPolyRef ref) const
{
    if (!ref)
        return false;

    unsigned int salt, it, type, ip;
    DecodePolyId(&salt, &it, &type, &ip, ref);

    if (type == kPolyTypeOffMeshConnection)
    {
        if (ip >= m_offMeshConnections.Capacity())
            return NavMeshProjectSettings::kInvalidAgentTypeID;

        if (m_offMeshConnections[ip].salt != salt)
            return NavMeshProjectSettings::kInvalidAgentTypeID;

        return m_offMeshConnections[ip].agentTypeID;
    }
    else
    {
        if (it >= (unsigned int)m_tiles.Capacity())
            return NavMeshProjectSettings::kInvalidAgentTypeID;

        if (m_tiles[it].salt != salt || m_tiles[it].header == 0)
            return NavMeshProjectSettings::kInvalidAgentTypeID;

        if (ip >= (unsigned int)m_tiles[it].header->polyCount)
            return NavMeshProjectSettings::kInvalidAgentTypeID;

        return m_tiles[it].header->agentTypeId;
    }
}

NavMeshStatus NavMesh::RemoveTile(NavMeshTileRef ref, unsigned long long surfaceID, const unsigned char** data, int* dataSize)
{
    if (!ref)
        return kNavMeshFailure | kNavMeshInvalidParam;
    unsigned int tileIndex = DecodePolyIdTile((NavMeshPolyRef)ref);
    unsigned int tileSalt = DecodePolyIdSalt((NavMeshPolyRef)ref);
    if ((int)tileIndex >= m_tiles.Capacity())
        return kNavMeshFailure | kNavMeshInvalidParam;
    NavMeshTile* tile = &m_tiles[tileIndex];
    if (tile->salt != tileSalt)
        return kNavMeshFailure | kNavMeshInvalidParam;

    SyncWriteDependencies();

    // Remove tile from hash lookup.
    DebugAssertMsg(m_SurfaceIDToData.find(surfaceID) != m_SurfaceIDToData.end(), "Attempt to remove a tile from an unknown surface id.");
    TileLUT& lut = m_SurfaceIDToData[surfaceID].m_TileLUT;
    lut.erase(std::make_pair(tile->header->x, tile->header->y));

    // Remove links to connected tiles.
    UnconnectLinks(tile);
    // Remove off-mesh connections connected to the tile.
    // unconnectLinks() unconnects most of them, but one-directional
    // links may not be unconnected.
    UnconnectOffMeshConnectionsToTile(ref);

    // Reset tile.
    if (tile->flags & kTileFreeData)
    {
        // We own the data - it's ok to cast away const and free it.
        ENG_FREE_LABEL(tile->data, kMemAI);
        if (data)
            *data = 0;
        if (dataSize)
            *dataSize = 0;
    }
    else
    {
        if (data)
            *data = tile->data;
        if (dataSize)
            *dataSize = tile->dataSize;
    }
    tile->data = 0;
    tile->dataSize = 0;

    ENG_FREE_LABEL(tile->polyLinks, kMemAI);
    tile->polyLinks = NULL;

    tile->header = 0;
    tile->flags = 0;
    tile->polys = 0;
    tile->verts = 0;
    tile->detailMeshes = 0;
    tile->detailVerts = 0;
    tile->detailTris = 0;
    tile->bvTree = 0;

    // Update salt, salt should never be zero.
    tile->salt = (tile->salt + 1) & ((1 << kPolyRefSaltBits) - 1);
    if (tile->salt == 0)
        tile->salt++;

    // Add to free list.
    m_tiles.Release(tileIndex);

    return kNavMeshSuccess;
}

NavMeshTileRef NavMesh::GetTileRef(const NavMeshTile* tile) const
{
    if (!tile)
        return 0;
    const unsigned int it = tile - &m_tiles[0];
    return (NavMeshTileRef)EncodePolyId(tile->salt, it, 0, 0);
}

NavMeshPolyRef NavMesh::GetPolyRefBase(const NavMeshTile* tile) const
{
    if (!tile)
        return 0;
    const unsigned int it = tile - &m_tiles[0];
    return EncodePolyId(tile->salt, it, 0, 0);
}

// Returns start and end location of an off-mesh link polygon.
// As seen when approached from prevRef.
NavMeshStatus NavMesh::GetOffMeshConnectionEndPoints(NavMeshPolyRef prevRef, NavMeshPolyRef offMeshPolyRef, Rainbow::Vector3f* startPos, Rainbow::Vector3f* endPos) const
{
    // Make sure that the current poly is indeed off-mesh link.
    if (DecodePolyIdType(offMeshPolyRef) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    const OffMeshConnection* con = GetOffMeshConnection(offMeshPolyRef);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Find link which leads to previous polygon to figure out which way we are traversing the off-mesh connection.
    const NavMeshLink* foundLink = NULL;
    for (const NavMeshLink* link = GetLink(con->firstLink); link != NULL; link = GetNextLink(link))
    {
        if (link->ref == prevRef)
        {
            foundLink = link;
            break;
        }
    }
    if (!foundLink)
        return kNavMeshFailure;

    // OffMesh connection can only have edge 0 or 1
    Assert(foundLink->edge == 0 || foundLink->edge == 1);

    // Set endpoints based on direction
    if (foundLink->edge == 0)
    {
        *startPos = con->endPoints[0].pos;
        *endPos = con->endPoints[1].pos;
        return kNavMeshSuccess;
    }
    else if (foundLink->edge == 1)
    {
        *startPos = con->endPoints[1].pos;
        *endPos = con->endPoints[0].pos;
        return kNavMeshSuccess;
    }

    return kNavMeshFailure;
}

NavMeshStatus NavMesh::GetNearestOffMeshConnectionEndPoints(NavMeshPolyRef prevRef, NavMeshPolyRef offMeshPolyRef, NavMeshPolyRef nextRef, const Rainbow::Vector3f& currentPos,
    Rainbow::Vector3f* startPos, Rainbow::Vector3f* endPos) const
{
    // Make sure that the current poly is indeed off-mesh link.
    if (DecodePolyIdType(offMeshPolyRef) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    const OffMeshConnection* con = GetOffMeshConnection(offMeshPolyRef);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Find link which leads to previous polygon to figure out which way we are traversing the off-mesh connection.
    const NavMeshLink* prevLink = NULL;
    for (const NavMeshLink* link = GetLink(con->firstLink); link != NULL; link = GetNextLink(link))
    {
        if (link->ref == prevRef)
        {
            prevLink = link;
            break;
        }
    }
    if (!prevLink)
        return kNavMeshFailure;

    // Find link which leads to next polygon to figure out which way we are traversing the off-mesh connection.
    const NavMeshLink* nextLink = NULL;
    for (const NavMeshLink* link = GetLink(con->firstLink); link != NULL; link = GetNextLink(link))
    {
        if (link->ref == nextRef)
        {
            nextLink = link;
            break;
        }
    }
    if (!nextLink)
        return kNavMeshFailure;

    // OffMesh connection can only have edge 0 or 1
    Assert(prevLink->edge == 0 || prevLink->edge == 1);

    if (con->width > 0.0f)
    {
        // For wide connections we try to go across at the same relative locations.
        // I.e. of the nearest point along the entry segment is at t=0.3, then we try
        // to choose the same proportional location at the exit segment too.
        // We clamp the entry and exit locations to the links. This is done since
        // the connection can be linked to many polygons at the destination.
        // Note:    It should be possible to constrain the link traversal during the search
        //          so that we only exit at links which overlap the input range. That way
        //          we could have the proportional locations to match at entry and exit.
        Rainbow::Vector3f startP, startQ, endP, endQ;
        if (prevLink->edge == 0)
        {
            startP = con->endPoints[0].mapped[0];
            startQ = con->endPoints[0].mapped[1];
            endP = con->endPoints[1].mapped[0];
            endQ = con->endPoints[1].mapped[1];
        }
        else if (prevLink->edge ==  1)
        {
            startP = con->endPoints[1].mapped[0];
            startQ = con->endPoints[1].mapped[1];
            endP = con->endPoints[0].mapped[0];
            endQ = con->endPoints[0].mapped[1];
        }
        else
        {
            startP = startQ = endP = endQ = Rainbow::Vector3f::zero;
        }

        // Find proportional location on entry segment.
        float t0 = 0.0f;
        SqrDistancePointSegment2D(&t0, currentPos, startP, startQ);
        float t1 = 1.0f - t0;   // Exit segment points are in reverse.

        // Clamp both factors to link range.
        const float s = 1.0f / 255.0f;
        const float startTmin = std::max(prevLink->bmin * s, 0.0f);
        const float startTmax = std::min(prevLink->bmax * s, 1.0f);
        const float endTmin = std::max(nextLink->bmin * s, 0.0f);
        const float endTmax = std::min(nextLink->bmax * s, 1.0f);

        t0 = Rainbow::FloatClamp(t0, startTmin, startTmax);
        t1 = Rainbow::FloatClamp(t1, endTmin, endTmax);

        *startPos = Rainbow::Lerp(startP, startQ, t0);
        *endPos = Rainbow::Lerp(endP, endQ, t1);

        return kNavMeshSuccess;
    }
    else
    {
        // For point-to-point connections we use the end points provided by the user.
        // Set endpoints based on direction
        if (prevLink->edge == 0)
        {
            *startPos = con->endPoints[0].pos;
            *endPos = con->endPoints[1].pos;
        }
        else if (prevLink->edge ==  1)
        {
            *startPos = con->endPoints[1].pos;
            *endPos = con->endPoints[0].pos;
        }

        return kNavMeshSuccess;
    }

    return kNavMeshFailure;
}

NavMeshStatus NavMesh::SetOffMeshConnectionCostModifier(NavMeshPolyRef ref, float costOverride)
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    OffMeshConnection* con = GetOffMeshConnectionUnsafe(ref);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;

    SyncWriteDependencies();

    if (costOverride >= 0.0f)
        con->costModifier = costOverride;
    else
        con->costModifier = -1.0f;

    BumpTimeStamp();
    return kNavMeshSuccess;
}

NavMeshStatus NavMesh::SetOffMeshConnectionFlags(NavMeshPolyRef ref, unsigned int flags)
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    OffMeshConnection* con = GetOffMeshConnectionUnsafe(ref);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;

    SyncWriteDependencies();

    con->flags = flags;
    BumpTimeStamp();
    return kNavMeshSuccess;
}

NavMeshStatus NavMesh::GetOffMeshConnectionUserID(NavMeshPolyRef ref, int* userID) const
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    const OffMeshConnection* con = GetOffMeshConnection(ref);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;
    *userID = con->userID;
    return kNavMeshSuccess;
}

NavMeshStatus NavMesh::SetOffMeshConnectionUserID(NavMeshPolyRef ref, const int userID)
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;
    OffMeshConnection* con = GetOffMeshConnectionUnsafe(ref);
    if (con == NULL)
        return kNavMeshFailure | kNavMeshInvalidParam;

    SyncWriteDependencies();

    con->userID = userID;
    return kNavMeshSuccess;
}

const OffMeshConnection* NavMesh::GetOffMeshConnection(const NavMeshPolyRef ref) const
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return NULL;

    unsigned int index = DecodePolyIdPoly(ref);
    if (index >= m_offMeshConnections.Capacity())
        return NULL;

    unsigned int salt = DecodePolyIdSalt(ref);
    if (salt != m_offMeshConnections[index].salt)
        return NULL;

    return &m_offMeshConnections[index];
}

OffMeshConnection* NavMesh::GetOffMeshConnectionUnsafe(const NavMeshPolyRef ref)
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return NULL;

    unsigned int index = DecodePolyIdPoly(ref);
    if (index >= m_offMeshConnections.Capacity())
        return NULL;

    unsigned int salt = DecodePolyIdSalt(ref);
    if (salt != m_offMeshConnections[index].salt)
        return NULL;

    SyncWriteDependencies();

    return &m_offMeshConnections[index];
}

static bool TestPointInCylinder(const Rainbow::Vector3f& point, const Rainbow::Vector3f& center, const float halfHeight, const float radius)
{
    if (Rainbow::Sqr(point.x - center.x) + Rainbow::Sqr(point.z - center.z) > Rainbow::Sqr(radius))
        return false;
    return Rainbow::Abs(point.y - center.y) <= halfHeight;
}

void NavMesh::ConnectOffMeshConnectionsToTile(NavMeshTile* tile)
{
    const int agentTypeID = tile->header->agentTypeId;
    const NavMeshBuildSettings& settings = m_SurfaceIDToData[tile->surfaceID].m_Settings;

    Rainbow::MinMaxAABB tileBounds = GetWorldTileBounds(*tile);
    // Expand the bounds to detect eligible nearby connections
    tileBounds.Expand(Rainbow::Vector3f(settings.agentRadius, settings.agentClimb, settings.agentRadius));

    for (unsigned int i = m_firstOffMeshConnection; i != kNavMeshNullLink; i = m_offMeshConnections[i].next)
    {
        OffMeshConnection& con = m_offMeshConnections[i];
        if (con.agentTypeID != agentTypeID)
            continue;
        // TODO: it might be possible that adding a new tile allows the end point to be mapped closer.
        //      if (con.endPoints[0].tileRef && con.endPoints[1].tileRef)
        //          continue;
        if (IntersectAABBAABB(con.bounds, tileBounds))
            ConnectOffMeshConnection(i, settings.agentRadius, settings.agentClimb);
    }
}

namespace detail
{
    // Check the given segment against all detail triangles in the given polygon and store the min/max intersection segment ratios in t0 and t1
    bool IntersectSegmentPolyDetail(const Rainbow::Vector3f& seg0, const Rainbow::Vector3f& seg1, const float verticalRange, const NavMeshTile& tile, const int polyIndex, float& t0, float& t1)
    {
        const Rainbow::Vector3f segDir = seg1 - seg0;
        const Rainbow::Vector2f seg0Flat{ seg0.x, seg0.z };
        const Rainbow::Vector2f seg1Flat{ seg1.x, seg1.z };
        const float sqrVerticalRange = Rainbow::Sqr(verticalRange);
        float polyT0 = FLT_MAX;
        float polyT1 = -FLT_MAX;
        bool inVerticalRange = false;

        // Go through each polygon detail triangle
        const NavMeshPoly& poly = tile.polys[polyIndex];
        const NavMeshPolyDetail& pd = tile.detailMeshes[polyIndex];
        for (int i = 0; i < pd.triCount; ++i)
        {
            const NavMeshPolyDetailIndex* const detailIndexBuffer = &tile.detailTris[(pd.triBase + i) * 4];
            const Rainbow::Vector3f& tri0 = detailIndexBuffer[0] < poly.vertCount ? tile.verts[poly.verts[detailIndexBuffer[0]]] : tile.detailVerts[pd.vertBase + detailIndexBuffer[0] - poly.vertCount];
            const Rainbow::Vector3f& tri1 = detailIndexBuffer[1] < poly.vertCount ? tile.verts[poly.verts[detailIndexBuffer[1]]] : tile.detailVerts[pd.vertBase + detailIndexBuffer[1] - poly.vertCount];
            const Rainbow::Vector3f& tri2 = detailIndexBuffer[2] < poly.vertCount ? tile.verts[poly.verts[detailIndexBuffer[2]]] : tile.detailVerts[pd.vertBase + detailIndexBuffer[2] - poly.vertCount];

            // Find 2D intersection between segment and triangle
            float triangleT0, triangleT1;
            if (!IntersectSegmentTriangle(seg0Flat, seg1Flat, { tri0.x, tri0.z }, { tri1.x, tri1.z }, { tri2.x, tri2.z }, triangleT0, triangleT1))
                continue;

            // Store the polygon min and max even if the triangle is not in the vertical range (as we connect to the whole polygon and not only specific triangles)
            polyT0 = Rainbow::FloatMin(polyT0, triangleT0);
            polyT1 = Rainbow::FloatMax(polyT1, triangleT1);

            if (inVerticalRange)
                continue;

            // Get the vertical distance from the sub-segment (between t0 and t1) to the triangle's plane and check if it's in range
            const Rainbow::Vector3f seg0Intersection = seg0 + triangleT0 * segDir;
            const Rainbow::Vector3f seg1Intersection = seg0 + triangleT1 * segDir;
            const Rainbow::Vector3f triNormal = Rainbow::CrossProduct(tri1 - tri0, tri2 - tri0);
            const float sqrDistance = GetVerticalSqrDistanceBetweenSegmentAndPlane(seg0Intersection, seg1Intersection, triNormal, tri0);
            inVerticalRange = sqrDistance < sqrVerticalRange;
        }

        if (inVerticalRange)
        {
            t0 = polyT0;
            t1 = polyT1;
        }
        return inVerticalRange;
    }
}

void NavMesh::FindPolygonsOverlappingSegment(int typeID, const Rainbow::Vector3f& pa, const Rainbow::Vector3f& pb, const float height,
    NavMeshPolyRef* polys, float* overlapMinMax, int* polyCount, const int maxPolys) const
{
    struct OverlapQuery : public NavMeshProcessCallback
    {
        const NavMesh* m_NavMesh;
        Rainbow::Vector3f m_Start;
        Rainbow::Vector3f m_End;
        float m_Height;
        NavMeshPolyRef* m_Polys;
        float* m_OverlapMinMax;
        int m_PolyCount;
        int m_MaxPolys;

        inline OverlapQuery(const NavMesh* navmesh, const Rainbow::Vector3f& start, const Rainbow::Vector3f& end, float height,
                            NavMeshPolyRef* polys, float* overlapMinMax, int maxPolys)
            : m_NavMesh(navmesh), m_Start(start), m_End(end), m_Height(height)
            , m_Polys(polys), m_OverlapMinMax(overlapMinMax), m_PolyCount(0), m_MaxPolys(maxPolys)
        {
        }

        virtual void ProcessPolygons(const NavMeshTile* tile, const NavMeshPolyRef* polyRefs, const NavMeshPoly** polys, const int itemCount) override
        {
            const Rainbow::Vector3f localStart = WorldToTile(*tile, m_Start);
            const Rainbow::Vector3f localEnd = WorldToTile(*tile, m_End);

            // Find nearest polygon amongst the nearby polygons.
            for (int i = 0; i < itemCount && m_PolyCount < m_MaxPolys; ++i)
            {
                float tmin, tmax;
                if (!detail::IntersectSegmentPolyDetail(localStart, localEnd, m_Height, *tile, GetPolyIndex(tile, polys[i]), tmin, tmax))
                    continue;

                m_Polys[m_PolyCount] = polyRefs[i];
                m_OverlapMinMax[m_PolyCount * 2 + 0] = tmin;
                m_OverlapMinMax[m_PolyCount * 2 + 1] = tmax;
                m_PolyCount++;
            }
        }
    };

    Rainbow::Vector3f bmin = Rainbow::Minimize(pa, pb);
    Rainbow::Vector3f bmax = Rainbow::Maximize(pa, pb);
    bmin.y -= height;
    bmax.y += height;

    // Get nearby polygons from proximity grid.
    OverlapQuery overlap(this, pa, pb, height, polys, overlapMinMax, maxPolys);
    QueryPolygons(typeID, (bmax + bmin) * 0.5f, (bmax - bmin) * 0.5f, &overlap);

    *polyCount = overlap.m_PolyCount;
}

void NavMesh::ConnectOffMeshConnection(unsigned int index, float connectRadius, float connectHeight)
{
    OffMeshConnection& con = m_offMeshConnections[index];
    const NavMeshPolyRef conRef = EncodeLinkId(con.salt, index);

    SyncWriteDependencies();

    const bool wideConnection = con.width > 0.0f && SqrMagnitude(con.axisX) > FLT_EPSILON;
    if (wideConnection)
    {
        // Segment-to-segment connection

        UnconnectOffMeshConnection(index);

        // Detect polygons overlapping each one of the connection's edges
        for (int i = 0; i < 2; i++)
        {
            const Rainbow::Vector3f extent = (i == 0 ? Normalize(con.axisX) : -Normalize(con.axisX)) * con.width * 0.5f;
            con.endPoints[i].mapped[0] = con.endPoints[i].pos - extent;
            con.endPoints[i].mapped[1] = con.endPoints[i].pos + extent;

            static const int kMaxResults = 32;
            NavMeshPolyRef polys[kMaxResults];
            float overlapMinMax[kMaxResults * 2];
            int count = 0;

            FindPolygonsOverlappingSegment(con.agentTypeID, con.endPoints[i].mapped[0], con.endPoints[i].mapped[1], connectHeight,
                polys, overlapMinMax, &count, kMaxResults);

            for (int j = 0; j < count; j++)
            {
                const NavMeshPolyRef mappedRef = polys[j];
                if (!mappedRef)
                    continue;

                const NavMeshTile* mappedTile = NULL;
                const NavMeshPoly* mappedPoly = NULL;
                if (NavMeshStatusFailed(GetTileAndPolyByRef(mappedRef, &mappedTile, &mappedPoly)))
                    continue;

                con.endPoints[i].tileRef = mappedRef;

                const unsigned char tmin = (unsigned char)(overlapMinMax[j * 2 + 0] * 255.0f);
                const unsigned char tmax = (unsigned char)(overlapMinMax[j * 2 + 1] * 255.0f);
                if (tmin >= tmax)
                    continue;

                // Link off-mesh connection to target poly.
                unsigned int idx = m_links.Alloc();
                if (idx != kNavMeshNullLink)
                {
                    NavMeshLink* link = &m_links[idx];
                    link->ref = mappedRef;
                    link->edge = (unsigned char)i;
                    link->side = 0xff;
                    link->bmin = tmin;
                    link->bmax = tmax;
                    // Add to linked list.
                    Assert(idx != con.firstLink);
                    link->next = con.firstLink;
                    con.firstLink = idx;
                }

                // Start end-point is always connect back to off-mesh connection,
                // Destination end-point only if it is bidirectional link.
                if (i == 0 || (i == 1 && (con.linkDirection & kLinkDirectionTwoWay)))
                {
                    // Link target poly to off-mesh connection.
                    unsigned int idx = m_links.Alloc();
                    if (idx != kNavMeshNullLink)
                    {
                        NavMeshLink* link = &m_links[idx];
                        link->ref = conRef;
                        link->edge = (unsigned char)i;
                        link->side = 0xff;
                        link->bmin = tmin;
                        link->bmax = tmax;

                        // Add to linked list.
                        const unsigned int ip = GetPolyIndex(mappedTile, mappedPoly);
                        Assert(idx != mappedTile->polyLinks[ip]);
                        link->next = mappedTile->polyLinks[ip];
                        mappedTile->polyLinks[ip] = idx;
                    }
                }
            }
        }
    }
    else
    {
        // Point-to-point connection
        const Rainbow::Vector3f ext = Rainbow::Vector3f(connectRadius, connectHeight, connectRadius);

        for (int i = 0; i < 2; i++)
        {
            if (con.endPoints[i].tileRef)
                continue;
            const Rainbow::Vector3f searchPos = con.endPoints[i].pos;
            Rainbow::Vector3f mappedPos;
            const NavMeshPolyRef mappedRef = FindNearestPoly(con.agentTypeID, searchPos, ext, &mappedPos);
            if (!mappedRef)
                continue;
            if (!TestPointInCylinder(mappedPos, searchPos, connectHeight, connectRadius))
                continue;

            NavMeshTile* mappedTile = NULL;
            const NavMeshPoly* mappedPoly = NULL;
            if (NavMeshStatusFailed(GetTileAndPolyByRef(mappedRef, &mappedTile, &mappedPoly)))
                continue;

            con.endPoints[i].mapped[0] = con.endPoints[i].mapped[1] = mappedPos;
            con.endPoints[i].tileRef = mappedRef;

            // Link off-mesh connection to target poly.
            unsigned int idx = m_links.Alloc();
            if (idx != kNavMeshNullLink)
            {
                NavMeshLink* link = &m_links[idx];
                link->ref = mappedRef;
                link->edge = (unsigned char)i;
                link->side = 0xff;
                link->bmin = link->bmax = 0;
                // Add to linked list.
                Assert(idx != con.firstLink);
                link->next = con.firstLink;
                con.firstLink = idx;
            }

            // Start end-point is always connect back to off-mesh connection,
            // Destination end-point only if it is bidirectional link.
            if (i == 0 || (i == 1 && (con.linkDirection & kLinkDirectionTwoWay)))
            {
                // Link target poly to off-mesh connection.
                unsigned int idx = m_links.Alloc();
                if (idx != kNavMeshNullLink)
                {
                    NavMeshLink* link = &m_links[idx];
                    link->ref = conRef;
                    link->edge = (unsigned char)i;
                    link->side = 0xff;
                    link->bmin = link->bmax = 0;

                    // Add to linked list.
                    const unsigned int ip = GetPolyIndex(mappedTile, mappedPoly);
                    Assert(idx != mappedTile->polyLinks[ip]);
                    link->next = mappedTile->polyLinks[ip];
                    mappedTile->polyLinks[ip] = idx;
                }
            }
        }
    }
}

NavMeshPolyRef NavMesh::AddOffMeshConnection(const struct OffMeshConnectionParams* params, float connectRadius, float connectHeight)
{
    SyncWriteDependencies();

    unsigned int index = m_offMeshConnections.Alloc();
    if (index >= kPolyRefPolyMask)
    {
        ErrorStringMsg("Failed to allocate OffMeshLink. Exceeding maximum count of %d.", kPolyRefPolyMask);
        m_offMeshConnections.Release(index);
        return 0;
    }
    OffMeshConnection& con = m_offMeshConnections[index];

    // Retain salt.
    unsigned int salt = con.salt;
    memset(&con, 0, sizeof(con));
    con.salt = salt;

    // Add to active off-mesh connection list.
    con.next = m_firstOffMeshConnection;
    m_firstOffMeshConnection = index;

    // fill out connection struct
    con.endPoints[0].pos = params->startPos;
    con.endPoints[1].pos = params->endPos;

    Rainbow::Vector3f dir = (params->endPos - params->startPos).GetNormalizedSafe();
    if (Magnitude(dir) < 0.00001f)
        dir = Rainbow::Vector3f(0, 0, 1);
    con.axisY = params->up;
    con.axisX = Rainbow::CrossProduct(con.axisY, dir);
    con.axisZ = Rainbow::CrossProduct(con.axisX, con.axisY);
    con.width = params->width;
    con.costModifier = params->costModifier;
    con.linkDirection = params->linkDirection;
    con.flags = params->flags;
    con.area = params->area;
    con.linkType = params->linkType;
    con.userID = params->userID;
    con.agentTypeID = params->agentTypeID;

    con.bounds.Init();
    if (con.width > 0.0f)
    {
        const Rainbow::Vector3f extent = (con.axisX).GetNormalizedSafe() * con.width * 0.5f;
        con.bounds.Encapsulate(con.endPoints[0].pos - extent);
        con.bounds.Encapsulate(con.endPoints[0].pos + extent);
        con.bounds.Encapsulate(con.endPoints[1].pos - extent);
        con.bounds.Encapsulate(con.endPoints[1].pos + extent);
    }
    else
    {
        con.bounds.Encapsulate(con.endPoints[0].pos);
        con.bounds.Encapsulate(con.endPoints[1].pos);
    }

    con.firstLink = kNavMeshNullLink;

    const NavMeshPolyRef dynRef = EncodeLinkId(con.salt, index);

    // Connect
    ConnectOffMeshConnection(index, connectRadius, connectHeight);

    BumpTimeStamp();

    return dynRef;
}

void NavMesh::UnconnectOffMeshConnectionsToTile(NavMeshTileRef ref)
{
    const unsigned int tileIndex = DecodePolyIdTile((NavMeshPolyRef)ref);

    for (unsigned int i = m_firstOffMeshConnection; i != kNavMeshNullLink; i = m_offMeshConnections[i].next)
    {
        OffMeshConnection& con = m_offMeshConnections[i];
        const NavMeshPolyRef ref = EncodeLinkId(con.salt, i);
        for (int j = 0; j < 2; j++)
        {
            if (con.endPoints[j].tileRef == 0)
                continue;

            // Remove links associated with the tile.
            unsigned int k = con.firstLink;
            while (k != kNavMeshNullLink)
            {
                unsigned int next = m_links[k].next;
                unsigned int targetTileIndex = DecodePolyIdTile((NavMeshPolyRef)m_links[k].ref);
                if (targetTileIndex == tileIndex)
                {
                    RemoveLinkBetween(ref, m_links[k].ref);
                    RemoveLinkBetween(m_links[k].ref, ref);
                }
                k = next;
            }
        }
    }
}

void NavMesh::UnconnectOffMeshConnection(unsigned int index)
{
    // Remove links
    OffMeshConnection& con = m_offMeshConnections[index];
    const NavMeshPolyRef ref = EncodeLinkId(con.salt, index);
    unsigned int i = con.firstLink;
    while (i != kNavMeshNullLink)
    {
        unsigned int next = m_links[i].next;
        RemoveLinkBetween(m_links[i].ref, ref);
        m_links.Release(i);
        i = next;
    }
    con.firstLink = kNavMeshNullLink;
}

NavMeshStatus NavMesh::RemoveOffMeshConnection(const NavMeshPolyRef ref)
{
    if (DecodePolyIdType(ref) != kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;

    unsigned int index = DecodePolyIdPoly(ref);
    if (index >= m_offMeshConnections.Capacity())
        return kNavMeshFailure | kNavMeshInvalidParam;

    unsigned int salt = DecodePolyIdSalt(ref);
    if (salt != m_offMeshConnections[index].salt)
        return kNavMeshFailure | kNavMeshInvalidParam;

    SyncWriteDependencies();

    // Remove links
    UnconnectOffMeshConnection(index);

    // Find previous offmesh link to be able to remove from the list.
    unsigned int i = m_firstOffMeshConnection;
    unsigned int prev = kNavMeshNullLink;
    while (i != kNavMeshNullLink)
    {
        if (i == index)
            break;
        prev = i;
        i = m_offMeshConnections[i].next;
    }
    // Remove from linked list
    const unsigned int next = m_offMeshConnections[index].next;
    if (prev == kNavMeshNullLink)
        m_firstOffMeshConnection = next;
    else
        m_offMeshConnections[prev].next = next;

    // Bump salt to distinguish deleted connections.
    m_offMeshConnections[index].salt++;
    if (m_offMeshConnections[index].salt == 0)
        m_offMeshConnections[index].salt = 1;

    m_offMeshConnections.Release(index);

    BumpTimeStamp();

    return kNavMeshSuccess;
}

const NavMeshLink* NavMesh::GetFirstLink(NavMeshPolyRef ref) const
{
    if (DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = GetOffMeshConnection(ref);
        if (con == NULL)
            return NULL;
        if (con->firstLink == kNavMeshNullLink)
            return NULL;
        return &m_links[con->firstLink];
    }
    else
    {
        const NavMeshTile* tile = NULL;
        const NavMeshPoly* poly = NULL;
        if (NavMeshStatusFailed(GetTileAndPolyByRef(ref, &tile, &poly)))
            return NULL;

        const unsigned int ip = GetPolyIndex(tile, poly);
        const unsigned int firstLink = tile->polyLinks[ip];
        if (firstLink == kNavMeshNullLink)
            return NULL;
        return &m_links[firstLink];
    }
}

const OffMeshConnection* NavMesh::GetFirstOffMeshConnection() const
{
    if (m_firstOffMeshConnection == kNavMeshNullLink)
        return NULL;
    return &m_offMeshConnections[m_firstOffMeshConnection];
}

const OffMeshConnection* NavMesh::GetNextOffMeshConnection(const OffMeshConnection* data) const
{
    if (!data)
        return NULL;
    if (data->next == kNavMeshNullLink)
        return NULL;
    Assert(data->next < m_offMeshConnections.Capacity());
    return &m_offMeshConnections[data->next];
}

unsigned int NavMesh::GetPolyFlags(NavMeshPolyRef ref) const
{
    if (DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = GetOffMeshConnection(ref);
        if (con == NULL)
            return 0;
        return con->flags;
    }
    else
    {
        const NavMeshTile* tile = NULL;
        const NavMeshPoly* poly = NULL;
        if (NavMeshStatusFailed(GetTileAndPolyByRef(ref, &tile, &poly)))
            return 0;
        return poly->flags;
    }
    return 0;
}

unsigned char NavMesh::GetPolyArea(NavMeshPolyRef ref) const
{
    if (DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = GetOffMeshConnection(ref);
        if (con == NULL)
            return 0;
        return con->area;
    }
    else
    {
        const NavMeshTile* tile = NULL;
        const NavMeshPoly* poly = NULL;
        if (NavMeshStatusFailed(GetTileAndPolyByRef(ref, &tile, &poly)))
            return 0;
        return poly->area;
    }
    return 0;
}

void NavMesh::GetPolyFlagsAndArea(NavMeshPolyRef ref, unsigned int* flags, unsigned char* area) const
{
    if (flags)
        *flags = 0;
    if (area)
        *area = 0;

    if (DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = GetOffMeshConnection(ref);
        if (con == NULL)
            return;
        if (flags)
            *flags = con->flags;
        if (area)
            *area = con->area;
    }
    else
    {
        const NavMeshTile* tile = NULL;
        const NavMeshPoly* poly = NULL;
        if (NavMeshStatusFailed(GetTileAndPolyByRef(ref, &tile, &poly)))
            return;
        if (flags)
            *flags = poly->flags;
        if (area)
            *area = poly->area;
    }
}

int NavMesh::GetPolyGeometry(NavMeshPolyRef ref, Rainbow::Vector3f* verts, NavMeshPolyRef* neighbours, int maxNeisPerEdge) const
{
    if (DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        // TODO: should we return off-mesh link geometry?
        return 0;
    }
    else
    {
        const NavMeshTile* tile = NULL;
        const NavMeshPoly* poly = NULL;
        if (NavMeshStatusFailed(GetTileAndPolyByRef(ref, &tile, &poly)))
            return 0;
        const int nverts = poly->vertCount;

        // Copy vertices
        if (verts != NULL)
        {
            for (int i = 0; i < nverts; i++)
                verts[i] = tile->verts[poly->verts[i]];
        }

        // Copy neighbours
        if (neighbours != NULL)
        {
            for (int i = 0; i < nverts * maxNeisPerEdge; i++)
                neighbours[i] = 0;

            const unsigned int ip = GetPolyIndex(tile, poly);
            const unsigned int firstLink = tile->polyLinks[ip];
            for (const NavMeshLink* link = GetLink(firstLink); link != NULL; link = GetNextLink(link))
            {
                // Only accept polygon connections.
                if (DecodePolyIdType(link->ref) == kPolyTypeOffMeshConnection)
                    continue;
                int index = (int)link->edge;
                if (index >= 0 && index < nverts)
                {
                    // find empty slot
                    for (int j = 0; j < maxNeisPerEdge; j++)
                    {
                        if (neighbours[index * maxNeisPerEdge + j] == 0)
                        {
                            neighbours[index * maxNeisPerEdge + j] = link->ref;
                            break;
                        }
                    }
                }
            }
        }
        return nverts;
    }
    return 0;
}

void NavMesh::AddWriteDependency(const Rainbow::JobFence& fence)
{
    m_WriteDependencies.push_back(fence);
}

void NavMesh::SyncWriteDependencies()
{
    SyncFences(m_WriteDependencies.begin(), m_WriteDependencies.size());
    m_WriteDependencies.resize_uninitialized(0);
}

void NavMesh::ForgetCompletedDependencies()
{
    for (size_t i = 0; i < m_WriteDependencies.size();)
    {
        if (IsFenceDone(m_WriteDependencies[i]))
        {
            m_WriteDependencies.erase_swap_back(m_WriteDependencies.begin() + i);
        }
        else
        {
            ++i;
        }
    }
}
