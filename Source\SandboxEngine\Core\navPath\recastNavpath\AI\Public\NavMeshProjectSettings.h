#pragma once

#include "NavMeshBuildSettings.h"

class NavMeshProjectSettings
{
public:
    struct NavMeshAreaData
    {
        core::string name;
        float cost;
    };

    enum BuiltinNavMeshProjectSettings
    {
        kWalkable = 0,
        kNotWalkable = 1,
        kJump = 2
    };

    enum
    {
        kInvalidAgentTypeID = -1,
        kDefaultAgentTypeID = 0
    };

    enum
    {
        kBuiltinAreaCount = 3,
        kAreaCount = 32
    };

    NavMeshProjectSettings();
    // ~NavMeshProjectSettings (); declared-by-macro

    virtual void Reset() ;
    
    

    void SetAreaCost(unsigned int index, float cost);
    float GetAreaCost(unsigned int index) const;
    void GetAllAreaCosts(float costs[kAreaCount]) const;
    int GetAreaFromName(const core::string& areaName) const;
    dynamic_array<core::string> GetAreaNames() const;

    // Create build settings and remember the agentTypeID for that
    const NavMeshBuildSettings& CreateSettings(unsigned short userdata = 0);

    // Updates the build settings - unless the agentTypeID of the
    // settings is not known
    void UpdateSettings(const NavMeshBuildSettings& settings);

    // Removes the build settings for agentTypeID unless the settings
    // are the default settings or the id is not known
    void RemoveSettings(int agentTypeID);

    // Gets the build settings for the agentTypeID
    // Returns null if the id is not known
    const NavMeshBuildSettings* GetSettingsByID(int agentTypeID) const;

    // Linear accessors
    // returns upper bound
    int GetSettingsCount() const;

    // Gets value by index
    // Returns null when out of bounds
    const NavMeshBuildSettings* GetSettingsByIndex(int index) const;

    // Settings names:
    // Sets the name unless the id is not known
    void SetSettingsNameForID(int agentTypeID, const core::string& name);

    // The name associated with the build settings for agentTypeID
    // Returns null if the id is not known
    const core::string* GetSettingsNameFromID(int agentTypeID) const;

    static const char* s_WarningCostLessThanOne;
    static const char* s_WarningUsingObsoleteAreaName;
    static const char* s_WarningTooFewSettingsNames;
    static const char* s_WarningTooManySettingsNames;
    static const char* s_WarningDuplicateAgentTypeIds;

private:

    NavMeshAreaData m_Areas[kAreaCount];

    int GetUnusedAgentTypeID(unsigned short userdata = 0);
    int m_LastAgentTypeID;

    // This separation of string data and string settings is done purely
    // to save string handling if names are not used - thus saving allocations at runtime.
    // esp. GC allocations in scripts are concerned.
    std::vector<NavMeshBuildSettings> m_Settings;
    dynamic_array<core::string> m_SettingNames;
};
NavMeshProjectSettings& GetNavMeshProjectSettings(); 
