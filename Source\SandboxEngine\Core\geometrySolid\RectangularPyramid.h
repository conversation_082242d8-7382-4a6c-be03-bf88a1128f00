#ifndef __RectangularPyramid_h__
#define __RectangularPyramid_h__ 1

#include "GeoSolid.h"
#include "Common/LazySingleton.h"

#include "BaseClass/SharePtr.h"
#include "Graphics/Mesh/Mesh.h"

namespace MNSandbox { 
	class SceneGeoSolid;
	namespace GeometrySolid {
		class RectangularPyramid : public GeoSolid
		{
		public:
			RectangularPyramid();
			~RectangularPyramid();
			void InitGeoSolidMeshData() override;
			void CreateDynamic() override;
			void SeparateSurfaces() override;
			int GetSurfaceCount() override { return 5; }
			GeoSolidFace GetGeoSolidFace(int ism) override
			{
				GeoSolidFace gsf = GeoSolidFace::UNKNOWN;
				switch (ism)
				{
					case 0:
						gsf = GeoSolidFace::FRONT;
						break;
					case 1:
						gsf = GeoSolidFace::BACK;
						break;
					case 2:
						gsf = GeoSolidFace::LEFT;
						break;
					case 3:
						gsf = GeoSolidFace::RIGHT;
						break;
					case 4:
						gsf = GeoSolidFace::BOTTOM;
						break;
				}
				return gsf;
			}
			const char* GetName() const override { return "Pyramid"; }
			const char* GetSurfaceName(const SceneModelObject::Surface& sf) override;
			DECLARE_GET_SINGLETON(RectangularPyramid)
		};

	}
}
#endif//__RectangularPyramid_h__