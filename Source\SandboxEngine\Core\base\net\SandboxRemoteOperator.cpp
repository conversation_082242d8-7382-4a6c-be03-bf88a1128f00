/**
* file : SandboxRemoteOperator
* func : ɳ��Զ�˲���
* by : chenzh
*/
#include "SandboxRemoteOperator.h"
#include "SandBoxManager.h"
#include "SandboxGameMap.h"
#include "SandboxCustomBuffer.h"
#include "base/stream/SandboxStreamBuffer.h"

#ifndef SPLIT_PACK_SIZE
#define SPLIT_PACK_SIZE 65000
#endif

namespace MNSandbox {

#if !defined(SANDBOX_REMOTEMSG_USE_STREAM)
	const char* RemoteOperator::ms_eventname = "SDBRemoteOpt";
#else
	const char* RemoteOperator::ms_eventname = "SDBRemoteBin";
	const char* RemoteOperator::ms_eventnameSplit = "SDBRemoteSplit";
#endif


	// ��Ϣ�ص�
#if !defined(SANDBOX_REMOTEMSG_USE_STREAM)
	void RemoteOperator::EventExcute::OnExecute(const char* eventname, void* context, int nLen, unsigned long long userdata)
	{
		//WeakRef<GameMap> gamemap = GetCurrentGameMap();
		//if (!gamemap)
		//	return; // too early

		MNJsonObject& jo = *(MNJsonObject*)context;
		RemoteMsgInfo msg;
		msg.ParseJsonObject(jo);
		m_owner->OnReceive(msg);
	}
#else
	void RemoteOperator::EventExcute::OnExecute(const char* eventname, int uin, void* context, int nLen, unsigned long long userdata)
	{
		if (nLen <= 0)
		{
			SANDBOX_ASSERT(false && "RemoteOperator receive msg error!");
			return;
		}

		// ������
		AutoRef<CustomBuffer> cb = CustomBuffer::CreateStatic(context, nLen);
		AutoRef<Stream> stream = SANDBOX_NEW(StreamBuffer);
		SANDBOXERR result = stream->LoadFromBinary(cb);
		if (result != SANDBOXERR::OK)
		{
			SANDBOX_ASSERTEX(result != SANDBOXERR::OK, ToString("remote msg parse failed! [", eventname, "] uin=", uin));
			return;
		}

		RemoteMsgInfo msg;
		if (!msg.LoadFromStream(stream))
		{
			SANDBOX_ASSERTEX(false, ToString("remote msg to stream failed! [", eventname, "] uin=", uin));
			return;
		}

		SDB_NETMONITOR_REMOTEOPT_RECV(msg._nodeid, msg._reflex, uin, nLen )

		msg._uin = uin;
		m_owner->OnReceive(msg);
	}
	void RemoteOperator::EventExcuteSplit::OnExecute(const char* eventname, int uin, void* context, int nLen, unsigned long long userdata)
	{
		if (nLen <= 1)
		{
			SANDBOX_ASSERTEX(false, ToString("RemoteOperator receive msg error! nLen=", nLen));
			return;
		}

		// header 1 ���ֽ�
		const char header = static_cast<const char*>(context)[0];
		const char* buffer = static_cast<const char*>(context) + 1;
		size_t len = (size_t)nLen - 1;
		AutoRef<CustomBuffer> cb;
		static std::vector<AutoRef<CustomBuffer>> s_msgBuffer;

		// �����ְ��߼�
		if (header == 1) // �ְ�
		{
			s_msgBuffer.push_back(CustomBuffer::CreateCopy(buffer, len));
			return;
		}
		else if (header == 0)
		{
			// ����ְ��ܴ�С
			size_t total = len;
			for (auto& v : s_msgBuffer)
				total += v->Size();

			cb = CustomBuffer::Create(total);

			// ���
			size_t index = 0;
			for (auto& v : s_msgBuffer)
			{
				memcpy(cb->Offset(index), v->Data(), v->Size());
				index += v->Size();
			}
			memcpy(cb->Offset(index), buffer, len);

			s_msgBuffer.clear(); // �ͷ�
		}
		else
		{
			SANDBOX_ASSERTEX(false, ToString("RemoteOperator receive msg split error! nLen=", nLen, ", header=", header));
			return;
		}

		// ������
		AutoRef<Stream> stream = SANDBOX_NEW(StreamBuffer);
		SANDBOXERR result = stream->LoadFromBinary(cb);
		if (result != SANDBOXERR::OK)
		{
			SANDBOX_ASSERTEX(result != SANDBOXERR::OK, ToString("remote msg parse failed! [", eventname, "] uin=", uin));
			return;
		}

		RemoteMsgInfo msg;
		if (!msg.LoadFromStream(stream))
		{
			SANDBOX_ASSERTEX(false, ToString("remote msg to stream failed! [", eventname, "] uin=", uin));
			return;
		}

		SDB_NETMONITOR_REMOTEOPT_RECV(msg._nodeid, msg._reflex, uin, cb->Size())

		msg._uin = uin;
		m_owner->OnReceive(msg);
	}
#endif

	RemoteOperator::RemoteOperator()
		: m_callback(this)
#if defined(SANDBOX_REMOTEMSG_USE_STREAM)
		, m_callbackSplit(this)
#endif
		, m_listenerSendToClient(this, &RemoteOperator::OnSendToClient)
		, m_listenerSendToClientMulti(this, &RemoteOperator::OnSendToClientMulti)
		, m_listenerSendToHost(this, &RemoteOperator::OnSendToHost)
		, m_listenerSendBroadcast(this, &RemoteOperator::OnSendBroadcast)
	{
		// ����Ϣ����
		RemoteMsg::GetSingleton().m_notifySendToClient.Subscribe(m_listenerSendToClient);
		RemoteMsg::GetSingleton().m_notifySendToClientMulti.Subscribe(m_listenerSendToClientMulti);
		RemoteMsg::GetSingleton().m_notifySendToHost.Subscribe(m_listenerSendToHost);
		RemoteMsg::GetSingleton().m_notifySendBroadcast.Subscribe(m_listenerSendBroadcast);

		// ��ɳ��������Ϣ�ص�
#if !defined(SANDBOX_REMOTEMSG_USE_STREAM)
		SandBoxManager::getSingleton().subscibeEx(&m_callback, (char*)ms_eventname);
#else
		SandBoxManager::getSingleton().subscibeBin(&m_callback, (char*)ms_eventname);
		SandBoxManager::getSingleton().subscibeBin(&m_callbackSplit, (char*)ms_eventnameSplit);
#endif
	}

	RemoteOperator::~RemoteOperator()
	{
		// ���ɳ��������Ϣ�ص�
		SandBoxManager::getSingleton().unSubscibeEx(&m_callback, (char*)ms_eventname);
#if defined(SANDBOX_REMOTEMSG_USE_STREAM)
		SandBoxManager::getSingleton().unSubscibeEx(&m_callbackSplit, (char*)ms_eventnameSplit);
#endif
	}

	void RemoteOperator::OnSendMessage(SEND_MODE mode, const RemoteMsgInfo& msg, int* uin, size_t cnt)
	{
		auto ub = MsgToBuffer(msg);
		if (!ub) return;

		SDB_NETMONITOR_REMOTEOPT_SEND(msg._nodeid, msg._reflex, msg._uin, ub->Size() )

#if !defined(SANDBOX_REMOTEMSG_USE_STREAM)
		SendToNetManager(mode, ms_eventname, ub, uin, cnt);
#else
		if (ub->Size() <= SPLIT_PACK_SIZE)
		{
			SendToNetManager(mode, ms_eventname, ub, uin, cnt);
			return;
		}

		// ��ub ���в��
		// ��ΪĬ�ϲ��ֻ�������������ͻ����Լ��㲥���������ֻ������10������ 640k ���ϵİ�����ֱ�Ӷ���
		// �������������ÿ���������� 65000 һ�£�ȷ�����ᱻĬ�ϲ���߼����β��

		size_t max = SPLIT_PACK_SIZE - 1; // 1 ���ֽ���header
		AutoRef<CustomBuffer> cell = CustomBuffer::Create(SPLIT_PACK_SIZE);
		size_t len = ub->Size();
		size_t idx = 0;

		do
		{
			cell->Offset<char>(0)[0] = 1; // header Ϊ 1 ��ʾ���к�����
			::memcpy(cell->Offset(1), ub->Offset(idx), max);
			SendToNetManager(mode, ms_eventnameSplit, cell, uin, cnt);
			len -= max;
			idx += max;
		}
		while (len > max);

		// ���һ����
		cell->Offset<char>(0)[0] = 0; // header Ϊ 0 ��ʾ���һ������
		::memcpy(cell->Offset(1), ub->Offset(idx), len);
		cell->Resize(len + 1);
		SendToNetManager(mode, ms_eventnameSplit, cell, uin, cnt);
#endif
	}

	void RemoteOperator::SendToNetManager(SEND_MODE mode, const char* name, const AutoRef<CustomBuffer>& cb, int* uin, size_t cnt)
	{
		switch (mode)
		{
		case SEND_MODE::TOHOST:
			SandBoxManager::getSingleton().sendToHost(name, cb->Data(), (int)cb->Size());
			break;
		case SEND_MODE::TOCLIENT:
			SandBoxManager::getSingleton().sendToClient(*uin, name, cb->Data(), (int)cb->Size());
			break;
		case SEND_MODE::TOCLIENTMULTI:
			if (cnt == 1)
				SandBoxManager::getSingleton().sendToClient(*uin, name, cb->Data(), (int)cb->Size());
			else if (cnt != 0)
				SandBoxManager::getSingleton().sendToClientMulti(uin, cnt, name, cb->Data(), (int)cb->Size());
			break;
		case SEND_MODE::BROADCAST:
			SandBoxManager::getSingleton().sendBroadCast(name, cb->Data(), (int)cb->Size());
			break;
		default:
			SANDBOX_ASSERT(false && "unkown SEND_MODE");
			break;
		}
	}

	void RemoteOperator::OnReceive(const RemoteMsgInfo& msg)
	{
		RemoteMsg::GetSingleton().m_notifyReceive.Emit(msg);
	}
	
	AutoRef<CustomBuffer> RemoteOperator::MsgToBuffer(const RemoteMsgInfo& msg)
	{
#if !defined(SANDBOX_REMOTEMSG_USE_STREAM)
		MNJsonObject jo;
		unsigned char* buffer = nullptr;
		int len = 0;

		msg.ToJsonObject(jo);
		jo.saveBinary(buffer, len);
		auto ub = CustomBuffer::CreateGlobal(buffer, len); // �����ͷ�
#else
		AutoRef<Stream> stream = SANDBOX_NEW(StreamBuffer);
		stream->SetVersion(Config::GetSingleton().GetSyncVersion(0));
		if (!msg.SaveToStream(stream))
		{
			SANDBOX_ASSERT(false && "send remote msg failed! serialize");
			return nullptr;
		}
		AutoRef<CustomBuffer> ub;
		if (!stream->SaveToBinary(ub, false))
		{
			SANDBOX_ASSERT(false && "send remote msg failed! to buffer");
			return nullptr;
		}
#endif
		return ub;
	}
}