#include "blocks/container_world.h"
#include "DefManagerProxy.h"
#include "blocks/BlockMaterialBase.h"
#include "world.h"
#ifdef BUILD_MINI_EDITOR_APP
#include "WorldSearch/SearchManager.h"
#endif //BUILD_MINI_EDITOR_APP

bool WorldContainerMgr::onSearchName(jsonxx::Array& resultArray, const std::string& name)
{
#ifdef BUILD_MINI_EDITOR_APP
	std::function<bool(WorldContainer&)> iterateWorldContainer = [this, &resultArray, &name](WorldContainer& container) -> bool {
		//公共接口处理
		const char* const szContainerText = container.getText();
		const std::string containerText = szContainerText ? std::string(szContainerText) : "NULL";;
		const std::string tombstoneTitle = container.getTombStoneTitle();
		const std::string tombstoneContent = container.getTombStoneContent();
		if (containerText.find(name) != std::string::npos
			|| tombstoneTitle.find(name) != std::string::npos
			|| tombstoneContent.find(name) != std::string::npos
			)
		{
			LOG_DEBUG("containerText = %s", containerText.c_str());
			LOG_DEBUG("tombstoneTitle = %s", tombstoneTitle.c_str());
			LOG_DEBUG("tombstoneContent = %s", tombstoneContent.c_str());
			jsonxx::Object obj;
			obj << "classname" << container.getContainerName();
			if (!containerText.empty())
			{
				obj << "text" << containerText;
			}
			if (!tombstoneTitle.empty())
			{
				obj << "tombstoneTitle" << tombstoneTitle;
			}
			if (!tombstoneContent.empty())
			{
				obj << "tombstoneContent" << tombstoneContent;
			}
			obj << "addr" << (int)&container;
			obj << "coordinateType" << "WCoord";
			const WCoord& pos = container.m_BlockPos;
			obj << "x" << pos.x;
			obj << "y" << pos.y;
			obj << "z" << pos.z;
			resultArray << obj;
		}

		std::string containerName = "";

		const std::vector<unsigned>& matchedBlockIds = SearchManager::getInstance()->getMatchedBlockIds();
		BlockMaterial* blockMaterial = container.getBlockMtl();
		int blockId = -1;
		if (blockMaterial)
		{
			blockId = blockMaterial->getBlockResID();
		}
		else
		{
			Block block = m_World->getBlock(container.m_BlockPos);
			blockId = block.getResID();
		}
		BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockId);

		if (blockDef)
		{
			containerName = blockDef->Name;
		}

		if (std::find(matchedBlockIds.begin(), matchedBlockIds.end(), blockId) != matchedBlockIds.end())
		{
			LOG_DEBUG("block@%x - blockDef->Name = %s", &container, blockDef->Name.c_str());
			jsonxx::Object obj;
			obj << "classname" << "WorldContainer";
			obj << "addr" << (int)&container;
			obj << "name" << containerName;
			obj << "coordinateType" << "WCoord";
			const WCoord& pos = container.m_BlockPos;
			obj << "x" << pos.x;
			obj << "y" << pos.y;
			obj << "z" << pos.z;
			resultArray << obj;
		}

		const std::vector<unsigned>& matchedItemIds = SearchManager::getInstance()->getMatchedItemIds();
		unsigned startOffset = 0;
		WorldStorageBox* storageBox = dynamic_cast<WorldStorageBox*>(&container);
		if (storageBox)
		{
			startOffset = STORAGE_START_INDEX;
		}
		for (unsigned i = 0; i < container.getGridCount(); ++i)
		{
			BackPackGrid* backpackGrid = container.index2Grid(startOffset + i);
			if (!backpackGrid)
			{
				continue;
			}
			if (!backpackGrid->def)
			{
				continue;
			}
			if (std::find(matchedItemIds.begin(), matchedItemIds.end(), backpackGrid->def->ID) != matchedItemIds.end())
			{
				jsonxx::Object parent;
				parent << "classname" << container.getContainerName();
				parent << "addr" << (long)&container;
				parent << "name" << containerName;
				parent << "coordinateType" << "WCoord";
				const WCoord& pos = container.m_BlockPos;
				parent << "x" << pos.x;
				parent << "y" << pos.y;
				parent << "z" << pos.z;
				jsonxx::Object obj;
				obj << "classname" << "BackPackGrid";
				obj << "addr" << (long)&backpackGrid;
				obj << "name" << backpackGrid->def->Name;
				obj << "index" << i;
				obj << "count" << backpackGrid->getNum();
				obj << "parent" << parent;
				resultArray << obj;
			}
		}
		return true;
	};
	forEachWorldContainer(iterateWorldContainer);
#endif //BUILD_MINI_EDITOR_APP
	return true;
}

bool WorldContainerMgr::onSearchCoordinate(jsonxx::Array& resultArray, const int& x, const int& y, const int& z)
{
	std::function<bool(WorldContainer&)> iterateWorldContainer = [this, &resultArray, &x, &y, &z](WorldContainer& container) -> bool {
		const WCoord& pos = container.m_BlockPos;
		//if (!FltEqual(pos.x, x) || !FltEqual(pos.y, y) || !FltEqual(pos.z, z))
		if (pos.x != x || pos.y != y || pos.z != z)
		{
			return true;
		}
		std::string containerName = "???";
		BlockMaterial* blockMaterial = container.getBlockMtl();
		int blockId = -1;
		if (blockMaterial)
		{
			blockId = blockMaterial->getBlockResID();
		}
		else
		{
			Block block = m_World->getBlock(container.m_BlockPos);
			if (!block.isEmpty())
			{
				blockId = block.getResID();
			}
		}
		BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockId);
		if (blockDef)
		{
			containerName = blockDef->Name;
			LOG_DEBUG("block@%x - blockDef->Name = %s", &container, blockDef->Name.c_str());
		}
				
		jsonxx::Object obj;
		obj << "classname" << "WorldContainer";
		obj << "addr" << (unsigned long)&container;
		obj << "name" << containerName;
		obj << "coordinateType" << "WCoord";
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		resultArray << obj;
		return true;
	};
	forEachWorldContainer(iterateWorldContainer);
	return true;
}

void WorldContainerMgr::forEachWorldContainer(const std::function<bool(WorldContainer&)>& func)
{
	// LOG_DEBUG("m_Containers.size() = %u", m_Containers.size());
	ContainHashTable::Element *iter = m_Containers.iterate(NULL);
	for ( ; iter ; iter = m_Containers.iterate(iter))
	{
		if (!func(*iter->value))
		{
			return;
		}
	}
}