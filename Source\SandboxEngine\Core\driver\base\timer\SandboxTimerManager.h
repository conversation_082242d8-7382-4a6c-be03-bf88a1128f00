#pragma once
/**
* file : SandboxTimerManager
* func : 沙盒定时器管理器
* by : chenzh
*/
#include "SandboxType.h"
#include "SandboxAutoRef.h"
#include "SandboxTimer.h"
#include "SandboxNotify.h"
#include "SandboxListener.h"
#include "SandboxSingleton.h"


namespace MNSandbox {


	class MNTimer;

	/* 定时器管理器 */
	class TimerManager
	{
	public:
		TimerManager();
		~TimerManager();

		// 释放
		void Release();

		/* 注册定时器 */
		bool RegisterTimer(MNTimer* timer, double delay);

		/* 注销定时器 */
		bool UnregisterTimer(MNTimer* timer);

		/* 计算剩余时间 */
		double CalcLeftTime(MNTimer* timer);

	private:
		// 注册逻辑
		bool RegisterTimerDelayReal(MNTimer* timer, double delay);

		// 执行一片
		void DoOnePiece();

	private:
		// 事件回调
		void OnFpsChanged(unsigned srcfps, unsigned dstfps);
		void OnTick(int times);

		// 事件监听
		ListenerClass<TimerManager, unsigned, unsigned> m_listenFpsChanged;
		ListenerClass<TimerManager, int> m_listenSysTick;

	private:
		// 定义多少片
		static const unsigned ms_pieceCnt = 2000; // 默认2000片，每tick一片

		// 当前tick，片索引
		unsigned m_pieceIndex = 0; // [0, ms_pieceCnt)
		// 定时器队列
		struct PieceData
		{
			AutoRef<MNTimer> _timer; // 计时器对象
			unsigned _waitTimes; // 等待次数，计数为0时，触发计时器
		};
		std::vector<PieceData> m_allPiece[ms_pieceCnt];
	};

	/////////////////////////////////////////////////////////////////////

	// 获取当前活跃的timer 管理器
	extern TimerManager& GetCurrentTimerMgr();

}