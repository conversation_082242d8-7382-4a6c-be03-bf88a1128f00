#pragma once
#include "geometrySolid/GeoSolidEnum.h"
#include "FrameworkPrefix.h"
#include "Math/Vector2f.h"

namespace MNSandbox {
	namespace GeometrySolid {

		//保留这些注释，需要时再启用
		class Vector2db
		{
		public:
			// DEFINE_GET_TYPESTRING(Vector2db)
			// template<class TransferFunction> void Transfer(TransferFunction& transfer);
			FORCEINLINE Vector2db() {}
			FORCEINLINE Vector2db(double x1, double y1) : x(x1), y(y1) {}
			//FORCEINLINE Vector2db(double v) : x(v), y(v) {}
			//FORCEINLINE void SetZero() { x = 0.0f; y = 0.0f; }
			FORCEINLINE void Set(double x1, double y1) { x = x1; y = y1; }
			FORCEINLINE double* GetPtr() { return &x; }
			FORCEINLINE const double* GetPtr() const { return &x; }

			inline Rainbow::Vector2f ToFloat() const
			{
				return Rainbow::Vector2f((float)x, (float)y);
			}

			FORCEINLINE double Length() const
			{
				return sqrt(x * x + y * y);
			}

			FORCEINLINE double LengthSqr() const
			{
				return x * x + y * y;
			}

			FORCEINLINE Vector2db Abs() const
			{
				return Vector2db(fabs(x), fabs(y));
			}

			FORCEINLINE Vector2db Maximize(const Vector2db &v) const
			{
				return Vector2db(Rainbow::Max(x, v.x), Rainbow::Max(y, v.y));
			}

			FORCEINLINE Vector2db Minimize(const Vector2db &v) const
			{
				return Vector2db(Rainbow::Min(x, v.x), Rainbow::Min(y, v.y));
			}

			FORCEINLINE double Distance(const Vector2db &v) const
			{
				return sqrt((x - v.x) * (x - v.x) + (y - v.y) * (y - v.y));
			}

			FORCEINLINE double DistanceSqr(const Vector2db &v) const
			{
				return (x - v.x) * (x - v.x) + (y - v.y) * (y - v.y);
			}

			friend double DotProduct(const Vector2db &v1, const Vector2db &v2);


			FORCEINLINE double SqrMagnitude() const
			{
				return DotProduct(*this, *this);
			}

			FORCEINLINE double SqrMagnitude()
			{
				return DotProduct(*this, *this);
			}

			FORCEINLINE Vector2db GetNormalized() const
			{
				Vector2db result;
				double len = Length();
				double scale = 1.0 / len;
				return Vector2db(x * scale, y * scale);
			}

			FORCEINLINE Vector2db GetNormalizedSafe(double epsilon = kEpsilon) const
			{
				double len = Length();
				if (len > epsilon)
				{
					return (*this) / len;
				}
				return Vector2db::zero;
			}

			FORCEINLINE double NormalizeSafe(double epsilon = kEpsilon)
			{
				double len = Length();
				if (len > epsilon)
				{
					(*this) /= len;
					return len;
				}
				else
				{
					return 0.0;
				}
			}

			FORCEINLINE double Normalize()
			{
				double len = Length();
				(*this) /= len;
				return len;
			}

			FORCEINLINE bool IsNormalized(double epsilon = kEpsilon) const
			{
				return fabs(Length() - 1.0) < epsilon;
			}

			FORCEINLINE Vector2db NormalizeFast()const
			{
				const double mag = SqrMagnitude();
				return (*this) * InvSqrt(mag);
			}

			FORCEINLINE Vector2db operator + (const Vector2db &v) const
			{
				return Vector2db(x + v.x, y + v.y);
			}

			FORCEINLINE Vector2db operator - (const Vector2db &v) const
			{
				return Vector2db(x - v.x, y - v.y);
			}

			FORCEINLINE Vector2db operator * (const Vector2db &v) const
			{
				return Vector2db(x*v.x, y*v.y);
			}

			FORCEINLINE Vector2db operator / (const Vector2db &v) const
			{
				return Vector2db(x / v.x, y / v.y);
			}

			FORCEINLINE Vector2db operator * (double s) const
			{
				return Vector2db(x * s, y * s);
			}


			FORCEINLINE Vector2db operator / (double s) const
			{
				return Vector2db(x / s, y / s);
			}

			FORCEINLINE Vector2db& operator = (const Vector2db &vec)
			{
				x = vec.x;
				y = vec.y;
				return *this;
			}

			FORCEINLINE Vector2db& operator += (const Vector2db &vec)
			{
				x += vec.x;
				y += vec.y;
				return *this;
			}

			FORCEINLINE Vector2db& operator -= (const Vector2db &vec)
			{
				x -= vec.x;
				y -= vec.y;
				return *this;
			}

			FORCEINLINE Vector2db& operator *= (double s)
			{
				x *= s;
				y *= s;
				return *this;
			}

			FORCEINLINE Vector2db& operator /= (double s)
			{
				x /= s;
				y /= s;
				return *this;
			}

			FORCEINLINE bool operator == (const Vector2db &vec) const
			{
				return x == vec.x && y == vec.y;
			}

			FORCEINLINE bool operator != (const Vector2db &vec) const
			{
				return x != vec.x || y != vec.y;
			}

			FORCEINLINE operator double *() { return &x; }
			FORCEINLINE operator const double *() const { return &x; }
			FORCEINLINE Vector2db operator +() const { return *this; }
			FORCEINLINE Vector2db operator -() const { return Vector2db(-x, -y); }
			//FORCEINLINE core::string ToString() const
			//{
			//	return Format("x:%llf,y:%llf", x, y);
			//}
		public:
			double x, y;
			static  const Vector2db zero;
			static  const Vector2db one;
			static  const Vector2db xAxis;
			static  const Vector2db yAxis;
		};

		FORCEINLINE double Dot(const Vector2db& lhs, const Vector2db& rhs) { return lhs.x * rhs.x + lhs.y * rhs.y; }

		//FORCEINLINE double SqrMagnitude(const Vector2db& inV) { return Dot(inV, inV); }

		FORCEINLINE double Magnitude(const Vector2db& inV) { return sqrt(Dot(inV, inV)); }

		//FORCEINLINE double Angle(const Vector2db& lhs, const Vector2db& rhs)
		//{
		//	return ::acos(FloatMin(1.0f, FloatMax(-1.0f, Dot(lhs, rhs) / (Magnitude(lhs) * Magnitude(rhs)))));
		//}

		//FORCEINLINE bool IsFinite(const Vector2db& f)
		//{
		//	return IsFinite(f.x) & IsFinite(f.y);
		//}

		//FORCEINLINE Vector2db Lerp(const Vector2db& from, const Vector2db& to, double t)
		//{
		//	return to * t + from * (1.0f - t);
		//}

		FORCEINLINE double DotProduct(const Vector2db &v1, const Vector2db &v2)
		{
			return v1.x * v2.x + v1.y * v2.y;
		}

		FORCEINLINE double CrossProduct(const Vector2db &v1, const Vector2db &v2)
		{
			return v1.x * v2.y - v2.x * v1.y;
		}

		FORCEINLINE Vector2db Scale(const Vector2db& lhs, const Vector2db& rhs)
		{
			return Vector2db(lhs.x * rhs.x, lhs.y * rhs.y);
		}

		FORCEINLINE Vector2db Normalize(const Vector2db& inV) { return inV / Magnitude(inV); }

		FORCEINLINE Vector2db NormalizeSafe(const Vector2db& inV, const Vector2db& defaultV = Vector2db::zero)
		{
			double mag = Magnitude(inV);
			if (mag > kEpsilon)
				return inV / mag;
			else
				return defaultV;
		}

		FORCEINLINE double Distance(const Vector2db& lhs, const Vector2db& rhs)
		{
			return lhs.Distance(rhs);
		}

		FORCEINLINE bool IsNormalized(const Vector2db& vec)
		{
			return vec.SqrMagnitude() == 1.0;
		}

		FORCEINLINE Vector2db Abs(const Vector2db& v)
		{
			return Vector2db(Rainbow::Abs(v.x), Rainbow::Abs(v.y));
		}

		//FORCEINLINE Vector2db NormalizeFast(const Vector2db& inV)
		//{
		//	double m = SqrMagnitude(inV);
		//	return inV * FastInvSqrt(m);
		//}

		//FORCEINLINE bool Equal(const Vector2db& p1, const Vector2db& p2, const double epsilon = kEpsilon)
		//{
		//	// return (Abs(p1.x - p2.x) < epsilon) && (Abs(p1.y - p2.y) < epsilon);
		//	return p1.x == p2.x && p1.y == p2.y;
		//}

		// template<class TransferFunction>
		// FORCEINLINE void Vector2db::Transfer(TransferFunction& transfer)
		// {
		// 	if (transfer.IsWriting())
		// 	{
		// 		double rx = FloatKeep(x, 4);
		// 		TRANSFER_WITH_NAME(rx, "x");
		// 		double ry = FloatKeep(y, 4);
		// 		TRANSFER_WITH_NAME(ry, "y");
		// 	}
		// 	else {
		// 		TRANSFER(x);
		// 		TRANSFER(y);
		// 	}
		// }

	}
}