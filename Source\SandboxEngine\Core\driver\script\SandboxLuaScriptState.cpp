/**
* file : SandboxLuaScriptState
* func : lua脚本虚拟机
* by : chenzh
*/
#include "SandboxLuaScriptState.h"
#include "SandboxLua.h"
#include "SandboxSceneObject.h"
#include "SandboxTimer.h"
#include "SandboxListener.h"
#include "SandboxCoreLuaDirector.h"
#include "SandboxLuaScript.h"
#include "SandboxLuaCoroutine.h"
#include "SandboxSceneManager.h"
#include "SandboxSceneRoot.h"
#include "SandboxGameMap.h"
#include "SandboxReflexEnum.h"
#include "SandboxUnit.h"
#include "SandboxModuleScriptNode.h"
#include "SandboxScriptNode.h"
#include "SandboxBinary.h"
#include "SandboxLuaUserdata.h"
#include "SandboxCustomBuffer.h"
#include "Common/GameStatic.h"
#include "script/SandboxLuaCoroutinePool.h"
#include "script/SandboxLuaCoroutineJob.h"
#include "event/notify/SandboxGlobalNotify.h"
#include "script/bridge/SandboxSceneObjectBridge.h"
#include "script/bridge/SandboxCommonBridge.h"
#include "script/bridge/SandboxEnumBridge.h"
#include "script/bridge/SandboxSignalBridge.h"
#include "script/bridge/SandboxCoroutineInstanceBridge.h"
#include "script/bridge/SandboxCoroutineRewriteBridge.h"
#include "script/bridge/SandboxReflexReferenceBridge.h"
#include "script/linker/SandboxLuaLinkerData.h"
#include "File/FileManager.h"
#if USE_OPTICK
#include "Debug/DebugMgr.h"
#endif // 0

#include "ScriptVM/OgreScriptLuaVM.h"
#if (LUA_MEM_PROFILER_ENABLE == 2)
#include "Profiler/LuaGCProfile.h"
#endif // LUA_MEM_PROFILER_ENABLE


#ifdef DEDICATED_SERVER
#include "ModuleInterface/ICloudProxy.h"
#endif


extern "C" {
#if (defined(IWORLD_SERVER_BUILD) && defined(SERVER_LUA_JIT)) || defined(CLIENT_LUA_JIT)
#else
#include "lstate.h"
#endif
#include <File/FileManager.h>
}

namespace MINIW
{
	void* LuaStateAlloc(void* ud, void* ptr, size_t osize, size_t nsize);
}

namespace MNSandbox { namespace Lua {

	StateLevel::StateLevel(lua_State* L)
	{
		GetSandboxScriptVM().GetLuaStateStack().Push(L);
	}

	StateLevel::~StateLevel()
	{
		GetSandboxScriptVM().GetLuaStateStack().Pop();
	}

}}

namespace MNSandbox {


	static const std::string s_nodehead = "nodeid=";

	/* lua内存分配 */
	size_t ScriptState::s_luaAllocTotal = 0;
	static void* lua_alloc(void* ud, void* ptr, size_t osize, size_t nsize)
	{
		ScriptState::s_luaAllocTotal = ScriptState::s_luaAllocTotal + nsize - osize;
#if 1
		return MINIW::LuaStateAlloc(ud, ptr, osize, nsize);
#else
		if (nsize == 0)
		{
			if (ptr)
				SANDBOX_FREE(ptr);
			return nullptr;
		}
		else if (osize == 0)
		{
			return SANDBOX_MALLOC(nsize);
		}
		else
		{
			return SANDBOX_REALLOC(ptr, nsize);
		}
#endif
	}
	void LoadDefaultLuaFile(lua_State* L)
	{

		if (Rainbow::AutoRefPtr<Rainbow::DataStream> fp = Rainbow::GetFileManager().OpenFile("ministudio/LuaProfiler.lua", true))
		{
			size_t len = fp->Size();
			AutoRef<CustomBuffer> cb = CustomBuffer::Create(len);
			size_t cnt = fp->Read(cb->Data(), len);

			luaL_loadbuffer(L, cb->Data<const char>(), cb->Size(), "ministudio/LuaProfiler.lua");
			if(lua_pcall(L, 0, 0, 0) != 0)
			{
				const char* szError = lua_tostring(L, -1);
				SANDBOX_WARNINGEX(false, szError);
				lua_pop(L, 1);
			}
		}
	}

	//////////////////////////////////////////////////////////////////

	double ScriptState::ms_waitTimeMin = 0.001;
	double ScriptState::ms_waitTimeMax = 1000.0;

	ScriptState::ScriptState()
		: m_listenCoroutineAdd(this, &ScriptState::OnLuaCoroutineAdd)
		, m_listenCoroutineRemove(this, &ScriptState::OnLuaCoroutineRemove)
		, m_listenCoroutineDirtyRelease(this, &ScriptState::OnCoroutineDirtyRelease)
	{
	}

	ScriptState::~ScriptState()
	{
		if (m_mainState)
			lua_close(m_mainState);
	}

	bool ScriptState::OpenState(lua_State* entryL)
	{
		if (m_mainState)
			return false;

		s_luaAllocTotal = 0; // 重置lua vm 内存使用量
		Vector3Bridge::s_Vector3BridgePoolIndex = 0; // 重置Vector3BridgePoolIndex
		Vector3Bridge::s_Vector3BridgePoolStart = 0; // 重置Vector3BridgePoolStart

		if (!InitMainL(entryL))
			return false;

		lua_State* L = m_mainState;

#if USE_OPTICK
		if (m_useNewL)
		{
			Rainbow::GetDebugMgr().RegisterLuaProfiler(L);
		}
#endif

		// 创建协程池 
		m_coPool = SANDBOX_NEW(Lua::LuaCoroutinePool, L);
		m_coPool->m_notifyCoroutineAdd.Subscribe(m_listenCoroutineAdd);
		m_coPool->m_notifyCoroutineRemove.Subscribe(m_listenCoroutineRemove);

		if (GetStaticNotifyScriptState().IsValid())
		{
			GetStaticNotifyScriptState().Emit(true);
		}

		SetRunState(RUNSTATE::RUNNING);
		return true;
	}

	void ScriptState::CloseState()
	{
		if (GetStaticNotifyScriptState().IsValid())
		{
			GetStaticNotifyScriptState().Emit(false);
		}
			
		SetRunState(RUNSTATE::CLOSED);

		// 清理协程池
		SANDBOX_DELETE(m_coPool);

		m_coroutines.clear();
		m_coDirtys.clear();
		m_listenCoroutineDirtyRelease.ClearBindNotify();

#if USE_OPTICK
		if (m_useNewL)
		{
			Rainbow::GetDebugMgr().UnregisterLuaProfiler(m_mainState);
		}
#endif

		ReleaseMainL();
	}

	AutoRef<LuaCoroutine> ScriptState::CreateCoroutine(const AutoRef<Lua::CoJobData>& jobdata, const WeakRef<LuaCoroutine>& owner)
	{
		// 缓存协程列表
		AutoRef<LuaCoroutine> coroutine = SANDBOX_NEW(LuaCoroutine, jobdata, owner);

		return coroutine;
	}

	void ScriptState::DestroyCoroutine(AutoRef<LuaCoroutine> coroutine)
	{
		if (!coroutine)
			return;

		lua_State* L = coroutine->GetLuaState();
		if (!m_mainState || !L)
		{
			return;
		}

		m_coDirtys.push_back(coroutine); // 放到脏表，下一帧释放
		if (!m_listenCoroutineDirtyRelease.IsBindNotify())
			GlobalNotify::GetInstance().GetNextTick().Subscribe(m_listenCoroutineDirtyRelease);

		// 清除缓存
		m_coroutines.erase(L);

		SANDBOX_RELEASE(coroutine);
	}

	void ScriptState::CoroutineRunOver(AutoRef<LuaCoroutine> coroutine)
	{
		if (!coroutine)
			return;

		lua_State* L = coroutine->GetLuaState();
		if (!m_mainState || !L)
		{
			return;
		}

		NotifyCoroutineRelease(L);
	}

	void ScriptState::BindLuaStateRef(lua_State* L, LuaCoroutine* co)
	{
		m_coroutines[L] = co;
	}

	void ScriptState::UnbindLuaStateRef(lua_State* L, LuaCoroutine* co)
	{
		auto iter = m_coroutines.find(L);
		if (iter == m_coroutines.end() || iter->second != co)
		{
			return;
		}

		m_coDirtys.push_back(co); // 放到脏表，下一帧释放
		if (!m_listenCoroutineDirtyRelease.IsBindNotify())
			GlobalNotify::GetInstance().GetNextTick().Subscribe(m_listenCoroutineDirtyRelease);

		m_coroutines.erase(iter);
	}

	AutoRef<LuaCoroutine> ScriptState::GetLuaStateRef(lua_State* L)
	{
		auto iter = m_coroutines.find(L);
		if (iter == m_coroutines.end())
		{
			return nullptr;
		}
		return iter->second;
	}

	void ScriptState::PrintRunError(lua_State* L, WeakRef<LuaCoroutine> co, const std::string& log)
	{
		if (!co)
			co = GetSandboxScriptVM().GetLuaStateRef(L).get();

		int line = 0;
		std::string path = GetNodePath(co, &line);
		std::string errorlog = ToString("[path:", path, "]:", line, ": ", log);

		if (GetStaticNotifyRunError().IsValid())
		{	
			GetStaticNotifyRunError().Emit(L, errorlog, nullptr, 0);
		}

		SANDBOX_LUAERROR(errorlog); 

#ifdef DEDICATED_SERVER
		if (Rainbow::GetICloudProxyPtr())
			Rainbow::GetICloudProxyPtr()->SimpleErrLog(0, 0, "ss_lua_error", errorlog);
#endif // DEDICATED_SERVER
	}

	void ScriptState::LogLuaRunError(lua_State* L, const std::string& errmsg)
	{
		LuaError err;
		LuaCoroutine::ParseLuaError(errmsg, err);
		ScriptState::LogLuaRunError(L, err); // 错误码
	}

	void ScriptState::LogLuaRunError(lua_State* L, const LuaError& err)
	{
		if (!err._node)
			return PrintRunError(L, nullptr, err._msg);

		if (GetStaticNotifyRunError().IsValid())
		{
			GetStaticNotifyRunError().Emit(L, err._msg, err._node, err._line);
		}
		std::string errorlog = ToString("[path:", err._node->GetPath(), "]:", err._line, ":", err._msg);
		SANDBOX_LUAERROR(errorlog);
#ifdef DEDICATED_SERVER
		if (Rainbow::GetICloudProxyPtr())
			Rainbow::GetICloudProxyPtr()->SimpleErrLog(0, 0, "ss_lua_error", errorlog);
#endif // DEDICATED_SERVER
	}

	void ScriptState::LogRunError(WeakRef<LuaCoroutine> co, const std::string& msg)
	{
		SANDBOX_ASSERT(co /*&& co->IsValid()*/);
		PrintRunError(co ? co->GetLuaState() : nullptr, co, msg);
	}

	void ScriptState::LogRunError(lua_State* L, const std::string& msg) // L 填nil会直接从当前执行的的co栈中取出来
	{
		WeakRef<LuaCoroutine> co = nullptr;
		if (L)
		{
			co = GetSandboxScriptVM().GetLuaStateRef(L).get();
		}
		else
		{
			co = GetSandboxScriptVM().GetLuaRunCoroutine().get();
		}
		if (!co || !co->IsValid())
		{
			SANDBOX_ASSERT(false && "lua is disactived!");
			return;
		}
		LogRunError(co, msg);
	}

	void ScriptState::PrintLuaLog(lua_State* L, const std::string& log)
	{
		int line = 0;
		SandboxNodeID keyNodeid = 0;
		std::string path = GetNodePath(L, &line, &keyNodeid);
		std::string msg = ToString("[path:", path, "]:", line, ": ", log);
		AutoRef<SandboxNode> node = GetSceneManager().GetNodeById(keyNodeid);

		if (GetStaticNotifyPrint().IsValid())
		{
			GetStaticNotifyPrint().Emit(L, log, node.get(), line);
		}

		SANDBOX_LUALOG(msg);
	}

	void ScriptState::PrintLuaLog(const std::string& log)
	{
		auto co = GetSandboxScriptVM().GetLuaRunCoroutine();
		if (co && co->IsValid())
		{
			PrintLuaLog(co->GetLuaState(), log);
		}
	}

	void ScriptState::LuaThrowError(lua_State* L, const std::string& error)
	{
		if (!L)
		{
			auto co = GetSandboxScriptVM().GetLuaRunCoroutine();
			if (co && co->IsValid())
			{
				L = co->GetLuaState();
			}
			if (!L)
			{
				SANDBOX_ASSERT(false);
				return;
			}
		}

		//std::string log;

		//// 行号
		//lua_Debug ar;
		//if (lua_getstack(L, 1, &ar) && lua_getinfo(L, "nSl", &ar))
		//{
		//	int line = ar.currentline;
		//	log = std::string("line:") + to_string(line) + std::string(" ");
		//}
		//log += error;

		//lua_pushstring(L, log.c_str());
		//lua_error(L);

		luaL_error(L, error.c_str());
	}

	/////////////////////////////////////////////////////

	std::string ScriptState::GetNodePath(WeakRef<LuaCoroutine> co, int* line)
	{
		if (line)
			*line = 0;

		if (co)
		{
			if (co->IsValid())
				return GetNodePath(co->GetLuaState(), line);

			auto node = co->GetScriptNode();
			if (node)
				return node->GetPath();
		}
		return std::string("lua state is deactive");
	}

	std::string ScriptState::GetNodePath(lua_State* L, int* line, SandboxNodeID* keyNodeid)
	{
		lua_Debug ar;

		memset(&ar, 0, sizeof(ar));

		// 获取堆栈
		if (lua_getstack(L, 1, &ar) == 0)
			return std::string();

		// 获取short source
		if (line)
			lua_getinfo(L, "Sl", &ar);
		else
			lua_getinfo(L, "S", &ar);

		if (strncmp(ar.source, s_nodehead.c_str(), s_nodehead.length()) != 0)
			return std::string("unkown");

		if (line)
			*line = ar.currentline;

		long long nodeid = atoll(&ar.source[s_nodehead.length()]);

		if (keyNodeid)
		{
			*keyNodeid = nodeid;
		}

		AutoRef<SandboxNode> node = GetSceneManager().GetNodeById(nodeid);
		return node ? node->GetPath() : std::string();
	}

	std::string ScriptState::LuaToString(lua_State* L, int objindex)
	{
		objindex = objindex >= 0 ? objindex : lua_gettop(L) + 1 + objindex;

		char szTemp[128];
		switch (lua_type(L, objindex))
		{
		case LUA_TUSERDATA:
		case LUA_TLIGHTUSERDATA:
			{
				KeepStackTop _luastack(L);

				lua_getmetatable(L, objindex);																// stack: meta
				if (lua_istable(L, -1))
				{
					lua_getfield(L, -1, "__reflextype");													// stack: meta, rt
					if (!lua_isnil(L, -1))
					{
						ReflexType* type = reinterpret_cast<ReflexType*>(lua_touserdata(L, -1));
						auto& policy = type->GetPolicy();
						if (policy.IsEnableToString())
						{
							return policy.m_cbToString(lua_touserdata(L, objindex));
						}
					}
				}
				sprintf(szTemp, "[userdata(%d)=%s:%p]", objindex, luaL_typename(L, objindex), lua_topointer(L, objindex));
				return szTemp;
			}
		case LUA_TNUMBER:
		case LUA_TSTRING:
			{
				return lua_tostring(L, objindex);
			}
		case LUA_TBOOLEAN:
			{
				return lua_toboolean(L, objindex) ? "true" : "false";
			}
		case LUA_TNIL:
			{
				return "nil";
			}
		default:
			{
				sprintf(szTemp, "[param(%d)=%s:%p]", objindex, luaL_typename(L, objindex), lua_topointer(L, objindex));
				return szTemp;
			}
		}
	}

	int ScriptState::OnPrint(lua_State* L)
	{
		/*if (IsLuaScriptTimeout(L))
		{
			LuaThrowError(L, "Error! Could be a infinite loop!");
			return 0;
		}*/

		int top = lua_gettop(L);

		std::string log;
		for (int i = 1; i <= top; i++)
		{
			if (i > 1)
			{
				log += "";
			}
			log += ScriptState::LuaToString(L, i);
		}

		PrintLuaLog(L, log);
		return 0;
	}

	int ScriptState::OnWait(lua_State* L)
	{
		auto& scriptState = GetSandboxScriptVM();
		if (scriptState.IsWaitToClose()) // 已经在结束过程中了，不需要在等待
			return 0;

		WeakRef<LuaCoroutine> coroutine = scriptState.GetLuaStateRef(L).get();
		if (!coroutine)
			return 0;

		if (!lua_isnumber(L, 1))
		{
			LuaThrowError(L, "Wait function [1] param is error! need number");
			return 0;
		}

		double timeout = lua_tonumber(L, 1);
		timeout = Rainbow::Clamp(timeout, ms_waitTimeMin, ms_waitTimeMax);

		// 创建定时器，延迟timeout 后，再次唤醒
		auto timer = MNTimer::CreateTimer(timeout);

		// userdata
		timer->BindUserdata.BindUserdata(coroutine, "co"); // 如果L中途释放了，会清空L

		typedef ListenerMethodRef<AutoRef<MNTimer>> TypeWaitIsOver;
		AutoRef<TypeWaitIsOver> listen = SANDBOX_NEW(TypeWaitIsOver, &ScriptState::OnWaitIsOver);
		timer->Subscribe(listen);

		coroutine->PauseUserdata.BindUserdata(timer); // 绑定timer，coroutine 释放时可以直接释放
		coroutine->PauseUserdata.BindUserdata(listen);
		return coroutine->Pause();
	}

	void ScriptState::OnWaitIsOver(AutoRef<MNTimer> timer)
	{
		auto ud = timer->BindUserdata.GetUserdata("co");
		WeakRef<LuaCoroutine> ref = ud ? ud.ToCast<LuaCoroutine>().get() : nullptr;
		if (!ref)
		{
			//SANDBOX_ASSERT(false && "userdata type is invalid!");
			return;
		}

		if (!ref->IsValid()) // 可能协程已经关闭了
			return;

		AutoRef<LuaCoroutine> co = ref.get();
		co->Resume();
	}

	int ScriptState::OnRequire(lua_State* L)
	{
		WeakRef<LuaCoroutine> coroutine = GetSandboxScriptVM().GetLuaStateRef(L).get();
		//SANDBOX_ASSERT(coroutine);
		if (!coroutine) {
			ScriptState::LuaThrowError(L, "require error! coroutine run failed!!");
			return 0;
		}
		// 第一个参数必须是SandboxNode
		AutoRef<SandboxNode> node;
		if (!lua_toC(L, 1, node))
		{
			ScriptState::LuaThrowError(L, "require error! param [1] need Module Script!");
			return 0;
		}
		if (!node)
		{
			ScriptState::LuaThrowError(L, "require error! param [1] is nil!");
			return 0;
		}

		auto moduleScript = node.ToCast<ModuleScriptNode>();
		if (!moduleScript)
		{
			ScriptState::LuaThrowError(L, "require error! param [1] need Module Script!!");
			return 0;
		}

		if (moduleScript->LoadScript())
		{
			return moduleScript->Require(L);
		}

		// 没有加载完成，可能是 yield 了
		typedef ListenerFunctionRef<WeakRef<ModuleScriptNode>> ListenerLoadOver;
		moduleScript->ms_notifyLoadOver.Subscribe(SANDBOX_NEW(ListenerLoadOver, [coroutine](WeakRef<ModuleScriptNode> ms) -> void {
			ScriptState::OnRequireIsOver(coroutine, ms);
		}));
		return coroutine->Pause();
	}

	int ScriptState::OnPcall(lua_State* L)
	{
		/*
		* param[1] : function
		* param[...] : params 参数
		*/
		if (!lua_isfunction(L, 1))
		{
			ScriptState::LogRunError(L, ToString("param [1] is not a function!"));
			return 0;
		}

		// 创建lua function 对象
		AutoRef<LuaFunction> luaf = LuaLinkerManager::GetInstance().CreateFunctionLinker(L, 1);// 绑定function
		if (!luaf)
		{
			ScriptState::LogRunError(L, ToString("param [1] is not a function!"));
			return 0;
		}

		// 创建参数对象
		AutoRef<ReflexLuaTuple> luaTuple;
		if (!lua_toC<AutoRef<ReflexLuaTuple>>(L, 2, luaTuple))
		{
			ScriptState::LogRunError(L, ToString("param [2] is not valid!"));
			return 0;
		}

		// 执行
		int cnt = luaf->CallFunctionResultCallback(L, [](lua_State* L, LuaCoroutine* co) -> int {
			if (!co) return 0;
			if (co->IsSuccess())
			{
				lua_pushboolean(L, (int)true);
				int cnt = 1;
				auto data = co->GetResult();
				cnt += data ? data->GetDatas(L) : 0;
				return cnt;
			}
			else
			{
				lua_pushboolean(L, (int)false);
				lua_pushstring(L, co->GetErrorLog().c_str());
				return 2;
			}
			return 0;
		}, luaTuple);
		return cnt;
	}

	int ScriptState::OnXpcall(lua_State* L)
	{
		/*
		* param[1] : function
		* param[2] : err run
		* param[...] : params 参数
		*/
		if (!lua_isfunction(L, 1))
		{
			ScriptState::LogRunError(L, ToString("param [1] is not a function!"));
			return 0;
		}
		if (!lua_isfunction(L, 2))
		{
			ScriptState::LogRunError(L, ToString("param [2] is not a function!"));
			return 0;
		}

		// 创建lua function 对象
		AutoRef<LuaFunction> luaf = LuaLinkerManager::GetInstance().CreateFunctionLinker(L, 1);// 绑定function
		if (!luaf)
		{
			ScriptState::LogRunError(L, ToString("param [1] is not a function!"));
			return 0;
		}
		AutoRef<LuaFunction> luaErr = LuaLinkerManager::GetInstance().CreateFunctionLinker(L, 2);// 绑定function
		if (!luaf)
		{
			ScriptState::LogRunError(L, ToString("param [2] is not a function!"));
			return 0;
		}

		// 创建参数对象
		AutoRef<ReflexLuaTuple> luaTuple;
		if (!lua_toC<AutoRef<ReflexLuaTuple>>(L, 3, luaTuple))
		{
			ScriptState::LogRunError(L, ToString("param [3] is not valid!"));
			return 0;
		}

		// 执行
		int cnt = luaf->CallFunctionResultCallback(L, [luaErr](lua_State* L, LuaCoroutine* co) -> int {
			if (!co) return 0;
			if (co->IsSuccess())
			{
				lua_pushboolean(L, (int)true);
				int cnt = 1;
				auto data = co->GetResult();
				cnt += data ? data->GetDatas(L) : 0;
				return cnt;
			}
			else
			{
				luaErr->CallLuaFunction<std::string>(co->GetErrorLog()); // 执行错误回调
				lua_pushboolean(L, (int)false);
				return 1;
			}
			return 0;
		}, luaTuple);
		return cnt;
	}

	int ScriptState::OnLuaAssert(lua_State* L)
	{
		/*
		* param[1] : boolean
		* param[2] : string
		*/
		bool condition = lua_toboolean(L, 1);
		const char* msg = lua_tostring(L, 2);
		SANDBOX_ASSERTEX(condition, ToString("msg : ", msg, ", backtrace : ", GetLuaTraceBack(L)));
		return 0;
	}

	void ScriptState::OnRequireIsOver(WeakRef<LuaCoroutine> co, WeakRef<ModuleScriptNode> ms)
	{
		SANDBOX_ASSERT(ms);
		if (!co || !co->IsValid()) // 可能协程已经关闭了
			return;

		AutoRef<LuaCoroutine> co_ref = co.get();
		lua_State* L = co->GetLuaState();

		int paramcnt = ms->Require(L); // 取出的数据放到 L 中
		co_ref->Resume(paramcnt);
	}

	void ScriptState::SetRunState(RUNSTATE state)
	{
		switch (state)
		{
		case RUNSTATE::IDLE:
			if (m_runState == RUNSTATE::CLOSED)
				m_runState = state;
			break;
		case RUNSTATE::RUNNING:
		case RUNSTATE::CLOSED:
			m_runState = state;
			break;
		case RUNSTATE::WAITTOCLOSE:
			if (m_runState == RUNSTATE::RUNNING)
				m_runState = state;
			break;
		}
	}

	void ScriptState::OnLuaCoroutineAdd(Lua::LuaCoroutineInstance* ins, lua_State* L)
	{
		// 新建协程
		GetStaticNotifyCoroutineNew().Emit(L, nullptr);
	}

	void ScriptState::OnLuaCoroutineRemove(Lua::LuaCoroutineInstance* ins, lua_State* L)
	{
		// 协程释放
		GetStaticNotifyCoroutineRelease().Emit(L);
	}

	void ScriptState::OnCoroutineDirtyRelease()
	{
		m_coDirtys.clear();
	}

	AutoRef<LuaCoroutine> ScriptState::GetLuaRunCoroutine() const
	{
		lua_State* L = GetSandboxScriptVM().GetTopLuaState();
		if (!L)
			return nullptr;

		return GetSandboxScriptVM().GetLuaStateRef(L);
	}

	std::string ScriptState::GetLuaTraceBack(lua_State* L)
	{
		if (!L)
			return std::string();

		lua_Debug ar;
		std::string msg;
		AutoRef<SandboxNode> node;

		for (int i = 1; i < 5; ++i)
		{
			node = nullptr;
			memset(&ar, 0, sizeof(ar));
			if (lua_getstack(L, i, &ar) == 0)
				break;

			lua_getinfo(L, "nSl", &ar);
			if (strncmp(ar.source, s_nodehead.c_str(), s_nodehead.length()) == 0)
			{
				long long nodeid = atoll(&ar.source[s_nodehead.length()]);
				node = GetSceneManager().GetNodeById(nodeid);
			}

			if (!msg.empty()) msg += "\n\t";

			// [path]:line:
			if (node)
			{
				msg += ToString("[", node->GetPath(), "]:", ar.currentline, ": ");
			}
			else
			{
				msg += ToString("[", ar.source, "]:", ar.currentline, ": ");
			}

			// msg
			if (ar.namewhat && ar.namewhat[0])  /* is there a name? */
			{
				msg += ToString("in function \'", ar.name, "\'");
			}
			else if (ar.what)
			{
				if (ar.what[0] == 'm')  /* main? */
					msg += ToString("in main chunk");
				else if (ar.what[0] == 'C' || ar.what[0] == 't')
					msg += ToString("?");  /* C function or tail call */
				else
					msg += ToString("in function <", ar.short_src, ":", ar.linedefined, ">");
			}
			else
				SANDBOX_ASSERT(false);
		}
		return msg;
	}

	/////////////////////////////////////////////////////////////

	static MINIW::GameStatic<Notify<lua_State*>> s_notifyRegisterMetatable;
	static MINIW::GameStatic<Notify<lua_State*>> s_notifyRegisterClassLibrary;
	static MINIW::GameStatic<Notify<lua_State*>> s_notifyApi;
	static MINIW::GameStatic<Notify<lua_State*, lua_State*>> s_notifyCoroutineNew; // 通知：新增线程
	static MINIW::GameStatic<Notify<lua_State*>> s_notifyCoroutineRelease; // 通知：线程销毁
	static MINIW::GameStatic<Notify<lua_State*>> s_notifyCoroutineResume; // 通知：线程激活
	static MINIW::GameStatic<Notify<lua_State*, const std::string&, WeakRef<SandboxNode>, int>> s_notifyRunError; // 通知：lua执行错误提示
	static MINIW::GameStatic<Notify<lua_State*, std::string, WeakRef<SandboxNode>, int>> s_notifyPrint; // 通知：lua执行打印 print
	static MINIW::GameStatic<Notify<AutoRef<LuaCoroutine>, WeakRef<SandboxNode>>> s_notifyLoadScriptNode; // 通知：加载节点
	static MINIW::GameStatic<Notify<bool>> s_notifyScriptState;		// 通知：虚拟机状态

	Notify<lua_State*>& ScriptState::GetStaticNotifyRegisterMetatable()
	{
		return *s_notifyRegisterMetatable.EnsureInitialized();
	}
	Notify<lua_State*>& ScriptState::GetStaticNotifyRegisterClassLibrary()
	{
		return *s_notifyRegisterClassLibrary.EnsureInitialized();
	}

    MNSandbox::Notify<lua_State*>& ScriptState::GetStaticNotifyAPI()
    {
		return *s_notifyApi.EnsureInitialized();
    }

    Notify<lua_State*, lua_State*>& ScriptState::GetStaticNotifyCoroutineNew()
	{
		return *s_notifyCoroutineNew.EnsureInitialized();
	}
	Notify<lua_State*>& ScriptState::GetStaticNotifyCoroutineRelease()
	{
		return *s_notifyCoroutineRelease.EnsureInitialized();
	}
	Notify<lua_State*>& ScriptState::GetStaticNotifyCoroutineResume()
	{
		return *s_notifyCoroutineResume.EnsureInitialized();
	}
	Notify<lua_State*, const std::string&, WeakRef<SandboxNode>, int>& ScriptState::GetStaticNotifyRunError()
	{
		return *s_notifyRunError.EnsureInitialized();
	}
	Notify<lua_State*, std::string, WeakRef<SandboxNode>, int>& ScriptState::GetStaticNotifyPrint()
	{
		return *s_notifyPrint.EnsureInitialized();
	}
	Notify<AutoRef<LuaCoroutine>, WeakRef<SandboxNode>>& ScriptState::GetStaticNotifyLoadScriptNode()
	{
		return *s_notifyLoadScriptNode.EnsureInitialized();
	}

	Notify<bool>& ScriptState::GetStaticNotifyScriptState()
	{
		return *s_notifyScriptState.EnsureInitialized();
	}

	ScriptState& GetSandboxScriptVM()
	{
		return GetCoreLuaDirector().GetSandboxLuaVM();
	}

	bool ScriptState::CallLuaFunction(lua_State* L, const std::string& FunctionName)
	{
#if (defined LUA_PROFILER)
		L = L ? L : GetSandboxScriptVM().GetLuaState();

		if (!L)
		{
			return false;
		}

		lua_getglobal(L, FunctionName.c_str());
		if (!lua_isfunction(L, -1))
		{
			const char* szError = lua_tostring(L, -1);
			lua_pop(L, 1);
			SANDBOX_ASSERT(false);
			return false;
		}

		if (lua_pcall(L, 0, 0, 0) != 0)
		{
			const char* szError = lua_tostring(L, -1);
			lua_pop(L, 1);
			SANDBOX_ASSERT(false);
			return false;
		}
#endif // LUA_PROFILER

		return true;
	}


	/////////////////////////////////////////////////////////////

	bool ScriptState::InitMainL(lua_State* entryL)
	{
		SANDBOX_WARNING(!m_mainState);

		// 初始化L
		lua_State* L = nullptr;
		if (!entryL) // 有表示从生存一侧传入的
		{
			L = CreateMainL();
			m_useNewL = true;
		}
		else
		{
			L = CreateMainThread(entryL);
			m_useNewL = false;
		}
		if (!L)
		{
			SANDBOX_ASSERT(false && "lua new state failed!");
			return false;
		}

#ifdef SANDBOX_TICK_LUA_GC_SWITCH
#ifdef DEDICATED_SERVER
		static int gcstepmul = 200;
#else
		static int gcstepmul = 2000;  // 在 1500 到 2500 之间取值，主要是看 峰值，以及均值的情况，做个取舍
#endif
		lua_gc(L, LUA_GCSETSTEPMUL, gcstepmul); // 加大每次 step操作数值，用于减少atomic 函数操作的耗时
#endif //SANDBOX_TICK_LUA_GC_SWITCH

		// 缓存L
		m_mainState = L;

		// 注册C++对象的metatable
		{
			SDBCHECKSTACKTOP(TestRegStack1, L);

			SceneObjectBridge::RegisterMetatable(L);
			EnumBridge::RegisterMetatable(L);
			EnumBridgeItem::RegisterMetatable(L);
			EnumBridgeContainer::RegisterMetatable(L);
			Vector3Bridge::RegisterMetatable(L);
			WCoordBridge::RegisterMetatable(L);
			QuaternionBridge::RegisterMetatable(L);
			TransformBridge::RegisterMetatable(L);
			Vector2Bridge::RegisterMetatable(L);
			Vector4Bridge::RegisterMetatable(L);
			RectFloatBridge::RegisterMetatable(L);
			ColorValueBridge::RegisterMetatable(L);
			ColorQuadBridge::RegisterMetatable(L);
			RayBridge::RegisterMetatable(L);
			TweenInfoBridge::RegisterMetatable(L);
			SignalBridge::RegisterMetatable(L);
			ConnectionBridge::RegisterMetatable(L);
			//SequenceFloatBridge::RegisterMetatable(L);
			Binary::RegisterMetatable(L);
			RangeInfoBridge::RegisterMetatable(L);
			Matrix3fBridge::RegisterMetatable(L);
			Matrix4fBridge::RegisterMetatable(L);
			SceneConfigBridge::RegisterMetatable(L);
			CoInstanceBridge::RegisterMetatable(L);
			if (!MNSandbox::Config::GetSingleton().IsPublicMode()) //冒险等地图使用的是gamevm，下面rewrite会导致lua出错
			{
				CoroutineRewriteBridge::RegisterMetatable(L);
			}
			ReflexReferenceBridge::RegisterMetatable(L);
			GetStaticNotifyRegisterMetatable().Emit(L);
		}

		// 注册C++对象
		{
			SDBCHECKSTACKTOP(TestRegStack2, L);

			SceneObjectBridge::Super::RegisterClassLibrary(L);
			SceneObjectBridge::RegisterClassLibrary(L);
			EnumBridge::Super::RegisterClassLibrary(L);
			EnumBridgeItem::Super::RegisterClassLibrary(L);
			EnumBridgeContainer::Super::RegisterClassLibrary(L);
			Vector3Bridge::RegisterClassLibrary(L);
			WCoordBridge::RegisterClassLibrary(L);
			QuaternionBridge::RegisterClassLibrary(L);
			TransformBridge::RegisterClassLibrary(L);
			Vector2Bridge::RegisterClassLibrary(L);
			Vector4Bridge::RegisterClassLibrary(L);
			RectFloatBridge::RegisterClassLibrary(L);
			ColorValueBridge::RegisterClassLibrary(L);
			ColorQuadBridge::RegisterClassLibrary(L);
			RayBridge::RegisterClassLibrary(L);
			TweenInfoBridge::RegisterClassLibrary(L);
			//SequenceFloatBridge::RegisterClassLibrary(L);
			Binary::RegisterClassLibrary(L);
			RangeInfoBridge::RegisterClassLibrary(L);
			Matrix3fBridge::RegisterClassLibrary(L);
			Matrix4fBridge::RegisterClassLibrary(L);
			SceneConfigBridge::RegisterClassLibrary(L);
			if (!MNSandbox::Config::GetSingleton().IsPublicMode()) //冒险等地图使用的是gamevm，下面rewrite会导致lua出错
			{
				CoroutineRewriteBridge::RegisterClassLibrary(L);
			}
			GetStaticNotifyRegisterClassLibrary().Emit(L);
		}

		// 注册全局(含方法)
		{
			SDBCHECKSTACKTOP(TestRegStack3, L);

			// 压入API game
			SceneObjectBridge::PushInstance(L, GetCurrentGameMap()->GetNode_Game());
			lua_pushvalue(L, -1);
			lua_setglobal(L, "Game");
			lua_setglobal(L, "game");

			// 压入枚举
			EnumBridgeContainer::PushInstance(L, &ReflexEnumContainer::Singleton::Get());
			lua_pushvalue(L, -1);
			lua_setglobal(L, "Enum");
			lua_setglobal(L, "enum");

			// 打印
			lua_pushcfunction(L, OnPrint);
			lua_pushvalue(L, -1);
			lua_setglobal(L, "Print");
			lua_setglobal(L, "print");

			// 延迟等待
			lua_pushcfunction(L, OnWait);
			lua_pushvalue(L, -1);
			lua_setglobal(L, "Wait");
			lua_setglobal(L, "wait");

			// require
			lua_pushcfunction(L, OnRequire);
			lua_pushvalue(L, -1);
			lua_setglobal(L, "Require");
			lua_setglobal(L, "require");

			// pcall
			lua_pushcfunction(L, OnPcall);
			lua_setglobal(L, "pcall");

			// xpcall
			lua_pushcfunction(L, OnXpcall);
			lua_setglobal(L, "xpcall");

#ifdef SANDBOX_DEV
			// lua 触发 assert
			lua_pushcfunction(L, OnLuaAssert);
			lua_setglobal(L, "LuaAssert");
#endif
			GetStaticNotifyAPI().Emit(L);
		}

		return true;
	}

	void ScriptState::ReleaseMainL()
	{
		if (m_useNewL)
		{
			//2024-09-09 Studio关闭云服调试：以小镇为例：≈164ms
			DestroyMainL();
		}
		else
		{
			ReleaseMainThread();
		}

		// 清理缓存
		m_useNewL = false;
		m_mainState = nullptr;
	}

	lua_State* ScriptState::CreateMainL()
	{
		lua_State* L = nullptr;
#if (defined(IWORLD_SERVER_BUILD) && defined(SERVER_LUA_JIT)) || defined(CLIENT_LUA_JIT)
		L = luaL_newstate();
#else
		L = lua_newstate(lua_alloc, nullptr);
#endif

#if (LUA_MEM_PROFILER_ENABLE == 2) //&& defined(BUILD_MINI_EDITOR_APP)
		//如果是Game，就在这里注册；如果是Studio，那么将在SandboxLuaScriptState注册。
		LuaGCProfile::GetInstancePtr()->OnCreateLuaState(L);
#endif

		// 加载默认库（仅新建的L 需要）
		InitLuaDefaultLibs(L);

#if (defined LUA_PROFILER)
		// 加载默认项
		LoadDefaultLuaFile(L);

		/*
		lua_getglobal(L, "debug");
		if (!lua_isnil(L, -1))
		{
			lua_pushcfunction(L, ConverToPath);
			lua_setfield(L, -2, "converToPath");
		}
		lua_pop(L, 1);
		
		auto hookFun = [](lua_State* L, lua_Debug* dbg) {
			ScriptState::OnLuaHookFunc(L, dbg);
		};

		if (lua_State* currState = GetLuaState())
		{
			lua_sethook(currState, hookFun, LUA_MASKLINE | LUA_MASKCALL | LUA_MASKRET | LUA_MASKCOUNT, 500000);
			ScriptState::CallLuaFunction(nullptr, "StartProfiler");
		}
		*/
#endif

		// 构建一个沙盒用的域 table “Sandbox”
		lua_pushvalue(L, LUA_GLOBALSINDEX);									// stack: t
		int tidx = lua_gettop(L);
		RegSandboxLocalMetatable(L, tidx);
		RegLimitDefaultLibApis(L, tidx);
		lua_pop(L, 1);														// stack:
		return L;
	}

	void ScriptState::DestroyMainL()
	{
		if (m_mainState)
		{
//#if (defined LUA_PROFILER)
			//ScriptState::CallLuaFunction(nullptr, "StopProfiler2");
			//lua_sethook(m_mainState, nullptr, LUA_MASKLINE | LUA_MASKCALL | LUA_MASKRET | LUA_MASKCOUNT, 500000);
//#endif
			lua_close(m_mainState);
		}
	}

	lua_State* ScriptState::CreateMainThread(lua_State* entryL)
	{
		SANDBOX_WARNING(entryL);
		lua_State* L = nullptr;

		// 新建协程
		int top = lua_gettop(entryL);
		lua_pushlightuserdata(entryL, (void*)this);							// stack: ud
		lua_rawget(entryL, LUA_REGISTRYINDEX);								// stack: L
		if (lua_isnil(entryL, -1))
		{
			lua_pop(entryL, 1);												// stack:

			lua_pushlightuserdata(entryL, (void*)this);						// stack: ud
			L = lua_newthread(entryL);										// stack: ud, L
			lua_rawset(entryL, LUA_REGISTRYINDEX);							// stack: 
		}
		else
		{
			SANDBOX_ASSERT(false);
			L = lua_tothread(entryL, -1);
			lua_pop(entryL, 1);
		}
		SANDBOX_ASSERT(top == lua_gettop(entryL));

		if (!L)
		{
			SANDBOX_ASSERT(false && "lua_newthread is failed!");
			return nullptr;
		}

		// 构建一个沙盒用的域 table “Sandbox”
		top = lua_gettop(L);
		{
			lua_getglobal(L, "Sandbox");										// stack: t
			if (lua_isnil(L, -1))
			{
				lua_pop(L, 1);													// stack: 

				// 加载默认项
				LoadDefaultLuaFile(L);

				lua_newtable(L);												// stack: t
				PushSandboxMetatable(L);										// stack: t, meta
				lua_setmetatable(L, -2);										// stack: t

				// 设置到全局
				lua_pushvalue(L, -1);											// stack: t, t
				lua_setglobal(L, "Sandbox");									// stack: t

				// _G 指定成自己
				lua_pushvalue(L, -1);											// stack: t, t
				lua_setfield(L, -2, "_G");										// stack: t

				// 注册
				int tidx = lua_gettop(L);
				RegSandboxLocalMetatable(L, tidx);
				RegLimitDefaultLibApis(L, tidx);
			}

			// 设置域
			lua_pushthread(L);													// stack: t, L
			lua_pushvalue(L, -2);												// stack: t, L, t
			lua_setfenv(L, -2);													// stack: t, L
			lua_pop(L, 2);														// stack: 
		}
		SANDBOX_ASSERT(top == lua_gettop(L));
		return L;
	}

	void ScriptState::ReleaseMainThread()
	{
		lua_State* L = m_mainState;
		if (L)
		{
			lua_pushlightuserdata(L, (void*)this);							// stack: ud
			lua_pushnil(L);													// stack: ud, nil
			lua_rawset(L, LUA_REGISTRYINDEX);								// stack:
		}
	}

	void ScriptState::InitLuaDefaultLibs(lua_State* L)
	{
		SDBCHECKSTACKTOP(TestRegStack0, L);

		lua_CFunction baselibs[] = {
			luaopen_base,
			luaopen_string,
			luaopen_table,
			luaopen_math,
			luaopen_os,
			nullptr,
		};
		int count = 0;
		for (int idx = 0; baselibs[idx] != nullptr; ++idx)
		{
			count = baselibs[idx](L);
			lua_pop(L, count);
		}

#if (defined LUA_PROFILER)
		static const luaL_Reg lualibs[] = {
			{LUA_IOLIBNAME, luaopen_io},
			{NULL, NULL}
		};

		const luaL_Reg* lib = lualibs;
		for (; lib->func; lib++) {
			lua_pushcfunction(L, lib->func);
			lua_pushstring(L, lib->name);
			lua_call(L, 1, 0);
		}
#endif // LUA_PROFILER

		// 提供editor 调试lua 用
		//minigame 也开放调试lua if (Config::GetSingleton().IsRunningInEditor())
		{
			count = luaopen_debug(L);
			lua_pop(L, count);
		}
	}

	void ScriptState::PushSandboxMetatable(lua_State* L)
	{
		luaL_getmetatable(L, "Sandbox");									// stack: meta
		if (lua_isnil(L, -1))
		{
			lua_pop(L, 1);													// stack: 

			// 设置metatable
			luaL_newmetatable(L, "Sandbox");								// stack: meta
			lua_pushvalue(L, LUA_GLOBALSINDEX);								// stack: meta, _G
			lua_setfield(L, -2, "__index");									// stack: meta
		}
	}

	void ScriptState::RegSandboxLocalMetatable(lua_State* L, int index)
	{
		SDBCHECKSTACKTOP(TestRegStack, L);

		// 设置local metatable
		luaL_getmetatable(L, "SandboxLocal");								// stack: meta
		if (lua_isnil(L, -1))
		{
			lua_pop(L, 1);													// stack:

			luaL_newmetatable(L, "SandboxLocal");							// stack: meta
			SANDBOX_ASSERT(lua_type(L, -1) == LUA_TTABLE);

			lua_pushvalue(L, index);										// stack: meta, uper
			lua_setfield(L, -2, "__index");									// stack: meta
			lua_pushvalue(L, index);										// stack: meta, uper
			lua_setfield(L, -2, "__newindex");								// stack: meta
		}
		lua_pop(L, 1);
	}

	void ScriptState::RegLimitDefaultLibApis(lua_State* L, int index)
	{
		SDBCHECKSTACKTOP(TestRegStack, L);

		lua_pushvalue(L, index);											// stack: t

		// 屏蔽loadfile, loadstring, dofile
		lua_pushstring(L, "loadfile unsupported");
		lua_setfield(L, -2, "loadfile");
		lua_pushstring(L, "loadstring unsupported");
		lua_setfield(L, -2, "loadstring");
		lua_pushstring(L, "dofile unsupported");
		lua_setfield(L, -2, "dofile");

		// os 限制
		lua_newtable(L);													// stack: t, tos
		lua_getglobal(L, "os");												// stack: t, tos, os
		SANDBOX_ASSERT(lua_type(L, -1) == LUA_TTABLE);
		lua_getfield(L, -1, "date");										// stack: t, tos, os, date
		lua_setfield(L, -3, "date");										// stack: t, tos, os
		lua_getfield(L, -1, "time");										// stack: t, tos, os, time
		lua_setfield(L, -3, "time");										// stack: t, tos, os
		lua_getfield(L, -1, "clock");										// stack: t, tos, os, clock
		lua_setfield(L, -3, "clock");										// stack: t, tos, os
		lua_pop(L, 1);														// stack: t, tos
		lua_setfield(L, -2, "os");											// stack: t

		//debug.traceback
		lua_newtable(L);													// stack: t, tdebug
		lua_getglobal(L, "debug");											// stack: t, tdebug, debug
		SANDBOX_ASSERT(lua_type(L, -1) == LUA_TTABLE);
		lua_pushcfunction(L, OnTraceback);									// stack: t, tdebug, debug, OnTraceback
		lua_setfield(L, -3, "traceback");									// stack: t, tdebug, debug
		lua_pushcfunction(L, OnGetInfo);									// stack: t, tdebug, debug, OnGetInfo
		lua_setfield(L, -3, "getinfo");										// stack: t, tdebug, debug
		lua_pop(L, 1);														// stack: t, tdebug
		lua_setfield(L, -2, "debug");										// stack: t

		lua_pop(L, 1);														// stack:
	}

	int ScriptState::OnTraceback(lua_State* L)
	{
		std::string msg = GetLuaTraceBack(L);
		lua_pushlstring(L, msg.c_str(), msg.length());
		return 1;
	}

	int ScriptState::OnGetInfo(lua_State* L)
	{
		lua_Debug ar;

		int level = luaL_optint(L, 1, 1);
		luaL_argcheck(L, level >= 0, 1, "level must be non-negative");
		if (lua_getstack(L, level, &ar) == 0)
			luaL_argerror(L, 1, "invalid level");

		lua_getinfo(L, "Snl", &ar);

		lua_newtable(L);
		lua_pushstring(L, ar.name);
		lua_setfield(L, -2, "name");

		lua_pushstring(L, ar.namewhat);
		lua_setfield(L, -2, "namewhat");

		lua_pushinteger(L, ar.currentline);
		lua_setfield(L, -2, "currentline");

		lua_pushinteger(L, ar.linedefined);
		lua_setfield(L, -2, "linedefined");

		lua_pushinteger(L, ar.lastlinedefined);
		lua_setfield(L, -2, "lastlinedefined");

		
		std::string source = ar.source;
		if (source.compare(0, s_nodehead.length(), s_nodehead) == 0)
		{
			SandboxNodeID nodeid = atoll(source.c_str() + s_nodehead.length());
			auto node = GetSceneManager().GetNodeById(nodeid).get();
			if (node)
			{
				std::string path = node->GetPath();
				lua_pushstring(L, path.c_str());
				lua_setfield(L, -2, "short_src");
			}
			else
			{
				lua_pushstring(L, ar.short_src);
				lua_setfield(L, -2, "short_src");
			}
		}
		else
		{
			lua_pushstring(L, ar.short_src);
			lua_setfield(L, -2, "short_src");
		}

		return 1;
	}

#if (defined LUA_PROFILER)
	void ScriptState::OnLuaHookFunc(lua_State* L, lua_Debug* dbg)
	{
		switch (dbg->event)
		{
		case LUA_HOOKCALL:
			ScriptState::CallLuaFunction(L, "ProfilerOnHookCall");
			break;
		case LUA_HOOKRET:
			ScriptState::CallLuaFunction(L, "ProfilerOnHookReturn");
			break;
		default:
			break;
		}
	}

	void ScriptState::ConverToPathNative(const std::string& source, std::string& dst)
	{
		if (source.compare(0, s_nodehead.length(), s_nodehead.c_str()) == 0)
		{
			SandboxNodeID nodeid = atoll(source.c_str() + s_nodehead.length());
			auto node = GetSceneManager().GetNodeById(nodeid).get();
			if (node)
			{
				dst = node->GetPath();
			}
		}
	}

	int ScriptState::ConverToPath(lua_State* L)
	{
		int n = lua_gettop(L);
		if (n == 1)
		{
			if (lua_isstring(L, -1))
			{
				std::string src = lua_tostring(L, -1);
				std::string dst = src;
				ScriptState::ConverToPathNative(src, dst);
				lua_pushstring(L, dst.c_str());
				return 1;
			}
		}
		return 0;
	}
#endif

	void ScriptState::StartLuaScriptTimeConsuming()
	{
		if (m_luaScriptTimeFlag == 0)
			m_luaScriptTimeConsuming = GetCurSysMilliSecond();

		m_luaScriptTimeFlag++;
	}

	void ScriptState::EndLuaScriptTimeConsuming()
	{
		if (m_luaScriptTimeFlag <= 0)
			SANDBOX_ASSERT(false && "m_luaScriptTimeFlag count error!");
		else
			m_luaScriptTimeFlag--;
	}

//	bool ScriptState::IsLuaScriptTimeout(lua_State* L)
//	{
//		// 暂时不做超时判断了，反馈很少死循环逻辑，断点反倒容易触发该判断。
//		return false;
//
//		static unsigned int __time = 60000;
//#ifdef IWORLD_DEV_BUILD
//		__time = 0xFFFFFFFF;
//#endif // IWORLD_DEV_BUILD
//		if (GetSandboxScriptVM().GetLuaScriptTimeFlag() <= 0)// 不是沙盒虚拟机进来的lua脚本，不做超时判断
//			return false;
//		unsigned int val = GetCurSysMilliSecond() - GetSandboxScriptVM().GetLuaScriptTimeConsuming();
//		//if (5000 < val && val < __time)// 耗时特别长的内容提示给开发者
//		//{
//		//	LogLuaRunError(L, ToString("Tips! So far, the script or call to the function has taken ", val, " ms"));
//		//}
//		return val > __time;// 大于该豪秒数可以认为死循环了 
//	}

	void ScriptState::StartLuaHookTiming()
	{
#ifdef IWORLD_DEV_BUILD
		auto sec = GetCurSysMicroSecond();
		if (m_luaScriptFunctionTimeList.size() > m_luaScriptFunctionTimeFlag)
		{
			m_luaScriptFunctionTimeList[m_luaScriptFunctionTimeFlag] = sec;
			m_luaScriptFunctionMemoryList[m_luaScriptFunctionTimeFlag] = s_luaAllocTotal;
		}
		else
		{
			m_luaScriptFunctionTimeList.push_back(sec);
			m_luaScriptFunctionMemoryList.push_back(s_luaAllocTotal);
		}
		m_luaScriptFunctionTimeFlag++;
#endif // IWORLD_DEV_BUILD
	}

	void ScriptState::EndLuaHookTiming(lua_State* L)
	{
#ifdef IWORLD_DEV_BUILD
		m_luaScriptFunctionTimeFlag--;
		if (m_luaScriptFunctionTimeFlag >= 0)
		{
			static int __time = 1000;
			static int __mem = 200000;
			{
				int mem = s_luaAllocTotal - m_luaScriptFunctionMemoryList[m_luaScriptFunctionTimeFlag];
				int t = GetCurSysMicroSecond() - m_luaScriptFunctionTimeList[m_luaScriptFunctionTimeFlag];
				if (t > __time || mem > __mem)
				{
					lua_Debug ar;
					if (lua_getstack(L, 2, &ar) != 0)
					{
						std::string log = ToString(t, " us --", mem, " bytes \n\t", GetLuaTraceBack(L));
						SANDBOX_LUAPROFILERLOG(log);

						if (Config::GetSingleton().IsShowDebugInfoAble())
						{
							if (t > m_luaScriptFunctionMaxTime)
							{
								m_luaScriptFunctionMaxTime = t;

								if (m_luaScriptFunctionTimeMap.size() > 10)
								{
									int minTime = t;
									for (auto iter = m_luaScriptFunctionTimeMap.begin(); iter != m_luaScriptFunctionTimeMap.end();++iter)
									{
										if (iter->first < minTime)
										{
											minTime = iter->first;
										}
									}
									m_luaScriptFunctionTimeMap.erase(minTime);
								}
								m_luaScriptFunctionTimeMap[t] = log;
							}
						}
					}
				}
				if (m_luaFunctionInfoRunningTime == 0) m_luaFunctionInfoRunningTime = GetCurSysMilliSecond();
				size_t timeVal = GetCurSysMilliSecond() - m_luaFunctionInfoRunningTime;
				if (timeVal < 35000 && timeVal > 5000)
				{
					lua_Debug ar, ar1;
					if (lua_getstack(L, 2, &ar1) != 0 && lua_getstack(L, 1, &ar) != 0)
					{
						AutoRef<SandboxNode> node = nullptr;
						lua_getinfo(L, "nSl", &ar);
						std::string key = ToString(ar.short_src, ar.name, ar.currentline);
						if (m_luaFunctionInfoMap.find(key) == m_luaFunctionInfoMap.end())
						{
							LuaFunctionInfo info;
							std::string title;
							if (strncmp(ar.source, s_nodehead.c_str(), s_nodehead.length()) == 0)
							{
								long long nodeid = atoll(&ar.source[s_nodehead.length()]);
								node = GetSceneManager().GetNodeById(nodeid);
							}
							if (node)
								title = ToString("[", node->GetPath(), "]:line ", ar.currentline, ": ");
							else
								title = ToString("[", ar.source, "]:line ", ar.currentline, ": ");
							memset(&ar, 0, sizeof(ar));
							if (lua_getstack(L, 0, &ar) != 0)
							{
								lua_getinfo(L, "nSl", &ar);
								if (ar.namewhat && ar.namewhat[0])
									title += ToString(ar.namewhat, ": \'", ar.name, "\'");
							}
							info.name = title;
							m_luaFunctionInfoMap.emplace(key, info);
						}
						LuaFunctionInfo& info = m_luaFunctionInfoMap[key];
						info.callCount++;
						if (info.maxTime < t) info.maxTime = t;
						info.totalTime += t;
					}
				}
				else if (timeVal > 60000)
				{
					m_luaFunctionInfoRunningTime = 0;
					std::vector<std::pair<std::string, LuaFunctionInfo>> vec(m_luaFunctionInfoMap.begin(), m_luaFunctionInfoMap.end());
					std::sort(vec.begin(), vec.end(), [](std::pair<std::string, LuaFunctionInfo>& v1, std::pair<std::string, LuaFunctionInfo>& v2){ return v1.second.totalTime > v2.second.totalTime; });

					for (auto& iter : vec)
					{
						std::string log = ToString(iter.second.name, " maxTime: ", iter.second.maxTime, " us - totalTime: ", iter.second.totalTime, " us - count: ", iter.second.callCount);
						SANDBOX_LUAPROFILERLOG(log);
					}
					m_luaFunctionInfoMap.clear();
				}
			}
		}
#endif // IWORLD_DEV_BUILD
	}

}