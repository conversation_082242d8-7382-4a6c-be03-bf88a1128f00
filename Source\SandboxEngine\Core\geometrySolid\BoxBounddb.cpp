#include "geometrySolid/BoxBounddb.h"
//#include "Math/Matrix4x4f.h"

namespace MNSandbox {
	namespace GeometrySolid {
		/*BoxBounddb BoxBounddb::transformBy(const Matrix4x4f& tm) const
		{
			Vector3db Vertices[8];
			Vertices[0] = m_MinPos;
			Vertices[1] = Vector3db(m_MinPos.x, m_MinPos.y, m_MaxPos.z);
			Vertices[2] = Vector3db(m_MinPos.x, m_MaxPos.y, m_MinPos.z);
			Vertices[3] = Vector3db(m_MaxPos.x, m_MinPos.y, m_MinPos.z);
			Vertices[4] = Vector3db(m_MaxPos.x, m_MaxPos.y, m_MinPos.z);
			Vertices[5] = Vector3db(m_MaxPos.x, m_MinPos.y, m_MaxPos.z);
			Vertices[6] = Vector3db(m_MinPos.x, m_MaxPos.y, m_MaxPos.z);
			Vertices[7] = m_MaxPos;

			Vertices[0] = tm.MultiplyPoint3(Vertices[0]);
			Vertices[1] = tm.MultiplyPoint3(Vertices[1]);
			Vertices[2] = tm.MultiplyPoint3(Vertices[2]);
			Vertices[3] = tm.MultiplyPoint3(Vertices[3]);
			Vertices[4] = tm.MultiplyPoint3(Vertices[4]);
			Vertices[5] = tm.MultiplyPoint3(Vertices[5]);
			Vertices[6] = tm.MultiplyPoint3(Vertices[6]);
			Vertices[7] = tm.MultiplyPoint3(Vertices[7]);

			BoxBounddb NewBox;
			NewBox += Vertices[0];
			NewBox += Vertices[1];
			NewBox += Vertices[2];
			NewBox += Vertices[3];
			NewBox += Vertices[4];
			NewBox += Vertices[5];
			NewBox += Vertices[6];
			NewBox += Vertices[7];

			return NewBox;
		}*/

		/**
		* Transforms and projects a world bounding box to screen space
		*
		* @param	ProjM - projection matrix
		* @return	transformed box
		*/
		/*BoxBounddb BoxBounddb::transformProjectBy(const Matrix4x4f& ProjM) const
		{
			Vector3db Vertices[8] =
			{
				Vector3db(m_MinPos),
					Vector3db(m_MinPos.x, m_MinPos.y, m_MaxPos.z),
					Vector3db(m_MinPos.x, m_MaxPos.y, m_MinPos.z),
					Vector3db(m_MaxPos.x, m_MinPos.y, m_MinPos.z),
					Vector3db(m_MaxPos.x, m_MaxPos.y, m_MinPos.z),
					Vector3db(m_MaxPos.x, m_MinPos.y, m_MaxPos.z),
					Vector3db(m_MinPos.x, m_MaxPos.y, m_MaxPos.z),
					Vector3db(m_MaxPos)
			};

			BoxBounddb NewBox;
			for (int VertexIndex = 0; VertexIndex < ARRAY_ELEMENTS(Vertices); VertexIndex++)
			{
				Vector4f projvert(Vertices[VertexIndex], 1.0f);
				ProjM.MultiplyVector4(projvert, projvert);
				NewBox += Vector3db(projvert.x, projvert.y, projvert.z) / projvert.w;
			}

			return NewBox;
		}*/

		/*void BoxBounddb::setVertexBuffer(const float *vert, size_t vertsize, size_t num)
		{
			m_isValid = false;

			while (num--)
			{
				*this += *(Vector3db *)vert;

				vert = (const float *)(((const char *)vert) + vertsize);
			}
		}*/
	}
}