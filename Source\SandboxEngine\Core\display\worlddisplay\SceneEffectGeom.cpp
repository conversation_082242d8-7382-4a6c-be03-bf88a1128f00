#include "SceneEffectGeom.h"

#include "Common/OgreWCoord.h"

using namespace MNSandbox;
using namespace Rainbow;

SceneEffectGeom::SceneEffectGeom()
{
}

SceneEffectGeom::SceneEffectGeom(CurveFace* curveFaces)
    : SceneEffectGeom()
{
}

SceneEffectGeom::~SceneEffectGeom()
{
	OnClear();
}

bool SceneEffectGeom::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectGeom::SetAwayShow(bool alwaysShow)
{
	m_AlwaysShow = alwaysShow;
}

void SceneEffectGeom::SetShow(bool bShow)
{
	m_bShow = bShow;
}

void SceneEffectGeom::SetCurveFaces(CurveFace* curveFaces)
{
	m_CurveFaces = curveFaces;
}

void SceneEffectGeom::SetStroke(int stroke, bool bRefresh)
{
	m_iStroke = stroke;
	if (bRefresh)
	{
		Refresh();
	}
}

void SceneEffectGeom::SetPos(const WCoord startpos, const WCoord endpos)
{
	m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
	m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
	m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
	m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
	m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
	m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
	m_StartPosBlock = m_StartPos / BLOCK_SIZE;
	m_EndPosBlock = m_EndPos / BLOCK_SIZE;
	m_vCenter = ((m_StartPos + m_EndPos) / 2).toVector3();
}

void SceneEffectGeom::SetPos(const WCoord startpos, const WCoord endpos, 
	Vector3f v, Quaternionf q, Vector3f vs, AutoRef<SandboxNode> node)
{
	m_vCenter = v;
	m_qRotation = q;
	m_vScale = vs;
	m_ownerNode = node.get();
	//m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
	//m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
	//m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
	//m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
	//m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
	//m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
	m_StartPos = startpos;
	m_EndPos = endpos;
	m_StartPosBlock = q * startpos.toVector3();
	m_EndPosBlock = q * endpos.toVector3();
	m_StartPosBlock = (v + m_StartPosBlock) / BLOCK_SIZE;
	m_EndPosBlock = (v + m_EndPosBlock) / BLOCK_SIZE;
}

void SceneEffectGeom::SetPos(const WCoord startpos, const WCoord endpos, bool bNormalize)
{
	if (bNormalize)
	{
		m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
		m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
		m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
		m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
		m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
		m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
	}
	else
	{
		m_StartPos = startpos;
		m_EndPos = endpos;
	}

	m_StartPosBlock = m_StartPos / BLOCK_SIZE;
	m_EndPosBlock = m_EndPos / BLOCK_SIZE;
}

void SceneEffectGeom::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;
}

void SceneEffectGeom::SetCenter(const MNCoord3f& center)
{
	m_vCenter = center;
}

void SceneEffectGeom::SetRadius(float radius)
{
	if (m_radius == radius)
	{
		return;
	}
	m_radius = radius;
	m_originRadius = radius;
}

void SceneEffectGeom::SetHeight(float h)
{
	if (m_fHeight == h)
		return;

	m_fHeight = h;
}