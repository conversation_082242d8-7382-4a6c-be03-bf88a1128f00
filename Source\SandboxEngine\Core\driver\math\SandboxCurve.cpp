#include "SandboxCurve.h"
#include "SandboxReflexTypePolicy.h"
#include "SandboxReflexTypePolicyEx.h"

namespace MNSandbox
{
	// #define RECORD_EFFECTOBJECT_REFLEX_LOG
	#define USE_SPECIAL_TYPE_REFLEX_VALUE
	
	RangeFloatCurve::RangeFloatCurve() {}
	RangeFloatCurve::RangeFloatCurve(bool)
	{
		static bool isFirst = true;
		static Rainbow::AnimationCurve DefaultCurve;
		if(isFirst)
		{
			isFirst = false;
			Rainbow::AnimationCurve::Keyframe keys[2] = { Rainbow::AnimationCurve::Keyframe(0.0f, 0.0f), Rainbow::AnimationCurve::Keyframe(1.0f, 1.0f) };
			keys[0].inSlope = 0.0f; keys[0].outSlope = 1.0f;
			keys[1].inSlope = 1.0f; keys[1].outSlope = 0.0f;
			DefaultCurve.Assign(keys, keys + 2);
		}
		ResetToMNSandboxCurve(DefaultCurve, _min);
		ResetToMNSandboxCurve(DefaultCurve, _max);
	}

	RangeFloatCurve::RangeFloatCurve(FloatCurve val): _min(val), _max(val) {}
	RangeFloatCurve::RangeFloatCurve(FloatCurve min, FloatCurve max) :_min(min), _max(max) {}

#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
	#define QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(T)	\
		static std::vector<T> all##T; 					\
		static std::unordered_map<int,int> map##T; 		\
		static void Add##T(const T& other){				\
			bool isFind = false;						\
			for(int i = 0; i < all##T.size(); i++)		\
			{											\
				if(all##T[i] == other)					\
				{										\
					isFind = true;						\
					map##T[i] = map##T[i] + 1;			\
					break;								\
				}										\
			}											\
			if(!isFind)									\
			{											\
				int idx = all##T.size();				\
				map##T[idx] = 1;						\
				all##T.push_back(other);				\
			}											\
		}
	
	QUICK_DEFINE_STATICS_REFLEX_SPECIAL_TYPE(RangeFloatCurve)

	static void PrintTop100RangeFloatCurve()
	{
		std::multimap<int, int> decentMaps;
		for(const auto& one : mapRangeFloatCurve)
		{
			decentMaps.emplace(one.second, one.first);
		}
		int count = 100;
		std::vector<RangeFloatCurve> curve1; 
		for(auto it = decentMaps.crbegin(); it != decentMaps.crend(); ++it)
		{
			count = count - 1;
			curve1.push_back(allRangeFloatCurve[it->second]);
			if(count < 0)
			{
				break;
			}
		}
		auto d2 = 10;
	}
#endif

#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
	static RangeFloatCurve& GetRangeFloatCurve1()
	{
		static RangeFloatCurve ret;
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_Curve.clear();
			ret._min.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(3);
			ret._min.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(3);
			ret._min.m_RotationOrder = static_cast<math::RotationOrder>(4);

			ret._max.m_Curve.clear();
			ret._max.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(3);
			ret._max.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(3);
			ret._max.m_RotationOrder = static_cast<math::RotationOrder>(4);
		}
		return ret;
	}
	static RangeFloatCurve& GetRangeFloatCurve2()
	{
		static RangeFloatCurve ret = GetRangeFloatCurve1();
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_RotationOrder = static_cast<math::RotationOrder>(3);
			ret._max.m_RotationOrder = static_cast<math::RotationOrder>(3);
		}
		return ret;
	}
	static RangeFloatCurve& GetRangeFloatCurve3()
	{
		static RangeFloatCurve ret(true);
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_Curve[0].time = 0.0f;
			ret._min.m_Curve[0].value = 1.0f;
			ret._min.m_Curve[0].outSlope = 0.0f;
			ret._min.m_Curve[0].inSlope = 0.0f;
			ret._min.m_Curve[1].time = 1.0f;
			ret._min.m_Curve[1].value = 1.0f;
			ret._min.m_Curve[1].outSlope = 0.0f;
			ret._min.m_Curve[1].inSlope = 0.0f;
			ret._min.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(2);
			ret._min.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(2);
			ret._min.m_RotationOrder = static_cast<math::RotationOrder>(4);

			ret._max.m_Curve[0].time = 0.0f;
			ret._max.m_Curve[0].value = 1.0f;
			ret._max.m_Curve[0].outSlope = 0.0f;
			ret._max.m_Curve[0].inSlope = 0.0f;
			ret._max.m_Curve[1].time = 1.0f;
			ret._max.m_Curve[1].value = 1.0f;
			ret._max.m_Curve[1].outSlope = 0.0f;
			ret._max.m_Curve[1].inSlope = 0.0f;
			ret._max.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(2);
			ret._max.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(2);
			ret._max.m_RotationOrder = static_cast<math::RotationOrder>(4);
		}
		return ret;
	}
	static RangeFloatCurve& GetRangeFloatCurve4()
	{
		static RangeFloatCurve ret = GetRangeFloatCurve3();
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_RotationOrder = static_cast<math::RotationOrder>(2);
			ret._max.m_RotationOrder = static_cast<math::RotationOrder>(2);
		}
		return ret;
	}
	static RangeFloatCurve& GetRangeFloatCurve5()
	{
		static RangeFloatCurve ret = GetRangeFloatCurve3();
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_Curve[0].value = 0.0f;
			ret._min.m_Curve[0].outSlope = 1.0f;
			ret._min.m_Curve[1].inSlope = 1.0f;

			ret._max.m_Curve[0].value = 0.0f;
			ret._max.m_Curve[0].outSlope = 1.0f;
			ret._max.m_Curve[1].inSlope = 1.0f;
		}
		return ret;
	}
	static RangeFloatCurve& GetRangeFloatCurve6()
	{
		static RangeFloatCurve ret = GetRangeFloatCurve5();
		static bool isFirst = true;
		if(isFirst)
		{
			isFirst = false;
			ret._min.m_RotationOrder = static_cast<math::RotationOrder>(2);
			ret._max.m_RotationOrder = static_cast<math::RotationOrder>(2);
		}
		return ret;
	}
	static int isSpecialDefaultFloatCurve(const RangeFloatCurve& origin)
	{
		if(origin == GetRangeFloatCurve1())
		{
			return 1;
		}
		else if(origin == GetRangeFloatCurve2())
		{
			return 2;
		}
		else if(origin == GetRangeFloatCurve3())
		{
			return 3;
		}
		else if (origin == GetRangeFloatCurve4())
		{
			return 4;
		}
		else if (origin == GetRangeFloatCurve5())
		{
			return 5;
		}
		else if (origin == GetRangeFloatCurve6())
		{
			return 6;
		}
		return 0;
	}
#endif

#if 0
	template<> 
	void ReflexPolicySerialize<CurvePointTemplate<float>>::CallbackSerialize(const void* data, MNJsonVal& out) 
	{ 
		auto& v = Data(data); 
		MNJsonObject object;
		object << "ver" << (MNJsonNumber)(v.ver);
		object << "time" << (MNJsonNumber)(v.time);
		{
			MNJsonVal item;
			ReflexPolicySerialize<float>::CallbackSerialize((const void*)(&v.value), item);
			object.import("value", item);
		}
		{
			MNJsonVal item;
			ReflexPolicySerialize<float>::CallbackSerialize((const void*)(&v.inSlope), item);
			object.import("inSlope", item);
		}
		{
			MNJsonVal item;
			ReflexPolicySerialize<float>::CallbackSerialize((const void*)(&v.outSlope), item);
			object.import("outSlope", item);
		}
		object << "tangentMode" << (MNJsonNumber)(v.tangentMode);
		object << "weightedMode" << (MNJsonNumber)(v.weightedMode);
		{
			MNJsonVal item;
			ReflexPolicySerialize<float>::CallbackSerialize((const void*)(&v.inWeight), item);
			object.import("inWeight", item);
		}
		{
			MNJsonVal item;
			ReflexPolicySerialize<float>::CallbackSerialize((const void*)(&v.outWeight), item);
			object.import("outWeight", item);
		}
		out.import(object); 
	} 
	template<> 
	bool ReflexPolicySerialize<CurvePointTemplate<float>>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& value = Data(data);
		const auto& object = in.get<MNJsonObject>();
		auto ret = true;
		ret &= object.has<MNJsonNumber>("ver");
		std::remove_reference<decltype(value)>::type temp;
		if (ret)
		{
			temp.ver = static_cast<int>(object.get<MNJsonNumber>("ver"));
			temp.time = object.get<MNJsonNumber>("time");
			{
				decltype(value.value) temp2;
				const auto& item = object.get<MNJsonVal>("value");
				ReflexPolicySerialize<float>::CallbackUnserialize((void*)(&temp2), item);
				temp.value = temp2;
			}
			{
				decltype(value.inSlope) temp2;
				const auto& item = object.get<MNJsonVal>("inSlope");
				ReflexPolicySerialize<float>::CallbackUnserialize((void*)(&temp2), item);
				temp.inSlope = temp2;
			}
			{
				decltype(value.outSlope) temp2;
				const auto& item = object.get<MNJsonVal>("outSlope");
				ReflexPolicySerialize<float>::CallbackUnserialize((void*)(&temp2), item);
				temp.outSlope = temp2;
			}
			temp.tangentMode = static_cast<int>(object.get<MNJsonNumber>("tangentMode"));
			temp.weightedMode = static_cast<int>(object.get<MNJsonNumber>("weightedMode"));
			{
				decltype(value.outSlope) temp2;
				const auto& item = object.get<MNJsonVal>("outSlope");
				ReflexPolicySerialize<float>::CallbackUnserialize((void*)(&temp2), item);
				temp.outSlope = temp2;
			}
			{
				decltype(value.inWeight) temp2;
				const auto& item = object.get<MNJsonVal>("inWeight");
				ReflexPolicySerialize<float>::CallbackUnserialize((void*)(&temp2), item);
				temp.inWeight = temp2;
			}
			value = temp;
		}
		return ret;
	}
	template<> 
	std::string ReflexPolicyToString<CurvePointTemplate<float>>::CallbackToString(const void* data)
	{
		MNJsonVal value;
		ReflexPolicySerialize<CurvePointTemplate<float>>::CallbackSerialize(data, value);
		return value.get<MNJsonObject>().json_nospace();
	} 
	template<> 
	size_t ReflexPolicySerialize<CurvePointTemplate<float>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& value = Data(data); 
		size_t len = 0;
		len += out->WriteNumber<int>(value.ver);
		len += out->WriteNumber<float>(value.time);
		len += ReflexPolicySerialize<float>::ReflexToBinary((const void*)(&value.value), out);
		len += ReflexPolicySerialize<float>::ReflexToBinary((const void*)(&value.inSlope), out);
		len += ReflexPolicySerialize<float>::ReflexToBinary((const void*)(&value.outSlope), out);
		len += out->WriteNumber<int>(value.tangentMode);
		len += out->WriteNumber<int>(value.weightedMode);
		len += ReflexPolicySerialize<float>::ReflexToBinary((const void*)(&value.inWeight), out);
		len += ReflexPolicySerialize<float>::ReflexToBinary((const void*)(&value.outWeight), out);
		return len;
	}
	template<> 
	bool ReflexPolicySerialize<CurvePointTemplate<float>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& value = Data(data); 
		auto ret = true;
		ret &= in->ReadNumber<int>(value.ver);
		ret &= in->ReadNumber<float>(value.time);
		ret &= ReflexPolicySerialize<float>::ReflexFromBinary((void*)(&value.value),in);
		ret &= ReflexPolicySerialize<float>::ReflexFromBinary((void*)(&value.inSlope),in);
		ret &= ReflexPolicySerialize<float>::ReflexFromBinary((void*)(&value.outSlope),in);
		ret &= in->ReadNumber<int>(value.tangentMode);
		ret &= in->ReadNumber<int>(value.weightedMode);
		ret &= ReflexPolicySerialize<float>::ReflexFromBinary((void*)(&value.inWeight),in);
		ret &= ReflexPolicySerialize<float>::ReflexFromBinary((void*)(&value.outWeight),in);
		return ret;
	}


    template<> 
	void ReflexPolicySerialize<CurveTemplate<float>>::CallbackSerialize(const void* data, MNJsonVal& out) 
	{ 
		auto& v = Data(data); 
		MNJsonObject object;
		object << "ver" << (MNJsonNumber)(v.ver);
		MNJsonArray points;
		for (auto& one : v.m_Curve)
		{
			MNJsonVal itemValue;
			ReflexPolicySerialize<CurvePointTemplate<float>>::CallbackSerialize((const void*)(&one), itemValue);
			points.import(itemValue);
		}
		object << "points" << points;
		object << "preInfinity" << (MNJsonNumber)(v.m_PreInfinity);
		object << "postInfinity" << (MNJsonNumber)(v.m_PostInfinity);
		object << "rotationOrder" << (MNJsonNumber)(v.m_RotationOrder);
		out.import(object); 
	} 
	template<> 
	bool ReflexPolicySerialize<CurveTemplate<float>>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& value = Data(data);
		const auto& object = in.get<MNJsonObject>();
		auto ret = true;
		ret &= object.has<MNJsonNumber>("ver");
		std::remove_reference<decltype(value)>::type temp;
		if (ret)
		{
			temp.ver = static_cast<int>(object.get<MNJsonNumber>("ver"));
			const auto& points = object.get<MNJsonArray>("points");
			for (int i = 0; i < points.size(); i++)
			{
				const auto& itemJson = points.get<MNJsonVal>(i);
				decltype(value.m_Curve)::value_type itemValue;
				ReflexPolicySerialize<CurvePointTemplate<float>>::CallbackUnserialize((void*)(&itemValue), itemJson);
				temp.m_Curve.emplace_back(itemValue);
			}
			temp.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(object.get<MNJsonNumber>("preInfinity"));
			temp.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(object.get<MNJsonNumber>("postInfinity"));
			temp.m_RotationOrder = static_cast<math::RotationOrder>(object.get<MNJsonNumber>("postInfinity"));
			value = temp;
		}
		return ret;
	}
	template<> 
	std::string ReflexPolicyToString<CurveTemplate<float>>::CallbackToString(const void* data)
	{
		MNJsonVal value;
		ReflexPolicySerialize<CurveTemplate<float>>::CallbackSerialize(data, value);
		return value.get<MNJsonObject>().json_nospace();
	} 
	template<> 
	size_t ReflexPolicySerialize<CurveTemplate<float>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& value = Data(data); 
		size_t len = 0;
		len += out->WriteNumber<int>(value.ver);
		len += out->WriteNumber<int>(value.m_Curve.size());
		for (auto& one : value.m_Curve)
		{
			len += ReflexPolicySerialize<CurvePointTemplate<float>>::ReflexToBinary((const void*)(&one), out);
		}
		len += out->WriteNumber<int>(static_cast<int>(value.m_PreInfinity));
		len += out->WriteNumber<int>(static_cast<int>(value.m_PostInfinity));
		len += out->WriteNumber<int>(static_cast<int>(value.m_RotationOrder));
		return len;
	}
	template<> 
	bool ReflexPolicySerialize<CurveTemplate<float>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& value = Data(data); 
		auto ret = true;
		ret &= in->ReadNumber<int>(value.ver);
		int arraySize = 0;
		ret &= in->ReadNumber<int>(arraySize);
		for (int i = 0; i < arraySize; i++)
		{
			decltype(value.m_Curve)::value_type point;
			ret &= ReflexPolicySerialize<CurvePointTemplate<float>>::ReflexFromBinary((void*)(&point),in);
			if (!ret)
			{
				return false;
			}
			value.m_Curve.emplace_back(point);
		}
		int p1, p2, p3;
		ret &= in->ReadNumber<int>(p1);
		ret &= in->ReadNumber<int>(p2);
		ret &= in->ReadNumber<int>(p3);
		value.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(p1);
		value.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(p2);
		value.m_RotationOrder = static_cast<math::RotationOrder>(p3);
		return ret;
	}
#endif // 0


#define REG_REFLEXTYPEPOLICY_CURVE_POINT(Type, TEnum, ReflexFunc) \
    template<> \
	void ReflexPolicySerialize<CurvePointTemplate<Type>>::CallbackSerialize(const void* data, MNJsonVal& out) \
	{                                                   \
		auto& v = Data(data);                           \
		MNJsonObject object;                            \
		object << "ver" << (MNJsonNumber)(v.ver);       \
		object << "time" << (MNJsonNumber)(v.time);     \
		{                                               \
			MNJsonVal item;                             \
			ReflexFunc<Type>::CallbackSerialize((const void*)(&v.value), item);\
			object.import("value", item);               \
		}                                               \
		{                                               \
			MNJsonVal item;                             \
			ReflexFunc<Type>::CallbackSerialize((const void*)(&v.inSlope), item);\
			object.import("inSlope", item);             \
		}                                               \
		{                                               \
			MNJsonVal item;                             \
			ReflexFunc<Type>::CallbackSerialize((const void*)(&v.outSlope), item);\
			object.import("outSlope", item);            \
		}                                               \
		object << "tangentMode" << (MNJsonNumber)(v.tangentMode);\
		object << "weightedMode" << (MNJsonNumber)(v.weightedMode);\
		{                                               \
			MNJsonVal item;                             \
			ReflexFunc<Type>::CallbackSerialize((const void*)(&v.inWeight), item);\
			object.import("inWeight", item);            \
		}                                               \
		{                                               \
			MNJsonVal item;                             \
			ReflexFunc<Type>::CallbackSerialize((const void*)(&v.outWeight), item);\
			object.import("outWeight", item);           \
		}                                               \
		out.import(object);                             \
	}                                                   \
	template<>                                          \
	bool ReflexPolicySerialize<CurvePointTemplate<Type>>::CallbackUnserialize(void* data, const MNJsonVal& in)\
	{                                                   \
		auto& value = Data(data);                       \
		const auto& object = in.get<MNJsonObject>();    \
		auto ret = true;                                \
		ret &= object.has<MNJsonNumber>("ver");         \
		std::remove_reference<decltype(value)>::type temp;\
		if (ret)                                        \
		{                                               \
			temp.ver = static_cast<int>(object.get<MNJsonNumber>("ver"));\
			temp.time = object.get<MNJsonNumber>("time");\
			{                                           \
				decltype(value.value) temp2;            \
				const auto& item = object.get<MNJsonVal>("value");\
				ReflexFunc<Type>::CallbackUnserialize((void*)(&temp2), item);\
				temp.value = temp2;                     \
			}                                           \
			{                                           \
				decltype(value.inSlope) temp2;          \
				const auto& item = object.get<MNJsonVal>("inSlope");\
				ReflexFunc<Type>::CallbackUnserialize((void*)(&temp2), item);\
				temp.inSlope = temp2;                   \
			}                                           \
			{                                           \
				decltype(value.outSlope) temp2;         \
				const auto& item = object.get<MNJsonVal>("outSlope");\
				ReflexFunc<Type>::CallbackUnserialize((void*)(&temp2), item);\
				temp.outSlope = temp2;                  \
			}                                           \
			temp.tangentMode = static_cast<int>(object.get<MNJsonNumber>("tangentMode"));\
			temp.weightedMode = static_cast<int>(object.get<MNJsonNumber>("weightedMode"));\
			{                                           \
				decltype(value.inWeight) temp2;         \
				const auto& item = object.get<MNJsonVal>("inWeight");\
				ReflexFunc<Type>::CallbackUnserialize((void*)(&temp2), item);\
				temp.inWeight = temp2;                  \
			}                                           \
			{                                           \
				decltype(value.outWeight) temp2;         \
				const auto& item = object.get<MNJsonVal>("outWeight");\
				ReflexFunc<Type>::CallbackUnserialize((void*)(&temp2), item);\
				temp.outWeight = temp2;                  \
			}                                           \
			value = temp;                               \
		}                                               \
		return ret;                                     \
	}                                                   \
	template<>                                          \
	std::string ReflexPolicyToString<CurvePointTemplate<Type>>::CallbackToString(const void* data)\
	{                                                   \
		MNJsonVal value;                                \
		ReflexPolicySerialize<CurvePointTemplate<Type>>::CallbackSerialize(data, value);\
		return value.get<MNJsonObject>().json_nospace();\
	}                                                   \
	template<>                                          \
	size_t ReflexPolicySerialize<CurvePointTemplate<Type>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)\
	{                                                   \
		auto& value = Data(data);                       \
		size_t len = 0;                                 \
		len += out->WriteNumber<int>(value.ver);        \
		len += out->WriteNumber<float>(value.time);     \
		len += ReflexFunc<Type>::ReflexToBinary((const void*)(&value.value), out);\
		len += ReflexFunc<Type>::ReflexToBinary((const void*)(&value.inSlope), out);\
		len += ReflexFunc<Type>::ReflexToBinary((const void*)(&value.outSlope), out);\
		len += out->WriteNumber<int>(value.tangentMode);\
		len += out->WriteNumber<int>(value.weightedMode);\
		len += ReflexFunc<Type>::ReflexToBinary((const void*)(&value.inWeight), out);\
		len += ReflexFunc<Type>::ReflexToBinary((const void*)(&value.outWeight), out);\
		return len;                                     \
	}                                                   \
	template<>                                          \
	bool ReflexPolicySerialize<CurvePointTemplate<Type>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)\
	{                                                   \
		auto& value = Data(data);                       \
		auto ret = true;                                \
		ret &= in->ReadNumber<int>(value.ver);          \
		ret &= in->ReadNumber<float>(value.time);       \
		ret &= ReflexFunc<Type>::ReflexFromBinary((void*)(&value.value),in);\
		ret &= ReflexFunc<Type>::ReflexFromBinary((void*)(&value.inSlope),in);\
		ret &= ReflexFunc<Type>::ReflexFromBinary((void*)(&value.outSlope),in);\
		ret &= in->ReadNumber<int>(value.tangentMode);  \
		ret &= in->ReadNumber<int>(value.weightedMode); \
		ret &= ReflexFunc<Type>::ReflexFromBinary((void*)(&value.inWeight),in);\
		ret &= ReflexFunc<Type>::ReflexFromBinary((void*)(&value.outWeight),in);\
		return ret;\
	}\
	RegisterReflexTypePolicyClass(CurvePointTemplate<Type>, TEnum, ReflexPolicySerializeReg<CurvePointTemplate<Type>>) \
// end REG_REFLEXTYPEPOLICY_CURVE_POINT

    REG_REFLEXTYPEPOLICY_CURVE_POINT(float, REFLEXTYPEENUM_CURVE_POINT_FLOAT, ReflexPolicySerialize);
    REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Quaternionf, REFLEXTYPEENUM_CURVE_POINT_QUATERNION, ReflexPolicyFunc);
    REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Vector3f, REFLEXTYPEENUM_CURVE_POINT_VECTOR3F, ReflexPolicyFunc);
    REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Vector4f, REFLEXTYPEENUM_CURVE_POINT_VECTOR4F, ReflexPolicyFunc);

    // REG_REFLEXTYPEPOLICY_CURVE_POINT(float, REFLEXTYPEENUM_CURVE_POINT_FLOAT, ReflexPolicySerialize<Rainbow::Quaternionf>);
    // REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Quaternionf, REFLEXTYPEENUM_CURVE_POINT_QUATERNION, ReflexPolicyFunc<Rainbow::Quaternionf>);
    // REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Vector3f, REFLEXTYPEENUM_CURVE_POINT_VECTOR3F, ReflexPolicyFunc<Rainbow::Vector3f>);
    // REG_REFLEXTYPEPOLICY_CURVE_POINT(Rainbow::Vector4f, REFLEXTYPEENUM_CURVE_POINT_VECTOR4F, ReflexPolicyFunc<Rainbow::Vector4f>);

#define REG_REFLEXTYPEPOLICY_CURVE(Type, TEnum) \
    template<>\
	void ReflexPolicySerialize<CurveTemplate<Type>>::CallbackSerialize(const void* data, MNJsonVal& out)\
	{\
		auto& v = Data(data);\
		MNJsonObject object;\
		object << "ver" << (MNJsonNumber)(v.ver);\
		MNJsonArray points;\
		for (auto& one : v.m_Curve)\
		{\
			MNJsonVal itemValue;\
			ReflexPolicySerialize<CurvePointTemplate<Type>>::CallbackSerialize((const void*)(&one), itemValue);\
			points.import(itemValue);\
		}\
		object << "points" << points;\
		object << "preInfinity" << (MNJsonNumber)(v.m_PreInfinity);\
		object << "postInfinity" << (MNJsonNumber)(v.m_PostInfinity);\
		object << "rotationOrder" << (MNJsonNumber)(v.m_RotationOrder);\
		out.import(object);\
	}\
	template<> \
	bool ReflexPolicySerialize<CurveTemplate<Type>>::CallbackUnserialize(void* data, const MNJsonVal& in)\
	{\
		auto& value = Data(data);\
		const auto& object = in.get<MNJsonObject>();\
		auto ret = true;\
		ret &= object.has<MNJsonNumber>("ver");\
		std::remove_reference<decltype(value)>::type temp;\
		if (ret)\
		{\
			temp.ver = static_cast<int>(object.get<MNJsonNumber>("ver"));\
			const auto& points = object.get<MNJsonArray>("points");\
			for (int i = 0; i < points.size(); i++)\
			{\
				const auto& itemJson = points.get<MNJsonVal>(i);\
				decltype(value.m_Curve)::value_type itemValue;\
				ReflexPolicySerialize<CurvePointTemplate<Type>>::CallbackUnserialize((void*)(&itemValue), itemJson);\
				temp.m_Curve.emplace_back(itemValue);\
			}\
			temp.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(object.get<MNJsonNumber>("preInfinity"));\
			temp.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(object.get<MNJsonNumber>("postInfinity"));\
			temp.m_RotationOrder = static_cast<math::RotationOrder>(object.get<MNJsonNumber>("postInfinity"));\
			value = temp;\
		}\
		return ret;\
	}\
	template<> \
	std::string ReflexPolicyToString<CurveTemplate<Type>>::CallbackToString(const void* data)\
	{\
		MNJsonVal value;\
		ReflexPolicySerialize<CurveTemplate<Type>>::CallbackSerialize(data, value);\
		return value.get<MNJsonObject>().json_nospace();\
	}\
	template<> \
	size_t ReflexPolicySerialize<CurveTemplate<Type>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)\
	{\
		auto& value = Data(data); \
		size_t len = 0;\
		len += out->WriteNumber<int>(value.ver);\
		len += out->WriteNumber<int>(value.m_Curve.size());\
		for (auto& one : value.m_Curve)\
		{\
			len += ReflexPolicySerialize<CurvePointTemplate<Type>>::ReflexToBinary((const void*)(&one), out);\
		}\
		len += out->WriteNumber<int>(static_cast<int>(value.m_PreInfinity));\
		len += out->WriteNumber<int>(static_cast<int>(value.m_PostInfinity));\
		len += out->WriteNumber<int>(static_cast<int>(value.m_RotationOrder));\
		return len;\
	}\
	template<>\
	bool ReflexPolicySerialize<CurveTemplate<Type>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)\
	{\
		auto& value = Data(data);\
		auto ret = true;\
		ret &= in->ReadNumber<int>(value.ver);\
		int arraySize = 0;\
		ret &= in->ReadNumber<int>(arraySize);\
		for (int i = 0; i < arraySize; i++)\
		{\
			decltype(value.m_Curve)::value_type point;\
			ret &= ReflexPolicySerialize<CurvePointTemplate<Type>>::ReflexFromBinary((void*)(&point),in);\
			if (!ret)\
			{\
				return false;\
			}\
			value.m_Curve.emplace_back(point);\
		}\
		int p1, p2, p3;\
		ret &= in->ReadNumber<int>(p1);\
		ret &= in->ReadNumber<int>(p2);\
		ret &= in->ReadNumber<int>(p3);\
		value.m_PreInfinity = static_cast<Rainbow::InternalWrapMode>(p1);\
		value.m_PostInfinity = static_cast<Rainbow::InternalWrapMode>(p2);\
		value.m_RotationOrder = static_cast<math::RotationOrder>(p3);\
		return ret;\
	}\
    RegisterReflexTypePolicyClass(CurveTemplate<Type>, TEnum, ReflexPolicySerializeReg<CurveTemplate<Type>>) \
// end REG_REFLEXTYPEPOLICY_CURVE

	REG_REFLEXTYPEPOLICY_CURVE(float, REFLEXTYPEENUM_CURVE_FLOAT);
	REG_REFLEXTYPEPOLICY_CURVE(Rainbow::Quaternionf, REFLEXTYPEENUM_CURVE_QUATERNION);
	REG_REFLEXTYPEPOLICY_CURVE(Rainbow::Vector3f, REFLEXTYPEENUM_CURVE_VECTOR3F);
	REG_REFLEXTYPEPOLICY_CURVE(Rainbow::Vector4f, REFLEXTYPEENUM_CURVE_VECTOR4F);




	// RangeFloatCurve
	typedef MNSandbox::RangeFloatCurve rangeFloatCurve;
	template<>
	void ReflexPolicyFunc<rangeFloatCurve>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonArray curves;
		MNJsonVal itemValueMin, itemValueMax;
		ReflexPolicySerialize<CurveTemplate<float>>::CallbackSerialize((const void*)(&v._min), itemValueMin);
		ReflexPolicySerialize<CurveTemplate<float>>::CallbackSerialize((const void*)(&v._max), itemValueMax);
		curves << itemValueMin << itemValueMax;
		out.import(curves);
	}
	template<>
	bool ReflexPolicyFunc<rangeFloatCurve>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		if (!in.is<MNJsonArray>())
			return false;
		auto& jsona = in.get<MNJsonArray>();
		auto& v = Data(data);
		if (jsona.has<MNJsonVal>(0) && jsona.has<MNJsonVal>(1))
		{
			const auto& itemJsonMin = jsona.get<MNJsonVal>(0);
			const auto& itemJsonMax = jsona.get<MNJsonVal>(1);
			FloatCurve itemValueMin, itemValueMax;
			ReflexPolicySerialize<CurveTemplate<float>>::CallbackUnserialize((void*)(&itemValueMin), itemJsonMin);
			ReflexPolicySerialize<CurveTemplate<float>>::CallbackUnserialize((void*)(&itemValueMax), itemJsonMax);
			v.set(itemValueMin, itemValueMax);
			return true;
		}
		assert(false);
		return false;
	}
	template<>
	std::string ReflexPolicyFunc<rangeFloatCurve>::CallbackToString(const void* data)
	{
		return "";
	}
	template<>
	bool ReflexPolicyFunc<rangeFloatCurve>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		return false;
	}
	template<>
	int ReflexPolicyFunc<rangeFloatCurve>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		return 1;
	}
	template<>
	size_t ReflexPolicyFunc<rangeFloatCurve>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		AddRangeFloatCurve(v);
		static int count = 0;
		count++;
		if(count >= 1000)
		{
			PrintTop100RangeFloatCurve();
			count = 0;
		}
#endif
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		int specialType = isSpecialDefaultFloatCurve(v);
		if (specialType == 0)
		{
#endif
			len += ReflexPolicySerialize<CurveTemplate<float>>::ReflexToBinary((const void*)(&v._min), out);
			len += ReflexPolicySerialize<CurveTemplate<float>>::ReflexToBinary((const void*)(&v._max), out);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			len += out->WriteNumber<int>(static_cast<int>(specialType + 0xf));
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = specialType != 0 ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: RangeFloatCurve ReflexToBinary ", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return len;
	}
	template<>
	bool ReflexPolicyFunc<rangeFloatCurve>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		bool ret = true;

#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		int specialType = 0;
		auto currentOffset = in->GetOffset();
		ret &= in->ReadNumber<int>(specialType);
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		bool isSpecial = specialType > 0xF;
#endif
		if (specialType <= 0xf)
		{
			// fallback origin data
			specialType = 0;
			in->SetCurrentOffset(currentOffset);
#endif
			FloatCurve itemValueMin;
			ret &= ReflexPolicySerialize<CurveTemplate<float>>::ReflexFromBinary((void*)(&itemValueMin), in);
			if (!ret)
			{
				return false;
			}

			FloatCurve itemValueMax;
			ret &= ReflexPolicySerialize<CurveTemplate<float>>::ReflexFromBinary((void*)(&itemValueMax), in);
			if (!ret)
			{
				return false;
			}
			v.set(itemValueMin, itemValueMax);
#ifdef USE_SPECIAL_TYPE_REFLEX_VALUE
		}
		else
		{
			specialType -= 0xf;
			if (specialType == 1)
			{
				v = GetRangeFloatCurve1();
			}
			else if (specialType == 2)
			{
				v = GetRangeFloatCurve2();
			}
			else if (specialType == 3)
			{
				v = GetRangeFloatCurve3();
			}
			else if (specialType == 4)
			{
				v = GetRangeFloatCurve4();
			}
			else if (specialType == 5)
			{
				v = GetRangeFloatCurve5();
			}
			else if (specialType == 6)
			{
				v = GetRangeFloatCurve6();
			}
		}
#endif
#ifdef RECORD_EFFECTOBJECT_REFLEX_LOG
		static std::uint32_t notSpecialCount = 0;
		static std::uint32_t specialCount = 0;
		auto dummy = isSpecial ? specialCount++ : notSpecialCount++;
		SANDBOX_ERRORLOG("EffectObject: RangeFloatCurve ReflexFromBinary", " Special: ", specialCount, " Not Special: ", notSpecialCount);
#endif
		return ret;
	}

	RegisterReflexTypePolicyClass(rangeFloatCurve, REFLEXTYPEENUM_CURVE_FLOAT_RANGE, ReflexPolicyFunc<rangeFloatCurve>);
}

