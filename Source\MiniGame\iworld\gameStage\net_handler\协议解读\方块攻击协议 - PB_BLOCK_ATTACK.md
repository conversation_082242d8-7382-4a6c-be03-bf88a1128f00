# 方块攻击协议 - PB_BLOCK_ATTACK

## 协议概述

PB_BLOCK_ATTACK 协议是沙盒游戏中新的方块挖掘机制，用于替代原有的PB_BLOCK_PUNCH协议。该协议采用简化的单向通信模式，客户端发送攻击请求，服务器直接处理而不发送响应协议。

- **PB_BLOCK_ATTACK_CH**: 客户端发送给服务器
- **PB_BLOCK_ATTACK_HC**: **不存在** - 该协议没有对应的HC响应

## 协议定义

### PB_BlockAttackCH (客户端 → 服务器)

```protobuf
message PB_BlockAttackCH
{
    optional game.common.PB_Vector3 blockpos = 1;  // 目标方块位置
    optional int32 dgmethod = 2;                   // 挖掘方法
    optional uint32 clienttick = 3;                // 客户端时间戳
}
```

**协议定义位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:114-119`

**字段说明**:
- `blockpos`: 目标方块的世界坐标
- `dgmethod`: 挖掘方法，通常为DIG_METHOD_NORMAL
- `clienttick`: 客户端时间戳，用于反作弊验证

## 协议流程分析

### 1. 客户端发送协议

**触发位置**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:837-851`

```cpp
bool MpPlayerControl::attackBlock(const WCoord& blockpos, DIG_METHOD_T dgmethod, ATTACK_TYPE attacktype)
{
    if (m_pWorld && m_pWorld->isRemoteMode())
    {
        PB_BlockAttackCH blockAttackCH;
        PB_Vector3* blockPos = blockAttackCH.mutable_blockpos();
        blockPos->set_x(blockpos.x);
        blockPos->set_y(blockpos.y);
        blockPos->set_z(blockpos.z);
        blockAttackCH.set_dgmethod(dgmethod);

        GetGameNetManagerPtr()->sendToHost(PB_BLOCK_ATTACK_CH, blockAttackCH);
    }

    return PlayerControl::attackBlock(blockpos, dgmethod, attacktype);
}
```

**调用链**:
1. `AttackBlockState::update()` - 攻击状态更新
2. `AttackBlockState::attackBlock()` - 状态层攻击逻辑
3. `MpPlayerControl::attackBlock()` - 发送网络协议
4. `PlayerControl::attackBlock()` - 执行本地攻击逻辑

### 2. 服务器处理协议

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1553-1605`

```cpp
void MpGameSurviveNetHandler::handleAttackBlock2Host(int uin, const PB_PACKDATA& pkg)
{
    ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
    if (player == NULL) return;

    PB_BlockAttackCH CH;
    CH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    WCoord blockpos = MPVEC2WCoord(CH.blockpos());
    
    long long tick = CH.has_clienttick() ? CH.clienttick() : 0;

    //todo 反外挂 (当前被注释掉)
    if (player->GetCheatHandler())
    {
        // 反作弊代码被注释，暂未启用
    }

    // 获取AttackBlock状态并执行攻击
    auto pState = player->getActionStatePtr("AttackBlock");
    if (nullptr != pState)
    {
        int mineticks;
        int blockid = 0;
        auto pWorld = player->getWorld();
        blockid = pWorld->getBlockID(blockpos);
        if (blockid == 0)
        {
            // 记录位置，防止挖掘进度异常
            player->m_CurDigBlockPos = blockpos;
            return;
        }
        mineticks = player->getMineBlockTicks(player->getCurToolID(), blockid, 
                                            pWorld->getBlockData(blockpos), &player->m_MineType);
        player->attackBlock(blockpos, DIG_METHOD_NORMAL);
    }
}
```

**处理步骤**:
1. 验证玩家状态 (`checkDownedPlayerByMsg2Host`)
2. 解析协议数据
3. 坐标转换 (`MPVEC2WCoord`)
4. 反作弊检查（当前被注释）
5. 获取AttackBlock状态
6. 验证目标方块存在性
7. 计算挖掘时间
8. 执行攻击逻辑

### 3. 服务器响应机制

**重要发现**: PB_BLOCK_ATTACK协议**没有对应的HC响应协议**

与PB_BLOCK_PUNCH和PB_BLOCK_INTERACT不同，PB_BLOCK_ATTACK协议采用单向通信：
- ❌ 没有`PB_BlockAttackHC`协议定义
- ❌ 没有`handleAttackBlock2Client`处理函数  
- ❌ 没有`notifyAttackBlock2Tracking`广播函数
- ❌ 没有客户端处理器注册

这意味着：
1. 服务器处理攻击后不会向其他客户端广播
2. 其他玩家无法看到当前玩家的攻击动作
3. 攻击效果仅在本地和服务器端生效

## 协议注册

**注册位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp:168`

```cpp
// 服务器端处理器注册
REGIS_HOST_HANDLER(PB_BLOCK_ATTACK_CH, handleAttackBlock2Host);

// 客户端处理器注册 - 不存在
// REGIS_CLIENT_HANDLER(PB_BLOCK_ATTACK_HC, handleAttackBlock2Client); // 无此注册
```

## 与其他协议的对比

| 协议 | CH协议 | HC协议 | 广播函数 | 状态同步 |
|------|--------|--------|----------|----------|
| PB_BLOCK_PUNCH | ✅ | ✅ | notifyPunchBlock2Tracking | 完整 |
| PB_BLOCK_INTERACT | ✅ | ✅ | notifyInteractBlock2Tracking | 完整 |
| PB_BLOCK_EXPLOIT | ✅ | ✅ | notifyExploitBlock2Tracking (已注释) | 部分 |
| **PB_BLOCK_ATTACK** | ✅ | ❌ | ❌ | **无** |

## 协议特点

### 优点
1. **简化通信**: 减少网络流量，提高性能
2. **降低延迟**: 无需等待服务器响应
3. **减少复杂性**: 避免状态同步问题

### 缺点
1. **缺乏同步**: 其他玩家无法看到攻击动作
2. **调试困难**: 无法追踪攻击状态传播
3. **一致性风险**: 客户端和服务器状态可能不同步

## 潜在问题

1. **多人游戏体验**: 其他玩家看不到当前玩家的挖掘动作
2. **反作弊机制**: 当前反作弊代码被注释，存在安全隐患
3. **状态管理**: 缺乏状态同步可能导致游戏状态不一致

## 建议改进

1. **添加HC协议**: 实现PB_BlockAttackHC用于状态同步
2. **启用反作弊**: 取消注释反作弊检查代码
3. **添加广播机制**: 实现notifyAttackBlock2Tracking函数

## 相关协议

- **PB_BLOCK_PUNCH**: 传统挖掘协议（已被替代）
- **PB_BLOCK_INTERACT**: 方块放置协议
- **PB_BLOCK_EXPLOIT**: 特殊开采协议