#pragma once
#include "Input/OgreInputManager.h"
#include "CameraInfo.h"
#include "TPSCamera.h"

class IPlayerControl;

class RecordEditCamera :public TPSCamera //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	RecordEditCamera(CameraManager* cameraManager);
	~RecordEditCamera();

	void update(float deltaSeconds) override;
	int onInputEvent(const Rainbow::InputEvent &event) override;
	void onSwitchTo();
	//tolua_end
private:
	int onPcInputEvent(const Rainbow::InputEvent &event);
	int onTouchInputEvent(const Rainbow::InputEvent &event);

	bool m_IsInit;
	//MINIW::Point2D m_PreMousePos;
	MINIW::Point2D m_RotateStart;
	int m_RotateID;

public:
	//tolua_begin
	float m_MoveUp;

	float m_PreRotateY;
	float m_PreRotateX;
	//tolua_end
}; //tolua_exports