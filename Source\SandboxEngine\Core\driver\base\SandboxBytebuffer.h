#pragma once
#include "SandboxMacros.h"


namespace MNSandbox {

	class EXPORT_SANDBOXDRIVERMODULE ByteBuffer
	{
	public:
		ByteBuffer(int defaultsize = 16);
		~ByteBuffer();

		char* GetBuffer(int offset = 0) const {
			return (char*)(m_buffer + offset);
		}

		char* GetCurReadBuffer() const {
			return GetBuffer(m_read_pos);
		}
		char* GetCurWriteBuffer(int offset = 0) const {
			return GetBuffer(m_write_pos);
		}

		int GetAvailableReadBytes() const { return m_write_pos - m_read_pos; }
		int GetAvailableWriteBytes() const { return m_size - m_write_pos; }
		bool SeekRead(int pos);
		bool SeekWrite(int pos);
		int GetSize() const { return m_size; }

		bool ReadChars(char* str, int len);
		bool ReadChar(char& c);
		bool ReadShort(short& s);
		bool ReadInt(int& i);
		bool WriteStringStream(std::stringstream& stream, int len);

		bool WriteShort(short s);
		bool WriteInt(int i);
		bool WriteChar(char c);
		bool WriteChars(const char* str, int len);

		void Reset();
		bool Resize(int s);
	private:
		bool reallocate();

		char* m_buffer;
		int    m_read_pos;
		int    m_write_pos;
		int    m_size;

	};
}// namespace MNSandbox
