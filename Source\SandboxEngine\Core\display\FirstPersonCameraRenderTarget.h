//#pragma once
//#include "world_types.h"
//#include "Utilities/ConcurrentFreeList.h"
//#include "SandboxEngine.h"
//#include "UICommon/RenderTextureHolder.h"
//#include "BaseClass/PPtr.h"
//#include "Render/CustomRendererList.h"
//namespace Rainbow
//{
//	class EXPORT_SANDBOXENGINE FirstPersonCameraRenderTarget
//	{
//	public:
//
//		FirstPersonCameraRenderTarget();
//		~FirstPersonCameraRenderTarget();
//
//		void OnLeaveWorld();
//		void InitCameraProperty(Camera* camera);
//	private:
//		PPtr<Camera> m_Camera;
//
//	private:
//	};
//	EXPORT_SANDBOXENGINE FirstPersonCameraRenderTarget& GetFirstPersonCameraRenderTarget();
//}