/*
*	file: SceneEffectBox
*	func: 在世界场景中绘制盒
*	by: chenzh
*	time: 2022.8.1
*/
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectBox.h"
#include "SceneEffectLine.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "BlockMeshVert.h"

SceneEffectBox::SceneEffectBox()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE;
	m_eSes = SceneEffectShape::BOX;
}

SceneEffectBox::SceneEffectBox(const WCoord startpos, const WCoord endpos, 
	CURVEFACEMTLTYPE mtltype, CurveFace* curveFaces)
{
	m_MtlType = mtltype;
	m_eSes = SceneEffectShape::BOX;
	
	m_StartPos.x = Rainbow::Min(startpos.x, endpos.x);
	m_StartPos.y = Rainbow::Min(startpos.y, endpos.y);
	m_StartPos.z = Rainbow::Min(startpos.z, endpos.z);
	m_EndPos.x = Rainbow::Max(startpos.x, endpos.x);
	m_EndPos.y = Rainbow::Max(startpos.y, endpos.y);
	m_EndPos.z = Rainbow::Max(startpos.z, endpos.z);
	m_StartPosBlock = m_StartPos / BLOCK_SIZE;
	m_EndPosBlock = m_EndPos / BLOCK_SIZE;

	SetCurveFaces(curveFaces);
}

SceneEffectBox::~SceneEffectBox()
{
	m_Vertices1.clear();
	m_Vertices2.clear();
	m_Vertices3.clear();
	m_Vertices4.clear();
	m_Vertices5.clear();
	m_Vertices6.clear();
	m_Indices.clear();
}

void SceneEffectBox::OnClear()
{
	m_Vertices1.clear();
	m_Vertices2.clear();
	m_Vertices3.clear();
	m_Vertices4.clear();
	m_Vertices5.clear();
	m_Vertices6.clear();
	m_Indices.clear();
}

void SceneEffectBox::Refresh()
{
	if (m_EndPos == m_StartPos)
		return;

	Rainbow::Vector3f dim = (m_EndPos - m_StartPos).toVector3();
	CalcVertexsAndIndices(dim);

	m_drawpos = m_StartPos;
}

void SceneEffectBox::OnDraw(World* pWorld)
{
	if (m_Vertices1.empty())
		return;

	auto CurveRender = m_CurveFaces ? m_CurveFaces : pWorld->getRender()->getCurveRender();
	if (CurveRender)
	{
		CURVEFACEMTLTYPE mtltype = m_Translucent ? CURVEFACEMTL_TEXWHITE : m_MtlType; // 利用纹理空作为半透效果
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices1, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices2, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices3, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices4, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices5, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices6, m_Indices);
	}
}

bool SceneEffectBox::IsActive(World* pWorld) const
{
	return (m_StartPosBlock.y >= 0 && m_EndPosBlock.y >= 0) && (pWorld->getChunk(m_StartPosBlock) || pWorld->getChunk(m_EndPosBlock));
}

void SceneEffectBox::CalcVertexsAndIndices(const Rainbow::Vector3f dim)
{
	OnClear(); // 清理缓存
	
	Rainbow::Vector3f startpos(0, 0, 0);
	if (dim.x < FLT_EPSILON || dim.y < FLT_EPSILON || dim.z < FLT_EPSILON)
	{
		return;
	}

	Rainbow::Vector3f vertex[8];
	vertex[0] = startpos;
	vertex[1] = startpos + Rainbow::Vector3f(dim.x, 0, 0);
	vertex[2] = startpos + Rainbow::Vector3f(0, dim.y, 0);
	vertex[3] = startpos + Rainbow::Vector3f(dim.x, dim.y, 0);
	vertex[4] = startpos + Rainbow::Vector3f(0, 0, dim.z);
	vertex[5] = startpos + Rainbow::Vector3f(dim.x, 0, dim.z);
	vertex[6] = startpos + Rainbow::Vector3f(0, dim.y, dim.z);
	vertex[7] = startpos + Rainbow::Vector3f(dim.x, dim.y, dim.z);

	float u[3][2], v[3][2];
	u[0][0] = 0.0f; u[0][1] = dim.z;
	v[0][0] = 0.0f; v[0][1] = dim.x;
	u[1][0] = 0.0f; u[1][1] = dim.z;
	v[1][0] = 0.0f; v[1][1] = dim.y;
	u[2][0] = 0.0f; u[2][1] = dim.x;
	v[2][0] = 0.0f; v[2][1] = dim.y;

	// CURVEFACEMTL_RAILHINT 和 CURVEFACEMTL_RAILRED 两边有透明像素，特殊处理
	switch (m_MtlType)
	{
		case CURVEFACEMTL_TEXWHITE:
			u[0][0] = u[1][0] = u[2][0] = 0.49f;
			u[0][1] = u[1][1] = u[2][1] = 0.51f;
			break;
		case CURVEFACEMTL_RAILHINT:
		case CURVEFACEMTL_RAILRED:
		case CURVEFACEMTL_LIGHTNING1:
			u[0][0] = u[1][0] = u[2][0] = 0.3f;
			u[0][1] = u[1][1] = u[2][1] = 0.7f;
			break;
	}

	m_Indices.push_back(0);
	m_Indices.push_back(2);
	m_Indices.push_back(1);
	m_Indices.push_back(1);
	m_Indices.push_back(2);
	m_Indices.push_back(3);

	// 利用纹理为空，作为半透效果显示
	if (m_Translucent)
	{
		u[0][0] = 0.0f; u[0][1] = 0.0f;
		v[0][0] = 0.0f; v[0][1] = 0.0f;
		u[1][0] = 0.0f; u[1][1] = 0.0f;
		v[1][0] = 0.0f; v[1][1] = 0.0f;
		u[2][0] = 0.0f; u[2][1] = 0.0f;
		v[2][0] = 0.0f; v[2][1] = 0.0f;
	}

	m_Vertices1.resize(4);
	m_Vertices2.resize(4);
	m_Vertices3.resize(4);
	m_Vertices4.resize(4);
	m_Vertices5.resize(4);
	m_Vertices6.resize(4);

	BlockVector left{ m_Color }, right{ m_Color }, font{ m_Color }, back{ m_Color }, bottom{ m_Color }, up{ m_Color };

	switch (m_face)
	{
	case DIR_NEG_X:
		left = m_faceColor;
		break;
	case DIR_POS_X:
		right = m_faceColor;
		break;
	case DIR_NEG_Z:
		font = m_faceColor;
		break;
	case DIR_POS_Z:
		back = m_faceColor;
		break;
	case DIR_NEG_Y:
		bottom = m_faceColor;
		break;
	case DIR_POS_Y:
		up = m_faceColor;
		break;
	}

	SceneEffectLine::FillVertBuffer(m_Vertices1[0], vertex[0], u[0][0], v[0][0], bottom);
	SceneEffectLine::FillVertBuffer(m_Vertices1[1], vertex[4], u[0][1], v[0][0], bottom);
	SceneEffectLine::FillVertBuffer(m_Vertices1[2], vertex[1], u[0][0], v[0][1], bottom);
	SceneEffectLine::FillVertBuffer(m_Vertices1[3], vertex[5], u[0][1], v[0][1], bottom);

	SceneEffectLine::FillVertBuffer(m_Vertices2[0], vertex[1], u[1][0], v[1][0], right);
	SceneEffectLine::FillVertBuffer(m_Vertices2[1], vertex[5], u[1][1], v[1][0], right);
	SceneEffectLine::FillVertBuffer(m_Vertices2[2], vertex[3], u[1][0], v[1][1], right);
	SceneEffectLine::FillVertBuffer(m_Vertices2[3], vertex[7], u[1][1], v[1][1], right);

	SceneEffectLine::FillVertBuffer(m_Vertices3[0], vertex[3], u[0][0], v[0][0], up);
	SceneEffectLine::FillVertBuffer(m_Vertices3[1], vertex[7], u[0][1], v[0][0], up);
	SceneEffectLine::FillVertBuffer(m_Vertices3[2], vertex[2], u[0][0], v[0][1], up);
	SceneEffectLine::FillVertBuffer(m_Vertices3[3], vertex[6], u[0][1], v[0][1], up);

	SceneEffectLine::FillVertBuffer(m_Vertices4[0], vertex[2], u[1][0], v[1][0], left);
	SceneEffectLine::FillVertBuffer(m_Vertices4[1], vertex[6], u[1][1], v[1][0], left);
	SceneEffectLine::FillVertBuffer(m_Vertices4[2], vertex[0], u[1][0], v[1][1], left);
	SceneEffectLine::FillVertBuffer(m_Vertices4[3], vertex[4], u[1][1], v[1][1], left);

	SceneEffectLine::FillVertBuffer(m_Vertices5[0], vertex[3], u[2][0], v[2][0], font);
	SceneEffectLine::FillVertBuffer(m_Vertices5[1], vertex[2], u[2][1], v[2][0], font);
	SceneEffectLine::FillVertBuffer(m_Vertices5[2], vertex[1], u[2][0], v[2][1], font);
	SceneEffectLine::FillVertBuffer(m_Vertices5[3], vertex[0], u[2][1], v[2][1], font);

	SceneEffectLine::FillVertBuffer(m_Vertices6[0], vertex[6], u[2][0], v[2][0], back);
	SceneEffectLine::FillVertBuffer(m_Vertices6[1], vertex[7], u[2][1], v[2][0], back);
	SceneEffectLine::FillVertBuffer(m_Vertices6[2], vertex[4], u[2][0], v[2][1], back);
	SceneEffectLine::FillVertBuffer(m_Vertices6[3], vertex[5], u[2][1], v[2][1], back);
}