
#include "CustomModelPackingSelectArea.h"
#include "IPlayerControl.h"
#include "BlockScene.h"
#include "WorldRender.h"
#include "MechaMesh.h"
#include "OgreScriptLuaVM.h"
#include "OgreUtils.h"
#include "section.h"
#include "IActorLocoMotion.h"

#include "world.h"

using namespace MINIW;
using namespace Rainbow;

CustomModelPackingSelectArea::CustomModelPackingSelectArea()
{
	init();
}

CustomModelPackingSelectArea::~CustomModelPackingSelectArea()
{
	clearPreBlocksMesh();
}

bool CustomModelPackingSelectArea::isValidPos(WCoord &pos)
{
	return pos.y >= 0;
}

void CustomModelPackingSelectArea::clearPreBlocksMesh()
{
	for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_SectionMeshs[i]);
	}
	m_SectionMeshs.clear();
}

void CustomModelPackingSelectArea::setOperateDistance(int distance)
{
	m_iOperateDistance = distance;
}

void CustomModelPackingSelectArea::setOperateMode(PACKING_CM_MODE mode)
{
	if (m_iMode == mode)
		return;

	m_iMode = mode;
	if (m_iMode == PACKING_CM_MODE_BLOCK)
		m_iAreaLimitLength = m_iAreaLimitHeight = m_iAreaLimitWidth = 100-1;
	else if(m_iMode == PACKING_CM_MODE_CM)
		m_iAreaLimitLength = m_iAreaLimitHeight = m_iAreaLimitWidth = 5-1;

	m_StartPos = m_EndPos = m_OperatePos = WCoord(0, -1, 0);
	m_bIsExceedLimit = false;

	clearPreBlocksMesh();
}

PACKING_CM_MODE CustomModelPackingSelectArea::getOperateMode()
{
	return m_iMode;
}

void CustomModelPackingSelectArea::getAreaSize(WCoord &starpos, WCoord &endpos)
{
	starpos = m_StartPos;
	endpos = m_EndPos;
}

void CustomModelPackingSelectArea::tick()
{
	checkAreaBoxChange();
}

bool CustomModelPackingSelectArea::confirmArea()
{
	if (!isValidPos(m_StartPos))
	{
		m_StartPos = getCurOperatePos();
		if(isValidPos(m_StartPos) && MINIW::ScriptVM::game())
			MINIW::ScriptVM::game()->callFunction("PackingConfirmStartPosTips", "i", m_iMode);
	}
	else if (!isValidPos(m_EndPos))
	{
		m_EndPos = getCurOperatePos();
		if (m_bIsExceedLimit)
		{
			int xLeng = abs(m_EndPos.x - m_StartPos.x);
			int yLeng = abs(m_EndPos.y - m_StartPos.y);
			int zLeng = abs(m_EndPos.z - m_StartPos.z);

			if (xLeng > m_iAreaLimitLength - 1) {
				xLeng = m_iAreaLimitLength;
				if (m_EndPos.x > m_StartPos.x)
					m_EndPos.x = m_StartPos.x + m_iAreaLimitLength;
				else
					m_EndPos.x = m_StartPos.x - m_iAreaLimitLength;
			}
			if (yLeng > m_iAreaLimitHeight - 1) {
				yLeng = m_iAreaLimitHeight;
				if (m_EndPos.y > m_StartPos.y)
					m_EndPos.y = m_StartPos.y + m_iAreaLimitHeight;
				else
					m_EndPos.y = m_StartPos.y - m_iAreaLimitHeight;
			}
			if (zLeng > m_iAreaLimitWidth - 1) {
				zLeng = m_iAreaLimitWidth;
				if (m_EndPos.z > m_StartPos.z)
					m_EndPos.z = m_StartPos.z + m_iAreaLimitWidth;
				else
					m_EndPos.z = m_StartPos.z - m_iAreaLimitWidth;
			}
			//GetGameEventQue().postInfoTips(4881);
		}

		updateAreaBox(m_EndPos);
		return true;
	}	

	return false;
}

void CustomModelPackingSelectArea::init()
{
	m_iOperateDistance = 5;
	m_StartPos = m_EndPos = m_OperatePos = WCoord(0, -1, 0);
	m_iAreaLimitLength = m_iAreaLimitWidth = m_iAreaLimitHeight = 100-1;
	m_bIsExceedLimit = false;
	m_iMode = PACKING_CM_MODE_BLOCK;
}

WCoord CustomModelPackingSelectArea::getCurOperatePos()
{
	if (!GetIPlayerControl())
		return WCoord(0, -1, 0);

	WCoord pos = Rainbow::Vector3f(0, 0, 0);
	bool isGet = GetIPlayerControl()->GetPlayerControlAimPos(pos.x, pos.y, pos.z, m_iOperateDistance);
	Rainbow::Vector3f dir  = Rainbow::Normalize(GetIPlayerControl()->getLookDir());
	if (isGet)
	{
		dir.x = (dir.x == 0.0f) ? 0.0f : (dir.x > 0.0f ? 1.0f : -1.0f);
		dir.y = (dir.y == 0.0f) ? 0.0f : (dir.y > 0.0f ? 1.0f : -1.0f);
		dir.z = (dir.z == 0.0f) ? 0.0f : (dir.z > 0.0f ? 1.0f : -1.0f);
		pos += dir;
		return CoordDivBlock(pos);
	}

	return WCoord(0, -1, 0);
}

void CustomModelPackingSelectArea::checkAreaBoxChange()
{
	if (!GetIPlayerControl())
		return;

	if (!isValidPos(m_EndPos)) //区域未确定
	{
		WCoord curOperatePos = getCurOperatePos();
		if (curOperatePos == m_OperatePos)  //操作点发生了变化
			return;

		updateAreaBox(curOperatePos);
		m_OperatePos = curOperatePos;
	}
	
}

void CustomModelPackingSelectArea::updateAreaBox(WCoord curoperatepos)
{
	WCoord pos = WCoord(0, 0, 0);

	World *pWorld = GetIPlayerControl()->getIWorld();
	if (!pWorld)
		return;

	std::map<WCoord, std::vector<WCoord>> sectionPreBlocks;

	if (!isValidPos(m_StartPos)) //起点未确定 
	{		
		WCoord ogigin = BlockDivSection(curoperatepos)*CHUNK_BLOCK_X;
		sectionPreBlocks[ogigin].push_back(curoperatepos);
	}
	else {
		WCoord endPos = curoperatepos;
		if (isValidPos(m_EndPos)) {  //区域确定了
			endPos = m_EndPos;
		}

		int xLeng = abs(endPos.x - m_StartPos.x);
		int yLeng = abs(endPos.y - m_StartPos.y);
		int zLeng = abs(endPos.z - m_StartPos.z);

		m_bIsExceedLimit = false;
		if (xLeng > m_iAreaLimitLength - 1) {
			m_bIsExceedLimit = true;
			xLeng = m_iAreaLimitLength;
			if (endPos.x > m_StartPos.x)
				endPos.x = m_StartPos.x + m_iAreaLimitLength;
			else
				endPos.x = m_StartPos.x - m_iAreaLimitLength;
		}
		if (yLeng > m_iAreaLimitHeight - 1) {
			m_bIsExceedLimit = true;
			yLeng = m_iAreaLimitHeight;
			if (endPos.y > m_StartPos.y)
				endPos.y = m_StartPos.y + m_iAreaLimitHeight;
			else
				endPos.y = m_StartPos.y - m_iAreaLimitHeight;
		}
		if (zLeng > m_iAreaLimitWidth - 1) {
			m_bIsExceedLimit = true;
			zLeng = m_iAreaLimitWidth;
			if (endPos.z > m_StartPos.z)
				endPos.z = m_StartPos.z + m_iAreaLimitWidth;
			else
				endPos.z = m_StartPos.z - m_iAreaLimitWidth;
		}

		for (int x = 0; x <= xLeng; x++)
		{
			for (int y = 0; y <= yLeng; y++)
			{
				for (int z = 0; z <= zLeng; z++)
				{
					int pos_x = x, pos_y = y, pos_z = z;
					if (endPos.x < m_StartPos.x) {
						pos_x = -x;
					}
					if (endPos.y < m_StartPos.y) {
						pos_y = -y;
					}
					if (endPos.z < m_StartPos.z) {
						pos_z = -z;
					}
					WCoord pos = WCoord(m_StartPos.x + pos_x, m_StartPos.y + pos_y, m_StartPos.z + pos_z);
					WCoord ogigin = BlockDivSection(pos)*CHUNK_BLOCK_X;
					/*Chunk *chunk = pWorld->getChunk(pos);
					if (!chunk)
						pWorld->syncLoadChunk(pos, 1);

					Section *section = pWorld->getSection(pos);
					if (section)
					{*/
						if ((m_StartPos.x == pos.x && m_StartPos.y == pos.y) ||
							(m_StartPos.x == pos.x && m_StartPos.z == pos.z) ||
							(m_StartPos.y == pos.y && m_StartPos.z == pos.z) ||
							(endPos.x == pos.x && endPos.y == pos.y) ||
							(endPos.x == pos.x && endPos.z == pos.z) ||
							(endPos.y == pos.y && endPos.z == pos.z) ||
							(m_StartPos.y == pos.y && endPos.z == pos.z) ||
							(m_StartPos.y == pos.y && endPos.x == pos.x) ||
							(endPos.y == pos.y && m_StartPos.z == pos.z) ||
							(endPos.y == pos.y && m_StartPos.x == pos.x) ||
							(endPos.z == pos.z && m_StartPos.x == pos.x) ||
							(endPos.x == pos.x && m_StartPos.z == pos.z)) {

								std::map<WCoord, std::vector<WCoord>>::iterator iter = sectionPreBlocks.find(ogigin);
								if (sectionPreBlocks.end() != iter)
									iter->second.push_back(pos);
								else
									sectionPreBlocks[ogigin].push_back(pos);
						}
					/*}
					else {
						LOG_INFO("CustomModelPackingSelectArea section == NULL");
					}*/
				}

			}
		}
	}

	createAreaBoxMesh(sectionPreBlocks);
}

void CustomModelPackingSelectArea::createAreaBoxMesh(std::map<WCoord, std::vector<WCoord>> &sectionpreblocks)
{
	World *pWorld = GetIPlayerControl()->getIWorld();
	if (!pWorld)
		return;

	clearPreBlocksMesh();

	std::map<WCoord, std::vector<WCoord>>::iterator iter = sectionpreblocks.begin();
	for (; iter != sectionpreblocks.end(); ++iter)
	{
		Chunk *chunk = pWorld->getChunk(iter->second[0]);
		if (!chunk)
			pWorld->syncLoadChunk(iter->second[0], 1);

		Section *section = ENG_NEW(Section)(chunk, 0);
		section->initialize();
		section->m_Origin = iter->first;
		section->allocBlocks();
		for (int i = 0; i < (int)iter->second.size(); i++)
		{
			WCoord pos = iter->second[i] - section->m_Origin;
			section->setBlock(pos.x, pos.y, pos.z, 103, 0);
		}

		FixedString filename = FixedString("blocks/map_edit_pre_mesh.png");
		if (m_bIsExceedLimit) filename = FixedString("blocks/map_edit_pre_mesh2.png");

		MechaMeshObject*mesh = section->createMapEditPreMesh(filename);

		if (mesh) {
			m_SectionMeshs.push_back(mesh);
		}
		section->destroy();
		ENG_DELETE(section);
	}

	if (pWorld && pWorld->getRenderer())
	{
		BlockScene *scene = pWorld->getRenderer()->getScene();
		if (scene)
		{
			for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
			{
				m_SectionMeshs[i]->AttachToScene(scene);
			}
		}
	}
}