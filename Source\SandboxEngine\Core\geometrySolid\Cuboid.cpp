#include "Cuboid.h"

#include "scene/SandboxSceneObject.h"
#include "geometrySolid/CompositeGeoSolid.h"

using namespace Rainbow;

namespace MNSandbox {
	namespace GeometrySolid {
		IMPLEMENT_GET_SINGLETON(Cuboid)
		Cuboid::Cuboid()
		{
			m_eGss = GeoSolidShape::CUBOID;
			if (!m_daVertices.empty())
			{
				return;
			}
			//WarningStringMsg("sizeof(GeoSolidInteractState) = %d", sizeof(GeoSolidInteractState));
			//WarningStringMsg("sizeof(GeoSolidVertex) = %d", sizeof(GeoSolidVertex));
			//WarningStringMsg("sizeof(GeoSolidEdge) = %d", sizeof(GeoSolidEdge));
			//WarningStringMsg("sizeof(GeoSolidTriangle) = %d", sizeof(GeoSolidTriangle));
			//WarningStringMsg("sizeof(GeoSolid) = %d", sizeof(GeoSolid));
			//WarningStringMsg("sizeof(CompositeGeoSolid) = %d", sizeof(CompositeGeoSolid));
			GsVector3 v3Extent(0.5, 0.5, 0.5);
			v3Extent *= BASIC_GEO_SOLID_SCALE;
			GsVector3 v3Center(0, 0, 0);
			m_daVertices.resize(8);
			m_daVertices[0] = v3Center + GsVector3(-v3Extent.x, -v3Extent.y, -v3Extent.z);
			m_daVertices[1] = v3Center + GsVector3(v3Extent.x, -v3Extent.y, -v3Extent.z);
			m_daVertices[2] = v3Center + GsVector3(v3Extent.x, -v3Extent.y, v3Extent.z);
			m_daVertices[3] = v3Center + GsVector3(-v3Extent.x, -v3Extent.y, v3Extent.z);
			m_daVertices[4] = v3Center + GsVector3(-v3Extent.x, v3Extent.y, -v3Extent.z);
			m_daVertices[5] = v3Center + GsVector3(v3Extent.x, v3Extent.y, -v3Extent.z);
			m_daVertices[6] = v3Center + GsVector3(v3Extent.x, v3Extent.y, v3Extent.z);
			m_daVertices[7] = v3Center + GsVector3(-v3Extent.x, v3Extent.y, v3Extent.z);
			InitGeoSolidMeshData();
		}


		Cuboid::~Cuboid()
		{
		}

		void Cuboid::InitGeoSolidMeshData()
		{
			if (!m_daTriangles.empty())
			{
				return;
			}
			//左下、左上、右上、右下
			m_daUvs.emplace_back(GsVector2::zero);
			m_daUvs.emplace_back(GsVector2(0, 1));
			m_daUvs.emplace_back(GsVector2::one);
			m_daUvs.emplace_back(GsVector2(1, 0));
			const UInt32 iuvbl = 0;
			const UInt32 iuvtl = 1;
			const UInt32 iuvtr = 2;
			const UInt32 iuvbr = 3;
			const UInt8 x = 0;
			const UInt8 y = 1;
			const UInt8 z = 2;
			//顺时针
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(0, 4, 5, iuvbl, iuvtl, iuvtr, x, y, m_eGss, GeoSolidFace::FRONT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(0, 5, 1, iuvbl, iuvtr, iuvbr, x, y, m_eGss, GeoSolidFace::FRONT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(2, 6, 7, iuvbl, iuvtl, iuvtr, x, y, m_eGss, GeoSolidFace::BACK));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(2, 7, 3, iuvbl, iuvtr, iuvbr, x, y, m_eGss, GeoSolidFace::BACK));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(3, 7, 4, iuvbl, iuvtl, iuvtr, z, y, m_eGss, GeoSolidFace::LEFT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(3, 4, 0, iuvbl, iuvtr, iuvbr, z, y, m_eGss, GeoSolidFace::LEFT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(1, 5, 6, iuvbl, iuvtl, iuvtr, z, y, m_eGss, GeoSolidFace::RIGHT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(1, 6, 2, iuvbl, iuvtr, iuvbr, z, y, m_eGss, GeoSolidFace::RIGHT));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(4, 7, 6, iuvbl, iuvtl, iuvtr, x, z, m_eGss, GeoSolidFace::TOP));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(4, 6, 5, iuvbl, iuvtr, iuvbr, x, z, m_eGss, GeoSolidFace::TOP));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(3, 0, 1, iuvbl, iuvtl, iuvtr, x, z, m_eGss, GeoSolidFace::BOTTOM));
			m_daTriangles.emplace_back(GeoSolid::obtain<GeoSolidTriangle>(3, 1, 2, iuvbl, iuvtr, iuvbr, x, z, m_eGss, GeoSolidFace::BOTTOM));
			UpdateVerticesEdgesTriangles(m_daGeoSolidVertices, m_daTriangles, m_daEdges, m_daVertices);
			UpdateBound(m_daGeoSolidVertices, m_bb);
			m_daBoxBounds.emplace_back(m_bb);
			CreateDynamic();
			SeparateSurfaces();
		}

		void Cuboid::CreateDynamic()
		{
			if (!m_daPrimitiveVertices.empty() || !m_daPrimitiveIndices.empty())
			{
				return;
			}
			GeoSolidArray<PrimitiveVertex>& daPvs = m_daPrimitiveVertices;
			GeoSolidArray<UInt32>& daPis = m_daPrimitiveIndices;
			const GeoSolidArray<GsVector3>& daVertices = m_daVertices;
			const ColorRGBA32 colorVertex = ColorRGBA32::white;
			//TODO: 2023-08-18 09:55:50: 从InitGeoSolidMeshData中统一解析得来。其它类型同。
			const PrimitiveVertex aPvs[] =
			{
				{daVertices[0], GsVector3::neg_zAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[4], GsVector3::neg_zAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[5], GsVector3::neg_zAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[1], GsVector3::neg_zAxis, colorVertex, GsVector2(1, 0)},

				{daVertices[2], GsVector3::zAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[6], GsVector3::zAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[7], GsVector3::zAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[3], GsVector3::zAxis, colorVertex, GsVector2(1, 0)},

				{daVertices[3], GsVector3::neg_xAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[7], GsVector3::neg_xAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[4], GsVector3::neg_xAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[0], GsVector3::neg_xAxis, colorVertex, GsVector2(1, 0)},

				{daVertices[1], GsVector3::xAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[5], GsVector3::xAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[6], GsVector3::xAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[2], GsVector3::xAxis, colorVertex, GsVector2(1, 0)},

				{daVertices[4], GsVector3::yAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[7], GsVector3::yAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[6], GsVector3::yAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[5], GsVector3::yAxis, colorVertex, GsVector2(1, 0)},

				{daVertices[3], GsVector3::neg_yAxis, colorVertex, GsVector2(0, 0)},
				{daVertices[0], GsVector3::neg_yAxis, colorVertex, GsVector2(0, 1)},
				{daVertices[1], GsVector3::neg_yAxis, colorVertex, GsVector2(1, 1)},
				{daVertices[2], GsVector3::neg_yAxis, colorVertex, GsVector2(1, 0)},
			};
			const UInt32 aIndices[] =
			{
				0, 1, 2, 0, 2, 3,
				4, 5, 6, 4, 6, 7,
				8, 9, 10, 8, 10, 11,
				12, 13, 14, 12, 14, 15,
				16, 17, 18, 16, 18, 19,
				20, 21, 22, 20, 22, 23
			};
			const UInt32 cpv = sizeof(aPvs) / sizeof(PrimitiveVertex);
			const UInt32 cpi = sizeof(aIndices) / sizeof(UInt32);
			daPvs.reserve(cpv);
			daPvs.assign(aPvs, aPvs + cpv);
			daPis.reserve(cpi);
			daPis.assign(aIndices, aIndices + cpi);
		}

		void Cuboid::SeparateSurfaces()
		{
			if (!m_daMeshes.empty())
			{
				return;
			}
			const int csf = GetSurfaceCount();
			m_daMeshes.reserve(csf);
			if (!m_mesh)
			{
				return;
			}
			GeoSolidArray<PrimitiveVertex> daPvs = GetPvs(m_mesh, m_daPrimitiveVertices);
			if (daPvs.empty())
			{
				return;
			}
			GeoSolidArray<UInt32> daPis = GetPis(m_mesh, m_daPrimitiveIndices);
			if (daPis.empty())
			{
				return;
			}
			m_mesh->SetIsReadable(false);
			UInt32 opv = 0;
			UInt32 opi = 0;
			const UInt32 cv = 4;
			const UInt32 ci = 6;
			for (int isf = 0; isf < csf; ++isf)
			{
				SharePtr<Mesh> mesh = GeoSolid::CreateMesh(daPvs, daPis, cv, ci, opv, opi);
				m_daMeshes.emplace_back(mesh);
				opv += cv;
				opi += ci;
			}
		}

		const char* Cuboid::GetSurfaceName(const SceneModelObject::Surface& sf)
		{
			switch (sf)
			{
				case SceneModelObject::Surface::FACE1:
					return "Front";
				case SceneModelObject::Surface::FACE2:
					return "Back";
				case SceneModelObject::Surface::FACE3:
					return "Left";
				case SceneModelObject::Surface::FACE4:
					return "Right";
				case SceneModelObject::Surface::FACE5:
					return "Top";
				case SceneModelObject::Surface::FACE6:
					return "Bottom";
			}
			return "Face1";
		}

	}
}