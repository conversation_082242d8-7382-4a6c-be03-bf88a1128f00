
#include "SandboxAssetMgr.h"
#include "SandboxGlobalNotify.h"
#include "Misc/TimeManager.h"
#include "SandboxAssetHttpMgr.h"
#include "SandboxAssetUploadMgr.h"
#include "SandboxAssetReqMgr.h"
#include "SandboxAssetAutoCheckMgr.h"
#include "SandboxAssetLogMgr.h"
#include "SandboxCloudAssetLoader.h"

namespace MNSandbox {
	

	AssetMgr::AssetMgr()
	: m_listenGlobalTick(this, &AssetMgr::OnTick)
	, m_listenEnterMap(this, &AssetMgr::OnEnterMap)
	, m_listenLeaveMap(this, &AssetMgr::OnLeaveMap)
	{

	}
	AssetMgr::~AssetMgr()
	{
	}

	void AssetMgr::Init()
	{
		if (!m_listenGlobalTick.GetListener()->IsBindNotify()) {
			GlobalNotify::GetInstance().m_SysTickReal.Subscribe(m_listenGlobalTick);
		}
		if (!m_listenEnterMap.GetListener()->IsBindNotify()) {
			GlobalNotify::GetInstance().m_enterMap.Subscribe(m_listenEnterMap);
		}
		if (!m_listenLeaveMap.GetListener()->IsBindNotify()) {
			GlobalNotify::GetInstance().m_leaveMap.Subscribe(m_listenLeaveMap);
		}

		m_httpMgr = SANDBOX_NEW(AssetHttpMgr);
		m_asseetReqMgr = SANDBOX_NEW(AssetReqMgrNew);
		m_uploadMgr = SANDBOX_NEW(AssetUploadMgr);
		m_autoCheckMgr = SANDBOX_NEW(AssetAutoCheckMgr);
		m_assetLogMgr = SANDBOX_NEW(AssetLogMgr);
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
		m_assetDetailMgr = SANDBOX_NEW(AssetDetailMgr);
#endif
	}

	void AssetMgr::Release()
	{
		m_httpMgr = nullptr;
		m_asseetReqMgr = nullptr;
		m_uploadMgr = nullptr;
		m_autoCheckMgr = nullptr;
		m_assetLogMgr = nullptr;
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
		m_assetDetailMgr = nullptr;
#endif
	}

	void AssetMgr::OnTick()
	{
		Gc();
	}

	void AssetMgr::Gc()
	{
		//auto t1 = Rainbow::GetTimeUS();
		m_httpMgr->Gc();
		m_asseetReqMgr->Gc();
		//SANDBOX_LOG("AssetMgr::Gc:", std::to_string(Rainbow::GetTimeUS() - t1));
	}


	void AssetMgr::OnEnterMap()
	{
		m_asseetReqMgr->Load(Config::GetSingleton().GetMapId());
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
		m_assetDetailMgr->EnterMap(Config::GetSingleton().GetMapId());
#endif
		SandboxCloudAssetLoader::SandboxCloudAssetLoaderTest();
	}

	void AssetMgr::OnLeaveMap()
	{
		m_asseetReqMgr->Save(Config::GetSingleton().GetMapId());
		m_assetLogMgr->Clear();
#ifdef OPEN_STATISTICS_ASSSET_DETAIL
		m_assetDetailMgr->LeaveMap(Config::GetSingleton().GetMapId());
#endif
	}


}
