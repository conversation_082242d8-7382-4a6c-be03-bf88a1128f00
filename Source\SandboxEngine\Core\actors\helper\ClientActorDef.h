﻿/*
* file : ClientActorDef
* func : actor 相关结构定义
*/
#ifndef __CLIENT_ACTOR_DEF_H__
#define __CLIENT_ACTOR_DEF_H__

#include <string>


#ifdef REDEFINE_OBJ_TYPE
enum tagOBJ_TYPE
{
	OBJ_TYPE_MONSTER = 0, 	/* 怪物 */
	OBJ_TYPE_BOX = 1, 	/* 箱子 */
	OBJ_TYPE_DROPITEM = 2, 	/* 掉落 */
	OBJ_TYPE_NPC = 3, 	/* npc */
	OBJ_TYPE_CREATURE = 4, 	/* 动物 */
	OBJ_TYPE_ROLE = 5, 	/* 角色 */
	OBJ_TYPE_MINECART = 6, 	/* 矿车 */
	OBJ_TYPE_FURNACE = 7, 	/* 熔炉 */
};
#endif
//tolua_begin
enum ObjType
{
	OBJ_TYPE_EXPORB = 8, //经验球
	OBJ_TYPE_ARROW = 9,  //箭
	OBJ_TYPE_TNT = 10,
	OBJ_TYPE_FALLSAND = 11,
	OBJ_TYPE_FLYBLOCK = 12,
	OBJ_TYPE_VALUE = 13,
	OBJ_TYPE_STRING = 14,
	OBJ_TYPE_PISTON = 15,
	OBJ_TYPE_THROWABLE = 16,
	OBJ_TYPE_ENDEREYE,
	OBJ_TYPE_MOBSPAWNER,
	OBJ_TYPE_SIGNS,
	OBJ_TYPE_FIREWORK,
	OBJ_TYPE_BOAT,
	OBJ_TYPE_FUNNEL,
	OBJ_TYPE_EMITTER,
	OBJ_TYPE_HORSE,
	OBJ_TYPE_EFFECT,
	OBJ_TYPE_ITEMEXPO,
	OBJ_TYPE_COBBLE,
	OBJ_TYPE_OTHERPROJECTILE,
	OBJ_TYPE_MECHA_UNIT,
	OBJ_TYPE_MECHA_DRIVER,
	OBJ_TYPE_BOSS,
	OBJ_TYPE_FIREBALL,
	OBJ_TYPE_SENSOR,
	OBJ_TYPE_RAILKNOT,
	OBJ_TYPE_THROWBLOCK,
	OBJ_TYPE_ROCKET,
	OBJ_TYPE_RADIOUNIT,
	OBJ_TYPE_INTERPRETERUNIT,
	OBJ_TYPE_BLOCK_LASER,
	OBJ_TYPE_GIANT,
	OBJ_TYPE_DRAGON,
	OBJ_TYPE_FLYMONSTER,
	OBJ_TYPE_AQUATICMONSTER,
	OBJ_TYPE_REGIONREPLICATOR,
	OBJ_TYPE_BUILDBLUEPRINT,
	OBJ_TYPE_LASER,
	OBJ_TYPE_HOOK,
	OBJ_TYPE_COLLIDER,
	OBJ_TYPE_VEHICLE,
	OBJ_TYPE_MODELCRAFT,
	OBJ_TYPE_WORKSHOP,
	OBJ_TYPE_TRANSFER,
	OBJ_TYPE_BOOKEDITORTABLE,
	OBJ_TYPE_WHEEL,
	OBJ_TYPE_FULLYCUSTOMMODEL,
	OBJ_TYPE_SHAPESHIFT_HORSE,
	OBJ_TYPE_ACTIONER,		//动作序列器
	OBJ_TYPE_DRIVERSEAT,	//驾驶座、乘客座
	OBJ_TYPE_DRAGON_MOUNT,  //魔龙坐骑
	OBJ_TYPE_ARM_PRISMATIC,	//液压臂container
	OBJ_TYPE_ARM_SPHERIAL,	//球铰链
	OBJ_TYPE_VILLAGER,		//村民（解除诅咒后的野人）
	OBJ_TYPE_VILLAGESOUVENIR, //野人纪念品包裹(驯服后的野人死亡/叛逃后掉落)
	OBJ_TYPE_IMPORTMODEL,		//导入的模型
	OBJ_TYPE_MOON_MOUNT,    //月亮坐骑
	OBJ_TYPE_VACANTBOSS,

    OBJ_TYPE_ALTAR,//祭台
	OBJ_TYPE_ONEQUARTERBLOCK, //1/4方块
	OBJ_TYPE_FLYSNAKEGOD, //羽蛇神
	OBJ_TYPE_SKIN_NPC,		//装扮npc
	OBJ_TYPE_DOUDU_MOUNT,	//缤纷幻想坐骑
	OBJ_TYPE_HOMELAND_LIVES, // 家园生物
	OBJ_TYPE_HOMELAND_PRAY, // 家园祈愿
	OBJ_TYPE_HOMELAND_NPC,//家园NPC
	OBJ_TYPE_HOMELANT_PLANT, // 家园农作物
	OBJ_TYPE_HONOR_FRAME, // 荣耀架
	OBJ_TYPE_ACTOR_PET,		// 宠物
	OBJ_TYPE_KEY_PEDESTAL,	//火山祭坛 钥匙底座
	OBJ_TYPE_FARMLAND,
    OBJ_TYPE_STARSTATION_TRANSFER_CONSOLE, //星站控制台
	OBJ_TYPE_STARSTATION_TRANSFER_CABIN, //星站传送舱
	OBJ_TYPE_FEED_TROUGH, //饲养槽
	OBJ_TYPE_PERISTELE,		//石柱 柱顶
	OBJ_TYPE_PUMPKIN_HORSE, //南瓜坐骑
	OBJ_TYPE_TRIXENIE,		//两栖，三栖生物
	OBJ_TYPE_COAGULATION, //凝浆块
	OBJ_TYPE_KEYEFFECT,   // 祭坛钥匙
	OBJ_TYPE_MINCLUB,	  // 跳舞方块 code by wuyuwang 2021年9月18日
	OBJ_TYPE_SANDWORM,
	OBJ_TYPE_DESERTBUSINESSMAN, // 沙漠商人
	OBJ_TYPE_DESERTBUSINESSMANGUARD, //沙漠护卫
	OBJ_TYPE_DESERTVILLAGER, //沙漠村民男和沙漠村民女
	OBJ_TYPE_PACKHORSE, //骆驼
	OBJ_TYPE_SANDMAN, //沙人
	OBJ_TYPE_VENOM, //剧毒(液体)
	OBJ_TYPE_SEASPIRITGUARDING,// 海灵守卫
	OBJ_TYPE_FISHINGVILLAGER, //渔村村民男和渔村村民女
	OBJ_TYPE_FISHERMAN, //渔村钓手
	OBJ_TYPE_POSEIDONSTATUE,
	OBJ_TYPE_THORNBALL, //刺球
	OBJ_TYPE_COCONUT_PRO,
	OBJ_TYPE_CRAB,//螃蟹
	OBJ_TYPE_HIPPOCAMPUS,//非驯服的海马
	OBJ_TYPE_SMALL_HIPPOCAMPUS,//小海马
	OBJ_TYPE_HIPPOCAMPUS_HORSE,//驯服的海马
	OBJ_TYPE_PIRATE_SHIP,
	OBJ_TYPE_COCONUT,
	OBJ_TYPE_SHOW_OMOD, //显示用的模型
	OBJ_TYPE_GIANT_SCALLOPS,  // 巨型扇贝
	OBJ_TYPE_PEARL, // 珍珠 3521
	OBJ_TYPE_FISHHOOK,	// 鱼钩
	OBJ_TYPE_COLORPALETTE,	// 调色架
	OBJ_TYPE_SOLIDSAND,	// 固体沙子
	OBJ_TYPE_FISHFRAME,	// 晒鱼架
	OBJ_TYPE_SMALL_TORCH, // 小火炬
	OBJ_TYPE_POPULUS_LEAF, // 胡杨树叶
	OBJ_TYPE_LIGHTMUSHROOM, // 荧光小菇
	OBJ_TYPE_KEYDOOR,   // 钥匙门
	OBJ_TYPE_COLLECTING_PIPE,   // 收集管道
	OBJ_TYPE_PORTAL,   // 传送门
	OBJ_TYPE_SNOWMAN, //雪人
	OBJ_TYPE_ICECRYSTALSHROOM, // 冰晶喷菇
	OBJ_TYPE_SNOWBALL, //雪球
	OBJ_TYPE_SCHOOLFENCE, //学校围栏
	OBJ_TYPE_FUSIONCAGE,//融合器
	OBJ_TYPE_VORTEX, //漩涡
	OBJ_TYPE_JAR, //罐子
	OBJ_TYPE_TRAVELING_TRADER_NPC, //游商
	OBJ_TYPE_STARSTATION_CARGO, //星链货运终端
	OBJ_TYPE_TALKINGSTATUE,
	OBJ_TYPE_COMPUTER,
	OBJ_TYPE_MONSTERSUMMONER,
	OBJ_TYPE_MOD_CONTAINER,
	OBJ_TYPE_MOD_CONTAINER_TRANSFER,
	OBJ_TYPE_STORAGEBOXHORSE,
	OBJ_TYPE_PLAYERCORPSE, //玩家尸体
	OBJ_TYPE_EROSION_CONTAINER,//方块的腐蚀Container
	OBJ_TYPE_CUBECHEST, //立方体宝箱
	OBJ_TYPE_AIR_PLANE, //空投飞机
	OBJ_TYPE_ARCHITECTURE,//建筑
	OBJ_TYPE_GAMEOBJECT = 9999, //实体对象
};

//开发者事件:特效触发类型
enum tagTriggerEffectType {
	TriggerEffect_Player = 5,	//玩家
	TriggerEffect_Mob,			//生物
	TriggerEffect_Projectile,	//投射物
	TriggerEffect_DropItem,	//掉落物
};

enum
{
	ACTORFLAG_ONGROUND = 0,
	ACTORFLAG_PERSISTENCE = 1,
	ACTORFLAG_SNEAK = 2,
	ACTORFLAG_FLY = 3,
	ACTORFLAG_INFUSE = 4,
	ACTORFLAG_SHEARED = 5,
	ACTORFLAG_RUN = 6,
	ACTORFLAG_SLEEP = 8,
	ACTORFLAG_SIT = 9,
	ACTORFLAG_AI_SIT = 10,
	ACTORFLAG_AI_SLEEP = 11,
	ACTORFLAG_AI_MATE = 12,
	ACTORFLAG_AI_EAT = 13,
	ACTORFLAG_AI_DANCING = 14,
	ACTORFLAG_AI_MILKING = 15,
	ACTORFLAG_AI_SIT_COMMAND= 16,
	ACTORFLAG_AI_TOPPLEOVER = 17,
	ACTORFLAG_AI_SCROLL = 18,
	ACTORFLAG_AI_CONCEAL = 19,
	ACTORFLAG_AI_BREATHE = 20,
	ACTORFLAG_AI_HOLD = 21,
	ACTORFLAG_AI_BUMP = 22,
	ACTORFLAG_AI_HUNGERSIT = 23,
	ACTORFLAG_AI_DIE = 24,
	ACTORFLAG_AI_NPCSLEEP = 25,
	ACTORFLAG_AI_TIRED = 26,
	ACTORFLAG_AI_CRAFT = 27,
	ACTORFLAG_FLOATAGE = 28,
	ACTORFLAG_AI_WARNING = 29,
	ACTORFLAG_NOD = 30,  //打瞌睡
	ACTORFLAG_AI_ESCAPE = 31,  //逃跑
	ACTORFLAG_INTERACTIVE_CORPSE = 32, //可交互的尸体
};

enum
{
	ACTOR_ANIM_FLAG_NOD,			 //打瞌睡
	ACTOR_ANIM_FLAG_EAT,			 //吃东西
	ACTOR_ANIM_FLAG_SIT_BY_HUNGER,   //坐下饥饿摸肚子
	ACTOR_ANIM_FLAG_STAND_BY_HUNGER, //站着饥饿摸肚子
	ACTOR_ANIM_FLAG_FLEE,			//逃跑
	ACTOR_ANIM_FLAG_SIT,			//坐下
	ACTOR_ANIM_FLAG_DANCE,			//跳舞
	ACTOR_ANIM_FLAG_SING,			//唱歌
	ACTOR_ANIM_FLAG_CAY,			//哭泣
	ACTOR_ANIM_FLAG_RUB_HAND,		//搓手制作物品
	ACTOR_ANIM_FLAG_TALK,			//交谈
	ACTOR_ANIM_FLAG_SHOOT,			//射箭
	ACTOR_ANIM_FLAG_THINK,			//疑问
	ACTOR_ANIM_FLAG_STAND_SLEEP,    //站着睡觉
	ACTOR_ANIM_FLAG_WAKEUP,			//被吵醒
	ACTOR_ANIM_FLAG_DEFENSE,		//右手持盾防御
	ACTOR_ANIM_FLAG_LEFTDEFENSE,	//左手持盾防御
};

enum AIMoveTerrain
{
	LandTerrain = 1,
	WaterTerrain = 2,
	LavaTerrain = 4,
// 	HoneyTerrain = 8,
};

enum ACTOR_MOVEMODE_T
{
	ACTORMOVE_NORMAL = 0,
	ACTORMOVE_JUMP
};

struct ActorAttribut
{
	float MaxHP;		//最大生命值
	float NowHP;		//当前生命值
	float HPRecover;	//生命恢复
	float MaxHunger;	//最大饥饿值
	int NowHunger;	//当前饥饿值
	float MaxOxygen;	//最大氧气值
	float NowOxygen;	//当前氧气值
	float MoveSpeed;	//移动速度
	float RunSpeed;		//奔跑速度
	float SwimSpeed;	//游泳速度
	float JumpSpeed;	//跳跃力
	float Weight;		//重量
	float SneakSpeed;	//潜行速度
	float Dodge;		//闪避率
	float NearAttack;	//近战攻击
	float RemoteAttack;	//远程攻击
	float NearArmor;	//近战防御
	float RemoteArmor;	//远程防御
	float Dimension;	//大小
	int Score;	//分数
	int Level;	//等级
	int LevelModeExp;	//等级模式:当前经验
	int LevelModeLevel;	//等级模式:当前等级
	float Strength; //当前体力值
	float MaxStrength; // 最大体力值
	float StrengthRecover;  // 体力值恢复速度
	float ExtraHP;		//额外生命值
	float Armor;		// 护盾值
};

struct BodyEffectBrief {
	std::string effect_name;
	int effect_id;
	float effect_scale;
	float effect_looptime;
};
//tolua_end

#endif
