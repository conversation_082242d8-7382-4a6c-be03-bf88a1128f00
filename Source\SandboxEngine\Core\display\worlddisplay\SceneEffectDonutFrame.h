#pragma once
/**
* file : SceneEffectDonutFrame
* func : 场景效果 （甜甜圈框）
* by : pengdapu
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectEllipse;
class SceneEffectLine;

class SceneEffectDonutFrame : public SceneEffectGeom
{
public:
	SceneEffectDonutFrame();
	virtual ~SceneEffectDonutFrame();
	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;
	bool IsActive(World* pWorld) const override;
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
public:
	void SetRadius(float radius) override;
	void SetInnerRadius(float ir);
private:
	SceneEffectEllipse* m_aEllipses[8] = { nullptr, nullptr, nullptr, nullptr, nullptr, nullptr, nullptr, nullptr, };
	/**
	@brief	上方半径。下方使用父类的m_fRadius
	 */
	float m_fInnerRadius;
};
