
#ifndef __LOOSE_AABBTREE_H__
#define __LOOSE_AABBTREE_H__

#include "world_types.h"
//#include "OgreRay.h"
#include "Common/OgreWCoord.h"
#include "Geometry/Ray.h"
class LooseBinaryTree;

class BinaryTreeNode
{
public:	
	BinaryTreeNode(LooseBinaryTree *tree, BinaryTreeNode *parent, int level, const WCoord &center, const WCoord &radius);
	~BinaryTreeNode();

	void addObject(void *userdata);
	void removeObject(void *userdata);

	bool isInNode(const WCoord &minpos, const WCoord &maxpos);
	bool isOutNode(const WCoord &minpos, const WCoord &maxpos);
	BinaryTreeNode *getContainNode(const WCoord &center, const WCoord &radius);
	

	void pickObjects(std::vector<void *>&objs, const Rainbow::Ray &ray);
	void getObjectsInBox(std::vector<void *>&objs, const WCoord &minpos, const WCoord &maxpos);
public:
	WCoord m_Center;
	WCoord m_Radius;
	//WCoord m_Zone;  //the total size of this grid node  = m_FixSize + m_Zone
	WCoord m_MinPos;
	WCoord m_MaxPos;
	int  m_Level;

	int m_SplitDir; //0-x, 1-y, 2-z
	BinaryTreeNode *m_pChild[2];
	BinaryTreeNode *m_pFather;
	LooseBinaryTree *m_pTree;

	std::vector<void *>m_Objects;
};

class LooseBinaryTree
{
	friend class BinaryTreeNode;

public:	
	LooseBinaryTree(size_t maxlevel, const WCoord &minpos, const WCoord &maxpos);
	~LooseBinaryTree();

	BinaryTreeNode *attachObject(const WCoord &minpos, const WCoord &maxpos, void *userdata);
	void detachObject(BinaryTreeNode *node, void *userdata);
	BinaryTreeNode *updateObject(BinaryTreeNode *node, const WCoord &minpos, const WCoord &maxpos, void *userdata);
	
	void getObjectsInBox(std::vector<void*> &objs, const WCoord &minpos, const WCoord &maxpos);
	void pickObjects(std::vector<void*> &objs, const Rainbow::Ray &ray);
private:
	BinaryTreeNode *m_pRoot;
	int      m_MaxLevel;
	int      m_CurNodeNum;
	WCoord   m_Origin;
};
/*
class CollisionTree;
class IClientActor;

class CollideObject
{
	friend class CollisionTree;

public:
	CollideObject() : m_CollideTree(NULL), m_CollideNodeIndex(-1), m_pNextObject(NULL)
	{

	}
	void clearCollideInfo()
	{
		m_CollideTree = NULL;
		m_CollideNodeIndex = -1;
		m_pNextObject = NULL;
	}
	CollisionTree *getCollideTree()
	{
		return m_CollideTree;
	}
	virtual void getCollideBox(CollideAABB &box) = 0;

protected:
	CollisionTree *m_CollideTree;
	int m_CollideNodeIndex;
	CollideObject *m_pNextObject;
};

#define TREE_NODES_Y 16
#define TREE_NODES_X 8
#define TREE_NODES_Z 8
#define TREE_NODES_TOTAL (TREE_NODES_X*TREE_NODES_Y*TREE_NODES_Z)
#define TREE_NODESIZE_X (CHUNK_SIZE_X/TREE_NODES_X)
#define TREE_NODESIZE_Y (CHUNK_SIZE_Y/TREE_NODES_Y)
#define TREE_NODESIZE_Z (CHUNK_SIZE_Z/TREE_NODES_Z)

class CollisionTree
{
public:
	CollisionTree(const WCoord &origin);
	~CollisionTree();

	void addActor(IClientActor *pactor);
	void removeActor(IClientActor *pactor);
	void onActorMoved(IClientActor *pactor);

	bool isCollide(const WCoord &pos, const WCoord &dim, IClientActor *exclude);
	void getActorsInBoxExclude(std::vector<IClientActor *>&actors, const CollideAABB &box, IClientActor *exclude);
	void getActorsOfTypeInBox(std::vector<IClientActor *>&actors, const CollideAABB &box, int actortype);

	IClientActor *pickActor(const WCoord &pos, const Rainbow::Vector3f &dir, IClientActor);

private:
	void pos2xyz(const WCoord &pos, int &x, int &y, int &z);
	int pos2Index(const WCoord &pos)
	{
		int x, y, z;
		pos2xyz(pos, x, y, z);
		return xyz2Index(x, y, z);
	}
	int xyz2Index(int x, int y, int z)
	{
		assert(x>=0 && x<TREE_NODES_X);
		assert(z>=0 && z<TREE_NODES_Z);
		assert(y>=0 && y<TREE_NODES_Y);
		return y*TREE_NODES_X*TREE_NODES_Z + z*TREE_NODES_X + x;
	}

private:
	WCoord m_Origin;
	CollideObject *m_Nodes[TREE_NODES_Y*TREE_NODES_Z*TREE_NODES_X];
};
*/

#endif