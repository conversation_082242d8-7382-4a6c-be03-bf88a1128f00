#include "RainSnowRenderObject.h"

#include "Graphics/Mesh/Mesh.h"
#include "Render/RenderUtils.h"
#include "Render/ShaderMaterial/MaterialManager.h"

#include "Render/RenderScene.h"
#include "Render/VertexLayouts/MeshVertexLayout.h"
#include "display/worlddisplay/RainSnow.h"
using namespace Rainbow;


RainSnowRenderObject::RainSnowRenderObject(RainSnowRenderable* component): RenderObject(component)
{
	m_MeshRenderData.SetVertexLayout(GetMeshVertexLayout(0, false));
	m_RainSnowEffect = static_cast<RainSnowRenderable*>(m_Component);
}

RainSnowRenderObject::~RainSnowRenderObject()
{
}


void RainSnowRenderObject::ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator)
{
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Rendering);
	m_RainSnowEffect->GenerateMesh();
	m_RenderBuffer = &m_RainSnowEffect->GetMeshData();
	dynamic_array<DrawBuffersRange> drawBuffersRanges(kMemTempAlloc);

	if (m_RenderBuffer->m_RainVertNum == 0 && m_RenderBuffer->m_SnowVertNum == 0) return;

	size_t vertexStride = m_RenderBuffer->m_VertStride;
	size_t vertexOffset = 0;
	if (m_RenderBuffer->m_RainVertNum > 0)
	{
		drawBuffersRanges.emplace_back(DrawBuffersRange::CreateIndexed(
			vertexStride, kPrimitiveTriangles, m_RenderBuffer->m_RainVertNum, vertexOffset, 0, 0));
		vertexOffset += m_RenderBuffer->m_RainVertNum * vertexStride;
	}

	if (m_RenderBuffer->m_SnowVertNum > 0)
	{
		DrawBuffersRange drawRange = DrawBuffersRange::CreateIndexed(
			vertexStride, kPrimitiveTriangles, m_RenderBuffer->m_SnowVertNum, vertexOffset, 0, 0);
		drawRange.firstVertex = drawRange.baseVertex;
		drawRange.baseVertex = 0;
		drawBuffersRanges.emplace_back(drawRange);

	}
	if (drawBuffersRanges.size() == 0)
		return;

	m_MeshRenderData.UploadDynamicVertexBuffer(
		GetGfxDevice(),
		m_RenderBuffer->m_VBBufferSize,
		vertexStride,
		m_RenderBuffer->m_Mask,
		m_RenderBuffer->m_VB,
		drawBuffersRanges
	);

	MeshPrimitiveRenderData& meshPrimitiveData = extractor.AddMeshPrimitiveRenderData(viewNode, allocator);
	meshPrimitiveData.m_MaterialRenderDatas.add(allocator, drawBuffersRanges.size());
	meshPrimitiveData.m_SharedMeshRenderingData = m_MeshRenderData.AcquireSharedMeshRenderingData();
	meshPrimitiveData.m_VertexLayout = m_MeshRenderData.GetVertexLayout();

	for (size_t i = 0; i < drawBuffersRanges.size(); i++)
	{
		m_RenderBuffer->m_Mats[i]->CollectSharedMaterialDataList(meshPrimitiveData.m_MaterialRenderDatas[i].m_SharedMaterialDataList);

		auto& meshPrim = extractor.AllocateMeshPrimitive(allocator, 0, i);
		meshPrim.m_DrawBuffersRanges.emplace_back() = drawBuffersRanges[i];
		extractor.AddMeshPrimitive(meshPrim, viewNode);
	}
}
