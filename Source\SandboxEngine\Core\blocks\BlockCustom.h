
#ifndef __BLOCK_CUSTOM_H__
#define __BLOCK_CUSTOM_H__

#include "BlockBasic.h"
#include "SandboxEngine.h"
class SectionMesh;

class EXPORT_SANDBOXENGINE BlockCustom;
class BlockCustom : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockCustom)
public:
	virtual void init(int resid) override;
	//tolua_begin
	//virtual const char *getGeomName() override;
	//virtual std::string getDefaultGeomName() override
	//{
	//	return "block";
	//}
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	//virtual void createPickData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos) override;
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_LITTLE;
	//}
	//virtual bool isOpaqueCube()
	//{
	//	return true;
	//}

	virtual void refreshCusotmBlock() override;

	SectionMesh *createBlockModelProtoMesh(std::string geomname);

	virtual bool hasSolidTopSurface(int blockdata) override;
	//tolua_end
private:
	virtual void initDrawType() override;
	virtual void initGeomName() override;
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf);
}; //tolua_exports

#endif
