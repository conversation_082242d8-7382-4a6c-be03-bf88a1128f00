@echo off
set PATH_TOLUA_EXE=..\..\..\External\Game\lua\tolua\bin\tolua++.exe
set PATH_PYTHON_EXE=..\..\..\..\Tools\buildtools\Python\Win64\python.exe
set TMPL_PYTHON_TARGET=..\..\..\..\Tools\tolua_sandbox\TmplToLua.py

set TARGET_TEMP_PKG=SandboxCoreDriverToLua_Temp.pkg
%PATH_PYTHON_EXE% %TMPL_PYTHON_TARGET% -p SandboxCoreDriverToLua.pkg -d %TARGET_TEMP_PKG%

%PATH_TOLUA_EXE% -o SandboxCoreDriverToLua.cpp -n SandboxCoreDriverToLua %TARGET_TEMP_PKG%
echo SandboxCoreDriverToLua tolua

del %TARGET_TEMP_PKG% rem remove tempfile

set SRC_PYTHON_TARGET=..\..\..\..\Tools\tolua_sandbox\checktolua.py
%PATH_PYTHON_EXE% %SRC_PYTHON_TARGET% -s SandboxCoreDriverToLua.cpp -p SandboxCoreDriverToLua.pkg
echo SandboxCoreDriver check

rem pause