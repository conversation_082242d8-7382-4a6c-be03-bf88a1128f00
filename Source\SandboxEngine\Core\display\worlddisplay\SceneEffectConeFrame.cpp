/**
* file : SceneEffectConeFrame
* func : 场景效果 （圆锥、圆台框）
* by : pengdapu
*/
#include "SceneEffectConeFrame.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "SceneEffectEllipse.h"
#include "WorldRender.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectConeFrame::SceneEffectConeFrame()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_eSes = SceneEffectShape::CONE_FRAME;
}

SceneEffectConeFrame::~SceneEffectConeFrame()
{
}

void SceneEffectConeFrame::SetRadius(float radius)
{
	if (m_radius == radius)
	{
		return;
	}

	m_radius = radius;
	m_fHeight = 1.7f * radius;
	m_fUpRadius = radius;
	m_originRadius = radius;
}

void SceneEffectConeFrame::SetHeight(float h)
{
	if (m_fHeight == h)
		return;

	m_fHeight = h;
	m_fUpRadius = m_fHeight * tan(m_fAngle) + m_radius;
}

void SceneEffectConeFrame::SetAngle(float angle)
{
	if (m_fAngle == angle)
	{
		return;
	}
	m_fAngle = angle;
	if (m_fHeight > 0)
	{
		m_fUpRadius = m_fHeight * tan(m_fAngle) + m_radius;
	}
}

void SceneEffectConeFrame::OnClear()
{
	SANDBOX_DELETE(m_ellipseD);
	SANDBOX_DELETE(m_ellipseU);
	for (int i = 0; i < 4; ++i)
	{
		SANDBOX_DELETE(m_aGeneratrices[i]);
	}
}

void SceneEffectConeFrame::Refresh()
{
	OnClear();
	SceneEffectEllipse* aEllipses[2] = {
		SANDBOX_NEW(SceneEffectEllipse),
		SANDBOX_NEW(SceneEffectEllipse),
	};

	m_ellipseD = aEllipses[0];
	m_ellipseU = aEllipses[1];

	m_ellipseD->SetRadius(m_radius);
	m_ellipseU->SetRadius(m_fUpRadius);

	for (int i = 0; i < 2; ++i)
	{
		SceneEffectEllipse* ellipse = aEllipses[i];
		ellipse->SetColor(m_Color, m_Color);
		ellipse->SetStroke(m_iStroke);
		ellipse->SetMtlType(m_MtlType);
		ellipse->SetRotationAxis(Vector3f::neg_zAxis, Vector3f::neg_zAxis);
	}

	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* generatrix = m_aGeneratrices[i] = SANDBOX_NEW(SceneEffectLine);;
		generatrix->SetStroke(m_iStroke);
		generatrix->SetColor(m_Color);
		generatrix->SetMtlType(m_MtlType);
	}

	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectConeFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	if (m_ellipseD) m_ellipseD->OnDraw(pWorld);
	if (m_ellipseU) m_ellipseU->OnDraw(pWorld);
	for (int i = 0; i < 4; ++i)
	{
		SceneEffectLine* generatrix = m_aGeneratrices[i];
		if (generatrix) generatrix->OnDraw(pWorld);
	}
}

bool SceneEffectConeFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectConeFrame::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;
	//const float h = m_fHeight * vs.y;
	//向前倒向Z轴
	const float h = m_fHeight * vs.z;
	Matrix4x4f matRotate;
	QuaternionfToMatrix(q, matRotate);
	if (m_ellipseD)
	{
		m_ellipseD->RefreshEllipseFrame(vc, q, m_radius * vs.x, m_radius * vs.y);
	}
	if (m_ellipseU)
	{
		Vector3f vcUp(0.f);
		vcUp.z = h;
		vcUp = matRotate.MultiplyPoint3(vcUp);
		vcUp += vc;
		m_ellipseU->RefreshEllipseFrame(vcUp, q, m_fUpRadius * vs.x, m_fUpRadius * vs.y);
	}

	Vector3f aVs[8];

	//aVs[0].Set(m_radius * vs.x, 0, 0);
	//aVs[1].Set(0, 0, m_radius * vs.z);
	//aVs[2].Set(-m_radius * vs.x, 0, 0);
	//aVs[3].Set(0, 0, -m_radius * vs.z);

	//aVs[4].Set(m_fUpRadius * vs.x, h, 0);
	//aVs[5].Set(0, h, m_fUpRadius * vs.z);
	//aVs[6].Set(-m_fUpRadius * vs.x, h, 0);
	//aVs[7].Set(0, h, -m_fUpRadius * vs.z);

	aVs[0].Set(m_radius * vs.x, 0, 0);
	aVs[1].Set(0, m_radius * vs.y, 0);
	aVs[2].Set(-m_radius * vs.x, 0, 0);
	aVs[3].Set(0, -m_radius * vs.y, 0);

	aVs[4].Set(m_fUpRadius * vs.x, 0, h);
	aVs[5].Set(0, m_fUpRadius * vs.y, h);
	aVs[6].Set(-m_fUpRadius * vs.x, 0, h);
	aVs[7].Set(0, -m_fUpRadius * vs.y, h);

	for (int i = 0; i < 8; ++i)
	{
		aVs[i] = matRotate.MultiplyPoint3(aVs[i]);
		aVs[i] += vc;
	}

	for (int i = 0; i < 4; ++i)
	{
		if (!m_aGeneratrices[i])
		{
			continue;
		}
		m_aGeneratrices[i]->SetPos(aVs[i], aVs[i + 4], false);
		m_aGeneratrices[i]->Refresh();
	}
}
