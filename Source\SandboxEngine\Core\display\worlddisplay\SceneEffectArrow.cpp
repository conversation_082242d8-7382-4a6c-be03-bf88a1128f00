/**
* file : SceneEffectArrow
* func : 场景效果（箭头）
* by : yangzy
*/
#include "SceneEffectArrow.h"
#include "SandboxPlane.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "OgreUtils.h"
#include "world.h"

using namespace MINIW;
using namespace Rainbow;

std::map<DirectionType, MNSandbox::MNCoord3f> SceneEffectArrow::m_mapArrowOriginDir = {
	std::make_pair(DIR_NEG_X, Rainbow::Vector3f::neg_xAxis), std::make_pair(DIR_POS_X, Rainbow::Vector3f::xAxis),
	std::make_pair(DIR_NEG_Y, Rainbow::Vector3f::neg_yAxis), std::make_pair(DIR_POS_Y, Rainbow::Vector3f::yAxis),
	std::make_pair(DIR_NEG_Z, Rainbow::Vector3f::neg_zAxis), std::make_pair(DIR_POS_Z, Rainbow::Vector3f::zAxis)
};

SceneEffectArrow::SceneEffectArrow()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
}

SceneEffectArrow::SceneEffectArrow(MNSandbox::MNCoord3f arrowPos, MNSandbox::MNCoord3f arrowDir, 
	DirectionType dir, float ratio, CURVEFACEMTLTYPE mtltype, CurveFace* curveFaces)
	: m_arrowPos(arrowPos)
	, m_arrowDir(arrowDir)
	, m_dir(dir)
{
	m_MtlType = mtltype;

	SetRatio(ratio);
	SetCurveFaces(curveFaces);
}

SceneEffectArrow::~SceneEffectArrow()
{
}

void SceneEffectArrow::SetArrow(MNSandbox::MNCoord3f arrowPos, MNSandbox::MNCoord3f arrowDir, DirectionType dir)
{ 
	if (m_arrowPos == arrowPos && m_arrowDir == arrowDir && m_dir == dir)
	{
		return;
	}

	m_arrowPos = arrowPos; 
	m_arrowDir = arrowDir; 
	m_dir = dir;
}

void SceneEffectArrow::SetArrowRange(int arrowWidth, int arrowLength)
{
	m_arrowWidth = arrowWidth;
	m_arrowLength = arrowLength;

	ms_arrowWidth = arrowWidth;
	ms_arrowLength = arrowLength;
}

// 设置比率
void SceneEffectArrow::SetRatio(float ratio)
{ 
	m_lineWidth = std::ceil((float)ms_lineWidth * ratio);
	m_lineLength = std::ceil((float)ms_lineLength * ratio);
	m_arrowWidth = std::ceil((float)ms_arrowWidth * ratio);
	m_arrowLength = std::ceil((float)ms_arrowLength * ratio);
}

void SceneEffectArrow::OnClear()
{
	SANDBOX_DELETE(m_arrowLine._line);

	for (auto& lineItem : m_vecArrowLineSimplify)
	{
		SANDBOX_DELETE(lineItem._line);
	}

	m_vecVertexs1.clear();
	m_vecVertexs2.clear();
	m_vecVertexs3.clear();
	m_vecVertexs4.clear();
	m_vecIndices.clear();
	m_vecRayPickLine.clear();
}

void SceneEffectArrow::Refresh()
{
	OnClear();
	RefreshArrowFrame();
}

void SceneEffectArrow::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
		
	auto curveRender = m_CurveFaces ? m_CurveFaces : pWorld->getRender()->getCurveRender();
	if (m_arrowLine._line && curveRender && m_arrowType == ARROWTYPE::NORMAL)
	{
		m_arrowLine._line->SetDrawPos(m_arrowLine._startDrawPos);
		m_arrowLine._line->OnDraw(pWorld);

		auto pos = m_arrowPos + m_arrowDir * (float)m_lineLength;
		curveRender->addRect((int)m_MtlType, pos.ToWCoord(), m_vecVertexs1, m_vecIndices);
		curveRender->addRect((int)m_MtlType, pos.ToWCoord(), m_vecVertexs2, m_vecIndices);
		curveRender->addRect((int)m_MtlType, pos.ToWCoord(), m_vecVertexs3, m_vecIndices);
		curveRender->addRect((int)m_MtlType, pos.ToWCoord(), m_vecVertexs4, m_vecIndices);
	}
	else if (m_arrowType == ARROWTYPE::SIMPLIFY && m_vecArrowLineSimplify.size() == 2)
	{
		for (auto& lineItem : m_vecArrowLineSimplify)
		{
			if (lineItem._line)
			{
				lineItem._line->SetDrawPos(lineItem._startDrawPos);
				lineItem._line->OnDraw(pWorld);
			}
		}
	}
}

bool SceneEffectArrow::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectArrow::RefreshArrowFrame()
{
	if ((m_arrowPos != MNSandbox::MNCoord3f(0.0)) && (m_arrowDir != MNSandbox::MNCoord3f(0.0)))
	{
		//普通箭头
		if (m_arrowType == ARROWTYPE::NORMAL)
		{
			//箭头线段
			m_arrowLine._line = SANDBOX_NEW(SceneEffectLine, m_arrowPos.ToWCoord(), (m_arrowPos + m_arrowDir * (float)m_lineLength).ToWCoord(), m_lineWidth, m_MtlType, false);
			m_arrowLine._startDrawPos = m_arrowPos;
			m_arrowLine._endDrawPos = m_arrowPos + m_arrowDir * (float)m_lineLength;
			m_arrowLine._line->SetColor(m_Color);
			m_arrowLine._line->SetCurveFaces(m_CurveFaces);
			m_arrowLine._line->Refresh();

			//四棱锥
			Rainbow::Vector3f startpos = Vector3f(0, 0, 0);
			Rainbow::Vector3f dir = m_mapArrowOriginDir[m_dir];
			Rainbow::Vector3f endpos = startpos + dir * (float)m_arrowLength;
			Rainbow::Vector3f border = dir * (float)m_arrowWidth;
			startpos -= border;
			endpos += border;
			Rainbow::Vector3f dirVertical1, dirVertical2;
			Rainbow::Vector3f vertex[5];

			if (Abs(dir.x) < 0.1f && Abs(dir.z) < 0.1f)
			{
				dirVertical1 = Rainbow::Vector3f(1.0f, 0.0f, 0.0f);
			}
			else
			{
				dirVertical1 = Rainbow::Vector3f(0.0f, 1.0f, 0.0f);
			}

			dirVertical2 = CrossProduct(dir, dirVertical1);
			dirVertical1 = CrossProduct(dir, dirVertical2);
			dirVertical1 = MINIW::Normalize(dirVertical1);
			dirVertical2 = MINIW::Normalize(dirVertical2);
			dirVertical1 *= (float)m_arrowWidth;
			dirVertical2 *= (float)m_arrowWidth;

			vertex[0] = startpos + dirVertical1 + dirVertical2;
			vertex[1] = startpos + dirVertical1 - dirVertical2;
			vertex[2] = startpos - dirVertical1 + dirVertical2;
			vertex[3] = startpos - dirVertical1 - dirVertical2;

			auto tmpV1 = endpos + dirVertical1 + dirVertical2;
			auto tmpV2 = endpos + dirVertical1 - dirVertical2;
			auto tmpV3 = endpos - dirVertical1 + dirVertical2;
			auto tmpV4 = (tmpV1 + tmpV2) / (float)2;
			auto tmpV5 = (tmpV1 + tmpV3) / (float)2;

			//锥点
			vertex[4] = Rainbow::Vector3f(endpos.x, tmpV5.y, tmpV4.z);

			auto originPos = m_arrowPos + m_arrowDir * (float)m_lineLength;
			m_vecRayPickLine.emplace_back(DrawLine(vertex[0] + originPos, vertex[4] + originPos));
			m_vecRayPickLine.emplace_back(DrawLine(vertex[1] + originPos, vertex[4] + originPos));
			m_vecRayPickLine.emplace_back(DrawLine(vertex[2] + originPos, vertex[4] + originPos));
			m_vecRayPickLine.emplace_back(DrawLine(vertex[3] + originPos, vertex[4] + originPos));

			for (auto i = 0; i < 5; i++)
			{
				vertex[i] = GetQua() * vertex[i];
			}

			float u1 = 0.49f;
			float u2 = 0.51f;;
			float v1 = 0.0f;
			float v2 = m_arrowWidth / 100;

			m_vecIndices.push_back(0);
			m_vecIndices.push_back(2);
			m_vecIndices.push_back(1);

			m_vecVertexs1.resize(3);
			m_vecVertexs2.resize(3);
			m_vecVertexs3.resize(3);
			m_vecVertexs4.resize(3);

			SceneEffectLine::FillVertBuffer(m_vecVertexs1[0], vertex[0], u1, v1, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs1[1], vertex[4], u2, v1, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs1[2], vertex[1], u1, v2, m_Color);

			SceneEffectLine::FillVertBuffer(m_vecVertexs2[0], vertex[1], u1, v2, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs2[1], vertex[4], u2, v2, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs2[2], vertex[3], u1, v1, m_Color);

			SceneEffectLine::FillVertBuffer(m_vecVertexs3[0], vertex[3], u1, v1, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs3[1], vertex[4], u2, v1, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs3[2], vertex[2], u1, v2, m_Color);

			SceneEffectLine::FillVertBuffer(m_vecVertexs4[0], vertex[2], u1, v2, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs4[1], vertex[4], u2, v2, m_Color);
			SceneEffectLine::FillVertBuffer(m_vecVertexs4[2], vertex[0], u1, v1, m_Color);
		}
		//精简箭头
		else if (m_vecArrowLineSimplify.size() == 2)
		{
			MNSandbox::MNCoord3f originPos = (m_arrowPos + m_arrowDir * (float)m_lineOffsetSimplify);
			MNSandbox::MNCoord3f vecBeg = originPos + (Rainbow::Vector3f::zero - m_arrowDir) * (float)m_lineLengthSimplify;
			MNSandbox::MNCoord3f vecEnd = MNSandbox::MNCoord3f(0.0);
			float radian = Rainbow::HALF_PI * 0.25;
			
			for (auto& lineItem : m_vecArrowLineSimplify)
			{
				Rainbow::Matrix3x3f matrix;
				matrix.SetAxisAngle(Rainbow::Vector3f::yAxis, radian);
				vecEnd = matrix.MultiplyPoint3(vecBeg - originPos) + originPos;

				lineItem._line = SANDBOX_NEW(SceneEffectLine, originPos.ToWCoord(), vecEnd.ToWCoord(), m_lineWidthSimplify, m_MtlType, false);
				lineItem._startDrawPos = originPos;
				lineItem._endDrawPos = vecEnd;
				lineItem._line->SetColor(m_Color);
				lineItem._line->SetCurveFaces(m_CurveFaces);
				lineItem._line->Refresh();

				radian = -radian;
			}
		}
	}
}

bool SceneEffectArrow::RayArrow(MNSandbox::Ray& ray, MNSandbox::MNCoord3f& targetPos)
{
	// 精简箭头不判断pick
	if (m_arrowType == ARROWTYPE::SIMPLIFY)
	{
		return false;
	}

	m_vecRayPickLine.emplace_back(DrawLine(m_arrowLine._startDrawPos, m_arrowLine._endDrawPos));

	for (auto& item : m_vecRayPickLine)
	{
		MNSandbox::Ray rayLine(item._startDrawPos, item._endDrawPos - item._startDrawPos);
		float lenLineSquare = rayLine.GetDir().GetLengthSquare();
		auto plane = MNSandbox::Plane::CreateByPointX3(item._startDrawPos, item._endDrawPos, ray.GetPos());

		//投影
		auto dir = ray.GetDir();
		dir.Normalize();
		auto vecProjection = plane.VectorProjection(dir);
		MNSandbox::Ray rayPlane(ray.GetPos(), vecProjection);

		float dstDist;

		// 平面交点
		if (!rayLine.IntersectRay(rayPlane, &targetPos, &dstDist, nullptr))
		{	
			continue;
		}

		// 确保是线段
		if (dstDist * dstDist > lenLineSquare)
		{
			continue;
		}

		MNSandbox::MNCoord3f vecPlane(targetPos - ray.GetPos());

		// 两条射线之间的最短线段
		MNSandbox::MNCoord3f targetVec(vecPlane - (dir * vecPlane.DotProduct(dir)));

		float distSquare = targetVec.GetLengthSquare();
		static float fSensitivity = 5.0;

		if (distSquare <= m_lineWidth * m_lineWidth * fSensitivity)
		{
			return true;
		}
	}

	targetPos = MNSandbox::MNCoord3f(0.0);
	return false;
}

Rainbow::Quaternionf SceneEffectArrow::GetQua()
{
	auto originDir = m_mapArrowOriginDir[m_dir];
	return Rainbow::FromToQuaternion(originDir, m_arrowDir);
}

void SceneEffectArrow::SetArrowType(ARROWTYPE arrowType)
{
	m_arrowType = arrowType;
	m_vecArrowLineSimplify.resize(m_arrowType == ARROWTYPE::SIMPLIFY ? 2 : 0);
}
