#pragma once
#include "LegacyModule.h"
#include "FrameworkPrefix.h"
#include "Serialize/SerializeUtility.h"
#include "Utilities/dynamic_array.h"
#include "GfxDevice/GfxDeviceTypes.h"
namespace Rainbow
{
	struct GamePackageInfo
	{
		DECLARE_SERIALIZE(GamePackageInfo)
		core::string name;
		core::string pkgFilePath;
		//这个值不要超过100，避免和Patch包的优先级冲突
		int priority;
		dynamic_array<core::string> filePrefix;
		GfxDeviceRenderer renderer= kGfxRendererUnknown;
		int luajitVersion = 0;

	};

	struct RemoteUrlInfo
	{
		DECLARE_SERIALIZE(RemoteUrlInfo)
		core::string url;
		//越大优先级越大
		int priority;
	};

	struct SubprojectInfo
	{
		DECLARE_SERIALIZE(SubprojectInfo)
		core::string name;
		core::string envs;
		core::string pkgPathPrefix;
	};
	
	//游戏配置的一个json文件
	class EXPORT_LEGACYMODULE GameSetting
	{
	public:
		GameSetting();
		DECLARE_SERIALIZE(GameSetting)
		
		void LoadConfig();
		//void InitDefault();

		void SetPkgVersion(int index, UInt32 version);
		/**
		@brief 初始化字项目信息
		**/
		void InitCurSubprojectInfo(int env);

		/**
		 @fn core::string GetPkgsVersionStr();
		 @brief 所有的Pkg版本组成的字符串
		 @returns The pkgs version string.
		 */

		core::string GetPkgsVersionStr();
		core::string GetTargetPkgsVersionStr();
		//资源要去下载的网址列表
		dynamic_array<RemoteUrlInfo>  m_RemoteUrlList;
		//资源下载的存储路径
		core::string m_RemoteDownPath;
		//热更服务器的信息
		core::string m_PkgServer;

		//首包
		GamePackageInfo  m_FirstPkg;
		GamePackageInfo  m_EnginePkg;
		//GamePackageInfo  m_CommonPkg;
		GamePackageInfo  m_StudioPkg;
		//GamePackageInfo  m_FirstPkgScript;
		//GamePackageInfo  m_UniverseFirstPkgScript;
		//其他热更包
		dynamic_array<GamePackageInfo>  m_PkgLists;
		//是否使用hotfix
		bool m_UseHotfix=false;
		//是否是发布给玩家的版本
		bool m_PublishForTarget = false;
		////Android保存PKG的模式， 0表示采用解压到包外的目录,app删除不删除， 1 表示解压到包内的目录，app删除，资源也跟着删除
		UInt32 m_AndroidPKGMode = 0;
		//子项目信息
		dynamic_array<SubprojectInfo>  m_SubprojectsInfo;
		//当前子项目信息
		SubprojectInfo m_CurSubprojectInfo;

		//隐藏ui模式下是否隐藏摇杆
		bool m_HideTouchRocker = false;
		//触控摇杆使用相对屏幕大小
		bool m_RockerUseRelSize = false;
		
		//修改下网络超时的注释，给lua的http接口扩展超时参数 code_by:huangfubin 2023.5.17
		// CURLOPT_CONNECTTIMEOUT：连接对方主机时的最长等待时间
		int  m_HttpConnectTimeout = 15;
		// CURLOPT_TIMEOUT：整个curl函数执行过程的最长等待时间，包含连接等待时间
		int  m_HttpTimeout = 30;	//原来的名字叫 m_HttpRespTimeout，避免理解歧义，改了
		bool m_EnableGameLanguage = true;

		int m_LowSpeedLimit = 0;
		int m_LowSpeedTime = 0;
		bool m_EnableShowPkgVersion = false;

		core::string m_EditorPkgPath;

		int m_ReportHttpTime = 0;
		core::string m_AddPackageFromSDCard;

	private:
		//这里的值和m_PkgLists的排列顺序有关系
		dynamic_array<UInt32> m_PkgVersionNum;

	};

	EXPORT_LEGACYMODULE GameSetting& GetGameSetting();
}