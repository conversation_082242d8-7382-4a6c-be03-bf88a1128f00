#pragma once
/*
*	file: SceneEffectLine
*	func: 在世界场景中绘制线条
*	by: chenzh
*	time: 2021.1.22
*/
#ifndef __SCENE_EFFECT_LINE_H__
#define __SCENE_EFFECT_LINE_H__

#include "SceneEffectGeom.h"
#include "world_types.h"

class World;

class EXPORT_SANDBOXENGINE SceneEffectLine : public SceneEffectGeom //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	SceneEffectLine();
	SceneEffectLine(const WCoord startpos, const WCoord endpos, int width = 12, 
		CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE, bool bNormalize = true, CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectLine();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;
	//tolua_end

	static void FillVertBuffer(BlockGeomVert& vert, const Rainbow::Vector3f& pos, float u, float v, BlockVector color = MakeBlockVector(255, 255, 255, 255));
	static void PushIndexBuffer(std::vector<unsigned short>& indices, unsigned short idx0, unsigned short idx1, unsigned short idx2);

	/* 设置绘制起始位置(角色坐标cm) */
	void SetDrawPos(const WCoord pos) { m_drawpos = pos; }

protected:

	void CalcVertexsAndIndices(const Rainbow::Vector3f& dim);

private:
	// 缓存
	WCoord m_drawpos;
	std::vector<BlockGeomVert> m_Vertices1; // 顶点缓存
	std::vector<BlockGeomVert> m_Vertices2;
	std::vector<BlockGeomVert> m_Vertices3;
	std::vector<BlockGeomVert> m_Vertices4;
	std::vector<unsigned short> m_Indices; // 索引缓存
}; //tolua_exports

#endif
