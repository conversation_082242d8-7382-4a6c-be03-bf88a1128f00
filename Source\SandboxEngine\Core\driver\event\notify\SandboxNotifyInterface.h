#pragma once
/**
* file : SandboxNotifyInterface
* func : 沙盒通知类
* by : chenzh
*/
#include "SandboxType.h"
#include "SandboxRuntimeClass.h"
#include "SandboxRef.h"
#include "SandboxAutoRef.h"
#include "SandboxWeakRef.h"

#include <set>


namespace MNSandbox {

	class NotifyInterface;

	static const int ms_ListenerDefaultSort = 100; // 默认值

	/**
	* 监听
	*/
	class EXPORT_SANDBOXDRIVERMODULE ListenerInterface : public Ref
	{
		DECLARE_REFCLASS(ListenerInterface)
	public:
		/* 监听类型 */
		enum class LISTENERTYPE
		{
			IDLE = 0,
			NORMAL, // 普通监听类型
			LUA, // lua监听类型
		};

	public:
		ListenerInterface();
		virtual ~ListenerInterface();

		virtual void Release() { ClearBindNotify(); }

		/* 类型 */
		//virtual const std::string& GetTypeName() = 0;

		/* 监听类型 */
		inline virtual LISTENERTYPE GetListenerType() { return m_listenerType; }

		/* 绑定多个通知 */
		bool CanBindMultiNotify() const { return m_bindMultiNotify; }
		void SetCanBindMultiNotify() { m_bindMultiNotify = true; }

		/* 是否有监听 */
		bool IsBindNotify() const { return !m_bindNotifys.empty(); }
		/* 是否绑定了指定监听 */
		bool IsBindNotify(NotifyInterface* notify) const
		{
			return m_bindNotifys.find(notify) != m_bindNotifys.end();
		}

		/* 设置监听 */
		void AddBindNotify(NotifyInterface* notify, int sort = ms_ListenerDefaultSort);
		void RemoveBindNotify(NotifyInterface* notify);
		void UpdateClearNotify(NotifyInterface* notify);

		/* 清除监听 */
		void ClearBindNotify();

		/* 设置运行次数 */
		void SetRunTimes(int cnt) { m_runtimes = cnt; }
		inline int GetRunTimes() const { return m_runtimes; }

		/* 消耗一次执行次数 */
		void CostRunTime();

	protected:
		// 监听类型
		LISTENERTYPE m_listenerType = LISTENERTYPE::IDLE;

	private:
		friend class TNotifyInterface;

		/* 是否支持绑定多个通知 */
		bool m_bindMultiNotify = false;
		/* 绑定通知 */
		std::set<NotifyInterface*> m_bindNotifys;
		/* 执行次数限制,-1表示不限制 */
		int m_runtimes = -1;
	};

	/**
	* 通知
	*/
	class EXPORT_SANDBOXDRIVERMODULE NotifyInterface : public noncopyable
	{
	private:
		static const ::MNSandbox::RuntimeClass m_RTTI;
	public:
		typedef NotifyInterface ThisType;
		static const ::MNSandbox::RuntimeClass* RTTI() { return &m_RTTI; }
		virtual const ::MNSandbox::RuntimeClass* GetRTTI() const { return &m_RTTI; }
	public:
		NotifyInterface();
		virtual ~NotifyInterface();

	protected:
		/* 订阅 */
		void _subscribe(const AutoRef<ListenerInterface>& listener, int sort=ms_ListenerDefaultSort);
		/* 取消订阅 */
		void _unsubscribe(const AutoRef<ListenerInterface>& listener);

		/* 监听 */
		virtual void OnAddListener(const AutoRef<ListenerInterface>& v, int sort) = 0;
		/* 取消监听 */
		virtual void OnRemoveListener(const AutoRef<ListenerInterface>& v) = 0;

		/* 检测监听是否监听的自己 */
		bool CheckListenSelf(AutoRef<ListenerInterface> v)
		{
			return v && v->IsBindNotify(this);
		}

	protected:
		friend class ListenerInterface;

	};

	////////////////////////////////////////////////////////////////////////////

	/**
	* 监听
	*/
	template<typename... Args>
	class Listener : public ListenerInterface
	{
		DECLARE_REFCLASS_TEMPLATEBASE(Listener)
	public:
		using Base = Listener;
	public:
		Listener()
		{
			m_listenerType = ListenerInterface::LISTENERTYPE::NORMAL;
		}
		virtual ~Listener() {};

		/* 执行 */
		bool Exec(const Args&... args)
		{
#if (defined SANDBOX_USE_PROFILE)
			if (m_useProfile)
			{
				Profile::ProfileCall<Profile::Manager::CallType::LISTENER> _profileCall;

				bool ret = Trigger(args...);
				if (GetRunTimes() > 0)
					CostRunTime();
				return ret;
			}
#endif
			bool ret = Trigger(args...);
			if (GetRunTimes() > 0)
				CostRunTime();
			return ret;
		}

		/* 自定义数据 */
		AutoRef<Ref> GetCustomData() { return m_customData; }
		void SetCustomData(AutoRef<Ref> data) { m_customData = data; }

	protected:
		/* 触发回调 */
		virtual bool Trigger(const Args&... args) = 0;

#if (defined SANDBOX_USE_PROFILE)
	public:
		bool m_useProfile = true;
#endif
	private:
		/* 自定义数据 */
		AutoRef<Ref> m_customData;
	};

	template<>
	class Listener<> : public ListenerInterface
	{
		DECLARE_REFCLASS_TEMPLATEBASE(Listener<>)
	public:
		using Base = Listener<>;
	public:
		Listener()
		{
			m_listenerType = ListenerInterface::LISTENERTYPE::NORMAL;
		}
		virtual ~Listener() {};

		/* 执行 */
		bool Exec()
		{
#if (defined SANDBOX_USE_PROFILE)
			if (m_useProfile)
			{
				Profile::ProfileCall<Profile::Manager::CallType::LISTENER> _profileCall;

				bool ret = Trigger();
				if (GetRunTimes() > 0)
					CostRunTime();
				return ret;
			}
#endif
			bool ret = Trigger();
			if (GetRunTimes() > 0)
				CostRunTime();
			return ret;
		}

		/* 自定义数据 */
		AutoRef<Ref> GetCustomData() { return m_customData; }
		void SetCustomData(AutoRef<Ref> data) { m_customData = data; }

	protected:
		/* 触发回调 */
		virtual bool Trigger() = 0;

#if (defined SANDBOX_USE_PROFILE)
	public:
		bool m_useProfile = true;
#endif
	private:
		/* 自定义数据 */
		AutoRef<Ref> m_customData;
	};
}