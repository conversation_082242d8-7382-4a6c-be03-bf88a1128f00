
#ifndef __ACHIEVEMENTDATA_H__
#define __ACHIEVEMENTDATA_H__
#include <vector>
#include "defdata.h"
//struct DeathStatistics
//{
//	int murdererid;
//	int deathnum;
//};
//
//struct DistanceStatistics
//{
//	int type;				//0���� 1��Ӿ 2ˤ�� 3���� 4���� 5ˮ���ƶ� 6���� 7����
//	int distance;
//};
//
//struct BiologyStatistics
//{
//	int biologyid;
//	int killnum;
//	int multiplynum;
//	int tamenum;
//};
//
//struct ItemStatistics
//{
//	int itemid;
//	int pickupnum;
//	int syntheticnum;
//	int usenum;
//	int consumenum;
//	int collectnum;
//	
//};

//tolua_begin
enum
{
	LOCK,	//δ����
	UNLOCK_UNACTIVATE,	//����δ����
	ACTIVATE_UNCOMPLETE,	//����δ���
	ACTIVATE_COMPLETE		//���������
};

enum
{
	REWARD_UNRECEIVE,	//������ȡ
	REWARD_CAN_RECEIVE,	//����ȡ
	REWARD_RECEIVED		//�Ѿ���ȡ
};

struct GameStatistics
{
	int goal;			//ͳ�Ƶ�Ŀ����Ϊ
	int id;			
	int num;			//ͳ�Ƶ�����
};
//tolua_end

struct EXPORT_SANDBOXENGINE AchievementInfo;
//tolua_begin
struct AchievementInfo
{
	const AchievementDef *achievementDef;
	int achievementState;			//0δ���� 1����δ���� 2����δ��� 3���������
	int rewardState;				//0������ȡ 1����ȡ 2����ȡ
	int arryNum;					//�ɾ�����ɵ��������Ϊ������
	int completeYear;	// ���ʱ��
	unsigned char completeMonth;
	unsigned char completeDay;
};
//tolua_end

#endif