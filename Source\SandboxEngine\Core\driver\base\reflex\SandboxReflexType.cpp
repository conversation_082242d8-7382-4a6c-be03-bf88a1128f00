/**
* file : SandboxReflexType
* func : 反射数据类型
* by : chenzh
*/
#include "base/reflex/SandboxReflexType.h"
#include "util/SandboxUnit.h"
#include "base/stream/SandboxStreamBuffer.h"
#include <string>


namespace MNSandbox {


	ReflexType* ReflexType::Idle = &ReflexType::GetSingleton<void>();


	ReflexTypePolicy::ReflexTypePolicy()
	{
        m_cbSerialize = &ReflexPolicyFuncBase::CallbackSerialize;
        m_cbUnserialize = &ReflexPolicyFuncBase::CallbackUnserialize;
        m_cbLuaToC = &ReflexPolicyFuncBase::CallbackLuaToC;
        m_cbLuaPushC = &ReflexPolicyFuncBase::CallbackLuaPushC;
        m_cbToString = &ReflexPolicyFuncBase::CallbackToString;
        m_fSizeOf = &ReflexPolicyFuncBase::SizeOf;
        m_fStructure = &ReflexPolicyFuncBase::Structure;
        m_fDestructor = &ReflexPolicyFuncBase::Destructor;
        m_fIsSame = &ReflexPolicyFuncBase::IsSame;
        m_fCopy = &ReflexPolicyFuncBase::Copy;
        m_fToEnumVal = &ReflexPolicyFuncBase::ToEnumVal;
        m_fGetEnumVal = &ReflexPolicyFuncBase::GetEnumVal;
		m_cbReflexToBinary = &ReflexPolicyFuncBase::ReflexToBinary;
		m_cbReflexFromBinary = &ReflexPolicyFuncBase::ReflexFromBinary;
		m_versionCompatibility = nullptr;
	}

	ReflexTypePolicy& ReflexTypePolicy::operator=(const ReflexTypePolicy& src)
	{
		m_cbSerialize = src.m_cbSerialize;
		m_cbUnserialize = src.m_cbUnserialize;
		m_cbLuaToC = src.m_cbLuaToC;
		m_cbLuaPushC = src.m_cbLuaPushC;
		m_cbToString = src.m_cbToString;
		m_fSizeOf = src.m_fSizeOf;
		m_fStructure = src.m_fStructure;
		m_fDestructor = src.m_fDestructor;
		m_fCopy = src.m_fCopy;
		m_fIsSame = src.m_fIsSame;
		m_fToEnumVal = src.m_fToEnumVal;
		m_fGetEnumVal = src.m_fGetEnumVal;
		m_cbReflexToBinary = src.m_cbReflexToBinary;
		m_cbReflexFromBinary = src.m_cbReflexFromBinary;
		m_versionCompatibility = src.m_versionCompatibility;

		// cast to
		size_t cnt = m_groupCastTo.size();
		m_groupCastTo.insert(src.m_groupCastTo.begin(), src.m_groupCastTo.end());
		SANDBOX_ASSERT(m_groupCastTo.size() == cnt + src.m_groupCastTo.size()); // 不能重复
		return *this;
	}

	bool ReflexTypePolicy::New(void** ptr, void* (*fMalloc)(unsigned)) const
	{
		auto size = m_fSizeOf();
		if (size == 0)
		{
			return false;
		}

		if (fMalloc)
		{
			*ptr = fMalloc(size);
		}
		else
		{
			*ptr = SANDBOX_MALLOC(size);
		}
		m_fStructure(*ptr);
		return true;
	}

	void ReflexTypePolicy::Delete(void* ptr, void(*fFree)(void*)) const
	{
		if (ptr)
		{
			m_fDestructor(ptr);
			if (fFree)
			{
				fFree(ptr);
			}
			else
			{
				SANDBOX_FREE(ptr);
			}
			ptr = nullptr;
		}
	}

	void ReflexTypePolicy::SerializeEx(const ReflexType* reftype, const void* data, MNJsonVal& out) const
	{
		m_cbSerialize(data, out);

		if (out.is<MNJsonObject>())
		{
			auto& jsonobj = out.get<MNJsonObject>();
			jsonobj << "__reflextype" << reftype->GetName();
		}
	}

	size_t ReflexTypePolicy::ReflexToBinaryEx(const ReflexType* reftype, const void* data, const AutoRef<Stream>& out) const
	{
		if (!IsEnableReflexToBinary())
		{
			SANDBOX_ASSERT(false);
			return Stream::Error;
		}

		size_t len = 0;

		unsigned short eType = static_cast<unsigned short>(reftype->GetReflexTypeEnum());
		SANDBOX_ASSERT(eType != REFLEXTYPEENUM_IDLE);
		len += out->WriteNumber<unsigned short>(eType);

		size_t vLen = m_cbReflexToBinary(data, out);
		if (vLen == Stream::Error)
			return Stream::Error;
		len += vLen;
		return len;
	}

	bool ReflexTypePolicy::ReflexFromBinaryEx(const ReflexType* reftype, void* data, const AutoRef<Stream>& in) const
	{
		if (!IsEnableReflexFromBinary())
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		unsigned short eType;
		if (!in->ReadNumber<unsigned short>(eType))
			return false;
		if (reftype && reftype->GetReflexTypeEnum() != eType)
			return false;

		return m_cbReflexFromBinary(data, in);
	}

	bool ReflexTypePolicy::ReflexFromBinary(void* data, const AutoRef<Stream>& in) const
	{
		if (!IsEnableReflexFromBinary())
			return false;

		return m_cbReflexFromBinary(data, in);
	}

	bool ReflexTypePolicy::CanCastTo(const ReflexType* dstType) const
	{
		return m_groupCastTo.find(dstType) != m_groupCastTo.end();
	}

	bool ReflexTypePolicy::CastTo(const ReflexType* dstType, const void* src, void* dst) const
	{
		auto iter = m_groupCastTo.find(dstType);
		if (iter == m_groupCastTo.end())
			return false;

		return (iter->second)(src, dst);
	}

	void ReflexTypePolicy::RegCastTo(const ReflexType* dstType, FuncCastTo callback)
	{
		SANDBOX_ASSERT(m_groupCastTo.find(dstType) == m_groupCastTo.end());
		m_groupCastTo[dstType] = callback;
	}

	const ReflexType* ReflexTypePolicy::GetReflexTypeByVersion(unsigned version) const
	{
		const ReflexType* type = m_versionCompatibility ? m_versionCompatibility(version) : ReflexType::Idle;
		if (!type)
			return ReflexType::Idle;
		return type;
	}

	///////////////////////////////////////////////////////////////////////////

	ReflexType::ReflexType_Ins::~ReflexType_Ins()
	{
		if (_needDel)
		{
			SANDBOX_ORIGINAL_DELETE(_ins);
		}
	}

	void ReflexType::ReflexType_Ins::InitByTypeName(const std::string& defname)
	{
		_ins = Container::Get().GetTypeByName(defname);
		if (!_ins || _ins == ReflexType::Idle)
		{
			_ins = SANDBOX_ORIGINAL_NEW(ReflexType);
			_needDel = true;
			Container::Get().AddReflexType(defname, _ins);
		}
	}

	///////////////////////////////////////////////////////////////////////////

	void ReflexType::SetName(const std::string& name)
	{
		SANDBOX_ASSERT(m_name.empty() && !name.empty());
		m_name = name;
		Container::Get().AddReflexType(m_name, this);
	}

	void ReflexType::SetReflexTypeEnum(REFLEXTYPEENUM typeEnum)
	{
		SANDBOX_ASSERT(typeEnum != REFLEXTYPEENUM_IDLE);
		m_typeEnum = typeEnum;
		Container::Get().AddReflexTypeEnum(typeEnum, this);
	}

	///////////////////////////////////////////////////////////////////////////

	ReflexType::Container& ReflexType::Container::Get()
	{
		static ReflexType::Container ms_container;
		return ms_container;
	}

	ReflexType* ReflexType::Container::GetTypeByName(const std::string& tName)
	{
		auto iter = m_datas.find(tName);
		return iter != m_datas.end() ? iter->second : ReflexType::Idle;
	}

	ReflexType* ReflexType::Container::GetTypeByEnum(REFLEXTYPEENUM typeEnum)
	{
		auto iter = m_enum2Type.find(typeEnum);
		return iter != m_enum2Type.end() ? iter->second : ReflexType::Idle;
	}

	void ReflexType::Container::AddReflexType(const std::string& tName, ReflexType* reflextype)
	{
		//SANDBOX_ASSERT(m_datas.find(tName) == m_datas.end());
		auto iter = m_datas.find(tName);
		if (iter != m_datas.end())
		{
			SANDBOX_ASSERT(iter->second == reflextype);
		}
		else
		{
			m_datas.insert(std::make_pair(tName, reflextype));
		}
	}

	void ReflexType::Container::AddReflexTypeEnum(REFLEXTYPEENUM typeEnum, ReflexType* reflextype)
	{
		auto iter = m_enum2Type.find(typeEnum);
		if (iter != m_enum2Type.end())
		{
			SANDBOX_ASSERT(iter->second == reflextype || typeEnum == REFLEXTYPEENUM_PTR_NODE);		//SandboxNode 的子类指针会冲突
		}
		else
		{
			m_enum2Type.insert(std::make_pair(typeEnum, reflextype));
		}
	}
}