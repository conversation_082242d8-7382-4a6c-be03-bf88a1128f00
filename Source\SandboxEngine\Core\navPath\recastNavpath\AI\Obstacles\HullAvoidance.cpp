
#include "./HullAvoidance.h"
#include "Geometry/Intersection.h"


static inline Rainbow::Vector2f ExtractXZVector2(const Rainbow::Vector3f& v)
{
    return Rainbow::Vector2f(v.x, v.z);
}

static inline bool Between(float value, float minValue, float maxValue)
{
    DebugAssert(minValue <= maxValue);
    return value > minValue && value < maxValue;
}

static inline void OrderVerticesByY(Rainbow::Vector3f* miny, Rainbow::Vector3f* maxy)
{
    DebugAssert(miny != maxy);
    DebugAssert(miny);
    DebugAssert(maxy);

    if (maxy->y < miny->y)
    {
        Rainbow::Vector3f temp = *miny;
        *miny = *maxy;
        *maxy = temp;
    }
}

static inline Rainbow::Vector2f SegmentNormal(const Rainbow::Vector2f& s0, const Rainbow::Vector2f& s1)
{
    const Rainbow::Vector2f ds = s1 - s0;
    return NormalizeSafe(Rainbow::Vector2f(-ds.y, ds.x));
}

static inline void DisplaceSegment(Rainbow::Vector2f& s0, Rainbow::Vector2f& s1, float distance)
{
    const Rainbow::Vector2f displacement = SegmentNormal(s0, s1) * distance;
    s0 = s0 + displacement;
    s1 = s1 + displacement;
}

int CalculateClippedBoxConvexHull(Rainbow::Vector3f* hullVertices, const Rainbow::Vector3f* boxVertices,
    float slabMinY, float slabMaxY)
{
    Vertex2Array points(12, kMemTempAlloc);
    Vertex2Array hull(13, kMemTempAlloc);

    CalculatePointsFromClippedBox(points, boxVertices, slabMinY, slabMaxY);
    CalculateConvexHull(hull, points);

    for (int i = 0; i < hull.size(); ++i)
        hullVertices[i] = Rainbow::Vector3f(hull[i].x, slabMinY, hull[i].y);

    return hull.size();
}

int CalculateExpandedClippedBoxConvexHull(Rainbow::Vector2f* segments2d, Rainbow::Vector2f* corners2d,
    const Rainbow::Vector3f* boxVertices,
    float slabMinY, float slabMaxY, float radius)
{
    Vertex2Array points(12, kMemTempAlloc);
    Vertex2Array hull(13, kMemTempAlloc);

    CalculatePointsFromClippedBox(points, boxVertices, slabMinY, slabMaxY);
    CalculateConvexHull(hull, points);
    if (hull.size() < 3)
        return 0;

    for (int i = 0, j = hull.size() - 1; i < hull.size(); j = i++)
    {
        Rainbow::Vector2f s0 = hull[j];
        Rainbow::Vector2f s1 = hull[i];
        DisplaceSegment(s0, s1, radius);

        segments2d[2 * j + 0] = s0;
        segments2d[2 * j + 1] = s1;

        corners2d[j] = hull[j];
    }

    return hull.size();
}

bool AlignedCylinderOverlapsOrientedBox(float* penetration, const Rainbow::Vector3f* boxVertices,
    const Rainbow::Vector3f& cylinderPosition, float cylinderRadius,
    float cylinderMinY, float cylinderMaxY)
{
    Vertex2Array points(12, kMemTempAlloc);
    Vertex2Array hull(13, kMemTempAlloc);

    const Rainbow::Vector2f cylinderPositionXZ = ExtractXZVector2(cylinderPosition);

    CalculatePointsFromClippedBox(points, boxVertices, cylinderMinY, cylinderMaxY);
    CalculateConvexHull(hull, points);
    if (hull.size() < 3)
    {
        *penetration = 0.0f;
        return false;
    }
    return CircleHullOverlap(penetration, hull, cylinderPositionXZ, cylinderRadius);
}

bool AlignedCylinderOverlapsOrientedCapsule(float* penetration, const Rainbow::Vector3f& capsuleCenter, const Rainbow::Vector3f& capsuleExtents, const Rainbow::Vector3f& capsuleAxis,
    const Rainbow::Vector3f& cylinderPosition, float cylinderRadius, float cylinderMinY, float cylinderMaxY)
{
    float capsuleRadius;
    Rainbow::Vector2f points[2];

    *penetration = 0.0f;

    int ncircles = CalculateClippedCapsule(points, &capsuleRadius, capsuleCenter, capsuleExtents, capsuleAxis, cylinderMinY, cylinderMaxY);
    if (ncircles == 0)
        return false;

    const Rainbow::Vector2f pos = ExtractXZVector2(cylinderPosition);
    const Rainbow::Vector2f cap1 = points[0];

    if (ncircles == 1)
    {
        const float separation = Magnitude(pos - cap1) - capsuleRadius - cylinderRadius;
        if (separation > 0.0f)
            return false;

        *penetration = -separation;
        return true;
    }

    const Rainbow::Vector2f cap2 = points[1];
    if (ncircles == 2)
    {
        const float separation = DistancePointLine(pos, cap1, cap2) - capsuleRadius - cylinderRadius;
        if (separation > 0.0f)
            return false;

        *penetration = -separation;
        return true;
    }

    return false;
}

// Assumes Z-order vertex indexing
//     6      7
//     +-----+
//   4/|   5/|
//   +-----+ |
//   | +---|-+
//   |/ 2  |/ 3
//   +-----+
//  0      1
//
static inline void GetBoxEdgeVertices(const Rainbow::Vector3f* boxVertices, int i, Rainbow::Vector3f* edgeVertex1, Rainbow::Vector3f* edgeVertex2)
{
    DebugAssert(i >= 0 && i < 12);
    DebugAssert(edgeVertex1);
    DebugAssert(edgeVertex2);

    const int v0[] =  {0, 1, 3, 2, 4, 5, 7, 6, 0, 1, 2, 3};
    const int v1[] =  {1, 3, 2, 0, 5, 7, 6, 4, 4, 5, 6, 7};

    *edgeVertex1 = boxVertices[v0[i]];
    *edgeVertex2 = boxVertices[v1[i]];
}

// Clip 3d oriented box by y-slab and return the 2d positions internal to slab
// useful for creating the convex hull of the shape inside slab
void CalculatePointsFromClippedBox(Vertex2Array& points, const Rainbow::Vector3f* box, float slabMinY, float slabMaxY)
{
    points.resize_uninitialized(0);
    points.reserve(12);

    // Start by adding vertices in the slab
    for (int i = 0; i < 8; ++i)
    {
        if (Between(box[i].y, slabMinY, slabMaxY))
        {
            points.push_back(ExtractXZVector2(box[i]));
        }
    }

    // Add the intersection of straddling edges of the cube
    for (int i = 0; i < 12; ++i)
    {
        Rainbow::Vector3f edgeVertex1, edgeVertex2;

        GetBoxEdgeVertices(box, i, &edgeVertex1, &edgeVertex2);
        OrderVerticesByY(&edgeVertex1, &edgeVertex2);

        if (Between(slabMaxY, edgeVertex1.y, edgeVertex2.y))
        {
            const float t = (slabMaxY - edgeVertex1.y) / (edgeVertex2.y - edgeVertex1.y);
            const Rainbow::Vector3f intersection = Lerp(edgeVertex1, edgeVertex2, t);
            points.push_back(ExtractXZVector2(intersection));
        }

        if (Between(slabMinY, edgeVertex1.y, edgeVertex2.y))
        {
            const float t = (slabMinY - edgeVertex1.y) / (edgeVertex2.y - edgeVertex1.y);
            const Rainbow::Vector3f intersection = Lerp(edgeVertex1, edgeVertex2, t);
            points.push_back(ExtractXZVector2(intersection));
        }
    }
}

static bool CompareVertices(const Rainbow::Vector2f& a, const Rainbow::Vector2f& b)
{
    return a.x < b.x || (a.x == b.x && a.y < b.y);
}

static inline float CalculatePointSide(const Rainbow::Vector2f& l0, const Rainbow::Vector2f& l1, const Rainbow::Vector2f& point)
{
    return (l1.y - l0.y) * (point.x - l0.x) - (l1.x - l0.x) * (point.y - l0.y);
}

// Creates convex hull from points - side-effect: sorts the input points
void CalculateConvexHull(Vertex2Array& hull, Vertex2Array& points)
{
    // TODO : prune (near) duplicate points before calculating hull
    hull.resize_uninitialized(0);
    if (points.empty())
        return;

    hull.reserve(points.size() + 1);
    std::sort(points.begin(), points.end(), CompareVertices);

    // Andrews monotone chain
    for (int i = 0; i < points.size(); ++i)
    {
        while (hull.size() >= 2 && CalculatePointSide(hull[hull.size() - 2], hull[hull.size() - 1], points[i]) <= 0)
            hull.pop_back();
        hull.push_back(points[i]);
    }

    for (int i = points.size() - 2, j = hull.size() + 1; i >= 0; --i)
    {
        while (hull.size() >= j && CalculatePointSide(hull[hull.size() - 2], hull[hull.size() - 1], points[i]) <= 0)
            hull.pop_back();
        hull.push_back(points[i]);
    }
    hull.pop_back();
}

static inline Rainbow::Vector2f AverageVertices(const Vertex2Array& points)
{
    const size_t pointCount = points.size();
    DebugAssert(pointCount > 0);

    Rainbow::Vector2f pointSum = points[0];
    for (size_t i = 1; i < pointCount; ++i)
    {
        pointSum += points[i];
    }
    return pointSum * (1.0f / pointCount);
}

static inline float ParallelogramArea(const Rainbow::Vector2f& p0, const Rainbow::Vector2f& p1, const Rainbow::Vector2f& p2)
{
    const Rainbow::Vector2f d1 = p1 - p0;
    const Rainbow::Vector2f d2 = p2 - p0;
    return d1.x * d2.y - d2.x * d1.y;
}

static inline float DistanceSqr(const Rainbow::Vector2f& u, const Rainbow::Vector2f& v)
{
    return Rainbow::SqrMagnitude(u - v);
}

static inline float LinePointSqrDistance(const Rainbow::Vector2f& l0, const Rainbow::Vector2f& l1, const Rainbow::Vector2f& p)
{
    const Rainbow::Vector2f dl = l1 - l0;
    const Rainbow::Vector2f dp = p - l0;
    const float lineSqrLength = Dot(dl, dl);
    if (lineSqrLength == 0.0f)
        return DistanceSqr(l0, p);

    const float t = Dot(dp, dl) / lineSqrLength;
    if (t <= 0.0f)
        return DistanceSqr(l0, p);

    if (t >= 1.0f)
        return DistanceSqr(l1, p);

    const Rainbow::Vector2f closestPointOnLineSegment = Lerp(l0, l1, t);
    return DistanceSqr(closestPointOnLineSegment, p);
}

bool CircleHullOverlap(float* penetration, const Vertex2Array& hull, const Rainbow::Vector2f& circlePosition, float circleRadius)
{
    const size_t hullSize = hull.size();
    DebugAssert(hullSize > 2);
    *penetration = 0.0f;

    Rainbow::Vector2f simpleCentroid = AverageVertices(hull);

    for (int i = 0, j = hullSize - 1; i < hullSize; j = i++)
    {
        if (ParallelogramArea(simpleCentroid, hull[j], circlePosition) > 0
            || ParallelogramArea(simpleCentroid, hull[i], circlePosition) < 0)
            continue;

        const float linePointSqrDistance = LinePointSqrDistance(hull[j], hull[i], circlePosition);
        if (ParallelogramArea(hull[j], hull[i], circlePosition) < 0)
        {
            *penetration = Rainbow::Sqrt(linePointSqrDistance) + circleRadius;
            return true;
        }

        if (linePointSqrDistance >= circleRadius * circleRadius)
        {
            *penetration = 0.0f;
            return false;
        }

        *penetration = circleRadius - Rainbow::Sqrt(linePointSqrDistance);
        return true;
    }

    DebugAssert(false);  // Degenerate hull (e.g. non-convex)
    return false;
}

int CalculateClippedCapsule(Rainbow::Vector2f* points, float* radius,
    const Rainbow::Vector3f& capsuleCenter, const Rainbow::Vector3f& capsuleExtents, const Rainbow::Vector3f& capsuleAxis,
    float slabMinY, float slabMaxY)
{
    DebugAssert(slabMaxY > slabMinY);
    float capRadius = 0.0f, capHeight = 0.0f;
    FitCapsuleToExtents(&capRadius, &capHeight, capsuleExtents);

    Rainbow::Vector3f capUpAxis(capsuleAxis);
    if (capUpAxis[1] < 0)
        capUpAxis = -capUpAxis;

    const float capMinY = capsuleCenter.y - capHeight * capUpAxis.y;
    const float capMaxY = capsuleCenter.y + capHeight * capUpAxis.y;

    const Rainbow::Vector2f endLo = ExtractXZVector2(capsuleCenter - capHeight * capUpAxis);
    const Rainbow::Vector2f endHi = ExtractXZVector2(capsuleCenter + capHeight * capUpAxis);

    // Early out for y-axis aligned capsule
    // also preventing division by zero.
    bool axisAligned = capUpAxis.y > 0.99f;

    if (capMinY > slabMaxY)
    {
        const float hMin = capMinY - slabMaxY;
        if (hMin > capRadius)
            return 0;

        // Capsule lower sphere straddles upper slab plane
        points[0] = endLo;
        *radius = sqrtf(capRadius * capRadius - hMin * hMin);

        if (axisAligned)
            return 1;

        const float hMax = capMaxY - slabMaxY;
        if (hMax < capRadius)
        {
            // Upper sphere straddles upper slab plane
            // Keep existing (largest) radius
            points[1] = endHi;
        }
        else
        {
            // Upper sphere outside upper slab plane find the fudge projected position
            const float fudgedDisplacementY = capRadius * (1.0f - capUpAxis.y);
            const float t = clamp01((hMin + fudgedDisplacementY) / (capMaxY - capMinY));
            Rainbow::Vector2f capHi = Lerp(endLo, endHi, t);
            points[1] = capHi;
        }
        return 2;
    }

    if (capMaxY < slabMinY)
    {
        const float hMax = slabMinY - capMaxY;
        if (hMax > capRadius)
            return 0;

        // Capsule upper sphere straddles lower slab plane
        points[0] = endHi;
        *radius = sqrtf(capRadius * capRadius - hMax * hMax);

        if (axisAligned)
            return 1;

        const float hMin = slabMinY - capMinY;
        if (hMin < capRadius)
        {
            // Lower sphere straddles lower slab plane
            // Keep existing (largest) radius
            points[1] = endLo;
        }
        else
        {
            // Lower sphere outside lower slab plane find the fudge projected position
            const float fudgedDisplacementY = capRadius * (1.0f - capUpAxis.y);
            const float t = clamp01((hMin - fudgedDisplacementY) / (capMaxY - capMinY));
            Rainbow::Vector2f capLo = Lerp(endLo, endHi, t);
            points[1] = capLo;
        }
        return 2;
    }

    Rainbow::Vector2f capLo = endLo;
    if (capMinY < slabMinY)
    {
        const float fudgedDisplacementY = capRadius * (1.0f - capUpAxis.y);
        const float t = clamp01((slabMinY - capMinY - fudgedDisplacementY) / (capMaxY - capMinY));
        capLo = Lerp(endLo, endHi, t);
    }

    Rainbow::Vector2f capHi = endHi;
    if (capMaxY > slabMaxY)
    {
        const float fudgedDisplacementY = capRadius * (1.0f - capUpAxis.y);
        const float t = clamp01((slabMaxY - capMinY + fudgedDisplacementY) / (capMaxY - capMinY));
        capHi = Lerp(endLo, endHi, t);
    }

    points[0] = capLo;
    points[1] = capHi;

    *radius = capRadius;

    return 2;
}

void FitCapsuleToExtents(float* radius, float* height, const Rainbow::Vector3f& capsuleExtents)
{
    const float r = std::max(capsuleExtents.x, capsuleExtents.z);
    *radius = r;
    *height = std::max(0.0f, capsuleExtents.y - r);
}

void CalcCapsuleWorldExtents(Rainbow::Vector3f* worldExtents, const Rainbow::Vector3f& localExtents, const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis)
{
    float radius = 0, height = 0;
    FitCapsuleToExtents(&radius, &height, localExtents);
    *worldExtents = Rainbow::Abs(yAxis) * height + Rainbow::Vector3f(radius, radius, radius);
}

void CalcBoxWorldExtents(Rainbow::Vector3f* worldExtents, const Rainbow::Vector3f& localExtents, const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis)
{
    *worldExtents = Rainbow::Abs(xAxis) * localExtents.x + Rainbow::Abs(yAxis) * localExtents.y + Rainbow::Abs(zAxis) * localExtents.z;
}

void CalculateOrientedBoxCorners(Rainbow::Vector3f* cornerVertices, const Rainbow::Vector3f& position, const Rainbow::Vector3f& extents,
    const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis)
{
    for (int i = 0; i < 8; ++i)
    {
        Rainbow::Vector3f corner = position;
        corner += xAxis * ((i & 1) ? extents.x : -extents.x);
        corner += yAxis * ((i & 2) ? extents.y : -extents.y);
        corner += zAxis * ((i & 4) ? extents.z : -extents.z);
        cornerVertices[i] = corner;
    }
}
