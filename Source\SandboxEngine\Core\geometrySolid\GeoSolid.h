#ifndef __GeoSolid_h__
#define __GeoSolid_h__ 1

#include "nodes/SandboxSurfaceObject.h"
#include "geometrySolid/GeoSolidEnum.h"
#include "geometrySolid/GeometryMath.h"
#include "ThreadFlyweightPattern.h"
#include "nodes/service/MeshCacheManager.h"

#include "flatbuffers/flatbuffers.h"
#include "proto_fb/FlatSaveCommon_generated.h"

#include "Math/Color.h"
#include "BaseClass/SharePtr.h"
#include "Graphics/Mesh/Mesh.h"

#include <string>
#include <list>

#if !GEO_SOLID_RECYCLE
#define DECLARE_NON_FLYWEIGHT_PATTERN(classname) \
public: \
	void onRecycle(); \
    static void recycle(classname*&); \
    static classname* obtain(); \
    static void releaseAll() {} \
private: \
    classname* next; \
	static int s_PoolSize; \
	static int s_MaxPoolSize; \
    static classname* head;


#define IMPLEMENT_NON_FLYWEIGHT_PATTERN(classname) \
int classname::s_PoolSize = 0; \
classname* classname::head = 0; \
void classname::recycle(classname*& instance) { \
	delete (instance); \
	instance = nullptr; \
} \
classname* classname::obtain() { \
    classname* cur = new (classname); \
    return cur; \
}

#endif

namespace Rainbow {
	class MeshRenderer;
}

namespace FBSave {
	class GeoSolidTriangle;
}

namespace MNSandbox {
    namespace GeometrySolid {
		//TODO: 2024-01-31 17:41:02: 精度丢失，对fbs文件内部使用double和旧版float的分支
		inline FBSave::Vec2 Vector2ToVec2(const GsVector2& c)
		{
			return FBSave::Vec2(c.x, c.y);
		}

		inline GsVector2 Vec2ToVector2(const FBSave::Vec2* c)
		{
			if (c)
				return GsVector2(c->x(), c->y());
			else
				return GsVector2::zero;
		}

		inline FBSave::Vec3 Vector3ToVec3(const GsVector3& c)
		{
			return FBSave::Vec3(c.x, c.y, c.z);
		}

		inline GsVector3 Vec3ToVector3(const FBSave::Vec3* c)
		{
			if (c)
				return GsVector3(c->x(), c->y(), c->z());
			else
				return GsVector3::zero;
		}

		inline FBSave::Mat3x3f Matrix3x3ToFbsMat3x3f(const GsMatrix3x3& m)
		{
			return FBSave::Mat3x3f(
				m.m_Data33[0][0], m.m_Data33[0][1], m.m_Data33[0][2],
				m.m_Data33[1][0], m.m_Data33[1][1], m.m_Data33[1][2],
				m.m_Data33[2][0], m.m_Data33[2][1], m.m_Data33[2][2]
			);
		}

		inline GsMatrix3x3 FbsMat3x3ToMatrix3x3(const FBSave::Mat3x3f* m)
		{
			if (m)
				//这个构造函数用的列优先，存储为行优先时，需要转置
				return GsMatrix3x3(
					m->_11(), m->_21(), m->_31(),
					m->_12(), m->_22(), m->_32(),
					m->_13(), m->_23(), m->_33()
				);
			else
				return GsMatrix3x3::identity;
		}

		struct PrimitiveVertex
		{
			Rainbow::Vector3f pos;
			Rainbow::Vector3f normal;
			Rainbow::ColorRGBA32 color;
			Rainbow::Vector2f uv;
			
			PrimitiveVertex()
			{
			}

			PrimitiveVertex(const GsVector3& p, const GsVector3& n, 
				const Rainbow::ColorRGBA32& c, const GsVector2& u)
			{
				pos = p.ToFloat();
				normal = n.ToFloat();
				color = c;
				uv = u.ToFloat();
			}
		};

		class GeoSolid;

		/**
		@brief	在开始复制数据阶段可知，不需计算
		 */
		struct GeoSolidInteractState {
			/**
			@brief	在开始复制数据阶段可知，不需计算
			 */
			bool from : 1;
			/**
			@brief	包含点重合、点在三角形边上、点在三角形内的情况
			 */
			bool inside : 1;
			/**
			@brief	完全重合，或部分面重合
			 */
			bool coincide : 1;
			bool hasCheckedInside : 1;
			bool hasCheckedCoincide : 1;
			GeoSolidInteractState();
			inline void operator|=(const GeoSolidInteractState& right)
			{
				from |= right.from;
				inside |= right.inside;
				coincide |= right.coincide;
				hasCheckedInside |= right.hasCheckedInside;
				hasCheckedCoincide |= right.hasCheckedCoincide;
			}
			void reset()
			{
				from = false;
				inside = false;
				coincide = false;
				hasCheckedInside = false;
				hasCheckedCoincide = false;
			}
		};

		struct GeoSolidVertexNeighbor
		{
			GsIv iv;
			//TODO:暂时无用
			float d;
		};
		class GeoSolidVertex
		{
		public:
			GsVector3 v;
			/**
			@brief	未经旋转变化的，在Src的坐标系下的坐标值
			 */
			GsVector3 vInSrc;
			/**
			@brief	未经旋转变化的，在Cut的坐标系下的坐标值
			 */
			GsVector3 vInCut;
			GeoSolidInteractState intersectSrc;
			GeoSolidInteractState intersectCut;
			bool isDrawn : 1;
			/**
			@brief	邻点，按距离从小到大排序
					运行时数据
			 */
			std::list<GeoSolidVertexNeighbor> listNeighbors;
		private:
			GeoSolidVertex();
		public:
			~GeoSolidVertex();
		private:
			void Copy(const GeoSolidVertex& gsv);
			void Set(const GsVector3& v0, const bool fromSrc, const bool fromCut);
			void Set(const GeoSolidVertex& gsv);
			void Set(const GeoSolidVertex* gsv);
			GeoSolidVertex& operator= (const GeoSolidVertex& gsv);
			#if GEO_SOLID_RECYCLE
			DECLARE_THREAD_FLYWEIGHT_PATTERN(GeoSolidVertex)
			#else
			DECLARE_NON_FLYWEIGHT_PATTERN(GeoSolidVertex)
			#endif
			friend class GeoSolid;
		};

		//没有方向
		class GeoSolidEdge
		{
		public:
			GsIv iv0;
			GsIv iv1;
			GeoSolidInteractState intersectSrc;
			GeoSolidInteractState intersectCut;
			bool isDrawn;
		private:
			GeoSolidEdge();
		public:
			~GeoSolidEdge();
			bool Is(const GsIv& iv0, const GsIv& iv1) const;
		private:
			void Copy(const GeoSolidEdge& gse);
			void Set(const GsIv& iv0, const GsIv& iv1);
			void Set(const GsIv& iv0, const GsIv& iv1, const bool src, const bool cut);
			void Set(const GeoSolidEdge& gse);
			void Set(const GeoSolidEdge* gse);
			GeoSolidEdge& operator= (const GeoSolidEdge& gse);
			#if GEO_SOLID_RECYCLE
			DECLARE_THREAD_FLYWEIGHT_PATTERN(GeoSolidEdge)
			#else
			DECLARE_NON_FLYWEIGHT_PATTERN(GeoSolidEdge)
			#endif
			friend class GeoSolid;
		};

		//TODO: 2023-11-10 15:19:13: 优化：记录邻三角形以更快地拓扑
		class GeoSolidTriangle
		{
		public:
			/**
			@brief  以012的顺序绘制，在算法计算中分别对应A、B、C三个点。
					渲染使用
			 */
			GsIv iv0;
			GsIv iv1;
			GsIv iv2;
			/**
			@brief  在算法计算中分别对应AB、AC、BC三条线段。
					正式运行中为运行时数据
					若打开调试，保留线段以渲染展示
					渲染使用
			 */
			GsIe ie0;
			GsIe ie1;
			GsIe ie2;
			/**
			@brief	//TODO: 2023-10-24 16:48:45: 待更新的优化注释
					UV优化：
					index以半个float为大小
					每个三角形从3 * 2个float（6个4Bytes）变成3个UInt32（3个4Bytes）
					每个四边形（含两个三角形的面）从6 * 2个float（12个4Bytes）变成6个UInt32（6个4Bytes）
					有优化的类型：
						立方体（少6*9Bytes）
						三棱柱（少(3*9+2*1.5)Bytes）
						圆柱[少2(UInt32) * 2(底面) * (2n/3 - 2)个float]
						圆锥[少2 * (2n/3 - 3)个float]与组合模型有优化
						球（m层n瓣）[侧面的每个四边形少2个float(6float变成3float+2UInt32)总计2(m-2)n；极点三角形每个多出1个UInt32，总计n个。总体减少(2m-3)n个float]
					未优化的类型：
						四棱锥（多2 * 3个float）
					不包括dynamic_array的开销
					渲染使用
			*/
			UInt32 iuv0;
			UInt32 iuv1;
			UInt32 iuv2;
			/**
			@brief	法线优化：
					index以半个float为大小
					有优化的类型：
						立方体（少2 * 6个float）
						三棱柱（少2 * 1个float）
						圆柱[少2(UInt32) * 2(底面) * (2n/3 - 2)个float]
						圆锥[少2 * (2n/3 - 3)个float]与组合模型有优化
						球（m层n瓣）[侧面的每个四边形少2个float(6float变成3float+2UInt16)总计2(m-2)n；极点三角形每个多出1个UInt32，总计n个。总体减少(2m-3)n个float]
					未优化的类型：
						四棱锥（多2 * 3个float）
					不包括dynamic_array的开销
					渲染使用
			*/
			UInt32 ivn;
			/**
			@brief  相对于主模型的位移，即相对于主模型的中心点
					渲染使用：根据几何类型执行不同的纹理Tile计算
			 */
			UInt32 ivc;
			/**
			@brief  能支持少于500的立方体、三棱柱、四棱锥的组合。
					同im。目前ic同。
					修改位域后，需修改GeoSolidSubMesh。
					局部包围盒，以原点为中心点
			 */
			UInt32 ibb;
			/**
			@brief  从部位到本体的转换矩阵。即mat * vChild = vParent
					渲染使用：纹理Tile模式在不同方向上的计算
			 */
			UInt32 im;
			//目前位域同上
			/**
			@brief  为了保留各部位设置的颜色而引入
					渲染使用
			 */
			UInt32 ic;
			/**
			@brief  面积
			 */
			GsDigit area;
			/**
			@brief  渲染使用：根据几何类型执行不同的纹理Tile计算
			 */
			GeoSolidShape gss : 4;
			/**
			@brief  渲染使用：根据几何类型执行不同的纹理Tile计算
			 */
			GeoSolidFace gsf : 4;
			/**
			@brief  仅0、1、2，下同。
					渲染使用：纹理Tile模式在各个面上的不同计算方向。
			 */
			UInt8 uIndex : 2;
			UInt8 vIndex : 2;
			/**
			@brief  在计算相交和合并后的移除中，根据此标识移除当前三角形。
					运行时数据。
			 */
			bool markRemoval : 1;
			/**
			@brief 记录合并中是否被翻转过。在计算圆柱、圆锥的侧面，以及球的纹理Tile中使用
			 */
			bool turnover : 1;
			char : 2;
			/**
			@brief 运行时数据
			 */
			GeoSolidInteractState intersectSrc;
			char : 3;
			GeoSolidInteractState intersectCut;
			char : 3;

			#if COMPOSITE_STD_USE_POINTER
            /**
			@brief	包含的新顶点，含内部与边上，不包括重合
					运行时数据
             */
			GeoSolidSet<GsIv>* setNewIvs;
			/**
			@brief	对该三角形进行分割时，需要包含的线段
					运行时数据
			 */
			GeoSolidSet<GsIe>* setIesDivider;
			/**
			@brief	重叠部分的三角形
					运行时数据
			 */
			GeoSolidSet<GsIt>* setCoincidentIts;
			#else
			GeoSolidSet<GsIv> setNewIvs;
			GeoSolidSet<GsIe> setIesDivider;
			GeoSolidSet<GsIt> setCoincidentIts;
			#endif
		private:
			GeoSolidTriangle();
		public:
			~GeoSolidTriangle();
		private:
			void Copy(const GeoSolidTriangle& gst);
			/**
			@brief	从SgCore的运算结果转换过来
			 */
			void Set(const GsIndex iv0, const GsIndex iv1, const GsIndex iv2);
			/**
			@brief	在基本类型中使用
			 */
			void Set(const GsIndex iv0, const GsIndex iv1, const GsIndex iv2,
				const UInt32& iuv0, const UInt32& iuv1, const UInt32& iuv2,
				const UInt8& uIndex, const UInt8& vIndex, const GeoSolidShape& gss, const GeoSolidFace& gsf);
			//以下三个在合并计算中使用
			void Set(const GsIv iv0, const GsIv iv1, const GsIv iv2, const UInt32& ibb, const UInt32& im,
				const GeoSolidShape& gss, const GeoSolidFace& gsf, const UInt32& ivn,
				const bool src, const bool cut, const UInt32 ic);
			void Set(const GsIv iv0, const GsIv iv1, const GsIv iv2, const GeoSolidTriangle& gst);
			void Set(const GsIv iv0, const GsIv iv1, const GsIv iv2,
				const UInt32& iuv0, const UInt32& iuv1, const UInt32& iuv2,
				const UInt8& uIndex, const UInt8& vIndex, const UInt32& ibb, const UInt32& im, const GeoSolidShape& gss, const GeoSolidFace& gsf,
				const UInt32& ivn,
				const bool src, const bool cut, const UInt32 ic);
			void Set(const GeoSolidTriangle& gst);
			void Set(const GeoSolidTriangle* gst);
			void Set(const FBSave::GeoSolidTriangle& fbsGst);
			void Set(const FBSave::GeoSolidTriangle* fbsGst);
			GeoSolidTriangle& operator= (const GeoSolidTriangle& gst);
		public:
			bool IsValid() const;
			bool Neighbor(const GeoSolidTriangle& gst) const;
			flatbuffers::Offset<FBSave::GeoSolidTriangle> ToFbs(flatbuffers::FlatBufferBuilder& builder) const;
			void FromFbs(const FBSave::GeoSolidTriangle* fbsGst);

			const GeoSolidSet<GsIv>& GetNewIvs() const;
			GeoSolidSet<GsIv>& GetNewIvs();
			void AddNewIv(const GsIv& iv);
			void ClearNewIvs();

			GeoSolidSet<GsIe>& GetIesDivider();
			void AddIeDivider(const GsIe& ie);
			void ClearIesDivider();

			GeoSolidSet<GsIt>& GetCoincidentIts();
			void AddCoincidentIts(const GsIt& it);
			void ClearCoincidentIts();

			#if GEO_SOLID_RECYCLE
			DECLARE_THREAD_FLYWEIGHT_PATTERN(GeoSolidTriangle)
			#else
			DECLARE_NON_FLYWEIGHT_PATTERN(GeoSolidTriangle)
			#endif
			friend class GeoSolid;
		};

		#if COMPOSITE_ELEMENT_USE_POINTER
		using GeoSolidVertexArray = GeoSolidArray<GeoSolidVertex*>;
		using GeoSolidTriangleArray = GeoSolidArray<GeoSolidTriangle*>;
		#else
		using GeoSolidVertexArray = GeoSolidArray<GeoSolidVertex>;
		using GeoSolidTriangleArray = GeoSolidArray<GeoSolidTriangle>;
		#endif
		using GeoSolidEdgeArray = GeoSolidArray<GeoSolidEdge*>;
		#if COMPOSITE_ELEMENT_USE_POINTER
		#else
		#endif

        class EXPORT_SANDBOXENGINE GeoSolid
        {
        public:
            GeoSolid();
            virtual ~GeoSolid();
			static void LeaveWorld();
			static void UpdateVerticesEdgesTriangles(
				GeoSolidVertexArray& daGeoSolidVertices,
				GeoSolidTriangleArray& daTriangles,
				GeoSolidEdgeArray& daEdges,
				GeoSolidArray<GsVector3>& daVs
			);
			static GsIe AddEdge(GeoSolidEdgeArray& daEdges, const GeoSolidEdge* gse);
			static int FindEdge(GeoSolidEdgeArray& daEdges, const GsIv iv0, const GsIv iv1);

			GEO_SOLID_INLINE GeoSolidVertex& GetVertex(const GsIv& iv);
			GEO_SOLID_INLINE const GeoSolidVertex& GetVertex(const GsIv& iv) const;
			bool RemoveVertex(const GsIv& iv);
			GEO_SOLID_INLINE GsIv GetVertexCount();
			GEO_SOLID_INLINE GsIv GetVertexCount() const;
			GEO_SOLID_INLINE void SetNullVertex(const GsIv& iv);
			int FindVertex(const GeoSolidVertex& gsv, const GsDigit errorThreshold = 1e-2);

			GEO_SOLID_INLINE GeoSolidEdge& GetEdge(const GsIe& ie);
			bool RemoveEdge(const GsIe& ie);
			GEO_SOLID_INLINE GsIe GetEdgeCount();
			GEO_SOLID_INLINE void SetNullEdge(const GsIe& ie);

			GEO_SOLID_INLINE GeoSolidTriangle& GetTriangle(const GsIt& it);
			GEO_SOLID_INLINE const GeoSolidTriangle& GetTriangle(const GsIt& it) const;
			GEO_SOLID_INLINE bool RemoveTriangle(const GsIt& it);
			GEO_SOLID_INLINE GsIt GetTriangleCount();
			GEO_SOLID_INLINE GsIt GetTriangleCount() const;
			GEO_SOLID_INLINE void SetNullTriangle(const GsIt& it);
			int FindTriangle(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2);

			virtual void InitGeoSolidMeshData() {}
            /**
            @brief 获取Mesh
            @param
			*/
			Rainbow::SharePtr<Rainbow::Mesh> CreateMesh(const GeoSolidArray<PrimitiveVertex>& daPvs, const GeoSolidArray<UInt32>& daPis);
			Rainbow::SharePtr<Rainbow::Mesh> CreateMesh(const GeoSolidArray<PrimitiveVertex>& daPvs, const GeoSolidArray<UInt32>& daPis,
				const int cpv, const int cpi, const int opv, const int opi);
			GeoSolidArray<PrimitiveVertex> GetPvs(Rainbow::SharePtr<Rainbow::Mesh> mesh, GeoSolidArray<PrimitiveVertex>& daPvsOrigin);
			GeoSolidArray<UInt32> GetPis(Rainbow::SharePtr<Rainbow::Mesh> mesh, GeoSolidArray<UInt32>& daPisOrigin);
			virtual void CreateDynamic() = 0;
			virtual void SeparateSurfaces() = 0;
			static Rainbow::SharePtr<Rainbow::Mesh> CopySurfaceMesh(Rainbow::SharePtr<Rainbow::Mesh> meshOrigin);
			Rainbow::SharePtr<Rainbow::Mesh> GetCopySurfaceMesh(const int& i);


			GeoSolidShape GetShape() const { return m_eGss; }
			virtual int GetSubMeshCount() { return 1; }
			virtual int GetSurfaceCount() { return 1; }
			virtual GeoSolidFace GetGeoSolidFace(int ism) = 0;
			virtual GeoSolidShape GetGeoSolidShape(int ism) { return m_eGss; }
            static void UpdateBound(GeoSolidVertexArray& daGeoSolidVertices, GsBoxBound& bb);
			virtual const char* GetName() const { return "GeometrySolid"; }
			virtual std::string GetCacheKey() const { return GetName(); }
			virtual void SetColor(const Rainbow::ColorQuad& cq) { }
			
			virtual Rainbow::SharePtr<Rainbow::Mesh> GetSurfaceMesh(const int& i) {
				if (m_daMeshes.empty()) SeparateSurfaces();
				return i < 0 || i >= m_daMeshes.size() ? nullptr : m_daMeshes[i];
			}
			GeoSolidVertexArray& GetGeoSolidVertices() { return m_daGeoSolidVertices; }
			GeoSolidEdgeArray& GetEdges() { return m_daEdges; }
			GeoSolidTriangleArray& GetTriangles() { return m_daTriangles; }
			GeoSolidArray<GsMatrix3x3>& GetMatrices() { return m_daMatrices; }
			GeoSolidArray<GsBoxBound>& GetBoxBounds() { return m_daBoxBounds; }
			GeoSolidArray<GsVector3>& GetCenters() { return m_daCenters; }
			GeoSolidArray<Rainbow::ColorRGBA32>& GetColors() { return m_daColors; }
			GeoSolidArray<Vector2db>& GetUvs() { return m_daUvs; }
			virtual Rainbow::SharePtr<Rainbow::Mesh> GetMesh();
			GsVector3 GetCenter() { return m_bb.getCenter(); }
			GsVector3 GetSize() { return m_bb.m_MaxPos - m_bb.m_MinPos; }
			GsBoxBound GetBoxBound() { return m_bb; }
			static UInt32 AddUv(GeoSolidArray<Vector2db>& daUvs, const Vector2db& uv);
			Rainbow::SharePtr<Rainbow::Mesh> GetCacheMesh(const Rainbow::ColorQuad& cq);
        public:
			virtual GeoSolid* GetByScale(const Rainbow::Vector3f& vs) { return this; }
			static GeoSolid* CreateGeoSolid(const GeoSolidShape& eGss);
			#if COMPOSITE_GEO_SOLID_DEBUG
			static const char* GetFaceName(const GeoSolidFace& eGsf);
			static const char* GetShapeName(const GeoSolidShape& eGss);
			#endif
			virtual const char* GetSurfaceName(const SceneModelObject::Surface& sf);
			template<class GeoSolidClass, typename... Args>
			static GeoSolidClass* obtain(const Args&... args)
			{
				GeoSolidClass* gsc = GeoSolidClass::obtain();
				gsc->Set(args...);
				return gsc;
			}
		protected:
			GeoSolidArray<GsVector3> m_daVertices;
			GeoSolidArray<PrimitiveVertex> m_daPrimitiveVertices;
			GeoSolidArray<UInt32> m_daPrimitiveIndices;
			GeoSolidVertexArray m_daGeoSolidVertices;
			GeoSolidTriangleArray m_daTriangles;
			GeoSolidEdgeArray m_daEdges;
			GeoSolidArray<GsMatrix3x3> m_daMatrices;
			/**
			@brief	部位坐标系
			 */
			GeoSolidArray<GsBoxBound> m_daBoxBounds;
			/**
			@brief	在本体坐标系下，相对于Src的位移
			 */
			GeoSolidArray<GsVector3> m_daCenters;
			/**
			@brief	部位坐标系到本体
			 */
			GeoSolidArray<GsVector3> m_daNormals;
			GeoSolidArray<Rainbow::ColorRGBA32> m_daColors;
			GeoSolidArray<Vector2db> m_daUvs;
			GeoSolidArray<Rainbow::SharePtr<Rainbow::Mesh>> m_daMeshes;
			Rainbow::SharePtr<Rainbow::Mesh> m_mesh;
			GsBoxBound m_bb = GsBoxBound(GsVector3::zero, GsVector3::one);
			/**
			@brief	仅在基础模型中传递一次。组合模型不使用。
			*/
			//Rainbow::ColorQuad m_cq;
            /**
            @brief	形状类型
            */
            GeoSolidShape m_eGss : 8;
			static GeoSolidArray<PrimitiveVertex> s_daPvsEmpty;
			static GeoSolidArray<UInt32> s_daPisEmpty;
            static Rainbow::ShaderChannelMask s_ShaderChannelMask;
			friend class GeoSolid;
        };
    }
}
#endif//__GeoSolid_h__