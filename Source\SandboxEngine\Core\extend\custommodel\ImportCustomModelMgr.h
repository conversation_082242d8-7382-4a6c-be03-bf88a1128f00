#ifndef __IMPORTCUSTOMMODELMGR_H__
#define __IMPORTCUSTOMMODELMGR_H__

#include "ImportCustomModel.h"
#include "Utilities/Singleton.h"
#include "SandboxEngine.h"
#include "BaseClass/SharePtr.h"
#include "OgreModelData.h"
#include "Graphics/Texture2D.h"
namespace Rainbow
{
	class Model;
	class ModelData;
	class TextureRenderGen;
}
class IconDesc;


struct ResourceFolderSetInfo;
class EXPORT_SANDBOXENGINE ImportCustomModelMgr;
class ImportCustomModelMgr //tolua_exports
	: public Rainbow::Singleton<ImportCustomModelMgr>
{ //tolua_exports
public:
	//tolua_begin
	ImportCustomModelMgr();
	~ImportCustomModelMgr();
	//tolua_end
public:
	//tolua_begin
	void loadMapImportCustomModelData(long long owid, int realowneruin, int specialType = 0); //加载地图库模型
	void loadOneModImportCustomRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod=false, bool ignorecheck = false);
	void leaveWorld(); //退出地图的一些处理

	bool moveResImportCustomModelToMap(std::string filename);	//拷贝总库模型到地图
	bool moveMapImportCustomModelToRes(std::string filename);	//拷贝地图模型到总库
	bool moveIcmRes(int destlibtype, std::string filename, ResourceFolderSetInfo *destclass);

	//从资源中心删除资源
	bool removeResByResourceCenter(int libtype, std::string filename);
	bool removeMapImportCustomModel(std::string filename);	//移除地图模型

	int getFreeId(int type);//获取可用道具id


	
	void loadResImportCustomModelData(); //加载总库模型
	void onSwitchAccountSucceed(int uin); //切换账号的处理
	void loadMapImportCustomModel(std::string filename); //加载地图库的单个模型
	void loadResImportCustomModel(std::string filename); //加载总库的单个模型
	void loadPreImportCustomModel(std::string filename); //加载预览的单个模型


	ImportCustomModel *findImportCustomModel(int type, std::string skey); //查找已加载的导入模型信息
	std::string importResModelZip(std::string sname, std::string sdesc, int itype, std::string sfilename); //导入模型文件，返回model的key值
	std::string copyModelZipFromStudioToRes(char * srcPath); //拷贝模型zip包到总库

	Rainbow::SharePtr<Rainbow::Texture2D> getModelIcon(std::string skey, int &u, int &v, int &width, int &height, int &r, int &g, int &b);
	Rainbow::SharePtr<Rainbow::Texture2D> genModelTexture(std::string skey, int *libtype = NULL);
	std::unordered_map<std::string, IconDesc*>& getIconDescs() { return m_IconDescs; }
	std::unordered_map<std::string, IconDesc*>& getMapIconDesc() { return m_MapIconDescs; }

	/*
		分帧加载，预加载
	*/
	void preLoadMapImportCustomModelData(long long owid, int realowneruin);
	/*
		分帧加载,加载一个.icm模型数据
	*/
	void loadOneMapImportCustomModelData(std::string& filename);

	bool reEncryptImportCustomModel(long long owid, int olduin, int newuin, const std::string& sAuthName);
	//tolua_end
public:
	//tolua_begin
	void clearMapImportModels(); //清除地图库模型
	void clearResImportModels(); //清除总库模型
	void clearPreImportModels(); //清除预览模型

	bool addMapImportModels(ImportCustomModel * pModel); //添加新地图模型，比如客机同步
	void syncImportModel(int uin, unsigned int startIndex);	//同步导入模型到客机，每次最多60个

	//tolua_end
public:
	Rainbow::Model* getImportModel(std::string skey, int* libtype = nullptr, int iType = -1);
	//tolua_begin
	Rainbow::Model *getImportHandModel(std::string skey);
	bool checkModelLegitimacy(std::string skey, int iType=-1);
	//tolua_end
private:
	void LoadModImportModel(std::string skey, std::string modroot);
	Rainbow::Model* createImportModel(std::string skey, int* libtype = nullptr, int iType = -1);
	Rainbow::Model* createImportHandModel(std::string skey);
	Rainbow::SharePtr<Rainbow::Texture2D> createModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b);
	Rainbow::SharePtr<Rainbow::Texture2D> createModelTexture(std::string skey, int* libtype = NULL);

	long long m_CurOWID;
	int m_nSpecialType;
	int m_CurOWRealUin;  //当前地图的作者uin
	std::vector<ImportCustomModel*> m_MapImportCustomModels;
	std::vector<ImportCustomModel*> m_ResImportCustomModels;
	std::unordered_map<std::string, ImportCustomModel*> m_PreImportCustomModels;
	bool m_bLoadedRes;

protected:
	std::unordered_map<std::string, IconDesc*> m_IconDescs;
	std::unordered_map<std::string, IconDesc*> m_MapIconDescs;
	Rainbow::TextureRenderGen *m_TextureGen;

private:
	std::unordered_map<std::string, Rainbow::SharePtr<Rainbow::ModelData>> m_ModelDataCache;
	std::unordered_map<std::string, Rainbow::SharePtr<Rainbow::Texture2D>  > m_TextureCache;

	std::unordered_map<std::string, Rainbow::SharePtr<Rainbow::ModelData>> m_ModModelDataCache;
	std::unordered_map<std::string, Rainbow::SharePtr<Rainbow::Texture2D>  > m_ModTextureCache;
}; //tolua_exports



#endif //__IMPORTCUSTOMMODELMGR_H__