#pragma once
#include "Input/OgreInputManager.h"
#include "OgreRect.h"
#include "CameraInfo.h"

class IPlayerControl;

class TPSCamera :public CameraBase //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	TPSCamera(CameraManager* cameraManager);
	~TPSCamera();

	void update(float deltaSeconds) override;
	int onInputEvent(const Rainbow::InputEvent &event) override;

	float m_MoveSpeed;
	float m_MoveStrafe;
	float m_MoveForward;
	float m_RotX;
	float m_RotY;

	float m_RotSpeed;
	//tolua_end
}; //tolua_exports

class EditingCustomCamera :public TPSCamera //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	EditingCustomCamera(CameraManager* cameraManager);
	~EditingCustomCamera();

	void update(float deltaSeconds) override;
	int onInputEvent(const Rainbow::InputEvent &event) override;
	void onSwitchTo();
	//tolua_end
private:
	int onPcInputEvent(const Rainbow::InputEvent &event);
	int onTouchInputEvent(const Rainbow::InputEvent &event);

	bool m_IsInit;
	MINIW::Point2D m_PreMousePos;
	MINIW::Point2D m_RotateStart;
	int m_RotateID;

public:
	//tolua_begin
	float m_MoveUp;

	float m_PreRotateY;
	float m_PreRotateX;
	//tolua_end
}; //tolua_exports
