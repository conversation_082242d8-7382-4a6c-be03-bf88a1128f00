#pragma once

#include <string>
#include <functional>
#include "SandboxEngine.h"

class EXPORT_SANDBOXENGINE WorldEventMgrProxy
{
public:
    WorldEventMgrProxy();
    virtual ~WorldEventMgrProxy() = default;

    virtual void onChunkLoaded(int chunkX, int chunkZ) = 0;
    virtual void onChunkUnloaded(int chunkX, int chunkZ) = 0;
    virtual void onDestroyContainer(int x,int y,int z) = 0;
};

EXPORT_SANDBOXENGINE WorldEventMgrProxy* GetWorldEventMgrProxy();

