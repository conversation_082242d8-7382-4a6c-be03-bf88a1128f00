#pragma once
/**
* file : SceneEffectHemisphereFrame
* func : 场景效果 （球框）
* by : pengdapu
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectEllipse;

/**
@brief	默认朝正Z轴
*/
class SceneEffectHemisphereFrame : public SceneEffectGeom
{
public:
	SceneEffectHemisphereFrame();
	virtual ~SceneEffectHemisphereFrame();
	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;
	bool IsActive(World* pWorld) const override;
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
public:
	void SetRadius(float radius) override;
private:
	/**
	@brief	圆
	 */
	SceneEffectEllipse* m_ellipseZ = nullptr;
	/**
	@brief	半圆
	 */
	SceneEffectEllipse* m_ellipseX = nullptr;
	SceneEffectEllipse* m_ellipseY = nullptr;
};
