﻿#include "IGameMode.h"
#include "WorldManager.h"
#include "Environment.h"
#include "world.h"
#include "OgreUtils.h"
#include "ObserverEventManager.h"
#include "IPlayerControl.h"
#include "LightningMgr.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "DefManagerProxy.h"
#include "MpActorManager.h"
#include "WeatherManager.h"
#include "WeatherBase.h"
#include "EffectParticle.h"
#include "SandboxRenderSetting.h"
#include "SkyPlane.h"
#include "WorldRender.h"
#include "MiniCraftRenderer.h"
#include "Optick/optick.h"
#include "EffectManager.h"
#include "blocks/BlockMaterialMgr.h"
#include "DangerNightManagerInterface.h"
#include "coreMisc.h"

using namespace MINIW;

Environment::Environment(World* pworld) : m_World(pworld)
{
	genLightBrightTable();
	if (m_World->getCurMapID() == MAPID_GROUND)
	{
		m_pLightningMgr = ENG_NEW(LightningMgr)(pworld);  //地表才有闪电
	}
	else
	{
		m_pLightningMgr = NULL;
	}
	m_Tick = 0;
	m_weatherMgr = ENG_NEW(WeatherManager)(pworld);
	m_SunLightSubtract = calculateSkylightSubtracted();
	m_Darking = false;
	m_PrevDarkStrength = m_DarkStrength = 0;
	m_UpdateTime = 0;
	m_ParticleInsectVec.clear();
	m_ParticleSmokeVec.clear();
	m_ParticleLeafVec.clear();
	m_ParticleLightSpotVec.clear();
	m_SnowCoverVec = Rainbow::Vector4f(-0.1f, 0.6f, -0.25f, 4.0f);
	m_CurBiomeType = 0;
	m_PreCurBiomeType = 0;
	m_NearBiomeType = 0;
	m_LastBiomeType = 0;
	m_NextBiomeType = 0;
	m_BiomeBorderCurDist = 0.f;
	//m_BiomeBorderOldDist = 0.f;
	m_BiomeBorderDefaultDist = 16.f;
	m_BiomeFogStart = 30.0f;
	m_BiomeFogEnd = 50.0f;
	m_AuroraEnable = false;
	m_NextBiomeTypeMinDist = 0.f;
	m_NextBiomeTypePercent = 0.f;
	m_ShadowIntensity = 1.0f;
	m_WaterColor = Rainbow::ColorRGBAf(0.165f, 0.234f, 0.36f, 1.0f);
	m_WaterWaveSpeed = 1.0f;
	m_WindStrength = 1.0f;
	m_IsWaterGlow = 0;
}

Environment::~Environment()
{
	ENG_DELETE(m_pLightningMgr);
	ENG_DELETE(m_weatherMgr);

	for (int i = 0; i < m_ParticleInsectVec.size(); ++i)
	{
		EnvironmentParticle& data = m_ParticleInsectVec[i];
		if (data.particle)
		{
			data.particle->setNeedClear();
			data.particle = NULL;
		}
	}
	m_ParticleInsectVec.clear();

	for (int i = 0; i < m_ParticleLightSpotVec.size(); ++i)
	{
		EnvironmentParticle& data = m_ParticleLightSpotVec[i];
		if (data.particle)
		{
			data.particle->setNeedClear();
			data.particle = NULL;
		}
	}
	m_ParticleLightSpotVec.clear();

	for (int i = 0; i < m_ParticleSmokeVec.size(); ++i)
	{
		EnvironmentParticle& data = m_ParticleSmokeVec[i];
		if (data.particle)
		{
			data.particle->setNeedClear();
			data.particle = NULL;
		}
	}
	m_ParticleSmokeVec.clear();

	for (int i = 0; i < m_ParticleLeafVec.size(); ++i)
	{
		EnvironmentParticle& data = m_ParticleLeafVec[i];
		if (data.particle)
		{
			data.particle->setNeedClear();
			data.particle = NULL;
		}
	}
	m_ParticleLeafVec.clear();
}

void Environment::load(WorldMapData* mapdata)
{
	m_Darking = mapdata->darking;
	m_PrevDarkStrength = m_DarkStrength = mapdata->darkstrength;
	m_weatherMgr->load(mapdata);
}

void Environment::save(WorldMapData* mapdata)
{
	if (mapdata == NULL) return;

	mapdata->darking = m_Darking;
	mapdata->darkstrength = m_DarkStrength;
	m_weatherMgr->save(mapdata);
}

void Environment::tick()
{
	OPTICK_EVENT();
	m_SunLightSubtract = calculateSkylightSubtracted();
	m_Tick++;
	m_UpdateTime = 0;
	m_weatherMgr->tick();
	if (m_World->getCurMapID() == MAPID_GROUND)
	{
		effectTick();
		snowEffectTick();
		biomeTick();
	}
}

void Environment::update(unsigned int dtick)
{
	OPTICK_EVENT();
	m_UpdateTime += float(dtick) / GAME_TICK_MSEC;
	if (m_UpdateTime > 1.0f) m_UpdateTime = 1.0f;

	if (m_pLightningMgr)
		m_pLightningMgr->update(dtick);

	m_weatherMgr->update(dtick);
}

float Environment::getCelestialAngle()
{
	return calculateCelestialAngle(m_World->getTimeInDay());
}

void Environment::resetRainThunder()
{
	
}

void Environment::toggleRain()
{
	getWeatherMgr()->ToggleRain();
}

void Environment::toggleThunder()
{
	getWeatherMgr()->ToggleThunder();
}

void Environment::cutWeather()
{
	getWeatherMgr()->CutAllGroupWeather();
}

void Environment::setDark(bool darking)
{
	m_Darking = darking;
	g_WorldMgr->sendWGlobalUpdate();
}

float Environment::getRainStrength()
{
	WCoord curpos(0, -1, 0);
	if (GetIPlayerControl())
	{
		curpos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
	}

	return m_weatherMgr->getWeatherStrength(curpos, GROUP_RAIN_WEATHER);
}

float Environment::getThunderStrength()
{
	WCoord curpos(0, -1, 0);
	if (GetIPlayerControl())
	{
		curpos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());

	}
	return m_weatherMgr->getWeatherStrength(curpos, GROUP_THUNDER_WEATHER);
}

float Environment::getDarkStrength()
{
	return m_PrevDarkStrength + (m_DarkStrength - m_PrevDarkStrength) * m_UpdateTime;
}

float Environment::getWeatherStrength(const WCoord& blockpos, int weatherid)
{
	return m_weatherMgr->getWeatherStrength(blockpos, weatherid);
}

void Environment::getBiomeFogRangeByLv(int lv, float& fogStart, float& fogEnd)
{
	if (!m_World || !m_World->getRender() ||
		!m_World->getRender()->getSky())
	{
		return;
	}

	fogStart = 30.0f;
	fogEnd = 50.0f;
	Rainbow::SkyPlane* sky = m_World->getRender()->getSky();
	if (m_CurBiomeType != MAX_BIOME_TYPE)
	{
		if (GetDefManagerProxy() != nullptr)
		{
			auto curBiomeDef = GetDefManagerProxy()->getBiomeDef(m_CurBiomeType);
			if (curBiomeDef)
			{
				sky->getFogRange(fogStart, fogEnd, lv, (Rainbow::BiomeSkyBoxType)curBiomeDef->skyboxType);
			}
		}
	}
}

void Environment::getBiomeFogRange(float& fogStart, float& fogEnd)
{
	int lv = getBiomeFogLevel();
	getBiomeFogRangeByLv(lv, fogStart, fogEnd);
}

void Environment::genLightBrightTable()
{
	float x0 = 0.0f;  //hell x0=0.1f;
	for (int i = 0; i <= 15; ++i)
	{
		float x = 1.0f - float(i) / 15.0f;
		m_LightBrightTable[i] = (1.0f - x) / (x * 3.0f + 1.0f) * (1.0f - x0) + x0;
	}
}

float Environment::calculateCelestialAngle(int t)
{
	
	float normalizedTime = t / float(TICKS_ONEDAY) - 0.25f;
	if(normalizedTime < 0) normalizedTime += 1.0f;
	else if(normalizedTime > 1.0f) normalizedTime -= 1.0f;
	
	// 根据当前时间判断是白天还是黑夜
	// 在我们的规范化时间中，[0.0-0.5]是白天，[0.5-1.0]是黑夜
	// 需要调整使白天占据[0.0-0.75]，黑夜占据[0.75-1.0]
	float adjustedTime;
	if(normalizedTime < 0.5f) {
		// 白天 - 从[0.0-0.5]映射到[0.0-0.75]
		adjustedTime = normalizedTime * 1.5f;
	} else {
		// 黑夜 - 从[0.5-1.0]映射到[0.75-1.0]
		adjustedTime = 0.75f + (normalizedTime - 0.5f) * 0.5f;
	}
	
	// 平滑
	float adjust = 1.0f - (CosByAngle(adjustedTime * 180.0f) + 1.0f) / 2.0f;
	return adjustedTime + (adjust - adjustedTime) / 3.0f;
}

int Environment::calculateSkylightSubtracted()
{
	//WCoord curpos(0, -1, 0);
	//if (GetIPlayerControl())
	//{
	//	curpos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
	//}

	float angle = getCelestialAngle();
	float x = 1.0f - (CosByAngle(angle*360.0f)*2.0f + 0.5f);

	if (x < 0) x = 0;
	else if (x > 1.0f) x = 1.0f;

	float hourOfDay = m_World->getHours();
	
	// 白天亮度补偿：6-18点形成完整的补偿曲线，12点最大
	if (hourOfDay >= 6.0f && hourOfDay <= 18.0f) {
		float maxCompensation = 0.2f;
		float normalizedTime;
		
		if (hourOfDay <= 12.0f) {
			// 上午：6-12点，补偿从0增加到最大值
			normalizedTime = (hourOfDay - 6.0f) / 6.0f;
		} else {
			// 下午：12-18点，补偿从最大值减少到0
			normalizedTime = 1.0f - (hourOfDay - 12.0f) / 6.0f;
		}
		
		float compensationFactor = maxCompensation * normalizedTime;
		x = max(0.0f, x - compensationFactor);
	}

	x = 1.0f - x;
	//x = x * (1.0f - (getWeatherStrength(curpos, GROUP_RAIN_WEATHER) + getWeatherStrength(curpos, GROUP_SNOW_WEATHER)) * 6.0f / 16.0f);
	//x = x * (1.0f - getWeatherStrength(curpos, GROUP_THUNDER_WEATHER) * 9.0f / 16.0f);
	//x = x * (1.0f - m_weatherMgr->getWeatherStrength(curpos, GROUP_TEMPEST_WEATHER) * 6.0f / 16.0f);
	x = x * (1.0f - m_weatherMgr->getWeatherLightFactor());
	x = 1.0f - x;

	float factor = getLightningImpactFactor();
	if (factor > 0)
		x -= 0.4 * factor;

	return (int)(x * 10.0f);
}


void Environment::createOneLightning(int percenttoplayer/* =5 */)
{
	if (!m_World || !m_pLightningMgr || !GetWorldManagerPtr())
		return;

	if (m_World->isRemoteMode())
		return;

	std::vector<IClientPlayer*> players;
	GetWorldManagerPtr()->getAllPlayers(players);
	if (players.size() == 0)
	{
		return;
	}
	int index = GenRandomInt(1, (int)players.size());
	if (index <= 0 || index > (int)players.size() || !players[index - 1])
		return;

	int percent = percenttoplayer; //劈击玩家位置的概率，基准概率5%
	for (int i = 0; i < EQUIP_WEAPON; ++i)
	{
		int itemid = 0;
		bool result = players[index - 1]->getPlayerAttribComponent()->Event2().Emit<int&, int>("PlayerAttrib_getEquipItem", itemid, i);//->getEquipItem((EQUIP_SLOT_TYPE)i);
		if (!result)
		{
			Assert(false);
		}
		auto* toolDef = GetDefManagerProxy()->getToolDef(itemid);
		if (toolDef && (toolDef->Type == 2 || toolDef->Type == 3)) //铁或者链材质的装备增加5%的概率
		{
			percent += 5;
		}
	}

	WCoord targetPos = players[index - 1]->iGetPosition();
	WCoord blockPos = CoordDivBlock(targetPos);
	if (m_weatherMgr->getWeather(blockPos) == GROUP_THUNDER_WEATHER)
	{
		if (GenRandomInt(1, 100) > percent)
		{
			targetPos.x = GenRandomInt(targetPos.x - 30 * BLOCK_SIZE, targetPos.x + 30 * BLOCK_SIZE);
			targetPos.z = GenRandomInt(targetPos.z - 30 * BLOCK_SIZE, targetPos.z + 30 * BLOCK_SIZE);
		}

		createLightning(targetPos);
		notifyLightning2Tracking(targetPos, dynamic_cast<IClientActor*>(players[index - 1]));
	}
}

void Environment::createLightning(WCoord& targetpos)
{
	WCoord startPos(targetpos.x, -1, targetpos.z);
	WCoord endPos(targetpos.x, -1, targetpos.z);
	if (GetIPlayerControl())  //由观察者看去的方向确定闪电特效的起点和终点
	{
		Rainbow::Vector3f dir1 = Rainbow::Vector3f(1, 0, 0);
		WCoord observerPos = GetIPlayerControl()->GetPlayerControlPosition();
		if (observerPos == targetpos)
		{
			WCoord pt = targetpos + WCoord(1, 0, 1);
			dir1 = Rainbow::Vector3f((pt.x - targetpos.x), 0, (pt.z - targetpos.z));
		}
		else
		{
			dir1 = Rainbow::Vector3f((targetpos.x - observerPos.x), 0, (targetpos.z - observerPos.z));
		}


		Rainbow::Vector3f dir = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), dir1);
		dir  = MINIW::Normalize(dir);
		dir *= (LIGHTNING_WIDTH/2); //1/2宽

		WCoord offsetPos(dir.x, 0, dir.z);

		//确定起点位置
		startPos.y = targetpos.y + 40 * BLOCK_SIZE;
		startPos -= offsetPos;

		//确定终点位置
		endPos += offsetPos;

		MINIW::WorldRay ray;
		ray.m_Origin = targetpos.toWorldPos();
		ray.m_Dir = Rainbow::Vector3f(0, -256 * BLOCK_SIZE, 0);
		ray.m_Range = ray.m_Dir.Length();
		ray.m_Dir /= ray.m_Range;

		ActorExcludes excludes;
		IntersectResult inter_result;
		if (m_World->pickGround(ray, &inter_result)) //击中方块
		{
			endPos.y = (inter_result.block.y + 1) * BLOCK_SIZE;
		}
		else
			endPos.y = 0;

		//调整确定最终的高度
		int num = (startPos.y - endPos.y) / LIGHTNING_WIDTH;
		if (num < 3)
			num = 3;

		startPos.y = endPos.y + num * LIGHTNING_WIDTH;

		addLightning(&startPos, &endPos);
	}
}

void Environment::notifyLightning2Tracking(WCoord& targetpos, IClientActor* actor)
{
	PB_LightningHC lightningHC;
	PB_Vector3* pos = lightningHC.mutable_targetpos();
	pos->set_x(targetpos.x);
	pos->set_y(targetpos.y);
	pos->set_z(targetpos.z);

	IClientPlayer* player = dynamic_cast<IClientPlayer*>(actor);
	bool includeMe = false;
	if (player)
		includeMe = !player->hasUIControl();

	m_World->getMpActorMgr()->sendMsgToTrackingPlayers(PB_LIGHTNING_HC, lightningHC, actor, includeMe);
}

void Environment::addLightning(WCoord* startPos, WCoord* endPos)
{
	if (m_pLightningMgr)
		m_pLightningMgr->addLightning(startPos, endPos);
}

bool Environment::isLightning()
{
	return m_weatherMgr->isCurGlobalWeather(THUNDER_WEATHER);
	if (m_pLightningMgr)
		return m_pLightningMgr->getLightningNum() > 0;

	return false;
}

float Environment::getLightningImpactFactor()
{
	if (!GetIPlayerControl() || !m_pLightningMgr)
		return 0;

	return	m_pLightningMgr->getLightningImpactFactor(GetIPlayerControl()->GetPlayerControlPosition());
}

void Environment::setCurWeather(int weather)
{
	//MKTODO:
	m_weatherMgr->changeAllGroupWeather(weather);
}

bool Environment::isRaining()
{
	return m_weatherMgr->isCurGlobalWeather(RAIN_WEATHER);
}

int Environment::getCurWeather()
{
	WCoord curpos(0, -1, 0);
	int weatherID = -1;
	if (GetIPlayerControl())
	{
		curpos = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
		weatherID = m_weatherMgr->getWeather(curpos);
	}
	return weatherID;
}

int Environment::getCurWeather(WCoord blockPos)
{
	return m_weatherMgr->getWeather(blockPos);
}

void Environment::effectTick()
{
	if (!m_World || m_World->isRemoteMode() || m_World->getCurMapID() != MAPID_GROUND || !GetWorldManagerPtr()) return;
	if (m_Tick % 600 != 0) return;//30秒一次

	std::vector<IClientPlayer*> players;
	GetWorldManagerPtr()->getAllPlayers(players);
	float curTime = m_World->getHours();
	bool isNight = curTime < 5.0f || curTime > 18.0f;
	for (int i = 0; i < players.size(); i++)
	{
		if (!players[i]) continue;
		IClientPlayer* player = players[i];
		WCoord blockpos = CoordDivBlock(player->iGetPosition());
		int height = m_World->getTopHeight(blockpos.x, blockpos.z);
		if (height < 63 && height > 80) continue;
		blockpos.y = height ;
		// 处于不同地形有不同特效
		int terraintype = m_World->getTerrainType();
		if (terraintype == TERRAIN_FLAT || terraintype >= TERRAIN_HARDSAND_FLAT && terraintype <=
			TERRAIN_PLANTSPACE_STONE)
		{
			// 空白地形不播放特效
		}
		else
		{
			BIOME_TYPE biomeType = m_World->getBiomeType(blockpos.x, blockpos.z);
			//全天：
			//干旱草原
			if (biomeType == BIOME_PLAINS_ARID)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_arid.prefab");
			}
			//蒲公英花海
			else if (biomeType == BIOME_PLAINS_DANDELION)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_dandelion.prefab");
			}
			//稻田
			else if (biomeType == BIOME_BASIN_RICE)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_rice.prefab");
			}
			//沼泽：
			else if (biomeType == BIOME_RAINFOREST || biomeType == BIOME_SWAMPLAND || biomeType ==
				BIOME_SWAMPLAND_RIVERSIDE)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_swamp.prefab");
			}
			//草原：
			else if (biomeType == BIOME_PLAINS)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_plains.prefab");
			}
			//竹林盆地
			else if (biomeType == BIOME_BASIN_BAMBOO)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_basinBamboo.prefab");
			}
			//桃林盆地
			else if (biomeType == BIOME_BASIN_PEACH)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_basinPeach.prefab");
			}
			//森林：
			else if (biomeType == BIOME_FOREST || biomeType == BIOME_FOREST_HILLS ||
				biomeType == BIOME_JUNGLE || biomeType == BIOME_RAINFOREST|| biomeType == BIOME_PLAINS_RAPESEED || 
				(biomeType >= BIOME_FOREST_LAVENDER && biomeType <=BIOME_FOREST_CHRYSANTH) || 
				biomeType == BIOME_AIRISLAND || biomeType == BIOME_EXTREMEHILLS_PLUM)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_forest.prefab");
			}
			else if (biomeType == BIOME_DESERT_HUYANG)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_huyang.prefab");
			}
			else if (biomeType == BIOME_JUNGLE_LANHUAYING)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_lanhuaying.prefab");
			}
			else if (biomeType == BIOME_EXTREMEHILLS_YINXING)
			{
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_yinxing.prefab");
			}
			else if (biomeType == BIOME_EXTREMEHILLS_FENGYE)
			{
				playFengyeeffect(blockpos);
				playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_fengye_2.prefab");
			}

			if (isNight)
			{
				//草原
				if (biomeType == BIOME_PLAINS)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_plains.prefab");
				}
				//干旱
				else if (biomeType == BIOME_PLAINS_ARID)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_arid.prefab");
				}
				//油菜花
				else if (biomeType == BIOME_PLAINS_RAPESEED)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_rapeseed.prefab");
				}
				//蒲公英
				else if (biomeType == BIOME_PLAINS_DANDELION)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_dandelion.prefab");
				}
				//森林等各种林
				else if (biomeType == BIOME_FOREST || biomeType == BIOME_FOREST_HILLS || biomeType == BIOME_JUNGLE ||
					biomeType == BIOME_RAINFOREST)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_forest.prefab");
				}
				//薰衣草
				else if (biomeType == BIOME_FOREST_LAVENDER)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_lavender.prefab");
				}
				//狗尾草
				else if (biomeType == BIOME_FOREST_FOXTAIL)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_foxtail.prefab");
				}
				//菊花
				else if (biomeType == BIOME_FOREST_CHRYSANTH)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_chrysanth.prefab");
				}
				//盆地
				else if (biomeType >= BIOME_BASIN && biomeType <= BIOME_BASIN_PEACH)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_basin.prefab");
				}
				//稻田
				else if (biomeType == BIOME_BASIN_RICE)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_rice.prefab");
				}
				//沼泽
				else if (biomeType == BIOME_SWAMPLAND || biomeType == BIOME_SWAMPLAND_RIVERSIDE)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_swamp.prefab");
				}
				//空岛及发光空岛
				else if (biomeType == BIOME_AIRISLAND || biomeType == BIOME_AIRISLAND_SHINE)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_airisland.prefab");
				}
				//郁金香海岛
				else if (biomeType == BIOME_ISLAND_LAND_TULIP || biomeType == BIOME_ISLAND_SHORE_TULIP)
				{
					playEnvironmentParticle(m_ParticleLightSpotVec, blockpos,
					                        "prefab/particles/scene_glow_tulip.prefab");
				}
			}
		}
		//有些特效需要在白天停止
		if (!isNight)
		{
			for (int i = 0; i < m_ParticleLightSpotVec.size(); ++i)
			{
				EnvironmentParticle& data = m_ParticleLightSpotVec[i];
				if (data.particle)
				{
					data.particle->setNeedClear();
					data.particle = nullptr;
				}
			}
			m_ParticleLightSpotVec.clear();
		}
	}
}

void Environment::playEnvironmentParticle(std::vector<EnvironmentParticle>& vec, const WCoord& blockpos, std::string path,float range)
{
	for (int i = 0; i < vec.size(); ++i)
	{
		EnvironmentParticle& data = vec[i];
		if (data.particle)
		{
			WCoord pos = data.blockpos - blockpos;
			float len = (float)sqrt(pos.x * pos.x + pos.z * pos.z);
			if (len < range) // 新特效需要远离已有得特效
				return;
		}
	}
	EnvironmentParticle ep; 
	ep.particle = m_World->getEffectMgr()->playParticleEffectAsync(path.c_str(), BlockCenterCoord(blockpos), 0, 0, 0, true,32);
	ep.blockpos = blockpos;
	vec.push_back(ep);
}

void Environment::getBiomeBorderDistance(const WCoord& pos)
{
	if (!m_World) return;
	WCoord blockpos = CoordDivBlock(pos);
	int currentType = m_World->getBiomeType(blockpos.x, blockpos.z);
	if (currentType == BIOME_RIVER) 
	{
		m_CurBiomeType = m_PreCurBiomeType;
	}
	else 
	{ 
		m_PreCurBiomeType = m_CurBiomeType;
		m_CurBiomeType = currentType;
	}
	//m_CurBiomeType = m_World->getBiomeType(blockpos.x, blockpos.z);
	m_NearBiomeType = m_CurBiomeType;
	int dist = m_BiomeBorderDefaultDist + 1;
	int fdist = m_BiomeBorderDefaultDist;
	WCoord nearBlockPos = blockpos;
	m_NextBiomeTypeMinDist = m_BiomeBorderDefaultDist * BLOCK_SIZE;
	for (int x = -dist; x <= dist; ++x)
	{
		for (int z = -dist; z <= dist; ++z)
		{
			int type = m_World->getBiomeType(blockpos.x + x, blockpos.z + z);
			//河流不参与过度
			if (type == BIOME_RIVER) continue;

			if (type != m_CurBiomeType)
			{
				int val = (int)sqrt(x * x + z * z);
				if (fdist > val)
				{
					fdist = val;
					m_NearBiomeType = type;
				}
			}
			if (m_NextBiomeType != 0 && type == m_NextBiomeType)
			{
				if (x == 0 && z == 0)
				{
					m_NextBiomeTypeMinDist = 0.f;
				}
				else if (m_NextBiomeTypeMinDist > 0.03f)
				{
					int distX = (blockpos.x + x + 0.5f) * BLOCK_SIZE - pos.x;
					int distZ = (blockpos.z + z + 0.5f) * BLOCK_SIZE - pos.z;
					float val = sqrt(distX * distX + distZ * distZ);
					if (val < m_NextBiomeTypeMinDist)
						m_NextBiomeTypeMinDist = val;
				}
			}
		}
	}
	m_NextBiomeTypePercent = 1.0f - m_NextBiomeTypeMinDist / (m_BiomeBorderDefaultDist * BLOCK_SIZE);
	m_NextBiomeTypePercent = clamp(m_NextBiomeTypePercent, 0.f, 1.0f);
	if (m_NearBiomeType != m_CurBiomeType)
	{
		if (m_NextBiomeType == 0 && m_NearBiomeType != MAX_BIOME_TYPE)
		{
			//m_LastBiomeType = m_CurBiomeType;
			m_NextBiomeType = m_NearBiomeType;
		}
		else if (m_NextBiomeTypePercent < 0.03f && m_NearBiomeType != m_NextBiomeType)
			m_NextBiomeType = 0;
	}
	else
	{
		if (m_CurBiomeType == m_NextBiomeType || m_NextBiomeType != 0 && (m_NextBiomeTypePercent < 0.03f || m_NextBiomeTypePercent > 0.98f))
		{
			m_NextBiomeType = 0;
		}
	}

	m_BiomeBorderCurDist = fdist;
}

void Environment::snowEffectTick()
{
	if (!m_World || !GetIPlayerControl()) return;
	// 出现覆盖雪效果
	Rainbow::GetSandboxRenderSetting().SetGrassSnowCoverEnable(true);
	GetBlockMaterialMgrPtr()->setPlantSnowCoverThickness(m_SnowCoverVec);

	float percent = 0.f;
	if (m_CurBiomeType == BIOME_ICE_PLAINS || m_CurBiomeType >= BIOME_ICE_PLAINS_CONIFEROUS_FOREST && m_CurBiomeType <= BIOME_ICE_PLAINS_SECOND_MOUNTAIN)
	{
		percent = 1.0f;
	}
	else
	{
		if (m_NearBiomeType == BIOME_ICE_PLAINS || m_NearBiomeType >= BIOME_ICE_PLAINS_CONIFEROUS_FOREST && m_NearBiomeType <= BIOME_ICE_PLAINS_SECOND_MOUNTAIN)
		{
			percent = 1.0f - m_BiomeBorderCurDist / m_BiomeBorderDefaultDist;
		}
	}
	if (percent > 0.f)
	{
		if (m_World->getRender())
		{
			bool enable = (GetWorldManagerPtr()->getWorldTime() / TICKS_ONEDAY) % 4 == 0;
			float hour = m_World->getHours();
			if (hour < 20.0f && hour > 4.0f) enable = false;
			enable |= m_AuroraEnable;
			Rainbow::SkyPlane* sky = m_World->getRender()->getSky();
			if (sky) // 极光效果
			{
				sky->SetAuroraCouldShow(enable);
				sky->SetAuroraStrength(percent);
			}
		}
	}
	else
	{
		if (m_World->getRender())
		{
			Rainbow::SkyPlane* sky = m_World->getRender()->getSky();
			if (sky)
			{
				sky->SetAuroraCouldShow(false || m_AuroraEnable);
			}
		}
	}
}
void Environment::AdjustByStep(float& currentParameter, const float targetParameter, const float step)
{
	if (currentParameter < targetParameter)
	{
		currentParameter += step;
		if (currentParameter > targetParameter) currentParameter = targetParameter;
	}
	else if (currentParameter > targetParameter)
	{
		currentParameter -= step;
		if (currentParameter < targetParameter) currentParameter = targetParameter;
	}
}
void Environment::AdjustByStep(Rainbow::ColorRGBAf& currentColor, const Rainbow::ColorRGBAf& targetColor, const float step) {
	AdjustByStep(currentColor.r, targetColor.r, step);
	AdjustByStep(currentColor.g, targetColor.g, step);
	AdjustByStep(currentColor.b, targetColor.b, step);
	AdjustByStep(currentColor.a, targetColor.a, step);
}
void Environment::biomeTick()
{
	if (!m_World || !GetIPlayerControl()) return;
	if (m_Tick % 2 == 0)
	{
		getBiomeBorderDistance(GetIPlayerControl()->GetPlayerControlPosition());

		Rainbow::SkyPlane* sky = m_World->getRender()->getSky();
		if (sky)
		{
			float fogStart = 30.0f;
			float fogEnd = 50.0f;
			//Rainbow::EnvData envData;

			int lv = getBiomeFogLevel();

			if (m_CurBiomeType != MAX_BIOME_TYPE && m_NextBiomeType != MAX_BIOME_TYPE)
			{
				if (GetDefManagerProxy() != nullptr)
				{
					auto curBiomeDef = GetDefManagerProxy()->getBiomeDef(m_CurBiomeType);
					if (curBiomeDef )
					{
						auto type = (Rainbow::BiomeSkyBoxType)curBiomeDef->skyboxType;
						sky->getFogRange(fogStart, fogEnd, lv, type);
						sky->getEnvData(m_EnvData, type);
					}
					if (m_NextBiomeType != 0)
					{
						auto nextBiomeDef = GetDefManagerProxy()->getBiomeDef(m_NextBiomeType);
						if (nextBiomeDef)
						{
							if (sky->GetWeatherType() != Rainbow::WeatherType::WEATHER_SUNSHINE)
								sky->SetTerrainBiomeSkyBoxLerp(0.f, (Rainbow::BiomeSkyBoxType)nextBiomeDef->skyboxType);
							//else if (!nextBiomeDef->ID == 18)//河流不要切换天空盒参数
							else 
								sky->SetTerrainBiomeSkyBoxLerp(m_NextBiomeTypePercent, (Rainbow::BiomeSkyBoxType)nextBiomeDef->skyboxType);
						}
					}
					else if (m_CurBiomeType != MAX_BIOME_TYPE)
					{
						if (curBiomeDef)
						{
							sky->SetCurBiomeSkyBoxType((Rainbow::BiomeSkyBoxType)curBiomeDef->skyboxType);
							if (sky->GetWeatherType() != Rainbow::WeatherType::WEATHER_SUNSHINE)
								sky->SetTerrainBiomeSkyBoxLerp(0.f, (Rainbow::BiomeSkyBoxType)curBiomeDef->skyboxType);
							//else if (!curBiomeDef->ID == 18)//河流不要切换天空盒参数
							else
								sky->SetTerrainBiomeSkyBoxLerp(1.0f, (Rainbow::BiomeSkyBoxType)curBiomeDef->skyboxType);
						}
					}

				}
			}
			AdjustByStep(m_BiomeFogStart, fogStart,0.5f);
			AdjustByStep(m_BiomeFogEnd, fogEnd,0.5f);
			AdjustByStep(m_WindStrength, m_EnvData.windStrength,0.05f);
			AdjustByStep(m_ShadowIntensity, m_EnvData.shadowIntensity,0.05f);
			m_EnvData.waterWaveSpeed = m_World->m_Environ->m_WaterWaveSpeed ;
			AdjustByStep(m_WaterColor, m_EnvData.waterColor,0.05f);
			AdjustByStep(m_IsWaterGlow, m_EnvData.isWaterGlow,0.05f);
		}
	}
}

int Environment::getBiomeFogLevel()
{
	int viewRange = Rainbow::GetMiniCraftRenderer().GetClientSetting().m_ViewSectionRange;
	int lv = 0;
	switch (viewRange)
	{
		//case 2:		lv = 0;
		//	break;
	case 4:		lv = 1;
		break;
	case 8:		lv = 2;
		break;
	case 10:	lv = 3;
		break;
	case 16:	lv = 4;
		break;
	default:	lv = 0;
		break;
	}

	return lv;
}

void Environment::playFengyeeffect(WCoord blockpos)
{
	if (m_Tick % 3600 == 0)
		playEnvironmentParticle(m_ParticleLeafVec, blockpos, "prefab/particles/scene_fengye.prefab", 0);
}