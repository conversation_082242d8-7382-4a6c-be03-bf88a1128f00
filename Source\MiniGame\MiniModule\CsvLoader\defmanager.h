#ifndef __DEFMANAGER_H__
#define __DEFMANAGER_H__

#include "defdata.h"
#include "DefDataTable.h"
#include <string>
#include <set>
#include "CsvParser.h"
#include "ICsvLoadConfig.h"
#include "IWorldExports.h"
#include "DefManagerProxy.h"
#include "ClientMob.h"
#include "ClientMacrosConfig.h"

#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
#include "HomeDefdata.h"//暂时要引用  eve1合并后在修改
#endif
#include "BaseClass/SharePtr.h"
#include "Graphics/Texture2D.h"
#include "SandboxGame/SandboxGameDef.h"
#include "Utilities/robin_map.h"

class MultiLanCSVParser;
class AbsCsv;
//tolua_begin
struct BiomeMapGrid
{
	const BiomeDef *def[4];
	float weight[4];
};
//tolua_end
class ChunkRandGen;
class ChestSpawnCsv;
class SkinningToolCsv;
class DirtyWordFilter;
class DefManager : public DefManagerProxy//tolua_exports
{//tolua_exports
public:
	DefManager();
	~DefManager();
	static DefManager* getSingletonPtr();

	void initCsvLoadConfig();
	Rainbow::SharePtr<Rainbow::Texture2D> getItemTexByItemId(int itemid);
	//ICsvLoadConfig* getCsvLoadConfig();
	//tolua_begin
	enum CfgIdType
	{
		CfgIdType_Ori = 1,      // 原始配置id
		CfgIdType_Custom = 2,   // 自定义配置id
		CfgIdType_All = 3,      // 原始的和mod的
	};
	bool IsInit() override;
    /**
    @brief 重构过渡函数
    */
	static const char* ColumnLang(const MINIW::CSVParser::TableLine &row, const char *col, int lang);
	static unsigned int CalItemCrc(const ItemDef *itemdef, const BlockDef *blockdef);
	static ENCHANT_TYPE Name2EnchantType(const char* name);
	ENCHANT_TYPE Name2EnchantTypeWrapper(const char* name) override;

	void setUserTypePointer();

	//std::vector<BiomeDef *> &getBiomes();
	const BiomeMapGrid &getBiomeDef(int heat, int humid);

	int getTreeDefID(const char *name);
	const TreeDef *getTreeDef(int id);

	const CraftingDef *findCrafting(int gridx, int gridy, const int *ids, const int *counts, int &nresult);

	VoxelPalette *getVoxlPalette(int i);

	unsigned int calCrcCode(int t);
	bool checkCrcCode(int t); //t=0: 合成表,  1: 成就表,  2: 物品表, 3: mobspawner.csv, 4: monsterdef.csv, 5: horseegg.csv
	
	std::string getRandomName(int sex); //0:无性别限制, 1-male, 2-female
	std::string filterString(char *content, bool checknum = true);
	//效率更高的函数
	void filterStringDirect(char *content);
	bool checkFilterString(const char *content);
	//void addFilterString(char* content);   //动态增加一个过滤词
	void addFilterString(char* content, unsigned int type1 = 0, unsigned int type2 = 0, unsigned int score = 0); 
	void delFilterString(char * content);
	void addInvalidCode(char * content);   //动态增加一个要过滤的无效字符(包含汉字,号等)
	void translateLetter(std::string title, std::string content, std::string& transTitle, std::string& transContent);
	void translateBookCover(std::string title, std::string author, std::string& transTitle, std::string& transAuthor);
	std::string getTransStrByKey(TRANSLATE_TYPE type, std::string key, std::string oldVal = "", std::string oldMultiLanVal = "");
	void getS2(std::string& s2_, std::string& s2t_);
    /**
    @deprecated
    */
	ItemDef *getItemDef(int id, bool takeplace=false);
	ItemDef* addItemDef(int id, int type, std::string model = "", std::string name = "", std::string desc = "", short involvedid = 0);
	ItemDef* addItemDefByCopy(int id, int type, int copyId);
	bool addModItemDef(int id, ItemDef* def);
	bool removeModItemDef(int id);
	bool addModBlockDef(int id, BlockDef* def);
	bool removeModBlockDef(int id);
	int	getItemNum();
	int	getToolNum();
	ItemDef* getAutoUseForeignID(int id);
	int GetPlayerBuffAtkType(int buff);

	bool checkItemCrc(int itemid);
    void removeCustom(int id, int type, bool needresetcrc = false, int involvedid = 0);
	void clearAllCustomItem();
	bool getItemsByGroup(int id, std::vector<int>& out);

	//for pack gift
	PackGiftDef* getPackGiftDef(int iPackID);
	PackGiftDef* getPackGiftDefByItemID(int iItemID);

	BiomeDef *getBiomeDef(int id);
	BiomeDef* addBiomeDefByCopy(int id, int copyId);
	const BiomeDef *getOriginalBiomeDefById(int terrainId);	//(地表生成)获取原始的的定义.
	const int getOriginalBiomeArrayVal(int terrainId, int blockId, const char* strType);	//(地表生成)获取原始数据, 数组里面的(如:花)
	int getBiomeDefCount(); //系统默认的biome数量
	DefDataTable<OreDef> &getOriginalOreTable();

	/*
		(矿物生成)获取原始的的矿物定义. 这里用的blockid 默认的mapid是用的0
		default true没有找到返回默认值401对应的OreDef  false 没有找到就返回nullptr
	*/
	const OreDef *getOriginalOreDefById(int blockId, bool defaultOreId = true, bool igoremapid=false);

	PlanetBiomeGeneDef *getPlanetBoimeGeneDef();
	
    /**
    @deprecated
    */
	BlockDef *getBlockDef(int id, bool takeplace = true);
	BlockDef* addBlockDef(int id, int type, const std::string& model = "", const std::string& name = "", const std::string& desc = "") override;
	BlockDef* addBlockDefByCopy(int id, int type, int copyId);
	void clearTempBlockDef();
	int getMaxID();
	std::vector<BlockDef*>& getBlockDefTable();
	const BlockEffectDef* getBlockEffectDef(const char* blockTypeName);


    /**
    @deprecated
    */
	const FoodDef *getFoodDef(int id);
	int getFoodNum();
	const FoodDef *getFoodDefByIndex(int index);
	FoodDef* addFoodDefByCopy(int id, int copyId);
    FoodDef* getOriginalFoodDef(int id);
	void removeFoodDef(int id);
	bool isFood(int id);

	//天气地形组
	BiomeGroupDef* getBiomeGroupDef(int id);
	int getBiomeGroupNum();
	BiomeGroupDef* getBiomeGroupDefByIndex(int index);

	virtual void ParseWCoord(const std::string& str, WCoord& posData);
	virtual void ParseItemPosData(const std::string& str, ItemPosDataDef& posData);
	ItemInHandDef* getItemInHandDef(int id);

	const ItemSkillDef* getItemSkillDef(int id);
	DefDataTable<AchievementDef> &getAchievementTable();
	const AchievementDef *getAchievementDef(int id);
	//附魔
	int getEnchantNum();
	const EnchantDef *getEnchantDefByIndex(int index);
	const EnchantDef *getEnchantDef(int id);
	void setCurAccordEnchants(int tooltype);
	int getCurAccordEnchantsNum();
	const EnchantDef *getCurAccordEnchantDef(int index);
	const EnchantMentDef *getEnchantMentDef(int type);
	int getBuffDefNum();
	const BuffDef *getBuffDef(int id, int level);
	const BuffDef *getBuffDef(int id);
	const BuffDef *getBuffDefByIndex(int index);

	BuffDef* getStatusDefRaw(int id);				// 只从 m_BuffTable 获取定义， 新的prefeb插件系统用
	BuffDef* addStatusDefRawByCopy(int id, int copyId);	// 往 m_BuffTable 添加定义
	bool finalizeStatusDefRaw();					// 添加或者删除buff配置的一些收尾操作
	void removeStatusDefRaw(int id);
	//符文code by:tanzhenyu
	const RuneDef *getRuneDef(int id);
	//状态
	int getStatusNum(int type = 3);
	int getRealStatusId(int id, int lv);
	bool isCustomStatus(int id);
	bool isEquipStatus(int id);
	const BuffDef* getStatusDef(int id);//此处id是完整的状态id
	const BuffDef* getStatusDefByIndex(int index, int type = 3);
    const std::vector<int> GetBuffByNature(char nature);
    std::vector<int> GetBuffNatureNameByNature(char nature);

	/*冒险20220623 codeby：zhangyusong*/
	const BuffDef* getToolStatusDef(int id);
	int getBuffEffectDefNum();
	const BuffEffectDef* getBuffEffectDef(int id);
	const BuffEffectDef* getBuffEffectDefByIndex(int index);

	int getBuffEffectEnumDefNum();
	const BuffEffectEnumDef* getBuffEffectEnumDef(int id);
	const BuffEffectEnumDef* getBuffEffectEnumDefByIndex(int index);

	const BuffEffectSliderDef* getBuffEffectSliderDef(int id);

	int getIconLibDefNum();
	const IconLibDef* getIconLibDef(int id);
	const IconLibDef* getIconLibDefByIndex(int index);

	const GunDef *getGunDef(int id);
	GunDef* getOrignalGunDef(int id);
	int getAchievementDefNum();
	int getFurnaceDefNum();
	const FurnaceDef *getFurnaceDef(int id);
	const FurnaceDef *getFurnaceDefByIndex(int index);
	const FurnaceDef *getFurnaceDefByMaterialID(int materialid, bool takeplace = false);
	const FurnaceDef *getFurnaceDefByMaterialIDWithType(int materialid, bool takeplace = false, int type = 1);
	const FurnaceDef* getFurnaceDefByResult(int resultid);
	int getCraftingDefNum();
	int getCraftingDefNumCount(int resultId);//获取有多少种加工配方
	CraftingDef* findCraftingBySort(int resultId,int times);//按顺序获取加工配方
	CraftingDef *findCrafting(int resultId);
	CraftingDef* findCraftingByDoubleWeapon(int rightWeaponId, int leftWeaponId);
	CraftingDef *getCraftingDef(int id, bool takeplace=false, bool getDefause = true);
	CraftingDef* getCraftingDefByCopy(int id, int nCopyId);
	CraftingDef* getCraftingDefByIndex(int index);
	const CraftingDef* findDisassemble(int resultId);
	bool isCraftingType(int resultId, int tabIndex);
	int getCraftEmptyHandID() { return 11000; }
	int getCraftDefaultToolID() { return 11616; }
	
	//获取食谱相关函数
	int getCookbookNum();
	CraftingDef* getCookbookDef(int id, bool takeplace = false, bool getDefause = true);
	CraftingDef* getCookbookByIndex(int index);
	CraftingDef* findCookbookByResultId(int resultId);
	bool isCookbookType(int resultId);
	//通过配方获取相应具体配置
	CraftingDef* getCookBookByDef(std::unordered_map<int, int>& materialMap);


	const SprayPaintDef* getSprayPaintDef(int id);
    /**
    @deprecated
    */
	const ToolDef *getToolDef(int id);
	ToolDef* addToolDefByCopy(int id, int copyId);
	ToolDef* getOriginalToolDef(int id);

    DefDataTable<ToolDef>& getToolTable();
    std::map<int, int>& getMineToolIcon();
    /**
    @deprecated
    */
	int getMonsterDefNum();
	MonsterDef *getMonsterDef(int id, bool takeplace=false, bool bUseOne = false);
	MonsterDef* addMonsterDef(int id, int type, const std::string& model = "", const std::string& name = "") override;
	MonsterDef* addMonsterDefByCopy(int id, int copyId, int type, const std::string& model = "", const std::string& name = "") override;
	MonsterDef* getOriginalMonsterDef(int id);
	ProjectileDef* addProjectileDefByCopy(int id, int copyId);
	ProjectileDef* getProjectileDef(int id, bool takeplace = false);
	ProjectileDef* getOriginalProjectileDef(int id);
	const MonsterDef *getMonsterDefByIndex(int index);
	DefDataTable<MonsterDef>& getMonsters();
	MonsterDef* getIgnoreEditPlugin(int id, bool takeplace = false);

	MonsterSpawnDef* getMonsterSpawnDef(int type);

	DefDataTable<HorseEggDef> &getHorseEggTable();
	DefDataTable<HorseDef> getHorses();
	const HorseDef *getHorseDef(int id);
	const char *getStringDef(int id);
	RoleDef *getRoleDef(char model, int geniuslv);
	RoleDef* getRoleDef(int roleID);
	int getRoleDefNum();

	BuildReplaceDef *getBuildReplaceDef(int id) override;
	
	WorkbenchTechCsvDef* findItembenchTech(int itemid);
	TechTree* getWorkbenchTechTree(int level);
    /**
    @deprecated
    */
	RoleSkinDef *getRoleSkinDef(int id);
	RoleSkinDef* GetRoleSkinByIndex(int index);
	RoleSkinDef* GetRoleSkinByName(const std::string& name);
	int GetRoleSkinCount();

	AvatarModelDef *getAvatarSkinDef(char *uin, int modelId);
	AvatarModelDef *getAvatarSkinByIndex(int index);
	int getAvatarSkinNum();

	const StorePropDef *getStorePropByIndex(int i);
	StorePropDef *getStorePropByID(int id);
	int getStorePropNum();
	//const HomeTraderDef *getHomeTraderByIndex(int i);
	//HomeTraderDef *getHomeTraderByItemID(int id);
	//int getHomeTraderNum();
	//20210913：修复迷你币配置表删除部分档位其他档位无法显示的BUG  codeby：范伊蒙
	MiniCoinDef *getMiniCoinDefByIndex(int index);
	MiniCoinDef *getMiniCoinDef(int id);
	int getMiniCoinNum();
	StoreHorseDef *getStoreHorseByIndex(int i);
	StoreHorseDef *getStoreHorseByID(int id);
	int getStoreHorseNum();
	//通过生物id来判断是不是商场坐骑
	bool isStoreHorseById(int id);
	const HorseAbilityDef *getHorseAbilityDef(int id);
	const NpcTradeDef *getNpcTradeDef(int id);
	int getNpcTradeNum();
	bool isNpcTrade(int monsterid);
	SignInDef *getSignInDef(int signintype, int whatday);
	const ExtremityScoreDef *getExtremityScoreDef(int Type, int GoalID=0);
	const HeadIconDef *getHeadIconDef(int id);
	int getHeadIconNum();
	DefDataTable<ChestDef> &getChestDefTable();
	const ChestDef *getChestDef(int id);
	DefDataTable<ChestSpawnDef>& getChestSpawnTable();
	DefDataTable<SkinningToolDef>& getSkinningToolTable();
	PlantDef *getPlantDef(int level);
	FruitDef *getFruitDef(int id);
	const BookDef *getBookDefByID(int id);
	const BookDef *getBookDefByItemID(int id);
	int getBookNum();
	const BookSeriesDef *getBookSeriesDef(int id);
	const RecycleDef *getRecycleDef(int id);
	DefDataTable<GameRuleDef> &getGameRuleTable();
	int getGameRuleNum();
	const GameRuleDef *getGameRuleDef(int id);
	const RuleOptionDef *getRuleOptionDef(int id);
	FuncSwitchDef *getFuncSwitchDef(int id);
	TaskDef *getTaskDef(int id);
	PlotDef *getPlotDef(int id);
	int getHotkeyNum();
	HotkeyDef *getHotkeyDef(int id);
	HotkeyDef *getHotkeyDefByKey(const char *key);
	int getPlantTreesNum();
	PlantTreesDef *getPlantTreesDef(int index);

	int getRoleActionNum();
	RoleActionDef *getRoleActionDef(int index);
	BPSettingDef *getBPSettingDef(int index);
	BPMissionDef *getBPMissionDef(int index);
	BPRewardDef *getBPRewardDef(int index);
	BPDrawDef *getBPDrawDef(int index);
	short getBPSettingReward(int season, int type, int index);
	int getBPSettingDefNum();
	int getBPMissionDefNum();
	int getBPRewardDefNum();
	int getBPDrawDefNum();
	short getBPSettingMaxIntegral(int season, int index);
	int getBPSettingMissionNum(int season);
	short getBPSettingMission(int season, int index);
	const char *getKeyName(int code);
	static MODATTRIB_TYPE Name2ModAttrib(const char *name);

	int getBiomeDefNum(int fieldid, int id, int index);
	int getBiomeDefId(int fieldid, int id, int index);
	void setLanguage(int i);

	DefDataTable<MobSpawnerDef> &getMobSpawnerTable();
	int getRandomDungeonSpawner(ChunkRandGen *randgen);

	int getFileToLoadNum();
	FileToLoad *getFileToLoadDef(int id);

	int getFileNoToLoadNum();
	std::string &getFileNoToLoadDef(int id);

	std::vector<ModsToLoad> &getModsToLoadDefTable();
	int getModsToLoadNum();
	ModsToLoad *getModsToLoadDef(int id);

	int getAntiCrackToLoadNum();
	AntiCrack *getAntiCrackToLoadDef(int id);
		
	std::vector<int> &getNpcPlotIds();
	int getNpcPlotDefNum();
	NpcPlotDef *getNpcPlotDef(int id);
	NpcPlotDef *getNpcPlotDefByIndex(int index);

	//可配置对话系统
	int getNpcPlotConfigurableDefNum();
	NpcPlotDef *getNpcPlotConfigurableDefByIndex(int index);
	NpcPlotDef *getNpcPlotConfigurableDefByID(int id);
	
	std::vector<int> &getNpcTaskIds();
	int getNpcTaskDefNum();
	NpcTaskDef *getNpcTaskDef(int id);
	NpcTaskDef *getNpcTaskDefByIndex(int index);
	NpcTaskDef *getNpcTaskDefByIndexAnyway(int index);

	std::vector<int> getNpcShopIds();
	int getNpcShopDefNum();
	NpcShopDef* getNpcShopDef(int id);
	NpcShopDef* getNpcShopDefByIndex(int index);
	int getNpcShopNpcInnerId(std::string key, unsigned int foreign_id, int default_value);
	int getNpcShopNpcRealId(std::string key, int default_value);

	int getDialogueNum(int type, int id, int taskstate=0);
	const DialogueDef *getDialogueDef(int type, int id, int index, int taskstate=0);
	const AnswerDef *getAnswerDefByDialogue(const DialogueDef *def, int index);
	const PlayActDef *getPlayActDef(int id);
	int getPlayActDefNum();
	const PlayActDef *getPlayActDefByIndex(int index);

	bool reloadPhysicsMaterialCSV();
	const PhysicsMaterialDef *getPhysicsMaterialDef(int id);
	int getPhysicsMaterialDefNum();
	const PhysicsMaterialDef *getPhysicsMaterialDefByIndex(int index);

	bool reloadPhysicsPartsCSV();
	std::vector<int> &getPhysicsPartIds();
	const PhysicsPartsDef *getPhysicsPartsDef(int id);
	int getPhysicsPartsDefNum();
	const PhysicsPartsDef *getPhysicsPartsDefByIndex(int index);
	void AddPhysicsPartKeyDefFromFunc(std::vector<PhysicsPartKeyDef>&Keys, jsonxx::Object FuncDef);
	bool reloadPhysicsPartsTypeCSV();
	PhysicsPartsTypeInfoDef* getPhysicsPartsTypeDef(int iType, int iSubType);
	PhysicsPartsTypeInfoDef* getPhysicsPartsTypeDefWithPartsId(int iPartsId);
	int getPhysicsPartKeysNum(int PartsId);
	const PhysicsPartKeyDef* getPhysicsPartKeyDefByIndex(int PartsId,int index);

	int getPhysicsPartsConnectDefNum();
	PhysicsPartsConnectDef* getPhysicsPartsConnectDef(int controlType, int controlSubType,int workType, int workSubType);
	PhysicsPartsConnectDef* getPhysicsPartsConnectDefByIndex(int index);

	PhysicsActorDef* getPhysicsActorDef(int id);
	PhysicsActorDef* addPhysicsActorDefByCopy(int id, int copyId);
	const RecordEffectDef *getRecordEffectDef(int id);
	int getRecordEffectDefNum();
	const RecordEffectDef *getRecordEffectDefByIndex(int index);
	int getARGradeDefSize();
	ARGradeDef *getARGradeDef(int id);
	int getARStyleDefSize();
	ARStyleDef *getARStyleDef(int id);

	const SkinActDef* getSkinActDef(int id, int skinid);
	const SummonDef* getSummonByID(int id);

	/**
	   获取ResourcePackDefCsv相关数据
	*/
	const ResourcePackDef* getResourcePackDef(int id);

	const FishingDef* getFishingDef(int id);
	bool isFishNeedUp(int id);
	int getOneFishingResult(int biome, int toolId, bool isTempest);

	STGameZone* countryGameZone(const char* country);
	const std::string& getCountryCode(const std::string* code);
	
	const ColorMixDef* getColorMixDef(int id);
	int getColorMixDefDefNum();
	int getColorMixDefMixColorID(int id, int mixid);
	unsigned int getColorMixDefColor(int id);

	int getTriggerActDefNum();

	//获取触发器选项配置
	TriggerItemDef *getTriggerItemDef(int id);
	TriggerEnumDef *getTriggerEnumDef(int id);
	TriggerFunctionDef *getTriggerFunctionDef(int id);
	TriggerParamDef *getTriggerParamDef(int id);
	TriggerActDef* getTriggerActDef(int id);
	TriggerActDef* getTriggerActDefByIndex(int index);
	int getTriggerActBySrcAct(int act);
	//获取脚本API配置
	ScriptAPIDef *getScriptAPIDef(int id);

	//特效
	int getParticleDefNum();
	ParticleDef* getParticleDef(int id);
	ParticleDef* getParticleDefByIndex(int index);
	ParticleDef* getParticleDefByPath(const char* path);	//根据特效的路径全名获得定义
	ParticleDef* getParticleDefByPath2(const char* path);
	ParticleDef* getParticleDefByName(const char* name);	//根据特效的名字获得定义
	int getParticleDefTypeNum(int type);
	ParticleDef* getParticleDefByType(int type, int index);
	
	//音效
	int getSoundDefNum();
	SoundDef* getSoundDef(int id);
	SoundDef* getSoundDefByIndex(int index);
	SoundDef* getSoundDefByPath(const char* path);
	/*
		通过type和index获取soundid
	*/
	int	getSoundIdByTypeAndIndex(int type, int index);
	/*
		获取对应type的所有音效数量
	*/
	int getSoundTypeDefNum(int type);
	//素材库
	int getTriggerResourceTypeDefNum();
	TriggerResourceType* getTriggerResourceTypeDefByIndex(int index);
	//野人名字
	const char *getWildmanNameDef(int id);
	int getWildmanNameDefNum();
	void addCannotReadPath(std::string &path);
	void setCannotReadPathReady(bool ready);
	ICsvLoadConfig& getCsvLoadConfig();
	bool load();
	bool loadEx();
	bool loadFirst();
	bool loadInLoading( int step = 0 );
	//睡觉buff改版
	std::map<std::string, int> BiomeTotalWeight;//每个生态的总权重
	std::map<int, std::map<std::string, int>> BiomeBuffWeight;//每个生态的权重
	BIOME_TYPE getbiot(const char *name) override;
	int getCurBiomeBuff(int biomeid);
	int getCurBiomeBuffFixed(int biomeid,float fixed, std::vector<int> SleepDebuff);
	void SetSleepBiomeWeight();
	void resetCrcCode(int type);

	void addDefByCustomModel(int id, int type, std::string filename="", std::string name = "", std::string desc = "", Rainbow::Vector3f box = Rainbow::Vector3f(0, 0, 0), short involvedid=0);
	GunDef* addGunDef(int id);
	GunDef* addGunDef(int id, const GunDef* templateGunDef);
	GunDef* addGunDefByCopy(int id, int copyId);
	void getNpcShopChangeConfigStatus(const char* path, std::map<int, std::vector<int> >& mNpcShopChangeInfo);
	void setNpcShopChangeConfigStatus(const char* path, const std::map<int, std::vector<int> >& mNpcShopChangeInfo);

	void linkTriggerAct2AnimAct(TriggerActDef* def);

	int addEquipStatus(int id);
	int getStatusIdByEquipId(int id);
	int addToolStatus(int id);
	int getStatusIdByToolId(int id);

	void parseNpcPlotTemplateDate(NpcPlotDef &def, const MINIW::CSVParser::TableLine parserLine);

	std::vector<CharacterDef> &getCharDefTable();
	ChunkRandGen *getRandGen();
	std::vector<int> &getMonsterBiomesIDs();
	BiomePlantTryDef& getBiomePlantTries();
	bool isSpecialUin(int uin);

	//海外定制化配置
	int getOverseasGrayNum();
	OverseasGrayDef* getOverseasGrayDef(int index);
	void toLowerCaseMultilingual(core::string &msg);
	DefDataTable<RuneDef>& getRuneDefTable();
    std::map<int, std::set<int>>& GetMaterialIdSet();
	WikiConfigDef* getWikiConfigDef(int index);

	//音乐系统配置 乐器配置
	const MusicalDef *getMusicalDef(int id);
	const MusicalDef *getMusicalDefByCode(int code);
	ScoreDef  *getScoreDefByIndex(int index);
	int  getScoreTableSize();

	// midi 获取乐器编码表信息
	InstrumentDef *getInstrumentDef(int ID);

	int getDriftBottleOfficalTextNum();
	const DriftBottleOfficialText* getDriftBottleOfficialTextByIndex(int index);

	int getLettersOfficalTextNum();
	const LettersOfficialText* getLettersOfficialTextByIndex(int index);
	std::vector<LettersOfficialText*> getLettersOfficialDef()
	{
		return m_LettersOfficialText;
	}
	bool copyLettersOfficialText(std::vector<LettersOfficialText*> value);

	int getToolTypeNeedMaterialIdsNum(int tooltype);
	int getToolTypeActiveIdByIndex(int tooltype, int index);
	/**
	@brief csv tolua对象初始化
	**/
	void HomelandInitCsv();

	//获取UgcModelDef
	UgcModelDef* getUgcModelDef(int id) override;
	//获取ugcMaterialDef
	UgcMaterialDef* getUgcMaterialDef(int id) override;

	ItemEquipDef* addEquipDef(int id) override;
    ItemEquipDef* getEquipDef(int id) override;
	void removeEquipDef(int id) override;

    void removeCustomGunDef(int id) override;
	CustomGunDef* addCustomGunDef(int id) override;
    CustomGunDef* getCustomGunDef(int id) override;

	ItemStatusDef* addItemStatusDef(int id) override;
	ItemStatusDef* getItemStatusDef(int id) override;
	bool IsShowUseBtnForItemRightUse(int id) override;
	void removeItemStatusDef(int id) override;

	CustomPrefabDescInfo* getCustomPrefabDescInfo(std::string name) override;
	CustomPrefabDescInfo* addCustomPrefabDescInfo(std::string name) override;

	std::map<std::string, int>& GetParticlesStrDefCsvStrMap();
	std::map<int, std::string>& GetParticlesStrDefCsvIdMap();
	std::map<std::string, int>& GetSoundStrDefCsvStrMap();
	std::map<int, std::string>& GetSoundStrDefCsvIdMap();

	virtual OperateUIData* getOperateUIData(int id) override;
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
		//家园制作
	int getHomeProducerDefNum();
	HomeProducerDef *getHomeProducerDef(int id);
	HomeProducerDef *getHomeProducerDefByIndex(int index);

	int getHomeTabDefNum();
	const HomeTabDef* getHomeTabDefByIndex(int index);
	const HomeTabDef* getHomeTabDefById(int id);

	int getHomeItemDefDefNum();
	const HomeItemDef *getHomeItemDef(int itemid);
	const HomeItemDef *getHomeItemDefByIndex(int index);
	bool findHomeItemDefFunctionId(int itemid, int functionid);

	//家园Npc
	int getHomeNpcDefNum();
	bool getHomeNpcDef(std::vector<HomeNpcInfo> &vec, int type);
	const HomeNpcInfo *getHomeNpcDef(int type, int id);
	const HomeNpcInfo *getHomeNpcDefByIndex(int type, int index);


	//宠物
	int getPetDefNum();
	const PetInfoDef *getPetDef(int id, int stage, int quality);
	const PetInfoDef* getPetDefByIndex(int index, int stage, int quality);

	int getPetSkillsDefNum();
	const PetSkillsDef *getPetSkillsDef(int id);
	const PetSkillsDef *getPetSkillsDefByIndex(int index);


	int getPetExploreDefNum();
	const PetExploreDef *getPetExploreDef(int id);
	const PetExploreDef *getPetExploreDefByIndex(int index);

	const PetEventDef *getPetEventDef(int id);

	// 家园等级配置
	const HomeLvDef *getHomeLvDef(int id);
	int getHomeLevelDefNum();

	int getHomePackDef(int id);
	const AwardPackDef *getHomePackAwardDefByIndex(int id, int index);
	/*
		获取家园地图size(通过HomeChunkDef表配置的最大值最小值换算得来)
	*/
	int getHomeLandMaxX();
	int getHomeLandMaxZ();

	//系列抽奖表
	int getHomeDrawDefNum();
	const HomeDrawDef *getHomeDrawDefByIndex(int index);
	const HomeDrawDef *getHomeDrawDefById(int id);

	//家园扩建表
	int getHomeBuildDefNum();
	const HomeBuildDef *getHomeBuildDefByIndex(int index);
	const HomeBuildDef *getHomeBuildDefById(int id);

	//家园交易表
	int getHomeTraderDefNum();
	const HomeTraderDef *getHomeTraderDefByIndex(int index);
	const HomeTraderDef *getHomeTraderDefById(int id);

	//家园物品解锁表
	int getHomeItemUnlockDefNum();
	const HomeItemUnlockDef *getHomeItemUnlockDefByIndex(int index);
	const HomeItemUnlockDef *getHomeItemUnlockDefById(int id);

	// 根据农场农田id获取农田信息
	const FarmInfoDef* getFarmInfoDef(int farmlandID);

	// 获取农作物种子信息
	const HomeCropsSeedDef* getHomeCropsSeedDef(int cropsSeedId);
	// 获取生物蛋信息
	const HomeAnimalDef* getHomeAnimalDef(int animalseedid);

	// 获取农场农田的数量
	int getFamrInfoDefNum();

	//宠物特性包信息
	int getPetSkillPackDefNum();
	const PetSkillPackDef* getPetSkillPackDefById(int id);

	int getHomeTaskDefNum();
	HomeTaskDef *getHomeTaskDef(int id);
	HomeTaskDef *getHomeTaskDefByIndex(int index);

	/*
		获取农场信息
	*/
	const HomeRanchDef* getHomeRanchDef(int PastureLv);
	//获取神秘商人配置表信息
	const HomeMysticalDef* getHomeMystiaclDef(int MysticalLv);
	/*
		获取语音区域
	*/
	int getVoiceZone(const char* country);
	/*
		家园地图chunk配置
	*/
	const HomeChunkInfoDef* getHomeChunkDef(int chunx, int chunz);

	//获取家园特定区域所在chunk位置,只返回配置表中第一个chunk的序号（返回值是 x * 1000 + z,这里只是chunk的序号，算位置需要乘以SECTION_BLOCK_DIM）
	bool getHomeRegionChunkPos(int regionType, int& chunkx, int& chunkz);

	//获取家园指定区域的 所有chunk索引
	void getHomeChunksByChunkID(int regionType, int chunkfunction, std::vector<HomeChunkListInfo>& chunklists, std::vector<HomeChunkListInfo>* disableChunklist = NULL);

	/*
	* 机器人对话框配置
	*/
	int getBotConversationsDefNum();
	const BotConversationsDef* getBotConversationsDefIndex(unsigned int index);
	const BotConversationsDef* getBotConversationsDef(unsigned int id);
#endif
	void SetClientActorPetTamedOwnerUin(ClientMob* mob);
	DevUIResourceDef* GetDevUIResourceDef(int id, bool takeplace = false);
	int GetDyeColor(int id);
	//tolua_end

	virtual void ParseTypeCollides(const std::string& jsonColliedes, std::vector<TypeCollideAABB>& collides);

	std::map<int, CraftingDef*> getCustomCraftingDefMap();
	std::vector<CraftingDef>getCraftingDefByType(int type);
	std::vector<CraftingDef>getCraftingDefBySubType(int type, int subtype);
	const SurviveObjectiveDef* getSurviveObjectiveDef(unsigned int id);
    int getSurviveObjectiveDefCsvNum();
	const SurviveObjectiveDef* getSurviveObjectiveDefByIndex(int index);
    int getSurviveObjectiveFrontTaskNum(unsigned int taskID);
    int getSurviveObjectiveFrontSize();
    const SurviveTaskDef* getSurviveTaskDef(unsigned int taskID);
    std::vector<int> getSurviveTaskListByObjectiveID(int objectiveID) const;
    int getSurviveTaskDefCsvNum();
    int getSurviveTaskFrontSize();
    const SurviveTaskDef* getSurviveTaskDefByIndex(int index);
	void ImportTaskDef(std::unordered_map<int, SurviveTaskDef*>& in);
	void ExportTaskObjective(std::unordered_map<int, std::vector<int>>& out);
	void ImportTaskObjective(std::unordered_map<int, std::vector<int>>& in);
	void onParseTask(CSVParser& parser, std::unordered_map<int, SurviveTaskDef*>& out);
	int getFrontTaskNum(int taskid);
	void Import(std::unordered_map<int, SurviveObjectiveDef*>& in);
	void onParseObjective(CSVParser& parser, std::unordered_map<int, SurviveObjectiveDef*>& out);
	int getItemBakeTo(int materialId);
	//获取刷怪房配置
	DefDataTable<DungeonsDef>& getDungeonsDefTable();
    DungeonsDef* getDungeonsDefById(int id);
	//获取怪物雕像配置
	DefDataTable<MonsterStatueDef>& getMonsterStatueDefTable();
	MonsterStatueDef* getMonsterStatueDefById(int id);
	const PlayerAttribCsvDef* getPlayerAttribCsvDef(PlayerAttributeType tpe) override;
	const ArchitecturalBlueprintCsvDef* getArchitecturalBlueprintCsvDef(int id) override;
	const BuildingMaintenanceCsvDef* getBuildingMaintenanceCsvDef(int id) override;
	std::vector<const BuildingMaintenanceCsvDef*> getAllBuildingMaintenanceCsvDef() override;
	const EquipGroupDef* getEquipGroupDef(int id);

	const DieInfoCsvDef* getDieinfoCsvDef(int id);
	// 根据蓝图 制造道具ID查询消耗道具和数量 (消耗道具ID -> 数量)
	std::map<int, int> getConsumedItemsByProduceItemID(int produceItemID);
private:

	ChunkRandGen *m_RandGen;

	dynamic_array<BiomeDef> m_Biomes;
	//DefDataTable<BiomeDef>m_BiomeTable;
	
	BiomePlantTryDef m_BiomePlantTries;

	DefDataTable<OreDef>m_OreTable;

	DefDataTable<TreeDef>m_TreeTable;
	DefDataTable<GunDef>m_GunTable;
	std::vector<CraftingDef *>m_Craftings;
	DefDataTable<CraftingDef>m_CraftingTable;
	std::vector<CraftingDef*>m_CookbookTable;//食谱 
	std::set<int> m_CraftingMaterialIDSet;
	
	DefDataTable<HorseDef>m_Horses;
	DefDataTable<IconLibDef>m_IconLibTable;
	std::map<int, FurnaceDef*> m_BakeTable; //熔炉中哪些烹饪是可炙烤的.
	std::vector<int>m_BuffEffectIds;
	std::vector<int>m_BuffEffectEnumIds;
	std::vector<int>m_IconLibIds;
	std::vector<FurnaceDef *>m_Furnaces;
	DefDataTable<FurnaceDef>m_FurnaceTable;
	DefDataTable<AchievementDef>m_AchievementTable;
	DefDataTable<EnchantDef>m_EnchantTable;
	DefDataTable<RuneDef>m_RuneTable;//code by:tanzhenyu
    std::map<int, std::set<int>> m_materialIds;
	std::map<int, std::vector<int>> m_toolTypeNeedMaterialIds;
	DefDataTable<EnchantMentDef>m_EnchantMentTable;
	std::vector<char *>m_RandomSurnames;
	std::vector<char *>m_RandomMaleNames;
	std::vector<char *>m_RandomFemaleNames;
	std::vector<char *>m_FilterStrings;
	std::vector<char *>m_FilterStringsWhole;  //纯英文敏感词

	std::vector<VoxelPalette *>m_VoxPalettes;
	std::vector<EnchantDef *>m_AccordEnchants;
	DefDataTable<StringDef>m_StringDefTable;
	DefDataTable<ChestDef>m_ChestDefTable;
	DefDataTable<ChestSpawnDef>m_ChestSpawnTable;
	DefDataTable<SkinningToolDef>m_SkinningToolTable;
	std::vector<CharacterDef>m_CharDefTable;
	DefDataTable<MonsterSpawnDef> m_MonsterSpawnTable;
	DefDataTable<MobSpawnerDef>m_MobSpawnerTable;
	DefDataTable<RoleDef>m_RoleTable;
	DefDataTable<MiniCoinDef>m_MiniCoinTable;
	DefDataTable<NpcTradeDef>m_NpcTradeTable;
	DefDataTable<SignInDef>m_SignInTable;
	DefDataTable<ExtremityScoreDef>m_ExtremityScoreTable;
	DefDataTable<HeadIconDef>m_HeadIconTable;
	DefDataTable<PlantDef>m_PlantTable;
	DefDataTable<FruitDef>m_FruitTable;
	DefDataTable<HorseAbilityDef>m_HorseAbilityTable;
	DefDataTable<BookSeriesDef>m_BookSeriesTable;
	DefDataTable<RecycleDef>m_RecycleTable;
	DefDataTable<GameRuleDef>m_GameRuleTable;
	DefDataTable<FuncSwitchDef>m_FuncSwitchTable;
	DefDataTable<TaskDef>m_TaskDefTable;
	DefDataTable<PlotDef>m_PlotDefTable;
	DefDataTable<HotkeyDef>m_HotkeyDefTable;
	DefDataTable<KeyDef>m_KeyDefTable;
	DefDataTable<StringDef>m_WildmanNameTable;
	DefDataTable<DungeonsDef>m_DungeonsTable;
	DefDataTable<MonsterStatueDef> m_MonsterStatueDefTable;

	std::vector<RoleActionDef>m_RoleActionTable;
	std::vector<StorePropDef>m_StorePropArray;
	//std::vector<HomeTraderDef *>m_HomeTraderArray;
	std::vector<StoreHorseDef>m_StoreHorseArray;
	//加个映射表来减少搜索量,这里只是判断是不是商场坐骑
	std::map<int, bool>m_StoreHorseIdToHave;
	std::vector<BookDef>m_BookArray;
	std::vector<AvatarModelDef>m_AvatarSkinArray;
	//公益活动用
	std::vector<PlantTreesDef>m_PlantTreesDefArray;
	//将要下载的文件
	std::vector<FileToLoad> m_FileToLoadDefTable;
	std::vector<std::string> m_FileNoToLoadDefTable;
	std::vector<ModsToLoad> m_ModsToLoadDefTable;
	std::vector<AntiCrack> m_AntiCrackDefTable;
	std::map<int, int> m_SpecialUinDefTable;

	DefDataTable<NpcPlotDef>m_NpcPlots;
	std::vector<int>m_NpcPlotIds;
	DefDataTable<NpcPlotDef>m_NpcPlotConfigurables;
	std::vector<int>m_NpcPlotConfigurablesIds;
	DefDataTable<NpcTaskDef>m_NpcTasks;
	std::vector<int>m_NpcTaskIds;
	std::vector<int>m_NpcShopIds;
	std::vector<PlayActDef> m_PlayAct;
	std::vector<RecordEffectDef> m_RecordEffectDef;


	std::vector<int> monsterBiomesIDs;

	PlanetBiomeGeneDef m_planetBiomeGeneDef;

	std::vector<PhysicsMaterialDef> m_PhysicsMaterial;
	DefDataTable<PhysicsPartsDef> m_PhysicsParts;
	DefDataTable<PhysicsPartsTypeDef> m_PhysicsPartsTypeDef;

	DefDataTable<PhysicsPartsConnectDef> m_PhysicsPartsConnectDef;
	std::vector<int> m_PhysicsPartsIds;
	//AR 评分
	std::vector<ARGradeDef>m_ARGradeDefTable;
	//AR 风格
	std::vector<ARStyleDef>m_ARStyleDefTable;


	//触发器选项配置
	std::vector<TriggerItemDef>m_TriggerItemDefTable;
	std::vector<TriggerEnumDef>m_TriggerEnumDefTable;
	std::vector<TriggerFunctionDef>m_TriggerFunctionDefTable;
	std::vector<TriggerParamDef>m_TriggerParamDefTable;
	std::map<int, TriggerActDef>m_TriggerActDefMap;
	std::map<int, int> m_TriggerActLinkMap;
	//脚本API配置
	std::vector<ScriptAPIDef>m_ScriptAPIDefTable;

	// 建筑替换配置
	std::map<int, BuildReplaceDef> m_BuildReplaceTable;

	//音效
	std::vector<SoundDef>m_SoundDefTable;
	std::map<int, std::vector<int>>	m_SoundDefTypeTableMap; //音效类型 分类索引添加<typeid, defids>
    tsl::robin_map<UInt32, int> m_SoundDefPathMap; //音效类型 路径索引添加<path string hash, index>
	//素材库类型
	std::vector<TriggerResourceType> m_TriggerResourceTypeDefTable;

	//DirtyWordFilter* m_pDirtyFilter;   //ͨ通用的不启用全词匹配脏词
	//DirtyWordFilter* m_pDirtyFliterWithFullMatch; //全英文下启用全词匹配脏词
	

	std::vector<BPSettingDef>m_BPSettingTable;
	std::vector<BPDrawDef>m_BPDrawTable;
	std::vector<BPMissionDef>m_BPMissionTable;
	std::vector<BPRewardDef>m_BPRewardTable;


	//海外定制化配置
	std::vector<OverseasGrayDef>m_OverseasGrayTable;
	std::vector<WikiConfigDef> m_wikiConfigTable;
	
	//家园相关
	DefDataTable<PetSkillsDef>		    m_PetSkillsTable;
	DefDataTable<PetDef>				m_PetTable;
	DefDataTable<PetExploreDef>			m_PetExploreTable;
	DefDataTable<PetEventDef>			m_PetEventTable;
	DefDataTable<PetSkillPackDef>       m_PetSkillPackTable;
	std::vector<ScoreDef>		m_ScoreTable;
	//midi
	std::map<int, InstrumentDef> m_MidiInstrumentTable;
	ICsvLoadConfig* m_CsvLoadConfig;
	//tooldef配置effect
	DefDataTable<BuffDef> m_CurrentStatusDefs;
	//漂流瓶官方文本
	std::vector<DriftBottleOfficialText*> m_DriftBottleOfficialText;
	//随机信件官方文本
	std::vector<LettersOfficialText*> m_LettersOfficialText;
private:
	void InsertMaterialSet(int activateItemID, int toolType);
	void InsertToolTypeSet(int activateItemID, int toolType);
public:
	//tolua_begin
	void clear();

	bool hasLoaded(const char* filename);
    bool hasLoaded(const Rainbow::NoFreeFixedString& filename);
	bool preloadVoxelPalette();

	bool loadRandomNames();
	bool loadFilterString();
	bool loadFilterStringCN(const char *filepath);
	bool loadFilterStringEN(const char *filepath);
	bool loadMiniCoinDef();
	bool loadVoxelPalette(const char *filepath);

	bool loadBiomeCSV();
	void praseBiome(MultiLanCSVParser& parser);
	bool loadPlanetBiomeCSV();
	bool loadOreCSV();
	bool loadGunDef();
	bool loadCraftingCSV();
	void removeCraftingDef(int id);
	void finalizeCrafting();
	bool isCraftingMaterial(int id);
	bool loadHorseCSV();
	bool loadHorseEggDef();
	bool loadMonsterBiomeCSV();
	bool loadBuffCSV();
	bool loadBuffEffectCSV();
	bool loadBuffEffectEnumCSV();
	bool loadBuffEffectSliderCSV();
	bool loadIconLibCSV();
	bool loadFurnaceCSV();
	bool loadAchievementCSV();
	bool loadEnchantCSV();
	bool loadRuneCSV();//code by:tanzhenyu
	bool loadEnchantMentCSV();
	bool loadScoreCSV();
	bool loadChestDef();
	bool loadChestSpawnDef();
	bool loadSkinningToolDef();
	bool loadCharacterDef();
	bool loadMonsterSpawnDef();
	bool loadMobSpawnerDef();
	bool loadRoleDef();
	bool loadStorePropDef();
	bool loadStoreHorseDef();
	bool loadNpcTradeDef();
	bool loadSignInDef();
	bool loadExtremityScoreDef();
	bool loadHeadIconDef();
	bool loadPlantDef();
	bool loadFruitDef();
	bool loadHorseAblityDef();
	bool loadBookDef();
	bool loadBookSeriesDef();
	bool loadRecycleDef();
	bool loadGameRuleDef();
	bool loadFuncSwitchDef();
	bool loadTaskDef();
	bool loadPlotDef();
	bool loadHotkeyDef();
	bool loadKeyDef();
	bool loadPlantTreesDef();
	bool loadResToLoadDef();
	bool loadResNoToLoadDef();
	bool loadSpecialUinDef();
	bool loadModsToLoadDef();
	bool loadAntiCrackToLoadDef();
	bool loadNpcPlotCSV();
	bool loadNpcTaskCSV();
	bool loadPlayActDef();
	bool loadPhysicsMaterialCSV();
	bool loadPhysicsPartsCSV();
	bool loadPhysicsPartsTypeCSV();
	bool loadPhysicsPartsConnectCSV();
	bool loadRecordEffectCSV();
	bool loadARGradeDef();
	bool loadARStyleDef();
	bool loadTriggerItemDef();
	bool loadTriggerEnumDef();
	bool loadTriggerFunctionDef();
	bool loadTriggerParamDef();
	bool loadTriggerActDef();
	bool loadScriptAPIDef();
	bool loadTriggerResourceTypeDef();
	bool loadSoundDef();
	bool loadWildmanNameDef();
	bool loadRoleActionDef();
	bool loadOverseasGrayCSV();
	bool loadWikiConfigCSV();
	bool loadBPSettingCSV();
	bool loadBPMissionCSV();
	bool loadBPDrawCSV();
	bool loadBPRewardCSV();
	void calBiomeMap();
	bool loadInstrumentDef();
	void clearRandomNames();
	bool loadDriftBottleOfficialText();
	bool loadDungeonsDef();
	bool loadBuildReplaceCSV();
	//tolua_end
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
	bool loadPetDef();
	bool loadPetSkillsDef();
	bool loadFarmInfoDef();	
	bool loadPetExploreDef();
	bool loadPetEventDef();
	bool loadPetSkillPackDef();
	bool loadMonsterStatueDef();
	bool loadLettersOfficialText();
#endif
	virtual int setModMainTask(const std::string& taskFilePath, const std::string& objectFilePath, std::unordered_map<int, std::vector<int>>& m_TaskObjectiveTableCache, std::unordered_map<int, SurviveTaskDef*>& m_TaskDefTableCache, std::unordered_map<int, SurviveObjectiveDef*>& m_ObjectiveDefTableCache) override;
	virtual int ModParseBiomeGroupDef(const std::string& filePath, std::unordered_map<int, BiomeGroupDef*>& cache);
	virtual void ResetBiomeGroupDefFromCache(std::unordered_map<int, BiomeGroupDef*>& cache);
private:
	BiomeMapGrid m_BiomeMap[101][101];
	int m_CurLanguage;
	//unsigned int m_CrcCode[MAX_CRCCODE_TYPE];
	char* letterUpperUtf8;
	char* letterLowerUtf8;
	//新的屏蔽词算法
	//CKeywordFilter m_pFilter;
	//预加载列表
	std::map<Rainbow::FixedString, bool> m_mLoadedFlag;
private:
	std::vector<AbsCsv*> m_vCsvList;

    /**
    @brief 重构过渡使用
    */
	static const char* s_LandPrefix[];

	//重构过程的过渡声明
	friend class ItemDefCsv;
	friend class MonsterCsv;
};//tolua_exports

EXPORT_IWORLD DefManager& GetDefManager();
bool IsInitDefManager();

#define g_DefMgr GetDefManager()

template<typename Func>
bool LoadCsvWithLog(Func loadFunc, const char* name) {
    bool ret = loadFunc();
    if (ret) {
        LOG_INFO("load csv succeed: %s", name);
    } else {
        LOG_WARNING("load csv failed: %s", name);
    }
    return ret;
}

#endif
