#include "tween/EaseManager.h"
#include "GameCamera.h"

struct CameraAnimInfo
{
	//当前持续时长 单位微秒
	unsigned int duration;
	//总时长 单位微秒
	unsigned int time;
	//插值类型
	float offset;
	float oldValue;
	fairygui::EaseType easetype;
	Rainbow::Vector3f target;
	Rainbow::Vector3f lookDir;
	Rainbow::Vector3f oldDir;
};


enum class AdjustStatus {
	over = 0,
	translate = 1,
	focus = 2,
};

class EXPORT_SANDBOXENGINE UGCGameCamera : public GameCamera
{
public:
	UGCGameCamera();
	virtual ~UGCGameCamera();

	virtual Rainbow::WorldPos CalCollidedEyePos(World* pworld, const Rainbow::WorldPos& pos, const Rainbow::Vector3f& dir, float dist);
	virtual void setPosition(const Rainbow::WorldPos& pos);
	virtual void update(float dtime, World* pworld);
	virtual void applyToEngine(World* pworld);
	virtual Rainbow::Quaternionf getRotation(float pitch, float yaw, float roll);
	virtual float getRotatePitch();
	virtual float getRotateYaw();
	virtual void rotate(float yaw, float pitch);

	//还原相机
	void resetGameCamera();

	Rainbow::WorldPos CalCollidedEyePosWithHeadOffset(World* pworld, const Rainbow::WorldPos& pos, const Rainbow::Vector3f& offsetpos, const Rainbow::Vector3f& dir, float dist);
	inline long long getActorObjId()
	{
		return m_ActorObjId;
	}

	inline Rainbow::WorldPos getCameraOffset()
	{
		return m_CameraOffset;
	}

	inline bool getMoveFollowable()
	{
		return m_MoveFollowable;
	}

	inline bool getStandFixedPoint()
	{
		return m_StandFixedPoint;
	}

	inline float getRotateRoll() 
	{
		return m_RotateRoll;
	}

	//获取当前相对坐标系的移动距离
	Rainbow::Vector3f getRelationAxisOffset();

	inline void setActorObjId(long long actorObjId)
	{
		m_ActorObjId = actorObjId;
	}

	inline void setCameraOffset(Rainbow::WorldPos cameraOffset)
	{
		m_CameraOffset = cameraOffset;
	}

	inline void setMoveFollowable(bool followable)
	{
		m_MoveFollowable = followable;
	}

	//如果设置在固定点了之后，再开跟随就不跟随了
	//挂到固定位置的时候其实也就是开起了以自己为中心旋转
	inline void setStandFixedPoint(bool isStandFixedPoint)
	{
		m_StandFixedPoint = isStandFixedPoint;
	}

	inline void setEnableAutoZoom(bool enable)
	{
		m_EnableAutoZoom = enable;
		m_bInitAutoZoom = true;
	}

	void setEnableRoleTranslucent(bool enable);

	void setLockYawRot(bool isLock);
	void setLockPitchRot(bool isLock);
	void setRotBySelfCenter(bool isSelfCenter);
	void setCameraTargetPos(Rainbow::WorldPos& targetPos, unsigned int time, int type = 0);
	void setCameraTargetRot(Rainbow::Vector3f& targetRot, unsigned int time, int easeType = 0);
	void setCameraTargetFov(float fov, unsigned int time, int easeType = 0);
	//执行相机动画
	void doCameraAnimation(float dtime);

	//改变相机位置
	void AdJustCamera(Rainbow::Vector3f pos, int animType, float time);
	void FocusCamera(Rainbow::Vector3f center, float length, Rainbow::Vector3f lookdir, int animType, float time);
protected:
	Rainbow::Vector3f getCameraConfigPosition(float yaw, float pitch);
	void updateCustomCamera(const Rainbow::Quaternionf& quat, World* pworld, IClientActor* pClientActor);
	void updatePlayerCustomCamera(const Rainbow::Quaternionf& quat);

private:
	//摄像机要跟随的东西
	long long m_ActorObjId;

	//锁yaw
	bool m_LockYawRot;
	//锁pitch
	bool m_LockPitchRot;
	//移动是否跟随, true是跟随，false就是不跟随
	bool m_MoveFollowable;
	//固定设置到某个点，如果固定在某个点那么也会以自我为中心旋转
	bool m_StandFixedPoint;
	//播放相机动效
	bool m_PlayCameraAnim;
	//缩进缩出是否可用
	bool m_EnableAutoZoom;
	bool m_bInitAutoZoom;
	//角色透明
	bool m_RoleTranslucent;
	//是否以自我为中心旋转
	bool m_RotSelfCenter;;
	//是否在高级模式相机调整
	AdjustStatus m_AdjustCamera;

	//偏移位置
	Rainbow::WorldPos m_CameraOffset;
	//当前动画位置
	Rainbow::Vector3f m_CameraAniPos;

	//相机自转时保存的绕玩家公转角度
	Rainbow::Vector3f m_CameraRotationAngle;

	float m_RotateRoll;
	float m_LockCameraPitch;
	float m_LockCameraYaw;
	//相关动效信息
	CameraAnimInfo m_MoveAnimInfo[3];
	CameraAnimInfo m_AbsMoveAnimInfo[3];
	CameraAnimInfo m_TargetRotateAnimInfo;
	CameraAnimInfo m_RotAnimInfo[3];
	CameraAnimInfo m_FovAnimInfo;

	int m_LastYawRemainderNum;
};