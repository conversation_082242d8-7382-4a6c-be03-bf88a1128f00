//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON>@inside.org
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Math/Vector3f.h"
#include "NavMesh.h"
#include "QueryFilter.h"
#include "Public/NavMeshProjectSettings.h"
#include "Public/NavMeshTypes.h"

struct NavMeshNode;
class NavMeshNodePool;

struct NavMeshRaycastResult
{
    float t;                 // hit parameter along the segment, 1 if no hit.
    Rainbow::Vector3f normal;         // edge hit normal
    float totalCost;         // weighed length of raycast, using filter weights.
    NavMeshPolyRef lastPoly; // last valid polygon
    NavMeshPolyRef hitPoly;  // last polygon or polygon hit - zero if external edge
};

class NavMeshQuery
{
public:
    NavMeshQuery(const NavMesh* navmesh, const int maxNodes);
    ~NavMeshQuery();

    // Initializes the nav mesh query.
    // Params:
    //  nav - (in) Pointer to navigation mesh data.
    //  maxNodes - (in) Maximum number of search nodes to use (max 65536).
    NavMeshStatus InitPools(const NavMesh* nav, const int maxNodes);

    // Finds the nearest navigation polygon around the center location.
    // Params:
    //  center[3] - (in) The center of the search box.
    //  extents[3] - (in) The extents of the search box.
    //  filter - (in) Path polygon filter.
    //  preferInside - (in) Favors the polygons onto which the center projects along the surface's up axis, if available.
    //  nearestRef - (out) Reference to the nearest polygon.
    //  nearestPt[3] - (out, opt) The nearest point on found polygon, null if not needed.
    void FindNearestPoly(const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, const QueryFilter* filter, bool preferInside,
        NavMeshPolyRef* nearestRef, Rainbow::Vector3f* nearestPt) const;


    // Returns polygons which overlap the query box.
    // Params:
    //  center[3] - (in) the center of the search box.
    //  extents[3] - (in) the extents of the search box.
    //  filter - (in) path polygon filter.
    //  polys - (out) array holding the search result.
    //  polyCount - (out) Number of polygons in search result array.
    //  maxPolys - (in) The max number of polygons the polys array can hold.
    void QueryPolygons(const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents, const QueryFilter* filter,
        NavMeshPolyRef* polys, int* polyCount, const int maxPolys) const;

    // Initializes sliced path find query.
    // Note 1: calling any other NavMeshQuery method before calling FindPathEnd()
    // may results in corrupted data!
    // Note 2: The pointer to filter is store, and used in subsequent
    // calls to UpdateSlicedFindPath().
    // Params:
    //  startRef - (in) ref to path start polygon.
    //  endRef - (in) ref to path end polygon.
    //  startPos[3] - (in) Path start location.
    //  endPos[3] - (in) Path end location.
    //  filter - (in) path polygon filter.
    NavMeshStatus InitSlicedFindPath(NavMeshPolyRef startRef, NavMeshPolyRef endRef,
        const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const QueryFilter* filter);

    NavMeshStatus InitSlicedFindPath(NavMeshPolyRef startRef, NavMeshPolyRef endRef,
        const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const int agentType, const int areaMask, const float costs[NavMeshProjectSettings::kAreaCount]);

    // Updates sliced path find query.
    // Params:
    //  maxIter - (in) Max number of iterations to update.
    //  doneIters - (out,opt) Number of iterations done during the update.
    // Returns: Path query state.
    NavMeshStatus UpdateSlicedFindPath(const int maxIter, int* doneIters);

    // Finalizes sliced path find query and returns found path.
    //  pathCount - (out) Number of polygons in search result.
    NavMeshStatus FinalizeSlicedFindPath(int* pathCount);

    // Finalizes partial sliced path find query and returns path to the furthest
    // polygon on the existing path that was visited during the search.
    //  existing - (out) Array of polygons in the existing path.
    //  existingSize - (out) Number of polygons in existing path array.
    //  pathCount - (out) Number of polygons in search result.
    NavMeshStatus FinalizeSlicedFindPathPartial(int* pathCount, const NavMeshPolyRef* existing, const int existingSize);

    // Returns found path, must call finalizeSlicedFindPath() or finalizeSlicedFindPathPartial() before calling this function.
    //  path - (out) array holding the search result.
    //  pathCount - (out) Number of polygons in search result array.
    //  maxPath - (in) The max number of polygons the path array can hold.
    NavMeshStatus GetPath(NavMeshPolyRef* path, int* pathCount, const int maxPath);

    // Finds a straight path from start to end locations within the corridor
    // described by the path polygons.
    // Start and end locations will be clamped on the corridor.
    // The returned polygon references are point to polygon which was entered when
    // a path point was added. For the end point, zero will be returned. This allows
    // to match for example off-mesh link points to their representative polygons.
    // Params:
    //  startPos[3] - (in) Path start location.
    //  endPo[3] - (in) Path end location.
    //  path - (in) Array of connected polygons describing the corridor.
    //  pathSize - (in) Number of polygons in path array.
    //  straightPath - (out) Points describing the straight path.
    //  straightPathFlags - (out, opt) Flags describing each point type, see NavMeshStraightPathFlags.
    //  straightPathRefs - (out, opt) References to polygons at point locations.
    //  straightPathCount - (out) Number of points in the path.
    //  maxStraightPath - (in) The max number of points the straight path array can hold. Must be at least 1.
    NavMeshStatus FindStraightPath(const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const NavMeshPolyRef* path, const int pathSize,
        Rainbow::Vector3f* straightPath, unsigned char* straightPathFlags, NavMeshPolyRef* straightPathRefs,
        int* straightPathCount, const int maxStraightPath) const;

    // Moves from startPos to endPos constrained to the navmesh.
    // If the endPos is reachable, the resultPos will be endPos,
    // or else the resultPos will be the nearest point in navmesh.
    // Note: The resulting point is not projected to the ground, use GetPolyHeight() to get height.
    // Note: The algorithm is optimized for small delta movement and small number of polygons.
    // Params:
    //  startRef - (in) ref to the polygon where startPos lies.
    //  startPos[3] - (in) start position of the mover.
    //  endPos[3] - (in) desired end position of the mover.
    //  filter - (in) path polygon filter.
    //  resultPos[3] - (out) new position of the mover.
    //  visited - (out) array of visited polygons.
    //  visitedCount - (out) Number of entries in the visited array.
    //  maxVisitedSize - (in) max number of polygons in the visited array.
    NavMeshStatus MoveAlongSurface(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const QueryFilter* filter,
        Rainbow::Vector3f* resultPos, NavMeshPolyRef* visited, int* visitedCount, const int maxVisitedSize) const;
    NavMeshStatus MoveAlongSurface(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const QueryFilter* filter,
        Rainbow::Vector3f* resultPos, NavMeshPolyRef* visited, int* visitedCount, const int maxVisitedSize, NavMeshNodePool* nodePool) const;

    // Casts 'walkability' ray along the navmesh surface from startPos towards the endPos.
    // Params:
    //  startRef - (in) ref to the polygon where the start lies.
    //  startPos[3] - (in) start position of the query.
    //  endPos[3] - (in) end position of the query.
    //  filter - (in) path polygon filter.
    //  result - (out) raycast result data.
    //  path - (out,opt) visited path polygons.
    //  pathCount - (out,opt) Number of polygons visited.
    //  maxPath - (in) max number of polygons in the path array.
    NavMeshStatus Raycast(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
        const QueryFilter* filter,
        NavMeshRaycastResult* result,
        NavMeshPolyRef* path, int* pathCount, const int maxPath) const;

    /// Finds the distance from the specified position to the nearest polygon wall.
    ///  @param[in]         startRef           The reference id of the polygon containing @p centerPos.
    ///  @param[in]         centerPos          The center of the search circle. [(x, y, z)]
    ///  @param[in]         filter             The polygon filter to apply to the query.
    ///  @param[out]        hitDist            The distance to the nearest wall from @p centerPos.
    ///  @param[out]        hitPos             The nearest position on the wall that was hit. [(x, y, z)]
    ///  @param[out]        hitNormal          The normalized ray formed from the wall point to the
    ///                                        source point. [(x, y, z)]
    /// @returns The status flags for the query.
    NavMeshStatus FindDistanceToWall(NavMeshPolyRef startRef, const Rainbow::Vector3f& centerPos,
        const QueryFilter* filter,
        float* hitDist, Rainbow::Vector3f* hitPos, Rainbow::Vector3f* hitNormal, unsigned int* hitFlags) const;

    // Finds non-overlapping local neighbourhood around center location.
    // Note: The algorithm is optimized for small query radius and small number of polygons.
    // Params:
    //  startRef - (in) ref to the polygon where the search starts.
    //  centerPos[3] - (in) center if the query circle.
    //  radius - (in) radius of the query circle.
    //  filter - (in) path polygon filter.
    //  resultRef - (out) refs to the polygons touched by the circle.
    //  resultParent - (out, opt) parent of each result polygon.
    //  resultCount - (out) number of results.
    //  maxResult - (int) maximum capacity of search results.
    NavMeshStatus FindLocalNeighbourhood(NavMeshPolyRef startRef, const Rainbow::Vector3f& centerPos, const float radius,
        const QueryFilter* filter,
        NavMeshPolyRef* resultRef, NavMeshPolyRef* resultParent,
        int* resultCount, const int maxResult) const;

    // Returns wall segments of specified polygon.
    // If 'segmentRefs' is specified, both the wall and portal segments are returned.
    // Wall segments will have null (0) polyref, and portal segments store the polygon they lead to.
    // Params:
    //  ref - (in) ref to the polygon.
    //  filter - (in) path polygon filter.
    //  segmentVerts[2*maxSegments] - (out) wall segments (2 endpoints per segment).
    //  segmentRefs[maxSegments] - (out,opt) reference to a neighbour.
    //  segmentCount - (out) number of wall segments.
    //  maxSegments - (in) max number of segments that can be stored in 'segments'.
    NavMeshStatus GetPolyWallSegments(NavMeshPolyRef ref, const QueryFilter* filter,
        Rainbow::Vector3f* segmentVerts, NavMeshPolyRef* segmentRefs, int* segmentCount,
        const int maxSegments) const;

    // Returns closest point on navigation polygon.
    // Uses detail polygons to find the closest point to the navigation polygon surface.
    // Params:
    //  ref - (in) ref to the polygon.
    //  pos[3] - (in) the point to check.
    //  closest[3] - (out) closest point.
    // Returns: true if closest point found.
    NavMeshStatus ClosestPointOnPoly(NavMeshPolyRef ref, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const;

    // Returns closest point on navigation polygon boundary.
    // Uses the navigation polygon boundary to snap the point to poly boundary
    // if it is outside the polygon. Much faster than ClosestPointToPoly. Does not affect height.
    // Params:
    //  ref - (in) ref to the polygon.
    //  pos[3] - (in) the point to check.
    //  closest[3] - (out) closest point.
    // Returns: true if closest point found.
    NavMeshStatus ClosestPointOnPolyBoundary(NavMeshPolyRef ref, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const;

    // Returns height of the polygon at specified location.
    // Params:
    //  ref - (in) ref to the polygon.
    //  pos[3] - (in) the point where to locate the height.
    //  height - (out) height at the location.
    // Returns: true if over polygon.
    NavMeshStatus GetPolyHeightLocal(NavMeshPolyRef ref, const Rainbow::Vector3f& pos, float* height) const;

    NavMeshStatus ProjectToPoly(Rainbow::Vector3f* projPos, NavMeshPolyRef ref, const Rainbow::Vector3f& pos) const;

    // Returns true if poly reference ins in closed list.
    bool IsInClosedList(NavMeshPolyRef ref) const;

    class NavMeshNodePool* GetNodePool() const { return m_NodePool; }

    const NavMesh* GetAttachedNavMesh() const { return m_NavMesh; }

    NavMeshStatus GetUpAxis(NavMeshPolyRef ref, Rainbow::Vector3f* up) const;

    // Returns portal points between two polygons.
    NavMeshStatus GetPortalPoints(NavMeshPolyRef from, NavMeshPolyRef to, Rainbow::Vector3f* left, Rainbow::Vector3f* right) const;

private:

    void UpdateNeighbourLink(const NavMeshLink* link, const NavMeshPolyRef parentRef, const NavMeshPolyRef bestRef, const NavMeshNode* bestNode);

    // Returns neighbour tile based on side.
    NavMeshTile* getNeighbourTileAt(int x, int y, int side) const;

    // Calculate the closest point on a polygon
    // Returns true unless the 2D projected point is outside the polygon
    bool ClosestPointOnPolyInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly, const NavMeshPolyRef ref, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const;

    // Retraces portals between corners register if type of polygon changes
    // Modifies straightPathCurrentSize with the new straight path size
    // Returns false if the retraced path does not fit in the given straight path buffer
    bool RetracePortals(int startIndex, int endIndex, const NavMeshPolyRef* path, const Rainbow::Vector3f& termPos, bool termPosIsPathFinalPos,
        Rainbow::Vector3f* straightPath , unsigned char* straightPathFlags, NavMeshPolyRef* straightPathRefs, const int maxStraightPath, int& straightPathCurrentSize) const;

    NavMeshPolyRef ValidatePoly(NavMeshPolyRef ref) const;

    struct NavMeshQueryData
    {
        NavMeshStatus status;
        NavMeshNode* lastBestNode;
        NavMeshNode* startNode;
        float lastBestNodeCost;
        NavMeshPolyRef startRef, endRef;
        Rainbow::Vector3f startPos, endPos;
        const QueryFilter* filter;
    };

    const NavMesh* m_NavMesh;       // Pointer to navmesh data.
    NavMeshQueryData m_QueryData;   // Sliced query state.
    QueryFilter m_Filter;

    class NavMeshNodePool* m_TinyNodePool;  // Pointer to small node pool.
    class NavMeshNodePool* m_NodePool;      // Pointer to node pool.
    class NavMeshNodeQueue* m_OpenList;     // Pointer to open list queue.

    static constexpr float k_Eps{ 1e-4f };
};
