#include "FullyCustomModel.h"
#include "FullyCustomModelMgr.h"
#include "File/FileManager.h"
#include "OgreUtils.h"
#include "ClientInfoProxy.h"
#include "gameplay/ResourceCenter.h"
#include "IPlayerControl.h"
#include "worldData/worldDef.h"
#include "DefManagerProxy.h"
#include "custommotion/CustomMotionMgr.h"
#include "custommotion/CustomMotion.h"

#include "IRecordInterface.h"
#include "CustomCommonHead.h"
#include "WorldStringManagerProxy.h"
#include "WorldManager.h"

using namespace Rainbow;
using namespace MNSandbox;
using std::vector;
using std::string;

bool FullyCustomModel::load(std::string path, std::string filename, int checkuin,
	FCMMoveFileLoadData* movedata/* =NULL */, bool input/* =false */,
	LoadMapModData* moddata/* =NULL */, int specialType/* = NORMAL_WORLD*/, bool ignorecheck/* = false*/)
{
	void* buf = nullptr;
	int buflen = 0;
	AutoRefPtr<DataStream> ds = GetFileManager().OpenFile(path.c_str(), true, kFileOpAll);
	if (ds)
	{
		buflen = ds->Size();
		buf = malloc(buflen);
		ds->Read(buf, buflen);
		//ds->Close();
		//ENG_DELETE(ds);
	}
	else
	{
#if BUILD_MINI_EDITOR_APP
		{
			FileAutoClose fac(path, O_RDONLY | O_BINARY);
			if (fac.isNull())
			{
				return false;
			}

			buflen = fac.fileSize();
			if (buflen == 0)
			{
				return false;
			}

			buf = malloc(buflen);
			if (buf == NULL)
			{
				return false;
			}

			fac.seek(0, SEEK_SET);
			if (!fac.read(buf, buflen))
			{
				free(buf);
				return false;
			}

			if (buf == NULL)
			{
				return false;
			}
		}
#else
		{
			return false;
		}
#endif
	}

	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	if (!FBSave::VerifyFullyCustomModelBuffer(verifier))
	{
		free(buf);
		return false;
	}

	const FBSave::FullyCustomModel* fbsFcm = FBSave::GetFullyCustomModel(buf);
	if (fbsFcm == NULL)
	{
		free(buf);
		return false;
	}

	m_iAuthUin = fbsFcm->authuin();
	m_eFcmSaveType = (CLOSE_EDIT_FCM_UI_TYPE)fbsFcm->type();

	if (GetClientInfoProxy()->isEducationLiteGame())
	{
		auto result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("MiniCodeManager_minicodecmOrFcmNeedCheck", MNSandbox::SandboxContext(nullptr).SetData_Bool("isFcm", true));
		if (result.IsExecSuccessed() && !result.GetData_Bool("checkResult"))
		{
			checkuin = FCM_PREVIEW_IGNORE_CHECK_UIN;
		}
		LOG_INFO("dzh2 Load FullyCustomModel, checkuin=%d", checkuin);
	}
	bool bCheck = (checkuin != FCM_LOAD_IGNORE_CHECK_UIN && checkuin != FCM_PREVIEW_IGNORE_CHECK_UIN);
	ItemInfo* resInfo = nullptr;
	if (ResourceCenter::GetInstancePtr())
	{
		resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename);
	}
	if (!resInfo)
	{

		if (bCheck) //客机加载忽略检测，已经在主机检测过了
		{
#ifdef  IWORLD_SERVER_BUILD 
			if (m_iAuthUin != checkuin && m_iAuthUin != GetClientInfoProxy()->getUin())
#else
			if (m_iAuthUin != checkuin &&
				m_iAuthUin != GetClientInfoProxy()->getUin()
				&& (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), m_iAuthUin) == false)
				&& !GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
#endif
			{
				free(buf);
				return false;
			}
		}

		if (m_eFcmSaveType == CLOSE_EDIT_FCM_UI_TYPE::SAVE_EXAMPLE && bCheck)  //示例模型忽略检测
		{
			free(buf);
			return false;
		}

	}

	//不是地图作者下载的资源,也不是自己下载的资源，也不是模板地图导入的资源 不允许加载
	if (resInfo && ((bCheck && checkuin != resInfo->download_uin) && 
							 resInfo->download_uin != GetClientInfoProxy()->getUin() &&
							(GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false)) )
	{
		free(buf);
		return false;
	}

	bool isDownloadFcm = false;
	typedef std::map<std::string, bool> STRING_MAP;
	STRING_MAP* downloadSubCMs = NULL;
	int downloadCMNum = 0;
	if (checkuin == FCM_PREVIEW_IGNORE_CHECK_UIN || 
	   (resInfo && (resInfo->download_uin == checkuin || 
		resInfo->download_uin == GetClientInfoProxy()->getUin() ||
	   (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin)))))
	{
		downloadSubCMs = ENG_NEW(STRING_MAP); // ENG_NEW(std::map<std::string, bool>);
		isDownloadFcm = true;
	}

	int lastPointPos = path.find_last_of(".");
	int translateType = -1;
	if (lastPointPos > 0)
	{
		string suffixName = path.substr(lastPointPos);
		if (suffixName == ".fcm")
		{
			translateType = 4; //完全自定义模型   TRANSLATE_TYPE
		}
		else if (suffixName == ".pfcm")
		{
			translateType = 5; //微缩组合模型
		}
	}

	if (fbsFcm->name())
	{
		m_sName = fbsFcm->name()->c_str();
		std::string transKey(filename);
		transKey.append("_name");
		if (!m_sName.empty() && specialType != HOME_GARDEN_WORLD && translateType > -1)
		{
			//  TRANSLATE_TYPE
			m_sName = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)translateType, transKey, m_sName);
		}
	}
	if(fbsFcm->authorname())
		m_sAuthName = fbsFcm->authorname()->c_str();
	if (fbsFcm->desc())
	{
		m_sDesc = fbsFcm->desc()->c_str();
		std::string transKey(filename);
		transKey.append("_desc");
		if (!m_sDesc.empty() && specialType != HOME_GARDEN_WORLD && translateType > -1)
		{
			//  TRANSLATE_TYPE
			m_sDesc = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)translateType, transKey, m_sDesc);
		}
	}
	if(fbsFcm->key())
		m_sKey = fbsFcm->key()->c_str();

	m_iPackingCMForwardDir = fbsFcm->forwarddir();
	m_iUseDownloadCMNum = fbsFcm->downloadcmnum();

	m_PackingCMForwardDirs.clear();
	if (fbsFcm->cm_forwarddir())
	{
		for (int i = 0; i < (int)fbsFcm->cm_forwarddir()->size(); i++)
		{
			auto src = fbsFcm->cm_forwarddir()->Get(i);
			m_PackingCMForwardDirs[src->skey()->c_str()] = src->dir();
		}
	}
	
	m_sFileName = filename;

	m_vFcbd.clear();
	if (fbsFcm->bonedatas())
	{
		bool bingorecheck = false;
		if (g_WorldMgr && m_iAuthUin == g_WorldMgr->getRealOwnerUin())
		{
			ItemInfo* info = ResourceCenter::GetInstancePtr()->getItemInfoByFileName(MAP_LIB, m_sFileName); // 地图内二创模型忽略校验加载
			if (info && info->download_uin == 0 && info->itemStatus == CANNOT_UPLOAD_STATUS)
			{
				bingorecheck = true;
			}
		}

		for (int i = 0; i < (int)fbsFcm->bonedatas()->size(); i++)
		{
			auto srcBone = fbsFcm->bonedatas()->Get(i);
			FullyCustomBoneData* fcbd = ENG_NEW(FullyCustomBoneData)(this);
			fcbd->fromFbs(srcBone, downloadSubCMs, downloadCMNum, moddata, bingorecheck);
			m_vFcbd.push_back(fcbd);
		}
	}

	m_attachmentBoneNames.clear();
	if (fbsFcm->attachment())
	{
		for (int i = 0; i < (int)fbsFcm->attachment()->size(); i++)
		{
			auto boneName = fbsFcm->attachment()->Get(i);
			m_attachmentBoneNames.push_back(boneName->c_str());
		}
	}

	if (input && downloadCMNum > 0 && resInfo && resInfo->itemStatus == DOWNLOAD_STATUS)
		m_iUseDownloadCMNum = downloadCMNum;

	if (movedata)
	{
		if (movedata->moveFromLibType == MAP_MODEL_CLASS)
		{
			moveMapAvatarModelToRes(movedata->owid, NULL, isDownloadFcm, specialType);
		}
		else if (movedata->moveFromLibType == RES_MODEL_CLASS && !moveResAvatarModelToMap(movedata->owid, movedata->classname, movedata->folderindex, NULL, isDownloadFcm, specialType))
		{
			free(buf);
			ENG_DELETE(downloadSubCMs);
			return false;
		}
	}

	if (downloadSubCMs)
	{
		if (downloadSubCMs->size() > 0)
		{
			int iType = (checkuin == FCM_PREVIEW_IGNORE_CHECK_UIN ? PREVIEW_MODEL_CLASS : -1);
			if (!loadDownloadSubCMs(path, downloadSubCMs, iType))
			{
				free(buf);
				ENG_DELETE(downloadSubCMs);
				return false;
			}
		}
		ENG_DELETE(downloadSubCMs)
	}	
	
	m_frameEventData.clear();
	if(fbsFcm->frameeventdata())
	{
		auto frameeventdata = fbsFcm->frameeventdata();
		for (auto i = 0; i < frameeventdata->size(); i++)
		{
			auto one = frameeventdata->Get(i);
			Rainbow::LegacyAnimationFrameEventData frameEvent;
			frameEvent.id = static_cast<int>(one->id());
			frameEvent.tick = static_cast<UInt32>(one->tick());
			frameEvent.event = one->event()->str();
			frameEvent.triggerCount = static_cast<UInt32>(one->triggercount());
			m_frameEventData.emplace_back(frameEvent);
		}
	}
	free(buf);
	return true;
}

bool FullyCustomModel::save(std::string path, std::string skey, int uin, std::string authname,
	std::string name, std::string desc, int eFcmSaveType)
{
	m_sName = name;
	m_sDesc = desc;
	m_sKey = skey;
	m_iAuthUin = uin;
	m_sAuthName = authname;
	m_eFcmSaveType = (CLOSE_EDIT_FCM_UI_TYPE)eFcmSaveType;

	return save(path);
}

bool FullyCustomModel::save(const std::string& path, bool relativePath)
{
	flatbuffers::FlatBufferBuilder builder;

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::FullyCustomBoneData>>> fbsVectorFcbd = 0;
	std::vector<flatbuffers::Offset<FBSave::FullyCustomBoneData>> vFbsFcbd;

	size_t len = m_vFcbd.size();
	for (size_t i = 0; i < len; i++)
	{
		auto fbsFcbd = m_vFcbd[i]->toFbs(builder);
		vFbsFcbd.push_back(fbsFcbd);

	}
	fbsVectorFcbd = builder.CreateVector(vFbsFcbd);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CustomModelForwardDir>>> cmforwardirsoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::CustomModelForwardDir>> cmforwardirs;
	auto iter = m_PackingCMForwardDirs.begin();
	for (; iter != m_PackingCMForwardDirs.end(); iter++)
	{
		auto fbsKey = builder.CreateString(iter->first);
		cmforwardirs.push_back(FBSave::CreateCustomModelForwardDir(builder, fbsKey, iter->second));
	}
	cmforwardirsoffset = builder.CreateVector(cmforwardirs);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KeyFrameEventData>>> frameeventoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::KeyFrameEventData>> frameevent;
	for(const auto& one : m_frameEventData)
	{
		frameevent.emplace_back(FBSave::CreateKeyFrameEventDataDirect(builder, static_cast<int32_t>(one.id), 
		static_cast<uint32_t>(one.tick), one.event.c_str(), static_cast<uint32_t>(one.triggerCount)));
	}
	frameeventoffset = builder.CreateVector(frameevent);	

	unsigned long t = (unsigned long)time(NULL);
	auto fbsFcm = FBSave::CreateFullyCustomModel(builder, builder.CreateString(m_sName),
		builder.CreateString(m_sAuthName), m_iAuthUin, fbsVectorFcbd, t,
		builder.CreateString(m_sDesc), builder.CreateString(m_sKey), m_eFcmSaveType,
		m_iPackingCMForwardDir, cmforwardirsoffset, m_iUseDownloadCMNum, frameeventoffset, builder.CreateVectorOfStrings(m_attachmentBoneNames)
	);
	builder.Finish(fbsFcm);

	bool ok = false;
	if (relativePath)
	{
		ok = GetFileManager().SaveToWritePath(path.c_str(), builder.GetBufferPointer(), builder.GetSize());
	}
	else
	{
		ok = GetFileManager().SaveFile(path.c_str(), builder.GetBufferPointer(), builder.GetSize());
	}
	return ok;
}

bool FullyCustomModel::cloneToSave(FullyCustomModel* fcmSrc, std::string strPath, bool relativePath)
{

	flatbuffers::FlatBufferBuilder builder;

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::FullyCustomBoneData>>> fbsVectorFcbd = 0;
	std::vector<flatbuffers::Offset<FBSave::FullyCustomBoneData>> vFbsFcbd;

	size_t len = fcmSrc->m_vFcbd.size();
	for (size_t i = 0; i < len; i++)
	{
		auto fbsFcbd = fcmSrc->m_vFcbd[i]->toFbs(builder);
		vFbsFcbd.push_back(fbsFcbd);
	}
	fbsVectorFcbd = builder.CreateVector(vFbsFcbd);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CustomModelForwardDir>>> cmforwardirsoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::CustomModelForwardDir>> cmforwardirs;
	auto iter = fcmSrc->m_PackingCMForwardDirs.begin();
	for (; iter != fcmSrc->m_PackingCMForwardDirs.end(); iter++)
	{
		auto fbsKey = builder.CreateString(iter->first);
		cmforwardirs.push_back(FBSave::CreateCustomModelForwardDir(builder, fbsKey, iter->second));
	}
	cmforwardirsoffset = builder.CreateVector(cmforwardirs);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::KeyFrameEventData>>> frameeventoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::KeyFrameEventData>> frameevent;
	for(const auto& one : fcmSrc->m_frameEventData)
	{
		frameevent.emplace_back(FBSave::CreateKeyFrameEventDataDirect(builder, static_cast<int32_t>(one.id), 
		static_cast<uint32_t>(one.tick), one.event.c_str(), static_cast<uint32_t>(one.triggerCount)));
	}
	frameeventoffset = builder.CreateVector(frameevent);	

	unsigned long t = (unsigned long)time(NULL);
	auto fbsFcm = FBSave::CreateFullyCustomModel(builder, builder.CreateString(m_sName),
		builder.CreateString(m_sAuthName), m_iAuthUin, fbsVectorFcbd, t,
		builder.CreateString(m_sDesc), builder.CreateString(m_sKey), m_eFcmSaveType,
		fcmSrc->m_iPackingCMForwardDir, cmforwardirsoffset, fcmSrc->m_iUseDownloadCMNum, frameeventoffset, builder.CreateVectorOfStrings(fcmSrc->m_attachmentBoneNames));
	builder.Finish(fbsFcm);

	bool ok = false;
	if (relativePath)
	{
		ok = GetFileManager().SaveToWritePath(strPath.c_str(), builder.GetBufferPointer(), builder.GetSize());
	}
	else
	{
		ok = GetFileManager().SaveFile(strPath.c_str(), builder.GetBufferPointer(), builder.GetSize());
	}
	return ok;
}

void FullyCustomModel::reEncrypt(long long owid, int newuin, std::string authnickname, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256] = { 0 };
	if(m_eFcmSaveType == FULLY_PACKING_CUSTOM_MODEL)
		sprintf(path, "%s/w%lld/custommodel/fully/packing/%s.pfcm", rootpath.c_str(), owid, m_sKey.c_str());
	else
		sprintf(path, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), owid, m_sKey.c_str());

	save(path, m_sKey, newuin, authnickname, m_sName, m_sDesc, m_eFcmSaveType);
}

std::string FullyCustomModel::saveCusMotion(int motionid, long long owid, std::string motionname, int specialType/* = NORMAL_WORLD*/)
{
	if (!GetIPlayerControl())
		return "";

	if (!CustomMotionMgr::GetInstancePtr())
		return "";

	std::map<int, CustomMotion *> customMotions;

	//获取全部需要保存的骨骼
	int idx = 0;
	for (size_t i = 0; i < m_vFcbd.size(); i++)
	{
		oneBoneDataConvertToMotion(m_vFcbd[i], customMotions, motionid, idx);
	}

	char sMotionGroupId[64] = { 0 };
	if (customMotions.size() > 0)
	{
		MINIW::ScriptVM::game()->callFunction("GetNewMotionData", "ii>s", 2, (int)MAP_LIB, &sMotionGroupId);  //2 来源自定义模型
	}

	if (sMotionGroupId[0] == 0)
		return "";

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	auto iter = customMotions.begin();
	for (; iter != customMotions.end(); )
	{
		CustomMotion *pCusMotion = iter->second;
		CustomMotionMgr::GetInstancePtr()->addCustomMotion(pCusMotion);
		char path[64] = { 0 };
		if(owid > 0)
			sprintf(path, "%s/w%lld/custommotion/%s.cmt", rootpath.c_str(), owid, pCusMotion->getKey().c_str());
		else
			sprintf(path, "data/custommotion/%s.cmt", pCusMotion->getKey().c_str());

		bool ret = iter->second->save(path);

		int motionId = iter->first;
		
		std::string fileName = pCusMotion->getKey();
		
		iter++;
		int end = (iter == customMotions.end()) ? 1 : 0;

		MINIW::ScriptVM::game()->callFunction("CusModelAddMotionsData", "sisis", sMotionGroupId, motionId, fileName.c_str(), end, motionname.c_str());
		CustomMotionMgr::GetInstancePtr()->addCusMotionToAct(fileName, pCusMotion);
	}

	//文本上报
	if (!motionname.empty())
	{
		GetWorldStringManagerProxy()->insert(sMotionGroupId, motionname, SAVEFILETYPE::CUSTOM_MOTION);
		//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
		//	.SetData_Number("type", 24)
		//	.SetData_String("content", motionname)
		//	.SetData_String("key", sMotionGroupId));
	}

	return sMotionGroupId;
}

std::string FullyCustomModel::getDesc()
{
	return m_sDesc;
}

void FullyCustomModel::setDesc(std::string desc)
{
	m_sDesc = desc;
}

std::string FullyCustomModel::getName()
{
	return m_sName;
}

void FullyCustomModel::setName(std::string name)
{
	m_sName = name;
}

int FullyCustomModel::getModelType()
{
	return m_eFcmSaveType;
}

void FullyCustomModel::setModelType(int eFcmSaveType)
{
	m_eFcmSaveType = eFcmSaveType;
}

bool FullyCustomModel::isSecondaryCreation()
{
	return m_iUseDownloadCMNum > 0;
}

std::string FullyCustomModel::getKey()
{
	return m_sKey;
}

int FullyCustomModel::getAuthUin()
{
	return m_iAuthUin;
}

void FullyCustomModel::setAuthUin(int uin)
{
	m_iAuthUin = uin;
}

bool FullyCustomModel::isDownload()
{
	if (m_iAuthUin > 0)
		return m_iAuthUin != GetClientInfoProxy()->getUin();

	return false;
}

bool FullyCustomModel::isLeaveWorldDel()
{
	return m_bLeaveworlddel;
}

std::string FullyCustomModel::getAuthName()
{
	return m_sAuthName;
}

void FullyCustomModel::setAuthName(std::string authname)
{
	m_sAuthName = authname;
}

std::string FullyCustomModel::getModelName()
{
	return m_sName;
}

std::string FullyCustomModel::getModelDesc()
{
	return m_sDesc;
}

void FullyCustomModel::setKey(std::string skey)
{
	m_sKey = skey;
}

void FullyCustomModel::setEditing(bool editing)
{
	m_bEditing = editing;

	//原有代码。新引擎接入后，偶现崩溃或模型消失问题
	function<bool(FullyCustomBoneData&)> func = [this](FullyCustomBoneData& fcbd) -> bool {
		if (fcbd.model2)
		{
			fcbd.model2 = nullptr;
		}
		return true;
	};
	iterate(func);

	if (m_CurSelectBoneEffect.effect)
		m_CurSelectBoneEffect.effect = nullptr;

	m_CurSelectBoneEffect.bonename = "";

	if (editing)
	{
		m_iUseDownloadCMNum = 0;
		checkEditUseCMIsDownloaded();
	}
}

bool FullyCustomModel::isEdited()
{
	//return m_BoneDatas.size() > 0;
	return !m_sKey.empty();
}

void FullyCustomModel::addVersion(int v)
{
	m_iVersion += v;
}

void FullyCustomModel::setVersion(int v)
{
	m_iVersion = v;
}

int FullyCustomModel::getVersion()
{
	return m_iVersion;
}

void FullyCustomModel::setNeedSave(bool b)
{
	m_bNeedSave = b;
}

bool FullyCustomModel::needSave()
{
	return m_bNeedSave;
}

std::string& FullyCustomModel::getFileName()
{
	return m_sFileName;
}

void FullyCustomModel::setFileName(std::string filename)
{
	m_sFileName = filename;
}

void FullyCustomModel::setLeaveWorldDel(bool b)
{
	m_bLeaveworlddel = b;
}


void FullyCustomModel::setSplitSkeletonModel(bool isSplit)
{
	m_splitSkeletonModel = isSplit;
}

bool FullyCustomModel::isSplitSkeletonModel()
{
	return m_splitSkeletonModel;
}
