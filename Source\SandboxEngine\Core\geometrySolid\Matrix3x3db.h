#pragma once
#include "geometrySolid/Vector3db.h"
#include "Math/Matrix3x3f.h"
#include "Math/Matrix4x4f.h"

namespace MNSandbox {
	namespace GeometrySolid {
		//保留这些注释，需要时再启用
		class Vector3db;
		class Matrix3x3db
		{
		public:
			union
			{
				double m_Data[9];
				double m_Data33[3][3];
			};

			//DECLARE_SERIALIZE_NO_PPTR(Matrix3x3db)

			FORCEINLINE Matrix3x3db() {}  // Default ctor is intentionally empty for performance reasons
			FORCEINLINE Matrix3x3db(double m00, double m01, double m02, double m10, double m11, double m12, double m20, double m21, double m22)
			{
				Get(0, 0) = m00; Get(1, 0) = m10; Get(2, 0) = m20;
				Get(0, 1) = m01; Get(1, 1) = m11; Get(2, 1) = m21;
				Get(0, 2) = m02; Get(1, 2) = m12; Get(2, 2) = m22;
			}
			explicit Matrix3x3db(const Rainbow::Matrix4x4f& other);
			//explicit Matrix3x3db(const class Matrix4x4db& m);
			// The Get function accesses the matrix in std math convention
			// m0,0 m0,1 m0,2
			// m1,0 m1,1 m1,2
			// m2,0 m2,1 m2,2

			// The floats are laid out:
			// m0   m3   m6
			// m1   m4   m7
			// m2   m5   m8

			inline Rainbow::Matrix3x3f ToFloat() const
			{
				Rainbow::Matrix3x3f m;
				m.m_Data[0] = m_Data[0];
				m.m_Data[1] = m_Data[1];
				m.m_Data[2] = m_Data[2];
				m.m_Data[3] = m_Data[3];
				m.m_Data[4] = m_Data[4];
				m.m_Data[5] = m_Data[5];
				m.m_Data[6] = m_Data[6];
				m.m_Data[7] = m_Data[7];
				m.m_Data[8] = m_Data[8];
				return m;
			}

			//行优先
			//FORCEINLINE double& Get(int row, int column) { return m_Data[row * 3 + column]; }
			//FORCEINLINE const double& Get(int row, int column) const { return m_Data[row * 3 + column]; }
			//列优先
			FORCEINLINE double& Get(int row, int column) { return m_Data[row + column * 3]; }
			FORCEINLINE const double& Get(int row, int column) const { return m_Data[row + column * 3]; }

			FORCEINLINE double& operator[](int row) { return m_Data[row]; }
			FORCEINLINE double operator[](int row) const { return m_Data[row]; }

			FORCEINLINE double* GetPtr() { return m_Data; }
			FORCEINLINE const double* GetPtr() const { return m_Data; }

			FORCEINLINE Vector3db GetColumn(int col) const { return Vector3db(Get(0, col), Get(1, col), Get(2, col)); }

			Matrix3x3db& operator*=(const Matrix3x3db& inM);
			Matrix3x3db& operator=(const Rainbow::Matrix4x4f& m);
			//Matrix3x3db& operator*=(const class Matrix4x4db& inM);
			friend Matrix3x3db operator*(const Matrix3x3db& lhs, const Matrix3x3db& rhs);
			Vector3db MultiplyVector3(const Vector3db& inV) const;
			void MultiplyVector3(const Vector3db& inV, Vector3db& output) const;

			Vector3db MultiplyPoint3(const Vector3db& inV) const { return MultiplyVector3(inV); }
			Vector3db MultiplyVector3Transpose(const Vector3db& inV) const;
			Vector3db MultiplyPoint3Transpose(const Vector3db& inV) const { return MultiplyVector3Transpose(inV); }

			Matrix3x3db& operator*=(double f);
			Matrix3x3db& operator/=(double f) { return *this *= (1.0 / f); }

			double GetDeterminant() const;

			Matrix3x3db& Transpose(const Matrix3x3db& inM);
			Matrix3x3db& Transpose();
			Matrix3x3db& Invert (const Matrix3x3db& inM) { return Transpose (inM); }
			bool Invert();
			//void InvertTranspose();

			Matrix3x3db& SetIdentity();
			Matrix3x3db& SetZero();
			Matrix3x3db& SetFromToRotation(const Vector3db& from, const Vector3db& to);
			//Matrix3x3db& SetAxisAngle(const Vector3db& rotationAxis, double radians);
			Matrix3x3db& SetBasis(const Vector3db& inX, const Vector3db& inY, const Vector3db& inZ);
			Matrix3x3db& SetBasisTransposed(const Vector3db& inX, const Vector3db& inY, const Vector3db& inZ);
			Matrix3x3db& SetScale(const Vector3db& inScale);
			Matrix3x3db& Scale(const Vector3db& inScale);

			//bool IsIdentity(double threshold = 0.0001);
			//FORCEINLINE core::string ToString() const
			//{
			//	return FormatString("%f,%f,%f,%f,%f,%f,%f,%f,%f",
			//		m_Data33[0][0], m_Data33[0][1], m_Data33[0][2],
			//		m_Data33[1][0], m_Data33[1][1], m_Data33[1][2],
			//		m_Data33[2][0], m_Data33[2][1], m_Data33[2][2]
			//	);
			//}

			static const Matrix3x3db zero;
			static const Matrix3x3db identity;
		};

		// Generates an orthornormal basis from a look at rotation, returns if it was successful
		// (Righthanded)
		bool LookRotationToMatrix(const Vector3db& viewVec, const Vector3db& upVec, Matrix3x3db* m);

		////参数是弧度
		//void EulerToMatrix(const Vector3db& v, Matrix3x3db& matrix);
		////参数是角度
		//FORCEINLINE void AngleEulerToMatrix(const Vector3db& AngleEuler, Matrix3x3db& matrix)
		//{
		//	Vector3db raduis(Deg2Rad(AngleEuler.x), Deg2Rad(AngleEuler.y), Deg2Rad(AngleEuler.z));
		//	EulerToMatrix(raduis, matrix);
		//};

		FORCEINLINE Vector3db Matrix3x3db::MultiplyVector3(const Vector3db& v) const
		{
			Vector3db res;
			res.x = m_Data[0] * v.x + m_Data[3] * v.y + m_Data[6] * v.z;
			res.y = m_Data[1] * v.x + m_Data[4] * v.y + m_Data[7] * v.z;
			res.z = m_Data[2] * v.x + m_Data[5] * v.y + m_Data[8] * v.z;
			return res;
		}

		FORCEINLINE void Matrix3x3db::MultiplyVector3(const Vector3db& v, Vector3db& output) const
		{
			output.x = m_Data[0] * v.x + m_Data[3] * v.y + m_Data[6] * v.z;
			output.y = m_Data[1] * v.x + m_Data[4] * v.y + m_Data[7] * v.z;
			output.z = m_Data[2] * v.x + m_Data[5] * v.y + m_Data[8] * v.z;
		}

		FORCEINLINE void MultiplyMatrices3x3(const Matrix3x3db* __restrict lhs, const Matrix3x3db* __restrict rhs, Matrix3x3db* __restrict res)
		{
			Assert(lhs != rhs && lhs != res && rhs != res);
			for (int i = 0; i < 3; ++i)
			{
				res->m_Data[i] = lhs->m_Data[i] * rhs->m_Data[0] + lhs->m_Data[i + 3] * rhs->m_Data[1] + lhs->m_Data[i + 6] * rhs->m_Data[2];
				res->m_Data[i + 3] = lhs->m_Data[i] * rhs->m_Data[3] + lhs->m_Data[i + 3] * rhs->m_Data[4] + lhs->m_Data[i + 6] * rhs->m_Data[5];
				res->m_Data[i + 6] = lhs->m_Data[i] * rhs->m_Data[6] + lhs->m_Data[i + 3] * rhs->m_Data[7] + lhs->m_Data[i + 6] * rhs->m_Data[8];
			}
		}

		//Qt Only
		//FORCEINLINE void QtMultiplyRowMatrices3x3(const Matrix3x3db* __restrict m1, const Matrix3x3db* __restrict m2, Matrix3x3db* __restrict res)
		//{
		//	res->Get(0, 0) = m1->Get(0, 0) * m2->Get(0, 0) + m1->Get(0, 1) * m2->Get(1, 0) + m1->Get(0, 2) * m2->Get(2, 0);
		//	res->Get(0, 1) = m1->Get(0, 0) * m2->Get(0, 1) + m1->Get(0, 1) * m2->Get(1, 1) + m1->Get(0, 2) * m2->Get(2, 1);
		//	res->Get(0, 2) = m1->Get(0, 0) * m2->Get(0, 2) + m1->Get(0, 1) * m2->Get(1, 2) + m1->Get(0, 2) * m2->Get(2, 2);

		//	res->Get(1, 0) = m1->Get(1, 0) * m2->Get(0, 0) + m1->Get(1, 1) * m2->Get(1, 0) + m1->Get(1, 2) * m2->Get(2, 0);
		//	res->Get(1, 1) = m1->Get(1, 0) * m2->Get(0, 1) + m1->Get(1, 1) * m2->Get(1, 1) + m1->Get(1, 2) * m2->Get(2, 1);
		//	res->Get(1, 2) = m1->Get(1, 0) * m2->Get(0, 2) + m1->Get(1, 1) * m2->Get(1, 2) + m1->Get(1, 2) * m2->Get(2, 2);

		//	res->Get(2, 0) = m1->Get(2, 0) * m2->Get(0, 0) + m1->Get(2, 1) * m2->Get(1, 0) + m1->Get(2, 2) * m2->Get(2, 0);
		//	res->Get(2, 1) = m1->Get(2, 0) * m2->Get(0, 1) + m1->Get(2, 1) * m2->Get(1, 1) + m1->Get(2, 2) * m2->Get(2, 1);
		//	res->Get(2, 2) = m1->Get(2, 0) * m2->Get(0, 2) + m1->Get(2, 1) * m2->Get(1, 2) + m1->Get(2, 2) * m2->Get(2, 2);
		//}

		FORCEINLINE Matrix3x3db operator*(const Matrix3x3db& lhs, const Matrix3x3db& rhs)
		{
			Matrix3x3db temp;
			MultiplyMatrices3x3(&lhs, &rhs, &temp);
			return temp;
		}

		FORCEINLINE Vector3db Matrix3x3db::MultiplyVector3Transpose(const Vector3db& v) const
		{
			Vector3db res;
			res.x = Get(0, 0) * v.x + Get(1, 0) * v.y + Get(2, 0) * v.z;
			res.y = Get(0, 1) * v.x + Get(1, 1) * v.y + Get(2, 1) * v.z;
			res.z = Get(0, 2) * v.x + Get(1, 2) * v.y + Get(2, 2) * v.z;
			return res;
		}

		//template<class TransferFunction>
		//inline void Matrix3x3db::Transfer(TransferFunction& t)
		//{
		//	t.Transfer(Get(0, 0), "e00"); t.Transfer(Get(0, 1), "e01"); t.Transfer(Get(0, 2), "e02");
		//	t.Transfer(Get(1, 0), "e10"); t.Transfer(Get(1, 1), "e11"); t.Transfer(Get(1, 2), "e12");
		//	t.Transfer(Get(2, 0), "e20"); t.Transfer(Get(2, 1), "e21"); t.Transfer(Get(2, 2), "e22");
		//}

		//void OrthoNormalize(Matrix3x3db& matrix);
	}
}