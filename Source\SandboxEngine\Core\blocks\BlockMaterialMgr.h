
#ifndef __BLOCKMATERIALMGR_H__
#define __BLOCKMATERIALMGR_H__

#include "OgrePrerequisites.h"
#include "Misc/XMLData.h"
#include "Common/OgreHashTable.h"
#include "BlockMeshVert.h"
#include "Entity/OgreModelData.h"
#include "AssetPipeline/AssetManager.h"
#include "ShareRenderMaterial.h"
#include "SandboxEngine.h"
#include "Utilities/ConcurrentFreeList.h"
#include "SandboxNotifyOnly.h"
#include "CorePrerequisites.h"

class BlockIconLoadAsyncTask;
class BlockGeomTemplate;
class BlockMaterialMgr;
class MemStat;
class ShareMaterial;
struct IconDesc;
struct CustomSectionMeshPrimitiveData;

namespace Rainbow
{
	class TextureRenderGen;
	class Model;
}
namespace MNSandbox
{
	class BlockService;
}

//struct TriangleBlockPhyData
//{
//	TriangleBlockPhyData() : triangleFaceCount(0) {}
//	dynamic_array<Rainbow::Vector3f> blockVertList;
//	dynamic_array<UInt16> blockVertIdxList;
//	unsigned short triangleFaceCount;
//	WCoord pos;
//};

class EXPORT_SANDBOXENGINE BlockMaterialMgr;
class BlockMaterialMgr//tolua_exports
{//tolua_exports
	friend class MNSandbox::BlockService;
public:
	Rainbow::SharePtr<Rainbow::ModelData> loadModelData(const char* path, const char* animfile = NULL, int loadParam = Rainbow::RLF_CONVERT_BIT16, bool isAsync = false);
#ifdef IWORLD_SERVER_BUILD
	BlockMaterialMgr(bool onlylogic = true);
#else
	BlockMaterialMgr(bool onlylogic = false);
#endif
	
public:
	static BlockMaterialMgr* getSingletonPtr();
	static BlockMaterialMgr& getSingleton();

	~BlockMaterialMgr();

	//tolua_begin
	void clearOnEndGame();

	void update(unsigned int dtick);
	void tickCS();
	int  getInitStepCount();
	void Load();
	bool initStep(int step);
	int getInitStep();
	void loadGeom();
	void initAllBlockMaterial();

	bool IsUseVertexAnimationEffect(int resId) const ;
	bool IsUseVertexSnowCoverEffect(int resId) const ;
	void SetVertexAnimationEffectEnable(bool value);
	Rainbow::Model *getModel(const char *path, const char *animfile=NULL, int loadParam=Rainbow::RLF_CONVERT_BIT16, bool isAsync = false);
	void SetVertexSnowCoverEffectEnable(bool value);
	Rainbow::Entity *getEntity(const char *path);
	BlockGeomTemplate *getGeomTemplate(const Rainbow::FixedString &name);
	void genGeomTemplate(std::vector<WCoord> &vpos, std::vector<unsigned int> &color, const Rainbow::FixedString &name);
	BlockGeomTemplate* genGeomTemplateInJobThread(std::vector<WCoord>& vpos, std::vector<unsigned int>& color, const Rainbow::FixedString& name);
	void removeGenGeomTemplate(const Rainbow::FixedString &name);
	BlockMaterial *getMaterial(int resid);
	bool hasMaterial(int resid);
	bool checkExist(BlockMaterial* mtl);
	SectionMesh * getSectionMeshFromJson(jsonxx::Object blockJson);

	Rainbow::SharePtr<Rainbow::Texture2D>   genOneBlockIconTex(int blockid);
	Rainbow::SharePtr<Rainbow::Texture2D>   genOneBlockIconTexEx(int blockid, const Rainbow::Vector3f& eye, const Rainbow::Vector3f& at,bool isReadable);
	Rainbow::SharePtr<Rainbow::Texture2D>   genOneBlockIconTex(std::string geomname);
	Rainbow::SharePtr<Rainbow::Texture2D>   genOneItemIconTex(int itemid);
	Rainbow::SharePtr<Rainbow::Texture2D>   genOneItemIconTex(std::string geomname, bool textureReadable = false);
	Rainbow::SharePtr<Rainbow::Texture2D>   genOneItemIconTex(std::string vehicleuserdata, int itemid);

	Rainbow::SharePtr<Rainbow::Texture2D>   getWhiteColorTex()
	{
		return m_WhiteColor;
	}

	Rainbow::SharePtr<Rainbow::Texture2D>   getEnchantLightTex()
	{
		return m_EnchantLightTex;
	}

	Rainbow::SharePtr<Rainbow::Texture2D>   getExplodeTex()
	{
		return m_ExplodeTex;
	}

	BlockMaterial *newBlockMaterial(const char *name, int resid);
	void addRenderMaterial(const Rainbow::FixedString &texname, RenderBlockMaterial *mtl);
	RenderBlockMaterial* getRenderMaterial(const Rainbow::FixedString &texname);
	RenderBlockMaterial *createRenderMaterial(const char *texname, const BlockDef* blockDef, int gettextype=GETTEX_NORMAL, BlockDrawType drawtype=BLOCKDRAW_OPAQUE, int mipmethod=0, const char *mtlname=NULL, bool shareTexture = true);
	void createRenderMaterialStages(RenderBlockMaterial **stages, int nstage, const BlockDef* blockDef, const char *texname, BlockDrawType drawtype, int mipmethod=0);
	RenderBlockMaterial *createRandomRenderMaterial(const char *mtlname, const char *texname, const BlockDef* blockDef, int randomIndex, unsigned int color, int hsbIndex);
	void gcRenderMaterials();
	ShareMaterial *getShareMaterial() const;
	void resetRenderBlockMaterialPackableMaterialId();
	Rainbow::SharePtr<Rainbow::Texture2D> createRandomRenderTexture(Rainbow::SharePtr<Rainbow::Texture2D>   basetex, int randomIndex, unsigned int color, int hsbIndex);

	bool loadGeomFromJson(jsonxx::Object& geomObj, const char* path);

	void saveBlockSettingAtt(std::vector<int>& blocksettingatt, std::vector<unsigned char>& blocksettingattexid);

	void resetBlockSettingAtt();
	void refreshCustomBlock();
	bool loadGeomFile(const char* path);
	//BlockTemporaryMgr* getBlockTemporaryMgrPtr() { return &m_blockTemporaryMgr; }
	//tolua_end
	
	void refreshCustomBlockOne(int id);
	void refreshModBaseBlockOne(int id);

	//注册新方块 没用的代码
	//template<typename T>
	//void RegisterNewBlockMaterial(const char* name, const char* script = nullptr)
	//{
	//	m_blockImpl.AddNewBlockMaterial<T>(name, script);
	//}
	void destroyBlockMaterials(int blockid);

	bool genOneBlockIconTexAsync(void* gloader, int blockid, Rainbow::SharePtr<Rainbow::Texture2D>* tex);
	void onAsynTaskComplete(BlockIconLoadAsyncTask* task);

	//新方块创建 使用templateBlockId来作为模板来创建新方块 ps:templateBlockId :对应当前缓存中存在的BlockMaterial的BlockResID 
	MNSandbox::AutoRef<BlockMaterial> GenerateNewBlockMaterial(int templateBlockId = -1);
	//新方块创建 缓存中以modifyId来存储创建的新方块 ps：新方块使用jsonObj 反序列化出一个BlockMaterial 将其BlockResID设置为modifyId 
	// modifyId = -2 生成customBlock
	MNSandbox::AutoRef<BlockMaterial> GenerateNewBlockMaterial(const MNJsonObject& jsonObj, int modifyId = -1);
	void kickJobsAtlasTexture();
	
	bool AddBlockMaterial(MNSandbox::AutoRef<BlockMaterial> mtl);
	void InitCustomModelPlaceMob();
	bool GetCustomModelPlaceMob(int modelType);
	int genNewBlockId();
	void setPlantSnowCoverThickness(Rainbow::Vector4f value = Rainbow::Vector4f::zero);
	bool checkEnterVoidNight(World* pworld, const WCoord& blockpos);
	BlockDef* GetBlockNewDef(int blockid);
	void SetBlockNewDef(int blockid, BlockDef* ptrNewDef);

	void SetDynamicRendererData(World* pworld, const WCoord& blockWorldPos, bool enable);
	void RemoveDynamicRenderData(World* pworld, const WCoord& blockWorldPos);

	void SetBlockGeomTemplate(const Rainbow::FixedString& name, BlockGeomTemplate* geom);

	void OnWorldChange(World* world);

	void OnLODRangeChange();

	void BlendGrassVertexColor(const Rainbow::Vector4f& blockPos, BlockColor& vertexColor);

private:
	BlockMaterial* getOrCreateMaterial(int resid);
	BlockMaterial* getMaterialInternal(int resid) const;
	void destroyAllBlockMaterials();
	void releaseAllRenderMaterials();
	void setMaterialInternal(int resid, BlockMaterial* material);
	BlockMaterial* CreateNewBlockMaterial(int newblockid, BlockMaterial* templatePtr);
	//测试使用
	BlockMaterial* CreateNewBlockMaterial(int newblockid, int  templateBlockId);
	bool loadTextureAtlasFile();
	bool loadItemIcons();
	bool loadMaterialFile();
	bool loadGeomFile();
	bool genBlockIcon();

	BlockMaterial* initOneBlockMaterial(const BlockDef *def, int resid);
	void InitBlockMaterialByNode(BlockMaterial* mtl);

	void genRandomHSB();
	//void genRandomRGBs();
	//void genRandomRGBs(const std::vector<Rainbow::TextureData*>& blocktexs);
	//void genMapRandomRGB(std::map<unsigned int, unsigned int>& mapRandomRGB, const std::vector<Rainbow::TextureData*>& blocktexs);
	//void getRandomRGB(Rainbow::ColorQuad& colorResult, const Rainbow::ColorQuad& color);
	Rainbow::SharePtr<Rainbow::Texture2D>   copyTexture(Rainbow::SharePtr<Rainbow::Texture2D>   blocktex);
	Rainbow::SharePtr<Rainbow::Texture2D>   randomTexture(Rainbow::SharePtr<Rainbow::Texture2D>   blocktex, int randomIndex, int maxOffsetH = 0, int minOffsetH = 0, int maxOffsetS = 0, int minOffsetS = 0, int maxOffsetB = 0, int minOffsetB = 0);
	Rainbow::SharePtr<Rainbow::Texture2D>   colorTexture(Rainbow::SharePtr<Rainbow::Texture2D>   blocktex, const Rainbow::ColorQuad& color);
	//Rainbow::SharePtr<Rainbow::Texture2D>   mixedTexture(Rainbow::SharePtr<Rainbow::Texture2D>   blocktex, Rainbow::SharePtr<Rainbow::Texture2D>   blockmixedTex);
	void AddToMaterialQueue(BlockMaterial* mtl);
	void ProcessMaterialQueue();

	void OverrideShaderPropertySheet(Rainbow::ShaderPropertySheet* propertySheet, const CustomSectionMeshPrimitiveData& data);
	
	void InitGrassNoise();
private:
	/* 监听回调 */
	void OnBlockDigBegin(World*, int, const WCoord&, MNSandbox::SandboxNode_Ref);
	void OnBlockDigFinish(World*, int, const WCoord&, MNSandbox::SandboxNode_Ref);
	void OnBlockDigCancel(World*, int, const WCoord&, MNSandbox::SandboxNode_Ref);

	/* 监听 */
	MNSandbox::ListenerClass<BlockMaterialMgr, World*, int, const WCoord&, MNSandbox::SandboxNode_Ref> m_listenBlockDigBegin;
	MNSandbox::ListenerClass<BlockMaterialMgr, World*, int, const WCoord&, MNSandbox::SandboxNode_Ref> m_listenBlockDigFinish;
	MNSandbox::ListenerClass<BlockMaterialMgr, World*, int, const WCoord&, MNSandbox::SandboxNode_Ref> m_listenBlockDigCancel;

public:
	/* 通知 */
	static MNSandbox::NotifyOnly<unsigned&> m_notifyAllocNewId; // 通知分配新ID
	/*缓存方块更新通知*/
	static MNSandbox::NotifyOnly<int> m_notifyBlockChange; 

	static void OnOverrideShaderPropertySheet(Rainbow::ShaderPropertySheet* propertySheet, const CustomSectionMeshPrimitiveData& data);

public:
	//tolua_begin
	//色块随机数模板数量
	static const int RANDOMRGBS_SIZE = 15;
	//将4张不同的贴图贴在4个side面上，将16种随机情况记录在4个BlockState位上
	//tolua_end
	int m_RandFace4[16][4];

	static bool m_LeafUseCube;
	static int	m_BlockShape;
	int m_initStep;

	// 方块库生成
	//BlockImplements m_blockImpl;

#ifdef UGC_PROFILE_ENABLED
	float GetGeoTemplateMemStat() const
	{
		return m_GeoTemplateMemSize;
	}
#endif // UGC_PROFILE_ENABLED

	//
	//BlockTemporaryMgr m_blockTemporaryMgr;

	static bool isLava(int blockid, bool isForceCheckStill = false);
	static bool isWater(int blockid, bool isForceCheckStill = false);
	static bool isHoney(int blockid, bool isForceCheckStill = false);
	static bool isVenom(int blockid, bool isForceCheckStill = false);
	static bool isDriftsand(int blockid, bool isForceCheckStill = false);
private:
	std::map<Rainbow::FixedString, RenderBlockMaterial *>m_RenderMtls;
	std::map<Rainbow::FixedString, BlockGeomTemplate *>m_BlockGeoms;
	fixed_array<atomic_word, SOC_BLOCKID_MAX> m_BlockMtls;
	dynamic_array<int> m_BlockMtlsIndex;
	Rainbow::Mutex m_MaterialLock;

	Rainbow::SharePtr<Rainbow::Texture2D>   m_WhiteColor;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_EnchantLightTex;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_ExplodeTex;

	ShareMaterial* m_ShareMaterial;

	Rainbow::TextureRenderGen *m_TextureGen;


#ifdef UGC_PROFILE_ENABLED
	float m_GeoTemplateMemSize;
#endif // UGC_PROFILE_ENABLED

	//HSB随机取值范围的常量数据条数
	static const int MAX_RANGE_HSB = 5;
	static const int m_arrayRangeHSB[10][6];
	std::map<unsigned int, int> m_mapRandomHSB[2]; //HSB随机取值范围
	//int m_randomIndex;
	//std::map<unsigned int, unsigned int> m_randomRGBs[RANDOMRGBS_SIZE]; //色块对应的随机数模板

	Rainbow::ConcurrentQueue* m_MaterialQueue;
	Rainbow::ConcurrentFreeList<Rainbow::ConcurrentNode> m_NodePool;
	std::map<int, bool> m_CustomModelPlaceMob;
	dynamic_array<int> m_VertexAnimationBlockIds;
	dynamic_array<int> m_VertexSnowCoverBlockIds;
	std::unordered_map<int, BlockDef*> m_newDefCache;
	dynamic_array<UInt8> m_GrassNoise;
	int m_GrassNoiseSize;
};//tolua_exports


EXPORT_SANDBOXENGINE BlockMaterialMgr& GetBlockMaterialMgr();
EXPORT_SANDBOXENGINE BlockMaterialMgr* GetBlockMaterialMgrPtr();
#define g_BlockMtlMgr GetBlockMaterialMgr()

#endif