
#ifndef __GAMECAMERA_H__
#define __GAMECAMERA_H__

//#include "Components/Camera.h"
//#include "Misc/Stabilizer.h"
//#include "OgreCameraAnim.h"
#include "OgreWorldPos.h"
#include "world_types.h"
#include "SandboxObject.h"
//#include "SandboxParam.h"
#include "OgreRay.h"
#include "BaseClass/PPtr.h"
#include "SandboxEngine.h"

class CameraModel;
class IPlayerControl;

#define MINI_VARIABLE(varType, Name)\
protected: \
	varType m_##Name; \
public: \
	varType get##Name() const { return m_##Name; } \
	void set##Name(varType var) { m_##Name = var; }
namespace Rainbow{
	class Camera;
	class AABB;
	class GameScene;
}

namespace MINIW {
	class CameraAnimation;
}

//tolua_begin
enum CameraControlMode
{
	CAMERA_FPS			= 0,
	CAMERA_TPS_BACK		= 1,
	CAMERA_TPS_FRONT	= 2,
	CAMERA_TPS_BACK_2	= 3,
	CAMERA_TPS_OVERLOOK = 4,
	CAMERA_CUSTOM_VIEW	= 5,	// 自定义视角
	CAMERA_THIRD_SHOULDER = 6, //背视角第三人称过肩
	CAMERA_TPS_SIDE = 7, //侧视角
	CAMERA_TPS_BACK_SHOULDER = 8,
	CAMERA_MAX_MODE
};
//tolua_end

//使用分镜头（不在角色身上的--主机客机）可能出现的问题
//1、天空盒中心位置（好解决）
//2、地图加载（会影响游戏性能，且修改工程影响比较大）
//3、update调用（当镜头挂点不在角色视线范围时，不会调用update），使用tick

//使用
//创建镜头 
//CameraManager::GetInstance().addScriptCamera(name); 
//初始化镜头位置和转向CameraManager::GetInstance().setSpCameraPosRot(name, pos, quat); 
//或单独设置位置和转向 setSpCameraPos、setSpCameraRot

//动态镜头
//无操作镜头变换（如镜头挂在生物上）
//在需要变换的地方调用 CameraManager::GetInstance().setSpCameraPosRot(name, pos, quat); 或 setSpCameraPos、setSpCameraRot
// 有操作镜头（输入操作改变镜头的变换）
// c++继承 scriptCamera 注册输入消息接收回调改变位置转向变量  重载applyToEngine应用生效改变
// lua 创建时 CameraManager::GetInstance().addScriptCamera(name, scriptName);把lua文件名传入 lua实现applyToEngine消息绑定 返回CameraData 数据给c++设置镜头

//镜头转换 使用CameraManager::GetInstance().changeScriptCamera(name); 恢复使用主镜头（角色身上的镜头）CameraManager::GetInstance().changeMainGameCamera();


class CameraData //tolua_exports
{ //tolua_exports
public:
	CameraData()
	:m_QuatX(0)
	,m_QuatY(0)
	,m_QuatZ(0)
	,m_QuatW(0)
	,m_PosX(0)
	,m_PosY(0)
	,m_PosZ(0)
	{
	};
	CameraData(int qx, int qy, int qz, int qw, int px, int py, int pz)
		: m_QuatX(qx)
		, m_QuatY(qy)
		, m_QuatZ(qz)
		, m_QuatW(qw)
		, m_PosX(px)
		, m_PosY(py)
		, m_PosZ(pz)
	{
	};
	MINI_VARIABLE(int, QuatX);
	MINI_VARIABLE(int, QuatY);
	MINI_VARIABLE(int, QuatZ);
	MINI_VARIABLE(int, QuatW);
	MINI_VARIABLE(int, PosX);
	MINI_VARIABLE(int, PosY);
	MINI_VARIABLE(int, PosZ);
}; //tolua_exports
//脚本镜头 
class EXPORT_SANDBOXENGINE scriptCamera;
class scriptCamera : public MNSandbox::Object //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	scriptCamera();
	~scriptCamera();
	void initScriptCamera(const char* scriptName, float fov, float fwidth, float fheight, float fnear, float ffar, const Rainbow::WorldPos &pos);

	void update(float dtime);
// 	//使用输入消息分发机制，这个输入逻辑函数将废弃
// 	int onInputEvent(const Rainbow::LegacyInputEvent& event);
	void applyToEngine(World* pworld);
// 	void setRotate(float x, float y);
	void setRotation(Rainbow::Quaternionf quat);
	void setPosition(Rainbow::WorldPos pos);
	void setLookAt(Rainbow::WorldPos eye, Rainbow::Vector3f dir, Rainbow::Vector3f up);
	void setFov(float fov);
	Rainbow::WorldPos getPosition();
	Rainbow::Quaternionf getRotation();

	Rainbow::Vector3f GetForward()
	{
		return _rot * Rainbow::Vector3f::zAxis;
	}
	Rainbow::Vector3f GetRight()
	{
		return _rot * Rainbow::Vector3f::xAxis;
	}
	Rainbow::Vector3f GetUp()
	{
		return _rot * Rainbow::Vector3f::yAxis;
	}
	//tolua_end
public:
	//tolua_begin
	float m_fRotateY;
	float m_fRotateX;
	Rainbow::Camera* m_pCamera;
	Rainbow::Vector3f m_vLookDir;
	Rainbow::WorldPos m_wPosition;

	int m_nWinWidth;
	int m_nWinHeight;
	float m_fFov;
	float m_fCurrentFov;
	std::string m_strScriptName;


	Rainbow::Vector3f _pos;
	Rainbow::Quaternionf _rot;
	float m_MoveSpeed;
	float m_MoveStrafe;
	float m_MoveForward;
	float m_RotX;
	float m_RotY;

	float m_RotSpeed;
	//tolua_end
}; //tolua_exports

class EXPORT_SANDBOXENGINE GameCamera;
class GameCamera //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	GameCamera();
	virtual ~GameCamera();

	virtual Rainbow::WorldPos CalCollidedEyePos(World *pworld, const Rainbow::WorldPos &pos, const Rainbow::Vector3f &dir, float dist);

	virtual void update(float dtime, World *pworld);
	void tick();
	CameraControlMode getMode()
	{
		return m_Mode;
	}
	void setMode(CameraControlMode mode); //0: fps,  1: 背面观看角色，  2：正面观看角色
	void setScreenSize(int width, int height);
	virtual void setPosition(const Rainbow::WorldPos &pos);

	Rainbow::Camera* getEngineCamera();
	void SetEngineCameraPos(const Rainbow::WorldPos &pos);
	void SetEngineCameraRot(const Rainbow::Quaternionf& rot);
	Rainbow::Quaternionf GetEngineCameraRot();
	void UpdateEngineCamera(float dtime);
	void moveForward(float dist); //-dist = back
	void moveSide(float dist); //-dist = left
	void moveUp(float dist);
	virtual void rotate(float yaw, float pitch); //screen[-1, 1]
	void setRotate(float yaw, float pitch);
	void rotateOnScreen(int xpixels, int ypixels); //屏幕像素点的移动
	void updateShaking();

	void setBobbing(bool b);
	bool getBobbing();
	//void setZoom(bool needZoom, int zoomticks = 0, int recoverticks = 6);

	virtual void setZoomInOut(float targetFov, int zoomticks = 0, int recoverticks = 6);

	void disableZoom();
	void resetZoom();

	void setFov(float fov);
	float getFov() { return m_Fov; }

	void setDistMultiply(float t) { m_DistMultiply = t; }
	float getDistMultiply() { return m_DistMultiply; }

	void setCameraMode(CameraModel* cameraModel);

	void SetPlayer(IPlayerControl* player)
	{
		m_Player = player;
	}

	IPlayerControl* GetPlayer()
	{
		return m_Player;
	}

	Rainbow::WorldPos getPosition();
	const Rainbow::Vector3f& getLookDir() { return m_LookDir; }
	Rainbow::Quaternionf getRotation();
	virtual Rainbow::Quaternionf getRotation(float pitch, float yaw, float roll);
	WCoord getEyePos();

	void trigerFPSHandAnimation();

	void beginCaptureAnim(int seq);
	void endCaptureAnim();
	void saveCaptureAnim();
	void loadCaptureAnim();
	void playAnim(int seq);
	void stopAnim();
	virtual void applyToEngine(World* pworld);
	void applyToEngineWithLerpOrNot(World *pworld, bool bLerp);
	bool isShelteredByBlock(World* pworld, const Rainbow::AABB& box);

	virtual float getRotatePitch()
	{
		return m_RotatePitch;
	}

	virtual float getRotateYaw()
	{
		return m_RotateYaw;
	}

	float m_RotateYaw;
	float m_RotatePitch;

	bool m_IsNeedShowHand;
	bool m_IsNeedHideHand;

	float m_MoveForward; //-1: move back
	float m_MoveRight;
	float m_MoveUp;

	float m_CurrentShakeTime;
	
	
	Rainbow::Vector3f m_ShakingPower;

	float m_ShakingDuration;
	float m_ShakingOriginDuration;
	//tolua_end

public:

//#ifdef TestScriptCamera
	void addScriptCamera(char* name, const char* scriptName, float fov, float fnear, float ffar);
	void removeScriptCamera(char* name);
	scriptCamera* getScriptCameraWithName(char* name);
	int getScriptCameraCount();
	bool setSpCameraPosRot(char* name, Rainbow::WorldPos pos, Rainbow::Quaternionf qut/*float yaw, float pitch*/);
	bool setSpCameraPos(char* name, Rainbow::WorldPos pos);
	bool setSpCameraRot(char* name, Rainbow::Quaternionf qut);
	bool setSpCameraLookAt(char* name, Rainbow::WorldPos eye, Rainbow::Vector3f dir, Rainbow::Vector3f up);
	bool setSpCameraFov(char* name, float fov);
	bool getSpCameraPos(char* name, Rainbow::WorldPos& pos);
	bool getSpCameraRot(char* name, Rainbow::Quaternionf& rot);
//#endif
	//745 函数移植
	void getViewRayByScreenPt(MINIW::WorldRay* pRay, float x, float y);

	void InitEngineCamera(World* world = nullptr);

	void AttachCameralModeToScene(Rainbow::GameScene* scene);
	CameraModel* getCameraModel();

	void setRotateInterp(bool b) {
		m_bInterpRot = b;
	}
	bool getRotateInterp() {
		return m_bInterpRot;
	}
	void setCameraLerpSpeed(float spd) {
		m_CameraLerpSpeed = spd;
	}
	float getCameraLerpSpeed() {
		return m_CameraLerpSpeed;
	}

	bool isCameraVaild() {
		return m_pCamera.Get() != nullptr;
	}

	bool GetScreenPointFromeWeapon(int anchorId, Rainbow::Vector2f& screenPos);
	bool GetViewportPointFromeWeapon(int anchorId, Rainbow::Vector2f& viewportPos);

	bool GetScreenPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& screenPos);
	bool GetViewportPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& viewportPos);

	bool GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos);
private:
	bool GetPointFromeWeapon(int anchorId, Rainbow::Vector2f& pos, int pointType);
	bool GetPointFromeWeaponPart(int weaponAnchorId, int partAnchorId, Rainbow::Vector2f& pos, int pointType);

	void onChangeViewMode();

private:
	bool m_bInterpRot;
	float m_CameraLerpSpeed;
	//目标旋转插值
	Rainbow::Quaternionf m_qInterpRotate;
	template<typename T>
	T InternalInterp(const T& target, T& last, float dt, float lerpSpeed);
	//相机位置偏移平滑
	Rainbow::WorldPos m_vInterpCamPos;
protected:
	CameraControlMode m_Mode;
	Rainbow::PPtr<Rainbow::Camera> m_pCamera;
	//Rainbow::Camera *m_pCamera;
	//Rainbow::PPtr<Rainbow::Camera> m_FrontCamera;
	//Rainbow::Camera* m_FrontCamera;
	Rainbow::Vector3f m_LookDir;
	Rainbow::WorldPos m_Position;
	Rainbow::Vector3f m_SneakOffset;


	int m_WinWidth;
	int m_WinHeight;
	float m_Fov;
	float m_CurrentFov;
	IPlayerControl* m_Player;

	CameraModel* m_CameraModel;

	bool m_ZoomIn;
	bool m_NeedZoom;
	float m_CurZoomTime;
	float m_ZoomBeginTime;
	float m_ZoomRecoverTime;
	float m_DistMultiply;

	//For cameraBobing
	Rainbow::Vector3f m_ShakeVector;
	float m_ShakeRotZ;
	bool m_EnableCameraBob;

	bool m_TriggerZoom;
	float m_CurrentLocalPosY;
	float m_TargetFov;
	float m_DeltaFov;
	float m_StartZoomFov;

	float m_ShowHandDuration;
	float m_HideHandDuration;
	float m_LerpStartMark;
	float m_SwithThingHandlowBound;
	//Rainbow::Stabilizer<Rainbow::Vector3f> m_PositionStabilizer;
	//Rainbow::Stabilizer<Rainbow::Vector3f> m_RotationStabilizer;
	MINIW::CameraAnimation *m_CamAnim;

	std::map<std::string, scriptCamera*> m_mScriptCameras;

}; //tolua_exports

#endif