# Sleep状态下移动处理机制分析

## 概述

当玩家处于 **Sleep** 状态时，服务器会拒绝玩家的移动请求，并通过 `SendResetPosition2Client` 函数向其他玩家同步玩家的当前位置，而不是移动信息。

## 关键代码分析

### handleRoleMove2Host 处理流程

**代码位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1083-1385`

#### 1. Sleep状态检测

**代码位置**: 第1114-1118行

```cpp
if (player->isCurrentActionState("Sleep") || (motion.mapid() > 0 && motion.mapid() - 1 != player->getCurMapID()))
{
    SendResetPosition2Client(player, targetyaw, targetpitch);
    return;
}
```

**关键逻辑**:
- 当 `player->isCurrentActionState("Sleep")` 返回 `true` 时
- **立即调用** `SendResetPosition2Client` 
- **直接返回**，不执行后续的移动处理逻辑

#### 2. SendResetPosition2Client 函数

**代码位置**: 第337-344行

```cpp
static void SendResetPosition2Client(ClientPlayer *player, float yaw, float pitch)
{
    if (player->getWorld() == NULL) return;
    MpActorTrackerEntry *entry = player->getWorld()->getMpActorMgr()->getTrackerEntry(player->getObjId());
    if (entry == NULL) return;

    entry->sendActorMovementToClient(player->getUin(), player, yaw, pitch);
}
```

**功能说明**:
- 获取玩家的 `MpActorTrackerEntry` 追踪器
- 调用 `sendActorMovementToClient` 向其他玩家同步位置
- **注意**: 这里同步的是玩家的**当前位置**，不是移动后的位置

## 🔍 **Sleep状态移动处理机制**

### **正常移动流程 vs Sleep状态流程**

| 处理阶段 | 正常移动 | Sleep状态 |
|---------|---------|-----------|
| **移动请求接收** | ✅ 接收PB_ROLE_MOVE_CH | ✅ 接收PB_ROLE_MOVE_CH |
| **状态检查** | 通过检查 | ❌ `isCurrentActionState("Sleep")` = true |
| **位置更新** | ✅ 更新玩家位置 | ❌ 拒绝位置更新 |
| **移动同步** | ✅ 向其他玩家广播新位置 | ⚠️ 向其他玩家同步**当前位置** |
| **返回结果** | 继续执行移动逻辑 | 立即返回，终止处理 |

### **关键发现**

#### ❌ **不会向其他玩家同步移动信息**

**原因分析**:

1. **提前返回**: Sleep状态检查在移动处理逻辑之前，一旦检测到Sleep状态立即返回
2. **位置重置**: `SendResetPosition2Client` 同步的是玩家的**当前位置**，不是移动后的位置
3. **移动拒绝**: 服务器完全拒绝Sleep状态下的移动请求

#### ⚠️ **但会同步当前位置**

**`SendResetPosition2Client` 的作用**:

```cpp
entry->sendActorMovementToClient(player->getUin(), player, yaw, pitch);
```

- **目的**: 确保其他玩家看到的是玩家的**正确位置**
- **内容**: 玩家的当前位置和朝向（不是移动后的位置）
- **机制**: 通过 `MpActorTrackerEntry` 的移动广播系统

### **Sleep状态的其他限制**

除了Sleep状态，以下情况也会触发位置重置：

1. **地图不匹配**: `motion.mapid() - 1 != player->getCurMapID()`
2. **传送仓限制**: `player->isSittingInStarStationCabin()`
3. **移动过快**: 超过速度限制
4. **作弊检测**: 反作弊系统检测到异常
5. **碰撞检测**: 位置碰撞检测失败

## 📋 **处理流程图**

```
客户端发送PB_ROLE_MOVE_CH
         ↓
服务器接收移动请求
         ↓
    isCurrentActionState("Sleep")?
         ↓
    [是] → SendResetPosition2Client
         ↓
    向其他玩家同步当前位置
         ↓
         return (终止处理)
         
    [否] → 继续正常移动处理
         ↓
    更新玩家位置
         ↓
    向其他玩家广播新位置
```

## 总结

**当玩家处于Sleep状态时**:

1. ❌ **不会向其他玩家同步移动信息** - 服务器拒绝移动请求
2. ⚠️ **会同步当前位置** - 通过 `SendResetPosition2Client` 确保位置一致性
3. 🔒 **移动被完全阻止** - 提前返回，不执行任何移动逻辑
4. 🎯 **位置保持不变** - 玩家保持在Sleep开始时的位置

这种设计确保了：
- **游戏逻辑一致性**: Sleep状态下玩家不能移动
- **网络同步准确性**: 其他玩家看到的位置是正确的
- **防止作弊**: 避免Sleep状态下的移动作弊
