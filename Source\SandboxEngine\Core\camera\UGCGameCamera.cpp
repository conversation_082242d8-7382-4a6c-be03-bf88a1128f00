#include "SandboxCoreDriver.h"
#include "IRecordInterface.h"
#include "ClientInfoProxy.h"
#include "CameraManager.h"
#include "UGCGameCamera.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "gamemode/GameModeDef.h"
#include "IClientActor.h"
#include "CameraModel.h"
#include "OgreUtils.h"
#include "world.h"
#include "IActorLocoMotion.h"
#include "IActorBody.h"
#include "ClientInfoProxy.h"
#include "IWorldConfigProxy.h"
#include "IMiniDeveloperProxy.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace fairygui;

UGCGameCamera::UGCGameCamera() : m_ActorObjId(0), m_AdjustCamera(AdjustStatus::over)
{
	resetGameCamera();
}

UGCGameCamera::~UGCGameCamera()
{

}

Rainbow::WorldPos UGCGameCamera::CalCollidedEyePos(World* pworld, const Rainbow::WorldPos& pos, const Rainbow::Vector3f& dir, float dist)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return GameCamera::CalCollidedEyePos(pworld, pos, dir, dist);

	//沿相对坐标轴的偏移位置
	Vector3f cameraOffset = getRelationAxisOffset();

	//具体加上位置后的偏移
	Vector3f actualPos = pos.toVector3();

	//是否缩进
	if (!m_EnableAutoZoom)
		return WorldPos::fromVector3(cameraOffset) + actualPos + dir * (1.0f * dist);

	CollideAABB box;
	box.dim = WCoord(20, 20, 20);
	box.pos = WCoord(actualPos) - box.dim / 2;

	WCoord mvec = dir * dist;

	Rainbow::Vector3f colnormal;
	float mdist = pworld->moveBox(box, mvec, colnormal);

	return WorldPos::fromVector3(cameraOffset) + actualPos + dir * (mdist * dist);
}

Rainbow::WorldPos UGCGameCamera::CalCollidedEyePosWithHeadOffset(World* pworld, const Rainbow::WorldPos& pos, const Rainbow::Vector3f& offsetpos, const Rainbow::Vector3f& dir, float dist)
{
	//if (!GetIWorldConfigProxy()->IsUGCGame())
	//	return GameCamera::CalCollidedEyePos(pworld, pos, dir, dist);

	//沿相对坐标轴的偏移位置
	Vector3f cameraOffset = getRelationAxisOffset();

	//具体加上位置后的偏移
	Vector3f actualPos = pos.toVector3();

	//是否缩进
	if (!m_EnableAutoZoom)
		return WorldPos::fromVector3(cameraOffset) + actualPos + dir * (1.0f * dist);

	CollideAABB box;
	box.dim = WCoord(105, 105, 105);
	box.pos = WCoord(actualPos + offsetpos) - box.dim / 2;

	WCoord mvec = dir * dist;

	Rainbow::Vector3f colnormal;
	float mdist = pworld->moveBox(box, mvec, colnormal);
	
	// 当mdist小于1时，表示摄像机不能移动到目标位置（发生碰撞）
	// 此时直接返回眼睛位置（actualPos加上相对坐标轴偏移）
	if (mdist < 1.0f)
		return WorldPos::fromVector3(cameraOffset) + actualPos + Vector3f(0.f,0.f,16.f);
		
	return WorldPos::fromVector3(cameraOffset) + actualPos + offsetpos + dir * (mdist * dist);
}

void UGCGameCamera::resetGameCamera()
{
	//还原fov
	setFov(75.0);
	
	m_CameraOffset.x = 0;
	m_CameraOffset.y = 0;
	m_CameraOffset.z = 0;
	//m_CameraAniPos.x = m_Position.x;
	//m_CameraAniPos.y = m_Position.y;
	//m_CameraAniPos.z = m_Position.z;

	m_PlayCameraAnim = false;
	m_AdjustCamera = AdjustStatus::over;
	m_EnableAutoZoom = true;
	m_bInitAutoZoom = false;
	
	m_MoveFollowable = true;
	m_StandFixedPoint = false;
	m_RotSelfCenter = false;

	m_CameraRotationAngle = Vector3f(0, 0, 0);

	m_LockYawRot = false;
	m_LockPitchRot = false;

	for (int index = 0; index < 3; ++index)
	{
		m_MoveAnimInfo[index].duration = 0;
		m_MoveAnimInfo[index].time = 0;
		m_MoveAnimInfo[index].easetype = EaseType::Linear;
		m_MoveAnimInfo[index].offset = 0.0f;
		m_MoveAnimInfo[index].oldValue = MAX_FLOAT;
	}

	for (int index = 0; index < 3; ++index)
	{
		m_AbsMoveAnimInfo[index].duration = 0;
		m_AbsMoveAnimInfo[index].time = 0;
		m_AbsMoveAnimInfo[index].easetype = EaseType::Linear;
		m_AbsMoveAnimInfo[index].offset = 0.0f;
		m_AbsMoveAnimInfo[index].oldValue = MAX_FLOAT;
	}

	for (int index = 0; index < 3; ++index)
	{
		m_RotAnimInfo[index].duration = 0;
		m_RotAnimInfo[index].time = 0;
		m_RotAnimInfo[index].easetype = EaseType::Linear;
		m_RotAnimInfo[index].offset = 0.0f;
		m_RotAnimInfo[index].oldValue = MAX_FLOAT;
	}

	m_FovAnimInfo.duration = 0;
	m_FovAnimInfo.time = 0;
	m_FovAnimInfo.easetype = EaseType::Linear;
	m_FovAnimInfo.offset = 0;
	m_FovAnimInfo.oldValue = -1.0f;

	m_TargetRotateAnimInfo.duration = 0;
	m_TargetRotateAnimInfo.time = 0;
	m_TargetRotateAnimInfo.easetype = EaseType::Linear;
	m_TargetRotateAnimInfo.offset = 0;
	m_TargetRotateAnimInfo.oldValue = -1.0f;

	m_RotateRoll = 0.0f;

	m_LockCameraPitch = m_RotatePitch;
	m_LockCameraYaw = m_RotateYaw;

	m_ActorObjId = 0;
	m_LastYawRemainderNum = 0;
	setEnableRoleTranslucent(false);
}

void UGCGameCamera::setPosition(const Rainbow::WorldPos& pos)
{
	//if (!m_AdjustCamera)
	//	m_CameraAniPos = pos;

	if (!GetIWorldConfigProxy()->IsUGCGame())
	{
		GameCamera::setPosition(pos);
		return;
	}
	//如果不跟随
	if (!m_MoveFollowable)
		return;
	//固定位置就直接使用
	if (m_StandFixedPoint)
	{
		GameCamera::setPosition(m_CameraOffset);
		return;
	}

	GameCamera::setPosition(pos);
}

void UGCGameCamera::setEnableRoleTranslucent(bool enable)
{
	m_RoleTranslucent = enable;
	if (GetIPlayerControl())
	{
		dynamic_cast<IClientActor*>(GetIPlayerControl())->SetXrayEffectEnable(enable);
	}
}

void UGCGameCamera::setLockYawRot(bool isLock)
{
	if (m_LockYawRot == isLock)
		return;

	m_LockYawRot = isLock;
	m_LockCameraYaw = m_RotateYaw;
}

void UGCGameCamera::setLockPitchRot(bool isLock)
{
	if (m_LockPitchRot == isLock)
		return;

	m_LockPitchRot = isLock;
	m_LockCameraPitch = m_RotatePitch;
}

void UGCGameCamera::setRotBySelfCenter(bool isSelfCenter)
{
	if (m_Player == NULL)
		return;

	if (m_RotSelfCenter == isSelfCenter)
		return;

	m_RotSelfCenter = isSelfCenter;

	if (!m_RotSelfCenter)
		return;

	m_CameraRotationAngle.x = m_RotateYaw;
	m_CameraRotationAngle.y = m_RotatePitch;
	m_CameraRotationAngle.z = m_RotateRoll;
}

void UGCGameCamera::setCameraTargetPos(Rainbow::WorldPos& targetPos, unsigned int time, int easeType/* = 0*/)
{
	//立马生效，不执行动画
	if (easeType == -1 || time == 0)
	{
		m_CameraOffset += targetPos;
		return;
	}

	if (easeType < (int)EaseType::Linear || easeType >(int)EaseType::Custom)
		return;

	float offsetX = WorldPos::Fix2Flt(targetPos.x);
	float offsetY = WorldPos::Fix2Flt(targetPos.y);
	float offsetZ = WorldPos::Fix2Flt(targetPos.z);

	float oldValueX = WorldPos::Fix2Flt(m_CameraOffset.x);
	float oldValueY = WorldPos::Fix2Flt(m_CameraOffset.y);
	float oldValueZ = WorldPos::Fix2Flt(m_CameraOffset.z);

	if (abs(offsetX) > 0.1)
	{
		m_MoveAnimInfo[0].offset = offsetX;
		m_MoveAnimInfo[0].duration = 0;
		m_MoveAnimInfo[0].time = time;
		m_MoveAnimInfo[0].easetype = (EaseType)easeType;
	}

	if (abs(offsetY) > 0.1)
	{
		m_MoveAnimInfo[1].offset = offsetY;
		m_MoveAnimInfo[1].duration = 0;
		m_MoveAnimInfo[1].time = time;
		m_MoveAnimInfo[1].easetype = (EaseType)easeType;
	}

	if (abs(offsetZ) > 0.1)
	{
		m_MoveAnimInfo[2].offset = offsetZ;
		m_MoveAnimInfo[2].duration = 0;
		m_MoveAnimInfo[2].time = time;
		m_MoveAnimInfo[2].easetype = (EaseType)easeType;
	}

	m_MoveAnimInfo[0].oldValue = oldValueX;
	m_MoveAnimInfo[1].oldValue = oldValueY;
	m_MoveAnimInfo[2].oldValue = oldValueZ;

	m_PlayCameraAnim = true;
}

void UGCGameCamera::setCameraTargetRot(Vector3f& targetRot, unsigned int time, int easeType/* = 0*/)
{
	//立马生效，不执行动画
	if (easeType == -1 || time == 0)
	{
		m_RotateYaw += targetRot.x;
		m_RotatePitch += targetRot.y;
		m_RotateRoll += targetRot.z;
		return;
	}

	if (easeType < (int)EaseType::Linear || easeType >(int)EaseType::Custom)
		return;

	if (abs(targetRot.x) > 0.01)
	{
		m_LastYawRemainderNum = 0;
		m_RotAnimInfo[0].offset = targetRot.x;
		m_RotAnimInfo[0].oldValue = m_RotateYaw;
		m_RotAnimInfo[0].duration = 0;
		m_RotAnimInfo[0].time = time;
		m_RotAnimInfo[0].easetype = (EaseType)easeType;
	}

	if (abs(targetRot.y) > 0.01)
	{
		m_RotAnimInfo[1].offset = targetRot.y;
		m_RotAnimInfo[1].oldValue = m_RotatePitch;
		m_RotAnimInfo[1].duration = 0;
		m_RotAnimInfo[1].time = time;
		m_RotAnimInfo[1].easetype = (EaseType)easeType;
	}

	if (abs(targetRot.z) > 0.01)
	{
		m_RotAnimInfo[2].offset = targetRot.z;
		m_RotAnimInfo[2].oldValue = m_RotateRoll;
		m_RotAnimInfo[2].duration = 0;
		m_RotAnimInfo[2].time = time;
		m_RotAnimInfo[2].easetype = (EaseType)easeType;
	}

	m_PlayCameraAnim = true;
}

void UGCGameCamera::setCameraTargetFov(float fov, unsigned int time, int easeType/* = 0*/)
{
	//立马生效，不执行动画
	if (easeType == -1 || time == 0)
	{
		float curFov = m_Fov + fov;
		setFov(curFov);
		return;
	}

	if (easeType < (int)EaseType::Linear || easeType >(int)EaseType::Custom)
		return;
	/*
	float targetFov = fov;
	if (targetFov < 1.0f)
		targetFov = 1.0f;
	if (targetFov > 180.0f)
		targetFov = 180.0f;
	*/

	m_FovAnimInfo.offset = fov;
	m_FovAnimInfo.oldValue = m_Fov;
	m_FovAnimInfo.duration = 0;
	m_FovAnimInfo.time = time;
	m_FovAnimInfo.easetype = (EaseType)easeType;
	m_PlayCameraAnim = true;
}

Vector3f UGCGameCamera::getRelationAxisOffset()
{
	if (m_RotSelfCenter)
		return m_CameraOffset.toVector3();

	Quaternionf quat = getRotation(m_RotatePitch, m_RotateYaw, m_RotateRoll);

	//把相应的轴旋转指定角度
	Vector3f xAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::xAxis);
	Vector3f yAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::yAxis);
	Vector3f zAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);

	Vector3f cameraOffstVec = m_CameraOffset.toVector3();
	Vector3f relationXOffset = xAxisDir * cameraOffstVec.x;
	Vector3f relationYOffset = yAxisDir * cameraOffstVec.y;
	Vector3f relationZOffset = zAxisDir * cameraOffstVec.z;
	return relationXOffset + relationYOffset + relationZOffset;
}

void UGCGameCamera::update(float dtime, World* pworld)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
	{
		GameCamera::update(dtime, pworld);
		return;
	}

	doCameraAnimation(dtime);

	if (m_ActorObjId == 0)
	{
		GameCamera::update(dtime, pworld);
		return;
	}

	if (m_Player == NULL)
	{
		GameCamera::update(dtime, pworld);
		return;
	}


	IClientActor *pActor = pworld->getActorMgr()->iFindActorByWID(m_ActorObjId);
	if (pActor == NULL)
	{
		GameCamera::update(dtime, pworld);
		return;
	}

	applyToEngine(pworld);
}

void UGCGameCamera::applyToEngine(World* pworld)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
	{
		GameCamera::applyToEngine(pworld);
		return;
	}

	if (dynamic_cast<IClientPlayer*>(GetIPlayerControl())->IsRunSandboxPlayer()) return;

	if (m_Player == NULL) return;
	if (m_pCamera.Get() == nullptr)
	{
		InitEngineCamera();
	}
	if (m_pCamera.Get() == nullptr) return;

	//ar模式渲染
	if (GetIPlayerControl() && !GetIPlayerControl()->getIWorld()->getRenderTrunk())
	{
		Rainbow::Matrix4x4f cameraView;
		Rainbow::Matrix4x4f cameraProjection;
		Rainbow::Matrix4x4f mModelMatrixs;
		Rainbow::Vector3f cameraViewPos = cameraView.GetPosition();
		Rainbow::Vector3f mModelMatrixsPos = mModelMatrixs.GetPosition();
		Rainbow::Vector3f dir = cameraViewPos - mModelMatrixsPos;
		dir *= 15.0f;
		cameraViewPos += dir;
		Rainbow::Quaternionf cameraViewRotate;
		Rainbow::MatrixToQuaternionf(cameraView, cameraViewRotate);
		Rainbow::QuaternionfToMatrix(cameraViewRotate, cameraView);
		cameraView.SetTranslate(-cameraViewPos);
		return;
	}

	if (GetIPlayerControl() && GetIPlayerControl()->GetPlayerControlSneaking())
	{
		m_SneakOffset = 20 * Rainbow::Vector3f::neg_yAxis;
	}
	else
	{
		m_SneakOffset = Rainbow::Vector3f::zero;
	}

	Quaternionf quat;
	//相机自转
	Quaternionf rotationQuat;
	if (m_Mode == CAMERA_TPS_OVERLOOK)
	{
		quat = getRotation(0.0f, m_RotateYaw, 0.0f);
		rotationQuat = getRotation(0.0f, m_CameraRotationAngle.x, 0.0f);
	}
	else
	{
		quat = getRotation(m_RotatePitch, m_RotateYaw, 0);
		rotationQuat = getRotation(m_CameraRotationAngle.y, m_CameraRotationAngle.x, 0.0f);
	}

	if (m_RotSelfCenter)
		m_LookDir = RotateVectorByQuat(rotationQuat, Rainbow::Vector3f(0, 0, 1.0f));
	else
		m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));

	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false) && GAMERECORD_INTERFACE_EXEC(isPause(), false))
		return;

	Quaternionf riderot;
	bool has_riderot = false;
	auto clientactor = dynamic_cast<IClientActor*>(m_Player);
	auto riddencomponent = clientactor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
	if (riddencomponent)
	{
		bool result = riddencomponent->Event2().Emit<bool&, Rainbow::Quaternionf, IPlayerControl*>("Ridden_GetBindRot",
			has_riderot, riderot, m_Player);
		Assert(result);
	}

	IClientActor* pActor = NULL;
	if (m_ActorObjId == 0)
		pActor = clientactor;
	else
	{
		pActor = pworld->getActorMgr()->iFindActorByWID(m_ActorObjId);
		if (pActor == NULL)
			pActor = clientactor;
	}

	
	if (m_Mode == CAMERA_FPS)
	{
		WorldPos position = m_Position + m_SneakOffset;
		//if (m_AdjustCamera)
		//	position = m_CameraAniPos + m_SneakOffset;
		
		if (has_riderot) quat = riderot * quat;

		if (m_EnableCameraBob && !has_riderot)
		{
			float oscillationAx = 5;
			float oscillationAy = 5;
			float rotaionPower = 0.15f;

			if (GetIPlayerControl()->GetPlayerControlSneaking())
			{
				oscillationAx = 5 * 0.3f;
				oscillationAy = 5 * 0.3f;
				rotaionPower = 0.15f * 0.3f;
			}
			if (m_CameraModel == NULL) return;
			float currentTime = m_CameraModel->getCurrentShakeTime();
			m_ShakeVector.x = oscillationAx * Rainbow::Sin(0.5f * ONE_PI + 2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle());
			m_ShakeVector.y = oscillationAy * Abs(Rainbow::Sin(2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle()));
			m_ShakeVector.z = 0;
			m_ShakeRotZ = -rotaionPower * Rainbow::Sin(2 * currentTime * ONE_PI / m_CameraModel->getCurrentShakeCycle());

			const Quaternionf tmpRot = AxisAngleToQuaternionf(Vector3f(0, 0, 1), Deg2Rad(m_ShakeRotZ));

			if (m_RotSelfCenter)
			{
				const Rainbow::Vector3f tmp = RotateVectorByQuat(rotationQuat, m_ShakeVector);
				position += tmp;
			}
			else
			{
				const Rainbow::Vector3f tmp = RotateVectorByQuat(quat, m_ShakeVector);
				position += tmp;
			}

			quat = quat * tmpRot;
		}
		//把相应的轴旋转指定角度
		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3());
		else/* if (m_MoveFollowable)*/
		{
			Vector3f cameraOffset = getRelationAxisOffset();
			Vector3f targetPos = position.toVector3() + cameraOffset;
			m_pCamera->SetWorldPosition(targetPos);
		}
		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_TPS_BACK || m_Mode == CAMERA_TPS_BACK_2)
	{
		Rainbow::Vector3f offset(0, 45.0f, 0);
		float dist = (getRotatePitch() / -80.0f);
		if (dist < 0) dist = 0;
		dist = (320.0f - dist * 270.0f) * m_DistMultiply;


		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3() + m_SneakOffset);
		else
		{
			WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -m_LookDir, dist);
			m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
		}

		if (has_riderot)
		{
			riderot = riderot.Inverse();
			if (m_RotSelfCenter)
				riderot = riderot * rotationQuat;
			else
				riderot = riderot * quat;

			m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
		}

		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_TPS_BACK_SHOULDER)
	{
		// 过肩视角的相机偏移
		Rainbow::Vector3f offset(-40.0f, 10.0f, 0); // 向左偏移40单位,高度10单位
		float dist = (getRotatePitch() / -80.0f);
		if (dist < 0) dist = 0;
		dist = (240.0f - dist * 202.5f) * m_DistMultiply; // 距离从320减少25%到240,270减少25%到202.5

		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3() + m_SneakOffset);
		else
		{
			// 根据角色朝向调整相机偏移方向
			float actorYaw = pActor->getILocoMotion()->GetRotateYaw();
			Quaternionf actorQuat = getRotation(0, actorYaw, 0);
			Vector3f rotatedOffset = RotateVectorByQuat(actorQuat, offset);

			WorldPos pos = CalCollidedEyePosWithHeadOffset(pworld, m_Position, rotatedOffset, -m_LookDir, dist);
			m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
		}

		if (has_riderot)
		{
			riderot = riderot.Inverse();
			if (m_RotSelfCenter)
				riderot = riderot * rotationQuat;
			else
				riderot = riderot * quat;

			m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
		}

		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_TPS_FRONT)
	{
		Rainbow::Vector3f offset(0, 60.0f, 0);
		float dist = (-getRotatePitch() / -80.0f);
		if (dist < 0) dist = 0;
		dist = (500.0f - dist * 450.0f) * m_DistMultiply;
		if (m_RotSelfCenter)
			m_LookDir = RotateVectorByQuat(rotationQuat, Rainbow::Vector3f::zAxis);
		else
			m_LookDir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);

		if (m_StandFixedPoint)
		{
			Rainbow::Vector3f pos = m_CameraOffset.toVector3();
			m_pCamera->LookAt(pos + m_SneakOffset, pos + m_SneakOffset - m_LookDir, Rainbow::Vector3f::yAxis);
		}
		else/* if (m_MoveFollowable)*/
		{
			Rainbow::Vector3f pos = CalCollidedEyePos(pworld, m_Position + offset, m_LookDir, dist).toVector3();
			m_pCamera->LookAt(pos + m_SneakOffset, pos + m_SneakOffset - m_LookDir, Rainbow::Vector3f::yAxis);
		}

		if (has_riderot)
		{
			riderot = riderot.Inverse();
			if (m_RotSelfCenter)
				riderot = riderot * rotationQuat;
			else
				riderot = riderot * quat;
			m_LookDir = RotateVectorByQuat(riderot, Rainbow::Vector3f::zAxis);
		}
	}
	else if (m_Mode == CAMERA_TPS_OVERLOOK)
	{
		Rainbow::Vector3f offset(0, 40.0f, 0);
		float dist = 500.0f;
		//m_RotationStabilizer.AddValue(Vector3f(70.0f, 0.0f, 0.0f));
		quat = getRotation(70.0f, 0.0f, 0.0f);
		Rainbow::Vector3f dir;
		if (m_RotSelfCenter)
			dir = RotateVectorByQuat(rotationQuat, Rainbow::Vector3f::zAxis);
		else
			dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);

		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3() + m_SneakOffset);
		else/* if (m_MoveFollowable)*/
		{
			WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
			m_pCamera->SetWorldPosition(pos.toVector3() + m_SneakOffset);
		}
		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_THIRD_SHOULDER)
	{
		float yaw, pitch;
		Rainbow::Vector3f offset(0.0f, -80.0f, -80.0f);
		yaw = pActor->getILocoMotion()->GetRotateYaw() + 135.0f;
		pitch = 5.0f;
		this->setFov(45);
		Quaternionf quat;
		quat = getRotation(pitch, yaw, 0.0f);
		float dist = (m_RotatePitch / -80.0f);
		if (dist < 0) dist = 0;
		dist = (500.0f - dist * 450.0f) * m_DistMultiply;
		float m_RotateYaw = round(pActor->getILocoMotion()->GetRotateYaw() * 10) / 10.0;
		if (m_RotateYaw == 90.0)
		{
			offset.y = -80.0f;
			offset.z = 50.0f;
		}
		else if (m_RotateYaw == -90.0)
		{
			offset.y = -80.0f;
			offset.z = -20.0f;
		}
		else if (m_RotateYaw == -180.0)
		{
			offset.z = 80.0f;
		}
		Rainbow::Vector3f dir;
		if (m_RotSelfCenter)
			dir = RotateVectorByQuat(rotationQuat, Rainbow::Vector3f::zAxis);
		else
			dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3());
		else/* if (m_MoveFollowable)*/
		{
			WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
			m_pCamera->SetWorldPosition(pos.toVector3());
		}
		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_TPS_SIDE)
	{
		float yaw, pitch;
		Rainbow::Vector3f offset(0.0f, -80.0f, -80.0f);
		yaw = pActor->getILocoMotion()->GetRotateYaw() + 80.0f;
		pitch = 5.0f;
		this->setFov(45);
		Quaternionf quat;
		quat = getRotation(pitch, yaw, 0.0f);
		float dist = (m_RotatePitch / -80.0f);
		if (dist < 0) dist = 0;
		dist = (500.0f - dist * 450.0f) * m_DistMultiply;
		float m_RotateYaw = round(pActor->getILocoMotion()->GetRotateYaw() * 10) / 10.0;
		if (m_RotateYaw == 90.0)
		{
			offset.x = -80.0f;
			offset.y = -80.0f;
			offset.z = 50.0f;
		}
		else if (m_RotateYaw == -90.0)
		{
			offset.x = 70.0f;
			offset.y = -80.0f;
			offset.y = -80.0f;
		}
		else if (m_RotateYaw == -180.0)
		{
			offset.z = 80.0f;
		}
		Rainbow::Vector3f dir;
		if (m_RotSelfCenter)
			dir = RotateVectorByQuat(rotationQuat, Rainbow::Vector3f::zAxis);
		else
			dir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
		if (m_StandFixedPoint)
			m_pCamera->SetWorldPosition(m_CameraOffset.toVector3());
		else/* if (m_MoveFollowable)*/
		{
			WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -dir, dist);
			m_pCamera->SetWorldPosition(pos.toVector3());
		}
		m_pCamera->SetWorldRotation(quat);
	}
	else if (m_Mode == CAMERA_CUSTOM_VIEW)
	{
		//如果绑定到Actor上就使用第三视角
		if (pActor == dynamic_cast<IClientActor*>(m_Player))
			updatePlayerCustomCamera(quat);
		else
			updateCustomCamera(quat, pworld, pActor);
	}

	if (GetIPlayerControl() && m_CameraModel && GetIPlayerControl()->getBobbingByRocket())
	{
		WorldPos position1 = WorldPos::fromVector3(m_pCamera->GetWorldPosition());

		float oscillationAx = 2;

		m_ShakeVector.x = oscillationAx * Rainbow::Sin(0.5f * ONE_PI + 2 * m_CurrentShakeTime * ONE_PI / (0.2f * m_CameraModel->getCurrentShakeCycle()));
		m_ShakeVector.y = 0;
		m_ShakeVector.z = 0;

		Quaternionf quat1 = m_pCamera->GetWorldRotation();
		Rainbow::Vector3f tmp = RotateVectorByQuat(quat1, m_ShakeVector);
		m_pCamera->SetWorldPosition(m_pCamera->GetPosition() + tmp);
	}

	if (GetIPlayerControl() && GetIPlayerControl()->isShakingCamera() && m_ShakingDuration > 0)
	{
		// updateShaking();
	}

	// updateShaking();

	for (auto iter = m_mScriptCameras.begin(); iter != m_mScriptCameras.end(); iter++)
	{
		iter->second->applyToEngine(pworld);
	}

	// m_FrontCamera 已经被注释了. 暂时先注释掉下面这行
	//this->m_FrontCamera->SetWorldMatrix(this->m_pCamera->GetWorldMatrix());
}

void UGCGameCamera::updateCustomCamera(const Rainbow::Quaternionf& quat, World* pworld, IClientActor* pClientActor)
{
	bool followActorRot = !m_LockYawRot && !m_LockPitchRot && !m_RotSelfCenter;
	const Rainbow::Quaternionf& actorQuat = getRotation(pClientActor->getILocoMotion()->GetRotationPitch(), pClientActor->getILocoMotion()->GetRotateYaw(), 0);
	if (followActorRot)
		m_pCamera->SetWorldRotation(actorQuat);
	else
		m_pCamera->SetWorldRotation(quat);

	//固定位置
	if (m_StandFixedPoint)
	{
		m_pCamera->SetWorldPosition(m_Position.toVector3());
		return;
	}

	if (m_MoveFollowable)
	{
		//第三视角
		Rainbow::Vector3f offset(0, 60.0f, 0);
		float dist = (getRotatePitch() / -80.0f);
		if (dist < 0) dist = 0;
		dist = (500.0f - dist * 450.0f) * m_DistMultiply;

		WorldPos pos = CalCollidedEyePos(pworld, m_Position + offset, -m_LookDir, dist);
		m_pCamera->SetWorldPosition(pos.toVector3());
	}
}

//与父类GameCamera中的自定义视角完全一样
void UGCGameCamera::updatePlayerCustomCamera(const Rainbow::Quaternionf& quat)
{
	if (CameraManager::GetInstance().getCameraEditState() == CAMERA_EDIT_STATE_EDIT)
	{
		m_pCamera->SetWorldRotation(quat);
	}
	else
	{
		if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA
			&& (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_CAMERA_AND_BODY
				|| m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_ONLY_CAMERA))
		{
			if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
			{
				m_pCamera->SetWorldPosition(getCameraConfigPosition(m_RotateYaw, m_RotatePitch));
			}
			m_pCamera->SetWorldRotation(quat);
		}
		else
		{
			if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
			{
				if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_BACKVIEW)// && m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_CAMERA_AND_BODY)
				{
					m_pCamera->SetWorldPosition(getCameraConfigPosition(m_RotateYaw, m_RotatePitch));
				}
				else if (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW && m_Player->HasReversed())
				{
					m_pCamera->SetWorldPosition(getCameraConfigPosition(m_Player->getPlayerControlLocoMotion()->GetRotateYaw(), m_Player->getPlayerControlLocoMotion()->GetRotationPitch()));
				}
				else
				{
					m_pCamera->SetWorldPosition(getCameraConfigPosition(m_Player->getPlayerControlLocoMotion()->GetRotateYaw() + 180.0f, m_Player->getPlayerControlLocoMotion()->GetRotationPitch()));
				}
			}
		}
	}
}

void UGCGameCamera::AdJustCamera(Rainbow::Vector3f pos, int animType, float time)
{
	//if (m_AdjustCamera)
	//{
	//	return;
	//}
	if (!m_Player)
		return;

	m_AdjustCamera = AdjustStatus::translate;
	m_PlayCameraAnim = true;

	auto& playerPos = m_Player->GetPlayerControlPosition();
	unsigned int timePs = time * 10000;

	m_AbsMoveAnimInfo[0].offset = pos.x;
	m_AbsMoveAnimInfo[0].duration = 0;
	m_AbsMoveAnimInfo[0].time = timePs;
	m_AbsMoveAnimInfo[0].easetype = (EaseType)animType;

	m_AbsMoveAnimInfo[1].offset = pos.y;
	m_AbsMoveAnimInfo[1].duration = 0;
	m_AbsMoveAnimInfo[1].time = timePs;
	m_AbsMoveAnimInfo[1].easetype = (EaseType)animType;

	m_AbsMoveAnimInfo[2].offset = pos.z;
	m_AbsMoveAnimInfo[2].duration = 0;
	m_AbsMoveAnimInfo[2].time = timePs;
	m_AbsMoveAnimInfo[2].easetype = (EaseType)animType;

	m_AbsMoveAnimInfo[0].oldValue = playerPos.x;
	m_AbsMoveAnimInfo[1].oldValue = playerPos.y;
	m_AbsMoveAnimInfo[2].oldValue = playerPos.z;
	
	dynamic_cast<IClientActor*>(m_Player)->setCanControl(false);
}

void UGCGameCamera::FocusCamera(Rainbow::Vector3f center, float length, Rainbow::Vector3f lookdir, int animType, float time)
{
	unsigned int timePs = time * 10000;

	m_TargetRotateAnimInfo.offset = length;
	m_TargetRotateAnimInfo.duration = 0;
	m_TargetRotateAnimInfo.time = timePs;
	m_TargetRotateAnimInfo.easetype = (EaseType)animType;
	m_TargetRotateAnimInfo.target = center;
	m_TargetRotateAnimInfo.lookDir = lookdir;
	m_TargetRotateAnimInfo.oldDir = Rainbow::Vector3f(m_RotateYaw, m_RotatePitch, m_RotateRoll);
}

Vector3f UGCGameCamera::getCameraConfigPosition(float yaw, float pitch)
{
	if (m_StandFixedPoint)
		return m_CameraOffset.toVector3();

	Vector3f cameraOffset = getRelationAxisOffset();

	WorldPos defaultPos = m_Position + cameraOffset;
	if (!m_MoveFollowable)
		return defaultPos.toVector3();
	
	if (GetIPlayerControl() == NULL)
	{
		return defaultPos.toVector3();
	}

	World *pWorld = GetIPlayerControl()->getIWorld();
	if (pWorld == NULL)
	{
		return defaultPos.toVector3();
	}

	WCoord pos = GetIPlayerControl()->getCameraPosition();

	//相对位置转换成绝对位置
	if (GetIPlayerControl()->getOption(CAMERA_OPTION_INDEX_CAMERA_MOTION) == CAR_FOLLOWED_CAMERA)
	{
		auto actor = dynamic_cast<IClientActor*>(GetIPlayerControl());
		IActorBody* pActorBody = actor->GetIBody();
		if (pActorBody == NULL) return pos.toVector3();

		Rainbow::Quaternionf quat;

		int eyeHeight = actor->getEyeHeight();

		Rainbow::Vector3f offset, eyepos = pActorBody->getBindPointPos(0) + Rainbow::Vector3f(0, eyeHeight + 10.0f, 0);

		if (GetIPlayerControl()->getOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_ONLY_BODY
			|| GetIPlayerControl()->getOption(CAMERA_OPTION_INDEX_ROTATING) == CRT_NOTHING)
		{
			quat = getRotation(0, GetIPlayerControl()->getInitPlayerYaw() + 180.0f, 0.0f);
			offset = RotateVectorByQuat(quat, pos.toVector3());
		}
		else
		{
			quat = getRotation(0, yaw, 0.0f);
			if (m_RotSelfCenter)
				quat = getRotation(0, m_CameraRotationAngle.x, 0.0f);;
			offset = RotateVectorByQuat(quat, pos.toVector3());
		}

		bool cameraZoom = true;
		if (m_Player && m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_AUTO_ZOOM) != CAZ_AUTO_ZOOM_YES && !m_bInitAutoZoom)
		{
			cameraZoom = false;
		}
		if (m_EnableAutoZoom && cameraZoom)
		{
			// 视线阻挡判断
			float dist = offset.Length();
			offset.NormalizeSafe();
			pos = WCoord(CalCollidedEyePos(pWorld, Rainbow::WorldPos::fromVector3(eyepos), offset, dist));
		}
		else
		{
			pos = WCoord(WorldPos::fromVector3(eyepos + offset + cameraOffset));
		}
	}

	return pos.toVector3();
}

Rainbow::Quaternionf UGCGameCamera::getRotation(float pitch, float yaw, float roll)
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return AngleEulerToQuaternionf(Vector3f(pitch, yaw, roll));

	float pitchRot = pitch;
	float yawRot = yaw;
	if (m_LockPitchRot) 
	{
		pitchRot = m_LockCameraPitch;
	}
	if (m_LockYawRot)
	{
		yawRot = m_LockCameraYaw;
	}
	roll = m_RotateRoll;
	if (m_RotSelfCenter)
		roll = m_CameraRotationAngle.z;

	return AngleEulerToQuaternionf(Vector3f(pitchRot, yawRot, roll));
}

float UGCGameCamera::getRotatePitch()
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return m_RotatePitch;

	if (m_LockPitchRot)
	{
		return m_LockCameraPitch;
	}
	return m_RotatePitch;
}

float UGCGameCamera::getRotateYaw()
{
	if (!GetIWorldConfigProxy()->IsUGCGame())
		return m_RotateYaw;

	if (m_LockYawRot)
	{
		return m_LockCameraYaw;
	}
	return m_RotateYaw;
}

void UGCGameCamera::rotate(float yaw, float pitch)
{
	auto player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
	if ((GetIPlayerControl() && player->isInSpectatorMode() && player->getSpectatorType() == SPECTATOR_TYPE_FOLLW) ||
		(GetIPlayerControl() && player->getSpectatorType() == SPECTATOR_TYPE_OUTCONTROL))
		return;

	if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->GetTriggerOperateAttr(player->getUin(), 2)) //是否禁止旋转摄像头 
	{
		return;
	}
	if (m_Mode == CAMERA_TPS_OVERLOOK)
	{
		Rainbow::Vector3f dir(-yaw, 0, pitch);
		dir = MINIW::Normalize(dir);

		Direction2PitchYaw(&m_RotateYaw, NULL, dir);
	}
	else
	{
		if (m_Mode == CAMERA_CUSTOM_VIEW
			&& CameraManager::GetInstance().getCameraEditState() != CAMERA_EDIT_STATE_EDIT)
		{
			switch (m_Player->getCameraConfigOption(CAMERA_OPTION_INDEX_ROTATING_LIMIT))
			{
			case CRL_ONLY_X:
				pitch = 0.0f;
				break;
			case CRL_ONLY_Y:
				yaw = 0.0f;
				break;
			}
		}
		m_RotateYaw += yaw * 180.0f;
	}

	bool isFishingOpAndFPSMode = m_Mode == CameraControlMode::CAMERA_FPS && player->getOPWay() == PLAYEROP_WAY_FISHING;

	if (m_RotateYaw > 360.0f)
	{
		m_RotateYaw -= 360.0f;
		if (m_CameraModel != NULL && !isFishingOpAndFPSMode && !m_LockYawRot) {
			m_CameraModel->m_ArmYaw -= 360.0f;
			m_CameraModel->m_RenderYaw -= 360.0f;
			m_CameraModel->m_PreYaw -= 360.0f;
		}
	}
	if (m_RotateYaw < 0)
	{
		m_RotateYaw += 360.0f;
		if (m_CameraModel != NULL && !isFishingOpAndFPSMode && !m_LockYawRot) {
			m_CameraModel->m_ArmYaw += 360.0f;
			m_CameraModel->m_RenderYaw += 360.0f;
			m_CameraModel->m_PreYaw += 360.0f;
		}
	}

	float anglex = 88.0f;
	if (GetIPlayerControl() && dynamic_cast<IClientActor*>(GetIPlayerControl())->getUsingEmitter())		// 手动发射器上控制上下摆动幅度
	{
		anglex = 15.0f;
	}
	m_RotatePitch += pitch * 90.0f;
	if (m_RotatePitch < -anglex) m_RotatePitch = -anglex;
	if (m_RotatePitch > anglex) m_RotatePitch = anglex;
}

void UGCGameCamera::doCameraAnimation(float dtime)
{
	if (!m_PlayCameraAnim)
		return;
	
	bool xOffsetComplete = m_MoveAnimInfo[0].duration > m_MoveAnimInfo[0].time;
	bool yOffsetComplete = m_MoveAnimInfo[1].duration > m_MoveAnimInfo[1].time;
	bool zOffsetComplete = m_MoveAnimInfo[2].duration > m_MoveAnimInfo[2].time;

	bool yawOffsetComplete = m_RotAnimInfo[0].duration > m_RotAnimInfo[0].time;
	bool pitchOffsetComplete = m_RotAnimInfo[1].duration > m_RotAnimInfo[1].time;
	bool rollOffsetComplete = m_RotAnimInfo[2].duration > m_RotAnimInfo[2].time;

	bool absXOffsetComplete = m_AbsMoveAnimInfo[0].duration > m_AbsMoveAnimInfo[0].time;
	bool absYOffsetComplete = m_AbsMoveAnimInfo[1].duration > m_AbsMoveAnimInfo[1].time;
	bool absZOffsetComplete = m_AbsMoveAnimInfo[2].duration > m_AbsMoveAnimInfo[2].time;

	bool focusAnimComplete = m_TargetRotateAnimInfo.duration >= m_TargetRotateAnimInfo.time;

	if (xOffsetComplete && yOffsetComplete && zOffsetComplete && 
		yawOffsetComplete && pitchOffsetComplete && rollOffsetComplete &&
		absXOffsetComplete && absYOffsetComplete && absZOffsetComplete &&
		m_FovAnimInfo.duration > m_FovAnimInfo.time)
	{
		if (m_AdjustCamera != AdjustStatus::over)
		{
			if (!focusAnimComplete)
			{
				float nTime = 0.0f;
				if (m_TargetRotateAnimInfo.duration < m_TargetRotateAnimInfo.time)
				{
					float moveDur = m_TargetRotateAnimInfo.duration;
					float moveTime = m_TargetRotateAnimInfo.time;
					nTime = EaseManager::evaluate(m_TargetRotateAnimInfo.easetype, moveDur, moveTime, 1.70158f, 0);
					Rainbow::Vector3f angle;
					float curOffsetX = 0;
					angle.x = m_TargetRotateAnimInfo.oldDir.x + (m_TargetRotateAnimInfo.lookDir.x - m_TargetRotateAnimInfo.oldDir.x) * nTime;

					angle.y = m_TargetRotateAnimInfo.oldDir.y + (m_TargetRotateAnimInfo.lookDir.y - m_TargetRotateAnimInfo.oldDir.y) * nTime;

					angle.z = 0;


					auto quat = AngleEulerToQuaternionf(Vector3f(angle.y, angle.x, angle.z));
					Rainbow::Vector3f dir;
					dir = RotateVectorByQuat(quat, Rainbow::Vector3f(0, 0, 1.0f));
					dir.NormalizeSafe();

					Rainbow::Vector3f pos = m_TargetRotateAnimInfo.target - dir * m_TargetRotateAnimInfo.offset;

					if (m_Player)
					{
						m_Player->setCameraRotate(angle.x, angle.y, angle.z);
					}

					m_CameraAniPos = pos;
				}
				m_TargetRotateAnimInfo.duration += dtime * 10000;
			}
			else
			{
				if (m_Player)
					dynamic_cast<IClientActor*>(m_Player)->setCanControl(true);

				m_PlayCameraAnim = false;
				m_AdjustCamera = AdjustStatus::over;
			}
		}
		else
		{
			m_PlayCameraAnim = false;
		}
		//if (m_Player && m_AdjustCamera != AdjustStatus::over)
		//	m_Player->setCanControl(true);

		//m_AdjustCamera = AdjustStatus::over;
	}

	//可能为
	Vector3f oldCameraOffset(m_MoveAnimInfo[0].oldValue, m_MoveAnimInfo[1].oldValue, m_MoveAnimInfo[2].oldValue);

	Quaternionf quat = getRotation(m_RotatePitch, m_RotateYaw, m_RotateRoll);

	float normalizedTime = 0.0f;
	if (m_MoveAnimInfo[0].duration < m_MoveAnimInfo[0].time)
	{
		float moveDur = m_MoveAnimInfo[0].duration;
		float moveTime = m_MoveAnimInfo[0].time;
		normalizedTime = EaseManager::evaluate(m_MoveAnimInfo[0].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetX = 0;
		if (abs(m_MoveAnimInfo[0].offset) > 0.1f)
			curOffsetX = m_MoveAnimInfo[0].offset * normalizedTime;

		if (m_RotSelfCenter || m_StandFixedPoint)
		{
			//把相应的轴旋转指定角度
			Vector3f xAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::xAxis);
			Vector3f relationXOffset = xAxisDir * curOffsetX;
			m_CameraOffset = WorldPos::fromVector3(oldCameraOffset + relationXOffset);
		}
		else
		{
			m_CameraOffset.x = WorldPos::Flt2Fix(m_MoveAnimInfo[0].oldValue + curOffsetX);
		}
	}

	if (m_MoveAnimInfo[1].duration < m_MoveAnimInfo[1].time)
	{
		float moveDur = m_MoveAnimInfo[1].duration;
		float moveTime = m_MoveAnimInfo[1].time;
		normalizedTime = EaseManager::evaluate(m_MoveAnimInfo[1].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetY = 0;
		if (abs(m_MoveAnimInfo[1].offset) > 0.1f)
			curOffsetY = m_MoveAnimInfo[1].offset * normalizedTime;

		if (m_RotSelfCenter || m_StandFixedPoint)
		{
			Vector3f yAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::yAxis);
			Vector3f relationYOffset = yAxisDir * curOffsetY;
			m_CameraOffset = WorldPos::fromVector3(oldCameraOffset + relationYOffset);
		}
		else
		{
			m_CameraOffset.y = WorldPos::Flt2Fix(m_MoveAnimInfo[1].oldValue + curOffsetY);
		}
	}

	if (m_MoveAnimInfo[2].duration < m_MoveAnimInfo[2].time)
	{
		float moveDur = m_MoveAnimInfo[2].duration;
		float moveTime = m_MoveAnimInfo[2].time;
		normalizedTime = EaseManager::evaluate(m_MoveAnimInfo[2].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetZ = 0;
		if (abs(m_MoveAnimInfo[2].offset) > 0.1f)
			curOffsetZ = m_MoveAnimInfo[2].offset * normalizedTime;

		if (m_RotSelfCenter || m_StandFixedPoint)
		{
			Vector3f zAxisDir = RotateVectorByQuat(quat, Rainbow::Vector3f::zAxis);
			Vector3f relationZOffset = zAxisDir * curOffsetZ;
			m_CameraOffset = WorldPos::fromVector3(oldCameraOffset + relationZOffset);
		}
		else
		{
			m_CameraOffset.z = WorldPos::Flt2Fix(m_MoveAnimInfo[2].oldValue + curOffsetZ);
		}
	}

	if (m_RotAnimInfo[0].duration < m_RotAnimInfo[0].time)
	{
		float moveDur = m_RotAnimInfo[0].duration;
		float moveTime = m_RotAnimInfo[0].time;
		normalizedTime = EaseManager::evaluate(m_RotAnimInfo[0].easetype, moveDur, moveTime, 1.70158f, 0);
		float curYawOffset = 0.0f;
		if (abs(m_RotAnimInfo[0].offset) > 0.1f)
		{
			curYawOffset = m_RotAnimInfo[0].offset * normalizedTime;
			int intSection = curYawOffset;
			//对整数部分取余
			curYawOffset = (intSection % 360) + (curYawOffset - intSection);
		}
		float targetYaw = m_RotAnimInfo[0].oldValue + curYawOffset; 
		m_RotateYaw = targetYaw;
		
		int curRemainderNum = ((int)targetYaw) / 360;
		bool isOperateCameraModel = false;
		if (curRemainderNum != m_LastYawRemainderNum)
		{
			isOperateCameraModel = true;
			m_LastYawRemainderNum = curRemainderNum;
		}

		auto pPlayer = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
		if (m_RotateYaw > 360.0f)
		{
			m_RotateYaw -= 360.0f;
			if (isOperateCameraModel && pPlayer)
			{
				bool isFishingOpAndFPSMode = m_Mode == CameraControlMode::CAMERA_FPS && pPlayer->getOPWay() == PLAYEROP_WAY_FISHING;
				if (m_CameraModel != NULL && !isFishingOpAndFPSMode && !m_LockYawRot) 
				{
					m_CameraModel->m_ArmYaw -= 360.0f;
					m_CameraModel->m_RenderYaw -= 360.0f;
					m_CameraModel->m_PreYaw -= 360.0f;
				}
			}
		}
		if (m_RotateYaw < 0)
		{
			m_RotateYaw += 360.0f;
			if (isOperateCameraModel && pPlayer)
			{
				bool isFishingOpAndFPSMode = m_Mode == CameraControlMode::CAMERA_FPS && pPlayer->getOPWay() == PLAYEROP_WAY_FISHING;
				if (m_CameraModel != NULL && !isFishingOpAndFPSMode && !m_LockYawRot)
				{
					m_CameraModel->m_ArmYaw += 360.0f;
					m_CameraModel->m_RenderYaw += 360.0f;
					m_CameraModel->m_PreYaw += 360.0f;
				}
			}
		}
	}
	if (m_RotAnimInfo[1].duration < m_RotAnimInfo[1].time)
	{
		float moveDur = m_RotAnimInfo[1].duration;
		float moveTime = m_RotAnimInfo[1].time;
		normalizedTime = EaseManager::evaluate(m_RotAnimInfo[1].easetype, moveDur, moveTime, 1.70158f, 0);
		float curPitchOffset = 0.0f;
		if (abs(m_RotAnimInfo[1].offset) > 0.1f)
			curPitchOffset = m_RotAnimInfo[1].offset * normalizedTime;
		float targetPitch = m_RotAnimInfo[1].oldValue + curPitchOffset;
		m_RotatePitch = targetPitch;
		float anglex = 88.0f;
		if (m_RotatePitch < -anglex) m_RotatePitch = -anglex;
		if (m_RotatePitch > anglex) m_RotatePitch = anglex;
	}
	if (m_RotAnimInfo[2].duration < m_RotAnimInfo[2].time)
	{
		float moveDur = m_RotAnimInfo[2].duration;
		float moveTime = m_RotAnimInfo[2].time;
		normalizedTime = EaseManager::evaluate(m_RotAnimInfo[2].easetype, moveDur, moveTime, 1.70158f, 0);
		float curRollOffset = 0.0f;
		if (abs(m_RotAnimInfo[2].offset) > 0.1f)
			curRollOffset = m_RotAnimInfo[2].offset * normalizedTime;
		float targetRoll = m_RotAnimInfo[2].oldValue + curRollOffset;
		m_RotateRoll = targetRoll;
		if (m_RotateRoll > 360.0f) m_RotateRoll -= 360.0f;
		if (m_RotateRoll < 0) m_RotateRoll += 360.0f;
	}

	if (m_FovAnimInfo.duration < m_FovAnimInfo.time)
	{
		float moveDur = m_FovAnimInfo.duration;
		float moveTime = m_FovAnimInfo.time;
		normalizedTime = EaseManager::evaluate(m_FovAnimInfo.easetype, moveDur, moveTime, 1.70158f, 0);
		float curFovOffset = 0.0f;
		//0.1到-0.1间不处理
		if (abs(m_FovAnimInfo.offset) > 0.1f)
			curFovOffset = m_FovAnimInfo.offset * normalizedTime;
		float curFov = m_FovAnimInfo.oldValue + curFovOffset;
		if (curFov < 1.0f)
			curFov = 1.0f;
		if (curFov > 180.0f)
			curFov = 180.0f;
		setFov(curFov);
	}

	if (m_AbsMoveAnimInfo[0].duration < m_AbsMoveAnimInfo[0].time)
	{
		float moveDur = m_AbsMoveAnimInfo[0].duration;
		float moveTime = m_AbsMoveAnimInfo[0].time;
		normalizedTime = EaseManager::evaluate(m_AbsMoveAnimInfo[0].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetX = 0;
		if (abs(m_AbsMoveAnimInfo[0].offset) > 0.1f)
			curOffsetX = (m_AbsMoveAnimInfo[0].offset - m_AbsMoveAnimInfo[0].oldValue) * normalizedTime + m_AbsMoveAnimInfo[0].oldValue;

		m_CameraAniPos.x = curOffsetX;
	}

	if (m_AbsMoveAnimInfo[1].duration < m_AbsMoveAnimInfo[0].time)
	{
		float moveDur = m_AbsMoveAnimInfo[1].duration;
		float moveTime = m_AbsMoveAnimInfo[1].time;
		normalizedTime = EaseManager::evaluate(m_AbsMoveAnimInfo[1].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetY = 0;
		if (abs(m_AbsMoveAnimInfo[1].offset) > 0.1f)
			curOffsetY = (m_AbsMoveAnimInfo[1].offset - m_AbsMoveAnimInfo[1].oldValue) * normalizedTime + m_AbsMoveAnimInfo[1].oldValue;

		m_CameraAniPos.y = curOffsetY;
	}

	if (m_AbsMoveAnimInfo[2].duration < m_AbsMoveAnimInfo[0].time)
	{
		float moveDur = m_AbsMoveAnimInfo[2].duration;
		float moveTime = m_AbsMoveAnimInfo[2].time;
		normalizedTime = EaseManager::evaluate(m_AbsMoveAnimInfo[2].easetype, moveDur, moveTime, 1.70158f, 0);
		float curOffsetZ = 0;
		if (abs(m_AbsMoveAnimInfo[2].offset) > 0.1f)
			curOffsetZ = (m_AbsMoveAnimInfo[2].offset - m_AbsMoveAnimInfo[2].oldValue) * normalizedTime + m_AbsMoveAnimInfo[2].oldValue;

		m_CameraAniPos.z = curOffsetZ;
	}
	if (m_AdjustCamera != AdjustStatus::over && m_Player)
	{
		dynamic_cast<IClientActor*>(m_Player)->setPosition(m_CameraAniPos);
	}
	int deltaPs = dtime * 10000;
	m_MoveAnimInfo[0].duration += deltaPs;
	m_RotAnimInfo[0].duration += deltaPs;
	m_AbsMoveAnimInfo[0].duration += deltaPs;
	m_MoveAnimInfo[1].duration += deltaPs;
	m_RotAnimInfo[1].duration += deltaPs;
	m_AbsMoveAnimInfo[1].duration += deltaPs;
	m_MoveAnimInfo[2].duration += deltaPs;
	m_RotAnimInfo[2].duration += deltaPs;
	m_AbsMoveAnimInfo[2].duration += deltaPs;
	m_FovAnimInfo.duration += deltaPs;
}