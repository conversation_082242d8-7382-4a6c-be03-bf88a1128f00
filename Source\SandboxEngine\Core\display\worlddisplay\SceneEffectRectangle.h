#pragma once
/**
* file : SceneEffectRectangle
* func : 场景效果 （矩形）
* by : pengdapu
*/
#include "SceneEffectLine.h"
#include "SceneEffectFrame.h"
#include "SceneEffectSphere.h"
#include "SceneEffectFan.h"
#include "SandboxRay.h"

class SceneEffectSphere;

class SceneEffectRectangle : public SceneEffectGeom
{
public:
	SceneEffectRectangle();
	virtual ~SceneEffectRectangle();

	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;

public:
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
private:
	SceneEffectLine* m_aLines[4] = {nullptr, nullptr, nullptr, nullptr, };
};
