#include "CompositeGeoSolid.h"

#include "geometrySolid/GeometryMath.h"
#include "nodes/SandboxGeoSolid.h"

#include "GeoSolid_generated.h"
#include "ClientActorHelper.h"

#include "Components/Transform.h"
#include "Geometry/Plane.h"
#include "Geometry/Intersection.h"
#include "File/FileManager.h"
#include "Common/OgreUtils.h"
#include "Math/DeprecatedConversion.h"

#if COMPOSITE_GEO_SOLID_DEBUG
#include <iomanip>
#endif
#include <stack>

#define COMPOSITE_MERGE_INTO_CONVEX 0

#define COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES 0

namespace MNSandbox { namespace GeometrySolid {
	using namespace Rainbow;
	using namespace GeometryMath;

	#if COMPOSITE_GEO_SOLID_DEBUG
	const static int precisionV = 4;
	const static int precisionVn = 2;
	const static int precisionUv = 2;
	const static int precisionOfArea = 1;

	const static int wVn = precisionVn + 3;
	const static int wUv = precisionUv + 1 + 1;
	const static int wArea = precisionOfArea + 1 + 5;
	#endif

    CompositeGeoSolid::CompositeGeoSolid()
		: m_cSrcTriangle(0)
		, m_cCutTriangle(0)
		, m_strCacheKey("")
		, m_bIsSrcBasic(false)
		, m_bIsCutBasic(false)
		#if COMPOSITE_GEO_SOLID_DEBUG
		, m_bDebugDetail(false)
		, m_bDebugSummary(false)
		, m_bDebugRender(false)
		#endif
    {
		m_eGss = GeoSolidShape::COMPOSITE;
    }

    CompositeGeoSolid::~CompositeGeoSolid()
	{
		OPTICK_EVENT();
		ReleaseInGameRuntime();
		m_daBoxBounds.clear();
		m_daMatrices.clear();
		m_daNormals.clear();
		ClearSubMeshes();
		for (auto it = m_daMeshes.begin(); it != m_daMeshes.end(); ) { it = m_daMeshes.erase(it); }
		m_mesh = nullptr;
	}
	
	bool CompositeGeoSolid::Load(AutoRefPtr<DataStream> ds)
	{
		OPTICK_EVENT();
		if (!ds)
		{
			return false;
		}
		size_t buflen = ds->Size();
		#if COMPOSITE_GEO_SOLID_DEBUG
		SANDBOX_ASSERT(buflen);
		#endif
		if (buflen <= 0)
		{
			return false;
		}
		if (ds)
		{
			ds->Seek(0);
		}
		void* buf = malloc(buflen);
		ds->Read(buf, buflen);
		flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
		if (!FBSave::VerifyGeoSolidBuffer(verifier))
		{
			free(buf);
			return false;
		}
		const FBSave::GeoSolid* fbsGs = FBSave::GetGeoSolid(buf);
		if (fbsGs == NULL)
		{
			free(buf);
			return false;
		}
		FromFbs(fbsGs);
		free(buf);
		//ds->Close();
		return true;
	}

	bool CompositeGeoSolid::Load(const std::string& strPath)
	{
		AutoRefPtr<DataStream> ds = GetFileManager().OpenFile(strPath.c_str(), true);
		return Load(ds);
	}

	flatbuffers::Offset<FBSave::GeoSolid> CompositeGeoSolid::ToFbs(flatbuffers::FlatBufferBuilder& builder) const
	{
		//TODO: 2024-01-31 17:35:01: 修改保存文件中小数类型到double？
		std::vector<flatbuffers::Offset<FBSave::GeoSolidTriangle>> vFbsGsts;
		{
			GsIt ct = GetTriangleCount();
			for (GsIt it = 0; it < ct; ++it)
			{
				const GeoSolidTriangle& gst = GetTriangle(it);
				auto fbsgst = gst.ToFbs(builder);
				vFbsGsts.emplace_back(fbsgst);
			}
		}

		std::vector<flatbuffers::Offset<FBSave::GeoSolidSubMesh>> vFbsGssms;
		{
			UInt32 csm = m_daSubMeshes.size();
			for (UInt32 ism = 0; ism < csm; ++ism)
			{
				const GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
				auto fbsgssm = gssm.ToFbs(builder);
				vFbsGssms.emplace_back(fbsgssm);
			}
		}

		std::vector<FBSave::Mat3x3f> vFbsMatrices;
		for (const GsMatrix3x3& mat : m_daMatrices)
		{
			vFbsMatrices.emplace_back(Matrix3x3ToFbsMat3x3f(mat));
		}

		std::vector<flatbuffers::Offset<FBSave::BoxBound>> vFbsBbs;
		for (const GsBoxBound& bb : m_daBoxBounds)
		{
			auto fbsv3Min = Vector3ToVec3(bb.getMinPos());
			auto fbsv3Max = Vector3ToVec3(bb.getMaxPos());
			auto fbsbb = FBSave::CreateBoxBound(builder, &fbsv3Min, &fbsv3Max);
			vFbsBbs.emplace_back(fbsbb);
		}

		std::vector<FBSave::Vec3> vFbsCenters;
		for (const GsVector3& vc : m_daCenters)
		{
			vFbsCenters.emplace_back(Vector3ToVec3(vc));
		}

		std::vector<FBSave::Vec3> vFbsVertices;
		{
			GsIv cv = GetVertexCount();
			for (GsIv iv = 0; iv < cv; ++iv)
			{
				const GeoSolidVertex& gsv = GetVertex(iv);
				vFbsVertices.emplace_back(Vector3ToVec3(gsv.v));
			}
		}

		std::vector<FBSave::Vec3> vFbsNormals;
		for (const GsVector3& vn : m_daNormals)
		{
			vFbsNormals.emplace_back(Vector3ToVec3(vn));
		}

		std::vector<FBSave::Vec2> vFbsUvs;
		for (const GsVector2& uv : m_daUvs)
		{
			vFbsUvs.emplace_back(Vector2ToVec2(uv));
		}

		std::vector<uint32_t> vFbsColors;
		for (const ColorRGBA32& rgba : m_daColors)
		{
			vFbsColors.emplace_back(*(UInt32*)&rgba);
		}

		return FBSave::CreateGeoSolid(builder,
			builder.CreateVector(vFbsGsts),
			builder.CreateVector(vFbsGssms),
			builder.CreateVectorOfStructs(vFbsMatrices),
			builder.CreateVector(vFbsBbs),
			builder.CreateVectorOfStructs(vFbsCenters),
			builder.CreateVectorOfStructs(vFbsVertices),
			builder.CreateVectorOfStructs(vFbsNormals),
			builder.CreateVectorOfStructs(vFbsUvs),
			builder.CreateVector(vFbsColors)
		);
	}

	void CompositeGeoSolid::FromFbs(const FBSave::GeoSolid* fbsGs)
	{
		if (!fbsGs)
		{
			return;
		}

		{
			OPTICK_EVENT("ct");
			auto& fbsvTriangles = *fbsGs->triangles();
			OPTICK_TAG("CompositeGeoSolid::FromFbs() ct = ", fbsvTriangles.size());
			#if COMPOSITE_ELEMENT_USE_POINTER
			m_daTriangles.reserve(fbsvTriangles.size());
			for (auto iter = fbsvTriangles.begin(); iter != fbsvTriangles.end(); ++iter)
			{
				GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(*iter);
				m_daTriangles.emplace_back(gst);
			}
			#else
			GeoSolidTriangle gstDefault(0, 0, 0);
			m_daTriangles.clear();
			m_daTriangles.resize(fbsvTriangles.size(), gstDefault);
			GsIt it = 0;
			for (auto iter = fbsvTriangles.begin(); iter != fbsvTriangles.end(); ++iter)
			{
				GeoSolidTriangle& gst = GetTriangle(it++);
				gst.FromFbs(*iter);
			}
			#endif
		}

		{
			OPTICK_EVENT("csm");
			auto& fbsvSubMeshes = *fbsGs->submeshes();
			OPTICK_TAG("CompositeGeoSolid::FromFbs(): csm = ", fbsvSubMeshes.size());
			m_daSubMeshes.reserve(fbsvSubMeshes.size());
			//GeoSolidSubMesh gssmDefault(false);
			//m_daSubMeshes.clear();
			//m_daSubMeshes.resize(fbsvSubMeshes.size(), gssmDefault);
			//UInt32 ism = 0;
			for (auto iter = fbsvSubMeshes.begin(); iter != fbsvSubMeshes.end(); ++iter)
			{
				GeoSolidSubMesh* pgssm = GeoSolidSubMesh::obtain();
				m_daSubMeshes.emplace_back(pgssm);
				GeoSolidSubMesh& gssm = *pgssm;
				//GeoSolidSubMesh& gssm = GetSubMesh(ism++);
				gssm.FromFbs(*iter);
				for (const GsIt& it : gssm.setTriangleIndices)
				{
					GeoSolidTriangle& gst = GetTriangle(it);
					gst.ibb = gssm.ibb;
					gst.im = gssm.im;
					gst.ivc = gssm.ivc;
					gst.gss = gssm.eGss;
					gst.gsf = gssm.eGsf;
					if (gssm.ic >= 0)
					{
						gst.ic = gssm.ic;
					}
				}
			}
		}

		{
			OPTICK_EVENT("cv");
			auto& fbsvVertices = *fbsGs->vertices();
			OPTICK_TAG("CompositeGeoSolid::FromFbs(): cv = ", fbsvVertices.size());
			#if COMPOSITE_ELEMENT_USE_POINTER
			m_daGeoSolidVertices.reserve(fbsvVertices.size());
			for (auto iter = fbsvVertices.begin(); iter != fbsvVertices.end(); ++iter)
			{
				//GeoSolidVertex* gsv = SANDBOX_NEW(GeoSolidVertex);
				GeoSolidVertex* gsv = GeoSolidVertex::obtain();
				gsv->v = Vec3ToVector3(*iter);
				m_daGeoSolidVertices.emplace_back(gsv);
			}
			#else
			m_daGeoSolidVertices.resize(fbsvVertices.size());
			GsIv iv = 0;
			for (auto iter = fbsvVertices.begin(); iter != fbsvVertices.end(); ++iter)
			{
				GeoSolidVertex& gsv = GetVertex(iv++);
				gsv.v = Vec3ToVector3(*iter);
			}
			#endif
		}

		{
			OPTICK_EVENT("CompositeGeoSolid::FromFbs(): else");
			auto& fbsvMatrices = *fbsGs->matrices();
			m_daMatrices.reserve(fbsvMatrices.size());
			for (auto iter = fbsvMatrices.begin(); iter != fbsvMatrices.end(); ++iter)
			{
				const GsMatrix3x3 mat = FbsMat3x3ToMatrix3x3(*iter);
				m_daMatrices.emplace_back(mat);
			}

			auto& fbsvBoxBounds = *fbsGs->boxbounds();
			m_daBoxBounds.reserve(fbsvBoxBounds.size());
			for (auto iter = fbsvBoxBounds.begin(); iter != fbsvBoxBounds.end(); ++iter)
			{
				GsBoxBound bb;
				bb.m_MinPos = Vec3ToVector3(iter->min());
				bb.m_MaxPos = Vec3ToVector3(iter->max());
				m_daBoxBounds.emplace_back(bb);
			}

			auto& fbsvCenters = *fbsGs->centers();
			m_daCenters.reserve(fbsvCenters.size());
			for (auto iter = fbsvCenters.begin(); iter != fbsvCenters.end(); ++iter)
			{
				const GsVector3 vc = Vec3ToVector3(*iter);
				m_daCenters.emplace_back(vc);
			}

			auto& fbsvNormals = *fbsGs->normals();
			m_daNormals.reserve(fbsvNormals.size());
			for (auto iter = fbsvNormals.begin(); iter != fbsvNormals.end(); ++iter)
			{
				const GsVector3 vn = Vec3ToVector3(*iter);
				m_daNormals.emplace_back(vn);
			}

			auto& fbsvUvs = *fbsGs->uvs();
			m_daUvs.reserve(fbsvUvs.size());
			for (auto iter = fbsvUvs.begin(); iter != fbsvUvs.end(); ++iter)
			{
				const GsVector2 uv = Vec2ToVector2(*iter);
				m_daUvs.emplace_back(uv);
			}

			if (auto fbsvColors = fbsGs->colors())
			{
				m_daColors.reserve(fbsvColors->size());
				for (auto iter = fbsvColors->begin(); iter != fbsvColors->end(); ++iter)
				{
					const ColorRGBA32 rgba(*iter);
					m_daColors.emplace_back(rgba);
				}
			}
			if (m_daColors.empty())
			{
				m_daColors.emplace_back(0xffffffff);
			}
		}
	}

	SharePtr<Mesh> CompositeGeoSolid::GetMesh()
	{
		if (m_mesh)
		{
			return m_mesh;
		}
		m_mesh = CreateMeshWithVertexNormal();
		m_daPrimitiveVertices.clear();
		m_daPrimitiveIndices.clear();
		return m_mesh;
	}

	void CompositeGeoSolid::ReleaseInGameRuntime()
	{
		OPTICK_EVENT();
		//保留跟Tile有关需要计算的数据
		m_daPrimitiveVertices.clear();
		m_daPrimitiveIndices.clear();
		m_daUvs.clear();
		m_daCenters.clear();
		m_daColors.clear();
		#if COMPOSITE_ELEMENT_USE_POINTER
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle* gst = m_daTriangles[it];
			GeoSolidTriangle::recycle(gst);
			gst = m_daTriangles[it] = nullptr;
		}
		const GsIe ce = GetEdgeCount();
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge* gse = m_daEdges[ie];
			GeoSolidEdge::recycle(gse);
			gse = m_daEdges[ie] = nullptr;
		}
		const GsIv cv = GetVertexCount();
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			GeoSolidVertex* gsv = m_daGeoSolidVertices[iv];
			GeoSolidVertex::recycle(gsv);
			gsv = m_daGeoSolidVertices[iv] = nullptr;
		}
		m_daTriangles.clear();
		m_daEdges.clear();
		m_daGeoSolidVertices.clear();
		#else
		m_daTriangles.clear();
		m_daEdges.clear();
		m_daGeoSolidVertices.clear();
		#endif
	}

	SharePtr<Mesh> CompositeGeoSolid::CreateMeshWithVertexNormal()
	{
		if (m_daGeoSolidVertices.empty())
		{
			return nullptr;
		}
		if (m_daTriangles.empty())
		{
			return nullptr;
		}
		const UInt32 csm = m_daSubMeshes.size();
		if (csm <= 0)
		{
			return nullptr;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		CorrectOldNormals();
		if (m_bDebugRender)
		{
			if (m_daEdges.empty())
			{
				RegenerateEdges();
			}
		}
		if (m_bDebugDetail)
		{
			for (UInt32 ism = 0; ism < csm; ++ism)
			{
				PrintSubMesh(ism, true);
				WarningString("=======================");
			}
			GeoGebraExecuteWhole();
		}
		#endif
		GeoSolidArray<PrimitiveVertex>& daPvs = m_daPrimitiveVertices;
		GeoSolidArray<UInt32>& daPis = m_daPrimitiveIndices;
		daPvs.clear();
		daPis.clear();
		struct GeoSolidRenderTriangle {
			GsVector3 v0Local;
			GsVector3 v1Local;
			GsVector3 v2Local;
		};
		dynamic_array<GeoSolidRenderTriangle> daRenderTriangles;
		daRenderTriangles.resize_uninitialized(m_daTriangles.size());
		const GsIv cv = GetVertexCount();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			if (gssm.setTriangleIndices.empty())
			{
				continue;
			}
			if (gssm.eGss == GeoSolidShape::NONE)
			{
				continue;
			}
			if (gssm.eGsf == GeoSolidFace::UNKNOWN)
			{
				continue;
			}
			gssm.cpv = 0;
			gssm.cpi = 0;
			const UInt32 oopv = daPvs.size();
			const UInt32 oopi = daPis.size();
			UInt32 oiv = 0;
			GsVector3 vtc;
			GsVector3 vbc;
			const GsBoxBound& bb = m_daBoxBounds[gssm.ibb];
			const GsMatrix3x3& mat = m_daMatrices[gssm.im];
			const GsIt& itFirst = *gssm.setTriangleIndices.begin();
			const GeoSolidTriangle& gstFirst = GetTriangle(itFirst);
			GsMatrix3x3 matInverse = mat;
			matInverse.Invert();
			for (const GsIt& it : gssm.setTriangleIndices)
			{
				GeoSolidTriangle& gst = GetTriangle(it);
				const UInt32 oPv = daPvs.size();
				if (gst.iv0 >= cv || gst.iv1 >= cv || gst.iv2 >= cv)
				{
					//某种遗留错误
					SANDBOX_ASSERT(false);
					WarningString("cgs: invalid iv in triangle");
					return nullptr;
				}
				const GeoSolidVertex& gsv0 = GetVertex(gst.iv0);
				const GeoSolidVertex& gsv1 = GetVertex(gst.iv1);
				const GeoSolidVertex& gsv2 = GetVertex(gst.iv2);
				const GsVector3& vc = m_daCenters[gst.ivc];
				GeoSolidRenderTriangle& gsrt = daRenderTriangles[(GsIndex)it];
				GsVector3 v0InWhole = gsv0.v;
				GsVector3 v1InWhole = gsv1.v;
				GsVector3 v2InWhole = gsv2.v;
				gsrt.v0Local = matInverse.MultiplyVector3(v0InWhole);
				gsrt.v1Local = matInverse.MultiplyVector3(v1InWhole);
				gsrt.v2Local = matInverse.MultiplyVector3(v2InWhole);
			}
		}
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			if (gssm.setTriangleIndices.empty())
			{
				continue;
			}
			gssm.cpv = 0;
			gssm.cpi = 0;
			const UInt32 oopv = daPvs.size();
			const UInt32 oopi = daPis.size();
			UInt32 oiv = 0;
			GsVector3 vtc;
			GsVector3 vbc;
			const GsBoxBound& bb = m_daBoxBounds[gssm.ibb];
			const GsMatrix3x3& mat = m_daMatrices[gssm.im];
			const GsVector3& vc = m_daCenters[gssm.ivc];
			if ((gssm.eGss == GeoSolidShape::CONE || gssm.eGss == GeoSolidShape::CYLINDER) && (gssm.eGsf == GeoSolidFace::SIDE))
			{
				vtc = vc;
				vtc.y += bb.getExtension().y;
				vbc = vc;
				vbc.y -= bb.getExtension().y;
			}
			for (const GsIt& it : gssm.setTriangleIndices)
			{
				GeoSolidTriangle& gst = GetTriangle(it);
				const UInt32 oPv = daPvs.size();
				const GeoSolidVertex& gsv0 = GetVertex(gst.iv0);
				const GeoSolidVertex& gsv1 = GetVertex(gst.iv1);
				const GeoSolidVertex& gsv2 = GetVertex(gst.iv2);
				const ColorRGBA32& color = m_daColors[gst.ic];
				const GsVector2& uv0 = GetUv(gst.iuv0);
				const GsVector2& uv1 = GetUv(gst.iuv1);
				const GsVector2& uv2 = GetUv(gst.iuv2);
				if ((gssm.eGss == GeoSolidShape::CONE || gssm.eGss == GeoSolidShape::CYLINDER) && (gssm.eGsf == GeoSolidFace::SIDE))
				{
					GeoSolidRenderTriangle& gsrt = daRenderTriangles[(GsIndex)it];
					const GsVector3 vbcLocal = (gsrt.v0Local + gsrt.v1Local + gsrt.v2Local) / 3.0;
					GsVector3 vpc = vtc;
					vpc.y = vbcLocal.y;
					const GsVector3 vcbcLocal = vbcLocal - vpc;
					const GsVector3 vcbc = mat.MultiplyVector3(vcbcLocal);
					const GsVector3& vnt = m_daNormals[gst.ivn];
					const GsDigit dp = DotProduct(vcbc, vnt);
					gst.turnover = dp < 0;
				}
				if (gssm.eGss == GeoSolidShape::SPHERE)
				{
					GeoSolidRenderTriangle& gsrt = daRenderTriangles[(GsIndex)it];
					const GsVector3 vbcLocal = (gsrt.v0Local + gsrt.v1Local + gsrt.v2Local) / 3.0;
					const GsVector3 vcbcLocal = vbcLocal - vc;
					const GsVector3 vcbc = mat.MultiplyVector3(vcbcLocal);
					const GsVector3& vnt = m_daNormals[gst.ivn];
					const GsDigit dp = DotProduct(vcbc, vnt);
					gst.turnover = dp < 0;

					GsVector3 vn0 = gsv0.v - vc;
					vn0.NormalizeSafe();
					GsVector3 vn1 = gsv1.v - vc;
					vn1.NormalizeSafe();
					GsVector3 vn2 = gsv2.v - vc;
					vn2.NormalizeSafe();
					if (gst.turnover)
					{
						vn0 *= -1.f;
						vn1 *= -1.f;
						vn2 *= -1.f;
					}
					daPvs.emplace_back(PrimitiveVertex{ gsv0.v, vn0, color, uv0 });
					daPvs.emplace_back(PrimitiveVertex{ gsv1.v, vn1, color, uv1 });
					daPvs.emplace_back(PrimitiveVertex{ gsv2.v, vn2, color, uv2 });
				}
				else if (gssm.eGss == GeoSolidShape::CONE && gssm.eGsf == GeoSolidFace::SIDE)
				{
					const GeoSolidRenderTriangle& gsrt = daRenderTriangles[(GsIndex)it];
					const GsVector3& v0 = gsrt.v0Local;
					const GsVector3& v1 = gsrt.v1Local;
					const GsVector3& v2 = gsrt.v2Local;
					const GsVector3& vt = vtc;
					const bool isV0Top = IsVertexEqual(v0, vt, 1e-4);
					const bool isV1Top = IsVertexEqual(v1, vt, 1e-4);
					const bool isV2Top = IsVertexEqual(v2, vt, 1e-4);
					GsVector3 vn0(0, 1, 0);
					GsVector3 vn1(0, 1, 0);
					GsVector3 vn2(0, 1, 0);
					if (!isV0Top)
					{
						const GsVector3& vp = v0;
						GsVector3 vpc = vt;
						vpc.y = vp.y;
						const GsVector3 vtp = vp - vt;
						const GsVector3 vtpc = vpc - vt;
						const GsVector3 vtbc = vbc - vt;
						//相似三角形
						const GsVector3 vtq = vtp * vtbc.Length() / vtpc.Length();
						const GsVector3 vq = vtq + vt;
						const GsDigit ltbcOnTQ = DotProduct(vtbc, vtq) / vtq.Length();
						const GsVector3 vtbcOnTQ = vtq * ltbcOnTQ / vtq.Length();
						const GsVector3 pbcOnTQ = vtbcOnTQ + vt;
						GsVector3 vnb = vtbcOnTQ - vtbc;
						vnb.NormalizeSafe();
						const GsDigit proportion = vtp.Length() / vtq.Length();
						vn0 = GsVector3::yAxis * (1 - proportion) + vnb * proportion;
					}
					if (!isV1Top)
					{
						const GsVector3& vp = v1;
						GsVector3 vpc = vt;
						vpc.y = vp.y;
						const GsVector3 vtp = vp - vt;
						const GsVector3 vtpc = vpc - vt;
						const GsVector3 vtbc = vbc - vt;
						//相似三角形
						const GsVector3 vtq = vtp * vtbc.Length() / vtpc.Length();
						const GsDigit ltbcOnTQ = DotProduct(vtbc, vtq) / vtq.Length();
						const GsVector3 vtbcOnTQ = vtq * ltbcOnTQ / vtq.Length();
						GsVector3 vnb = vtbcOnTQ - vtbc;
						vnb.NormalizeSafe();
						const GsDigit proportion = vtp.Length() / vtq.Length();
						vn1 = GsVector3::yAxis * (1 - proportion) + vnb * proportion;
					}
					if (!isV2Top)
					{
						const GsVector3& vp = v2;
						GsVector3 vpc = vt;
						vpc.y = vp.y;
						const GsVector3 vtp = vp - vt;
						const GsVector3 vtpc = vpc - vt;
						const GsVector3 vtbc = vbc - vt;
						//相似三角形
						const GsVector3 vtq = vtp * vtbc.Length() / vtpc.Length();
						const GsDigit ltbcOnTQ = DotProduct(vtbc, vtq) / vtq.Length();
						const GsVector3 vtbcOnTQ = vtq * ltbcOnTQ / vtq.Length();
						GsVector3 vnb = vtbcOnTQ - vtbc;
						vnb.NormalizeSafe();
						const GsDigit proportion = vtp.Length() / vtq.Length();
						vn2 = GsVector3::yAxis * (1 - proportion) + vnb * proportion;
					}
					{
						const GsVector3 vn0InWhole = mat.MultiplyVector3(vn0);
						const GsVector3 vn1InWhole = mat.MultiplyVector3(vn1);
						const GsVector3 vn2InWhole = mat.MultiplyVector3(vn2);
						vn0 = vn0InWhole;
						vn1 = vn1InWhole;
						vn2 = vn2InWhole;
					}
					if (gst.turnover)
					{
						vn0 *= -1.f;
						vn1 *= -1.f;
						vn2 *= -1.f;
					}
					vn0.NormalizeSafe();
					vn1.NormalizeSafe();
					vn2.NormalizeSafe();
					daPvs.emplace_back(PrimitiveVertex{ gsv0.v, vn0, color, uv0 });
					daPvs.emplace_back(PrimitiveVertex{ gsv1.v, vn1, color, uv1 });
					daPvs.emplace_back(PrimitiveVertex{ gsv2.v, vn2, color, uv2 });
				}
				else if (gssm.eGss == GeoSolidShape::CYLINDER && gssm.eGsf == GeoSolidFace::SIDE)
				{
					const GeoSolidRenderTriangle& gsrt = daRenderTriangles[(GsIndex)it];
					const GsVector3& v0 = gsrt.v0Local;
					const GsVector3& v1 = gsrt.v1Local;
					const GsVector3& v2 = gsrt.v2Local;

					GsVector3 vcCross0 = vc;
					vcCross0.y = v0.y;
					GsVector3 vn0 = v0 - vcCross0;

					GsVector3 vcCross1 = vc;
					vcCross1.y = v1.y;
					GsVector3 vn1 = v1 - vcCross1;

					GsVector3 vcCross2 = vc;
					vcCross2.y = v2.y;
					GsVector3 vn2 = v2 - vcCross2;
					{
						const GsVector3 vn0InWhole = mat.MultiplyVector3(vn0);
						const GsVector3 vn1InWhole = mat.MultiplyVector3(vn1);
						const GsVector3 vn2InWhole = mat.MultiplyVector3(vn2);
						vn0 = vn0InWhole;
						vn1 = vn1InWhole;
						vn2 = vn2InWhole;
					}
					if (gst.turnover)
					{
						vn0 *= -1.0;
						vn1 *= -1.0;
						vn2 *= -1.0;
					}
					vn0.NormalizeSafe();
					vn1.NormalizeSafe();
					vn2.NormalizeSafe();
					daPvs.emplace_back(PrimitiveVertex{ gsv0.v, vn0, color, uv0 });
					daPvs.emplace_back(PrimitiveVertex{ gsv1.v, vn1, color, uv1 });
					daPvs.emplace_back(PrimitiveVertex{ gsv2.v, vn2, color, uv2 });
				}
				else
				{
					const GsVector3& vn = m_daNormals[gst.ivn];
					daPvs.emplace_back(PrimitiveVertex{ gsv0.v, vn, color, uv0 });
					daPvs.emplace_back(PrimitiveVertex{ gsv1.v, vn, color, uv1 });
					daPvs.emplace_back(PrimitiveVertex{ gsv2.v, vn, color, uv2 });
				}
				daPis.emplace_back(oPv);
				daPis.emplace_back(oPv + 1);
				daPis.emplace_back(oPv + 2);
				#if COMPOSITE_GEO_SOLID_DEBUG
				if (m_bDebugRender)
				{
					CreateEdgeMesh(gst.ie0);
					CreateEdgeMesh(gst.ie1);
					CreateEdgeMesh(gst.ie2);
					CreatePointMesh(gst.iv0);
					CreatePointMesh(gst.iv1);
					CreatePointMesh(gst.iv2);
				}
				#endif
			}
			//统计SubMesh中的数据
			gssm.cpv = daPvs.size() - oopv;
			gssm.cpi = daPis.size() - oopi;
		}

		const UInt32 cpv = daPvs.size();
		const UInt32 cpi = daPis.size();
		SharePtr<Mesh> mesh = MoveToSharePtr(CreateObjectWithLabel<Mesh>(kMemMesh));
		mesh->GetWritableIndexFormat() = kIndexFormat32;
		mesh->GetSharedMeshData()->AllocMemory(cpv, cpi, s_ShaderChannelMask);

		VertexData& vd = mesh->GetSharedMeshData()->GetVertexData();
		//顶点数据
		memcpy(vd.GetDataPtr(), daPvs.data(), cpv * sizeof(PrimitiveVertex));
		SharedMeshData::IndexContainer& indexBuffer = mesh->GetSharedMeshData()->GetIndexBuffer();
		//顶点索引数据
		memcpy(indexBuffer.data(), daPis.data(), cpi * sizeof(UInt32));

		//子模型数据
		SharedMeshData::SubMeshContainer& container = mesh->GetSharedMeshData()->GetSubMeshes();
		const GfxPrimitiveType topology = GfxPrimitiveType::kPrimitiveTriangles;
		container.resize_uninitialized(1);
		SubMesh& subMesh = container[0];
		subMesh.baseVertex = 0;
		subMesh.firstVertex = 0;
		subMesh.vertexCount = cpv;
		subMesh.trianglesFirstIndexByte = 0;
		subMesh.trianglesIndexCount = 3;
		subMesh.firstIndexByte = 0;
		subMesh.indexCount = cpi;
		subMesh.topology = topology;
		mesh->UploadMeshData(false);
		mesh->RecalculateBounds();
		return mesh;
	}

	#if COMPOSITE_GEO_SOLID_DEBUG
	void CompositeGeoSolid::CreatePointMesh(const GsIv& iv)
	{
		GeoSolidVertex& gsv = GetVertex(iv);
		if (gsv.isDrawn)
		{
			return;
		}
		gsv.isDrawn = true;
		const UInt32 oPv = m_daPrimitiveVertices.size();
		const GsDigit extent = 1.0;
		const GsVector3 v3Extent(extent, extent, extent);
		dynamic_array<GsVector3> daVs;
		daVs.resize_uninitialized(8);
		daVs[0] = gsv.v + GsVector3(-v3Extent.x, -v3Extent.y, -v3Extent.z);
		daVs[1] = gsv.v + GsVector3(v3Extent.x, -v3Extent.y, -v3Extent.z);
		daVs[2] = gsv.v + GsVector3(v3Extent.x, -v3Extent.y, v3Extent.z);
		daVs[3] = gsv.v + GsVector3(-v3Extent.x, -v3Extent.y, v3Extent.z);
		daVs[4] = gsv.v + GsVector3(-v3Extent.x, v3Extent.y, -v3Extent.z);
		daVs[5] = gsv.v + GsVector3(v3Extent.x, v3Extent.y, -v3Extent.z);
		daVs[6] = gsv.v + GsVector3(v3Extent.x, v3Extent.y, v3Extent.z);
		daVs[7] = gsv.v + GsVector3(-v3Extent.x, v3Extent.y, v3Extent.z);
		//const ColorRGBA32& rgba = gsv.rgba;
		const ColorRGBA32 rgba(0, 0, 0, 255);
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_zAxis, rgba, GsVector2(0, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::neg_zAxis, rgba, GsVector2(0, 1)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::neg_zAxis, rgba, GsVector2(1, 1)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::neg_zAxis, rgba, GsVector2(1, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::zAxis, rgba, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::zAxis, rgba, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::zAxis, rgba, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::zAxis, rgba, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_xAxis, rgba, GsVector2(0, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::neg_xAxis, rgba, GsVector2(0, 1)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::neg_xAxis, rgba, GsVector2(1, 1)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::neg_xAxis, rgba, GsVector2(1, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::xAxis, rgba, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::xAxis, rgba, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::xAxis, rgba, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::xAxis, rgba, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::yAxis, rgba, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::yAxis, rgba, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::yAxis, rgba, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::yAxis, rgba, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_yAxis, rgba, GsVector2(0, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::neg_yAxis, rgba, GsVector2(1, 0)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::neg_yAxis, rgba, GsVector2(1, 1)});
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::neg_yAxis, rgba, GsVector2(0, 1)});
		const UInt32 aIndices[] =
		{
			0, 1, 2, 0, 2, 3,
			4, 5, 6, 4, 6, 7,
			8, 9, 10, 8, 10, 11,
			12, 13, 14, 12, 14, 15,
			16, 17, 18, 16, 18, 19,
			20, 21, 22, 20, 22, 23
		};
		const int ci = sizeof(aIndices) / sizeof(UInt32);
		for (int i = 0; i < ci; ++i)
		{
			m_daPrimitiveIndices.emplace_back(oPv + aIndices[i]);
		}
	}

	void CompositeGeoSolid::CreateEdgeMesh(const GsIe& ie)
	{
		GeoSolidEdge& gse = GetEdge(ie);
		if (gse.isDrawn)
		{
			return;
		}
		gse.isDrawn = true;
		const UInt32 oPv = m_daPrimitiveVertices.size();
		const GeoSolidVertex& gsv0 = GetVertex(gse.iv0);
		const GeoSolidVertex& gsv1 = GetVertex(gse.iv1);
		GsVector3 v3Direction = gsv1.v - gsv0.v;
		v3Direction.NormalizeSafe();
		GsMatrix3x3 matRotate;
		matRotate.SetFromToRotation(GsVector3::yAxis, v3Direction);
		const GsDigit extent = 0.2;
		GsVector3 ve0(-extent, 0, -extent);
		GsVector3 ve1(extent, 0, -extent);
		GsVector3 ve2(extent, 0, extent);
		GsVector3 ve3(-extent, 0, extent);
		ve0 = matRotate.MultiplyVector3(ve0);
		ve1 = matRotate.MultiplyVector3(ve1);
		ve2 = matRotate.MultiplyVector3(ve2);
		ve3 = matRotate.MultiplyVector3(ve3);
		//按正y轴方向由gsv1指向gsv2为标准
		dynamic_array<GsVector3> daVs;
		daVs.resize_uninitialized(8);
		daVs[0] = gsv0.v + ve0;
		daVs[1] = gsv0.v + ve1;
		daVs[2] = gsv0.v + ve2;
		daVs[3] = gsv0.v + ve3;
		daVs[4] = gsv1.v + ve0;
		daVs[5] = gsv1.v + ve1;
		daVs[6] = gsv1.v + ve2;
		daVs[7] = gsv1.v + ve3;
		//const ColorRGBA32& rgba0 = gsv0.rgba;
		//const ColorRGBA32& rgba1 = gsv1.rgba;
		const ColorRGBA32 rgba0(0, 0, 0, 255);
		const ColorRGBA32 rgba1(0, 0, 0, 255);
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_zAxis, rgba0, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::neg_zAxis, rgba1, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::neg_zAxis, rgba1, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::neg_zAxis, rgba0, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::zAxis, rgba0, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::zAxis, rgba1, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::zAxis, rgba1, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::zAxis, rgba0, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_xAxis, rgba0, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::neg_xAxis, rgba0, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::neg_xAxis, rgba1, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::neg_xAxis, rgba1, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::xAxis, rgba1, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::xAxis, rgba1, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::xAxis, rgba0, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::xAxis, rgba0, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[7], GsVector3::yAxis, rgba1, GsVector2(0, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[6], GsVector3::yAxis, rgba1, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[5], GsVector3::yAxis, rgba1, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[4], GsVector3::yAxis, rgba1, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[0], GsVector3::neg_yAxis, rgba0, GsVector2(0, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[1], GsVector3::neg_yAxis, rgba0, GsVector2(1, 0) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[2], GsVector3::neg_yAxis, rgba0, GsVector2(1, 1) });
		m_daPrimitiveVertices.emplace_back(PrimitiveVertex{ daVs[3], GsVector3::neg_yAxis, rgba0, GsVector2(0, 1) });
		const UInt32 aIndices[] =
		{
			0, 1, 2, 0, 2, 3,
			4, 5, 6, 4, 6, 7,
			8, 9, 10, 8, 10, 11,
			12, 13, 14, 12, 14, 15,
			16, 17, 18, 16, 18, 19,
			20, 21, 22, 20, 22, 23
		};
		const int ci = sizeof(aIndices) / sizeof(UInt32);
		for (int i = 0; i < ci; ++i)
		{
			m_daPrimitiveIndices.emplace_back(oPv + aIndices[i]);
		}
	}

	void CompositeGeoSolid::CorrectOldNormals()
	{
		const UInt32 cvn = m_daNormals.size();
		const GsIt ct = GetTriangleCount();
		for (UInt32 ivn = 0; ivn < cvn; ++ivn)
		{
			const GsVector3& vn = m_daNormals[ivn];
			const bool zeroVector = IsZero(vn.x) && IsZero(vn.y) && IsZero(vn.z);
			if (!zeroVector)
			{
				continue;
			}
			for (GsIt it = 0; it < ct; ++it)
			{
				GeoSolidTriangle& gst = GetTriangle(it);
				if (gst.ivn == ivn)
				{
					const GsVector3& va = GetVertex(gst.iv0).v;
					const GsVector3& vb = GetVertex(gst.iv1).v;
					const GsVector3& vc = GetVertex(gst.iv2).v;
					const GsVector3 vab = vb - va;
					const GsVector3 vbc = vc - vb;
					GsVector3 vcp = CrossProduct(vab, vbc);
					gst.ivn = AddNormal(vcp);
					const GsVector3 viab = vab.GetNormalizedSafe();
					const GsVector3 vibc = vbc.GetNormalizedSafe();
					if (viab == vibc)
					{
						continue;
					}
					if (gst.ivn == ivn)
					{
						const GsVector3 vabEx = vab * 10000.0;
						const GsVector3 vbcEx = vbc * 10000.0;
						GsVector3 vcp = CrossProduct(vabEx, vbcEx);
						gst.ivn = AddNormal(vcp);
					}
					if (gst.ivn == ivn)
					{
						const GsVector3 vac = vc - va;
						const GsVector3 vacEx = vac * 10000.0;
						GsVector3 vcp = CrossProduct(vab, vacEx);
						gst.ivn = AddNormal(vcp);
					}
					SANDBOX_ASSERT(gst.ivn != ivn);
				}
			}
		}
	}

	#endif

	void CompositeGeoSolid::UpdateBound()
	{
		GeoSolid::UpdateBound(m_daGeoSolidVertices, m_bb);
		//Center();
	}

	//void CompositeGeoSolid::Center()
	//{
	//	GsVector3 vcWhole = m_bb.getCenter();
	//	vcWhole.y = 0;
	//	m_bb.m_MinPos -= vcWhole;
	//	m_bb.m_MaxPos -= vcWhole;
	//	for (GeoSolidVertex* gsv : m_daGeoSolidVertices)
	//	{
	//		gsv->v -= vcWhole;
	//	}
	//	for (GsVector3& vc : m_daCenters)
	//	{
	//		vc -= vcWhole;
	//	}
	//	for (GsBoxBound& bb : m_daBoxBounds)
	//	{
	//		bb.m_MinPos -= vcWhole;
	//		bb.m_MaxPos -= vcWhole;
	//	}
	//}

	void CompositeGeoSolid::SeparateSurfaces()
	{
		if (!m_daMeshes.empty())
		{
			return;
		}
		const int csm = GetSurfaceCount();
		m_daMeshes.reserve(csm);
		UInt32 opv = 0;
		UInt32 opi = 0;
		if (!m_mesh)
		{
			return;
		}
		const GeoSolidArray<PrimitiveVertex> daPvs = GetPvs(m_mesh, m_daPrimitiveVertices);
		if (daPvs.empty())
		{
			return;
		}
		const GeoSolidArray<UInt32> daPis = GetPis(m_mesh, m_daPrimitiveIndices);
		if (daPis.empty())
		{
			return;
		}
		m_mesh->SetIsReadable(false);
		for (int ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			SharePtr<Mesh> mesh = GeoSolid::CreateMesh(daPvs, daPis, gssm.cpv, gssm.cpi, opv, opi);
			m_daMeshes.emplace_back(mesh);
			opv += gssm.cpv;
			opi += gssm.cpi;
		}
	}

	GeoSolidFace CompositeGeoSolid::GetGeoSolidFace(int ism)
	{
		return ism >= 0 && ism < m_daSubMeshes.size() ? m_daSubMeshes[ism]->eGsf : GeoSolidFace::UNKNOWN;
		//const GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
		//return gssm.eGsf;
	}

	GeoSolidShape CompositeGeoSolid::GetGeoSolidShape(int ism)
	{
		return ism >= 0 && ism < m_daSubMeshes.size() ? m_daSubMeshes[ism]->eGss : GeoSolidShape::NONE;
		//const GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
		//return gssm.eGss;
	}

	GeoSolidSubMesh* CompositeGeoSolid::GetGeoSolidSubMesh(int ism)
	{
		return ism >= 0 && ism < m_daSubMeshes.size() ? m_daSubMeshes[ism] : nullptr;
		//return &*m_daSubMeshes[ism];
	}

	SharePtr<Mesh> CompositeGeoSolid::GetSurfaceMesh(const int& i)
	{
		//TODO: 2024-01-12 15:51:06: 将此函数搬到父类，同时给6种基础模型使用，需要使用再创建。使优化达到极致
		if (m_daMeshes.empty())
		{
			m_daMeshes.resize(m_daSubMeshes.size(), nullptr);
		}
		if (i < 0 || i >= m_daMeshes.size())
		{
			return nullptr;
		}
		{
			SharePtr<Mesh> mesh = m_daMeshes[i];
			if (mesh)
			{
				return mesh;
			}
		}
		GeoSolidArray<PrimitiveVertex> daPvs = GetPvs(m_mesh, m_daPrimitiveVertices);
		if (daPvs.empty())
		{
			return nullptr;
		}
		GeoSolidArray<UInt32> daPis = GetPis(m_mesh, m_daPrimitiveIndices);
		if (daPis.empty())
		{
			return nullptr;
		}
		const GeoSolidSubMesh& gssmI = *m_daSubMeshes[i];
		const int cpv = gssmI.cpv;
		const int cpi = gssmI.cpi;
		int opv = 0;
		int opi = 0;
		for (int j = 0; j < i; ++j)
		{
			const GeoSolidSubMesh& gssmJ = *m_daSubMeshes[j];
			opv += gssmJ.cpv;
			opi += gssmJ.cpi;
		}
		SharePtr<Mesh> mesh = m_daMeshes[i] = GeoSolid::CreateMesh(daPvs, daPis, cpv, cpi, opv, opi);
		return mesh;
	}

	//GeoSolidSubMesh& CompositeGeoSolid::GetSubMesh(const UInt32& ism)
	//{
	//	return ism >= 0 && ism < m_daSubMeshes.size() ? *m_daSubMeshes[ism] : GeoSolidSubMesh();
	//}

	//const GeoSolidSubMesh& CompositeGeoSolid::GetSubMesh(const UInt32& ism) const
	//{
	//	return ism >= 0 && ism < m_daSubMeshes.size() ? *m_daSubMeshes[ism] : GeoSolidSubMesh();
	//}

	GsVector2& CompositeGeoSolid::GetUv(const GsIndex& iuv)
	{
		return m_daUvs[iuv >= 0 && iuv < m_daUvs.size() ? iuv : 0];
	}

	GsIv CompositeGeoSolid::AddVertex(GeoSolidVertex* gsvIn, const GsDigit errorThreshold)
	{
		if (!IsFloatValid(gsvIn->v.x) || !IsFloatValid(gsvIn->v.y) || !IsFloatValid(gsvIn->v.z))
		{
			SANDBOX_ASSERT(false);
			return 0;
		}
		int index = FindVertex(*gsvIn, errorThreshold);
		if (index >= 0 && index < m_daGeoSolidVertices.size())
		{
			GsIv iv = (GsIv)index;
			GeoSolidVertex& gsvExist = GetVertex(iv);
			gsvExist.intersectSrc.from |= gsvIn->intersectSrc.from;
			gsvExist.intersectCut.from |= gsvIn->intersectCut.from;
			gsvExist.intersectSrc.inside |= gsvIn->intersectSrc.inside;
			gsvExist.intersectCut.inside |= gsvIn->intersectCut.inside;
			gsvExist.intersectSrc.hasCheckedInside |= gsvIn->intersectSrc.hasCheckedInside;
			gsvExist.intersectCut.hasCheckedInside |= gsvIn->intersectCut.hasCheckedInside;
			return iv;
		}
		GsIv iv = AddVertexAlways(gsvIn);
		GeoSolidVertex& gsv = GetVertex(iv);
		if (!gsv.intersectSrc.hasCheckedInside)
		{
			gsv.intersectSrc.inside = IsVertexInGeoSolid(iv, true);
		}
		if (!gsv.intersectCut.hasCheckedInside)
		{
			gsv.intersectCut.inside = IsVertexInGeoSolid(iv, false);
		}
		return iv;
	}

	GsIv CompositeGeoSolid::AddVertexAlways(const GeoSolidVertex* gsvIn)
	{
		#if COMPOSITE_ELEMENT_USE_POINTER
		//GeoSolidVertex* pgsv = SANDBOX_NEW(GeoSolidVertex, gsvIn);
		GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(gsvIn);
		gsv->vInSrc = RotateVectorByQuat(m_qRotateInverseSrc, gsv->v);
		gsv->vInCut = RotateVectorByQuat(m_qRotateInverseCut, gsv->v);
		m_daGeoSolidVertices.push_back(gsv);
		#else
		GeoSolidVertex gsv(gsvIn);
		gsv.vInSrc = RotateVectorByQuat(m_qRotateInverseSrc, gsv->v);
		gsv.vInCut = RotateVectorByQuat(m_qRotateInverseCut, gsv->v);
		m_daGeoSolidVertices.push_back(gsv);
		#endif
		const GsIv iv = GetVertexCount() - 1;
		return iv;
	}

	void CompositeGeoSolid::AddVertexToTriangle(const GsIv& iv, const GsIt& it)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		if (iv == gst.iv0 || iv == gst.iv1 || iv == gst.iv2)
		{
			return;
		}
		GeoSolidVertex& gsv = GetVertex(iv);
		GeoSolidSet<GsIv>& setNewIvs = gst.GetNewIvs();
		if (setNewIvs.find(iv) != setNewIvs.end())
		{
			return;
		}
		setNewIvs.insert(iv);
		#if !COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES
		std::list<GsIe> listIesToAdd;
		GeoSolidSet<GsIe>& setIesDivider = gst.GetIesDivider();
		for (auto itIe = setIesDivider.begin(); itIe != setIesDivider.end(); )
		{
			const GsIe& ie = *itIe;
			const bool inLine = IsVertexInEdge(iv, ie);
			if (!inLine)
			{
				++itIe;
				continue;
			}
			GeoSolidEdge& gse = GetEdge(ie);
			if (gse.iv0 == iv || gse.iv1 == iv)
			{
				++itIe;
				continue;
			}
			DivideEdge(ie);
			GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(gse.iv0, iv, gst.intersectSrc.from, gst.intersectCut.from);
			GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(gse.iv1, iv, gst.intersectSrc.from, gst.intersectCut.from);
			const GsIe ie0 = AddEdge(gse0);
			const GsIe ie1 = AddEdge(gse1);
			GeoSolidEdge::recycle(gse0);
			GeoSolidEdge::recycle(gse1);
			listIesToAdd.emplace_back(ie0);
			listIesToAdd.emplace_back(ie1);
			itIe = setIesDivider.erase(itIe);
		}
		for (const GsIe& ie : listIesToAdd)
		{
			setIesDivider.emplace(ie);
		}
		#endif
	}

	GsIe CompositeGeoSolid::AddEdge(const GeoSolidEdge* gseIn)
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		SANDBOX_ASSERT(gseIn.iv0 != gseIn.iv1);
		#endif
		const GsIe ie = GeoSolid::AddEdge(m_daEdges, gseIn);
		#if !COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES
		AddNeighborsToEach(ie);
		#endif
		return ie;
	}

	GsIe CompositeGeoSolid::AddEdgeIntoTriangle(const GsIe& ieMain, const GsIt& it)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		GeoSolidEdge& gseMain = GetEdge(ieMain);
		GsIv ivg = gseMain.iv0;
		GsIv ivh = gseMain.iv1;
		GsVector3 vg = GetVertex(ivg).v;
		GsVector3 vh = GetVertex(ivh).v;
		GsIe ieGH = ieMain;
		#if !COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES
		//在边上的线段直接跳过，有顶点决定了分割
		{
			int ieg = -1;
			int ieh = -1;
			IsVertexInTriangleEdge(gst, ivg, &ieg);
			IsVertexInTriangleEdge(gst, ivh, &ieh);
			if (ieg >= 0 && ieg == ieh)
			{
				return ieMain;
			}
			if (ivg == gst.iv0)
			{
				if (ieh == gst.ie0 || ieh == gst.ie1)
				{
					return ieMain;
				}
			}
			else if (ivg == gst.iv1)
			{
				if (ieh == gst.ie0 || ieh == gst.ie2)
				{
					return ieMain;
				}
			}
			else if (ivg == gst.iv2)
			{
				if (ieh == gst.ie1 || ieh == gst.ie2)
				{
					return ieMain;
				}
			}
			if (ivh == gst.iv0)
			{
				if (ieg == gst.ie0 || ieg == gst.ie1)
				{
					return ieMain;
				}
			}
			else if (ivh == gst.iv1)
			{
				if (ieg == gst.ie0 || ieg == gst.ie2)
				{
					return ieMain;
				}
			}
			else if (ivh == gst.iv2)
			{
				if (ieg == gst.ie1 || ieg == gst.ie2)
				{
					return ieMain;
				}
			}
		}
		//截短
		GsVector3 vix;
		GsVector3 viy;
		GsIe ieWhereGIn = -1;
		GsIe ieWhereHIn = -1;
		const bool isGInTriangle = IsVertexInsideTriangle(ivg, it, &ieWhereGIn);
		const bool isHInTriangle = IsVertexInsideTriangle(ivh, it, &ieWhereHIn);
		int cCross = WhetherEdgeCrossTriangle(ieGH, it, vix, viy);
		if (cCross == 1)
		{
			//GeoSolidVertex gsv(vix, gst.intersectSrc.from, gst.intersectCut.from);
			GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vix, gst.intersectSrc.from, gst.intersectCut.from);
			GsDigit error = 1e-3;
			GsIv ivgNew = AddVertex(gsv, error);
			GeoSolidVertex::recycle(gsv);
			//新点如果与线段端点重合，则线段与三角形交点恰好是端点
			if (ivgNew != ivg && ivgNew != ivh)
			{
				GsIv ivhOld = isGInTriangle && ieWhereGIn >= 0 ? ivg : ivh;
				if (ivgNew == ivhOld)
				{
					return ieGH;
				}
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivgNew, ivhOld, gseMain.intersectSrc.from, gseMain.intersectCut.from);
				ieGH = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
			}
		}
		else if (cCross == 2)
		{
			GeoSolidVertex* gsvx = GeoSolid::obtain<GeoSolidVertex>(vix, gst.intersectSrc.from, gst.intersectCut.from);
			GeoSolidVertex* gsvy = GeoSolid::obtain<GeoSolidVertex>(viy, gst.intersectSrc.from, gst.intersectCut.from);
			GsDigit error = 1e-3;
			GsIv ivgNew = AddVertex(gsvx, error);
			GsIv ivhNew = AddVertex(gsvy, error);
			GeoSolidVertex::recycle(gsvx);
			GeoSolidVertex::recycle(gsvy);
			if (ivgNew == ivhNew)
			{
				return ieGH;
			}
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivgNew, ivhNew, gseMain.intersectSrc.from, gseMain.intersectCut.from);
			ieGH = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
		}

		if (cCross == 1 || cCross == 2)
		{
			//重新赋值
			GeoSolidEdge& gseShort = GetEdge(ieGH);
			ivg = gseShort.iv0;
			ivh = gseShort.iv1;
			vg = GetVertex(ivg).v;
			vh = GetVertex(ivh).v;
		}
		#endif

		GeoSolidSet<GsIe>& setIesDivider = gst.GetIesDivider();
		if (setIesDivider.find(ieGH) != setIesDivider.end())
		{
			return ieGH;
		}
		{
			GeoSolidEdge& gseGH = GetEdge(ieGH);
			if ((gseGH.iv0 == gst.iv0 && gseGH.iv1 == gst.iv1) ||
				(gseGH.iv0 == gst.iv0 && gseGH.iv1 == gst.iv2) ||
				(gseGH.iv0 == gst.iv1 && gseGH.iv1 == gst.iv0) ||
				(gseGH.iv0 == gst.iv1 && gseGH.iv1 == gst.iv2) ||
				(gseGH.iv0 == gst.iv2 && gseGH.iv1 == gst.iv0) ||
				(gseGH.iv0 == gst.iv2 && gseGH.iv1 == gst.iv1))
			{
				return ieGH;
			}
		}
		const GsVector3 vgh = vh - vg;
		const GsVector3 vigh = vgh.GetNormalizedSafe();
		std::unordered_set<GsIe> setIesToAdd;
		std::unordered_set<GsIe> setIesToRemove;
		bool hasDividedByEdge = false;
		GsVector3 vIntersect;
		for (const GsIe& iePQ : setIesDivider)
		{
			//DivideEdgeByEdge
			GeoSolidEdge& gsePQ = GetEdge(iePQ);
			const GsIv ivp = gsePQ.iv0;
			const GsIv ivq = gsePQ.iv1;
			const GsVector3& vp = GetVertex(ivp).v;
			const GsVector3& vq = GetVertex(ivq).v;
			const GsVector3 vpq = vq - vp;
			const GsVector3 vipq = vpq.GetNormalizedSafe();
			const GsVector3 vipqNeg = -vipq;
			bool isGCoincideWithP = ivg == ivp;
			bool isGCoincideWithQ = ivg == ivq;
			bool isHCoincideWithP = ivh == ivp;
			bool isHCoincideWithQ = ivh == ivq;
			if (!IsVertexEqual(vipq, vigh, 1e-6) && !IsVertexEqual(vipqNeg, vigh, 1e-6))
			{
				if (isGCoincideWithP || isGCoincideWithQ || isHCoincideWithP || isHCoincideWithQ)
				{
					continue;
				}
				bool inEdge;
				GsIv ivIntersect;
				GsIe ieDivided;
				//点在线段上，单独判断
				if (inEdge = IsVertexInEdge(ivp, ieGH))
				{
					ivIntersect = ivp;
					ieDivided = ieGH;
				}
				else if (inEdge = IsVertexInEdge(ivq, ieGH))
				{
					ivIntersect = ivq;
					ieDivided = ieGH;
				}
				else if (inEdge = IsVertexInEdge(ivg, iePQ))
				{
					ivIntersect = ivg;
					ieDivided = iePQ;
				}
				else if (inEdge = IsVertexInEdge(ivh, iePQ))
				{
					ivIntersect = ivh;
					ieDivided = iePQ;
				}
				if (inEdge)
				{
					GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(ivIntersect, 0, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(ivIntersect, 0, gst.intersectSrc.from, gst.intersectCut.from);
					if (ieDivided == ieGH)
					{
						gse0->iv1 = ivg;
						gse1->iv1 = ivh;
					}
					else if (ieDivided == iePQ)
					{
						gse0->iv1 = ivp;
						gse1->iv1 = ivq;
					}
					else
					{
						SANDBOX_ASSERT(false);
						continue;
					}
					const GsIe ie0 = AddEdge(gse0);
					const GsIe ie1 = AddEdge(gse1);
					GeoSolidEdge::recycle(gse0);
					GeoSolidEdge::recycle(gse1);
					setIesToAdd.emplace(ie0);
					setIesToAdd.emplace(ie1);
					if (ieDivided == iePQ)
					{
						setIesToRemove.emplace(ieDivided);
						setIesToAdd.emplace(ieGH);
					}
					DivideEdge(ieDivided);
					hasDividedByEdge = true;
					continue;
				}
				else
				{
					bool intersect = WhetherLineIntersectLine(vp, vq, vg, vh, &vIntersect);
					if (!intersect)
					{
						continue;
					}
					if (isGCoincideWithP || isGCoincideWithQ || isHCoincideWithP || isHCoincideWithQ)
					{
						continue;
					}
					else
					{
						GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(
							vIntersect, 
							gst.intersectSrc.from, gst.intersectCut.from
						);
						if (gst.intersectSrc.from)
						{
							gsv->intersectSrc.inside = true;
							gsv->intersectSrc.hasCheckedInside = true;
						}
						if (gst.intersectCut.from)
						{
							gsv->intersectCut.inside = true;
							gsv->intersectCut.hasCheckedInside = true;
						}
						const GsIv ivNew = AddVertex(gsv);
						GeoSolidVertex::recycle(gsv);
						if (ivNew == ivp || ivNew == ivq || ivNew == ivg || ivNew == ivh)
						{
							continue;
						}
						GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(ivp, ivNew, gst.intersectSrc.from, gst.intersectCut.from);
						GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(ivq, ivNew, gst.intersectSrc.from, gst.intersectCut.from);
						GeoSolidEdge* gse2 = GeoSolid::obtain<GeoSolidEdge>(ivg, ivNew, gst.intersectSrc.from, gst.intersectCut.from);
						GeoSolidEdge* gse3 = GeoSolid::obtain<GeoSolidEdge>(ivh, ivNew, gst.intersectSrc.from, gst.intersectCut.from);
						const GsIe ie0 = AddEdge(gse0);
						const GsIe ie1 = AddEdge(gse1);
						const GsIe ie2 = AddEdge(gse2);
						const GsIe ie3 = AddEdge(gse3);
						GeoSolidEdge::recycle(gse0);
						GeoSolidEdge::recycle(gse1);
						GeoSolidEdge::recycle(gse2);
						GeoSolidEdge::recycle(gse3);
						setIesToAdd.emplace(ie0);
						setIesToAdd.emplace(ie1);
						setIesToAdd.emplace(ie2);
						setIesToAdd.emplace(ie3);
						setIesToRemove.emplace(ieGH);
						setIesToRemove.emplace(iePQ);
						DivideEdge(ieGH);
						hasDividedByEdge = true;
						continue;
					}
				}
			}
			//平行情况
			const GsVector3 vpg = vg - vp;
			const GsVector3 vph = vh - vp;
			const GsDigit lengthSqrPG = vpg.LengthSqr();
			const GsDigit lengthSqrPH = vph.LengthSqr();
			const bool isGInPQ = GeometryMath::IsVertexInSegment(vg, vp, vq);
			const bool isHInPQ = GeometryMath::IsVertexInSegment(vh, vp, vq);
			if (isGCoincideWithP || isGCoincideWithQ)
			{
				setIesToAdd.emplace(ieGH);
				if (isHInPQ)
				{
					GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
					if (isGCoincideWithP)
					{
						gse->iv0 = ivh;
						gse->iv1 = ivq;
					}
					else
					{
						gse->iv0 = ivh;
						gse->iv1 = ivp;
					}
					const GsIe ie = AddEdge(gse);
					GeoSolidEdge::recycle(gse);
					setIesToAdd.emplace(ie);
					setIesToRemove.emplace(iePQ);
				}
				hasDividedByEdge = true;
			}
			else if (isHCoincideWithP || isHCoincideWithQ)
			{
				setIesToAdd.emplace(ieGH);
				if (isGInPQ)
				{
					GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
					if (isHCoincideWithP)
					{
						gse->iv0 = ivg;
						gse->iv1 = ivq;
					}
					else
					{
						gse->iv0 = ivg;
						gse->iv1 = ivp;
					}
					const GsIe ie = AddEdge(gse);
					GeoSolidEdge::recycle(gse);
					setIesToAdd.emplace(ie);
					setIesToRemove.emplace(iePQ);
				}
				hasDividedByEdge = true;
			}
			else
			{
				if (isGInPQ && isHInPQ)
				{
					//在线段内
					GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
					if (lengthSqrPG < lengthSqrPH)
					{
						gse0->iv0 = ivp; gse0->iv1 = ivg;
						gse1->iv0 = ivh; gse1->iv1 = ivq;
					}
					else if (lengthSqrPG > lengthSqrPH)
					{
						gse0->iv0 = ivp; gse0->iv1 = ivh;
						gse1->iv0 = ivg; gse1->iv1 = ivq;
					}
					else
					{
						GeoSolidEdge::recycle(gse0);
						GeoSolidEdge::recycle(gse1);
						continue;
					}
					const GsIe ie0 = AddEdge(gse0);
					const GsIe ie1 = AddEdge(gse1);
					GeoSolidEdge::recycle(gse0);
					GeoSolidEdge::recycle(gse1);
					setIesToAdd.emplace(ie0);
					setIesToAdd.emplace(ie1);
					setIesToAdd.emplace(ieGH);
					setIesToRemove.emplace(iePQ);
					hasDividedByEdge = true;
				}
				else if (isGInPQ && !isHInPQ)
				{
					//其中一点在线段上
					GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(ivp, ivg, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(ivg, ivq, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse2 = GeoSolid::obtain<GeoSolidEdge>(ivq, ivh, gst.intersectSrc.from, gst.intersectCut.from);
					const GsIe ie0 = AddEdge(gse0);
					const GsIe ie1 = AddEdge(gse1);
					const GsIe ie2 = AddEdge(gse2);
					GeoSolidEdge::recycle(gse0);
					GeoSolidEdge::recycle(gse1);
					GeoSolidEdge::recycle(gse2);
					setIesToAdd.emplace(ie0);
					setIesToAdd.emplace(ie1);
					setIesToAdd.emplace(ie2);
					setIesToRemove.emplace(iePQ);
					hasDividedByEdge = true;
				}
				else if (!isGInPQ && isHInPQ)
				{
					GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(ivp, ivh, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(ivh, ivq, gst.intersectSrc.from, gst.intersectCut.from);
					GeoSolidEdge* gse2 = GeoSolid::obtain<GeoSolidEdge>(ivq, ivg, gst.intersectSrc.from, gst.intersectCut.from);
					const GsIe ie0 = AddEdge(gse0);
					const GsIe ie1 = AddEdge(gse1);
					const GsIe ie2 = AddEdge(gse2);
					GeoSolidEdge::recycle(gse0);
					GeoSolidEdge::recycle(gse1);
					GeoSolidEdge::recycle(gse2);
					setIesToAdd.emplace(ie0);
					setIesToAdd.emplace(ie1);
					setIesToAdd.emplace(ie2);
					setIesToRemove.emplace(iePQ);
					hasDividedByEdge = true;
				}
				else if (!isGInPQ && !isHInPQ)
				{
					const GsDigit dp = DotProduct(vpg, vph);
					if (dp > 0)
					{
						//CD在AB的一侧
						continue;
					}
					const GsVector3 viac = vpg.GetNormalizedSafe();
					//共线判断
					if (IsVertexEqual(vipq, viac, 1e-6) || IsVertexEqual(vipqNeg, viac, 1e-6))
					{
						//点C、点D分别在AB的两侧
						GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
						GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(0, 0, gst.intersectSrc.from, gst.intersectCut.from);
						if (lengthSqrPG < lengthSqrPH)
						{
							gse0->iv0 = ivg; gse0->iv1 = ivp;
							gse1->iv0 = ivq; gse1->iv1 = ivh;
						}
						else if (lengthSqrPG > lengthSqrPH)
						{
							gse0->iv0 = ivh; gse0->iv1 = ivp;
							gse1->iv0 = ivq; gse1->iv1 = ivg;
						}
						else
						{
							GeoSolidEdge::recycle(gse0);
							GeoSolidEdge::recycle(gse1);
							continue;
						}
						const GsIe ie0 = AddEdge(gse0);
						const GsIe ie1 = AddEdge(gse1);
						GeoSolidEdge::recycle(gse0);
						GeoSolidEdge::recycle(gse1);
						setIesToAdd.emplace(ie0);
						setIesToAdd.emplace(ie1);
						hasDividedByEdge = true;
					}
				}
			}
		}
		if (hasDividedByEdge)
		{
			for (const GsIe& ie : setIesToRemove)
			{
				auto itIe = setIesDivider.find(ie);
				if (itIe == setIesDivider.end())
				{
					continue;
				}
				setIesDivider.erase(itIe);
				DivideEdge(ie);
			}
			for (const GsIe& ie : setIesToAdd)
			{
				setIesDivider.emplace(ie);
			}
		}
		else
		{
			setIesDivider.emplace(ieGH);
		}
		return ieGH;
	}

	GsIndex CompositeGeoSolid::AddUvInTriangle(const GsIv& ivx, const GsIt& it)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		const GsIv iva = gst.iv0;
		const GsIv ivb = gst.iv1;
		const GsIv ivc = gst.iv2;
		if (ivx == iva)
		{
			return gst.iuv0;
		}
		if (ivx == ivb)
		{
			return gst.iuv1;
		}
		if (ivx == ivc)
		{
			return gst.iuv2;
		}
		GsIe ieInEdge = -1;
		const bool inside = IsVertexInsideTriangle(ivx, it, &ieInEdge);
		const bool inEdgeAB = ieInEdge == gst.ie0;
		const bool inEdgeAC = ieInEdge == gst.ie1;
		const bool inEdgeBC = ieInEdge == gst.ie2;
		const bool inEdge = inEdgeAB || inEdgeAC || inEdgeBC;
		//if (!inside && !inEdge)
		//{
		//	SANDBOX_ASSERT(false);
		//	return -1;
		//}
		GeoSolidVertex& gsvX = GetVertex(ivx);
		const GeoSolidVertex& gsvA = GetVertex(iva);
		const GeoSolidVertex& gsvB = GetVertex(ivb);
		const GeoSolidVertex& gsvC = GetVertex(ivc);
		GsVector2 v2a;
		GsVector2 v2b;
		GsVector2 v2c;
		GsVector2 v2x;
		if (gst.intersectSrc.from)
		{
			v2a.Set(gsvA.vInSrc[gst.uIndex], gsvA.vInSrc[gst.vIndex]);
			v2b.Set(gsvB.vInSrc[gst.uIndex], gsvB.vInSrc[gst.vIndex]);
			v2c.Set(gsvC.vInSrc[gst.uIndex], gsvC.vInSrc[gst.vIndex]);
			v2x.Set(gsvX.vInSrc[gst.uIndex], gsvX.vInSrc[gst.vIndex]);
		}
		else
		{
			v2a.Set(gsvA.vInCut[gst.uIndex], gsvA.vInCut[gst.vIndex]);
			v2b.Set(gsvB.vInCut[gst.uIndex], gsvB.vInCut[gst.vIndex]);
			v2c.Set(gsvC.vInCut[gst.uIndex], gsvC.vInCut[gst.vIndex]);
			v2x.Set(gsvX.vInCut[gst.uIndex], gsvX.vInCut[gst.vIndex]);
		}
		GsIndex iuv = -1;
		if (inEdge)
		{
			const GsVector2& uv0 = GetUv(gst.iuv0);
			const GsVector2& uv1 = GetUv(gst.iuv1);
			const GsVector2& uv2 = GetUv(gst.iuv2);
			if (inEdgeAB)
			{
				const GsVector2 v2ax = v2x - v2a;
				const GsVector2 v2ab = v2b - v2a;
				const GsVector2 uvx(
					IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x,
					IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y
				);
				iuv = AddUv(uvx);
			}
			else if (inEdgeAC)
			{
				const GsVector2 v2ax = v2x - v2a;
				const GsVector2 v2ac = v2c - v2a;
				const GsVector2 uvx(
					IsZero(v2ac.x) ? uv0.x : uv0.x + (uv2.x - uv0.x) * v2ax.x / v2ac.x,
					IsZero(v2ac.y) ? uv0.y : uv0.y + (uv2.y - uv0.y) * v2ax.y / v2ac.y
				);
				iuv = AddUv(uvx);
			}
			else if (inEdgeBC)
			{
				const GsVector2 v2bx = v2x - v2b;
				const GsVector2 v2bc = v2c - v2b;
				const GsVector2 uvx(
					IsZero(v2bc.x) ? uv1.x : uv1.x + (uv2.x - uv1.x) * v2bx.x / v2bc.x,
					IsZero(v2bc.y) ? uv1.y : uv1.y + (uv2.y - uv1.y) * v2bx.y / v2bc.y
				);
				iuv = AddUv(uvx);
			}
		}
		else
		{
			const GsVector2 v2ax = v2x - v2a;
			const GsVector2 v2bx = v2x - v2b;
			const GsVector2 v2ab = v2b - v2a;
			const GsVector2 v2ac = v2c - v2a;
			const GsVector2 v2bc = v2c - v2b;
			const GsVector2& uv0 = GetUv(gst.iuv0);
			const GsVector2& uv1 = GetUv(gst.iuv1);
			const GsVector2& uv2 = GetUv(gst.iuv2);
			GsVector2 uvx;
			if (!IsZero(v2ab.x) && !IsZero(uv1.x - uv0.x))
			{
				uvx.x = IsZero(v2ax.x - v2ab.x) ? uv1.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x;
			}
			else if (!IsZero(v2ac.x) && !IsZero(uv2.x - uv0.x))
			{
				uvx.x = IsZero(v2ax.x - v2ac.x) ? uv2.x : uv0.x + (uv2.x - uv0.x) * v2ax.x / v2ac.x;
			}
			else if (!IsZero(v2bc.x) && !IsZero(uv2.x - uv1.x))
			{
				uvx.x = IsZero(v2bx.x - v2bc.x) ? uv2.x : uv1.x + (uv2.x - uv1.x) * v2bx.x / v2bc.x;
			}
			else
			{
				uvx.x = IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x;
			}
			if (!IsZero(v2ab.y) && !IsZero(uv1.y - uv0.y))
			{
				uvx.y = IsZero(v2ax.y - v2ab.y) ? uv1.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y;
			}
			else if (!IsZero(v2ac.y) && !IsZero(uv2.y - uv0.y))
			{
				uvx.y = IsZero(v2ax.y - v2ac.y) ? uv2.y : uv0.y + (uv2.y - uv0.y) * v2ax.y / v2ac.y;
			}
			else if (!IsZero(v2bc.y) && !IsZero(uv2.y - uv1.y))
			{
				uvx.y = IsZero(v2bx.y - v2bc.y) ? uv2.y : uv1.y + (uv2.y - uv1.y) * v2bx.y / v2bc.y;
			}
			else
			{
				uvx.y = IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y;
			}
			iuv = AddUv(uvx);
		}
		return iuv;
	}

	UInt32 CompositeGeoSolid::AddNormal(const GsVector3& vn)
	{
		const UInt32 cvn = m_daNormals.size();
		for (int ivn = cvn - 1; ivn >= 0; --ivn)
		{
			const GsVector3& vn0 = m_daNormals[ivn];
			if (IsVertexEqual(vn, vn0, 1e-2))
			{
				return ivn;
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		const bool zeroVector = IsZero(vn.x) && IsZero(vn.y) && IsZero(vn.z);
		SANDBOX_ASSERT(!zeroVector);
		#endif
		m_daNormals.emplace_back(vn);
		return cvn;
	}

	UInt32 CompositeGeoSolid::AddUv(const GsVector2& uv)
	{
		return GeoSolid::AddUv(m_daUvs, uv);
	}

	UInt32 CompositeGeoSolid::AddColor(const ColorRGBA32& c)
	{
		const UInt32 cc = m_daColors.size();
		for (UInt32 ic = 0; ic < cc; ++ic)
		{
			const ColorRGBA32& c0 = m_daColors[ic];
			if (c0 == c)
			{
				return ic;
			}
		}
		m_daColors.emplace_back(c);
		return cc;
	}

	UInt32 CompositeGeoSolid::AddBoxBound(const GsBoxBound& bb)
	{
		const UInt32 cbb = m_daBoxBounds.size();
		for (UInt32 ibb = 0; ibb < cbb; ++ibb)
		{
			const GsBoxBound& bb0 = m_daBoxBounds[ibb];
			if (IsVertexEqual(bb0.m_MinPos, bb.m_MinPos) && IsVertexEqual(bb0.m_MaxPos, bb.m_MaxPos))
			{
				return ibb;
			}
		}
		m_daBoxBounds.emplace_back(bb);
		return cbb;
	}

	UInt32 CompositeGeoSolid::AddMatrix(const GsMatrix3x3& mat)
	{
		const UInt32 cm = m_daMatrices.size();
		for (UInt32 im = 0; im < cm; ++im)
		{
			const GsMatrix3x3& m0 = m_daMatrices[im];
			bool same = true;
			for (UInt32 i = 0; i < 9; ++i)
			{
				if (!IsZero(m0.m_Data[i] - mat.m_Data[i]))
				{
					same = false;
					break;
				}
			}
			if (same)
			{
				return im;
			}
		}
		m_daMatrices.emplace_back(mat);
		return cm;
	}

	void CompositeGeoSolid::RegenerateEdges()
	{
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(gst.iv0, gst.iv1, gst.intersectSrc.from, gst.intersectCut.from);
			GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(gst.iv0, gst.iv2, gst.intersectSrc.from, gst.intersectCut.from);
			GeoSolidEdge* gse2 = GeoSolid::obtain<GeoSolidEdge>(gst.iv1, gst.iv2, gst.intersectSrc.from, gst.intersectCut.from);
			gst.ie0 = AddEdge(gse0);
			gst.ie1 = AddEdge(gse1);
			gst.ie2 = AddEdge(gse2);
			GeoSolidEdge::recycle(gse0);
			GeoSolidEdge::recycle(gse1);
			GeoSolidEdge::recycle(gse2);
		}
	}

	void CompositeGeoSolid::SetColor(const ColorQuad& cq)
	{
		GeoSolid::SetColor(cq);
		if (m_daColors.size() > 1)
		{
			//有多个颜色
			return;
		}
		if (m_daColors.empty())
		{
			m_daColors.emplace_back(ColorRGBA32(cq.r, cq.g, cq.b, cq.a));
			return;
		}
		ColorRGBA32& c = m_daColors[0];
		c.Set(cq.r, cq.g, cq.b, cq.a);
	}

	CompositeGeoSolid::WriteOnceBoolean& CompositeGeoSolid::GetWriteOnceBoolean(
		GeoSolidArray<WriteOnceBoolean>& aWobs, const GsIndex& i, const GsIndex& c)
	{
		if (aWobs.size() < c)
		{
			aWobs.resize(c);
		}
		return aWobs[i];
	}

	GeoSolidArray<CompositeGeoSolid::WriteOnceBoolean>& CompositeGeoSolid::GetWriteOnceBooleanArray(
		GeoSolidArray<GeoSolidArray<WriteOnceBoolean>>& ddaWobs, const GsIndex& i, const GsIndex& c)
	{
		if (ddaWobs.size() < c)
		{
			ddaWobs.resize(c);
		}
		GeoSolidArray<WriteOnceBoolean>& aWobs = ddaWobs[i];
		return aWobs;
	}

	CompositeGeoSolid::WriteOnceBoolean& CompositeGeoSolid::GetWriteOnceBoolean(
		GeoSolidArray<GeoSolidArray<WriteOnceBoolean>>& ddaWobs, const GsIndex& r, const GsIndex& c, const GsIndex& cr, const GsIndex& cc)
	{
		GeoSolidArray<WriteOnceBoolean>& aWobs = GetWriteOnceBooleanArray(ddaWobs, r, cr);
		return GetWriteOnceBoolean(aWobs, c, cc);
	}

	void CompositeGeoSolid::ClearWriteOnceBoolean()
	{
		m_aIvsInSrc.clear();
		m_aIvsInCut.clear();
		m_aIesInSrc.clear();
		m_aIesInCut.clear();
		m_ddaIvsInsideIts.clear();
		m_ddaIvsOnIts.clear();
	}

	void CompositeGeoSolid::ReplaceVertexIndex(const GsIv& ivBefore, const GsIv& ivAfter)
	{
		if (ivBefore == ivAfter)
		{
			return;
		}
		//TODO: 2023-03-30 16:13:20: 是否可直接一步减到底？
		const GsIe ce = GetEdgeCount();
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge& gse = GetEdge(ie);
			if (gse.iv0 == ivBefore)
			{
				gse.iv0 = ivAfter;
			}
			if (gse.iv1 == ivBefore)
			{
				gse.iv1 = ivAfter;
			}
			if (gse.iv0 > ivBefore)
			{
				--gse.iv0;
			}
			if (gse.iv1 > ivBefore)
			{
				--gse.iv1;
			}
		}
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.iv0 == ivBefore)
			{
				gst.iv0 = ivAfter;
			}
			if (gst.iv1 == ivBefore)
			{
				gst.iv1 = ivAfter;
			}
			if (gst.iv2 == ivBefore)
			{
				gst.iv2 = ivAfter;
			}
			if (gst.iv0 > ivBefore)
			{
				--gst.iv0;
			}
			if (gst.iv1 > ivBefore)
			{
				--gst.iv1;
			}
			if (gst.iv2 > ivBefore)
			{
				--gst.iv2;
			}
			GeoSolidSet<GsIv>& setNewIvs = gst.GetNewIvs();
			auto itIv = setNewIvs.find(ivBefore);
			if (itIv != setNewIvs.end())
			{
				setNewIvs.erase(itIv);
				setNewIvs.emplace(ivAfter);
			}
			std::unordered_set<GsIv> setIndicesToAdd;
			std::unordered_set<GsIv> setIvsToRemove;
			for (itIv = setNewIvs.begin(); itIv != setNewIvs.end(); ++itIv)
			{
				const GsIv iv = *itIv;
				if (iv > ivBefore)
				{
					setIndicesToAdd.emplace(iv - 1);
					setIvsToRemove.emplace(iv);
				}
			}
			for (itIv = setIvsToRemove.begin(); itIv != setIvsToRemove.end(); ++itIv)
			{
				const GsIv iv = *itIv;
				setNewIvs.erase(iv);
			}
			for (itIv = setIndicesToAdd.begin(); itIv != setIndicesToAdd.end(); ++itIv)
			{
				const GsIv iv = *itIv;
				setNewIvs.emplace(iv);
			}
		}
	}

	void CompositeGeoSolid::ReplaceVerticesIndices(const std::vector<int>& vReplaceIndices)
	{
		const GsIv criv = (GsIv)vReplaceIndices.size();
		const GsIv cv = GetVertexCount();
		const GsIe ce = GetEdgeCount();
		const GsIt ct = GetTriangleCount();
		GeoSolidSet<GsIv> setSwap;
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			const int& iv0 = vReplaceIndices[(GsIndex)gst.iv0];
			const int& iv1 = vReplaceIndices[(GsIndex)gst.iv1];
			const int& iv2 = vReplaceIndices[(GsIndex)gst.iv2];
			if (iv0 < 0 || iv1 < 0 || iv2 < 0)
			{
				continue;
			}
			gst.iv0 = (GsIv)iv0;
			gst.iv1 = (GsIv)iv1;
			gst.iv2 = (GsIv)iv2;
			GeoSolidSet<GsIv>& setNewIvs = gst.GetNewIvs();
			if (setNewIvs.empty())
			{
				continue;
			}
			setSwap.clear();
			setSwap.reserve(setNewIvs.size());
			for (const GsIv& iv : setNewIvs)
			{
				if (iv >= criv)
				{
					continue;
				}
				const int& ivNew = vReplaceIndices[(GsIndex)iv];
				if (ivNew < 0)
				{
					continue;
				}
				setSwap.emplace((GsIv)ivNew);
			}
			setNewIvs.swap(setSwap);
		}
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge& gse = GetEdge(ie);
			const int& iv0 = vReplaceIndices[(GsIndex)gse.iv0];
			const int& iv1 = vReplaceIndices[(GsIndex)gse.iv1];
			if (iv0 < 0 || iv1 < 0)
			{
				continue;
			}
			gse.iv0 = (GsIv)iv0;
			gse.iv1 = (GsIv)iv1;
		}
		UInt32 csm = m_daSubMeshes.size();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			if (gssm.setVertexIndices.empty())
			{
				continue;
			}
			setSwap.clear();
			setSwap.reserve(gssm.setVertexIndices.size());
			for (const GsIv& iv : gssm.setVertexIndices)
			{
				if (iv >= criv)
				{
					continue;
				}
				const int& ivNew = vReplaceIndices[(GsIndex)iv];
				if (ivNew < 0)
				{
					continue;
				}
				setSwap.emplace((GsIv)ivNew);
			}
			gssm.setVertexIndices.swap(setSwap);
			gssm.cpv = gssm.setVertexIndices.size();
		}
	}

	void CompositeGeoSolid::ReplaceUvsIndices(const std::vector<int>& vReplaceIndices)
	{
		const UInt32 c = vReplaceIndices.size();
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			const int iuv0 = vReplaceIndices[gst.iuv0];
			const int iuv1 = vReplaceIndices[gst.iuv1];
			const int iuv2 = vReplaceIndices[gst.iuv2];
			if (iuv0 < 0 || iuv1 < 0 || iuv2 < 0)
			{
				continue;
			}
			gst.iuv0 = iuv0;
			gst.iuv1 = iuv1;
			gst.iuv2 = iuv2;
		}
	}

	void CompositeGeoSolid::ReplaceEdgesIndices(const std::vector<int>& vReplaceIndices)
	{
		if (vReplaceIndices.empty())
		{
			return;
		}
		const GsIe crie = (GsIe)vReplaceIndices.size();
		std::unordered_set<GsIe> setSwap;
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			//gst.ie0 = vReplaceIndices[gst.ie0];
			//gst.ie1 = vReplaceIndices[gst.ie1];
			//gst.ie2 = vReplaceIndices[gst.ie2];
			const int ie0 = vReplaceIndices[(GsIndex)gst.ie0];
			const int ie1 = vReplaceIndices[(GsIndex)gst.ie1];
			const int ie2 = vReplaceIndices[(GsIndex)gst.ie2];
			if (ie0 < 0 || ie1 < 0 || ie2 < 0)
			{
				continue;
			}
			if (ie0 != ie1 && ie0 != ie2 && ie1 != ie2)
			{
				gst.ie0 = (GsIe)ie0;
				gst.ie1 = (GsIe)ie1;
				gst.ie2 = (GsIe)ie2;
			}
			GeoSolidSet<GsIe>& setIesDivider = gst.GetIesDivider();
			if (setIesDivider.empty())
			{
				continue;
			}
			setSwap.clear();
			setSwap.reserve(setIesDivider.size());
			for (const GsIe& ie : setIesDivider)
			{
				if (ie >= crie)
				{
					continue;
				}
				const int& ieNew = vReplaceIndices[(GsIndex)ie];
				if (ieNew < 0)
				{
					continue;
				}
				setSwap.emplace((GsIe)ieNew);
			}
			setIesDivider.swap(setSwap);
		}
	}

	void CompositeGeoSolid::ReplaceTrianglesIndices(const std::vector<int>& vReplaceIndices)
	{
		if (vReplaceIndices.empty())
		{
			return;
		}
		const GsIt c = (GsIt)vReplaceIndices.size();
		GeoSolidSet<GsIt> setSwap;
		UInt32 csm = m_daSubMeshes.size();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			if (gssm.setTriangleIndices.empty())
			{
				continue;
			}
			setSwap.clear();
			for (const GsIt& it : gssm.setTriangleIndices)
			{
				if (it >= c)
				{
					continue;
				}
				const int& itNew = vReplaceIndices[(GsIndex)it];
				if (itNew < 0)
				{
					continue;
				}
				setSwap.emplace((GsIt)itNew);
			}
			gssm.setTriangleIndices.swap(setSwap);
		}
	}

	void CompositeGeoSolid::ExtendDivisionEdgeToSide(const GsIt& it)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		GeoSolidSet<GsIe>& setIesDivider = gst.GetIesDivider();
		if (setIesDivider.empty())
		{
			return;
		}
		std::list<GsIe> listIes;
		listIes.insert(listIes.begin(), setIesDivider.begin(), setIesDivider.end());
		std::list<GsIv> listIvs;
		//TODO：多条线路，深度遍历？
		while (!listIes.empty())
		{
			const GsIe ieFirst = listIes.front();
			listIes.pop_front();
			const GeoSolidEdge& gseFirst = GetEdge(ieFirst);
			listIvs.emplace_back(gseFirst.iv0);
			listIvs.emplace_back(gseFirst.iv1);
			if (listIes.empty())
			{
				break;
			}
			GsIv ivFront = listIvs.front();
			GsIv ivBack = listIvs.back();
			for (auto itIe = listIes.begin(); itIe != listIes.end(); )
			{
				const GsIe ie = *itIe;
				const GeoSolidEdge& gse = GetEdge(ie);
				if ((gse.iv0 == ivFront && gse.iv1 == ivBack) ||
					(gse.iv1 == ivFront && gse.iv0 == ivBack))
				{
					listIes.erase(itIe);
					ivFront = ivBack;
					break;
				}
				if (gse.iv0 == ivFront || gse.iv1 == ivFront)
				{
					if (gse.iv0 == ivFront)
					{
						listIvs.emplace_front(gse.iv1);
						ivFront = gse.iv1;
					}
					else
					{
						listIvs.emplace_front(gse.iv0);
						ivFront = gse.iv0;
					}
					listIes.erase(itIe);
					itIe = listIes.begin();
					continue;
				}
				if (gse.iv0 == ivBack || gse.iv1 == ivBack)
				{
					if (gse.iv0 == ivBack)
					{
						listIvs.emplace_back(gse.iv1);
						ivBack = gse.iv1;
					}
					else
					{
						listIvs.emplace_back(gse.iv0);
						ivBack = gse.iv0;
					}
					listIes.erase(itIe);
					itIe = listIes.begin();
					continue;
				}
				if (ivFront == ivBack)
				{
					break;
				}
				++itIe;
			}
			if (ivFront == ivBack)
			{
				//找到一串首尾相接的线段
				listIvs.clear();
				continue;
			}
			const bool isFrontInEdge = IsVertexInTriangleEdge(gst, ivFront);
			const bool isBackInEdge = IsVertexInTriangleEdge(gst, ivBack);
			if (isFrontInEdge && isBackInEdge)
			{
				//找到一串有出入的线段
				listIvs.clear();
				continue;
			}
			const GsVector3& va = GetVertex(gst.iv0).v;
			const GsVector3& vb = GetVertex(gst.iv1).v;
			const GsVector3& vc = GetVertex(gst.iv2).v;
			while (!isFrontInEdge)
			{
				auto itIv = std::next(listIvs.begin());
				const GsIv& iv0 = *itIv;
				const GsIv& iv1 = ivFront;
				const GsVector3& v0 = GetVertex(iv0).v;
				const GsVector3& v1 = GetVertex(iv1).v;
				const GsVector3 v01 = v1 - v0;
				const GsRay ray(v0, v1.GetNormalizedSafe());
				GsDigit timeRay;
				bool intersectEdges = IntersectRayTriangleEdges2(ray, va, vb, vc, timeRay);
				if (!intersectEdges)
				{
					break;
				}
				GsVector3 vp = ray.GetOrigin() + timeRay * ray.GetDirection();
				if (IsZero(timeRay))
				{
					break;
				}
				GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
				if (gst.intersectSrc.from)
				{
					gsv->intersectSrc.inside = true;
					gsv->intersectSrc.hasCheckedInside = true;
				}
				if (gst.intersectCut.from)
				{
					gsv->intersectCut.inside = true;
					gsv->intersectCut.hasCheckedInside = true;
				}
				const GsIv ivp = AddVertex(gsv);
				GeoSolidVertex::recycle(gsv);
				AddVertexToTriangle(ivp, it);
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(iv1, ivp, true, true);
				const GsIe ie = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
				AddEdgeIntoTriangle(ie, it);
				break;
			}
			while (!isBackInEdge)
			{
				auto itIv = std::prev(listIvs.end(), 2);
				const GsIv& iv0 = *itIv;
				const GsIv& iv1 = ivBack;
				const GsVector3& v0 = GetVertex(iv0).v;
				const GsVector3& v1 = GetVertex(iv1).v;
				const GsVector3 v01 = v1 - v0;
				const GsRay ray(v0, v1.GetNormalizedSafe());
				GsDigit timeRay;
				bool intersectEdges = IntersectRayTriangleEdges2(ray, va, vb, vc, timeRay);
				if (!intersectEdges)
				{
					break;
				}
				GsVector3 vp = ray.GetOrigin() + timeRay * ray.GetDirection();
				if (IsZero(timeRay))
				{
					break;
				}
				GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
				if (gst.intersectSrc.from)
				{
					gsv->intersectSrc.inside = true;
					gsv->intersectSrc.hasCheckedInside = true;
				}
				if (gst.intersectCut.from)
				{
					gsv->intersectCut.inside = true;
					gsv->intersectCut.hasCheckedInside = true;
				}
				const GsIv ivp = AddVertex(gsv);
				GeoSolidVertex::recycle(gsv);
				AddVertexToTriangle(ivp, it);
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(iv1, ivp, true, true);
				const GsIe ie = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
				AddEdgeIntoTriangle(ie, it);
				break;
			}
		}
	}

	void CompositeGeoSolid::AddNeighbor(const GsIv& iv, const GsIv& ivNeighbor, const GsDigit& distance)
	{
		GeoSolidVertex& gsv = GetVertex(iv);
		bool found = false;
		for (auto itNeighbor = gsv.listNeighbors.begin(); itNeighbor != gsv.listNeighbors.end(); ++itNeighbor)
		{
			if (itNeighbor->iv == ivNeighbor)
			{
				found = true;
				break;
			}
		}
		if (found)
		{
			return;
		}
		GeoSolidVertex& gsvNeighbor = GetVertex(ivNeighbor);
		auto itNeighbor = gsv.listNeighbors.begin();
		for (; itNeighbor != gsv.listNeighbors.end(); ++itNeighbor)
		{
			if (itNeighbor->d > distance)
			{
				break;
			}
		}
		GeoSolidVertexNeighbor gsvn;
		gsvn.iv = ivNeighbor;
		gsvn.d = distance;
		gsv.listNeighbors.emplace(itNeighbor, gsvn);
	}

	void CompositeGeoSolid::AddNeighborsToEach(const GsIe& ie)
	{
		GeoSolidEdge& gse = GetEdge(ie);
		GeoSolidVertex& gsv0 = GetVertex(gse.iv0);
		GeoSolidVertex& gsv1 = GetVertex(gse.iv1);
		const GsDigit distance = Distance(gsv0.v, gsv1.v);
		AddNeighbor(gse.iv0, gse.iv1, distance);
		AddNeighbor(gse.iv1, gse.iv0, distance);
	}

	void CompositeGeoSolid::DivideEdge(const GsIe& ie)
	{
		GeoSolidEdge& gse = GetEdge(ie);
		GeoSolidVertex& gsv0 = GetVertex(gse.iv0);
		GeoSolidVertex& gsv1 = GetVertex(gse.iv1);
		for (auto itNeighbor = gsv0.listNeighbors.begin(); itNeighbor != gsv0.listNeighbors.end(); ++itNeighbor)
		{
			if (itNeighbor->iv == gse.iv1)
			{
				gsv0.listNeighbors.erase(itNeighbor);
				break;
			}
		}
		for (auto itNeighbor = gsv1.listNeighbors.begin(); itNeighbor != gsv1.listNeighbors.end(); ++itNeighbor)
		{
			if (itNeighbor->iv == gse.iv0)
			{
				gsv1.listNeighbors.erase(itNeighbor);
				break;
			}
		}
	}

	bool CompositeGeoSolid::DivideEdgeByVertex(const GsIe& ie, const GsIv& iv, GsIe& ie0, GsIe& ie1)
	{
		if (!IsVertexInEdge(iv, ie))
		{
			return false;
		}
		GeoSolidEdge& gse = GetEdge(ie);
		GeoSolidVertex& gsv = GetVertex(iv);
		GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(iv, gse.iv0, gse.intersectSrc.from, gse.intersectCut.from);
		GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(iv, gse.iv1, gse.intersectSrc.from, gse.intersectCut.from);
		ie0 = AddEdge(gse0);
		ie1 = AddEdge(gse1);
		GeoSolidEdge::recycle(gse0);
		GeoSolidEdge::recycle(gse1);
		DivideEdge(ie);
		return true;
	}

	void CompositeGeoSolid::DivideTriangle(const GsIt& itMain)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		if (setNewIvs.empty())
		{
			gstMain.markRemoval = false;
			if (gstMain.intersectSrc.from)
			{
				WhetherTriangleIntersectGeoSolid(itMain, false);
				if (!gstMain.intersectSrc.hasCheckedInside)
				{
					gstMain.intersectSrc.inside = IsTriangleInGeoSolid(gstMain, true);
					gstMain.intersectSrc.hasCheckedInside = true;
				}
			}
			else if (gstMain.intersectCut.from)
			{
				WhetherTriangleIntersectGeoSolid(itMain, true);
				if (!gstMain.intersectCut.hasCheckedInside)
				{
					gstMain.intersectCut.inside = IsTriangleInGeoSolid(gstMain, false);
					gstMain.intersectCut.hasCheckedInside = true;
				}
			}
			return;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Before division:");
			PrintBeforeDivision(itMain);
		}
		#endif
		//从大三角形分割后的三角形集合
		std::list<GsIt> listItsResult;
		//DivideTriangleByVertices(itMain, listItsResult);
		DivideTriangleByMostCentralVertex(itMain, listItsResult);
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Before segment:");
			PrintAfterDivision(itMain, listItsResult);
		}
		#endif
		DivideTriangleByEdges(itMain, listItsResult);
		GeoSolidSet<GsIt>& setCoincidentIts = gstMain.GetCoincidentIts();
		if (!setCoincidentIts.empty())
		{
			for (auto itIt = listItsResult.begin(); itIt != listItsResult.end(); ++itIt)
			{
				const GsIt it = *itIt;
				GeoSolidTriangle& gst = GetTriangle(it);
				//for (auto itItCo = setCoincidentIts.begin(); itItCo != setCoincidentIts.end(); ++itItCo)
				for (const GsIt& itCo : setCoincidentIts)
				{
					//const GsIt itCo = *itItCo;
					if (itCo <= itMain)
					{
						continue;
					}
					AddEdgeIntoTriangle(gst.ie0, itCo);
					AddEdgeIntoTriangle(gst.ie1, itCo);
					AddEdgeIntoTriangle(gst.ie2, itCo);
				}
			}
		}

		for (auto itIt = listItsResult.begin(); itIt != listItsResult.end(); ++itIt)
		{
			const GsIt it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.intersectSrc.from)
			{
				WhetherTriangleIntersectGeoSolid(it, false);
				if (!gst.intersectSrc.hasCheckedInside)
				{
					gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, true);
					gst.intersectSrc.hasCheckedInside = true;
				}
			}
			else if (gst.intersectCut.from)
			{
				WhetherTriangleIntersectGeoSolid(it, true);
				if (!gst.intersectCut.hasCheckedInside)
				{
					gst.intersectCut.inside = IsTriangleInGeoSolid(gst, false);
					gst.intersectCut.hasCheckedInside = true;
				}
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			PrintAfterDivision(itMain, listItsResult);
			WarningString("-------------------------DivideTriangle-------------------------");
		}
		#endif
	}

	void CompositeGeoSolid::DivideTriangleByVertices(const GsIt& itMain, std::list<GsIt>& listItsResult)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		listItsResult.emplace_back(itMain);
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		for (auto itIv = setNewIvs.begin(); itIv != setNewIvs.end(); ++itIv)
		{
			std::list<GsIt> listItsToAdd;
			for (auto itIt = listItsResult.begin(); itIt != listItsResult.end(); )
			{
				const bool divide = DivideTriangleByVertex(*itIt, *itIv, listItsToAdd);
				if (divide)
				{
					itIt = listItsResult.erase(itIt);
				}
				else
				{
					++itIt;
				}
			}
			listItsResult.insert(listItsResult.end(), listItsToAdd.begin(), listItsToAdd.end());
		}
	}

	bool CompositeGeoSolid::DivideTriangleByMostCentralVertex(const GsIt& itMain, std::list<GsIt>& listItsResult)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		if (gstMain.markRemoval)
		{
			return true;
		}
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		if (setNewIvs.empty())
		{
			return false;
		}
		std::unordered_set<GsIv> setIvs(setNewIvs);
		const GsVector3& v0 = GetVertex(gstMain.iv0).v;
		const GsVector3& v1 = GetVertex(gstMain.iv1).v;
		const GsVector3& v2 = GetVertex(gstMain.iv2).v;
		const GsVector3 vbc = (v0 + v1 + v2) / 3.0;
		std::list<GsIt> listItsDivided;
		bool divide;
		do
		{
			GsIv ivMostCentral = *setIvs.begin();
			const GsVector3& vMostCentral = GetVertex(ivMostCentral).v;
			GsDigit minD = Distance(vbc, vMostCentral);
			if (setIvs.size() >= 2)
			{
				for (auto itIv = std::next(setIvs.begin()); itIv != setIvs.end(); ++itIv)
				{
					const GsIv& iv = *itIv;
					const GsVector3& v = GetVertex(iv).v;
					const GsDigit d = Distance(vbc, v);
					if (d < minD)
					{
						ivMostCentral = iv;
						minD = d;
					}
				}
			}
			listItsDivided.clear();
			divide = DivideTriangleByVertex(itMain, ivMostCentral, listItsDivided);
			setIvs.erase(ivMostCentral);
			if (divide)
			{
				break;
			}
		} while (!setIvs.empty());
		if (!divide)
		{
			return false;
		}
		for (auto itIt = listItsDivided.begin(); itIt != listItsDivided.end(); )
		{
			const GsIt& it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.markRemoval)
			{
				itIt = listItsDivided.erase(itIt);
				continue;
			}
			for (auto itIv = setIvs.begin(); itIv != setIvs.end(); ++itIv)
			{
				const GsIv& iv = *itIv;
				GsIe ieInEdge = GsIe(-1);
				const bool inside = IsVertexInsideTriangle(iv, it, &ieInEdge);
				const bool isInEdgeAB = ieInEdge == gst.ie0;
				const bool isInEdgeAC = ieInEdge == gst.ie1;
				const bool isInEdgeBC = ieInEdge == gst.ie2;
				const bool isInEdge = isInEdgeAB || isInEdgeAC || isInEdgeBC;
				if (!inside && !isInEdge)
				{
					continue;
				}
				gst.AddNewIv(iv);
			}
			++itIt;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			PrintAfterDivision(itMain, listItsDivided);
			WarningString("---------------");
		}
		#endif
		std::list<GsIt> listItsToAdd;
		for (auto itIt = listItsDivided.begin(); itIt != listItsDivided.end();)
		{
			const GsIt& it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			const bool divide = DivideTriangleByMostCentralVertex(it, listItsToAdd);
			if (divide)
			{
				itIt = listItsDivided.erase(itIt);
			}
			else
			{
				++itIt;
			}
		}
		listItsResult.insert(listItsResult.end(), listItsDivided.begin(), listItsDivided.end());
		listItsResult.insert(listItsResult.end(), listItsToAdd.begin(), listItsToAdd.end());
		return true;
	}

	bool CompositeGeoSolid::DivideTriangleByVertex(const GsIt& it, const GsIv& ivx, std::list<GsIt>& listItsResult)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		const GsIv iva = gst.iv0;
		const GsIv ivb = gst.iv1;
		const GsIv ivc = gst.iv2;
		const bool isCoincidedWithA = ivx == iva;
		const bool isCoincidedWithB = ivx == ivb;
		const bool isCoincidedWithC = ivx == ivc;
		if (isCoincidedWithA || isCoincidedWithB || isCoincidedWithC)
		{
			return false;
		}
		GsIe ieInEdge = GsIe(-1);
		const bool inside = IsVertexInsideTriangle(ivx, it, &ieInEdge);
		const bool isInEdgeAB = ieInEdge == gst.ie0;
		const bool isInEdgeAC = ieInEdge == gst.ie1;
		const bool isInEdgeBC = ieInEdge == gst.ie2;
		const bool isInEdge = isInEdgeAB || isInEdgeAC || isInEdgeBC;
		if (!inside && !isInEdge)
		{
			return false;
		}
		GeoSolidVertex& gsvX = GetVertex(ivx);
		const GeoSolidVertex& gsvA = GetVertex(iva);
		const GeoSolidVertex& gsvB = GetVertex(ivb);
		const GeoSolidVertex& gsvC = GetVertex(ivc);
		GsVector2 v2a;
		GsVector2 v2b;
		GsVector2 v2c;
		GsVector2 v2x;
		if (gst.intersectSrc.from)
		{
			v2a.Set(gsvA.vInSrc[gst.uIndex], gsvA.vInSrc[gst.vIndex]);
			v2b.Set(gsvB.vInSrc[gst.uIndex], gsvB.vInSrc[gst.vIndex]);
			v2c.Set(gsvC.vInSrc[gst.uIndex], gsvC.vInSrc[gst.vIndex]);
			v2x.Set(gsvX.vInSrc[gst.uIndex], gsvX.vInSrc[gst.vIndex]);
		}
		else
		{
			v2a.Set(gsvA.vInCut[gst.uIndex], gsvA.vInCut[gst.vIndex]);
			v2b.Set(gsvB.vInCut[gst.uIndex], gsvB.vInCut[gst.vIndex]);
			v2c.Set(gsvC.vInCut[gst.uIndex], gsvC.vInCut[gst.vIndex]);
			v2x.Set(gsvX.vInCut[gst.uIndex], gsvX.vInCut[gst.vIndex]);
		}
		if (isInEdge)
		{
			GeoSolidTriangle* gst0 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
			GeoSolidTriangle* gst1 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
			const GsVector2& uv0 = GetUv(gst.iuv0);
			const GsVector2& uv1 = GetUv(gst.iuv1);
			const GsVector2& uv2 = GetUv(gst.iuv2);
			if (isInEdgeAB)
			{
				gst0->iv0 = iva; gst0->iv1 = ivx; gst0->iv2 = ivc;
				gst1->iv0 = ivc; gst1->iv1 = ivx; gst1->iv2 = ivb;
				const GsVector2 v2ax = v2x - v2a;
				const GsVector2 v2ab = v2b - v2a;
				const GsVector2 uvx(
					IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x,
					IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y
				);
				const UInt32 iuvx = AddUv(uvx);
				gst0->iuv0 = gst.iuv0; gst0->iuv1 = iuvx; gst0->iuv2 = gst.iuv2;
				gst1->iuv0 = gst.iuv2; gst1->iuv1 = iuvx; gst1->iuv2 = gst.iuv1;
			}
			else if (isInEdgeAC)
			{
				gst0->iv0 = iva; gst0->iv1 = ivb; gst0->iv2 = ivx;
				gst1->iv0 = ivc; gst1->iv1 = ivx; gst1->iv2 = ivb;
				const GsVector2 v2ax = v2x - v2a;
				const GsVector2 v2ac = v2c - v2a;
				const GsVector2 uvx(
					IsZero(v2ac.x) ? uv0.x : uv0.x + (uv2.x - uv0.x) * v2ax.x / v2ac.x,
					IsZero(v2ac.y) ? uv0.y : uv0.y + (uv2.y - uv0.y) * v2ax.y / v2ac.y
				);
				const UInt32 iuvx = AddUv(uvx);
				gst0->iuv0 = gst.iuv0;
				gst0->iuv1 = gst.iuv1;
				gst0->iuv2 = iuvx;
				gst1->iuv0 = gst.iuv2;
				gst1->iuv1 = iuvx;
				gst1->iuv2 = gst.iuv1;
			}
			else if (isInEdgeBC)
			{
				gst0->iv0 = iva; gst0->iv1 = ivb; gst0->iv2 = ivx;
				gst1->iv0 = iva; gst1->iv1 = ivx; gst1->iv2 = ivc;
				const GsVector2 v2bx = v2x - v2b;
				const GsVector2 v2bc = v2c - v2b;
				const GsVector2 uvx(
					IsZero(v2bc.x) ? uv1.x : uv1.x + (uv2.x - uv1.x) * v2bx.x / v2bc.x,
					IsZero(v2bc.y) ? uv1.y : uv1.y + (uv2.y - uv1.y) * v2bx.y / v2bc.y
				);
				const UInt32 iuvx = AddUv(uvx);
				gst0->iuv0 = gst.iuv0;
				gst0->iuv1 = gst.iuv1;
				gst0->iuv2 = iuvx;
				gst1->iuv0 = gst.iuv0;
				gst1->iuv1 = iuvx;
				gst1->iuv2 = gst.iuv2;
			}
			gst.markRemoval = true;
			if (gst0->IsValid())
			{
				const GsIt ot0 = AddTriangle(gst0);
				listItsResult.emplace_back(ot0);
			}
			if (gst1->IsValid())
			{
				const GsIt ot1 = AddTriangle(gst1);
				listItsResult.emplace_back(ot1);
			}
		}
		else
		{
			GeoSolidTriangle* gst0 = GeoSolid::obtain<GeoSolidTriangle>(iva, ivb, ivx, gst);
			GeoSolidTriangle* gst1 = GeoSolid::obtain<GeoSolidTriangle>(iva, ivx, ivc, gst);
			GeoSolidTriangle* gst2 = GeoSolid::obtain<GeoSolidTriangle>(ivc, ivx, ivb, gst);
			const GsVector2 v2ax = v2x - v2a;
			const GsVector2 v2bx = v2x - v2b;
			const GsVector2 v2ab = v2b - v2a;
			const GsVector2 v2ac = v2c - v2a;
			const GsVector2 v2bc = v2c - v2b;
			const GsVector2& uv0 = GetUv(gst.iuv0);
			const GsVector2& uv1 = GetUv(gst.iuv1);
			const GsVector2& uv2 = GetUv(gst.iuv2);
			GsVector2 uvx;
			if (!IsZero(v2ab.x) && !IsZero(uv1.x - uv0.x))
			{
				uvx.x = IsZero(v2ax.x - v2ab.x) ? uv1.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x;
			}
			else if (!IsZero(v2ac.x) && !IsZero(uv2.x - uv0.x))
			{
				uvx.x = IsZero(v2ax.x - v2ac.x) ? uv2.x : uv0.x + (uv2.x - uv0.x) * v2ax.x / v2ac.x;
			}
			else if (!IsZero(v2bc.x) && !IsZero(uv2.x - uv1.x))
			{
				uvx.x = IsZero(v2bx.x - v2bc.x) ? uv2.x : uv1.x + (uv2.x - uv1.x) * v2bx.x / v2bc.x;
			}
			else
			{
				uvx.x = IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x;
			}
			if (!IsZero(v2ab.y) && !IsZero(uv1.y - uv0.y))
			{
				uvx.y = IsZero(v2ax.y - v2ab.y) ? uv1.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y;
			}
			else if (!IsZero(v2ac.y) && !IsZero(uv2.y - uv0.y))
			{
				uvx.y = IsZero(v2ax.y - v2ac.y) ? uv2.y : uv0.y + (uv2.y - uv0.y) * v2ax.y / v2ac.y;
			}
			else if (!IsZero(v2bc.y) && !IsZero(uv2.y - uv1.y))
			{
				uvx.y = IsZero(v2bx.y - v2bc.y) ? uv2.y : uv1.y + (uv2.y - uv1.y) * v2bx.y / v2bc.y;
			}
			else
			{
				uvx.y = IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y;
			}
			const UInt32 iuvx = AddUv(uvx);
			gst0->iuv0 = gst.iuv0; gst0->iuv1 = gst.iuv1; gst0->iuv2 = iuvx;
			gst1->iuv0 = gst.iuv0; gst1->iuv1 = iuvx; gst1->iuv2 = gst.iuv2;
			gst2->iuv0 = gst.iuv2; gst2->iuv1 = iuvx; gst2->iuv2 = gst.iuv1;
			gst.markRemoval = true;
			if (gst0->IsValid())
			{
				const GsIt ot0 = AddTriangle(gst0);
				listItsResult.emplace_back(ot0);
			}
			if (gst1->IsValid())
			{
				const GsIt ot1 = AddTriangle(gst1);
				listItsResult.emplace_back(ot1);
			}
			if (gst2->IsValid())
			{
				const GsIt ot2 = AddTriangle(gst2);
				listItsResult.emplace_back(ot2);
			}
		}
		return true;
	}

	void CompositeGeoSolid::DivideTriangleByEdges(const GsIt& itMain, std::list<GsIt>& listItsResult)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Before segmenting triangles by edges:");
			WarningStringMsg("listItsResult.size() = %u", listItsResult.size());
			WarningStringMsg("gstMain.setDivisionEdgeIndices.size() = %u", setIesDivider.size());
		}
		#endif
		//遍历中无法增删函数，因此使用两个数组来记录需要增删的元素
		std::list<GsIt> listItsToAdd;
		std::list<GsIt> listItsToRemove;
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		//用这些新增的线段，将小三角形再进行分割
		for (auto itIe = setIesDivider.begin(); itIe != setIesDivider.end(); ++itIe)
		{
			const GsIe ie = *itIe;
			const GeoSolidEdge& gse = GetEdge(ie);
			const GsIv ivp = gse.iv0;
			const GsIv ivq = gse.iv1;
			const GsVector3& vp = GetVertex(ivp).v;
			const GsVector3& vq = GetVertex(ivq).v;
			listItsToRemove.clear();
			listItsToAdd.clear();
			for (auto itIt = listItsResult.begin(); itIt != listItsResult.end(); ++itIt)
			{
				const GsIt it = *itIt;
				GeoSolidTriangle& gst = GetTriangle(it);
				if (ie == gst.ie0 || ie == gst.ie1 || ie == gst.ie2)
				{
					continue;
				}
				const GsIv iva = gst.iv0;
				const GsIv ivb = gst.iv1;
				const GsIv ivc = gst.iv2;
				const GeoSolidVertex& gsvA = GetVertex(iva);
				const GeoSolidVertex& gsvB = GetVertex(ivb);
				const GeoSolidVertex& gsvC = GetVertex(ivc);
				const GsVector3& va = gsvA.v;
				const GsVector3& vb = gsvB.v;
				const GsVector3& vc = gsvC.v;
				GsVector3 vOfPQAndAB;
				GsVector3 vOfPQAndAC;
				GsVector3 vOfPQAndBC;
				bool crossAB = WhetherLineIntersectLine(vp, vq, va, vb, &vOfPQAndAB);
				bool crossAC = WhetherLineIntersectLine(vp, vq, va, vc, &vOfPQAndAC);
				bool crossBC = WhetherLineIntersectLine(vp, vq, vb, vc, &vOfPQAndBC);
				int crossCount = 0;
				crossCount += crossAB ? 1 : 0;
				crossCount += crossAC ? 1 : 0;
				crossCount += crossBC ? 1 : 0;
				if (crossCount <= 0)
				{
					continue;
				}
				GsVector2 v2a;
				GsVector2 v2b;
				GsVector2 v2c;
				if (gstMain.intersectSrc.from)
				{
					v2a.Set(gsvA.vInSrc[gst.uIndex], gsvA.vInSrc[gst.vIndex]);
					v2b.Set(gsvB.vInSrc[gst.uIndex], gsvB.vInSrc[gst.vIndex]);
					v2c.Set(gsvC.vInSrc[gst.uIndex], gsvC.vInSrc[gst.vIndex]);
				}
				else
				{
					v2a.Set(gsvA.vInCut[gst.uIndex], gsvA.vInCut[gst.vIndex]);
					v2b.Set(gsvB.vInCut[gst.uIndex], gsvB.vInCut[gst.vIndex]);
					v2c.Set(gsvC.vInCut[gst.uIndex], gsvC.vInCut[gst.vIndex]);
				}
				if (crossCount == 1)
				{
					GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(
						GsVector3::zero,
						gstMain.intersectSrc.from, gstMain.intersectCut.from
					);
					if (gstMain.intersectSrc.from)
					{
						gsv->intersectSrc.inside = true;
						gsv->intersectSrc.hasCheckedInside = true;
					}
					if (gstMain.intersectCut.from)
					{
						gsv->intersectCut.inside = true;
						gsv->intersectCut.hasCheckedInside = true;
					}
					//线段必定穿过这条边对面的顶点
					if (crossAB)
					{
						gsv->v = vOfPQAndAB;
					}
					else if (crossAC)
					{
						gsv->v = vOfPQAndAC;
					}
					else if (crossBC)
					{
						gsv->v = vOfPQAndBC;
					}
					else
					{
						GeoSolidVertex::recycle(gsv);
						continue;
					}
					GsIv ivx = AddVertex(gsv);
					GeoSolidVertex::recycle(gsv);
					if (ivx == iva || ivx == ivb || ivx == ivc)
					{
						continue;
					}
					GeoSolidVertex& gsvX = GetVertex(ivx);
					GsVector2 v2x;
					if (gstMain.intersectSrc.from)
					{
						v2x.Set(gsvX.vInSrc[gst.uIndex], gsvX.vInSrc[gst.vIndex]);
					}
					else
					{
						v2x.Set(gsvX.vInCut[gst.uIndex], gsvX.vInCut[gst.vIndex]);
					}
					setNewIvs.emplace(ivx);
					GeoSolidTriangle* gst0 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
					GeoSolidTriangle* gst1 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
					const GsVector2 v2ax = v2x - v2a;
					const GsVector2 v2bx = v2x - v2b;
					const GsVector2 v2ab = v2b - v2a;
					const GsVector2 v2ac = v2c - v2a;
					const GsVector2 v2bc = v2c - v2b;
					const GsVector2& uv0 = GetUv(gst.iuv0);
					const GsVector2& uv1 = GetUv(gst.iuv1);
					const GsVector2& uv2 = GetUv(gst.iuv2);
					if (crossAB)
					{
						gst0->iv0 = iva; gst0->iv1 = ivx; gst0->iv2 = ivc;
						gst1->iv0 = ivc; gst1->iv1 = ivx; gst1->iv2 = ivb;
						const GsVector2 uvx(
							IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ax.x / v2ab.x,
							IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ax.y / v2ab.y
						);
						const UInt32 iuvx = AddUv(uvx);
						gst0->iuv0 = gst.iuv0; gst0->iuv1 = iuvx; gst0->iuv2 = gst.iuv2;
						gst1->iuv0 = gst.iuv2; gst1->iuv1 = iuvx; gst1->iuv2 = gst.iuv1;
					}
					else if (crossAC)
					{
						gst0->iv0 = iva; gst0->iv1 = ivb; gst0->iv2 = ivx;
						gst1->iv0 = ivb; gst1->iv1 = ivc; gst1->iv2 = ivx;
						const GsVector2 uvx(
							IsZero(v2ac.x) ? uv0.x : uv0.x + (uv2.x - uv0.x) * v2ax.x / v2ac.x,
							IsZero(v2ac.y) ? uv0.y : uv0.y + (uv2.y - uv0.y) * v2ax.y / v2ac.y
						);
						const UInt32 iuvx = AddUv(uvx);
						gst0->iuv0 = gst.iuv0; gst0->iuv1 = gst.iuv1; gst0->iuv2 = iuvx;
						gst1->iuv0 = gst.iuv1; gst1->iuv1 = gst.iuv2; gst1->iuv2 = iuvx;
					}
					else if (crossBC)
					{
						gst0->iv0 = iva; gst0->iv1 = ivb; gst0->iv2 = ivx;
						gst1->iv0 = iva; gst1->iv1 = ivx; gst1->iv2 = ivc;
						const GsVector2 uvx(
							IsZero(v2bc.x) ? uv1.x : uv1.x + (uv2.x - uv1.x) * v2bx.x / v2bc.x,
							IsZero(v2bc.y) ? uv1.y : uv1.y + (uv2.y - uv1.y) * v2bx.y / v2bc.y
						);
						const UInt32 iuvx = AddUv(uvx);
						gst0->iuv0 = gst.iuv0;
						gst0->iuv1 = gst.iuv1;
						gst0->iuv2 = iuvx;
						gst1->iuv0 = gst.iuv0;
						gst1->iuv1 = iuvx;
						gst1->iuv2 = gst.iuv2;
					}
					else
					{
						continue;
					}
					gst.markRemoval = true;
					if (gst0->IsValid())
					{
						const GsIt ot0 = AddTriangle(gst0);
						listItsToAdd.emplace_back(ot0);
					}
					if (gst1->IsValid())
					{
						const GsIt ot1 = AddTriangle(gst1);
						listItsToAdd.emplace_back(ot1);
					}
					listItsToRemove.emplace_back(it);
				}
				else if (crossCount == 2)
				{
					GeoSolidVertex* gsv0 = GeoSolid::obtain<GeoSolidVertex>(
						GsVector3::zero,
						gstMain.intersectSrc.from, gstMain.intersectCut.from
					);
					GeoSolidVertex* gsv1 = GeoSolid::obtain<GeoSolidVertex>(
						GsVector3::zero,
						gstMain.intersectSrc.from, gstMain.intersectCut.from
					);
					if (gstMain.intersectSrc.from)
					{
						gsv0->intersectSrc.inside = true;
						gsv0->intersectSrc.hasCheckedInside = true;
						gsv1->intersectSrc.inside = true;
						gsv1->intersectSrc.hasCheckedInside = true;
					}
					if (gstMain.intersectCut.from)
					{
						gsv0->intersectCut.inside = true;
						gsv0->intersectCut.hasCheckedInside = true;
						gsv1->intersectCut.inside = true;
						gsv1->intersectCut.hasCheckedInside = true;
					}
					//线段必定穿过这条边对面的顶点
					if (crossAB && crossAC)
					{
						gsv0->v = vOfPQAndAB;
						gsv1->v = vOfPQAndAC;
					}
					else if (crossAB && crossBC)
					{
						gsv0->v = vOfPQAndAB;
						gsv1->v = vOfPQAndBC;
					}
					else if (crossAC && crossBC)
					{
						gsv0->v = vOfPQAndAC;
						gsv1->v = vOfPQAndBC;
					}
					else
					{
						GeoSolidVertex::recycle(gsv0);
						GeoSolidVertex::recycle(gsv1);
						continue;
					}
					GsDigit error = 1e-2;
					GsIv ivg = AddVertex(gsv0, error);
					GsIv ivh = AddVertex(gsv1, error);
					GeoSolidVertex::recycle(gsv0);
					GeoSolidVertex::recycle(gsv1);
					if (ivg == ivh)
					{
						continue;
					}
					setNewIvs.emplace(ivg);
					setNewIvs.emplace(ivh);
					GeoSolidVertex& gsvG = GetVertex(ivg);
					GeoSolidVertex& gsvH = GetVertex(ivh);
					GsVector2 v2g;
					GsVector2 v2h;
					if (gstMain.intersectSrc.from)
					{
						v2g.Set(gsvG.vInSrc[gst.uIndex], gsvG.vInSrc[gst.vIndex]);
						v2h.Set(gsvH.vInSrc[gst.uIndex], gsvH.vInSrc[gst.vIndex]);
					}
					else
					{
						v2g.Set(gsvG.vInCut[gst.uIndex], gsvG.vInCut[gst.vIndex]);
						v2h.Set(gsvH.vInCut[gst.uIndex], gsvH.vInCut[gst.vIndex]);
					}
					GeoSolidTriangle* gst0 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
					GeoSolidTriangle* gst1 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
					GeoSolidTriangle* gst2 = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gst);
					const GsVector2& uv0 = GetUv(gst.iuv0);
					const GsVector2& uv1 = GetUv(gst.iuv1);
					const GsVector2& uv2 = GetUv(gst.iuv2);
					if (crossAB && crossAC)
					{
						gst0->iv0 = iva; gst0->iv1 = ivg; gst0->iv2 = ivh;
						gst1->iv0 = ivc; gst1->iv1 = ivg; gst1->iv2 = ivb;
						gst2->iv0 = ivc; gst2->iv1 = ivh; gst2->iv2 = ivg;
						const GsVector2 v2ab = v2b - v2a;
						const GsVector2 v2ac = v2c - v2a;
						const GsVector2 v2ag = v2g - v2a;
						const GsVector2 v2ah = v2h - v2a;
						const GsVector2 uvg(
							IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ag.x / v2ab.x,
							IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ag.y / v2ab.y
						);
						const GsVector2 uvh(
							IsZero(v2ac.x) ? uv0.x : uv0.x + (uv2.x - uv0.x) * v2ah.x / v2ac.x,
							IsZero(v2ac.y) ? uv0.y : uv0.y + (uv2.y - uv0.y) * v2ah.y / v2ac.y
						);
						const UInt32 iuvg = AddUv(uvg);
						const UInt32 iuvh = AddUv(uvh);
						gst0->iuv0 = gst.iuv0; gst0->iuv1 = iuvg; gst0->iuv2 = iuvh;
						gst1->iuv0 = gst.iuv2; gst1->iuv1 = iuvg; gst1->iuv2 = gst.iuv1;
						gst2->iuv0 = gst.iuv2; gst2->iuv1 = iuvh; gst2->iuv2 = iuvg;

					}
					else if (crossAB && crossBC)
					{
						gst0->iv0 = iva; gst0->iv1 = ivg; gst0->iv2 = ivc;
						gst1->iv0 = ivc; gst1->iv1 = ivg; gst1->iv2 = ivh;
						gst2->iv0 = ivh; gst2->iv1 = ivg; gst2->iv2 = ivb;
						const GsVector2 v2ab = v2b - v2a;
						const GsVector2 v2bc = v2c - v2b;
						const GsVector2 v2ag = v2g - v2a;
						const GsVector2 v2bh = v2h - v2b;
						const GsVector2 uvg(
							IsZero(v2ab.x) ? uv0.x : uv0.x + (uv1.x - uv0.x) * v2ag.x / v2ab.x,
							IsZero(v2ab.y) ? uv0.y : uv0.y + (uv1.y - uv0.y) * v2ag.y / v2ab.y
						);
						const GsVector2 uvh(
							IsZero(v2bc.x) ? uv1.x : uv1.x + (uv2.x - uv1.x) * v2bh.x / v2bc.x,
							IsZero(v2bc.y) ? uv1.y : uv1.y + (uv2.y - uv1.y) * v2bh.y / v2bc.y
						);
						const UInt32 iuvg = AddUv(uvg);
						const UInt32 iuvh = AddUv(uvh);
						gst0->iuv0 = gst.iuv0;
						gst0->iuv1 = iuvg;
						gst0->iuv2 = gst.iuv2;
						gst1->iuv0 = gst.iuv2;
						gst1->iuv1 = iuvg;
						gst1->iuv2 = iuvh;
						gst2->iuv0 = iuvh;
						gst2->iuv1 = iuvg;
						gst2->iuv2 = gst.iuv1;
					}
					else if (crossAC && crossBC)
					{
						gst0->iv0 = iva; gst0->iv1 = ivb; gst0->iv2 = ivg;
						gst1->iv0 = ivg; gst1->iv1 = ivb; gst1->iv2 = ivh;
						gst2->iv0 = ivc; gst2->iv1 = ivg; gst2->iv2 = ivh;
						const GsVector2 v2ac = v2c - v2a;
						const GsVector2 v2bc = v2c - v2b;
						const GsVector2 v2ag = v2g - v2a;
						const GsVector2 v2bh = v2h - v2b;
						const GsVector2 uvg(
							IsZero(v2ac.x) ? uv0.x : uv0.x + (uv2.x - uv0.x) * v2ag.x / v2ac.x,
							IsZero(v2ac.y) ? uv0.y : uv0.y + (uv2.y - uv0.y) * v2ag.y / v2ac.y
						);
						const GsVector2 uvh(
							IsZero(v2bc.x) ? uv1.x : uv1.x + (uv2.x - uv1.x) * v2bh.x / v2bc.x,
							IsZero(v2bc.y) ? uv1.y : uv1.y + (uv2.y - uv1.y) * v2bh.y / v2bc.y
						);
						const UInt32 iuvg = AddUv(uvg);
						const UInt32 iuvh = AddUv(uvh);
						gst0->iuv0 = gst.iuv0;
						gst0->iuv1 = gst.iuv1;
						gst0->iuv2 = iuvg;
						gst1->iuv0 = iuvg;
						gst1->iuv1 = gst.iuv1;
						gst1->iuv2 = iuvh;
						gst2->iuv0 = gst.iuv2;
						gst2->iuv1 = iuvg;
						gst2->iuv2 = iuvh;
					}
					else
					{
						continue;
					}
					gst.markRemoval = true;
					if (gst0->IsValid())
					{
						const GsIt ot0 = AddTriangle(gst0);
						listItsToAdd.emplace_back(ot0);
					}
					if (gst1->IsValid())
					{
						const GsIt ot1 = AddTriangle(gst1);
						listItsToAdd.emplace_back(ot1);
					}
					if (gst2->IsValid())
					{
						const GsIt ot2 = AddTriangle(gst2);
						listItsToAdd.emplace_back(ot2);
					}
					listItsToRemove.emplace_back(it);
				}
			}
			for (auto itIt = listItsToRemove.begin(); itIt != listItsToRemove.end(); ++itIt)
			{
				listItsResult.remove(*itIt);
			}
			for (auto itIt = listItsToAdd.begin(); itIt != listItsToAdd.end(); ++itIt)
			{
				if (std::find(listItsResult.rbegin(), listItsResult.rend(), *itIt) != listItsResult.rend())
				{
					continue;
				}
				listItsResult.emplace_back(*itIt);
			}
		}
	}

	void CompositeGeoSolid::RemoveShorterEdgesInEdge(const GsIt& itMain)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		//将在三角形边上的短边移除
		for (auto itIe = setIesDivider.begin(); itIe != setIesDivider.end();)
		{
			const GsIe& ie = *itIe;
			const GeoSolidEdge& gse = GetEdge(ie);
			const GsIv& ivp = gse.iv0;
			const GsIv& ivq = gse.iv1;
			int ieInWhichPIn = -1;
			int ieInWhichQIn = -1;
			const bool isPInEdge = IsVertexInTriangleEdge(gstMain, ivp, &ieInWhichPIn);
			if (!isPInEdge)
			{
				++itIe;
				continue;
			}
			const bool isQInEdge = IsVertexInTriangleEdge(gstMain, ivq, &ieInWhichQIn);
			if (!isQInEdge)
			{
				++itIe;
				continue;
			}
			if (ieInWhichPIn == ieInWhichQIn)
			{
				itIe = setIesDivider.erase(itIe);
			}
			else
			{
				++itIe;
			}
		}
	}

	void CompositeGeoSolid::DivideTriangleEdges(const GsIt& itMain, std::list<GsIe>& listIes)
	{
		const GeoSolidTriangle& gstMain = GetTriangle(itMain);
		listIes.emplace_back(gstMain.ie0);
		listIes.emplace_back(gstMain.ie1);
		listIes.emplace_back(gstMain.ie2);
		std::list<GsIe> listIesToAdd;
		const GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		for (auto itIv = setNewIvs.begin(); itIv != setNewIvs.end(); ++itIv)
		{
			const GsIv& iv = *itIv;
			listIesToAdd.clear();
			for (auto itIe = listIes.begin(); itIe != listIes.end();)
			{
				const GsIe& ie = *itIe;
				const bool inLine = IsVertexInEdge(iv, ie, 3e-8);
				if (!inLine)
				{
					++itIe;
					continue;
				}
				DivideEdge(ie);
				GeoSolidEdge& gse = GetEdge(ie);
				GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(gse.iv0, iv, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(gse.iv1, iv, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				const GsIe ie0 = AddEdge(gse0);
				const GsIe ie1 = AddEdge(gse1);
				GeoSolidEdge::recycle(gse0);
				GeoSolidEdge::recycle(gse1);
				listIesToAdd.emplace_back(ie0);
				listIesToAdd.emplace_back(ie1);
				itIe = listIes.erase(itIe);
			}
			listIes.insert(listIes.end(), listIesToAdd.begin(), listIesToAdd.end());
		}
	}

	void CompositeGeoSolid::ExtendAndCollectInOutEdges(const GsIt& itMain, const std::vector<int>& vIesWhereIvIn, 
		std::list<InOutEdge>& listInOutEdges, std::list<GsIe>& listIesNew)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		std::list<GsIe> listIes;
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		listIes.insert(listIes.begin(), setIesDivider.begin(), setIesDivider.end());
		std::list<GsIv> listIvs;
		const GsIv& iva = gstMain.iv0;
		const GsIv& ivb = gstMain.iv1;
		const GsIv& ivc = gstMain.iv2;
		const GsIv cv = GetVertexCount();
		std::list<InOutEdge> listNotInOutEdges;
		while (!listIes.empty())
		{
			const GsIe ieFirst = listIes.front();
			const GeoSolidEdge& gseFirst = GetEdge(ieFirst);
			listIes.pop_front();
			listIvs.clear();
			listIvs.emplace_back(gseFirst.iv0);
			listIvs.emplace_back(gseFirst.iv1);
			GsIv ivFront = listIvs.front();
			GsIv ivBack = listIvs.back();
			InOutEdge ioe;
			ioe.ieFront = -1;
			ioe.ieBack = -1;
			bool isFrontInEdge = (ioe.ieFront = vIesWhereIvIn[ivFront]) >= 0;
			bool isBackInEdge = (ioe.ieBack = vIesWhereIvIn[ivBack]) >= 0;
			if (ioe.ieFront >= 0 && ioe.ieFront == ioe.ieBack)
			{
				continue;
			}
			for (auto itIe = listIes.begin(); itIe != listIes.end(); )
			{
				if (isFrontInEdge && isBackInEdge)
				{
					break;
				}
				const GsIe ie = *itIe;
				const GeoSolidEdge& gse = GetEdge(ie);
				if ((gse.iv0 == ivFront && gse.iv1 == ivBack) ||
					(gse.iv1 == ivFront && gse.iv0 == ivBack))
				{
					listIes.erase(itIe);
					ivFront = ivBack;
					break;
				}
				if (gse.iv0 == ivFront || gse.iv1 == ivFront)
				{
					if (gse.iv0 == ivFront)
					{
						listIvs.emplace_front(gse.iv1);
						ivFront = gse.iv1;
					}
					else
					{
						listIvs.emplace_front(gse.iv0);
						ivFront = gse.iv0;
					}
					isFrontInEdge = (ioe.ieFront = vIesWhereIvIn[ivFront]) >= 0;
					listIes.erase(itIe);
					itIe = listIes.begin();
					continue;
				}
				if (gse.iv0 == ivBack || gse.iv1 == ivBack)
				{
					if (gse.iv0 == ivBack)
					{
						listIvs.emplace_back(gse.iv1);
						ivBack = gse.iv1;
					}
					else
					{
						listIvs.emplace_back(gse.iv0);
						ivBack = gse.iv0;
					}
					isBackInEdge = (ioe.ieBack = vIesWhereIvIn[ivBack]) >= 0;
					listIes.erase(itIe);
					itIe = listIes.begin();
					continue;
				}
				if (ivFront == ivBack)
				{
					if (ivFront == listIvs.front())
					{
						listIvs.emplace_back(ivFront);
					}
					else if (ivBack == listIvs.front())
					{
						listIvs.emplace_back(ivBack);
					}
					break;
				}
				if (isFrontInEdge && isBackInEdge)
				{
					break;
				}
				++itIe;
			}
			for (const GsIv& iv : listIvs)
			{
				GsIndex iuv = AddUvInTriangle(iv, itMain);
				GeoSolidPolygonVertex gspv(iv, iuv);
				ioe.listGspvs.emplace_back(gspv);
			}
			if (isFrontInEdge && isBackInEdge)
			{
				//找到一串首尾相接的线段
				listIvs.clear();
				listInOutEdges.emplace_back(ioe);
				continue;
			}
			if (ivFront == ivBack)
			{
				//鸟洞
				struct VertexAwayFromEdge {
					GsDigit d;
					GsIe ie;
					std::list<GeoSolidPolygonVertex>::iterator itGspv;
				};
				std::vector<bool> vCaculated;
				vCaculated.resize(cv, false);
				std::list<VertexAwayFromEdge> listVafes;
				for (auto itGspv = ioe.listGspvs.begin(); itGspv != ioe.listGspvs.end(); ++itGspv)
				{
					if (vCaculated[itGspv->iv])
					{
						continue;
					}
					VertexAwayFromEdge vafe;
					vafe.itGspv = itGspv;
					GsDigit d0 = GetDistanceFromEdge(itGspv->iv, gstMain.ie0);
					GsDigit d1 = GetDistanceFromEdge(itGspv->iv, gstMain.ie1);
					GsDigit d2 = GetDistanceFromEdge(itGspv->iv, gstMain.ie2);
					if (d0 < d1)
					{
						if (d0 < d2)
						{
							vafe.d = d0;
							vafe.ie = gstMain.ie0;
						}
						else
						{
							vafe.d = d2;
							vafe.ie = gstMain.ie2;
						}
					}
					else
					{
						if (d1 < d2)
						{
							vafe.d = d1;
							vafe.ie = gstMain.ie1;
						}
						else
						{
							vafe.d = d2;
							vafe.ie = gstMain.ie2;
						}
					}
					listVafes.emplace_back(vafe);
				}
				VertexAwayFromEdge vafeNearest;
				vafeNearest.d = std::numeric_limits<GsDigit>::max();
				for (VertexAwayFromEdge& vafe : listVafes)
				{
					if (vafeNearest.d > vafe.d)
					{
						vafeNearest = vafe;
					}
				}
				const GsVector3 vProjection = CastVertexOnEdge(vafeNearest.itGspv->iv, vafeNearest.ie);
				GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(
					vProjection, 
					gstMain.intersectSrc.from, gstMain.intersectCut.from
				);
				const GsIv iv = AddVertex(gsv);
				GeoSolidVertex::recycle(gsv);
				ioe.ieFront = ioe.ieBack = vafeNearest.ie;
				if (vafeNearest.itGspv->iv == ioe.listGspvs.front().iv)
				{
					ioe.listGspvs.emplace_back(ioe.listGspvs.front());
				}
				else if (vafeNearest.itGspv->iv == ioe.listGspvs.back().iv)
				{
					ioe.listGspvs.emplace_front(ioe.listGspvs.back());
				}
				else
				{
					//顺序有异常错误
					SANDBOX_ASSERT(false);
				}
				const UInt32 iuv = AddUvInTriangle(iv, itMain);
				GeoSolidPolygonVertex gspv(iv, iuv);
				ioe.listGspvs.emplace_front(gspv);
				ioe.listGspvs.emplace_back(gspv);
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(vafeNearest.itGspv->iv, iv, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				const GsIe ie = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
				listIesNew.emplace_back(ie);
				//TODO: 2024-02-21 11:20:15: 相交？
				AddEdgeIntoTriangle(ie, itMain);
				std::list<GeoSolidPolygonVertex> listGspvsReorder;
			}
			if (ioe.ieFront >= 0 && ioe.ieBack >= 0)
			{
				listInOutEdges.emplace_back(ioe);
			}
			else
			{
				listNotInOutEdges.emplace_back(ioe);
			}
		}
		for (auto itIoe = listNotInOutEdges.begin(); itIoe != listNotInOutEdges.end(); ++itIoe)
		{
			InOutEdge& ioe = *itIoe;
			if (ioe.ieFront >= 0 || ioe.ieBack >= 0)
			{
				continue;
			}
			//两端没有出入的，先让其中一端连上边
			const GsIv& ivFront = ioe.listGspvs.front().iv;
			const GsIv& ivBack = ioe.listGspvs.back().iv;
			const GsVector3& vBack = GetVertex(ivBack).v;
			GsVector3 vp;
			GsIe ieCross;
			GsIv iv;
			{
				GsDigit df;
				GsDigit db;
				GsVector3 vpf;
				GsIe& ieCrossFront = gstMain.ie0;
				GsVector3 vpb;
				GsIe& ieCrossBack = gstMain.ie0;
				{
					const GsVector3& vFront = GetVertex(ivFront).v;
					GsVector3 vpf0 = CastVertexOnEdge(ivFront, gstMain.ie0);
					GsVector3 vpf1 = CastVertexOnEdge(ivFront, gstMain.ie1);
					GsVector3 vpf2 = CastVertexOnEdge(ivFront, gstMain.ie2);
					const GsDigit df0 = Distance(vFront, vpf0);
					const GsDigit df1 = Distance(vFront, vpf1);
					const GsDigit df2 = Distance(vFront, vpf2);
					vpf = vpf0;
					df = df0;
					if (df1 < df0)
					{
						vpf = vpf1;
						ieCrossFront = gstMain.ie1;
						df = df1;
						if (df2 < df1)
						{
							vpf = vpf2;
							ieCrossFront = gstMain.ie2;
							df = df2;
						}
					}
					else
					{
						if (df2 < df0)
						{
							vpf = vpf2;
							ieCrossFront = gstMain.ie2;
							df = df2;
						}
					}
				}
				{
					const GsVector3& vBack = GetVertex(ivBack).v;
					GsVector3 vpb0 = CastVertexOnEdge(ivBack, gstMain.ie0);
					GsVector3 vpb1 = CastVertexOnEdge(ivBack, gstMain.ie1);
					GsVector3 vpb2 = CastVertexOnEdge(ivBack, gstMain.ie2);
					const GsDigit db0 = Distance(vBack, vpb0);
					const GsDigit db1 = Distance(vBack, vpb1);
					const GsDigit db2 = Distance(vBack, vpb2); 
					vpb = vpb0;
					db = db2;
					if (db1 < db0)
					{
						vpb = vpb1;
						ieCrossBack = gstMain.ie1;
						db = db1;
						if (db2 < db1)
						{
							vpb = vpb2;
							ieCrossBack = gstMain.ie2;
							db = db2;
						}
					}
					else
					{
						if (db2 < db0)
						{
							vpb = vpb2;
							ieCrossBack = gstMain.ie2;
							db = db2;
						}
					}
				}
				if (df < db)
				{
					vp = vpf;
					ieCross = ieCrossFront;
					iv = ivFront;
				}
				else
				{
					vp = vpb;
					ieCross = ieCrossBack;
					iv = ivBack;
				}
			}
			GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
			if (gstMain.intersectSrc.from)
			{
				gsv->intersectSrc.inside = true;
				gsv->intersectSrc.hasCheckedInside = true;
			}
			if (gstMain.intersectCut.from)
			{
				gsv->intersectCut.inside = true;
				gsv->intersectCut.hasCheckedInside = true;
			}
			const GsIv ivp = AddVertex(gsv);
			GeoSolidVertex::recycle(gsv);
			const GsIndex iuv = AddUvInTriangle(ivp, itMain);
			GeoSolidPolygonVertex gspv(ivp, iuv);
			ioe.listGspvs.emplace_front(gspv);
			if (iv == ivFront)
			{
				ioe.ieFront = ieCross;
			}
			else
			{
				ioe.ieBack = ieCross;
			}
			AddVertexToTriangle(ivp, itMain);
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivFront, ivp, true, true);
			const GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			listIesNew.emplace_back(ie);
			GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
			setIesDivider.emplace(ie);
		}
		const GsVector3& va = GetVertex(iva).v;
		listNotInOutEdges.sort([this, va](const InOutEdge& ioe0, const InOutEdge& ioe1) -> bool {
			GsVector3 v0;
			if (ioe0.ieFront < 0 && ioe0.ieBack < 0)
			{
				SANDBOX_ASSERT(false);
				return false;
			}
			else if (ioe0.ieFront < 0)
			{
				v0 = GetVertex(ioe0.listGspvs.front().iv).v;
			}
			else if (ioe0.ieBack < 0)
			{
				v0 = GetVertex(ioe0.listGspvs.back().iv).v;
			}
			else
			{
				return false;
			}
			GsVector3 v1;
			if (ioe1.ieFront < 0 && ioe1.ieBack < 0)
			{
				SANDBOX_ASSERT(false);
				return false;
			}
			else if (ioe1.ieFront < 0)
			{
				v1 = GetVertex(ioe1.listGspvs.front().iv).v;
			}
			else if (ioe0.ieBack < 0)
			{
				v1 = GetVertex(ioe1.listGspvs.back().iv).v;
			}
			else
			{
				return false;
			}
			const GsDigit d0 = Distance(va, v0);
			const GsDigit d1 = Distance(va, v1);
			return d0 < d1;
		});
		for (auto itIoe0 = listNotInOutEdges.begin(); itIoe0 != listNotInOutEdges.end();)
		{
			InOutEdge& ioe0 = *itIoe0;
			auto itIoe1 = std::next(itIoe0);
			if (itIoe1 != listNotInOutEdges.end())
			{
				InOutEdge& ioe1 = *itIoe1;
				if (ioe0.ieFront < 0)
				{
					std::reverse(ioe0.listGspvs.begin(), ioe0.listGspvs.end());
					GsIe ieTemp = ioe0.ieFront;
					ioe0.ieFront = ioe0.ieBack;
					ioe0.ieBack = ieTemp;
				}
				if (ioe1.ieBack < 0)
				{
					std::reverse(ioe1.listGspvs.begin(), ioe1.listGspvs.end());
					GsIe ieTemp = ioe1.ieFront;
					ioe1.ieFront = ioe1.ieBack;
					ioe1.ieBack = ieTemp;
				}
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(
					ioe0.listGspvs.back().iv, ioe1.listGspvs.front().iv, 
					gstMain.intersectSrc.from, gstMain.intersectCut.from
				);
				const GsIe ie = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
				listIesNew.emplace_back(ie);
				GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
				setIesDivider.emplace(ie);
				ioe0.listGspvs.insert(ioe0.listGspvs.end(), ioe1.listGspvs.begin(), ioe1.listGspvs.end());
				ioe0.ieBack = ioe1.ieBack;
				listNotInOutEdges.erase(itIoe1);
			}
			listInOutEdges.emplace_back(ioe0);
			itIoe0 = listNotInOutEdges.erase(itIoe0);
		}
		//剩下单个没有相连的，单独连边
		for (auto itIoe = listNotInOutEdges.begin(); itIoe != listNotInOutEdges.end(); ++itIoe)
		{
			InOutEdge& ioe = *itIoe;
			if (ioe.ieFront >= 0 || ioe.ieBack >= 0)
			{
				continue;
			}
			//两端没有出入的，先让其中一端连上边
			const GsIv& ivFront = ioe.listGspvs.front().iv;
			const GsIv& ivBack = ioe.listGspvs.back().iv;
			const GsVector3& vBack = GetVertex(ivBack).v;
			GsVector3 vp;
			GsIe ieCross;
			GsIv iv;
			{
				GsDigit df;
				GsDigit db;
				GsVector3 vpf;
				GsIe& ieCrossFront = gstMain.ie0;
				GsVector3 vpb;
				GsIe& ieCrossBack = gstMain.ie0;
				{
					const GsVector3& vFront = GetVertex(ivFront).v;
					GsVector3 vpf0 = CastVertexOnEdge(ivFront, gstMain.ie0);
					GsVector3 vpf1 = CastVertexOnEdge(ivFront, gstMain.ie1);
					GsVector3 vpf2 = CastVertexOnEdge(ivFront, gstMain.ie2);
					const GsDigit df0 = Distance(vFront, vpf0);
					const GsDigit df1 = Distance(vFront, vpf1);
					const GsDigit df2 = Distance(vFront, vpf2);
					vpf = vpf0;
					df = df0;
					if (df1 < df0)
					{
						vpf = vpf1;
						ieCrossFront = gstMain.ie1;
						df = df1;
						if (df2 < df1)
						{
							vpf = vpf2;
							ieCrossFront = gstMain.ie2;
							df = df2;
						}
					}
					else
					{
						if (df2 < df0)
						{
							vpf = vpf2;
							ieCrossFront = gstMain.ie2;
							df = df2;
						}
					}
				}
				{
					const GsVector3& vBack = GetVertex(ivBack).v;
					GsVector3 vpb0 = CastVertexOnEdge(ivBack, gstMain.ie0);
					GsVector3 vpb1 = CastVertexOnEdge(ivBack, gstMain.ie1);
					GsVector3 vpb2 = CastVertexOnEdge(ivBack, gstMain.ie2);
					const GsDigit db0 = Distance(vBack, vpb0);
					const GsDigit db1 = Distance(vBack, vpb1);
					const GsDigit db2 = Distance(vBack, vpb2);
					vpb = vpb0;
					db = db2;
					if (db1 < db0)
					{
						vpb = vpb1;
						ieCrossBack = gstMain.ie1;
						db = db1;
						if (db2 < db1)
						{
							vpb = vpb2;
							ieCrossBack = gstMain.ie2;
							db = db2;
						}
					}
					else
					{
						if (db2 < db0)
						{
							vpb = vpb2;
							ieCrossBack = gstMain.ie2;
							db = db2;
						}
					}
				}
				if (df < db)
				{
					vp = vpf;
					ieCross = ieCrossFront;
					iv = ivFront;
				}
				else
				{
					vp = vpb;
					ieCross = ieCrossBack;
					iv = ivBack;
				}
			}
			GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
			if (gstMain.intersectSrc.from)
			{
				gsv->intersectSrc.inside = true;
				gsv->intersectSrc.hasCheckedInside = true;
			}
			if (gstMain.intersectCut.from)
			{
				gsv->intersectCut.inside = true;
				gsv->intersectCut.hasCheckedInside = true;
			}
			const GsIv ivp = AddVertex(gsv);
			GeoSolidVertex::recycle(gsv);
			const GsIndex iuv = AddUvInTriangle(ivp, itMain);
			GeoSolidPolygonVertex gspv(ivp, iuv);
			ioe.listGspvs.emplace_front(gspv);
			if (iv == ivFront)
			{
				ioe.ieFront = ieCross;
			}
			else
			{
				ioe.ieBack = ieCross;
			}
			AddVertexToTriangle(ivp, itMain);
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivFront, ivp, true, true);
			const GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			listIesNew.emplace_back(ie);
			GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
			setIesDivider.emplace(ie);
			listInOutEdges.emplace_back(ioe);
		}
	}

	void CompositeGeoSolid::GetMinCycleInGraph(const std::set<GsIv>& setIvs,
		const std::list<GeoSolidPolygonVertex>& listGspvs, std::list<GsIv>& listIvsMinCycle)
	{
		const UInt32& civ = setIvs.size();
		if (civ <= 0)
		{
			return;
		}
		//转换下标，节省内存
		std::vector<GsIv> vIivsToIvs;
		vIivsToIvs.assign(setIvs.begin(), setIvs.end());
		std::vector<int> vIvsToIivs;
		const UInt32& cv = GetVertexCount();
		vIvsToIivs.resize(cv, -1);
		for (UInt32 iiv = 0; iiv < civ; ++iiv)
		{
			vIvsToIivs[vIivsToIvs[iiv]] = iiv;
		}
		//邻接矩阵
		std::vector< std::vector<GsDigit> > ddaEdges;
		std::vector< std::vector<GsDigit> > ddaDists;
		std::vector< std::vector< std::list<UInt32> > > ddaListIivs;
		ddaEdges.resize(civ);
		ddaDists.resize(civ);
		ddaListIivs.resize(civ);
		const GsDigit maxValue = std::numeric_limits<int>::max();
		for (UInt32 iiv = 0; iiv < civ; ++iiv)
		{
			ddaEdges[iiv].resize(civ);
			ddaDists[iiv].resize(civ);
			ddaListIivs[iiv].resize(civ);
			for (UInt32 jiv = 0; jiv < civ; ++jiv)
			{
				ddaEdges[iiv][jiv] = iiv == jiv ? 0 : maxValue;
				ddaDists[iiv][jiv] = iiv == jiv ? 0 : maxValue;
			}
		}
		//有向图路径值默认全部设为1。只有ivStart向邻点为单向，其余为双向。
		for (UInt32 iiv = 0; iiv < civ; ++iiv)
		{
			const GsIv& iv = vIivsToIvs[iiv];
			auto itGspv = std::find(listGspvs.begin(), listGspvs.end(), iv);
			if (itGspv == listGspvs.end())
			{
				SANDBOX_ASSERT(false);
				continue;
			}
			const GsVector3& v = GetVertex(iv).v;
			for (const GsIv& ivNeighbor : itGspv->setNeighborIvs)
			{
				const int iivNeighbor = vIvsToIivs[ivNeighbor];
				if (iivNeighbor < 0)
				{
					continue;
				}
				const GsVector3& vNeighbor = GetVertex(ivNeighbor).v;
				const GsDigit d = Distance(v, vNeighbor);
				ddaEdges[iiv][iivNeighbor] = d;
				ddaDists[iiv][iivNeighbor] = d;
				ddaListIivs[iiv][iivNeighbor].emplace_back(iiv);
				ddaListIivs[iiv][iivNeighbor].emplace_back(iivNeighbor);
			}
		}
		//Floyd动态规划算法
		GsDigit ans = maxValue;
		std::list<UInt32> listIivsMinCycle;
		for (UInt32 k = 0; k < civ; ++k)
		{
			for (UInt32 i = 0; i < k; ++i)
			{
				for (UInt32 j = i + 1; j < k; ++j)
				{
					const GsDigit& dij = ddaDists[i][j];
					const GsDigit& eik = ddaEdges[i][k];
					const GsDigit& ekj = ddaEdges[k][j];
					const GsDigit cycle = dij + eik + ekj;
					if (ans > cycle)
					{
						ans = cycle;
						listIivsMinCycle.clear();
						listIivsMinCycle.insert(listIivsMinCycle.end(), ddaListIivs[i][j].begin(), ddaListIivs[i][j].end());
						listIivsMinCycle.emplace_back(k);
					}
				}
			}
			for (UInt32 i = 0; i < civ; ++i)
			{
				for (UInt32 j = 0; j < civ; ++j)
				{
					GsDigit& dij = ddaDists[i][j];
					const GsDigit& dik = ddaDists[i][k];
					const GsDigit& dkj = ddaDists[k][j];
					const GsDigit d = dik + dkj;
					if (dij > d)
					{
						dij = d;
						std::list<UInt32>& listIivs = ddaListIivs[i][j];
						listIivs.clear();
						listIivs.insert(listIivs.end(), ddaListIivs[i][k].begin(), ddaListIivs[i][k].end());
						//相接处是相同顶点，只增加一个
						listIivs.insert(listIivs.end(), std::next(ddaListIivs[k][j].begin()), ddaListIivs[k][j].end());
					}
				}
			}
		}
		listIvsMinCycle.clear();
		for (const UInt32& iiv : listIivsMinCycle)
		{
			const GsIv& iv = vIivsToIvs[iiv];
			listIvsMinCycle.emplace_back(iv);
		}
	}

	void CompositeGeoSolid::DivideTriangleByPolygons(const GsIt& itMain)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		if (setIesDivider.empty())
		{
			if (!setNewIvs.empty())
			{
				DivideTriangle(itMain);
			}
			else
			{
				GeoSolidTriangle& gst = gstMain;
				const GsIt& it = itMain;
				if (gst.intersectSrc.from)
				{
					WhetherTriangleIntersectGeoSolid(it, false);
					if (!gst.intersectSrc.hasCheckedInside)
					{
						gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, true);
						gst.intersectSrc.hasCheckedInside = true;
					}
				}
				else if (gst.intersectCut.from)
				{
					WhetherTriangleIntersectGeoSolid(it, true);
					if (!gst.intersectCut.hasCheckedInside)
					{
						gst.intersectCut.inside = IsTriangleInGeoSolid(gst, false);
						gst.intersectCut.hasCheckedInside = true;
					}
				}
			}
			return;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Before starting:");
			PrintBeforeDivision(itMain);
		}
		#endif
		RemoveShorterEdgesInEdge(itMain);
		std::list<GsIe> listIesInEdges;
		DivideTriangleEdges(itMain, listIesInEdges);

		//将在边上的点和三角形顶点收集起来，后续的寻路做判断
		//所有的顶点
		std::set<GsIv> setIvsAll;
		setIvsAll.insert(setNewIvs.begin(), setNewIvs.end());
		setIvsAll.emplace(gstMain.iv0);
		setIvsAll.emplace(gstMain.iv1);
		setIvsAll.emplace(gstMain.iv2);
		//所有顶点的遍历计数器
		//当顶点形成一次多边形时，计数减1。减到0时，移除该点，不做遍历。
		const GsIv cv = GetVertexCount();
		std::vector<int> vIvsUseCounter;
		vIvsUseCounter.resize(cv, 0);
		//记录运算中的情况，主要是邻点
		//TODO: 2024-01-29 14:52:50: 把距离带过去，不用重复计算	
		std::list<GeoSolidPolygonVertex> listGspvsAll;
		for (const GsIv& iv : setIvsAll)
		{
			const GeoSolidVertex& gsv = GetVertex(iv);
			UInt32 iuv = AddUvInTriangle(iv, itMain);
			GeoSolidPolygonVertex gspv(iv, iuv);
			for (auto itNeighbor = gsv.listNeighbors.begin(); itNeighbor != gsv.listNeighbors.end(); ++itNeighbor)
			{
				const GsIv& ivNeighbor = itNeighbor->iv;
				bool findVertex =
					ivNeighbor == gstMain.iv0 ||
					ivNeighbor == gstMain.iv1 ||
					ivNeighbor == gstMain.iv2 ||
					setIvsAll.find(ivNeighbor) != setIvsAll.end()
				;
				if (!findVertex)
				{
					continue;
				}
				gspv.setNeighborIvs.emplace(ivNeighbor);
			}
			//默认所有值设1，后续再根据空间关系调整
			vIvsUseCounter[iv] = gspv.setNeighborIvs.size() >= 2 ? gspv.setNeighborIvs.size() - 1 : 1;
			listGspvsAll.emplace_back(std::move(gspv));
		}

		std::vector<int> vIesWhereIvIn;
		vIesWhereIvIn.resize(cv, -1);
		//三个顶点在使用过程另做判断
		for (const GsIv& iv : setNewIvs)
		{
			IsVertexInTriangleEdge(gstMain, iv, &vIesWhereIvIn[iv]);
		}

		for (const GsIv& iv : setNewIvs)
		{
			const GeoSolidVertex& gsv = GetVertex(iv);
			for (const GeoSolidVertexNeighbor& gsvn : gsv.listNeighbors)
			{
				const GsIv& ivNeighbor = gsvn.iv;
				if (vIvsUseCounter[ivNeighbor] <= 0)
				{
					continue;
				}
				bool findEdge = false;
				for (const GsIe& ie : setIesDivider)
				{
					const GeoSolidEdge& gse = GetEdge(ie);
					findEdge = gse.Is(iv, ivNeighbor);
					if (findEdge)
					{
						break;
					}
				}
				if (findEdge)
				{
					continue;
				}
				const int& ieWhereIvIn = vIesWhereIvIn[iv];
				if (ieWhereIvIn >= 0 && ieWhereIvIn == vIesWhereIvIn[ivNeighbor])
				{
					continue;
				}
				//在边上的不用添加
				if (ivNeighbor == gstMain.iv0)
				{
					if (ieWhereIvIn == gstMain.ie0 || ieWhereIvIn == gstMain.ie1)
					{
						continue;
					}
				}
				else if (ivNeighbor == gstMain.iv1)
				{
					if (ieWhereIvIn == gstMain.ie0 || ieWhereIvIn == gstMain.ie2)
					{
						continue;
					}
				}
				else if (ivNeighbor == gstMain.iv2)
				{
					if (ieWhereIvIn == gstMain.ie1 || ieWhereIvIn == gstMain.ie2)
					{
						continue;
					}
				}
				GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(iv, ivNeighbor, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				const GsIe ie = AddEdge(gse);
				GeoSolidEdge::recycle(gse);
				setIesDivider.emplace(ie);
			}
		}

		std::list<InOutEdge> listInOutEdges;
		std::list<GsIe> listIesNew;
		ExtendAndCollectInOutEdges(itMain, vIesWhereIvIn, listInOutEdges, listIesNew);
		for (const GsIe& ie : listIesNew)
		{
			const GeoSolidEdge& gse = GetEdge(ie);
			auto itGspv0 = std::find(listGspvsAll.begin(), listGspvsAll.end(), gse.iv0);
			if (itGspv0 != listGspvsAll.end())
			{
				itGspv0->setNeighborIvs.emplace(gse.iv1);
			}
			else
			{
				setIvsAll.emplace(gse.iv0);
				for (InOutEdge& ioe : listInOutEdges)
				{
					bool find = false;
					for (auto itGspv = ioe.listGspvs.begin(); itGspv != ioe.listGspvs.end(); ++itGspv)
					{
						if (itGspv->iv == gse.iv0)
						{
							itGspv->setNeighborIvs.emplace(gse.iv1);
							listGspvsAll.emplace_back(*itGspv);
							find = true;
							break;
						}
					}
					if (find)
					{
						break;
					}
				}
			}
			auto itGspv1 = std::find(listGspvsAll.begin(), listGspvsAll.end(), gse.iv1);
			if (itGspv1 != listGspvsAll.end())
			{
				itGspv1->setNeighborIvs.emplace(gse.iv0);
			}
			else
			{
				setIvsAll.emplace(gse.iv1);
				for (InOutEdge& ioe : listInOutEdges)
				{
					bool find = false;
					for (auto itGspv = ioe.listGspvs.begin(); itGspv != ioe.listGspvs.end(); ++itGspv)
					{
						if (itGspv->iv == gse.iv1)
						{
							itGspv->setNeighborIvs.emplace(gse.iv0);
							listGspvsAll.emplace_back(*itGspv);
							find = true;
							break;
						}
					}
					if (find)
					{
						break;
					}
				}
			}
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("After extending edges to sides:");
			PrintSingleGeoGebraExecution(itMain);
		}
		#endif
		//线段的遍历计数器，内部线段使用2次
		const GsIe ce = GetEdgeCount();
		std::vector<int> vIesUseCounter;
		vIesUseCounter.resize(ce, 0);
		for (const GsIe& ie : setIesDivider)
		{
			vIesUseCounter[ie] = 2;
		}
		//在三角形边上的线段，可覆盖处于gstMain.setIesDivider中设置为2的线段
		for (const GsIe& ie : listIesInEdges)
		{
			vIesUseCounter[ie] = 1;
		}
		std::set<GsIe> setIesAll;
		setIesAll.insert(setIesDivider.begin(), setIesDivider.end());
		setIesAll.insert(listIesInEdges.begin(), listIesInEdges.end());

		gstMain.markRemoval = true;
		std::list<GeoSolidPolygon> listGsps;

		for (const InOutEdge& ioe : listInOutEdges)
		{
			const GsIv& ivFront = ioe.listGspvs.front().iv;
			const GsIv& ivBack = ioe.listGspvs.back().iv;
			if (ivFront == ivBack)
			{
				std::list<GeoSolidPolygonVertex> listGspvsCopy = ioe.listGspvs;
				do
				{
					GeoSolidPolygonVertex& gspvFront = listGspvsCopy.front();
					GeoSolidPolygonVertex& gspvBack = listGspvsCopy.back();
					listGspvsCopy.pop_front();
					listGspvsCopy.pop_back();
					vIvsUseCounter[gspvFront.iv] = 2;
					vIvsUseCounter[gspvBack.iv] = 2;
				} while (!listGspvsCopy.empty() && listGspvsCopy.front().iv == listGspvsCopy.back().iv);
				for (const GeoSolidPolygonVertex& gspv : listGspvsCopy)
				{
					vIvsUseCounter[gspv.iv] = 2;
				}
			}
			else for (const GeoSolidPolygonVertex& gspv : ioe.listGspvs)
			{
				int counter = 0;
				const GsIv& iv = gspv.iv;
				auto itGspv = std::find(listGspvsAll.begin(), listGspvsAll.end(), iv);
				if (iv == gstMain.iv0 || iv == gstMain.iv1 || iv == gstMain.iv2)
				{
					counter = itGspv != listGspvsAll.end() ? itGspv->setNeighborIvs.size() - 1 : 2;
				}
				else if (vIesWhereIvIn[iv] >= 0)
				{
					counter = itGspv != listGspvsAll.end() ? itGspv->setNeighborIvs.size() - 1 : 2;
				}
				else
				{
					counter = itGspv != listGspvsAll.end() ? itGspv->setNeighborIvs.size() : 2;
				}
				vIvsUseCounter[gspv.iv] = counter;
			}
		}

		while (true)
		{
			if (setIvsAll.size() <= 2)
			{
				break;
			}
			std::list<GsIv> listIvsMinCycle;
			GetMinCycleInGraph(setIvsAll, listGspvsAll, listIvsMinCycle);
			if (listIvsMinCycle.empty())
			{
				SANDBOX_ASSERT(false);
				break;
			}
			GeoSolidPolygon gsp(this, gstMain.ivn);
			for (auto itIvCycle = listIvsMinCycle.begin(); itIvCycle != listIvsMinCycle.end(); ++itIvCycle)
			{
				const GsIv& iv = *itIvCycle;
				--vIvsUseCounter[iv];
				auto itGspv = std::find(listGspvsAll.begin(), listGspvsAll.end(), iv);
				if (itGspv == listGspvsAll.end())
				{
					SANDBOX_ASSERT(false);
					continue;
				}
				gsp.listGspvs.emplace_back(*itGspv);
				{
					auto itIvCycleNext = std::next(itIvCycle);
					if (itIvCycleNext == listIvsMinCycle.end())
					{
						itIvCycleNext = listIvsMinCycle.begin();
					}
					const GsIv& ivNext = *itIvCycleNext;
					for (const GsIe& ie : setIesAll)
					{
						const GeoSolidEdge& gse = GetEdge(ie);
						if (!gse.Is(iv, ivNext))
						{
							continue;
						}
						--vIesUseCounter[ie];
						if (vIesUseCounter[ie] <= 0)
						{
							auto itGspvNext = std::find(listGspvsAll.begin(), listGspvsAll.end(), ivNext);
							if (itGspvNext == listGspvsAll.end())
							{
								SANDBOX_ASSERT(false);
								break;
							}
							itGspv->setNeighborIvs.erase(ivNext);
							itGspvNext->setNeighborIvs.erase(iv);
							break;
						}
					}
				}
				//寻找在一条边上共出入的InOutEdge，判断这个环是否与其相同。相同删除首尾的作为各自的邻点。
				//const int& ieWhereIvIn = vIesWhereIvIn[iv];
				//if (ieWhereIvIn < 0)
				//{
				//	continue;
				//}
				//for (auto itIvCycleNext = std::next(itIvCycle); itIvCycleNext != listIvsMinCycle.end(); ++itIvCycleNext)
				//{
				//	const GsIv& ivNext = *itIvCycleNext;
				//	const int& ieWhereIvNeighborIn = vIesWhereIvIn[ivNext];
				//	if (ieWhereIvIn == ieWhereIvNeighborIn)
				//	{
				//		auto itGspvNext = std::find(listGspvsAll.begin(), listGspvsAll.end(), ivNext);
				//		if (itGspvNext == listGspvsAll.end())
				//		{
				//			SANDBOX_ASSERT(false);
				//			break;
				//		}
				//		itGspv->setNeighborIvs.erase(ivNext);
				//		itGspvNext->setNeighborIvs.erase(iv);
				//		break;
				//	}
				//}
			}
			for (auto itGspv = listGspvsAll.begin(); itGspv != listGspvsAll.end();)
			{
				if (vIvsUseCounter[itGspv->iv] <= 0)
				{
					setIvsAll.erase(itGspv->iv);
					itGspv = listGspvsAll.erase(itGspv);
					continue;
				}
				else
				{
					++itGspv;
				}
			}
			listGsps.emplace_back(gsp);
			if (setIvsAll.empty())
			{
				break;
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail && !listGsps.empty())
		{
			std::ostringstream oss;
			GeoGebraExecutePolygons(oss, listGsps);
			WarningString("After dividing into polygons:");
			WarningString(oss.str().c_str());
		}
		#endif
		//可能绕出非顺时针的多边形，将其翻转
		for (auto itGsp = listGsps.begin(); itGsp != listGsps.end(); ++itGsp)
		{
			GeoSolidPolygon& gsp = *itGsp;
			if (gsp.listGspvs.size() <= 2)
			{
				SANDBOX_ASSERT(false);
				continue;
			}
			std::unordered_map<GsIv, GsDigit> mIvsRadians;
			int cColinear = 0;
			for (auto itGspv = gsp.listGspvs.begin(); itGspv != gsp.listGspvs.end(); ++itGspv)
			{
				auto itGspvPrev = itGspv == gsp.listGspvs.begin() ? std::prev(gsp.listGspvs.end()) : std::prev(itGspv);
				auto itGspvNext = std::next(itGspv);
				if (itGspvNext == gsp.listGspvs.end())
				{
					itGspvNext = gsp.listGspvs.begin();
				}
				const GsIv& iv0 = itGspvPrev->iv;
				const GsIv& iv1 = itGspv->iv;
				const GsIv& iv2 = itGspvNext->iv;
				bool colinear = IsParalleled(iv0, iv1, iv2);
				if (colinear)
				{
					++cColinear;
					continue;
				}
				GsDigit radian = GetRadian(iv0, iv1, iv2);
				const bool convex = IsConvex(iv0, iv1, iv2, gstMain.ivn);
				if (!convex)
				{
					//绕出的法线与渲染不同，取另一侧的角度
					radian = kTwoPI - radian;
				}
				mIvsRadians[iv1] = radian;
			}
			bool toReverse = false;
			//内角和
			GsDigit innerRadianSumTheory = (gsp.listGspvs.size() - cColinear - 2) * kPI;
			GsDigit innerRadianSum = 0;
			for (auto p : mIvsRadians)
			{
				innerRadianSum += p.second;
			}
			//角度值相等，不需要很精确
			toReverse = !IsZero(innerRadianSum - innerRadianSumTheory, 1e-2) && innerRadianSum > innerRadianSumTheory;
			if (toReverse)
			{
				std::reverse(gsp.listGspvs.begin(), gsp.listGspvs.end());
			}
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		int i = 0;
		#endif
		GeoSolidSet<GsIt> setNewIts;
		for (auto itGsp = listGsps.begin(); itGsp != listGsps.end(); ++itGsp)
		{
			GeoSolidPolygon& gsp = *itGsp;
			gsp.EarClipping(gstMain, setNewIts);
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail && !setNewIts.empty())
			{
				std::ostringstream oss;
				oss << "Execute({";
				std::unordered_set<GsIv> setIvs;
				for (const GsIt& it : setNewIts)
				{
					const GeoSolidTriangle& gst = GetTriangle(it);
					setIvs.insert(gst.iv0);
					setIvs.insert(gst.iv1);
					setIvs.insert(gst.iv2);
				}
				WarningStringMsg("ct = %4u | cv = %4u", setNewIts.size(), setIvs.size());
				const GsIv& ivFirst = *setIvs.begin();
				GeoGebraAppendVertexCmd(oss, ivFirst);
				setIvs.erase(ivFirst);
				for (const GsIv& iv : setIvs)
				{
					oss << ',';
					GeoGebraAppendVertexCmd(oss, iv);
				}
				for (const GsIt& it : setNewIts)
				{
					oss << ',';
					GeoGebraAppendTriangleCmd(oss, it);
				}
				oss << "})";
				WarningStringMsg("After EarClipping gsp%u:", i++);
				WarningString(oss.str().c_str());
			}
			#endif
		}

		for (auto itIt = setNewIts.begin(); itIt != setNewIts.end(); ++itIt)
		{
			const GsIt it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.intersectSrc.from)
			{
				WhetherTriangleIntersectGeoSolid(it, false);
				if (!gst.intersectSrc.hasCheckedInside)
				{
					gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, true);
					gst.intersectSrc.hasCheckedInside = true;
				}
			}
			else if (gst.intersectCut.from)
			{
				WhetherTriangleIntersectGeoSolid(it, true);
				if (!gst.intersectCut.hasCheckedInside)
				{
					gst.intersectCut.inside = IsTriangleInGeoSolid(gst, false);
					gst.intersectCut.hasCheckedInside = true;
				}
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			std::list<GsIt> listNewIts;
			listNewIts.insert(listNewIts.end(), setNewIts.begin(), setNewIts.end());
			PrintAfterDivision(itMain, listNewIts);
			WarningString("-------------------------DivideTriangleByPolygons-------------------------");
		}
		#endif
	}

	void CompositeGeoSolid::RemoveColinearDividerVertex(const GsIt& itMain)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		for (auto itIv = setNewIvs.begin(); itIv != setNewIvs.end(); )
		{
			const GsIv& iv = *itIv;
			std::list<GsIe> listIesConnected;
			for (const GsIe& ie : setIesDivider)
			{
				GeoSolidEdge& gse = GetEdge(ie);
				if (gse.iv0 == iv || gse.iv1 == iv)
				{
					listIesConnected.emplace_back(ie);
				}
			}
			if (listIesConnected.size() != 2)
			{
				++itIv;
				continue;
			}
			GeoSolidEdge& gse0 = GetEdge(listIesConnected.front());
			GeoSolidEdge& gse1 = GetEdge(listIesConnected.back());
			const GsIv& iv0 = gse0.iv0 == iv ? gse0.iv1 : gse0.iv0;
			const GsIv& iv1 = iv;
			const GsIv& iv2 = gse1.iv0 == iv ? gse1.iv1 : gse1.iv0;
			const bool colinear = IsParalleled(iv0, iv1, iv2);
			if (!colinear)
			{
				++itIv;
				continue;
			}
			itIv = setNewIvs.erase(itIv);
			setIesDivider.erase(listIesConnected.front());
			setIesDivider.erase(listIesConnected.back());
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(iv0, iv2, gstMain.intersectSrc.from, gstMain.intersectCut.from);
			const GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			setIesDivider.emplace(ie);
		}
	}

	void CompositeGeoSolid::DivideTriangleByRemovingEdges(const GsIt& itMain)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		if (setIesDivider.empty())
		{
			if (!setNewIvs.empty())
			{
				DivideTriangle(itMain);
			}
			else
			{
				GeoSolidTriangle& gst = gstMain;
				const GsIt& it = itMain;
				if (gst.intersectSrc.from)
				{
					WhetherTriangleIntersectGeoSolid(it, false);
					if (!gst.intersectSrc.hasCheckedInside)
					{
						gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, true);
						gst.intersectSrc.hasCheckedInside = true;
					}
				}
				else if (gst.intersectCut.from)
				{
					WhetherTriangleIntersectGeoSolid(it, true);
					if (!gst.intersectCut.hasCheckedInside)
					{
						gst.intersectCut.inside = IsTriangleInGeoSolid(gst, false);
						gst.intersectCut.hasCheckedInside = true;
					}
				}
			}
			return;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Before starting:");
			PrintBeforeDivision(itMain);
		}
		#endif
		RemoveShorterEdgesInEdge(itMain);
		RemoveColinearDividerVertex(itMain);
		std::list<GsIe> listIesInEdge;
		DivideTriangleEdges(itMain, listIesInEdge);
		std::list<GsIe> listIesOfPolygon;
		{
			//std::unordered_set<GsIe> setIesAll = gstMain.setIesDivider;
			//setIesAll.emplace(gstMain.ie0);
			//setIesAll.emplace(gstMain.ie1);
			//setIesAll.emplace(gstMain.ie2);
			//GsIv ivFrom = gstMain.iv0;
			//GsIv ivIterate = gstMain.iv0;
			//GsIe iePath = gstMain.ie0;
			//do 
			//{
			//	if (ivFrom == gstMain.iv2 && ivIterate == gstMain.iv0)
			//	{
			//		break;
			//	}
			//	if (ivFrom == gstMain.iv1 && ivIterate == gstMain.iv2)
			//	{
			//		ivFrom == gstMain.iv2;
			//		iePath = gstMain.ie1;
			//	}
			//	if (ivFrom == gstMain.iv0 && ivIterate == gstMain.iv1)
			//	{
			//		ivFrom == gstMain.iv1;
			//		iePath = gstMain.ie2;
			//	}
			//	GeoSolidVertex& gsv = GetVertex(ivIterate);
			//	for (auto itGsvn = gsv.listNeighbors.begin(); itGsvn != gsv.listNeighbors.end(); ++itGsvn)
			//	{
			//		const GsIv& ivNeighbor = itGsvn->iv;
			//		if (ivNeighbor == gstMain.iv1 && iePath == gstMain.ie0)
			//		{
			//		}
			//		else if (ivNeighbor == gstMain.iv2 && iePath == gstMain.ie2)
			//		{
			//		}
			//		else if (ivNeighbor == gstMain.iv0 && iePath == gstMain.ie1)
			//		{
			//		}
			//		else
			//		{
			//			if (gstMain.setNewIvs.find(ivNeighbor) == gstMain.setNewIvs.end())
			//			{
			//				continue;
			//			}
			//			int ieInWhichIvIn;
			//			bool inEdge = IsVertexInLine(ivNeighbor, iePath);
			//			if (!inEdge)
			//			{
			//				continue;
			//			}
			//		}
			//		GeoSolidEdge gse(ivIterate, ivNeighbor, gstMain.intersectSrc.from, gstMain.intersectCut.from);
			//		GsIe ie = AddEdge(gse);
			//		listIesOfPolygon.emplace_back(ie);
			//		ivIterate = ivNeighbor;
			//		break;
			//	}
			//} while (true);
			GsIv ivIterate = gstMain.iv0;
			GsIv ivStart = gstMain.iv0;
			for (auto itIe = listIesInEdge.begin(); itIe != listIesInEdge.end(); )
			{
				const GsIe& ie = *itIe;
				GeoSolidEdge& gse = GetEdge(ie);
				GsIv ivOther;
				if (gse.iv0 == ivIterate)
				{
					ivOther = gse.iv1;
				}
				else if (gse.iv1 == ivIterate)
				{
					ivOther = gse.iv0;
				}
				else
				{
					++itIe;
					continue;
				}
				if (ivOther == ivStart)
				{
					listIesOfPolygon.emplace_back(ie);
					break;
				}
				const bool inEdge = 
					ivOther == gstMain.iv1 ||
					ivOther == gstMain.iv2 ||
					IsVertexInTriangleEdge(gstMain, ivOther);
				if (!inEdge)
				{
					++itIe;
					continue;
				}
				ivIterate = ivOther;
				listIesOfPolygon.emplace_back(ie);
				listIesInEdge.erase(itIe);
				if (listIesInEdge.empty())
				{
					break;
				}
				itIe = listIesInEdge.begin();
			}
		}
		std::set<GsIv> setIvsAll;
		setIvsAll.insert(setNewIvs.begin(), setNewIvs.end());
		//for (const GsIv& iv : setNewIvs)
		//{
		//	bool inEdge = false;
		//	for (const GsIe& ie : listIesOfPolygon)
		//	{
		//		GeoSolidEdge& gse = GetEdge(ie);
		//		if (gse.iv0 == iv || gse.iv1 == iv)
		//		{
		//			inEdge = true;
		//			break;
		//		}
		//	}
		//	if (inEdge)
		//	{
		//		continue;
		//	}
		//	setIvsAll.emplace(iv);
		//}
		std::unordered_set<GsIe> setIesRemoved;
		std::unordered_set<GsIv> setIvsRemoved;
		std::list<GsIt> listItsResult;
		gstMain.markRemoval = true;
		//收集已生成的线段
		std::unordered_set<GsIe> setIesFinal;
		setIesFinal.insert(listIesOfPolygon.begin(), listIesOfPolygon.end());
		setIesFinal.insert(setIesDivider.begin(), setIesDivider.end());
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			std::unordered_set<GsIv> setIvs;
			setIvs.insert(setNewIvs.begin(), setNewIvs.end());
			setIvs.emplace(gstMain.iv0);
			setIvs.emplace(gstMain.iv1);
			setIvs.emplace(gstMain.iv2);
			std::ostringstream ossVs;
			auto itIv = setIvs.begin();
			GeoGebraAppendVertexCmd(ossVs, *itIv);
			for (++itIv; itIv != setIvs.end(); ++itIv)
			{
				ossVs << ',';
				GeoGebraAppendVertexCmd(ossVs, *itIv);
			}
			std::ostringstream ossEs;
			auto itIe = listIesOfPolygon.begin();
			GeoGebraAppendEdgeCmd(ossEs, *itIe);
			for (++itIe; itIe != listIesOfPolygon.end(); ++itIe)
			{
				ossEs << ',';
				GeoGebraAppendEdgeCmd(ossEs, *itIe);
			}
			for (const GsIe& ie : setIesDivider)
			{
				ossEs << ',';
				GeoGebraAppendEdgeCmd(ossEs, ie);
			}
			WarningString("Edges of the triangle and dividers before:");
			GeoGebraExecuteCmdLists(ossVs, ossEs);
		}
		#endif
		for (auto itIe = listIesOfPolygon.begin(); itIe != listIesOfPolygon.end(); )
		{
			const GsIe& ieMain = *itIe;
			GeoSolidEdge& gseMain = GetEdge(ieMain);
			auto itIeNext = std::next(itIe);
			if (itIeNext == listIesOfPolygon.end())
			{
				itIeNext = listIesOfPolygon.begin();
			}
			if (setIesDivider.empty())
			{
				if (listIesOfPolygon.size() == 3)
				{
					const GsIv& iv0 = gseMain.iv0;
					const GsIv& iv1 = gseMain.iv1;
					GsIv iv2;
					GeoSolidEdge& gseNext = GetEdge(*itIeNext);
					if (gseNext.iv0 != iv0 && gseNext.iv0 != iv1)
					{
						iv2 = gseNext.iv0;
					}
					else
					{
						iv2 = gseNext.iv1;
					}
					GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(iv0, iv1, iv2, gstMain);
					CorrectClockwise(gst);
					gst->iuv0 = AddUvInTriangle(gst->iv0, itMain);
					gst->iuv1 = AddUvInTriangle(gst->iv1, itMain);
					gst->iuv2 = AddUvInTriangle(gst->iv2, itMain);
					const GsIt itNew = AddTriangle(gst);
					listItsResult.emplace_back(itNew);
					break;
				}
				else if (listIesOfPolygon.size() < 3)
				{
					break;
				}
			}
			auto itIePrev = itIe == listIesOfPolygon.begin() ? std::prev(listIesOfPolygon.end()) : std::prev(itIe);
			GsIv ivNearest;
			GsIe ie0New;
			GsIe ie1New;
			bool find = false;
			const GsVector3& v0 = GetVertex(gseMain.iv0).v;
			const GsVector3& v1 = GetVertex(gseMain.iv1).v;
			//const GsVector3 vm = (v0 + v1) * 0.5;
			GsDigit minD = std::numeric_limits<float>::max();
			for (const GsIv& ivDivider : setIvsAll)
			{
				if (ivDivider == gseMain.iv0 || ivDivider == gseMain.iv1)
				{
					continue;
				}
				const bool isColinear = IsParalleled(gseMain.iv0, gseMain.iv1, ivDivider, 1e-7);
				if (isColinear)
				{
					continue;
				}
				GeoSolidEdge* gse0 = GeoSolid::obtain<GeoSolidEdge>(gseMain.iv0, ivDivider, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				GeoSolidEdge* gse1 = GeoSolid::obtain<GeoSolidEdge>(gseMain.iv1, ivDivider, gstMain.intersectSrc.from, gstMain.intersectCut.from);
				const GsIe ie0 = AddEdge(gse0);
				const GsIe ie1 = AddEdge(gse1);
				GeoSolidEdge::recycle(gse0);
				GeoSolidEdge::recycle(gse1);
				{
					//排除已生成的边
					auto itIeRemoved = setIesRemoved.find(ie0);
					if (itIeRemoved != setIesRemoved.end())
					{
						continue;
					}
					itIeRemoved = setIesRemoved.find(ie1);
					if (itIeRemoved != setIesRemoved.end())
					{
						continue;
					}
				}
				{
					//不经过已移除的点
					bool cross = false;
					for (const GsIv& ivRemoved : setIvsRemoved)
					{
						cross = IsParalleled(gseMain.iv0, ivRemoved, ivDivider);
						if (cross)
						{
							break;
						}
					}
					if (cross)
					{
						continue;
					}
					for (const GsIv& ivRemoved : setIvsRemoved)
					{
						cross = IsParalleled(gseMain.iv1, ivRemoved, ivDivider);
						if (cross)
						{
							break;
						}
					}
					if (cross)
					{
						continue;
					}
				}
				//最多只有一条边是现有边
				int ie0Check = -1;
				int ie1Check = -1;
				//TODO: 2024-02-26 16:55:19: 可能存在为前两个，或后两个的情况。不过现在已在异常中处理。
				if (ie0 == *itIePrev || ie0 == *itIeNext)
				{
					ie1Check = ie1;
				}
				else if (ie1 == *itIePrev || ie1 == *itIeNext)
				{
					ie0Check = ie0;
				}
				else
				{
					ie0Check = ie0;
					ie1Check = ie1;
				}
				bool findIe0Intersection = false;
				bool findIe1Intersection = false;
				if (ie0Check >= 0)
				{
					//将与现有边相交的情况排除
					for (const GsIe& ieExist : setIesFinal)
					{
						if (ieExist == ie0)
						{
							continue;
						}
						findIe0Intersection = WhetherEdgeCrossEdge(ie0, ieExist);
						if (findIe0Intersection)
						{
							break;
						}
					}
					if (!findIe0Intersection)
					{
						//将经过现有顶点的边排除
						for (const GsIv& ivOther : setIvsAll)
						{
							if (ivOther == gseMain.iv0 || ivOther == ivDivider)
							{
								continue;
							}
							findIe0Intersection = IsVertexInEdge(ivOther, ie0, 1.6e-4);
							if (findIe0Intersection)
							{
								break;
							}
						}
					}
				}
				if (findIe0Intersection)
				{
					continue;
				}
				if (ie1Check >= 0)
				{
					for (const GsIe& ieExist : setIesFinal)
					{
						if (ieExist == ie1)
						{
							continue;
						}
						findIe1Intersection = WhetherEdgeCrossEdge(ie1, ieExist);
						if (findIe1Intersection)
						{
							break;
						}
					}
					if (!findIe1Intersection)
					{
						for (const GsIv& ivOther : setIvsAll)
						{
							if (ivOther == gseMain.iv1 || ivOther == ivDivider)
							{
								continue;
							}
							findIe1Intersection = IsVertexInEdge(ivOther, ie1, 1.6e-4);
							if (findIe1Intersection)
							{
								break;
							}
						}
					}
				}
				if (findIe1Intersection)
				{
					continue;
				}
				find = true;
				const GsDigit d = GetVertexDistanceFromEdge(ivDivider, ieMain);
				if (minD > d)
				{
					//找近点连接
					minD = d;
					ivNearest = ivDivider;
					ie0New = ie0;
					ie1New = ie1;
				}
			}
			if (!find)
			{
				++itIe;
				continue;
			}
			GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(gseMain.iv0, gseMain.iv1, ivNearest, gstMain);
			CorrectClockwise(gst);
			gst->iuv0 = AddUvInTriangle(gst->iv0, itMain);
			gst->iuv1 = AddUvInTriangle(gst->iv1, itMain);
			gst->iuv2 = AddUvInTriangle(gst->iv2, itMain);
			const GsIt itNew = AddTriangle(gst);
			listItsResult.emplace_back(itNew);
			setIesFinal.emplace(ie0New);
			setIesFinal.emplace(ie1New);
			auto itIe0InEdge = listIesOfPolygon.end();
			auto itIe1InEdge = listIesOfPolygon.end();
			int ieOther = -1;
			if (ie0New == *itIePrev)
			{
				itIe0InEdge = itIePrev;
				ieOther = ie1New;
			}
			else if (ie0New == *itIeNext)
			{
				itIe0InEdge = itIeNext;
				ieOther = ie1New;
			}
			if (ie1New == *itIePrev)
			{
				itIe1InEdge = itIePrev;
				ieOther = ie0New;
			}
			else if (ie1New == *itIeNext)
			{
				itIe1InEdge = itIeNext;
				ieOther = ie0New;
			}
			if (itIe0InEdge != listIesOfPolygon.end() || itIe1InEdge != listIesOfPolygon.end())
			{
				if (itIe0InEdge != listIesOfPolygon.end() && itIe1InEdge != listIesOfPolygon.end())
				{
					if (listIesOfPolygon.size() > 3)
					{
						//其中一个点处在未生成三角形的多边形中，另外两个点偏移剩下的多边形，将被移除
						std::unordered_map<GsIv, bool> mapIvUse;
						GeoSolidEdge& gse0 = GetEdge(*itIe);
						GeoSolidEdge& gse1 = GetEdge(*itIe0InEdge);
						GeoSolidEdge& gse2 = GetEdge(*itIe1InEdge);
						mapIvUse[gse0.iv0] = false;
						mapIvUse[gse0.iv1] = false;
						mapIvUse[gse1.iv0] = false;
						mapIvUse[gse1.iv1] = false;
						mapIvUse[gse2.iv0] = false;
						mapIvUse[gse2.iv1] = false;
						itIe = listIesOfPolygon.erase(itIe);
						itIe = listIesOfPolygon.erase(itIe0InEdge);
						itIe = listIesOfPolygon.erase(itIe1InEdge);
						if (itIe == listIesOfPolygon.end())
						{
							itIe = listIesOfPolygon.begin();
						}
						GeoSolidEdge& gseNext = GetEdge(*itIe);
						mapIvUse[gseNext.iv0] = true;
						mapIvUse[gseNext.iv1] = true;
						for (const auto& p : mapIvUse)
						{
							if (!p.second)
							{
								setIvsAll.erase(p.first);
								setIvsRemoved.emplace(p.first);
							}
						}
						continue;
					}
					else
					{
						break;
					}
				}
				else
				{
					//（1）有一条新边在外圈（或三角形）边上时
					auto itIeInEdge = itIe0InEdge != listIesOfPolygon.end() ? itIe0InEdge : itIe1InEdge;
					GeoSolidEdge& gseInEdge = GetEdge(*itIeInEdge);
					if (setIesDivider.find(ieOther) != setIesDivider.end())
					{
						//①另一条新边在内边Divider时，将Divider对面的顶点去掉
						//使用Divider替换两条外边（目标边与新边）
						//该内边变成外边
						int ivOpposite = -1;
						if (gseMain.iv0 == gseInEdge.iv0 || gseMain.iv0 == gseInEdge.iv1)
						{
							ivOpposite = gseMain.iv0;
						}
						else if (gseMain.iv1 == gseInEdge.iv0 || gseMain.iv1 == gseInEdge.iv1)
						{
							ivOpposite = gseMain.iv1;
						}
						SANDBOX_ASSERT(ivOpposite >= 0);
						if (ivOpposite >= 0)
						{
							setIvsAll.erase(ivOpposite);
							setIvsRemoved.emplace(ivOpposite);
						}
						setIesDivider.erase(ieOther);
					}
					else
					{
						//②将目标边与该新边的共同点移除
						//使用两条边替换一条边
						int ivCommon = -1;
						if (gseInEdge.iv0 == gseMain.iv0 || gseInEdge.iv0 == gseMain.iv1)
						{
							ivCommon = gseInEdge.iv0;
						}
						else if (gseInEdge.iv1 == gseMain.iv0 || gseInEdge.iv1 == gseMain.iv1)
						{
							ivCommon = gseInEdge.iv1;
						}
						SANDBOX_ASSERT(ivCommon >= 0);
						if (ivCommon >= 0)
						{
							setIvsAll.erase(ivCommon);
							setIvsRemoved.emplace(ivCommon);
						}
						setIesDivider.erase(*itIeInEdge);
					}
					setIesRemoved.emplace(*itIe);
					setIesDivider.erase(*itIe);
					listIesOfPolygon.erase(itIeInEdge);
					listIesOfPolygon.erase(itIe);
					if (itIeInEdge == itIePrev)
					{
						itIe = listIesOfPolygon.emplace(itIeNext, ieOther);
					}
					else
					{
						auto itIeWhere = std::next(itIePrev);
						if (itIeWhere == listIesOfPolygon.end())
						{
							itIeWhere = listIesOfPolygon.begin();
						}
						auto itIeWherePrev = std::prev(itIeWhere == listIesOfPolygon.begin() ? listIesOfPolygon.end() : itIeWhere);
						if (*itIeWhere == ieOther)
						{
							itIe = listIesOfPolygon.erase(itIeWhere);
							--itIe;
						}
						else if (*itIeWherePrev == ieOther)
						{
							itIe = listIesOfPolygon.erase(itIeWherePrev);
							--itIe;
						}
						else
						{
							itIe = listIesOfPolygon.emplace(itIeWhere, ieOther);
						}
					}
				}
			}
			else
			{
				//（2）产生两条真正的新边
				setIesRemoved.emplace(*itIe);
				{
					setIesDivider.erase(*itIe);
					setIesDivider.erase(ie0New);
					setIesDivider.erase(ie1New);
				}
				listIesOfPolygon.erase(itIe);
				GeoSolidEdge& gseNext = GetEdge(*itIeNext);
				int ivCommon = -1;
				if (gseNext.iv0 == gseMain.iv0 || gseNext.iv0 == gseMain.iv1)
				{
					ivCommon = gseNext.iv0;
				}
				else if (gseNext.iv1 == gseMain.iv0 || gseNext.iv1 == gseMain.iv1)
				{
					ivCommon = gseNext.iv1;
				}
				SANDBOX_ASSERT(ivCommon >= 0);
				GeoSolidEdge& gse0 = GetEdge(ie0New);
				GeoSolidEdge& gse1 = GetEdge(ie1New);
				if (gse0.iv0 == ivCommon || gse0.iv1 == ivCommon)
				{
					listIesOfPolygon.emplace(itIeNext, ie1New);
					itIe = listIesOfPolygon.emplace(itIeNext, ie0New);
				}
				else if (gse1.iv0 == ivCommon || gse1.iv1 == ivCommon)
				{
					listIesOfPolygon.emplace(itIeNext, ie0New);
					itIe = listIesOfPolygon.emplace(itIeNext, ie1New);
				}
				else
				{
					SANDBOX_ASSERT(false);
				}
			}
			++itIe;
		}
		for (const GsIt& it : listItsResult)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.intersectSrc.from)
			{
				WhetherTriangleIntersectGeoSolid(it, false);
				if (!gst.intersectSrc.hasCheckedInside)
				{
					gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, true);
					gst.intersectSrc.hasCheckedInside = true;
				}
			}
			else if (gst.intersectCut.from)
			{
				WhetherTriangleIntersectGeoSolid(it, true);
				if (!gst.intersectCut.hasCheckedInside)
				{
					gst.intersectCut.inside = IsTriangleInGeoSolid(gst, false);
					gst.intersectCut.hasCheckedInside = true;
				}
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			std::unordered_set<GsIv> setIvs;
			setIvs.insert(setNewIvs.begin(), setNewIvs.end());
			setIvs.emplace(gstMain.iv0);
			setIvs.emplace(gstMain.iv1);
			setIvs.emplace(gstMain.iv2);
			std::ostringstream ossVs;
			auto itIv = setIvs.begin();
			GeoGebraAppendVertexCmd(ossVs, *itIv);
			for (++itIv; itIv != setIvs.end(); ++itIv)
			{
				ossVs << ',';
				GeoGebraAppendVertexCmd(ossVs, *itIv);
			}
			std::ostringstream ossEs;
			auto itIe = setIesFinal.begin();
			GeoGebraAppendEdgeCmd(ossEs, *itIe);
			for (++itIe; itIe != setIesFinal.end(); ++itIe)
			{
				ossEs << ',';
				GeoGebraAppendEdgeCmd(ossEs, *itIe);
			}
			WarningString("Edges of the triangle and dividers:");
			GeoGebraExecuteCmdLists(ossVs, ossEs);

			WarningString("After division:");
			PrintAfterDivision(itMain, listItsResult);
			WarningString("-------------------------DivideTriangleByRemovingEdges-------------------------");
		}
		#endif
	}

	bool CompositeGeoSolid::WhetherTriangleHasEdge(const GeoSolidTriangle& gst, const GsIe& ie)
	{
		GeoSolidEdge& gse = GetEdge(ie);
		return (gst.iv0 == gse.iv0 && gst.iv1 == gse.iv1)
			|| (gst.iv1 == gse.iv0 && gst.iv0 == gse.iv1)
			|| (gst.iv0 == gse.iv0 && gst.iv2 == gse.iv1)
			|| (gst.iv2 == gse.iv0 && gst.iv0 == gse.iv1)
			|| (gst.iv1 == gse.iv0 && gst.iv2 == gse.iv1)
			|| (gst.iv2 == gse.iv0 && gst.iv1 == gse.iv1)
		;
	}

	bool CompositeGeoSolid::IsVertexInTriangleEdge(const GeoSolidTriangle& gst, const GsIv& iv, int* pie, const GsDigit errorThreshold)
	{
		const GsVector3& vp = GetVertex(iv).v;
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		if (GeometryMath::IsParalleled(va, vp, vb, errorThreshold))
		{
			if (pie)
			{
				*pie = gst.ie0;
			}
			return true;
		}
		if (GeometryMath::IsParalleled(va, vp, vc, errorThreshold))
		{
			if (pie)
			{
				*pie = gst.ie1;
			}
			return true;
		}
		if (GeometryMath::IsParalleled(vb, vp, vc, errorThreshold))
		{
			if (pie)
			{
				*pie = gst.ie2;
			}
			return true;
		}
		if (pie)
		{
			*pie = -1;
		}
		return false;
	}

	GsIe CompositeGeoSolid::GetEdgeInTriangleEdge(const GeoSolidTriangle& gst, const GsIe& ie)
	{
		GsIe ieSrc = GsIe(-1);
		if (IsEdgeInEdge(ie, gst.ie0))
		{
			ieSrc = gst.ie0;
		}
		else if (IsEdgeInEdge(ie, gst.ie1))
		{
			ieSrc = gst.ie1;
		}
		else if (IsEdgeInEdge(ie, gst.ie2))
		{
			ieSrc = gst.ie2;
		}
		return ieSrc;
	}

	bool CompositeGeoSolid::IsEdgeInEdge(const GsIe& ie, const GsIe& ieLong)
	{
		GeoSolidEdge& gse = GetEdge(ie);
		GeoSolidEdge& gseLong = GetEdge(ieLong);
		const GsVector3& va = GetVertex(gseLong.iv0).v;
		const GsVector3& vb = GetVertex(gseLong.iv1).v;
		const GsVector3& vc = GetVertex(gse.iv0).v;
		const GsVector3& vd = GetVertex(gse.iv1).v;
		const GsVector3 vab = vb - va;
		const GsVector3 vcd = vd - vc;
		const GsVector3 vn = CrossProduct(vab, vcd);
		if (!IsZero(vn.LengthSqr()))
		{
			//不共线
			return false;
		}
		const GsVector3 vac = vc - va;
		const GsVector3 vad = vd - va;
		const GsVector3 vbc = vc - vb;
		const GsVector3 vbd = vd - vb;
		//同时在A、B的同一侧
		return DotProduct(vac, vad) > 0 && DotProduct(vbc, vbd) > 0;
	}

	bool CompositeGeoSolid::WhetherEdgeCrossEdge(const GsIe& ie0, const GsIe& ie1)
	{
		GeoSolidEdge& gse0 = GetEdge(ie0);
		GeoSolidEdge& gse1 = GetEdge(ie1);
		const GsVector3& va = GetVertex(gse0.iv0).v;
		const GsVector3& vb = GetVertex(gse0.iv1).v;
		const GsVector3& vc = GetVertex(gse1.iv0).v;
		const GsVector3& vd = GetVertex(gse1.iv1).v;
		return WhetherLineIntersectLine(va, vb, vc, vd);
	}

	bool CompositeGeoSolid::WhetherEdgeCrossTriangle(const GsIe& ie, const GsIt& it)
	{
		const GeoSolidEdge& gse = GetEdge(ie);
		const GeoSolidTriangle& gst = GetTriangle(it);
		const GsVector3& vp = GetVertex(gse.iv0).v;
		const GsVector3& vq = GetVertex(gse.iv1).v;
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const bool crossAB = WhetherLineIntersectLine(vp, vq, va, vb);
		if (crossAB)
		{
			return true;
		}
		const GsVector3& vc = GetVertex(gst.iv2).v;
		const bool crossAC = WhetherLineIntersectLine(vp, vq, va, vc);
		if (crossAC)
		{
			return true;
		}
		const bool crossBC = WhetherLineIntersectLine(vp, vq, vb, vc);
		if (crossBC)
		{
			return true;
		}
		return false;
	}

	int CompositeGeoSolid::WhetherEdgeCrossTriangle(const GsIe& ie, const GsIt& it, GsVector3& vix, GsVector3& viy)
	{
		const GeoSolidEdge& gse = GetEdge(ie);
		const GeoSolidTriangle& gst = GetTriangle(it);
		const GsVector3& vp = GetVertex(gse.iv0).v;
		const GsVector3& vq = GetVertex(gse.iv1).v;
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		GsVector3 viAB;
		GsVector3 viAC;
		GsVector3 viBC;	
		const bool crossAB = WhetherLineIntersectLine(vp, vq, va, vb, &viAB);
		const bool crossAC = WhetherLineIntersectLine(vp, vq, va, vc, &viAC);
		const bool crossBC = WhetherLineIntersectLine(vp, vq, vb, vc, &viBC);
		int c = 0;
		c += crossAB;
		c += crossAC;
		c += crossBC;
		if (c == 1)
		{
			if (crossAB)
			{
				vix = viAB;
			}
			else if (crossAC)
			{
				vix = viAC;
			}
			else if (crossBC)
			{
				vix = viBC;
			}
		}
		else if (c == 2)
		{
			if (crossAB && crossAC)
			{
				vix = viAB;
				viy = viAC;
			}
			else if (crossAB && crossBC)
			{
				vix = viAB;
				viy = viBC;
			}
			else if (crossAC && crossBC)
			{
				vix = viAC;
				viy = viBC;
			}
		}
		return c;
	}

	GsIt CompositeGeoSolid::AddTriangle(GeoSolidTriangle* gstIn)
	{
		//无需查找。整个过程不存在添加相同三角形的情况
		int index = FindTriangle(gstIn->iv0, gstIn->iv1, gstIn->iv2);
		if (index >= 0 && index < m_daTriangles.size())
		{
			GsIt it = (GsIt)index;
			GeoSolidTriangle& gst = GetTriangle(it);
			gst.ivn = gstIn->ivn;
			if (gstIn->intersectSrc.from)
			{
				gst.ic = gstIn->ic;
			}
			gst.intersectSrc |= gstIn->intersectSrc;
			gst.intersectCut |= gstIn->intersectCut;
			gst.markRemoval = gstIn->markRemoval;
			return it;
		}
		GeoSolidEdge* gseNew0 = GeoSolid::obtain<GeoSolidEdge>(gstIn->iv0, gstIn->iv1, gstIn->intersectSrc.from, gstIn->intersectCut.from);
		GeoSolidEdge* gseNew1 = GeoSolid::obtain<GeoSolidEdge>(gstIn->iv0, gstIn->iv2, gstIn->intersectSrc.from, gstIn->intersectCut.from);
		GeoSolidEdge* gseNew2 = GeoSolid::obtain<GeoSolidEdge>(gstIn->iv1, gstIn->iv2, gstIn->intersectSrc.from, gstIn->intersectCut.from);
		const GsIe ie0 = AddEdge(gseNew0);
		const GsIe ie1 = AddEdge(gseNew1);
		const GsIe ie2 = AddEdge(gseNew2);
		GeoSolidEdge::recycle(gseNew0);
		GeoSolidEdge::recycle(gseNew1);
		GeoSolidEdge::recycle(gseNew2);
		GeoSolidSet<GsIe>& setIesDivider = gstIn->GetIesDivider();
		setIesDivider.insert(ie0);
		setIesDivider.insert(ie1);
		setIesDivider.insert(ie2);
		gstIn->ie0 = ie0;
		gstIn->ie1 = ie1;
		gstIn->ie2 = ie2;
		GeoSolidVertex& gsv0 = GetVertex((GsIndex)gstIn->iv0);
		GeoSolidVertex& gsv1 = GetVertex((GsIndex)gstIn->iv1);
		GeoSolidVertex& gsv2 = GetVertex((GsIndex)gstIn->iv2);
		const GsVector3& v0 = gsv0.v;
		const GsVector3& v1 = gsv1.v;
		const GsVector3& v2 = gsv2.v;
		gstIn->area = GetArea(v0, v1, v2);
		gstIn->markRemoval = !gstIn->IsValid();
		GeoSolidTriangle* gstNew = GeoSolid::obtain<GeoSolidTriangle>(gstIn);
		m_daTriangles.emplace_back(gstNew);
		const GsIndex it = m_daTriangles.size() - 1;
		return (GsIt)it;
	}

	void CompositeGeoSolid::WhetherTriangleIntersectGeoSolid(const GsIt& it, bool targetSrc)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		//TODO: 2023-03-30 11:18:26: 优化：循环合并
		//TODO: 2023-03-30 11:45:14: 优化：顺序
		//if_else代码逻辑相同。这里为了兼容GeoSolidInteractState的位域优化而做出的修改。
		if (targetSrc)
		{
			if (!gst.intersectSrc.hasCheckedInside)
			{
				gst.intersectSrc.inside = IsTriangleInGeoSolid(gst, targetSrc);
				gst.intersectSrc.hasCheckedInside = true;
			}
			//下方keep保留Src部分的共面
			//if (!gst.intersectSrc.from)
			//{
			//	gst.intersectSrc.from = WhetherTriangleIsPartOfTriangle(it, targetSrc);
			//	if (gst.intersectSrc.from)
			//	{
			//		gst.intersectSrc.hasCheckedCoincide = true;
			//		gst.intersectSrc.coincide = true;
			//	}
			//}
			if (!gst.intersectSrc.hasCheckedCoincide)
			{
				gst.intersectSrc.coincide = WhetherTriangleCoincideWithGeoSolid(it, targetSrc);
				gst.intersectSrc.hasCheckedCoincide = true;
			}
		}
		else
		{
			if (!gst.intersectCut.hasCheckedInside)
			{
				gst.intersectCut.inside = IsTriangleInGeoSolid(gst, targetSrc);
				gst.intersectCut.hasCheckedInside = true;
			}
			if (!gst.intersectCut.from)
			{
				gst.intersectCut.from = WhetherTriangleIsPartOfTriangle(it, targetSrc);
				if (gst.intersectCut.from)
				{
					gst.intersectCut.hasCheckedCoincide = true;
					gst.intersectCut.coincide = true;
				}
			}
			if (!gst.intersectCut.hasCheckedCoincide)
			{
				gst.intersectCut.coincide = WhetherTriangleCoincideWithGeoSolid(it, targetSrc);
				gst.intersectCut.hasCheckedCoincide = true;
			}
		}
	}

	bool CompositeGeoSolid::WhetherTriangleCoincideWithGeoSolid(const GsIt& itMain, bool targetSrc)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		const int ct = targetSrc ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = targetSrc ? 0 : m_cSrcTriangle;
		const GsIv iva = gstMain.iv0;
		const GsIv ivb = gstMain.iv1;
		const GsIv ivc = gstMain.iv2;
		const GsVector3& va = GetVertex(iva).v;
		const GsVector3& vb = GetVertex(ivb).v;
		const GsVector3& vc = GetVertex(ivc).v;
		for (int i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gstMain.ivn != gst.ivn)
			{
				//不平行
				continue;
			}
			const GsIv ivd = gst.iv0;
			const GsIv ive = gst.iv1;
			const GsIv ivf = gst.iv2;
			const GsVector3& vd = GetVertex(ivd).v;
			const GsVector3& ve = GetVertex(ive).v;
			const GsVector3& vf = GetVertex(ivf).v;
			//判断四点共面
			//const GsDigit stpA = GetSignedVolumeOfParallelepiped(va, vd, ve, vf);
			//if (!IsZero(stpA))
			//{
			//	const GsDigit stpB = GetSignedVolumeOfParallelepiped(vb, vd, ve, vf);
			//	if (!IsZero(stpB))
			//	{
			//		const GsDigit stpC = GetSignedVolumeOfParallelepiped(vc, vd, ve, vf);
			//		if (!IsZero(stpC))
			//		{
			//			continue;
			//		}
			//	}
			//}
			//const GsDigit stpA = GetSignedVolumeOfParallelepiped(va, vd, ve, vf);
			//if (!IsZero(stpA))
			//{
			//	continue;
			//}
			//const GsDigit stpB = GetSignedVolumeOfParallelepiped(vb, vd, ve, vf);
			//if (!IsZero(stpB))
			//{
			//	continue;
			//}
			//const GsDigit stpC = GetSignedVolumeOfParallelepiped(vc, vd, ve, vf);
			//if (!IsZero(stpC))
			//{
			//	continue;
			//}
			const GsVector3& vn = m_daNormals[gst.ivn];
			const GsVector3 vad = vd - va;
			const GsDigit dp0 = DotProduct(vad, vn);
			if (!IsZero(dp0, 8e-6))
			{
				const GsVector3 vae = ve - va;
				const GsDigit dp1 = DotProduct(vae, vn);
				if (!IsZero(dp1, 8e-6))
				{
					const GsVector3 vaf = vf - va;
					const GsDigit dp2 = DotProduct(vaf, vn);
					if (!IsZero(dp2, 8e-6))
					{
						continue;
					}
				}
			}
			//有一条线段穿过三角形，即有重合部分
			bool crossTri =
				WhetherLineIntersectLine(va, vb, vd, ve) ||
				WhetherLineIntersectLine(va, vb, vd, vf) ||
				WhetherLineIntersectLine(va, vb, ve, vf) ||
				WhetherLineIntersectLine(va, vc, vd, ve) ||
				WhetherLineIntersectLine(va, vc, vd, vf) ||
				WhetherLineIntersectLine(va, vc, ve, vf) ||
				WhetherLineIntersectLine(vb, vc, vd, ve) ||
				WhetherLineIntersectLine(vb, vc, vd, vf) ||
				WhetherLineIntersectLine(vb, vc, ve, vf);
			if (crossTri)
			{
				return true;
			}
			//下方特殊情况判断：点在边上、点重合与点在外侧
			//点在三角形所在平面上，共有4中状态：内部、在边上、重合、外部
			GsIe ieInWhichAIs = GsIe(-1);
			GsIe ieInWhichBIs = GsIe(-1);
			GsIe ieInWhichCIs = GsIe(-1);
			if (IsVertexInsideTriangle(iva, it, &ieInWhichAIs) ||
				IsVertexInsideTriangle(ivb, it, &ieInWhichBIs) ||
				IsVertexInsideTriangle(ivc, it, &ieInWhichCIs))
			{
				//实际为三个点都在内部
				return true;
			}
			const bool isAInEdge = ieInWhichAIs == gst.ie0 || ieInWhichAIs == gst.ie1 || ieInWhichAIs == gst.ie2;
			const bool isBInEdge = ieInWhichBIs == gst.ie0 || ieInWhichBIs == gst.ie1 || ieInWhichBIs == gst.ie2;
			const bool isCInEdge = ieInWhichCIs == gst.ie0 || ieInWhichCIs == gst.ie1 || ieInWhichCIs == gst.ie2;
			const bool isACoincide = iva == ivd || iva == ive || iva == ivf;
			const bool isBCoincide = ivb == ivd || ivb == ive || ivb == ivf;
			const bool isCCoincide = ivc == ivd || ivc == ive || ivc == ivf;
			const bool beIncluded = ((isAInEdge || isACoincide) && (isBInEdge || isBCoincide) && (isCInEdge || isCCoincide));
			if (beIncluded)
			{
				//三点重合
				//或两点重合、一点在边（已是一个三角形时，三点不共线）
				//或一点重合、两点在边（已是一个三角形时，三点不共线）
				//或三点在边（已是一个三角形时，三点不共线）
				return true;
			}
			int cCoincide = 0;
			cCoincide += isACoincide ? 1 : 0;
			cCoincide += isBCoincide ? 1 : 0;
			cCoincide += isCCoincide ? 1 : 0;
			int cInEdge = 0;
			cInEdge += isAInEdge ? 1 : 0;
			cInEdge += isBInEdge ? 1 : 0;
			cInEdge += isCInEdge ? 1 : 0;
			if (cInEdge == 0 && cCoincide == 1)
			{
				//相似三角形
				GsVector3 vCommon;
				GsVector3 vp1;
				GsVector3 vp2;
				GsVector3 vq1;
				GsVector3 vq2;
				if (isACoincide)
				{
					vCommon = va;
					vp1 = vb;
					vp2 = vc;
					if (iva == ivd)
					{
						vq1 = ve;
						vq2 = vf;
					}
					else if (iva == ive)
					{
						vq1 = vd;
						vq2 = vf;
					}
					else if (iva == ivf)
					{
						vq1 = vd;
						vq2 = ve;
					}
					else
					{
						continue;
					}
				}
				else if (isBCoincide)
				{
					vCommon = vb;
					vp1 = va;
					vp2 = vc;
					if (ivb == ivd)
					{
						vq1 = ve;
						vq2 = vf;
					}
					else if (ivb == ive)
					{
						vq1 = vd;
						vq2 = vf;
					}
					else if (ivb == ivf)
					{
						vq1 = vd;
						vq2 = ve;
					}
					else
					{
						continue;
					}
				}
				else if (isCCoincide)
				{
					vCommon = vc;
					vp1 = va;
					vp2 = vb;
					if (ivc == ivd)
					{
						vq1 = ve;
						vq2 = vf;
					}
					else if (ivc == ive)
					{
						vq1 = vd;
						vq2 = vf;
					}
					else if (ivc == ivf)
					{
						vq1 = vd;
						vq2 = ve;
					}
					else
					{
						continue;
					}
				}
				else
				{
					continue;
				}
				//CP1/CP2 == CQ1/CQ2
				//CP1 * CQ2 == CP2 * CQ1
				const GsVector3 vcp1 = vp1 - vCommon;
				const GsVector3 vcp2 = vp2 - vCommon;
				const GsVector3 vcq1 = vq1 - vCommon;
				const GsVector3 vcq2 = vq2 - vCommon;
				const GsDigit lsqrtCP1 = vcp1.LengthSqr();
				const GsDigit lsqrtCP2 = vcp2.LengthSqr();
				const GsDigit lsqrtCQ1 = vcq1.LengthSqr();
				const GsDigit lsqrtCQ2 = vcq2.LengthSqr();
				GsDigit e0, e1;
				e0 = lsqrtCP1 * lsqrtCQ2;
				e1 = lsqrtCP2 * lsqrtCQ1;
				if (IsZero(e0 - e1))
				{
					return true;
				}
				e0 = lsqrtCP1 * lsqrtCQ1;
				e1 = lsqrtCP2 * lsqrtCQ2;
				if (IsZero(e0 - e1))
				{
					return true;
				}
			}
			else if (cInEdge == 0 && cCoincide == 0)
			{
				//包含△DEF
				const bool include =
					IsVertexInsideTriangle(ivd, itMain) ||
					IsVertexInsideTriangle(ive, itMain) ||
					IsVertexInsideTriangle(ivf, itMain);
				if (include)
				{
					return true;
				}
			}
		}
		return false;
	}

	bool CompositeGeoSolid::WhetherTriangleIsPartOfTriangle(const GsIt& itMain, bool targetSrc)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		const int ct = targetSrc ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = targetSrc ? 0 : m_cSrcTriangle;
		const GsIv iva = gstMain.iv0;
		const GsIv ivb = gstMain.iv1;
		const GsIv ivc = gstMain.iv2;
		const GsVector3& va = GetVertex(iva).v;
		const GsVector3& vb = GetVertex(ivb).v;
		const GsVector3& vc = GetVertex(ivc).v;
		const GsVector3 vbc = (va + vb + vc) / 3.f;
		std::list<GsIt> listIt0;
		std::list<GsIt> listIt1;
		std::list<GsIt> listIt2;
		for (UInt32 i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.ivn != gstMain.ivn)
			{
				continue;
			}
			if (IsVertexOnTriangle(iva, it))
			{
				listIt0.emplace_back(it);
			}
			if (IsVertexOnTriangle(ivb, it))
			{
				listIt1.emplace_back(it);
			}
			if (IsVertexOnTriangle(ivc, it))
			{
				listIt2.emplace_back(it);
			}
		}
		if (listIt0.empty() || listIt1.empty() || listIt2.empty())
		{
			//其中一点不在三角形内
			return false;
		}
		bool findAllSame = false;
		for (auto itIt0 = listIt0.begin(); itIt0 != listIt0.end(); ++itIt0)
		{
			for (auto itIt1 = listIt1.begin(); itIt1 != listIt1.end(); ++itIt1)
			{
				if (*itIt0 != *itIt1)
				{
					continue;
				}
				for (auto itIt2 = listIt2.begin(); itIt2 != listIt2.end(); ++itIt2)
				{
					if (*itIt0 == *itIt2)
					{
						findAllSame = true;
						break;
					}
				}
				if (findAllSame)
				{
					break;
				}
			}
			if (findAllSame)
			{
				break;
			}
		}
		if (findAllSame)
		{
			return true;
		}
		//有两个点在同一个三角形内
		for (auto itIt0 = listIt0.begin(); itIt0 != listIt0.end(); ++itIt0)
		{
			const GsIt& it0 = *itIt0;
			for (auto itIt1 = listIt1.begin(); itIt1 != listIt1.end(); ++itIt1)
			{
				const GsIt& it1 = *itIt1;
				for (auto itIt2 = listIt2.begin(); itIt2 != listIt2.end(); ++itIt2)
				{
					const GsIt& it2 = *itIt2;
					if (it0 == it1 || it0 == it2 || it1 == it2)
					{
						//取其中一条判断
						GsIe ieCross0;
						GsIe ieCross1;
						GsIt itFar;
						GsIt itCommon;
						if (it0 == it1)
						{
							ieCross0 = gstMain.ie1;
							ieCross1 = gstMain.ie2;
							itFar = it2;
							itCommon = it0;
						}
						else if (it0 == it2)
						{
							ieCross0 = gstMain.ie0;
							ieCross1 = gstMain.ie2;
							itFar = it1;
							itCommon = it0;
						}
						else if (it1 == it2)
						{
							ieCross0 = gstMain.ie0;
							ieCross1 = gstMain.ie1;
							itFar = it0;
							itCommon = it1;
						}
						else
						{
							continue;
						}
						const bool bcInsideCommon = IsVertexInsideTriangle(vbc, itCommon);
						const bool bcInsideFar = IsVertexInsideTriangle(vbc, itFar);
						if (!bcInsideCommon && !bcInsideFar)
						{
							continue;
						}
						const bool cross0 = WhetherEdgeCrossTriangle(ieCross0, itFar);
						if (cross0)
						{
							return true;
						}
						const bool cross1 = WhetherEdgeCrossTriangle(ieCross1, itFar);
						return cross1;
					}
				}
			}
		}
		//三个点在各自三角形内
		for (auto itIt0 = listIt0.begin(); itIt0 != listIt0.end(); ++itIt0)
		{
			const GsIt& it0 = *itIt0;
			for (auto itIt1 = listIt1.begin(); itIt1 != listIt1.end(); ++itIt1)
			{
				const GsIt& it1 = *itIt1;
				if (it0 == it1)
				{
					continue;
				}
				const bool e0Cross = WhetherEdgeCrossTriangle(gstMain.ie0, it0) || WhetherEdgeCrossTriangle(gstMain.ie0, it1);
				if (!e0Cross)
				{
					continue;
				}
				for (auto itIt2 = listIt2.begin(); itIt2 != listIt2.end(); ++itIt2)
				{
					const GsIt& it2 = *itIt2;
					if (it0 == it2 || it1 == it2)
					{
						continue;
					}
					const bool e1Cross = WhetherEdgeCrossTriangle(gstMain.ie1, it0) || WhetherEdgeCrossTriangle(gstMain.ie1, it2);
					const bool e2Cross = WhetherEdgeCrossTriangle(gstMain.ie2, it1) || WhetherEdgeCrossTriangle(gstMain.ie2, it2);
					return e0Cross && e1Cross && e2Cross;
				}
			}
		}
		return false;
	}

	bool CompositeGeoSolid::WhetherTriangleIsPartOfTriangle2(const GsIt& itMain, bool targetSrc)
	{
		GeoSolidTriangle& gstMain = GetTriangle(itMain);
		const int ct = targetSrc ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = targetSrc ? 0 : m_cSrcTriangle;
		const GsIv iva = gstMain.iv0;
		const GsIv ivb = gstMain.iv1;
		const GsIv ivc = gstMain.iv2;
		const GsVector3& va = GetVertex(iva).v;
		const GsVector3& vb = GetVertex(ivb).v;
		const GsVector3& vc = GetVertex(ivc).v;
		bool isV0InTriangle = false;
		bool isV1InTriangle = false;
		bool isV2InTriangle = false;
		bool isE0InTriangle = false;
		bool isE1InTriangle = false;
		bool isE2InTriangle = false;
		for (int i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			GeoSolidTriangle& gst = GetTriangle(it);
			const GsIv ivd = gst.iv0;
			const GsIv ive = gst.iv1;
			const GsIv ivf = gst.iv2;
			const GsVector3& vd = GetVertex(ivd).v;
			const GsVector3& ve = GetVertex(ive).v;
			const GsVector3& vf = GetVertex(ivf).v;
			if (!GeometryMath::IsVertexOnTriangle(va, vd, ve, vf))
			{
				continue;
			}
			if (!GeometryMath::IsVertexOnTriangle(vb, vd, ve, vf))
			{
				continue;
			}
			if (!GeometryMath::IsVertexOnTriangle(vc, vd, ve, vf))
			{
				continue;
			}
			return true;
		}
		return false;
	}

	bool CompositeGeoSolid::IsVertexInGeoSolid(const GsIv iv, bool src)
	{
		const GsIv cv = GetVertexCount();
		WriteOnceBoolean& wob = GetWriteOnceBoolean(src ? m_aIvsInSrc : m_aIvsInCut, iv, cv);
		if (wob.c)
		{
			return wob.b;
		}
		wob.c = true;
		GeoSolidVertex& gsv = GetVertex(iv);
		const GsBoxBound& bb = src ? m_bbSrc : m_bbCut;
		const GsVector3& vp = gsv.v;
		if (!IsVertexInBoxBound(vp, bb))
		{
			wob.b = false;
			return false;
		}
		const int ct = src ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = src ? 0 : m_cSrcTriangle;
		//x -x y -y z -z
		GsRay aRays[] = {
			GsRay(GsVector3::zero, GsVector3::xAxis),
			GsRay(GsVector3::zero, GsVector3::neg_xAxis),
			GsRay(GsVector3::zero, GsVector3::yAxis),
			GsRay(GsVector3::zero, GsVector3::neg_yAxis),
			GsRay(GsVector3::zero, GsVector3::zAxis),
			GsRay(GsVector3::zero, GsVector3::neg_zAxis),
		};
		const GsDigit maxFloat = std::numeric_limits<GsDigit>::max();
		GsDigit aTimes[6] = {
			maxFloat, maxFloat, maxFloat, maxFloat, maxFloat, maxFloat,
		};
		int aIts[6] = {
			-1, -1, -1, -1, -1, -1,
		};
		bool inside = false;
		for (int i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			GeoSolidTriangle& gst = GetTriangle(it);
			const GsVector3& va = GetVertex(gst.iv0).v;
			const GsVector3& vb = GetVertex(gst.iv1).v;
			const GsVector3& vc = GetVertex(gst.iv2).v;
			if (GeometryMath::IsVertexOnTriangle(vp, va, vb, vc))
			{
				//点在三角面上
				inside = true;
				break;
			}
			//射线检测
			const GsVector3& vn = m_daNormals[gst.ivn];
			for (int ir = 0; ir < 6; ++ir)
			{
				GsRay& ray = aRays[ir];
				ray.SetOrigin(vp);
				GsDigit dp = DotProduct(ray.GetDirection(), vn);
				if (dp <= 0)
				{
					continue;
				}
				GsDigit time;
				bool intersect = IntersectRayTriangle(ray, va, vb, vc, &time);
				if (!intersect)
				{
					continue;
				}
				if (time < aTimes[ir])
				{
					aTimes[ir] = time;
					aIts[ir] = (int)it;
				}
			}
		}
		if (inside)
		{
			wob.b = true;
			return true;
		}
		bool sixIntersections = true;
		for (int ir = 0; ir < 6; ++ir)
		{
			if (aIts[ir] < 0)
			{
				sixIntersections = false;
				break;
			}
		}
		wob.b = sixIntersections;
		return sixIntersections;
	}

	bool CompositeGeoSolid::IsVertexInGeoSolid(const GsVector3& vp, bool src)
	{
		const GsBoxBound& bb = src ? m_bbSrc : m_bbCut;
		if (!IsVertexInBoxBound(vp, bb))
		{
			return false;
		}
		const int ct = src ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = src ? 0 : m_cSrcTriangle;
		//x -x y -y z -z
		GsRay aRays[] = {
			GsRay(GsVector3::zero, GsVector3::xAxis),
			GsRay(GsVector3::zero, GsVector3::neg_xAxis),
			GsRay(GsVector3::zero, GsVector3::yAxis),
			GsRay(GsVector3::zero, GsVector3::neg_yAxis),
			GsRay(GsVector3::zero, GsVector3::zAxis),
			GsRay(GsVector3::zero, GsVector3::neg_zAxis),
		};
		const GsDigit maxFloat = std::numeric_limits<GsDigit>::max();
		GsDigit aTimes[6] = {
			maxFloat,
			maxFloat,
			maxFloat,
			maxFloat,
			maxFloat,
			maxFloat,
		};
		int aIts[6] = {
			-1, -1, -1, -1, -1, -1,
		};
		bool inside = false;
		for (int i = 0; i < ct; ++i)
		{
			GsIt it = (GsIt)i + ot;
			GeoSolidTriangle& gst = GetTriangle(it);
			const GsVector3& va = GetVertex(gst.iv0).v;
			const GsVector3& vb = GetVertex(gst.iv1).v;
			const GsVector3& vc = GetVertex(gst.iv2).v;
			if (IsVertexEqual(vp, va) || 
				IsVertexEqual(vp, vb) || 
				IsVertexEqual(vp, vc))
			{
				//点重合
				inside = true;
				break;
			}
			if (GeometryMath::IsVertexInSegment(vp, va, vb) || 
				GeometryMath::IsVertexInSegment(vp, va, vc) || 
				GeometryMath::IsVertexInSegment(vp, vb, vc))
			{
				//点在直线上
				inside = true;
				break;
			}
			if (GeometryMath::IsVertexInsideTriangle(vp, va, vb, vc))
			{
				//点在三角面上
				inside = true;
				break;
			}
			//射线检测
			const GsVector3& vn = m_daNormals[gst.ivn];
			for (int ir = 0; ir < 6; ++ir)
			{
				GsRay& ray = aRays[ir];
				ray.SetOrigin(vp);
				GsDigit dp = DotProduct(ray.GetDirection(), vn);
				if (dp <= 0)
				{
					continue;
				}
				GsDigit time;
				bool intersect = IntersectRayTriangle(ray, va, vb, vc, &time);
				if (!intersect)
				{
					continue;
				}
				if (time < aTimes[ir])
				{
					aTimes[ir] = time;
					aIts[ir] = (int)it;
				}
			}
		}
		if (inside)
		{
			return true;
		}
		bool sixIntersect = true;
		for (int ir = 0; ir < 6; ++ir)
		{
			if (aIts[ir] < 0)
			{
				sixIntersect = false;
				break;
			}
		}
		return sixIntersect;
	}

	bool CompositeGeoSolid::IsTriangleInGeoSolid(const GeoSolidTriangle& gst, bool src)
	{
		//存在架空横跨模型的情况
		const bool isV0In = IsVertexInGeoSolid(gst.iv0, src);
		if (!isV0In)
		{
			return false;
		}
		const bool isV1In = IsVertexInGeoSolid(gst.iv1, src);
		if (!isV1In)
		{
			return false;
		}
		const bool isV2In = IsVertexInGeoSolid(gst.iv2, src);
		if (!isV2In)
		{
			return false;
		}
		//TODO: 2023-04-28 14:33:02: 是否对基础模型开放严格的检测？
		//基础模型是凸面模型，不需要检测线段跨越模型的情况
		if (src && m_bIsSrcBasic)
		{
			return true;
		}
		if (!src && m_bIsCutBasic)
		{
			return true;
		}
		const bool isE0In = IsEdgeInGeoSolid(gst.ie0, src);
		if (!isE0In)
		{
			return false;
		}
		const bool isE1In = IsEdgeInGeoSolid(gst.ie1, src);
		if (!isE1In)
		{
			return false;
		}
		const bool isE2In = IsEdgeInGeoSolid(gst.ie2, src);
		return isE2In;
	}

	bool CompositeGeoSolid::IsEdgeInGeoSolid(const GsIe& ie, bool src)
	{
		const GsIe ce = GetEdgeCount();
		WriteOnceBoolean& wob = GetWriteOnceBoolean(src ? m_aIesInSrc : m_aIesInCut, ie, ce);
		if (wob.c)
		{
			return wob.b;
		}
		wob.c = true;
		GeoSolidEdge& gse = GetEdge(ie);
		if (gse.iv0 == gse.iv1)
		{
			#if COMPOSITE_GEO_SOLID_DEBUG
			SANDBOX_ASSERT(false);
			#endif
			return false;
		}
		bool inside = false;
		const GsIv cv = GetVertexCount();
		const GsIv iva = gse.iv0;
		const GsIv ivb = gse.iv1;
		const int ct = src ? m_cSrcTriangle : m_cCutTriangle;
		const int ot = src ? 0 : m_cSrcTriangle;
		std::list<GsIt> listItsInWhichIv0;
		std::list<GsIt> listItsInWhichIv1;
		for (int i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			bool on0 = IsVertexOnTriangle(iva, it);
			if (on0)
			{
				listItsInWhichIv0.emplace_back(it);
			}
			bool on1 = IsVertexOnTriangle(ivb, it);
			if (on1)
			{
				listItsInWhichIv1.emplace_back(it);
			}
		}
		for (const GsIt& it0 : listItsInWhichIv0)
		{
			const GeoSolidTriangle& gst0 = GetTriangle(it0);
			//if (src && !gst0.intersectSrc.from)
			//{
			//	continue;
			//}
			//if (!src && !gst0.intersectCut.from)
			//{
			//	continue;
			//}
			for (const GsIt& it1 : listItsInWhichIv1)
			{
				const GeoSolidTriangle& gst1 = GetTriangle(it1);
				//if (src && !gst1.intersectSrc.from)
				//{
				//	continue;
				//}
				//if (!src && !gst1.intersectCut.from)
				//{
				//	continue;
				//}
				if (gst0.ivn == gst1.ivn && gst0.Neighbor(gst1))
				{
					inside = true;
					break;
				}
			}
			if (inside)
			{
				break;
			}
		}
		if (inside)
		{
			wob.b = true;
			return true;
		}
		const GeoSolidVertex& gsvEA = GetVertex(iva);
		const GeoSolidVertex& gsvEB = GetVertex(ivb);
		const GsVector3& va = gsvEA.v;
		const GsVector3& vb = gsvEB.v;
		const GsVector3 vab = vb - va;
		const GsDigit lab = vab.Length();
		const GsVector3 viab = vab / lab;
		const GsRay ray(va, viab);
		GsDigit time;
		//该线段的头尾可能与多个顶点邻近或重合
		const GsDigit error = 1e-3;
		struct RayCastingResult {
			GsDigit time = 0;
			GsDigit dp = 0;
			int it = -1;
			bool onVertex = false;
			bool onEdge = false;
		};
		std::list<RayCastingResult> listRcrHeads;
		std::list<RayCastingResult> listRcrTails;
		for (int i = 0; i < ct; ++i)
		{
			const GsIt it = (GsIt)i + ot;
			const GeoSolidTriangle& gst = GetTriangle(it);
			const bool isACoincide = gse.iv0 == gst.iv0 || gse.iv0 == gst.iv1 || gse.iv0 == gst.iv2;
			const bool isBCoincide = gse.iv1 == gst.iv0 || gse.iv1 == gst.iv1 || gse.iv1 == gst.iv2;
			const GsVector3& vta = GetVertex(gst.iv0).v;
			const GsVector3& vtb = GetVertex(gst.iv1).v;
			const GsVector3& vtc = GetVertex(gst.iv2).v;
			GsIe ieInWhichAIs = GsIe(-1);
			GsIe ieInWhichBIs = GsIe(-1);
			const bool isAInTriangle = IsVertexInsideTriangle(iva, it, &ieInWhichAIs);
			const bool isBInTriangle = IsVertexInsideTriangle(ivb, it, &ieInWhichBIs);
			const bool isAInEdge = ieInWhichAIs == gst.ie0 || ieInWhichAIs == gst.ie1 || ieInWhichAIs == gst.ie2;
			const bool isBInEdge = ieInWhichBIs == gst.ie0 || ieInWhichBIs == gst.ie1 || ieInWhichBIs == gst.ie2;
			if ((isAInTriangle || isAInEdge || isACoincide) && (isBInTriangle || isBInEdge || isBCoincide))
			{
				inside = true;
				break;
			}
			bool intersect = IntersectLineTriangle(ray, vta, vtb, vtc, &time);
			if (intersect)
			{
				const GsVector3 vIntersect = ray.GetOrigin() + ray.GetDirection() * time;
				const GsVector3& vn = m_daNormals[gst.ivn];
				const GsDigit dp = DotProduct(ray.m_Direction, vn);
				if (IsZero(time - lab, error))
				{
					bool canInsert = listRcrHeads.empty();
					for (auto itRcr = listRcrHeads.begin(); itRcr != listRcrHeads.end(); )
					{
						RayCastingResult& rcrHead = *itRcr;
						if (Rainbow::Abs(time - lab) < Rainbow::Abs(rcrHead.time - lab))
						{
							//可能在下方的else-if中保存了多个相同time的result。Tail同理。
							if (Rainbow::Abs(time - rcrHead.time) > error)
							{
								//超过精度范围的移除
								itRcr = listRcrHeads.erase(itRcr);
							}
							else
							{
								++itRcr;
							}
							canInsert = true;
						}
						else if (Rainbow::Abs(time - lab) == Rainbow::Abs(rcrHead.time - lab))
						{
							canInsert = true;
							break;
						}
						else
						{
							++itRcr;
						}
					}
					if (canInsert)
					{
						RayCastingResult rcrHead;
						rcrHead.time = time;
						rcrHead.dp = dp;
						rcrHead.it = (int)it;
						rcrHead.onVertex = 
							IsVertexEqual(vIntersect, vta, 1e-2) ||
							IsVertexEqual(vIntersect, vtb, 1e-2) ||
							IsVertexEqual(vIntersect, vtc, 1e-2);
						rcrHead.onEdge = 
							GeometryMath::IsParalleled(vta, vIntersect, vtb) ||
							GeometryMath::IsParalleled(vta, vIntersect, vtc) ||
							GeometryMath::IsParalleled(vtb, vIntersect, vtc);
						listRcrHeads.emplace_back(rcrHead);
					}
				}
				else if (IsZero(time, error))
				{
					bool canInsert = listRcrTails.empty();
					for (auto itRcr = listRcrTails.begin(); itRcr != listRcrTails.end(); )
					{
						RayCastingResult& rcrTail = *itRcr;
						if (Rainbow::Abs(time) < Rainbow::Abs(rcrTail.time))
						{
							if (Rainbow::Abs(time - rcrTail.time) > error)
							{
								itRcr = listRcrTails.erase(itRcr);
							}
							else
							{
								++itRcr;
							}
							canInsert = true;
						}
						else if (Rainbow::Abs(time) == Rainbow::Abs(rcrTail.time))
						{
							canInsert = true;
							break;
						}
						else
						{
							++itRcr;
						}
					}
					if (canInsert)
					{
						RayCastingResult rcrTail;
						rcrTail.time = time;
						rcrTail.dp = dp;
						rcrTail.it = (int)it;
						rcrTail.onVertex =
							IsVertexEqual(vIntersect, vta, 1e-2) ||
							IsVertexEqual(vIntersect, vtb, 1e-2) ||
							IsVertexEqual(vIntersect, vtc, 1e-2);
						rcrTail.onEdge =
							GeometryMath::IsParalleled(vta, vIntersect, vtb) ||
							GeometryMath::IsParalleled(vta, vIntersect, vtc) ||
							GeometryMath::IsParalleled(vtb, vIntersect, vtc);
						listRcrTails.emplace_back(rcrTail);
					}
				}
				else if (time >= 0.f)
				{
					//外部部分统一放到rcHead中
					bool canInsert = listRcrHeads.empty();
					for (auto itRcr = listRcrHeads.begin(); itRcr != listRcrHeads.end(); )
					{
						RayCastingResult& rcrHead = *itRcr;
						if (time < rcrHead.time)
						{
							if (Rainbow::Abs(time - rcrHead.time) > error)
							{
								itRcr = listRcrHeads.erase(itRcr);
							}
							else
							{
								++itRcr;
							}
							canInsert = true;
						}
						else if (time == rcrHead.time)
						{
							canInsert = true;
							break;
						}
						else
						{
							++itRcr;
						}
					}
					if (canInsert)
					{
						RayCastingResult rcrHead;
						rcrHead.time = time;
						rcrHead.dp = dp;
						rcrHead.it = (int)it;
						rcrHead.onVertex =
							IsVertexEqual(vIntersect, vta, 1e-2) ||
							IsVertexEqual(vIntersect, vtb, 1e-2) ||
							IsVertexEqual(vIntersect, vtc, 1e-2);
						rcrHead.onEdge =
							GeometryMath::IsParalleled(vta, vIntersect, vtb) ||
							GeometryMath::IsParalleled(vta, vIntersect, vtc) ||
							GeometryMath::IsParalleled(vtb, vIntersect, vtc);
						listRcrHeads.emplace_back(rcrHead);
					}
				}
				else
				{
					continue;
				}
			}
		}
		if (inside)
		{
			wob.b = true;
			return true;
		}
		bool rayCastInside = false;
		int cCast = 0;
		cCast += !listRcrHeads.empty();
		cCast += !listRcrTails.empty();
		if (cCast == 0)
		{
			rayCastInside = false;
		}
		else if (cCast == 1)
		{
			if (!listRcrTails.empty())
			{
				//射线开始位置在三角形附近
				//从外向内的方向穿入
				for (const RayCastingResult& rcrTail : listRcrTails)
				{
					const bool tailOnSurface = IsZero(rcrTail.time, error);
					rayCastInside = rcrTail.dp < 0 && tailOnSurface;
					if (rayCastInside)
					{
						break;
					}
				}
			}
			else if (!listRcrHeads.empty())
			{
				//射线头，向量头
				//从里面穿出
				for (const RayCastingResult& rcrHead : listRcrHeads)
				{
					const bool headOnSurface = IsZero(rcrHead.time - lab, error);
					rayCastInside = rcrHead.dp > 0 && (headOnSurface || rcrHead.time >= lab);
					if (rayCastInside)
					{
						break;
					}
				}
			}
		}
		else if (cCast == 2)
		{
			for (const RayCastingResult& rcrTail : listRcrTails)
			{
				for (const RayCastingResult& rcrHead : listRcrHeads)
				{
					const bool tailOnSurface = IsZero(rcrTail.time, error);
					const bool headOnSurface = IsZero(rcrHead.time - lab, error);
					bool checkInOut;
					if (tailOnSurface && headOnSurface)
					{
						checkInOut = rcrTail.dp < 0 && rcrHead.dp > 0;
					}
					else if (tailOnSurface && rcrHead.time <= lab)
					{
						checkInOut = rcrTail.dp < 0 && rcrHead.dp > 0;
					}
					else if (headOnSurface && rcrTail.time >= 0)
					{
						checkInOut = rcrTail.dp < 0 && rcrHead.dp > 0;
					}
					else
					{
						//判断是否在凹陷部分内部穿过
						checkInOut = rcrTail.dp < 0 && rcrHead.dp > 0;
					}
					if (checkInOut)
					{
						if ((rcrHead.onEdge || rcrHead.onVertex) && (rcrTail.onEdge || rcrTail.onVertex))
						{
							const GsVector3 vm = (va + vb) * 0.5;
							rayCastInside = IsVertexInGeoSolid(vm, src);
						}
						else
						{
							rayCastInside = true;
						}
					}
					if (rayCastInside)
					{
						break;
					}
				}
				if (rayCastInside)
				{
					break;
				}
			}
			if (!rayCastInside)
			{
				//TODO: 2023-05-17 15:03:20: 是否有更快的判断方法？
				const GsVector3 vm = (va + vb) * 0.5;
				rayCastInside = IsVertexInGeoSolid(vm, src);
			}
		}
		else
		{
			rayCastInside = false;
		}
		wob.b = rayCastInside;
		return rayCastInside;
	}

	GsDigit CompositeGeoSolid::GetDistanceFromEdge(const GsIv& iv, const GsIe& ie)
	{
		const GsVector3& vc = GetVertex(iv).v;
		const GeoSolidEdge& gse = GetEdge(ie);
		const GsVector3& va = GetVertex(gse.iv0).v;
		const GsVector3& vb = GetVertex(gse.iv1).v;
		const GsDigit area = GetArea(vc, va, vb);
		const GsDigit bottom = Distance(va, vb);
		return area / bottom;
	}

	GsVector3 CompositeGeoSolid::CastVertexOnEdge(const GsIv& iv, const GsIe& ie)
	{
		const GsVector3& vc = GetVertex(iv).v;
		const GeoSolidEdge& gse = GetEdge(ie);
		const GsVector3& va = GetVertex(gse.iv0).v;
		const GsVector3& vb = GetVertex(gse.iv1).v;
		return CastVertexOnLine(vc, va, vb);
	}

	void CompositeGeoSolid::RemoveSubMesh(const int ism)
	{
		GeoSolidSubMesh* gssm = m_daSubMeshes[ism];
		m_daSubMeshes.erase(m_daSubMeshes.begin() + ism);
		GeoSolidSubMesh::recycle(gssm);
	}

	#if COMPOSITE_GEO_SOLID_DEBUG
	void CompositeGeoSolid::GeoGebraExecuteCmdLists(std::ostringstream& ossVs, std::ostringstream& ossTs)
	{
		ossVs.seekp(0, std::ios::end);
		ossTs.seekp(0, std::ios::end);
		const UInt32 cossVs = ossVs.tellp() / sizeof(char);
		const UInt32 cossTs = ossTs.tellp() / sizeof(char);
		const UInt32 LARGE = 1 << 16;
		if (cossVs >= LARGE || cossTs >= LARGE || (cossVs + cossTs) >= LARGE)
		{
			std::ostringstream ossFinalVs;
			ossFinalVs << "Execute({" << ossVs.str() << "})";
			WarningString(ossFinalVs.str().c_str());
			std::ostringstream ossFinalTs;
			ossFinalTs << "Execute({" << ossTs.str() << "})";
			WarningString(ossFinalTs.str().c_str());
		}
		else
		{
			std::ostringstream ossAll;
			ossAll << "Execute({" << ossVs.str() << ',' << ossTs.str() << "})";
			WarningString(ossAll.str().c_str());
		}
	}

	void CompositeGeoSolid::GeoGebraExecuteCmdLists(std::ostringstream& ossVs, std::ostringstream& ossEs, std::ostringstream& ossTs)
	{
		ossVs.seekp(0, std::ios::end);
		ossEs.seekp(0, std::ios::end);
		ossTs.seekp(0, std::ios::end);
		const UInt32 cossVs = ossVs.tellp() / sizeof(char);
		const UInt32 cossEs = ossVs.tellp() / sizeof(char);
		const UInt32 cossTs = ossTs.tellp() / sizeof(char);
		const UInt32 LARGE = 1 << 16;
		if (cossVs >= LARGE || cossEs >= LARGE || cossTs >= LARGE || 
			(cossVs + cossEs) >= LARGE ||
			(cossVs + cossTs) >= LARGE ||
			(cossEs + cossTs) >= LARGE)
		{
			std::ostringstream ossFinalVs;
			ossFinalVs << "Execute({" << ossVs.str() << "})";
			WarningString(ossFinalVs.str().c_str());

			std::string strEs = ossEs.str();
			if (!strEs.empty())
			{
				std::ostringstream ossFinalEs;
				ossFinalEs << "Execute({" << strEs << "})";
				WarningString(ossFinalEs.str().c_str());
			}
			std::ostringstream ossFinalTs;
			ossFinalTs << "Execute({" << ossTs.str() << "})";
			WarningString(ossFinalTs.str().c_str());
		}
		else
		{
			std::ostringstream ossAll;
			std::string strEs = ossEs.str();
			ossAll << "Execute({" << ossVs.str();
			if (!strEs.empty())
			{
				ossAll << ',' << ossEs.str();
			}
			ossAll << ',' << ossTs.str() << "})";
			WarningString(ossAll.str().c_str());
		}
	}

	void CompositeGeoSolid::GeoGebraExecuteWhole(bool printDivided)
	{
		//GeoGebra为右手坐标系，复制使用时对x值取反，以匹配游戏中的形状
		const GsIv cv = GetVertexCount();
		const UInt32 ct = m_daTriangles.size();
		//WarningString("===================================================");
		//WarningString("Vertices in World Coordiante:");
		std::ostringstream ossVs;
		std::ostringstream ossTs;
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			//const GeoSolidVertex& gsv = GetGeoSolidVertex(iv);
			//WarningStringMsg("V%d = (%9.2f, %9.2f, %9.2f)",
			//	iv, -gsv.v.x, gsv.v.y, gsv.v.z
			//);
			if (iv != 0)
			{
				ossVs << ',';
			}
			GeoGebraAppendVertexCmd(ossVs, iv);
		}
		//WarningString("Vertices in world coordiante:");
		int itFirst = -1;
		for (int it = 0; it < ct; ++it)
		{
			const GeoSolidTriangle& gst = GetTriangle((GsIt)it);
			if (!printDivided && gst.markRemoval)
			{
				continue;
			}
			if (itFirst < 0)
			{
				itFirst = it;
			}
			//WarningStringMsg("T%d = Polygon(V%d, V%d, V%d)",
			//	it, gst.iv0, gst.iv1, gst.iv2
			//);
			if (it != itFirst)
			{
				ossTs << ',';
			}
			GeoGebraAppendTriangleCmd(ossTs, (GsIt)it);
		}
		WarningString("GeoGebra cmd list:");
		GeoGebraExecuteCmdLists(ossVs, ossTs);
		WarningString("-----------------------------");
	}

	void CompositeGeoSolid::GeoGebraExecutePolygons(std::ostringstream& oss, std::list<GeoSolidPolygon>& listGsps)
	{
		if (listGsps.empty())
		{
			return;
		}
		oss << "Execute({";
		auto itGspFirst = listGsps.begin();
		std::set<GsIv> setIvs;
		for (auto itGsp = listGsps.begin(); itGsp != listGsps.end(); ++itGsp)
		{
			GeoSolidPolygon& gsp = *itGsp;
			for (auto itGspv = gsp.listGspvs.begin(); itGspv != gsp.listGspvs.end(); ++itGspv)
			{
				setIvs.insert(itGspv->iv);
			}
		}
		for (auto itIv = setIvs.begin(); itIv != setIvs.end(); ++itIv)
		{
			const GsIv& iv = *itIv;
			GeoGebraAppendVertexCmd(oss, iv);
			oss << ',';
		}
		GeoSolidPolygon& gspFirst = *itGspFirst;
		gspFirst.GeoGebraAppendCmd(oss);
		for (auto itGsp = std::next(itGspFirst); itGsp != listGsps.end(); ++itGsp)
		{
			oss << ',';
			GeoSolidPolygon& gsp = *itGsp;
			gsp.GeoGebraAppendCmd(oss);
		}
		oss << '}';
		oss << ')';
	}

	void CompositeGeoSolid::GeoGebraAppendVertexCmd(std::ostringstream& oss, const GsIv& iv)
	{
		//GeoGebra为右手坐标系，复制使用时对x值取反，以匹配游戏中的形状
		const GeoSolidVertex& gsv = GetVertex(iv);
		oss << '\"'
			<< 'V' << iv << '='
			<< '('
			<< std::fixed << std::setprecision(precisionV) << -gsv.v.x
			<< ','
			<< std::fixed << std::setprecision(precisionV) << gsv.v.y
			<< ','
			<< std::fixed << std::setprecision(precisionV) << gsv.v.z
			<< ')'
			<< '\"'
		;
	}

	void CompositeGeoSolid::GeoGebraAppendEdgeCmd(std::ostringstream& oss, const GsIe& ie)
	{
		const GeoSolidEdge& gse = GetEdge(ie);
		oss << "\"E" << ie << "=Segment(V" << gse.iv0 << ",V" << gse.iv1 << ")\"";
	}

	void CompositeGeoSolid::GeoGebraAppendAnonymousEdgeCmd(std::ostringstream& oss, const GsIv& iv0, const GsIv& iv1)
	{
		oss << "\"Segment(V" << iv0 << ",V" << iv1 << ")\"";
	}

	void CompositeGeoSolid::GeoGebraAppendTriangleCmd(std::ostringstream& oss, const GsIt& it)
	{
		const GeoSolidTriangle& gst = GetTriangle(it);
		oss << "\"T" << it << "=Polygon(V" << gst.iv0 << ",V" << gst.iv1 << ",V" << gst.iv2 << ")\"";
	}

	void CompositeGeoSolid::GeoGebraAppendBarycenterCmd(std::ostringstream& oss, const GsIt& it)
	{
		const GeoSolidTriangle& gst = GetTriangle(it);
		const GsVector3& v0 = GetVertex(gst.iv0).v;
		const GsVector3& v1 = GetVertex(gst.iv1).v;
		const GsVector3& v2 = GetVertex(gst.iv2).v;
		const GsVector3 vbc = (v0 + v1 + v2) / 3.f;
		oss << '\"'
			<< "C" << it << '='
			<< '('
			<< std::fixed << std::setprecision(precisionV) << -vbc.x
			<< ','
			<< std::fixed << std::setprecision(precisionV) << vbc.y
			<< ','
			<< std::fixed << std::setprecision(precisionV) << vbc.z
			<< ')'
			<< '\"'
		;
	}

	void CompositeGeoSolid::GeoGebraAddTriangleCmd(std::ostringstream& oss, const GsIt& it)
	{
		const GeoSolidTriangle& gst = GetTriangle(it);
		GeoGebraAppendVertexCmd(oss, gst.iv0);
		oss << ',';
		GeoGebraAppendVertexCmd(oss, gst.iv1);
		oss << ',';
		GeoGebraAppendVertexCmd(oss, gst.iv2);
		oss << ',';
		GeoGebraAppendEdgeCmd(oss, gst.ie0);
		oss << ',';
		GeoGebraAppendEdgeCmd(oss, gst.ie1);
		oss << ',';
		GeoGebraAppendEdgeCmd(oss, gst.ie2);
		oss << ',';
		GeoGebraAppendBarycenterCmd(oss, it);
		oss << ',';
		GeoGebraAppendTriangleCmd(oss, it);
	}

	void CompositeGeoSolid::PrintSingleTriangle(const GsIt& it, bool vn, bool uv, bool area, bool vbc, bool uvIndex)
	{
		std::ostringstream oss;
		const GeoSolidTriangle& gst = GetTriangle(it);
		oss << "  T" << it << "=Polygon(V" << gst.iv0 << ", V" << gst.iv1 << ", V" << gst.iv2 << ')';
		if (vn)
		{
			const GsVector3& vn = m_daNormals[gst.ivn];
			oss << " | vn = ("
				<< std::fixed << std::setw(wVn) << std::setprecision(precisionVn) << vn.x << ", "
				<< std::fixed << std::setw(wVn) << std::setprecision(precisionVn) << vn.y << ", "
				<< std::fixed << std::setw(wVn) << std::setprecision(precisionVn) << vn.z << ')';
		}
		if (uv)
		{
			const GsVector2& uv0 = GetUv(gst.iuv0);
			const GsVector2& uv1 = GetUv(gst.iuv1);
			const GsVector2& uv2 = GetUv(gst.iuv2);
			oss << " | uvs = {("
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv0.x << ", "
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv0.y << "), ("
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv1.x << ", "
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv1.y << "), ("
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv2.x << ", "
				<< std::fixed << std::setw(wUv) << std::setprecision(precisionUv) << uv2.y << ")}";
		}
		if (area)
		{
			oss << " | a = " << std::fixed << std::setw(wArea) << std::setprecision(precisionOfArea) << gst.area;
		}
		if (vbc)
		{
			const GsVector3& v0 = GetVertex(gst.iv0).v;
			const GsVector3& v1 = GetVertex(gst.iv1).v;
			const GsVector3& v2 = GetVertex(gst.iv2).v;
			const GsVector3 vbc = (v0 + v1 + v2) / 3.f;
			oss << " | vbc = ("
				<< std::fixed << std::setprecision(precisionV) << vbc.x << ", "
				<< std::fixed << std::setprecision(precisionV) << vbc.y << ", "
				<< std::fixed << std::setprecision(precisionV) << vbc.z << ')';
		}
		if (uvIndex)
		{
			oss << " | u of " << (gst.uIndex == 0 ? 'x' : gst.uIndex == 1 ? 'y' : gst.uIndex == 2 ? 'z' : '?')
				<< " | v of " << (gst.vIndex == 0 ? 'x' : gst.vIndex == 1 ? 'y' : gst.vIndex == 2 ? 'z' : '?');
		}
		WarningString(oss.str().c_str());
	}

	void CompositeGeoSolid::PrintSingleGeoGebraExecution(const GsIt& itMain)
	{
		const GeoSolidTriangle& gstMain = GetTriangle(itMain);
		std::ostringstream ossVs;
		std::ostringstream ossEs;
		std::unordered_set<GsIv> setIvs;
		setIvs.insert(gstMain.iv0);
		setIvs.insert(gstMain.iv1);
		setIvs.insert(gstMain.iv2);
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		for (auto itIe = setIesDivider.begin(); itIe != setIesDivider.end(); ++itIe)
		{
			const GsIe& ie = *itIe;
			GeoSolidEdge& gse = GetEdge(ie);
			setIvs.insert(gse.iv0);
			setIvs.insert(gse.iv1);
		}
		for (auto itIv = setIvs.begin(); itIv != setIvs.end(); ++itIv)
		{
			GeoGebraAppendVertexCmd(ossVs, *itIv);
			ossVs << ',';
		}
		GeoGebraAppendBarycenterCmd(ossVs, itMain);
		for (auto itIe = setIesDivider.begin(); itIe != setIesDivider.end(); ++itIe)
		{
			const GsIe ie = *itIe;
			GeoSolidEdge& gse = GetEdge(ie);
			GeoGebraAppendEdgeCmd(ossEs, ie);
			ossEs << ',';
			//WarningStringMsg("  E%d = Segment(V%d, V%d)", ie, gse.iv0, gse.iv1);
		}

		GeoGebraAppendEdgeCmd(ossEs, gstMain.ie0);
		ossEs << ',';
		GeoGebraAppendEdgeCmd(ossEs, gstMain.ie1);
		ossEs << ',';
		GeoGebraAppendEdgeCmd(ossEs, gstMain.ie2);
		ossEs << ',';
		GeoGebraAppendTriangleCmd(ossEs, itMain);

		//WarningString("GeoGebra cmd list:");
		GeoGebraExecuteCmdLists(ossVs, ossEs);
	}

	void CompositeGeoSolid::PrintBeforeDivision(const GsIt& itMain)
	{
		const GeoSolidTriangle& gstMain = GetTriangle(itMain);
		const GsVector3& vn = m_daNormals[gstMain.ivn];
		std::ostringstream ossMain;
		ossMain << "Execute({";
		GeoGebraAddTriangleCmd(ossMain, itMain);
		ossMain << '}' << ')';
		WarningString(ossMain.str().c_str());
		PrintSingleTriangle(itMain, true, false, false, false, false);
		const ColorRGBA32& c = m_daColors[gstMain.ic];
		WarningStringMsg("  color = #%02X%02X%02X", c.r, c.g, c.b);
		if (gstMain.gss == GeoSolidShape::SPHERE)
		{
			WarningString("  gss = Sphere");
		}
		else
		{
			WarningStringMsg("  gss = %s | gsf = %s", GetShapeName(gstMain.gss), GetFaceName(gstMain.gsf));
		}
		GeoSolidEdge& gse0 = GetEdge(gstMain.ie0);
		GeoSolidEdge& gse1 = GetEdge(gstMain.ie1);
		GeoSolidEdge& gse2 = GetEdge(gstMain.ie2);
		WarningStringMsg("  E%d = Segment(V%d, V%d), E%d = Segment(V%d, V%d), E%d = Segment(V%d, V%d)",
			gstMain.ie0, gse0.iv0, gse0.iv1,
			gstMain.ie1, gse1.iv0, gse1.iv1,
			gstMain.ie2, gse2.iv0, gse2.iv1
		);
		GeoSolidSet<GsIe>& setIesDivider = gstMain.GetIesDivider();
		WarningStringMsg("Inside segment lines(%u): ", setIesDivider.size());
		GeoSolidSet<GsIv>& setNewIvs = gstMain.GetNewIvs();
		WarningStringMsg("Being dividing by %u vertices:", setNewIvs.size());
		std::ostringstream ossIvs;
		std::vector<GsIv> vIvs;
		vIvs.assign(setNewIvs.begin(), setNewIvs.end());
		std::sort(vIvs.begin(), vIvs.end());
		for (int i = 0; i < vIvs.size(); ++i)
		{
			const GsIv& iv = vIvs[i];
			ossIvs << iv;
			if (i != vIvs.size() - 1)
			{
				ossIvs << ' ';
			}
		}
		WarningStringMsg("  {%s}", ossIvs.str().c_str());
		PrintSingleGeoGebraExecution(itMain);
	}

	void CompositeGeoSolid::PrintAfterDivision(const GsIt& itMain, const std::list<GsIt>& listItsResult)
	{
		if (listItsResult.empty())
		{
			WarningString("GeoGebra cmd list after division: empty");
			return;
		}
		//WarningStringMsg("listItsResult.size() = %u", listItsResult.size());
		std::ostringstream ossVs;
		std::ostringstream ossEs;
		std::ostringstream ossTs;
		const GeoSolidTriangle& gstMain = GetTriangle(itMain);
		//const GeoSolidVertex& gsv0 = GetGeoSolidVertex(gstMain.iv0);
		//const GeoSolidVertex& gsv1 = GetGeoSolidVertex(gstMain.iv1);
		//const GeoSolidVertex& gsv2 = GetGeoSolidVertex(gstMain.iv2);
		//WarningStringMsg("  V%d = (%.*f, %.*f, %.*f)", gstMain.iv0,
		//	precisionV, -gsv0.v.x,
		//	precisionV, gsv0.v.y,
		//	precisionV, gsv0.v.z
		//);
		//WarningStringMsg("  V%d = (%.*f, %.*f, %.*f)", gstMain.iv1,
		//	precisionV, -gsv1.v.x,
		//	precisionV, gsv1.v.y,
		//	precisionV, gsv1.v.z
		//);
		//WarningStringMsg("  V%d = (%.*f, %.*f, %.*f)", gstMain.iv2,
		//	precisionV, -gsv2.v.x,
		//	precisionV, gsv2.v.y,
		//	precisionV, gsv2.v.z
		//);
		std::ostringstream ossIvs;
		std::unordered_set<GsIv> setIvs;
		setIvs.insert(gstMain.iv0);
		setIvs.insert(gstMain.iv1);
		setIvs.insert(gstMain.iv2);
		for (auto itIt = listItsResult.begin(); itIt != listItsResult.end(); ++itIt)
		{
			const GsIt it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			setIvs.insert(gst.iv0);
			setIvs.insert(gst.iv1);
			setIvs.insert(gst.iv2);
			GeoSolidSet<GsIv>& setNewIvs = gst.GetNewIvs();
			for (const GsIv& iv : setNewIvs)
			{
				setIvs.insert(iv);
			}
		}
		for (const GsIe& ie : setIesDivider)
		{
			const GeoSolidEdge& gse = GetEdge(ie);
			setIvs.insert(gse.iv0);
			setIvs.insert(gse.iv1);
		}
		
		{
			const GsIv& ivFirst = *setIvs.begin();
			GeoGebraAppendVertexCmd(ossVs, ivFirst);
		}
		for (auto itIv = std::next(setIvs.begin()); itIv != setIvs.end(); ++itIv)
		{
			const GsIv& iv = *itIv;
			GeoSolidVertex& gsv = GetVertex(iv);
			//WarningStringMsg("  V%d = (%9.2f, %9.2f, %9.2f)",
			//	iv, -gsv.v.x, gsv.v.y, gsv.v.z
			//);
			ossVs << ',';
			GeoGebraAppendVertexCmd(ossVs, iv);
		}

		if (!setIesDivider.empty())
		{
			const GsIe& ieFirst = *setIesDivider.begin();
			GeoGebraAppendEdgeCmd(ossEs, ieFirst);
			for (auto itIe = std::next(setIesDivider.begin());
				itIe != setIesDivider.end(); ++itIe)
			{
				ossEs << ',';
				GeoGebraAppendEdgeCmd(ossEs, *itIe);
			}
		}

		//WarningString("After division, the triangles shown in the following:");
		{
			const GsIt& itFirst = *listItsResult.begin();
			GeoGebraAppendTriangleCmd(ossTs, itFirst);
		}
		for (auto itIt = std::next(listItsResult.begin()); itIt != listItsResult.end(); ++itIt)
		{
			const GsIt it = *itIt;
			GeoSolidTriangle& gst = GetTriangle(it);
			//WarningStringMsg("  T%d = Polygon(V%d, V%d, V%d)", it, gst.iv0, gst.iv1, gst.iv2);
			ossTs << ',';
			GeoGebraAppendTriangleCmd(ossTs, it);
		}

		//WarningString("GeoGebra cmd list after division:");
		GeoGebraExecuteCmdLists(ossVs, ossEs, ossTs);
	}

	void CompositeGeoSolid::PrintSubMesh(const UInt32& ism, bool triangle)
	{
		std::ostringstream ossVs;
		std::ostringstream ossTs;
		WarningStringMsg("gssm%u:", ism);
		const GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
		if (!gssm.setTriangleIndices.empty())
		{
			bool printNormal = false;
			switch (gssm.eGss)
			{
				case GeoSolidShape::CUBOID:
				case GeoSolidShape::WEDGE:
				case GeoSolidShape::PYRAMID:
					printNormal = true;
					break;
				case GeoSolidShape::CYLINDER:
				case GeoSolidShape::CONE:
					printNormal = gssm.eGsf == GeoSolidFace::BOTTOM || gssm.eGsf == GeoSolidFace::TOP;
					break;
				case GeoSolidShape::SPHERE:
					printNormal = false;
					break;
			}
			if (printNormal)
			{
				GeoSolidTriangle& gstFirst = GetTriangle(*gssm.setTriangleIndices.begin());
				const GsVector3& vn = m_daNormals[gstFirst.ivn];
				WarningStringMsg("  vn = (%*.*f, %*.*f, %*.*f)",
					wVn, precisionVn, vn.x, 
					wVn, precisionVn, vn.y, 
					wVn, precisionVn, vn.z
				);
			}
		}
		if (gssm.eGss == GeoSolidShape::SPHERE)
		{
			WarningString("  gss = Sphere");
		}
		else
		{
			WarningStringMsg("  gss = %s | gsf = %s", GetShapeName(gssm.eGss), GetFaceName(gssm.eGsf));
		}
		const ColorRGBA32& c = m_daColors[gssm.ic];
		WarningStringMsg("  color = #%02X%02X%02X", c.r, c.g, c.b);
		WarningStringMsg("  vertices(%u):", gssm.setVertexIndices.size());
		if (!gssm.setVertexIndices.empty())
		{
			std::vector<GsIv> vIvs;
			vIvs.assign(gssm.setVertexIndices.begin(), gssm.setVertexIndices.end());
			std::sort(vIvs.begin(), vIvs.end());
			const GsIv ivLast = vIvs[vIvs.size() - 1];
			for (const GsIv& iv : vIvs)
			{
				GeoSolidVertex& gsv = GetVertex(iv);
				//GeoGebra为右手坐标系，复制使用时对x值取反，以匹配游戏中的形状
				//WarningStringMsg("    V%u = (%.2f, %.2f, %.2f)", iv, -gsv.v.x, gsv.v.y, gsv.v.z);
				GeoGebraAppendVertexCmd(ossVs, iv);
				if (iv != ivLast)
				{
					ossVs << ',';
				}
			}
		}
		WarningStringMsg("  triangles(%u):", gssm.setTriangleIndices.size());
		if (!gssm.setTriangleIndices.empty())
		{
			std::vector<GsIt> vIts;
			vIts.reserve(gssm.setTriangleIndices.size());
			vIts.assign(gssm.setTriangleIndices.begin(), gssm.setTriangleIndices.end());
			std::sort(vIts.begin(), vIts.end());
			const GsIt itLast = vIts[vIts.size() - 1];
			const bool printVn = true;
			const bool printUv = true;
			const bool printArea = true;
			const bool printVbc = true;
			const bool printUVIndex = true;
			for (const GsIt& it : vIts)
			{
				if (triangle)
				{
					PrintSingleTriangle(it, printVn, printUv, printArea, printVbc, printUVIndex);
				}
				GeoGebraAppendTriangleCmd(ossTs, it);
				if (it != itLast)
				{
					ossTs << ',';
				}
			}
		}
		if (!gssm.setTriangleIndices.empty() && !gssm.setVertexIndices.empty())
		{
			WarningString("GeoGebra cmd list:");
			GeoGebraExecuteCmdLists(ossVs, ossTs);
		}
	}

	void CompositeGeoSolid::PrintSubMeshes()
	{
		WarningString("========================PrintSubMeshes========================");
		const UInt32 csm = m_daSubMeshes.size();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			PrintSubMesh(ism, false);
			WarningString("--------------PrintSubMeshes--------------");
		}
	}
	#endif

	void CompositeGeoSolid::CoplanarTrianglesIntersect2(const GsIt& itSrc, const GsIt& itCut)
	{
		GeoSolidTriangle& gstSrc = GetTriangle(itSrc);
		GeoSolidTriangle& gstCut = GetTriangle(itCut);
		const GsIv iva = gstSrc.iv0;
		const GsIv ivb = gstSrc.iv1;
		const GsIv ivc = gstSrc.iv2;
		const GsVector3& va = GetVertex(iva).v;
		const GsVector3& vb = GetVertex(ivb).v;
		const GsVector3& vc = GetVertex(ivc).v;
		const GsIv ivd = gstCut.iv0;
		const GsIv ive = gstCut.iv1;
		const GsIv ivf = gstCut.iv2;
		const GsVector3& vd = GetVertex(ivd).v;
		const GsVector3& ve = GetVertex(ive).v;
		const GsVector3& vf = GetVertex(ivf).v;
		GsVector3 vOfABAndDE;
		GsVector3 vOfABAndDF;
		GsVector3 vOfABAndEF;
		GsVector3 vOfACAndDE;
		GsVector3 vOfACAndDF;
		GsVector3 vOfACAndEF;
		GsVector3 vOfBCAndDE;
		GsVector3 vOfBCAndDF;
		GsVector3 vOfBCAndEF;
		bool lineABIntersectDE = WhetherLineIntersectLine(va, vb, vd, ve, &vOfABAndDE);
		bool lineABIntersectDF = WhetherLineIntersectLine(va, vb, vd, vf, &vOfABAndDF);
		bool lineABIntersectEF = WhetherLineIntersectLine(va, vb, ve, vf, &vOfABAndEF);
		bool lineACIntersectDE = WhetherLineIntersectLine(va, vc, vd, ve, &vOfACAndDE);
		bool lineACIntersectDF = WhetherLineIntersectLine(va, vc, vd, vf, &vOfACAndDF);
		bool lineACIntersectEF = WhetherLineIntersectLine(va, vc, ve, vf, &vOfACAndEF);
		bool lineBCIntersectDE = WhetherLineIntersectLine(vb, vc, vd, ve, &vOfBCAndDE);
		bool lineBCIntersectDF = WhetherLineIntersectLine(vb, vc, vd, vf, &vOfBCAndDF);
		bool lineBCIntersectEF = WhetherLineIntersectLine(vb, vc, ve, vf, &vOfBCAndEF);
		int cABIntersections = 0;
		cABIntersections += lineABIntersectDE ? 1 : 0;
		cABIntersections += lineABIntersectDF ? 1 : 0;
		cABIntersections += lineABIntersectEF ? 1 : 0;
		int cACIntersections = 0;
		cACIntersections += lineACIntersectDE ? 1 : 0;
		cACIntersections += lineACIntersectDF ? 1 : 0;
		cACIntersections += lineACIntersectEF ? 1 : 0;
		int cBCIntersections = 0;
		cBCIntersections += lineBCIntersectDE ? 1 : 0;
		cBCIntersections += lineBCIntersectDF ? 1 : 0;
		cBCIntersections += lineBCIntersectEF ? 1 : 0;
		int cRespectively = cABIntersections + cACIntersections + cBCIntersections;
 		if (cRespectively <= 1)
		{
			//一个顶点也视为没有相交
			return;
		}
		//交一个顶点时，必定过Cut的某个顶点，下同
		//std::list<GsVector3> listVs;
		//if (lineABIntersectDE) AddVertex(listVs, vOfABAndDE);
		//if (lineABIntersectDF) AddVertex(listVs, vOfABAndDF);
		//if (lineABIntersectEF) AddVertex(listVs, vOfABAndEF);
		//if (lineACIntersectDE) AddVertex(listVs, vOfACAndDE);
		//if (lineACIntersectDF) AddVertex(listVs, vOfACAndDF);
		//if (lineACIntersectEF) AddVertex(listVs, vOfACAndEF);
		//if (lineBCIntersectDE) AddVertex(listVs, vOfBCAndDE);
		//if (lineBCIntersectDF) AddVertex(listVs, vOfBCAndDF);
		//if (lineBCIntersectEF) AddVertex(listVs, vOfBCAndEF);
		CoplanarTrianglesIntersect2(itSrc, itCut, lineABIntersectDE, lineABIntersectDF, lineABIntersectEF, vOfABAndDE, vOfABAndDF, vOfABAndEF);
		CoplanarTrianglesIntersect2(itSrc, itCut, lineACIntersectDE, lineACIntersectDF, lineACIntersectEF, vOfACAndDE, vOfACAndDF, vOfACAndEF);
		CoplanarTrianglesIntersect2(itSrc, itCut, lineBCIntersectDE, lineBCIntersectDF, lineBCIntersectEF, vOfBCAndDE, vOfBCAndDF, vOfBCAndEF);
		CoplanarTrianglesIntersect2(itCut, itSrc, lineABIntersectDE, lineACIntersectDE, lineBCIntersectDE, vOfABAndDE, vOfACAndDE, vOfBCAndDE);
		CoplanarTrianglesIntersect2(itCut, itSrc, lineABIntersectDF, lineACIntersectDF, lineBCIntersectDF, vOfABAndDF, vOfACAndDF, vOfBCAndDF);
		CoplanarTrianglesIntersect2(itCut, itSrc, lineABIntersectEF, lineACIntersectEF, lineBCIntersectEF, vOfABAndEF, vOfACAndEF, vOfBCAndEF);
	}

	void CompositeGeoSolid::CoplanarTrianglesIntersect(const GsIt& it0, const GsIt& it1,
		bool lineABIntersectDE, bool lineACIntersectDE, bool lineBCIntersectDE,
		GsVector3& vBetweenABAndDE, GsVector3& vBetweenACAndDE, GsVector3& vBetweenBCAndDE)
	{
		GeoSolidTriangle& gst0 = GetTriangle(it0);
		GeoSolidTriangle& gst1 = GetTriangle(it1);
		GsVector3 vg, vh;
		if (lineABIntersectDE && lineACIntersectDE)
		{
			vg = vBetweenABAndDE;
			vh = vBetweenACAndDE;
		}
		else if (lineABIntersectDE && lineBCIntersectDE)
		{
			vg = vBetweenABAndDE;
			vh = vBetweenBCAndDE;
		}
		else if (lineACIntersectDE && lineBCIntersectDE)
		{
			vg = vBetweenACAndDE;
			vh = vBetweenBCAndDE;
		}
		else
		{
			return;
		}
		GeoSolidVertex* gsv0 = GeoSolid::obtain<GeoSolidVertex>(vg, false, false);
		GeoSolidVertex* gsv1 = GeoSolid::obtain<GeoSolidVertex>(vh, false, false);
		if (gst0.intersectSrc.from || gst1.intersectSrc.from)
		{
			gsv0->intersectSrc.inside = true;
			gsv0->intersectSrc.hasCheckedInside = true;
			gsv1->intersectSrc.inside = true;
			gsv1->intersectSrc.hasCheckedInside = true;
		}
		if (gst0.intersectCut.from || gst1.intersectCut.from)
		{
			gsv0->intersectCut.inside = true;
			gsv0->intersectCut.hasCheckedInside = true;
			gsv1->intersectCut.inside = true;
			gsv1->intersectCut.hasCheckedInside = true;
		}
		GsDigit error = 1e-1;
		int cerror = 1;
		//局部动态精度判断
		GsIv ivg = AddVertex(gsv0, error);
		GsIv ivh = AddVertex(gsv1, error);
		if (ivg == ivh && vg != vh)
		{
			while (ivg == ivh)
			{
				error /= 10;
				++cerror;
				ivg = AddVertex(gsv0, error);
				ivh = AddVertex(gsv1, error);
				if (cerror >= 6)
				{
					//算到1e-6
					break;
				}
			}
		}
		GeoSolidVertex::recycle(gsv0);
		GeoSolidVertex::recycle(gsv1);
		if (ivg != ivh)
		{
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivg, ivh, true, true);
			const GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			const GsIe ie0 = AddEdgeIntoTriangle(ie, it0);
			const GsIe ie1 = AddEdgeIntoTriangle(ie, it1);
			const GeoSolidEdge& gse0 = GetEdge(ie0);
			const GeoSolidEdge& gse1 = GetEdge(ie1);
			AddVertexToTriangle(gse0.iv0, it0);
			AddVertexToTriangle(gse0.iv1, it0);
			AddVertexToTriangle(gse1.iv0, it1);
			AddVertexToTriangle(gse1.iv1, it1);
		}
	}

	void CompositeGeoSolid::CoplanarTrianglesIntersect2(const GsIt& itSrc, const GsIt& itCut, 
		bool lineABIntersectDE, bool lineABIntersectDF, bool lineABIntersectEF,
		GsVector3& vOfABAndDE, GsVector3& vOfABAndDF, GsVector3& vOfABAndEF)
	{
		GeoSolidTriangle& gstSrc = GetTriangle(itSrc);
		GeoSolidTriangle& gstCut = GetTriangle(itCut);
		const GsIv iva = gstSrc.iv0;
		const GsIv ivb = gstSrc.iv1;
		const GsIv ivc = gstSrc.iv2;
		const GsIv ivd = gstCut.iv0;
		const GsIv ive = gstCut.iv1;
		const GsIv ivf = gstCut.iv2;
		int cABIntersections = 0;
		cABIntersections += lineABIntersectDE ? 1 : 0;
		cABIntersections += lineABIntersectDF ? 1 : 0;
		cABIntersections += lineABIntersectEF ? 1 : 0;
		if (cABIntersections == 3)
		{
			//穿过一条边与D,E,F的其中一点
			GsVector3& vRealIntersect = vOfABAndDE;
			GsIv ivOfDEF;
			if (IsVertexEqual(vOfABAndDF, vOfABAndEF))
			{
				vRealIntersect = vOfABAndDE;
				ivOfDEF = ivf;
			}
			else if (IsVertexEqual(vOfABAndDE, vOfABAndEF))
			{
				vRealIntersect = vOfABAndDF;
				ivOfDEF = ive;
			}
			else if (IsVertexEqual(vOfABAndDE, vOfABAndDF))
			{
				vRealIntersect = vOfABAndEF;
				ivOfDEF = ivd;
			}
			else
			{
				SANDBOX_ASSERT(false);
				return;
			}
			GeoSolidVertex* gsvReal = GeoSolid::obtain<GeoSolidVertex>(vRealIntersect, true, true);
			GsIv ivReal = AddVertex(gsvReal);
			GeoSolidVertex::recycle(gsvReal);
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivReal, ivOfDEF, true, true);
			GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			AddVertexToTriangle(ivReal, itSrc);
			AddVertexToTriangle(ivReal, itCut);
			AddEdgeIntoTriangle(ie, itCut);
		}
		else if (cABIntersections == 2)
		{
			//普遍情况下的两点
			GsVector3& vi0 = vOfABAndDE;
			GsVector3& vi1 = vOfABAndDF;
			if (lineABIntersectDE && lineABIntersectDF)
			{
				vi0 = vOfABAndDE;
				vi1 = vOfABAndDF;
			}
			else if (lineABIntersectDE && lineABIntersectEF)
			{
				vi0 = vOfABAndDE;
				vi1 = vOfABAndEF;
			}
			else if (lineABIntersectDF && lineABIntersectEF)
			{
				vi0 = vOfABAndDF;
				vi1 = vOfABAndEF;
			}
			else
			{
				SANDBOX_ASSERT(false);
				return;
			}
			if (IsVertexEqual(vi0, vi1))
			{
				//AB穿过△DEF的顶点，刚好过两条边有交点
				return;
			}
			GeoSolidVertex* gsvi0 = GeoSolid::obtain<GeoSolidVertex>(vi0, true, true);
			GeoSolidVertex* gsvi1 = GeoSolid::obtain<GeoSolidVertex>(vi1, true, true);
			GsIv ivi0 = AddVertex(gsvi0);
			GsIv ivi1 = AddVertex(gsvi1);
			GeoSolidVertex::recycle(gsvi0);
			GeoSolidVertex::recycle(gsvi1);
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivi0, ivi1, true, true);
			GsIe ie = AddEdge(gse);
			GeoSolidEdge::recycle(gse);
			AddVertexToTriangle(ivi0, itSrc);
			AddVertexToTriangle(ivi1, itSrc);
			AddVertexToTriangle(ivi0, itCut);
			AddVertexToTriangle(ivi1, itCut);
			AddEdgeIntoTriangle(ie, itCut);
		}
	}

	void CompositeGeoSolid::LineRayIntersectTriangleForExtensiveVertex(const GsIt it, const GsIv ivg, const GsIv ivh)
	{
		GeoSolidTriangle& gst = GetTriangle(it);
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		const GsVector3& vg = GetVertex(ivg).v;
		const GsVector3& vh = GetVertex(ivh).v;
		const bool isGInEdge = GeometryMath::IsParalleled(va, vg, vb) || GeometryMath::IsParalleled(va, vg, vc) || GeometryMath::IsParalleled(vb, vg, vc);
		const bool isHInEdge = GeometryMath::IsParalleled(va, vh, vb) || GeometryMath::IsParalleled(va, vh, vc) || GeometryMath::IsParalleled(vb, vh, vc);
		if (isGInEdge && isHInEdge)
		{
			return;
		}
		//因为G、H必定在三角形内部、边上或与其顶点重合，所以取两者中点作为射线的发射点，
		//避开vg、vh在三角形边上或与其顶点重合时，射线检测到的第一个碰撞点就是vg、vh自己
		const GsVector3 vm = (vg + vh) * 0.5;
		//延长线扩展到与三角形边相交，产生新点与线段
		const GsVector3 vmg = vg - vm;
		const GsVector3 vmh = vh - vm;
		const GsVector3 vimg = vmg.GetNormalizedSafe();
		const GsVector3 vimh = vmh.GetNormalizedSafe();
		GsDigit timeRay;
		bool intersectTriangleEdges;
		GsRay ray;
		if (!isGInEdge)
		{
			ray.SetOrigin(vm);
			ray.SetDirection(vimg);
			//TODO: 2023-04-07 15:24:55: 是否可合并成直线与线段相交？
			intersectTriangleEdges = IntersectRayTriangleEdges(ray, va, vb, vc, timeRay);
			if (intersectTriangleEdges)
			{
				GsVector3 vp = ray.GetOrigin() + timeRay * ray.GetDirection();
				if (IsZero(timeRay))
				{
				}
				//else if (IsVertexEqual(vp, vg))
				//{
				//}
				//else if (IsVertexEqual(vp, vh))
				//{
				//}
				else
				{
					//不是在边上
					GsDigit distFromG = DistanceSqr(vp, vg);
					GsDigit distFromH = DistanceSqr(vp, vh);
					GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
					if (gst.intersectSrc.from)
					{
						gsv->intersectSrc.inside = true;
						gsv->intersectSrc.hasCheckedInside = true;
					}
					if (gst.intersectCut.from)
					{
						gsv->intersectCut.inside = true;
						gsv->intersectCut.hasCheckedInside = true;
					}
					const GsIv ivp = AddVertex(gsv);
					GeoSolidVertex::recycle(gsv);
					do {
						if (ivp == ivg || ivp == ivh)
						{
							break;
						}
						AddVertexToTriangle(ivp, it);
						GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(0, 0, true, true);
						if (distFromG < distFromH)
						{
							gse->iv0 = ivg;
							gse->iv1 = ivp;
						}
						else if (distFromG > distFromH)
						{
							gse->iv0 = ivh;
							gse->iv1 = ivp;
						}
						else
						{
							//WarningString("Vp has an equal distance to vg and vh.");
							GeoSolidEdge::recycle(gse);
							break;
						}
						const GsIe ie = AddEdge(gse);
						GeoSolidEdge::recycle(gse);
						AddEdgeIntoTriangle(ie, it);
					} while (false);
				}
			}
		}
		if (!isHInEdge)
		{
			ray.SetDirection(vimh);
			intersectTriangleEdges = IntersectRayTriangleEdges(ray, va, vb, vc, timeRay);
			if (intersectTriangleEdges)
			{
				GsVector3 vp = ray.GetOrigin() + timeRay * ray.GetDirection();
				if (IsZero(timeRay))
				{
				}
				//else if (IsVertexEqual(vp, vg))
				//{
				//}
				//else if (IsVertexEqual(vp, vh))
				//{
				//}
				else
				{
					//不是在边上
					GsDigit distFromG = DistanceSqr(vp, vg);
					GsDigit distFromH = DistanceSqr(vp, vh);
					GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(vp, false, false);
					if (gst.intersectSrc.from)
					{
						gsv->intersectSrc.inside = true;
						gsv->intersectSrc.hasCheckedInside = true;
					}
					if (gst.intersectCut.from)
					{
						gsv->intersectCut.inside = true;
						gsv->intersectCut.hasCheckedInside = true;
					}
					const GsIv ivp = AddVertex(gsv);
					GeoSolidVertex::recycle(gsv);
					do {
						if (ivp == ivg || ivp == ivh)
						{
							break;
						}
						AddVertexToTriangle(ivp, it);
						GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(0, 0, true, true);
						if (distFromG < distFromH)
						{
							gse->iv0 = ivg;
							gse->iv1 = ivp;
						}
						else if (distFromG > distFromH)
						{
							gse->iv0 = ivh;
							gse->iv1 = ivp;
						}
						else
						{
							//WarningString("Vp has an equal distance to vg and vh.");
							GeoSolidEdge::recycle(gse);
							break;
						}
						const GsIe ie = AddEdge(gse);
						GeoSolidEdge::recycle(gse);
						AddEdgeIntoTriangle(ie, it);
					} while (false);
				}
			}
		}
	}

	void CompositeGeoSolid::Interact(const InteractParams& stip)
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("+++++++++++++++++++++++++++++");
			WarningString("CompositeGeoSolid::Interact()");
		}
		#endif
		GeoSolid* gsSrc = stip.gsSrc;
		GeoSolid* gsCut = stip.gsCut;
		if (!gsSrc || !gsCut)
		{
			return;
		}
		switch (m_eGssSrc = gsSrc->GetShape())
		{
			case GeoSolidShape::CUBOID:
			case GeoSolidShape::WEDGE:
			case GeoSolidShape::PYRAMID:
			case GeoSolidShape::CYLINDER:
			case GeoSolidShape::CONE:
			case GeoSolidShape::SPHERE:
				m_bIsSrcBasic = true;
				break;
		}
		switch (m_eGssCut = gsCut->GetShape())
		{
			case GeoSolidShape::CUBOID:
			case GeoSolidShape::WEDGE:
			case GeoSolidShape::PYRAMID:
			case GeoSolidShape::CYLINDER:
			case GeoSolidShape::CONE:
			case GeoSolidShape::SPHERE:
				m_bIsCutBasic = true;
				break;
		}
		const ColorQuad& cqSrc = stip.cqSrc;
		const ColorQuad& cqCut = stip.cqCut;
		const ColorRGBA32 rgbaSrc(cqSrc.r, cqSrc.g, cqSrc.b, cqSrc.a);
		const ColorRGBA32 rgbaCut(cqCut.r, cqCut.g, cqCut.b, cqCut.a);
		const Matrix4x4f& mlwSrc = stip.mlwSrc;
		const Matrix4x4f& mlwCut = stip.mlwCut;
		const GsMatrix3x3 mlw3Src(mlwSrc);
		const GsMatrix3x3 mlw3Cut(mlwCut);
		const Quaterniondb qRotateSrc(mlwSrc.GetRotation());
		m_qRotateInverseSrc = qRotateSrc.Conjugate() / qRotateSrc.SqrMagnitude();
		const Quaterniondb qRotateCut(mlwCut.GetRotation());
		m_qRotateInverseCut = qRotateCut.Conjugate() / qRotateCut.SqrMagnitude();
		const GsVector3 v3ScaleSrc(mlwSrc.GetLossyScale());
		const GsVector3 v3ScaleCut(mlwCut.GetLossyScale());
		const GeoSolidTriangleArray& daTsSrc = gsSrc->GetTriangles();
		const GeoSolidTriangleArray& daTsCut = gsCut->GetTriangles();
		const GeoSolidEdgeArray& daEsSrc = gsSrc->GetEdges();
		const GeoSolidEdgeArray& daEsCut = gsCut->GetEdges();
		if (daEsSrc.empty())
		{
			if (CompositeGeoSolid* cgs = static_cast<CompositeGeoSolid*>(gsSrc))
			{
				cgs->RegenerateEdges();
			}
		}
		if (daEsCut.empty())
		{
			if (CompositeGeoSolid* cgs = static_cast<CompositeGeoSolid*>(gsCut))
			{
				cgs->RegenerateEdges();
			}
		}
		const GeoSolidVertexArray& daVsSrc = gsSrc->GetGeoSolidVertices();
		const GeoSolidVertexArray& daVsCut = gsCut->GetGeoSolidVertices();
		GeoSolidArray<GsBoxBound> daBbsSrc = gsSrc->GetBoxBounds();
		GeoSolidArray<GsBoxBound> daBbsCut = gsCut->GetBoxBounds();
		const GeoSolidArray<GsVector3>& daCentersSrc = gsSrc->GetCenters();
		const GeoSolidArray<GsVector3>& daCentersCut = gsCut->GetCenters();
		GeoSolidArray<ColorRGBA32> daColorsSrc = gsSrc->GetColors();
		GeoSolidArray<ColorRGBA32> daColorsCut = gsCut->GetColors();
		GeoSolidArray<GsMatrix3x3> daMatricesSrc = gsSrc->GetMatrices();
		GeoSolidArray<GsMatrix3x3> daMatricesCut = gsCut->GetMatrices();
		const GeoSolidArray<GsVector2>& daUvsSrc = gsSrc->GetUvs();
		const GeoSolidArray<GsVector2>& daUvsCut = gsCut->GetUvs();
		const GsVector3 vWorldSrc = mlwSrc.GetPosition();
		const GsVector3 vWorldCut = mlwCut.GetPosition();
		const GsVector3 vOffsetFromSrc = vWorldCut - vWorldSrc;
		m_bbSrc.m_MaxPos.Set(0, 0, 0);
		m_bbSrc.m_MinPos.Set(0, 0, 0);
		m_bbCut.m_MaxPos.Set(0, 0, 0);
		m_bbCut.m_MinPos.Set(0, 0, 0);
		//合并顶点、线段、三角面
		const UInt32 ctSrc = daTsSrc.size();
		const UInt32 ctCut = daTsCut.size();
		const UInt32 ct = ctSrc + ctCut;
		m_daTriangles.reserve(ct);
		//合并Cut的顶点、线段、三角面时，涉及的下标统一做偏移
		const UInt32 ceSrc = daEsSrc.size();
		const UInt32 ceCut = daEsCut.size();
		const UInt32 cvSrc = daVsSrc.size();
		const UInt32 cvCut = daVsCut.size();
		const UInt32 cbbSrc = daBbsSrc.size();
		const UInt32 cbbCut = daBbsCut.size();
		const UInt32 cvcSrc = m_bIsSrcBasic ? 1 : daCentersSrc.size();
		const UInt32 cvcCut = m_bIsCutBasic ? 1 : daCentersCut.size();
		const UInt32 cmSrc = m_bIsSrcBasic ? 1 : daMatricesSrc.size();
		const UInt32 cmCut = m_bIsCutBasic ? 1 : daMatricesCut.size();
		const UInt32 ccSrc = m_bIsSrcBasic ? 1 : daColorsSrc.size();
		const UInt32 ccCut = m_bIsCutBasic ? 1 : daColorsCut.size();
		const UInt32 cuvSrc = daUvsSrc.size();
		const UInt32 cuvCut = daUvsCut.size();
		m_cSrcTriangle = daTsSrc.size();
		m_cCutTriangle = daTsCut.size();
		m_daEdges.reserve(ceSrc + ceCut);
		m_daTriangles.reserve(ctSrc + ctCut);
		m_daBoxBounds.reserve(cbbSrc + cbbCut);
		m_daCenters.reserve(cvcSrc + cvcCut);
		m_daMatrices.reserve(cmSrc + cmCut);
		m_daColors.reserve(ccSrc + ccCut);
		m_daNormals.reserve(16);
		m_daUvs.reserve(cuvSrc + cuvCut);
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Merging arrays...");
		}
		#endif
		for (UInt32 iv = 0; iv < cvSrc; ++iv)
		{
			GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(daVsSrc[iv]);
			gsv->intersectSrc.from = true;
			gsv->intersectSrc.inside = true;
			gsv->intersectSrc.hasCheckedInside = true;
			gsv->intersectSrc.coincide = false;
			gsv->intersectSrc.hasCheckedCoincide = false;
			gsv->intersectCut.from = false;
			gsv->intersectCut.inside = false;
			gsv->intersectCut.hasCheckedInside = false;
			gsv->intersectCut.coincide = false;
			gsv->intersectCut.hasCheckedCoincide = false;
			gsv->v = mlw3Src.MultiplyVector3(gsv->v);
			m_bbSrc += gsv->v;
			m_daGeoSolidVertices.emplace_back(gsv);
		}
		//暂时以sgs的中心为中心点
		for (UInt32 iv = 0; iv < cvCut; ++iv)
		{
			GeoSolidVertex* gsv = GeoSolid::obtain<GeoSolidVertex>(daVsCut[iv]);
			gsv->intersectSrc.from = false;
			gsv->intersectSrc.inside = false;
			gsv->intersectSrc.hasCheckedInside = false;
			gsv->intersectSrc.coincide = false;
			gsv->intersectSrc.hasCheckedCoincide = false;
			gsv->intersectCut.from = true;
			gsv->intersectCut.inside = true;
			gsv->intersectCut.hasCheckedInside = true;
			gsv->intersectCut.coincide = false;
			gsv->intersectCut.hasCheckedCoincide = false;
			gsv->v = mlw3Cut.MultiplyVector3(gsv->v) + vOffsetFromSrc;
			m_bbCut += gsv->v;
			m_daGeoSolidVertices.emplace_back(gsv);
		}
		for (UInt32 ie = 0; ie < ceSrc; ++ie)
		{
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(daEsSrc[ie]);
			gse->intersectSrc.from = true;
			gse->intersectCut.from = false;
			gse->intersectSrc.inside = true;
			gse->intersectSrc.hasCheckedInside = true;
			gse->intersectCut.inside = false;
			m_daEdges.emplace_back(gse);
		}
		for (UInt32 ie = 0; ie < ceCut; ++ie)
		{
			GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(daEsCut[ie]);
			gse->iv0 += cvSrc;
			gse->iv1 += cvSrc;
			gse->intersectSrc.from = false;
			gse->intersectCut.from = true;
			gse->intersectSrc.inside = false;
			gse->intersectCut.inside = true;
			gse->intersectCut.hasCheckedInside = true;
			m_daEdges.emplace_back(gse);
		}
		for (UInt32 ibb = 0; ibb < cbbSrc; ++ibb)
		{
			GsBoxBound& bb = daBbsSrc[ibb];
			bb.m_MaxPos *= v3ScaleSrc;
			bb.m_MinPos *= v3ScaleSrc;
		}
		for (UInt32 ibb = 0; ibb < cbbCut; ++ibb)
		{
			GsBoxBound& bb = daBbsCut[ibb];
			bb.m_MaxPos *= v3ScaleCut;
			bb.m_MinPos *= v3ScaleCut;
		}

		if (m_bIsSrcBasic)
		{
			//这里的3x3统一按行优先处理
			daMatricesSrc.emplace_back(mlw3Src);
			daColorsSrc.emplace_back(rgbaSrc);
		}
		if (m_bIsCutBasic)
		{
			daMatricesCut.emplace_back(mlw3Cut);
			daColorsCut.emplace_back(rgbaCut);
		}
		for (UInt32 it = 0; it < ctSrc; ++it)
		{
			GeoSolidTriangle* pgst = GeoSolid::obtain<GeoSolidTriangle>(daTsSrc[it]);
			GeoSolidTriangle& gst = *pgst;
			gst.intersectSrc.from = true;
			gst.intersectSrc.inside = true;
			gst.intersectSrc.hasCheckedInside = true;
			gst.intersectCut.from = false;
			gst.intersectCut.inside = false;
			gst.intersectCut.hasCheckedInside = false;
			gst.iuv0 = AddUv(daUvsSrc[gst.iuv0]);
			gst.iuv1 = AddUv(daUvsSrc[gst.iuv1]);
			gst.iuv2 = AddUv(daUvsSrc[gst.iuv2]);
			const ColorRGBA32& c = daColorsSrc[gst.ic];
			gst.ic = AddColor(c);
			const GsBoxBound& bb = daBbsSrc[gst.ibb];
			gst.ibb = AddBoxBound(bb);
			const GsMatrix3x3& mat = daMatricesSrc[gst.im];
			gst.im = AddMatrix(mat);
			gst.ClearIesDivider();
			gst.ClearNewIvs();
			gst.ClearCoincidentIts();
			m_daTriangles.emplace_back(pgst);
		}
		for (UInt32 it = 0; it < ctCut; ++it)
		{
			GeoSolidTriangle* pgst = GeoSolid::obtain<GeoSolidTriangle>(daTsCut[it]);
			GeoSolidTriangle& gst = *pgst;
			gst.iv0 += cvSrc;
			gst.iv1 += cvSrc;
			gst.iv2 += cvSrc;
			gst.ie0 += ceSrc;
			gst.ie1 += ceSrc;
			gst.ie2 += ceSrc;
			gst.ivc += cvcSrc;
			//vn在下方重新计算
			gst.iuv0 = AddUv(daUvsCut[gst.iuv0]);
			gst.iuv1 = AddUv(daUvsCut[gst.iuv1]);
			gst.iuv2 = AddUv(daUvsCut[gst.iuv2]);
			const ColorRGBA32& c = daColorsCut[gst.ic];
			gst.ic = AddColor(c);
			const GsBoxBound& bb = daBbsCut[gst.ibb];
			gst.ibb = AddBoxBound(bb);
			const GsMatrix3x3& mat = daMatricesCut[gst.im];
			gst.im = AddMatrix(mat);
			gst.intersectSrc.from = false;
			gst.intersectSrc.inside = false;
			gst.intersectSrc.hasCheckedInside = false;
			gst.intersectCut.from = true;
			gst.intersectCut.inside = true;
			gst.intersectCut.hasCheckedInside = true;
			gst.ClearIesDivider();
			gst.ClearNewIvs();
			gst.ClearCoincidentIts();
			m_daTriangles.emplace_back(pgst);
		}
		//BoxBound、Matrix、Color嵌套层数不多，可不用合并
		if (m_bIsSrcBasic)
		{
			m_daCenters.emplace_back(GsVector3::zero);
		}
		else
		{
			for (UInt32 ivc = 0; ivc < cvcSrc; ++ivc)
			{
				m_daCenters.emplace_back(daCentersSrc[ivc]);
			}
		}
		if (m_bIsCutBasic)
		{
			GsVector3 vcCutInCut = RotateVectorByQuat(m_qRotateInverseCut, vOffsetFromSrc);
			m_daCenters.emplace_back(vcCutInCut);
		}
		else
		{
			for (UInt32 ivc = 0; ivc < cvcCut; ++ivc)
			{
				m_daCenters.emplace_back(daCentersCut[ivc]);
			}
		}

		const InteractMethod& eIm = stip.eIm;
		if (eIm != InteractMethod::SIMPLY_UNION)
		{
			//合并
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("MergeCoincidentVerticesContrast()...");
			}
			#endif
			MergeCoincidentVerticesContrast((GsIv)cvSrc, (GsIv)cvCut);
		}

		/**
			判断平面相交情况：
			从gs2获取的△DEF，切割从gs获取的△ABC，交线为GH
		*/
		RecaculateNormals(gsSrc, gsCut);

		#if !COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES
		RemapNeighborVertices();
		#endif

		if (eIm != InteractMethod::SIMPLY_UNION)
		{
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("Checking vertices inside each...");
			}
			#endif
			//判断所有点是否在Src或Cut里，包括重合与在边上
			const GsIv cv = GetVertexCount();
			for (GsIv iv = 0; iv < cv; ++iv)
			{
				GeoSolidVertex& gsv = GetVertex(iv);
				if (gsv.intersectSrc.from && !gsv.intersectCut.hasCheckedInside)
				{
					gsv.intersectCut.inside = IsVertexInGeoSolid(iv, false);
				}
				if (gsv.intersectCut.from && !gsv.intersectSrc.hasCheckedInside)
				{
					gsv.intersectSrc.inside = IsVertexInGeoSolid(iv, true);
				}
			}

			for (GsIndex it = 0; it < ct; ++it)
			{
				GeoSolidTriangle& gst = GetTriangle(it);
				if (gst.area >= 0)
				{
					continue;
				}
				const GsVector3& v0 = GetVertex((GsIndex)gst.iv0).v;
				const GsVector3& v1 = GetVertex((GsIndex)gst.iv1).v;
				const GsVector3& v2 = GetVertex((GsIndex)gst.iv2).v;
				gst.area = GetArea(v0, v1, v2);
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Rotating vertices in each coordinates...");
		}
		#endif
		//计算在各自模型坐标系下的坐标值
		const GsIv cv = GetVertexCount();
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			GeoSolidVertex& gsv = GetVertex(iv);
			gsv.vInSrc = RotateVectorByQuat(m_qRotateInverseSrc, gsv.v);
			gsv.vInCut = RotateVectorByQuat(m_qRotateInverseCut, gsv.v);
		}

		bool hasIntersections = false;
		if (eIm != InteractMethod::SIMPLY_UNION)
		{
			bool coplanar;
			GsVector3 vg, vh;
			//判断Src的每个面，与Cut的每个面，两两相交的情况
			//TODO: 2023-04-13 11:11:25: 优化：与球之间的相交，生成慢
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("Triangles intersecting...");
			}
			#endif
			for (GsIt itSrc = 0; itSrc < (GsIt)ctSrc; ++itSrc)
			{
				GeoSolidTriangle& gstSrc = GetTriangle(itSrc);
				const GsIv iva = gstSrc.iv0;
				const GsIv ivb = gstSrc.iv1;
				const GsIv ivc = gstSrc.iv2;
				const GsVector3& va = GetVertex(iva).v;
				const GsVector3& vb = GetVertex(ivb).v;
				const GsVector3& vc = GetVertex(ivc).v;
				for (UInt32 j = 0; j < ctCut; ++j)
				{
					const GsIt itCut = (GsIt)j + ctSrc;
					GeoSolidTriangle& gstCut = GetTriangle(itCut);
					const GsIv ivd = gstCut.iv0;
					const GsIv ive = gstCut.iv1;
					const GsIv ivf = gstCut.iv2;
					const GsVector3& vd = GetVertex(ivd).v;
					const GsVector3& ve = GetVertex(ive).v;
					const GsVector3& vf = GetVertex(ivf).v;
					//#if COMPOSITE_GEO_SOLID_DEBUG
					//if (m_bDebugDetail)
					//{
					//	std::ostringstream oss;
					//	oss << "Execute({";
					//	GeoGebraAppendVertexCmd(oss, gstSrc.iv0);
					//	oss << ',';
					//	GeoGebraAppendVertexCmd(oss, gstSrc.iv1);
					//	oss << ',';
					//	GeoGebraAppendVertexCmd(oss, gstSrc.iv2);
					//	oss << ',';
					//	GeoGebraAppendVertexCmd(oss, gstCut.iv0);
					//	oss << ',';
					//	GeoGebraAppendVertexCmd(oss, gstCut.iv1);
					//	oss << ',';
					//	GeoGebraAppendVertexCmd(oss, gstCut.iv2);
					//	oss << ',';
					//	GeoGebraAppendTriangleCmd(oss, itSrc);
					//	oss << ',';
					//	GeoGebraAppendTriangleCmd(oss, itCut);
					//	oss << "})";
					//	WarningStringMsg("T%u intersects with T%u", itSrc, itCut);
					//	WarningString(oss.str().c_str());
					//}
					//#endif
					bool coplanar;
					//bool intersect = IntersectTriangleTriangle(va, vb, vc, vd, ve, vf, &vg, &vh, &coplanar);
					//万恶之源：有浮点数误差
					Vector3f vfg;
					Vector3f vfh;
					bool intersect = Rainbow::IntersectTriTri(va.ToFloat(), vb.ToFloat(), vc.ToFloat(), 
						vd.ToFloat(), ve.ToFloat(), vf.ToFloat(), &vfg, &vfh, &coplanar);
					if (!intersect)
					{
						continue;
					}
					vg = vfg;
					vh = vfh;
					//if (coplanar)
					//{
					//	//偏移其中一个点，来跳过共面的判断
					//	//与共面相关的空间关系判断中有浮点数精度的问题
					//	const GsVector3& vn = m_daNormals[gstCut.ivn];
					//	GsVector3 vd0 = vd + vn;
					//	GsVector3 ve0 = ve - vn;
					//	GsVector3 vf0 = vf - vn;
					//	GsVector3 vg0;
					//	GsVector3 vh0;
					//	bool coplanar0;
					//	intersect = IntersectTriTri(va, vb, vc, vd0, ve0, vf0, &vg0, &vh0, &coplanar0);
					//	if (!intersect)
					//	{
					//		vd0 = vd - vn;
					//		ve0 = ve + vn;
					//		vf0 = vf - vn;
					//		intersect = IntersectTriTri(va, vb, vc, vd0, ve0, vf0, &vg0, &vh0, &coplanar0);
					//	}
					//	if (!intersect)
					//	{
					//		vd0 = vd - vn;
					//		ve0 = ve - vn;
					//		vf0 = vf + vn;
					//		intersect = IntersectTriTri(va, vb, vc, vd0, ve0, vf0, &vg0, &vh0, &coplanar0);
					//	}
					//	if (intersect)
					//	{
					//		vg = vg0;
					//		vh = vh0;
					//		coplanar = coplanar0;
					//	}
					//	//偏移不相交，维持原有结果
					//}
					if (coplanar)
					{
						hasIntersections = hasIntersections || true;
						//TODO: 2023-03-31 11:31:03: 
						//IntersectTriTri求共面三角形时的交点不准确，因此特地自求
						//共面的三角面，将新增的线段、点合并
						gstSrc.intersectCut.coincide = true;
						gstCut.intersectSrc.coincide = true;
						gstSrc.GetCoincidentIts().emplace(itCut);
						gstCut.GetCoincidentIts().emplace(itSrc);

						const bool isACoincideWithD = iva == ivd;
						const bool isACoincideWithE = iva == ive;
						const bool isACoincideWithF = iva == ivf;
						const bool isBCoincideWithD = ivb == ivd;
						const bool isBCoincideWithE = ivb == ive;
						const bool isBCoincideWithF = ivb == ivf;
						const bool isCCoincideWithD = ivc == ivd;
						const bool isCCoincideWithE = ivc == ive;
						const bool isCCoincideWithF = ivc == ivf;
						if ((isACoincideWithD && isBCoincideWithE && isCCoincideWithF)
							|| (isACoincideWithD && isBCoincideWithF && isCCoincideWithE)
							|| (isACoincideWithE && isBCoincideWithD && isCCoincideWithF)
							|| (isACoincideWithE && isBCoincideWithF && isCCoincideWithD)
							|| (isACoincideWithF && isBCoincideWithD && isCCoincideWithE)
							|| (isACoincideWithF && isBCoincideWithE && isCCoincideWithD)
							)
						{
							continue;
						}
						CoplanarTrianglesIntersect2(itSrc, itCut);
						//GsVector3 vBetweenABAndDE;
						//GsVector3 vBetweenABAndDF;
						//GsVector3 vBetweenABAndEF;
						//GsVector3 vBetweenACAndDE;
						//GsVector3 vBetweenACAndDF;
						//GsVector3 vBetweenACAndEF;
						//GsVector3 vBetweenBCAndDE;
						//GsVector3 vBetweenBCAndDF;
						//GsVector3 vBetweenBCAndEF;
						//bool lineABIntersectDE = WhetherLineIntersectLine(va, vb, vd, ve, &vBetweenABAndDE);
						//bool lineABIntersectDF = WhetherLineIntersectLine(va, vb, vd, vf, &vBetweenABAndDF);
						//bool lineABIntersectEF = WhetherLineIntersectLine(va, vb, ve, vf, &vBetweenABAndEF);
						//bool lineACIntersectDE = WhetherLineIntersectLine(va, vc, vd, ve, &vBetweenACAndDE);
						//bool lineACIntersectDF = WhetherLineIntersectLine(va, vc, vd, vf, &vBetweenACAndDF);
						//bool lineACIntersectEF = WhetherLineIntersectLine(va, vc, ve, vf, &vBetweenACAndEF);
						//bool lineBCIntersectDE = WhetherLineIntersectLine(vb, vc, vd, ve, &vBetweenBCAndDE);
						//bool lineBCIntersectDF = WhetherLineIntersectLine(vb, vc, vd, vf, &vBetweenBCAndDF);
						//bool lineBCIntersectEF = WhetherLineIntersectLine(vb, vc, ve, vf, &vBetweenBCAndEF);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineABIntersectDE, lineACIntersectDE, lineBCIntersectDE, vBetweenABAndDE, vBetweenACAndDE, vBetweenBCAndDE);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineABIntersectDF, lineACIntersectDF, lineBCIntersectDF, vBetweenABAndDF, vBetweenACAndDF, vBetweenBCAndDF);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineABIntersectEF, lineACIntersectEF, lineBCIntersectEF, vBetweenABAndEF, vBetweenACAndEF, vBetweenBCAndEF);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineABIntersectDE, lineABIntersectDF, lineABIntersectEF, vBetweenABAndDE, vBetweenABAndDF, vBetweenABAndEF);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineACIntersectDE, lineACIntersectDF, lineACIntersectEF, vBetweenACAndDE, vBetweenACAndDF, vBetweenACAndEF);
						//CoplanarTrianglesIntersect(itSrc, itCut, lineBCIntersectDE, lineBCIntersectDF, lineBCIntersectEF, vBetweenBCAndDE, vBetweenBCAndDF, vBetweenBCAndEF);
					}
					else
					{
						GeoSolidVertex* gsv0 = GeoSolid::obtain<GeoSolidVertex>(vg, false, false);
						GeoSolidVertex* gsv1 = GeoSolid::obtain<GeoSolidVertex>(vh, false, false);
						gsv0->intersectSrc.inside = true;
						gsv0->intersectSrc.hasCheckedInside = true;
						gsv0->intersectCut.inside = true;
						gsv0->intersectCut.hasCheckedInside = true;
						gsv1->intersectSrc.inside = true;
						gsv1->intersectSrc.hasCheckedInside = true;
						gsv1->intersectCut.inside = true;
						gsv1->intersectCut.hasCheckedInside = true;
						//局部动态精度判断
						GsDigit error = 1e-1;
						//int cerror = 1;
						GsIv ivg = AddVertex(gsv0, error);
						GsIv ivh = AddVertex(gsv1, error);
						GeoSolidVertex::recycle(gsv0);
						GeoSolidVertex::recycle(gsv1);
						bool bothCloseToSrcVertex = false;
						bool bothCloseToCutVertex = false;
						//if (ivg == ivh && vg != vh)
						if (ivg == ivh)
						{
							continue;
							//bothCloseToSrcVertex = ivg == iva || ivg == ivb || ivg == ivc;
							//bothCloseToCutVertex = ivg == ivd || ivg == ive || ivg == ivf;
							//if (bothCloseToSrcVertex && bothCloseToCutVertex)
							//{
							//	continue;
							//}
							//while (ivg == ivh)
							//{
							//	error /= 10;
							//	++cerror;
							//	ivg = AddVertex(gsv0, error);
							//	ivh = AddVertex(gsv1, error);
							//	if (cerror >= 4)
							//	{
							//		//算到1e-4
							//		break;
							//	}
							//}
						}
						hasIntersections = hasIntersections || true;
						if (!bothCloseToSrcVertex || !bothCloseToCutVertex)
						{
							GeoSolidEdge* gse = GeoSolid::obtain<GeoSolidEdge>(ivg, ivh, true, true);
							const GsIe ie = AddEdge(gse);
							GeoSolidEdge::recycle(gse);
							if (!bothCloseToSrcVertex)
							{
								const GsIe ieNew = AddEdgeIntoTriangle(ie, itSrc);
								const GeoSolidEdge& gseNew = GetEdge(ieNew);
								AddVertexToTriangle(gseNew.iv0, itSrc);
								AddVertexToTriangle(gseNew.iv1, itSrc);
							}
							if (!bothCloseToCutVertex)
							{
								const GsIe ieNew = AddEdgeIntoTriangle(ie, itCut);
								const GeoSolidEdge& gseNew = GetEdge(ieNew);
								AddVertexToTriangle(gseNew.iv0, itCut);
								AddVertexToTriangle(gseNew.iv1, itCut);
							}
						}
					}
				}
			}
		}
		if (!hasIntersections)
		{
			if (eIm == InteractMethod::HOLLOW)
			{
				KeepTrianglesToRender(eIm);
				RemoveInvalidTriangles();
				RemoveInvalidEdges();
				FinalizeSubMeshes();
				FinalizeEdges();
				FinalizeVertices(); 
			}
			CreateSubMeshes();
			UpdateBound();
			ClearWriteOnceBoolean();
			return;
		}

		//#if COMPOSITE_GEO_SOLID_DEBUG
		//if (m_bDebugDetail)
		//{
		//	WarningString("=========================Test DivideTriangleByPolygons=========================");
		//}
		//#endif
		//for (GsIt it = 0; it < (GsIt)ct; ++it)
		//{
		//	DivideTriangleByPolygons(it);
		//}

		#if COMPOSITE_DIVIDE_BY_VERTICES_AND_EDGES
		{
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("ExtendDivisionEdgeToSide():");
			}
			#endif
			for (GsIt it = 0; it < (GsIt)ct; ++it)
			{
				ExtendDivisionEdgeToSide(it);
			}
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("=========================DivideTriangle=========================");
			}
			#endif
			for (GsIt it = 0; it < (GsIt)ct; ++it)
			{
				DivideTriangle(it);
			}
		}
		#else
		{
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				//WarningString("=========================DivideTriangleByPolygons=========================");
				WarningString("=========================DivideTriangleByRemovingEdges=========================");
			}
			#endif
			for (GsIt it = 0; it < (GsIt)ct; ++it)
			{
				//DivideTriangleByPolygons(it);
				DivideTriangleByRemovingEdges(it);
			}
		}
		#endif

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningString("-----------------------------");
			WarningStringMsg("Src: cv = %4u | ce = %4u | ct = %4u", cvSrc, ceSrc, ctSrc);
			WarningStringMsg("Cut: cv = %4u | ce = %4u | ct = %4u", cvCut, ceCut, ctCut);
			std::ostringstream ossVs;
			std::ostringstream ossTs;
			for (GsIv iv = 0; iv < (GsIv)cvSrc; ++iv)
			{
				GeoGebraAppendVertexCmd(ossVs, iv);
				ossVs << ',';
			}
			for (GsIt it = 0; it < (GsIt)ctSrc; ++it)
			{
				GeoGebraAppendTriangleCmd(ossTs, it);
				ossTs << ',';
			}
			for (int i = 0; i < cvCut; ++i)
			{
				GsIv iv = GsIv(i) + cvSrc;
				GeoGebraAppendVertexCmd(ossVs, iv);
				if (i != cvCut - 1)
				{
					ossVs << ',';
				}
			}
			for (int i = 0; i < ctCut; ++i)
			{
				GsIt it = (GsIt)i + ctSrc;
				GeoGebraAppendTriangleCmd(ossTs, it);
				if (i != ctCut - 1)
				{
					ossTs << ',';
				}
			}
			if (m_bDebugDetail)
			{
				WarningString("GeoGebra cmd list:");
				GeoGebraExecuteCmdLists(ossVs, ossTs);
			}
			WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
		}
		#endif

		KeepTrianglesToRender(eIm);
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			//需在精简数据前，打印原始数据
			WarningString("After compositing:");
			WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
			if (m_bDebugDetail)
			{
				GeoGebraExecuteWhole(false);
			}
		}
		#endif

		CreateSubMeshes();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			PrintSubMeshes();
		}
		#endif

		//将邻近点合并成一个
		//MergeCoincidentVertices();
		// 
		//if (false)
		{
			ReconstructWithPolygon();
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugSummary)
			{
				//FinalizeEdges();
				//FinalizeVertices();
				WarningString("After reconstruction with polygon:");
				WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
				if (m_bDebugDetail)
				{
					GeoGebraExecuteWhole();
				}
			}
			#endif
		}

		//合并有共用边、而且有共线边的三角形
		//if (false)
		{
			MergeCoplanarAdjacentTriangles();
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugSummary)
			{
				FinalizeEdges();
				FinalizeVertices();
				WarningString("After merging coplanar adjacent triangles:");
				WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
				if (m_bDebugDetail)
				{
					GeoGebraExecuteWhole();
					PrintSubMeshes();
				}
			}
			#endif
		}

		RemoveInvalidTriangles();
		RemoveInvalidEdges();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningString("After removing the invalid:");
			WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
			if (m_bDebugDetail)
			{
				GeoGebraExecuteWhole();
			}
		}
		#endif

		//保留最终用于展示三角形的顶点和线段
		//TODO: 2023-11-02 18:23:39: 最终的法线、uv、颜色、矩阵、中心点
		FinalizeSubMeshes();
		FinalizeEdges();
		FinalizeVertices();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningString("After finalization:");
			WarningStringMsg("cv = %4u | ce = %4u | ct = %4u", m_daGeoSolidVertices.size(), m_daEdges.size(), m_daTriangles.size());
			if (m_bDebugDetail)
			{
				GeoGebraExecuteWhole();
			}
		}
		#endif

		UpdateBound();
		ClearWriteOnceBoolean();
	}

	void CompositeGeoSolid::RecaculateNormals(GeoSolid* gsSrc, GeoSolid* gsCut)
	{
		const GeoSolidTriangleArray& daTsSrc = gsSrc->GetTriangles();
		const GeoSolidTriangleArray& daTsCut = gsCut->GetTriangles();
		const UInt32 ctSrc = daTsSrc.size();
		const UInt32 ctCut = daTsCut.size();
		const bool unitRotateSrc = Equal(m_qRotateInverseSrc, Quaterniondb::identity);
		const bool recaculateSrc = !(m_bIsSrcBasic && unitRotateSrc);
		const bool unitRotateCut = Equal(m_qRotateInverseCut, Quaterniondb::identity);
		const bool recaculateCut = !(m_bIsCutBasic && unitRotateCut);
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningString("Recaculating normals...");
		}
		int skipZeroVector = 0;
		int skipAlmostLinear = 0;
		#endif
		//删除重合点后，再重新计算法线
		const GsDigit almostLinear = 1e-4;
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			const GsVector3& v0 = GetVertex(gst.iv0).v;
			const GsVector3& v1 = GetVertex(gst.iv1).v;
			const GsVector3& v2 = GetVertex(gst.iv2).v;
			const GsVector3 v01 = v1 - v0;
			const GsVector3 v02 = v2 - v0;
			GsVector3 vn = CrossProduct(v01, v02);
			if ((gst.intersectSrc.from && recaculateSrc) ||
				(gst.intersectCut.from && recaculateCut))
			{
				//T720，超市天花板，前方，右方，左下到右上
				//[1/2]Composite "Union" and "a"
				if (IsVertexEqual(vn, GsVector3::zero))
				{
					#if COMPOSITE_GEO_SOLID_DEBUG
					++skipZeroVector;
					#endif
					continue;
				}
				const GsDigit a = 0.5 * vn.Length();
				if (a < 1.0)
				{
					//太小的不去计算
					continue;
				}
				const GsVector3 vi01 = v01.GetNormalizedSafe();
				const GsVector3 v12 = v2 - v1;
				const GsVector3 vi12 = v12.GetNormalizedSafe();
				if (IsVertexEqual(vi01, vi12, almostLinear))
				{
					//太靠近的不去计算
					#if COMPOSITE_GEO_SOLID_DEBUG
					++skipAlmostLinear;
					#endif
					continue;
				}
				const GsVector3 v20 = v0 - v2;
				const GsVector3 vi20 = v20.GetNormalizedSafe();
				if (IsVertexEqual(vi01, vi20, almostLinear))
				{
					#if COMPOSITE_GEO_SOLID_DEBUG
					++skipAlmostLinear;
					#endif
					continue;
				}
				if (IsVertexEqual(vi12, vi20, almostLinear))
				{
					#if COMPOSITE_GEO_SOLID_DEBUG
					++skipAlmostLinear;
					#endif
					continue;
				}
			}
			vn.NormalizeSafe();
			//const UInt32 ivnOld = gst.ivn;
			const UInt32 ivn = AddNormal(vn);
			gst.ivn = ivn;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			WarningStringMsg("Skip  zero vectors: %d", skipZeroVector);
			WarningStringMsg("Skip almost linear: %d", skipAlmostLinear);
		}
		#endif
	}

	void CompositeGeoSolid::RemapNeighborVertices()
	{
		const GsIv cv = GetVertexCount();
		for (GsIv iv = 0; iv < cv; ++iv)
		{
			GeoSolidVertex& gsv = GetVertex(iv);
			gsv.listNeighbors.clear();
		}
		const GsIv ce = GetEdgeCount();
		for (GsIv ie = 0; ie < ce; ++ie)
		{
			AddNeighborsToEach(ie);
		}
	}

	void CompositeGeoSolid::MergeCoincidentVerticesContrast(const GsIv& cvSrc, const GsIv& cvCut)
	{
		//对重合的点进行删除，并做下标调整
		std::unordered_map<GsIv, GsIv> mReplaceIvs;
		std::vector<GsIv> vIvsToRemove;
		GsIndex oSrc = (GsIndex)cvSrc;
		for (GsIv iv = 0; iv < cvCut; ++iv)
		{
			GsIv ivCut = iv + oSrc;
			GeoSolidVertex& gsvCut = GetVertex(ivCut);
			for (GsIv ivSrc = 0; ivSrc < cvSrc; ++ivSrc)
			{
				GeoSolidVertex& gsvSrc = GetVertex(ivSrc);
				//仅合并完全相同的点，不能控制精度
				if (gsvSrc.v == gsvCut.v)
				{
					gsvSrc.intersectSrc.from = true;
					mReplaceIvs[ivCut] = ivSrc;
					vIvsToRemove.emplace_back(ivCut);
					break;
				}
			}
		}
		if (!vIvsToRemove.empty())
		{
			std::sort(vIvsToRemove.begin(), vIvsToRemove.end());
			UInt32 c = vIvsToRemove.size();
			for (int iiv = c - 1; iiv >= 0; --iiv)
			{
				const GsIv iv = vIvsToRemove[iiv];
				ReplaceVertexIndex(iv, mReplaceIvs[iv]);
			}
			for (int iiv = c - 1; iiv >= 0; --iiv)
			{
				const GsIv iv = vIvsToRemove[iiv];
				RemoveVertex(iv);
			}
		}
	}

	void CompositeGeoSolid::MergeCoincidentVertices()
	{
		//std::vector<int> vIvsToReplace;
		//std::vector<int> vIuvsToReplace;
		//const GsIv cv = GetGeoSolidVertexCount();
		//vIvsToReplace.resize(cv);
		//for (GsIv iv = 0; iv < cv; ++iv)
		//{
		//	vIvsToReplace[iv] = iv;
		//}
		//const UInt32 cuv = m_daUvs.size();
		//vIuvsToReplace.resize(cuv);
		//for (UInt32 iuv = 0; iuv < cuv; ++iuv)
		//{
		//	vIuvsToReplace[iuv] = iuv;
		//}
		//const GsIt ct = GetTriangleCount();
		//int decrement = 0;
		//for (GsIt it = 0; it < ct; ++it)
		//{
		//	GeoSolidTriangle& gst = GetTriangle(it);
		//	const GsVector3& v0 = GetGeoSolidVertex(gst.iv0).v;
		//	const GsVector3& v1 = GetGeoSolidVertex(gst.iv1).v;
		//	const GsVector3& v2 = GetGeoSolidVertex(gst.iv2).v;
		//	if (IsVertexEqual(v0, v1, 1) &&
		//		IsVertexEqual(v0, v2, 1) &&
		//		IsVertexEqual(v1, v2, 1)
		//		)
		//	{
		//		gst.markRemoval = true;
		//		vIvsToReplace[gst.iv1] = gst.iv0;
		//		vIvsToReplace[gst.iv2] = gst.iv0;
		//		vIuvsToReplace[gst.iuv1] = gst.iuv0;
		//		vIuvsToReplace[gst.iuv2] = gst.iuv0;
		//		decrement += 2;
		//	}
		//}
		//if (decrement <= 0)
		//{
		//	return;
		//}
		//ReplaceVerticesIndices(vIvsToReplace);
		//ReplaceUvsIndices(vIuvsToReplace);
		//#if COMPOSITE_GEO_SOLID_DEBUG
		//if (m_bDebugSummary)
		//{
		//	WarningStringMsg("After MergeCoincidentVertices(): dv = %d", decrement);
		//}
		//#endif
	}

	int CompositeGeoSolid::MergeTwoTriangles(const GsIt& itL, const GsIt& itR)
	{
		const GeoSolidTriangle& gstL = GetTriangle(itL);
		const GeoSolidTriangle& gstR = GetTriangle(itR);
		if (!gstL.IsValid() || !gstR.IsValid())
		{
			return -1;
		}
		if (gstL.ivn != gstR.ivn)
		{
			const GsVector3& vnL = m_daNormals[gstL.ivn];
			const GsVector3& vnR = m_daNormals[gstR.ivn];
			return -1;
		}
		if (gstL.gss != gstR.gss)
		{
			return -1;
		}
		if (gstL.gsf != gstR.gsf)
		{
			return -1;
		}
		const bool common0 = gstL.ie0 == gstR.ie0 || gstL.ie0 == gstR.ie1 || gstL.ie0 == gstR.ie2;
		const bool common1 = gstL.ie1 == gstR.ie0 || gstL.ie1 == gstR.ie1 || gstL.ie1 == gstR.ie2;
		const bool common2 = gstL.ie2 == gstR.ie0 || gstL.ie2 == gstR.ie1 || gstL.ie2 == gstR.ie2;
		if (!common0 && !common1 && !common2)
		{
			return -1;
		}
		GsIe ieCommon;
		GsIv ivAloneL;
		GsIv ivAloneR;
		UInt32 iuvL;
		UInt32 iuvR;
		if (common0)
		{
			ieCommon = gstL.ie0;
		}
		else if (common1)
		{
			ieCommon = gstL.ie1;
		}
		else if (common2)
		{
			ieCommon = gstL.ie2;
		}
		else
		{
			return -1;
		}
		GeoSolidEdge& gseCommon = GetEdge(ieCommon);
		if ((gstL.iv0 == gseCommon.iv0 && gstL.iv1 == gseCommon.iv1) ||
			(gstL.iv1 == gseCommon.iv0 && gstL.iv0 == gseCommon.iv1)
			)
		{
			ivAloneL = gstL.iv2;
			iuvL = gstL.iuv2;
		}
		else if ((gstL.iv0 == gseCommon.iv0 && gstL.iv2 == gseCommon.iv1) ||
			(gstL.iv2 == gseCommon.iv0 && gstL.iv0 == gseCommon.iv1)
			)
		{
			ivAloneL = gstL.iv1;
			iuvL = gstL.iuv1;
		}
		else if ((gstL.iv1 == gseCommon.iv0 && gstL.iv2 == gseCommon.iv1) ||
			(gstL.iv2 == gseCommon.iv0 && gstL.iv1 == gseCommon.iv1)
			)
		{
			ivAloneL = gstL.iv0;
			iuvL = gstL.iuv0;
		}
		else
		{
			return -1;
		}
		if ((gstR.iv0 == gseCommon.iv0 && gstR.iv1 == gseCommon.iv1) ||
			(gstR.iv1 == gseCommon.iv0 && gstR.iv0 == gseCommon.iv1)
			)
		{
			ivAloneR = gstR.iv2;
			iuvR = gstR.iuv2;
		}
		else if ((gstR.iv0 == gseCommon.iv0 && gstR.iv2 == gseCommon.iv1) ||
			(gstR.iv2 == gseCommon.iv0 && gstR.iv0 == gseCommon.iv1)
			)
		{
			ivAloneR = gstR.iv1;
			iuvR = gstR.iuv1;
		}
		else if ((gstR.iv1 == gseCommon.iv0 && gstR.iv2 == gseCommon.iv1) ||
			(gstR.iv2 == gseCommon.iv0 && gstR.iv1 == gseCommon.iv1)
			)
		{
			ivAloneR = gstR.iv0;
			iuvR = gstR.iuv0;
		}
		else
		{
			return -1;
		}
		const GeoSolidVertex& gsvCommon0 = GetVertex(gseCommon.iv0);
		const GeoSolidVertex& gsvCommon1 = GetVertex(gseCommon.iv1);
		const GeoSolidVertex& gsvAloneL = GetVertex(ivAloneL);
		const GeoSolidVertex& gsvAloneR = GetVertex(ivAloneR);
		GsIv ivCommon;
		UInt32 iuvCommon;
		const GsVector3 vLR = gsvAloneR.v - gsvAloneL.v;
		GsVector3 vLCommon;
		if (GeometryMath::IsParalleled(gsvAloneL.v, gsvCommon0.v, gsvAloneR.v))
		{
			ivCommon = gseCommon.iv1;
			vLCommon = gsvCommon1.v - gsvAloneL.v;
		}
		else if (GeometryMath::IsParalleled(gsvAloneL.v, gsvCommon1.v, gsvAloneR.v))
		{
			ivCommon = gseCommon.iv0;
			vLCommon = gsvCommon0.v - gsvAloneL.v;
		}
		else
		{
			return -1;
		}
		if (gstL.iv0 == ivCommon)
		{
			iuvCommon = gstL.iuv0;
		}
		else if (gstL.iv1 == ivCommon)
		{
			iuvCommon = gstL.iuv1;
		}
		else if (gstL.iv2 == ivCommon)
		{
			iuvCommon = gstL.iuv2;
		}
		else
		{
			return -1;
		}
		GeoSolidTriangle* gstLarge = GeoSolid::obtain<GeoSolidTriangle>(0, ivCommon, 0, gstL);
		gstLarge->iuv1 = iuvCommon;
		const GsVector3 vcp = CrossProduct(vLCommon, vLR);
		const GsDigit dp = DotProduct(vcp, m_daNormals[gstL.ivn]);
		if (dp > 0)
		{
			gstLarge->iv0 = ivAloneL;
			gstLarge->iv2 = ivAloneR;
			gstLarge->iuv0 = iuvL;
			gstLarge->iuv2 = iuvR;
		}
		else
		{
			gstLarge->iv0 = ivAloneR;
			gstLarge->iv2 = ivAloneL;
			gstLarge->iuv0 = iuvR;
			gstLarge->iuv2 = iuvL;
		}
		gstLarge->intersectCut = gstL.intersectCut;
		gstLarge->intersectSrc = gstL.intersectSrc;
		const GsIt it = AddTriangle(gstLarge);
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary && m_bDebugDetail)
		{
			WarningStringMsg("Merge common-side T%u and T%u into T%u:", itL, itR, it);
			std::ostringstream oss;
			oss << "Execute({";
			GeoGebraAddTriangleCmd(oss, itL);
			oss << ',';
			GeoGebraAddTriangleCmd(oss, itR);
			oss << ',';
			GeoGebraAddTriangleCmd(oss, it);
			oss << '}' << ')';
			WarningString(oss.str().c_str());
			PrintSingleTriangle(itL, true, true, true, false, false);
			PrintSingleTriangle(itR, true, true, true, false, false);
			PrintSingleTriangle(it , true, true, true, false, false);
		}
		#endif
		return (int)it;
	}

	void CompositeGeoSolid::MergeCoplanarAdjacentTriangles()
	{
		const UInt32 csm = m_daSubMeshes.size();
		const GsIt ct = GetTriangleCount();
		bool change = false;
		int dt = 0;
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			if (gssm.setTriangleIndices.empty())
			{
				continue;
			}
			GeoSolidSet<GsIt> setIts(gssm.setTriangleIndices);
			int itAdd;
			int itRemove0;
			int itRemove1;
			bool hasMerged;
			while (true)
			{
				hasMerged = false;
				itAdd = -1;
				itRemove0 = -1;
				itRemove1 = -1;
				for (auto itListL = setIts.begin(); itListL != setIts.end(); )
				{
					const GsIt itL = *itListL;
					GeoSolidTriangle& gstL = GetTriangle(itL);
					if (!gstL.IsValid())
					{
						itListL = setIts.erase(itListL);
						continue;
					}
					auto itListR = itListL;
					for (++itListR; itListR != setIts.end(); )
					{
						const GsIt itR = *itListR;
						GeoSolidTriangle& gstR = GetTriangle(itR);
						if (!gstR.IsValid())
						{
							itListR = setIts.erase(itListR);
							continue;
						}
						itAdd = MergeTwoTriangles(itL, itR);
						if (itAdd < 0)
						{
							++itListR;
							continue;
						}
						++dt;
						itRemove0 = (int)itL;
						itRemove1 = (int)itR;
						gstL.markRemoval = true;
						gstR.markRemoval = true;
						hasMerged = true;
						change = true;
						break;
					}
					if (hasMerged)
					{
						break;
					}
					++itListL;
				}
				if (!hasMerged)
				{
					break;
				}
				setIts.erase((GsIt)itRemove0);
				setIts.erase((GsIt)itRemove1);
				setIts.insert((GsIt)itAdd);
			}
			gssm.setTriangleIndices.swap(setIts);
		}
		if (!change)
		{
			return;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningStringMsg("After MergeCoplanarAdjacentTriangles(): dt = %d", dt);
		}
		#endif
	}

	void CompositeGeoSolid::MergeIntoPolygons(std::list<GeoSolidPolygon>& listGsps)
	{
		do
		{
			bool hasMerged = false;
			for (auto itGspL = listGsps.begin(); itGspL != listGsps.end();)
			{
				GeoSolidPolygon& gspL = *itGspL;
				if (gspL.listGspvs.size() <= 2)
				{
					itGspL = listGsps.erase(itGspL);
					continue;
				}
				bool hasSingleMerged = false;
				for (auto itGspR = listGsps.begin(); itGspR != listGsps.end();)
				{
					if (itGspL == itGspR)
					{
						++itGspR;
						continue;
					}
					GeoSolidPolygon& gspR = *itGspR;
					if (gspR.listGspvs.size() <= 2)
					{
						itGspR = listGsps.erase(itGspR);
						continue;
					}
					#if COMPOSITE_GEO_SOLID_DEBUG
					GeoSolidPolygon gspLOld = gspL;
					#endif
					#if COMPOSITE_MERGE_INTO_CONVEX
					if (gspL.MergeIntoConvex(gspR))
					#else
					if (gspL.MergeWithoutHoles(gspR))
					#endif
					{
						#if COMPOSITE_GEO_SOLID_DEBUG
						if (m_bDebugDetail)
						{
							WarningString("-------------------------");
							std::ostringstream ossL;
							ossL << ' ' << ' ';
							gspLOld.Print(ossL);
							std::ostringstream ossR;
							ossR << ' ' << ' ';
							gspR.Print(ossR);
							std::ostringstream ossResult;
							ossResult << ' ' << ' ';
							gspL.Print(ossResult);
							WarningString("Merging");
							WarningString(ossL.str().c_str());
							WarningString("  and");
							WarningString(ossR.str().c_str());
							WarningString("  into convex polygon");
							WarningString(ossResult.str().c_str());
						}
						#endif
						#if COMPOSITE_MERGE_INTO_CONVEX
						gspL.RemoveLinearMiddleVertices();
						#endif
						gspL.area += gspR.area;
						listGsps.erase(itGspR);
						itGspR = listGsps.begin();
						hasMerged = hasSingleMerged = true;
					}
					else
					{
						++itGspR;
					}
				}
				if (hasSingleMerged)
				{
					itGspL = listGsps.begin();
				}
				else
				{
					++itGspL;
				}
			}
			if (!hasMerged)
			{
				break;
			}
		} while (false);
	}

	void CompositeGeoSolid::SortPolygonsByArea(std::list<GeoSolidPolygon>& listGsps)
	{
		listGsps.sort([](GeoSolidPolygon& a, GeoSolidPolygon& b)->bool {
			return a.area < b.area;
		});
	}

	void CompositeGeoSolid::ReconstructWithPolygon(const UInt32& ism)
	{
		GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
		switch (gssm.eGss)
		{
			case GeoSolidShape::CYLINDER:
			case GeoSolidShape::CONE:
				if (gssm.eGsf == GeoSolidFace::SIDE)
				{
					return;
				}
				break;
			case GeoSolidShape::SPHERE:
				return;
		}
		for (auto itIt = gssm.setTriangleIndices.begin(); itIt != gssm.setTriangleIndices.end(); )
		{
			const GsIt it = *itIt;
			const GeoSolidTriangle& gst = GetTriangle(it);
			if (!gst.IsValid() || gst.markRemoval)
			{
				itIt = gssm.setTriangleIndices.erase(itIt);
			}
			else
			{
				++itIt;
			}
		}
		if (gssm.setTriangleIndices.size() <= 2)
		{
			return;
		}
		std::list<GeoSolidPolygon> listGsps;
		std::list<GsIt> listIteratingIts;
		listIteratingIts.assign(gssm.setTriangleIndices.begin(), gssm.setTriangleIndices.end());
		//锁定合并顺序，精确调试
		listIteratingIts.sort(std::greater<GsIt>());
		const GsIt itTmplt = listIteratingIts.front();
		const GeoSolidTriangle& gstTmplt = GetTriangle(itTmplt);
		while (!listIteratingIts.empty())
		{
			UInt32 cit = listIteratingIts.size();
			const GsIt itFirst = listIteratingIts.front();
			listIteratingIts.pop_front();
			GeoSolidTriangle& gstFirst = GetTriangle(itFirst);
			const UInt32& ivnRender = gstFirst.ivn;
			//GeoSolidPolygon* pgsp = SANDBOX_NEW(GeoSolidPolygon, this, gstFirst.ivn);
			std::list<GeoSolidPolygonVertex> listGspvs;
			std::unordered_set<GsIe> setPolygonIes;
			listGspvs.push_back({ gstFirst.iv0, gstFirst.iuv0 });
			listGspvs.push_back({ gstFirst.iv1, gstFirst.iuv1 });
			listGspvs.push_back({ gstFirst.iv2, gstFirst.iuv2 });
			setPolygonIes.insert(gstFirst.ie0);
			setPolygonIes.insert(gstFirst.ie1);
			setPolygonIes.insert(gstFirst.ie2);
			bool hasMerged = false;
			while (true)
			{
				bool hasSingleMerge = false;
				for (auto itIt = listIteratingIts.begin(); itIt != listIteratingIts.end(); ++itIt)
				{
					const GsIt it = *itIt;
					GeoSolidTriangle& gst = GetTriangle(it);
					const bool common0 = setPolygonIes.find(gst.ie0) != setPolygonIes.end();
					const bool common1 = setPolygonIes.find(gst.ie1) != setPolygonIes.end();
					const bool common2 = setPolygonIes.find(gst.ie2) != setPolygonIes.end();
					int cc = 0;
					cc += common0;
					cc += common1;
					cc += common2;
					//找到一个合并后，跳出循环，从头开始遍历
					if (cc == 1)
					{
						//记录公共边，以ivc0 ivi ivc1为渲染顺序，拼到多边形中
						GsIv ivc0;
						//GsIv ivc1;
						//共边外的点
						GsIv ivi;
						UInt32 iuvi;
						GsIe ieAdd0;
						GsIe ieAdd1;
						GsIe ieCommon;
						if (common0)
						{
							ivc0 = gst.iv1;
							ivi = gst.iv2;
							iuvi = gst.iuv2;
							//ivc1 = gst.iv0;
							ieCommon = gst.ie0;
							ieAdd0 = gst.ie1;
							ieAdd1 = gst.ie2;
						}
						else if (common1)
						{
							ivc0 = gst.iv0;
							ivi = gst.iv1;
							iuvi = gst.iuv1;
							//ivc1 = gst.iv2;
							ieCommon = gst.ie1;
							ieAdd0 = gst.ie0;
							ieAdd1 = gst.ie2;
						}
						else if (common2)
						{
							ivc0 = gst.iv2;
							ivi = gst.iv0;
							iuvi = gst.iuv0;
							//ivc1 = gst.iv1;
							ieCommon = gst.ie2;
							ieAdd0 = gst.ie0;
							ieAdd1 = gst.ie1;
						}
						else
						{
							continue;
						}
						auto itGspvFound = std::find(listGspvs.begin(), listGspvs.end(), ivi);
						if (itGspvFound != listGspvs.end())
						{
							continue;
						}
						bool canInsert = false;
						for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
						{
							if (itGspv->iv != ivc0)
							{
								continue;
							}
							//TODO: 2023-11-09 10:28:13: 待优化：cc == 2 会合并成环形，需调整合成逻辑
							//原来为v0 v1 v2 v3的顺序，在v1、v2之间插入新点，来判断v1和v2的凹凸性。
							auto itGspv0 = itGspv == listGspvs.begin() ? std::prev(listGspvs.end()) : std::prev(itGspv);
							auto itGspv1 = itGspv;
							auto itGspv2 = std::next(itGspv);
							if (itGspv2 == listGspvs.end())
							{
								itGspv2 = listGspvs.begin();
							}
							const GsIv& iv0 = itGspv0->iv;
							const GsIv& iv1 = itGspv1->iv;
							const bool convex1 = IsConvex(iv0, iv1, ivi, ivnRender);
							if (!convex1)
							{
								break;
							}
							const GsIv& iv2 = itGspv2->iv;
							const bool convexI = IsConvex(iv1, ivi, iv2, ivnRender);
							if (!convexI)
							{
								//避免误差带来的异常
								break;
							}
							auto itGspv3 = std::next(itGspv2);
							if (itGspv3 == listGspvs.end())
							{
								itGspv3 = listGspvs.begin();
							}
							const GsIv& iv3 = itGspv3->iv;
							const bool convex2 = IsConvex(ivi, iv2, iv3, ivnRender);
							if (!convex2)
							{
								break;
							}
							canInsert = true;
							auto itGspvNext = std::next(itGspv);
							listGspvs.insert(itGspvNext, { ivi, iuvi });
							break;
						}//for
						if (!canInsert)
						{
							continue;
						}
						//循环不再遍历，直接移除
						setPolygonIes.erase(ieCommon);
						setPolygonIes.emplace(ieAdd0);
						setPolygonIes.emplace(ieAdd1);
					}
					else if (cc == 2)
					{
						//凹边合并，去除点和边，跨点连边
						//记录两条公共边
						GsIe iec0, iec1;
						GsIe ieAdd;
						GsIv ivToRemove;
						//GsIv ivJump0;
						//GsIv ivJump1;
						if (common0 && common1)
						{
							iec0 = gst.ie0;
							iec1 = gst.ie1;
							ieAdd = gst.ie2;
							//ivJump0 = gst.iv1;
							//ivJump1 = gst.iv2;
							ivToRemove = gst.iv0;
						}
						else if (common0 && common2)
						{
							iec0 = gst.ie0;
							iec1 = gst.ie2;
							ieAdd = gst.ie1;
							//ivJump0 = gst.iv0;
							//ivJump1 = gst.iv2;
							ivToRemove = gst.iv1;
						}
						else if (common1 && common2)
						{
							iec0 = gst.ie1;
							iec1 = gst.ie2;
							ieAdd = gst.ie0;
							//ivJump0 = gst.iv0;
							//ivJump1 = gst.iv1;
							ivToRemove = gst.iv2;
						}
						else
						{
							continue;
						}
						//TODO: 2023-11-09 10:28:13: 环形多边形，生成鸟洞的情况
						int cFindIv = 0;
						auto itGspvFound = listGspvs.begin();
						for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
						{
							if (itGspv->iv == ivToRemove)
							{
								++cFindIv;
								itGspvFound = itGspv;
							}
						}
						if (cFindIv != 1)
						{
							continue;
						}
						auto itGspvPrev = itGspvFound == listGspvs.begin() ? std::prev(listGspvs.end()) : std::prev(itGspvFound);
						auto itGspvNext = std::next(itGspvFound);
						if (itGspvNext == listGspvs.end())
						{
							itGspvNext = listGspvs.begin();
						}
						const bool convex = IsConvex(itGspvPrev->iv, itGspvFound->iv, itGspvNext->iv, ivnRender);
						if (convex)
						{
							//不是凹形，有重叠的三角形在内部共用两条边
							gst.markRemoval = true;
							itIt = listIteratingIts.erase(itIt);
							continue;
						}
						//循环不再遍历，直接移除
						listGspvs.erase(itGspvFound);
						setPolygonIes.erase(iec0);
						setPolygonIes.erase(iec1);
						setPolygonIes.insert(ieAdd);
					}
					else if (cc == 3)
					{
						//鸟洞
						int hit = 0;
						for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); )
						{
							if (hit >= 3)
							{
								break;
							}
							if (itGspv->iv == gst.iv0 || itGspv->iv == gst.iv1 || itGspv->iv == gst.iv2)
							{
								itGspv = listGspvs.erase(itGspv);
								++hit;
							}
							else
							{
								++itGspv;
							}
						}
						setPolygonIes.erase(gst.ie0);
						setPolygonIes.erase(gst.ie1);
						setPolygonIes.erase(gst.ie2);
					}
					else
					{
						continue;
					}
					hasSingleMerge = true;
					hasMerged |= hasSingleMerge;
					itIt = listIteratingIts.erase(itIt);
					gst.markRemoval = true;
					break;
				}//for
				if (!hasSingleMerge)
				{
					break;
				}
			}//while2
			if (listGspvs.size() <= 2)
			{
				continue;
			}
			GeoSolidPolygon gsp(this, gstFirst.ivn);
			gsp.listGspvs.swap(listGspvs);
			gstFirst.markRemoval = true;
			//#if COMPOSITE_GEO_SOLID_DEBUG
			//if (m_bDebugDetail)
			//{
			//	std::ostringstream oss;
			//	gsp.GeoGebraExecuteCmd(oss);
			//	WarningString("GeoGebra polygon before merging colinear vertices:");
			//	WarningString(oss.str().c_str());
			//}
			//#endif
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				std::ostringstream oss;
				oss << ' ' << ' ';
				gsp.Print(oss);
				//WarningString("GeoGebra polygon after merging colinear vertices:");
				WarningString("---------------------------");
				WarningStringMsg("gsp%u:", listGsps.size());
				WarningString(oss.str().c_str());
			}
			#endif
			#if COMPOSITE_MERGE_INTO_CONVEX
			//删除中间出现的共线点
			gsp.RemoveLinearMiddleVertices();
			#endif
			listGsps.emplace_back(gsp);
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail && !listGsps.empty())
		{
			std::ostringstream oss;
			GeoGebraExecutePolygons(oss, listGsps);
			WarningString("After merging triangles:");
			WarningString(oss.str().c_str());
		}
		#endif

		MergeIntoPolygons(listGsps);

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail && !listGsps.empty())
		{
			std::ostringstream oss;
			GeoGebraExecutePolygons(oss, listGsps);
			WarningString("After merging polygons:");
			WarningString(oss.str().c_str());
		}
		#endif

		#if COMPOSITE_GEO_SOLID_DEBUG
		int i = 0;
		#endif
		GeoSolidSet<GsIt> setNewIts;
		for (auto itGsp = listGsps.begin(); itGsp != listGsps.end(); ++itGsp)
		{
			GeoSolidPolygon& gsp = *itGsp;
			#if !COMPOSITE_MERGE_INTO_CONVEX
			gsp.RemoveLinearMiddleVertices();
			#endif
			gsp.EarClipping(gstTmplt, setNewIts);
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail && !setNewIts.empty())
			{
				std::ostringstream oss;
				oss << "Execute({";
				std::unordered_set<GsIv> setIvs;
				for (const GsIt& it : setNewIts)
				{
					const GeoSolidTriangle& gst = GetTriangle(it);
					setIvs.insert(gst.iv0);
					setIvs.insert(gst.iv1);
					setIvs.insert(gst.iv2);
				}
				WarningStringMsg("ct = %4u | cv = %4u", setNewIts.size(), setIvs.size());
				const GsIv& ivFirst = *setIvs.begin();
				GeoGebraAppendVertexCmd(oss, ivFirst);
				setIvs.erase(ivFirst);
				for (const GsIv& iv : setIvs)
				{
					oss << ',';
					GeoGebraAppendVertexCmd(oss, iv);
				}
				for (const GsIt& it : setNewIts)
				{
					oss << ',';
					GeoGebraAppendTriangleCmd(oss, it);
				}
				oss << "})";
				WarningStringMsg("After EarClipping gsp%u:", i++);
				WarningString(oss.str().c_str());
				//WarningString("  uvs:");
				//for (const GsIt& it : setNewIts)
				//{
				//	const GeoSolidTriangle& gst = GetTriangle(it);
				//  const GsVector2& uv0 = GetUv(gst.iuv0);
				//  const GsVector2& uv1 = GetUv(gst.iuv1);
				//  const GsVector2& uv2 = GetUv(gst.iuv2);
				//	WarningStringMsg("    T%u: uv%u = (%5.2f, %5.2f) | uv%u = (%5.2f, %5.2f) | uv%u = (%5.2f, %5.2f)", 
				//		it,
				//		gst.iv0, uv0.x, uv0.y,
				//		gst.iv1, uv1.x, uv1.y,
				//		gst.iv2, uv2.x, uv2.y
				//	);
				//}
			}
			#endif
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		UInt32 ctOld;
		UInt32 cvOld;
		if (m_bDebugDetail)
		{
			ctOld = gssm.setTriangleIndices.size();
			cvOld = gssm.setVertexIndices.size();
		}
		#endif
		gssm.setTriangleIndices.swap(setNewIts);
		gssm.setVertexIndices.clear();
		for (const GsIt& it : gssm.setTriangleIndices)
		{
			const GeoSolidTriangle& gst = GetTriangle(it);
			gssm.setVertexIndices.insert(gst.iv0);
			gssm.setVertexIndices.insert(gst.iv1);
			gssm.setVertexIndices.insert(gst.iv2);
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			const UInt32 ct = gssm.setTriangleIndices.size();
			const UInt32 cv = gssm.setVertexIndices.size();
			WarningStringMsg("Reconstruct gssm%u:", ism);
			WarningStringMsg("Before: ct = %4u | cv = %4u", ctOld, cvOld);
			WarningStringMsg("After : ct = %4u | cv = %4u", ct, cv);
		}
		#endif
	}

	void CompositeGeoSolid::ReconstructWithPolygon2(const UInt32& ism)
	{
		GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
		switch (gssm.eGss)
		{
			case GeoSolidShape::CYLINDER:
			case GeoSolidShape::CONE:
				//if (gssm.eGsf == GeoSolidFace::SIDE)
				{
					return;
				}
				//break;
			case GeoSolidShape::SPHERE:
				return;
		}
		for (auto itIt = gssm.setTriangleIndices.begin(); itIt != gssm.setTriangleIndices.end(); )
		{
			const GsIt it = *itIt;
			const GeoSolidTriangle& gst = GetTriangle(it);
			if (!gst.IsValid() || gst.markRemoval)
			{
				itIt = gssm.setTriangleIndices.erase(itIt);
			}
			else
			{
				++itIt;
			}
		}
		if (gssm.setTriangleIndices.size() <= 2)
		{
			return;
		}
		std::list<GeoSolidPolygon> listGsps;
		std::list<GsIt> listOrderedIts;
		listOrderedIts.assign(gssm.setTriangleIndices.begin(), gssm.setTriangleIndices.end());
		//锁定合并顺序，精确调试
		listOrderedIts.sort(std::greater<GsIt>());
		const GsIt itTmplt = listOrderedIts.front();
		const GeoSolidTriangle& gstTmplt = GetTriangle(itTmplt);
		const UInt32& ivnRender = gstTmplt.ivn;
		for (auto& it : gssm.setTriangleIndices)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			gst.markRemoval = true;
			GeoSolidPolygon gsp(this, ivnRender);
			gsp.AsTriangle(gst);
			listGsps.emplace_back(gsp);
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail && !listGsps.empty())
		{
			std::ostringstream oss;
			GeoGebraExecutePolygons(oss, listGsps);
			WarningString("After merging triangles:");
			WarningString(oss.str().c_str());
		}
		#endif

		SortPolygonsByArea(listGsps);
		MergeIntoPolygons(listGsps);

		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail && !listGsps.empty())
		{
			std::ostringstream oss;
			GeoGebraExecutePolygons(oss, listGsps);
			WarningString("After merging polygons:");
			WarningString(oss.str().c_str());
		}
		#endif

		#if COMPOSITE_GEO_SOLID_DEBUG
		int i = 0;
		#endif
		GeoSolidSet<GsIt> setNewIts;
		for (auto itGsp = listGsps.begin(); itGsp != listGsps.end(); ++itGsp)
		{
			GeoSolidPolygon& gsp = *itGsp;
			#if !COMPOSITE_MERGE_INTO_CONVEX
			#if COMPOSITE_GEO_SOLID_DEBUG
			const bool removeLinear = gsp.RemoveLinearMiddleVertices();
			if (removeLinear && m_bDebugDetail)
			{
				std::ostringstream oss;
				gsp.GeoGebraExecuteCmd(oss);
				WarningString(oss.str().c_str());
			}
			#else
			gsp.RemoveLinearMiddleVertices();
			#endif
			#endif
			gsp.EarClipping(gstTmplt, setNewIts);
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail && !setNewIts.empty())
			{
				std::ostringstream oss;
				oss << "Execute({";
				std::unordered_set<GsIv> setIvs;
				for (const GsIt& it : setNewIts)
				{
					const GeoSolidTriangle& gst = GetTriangle(it);
					setIvs.insert(gst.iv0);
					setIvs.insert(gst.iv1);
					setIvs.insert(gst.iv2);
				}
				WarningStringMsg("ct = %4u | cv = %4u", setNewIts.size(), setIvs.size());
				const GsIv& ivFirst = *setIvs.begin();
				GeoGebraAppendVertexCmd(oss, ivFirst);
				setIvs.erase(ivFirst);
				for (const GsIv& iv : setIvs)
				{
					oss << ',';
					GeoGebraAppendVertexCmd(oss, iv);
				}
				for (const GsIt& it : setNewIts)
				{
					oss << ',';
					GeoGebraAppendTriangleCmd(oss, it);
				}
				oss << "})";
				WarningStringMsg("After EarClipping gsp%u:", i++);
				WarningString(oss.str().c_str());
				//WarningString("  uvs:");
				//for (const GsIt& it : setNewIts)
				//{
				//	const GeoSolidTriangle& gst = GetTriangle(it);
				//	const GsVector2& uv0 = m_daUvs[gst.iuv0];
				//	const GsVector2& uv1 = m_daUvs[gst.iuv1];
				//	const GsVector2& uv2 = m_daUvs[gst.iuv2];
				//	WarningStringMsg("    T%u: uv%u = (%5.2f, %5.2f) | uv%u = (%5.2f, %5.2f) | uv%u = (%5.2f, %5.2f)", 
				//		it,
				//		gst.iv0, uv0.x, uv0.y,
				//		gst.iv1, uv1.x, uv1.y,
				//		gst.iv2, uv2.x, uv2.y
				//	);
				//}
			}
			#endif
		}

		#if COMPOSITE_GEO_SOLID_DEBUG
		UInt32 ctOld;
		UInt32 cvOld;
		if (m_bDebugDetail)
		{
			ctOld = gssm.setTriangleIndices.size();
			cvOld = gssm.setVertexIndices.size();
		}
		#endif
		gssm.setTriangleIndices.swap(setNewIts);
		gssm.setVertexIndices.clear();
		for (const GsIt& it : gssm.setTriangleIndices)
		{
			const GeoSolidTriangle& gst = GetTriangle(it);
			gssm.setVertexIndices.insert(gst.iv0);
			gssm.setVertexIndices.insert(gst.iv1);
			gssm.setVertexIndices.insert(gst.iv2);
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugDetail)
		{
			const UInt32 ct = gssm.setTriangleIndices.size();
			const UInt32 cv = gssm.setVertexIndices.size();
			WarningStringMsg("Reconstruct gssm%u:", ism);
			WarningStringMsg("Before: ct = %4u | cv = %4u", ctOld, cvOld);
			WarningStringMsg("After : ct = %4u | cv = %4u", ct, cv);
		}
		#endif
	}

	void CompositeGeoSolid::ReconstructWithPolygon()
	{
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningString("------------ReconstructWithPolygon------------");
		}
		int decrement = 0;
		#endif
		const UInt32 csm = m_daSubMeshes.size();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			#if COMPOSITE_GEO_SOLID_DEBUG
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			const UInt32 ctOld = gssm.setTriangleIndices.size();
			if (m_bDebugDetail)
			{
				WarningString("Before reconstruction:");
				PrintSubMesh(ism);
			}
			#endif
			#if COMPOSITE_MERGE_INTO_CONVEX
			ReconstructWithPolygon(ism);
			#else
			ReconstructWithPolygon2(ism);
			#endif
			#if COMPOSITE_GEO_SOLID_DEBUG
			if (m_bDebugDetail)
			{
				WarningString("After reconstruction:");
				PrintSubMesh(ism);
			}
			if (m_bDebugDetail)
			{
				WarningString("------------ReconstructWithPolygon------------");
			}
			const UInt32 ct = gssm.setTriangleIndices.size();
			decrement += ctOld - ct;
			#endif
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningStringMsg("After ReconstructWithPolygon(): dt = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::RemoveInvalidEdges()
	{
		const GsIe ce = GetEdgeCount();
		std::vector<int> vReplaceIndices;
		GeoSolidEdgeArray daFinalEdges;
		vReplaceIndices.resize((GsIndex)ce, -1);
		int decrement = 0;
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge& gse = GetEdge(ie);
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidEdge* pgse = &gse;
			if (gse.iv0 == gse.iv1)
			{
				++decrement;
				SetNullEdge(ie);
				GeoSolidEdge::recycle(pgse);
				continue;
			}
			daFinalEdges.emplace_back(pgse);
			SetNullEdge(ie);
			#else
			if (gse.iv0 == gse.iv1)
			{
				++decrement;
				continue;
			}
			daFinalEdges.emplace_back(gse);
			#endif
			const GsIndex ieNew = daFinalEdges.size() - 1;
			vReplaceIndices[(GsIndex)ie] = ieNew;
		}
		m_daEdges.swap(daFinalEdges);
		if (decrement <= 0)
		{
			return;
		}
		ReplaceEdgesIndices(vReplaceIndices);
		daFinalEdges.clear();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningStringMsg("After RemoveInvalidEdges(): de = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::RemoveInvalidTriangles()
	{
		const GsIt ct = GetTriangleCount();
		GeoSolidTriangleArray daFinalTriangles;
		std::vector<int> vReplaceIndices;
		vReplaceIndices.resize((GsIndex)ct, -1);
		int decrement = 0;
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidTriangle* pgst = &gst;
			if (gst.markRemoval || !gst.IsValid())
			{
				++decrement;
				SetNullTriangle(it);
				GeoSolidTriangle::recycle(pgst);
				continue;
			}
			daFinalTriangles.emplace_back(pgst);
			SetNullTriangle(it);
			#else
			if (gst.markRemoval || !gst.IsValid())
			{
				++decrement;
				continue;
			}
			daFinalTriangles.emplace_back(gst);
			#endif
			const UInt32 itNew = daFinalTriangles.size() - 1;
			vReplaceIndices[(GsIndex)it] = itNew;
		}
		m_daTriangles.swap(daFinalTriangles);
		if (decrement <= 0)
		{
			return;
		}
		ReplaceTrianglesIndices(vReplaceIndices);
		daFinalTriangles.clear();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			if (m_bDebugDetail)
			{
				WarningString("-------------------RemoveInvalidTriangles-------------------");
				int iStart = -1;
				const int c = vReplaceIndices.size();
				for (int i = 0; i < c; ++i)
				{
					if (i != vReplaceIndices[i])
					{
						iStart = i;
						break;
					}
				}
				if (iStart >= 0)
				{
					std::ostringstream oss;
					const int itStart = vReplaceIndices[iStart];
					oss << 'T' << iStart << "->";
					if (itStart >= 0)
					{
						oss << 'T' << itStart;
					}
					else
					{
						oss << 'X';
					}
					for (int i = iStart + 1; i < c; ++i)
					{
						const int it = vReplaceIndices[i];
						oss << " | T" << i << "->";
						if (it >= 0)
						{
							oss << 'T' << it;
						}
						else
						{
							oss << 'X';
						}
					}
					WarningString(oss.str().c_str());
				}
			}
			WarningStringMsg("After RemoveInvalidTriangles(): dt = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::RemoveTriangles(const std::vector<bool>& vItsToRemove)
	{
		const GsIt ct = GetTriangleCount();
		GeoSolidTriangleArray daFinalTriangles;
		std::vector<int> vReplaceIndices;
		vReplaceIndices.resize((GsIndex)ct, -1);
		int decrement = 0;
		for (GsIt it = 0; it < ct; ++it)
		{
			GeoSolidTriangle& gst = GetTriangle(it);
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidTriangle* pgst = &gst;
			if (vItsToRemove[(GsIndex)it])
			{
				++decrement;
				SetNullTriangle(it);
				GeoSolidTriangle::recycle(pgst);
				continue;
			}
			daFinalTriangles.emplace_back(pgst);
			SetNullTriangle(it);
			#else
			if (vItsToRemove[(GsIndex)it])
			{
				++decrement;
				continue;
			}
			daFinalTriangles.emplace_back(gst);
			#endif
			const UInt32 itNew = daFinalTriangles.size() - 1;
			vReplaceIndices[(GsIndex)it] = itNew;
		}
		m_daTriangles.swap(daFinalTriangles);
		if (decrement <= 0)
		{
			return;
		}
		ReplaceTrianglesIndices(vReplaceIndices);
		daFinalTriangles.clear();
	}

	void CompositeGeoSolid::KeepTrianglesToRender(const InteractMethod& eIm)
	{
		bool clockwise;
		bool anticlockwise;
		const GsIt ct = GetTriangleCount();
		int decrement = 0;
		for (GsIt it = 0; it < ct; ++it)
		{
			clockwise = false;
			anticlockwise = false;
			GeoSolidTriangle& gst = GetTriangle(it);
			if (!gst.markRemoval && !IsZero(gst.area, 3e-4))
			{
				switch (eIm)
				{
					case InteractMethod::HOLLOW:
						HollowTriangle(gst, clockwise, anticlockwise);
						break;
					case InteractMethod::UNION:
						UnionTriangle(gst, clockwise, anticlockwise);
						break;
					case InteractMethod::INTERSECT:
						IntersectTriangle(gst, clockwise, anticlockwise);
						break;
				}
			}
			if (!clockwise && !anticlockwise)
			{
				gst.markRemoval = true;
				++decrement;
				continue;
			}
			gst.ClearIesDivider();
			gst.ClearNewIvs();
			if (anticlockwise)
			{
				GsIv tempIv;
				tempIv = gst.iv1;
				gst.iv1 = gst.iv2;
				gst.iv2 = tempIv;

				UInt32 temp;
				temp = gst.iuv1;
				gst.iuv1 = gst.iuv2;
				gst.iuv2 = temp;

				GsIe tempIe;
				tempIe = gst.ie0;
				gst.ie0 = gst.ie1;
				gst.ie1 = tempIe;
				GsVector3 vn = -m_daNormals[gst.ivn];
				gst.ivn = AddNormal(vn);
				gst.turnover = !gst.turnover;
			}
			gst.intersectSrc.hasCheckedInside = false;
			gst.intersectSrc.hasCheckedCoincide = false;
			gst.intersectCut.hasCheckedInside = false;
			gst.intersectCut.hasCheckedCoincide = false;
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningStringMsg("After KeepTrianglesToRender(): dt = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::HollowTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise)
	{
		if (gst.intersectSrc.from)
		{
			if (gst.intersectSrc.inside
				&& !gst.intersectCut.inside)
			{
				clockwise = true;
			}
		}
		else if (gst.intersectCut.from)
		{
			if (gst.intersectSrc.inside && !gst.intersectSrc.coincide)
			{
				anticlockwise = true;
			}
		}
	}

	void CompositeGeoSolid::UnionTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise)
	{
		//if (gst.intersectSrc.from && gst.intersectCut.from)
		//{
		//	clockwise = gst.intersectSrc.inside || gst.intersectCut.inside;
		//}
		//else 
		if (gst.intersectSrc.from)
		{
			if (gst.intersectSrc.inside
				&& !gst.intersectCut.inside)
			{
				clockwise = true;
			}
			else if (gst.intersectSrc.inside
				&& gst.intersectCut.inside && gst.intersectCut.coincide)
			{
				clockwise = true;
			}
		}
		else if (gst.intersectCut.from)
		{
			if (gst.intersectCut.inside && 
				!gst.intersectSrc.inside)
			{
				clockwise = true;
			}
			//TODO：重叠情况，默认上方的显示Src里的
			//else if (gst.intersectCut.inside
			//	&& gst.intersectSrc.inside && gst.intersectSrc.coincide && !gst.intersectCut.coincide)
			//{
			//	clockwise = true;
			//}
		}
	}

	void CompositeGeoSolid::IntersectTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise)
	{
		if (gst.intersectCut.inside &&
			gst.intersectSrc.inside)
		{
			clockwise = true;
		}
	}

	void CompositeGeoSolid::FinalizeEdges()
	{
		const GsIt ct = GetTriangleCount();
		const GsIe ce = GetEdgeCount();
		dynamic_array<bool> daUsedIndices;
		daUsedIndices.resize_initialized((GsIndex)ce, false);
		for (GsIt it = 0; it < ct; ++it)
		{
			const GeoSolidTriangle& gst = GetTriangle(it);
			daUsedIndices[(GsIndex)gst.ie0] = true;
			daUsedIndices[(GsIndex)gst.ie1] = true;
			daUsedIndices[(GsIndex)gst.ie2] = true;
		}
		std::vector<int> vReplaceIndices;
		GeoSolidEdgeArray daFinalEdges;
		vReplaceIndices.resize((GsIndex)ce, -1);
		int decrement = 0;
		for (GsIe ie = 0; ie < ce; ++ie)
		{
			GeoSolidEdge& gse = GetEdge(ie);
			gse.isDrawn = false;
			gse.intersectSrc.hasCheckedInside = false;
			gse.intersectSrc.hasCheckedCoincide = false;
			gse.intersectCut.hasCheckedInside = false;
			gse.intersectCut.hasCheckedCoincide = false;
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidEdge* pgse = &gse;
			if (!daUsedIndices[(GsIndex)ie])
			{
				//上方已经筛选出所有的顶点，没有使用到的顶点可跳过记录到map的阶段
				++decrement;
				SetNullEdge(ie);
				GeoSolidEdge::recycle(pgse);
				continue;
			}
			daFinalEdges.emplace_back(pgse);
			SetNullEdge(ie);
			#else
			if (!daUsedIndices[(GsIndex)ie])
			{
				++decrement;
				continue;
			}
			daFinalEdges.emplace_back(gse);
			#endif
			const GsIe ieNew = (GsIe)daFinalEdges.size() - 1;
			vReplaceIndices[(GsIndex)ie] = (int)ieNew;
		}
		m_daEdges.swap(daFinalEdges);
		if (vReplaceIndices.empty())
		{
			return;
		}
		ReplaceEdgesIndices(vReplaceIndices);
		//WORKING... CLEAR ERROR
		daFinalEdges.clear();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningStringMsg("After FinalizeEdges(): de = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::FinalizeVertices()
	{
		const GsIt ct = GetTriangleCount();
		const GsIndex cv = m_daGeoSolidVertices.size();
		const GsIv civ = GetVertexCount();
		dynamic_array<bool> daUsedIndices;
		daUsedIndices.resize_initialized(cv, false);
		for (GsIt it = 0; it < ct; ++it)
		{
			const GeoSolidTriangle& gst = GetTriangle(it);
			daUsedIndices[(GsIndex)gst.iv0] = true;
			daUsedIndices[(GsIndex)gst.iv1] = true;
			daUsedIndices[(GsIndex)gst.iv2] = true;
		}
		std::vector<int> vReplaceIndices;
		GeoSolidVertexArray daFinalVertices;
		vReplaceIndices.resize(cv, -1);
		int decrement = 0;
		for (GsIv iv = 0; iv < civ; ++iv)
		{
			GeoSolidVertex& gsv = GetVertex(iv);
			gsv.intersectSrc.hasCheckedInside = false;
			gsv.intersectSrc.hasCheckedCoincide = false;
			gsv.intersectCut.hasCheckedInside = false;
			gsv.intersectCut.hasCheckedCoincide = false;
			#if COMPOSITE_ELEMENT_USE_POINTER
			GeoSolidVertex* pgsv = &gsv;
			if (!daUsedIndices[(GsIndex)iv])
			{
				//上方已经筛选出所有的顶点，没有使用到的顶点可跳过记录到map的阶段
				++decrement;
				SetNullVertex(iv);
				GeoSolidVertex::recycle(pgsv);
				continue;
			}
			daFinalVertices.emplace_back(pgsv);
			SetNullVertex(iv);
			#else
			GeoSolidVertex* pgsv = &gsv;
			if (!daUsedIndices[(GsIndex)iv])
			{
				++decrement;
				continue;
			}
			daFinalVertices.emplace_back(gsv);
			#endif
			const GsIndex ivNew = daFinalVertices.size() - 1;
			vReplaceIndices[(GsIndex)iv] = ivNew;
		}
		m_daGeoSolidVertices.swap(daFinalVertices);
		if (decrement <= 0)
		{
			return;
		}
		ReplaceVerticesIndices(vReplaceIndices);
		daFinalVertices.clear();
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (m_bDebugSummary)
		{
			WarningString("-------------------FinalizeVertices-------------------");
			int iStart = -1;
			const int c = vReplaceIndices.size();
			for (int i = 0; i < c; ++i)
			{
				if (i != vReplaceIndices[i])
				{
					iStart = i;
					break;
				}
			}
			if (iStart >= 0)
			{
				std::ostringstream oss;
				const int ivStart = vReplaceIndices[iStart];
				oss << 'V' << iStart << "->";
				if (ivStart >= 0)
				{
					oss << 'V' << ivStart;
				}
				else
				{
					oss << 'X';
				}
				for (int i = iStart + 1; i < c; ++i)
				{
					const int iv = vReplaceIndices[i];
					oss << " | V" << i << "->";
					if (iv >= 0)
					{
						oss << 'V' << iv;
					}
					else
					{
						oss << 'X';
					}
				}
				WarningString(oss.str().c_str());
			}
			WarningStringMsg("After FinalizeVertices(): dv = %d", decrement);
		}
		#endif
	}

	void CompositeGeoSolid::FinalizeSubMeshes()
	{
		std::vector<UInt32> vIsmToRemove;
		const UInt32 csm = m_daSubMeshes.size();
		for (UInt32 ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
			//GeoSolidSet<GsIv>& setIvs = gssm.GetIvSet();
			//GeoSolidSet<GsIt>& setIts = gssm.GetItSet();
			//gssm.cpv = setIvs.size();
			//if (setIts.empty() || setIvs.empty())
			gssm.cpv = gssm.setVertexIndices.size();
			if (gssm.setTriangleIndices.empty() || gssm.setVertexIndices.empty())
			{
				vIsmToRemove.emplace_back(ism);
			}
		}
		for (auto ritIsm = vIsmToRemove.rbegin(); ritIsm != vIsmToRemove.rend(); ++ritIsm)
		{
			RemoveSubMesh(*ritIsm);
		}
	}

	void CompositeGeoSolid::CreateSubMeshes()
	{
		const GsIt ct = GetTriangleCount();
		for (GsIt it = 0; it < ct; ++it)
		{
			const GeoSolidTriangle& gst = GetTriangle(it);
			if (gst.markRemoval)
			{
				continue;
			}
			GeoSolidSubMesh* gssmFound = nullptr;
			const GsVector3& vn = m_daNormals[gst.ivn];
			const UInt32 csm = m_daSubMeshes.size();
			for (int ism = csm - 1; ism >= 0; --ism)
			{
				GeoSolidSubMesh& gssm = *m_daSubMeshes[ism];
				//auto itIt = gssmIt.setTriangleIndices.begin();
				//const GsIt itFirst = *itIt;
				//GeoSolidTriangle& gstFirst = GetTriangle(itFirst);
				switch (gst.gss)
				{
					case GeoSolidShape::CUBOID:
					case GeoSolidShape::WEDGE:
					case GeoSolidShape::PYRAMID:
					{
						if (gssm.ivn == gst.ivn && 
							gssm.eGss == gst.gss && gssm.eGsf == gst.gsf && 
							gssm.ibb == gst.ibb && gssm.im == gst.im && gssm.ic == gst.ic)
						{
							gssmFound = &gssm;
						}
						break;
					}
					case GeoSolidShape::CYLINDER:
					case GeoSolidShape::CONE:
					case GeoSolidShape::SPHERE:
					{
						if (gssm.eGss == gst.gss && gssm.eGsf == gst.gsf &&
							gssm.ibb == gst.ibb && gssm.im == gst.im && gssm.ic == gst.ic)
						{
							gssmFound = &gssm;
						}
						break;
					}
				}
				if (gssmFound)
				{
					break;
				}
			}
			if (!gssmFound)
			{
				gssmFound = GeoSolidSubMesh::obtain();
				GeoSolidSubMesh& gssmNew = *gssmFound;
				//GeoSolidSubMesh gssmNew;
				gssmNew.eGss = gst.gss;
				gssmNew.eGsf = gst.gsf;
				gssmNew.ivn = gst.ivn;
				gssmNew.ivc = gst.ivc;
				gssmNew.ibb = gst.ibb;
				gssmNew.im = gst.im;
				gssmNew.ic = gst.ic;
				//m_daSubMeshes.emplace_back(std::move(gssmNew));
				m_daSubMeshes.emplace_back(gssmFound);
				//gssmFound = &GetSubMesh(GetSubMeshCount() - 1);
			}
			//{
				//GeoSolidSet<GsIv>& setIvs = gssmFound->GetIvSet();
				//GeoSolidSet<GsIt>& setIts = gssmFound->GetItSet();
				//setIts.emplace(it);
				//setIvs.emplace(gst.iv0);
				//setIvs.emplace(gst.iv1);
				//setIvs.emplace(gst.iv2);
			//}
			gssmFound->setTriangleIndices.emplace(it);
			gssmFound->setVertexIndices.emplace(gst.iv0);
			gssmFound->setVertexIndices.emplace(gst.iv1);
			gssmFound->setVertexIndices.emplace(gst.iv2);
		}
	}

	void CompositeGeoSolid::ClearSubMeshes()
	{
		const GsIndex csm = m_daSubMeshes.size();
		for (GsIndex ism = 0; ism < csm; ++ism)
		{
			GeoSolidSubMesh* gssm = m_daSubMeshes[ism];
			GeoSolidSubMesh::recycle(gssm);
			gssm = m_daSubMeshes[ism] = nullptr;
		}
		m_daSubMeshes.clear();
	}

	GsBoxBound CompositeGeoSolid::GetBoxBound(const int ibb)
	{
		return ibb >= 0 && ibb < m_daBoxBounds.size() ? m_daBoxBounds[ibb] : GsBoxBound(GsVector3::zero, GsVector3::one);
	}

	GsMatrix3x3 CompositeGeoSolid::GetMatrix(const int im)
	{
		return im >= 0 && im < m_daMatrices.size() ? m_daMatrices[im] : GsMatrix3x3::identity;
	}

	GsVector3 CompositeGeoSolid::GetNormal(GeoSolidSubMesh& gssm)
	{
		return gssm.ivn >= 0 && gssm.ivn < m_daNormals.size() ? m_daNormals[gssm.ivn] : GsVector3::yAxis;
	}

	bool CompositeGeoSolid::IsVertexInEdge(const GsIv& iv, const GsIe& ie, const GsDigit& ep)
	{
		//WriteOnceBoolean& wob = GetWriteOnceBoolean(m_ddaIvsInIes, iv, ie, GetVertexCount(), GetEdgeCount());
		//if (wob.c)
		//{
		//	return wob.b;
		//}
		const GsVector3& v = GetVertex(iv).v;
		const GeoSolidEdge& gse = GetEdge(ie);
		const GsVector3& va = GetVertex(gse.iv0).v;
		const GsVector3& vb = GetVertex(gse.iv1).v;
		return GeometryMath::IsVertexInSegment(v, va, vb, ep);
		//wob.c = true;
		//wob.b = GeometryMath::IsVertexInSegment(v, va, vb, ep);
		//return wob.b;
	}

	bool CompositeGeoSolid::IsParalleled(const GsIv& iva, const GsIv& ivb, const GsIv& ivc, const GsDigit& ep)
	{
		const GsVector3& va = GetVertex(iva).v;
		const GsVector3& vb = GetVertex(ivb).v;
		const GsVector3& vc = GetVertex(ivc).v;
		return GeometryMath::IsParalleled(va, vb, vc, ep);
	}

	bool CompositeGeoSolid::IsVertexInsideTriangle(const GsIv& iv, const GsIt& it, GsIe* pieInEdge /*= nullptr*/)
	{
		WriteOnceBoolean& wob = GetWriteOnceBoolean(m_ddaIvsInsideIts, iv, it, GetVertexCount(), GetTriangleCount());
		if (wob.c)
		{
			return wob.b;
		}
		const GeoSolidTriangle& gst = GetTriangle(it);
		wob.c = true;
		if (iv == gst.iv0 ||
			iv == gst.iv1 ||
			iv == gst.iv2)
		{
			wob.b = false;
			return false;
		}
		if (IsVertexInEdge(iv, gst.ie0))
		{
			wob.b = false;
			if (pieInEdge)
			{
				*pieInEdge = gst.ie0;
			}
			return false;
		}
		if (IsVertexInEdge(iv, gst.ie1))
		{
			wob.b = false;
			if (pieInEdge)
			{
				*pieInEdge = gst.ie1;
			}
			return false;
		}
		if (IsVertexInEdge(iv, gst.ie2))
		{
			wob.b = false;
			if (pieInEdge)
			{
				*pieInEdge = gst.ie2;
			}
			return false;
		}
		const GsVector3& vp = GetVertex(iv).v;
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		wob.b = CheckVertexInsideTriangle(vp, va, vb, vc);
		return wob.b;
	}

	bool CompositeGeoSolid::IsVertexInsideTriangle(const GsVector3& vp, const GsIt& it, GsIe* pieInEdge)
	{
		const GeoSolidTriangle& gst = GetTriangle(it);
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		if (GeometryMath::IsVertexInSegment(vp, va, vb))
		{
			if (pieInEdge)
			{
				*pieInEdge = gst.ie0;
			}
			return false;
		}
		if (GeometryMath::IsVertexInSegment(vp, va, vc))
		{
			if (pieInEdge)
			{
				*pieInEdge = gst.ie1;
			}
			return false;
		}
		if (GeometryMath::IsVertexInSegment(vp, vb, vc))
		{
			if (pieInEdge)
			{
				*pieInEdge = gst.ie2;
			}
			return false;
		}
		return CheckVertexInsideTriangle(vp, va, vb, vc);
	}

	bool CompositeGeoSolid::IsVertexOnTriangle(const GsIv& iv, const GsIt& it)
	{
		const GsIv cv = GetVertexCount();
		const GsIt ct = GetTriangleCount();
		WriteOnceBoolean& wob = GetWriteOnceBoolean(m_ddaIvsOnIts, iv, it, cv, ct);
		if (wob.c)
		{
			return wob.b;
		}
		wob.c = true;
		const GeoSolidTriangle& gst = GetTriangle(it);
		if (iv == gst.iv0 || iv == gst.iv1 || iv == gst.iv2)
		{
			wob.b = true;
			return true;
		}
		if (IsVertexInEdge(iv, gst.ie0))
		{
			wob.b = true;
			return true;
		}
		if (IsVertexInEdge(iv, gst.ie1))
		{
			wob.b = true;
			return true;
		}
		if (IsVertexInEdge(iv, gst.ie2))
		{
			wob.b = true;
			return true;
		}
		const GsVector3& vp = GetVertex(iv).v;
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		wob.b = CheckVertexInsideTriangle(vp, va, vb, vc);
		return wob.b;
	}

	bool CompositeGeoSolid::IsTriangleClockwise(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2, const UInt32& ivn)
	{
		const GsVector3& va = GetVertex(iv0).v;
		const GsVector3& vb = GetVertex(iv1).v;
		const GsVector3& vc = GetVertex(iv2).v;
		const GsVector3& vn = m_daNormals[ivn];
		return GeometryMath::IsTriangleClockwise(va, vb, vc, vn);
	}

	bool CompositeGeoSolid::IsConvex(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2, const UInt32& ivn)
	{
		const GsVector3& va = GetVertex(iv0).v;
		const GsVector3& vb = GetVertex(iv1).v;
		const GsVector3& vc = GetVertex(iv2).v;
		const GsVector3& vn = m_daNormals[ivn];
		return GeometryMath::IsConvex(va, vb, vc, vn);
	}

	GsDigit CompositeGeoSolid::GetRadian(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2)
	{
		const GsVector3& va = GetVertex(iv0).v;
		const GsVector3& vb = GetVertex(iv1).v;
		const GsVector3& vc = GetVertex(iv2).v;
		return GeometryMath::GetRadian(va, vb, vc);
	}

	GsDigit CompositeGeoSolid::GetDistance(const GsIv& iv0, const GsIv& iv1)
	{
		const GsVector3& va = GetVertex(iv0).v;
		const GsVector3& vb = GetVertex(iv1).v;
		return Distance(va, vb);
	}

	GsDigit CompositeGeoSolid::GetVertexDistanceFromEdge(const GsIv& iv, const GsIe& ie)
	{
		const GsVector3& v = GetVertex(iv).v;
		const GsVector3 vp = CastVertexOnEdge(iv, ie);
		return Distance(vp, v);
	}

	void CompositeGeoSolid::CorrectClockwise(GeoSolidTriangle& gst)
	{
		const GsVector3& va = GetVertex(gst.iv0).v;
		const GsVector3& vb = GetVertex(gst.iv1).v;
		const GsVector3& vc = GetVertex(gst.iv2).v;
		const GsVector3 vab = vb - va;
		const GsVector3 vac = vc - va;
		const GsVector3 vcp = CrossProduct(vab, vac);
		const GsVector3& vn = m_daNormals[gst.ivn];
		const GsDigit dp = DotProduct(vcp, vn);
		if (dp == 0)
		{
			SANDBOX_ASSERT(false);
		}
		else if (dp < 0)
		{
			{
				GsIv ivTemp = gst.iv0;
				gst.iv0 = gst.iv2;
				gst.iv2 = ivTemp;
			}
			{
				GsIe ieTemp = gst.ie0;
				gst.ie0 = gst.ie2;
				gst.ie2 = ieTemp;
			}
			{
				UInt32 iuvTemp = gst.iuv0;
				gst.iuv0 = gst.iuv2;
				gst.iuv2 = iuvTemp;
			}
		}
	}

	void CompositeGeoSolid::CorrectClockwise(GeoSolidTriangle* gst)
	{
		if (!gst)
		{
			return;
		}
		CorrectClockwise(*gst);
	}

	//=====================================================================================================//

	#if GEO_SOLID_RECYCLE
	IMPLEMENT_THREAD_FLYWEIGHT_PATTERN(GeoSolidSubMesh)
	#else
	IMPLEMENT_NON_FLYWEIGHT_PATTERN(GeoSolidSubMesh)
	#endif
	int GeoSolidSubMesh::s_MaxPoolSize = 999999;

	//GeoSolidSubMesh::GeoSolidSubMesh(bool _)
	//	: cpv(0)
	//	, cpi(0)
	//	, ibb(-1)
	//	, ivc(-1)
	//	, im(-1)
	//	, ivn(-1)
	//	, ic(-1)
	//	, eGss(GeoSolidShape::NONE)
	//	, eGsf(GeoSolidFace::UNKNOWN)
	//	//, setVertexIndices(nullptr)
	//	//, setTriangleIndices(nullptr)
	//{

	//}

	GeoSolidSubMesh::GeoSolidSubMesh()
		: cpv(0)
		, cpi(0)
		, ibb(-1)
		, ivc(-1)
		, im(-1)
		, ivn(-1)
		, ic(-1)
		, eGss(GeoSolidShape::NONE)
		, eGsf(GeoSolidFace::UNKNOWN)
	{
		//setVertexIndices = SANDBOX_NEW(GeoSolidSet<GsIv>);
		//setTriangleIndices = SANDBOX_NEW(GeoSolidSet<GsIt>);
	}

	void GeoSolidSubMesh::Set(const FBSave::GeoSolidSubMesh& fbsGssm)
	{
		cpv = fbsGssm.cpv();
		cpi = fbsGssm.cpi();
		ibb = fbsGssm.ibb();
		ivc = fbsGssm.ivc();
		im = fbsGssm.im();
		ivn = -1;
		ic = -1;
		eGss = GeoSolidShape(fbsGssm.gss());
		eGsf = GeoSolidFace(fbsGssm.gsf());
		if (fbsGssm.ic())
		{
			ic = fbsGssm.ic();
		}

		auto& fbsvIvs = *fbsGssm.setVertexIndices();
		GeoSolidSet<GsIv>& setIvs = setVertexIndices;
		setIvs.clear();
		setIvs.reserve(fbsvIvs.size());
		for (auto iter = fbsvIvs.begin(); iter != fbsvIvs.end(); ++iter)
		{
			setIvs.emplace(GsIv(*iter));
		}

		auto& fbsvIts = *fbsGssm.setTriangleIndices();
		GeoSolidSet<GsIt>& setIts = setTriangleIndices;
		setIts.clear();
		setIts.reserve(fbsvIts.size());
		for (auto iter = fbsvIts.begin(); iter != fbsvIts.end(); ++iter)
		{
			setIts.emplace(GsIt(*iter));
		}
	}

	GeoSolidSubMesh::~GeoSolidSubMesh()
	{
		//SANDBOX_DELETE(setVertexIndices);
		//SANDBOX_DELETE(setTriangleIndices);
	}

	flatbuffers::Offset<FBSave::GeoSolidSubMesh> GeoSolidSubMesh::ToFbs(flatbuffers::FlatBufferBuilder& builder) const
	{
		std::vector<uint32_t> vFbsIvs;
		const GeoSolidSet<GsIv>& setIvs = setVertexIndices;
		for (const GsIv& iv : setIvs)
		{
			vFbsIvs.emplace_back((GsIndex)iv);
		}
		std::vector<uint32_t> vFbsIts;
		const GeoSolidSet<GsIt>& setIts = setTriangleIndices;
		for (const GsIt& it : setIts)
		{
			vFbsIts.emplace_back((GsIndex)it);
		}
		return FBSave::CreateGeoSolidSubMesh(builder,
			builder.CreateVector(vFbsIvs), 
			builder.CreateVector(vFbsIts),
			cpv, cpi,
			ibb, ivc, im,
			UInt8(eGss), UInt8(eGsf)
		);
	}

	void GeoSolidSubMesh::FromFbs(const FBSave::GeoSolidSubMesh* fbsGssm)
	{
		if (!fbsGssm)
		{
			return;
		}
		cpv = fbsGssm->cpv();
		cpi = fbsGssm->cpi();
		ibb = fbsGssm->ibb();
		ivc = fbsGssm->ivc();
		im = fbsGssm->im();
		//TODO
		if (fbsGssm->ic())
		{
			ic = fbsGssm->ic();
		}
		eGss = GeoSolidShape(fbsGssm->gss());
		eGsf = GeoSolidFace(fbsGssm->gsf());


		{
			auto& fbsvIvs = *fbsGssm->setVertexIndices();
			//if (!setVertexIndices)
			//{
			//	setVertexIndices = SANDBOX_NEW(GeoSolidSet<GsIv>);
			//}
			GeoSolidSet<GsIv>& setIvs = setVertexIndices;
			setIvs.clear();
			const int cv = fbsvIvs.size();
			setIvs.reserve(cv);
			for (auto iter = fbsvIvs.begin(); iter != fbsvIvs.end(); ++iter)
			{
				setIvs.emplace(GsIv(*iter));
			}
		}

		{
			auto& fbsvIts = *fbsGssm->setTriangleIndices();
			//if (!setTriangleIndices)
			//{
			//	setTriangleIndices = SANDBOX_NEW(GeoSolidSet<GsIt>);
			//}
			GeoSolidSet<GsIt>& setIts = setTriangleIndices;
			setIts.clear();
			setIts.reserve(fbsvIts.size());
			for (auto iter = fbsvIts.begin(); iter != fbsvIts.end(); ++iter)
			{
				setIts.emplace(GsIt(*iter));
			}
		}
	}

	void GeoSolidSubMesh::onRecycle()
	{
		cpv = 0;
		cpi = 0;
		ibb = -1;
		ivc = -1;
		im = -1;
		ivn = -1;
		ic = -1;
		eGss = GeoSolidShape::NONE;
		eGsf = GeoSolidFace::UNKNOWN;
		setVertexIndices.clear();
		setTriangleIndices.clear();
	}

	//=====================================================================================================//

	GeoSolidPolygonVertex::GeoSolidPolygonVertex(const GsIv& iv, const UInt32& iuv)
		: iv(iv)
		, iuv(iuv)
		, earDirty(true)
	{

	}

	GeoSolidPolygonVertex::GeoSolidPolygonVertex(const GeoSolidPolygonVertex& gspv)
	{
		Copy(gspv);
	}

	void GeoSolidPolygonVertex::Copy(const GeoSolidPolygonVertex& gspv)
	{
		iv = gspv.iv;
		iuv = gspv.iuv;
		someValue = gspv.someValue;
		setNeighborIvs = gspv.setNeighborIvs;
		ear = gspv.ear;
		earDirty = gspv.earDirty;
	}

	GeoSolidPolygonVertex& GeoSolidPolygonVertex::operator=(const GeoSolidPolygonVertex& gspv)
	{
		Copy(gspv);
		return *this;
	}

	bool GeoSolidPolygonVertex::operator==(const GsIv& iv) const
	{
		return this->iv == iv;
	}

	bool GeoSolidPolygonVertex::operator==(const GeoSolidPolygonVertex& gspv) const
	{
		return iv == gspv.iv;
	}

	//=====================================================================================================//

	GeoSolidPolygon::GeoSolidPolygon(CompositeGeoSolid* cgs, const UInt32& ivn)
		: cgs(cgs)
		, ivn(ivn)
		, area(0.f)
	{

	}

	//GeoSolidPolygon::GeoSolidPolygon(const GeoSolidPolygon* gsp)
	//{
	//	Copy(*gsp);
	//}

	int GeoSolidPolygon::AddTriangle(const GeoSolidTriangle& gstTplt)
	{
		if (listGspvs.size() != 3)
		{
			return -1;
		}
		const GeoSolidPolygonVertex gspv0 = listGspvs.front();
		listGspvs.pop_front();
		const GeoSolidPolygonVertex gspv1 = listGspvs.front();
		listGspvs.pop_front();
		const GeoSolidPolygonVertex gspv2 = listGspvs.front();

		GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(gspv0.iv, gspv1.iv, gspv2.iv, gstTplt);
		gst->iuv0 = gspv0.iuv;
		gst->iuv1 = gspv1.iuv;
		gst->iuv2 = gspv2.iuv;
		gst->markRemoval = false;
		const GsIndex it = (GsIndex)cgs->AddTriangle(gst);
		return it;
	}

	void GeoSolidPolygon::AsTriangle(const GeoSolidTriangle& gst)
	{
		listGspvs.emplace_back(GeoSolidPolygonVertex{ gst.iv0, gst.iuv0 });
		listGspvs.emplace_back(GeoSolidPolygonVertex{ gst.iv1, gst.iuv1 });
		listGspvs.emplace_back(GeoSolidPolygonVertex{ gst.iv2, gst.iuv2 });
		area = gst.area;
	}

	bool GeoSolidPolygon::RemoveLinearMiddleVertices()
	{
		bool remove = false;
		auto itGspvLast = std::prev(listGspvs.end());
		for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end();)
		{
			auto itGspvPrev = itGspv == listGspvs.begin() ? itGspvLast : std::prev(itGspv);
			auto itGspvNext = itGspv == itGspvLast ? listGspvs.begin() : std::next(itGspv);
			const GsIv& iv0 = itGspvPrev->iv;
			const GsIv& iv1 = itGspv->iv;
			const GsIv& iv2 = itGspvNext->iv;
			const GsVector3& v0 = cgs->GetVertex(iv0).v;
			const GsVector3& v1 = cgs->GetVertex(iv1).v;
			const GsVector3& v2 = cgs->GetVertex(iv2).v;
			if (GeometryMath::IsParalleled(v0, v1, v2))
			{
				itGspv = listGspvs.erase(itGspv);
				remove = true;
			}
			else
			{
				++itGspv;
			}
		}
		#if COMPOSITE_GEO_SOLID_DEBUG
		if (cgs->m_bDebugDetail && remove)
		{
			std::ostringstream oss;
			oss << ' ' << ' ';
			Print(oss);
			WarningString("Have removed linear middle vertices:");
			WarningString(oss.str().c_str());
		}
		#endif
		return remove;
	}

	//void GeoSolidPolygon::Copy(const GeoSolidPolygon& gsp)
	//{
	//	cgs = gsp.cgs;
	//	ivn = gsp.ivn;
	//	vIvs = gsp.vIvs;
	//	setIes = gsp.setIes;
	//}

	bool GeoSolidPolygon::DivideConvex(const GeoSolidTriangle& gstTplt, std::unordered_set<GsIt>& setItResults)
	{
		const UInt32 cGspv = listGspvs.size();
		if (cGspv <= 2)
		{
			return false;
		}
		if (cGspv == 3)
		{
			//三角形
			int it = AddTriangle(gstTplt);
			setItResults.insert((GsIt)it);
			return true;
		}
		//默认分割，不做判断
		const GsVector3& vnRender = cgs->m_daNormals[ivn];
		auto itGspv2 = std::next(listGspvs.begin(), 2);
		for (; itGspv2 != listGspvs.end(); ++itGspv2)
		{
			auto itGspv1 = std::prev(itGspv2);
			auto itGspv0 = std::prev(itGspv1);
			const GsIv& iv0 = itGspv0->iv;
			const GsIv& iv1 = itGspv1->iv;
			const GsIv& iv2 = itGspv2->iv;
			GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(iv0, iv1, iv2, gstTplt);
			gst->iuv0 = itGspv0->iuv;
			gst->iuv1 = itGspv1->iuv;
			gst->iuv2 = itGspv2->iuv;
			const GsIt it = cgs->AddTriangle(gst);
			setItResults.emplace(it);
		}
		return true;
	}

	bool GeoSolidPolygon::MergeIntoConvex(const GeoSolidPolygon& gsp)
	{
		if (listGspvs.size() <= 2)
		{
			return false;
		}
		if (gsp.listGspvs.size() <= 2)
		{
			return false;
		}
		bool merge = false;
		for (auto itGspvL1 = listGspvs.begin(); itGspvL1 != listGspvs.end(); ++itGspvL1)
		{
			auto itGspvL2 = std::next(itGspvL1);
			if (itGspvL2 == listGspvs.end())
			{
				itGspvL2 = listGspvs.begin();
			}
			const GsIv ivL1 = itGspvL1->iv;
			const GsIv ivL2 = itGspvL2->iv;
			auto itGspvRLast = std::prev(gsp.listGspvs.end());
			for (auto itGspvR1 = gsp.listGspvs.begin(); itGspvR1 != gsp.listGspvs.end(); ++itGspvR1)
			{
				auto itGspvR2 = std::next(itGspvR1);
				if (itGspvR2 == gsp.listGspvs.end())
				{
					itGspvR2 = gsp.listGspvs.begin();
				}
				const GsIv ivR1 = itGspvR1->iv;
				const GsIv ivR2 = itGspvR2->iv;
				//重合部分，右边为反向
				if (ivL1 == ivR2 && ivL2 == ivR1)
				{
					//暂时先合并为凸多边形
					auto itGspvL0 = itGspvL1 == listGspvs.begin() ? std::prev(listGspvs.end()) : std::prev(itGspvL1);
					auto itGspvR3 = std::next(itGspvR2);
					if (itGspvR3 == gsp.listGspvs.end())
					{
						itGspvR3 = gsp.listGspvs.begin();
					}
					const GsIv ivL0 = itGspvL0->iv;
					const GsIv ivR3 = itGspvR3->iv;
					if (!cgs->IsParalleled(ivL0, ivL1, ivR3))
					{
						if (!cgs->IsConvex(ivL0, ivL1, ivR3, ivn))
						{
							continue;
						}
					}
					auto itGspvL3 = std::next(itGspvL2);
					if (itGspvL3 == listGspvs.end())
					{
						itGspvL3 = listGspvs.begin();
					}
					auto itGspvR0 = itGspvR1 == gsp.listGspvs.begin() ? std::prev(gsp.listGspvs.end()) : std::prev(itGspvR1);
					const GsIv ivL3 = itGspvL3->iv;
					const GsIv ivR0 = itGspvR0->iv;
					if (!cgs->IsParalleled(ivR0, ivL2, ivL3))
					{
						if (!cgs->IsConvex(ivR0, ivL2, ivL3, ivn))
						{
							continue;
						}
					}
					merge = true;
					if (itGspvR1 == gsp.listGspvs.begin())
					{
						//将链表平铺展示，插入右边这段
						//不包括终点
						listGspvs.insert(itGspvL2, itGspvR3, gsp.listGspvs.end());
					}
					else if (itGspvR2 == itGspvRLast)
					{
						//将链表平铺展示，插入左边这段
						listGspvs.insert(itGspvL2, itGspvR3, itGspvR1);
					}
					else if (itGspvR1 == itGspvRLast && itGspvR2 == gsp.listGspvs.begin())
					{
						//首尾循环
						listGspvs.insert(itGspvL2, itGspvR3, itGspvR1);
					}
					else
					{
						//两段
						//先拼接出口，再拼接入口，可首尾连上
						listGspvs.insert(itGspvL2, itGspvR3, gsp.listGspvs.end());
						listGspvs.insert(itGspvL2, gsp.listGspvs.begin(), itGspvR1);
					}
					break;
				}
				if (merge)
				{
					break;
				}
			}
		}
		return merge;
	}

	bool GeoSolidPolygon::MergeWithoutHoles(const GeoSolidPolygon& gsp)
	{
		if (listGspvs.size() <= 2)
		{
			return false;
		}
		if (gsp.listGspvs.size() <= 2)
		{
			return false;
		}
		bool merge = false;
		//按0,1,2,3的顺序记录下标，for中使用1,2遍历
		for (auto itGspvL1 = listGspvs.begin(); itGspvL1 != listGspvs.end(); ++itGspvL1)
		{
			//循环的游标式遍历
			//先长匹配，后短匹配
			auto itGspvL2 = itGspvL1 == listGspvs.begin() ? std::prev(listGspvs.end()) : std::prev(itGspvL1);
			for (; itGspvL2 != itGspvL1; --itGspvL2)
			{
				auto itGspvL3 = std::next(itGspvL2);
				if (itGspvL3 == listGspvs.end())
				{
					itGspvL3 = listGspvs.begin();
				}
				auto itGspvRLast = std::prev(gsp.listGspvs.end());
				//数量由上方两个for循环决定，用一个for即可
				for (auto itGspvR2 = gsp.listGspvs.begin(); itGspvR2 != gsp.listGspvs.end(); ++itGspvR2)
				{
					bool findCommonEdges = true;
					//R2头部固定，itGspvRCheck游标移动，反序匹配，用R1记录匹配的开始位置
					auto itGspvRCheck = itGspvR2;
					auto itGspvR1 = itGspvR2;
					//重合部分，右边为反向
					//寻找与L1~L2相同的R2~R1的线段
					for (auto itGspvLCheck = itGspvL1; ; ++itGspvLCheck)
					{
						if (itGspvLCheck == listGspvs.end())
						{
							itGspvLCheck = listGspvs.begin();
						}
						if (itGspvLCheck->iv != itGspvRCheck->iv)
						{
							findCommonEdges = false;
							break;
						}
						itGspvR1 = itGspvRCheck;
						if (itGspvRCheck == gsp.listGspvs.begin())
						{
							itGspvRCheck = gsp.listGspvs.end();
						}
						itGspvRCheck = std::prev(itGspvRCheck);
						if (itGspvLCheck == itGspvL2)
						{
							break;
						}
					}
					if (!findCommonEdges)
					{
						continue;
					}
					if (itGspvR1 == gsp.listGspvs.end())
					{
						//记录了上一个itGspvRCheck = gsp.listGspvs.end()，将其标记到最后一个的位置
						itGspvR1 = std::prev(gsp.listGspvs.end());
					}
					std::list<GeoSolidPolygonVertex> listGspvsInsert;
					auto itGspvR3 = std::next(itGspvR2);
					if (itGspvR3 == gsp.listGspvs.end())
					{
						itGspvR3 = gsp.listGspvs.begin();
					}
					if (itGspvR1 == itGspvR3)
					{
						//首尾连上的嵌合
						merge = true;
						{
							auto itGspvLRemove = std::next(itGspvL1);
							if (itGspvLRemove == listGspvs.end())
							{
								itGspvLRemove = listGspvs.begin();
							}
							for (; itGspvLRemove != itGspvL2; )
							{
								itGspvLRemove = listGspvs.erase(itGspvLRemove);
								if (itGspvLRemove == listGspvs.end())
								{
									itGspvLRemove = listGspvs.begin();
								}
							}
						}
						break;
					}
					if (itGspvR1 == gsp.listGspvs.begin())
					{
						//将链表平铺展示，插入右边这段
						//不包括终点
						listGspvsInsert.insert(listGspvsInsert.begin(), itGspvR3, gsp.listGspvs.end());
					}
					else if (itGspvR2 == itGspvRLast)
					{
						//将链表平铺展示，插入左边这段
						listGspvsInsert.insert(listGspvsInsert.begin(), itGspvR3, itGspvR1);
					}
					else if (itGspvR1 == itGspvRLast && itGspvR2 == gsp.listGspvs.begin())
					{
						//首尾循环
						listGspvsInsert.insert(listGspvsInsert.begin(), itGspvR3, itGspvR1);
					}
					else
					{
						//两段
						//先拼接出口，再拼接入口，可首尾连上
						listGspvsInsert.insert(listGspvsInsert.begin(), gsp.listGspvs.begin(), itGspvR1);
						listGspvsInsert.insert(listGspvsInsert.begin(), itGspvR3, gsp.listGspvs.end());
					}
					//检查循环
					bool hole = false;
					for (auto itGspvLCheck = std::next(itGspvL2); ; ++itGspvLCheck)
					{
						if (itGspvLCheck == listGspvs.end())
						{
							itGspvLCheck = listGspvs.begin();
						}
						if (itGspvLCheck == itGspvL1)
						{
							break;
						}
						for (const GeoSolidPolygonVertex& gspv : listGspvsInsert)
						{
							if (itGspvLCheck->iv == gspv.iv)
							{
								//有重叠即有鸟洞
								hole = true;
								break;
							}
						}
						if (hole)
						{
							break;
						}
					}
					if (hole)
					{
						continue;
					}
					merge = true;
					listGspvs.insert(itGspvL2, listGspvsInsert.begin(), listGspvsInsert.end());
					break;
				}
				if (merge)
				{
					break;
				}
				if (itGspvL2 == listGspvs.begin())
				{
					itGspvL2 = listGspvs.end();
				}
			}
			if (merge)
			{
				break;
			}
		}
		return merge;
	}

	void GeoSolidPolygon::UpdateEars()
	{
		const UInt32 cGspv = listGspvs.size();
		if (cGspv <= 2)
		{
			return;
		}
		const GsVector3& vnRender = cgs->m_daNormals[ivn];
		auto itGspvLast = std::prev(listGspvs.end());
		for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
		{
			if (!itGspv->earDirty)
			{
				continue;
			}
			const GsIv iv = itGspv->iv;
			const GsVector3& v = cgs->GetVertex(iv).v;
			auto itGspvPrev = itGspv == listGspvs.begin() ? itGspvLast : std::prev(itGspv);
			auto itGspvNext = itGspv == itGspvLast ? listGspvs.begin() : std::next(itGspv);
			const GsIv& ivPrev = itGspvPrev->iv;
			const GsIv& ivNext = itGspvNext->iv;
			const GsVector3& vPrev = cgs->GetVertex(ivPrev).v;
			const GsVector3& vNext = cgs->GetVertex(ivNext).v;
			itGspv->earDirty = false;
			const bool clockwise = GeometryMath::IsTriangleClockwise(vPrev, v, vNext, vnRender);
			if (!clockwise)
			{
				itGspv->ear = false;
				continue;
			}
			bool ear = true;
			for (auto jtGspv = itGspvNext; jtGspv != itGspvPrev; )
			{
				const GsIv ivCheck = jtGspv->iv;
				const GsVector3& vCheck = cgs->GetVertex(ivCheck).v;
				if (GeometryMath::CheckVertexInsideTriangle(vCheck, vPrev, v, vNext))
				{
					ear = false;
					break;
				}
				++jtGspv;
				if (jtGspv == listGspvs.end())
				{
					jtGspv = listGspvs.begin();
				}
			}
			itGspv->ear = ear;
			if (ear)
			{
				const GsDigit cos60 = 0.5;
				const GsVector3& va = vPrev;
				const GsVector3& vb = v;
				const GsVector3& vc = vNext;
				const GsVector3 vab = vb - va;
				const GsVector3 vac = vc - va;
				const GsDigit cosA = GeometryMath::Cosine(vab, vac);
				const GsVector3 vba = va - vb;
				const GsVector3 vbc = vc - vb;
				const GsDigit cosB = GeometryMath::Cosine(vba, vbc);
				const GsDigit cosC = GeometryMath::Cosine(vac, vbc);
				//方差
				const GsDigit variance = (
					(cosA - cos60) * (cosA - cos60) + 
					(cosB - cos60) * (cosB - cos60) + 
					(cosC - cos60) * (cosC - cos60)
					) 
					/ 3;
				itGspv->someValue = variance;
			}
		}
	}

	void GeoSolidPolygon::EarClipping(const GeoSolidTriangle& gstTplt, GeoSolidSet<GsIt>& setItResults)
	{
		const UInt32 civ = listGspvs.size();
		if (civ <= 2)
		{
			return;
		}
		if (civ == 3)
		{
			//三角形
			const int it = AddTriangle(gstTplt);
			setItResults.insert((GsIt)it);
			return;
		}
		const GsVector3& vnRender = cgs->m_daNormals[ivn];
		//记录耳尖
		UpdateEars();
		while (true)
		{
			const UInt32 c = listGspvs.size();
			if (c <= 2)
			{
				break;
			}
			auto itGspvEar = listGspvs.end();
			GsDigit min = std::numeric_limits<GsDigit>::max();
			//GsDigit maxDistToVbc = 0.f;
			for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
			{
				if (itGspv->ear && itGspv->someValue < min)
				{
					itGspvEar = itGspv;
					min = itGspv->someValue;
				}
			}
			if (itGspvEar == listGspvs.end())
			{
				break;
			}
			GeoSolidTriangle* gst = GeoSolid::obtain<GeoSolidTriangle>(0, 0, 0, gstTplt);
			{
				auto itGspvLast = std::prev(listGspvs.end());
				auto itGspvPrev = itGspvEar == listGspvs.begin() ? itGspvLast : std::prev(itGspvEar);
				auto itGspvNext = itGspvEar == itGspvLast ? listGspvs.begin() : std::next(itGspvEar);
				gst->iv0 = itGspvPrev->iv;
				gst->iv1 = itGspvEar ->iv;
				gst->iv2 = itGspvNext->iv;
				gst->iuv0 = itGspvPrev->iuv;
				gst->iuv1 = itGspvEar->iuv;
				gst->iuv2 = itGspvNext->iuv;
				listGspvs.erase(itGspvEar);
				itGspvPrev->earDirty = true;
				itGspvNext->earDirty = true;
			}
			gst->markRemoval = false;
			const GsIt it = cgs->AddTriangle(gst);
			setItResults.emplace(it);
			UpdateEars();
		}
	}

	#if COMPOSITE_GEO_SOLID_DEBUG
	void GeoSolidPolygon::GeoGebraAppendCmd(std::ostringstream& oss)
	{
		if (listGspvs.empty())
		{
			return;
		}
		auto itGspvLast = std::prev(listGspvs.end());
		for (auto itGspv0 = listGspvs.begin(); itGspv0 != itGspvLast; ++itGspv0)
		{
			auto itGspv1 = std::next(itGspv0);
			cgs->GeoGebraAppendAnonymousEdgeCmd(oss, itGspv0->iv, itGspv1->iv);
			oss << ',';
		}
		oss << "\"Polygon(";
		for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
		{
			const GsIv& iv = itGspv->iv;
			oss << 'V' << iv;
			if (itGspv != itGspvLast)
			{
				oss << ',';
			}
		}
		oss << ")\"";
	}

	void GeoSolidPolygon::GeoGebraExecuteCmd(std::ostringstream& oss)
	{
		oss << "Execute({";
		for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
		{
			cgs->GeoGebraAppendVertexCmd(oss, itGspv->iv);
			oss << ',';
		}
		GeoGebraAppendCmd(oss);
		oss << "})";
	}

	void GeoSolidPolygon::Print(std::ostringstream& oss)
	{
		if (listGspvs.empty())
		{
			return;
		}
		auto itGspvLast = std::prev(listGspvs.end());
		oss << '{';
		for (auto itGspv = listGspvs.begin(); itGspv != listGspvs.end(); ++itGspv)
		{
			const GsIv& iv = itGspv->iv;
			oss << iv;
			if (itGspv != itGspvLast)
			{
				oss << ',';
				oss << ' ';
			}
		}
		oss << '}';
	}
	#endif

}}