# ChestManager 宝箱管理系统分析

## 🎯 核心功能

ChestManager 是游戏中的宝箱管理系统，负责管理两种类型的宝箱：

- **建筑宝箱（Building Chests）**：固定在建筑物内的宝箱
- **随机宝箱（Random Chests）**：散布在世界各处的随机宝箱

## 📊 数据结构

### SocBuildingChest - 建筑宝箱结构

```cpp
struct SocBuildingChest {
    WCoord pos;           // 位置坐标
    int chestId;          // 宝箱ID
    int blockId;          // 方块ID
    unsigned short spawnCount;  // 生成次数
    unsigned short deadDay;     // 死亡天数
    bool isSpawned;       // 是否已生成
};
```

### SocChestInfo - 随机宝箱结构

```cpp
struct SocChestInfo {
    WCoord pos;                    // 位置坐标
    const ChestSpawnDef* config;   // 配置信息
    int chestId;                   // 宝箱ID
    unsigned short spawnCount;     // 生成次数
    unsigned short deadDay;        // 死亡天数
    bool isSpawned;               // 是否已生成
};
```

### ChestSpawnDef - 宝箱生成配置

```cpp
struct ChestSpawnDef {
    int id;                        // 宝箱类型ID
    std::string name;              // 名称
    std::string description;       // 描述
    int dropType;                  // 掉落类型
    int scatterRange;              // 散落半径
    int spawnType;                 // 生成类型（0固定，1随机）
    std::string fixedSpawnRefreshTime;  // 固定刷新时间
    float fixedSpawnProbability;   // 固定生成概率
    int randomSpawnCountMin;       // 随机生成最小数量
    int randomSpawnCountMax;       // 随机生成最大数量
};
```

## 🔄 宝箱生成机制

### 1. 随机宝箱生成

- **算法**：使用泊松圆盘采样算法在地图上均匀分布宝箱位置
- **种子机制**：基于世界 ID 和宝箱类型 ID 生成固定种子，确保每次启动位置一致
- **分布范围**：6000x6000 的地图范围内
- **目标数量**：约 800 个宝箱

```cpp
// 生成固定种子确保位置一致性
uint64_t poissonSeed = m_world->getOWID() * 1000 + def.id;
auto points = poisson_disk_sampling(x_min, x_max, z_min, z_max, r, 30, poissonSeed);
```

### 2. 建筑宝箱生成

- **配置来源**：从 JSON 配置文件加载预定义位置
- **动态生成**：根据建筑方块 ID 和区块种子动态生成宝箱类型
- **文件路径**：`{rootDir}/data/w{worldId}/soc_building_chests.json`

## ⏰ 刷新机制

### 1. 按游戏天数刷新

- **触发条件**：每当游戏天数变化时
- **处理流程**：清理并重新生成建筑宝箱
- **防重复机制**：使用`deadDay`字段防止同一天重复生成

```cpp
// 检查游戏天数变化
int currentGameDay = m_world->GetWorldMgr()->getWorldTimeDay();
if (currentGameDay > m_lastGameDay) {
    clearBuildingChests();
    refreshBuildingChests(currentGameDay);
    m_lastGameDay = currentGameDay;
}
```

### 2. 定时刷新机制

- **检查频率**：每游戏内 10 分钟（500 tick）检查一次
- **时间配置**：支持固定时间点刷新（如"6:00"）
- **时间计算**：小于 6 点的时间视为第二天
- **时间转换**：`requestTime = (adjustedHour - 6) * 3000 + minute * 50`

### 3. 时间格式说明

- 游戏内 1 小时 = 3000 tick
- 游戏内 1 分钟 = 50 tick
- 6:00 为游戏时间起始点

## 🗺️ 区块管理

### 1. 区块加载时生成

- **触发时机**：当区块被加载时调用`trySpawnChestsInChunk()`
- **生成条件**：
  - 区块已加载
  - 宝箱未生成（`isSpawned = false`）
  - 不在死亡当天（`gameday != deadDay`）
- **区块范围**：16x16 方块

### 2. 区块卸载处理

- **当前状态**：区块卸载时的处理代码已被注释
- **原设计**：标记宝箱为未生成状态

## 🎮 生命周期管理

### 生命周期流程图

```mermaid
flowchart TD
    A[游戏启动] --> B[ChestManager构造函数]
    B --> C[初始化随机宝箱位置<br/>initRandomChestsInfo]
    B --> D[加载建筑宝箱配置<br/>initBuildingChestsInfo]

    C --> E[使用泊松圆盘采样<br/>生成800个随机位置]
    D --> F[从JSON文件加载<br/>建筑宝箱位置]

    E --> G[游戏主循环 tick]
    F --> G

    G --> H{检查游戏天数<br/>是否变化?}
    H -->|是| I[清理建筑宝箱<br/>clearBuildingChests]
    H -->|否| J{检查是否到达<br/>定时刷新时间?}

    I --> K[刷新建筑宝箱<br/>refreshBuildingChests]
    K --> J

    J -->|是| L[处理固定时间宝箱<br/>refreshFixedTimeChest]
    J -->|否| M[区块加载事件<br/>onChunkLoaded]
    L --> M

    M --> N[尝试在区块生成宝箱<br/>trySpawnChestsInChunk]

    N --> O{遍历区块内宝箱}
    O --> P{宝箱是否已生成?<br/>isSpawned}
    P -->|是| O
    P -->|否| Q{是否在死亡当天?<br/>deadDay == gameDay}
    Q -->|是| O
    Q -->|否| R{区块是否已加载?}
    R -->|否| O
    R -->|是| S{宝箱类型?}

    S -->|建筑宝箱| T[生成建筑宝箱<br/>spawnBuildingChest]
    S -->|随机宝箱| U[生成随机宝箱<br/>spawnRandomChest]

    T --> V[生成宝箱ID<br/>generateBuildingChestId]
    V --> W[放置方块<br/>setBlockAll]

    U --> W
    W --> X{方块放置成功?}
    X -->|否| Y[生成失败]
    X -->|是| Z[创建容器<br/>addDungeonChest]

    Z --> AA[设置自动销毁<br/>setIsNeedDestroyWhenEmpty]
    AA --> BB[标记已生成<br/>isSpawned = true]
    BB --> CC[增加生成计数<br/>spawnCount++]

    Y --> O
    CC --> O

    O --> DD[玩家破坏宝箱事件<br/>onDestroyContainer]
    DD --> EE[标记未生成<br/>isSpawned = false]
    EE --> FF[记录死亡天数<br/>deadDay = currentGameDay]
    FF --> GG[等待下次刷新]

    GG --> G

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style G fill:#fff3e0
    style DD fill:#ffebee
    style BB fill:#e8f5e8
    style Y fill:#ffcdd2
```

### 1. 生成流程

1. **状态检查**：检查宝箱是否已生成（`isSpawned`）
2. **时间检查**：检查是否在死亡当天（`deadDay`）
3. **区块检查**：检查区块是否已加载
4. **方块放置**：调用`setBlockAll`在世界中放置宝箱方块
5. **容器创建**：创建`WorldStorageBox`容器对象
6. **属性设置**：设置`setIsNeedDestroyWhenEmpty(true)`自动销毁标志

### 2. 销毁处理

- **触发事件**：宝箱被破坏时调用`onDestroyContainer()`
- **状态更新**：标记为未生成状态（`isSpawned = false`）
- **时间记录**：记录死亡天数（`deadDay = currentGameDay`）
- **自动清理**：空宝箱会自动销毁

### 3. 关键状态转换

- **未生成 → 已生成**：通过区块加载或定时刷新触发
- **已生成 → 未生成**：通过玩家破坏或新游戏天清理触发
- **死亡状态**：使用`deadDay`防止同一天重复生成
- **自动清理**：空宝箱会在被清空后自动销毁

## 📋 主要方法功能

| 方法名                      | 作用描述                           |
| --------------------------- | ---------------------------------- |
| `ChestManager()`            | 构造函数，初始化管理器和加载配置   |
| `tick()`                    | 主循环，检查游戏天数变化和定时刷新 |
| `initRandomChestsInfo()`    | 使用泊松采样初始化随机宝箱位置     |
| `initBuildingChestsInfo()`  | 从 JSON 文件加载建筑宝箱配置       |
| `onChunkLoaded()`           | 区块加载时尝试生成宝箱             |
| `onChunkUnloaded()`         | 区块卸载时的处理（当前已注释）     |
| `trySpawnChestsInChunk()`   | 在指定区块尝试生成宝箱             |
| `spawnRandomChest()`        | 生成随机宝箱实体                   |
| `spawnBuildingChest()`      | 生成建筑宝箱实体                   |
| `onDestroyContainer()`      | 处理宝箱被破坏事件                 |
| `addBuildingChest()`        | 添加新的建筑宝箱到管理列表         |
| `refreshBuildingChests()`   | 刷新建筑宝箱（新游戏天时调用）     |
| `clearBuildingChests()`     | 清理建筑宝箱（当前实现为空）       |
| `generateBuildingChestId()` | 根据建筑方块和区块种子生成宝箱 ID  |
| `getChests()`               | 获取所有宝箱信息的统一接口         |
| `refreshFixedTimeChest()`   | 处理固定时间刷新的宝箱             |

## 🔧 配置系统

### 1. CSV 配置文件

- **chest_spawn.csv**：定义宝箱类型、刷新规则、掉落配置
- **chestdef.csv**：定义宝箱内容物和掉落概率

### 2. JSON 配置文件

- **路径**：`{rootDir}/data/w{worldId}/soc_building_chests.json`
- **内容**：存储建筑宝箱的具体位置信息
- **格式**：包含 posX、posY、posZ、blockId 等字段

### 3. 配置加载流程

1. 启动时加载 CSV 配置表
2. 根据世界 ID 加载对应的 JSON 建筑宝箱配置
3. 使用配置信息初始化宝箱管理数据

## 🎯 设计特点

1. **性能优化**：基于区块的延迟加载机制
2. **一致性保证**：使用固定种子确保宝箱位置的一致性
3. **灵活配置**：支持 CSV 和 JSON 双重配置系统
4. **生命周期管理**：完整的生成、刷新、销毁流程
5. **防重复机制**：通过 deadDay 防止同一天重复生成
6. **自动清理**：空宝箱自动销毁，避免资源浪费

这个系统设计得相当完善，既保证了宝箱分布的随机性和平衡性，又通过区块管理和生命周期控制确保了性能和游戏体验。
