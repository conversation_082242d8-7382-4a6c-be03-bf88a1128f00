#include "blocks/container_world.h"
#include "SandboxEventDispatcherManager.h"
#include "SandboxCoreDriver.h"


class WorldContainerLua : public WorldContainer //tolua_exports
{ //tolua_exports
public:
	//tolua_begin 
	WorldContainerLua(int  baseindex):WorldContainer(baseindex){
	m_SubTypeLua = 0;
}
	WorldContainerLua(const WCoord & blockpos,int  baseindex):WorldContainer(blockpos,baseindex){
	m_SubTypeLua = 0;
}
	virtual ~WorldContainerLua(){

}
/////////////////////////////////////////////////////////////////
/////////////////////////虚函数发事件/////////////////////////////
/////////////////////////////////////////////////////////////////
	virtual void enterWorld(World *pworld)
	{
		WorldContainer::enterWorld(pworld);
		registerUpdateDisplay();
		registerUpdateTick();

		Event().Emit("enterWorld", MNSandbox::SandboxContext(this).SetData_Userdata("World",pworld));
	}
	virtual void startVehicle()
	{
		Event().Emit("startVehicle", MNSandbox::SandboxContext(this));
	}
	virtual void leaveWorld()
	{
		Event().Emit("leaveWorld", MNSandbox::SandboxContext(this));
		WorldContainer::leaveWorld();
	}
	virtual int getObjType()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getObjType", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual bool canAddToChunk(Chunk *pchunk)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("canAddToChunk", MNSandbox::SandboxContext(this).SetData_Userdata("Chunk",pchunk));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual int getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getItemAndAttrib", MNSandbox::SandboxContext(this).SetData_Userdata("RepeatedPtrField<PB_ItemData>","pItemInfos",pItemInfos).SetData_Userdata("RepeatedField<float>","pAttrInfos",pAttrInfos));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual void syncItemUserdata(IClientPlayer *player)
	{
		Event().Emit("syncItemUserdata", MNSandbox::SandboxContext(this).SetData_Userdata("IClientPlayer",player));
	}
	virtual void syncCustomActorModelData(IClientPlayer *player)
	{
		Event().Emit("syncCustomActorModelData", MNSandbox::SandboxContext(this).SetData_Userdata("IClientPlayer",player));
	}
	virtual bool syncFullyCustomModelData(IClientPlayer *player)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("syncFullyCustomModelData", MNSandbox::SandboxContext(this).SetData_Userdata("IClientPlayer",player));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void updateCustomActorModelData(CustomActorModelData &data)
	{
		Event().Emit("updateCustomActorModelData", MNSandbox::SandboxContext(this).SetData_UserObject(data));
	}
	virtual void updateFullyCustomModelData(std::string skey)
	{
		Event().Emit("updateFullyCustomModelData", MNSandbox::SandboxContext(this).SetData_String(skey));
	}
	virtual void updateTick()
	{
		Event().Emit("updateTick", MNSandbox::SandboxContext(this));
	}
	virtual void updateDisplay(float dtime)
	{
		Event().Emit("updateDisplay", MNSandbox::SandboxContext(this).SetData_Number(dtime));
	}
	virtual void dropItems()
	{
		Event().Emit("dropItems", MNSandbox::SandboxContext(this));
	}
	virtual int calComparatorInputOverride()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("calComparatorInputOverride", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual BackPackGrid *index2Grid(int index) override
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("index2Grid", MNSandbox::SandboxContext(this).SetData_Number(index));

		if (auto_result.IsSuccessed())
		{
			return static_cast<BackPackGrid*>(auto_result.GetData_Userdata("BackPackGrid","auto_return"));
		}
		return NULL;
	}
	virtual bool canPutItem(int index)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("canPutItem", MNSandbox::SandboxContext(this).SetData_Number(index));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void onAttachUI()
	{
		Event().Emit("onAttachUI", MNSandbox::SandboxContext(this));
	}
	virtual void onDetachUI()
	{
		Event().Emit("onDetachUI", MNSandbox::SandboxContext(this));
	}
	virtual void onAttachToMech()
	{
		Event().Emit("onAttachToMech", MNSandbox::SandboxContext(this));
	}
	virtual void onDetachFromMech()
	{
		Event().Emit("onDetachFromMech", MNSandbox::SandboxContext(this));
	}
	virtual void addOpenUIN(int uin)
	{
		Event().Emit("addOpenUIN", MNSandbox::SandboxContext(this).SetData_Number(uin));
	}
	virtual void removeOpenUIN(int uin)
	{
		Event().Emit("removeOpenUIN", MNSandbox::SandboxContext(this).SetData_Number(uin));
	}
	virtual void afterChangeGrid(int index)
	{
		Event().Emit("afterChangeGrid", MNSandbox::SandboxContext(this).SetData_Number(index));
	}
	virtual void resetOpenUINs()
	{
		Event().Emit("resetOpenUINs", MNSandbox::SandboxContext(this));
	}
	virtual bool checkPutItem(int itemid, int num)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("checkPutItem", MNSandbox::SandboxContext(this).SetData_Number("itemid",itemid).SetData_Number("num",num));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual BackPackGrid *getGridByItemID(int itemid)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getGridByItemID", MNSandbox::SandboxContext(this).SetData_Number(itemid));

		if (auto_result.IsSuccessed())
		{
			return static_cast<BackPackGrid*>(auto_result.GetData_Userdata("BackPackGrid","auto_return"));
		}
		return NULL;
	}
	virtual void replaceGridByBluePrint(WorldContainer *container)
	{
		Event().Emit("replaceGridByBluePrint", MNSandbox::SandboxContext(this).SetData_Userdata("ContainerBuildBluePrint",container));
	}
	virtual const char *getText()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getText", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			static std::string tmpStr = auto_result.GetData_String("auto_return");
			return tmpStr.c_str();
		}
		return "";
	}
	virtual void setText(const char *text)
	{
		Event().Emit("setText", MNSandbox::SandboxContext(this).SetData_String(text?text:"auto__$NULL$__"));
	}
	virtual float getAttrib(int i)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getAttrib", MNSandbox::SandboxContext(this).SetData_Number(i));

		if (auto_result.IsSuccessed())
		{
			return float(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual bool needSetPassword()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("needSetPassword", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void setPassword(int password)
	{
		Event().Emit("setPassword", MNSandbox::SandboxContext(this).SetData_Number(password));
	}
	virtual int getPassword()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getPassword", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual std::string getBPFileName()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getBPFileName", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual std::string getBPDataStr()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getBPDataStr", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual void StartWorking(const char* sheetName, const char* authorName)
	{
		Event().Emit("StartWorking", MNSandbox::SandboxContext(this).SetData_String("sheetName",sheetName?sheetName:"auto__$NULL$__").SetData_String("authorName",authorName?authorName:"auto__$NULL$__"));
	}
	virtual void MakeCustoModel(int uin, const char* modelname, const char* modeldesc, int modeltype = 0)
	{
		Event().Emit("MakeCustoModel", MNSandbox::SandboxContext(this).SetData_Number("uin",uin).SetData_String("modelname",modelname?modelname:"auto__$NULL$__").SetData_String("modeldesc",modeldesc?modeldesc:"auto__$NULL$__").SetData_Number("modeltype",modeltype));
	}
	virtual void setBrushMonsterAttr(int MobResID, int everyNum, int maxNum, int spawnWide, int spawnHigh, int spawnDelay, bool numSwitch, bool DelaySwitch)
	{
		Event().Emit("setBrushMonsterAttr", MNSandbox::SandboxContext(this).SetData_Number("MobResID",MobResID).SetData_Number("everyNum",everyNum).SetData_Number("maxNum",maxNum).SetData_Number("spawnWide",spawnWide).SetData_Number("spawnHigh",spawnHigh).SetData_Number("spawnDelay",spawnDelay).SetData_Bool("numSwitch",numSwitch).SetData_Bool("DelaySwitch",DelaySwitch));
	}
	virtual BrushMonsterAttr getBrushMonsterAttr()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getBrushMonsterAttr", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_UserObject<BrushMonsterAttr>("auto_return");
		}
		return BrushMonsterAttr();
	}
	virtual void bindCustomAvatar(std::string bonename, std::string modelfilename, float scale, float yaw, float pitch, short offsetx, short offsety, short offsetz)
	{
		Event().Emit("bindCustomAvatar", MNSandbox::SandboxContext(this).SetData_String("bonename",bonename).SetData_String("modelfilename",modelfilename).SetData_Number("scale",scale).SetData_Number("yaw",yaw).SetData_Number("pitch",pitch).SetData_Number("offsetx",offsetx).SetData_Number("offsety",offsety).SetData_Number("offsetz",offsetz));
	}
	virtual void setRateByPower(int power)
	{
		Event().Emit("setRateByPower", MNSandbox::SandboxContext(this).SetData_Number(power));
	}
	virtual bool outsideWorkArea()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("outsideWorkArea", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual int getMachineAttribute(int iType, bool bNeedCal)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getMachineAttribute", MNSandbox::SandboxContext(this).SetData_Number("iType",iType).SetData_Bool("bNeedCal",bNeedCal));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual int checkIfCanGenVehicle()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("checkIfCanGenVehicle", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual void preVehicleBlock()
	{
		Event().Emit("preVehicleBlock", MNSandbox::SandboxContext(this));
	}
	virtual void buildVehicleBlock()
	{
		Event().Emit("buildVehicleBlock", MNSandbox::SandboxContext(this));
	}
	virtual bool getVehicleBuildInfo()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getVehicleBuildInfo", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void VehicleCentroidDisplay(bool isDisplay)
	{
		Event().Emit("VehicleCentroidDisplay", MNSandbox::SandboxContext(this).SetData_Bool(isDisplay));
	}
	virtual void clear()
	{
		Event().Emit("clear", MNSandbox::SandboxContext(this));
	}
	virtual int addItemByCount(int itemid, int num)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("addItemByCount", MNSandbox::SandboxContext(this).SetData_Number("itemid",itemid).SetData_Number("num",num));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual void removeItemByCount(int itemid, int num)
	{
		Event().Emit("removeItemByCount", MNSandbox::SandboxContext(this).SetData_Number("itemid",itemid).SetData_Number("num",num));
	}
	virtual bool updateActionerData(const char* jsonstr, WCoord startPos=WCoord(0,0,0), int rotateType = -1)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("updateActionerData", MNSandbox::SandboxContext(this).SetData_String("jsonstr",jsonstr?jsonstr:"auto__$NULL$__").SetData_UserObject("startPos",startPos).SetData_Number("rotateType",rotateType));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual std::string getActionerDataStr(bool fromWorkShop = false, WCoord start = WCoord(0, 0, 0))
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getActionerDataStr", MNSandbox::SandboxContext(this).SetData_Bool("fromWorkShop",fromWorkShop).SetData_UserObject("start",start));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual void setSensorValue(int value)
	{
		Event().Emit("setSensorValue", MNSandbox::SandboxContext(this).SetData_Number(value));
	}
	virtual int getSensorValue()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getSensorValue", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual bool getIsSensorRever()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getIsSensorRever", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void setIsSensorRever(bool b)
	{
		Event().Emit("setIsSensorRever", MNSandbox::SandboxContext(this).SetData_Bool(b));
	}
	virtual void setSensorValueAndIsRever(int value, bool b)
	{
		Event().Emit("setSensorValueAndIsRever", MNSandbox::SandboxContext(this).SetData_Number("value",value).SetData_Bool("b",b));
	}
	virtual bool getEditFlag()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getEditFlag", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual int getLootResultInfoSize()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getLootResultInfoSize", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual std::string getLootResultInfoByType(int type)
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getLootResultInfoByType", MNSandbox::SandboxContext(this).SetData_Number(type));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual void setTombStoneTitle(std::string title)
	{
		Event().Emit("setTombStoneTitle", MNSandbox::SandboxContext(this).SetData_String(title));
	}
	virtual void setTombStoneContent(std::string content)
	{
		Event().Emit("setTombStoneContent", MNSandbox::SandboxContext(this).SetData_String(content));
	}
	virtual std::string getTombStoneTitle()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getTombStoneTitle", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual std::string getTombStoneContent()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getTombStoneContent", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_String("auto_return");
		}
		return "";
	}
	virtual int  getMobGiftNum()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getMobGiftNum", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual void setTemperatureLev(int lev)
	{
		Event().Emit("setTemperatureLev", MNSandbox::SandboxContext(this).SetData_Number(lev));
	}
	virtual bool canEditActorModel()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("canEditActorModel", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual bool canEditFullyCustomModel()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("canEditFullyCustomModel", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual int getGridNum()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getGridNum", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual int getBaseIndex()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getBaseIndex", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return int(auto_result.GetData_Number("auto_return"));
		}
		return 0;
	}
	virtual bool remoteMarkBlockForUpdate()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("remoteMarkBlockForUpdate", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual void resetEntityBySyncData(std::string skey)
	{
		Event().Emit("resetEntityBySyncData", MNSandbox::SandboxContext(this).SetData_String(skey));
	}
	virtual void convertRotateByBluePrint(int rotatetype)
	{
		Event().Emit("convertRotateByBluePrint", MNSandbox::SandboxContext(this).SetData_Number(rotatetype));
	}
	virtual bool shieldDialogue()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("shieldDialogue", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual bool doOpenContainer()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("doOpenContainer", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return auto_result.GetData_Bool("auto_return");
		}
		return false;
	}
	virtual const char* getContainerName()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("getContainerName", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			static std::string tmpStr = auto_result.GetData_String("auto_return");
			return tmpStr.c_str();
		}
		return "";
	}
	virtual jsonxx::Value * saveJson()
	{
		MNSandbox::SandboxResult auto_result = Event().Emit("saveJson", MNSandbox::SandboxContext(this));

		if (auto_result.IsSuccessed())
		{
			return static_cast<jsonxx::Value*>(auto_result.GetData_Userdata("jsonxx::Value","auto_return"));
		}
		return NULL;
	}
	virtual void loadJson(jsonxx::Value *obj)
	{
		Event().Emit("loadJson", MNSandbox::SandboxContext(this).SetData_Userdata("jsonxx::Value",obj));
	}
/////////////////////////////////////////////////////////////////
/////////////////////////虚函数基类实现/////////////////////////////
/////////////////////////////////////////////////////////////////
	void enterWorldBase(World *pworld)
	{
		return WorldContainer::enterWorld(pworld);
	}
	void startVehicleBase()
	{
		return WorldContainer::startVehicle();
	}
	void leaveWorldBase()
	{
		return WorldContainer::leaveWorld();
	}
	bool canAddToChunkBase(Chunk *pchunk)
	{
		return WorldContainer::canAddToChunk(pchunk);
	}
	int getItemAndAttribBase(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
	{
		return WorldContainer::getItemAndAttrib(pItemInfos,pAttrInfos);
	}
	void syncItemUserdataBase(IClientPlayer *player)
	{
		return WorldContainer::syncItemUserdata(player);
	}
	void syncCustomActorModelDataBase(IClientPlayer *player)
	{
		return WorldContainer::syncCustomActorModelData(player);
	}
	bool syncFullyCustomModelDataBase(IClientPlayer *player)
	{
		return WorldContainer::syncFullyCustomModelData(player);
	}
	void updateCustomActorModelDataBase(CustomActorModelData &data)
	{
		return WorldContainer::updateCustomActorModelData(data);
	}
	void updateFullyCustomModelDataBase(std::string skey)
	{
		return WorldContainer::updateFullyCustomModelData(skey);
	}
	void updateTickBase()
	{
		return WorldContainer::updateTick();
	}
	void updateDisplayBase(float dtime)
	{
		return WorldContainer::updateDisplay(dtime);
	}
	void dropItemsBase()
	{
		return WorldContainer::dropItems();
	}
	int calComparatorInputOverrideBase()
	{
		return WorldContainer::calComparatorInputOverride();
	}
	BackPackGrid *index2GridBase(int index)
	{
		return WorldContainer::index2Grid(index);
	}
	bool canPutItemBase(int index)
	{
		return WorldContainer::canPutItem(index);
	}
	void onAttachUIBase()
	{
		return WorldContainer::onAttachUI();
	}
	void onDetachUIBase()
	{
		return WorldContainer::onDetachUI();
	}
	void onAttachToMechBase()
	{
		return WorldContainer::onAttachToMech();
	}
	void onDetachFromMechBase()
	{
		return WorldContainer::onDetachFromMech();
	}
	void addOpenUINBase(int uin)
	{
		return WorldContainer::addOpenUIN(uin);
	}
	void removeOpenUINBase(int uin)
	{
		return WorldContainer::removeOpenUIN(uin);
	}
	void afterChangeGridBase(int index)
	{
		return WorldContainer::afterChangeGrid(index);
	}
	void resetOpenUINsBase()
	{
		return WorldContainer::resetOpenUINs();
	}
	bool checkPutItemBase(int itemid, int num)
	{
		return WorldContainer::checkPutItem(itemid,num);
	}
	BackPackGrid *getGridByItemIDBase(int itemid)
	{
		return WorldContainer::getGridByItemID(itemid);
	}
	void replaceGridByBluePrintBase(WorldContainer* container)
	{
		return WorldContainer::replaceGridByBluePrint(container);
	}
	const char *getTextBase()
	{
		return WorldContainer::getText();
	}
	void setTextBase(const char *text)
	{
		return WorldContainer::setText(text);
	}
	float getAttribBase(int i)
	{
		return WorldContainer::getAttrib(i);
	}
	bool needSetPasswordBase()
	{
		return WorldContainer::needSetPassword();
	}
	void setPasswordBase(int password)
	{
		return WorldContainer::setPassword(password);
	}
	int getPasswordBase()
	{
		return WorldContainer::getPassword();
	}
	std::string getBPFileNameBase()
	{
		return WorldContainer::getBPFileName();
	}
	std::string getBPDataStrBase()
	{
		return WorldContainer::getBPDataStr();
	}
	void StartWorkingBase(const char* sheetName, const char* authorName)
	{
		return WorldContainer::StartWorking(sheetName,authorName);
	}
	void MakeCustoModelBase(int uin, const char* modelname, const char* modeldesc, int modeltype = 0)
	{
		return WorldContainer::MakeCustoModel(uin,modelname,modeldesc,modeltype);
	}
	void setBrushMonsterAttrBase(int MobResID, int everyNum, int maxNum, int spawnWide, int spawnHigh, int spawnDelay, bool numSwitch, bool DelaySwitch)
	{
		return WorldContainer::setBrushMonsterAttr(MobResID,everyNum,maxNum,spawnWide,spawnHigh,spawnDelay,numSwitch,DelaySwitch);
	}
	BrushMonsterAttr getBrushMonsterAttrBase()
	{
		return WorldContainer::getBrushMonsterAttr();
	}
	void bindCustomAvatarBase(std::string bonename, std::string modelfilename, float scale, float yaw, float pitch, short offsetx, short offsety, short offsetz)
	{
		return WorldContainer::bindCustomAvatar(bonename,modelfilename,scale,yaw,pitch,offsetx,offsety,offsetz);
	}
	void setRateByPowerBase(int power)
	{
		return WorldContainer::setRateByPower(power);
	}
	bool outsideWorkAreaBase()
	{
		return WorldContainer::outsideWorkArea();
	}
	int getMachineAttributeBase(int iType, bool bNeedCal)
	{
		return WorldContainer::getMachineAttribute(iType,bNeedCal);
	}
	int checkIfCanGenVehicleBase()
	{
		return WorldContainer::checkIfCanGenVehicle();
	}
	void preVehicleBlockBase()
	{
		return WorldContainer::preVehicleBlock();
	}
	void buildVehicleBlockBase()
	{
		return WorldContainer::buildVehicleBlock();
	}
	bool getVehicleBuildInfoBase()
	{
		return WorldContainer::getVehicleBuildInfo();
	}
	void VehicleCentroidDisplayBase(bool isDisplay)
	{
		return WorldContainer::VehicleCentroidDisplay(isDisplay);
	}
	void clearBase()
	{
		return WorldContainer::clear();
	}
	int addItemByCountBase(int itemid, int num)
	{
		return WorldContainer::addItemByCount(itemid,num);
	}
	void removeItemByCountBase(int itemid, int num)
	{
		return WorldContainer::removeItemByCount(itemid,num);
	}
	bool updateActionerDataBase(const char* jsonstr, WCoord startPos=WCoord(0,0,0), int rotateType = -1)
	{
		return WorldContainer::updateActionerData(jsonstr,startPos,rotateType);
	}
	std::string getActionerDataStrBase(bool fromWorkShop = false, WCoord start = WCoord(0, 0, 0))
	{
		return WorldContainer::getActionerDataStr(fromWorkShop,start);
	}
	void setSensorValueBase(int value)
	{
		return WorldContainer::setSensorValue(value);
	}
	int getSensorValueBase()
	{
		return WorldContainer::getSensorValue();
	}
	bool getIsSensorReverBase()
	{
		return WorldContainer::getIsSensorRever();
	}
	void setIsSensorReverBase(bool b)
	{
		return WorldContainer::setIsSensorRever(b);
	}
	void setSensorValueAndIsReverBase(int value, bool b)
	{
		return WorldContainer::setSensorValueAndIsRever(value,b);
	}
	bool getEditFlagBase()
	{
		return WorldContainer::getEditFlag();
	}
	int getLootResultInfoSizeBase()
	{
		return WorldContainer::getLootResultInfoSize();
	}
	std::string getLootResultInfoByTypeBase(int type)
	{
		return WorldContainer::getLootResultInfoByType(type);
	}
	void setTombStoneTitleBase(std::string title)
	{
		return WorldContainer::setTombStoneTitle(title);
	}
	void setTombStoneContentBase(std::string content)
	{
		return WorldContainer::setTombStoneContent(content);
	}
	std::string getTombStoneTitleBase()
	{
		return WorldContainer::getTombStoneTitle();
	}
	std::string getTombStoneContentBase()
	{
		return WorldContainer::getTombStoneContent();
	}
	int  getMobGiftNumBase()
	{
		return WorldContainer::getMobGiftNum();
	}
	void setTemperatureLevBase(int lev)
	{
		return WorldContainer::setTemperatureLev(lev);
	}
	bool canEditActorModelBase()
	{
		return WorldContainer::canEditActorModel();
	}
	bool canEditFullyCustomModelBase()
	{
		return WorldContainer::canEditFullyCustomModel();
	}
	int getGridNumBase()
	{
		return WorldContainer::getGridNum();
	}
	int getBaseIndexBase()
	{
		return WorldContainer::getBaseIndex();
	}
	bool remoteMarkBlockForUpdateBase()
	{
		return WorldContainer::remoteMarkBlockForUpdate();
	}
	void resetEntityBySyncDataBase(std::string skey)
	{
		return WorldContainer::resetEntityBySyncData(skey);
	}
	void convertRotateByBluePrintBase(int rotatetype)
	{
		return WorldContainer::convertRotateByBluePrint(rotatetype);
	}
	bool shieldDialogueBase()
	{
		return WorldContainer::shieldDialogue();
	}
	bool doOpenContainerBase()
	{
		return WorldContainer::doOpenContainer();
	}
	const char* getContainerNameBase()
	{
		return WorldContainer::getContainerName();
	}
	//tolua_end

	//tolua_begin
public:
	
	void setSubTypeLua(int pType)
	{
		m_SubTypeLua = pType;
	}

	int getSubTypeLua()
	{
		return m_SubTypeLua;
	}

	//tolua_end

	FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerByLua;
	}

	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER &builder)
	{
		auto basedata = saveContainerCommon(builder);
		jsonxx::Value* value = saveJson();
		flatbuffers::Offset<flatbuffers::Vector<unsigned char>>jsonxxBin(0);
		int binLen = 0;
		if (value)
		{
			unsigned char* input = NULL;
			int binLen = 0;
			value->saveBinary(input, binLen);
			if (binLen)
				jsonxxBin = builder.CreateVector(input, binLen);
			free(input);
		}
		auto actor = FBSave::CreateContainerByLua(builder, basedata, m_SubTypeLua, jsonxxBin);
		return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerByLua, actor.Union());
	}
	
	virtual bool load(const void *srcdata)
	{
		auto src = reinterpret_cast<const FBSave::ContainerByLua *>(srcdata);
		if (src)
		{
			loadContainerCommon(src->basedata());
			m_SubTypeLua = src->subTypeLua();
			auto jsonxxBin = src->jsonxxData();
			if (jsonxxBin && jsonxxBin->Length() > 0)
			{
				jsonxx::Value value;
				value.parseBinary((unsigned char*)jsonxxBin->Data(), jsonxxBin->Length());
				if (value.type_ == value.ARRAY_ || value.type_ == value.OBJECT_)
				{
					loadJson(&value);
				}
			}
			return true;
		}
		else
		{
			return false;
		}
	}
	
	static WorldContainer* createLuaContainerFactory(int pLuaType, bool pIsVehicle = false)
	{
		void *container = NULL;
		MINIW::ScriptVM::game()->callFunction("createWorldContainer", "ib>u", pLuaType, pIsVehicle, &container);
		return static_cast<WorldContainer*>(container);
	}

protected:
	int m_SubTypeLua;


}; //tolua_exports 
