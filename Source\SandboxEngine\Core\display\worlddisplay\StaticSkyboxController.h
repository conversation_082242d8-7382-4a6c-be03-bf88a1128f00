#pragma once

#include "Render/SceneObjects/RenderObject.h"
#include "Math/Vector3f.h"
#include "Math/Color.h"
#include "Graphics/Texture2D.h"
#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Math/Gradient.h"
#include "SkyPlaneDefine.h"

namespace Rainbow {

	class SkyBoxRenderer;
	class GameObject;
	class StaticSkyboxController
	{
	public:
		StaticSkyboxController();
		~StaticSkyboxController();
		void Init(Rainbow::GameObject* gameObject);

		void SetEnable(bool value);

		void SetTexture(Rainbow::Texture* texture);

		Texture* GetTexture();

		void OnBeginFadeTo(SharePtr<Rainbow::Texture> targetTex);
		void OnUpdateFadeTo(float percentage);
		void OnEndFadeTo();
	private:
		SharePtr<MaterialInstance> m_Material;
		SkyBoxRenderer* m_Renderer;
		SharePtr<Rainbow::Texture> m_FadeTargetTex;
	};

}  // namespace Rainbow