#pragma once
/**
* file : SceneEffectEllipse
* func : 场景效果 （椭圆）
* by : pengdapu
*/
#include "SceneEffectLine.h"
#include "SceneEffectFrame.h"
#include "SceneEffectSphere.h"
#include "SceneEffectFan.h"
#include "SandboxRay.h"

class SceneEffectSphere;

class SceneEffectEllipse : public SceneEffectGeom
{
public:
	SceneEffectEllipse();
	virtual ~SceneEffectEllipse();

	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;

public:
	// 设置操作点位置
	inline void SetOptSpherePos(MNSandbox::MNCoord3f wPos) { m_optSpherePos = wPos; }

	inline MNSandbox::MNCoord3f GetOptSpherePos() { return m_optSpherePos; }

	inline void SetColor(BlockVector circleColor, BlockVector optSphereColor) { m_Color = circleColor; m_optSphereColor = optSphereColor; }

	// 更新圆框架
	void RefreshEllipseFrame(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, float major, float minor);

	// 设置切分个数
	void SetSector(int sector, bool bRefresh = false);

	// 设置旋转轴
	void SetRotationAxis(Rainbow::Vector3f curAxis, Rainbow::Vector3f originAxis, bool bRefresh = false);

	// 设置选中
	void SetSelected(bool bSelected) { m_isSelected = bSelected; }

	void SetSemiMajorAxis(const float& semiMajorAxis) { m_semiMajorAxis = semiMajorAxis; }

	void SetSemiMinorAxis(const float& semiMinorAxis) { m_semiMinorAxis = semiMinorAxis; }

	void SetStartRadian(const float& radian) { m_fStartRadian = radian; }

	void SetEndRadian(const float& radian) { m_fEndRadian = radian; }

	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
private:

	struct DrawLine
	{
		SceneEffectLine* _line = nullptr; // 绘制线条
		MNSandbox::MNCoord3f _startDrawPos = MNSandbox::MNCoord3f(0.0);
		MNSandbox::MNCoord3f _endDrawPos = MNSandbox::MNCoord3f(0.0);
	};
	std::vector<DrawLine> m_vecDrawCircleLine;		// 绘制圆线条

	DrawLine m_selectedline;

	BlockVector m_optSphereColor = MakeBlockVector(211, 211, 211, 255);	//操作球颜色
	MNSandbox::MNCoord3f m_optSpherePos = MNSandbox::MNCoord3f(0.0);		  // 操作点位置
	Rainbow::Vector3f m_originRotationAxis = Rainbow::Vector3f::yAxis;	//初始旋转轴
	Rainbow::Vector3f m_curRotationAxis = Rainbow::Vector3f::yAxis;		//当前旋转轴
	EncircleDir m_emCurRotateDir = EncircleDir::Y_AXIS;			//当前旋转方向
	//切分个数
	int m_iSector = 32;
	//长轴
	float m_semiMajorAxis;
	//短轴
	float m_semiMinorAxis;

	float m_fStartRadian = 0;

	float m_fEndRadian = Rainbow::kTwoPI;

	int m_circleLineMinWidth = 1;	// 线宽
	int m_circleLineWidth = 1;		// 线宽

	bool m_isSelected = false; // 是否选中

	static const unsigned  ms_optSphereRadius = 30;		// 操作球半径
	static const unsigned  ms_circleLineMinWidth = 1;	// 线宽
	static const unsigned  ms_circleLineWidth = 1;		// 线宽
};
