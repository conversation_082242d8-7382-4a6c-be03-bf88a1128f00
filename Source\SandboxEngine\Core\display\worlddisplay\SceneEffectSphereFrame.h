#pragma once
/**
* file : SceneEffectSphereFrame
* func : 场景效果 （球框）
* by : pengdapu
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectEllipse;

class SceneEffectSphereFrame : public SceneEffectGeom
{
public:
	SceneEffectSphereFrame();
	virtual ~SceneEffectSphereFrame();
	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;
	bool IsActive(World* pWorld) const override;
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
public:
	void SetRadius(float radius) override;
	void SetSector(int sector);
private:
	SceneEffectEllipse* m_ellipseY = nullptr;
	SceneEffectEllipse* m_ellipseZ = nullptr;
	SceneEffectEllipse* m_ellipseX = nullptr;
	int m_iSector = 32;
};
