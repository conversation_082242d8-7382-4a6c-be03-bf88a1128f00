#include "FullyCustomModelMgr.h"
#include "PackingFullyCustomModelMgr.h"

#include "File/FileManager.h"
#include "File/DirVisitor.h"

#include "custommotion/CustomMotionMgr.h"
#include "custommotion/CustomMotion.h"
#include "worldData/WorldManager.h"
#include "IClientPlayer.h"
#include "IActorBody.h"
#include "SandboxGFunc.h"

using namespace MNSandbox;
using namespace Rainbow;

void FullyCustomModelMgr::clientSaveEditModelByResult(int result)
{
	if (m_pCurEditActorBody)
		SANDBOX_DELETE(m_pCurEditActorBody);

	if (!m_pCurEditFcm)
		return;

	std::string skey = m_pCurEditFcm->getKey();
	auto iter = m_FCMResInfos.find(skey);

	if (iter != m_FCMResInfos.end())
	{
		if (result == SAVE_CLOSE_FCM_UI_SUCCESS)
		{
			m_pCurEditFcm->addVersion(1);

			iter->second.version += 1;
		}
	}

	m_pCurEditFcm->setEditing(false);
	if (result == CLOSE_FCM_UI_ERR)
	{
		removeEditFailModel();
		syncCloseUI2Host(CLOSE_EDIT_FCM_UI_TYPE::NORMAL_CLOSE);
	}

	m_pCurEditFcm = NULL;
}


bool FullyCustomModelMgr::loadCusMotion(std::string skey, int motionid/* =-1 */)
{
	if (!CustomMotionMgr::GetInstancePtr())
		return false;

	CustomMotion *pCustomMotion = CustomMotionMgr::GetInstancePtr()->getCustomMotion(skey);
	if (!pCustomMotion)
		return false;

	auto *fcm = getCurEditFullyCustomModel();
	if (!fcm)
		return false;

	if (motionid == -1)
		motionid = pCustomMotion->getMotionId();

	auto iter = pCustomMotion->m_MotionDatas.begin();
	for (; iter != pCustomMotion->m_MotionDatas.end(); iter++)
	{
		std::string boneName = iter->first;
		auto &cmd = iter->second;
		for (size_t i = 0; i < cmd.ticks.size(); i++)
		{
			if (m_pCurEditActorBody)
			{
				insertCustomKeyFrame(motionid, iter->first, cmd.ticks[i], 
					cmd.posoffsets[i], cmd.quats[i], cmd.scale3s[i]
				);
			}
			else
			{
				fcm->insertCustomKeyFrame(motionid, boneName.c_str(), cmd.ticks[i], 
					cmd.posoffsets[i], cmd.quats[i], cmd.scale3s[i]
				);
			}
		}
	}

	fcm->setCustomSequenceEndtime(motionid, pCustomMotion->getDuration());

	return true;
}

std::string FullyCustomModelMgr::saveCusMotion(int motionid/* =-1 */, std::string motionname/* ="" */)
{
	auto *fcm = getCurEditFullyCustomModel();
	if (!fcm)
		return "";

	return fcm->saveCusMotion(motionid, m_CurOWID, motionname, m_nSpecialType);
}



FullyCustomModel* FullyCustomModelMgr::getOrCreate(const std::string& skey, int eFcmImportType /*= NEW_NORMALE_FCM_MODEL*/)
{
	FullyCustomModel* fcm = nullptr;
	#if BUILD_MINI_EDITOR_APP
	{
		if (!skey.empty())
		{
			fcm = findFullyCustomModel(MAP_MODEL_CLASS, skey);
			if (!fcm)
			{
				fcm = findFullyCustomModel(EDITOR_MODEL_CLASS, skey);
			}
		}
	}
	#else
	{
		if (eFcmImportType == NEW_NORMALE_FCM_MODEL && !skey.empty())
			fcm = findFullyCustomModel(MAP_MODEL_CLASS, skey);
	}
	#endif

	if (!fcm)
	{
		fcm = createNew(skey, eFcmImportType);
	}

	if (fcm)
	{
		fcm->setEditing(true);
	}
	return fcm;
}

FullyCustomModel* FullyCustomModelMgr::createNew(const std::string& skey, int eFcmImportType /*= NEW_NORMALE_FCM_MODEL*/)
{
	OPTICK_EVENT();
	bool ok = false;
	int uinRealOwner = FCM_LOAD_IGNORE_CHECK_UIN;
	if (GetWorldManagerPtr())
		uinRealOwner = GetWorldManagerPtr()->getRealOwnerUin();
	std::string strRootPath = GetWorldRootBySpecialType(m_nSpecialType);

	FullyCustomModel* fcm = ENG_NEW(FullyCustomModel)();
	if (eFcmImportType == NEW_EXAMPLE_FCM_MODEL)
	{
		char szFcmPath[256] = { 0 };
		sprintf(szFcmPath, "entity/custommodel/fcmexample/%s.fcm", skey.c_str());
		ok = fcm->load(szFcmPath, skey, FCM_LOAD_IGNORE_CHECK_UIN);
		#if BUILD_MINI_EDITOR_APP
		{
			fcm->setKey(skey);
		}
		#endif
	}
	else if (eFcmImportType == NEW_COPY_FCM_MODEL && m_CurOWID > 0)
	{
		char szFcmPath[256] = { 0 };
		sprintf(szFcmPath, "%s/w%lld/custommodel/fully/%s.fcm", strRootPath.c_str(), m_CurOWID, skey.c_str());
		ok = fcm->load(szFcmPath, skey, uinRealOwner, NULL, true, NULL, m_nSpecialType);
		if (!ok)
		{
			sprintf(szFcmPath, "data/custommodel/fully/%s.fcm", skey.c_str());
			ok = fcm->load(szFcmPath, skey, uinRealOwner, NULL, true);
		}
		fcm->setKey("");
	}
	else if (eFcmImportType == NEW_COPY_PACKING_FCM_MODEL && m_CurOWID > 0)
	{
		char szFcmPath[256] = { 0 };
		sprintf(szFcmPath, "%s/w%lld/custommodel/fully/packing/%s.pfcm", strRootPath.c_str(), m_CurOWID, skey.c_str());
		ok = fcm->load(szFcmPath, skey, uinRealOwner, NULL, true, NULL, m_nSpecialType);
		if (!ok)
		{
			sprintf(szFcmPath, "data/custommodel/fully/packing/%s.pfcm", skey.c_str());
			ok = fcm->load(szFcmPath, skey, uinRealOwner, NULL, true);
		}
		fcm->packingFcmConvertFcm();
	}
	else if (eFcmImportType == EDITOR_FCM_MODEL)
	{
		//skey : ??????bin??????????ˇ????
		ok = fcm->load(skey, skey, FCM_LOAD_IGNORE_CHECK_UIN);
		fcm->setKey(skey);
	}
	#ifdef AR_DEMO
	else if (eFcmImportType == NEW_NORMALE_FCM_MODEL)
	{
		char szFcmPath[256] = { 0 };
		sprintf(szFcmPath, "entity/custommodel/fcmexample/ar_custom_model_standard5.fcm");
		ok = fcm->load(szFcmPath, skey, FCM_LOAD_IGNORE_CHECK_UIN);
	}
	#endif
	#if BUILD_MINI_EDITOR_APP
	if (!ok)
	{
		ENG_DELETE(fcm);
		return nullptr;
	}
	#endif
	if (!ok)
	{
		//NEED REVIEWBY: Zuokuan
		if (eFcmImportType != NEW_NORMALE_FCM_MODEL)
		{
			ENG_DELETE(fcm);
			return nullptr;
		}
	}
	

	fcm->setNeedSave(true);
	addFcm(fcm, eFcmImportType);
	return fcm;
}

FullyCustomModel* FullyCustomModelMgr::createEmpty()
{
	bool ok = false;
	FullyCustomModel* fcm = ENG_NEW(FullyCustomModel)();
	fcm->setNeedSave(true);
	return fcm;
}

FullyCustomModel* FullyCustomModelMgr::copyExample(const std::string& strKeyExample, const std::string& strKeySave, bool relativePath)
{
	FullyCustomModel* fcmExample = getOrCreate(strKeyExample, NEW_EXAMPLE_FCM_MODEL);
	if (!fcmExample)
	{
		return nullptr;
	}
	return copyAndSave(fcmExample, strKeySave, relativePath);
}

FullyCustomModel* FullyCustomModelMgr::copyAndSave(FullyCustomModel* fcmSrc, const std::string& skey, bool relativePath)
{
	if (!fcmSrc)
	{
		return nullptr;
	}
	FullyCustomModel* fcm = ENG_NEW(FullyCustomModel)();
	fcm->setName(fcmSrc->getName());
	fcm->setDesc(fcmSrc->getDesc());
	fcm->setKey(skey);
	fcm->setAuthUin(FCM_LOAD_IGNORE_CHECK_UIN);
	fcm->setAuthName(fcmSrc->getAuthName());
	fcm->setModelType(fcmSrc->getModelType());
	bool ok = fcm->cloneToSave(fcmSrc, skey, relativePath);
	if (!ok)
	{
		return nullptr;
	}
	ok = fcm->load(skey, skey, FCM_LOAD_IGNORE_CHECK_UIN);
	if (!ok)
	{
		return nullptr;
	}
	return fcm;
}


bool FullyCustomModelMgr::isEdited(std::string skey/* ="" */)
{
	if(skey.empty())
		return m_pCurEditFcm && m_pCurEditFcm->isEdited();

	auto fcm = findFullyCustomModel(MAP_MODEL_CLASS, skey);

	return fcm && fcm->isEdited();
}

std::string FullyCustomModelMgr::saveCurEditModel(IClientPlayer *player, FCD_SAVE_TYPE host, std::string name, std::string desc, int fcmSaveType)
{
	if (!m_pCurEditFcm)
		return "";

	std::string skey = m_pCurEditFcm->getKey();
	if (skey.empty())
	{
		char ckey[64] = { 0 };
#if defined(_WIN32)
		sprintf(ckey, "%d%I64dedit", player->getUin(), time(NULL));
#else
		sprintf(ckey, "%d%ldedit", player->getUin(), time(NULL));
#endif
		skey = ckey;
	}

	std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

	char dir[256] = {0};
	char path[256] = {0};
	if (host == HOST_SAVE)
	{
		if (m_CurOWID <= 0)
			return "";
		sprintf(dir, "%s/w%lld/custommodel/fully/", rootpath.c_str(), m_CurOWID);
		sprintf(path, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, skey.c_str());
	}		
	else if (host == CLIENT_SAVE)
	{
		sprintf(dir, "data/http/custommodels/");
		sprintf(path, "data/http/custommodels/%s.fcm", skey.c_str());
	}
		

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}		

	std::string makeKey = "";
	if (fcmSaveType >= CLOSE_EDIT_FCM_UI_TYPE::SAVE_AND_CREATE_BLOCK) //?????????????¨??????°?????????
	{
		char ckey[64] = { 0 };
		char makePath[256] = { 0 };
#if defined(_WIN32)
		sprintf(ckey, "%d%I64d", player->getUin(), time(NULL));
#else
		sprintf(ckey, "%d%ld", player->getUin(), time(NULL));
#endif
		makeKey = ckey;

		if (host == HOST_SAVE)
		{
			if (m_CurOWID <= 0)
				return "";

			sprintf(makePath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, makeKey.c_str());
		}
		else if (host == CLIENT_SAVE)
		{
			sprintf(makePath, "data/http/custommodels/%s.fcm", makeKey.c_str());
		}


		if (m_pCurEditFcm->save(makePath, makeKey, player->getUin(), player->getNickname(), name, desc, fcmSaveType))
		{
			FullyCustomModel *fullycustommodel = ENG_NEW(FullyCustomModel)();
			if (fullycustommodel->load(makePath, makeKey, player->getUin(), NULL, false, NULL, m_nSpecialType))
			{
				m_vMapFcms.push_back(fullycustommodel);
			}
			else
			{
				ENG_DELETE(fullycustommodel);
				return "";
			}
		}
		else
			return "";
	}
	else
		makeKey = skey;

	//?¤?????¨?????????????
	if (fcmSaveType == CLOSE_EDIT_FCM_UI_TYPE::SAVE_EXAMPLE && host == HOST_SAVE)
	{
		//char ckey[64] = { 0 };
		char examplePath[256] = { 0 };

		if (!GetFileManager().IsFileExistWritePath("entity/custommodel/fcmexample/"))
		{
			GetFileManager().CreateWritePathDir("entity/custommodel/fcmexample/");
		}
#if defined(_WIN32)
		sprintf(examplePath, "entity/custommodel/fcmexample/%I64d.fcm", time(NULL));
#else
		sprintf(examplePath, "entity/custommodel/fcmexample/%ld.fcm", time(NULL));
#endif

		m_pCurEditFcm->save(examplePath, "", -999, "", name, desc, fcmSaveType);
	}

	//????????????????¨??????°?????????
	if (m_pCurEditFcm->save(path, skey, player->getUin(), player->getNickname(), name, desc))
	{
		m_pCurEditFcm->setFileName(skey);
	}

	return makeKey;
}

bool FullyCustomModelMgr::saveEditModel(std::string skey, CLOSE_EDIT_FCM_UI_TYPE eFcmSaveType)
{
	if (!m_pCurEditFcm)
		return false;

	string name = "";
	string desc = "";
	string dir;
	string path;

	bool ok = false;
	switch (eFcmSaveType)
	{
	case CLOSE_EDIT_FCM_UI_TYPE::SAVE_EXAMPLE:
	{
		dir = "entity/custommodel/fcmexample/";
		path = "entity/custommodel/fcmexample/" + skey + ".fcm";
		if (!GetFileManager().IsFileExistWritePath(dir.c_str()))
		{
			GetFileManager().CreateWritePathDir(dir.c_str());
		}
		ok = m_pCurEditFcm->save(path, "", -999, "", name, desc, eFcmSaveType);
	}
	break;
	case CLOSE_EDIT_FCM_UI_TYPE::SAVE_EDITOR:
	{
		path = skey;
		ok = m_pCurEditFcm->save(path, "", -999, "", name, desc, eFcmSaveType);
	}
	break;

	}

	//????????????????¨??????°?????????
	if (ok)
	{
		m_pCurEditFcm->setFileName(skey);
	}

	return true;
}

void FullyCustomModelMgr::reEncryptFullyCustomModelData(long long owid, int olduin, int newuin, std::string authnickname, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommodel/fully/", rootpath.c_str(), owid);
	if (!gFunc_isStdioDirExist(dir)) { return; }

	char path[256];
	sprintf(path, "%s/w%lld/custommodel/fully", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();

		FullyCustomModel *fcm = ENG_NEW(FullyCustomModel)();
		std::string  filepath = path;
		filepath += "/" + filename + ".fcm";
		if (fcm->load(filepath, filename, olduin, NULL, false, NULL, specialType))
		{
			fcm->reEncrypt(owid, newuin, authnickname, specialType);
		}		

		ENG_DELETE(fcm);
	}

	if (m_pPackingFCMMgr)
		m_pPackingFCMMgr->reEncryptFullyCustomModelData(owid, olduin, newuin, authnickname, specialType);
}

FCM_Res_Info *FullyCustomModelMgr::checkVersionByClientEdit(std::string skey)
{
	auto iter = m_FCMResInfos.find(skey);
	if (iter != m_FCMResInfos.end() && !iter->second.downloadurl.empty())
	{
		auto *fullyCustomModel = FullyCustomModelMgr::GetInstancePtr()->findFullyCustomModel(MAP_MODEL_CLASS, skey);
		if (fullyCustomModel)
		{
			//上传的版本已经是最新的版本
			if (fullyCustomModel->getVersion() <= iter->second.version)
				return &iter->second;
			else
				m_FCMResInfos.erase(iter);
		}
	}
	return NULL;
}

void FullyCustomModelMgr::getResPath(int libtype, std::string filename, bool ispacking, char *path)
{
	if (libtype == MAP_LIB)
	{
		if (m_CurOWID <= 0)
			return;

		if (ispacking)
			sprintf(path, "data/w%lld/custommodel/fully/packing/%s.pfcm", m_CurOWID, filename.c_str());
		else
			sprintf(path, "data/w%lld/custommodel/fully/%s.fcm", m_CurOWID, filename.c_str());
	}
	else if (libtype == PUBLIC_LIB)
	{
		if (ispacking)
			sprintf(path, "data/custommodel/fully/packing/%s.pfcm", filename.c_str());
		else
			sprintf(path, "data/custommodel/fully/%s.fcm", filename.c_str());
	}
}

std::string FullyCustomModelMgr::copyEditModelFile(IClientPlayer* player, std::string srcSkey)
{
	if (srcSkey.empty() || srcSkey.find_last_of("edit") == std::string::npos)
		return "";

	static std::string prevKeySuffix("null");
	static int prevCount = 0;

	// 文件名
	std::string strKeySave;
	char ckey[64] = { 0 };
#if defined(_WIN32)
	sprintf(ckey, "%d%I64dedit", player->getUin(), time(NULL));
#else
	sprintf(ckey, "%d%ldedit", player->getUin(), time(NULL));
#endif

	// 前缀和上一个文件一样了，需要添加后缀
	if (strcmp(ckey, prevKeySuffix.c_str()) == 0)
	{
		prevKeySuffix = ckey;
		prevCount++;


		sprintf(ckey, "%s%d", prevKeySuffix.c_str(), prevCount);
		LOG_INFO("copy fcm : %s", ckey);
	}
	else
	{
		prevKeySuffix = ckey;
		prevCount = 0;
		LOG_INFO("copy fcm : %s", ckey);
	}
	strKeySave = ckey;

	// 路径
	std::string rootpath = "data";
	char dir[256] = { 0 };
	char path[256] = { 0 };
	char srcPath[256] = { 0 };
	if (m_CurOWID <= 0)
		return "";
	sprintf(dir, "%s/w%lld/custommodel/fully/", rootpath.c_str(), m_CurOWID);
	sprintf(path, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, strKeySave.c_str());
	sprintf(srcPath, "%s/w%lld/custommodel/fully/%s.fcm", rootpath.c_str(), m_CurOWID, srcSkey.c_str());
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	// 创建fcm
	FullyCustomModel* fcm = getOrCreate(strKeySave);
	if (!fcm)
	{
		return "";
	}

	// 拷贝数据到新的fcm
	bool ok = fcm->load(srcPath, srcSkey, player->getUin(), NULL, true, NULL, m_nSpecialType);
	if (!ok)
	{
		return "";
	}

	if (fcm->save(path, strKeySave, player->getUin(), player->getNickname(), "", ""))
	{
		fcm->setFileName(strKeySave);
	}

	fcm->setEditing(false);
	return strKeySave;
}