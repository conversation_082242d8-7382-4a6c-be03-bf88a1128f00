#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

namespace Rainbow 
{
	class SimpleFullScreenEffect;

	class SimpleFullScreenEffectRenderObject : public Rainbow::RenderObject
	{
	public:
		explicit SimpleFullScreenEffectRenderObject(SimpleFullScreenEffect* component);
		~SimpleFullScreenEffectRenderObject();

		virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;

	private:
		VertexLayout* m_VertexLayout;

	};
}




