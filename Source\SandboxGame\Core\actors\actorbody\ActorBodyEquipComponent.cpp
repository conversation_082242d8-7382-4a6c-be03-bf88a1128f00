#include "ActorBodyEquipComponent.h"
#include "BlockScene.h"
#include "WorldRender.h"
#include "ClientItem.h"
#include "SandBoxManager.h"

#include "ModelItemMesh.h"
#include "GameMode.h"
#include "OgreUtils.h"
#include "BlockMesh.h"
#include "special_blockid.h"
#include "PlayerControl.h"
#include "MpActorManager.h"
#include "GameCamera.h"
#include "CameraManager.h"

#include "RecordPkgManager.h"
#include "GameNetManager.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"

#include "ActorVillager.h"
#include "ImportCustomModelMgr.h"
#include "Pkgs/PkgUtils.h"
#include "Entity/LegacySequenceMap.h"

#include "PlayerAttrib.h"
#include "Texture/LegacyTextureUtils.h"

#include "CarryComponent.h"
#include "RiddenComponent.h"
#include "ClientActorFuncWrapper.h"
#include "backpack.h"
#include "ActorGeniusMgr.h"
#include "ActorFishingVillager.h"
#include "BlockMaterialMgr.h"
#include "CustomModel.h"

#include "Optick/optick.h"
#include "Entity/ModelRenderer.h"
#include "Entity/ModelAnimationPlayer.h"
#include "PlayerLocoMotion.h"
#include "UGCEntity.h"
#include "Plugin.h"
#include "SandboxGameDef.h"
#include "UgcAssetMgr.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

namespace MNSandbox
{
	static Rainbow::FixedString s_AvatarTexDirs[] = { "helmet", "breast", "legging", "shoe", "helmet_lining", "breast_lining", "legging_lining", "shoe_lining", "pifeng", "", "" };
	static Rainbow::FixedString s_EquitTexDirs[] = { "head", "breast", "leg", "shoe", "head-linling", "breast-linling", "leg-linling", "shoe-linling","back" };
	static Rainbow::FixedString s_DefaultSkinName = "dao1";

	static Rainbow::FixedString s_MontionName1 = "mianjufumo";
	static Rainbow::FixedString s_MontionName2 = "yunmojiamian";

	static const int MAX_ACTOR_SKINS = 12;
	static const int MAX_EQUIP_LEVEL = 8;
	static Rainbow::FixedString s_SkinName[MAX_EQUIP_SLOTS][MAX_ACTOR_SKINS + 1];
	static Rainbow::FixedString s_SkinEqpName[MAX_EQUIP_SLOTS][MAX_EQUIP_LEVEL + 1];
}

const Rainbow::FixedString& GetSkinName(int slot, int level) {
	Assert(slot >= 0 && slot < MAX_EQUIP_SLOTS);
	Assert(level <= MAX_ACTOR_SKINS);

	if (s_SkinName[slot][level].empty()) {
		char skinname[64];
		snprintf(skinname, 64, "%s%.2d", s_AvatarTexDirs[slot].c_str(), level);
		s_SkinName[slot][level] = skinname;
	}
	return s_SkinName[slot][level];
}

const Rainbow::FixedString& GetSkinEquipName(int slot, int level) {
	Assert(slot >= 0 && slot < MAX_EQUIP_SLOTS);
	Assert(level <= MAX_EQUIP_LEVEL);

	if (s_SkinEqpName[slot][level].empty()) {
		char skinname[64];
		snprintf(skinname, 64, "%s_eq_%d", s_EquitTexDirs[slot].c_str(), level);
		s_SkinEqpName[slot][level] = skinname;
	}
	return s_SkinEqpName[slot][level];
}

/**************************************校准部件信息**************************************
校准挂点的位置及角度, 如后背, 挂点和胸前一样, 只是位置和角度做了修正.
参数: relPartDef: 输出参数, 得到实际的位置、角度信息
	  partDef: 输入参数, 带校准的部件
*****************************************************************************************/
static void AdjustCustomEquipPartDef(EquipmentPartDef& relPartDef, const EquipmentPartDef partDef)
{
	relPartDef = partDef;

	//后背
	if (partDef.nAnchorID == 1105)
	{
		relPartDef.nAnchorID = 105;
		//relPartDef.pitch += 90;
		relPartDef.y += -25;
		relPartDef.z += -15;
	}
}

ActorBodyEquipComponent::ActorBodyEquipComponent(ActorBody* actorBody) :
	m_ActorBody(actorBody), m_WeaponModel(NULL), m_WeaponModel_left(NULL),
	m_HelmetModel(NULL)
{
	m_DorsumEntity = NULL;

	//TODO:
	m_UIEquipmentPartDef = NULL;
	for (int slot = 0; slot < MAX_EQUIP_SLOTS; slot++)
	{
		for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
		{
			m_CustomEquipPartModel[slot][i] = NULL;
			m_PartAnchorId[slot][i] = 0;
		}
		m_EquipsModel[slot] = nullptr;

		m_NormalEquipItemIDs[slot] = 0;
	}
}

ActorBodyEquipComponent::~ActorBodyEquipComponent()
{
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_HelmetModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel);
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel_left);
}



//因为clearEquipItems的循环中有大量FixedString的构造和析构开销
//所以这里用全局的FixedString表来减少开销
void ActorBody::clearEquipItems(Rainbow::Model* pmodel) {
	if (pmodel == NULL) return;
	pmodel->ShowSkin(s_DefaultSkinName, true);

	for (int slot = 0; slot < MAX_EQUIP_SLOTS - 1; slot++) {
		for (int i = 1; i <= MAX_ACTOR_SKINS; i++) {
			pmodel->ShowSkin(GetSkinName(slot, i), false);
		}

		if (slot <= EQUIP_SHOE_LINING) {
			for (int i = 1; i <= MAX_EQUIP_LEVEL; i++) {
				pmodel->ShowSkin(GetSkinEquipName(slot, i), false);
			}
		}
	}
}

void ActorBodyEquipComponent::getSkinPart(char* name, EQUIP_SLOT_TYPE slot, int itemid)
{
	if (name == NULL)
	{
		return;
	}
	int equipmtl = (itemid % 100) / 10; //0: 皮， 1：锁甲， 2：铁甲， 3：金甲， 4：钻石甲  
	//static int s_EquipMeshByMtl[] = { 2, 3, 1, 1, 1 };
	static int s_EquipMeshByMtl[] = { 2, 3, 1, 5, 6 };
	const auto length = sizeof(s_EquipMeshByMtl) / sizeof(s_EquipMeshByMtl[0]);
	int equipmesh = (itemid == 0 || equipmtl >= length) ? 0 : s_EquipMeshByMtl[equipmtl];
	//新增的特别处理一下 花环 11228 helmet07     黄铜防御装备	12216-12219 helmet04 ... 04 
	if (itemid == ITEM_GARLAND || itemid == ITEM_DIVING_SUIT_SUPER || itemid == ITEM_DIVING_FINS_SUPER || itemid == ITEM_WINTER_LEGGING)
		equipmesh = 7;
	else if (IsBrassEquipID(itemid))
		equipmesh = 4;
	else if (itemid == ITEM_DIVING_MASK)
		equipmesh = 10;
	else if (itemid == ITEM_DIVING_MASK_SUPER || itemid == ITEM_WINTER_CLOTHES || itemid == ITEM_WINTER_SHOE)
		equipmesh = 9;
	else if (itemid == ITEM_DIVING_SUIT || itemid == ITEM_DIVING_FINS)
		equipmesh = 8;
	else if (itemid == ITEM_GARLAND_PRO)
		equipmesh = 11;
	else if (itemid == ITEM_WINTER_HELMET)
		equipmesh = 12;

	ActorVillager* villager = dynamic_cast<ActorVillager*>(m_ActorBody->m_OwnerActor);
	if (villager)
	{
		std::string subname = villager->getSubModeName(slot);
		sprintf(name, "%s", subname.c_str());
	}
	else
	{
		do
		{
			if (nullptr != m_ActorBody->m_OwnerActor)
			{
				int id = m_ActorBody->m_OwnerActor->getDefID();
				// 野人战士和野人投矛手
				if ((id == 3101 || id == 3105) && (slot >= EQUIP_HEAD && slot <= EQUIP_SHOE_LINING))
				{
					getEquitMesh(itemid, equipmesh);
					if (equipmesh >= 1 && equipmesh <= MAX_EQUIP_LEVEL)
					{
						//sprintf(name, "%s_eq_%d", s_EquitTexDirs[slot].c_str(), equipmesh);
						sprintf(name, "%s", GetSkinEquipName(slot, equipmesh).c_str());
						break;
					}
				}
			}
			//sprintf(name, "%s%.2d", s_AvatarTexDirs[slot].c_str(), equipmesh);
			sprintf(name, "%s", GetSkinName(slot, equipmesh).c_str());
		} while (false);
	}
}

void ActorBodyEquipComponent::getTexPath(char* texpath, EQUIP_SLOT_TYPE slot, int itemid)
{
	if (texpath == NULL)
	{
		return;
	}
	int equip_texid = itemid % 10000;
	if (m_ActorBody->isPlayer() && (GetPlayerSex(m_ActorBody->getModelID()) == 1 || GetPlayerSex(m_ActorBody->getModelID()) == 3) && slot != EQUIP_PIFENG)
	{
		sprintf(texpath, "entity/player/share/women/%s/%d.png", s_AvatarTexDirs[slot].c_str(), equip_texid);
	}
	else
	{
		sprintf(texpath, "entity/player/share/%s/%d.png", s_AvatarTexDirs[slot].c_str(), equip_texid);
	}
}

void ActorBodyEquipComponent::clearEquipSlot(EQUIP_SLOT_TYPE slot)
{
	ActorVillager* villager = dynamic_cast<ActorVillager*>(m_ActorBody->m_OwnerActor);
	if (villager)
	{
		if (slot != EQUIP_SLOT_TYPE::EQUIP_PIFENG)
		{
			std::string name = "";
			for (int i = 0; i < 7; i++) // 隐藏装备道具
			{
				name = villager->getSubModeName(slot, 2, i + 1);
				villager->showSkin(name.c_str(), false); // 职业模型
			}
			name = villager->getSubModeName(slot, 1, villager->getProfession());
			villager->showSkin(name.c_str(), false); // 职业模型
			//villager->notifyProfressCloth(name,false);
			villager->showSkin(villager->getSubModeName(slot, 0).c_str(), false); // 隐藏基础模型
		}
	}
	else
	{
		if (!m_ActorBody->m_Entity) return;
		Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();

		// player12转换成装扮逻辑		by chenlong
		if (m_ActorBody->IsAvatarPlayer())
		{
			m_ActorBody->GetAvatarComponent()->ClearEquipAvatar(slot);
		}
		else
		{
			//char skinname[64];
			for (int i = 1; i <= MAX_ACTOR_SKINS; i++)
			{
				//sprintf(skinname, "%s%.2d", s_AvatarTexDirs[slot].c_str(), i);
				if (model) model->ShowSkin(GetSkinName(slot, i), false);

			}
			if (nullptr != m_ActorBody->m_OwnerActor)
			{
				int id = m_ActorBody->m_OwnerActor->getDefID();
				if ((id == 3101 || id == 3105) && (slot >= EQUIP_HEAD && slot <= EQUIP_SHOE_LINING))
				{
					// 基础装备
					for (int i = 1; i <= MAX_EQUIP_LEVEL; i++)
					{
						//sprintf(skinname, "%s_eq_%d", s_EquitTexDirs[slot].c_str(), i);
						if (model) model->ShowSkin(GetSkinEquipName(slot, i), false);
					}
				}
			}
		}
	}
}


void ActorBodyEquipComponent::getEquitMesh(int itemid, int& equipmesh)
{
	// 皮质
	if (itemid >= 12201 && itemid <= 12204)
	{
		equipmesh = 1;
	}
	// 锁链
	else if (itemid >= 12211 && itemid <= 12214)
	{
		equipmesh = 2;
	}
	// 秘银
	else if (itemid >= 12221 && itemid <= 12224)
	{
		equipmesh = 3;
	}
	// 黄铜
	else if (itemid >= 12216 && itemid <= 12219)
	{
		equipmesh = 4;
	}
	// 钛金
	else if (itemid >= 12231 && itemid <= 12234)
	{
		equipmesh = 5;
	}
	// 钨金
	else if (itemid >= 12241 && itemid <= 12244)
	{
		equipmesh = 6;
	}
	// 防寒
	else if (itemid >= 12312 && itemid <= 12315)
	{
		equipmesh = 8;
	}
}

//装备武器皮肤
bool ActorBodyEquipComponent::equipSkinWeapon(int itemid, int skinid)
{
	if (itemid <= 0) return false;

	if (!m_ActorBody->m_Entity) return false;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (!model) return false;

	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	if (def == NULL) return false;
	OPTICK_EVENT();
	BaseItemMesh* mesh = NULL;

	//如果是普通玩家
	if (m_ActorBody->m_MutateMob == 0)
	{
		if (m_WeaponModel)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_WeaponModel);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel);
		}
		if (m_WeaponModel_left)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_WeaponModel_left);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel_left);
		}
		//官方皮肤武器
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit(
			"WeaponSkin_System_CreateSkinWeaponModel",
			SandboxContext(nullptr)
			.SetData_Number("itemid", itemid)
			.SetData_Number("uin", m_ActorBody->m_OwnerPlayer ? m_ActorBody->m_OwnerPlayer->getUin() : 0)
			.SetData_Number("displayType", ITEM_MODELDISP_HAND)
			.SetData_Number("skinid", skinid)
		);

		m_NormalEquipItemIDs[EQUIP_WEAPON] = itemid;
		m_ActorBody->GetAvatarComponent()->SetEquipAvatarPart(EQUIP_WEAPON, itemid);

		if (result.IsExecSuccessed())
		{
			mesh = (BaseItemMesh*)result.GetData_Userdata("WeaponSkinModelItemMeshLua", "mesh");
		}

		if (m_ActorBody->m_Entity) //切换视角后武器皮肤不显示
		{
			m_ActorBody->m_Entity->BindObject(101, mesh);
		}
		else
		{
			DESTORY_GAMEOBJECT_BY_COMPOENT(mesh);
		}

		m_ActorBody->stopWeaponMotion(20000);
	}

	//如果是玩家空手或者野兽状态
	if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->GetMainModel()->ShowSkin("dao1", itemid == 0 || m_ActorBody->m_MutateMob > 0);

	if (mesh != NULL)
	{
		m_WeaponModel = mesh;
		if (m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->isHost() && m_ActorBody->m_OwnerPlayer->IsXrayEffectEnable())
			m_WeaponModel->SetXrayEffectEnable(true);
		return true;
	}

	return false;
}


void ActorBodyEquipComponent::equipWeaponItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	OPTICK_EVENT();
	Rainbow::UGCEntity* ugcEntity = NULL;
	//如果是普通玩家
	if (m_ActorBody->m_MutateMob == 0)
	{
		if (m_WeaponModel)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_WeaponModel);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel);
			//m_WeaponModel->release();
			//m_WeaponModel = NULL;
		}
		if (m_WeaponModel_left)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_WeaponModel_left);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_WeaponModel_left);
		}
		if (m_ActorBody->m_OwnerActor)
		{
			auto CarryComp = m_ActorBody->m_OwnerActor->getCarryComponent();
			if (itemid == ITEM_FOOTBALLWEAR || itemid == ITEM_BASKETBALLWEAR || (CarryComp && CarryComp->isCarrying()))
				itemid = 0;
			auto riddenComponent = m_ActorBody->m_OwnerActor->getRiddenComponent();
			if (riddenComponent && riddenComponent->isVehicleController())
				itemid = 0;
		}

		m_NormalEquipItemIDs[EQUIP_WEAPON] = itemid;
		m_ActorBody->GetAvatarComponent()->SetEquipAvatarPart(EQUIP_WEAPON, itemid);

		do {
			if (itemid > 0)
			{
				//玩家在使用发射器时，不显示手中模型
				if (m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->getUsingEmitter())
				{
					break;
				}

				const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
				if (def == NULL) return;
				//float scale = def->WieldScale;
				//if(scale == 0) scale = 1.0f;
				PlayerAttrib* attr = NULL;
				if (m_ActorBody->m_OwnerPlayer)
				{
					attr = m_ActorBody->m_OwnerPlayer->getPlayerAttrib();
				}

				if (ITEM_TYPE_VEHICLE == def->Type && attr)
				{
					m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1.0f, 0, NORMAL_MESH, attr->getEquipGrid(EQUIP_WEAPON)->getUserdataStr());
				}
				else if (IsHookBlockID(itemid) && m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->getHookObj())
				{
					m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1.0f, 1);
				}
				else if (itemid == ITEM_SPRINKLER && attr)
				{
					//带水花洒 剩余次数存放在userdataInt
					int texIndex = 5;
					int leftCount = attr->getEquipGrid(EQUIP_WEAPON)->getUserDataInt();
					MINIW::ScriptVM::game()->callFunction("GetSprinkleModelTextureIndex", "i>i", leftCount, &texIndex);
					m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1.0f, texIndex);
				}
				else if (itemid == ITEM_COLOR_BRUSH)
				{
					if (m_ActorBody->m_OwnerPlayer)
					{
						m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1, 0, NORMAL_MESH);
						ModelItemMesh* itemmodel = dynamic_cast<ModelItemMesh*>(m_WeaponModel);
						if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
						{
							auto mainMesh = itemmodel->getEntity()->GetMainModel()->GetIthSkin(1);
							if (mainMesh) {
								mainMesh->show(true);
								char path[512];
								sprintf(path, "itemmods/%d/texture%d.png", itemid, 1);
								auto  tex = GetAssetManager().LoadAsset<Rainbow::Texture2D>(path /*, RLF_CONVERT_BIT16 */);
								if (tex) {
									mainMesh->SetTexture("g_DiffuseTex", tex);
								}

								auto color = m_ActorBody->m_OwnerPlayer->getSelectedColor();
								Rainbow::ColorQuad cq(color);
								ColorRGBAf rgb(cq.r / 255.0f, cq.g / 255.0f, cq.b / 255.0f, 1.0f);
								mainMesh->SetOverlayColor(rgb);
							}
						}
					}
				}
				else
				{
					BackPackGrid* grid = NULL;
					if (m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->getBackPack())
					{
						grid = m_ActorBody->m_OwnerPlayer->getBackPack()->index2Grid(m_ActorBody->m_OwnerPlayer->getCurShortcut() + m_ActorBody->m_OwnerPlayer->getShortcutStartIndex());
					}

					if (grid != NULL)
					{
						if (isDoubleWeapon(itemid))//双持武器ID
						{
							const CraftingDef* craftDef = GetDefManagerProxy()->findCrafting(itemid);
							if (craftDef && craftDef->MaterialID[0])
								m_WeaponModel = ClientItem::createItemModel(craftDef->MaterialID[0], ITEM_MODELDISP_HAND, 1, 0, NORMAL_MESH, grid->getUserdataStr());
							if (craftDef && craftDef->MaterialID[1])//第三人称视角左手武器
								m_WeaponModel_left = ClientItem::createItemModel(craftDef->MaterialID[1], ITEM_MODELDISP_HAND, 1, 0, NORMAL_MESH, grid->getUserdataStr());
						}
						else
						{
							m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1, 0, NORMAL_MESH, grid->getUserdataStr());
						}
					}
					else
					{
						if (isDoubleWeapon(itemid))//双持武器ID
						{
							const CraftingDef* craftDef = GetDefManagerProxy()->findCrafting(itemid);
							if (craftDef && craftDef->MaterialID[0])
								m_WeaponModel = ClientItem::createItemModel(craftDef->MaterialID[0], ITEM_MODELDISP_HAND);
							if (craftDef && craftDef->MaterialID[1])//第三人称视角左手武器
								m_WeaponModel_left = ClientItem::createItemModel(craftDef->MaterialID[1], ITEM_MODELDISP_HAND);
						}
						else
						{
							m_WeaponModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND);
						}

					}

					if (m_ActorBody->m_IsInUI && (def->MeshType == BLOCK_GEN_MESH || def->MeshType == 3) && m_WeaponModel->IsKindOf<BlockMesh>())
					{
						BlockMesh* blockmesh = static_cast<BlockMesh*>(m_WeaponModel);
						blockmesh->SetUseBlockVertexLight(0.0f);
					}
				}

				// todo ????????? m_WeaponModel ???????????????????
				//if (m_WeaponModel->GetScene() == nullptr && GetWorld().GetCurrentGameScene())
				//{
					//m_WeaponModel->AttachToScene(GetWorld().GetCurrentGameScene());
				//}
				if (m_WeaponModel)
				{
					if (def->MeshType == CUSTOM_GEN_MESH)
					{
						if (def->Icon == "customitem")
						{
							if (CustomModelMgr::GetInstancePtr() && CustomModelMgr::GetInstancePtr()->isBlockCM(def->Model.c_str()))
							{
							}
							else
							{
								float scale = GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.tpp_scale;
								m_WeaponModel->SetScale(Vector3f(scale, scale, scale));
								m_WeaponModel->SetPosition(WorldPos(GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.tpp_x, \
									GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.tpp_y, \
									GetWorldManagerPtr()->m_SurviveGameConfig->custommodelconfig.tpp_z));// Rainbow::Vector3f center(33, 30, 9);
							}
						}
						else if (def->Icon == "customegg")
						{
							m_WeaponModel->SetScale(Vector3f(0.5f, 0.5f, 0.5f));
						}
						if (m_WeaponModel)
						{
							ModelItemMesh* itemmodel = dynamic_cast<ModelItemMesh*>(m_WeaponModel);
							if (itemmodel && itemmodel->getEntity() && itemmodel->getEntity()->GetMainModel())
								itemmodel->getEntity()->GetMainModel()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
						}
					}
					else if (FULLY_CUSTOM_GEN_MESH == def->MeshType)
					{
						if (def->Icon == "fullycustomitem" || def->Icon == "fullycustomegg")
						{
							m_WeaponModel->SetScale(Vector3f(0.6f, 0.6f, 0.6f));
						}
					}
					else if (VEHICLE_GEN_MESH == def->MeshType)
					{
						m_WeaponModel->SetScale(Vector3f(0.5f, 0.5f, 0.5f));
						m_WeaponModel->SetPosition(Rainbow::Vector3f(-20, 0, -20));
					}
					else if (GetDefManagerProxy()->isFishNeedUp(itemid))
					{
						if (m_WeaponModel)
						{
							if (itemid == ITEM_HIPPOCAMPUS)
							{
								m_WeaponModel->SetScale(1.0f);
								m_WeaponModel->SetRotation(0, 90, 90);
								m_WeaponModel->SetPosition(WorldPos(1500, 2300, 0));
							}
							else if (itemid == ITEM_SMALL_HIPPOCAMPUS)
							{
								m_WeaponModel->SetScale(1.0f);
								m_WeaponModel->SetRotation(90, 90, -30);
								m_WeaponModel->SetPosition(WorldPos(-550, 1900, 200));
							}
							else if (itemid == ITEM_CRAB)
							{
								ActorFishingVillager* villager = dynamic_cast<ActorFishingVillager*>(m_ActorBody->m_OwnerActor);
								if (villager)
								{
									m_WeaponModel->SetScale(0.8f);
									m_WeaponModel->SetPosition(WorldPos(-500, 200, 0));
								}
								else
								{
									m_WeaponModel->SetScale(1.5f);
									m_WeaponModel->SetRotation(90, 0, 90);
									m_WeaponModel->SetPosition(WorldPos(0, 1900, 0));
								}
							}
							else if (itemid == 13629)
							{
								m_WeaponModel->SetScale(0.5f);
								m_WeaponModel->SetRotation(90, 0, 0);
								m_WeaponModel->SetPosition(WorldPos(-500, 1800, 0));
							}
							else if (itemid == 13602)//深海鱼
							{
								m_WeaponModel->SetScale(1.0f);
								m_WeaponModel->SetRotation(90, 0, 0);
								m_WeaponModel->SetPosition(WorldPos(0, 1800, 0));
							}
							else if (itemid == 13630 || itemid == 13626)//魔鬼鱼，荧光棒藻鱼
							{
								m_WeaponModel->SetScale(0.5f);
								m_WeaponModel->SetRotation(90, 0, 0);
								m_WeaponModel->SetPosition(WorldPos(0, 1800, 0));
							}
							else
							{
								m_WeaponModel->SetScale(1.0f);
								m_WeaponModel->SetRotation(90, 0, 0);
								m_WeaponModel->SetPosition(WorldPos(0, 1800, 0));
							}
						}
					}
					else if (IMPORT_MODEL_GEN_MESH == def->MeshType)
					{
						if (m_WeaponModel)
						{
							Vector3f vec3 = m_WeaponModel->GetScale();
							if (vec3.x > 0.5f)
							{
								m_WeaponModel->SetScale(Vector3f(0.5f, 0.5f, 0.5f));
							}
						}
					}
					else if (IsBowItem(itemid & 0xffff))
					{
						if (m_WeaponModel) m_WeaponModel->SetPosition(WorldPos(-200, 0, 100));
					}
					else if (itemid == ITEM_RPG18)
					{
						if (m_WeaponModel)
						{
							m_WeaponModel->SetScale(Vector3f(1.2f, 1.2f, 1.2f));
							m_WeaponModel->SetPosition(WorldPos(100, 200, 100));
						}
					}
					else if (def->ID == ITEM_ACTORMODELCRAFT)
					{
						if (m_WeaponModel)
						{
							m_WeaponModel->SetScale(Vector3f(0.5f, 0.5f, 0.5f));
						}
					}
					else
					{
						const ItemInHandDef* inHandDef = GetDefManagerProxy()->getItemInHandDef(itemid);
						if (!inHandDef)
						{
							inHandDef = GetDefManagerProxy()->getItemInHandDef(-1);  // 默认道具角度
						}
						if (inHandDef)
						{
							m_WeaponModel->SetPosition(WorldPos(inHandDef->RightHandItemPosData.Pos.x, inHandDef->RightHandItemPosData.Pos.y, inHandDef->RightHandItemPosData.Pos.z));// Rainbow::Vector3f center(33, 30, 9);
							m_WeaponModel->SetRotation(inHandDef->RightHandItemPosData.Rot.x, inHandDef->RightHandItemPosData.Rot.y, inHandDef->RightHandItemPosData.Rot.z);
							if (m_WeaponModel_left)
							{
								m_WeaponModel_left->SetPosition(WorldPos(inHandDef->LeftHandItemPosData.Pos.x, inHandDef->LeftHandItemPosData.Pos.y, inHandDef->LeftHandItemPosData.Pos.z));// Rainbow::Vector3f center(33, 30, 9);
								m_WeaponModel_left->SetRotation(inHandDef->LeftHandItemPosData.Rot.x, inHandDef->LeftHandItemPosData.Rot.y, inHandDef->LeftHandItemPosData.Rot.z);
							}
						}
					}
					//如果是鱼竿的话, 第三人称下改变角度
					if (m_WeaponModel)
					{
						const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
						if (tooldef && tooldef->IsModFishRod())
						{
							auto lua_const = GetLuaInterfaceProxy().get_lua_const();
							if (lua_const)
							{
								m_WeaponModel->SetRotation(lua_const->fishAnimYaw, lua_const->fishAnimPitch, lua_const->fishAnimRoll);
							}
						}
					}

					if (m_ActorBody->m_Entity && m_ActorBody->m_Entity->IsShow())
					{
						if (GetDefManagerProxy()->isFishNeedUp(itemid))
						{
							m_ActorBody->m_Entity->BindObject(0, m_WeaponModel);
						}
						else
						{
							auto itemInHandDef = GetDefManagerProxy()->getItemInHandDef(itemid);
							if (itemInHandDef && itemInHandDef->TPSSlotId > 0)
							{
								m_ActorBody->m_Entity->BindObject(itemInHandDef->TPSSlotId, m_WeaponModel);
							}
							else
							{
								m_ActorBody->m_Entity->BindObject(101, m_WeaponModel);
							}
							if (m_WeaponModel_left)
							{
								m_ActorBody->m_Entity->BindObject(100, m_WeaponModel_left);
							}
						}
					}

					m_ActorBody->stopWeaponMotion(20000);
					if (def->HandEffect[0])
					{
						if (def->HandEffectScale < 0.001f)
						{
							m_ActorBody->playWeaponMotion(def->HandEffect, true, 20000, 1.0f);
						}
						else
						{
							m_ActorBody->playWeaponMotion(def->HandEffect, true, 20000, def->HandEffectScale);
						}
					}
				}

			}
		} while (false);

	}

	//异步加载需要重新设置show
	if (m_WeaponModel && m_WeaponModel->IsKindOf<BaseItemMesh>())
	{
		BaseItemMesh* baseItemMesh = m_WeaponModel;
		if (baseItemMesh && baseItemMesh->isUGCEntity() && baseItemMesh->getUGCEntity())
		{
			Rainbow::UGCEntity* ugcEntity = dynamic_cast<Rainbow::UGCEntity*>(baseItemMesh->getUGCEntity());
			MNSandbox::WeakRef<MNSandbox::Ref> self = ugcEntity->GetWeakRef();
			ugcEntity->FinishCallback([this, self](bool success) {
				if (!self || !success)
					return;
				if (m_WeaponModel->IsKindOf<ModelItemMesh>())
					static_cast<ModelItemMesh*>(m_WeaponModel)->SetShow(m_ActorBody->m_bIsShow);
				});
		}
	}

	//如果是玩家空手或者野兽状态
	m_ActorBody->m_Entity->GetMainModel()->ShowSkin("dao1", itemid == 0 || m_ActorBody->m_MutateMob > 0);

	if (itemid > 0 && m_WeaponModel && m_ActorBody->m_OwnerPlayer)
	{
		MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerGlobalData_PlayerHandEffect",
			MNSandbox::SandboxContext(nullptr).SetData_Number("uin", m_ActorBody->m_OwnerPlayer->getUin()).SetData_Number("view", 1));
	}

	if (m_WeaponModel && m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->isHost() && m_ActorBody->m_OwnerPlayer->IsXrayEffectEnable())
		m_WeaponModel->SetXrayEffectEnable(true);
}

void ActorBodyEquipComponent::equipPifengItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	//先脱掉自定义装备
	takeoffCustomEquip(slot);
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (!model) return;
	bool partStatus = false;
	MINIW::ScriptVM::game()->callFunction("GetAvatarPartStatus", "ii>b", 0, 8, &partStatus);
	if (partStatus)
		return;

	if (itemid == 0)
	{
		clearEquipSlot(slot);
		//m_Model->ShowSkin(skinname, false);
		if (m_DorsumEntity)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
		}
		return;
	}

	if (m_ActorBody->isPlayer())
	{
		m_NormalEquipItemIDs[slot] = itemid;

		if (m_DorsumEntity)
		{
			char modelPath[128];
			sprintf(modelPath, "itemmods/%d/body.omod", itemid);
			if (m_DorsumEntity->GetResPath() != modelPath)
			{
				if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
				DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
			}
		}

		if (m_ActorBody->IsAvatarPlayer())
		{
			clearEquipSlot(slot);
		}

		if (IsMantleID(itemid))
		{
			if (m_ActorBody->canPlayerWearEquip())
			{
				if (m_ActorBody->IsAvatarPlayer())
				{
					m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
				}
				else
				{
					char texpath[256];
					if (itemid == ITEM_OXYGEN_MASK) // 
					{
						model->ShowSkin("pifeng03", true);
						getTexPath(texpath, slot, itemid);
						model->SetSkinTexture("pifeng03", texpath);
					}
					else
					{
						model->ShowSkin("pifeng01", true);
						getTexPath(texpath, slot, itemid);
						model->SetSkinTexture("pifeng01", texpath);
					}
				}
			}
		}
		// common part
		else if (itemid == ITEM_JETPACK
			|| itemid == ITEM_FIRE_ROCKET
			|| itemid == ITEM_FIRESAFETY_PACK
			|| itemid == ITEM_OXYGEN_PACK
			|| itemid == ITEM_SNAKEGOD_WING)
		{
			if (m_ActorBody->m_MutateMob > 0)
				return;

			model->ShowSkin("pifeng01", false);

			if (!m_DorsumEntity)
			{
				int anchorid = 105;
				char modelPath[128];
				sprintf(modelPath, "itemmods/%d/body.omod", itemid);
				m_DorsumEntity = g_BlockMtlMgr.getEntity(modelPath);
				m_DorsumEntity->SetRotation(0, 90, 0);
				m_DorsumEntity->SetPosition(WorldPos(0, -250, -150));
				m_DorsumEntity->GetMainModel()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
				if (model->IsShow())
				{
					if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->BindObject(anchorid, m_DorsumEntity);
				}
			}

			// special part
			if (itemid == ITEM_FIRE_ROCKET)
			{
				if (m_DorsumEntity)
				{
					m_DorsumEntity->PlayMotion("Chupinazo");
					m_DorsumEntity->SetScale(Rainbow::Vector3f(0.6f, 0.6f, 0.6f));
				}
			}
			else if (itemid == ITEM_SNAKEGOD_WING)
			{
				if (m_DorsumEntity)
				{
					m_DorsumEntity->PlayMotion("item_11591");
					m_DorsumEntity->SetScale(Rainbow::Vector3f(0.6f, 0.6f, 0.6f));
				}
			}
		}
		else
		{
			if (m_ActorBody->IsAvatarPlayer())
			{
				m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
			}
		}
	}
}

void ActorBodyEquipComponent::equipPifengSpecial(EQUIP_SLOT_TYPE slot, int itemid)
{
	if (!m_ActorBody->m_Entity) return;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (!model) return;
	bool partStatus = false;
	MINIW::ScriptVM::game()->callFunction("GetAvatarPartStatus", "ii>b", 0, 8, &partStatus);
	if (partStatus)
		return;

	if (itemid == 0)
	{
		clearEquipSlot(slot);
		//m_Model->ShowSkin(skinname, false);
		if (m_DorsumEntity)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
		}
		return;
	}
	m_NormalEquipItemIDs[slot] = itemid;

	if (m_ActorBody->IsAvatarPlayer())
	{
		clearEquipSlot(slot);
	}

	if (IsMantleID(itemid))
	{
		if (m_DorsumEntity)
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
		}
		//默认假人都能穿
		if (true)
		{
			if (m_ActorBody->IsAvatarPlayer())
			{
				m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
			}
			else
			{
				char texpath[256];
				if (itemid == ITEM_OXYGEN_MASK) // 
				{
					model->ShowSkin("pifeng03", true);
					getTexPath(texpath, slot, itemid);
					model->SetSkinTexture("pifeng03", texpath);
				}
				else
				{
					model->ShowSkin("pifeng01", true);
					getTexPath(texpath, slot, itemid);
					model->SetSkinTexture("pifeng01", texpath);
				}
			}
		}
	}
	else if (itemid == ITEM_JETPACK
		|| itemid == ITEM_FIRE_ROCKET
		|| itemid == ITEM_FIRESAFETY_PACK
		|| itemid == ITEM_OXYGEN_PACK
		|| itemid == ITEM_SNAKEGOD_WING
		)
	{
		model->ShowSkin("pifeng01", false);

		if (m_DorsumEntity)
		{
			char modelPath[128];
			sprintf(modelPath, "itemmods/%d/body.omod", itemid);
			if (m_DorsumEntity->GetResPath() == modelPath)
			{
				if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
				DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
			}
		}


		if (m_DorsumEntity == NULL)
		{
			int anchorid = 105;
			char modelPath[128];
			sprintf(modelPath, "itemmods/%d/body.omod", itemid);
			m_DorsumEntity = g_BlockMtlMgr.getEntity(modelPath);
			if (m_DorsumEntity == NULL) return;
			m_DorsumEntity->SetRotation(0, 90, 0);
			m_DorsumEntity->SetPosition(WorldPos(0, -250, -150));
			m_DorsumEntity->GetMainModel()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));
			if (model->IsShow())
			{
				if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->BindObject(anchorid, m_DorsumEntity);
			}
		}

		// special part
		if (itemid == ITEM_FIRE_ROCKET)
		{
			if (m_DorsumEntity)
			{
				m_DorsumEntity->PlayMotion("Chupinazo");
				m_DorsumEntity->SetScale(Rainbow::Vector3f(0.6f, 0.6f, 0.6f));
			}
		}
		else if (itemid == ITEM_SNAKEGOD_WING)
		{
			if (m_DorsumEntity)
			{
				m_DorsumEntity->PlayMotion("item_11591");
				m_DorsumEntity->SetScale(Rainbow::Vector3f(0.6f, 0.6f, 0.6f));
			}
		}
	}
}

void ActorBodyEquipComponent::equipShoeItem(EQUIP_SLOT_TYPE slot, int itemid, char* skinname)
{
	m_NormalEquipItemIDs[slot] = itemid;
	if (!m_ActorBody->m_Entity)
	{
		return;
	}
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (!model)
	{
		return;
	}
	if (nullptr != m_ActorBody->m_OwnerActor)
	{
		int id = m_ActorBody->m_OwnerActor->getDefID();

		// 野人
		// ???
		if (id == 3200 || id == 3201 || id == 3202)
		{
			if (itemid >= 0)
			{
				clearEquipSlot(slot);
				model->ShowSkin(skinname, true);

				// 戴花冠的时候显示头发 code-by: liya
				if (itemid == ITEM_GARLAND || itemid == ITEM_GARLAND_PRO)
				{
					ActorVillager* villager = dynamic_cast<ActorVillager*>(m_ActorBody->m_OwnerActor);
					if (villager && villager->getDef())
					{
						//驯服的野人
						if (villager->getDef()->Model == "100056" || villager->getDef()->Model == "100057" || villager->getDef()->Model == "100058")
						{
							char skname[256];
							int hid = villager->getDefID() == 3201 ? villager->getHairId() + 3 : villager->getHairId();
							sprintf(skname, "head_base_%d", hid);
							model->ShowSkin(skname, true);
						}
					}
				}
			}
			return;
		}
	}

	 if (slot == EQUIP_HEAD)
	{
		if (m_HelmetModel)
		{
			if (m_ActorBody->m_Entity)
			{
				m_ActorBody->m_Entity->UnbindObject(m_HelmetModel);
				m_ActorBody->m_Entity->StopMotion(s_MontionName2);
				m_ActorBody->m_Entity->StopMotion(s_MontionName1);
			}
			DESTORY_GAMEOBJECT_BY_COMPOENT(m_HelmetModel);
		}

		if (itemid == 11232) //陨魔面具
		{
			float scale = 1;
			if (IsVoidMelonBlock(itemid))
			{
				MINIW::ScriptVM::game()->callFunction("BlockVoidMelon_GetScale", ">f", &scale);
			}
			clearEquipSlot(slot);
			m_HelmetModel = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, scale);

			if (m_ActorBody->m_Entity)
			{
				if (model->IsShow())
				{
					m_ActorBody->m_Entity->BindObject(106, m_HelmetModel);
				}
				if (itemid == 11232)
				{
					m_ActorBody->m_Entity->PlayMotion(s_MontionName1, false);
					m_ActorBody->m_Entity->PlayMotion(s_MontionName2, false);
				}
			}
			return;
		}
	}

	//是猪头怪或者没有皮肤的玩家
	if (m_ActorBody->canPlayerWearEquip() || !m_ActorBody->isPlayer())
	{
		char texpath[256];
		clearEquipSlot(slot);

		if (itemid == 0)
		{
			//Show body part
			model->ShowSkin(skinname, true);
			return;
		}

		if (m_ActorBody->IsAvatarPlayer())
		{
			m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
		}
		else
		{
			model->ShowSkin(skinname, true);
			getTexPath(texpath, slot, itemid);
			model->SetSkinTexture(skinname, texpath);

			//Hide body part
			// 戴花环不需要隐藏 code-by: liya
			if (slot != EQUIP_HEAD || itemid != ITEM_GARLAND || itemid != ITEM_GARLAND_PRO)
			{
				//sprintf(skinname, "%s%.2d", s_AvatarTexDirs[slot].c_str(), 0);
				model->ShowSkin(GetSkinName(slot, 0), false);
			}
			else
			{
				// 把头上的东西显示出来
				//sprintf(skinname, "%s%.2d", s_AvatarTexDirs[EQUIP_HEAD].c_str(), 0);
				model->ShowSkin(GetSkinName(EQUIP_HEAD, 0), true);
			}
		}
	}
}

void ActorBodyEquipComponent::setEquipItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	if (!m_ActorBody->m_Entity) return;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (model == NULL) return;
	char skinname[64];
	getSkinPart(skinname, slot, itemid);

	if (m_ActorBody->m_takeoffAble)
	{
		//先脱掉自定义装备
		takeoffCustomEquip(slot);
	}
	m_ActorBody->m_takeoffAble = true;

	if (slot == EQUIP_WEAPON)
	{
		bool bUseSkin = equipSkinWeapon(itemid);
		if (!bUseSkin) equipWeaponItem(slot, itemid);

		//新枪械部件组装
		//if (m_WeaponModel && m_WeaponModel->IsKindOf<ModelItemMesh>() && m_OwnerPlayer)
		//{
		//	m_OwnerPlayer->AssembleGunPartModel(dynamic_cast<ModelItemMesh*>(m_WeaponModel));
		//}

		//新枪械调整相对 位置、旋转、缩放  下期做 TODO
		if (m_ActorBody->m_OwnerPlayer && m_ActorBody->m_OwnerPlayer->getCustomGunDef() && m_WeaponModel)
		{
			//const CustomGunDef* modGunDef = m_OwnerPlayer->getCustomGunDef();
			//Vector3f originPos = m_WeaponModel->GetPositionV3();
			//Vector3f originEuler = m_WeaponModel->GetEulerAngle();
			//Vector3f originScale = m_WeaponModel->GetScale();
			//m_WeaponModel->SetPosition(Vector3f(originPos.x, originPos.y, originPos.z));
			//m_WeaponModel->SetEulerAngle(Vector3f(originEuler.x, originEuler.y, originEuler.z));
			//m_WeaponModel->SetScale(Vector3f(originScale.x, originScale.y, originScale.z));
		}
	}
	else if (slot == EQUIP_PIFENG)
	{
		equipPifengItem(slot, itemid);
	}
	//else if (slot == EQUIP_HEAD || slot == EQUIP_HEAD_LINING)
	//{
	//	equipNormalItem(slot, itemid);
	//}
	else if (slot > EQUIP_NONE && slot < EQUIP_PIFENG)//if (slot == EQUIP_SHOE || slot == EQUIP_LEGGING || slot == EQUIP_BREAST || slot == EQUIP_HEAD)
	{
		equipShoeItem(slot, itemid, skinname);
	}
	//else
	//{
	//	equipNormalItem(slot, itemid);
	//}
	// else if (isCustomEquip(itemid))
	// {
	// 	ActorGeniusMgr* geniusMgr = GET_SUB_SYSTEM(ActorGeniusMgr);
	// 	// bool isOldRoleSkin = geniusMgr->isOldRoleSkin(m_ActorBody->getSkinID());
	// 	//没使用皮肤和avator才显示部件模型
	// 	// if (!((isPlayer() && getSkinID() > 0) || m_BodyType == 3 || m_MutateMob > 0) || isOldRoleSkin)
	// 	if (!m_ActorBody->m_MutateMob)
	// 		setCustomEquip(slot, itemid);
	// }
	//
	// else if (slot == EQUIP_SHOE || slot == EQUIP_LEGGING || slot == EQUIP_BREAST || slot == EQUIP_HEAD)
	// {
	// 	equipShoeItem(slot, itemid, skinname);
	// }

}


//int Slot2AnchorId[MAX_EQUIP_SLOTS] = {106,105,112,102,105,100};
std::string Slot2BoneName[MAX_EQUIP_SLOTS] = { "B_Head","B_Spine1","B_R_Calf","B_R_Foot","B_Head","B_Spine1","B_R_Calf","B_R_Foot","B_Pelvis","B_R_Hand"};

void ActorBodyEquipComponent::equipNormalItem(EQUIP_SLOT_TYPE slot, int itemid)
{
	auto entity = m_ActorBody->m_Entity;
	if (!entity) return;

	auto itemModel = m_EquipsModel[slot];
	if (itemModel)
	{
		entity->UnbindObject(itemModel);
	}
	m_EquipsModel[slot] = nullptr;

	auto model = ClientItem::createItemModel(itemid, ITEM_MODELDISP_HAND, 1);
	if (model)
	{
		m_EquipsModel[slot] = model;
		model->SetRotation(0, -90, 90);
		auto anchorid = Slot2BoneName[slot];
		entity->BindObject(anchorid, model);
	}
}



//根据tooldef->Type得到slot
static EQUIP_SLOT_TYPE GetSlotTypeByItemId(int itemid)
{
	EQUIP_SLOT_TYPE slot = MAX_EQUIP_SLOTS;
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(itemid);
	if (tooldef)
	{
		if (tooldef->Type == 8)
			slot = EQUIP_HEAD;
		else if (tooldef->Type == 9)
			slot = EQUIP_BREAST;
		else if (tooldef->Type == 10)
			slot = EQUIP_LEGGING;
		else if (tooldef->Type == 11)
			slot = EQUIP_SHOE;
		else if (tooldef->Type == 16)
			slot = EQUIP_PIFENG;
		else if (tooldef->Type == 35)
			slot = EQUIP_HEAD_LINING;
		else if (tooldef->Type == 36)
			slot = EQUIP_BREAST_LINING;
		else if (tooldef->Type == 37)
			slot = EQUIP_LEGGING_LINING;
		else if (tooldef->Type == 38)
			slot = EQUIP_SHOE_LINING;
	}

	return slot;
}

//是否为自定义装备
bool ActorBodyEquipComponent::isCustomEquip(int itemId)
{
	//TODO:
	//return false;

	if (itemId > 0)
	{
		const ItemDef* def = GetDefManagerProxy()->getItemDef(itemId);
		//if (def && def->CopyID == ITEM_EQUIT_TEMPLATE)
		//{
		//	return true;
		//}

		if (def)
		{
			int equipTemplateIDs[6] = { 12226, 12227, 12228, 12229, 12230, 10114 };
#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
			for (int i = 0; i < sizeof(equipTemplateIDs) / sizeof(int); ++i)
			{
				const int id = equipTemplateIDs[i];
				if (id == def->CopyID)
					return true;
			}
#else
			for (int i = 0; i < 6; i++)
			{
				if (equipTemplateIDs[i] == def->CopyID)
					return true;
			}
#endif//OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
		}
	}

	return false;
}

//???????????B
void ActorBodyEquipComponent::setCustomEquip(EQUIP_SLOT_TYPE slot, int itemid)
{
	//const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);
	ItemEquipDef* equipDef = nullptr;
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_tryGetItemEquipDef", SandboxContext(NULL).SetData_Number("itemid", itemid));
	if (result.IsSuccessed())
	{
		equipDef = result.GetData_Usertype<ItemEquipDef>("equipDef");
	}
	if (equipDef)
	{
		for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
		{
			EquipmentPartDef partDef = equipDef->partDef[i];
			putonCustomEquipPart(&partDef, slot, i);
		}
	}
}

//脱掉自定义装备
void ActorBodyEquipComponent::takeoffCustomEquip(int slot)
{
	int itemid = m_NormalEquipItemIDs[slot];

	//脱掉'头盔、披风'这种常规装备
	if (itemid > 0 && isNormalEquip(itemid))
	{
		EQUIP_SLOT_TYPE part_slot = GetSlotTypeByItemId(itemid);
		if (part_slot < MAX_EQUIP_SLOTS)
		{
			clearEquipSlot(part_slot);

			//TODO:然后还要把这个格子本来的装备穿上
		}

		m_NormalEquipItemIDs[slot] = 0;
	}

	//脱掉模型
	for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
	{
		if (m_CustomEquipPartModel[slot][i])
		{
			if (m_ActorBody->m_Entity)
				m_ActorBody->m_Entity->UnbindObject(m_CustomEquipPartModel[slot][i]);

			DESTORY_GAMEOBJECT_BY_COMPOENT(m_CustomEquipPartModel[slot][i]);
			m_PartAnchorId[slot][i] = 0;
		}
	}
}

//穿上常规装备
void ActorBodyEquipComponent::putonNormalEquip(int itemid)
{
	//???????????λ?Z
	//得到装备放置的位置
	EQUIP_SLOT_TYPE slot = GetSlotTypeByItemId(itemid);
	if (!m_ActorBody->m_Entity) return;
	Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
	if (slot < MAX_EQUIP_SLOTS)
	{
		char texpath[256];
		getTexPath(texpath, slot, itemid);

		if (m_ActorBody->isPlayer() && m_ActorBody->getSkinID() == 0)
		{
			//记录当前这个格子放置的常规道具id
			m_NormalEquipItemIDs[slot] = itemid;

			//脱掉当前部件

			if (IsMantleID(itemid))
			{
				//脱掉背部模型
				if (m_DorsumEntity)
				{
					if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
					DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
				}

				if (m_ActorBody->IsAvatarPlayer())
				{
					m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
				}
				else
				{
					if (itemid == ITEM_OXYGEN_MASK) // 氧气面罩
					{
						model->ShowSkin("pifeng03", true);
						model->SetSkinTexture("pifeng03", texpath);
					}
					else
					{
						//披风
						model->ShowSkin("pifeng01", true);
						model->SetSkinTexture("pifeng01", texpath);
					}
				}
			}
			else
			{
				if (m_ActorBody->IsAvatarPlayer())
				{
					clearEquipSlot(slot);
					m_ActorBody->GetAvatarComponent()->EquipAavatarPlayer(slot, itemid);
				}
				else
				{
					char skinname[64];
					getSkinPart(skinname, slot, itemid);
					clearEquipSlot(slot);

					if (itemid == 0)
					{
						//Show body part
						model->ShowSkin(skinname, true);
						return;
					}

					model->ShowSkin(skinname, true);
					model->SetSkinTexture(skinname, texpath);

					//Hide body part
					//sprintf(skinname, "%s%.2d", s_AvatarTexDirs[slot], 0);
					model->ShowSkin(GetSkinName(slot, 0), false);
				}
			}
		}
	}
}

//判断是否为普通的装备(即头盔、胸甲这种)
bool ActorBodyEquipComponent::isNormalEquip(int itemid)
{
	if (itemid >= 12201 && itemid <= 12225)
		return true;

	if (itemid >= 12231 && itemid <= 12235)
		return true;

	//钻石套装
	if (itemid >= 12241 && itemid <= 12244)
		return true;

	//花冠
	if (itemid == ITEM_GARLAND || itemid == ITEM_GARLAND_PRO)
		return true;

	if (IsMantleID(itemid))
		return true;

	return false;
}

/**************************************穿上自定义装备部件**************************************
函数名: putonCustomEquipPart
参  数: pPartDef:部件定义
		slot	:装备格子位置(头部、披风...)
		nPartIndex:部件索引, [0, 3)
**********************************************************************************************/
void ActorBodyEquipComponent::putonCustomEquipPart(EquipmentPartDef* pPartDef, int slot, int nPartIndex)
{
	assert(nPartIndex >= 0 && nPartIndex < MAX_CUSTOMEQUIP_PART_NUM);
	assert(slot >= 0 && slot < EQUIP_WEAPON);

	Entity* pEntity = NULL;

	if (pPartDef)
	{
		EquipmentPartDef partDef = *pPartDef;

		if (partDef.nAnchorID > 0)
		{
			//脱掉原有模型
			if (slot == EQUIP_PIFENG)
			{
				if (m_DorsumEntity)
				{
					if (m_ActorBody->m_Entity) m_ActorBody->m_Entity->UnbindObject(m_DorsumEntity);
					DESTORY_GAMEOBJECT_BY_COMPOENT(m_DorsumEntity);
				}
			}
			else if (slot == EQUIP_HEAD)
			{
				if (m_HelmetModel)
				{
					if (m_ActorBody->m_Entity)
					{
						m_ActorBody->m_Entity->UnbindObject(m_HelmetModel);
						//m_Entity->StopMotion(s_MontionName2);
					}
					DESTORY_GAMEOBJECT_BY_COMPOENT(m_HelmetModel);
				}
			}
			int RelevantItemID = partDef.RelevantID;
			int MeshType = partDef.MeshType;
			Rainbow::Model* pModel = NULL;
			bool skipEquip = false;

			if (RelevantItemID > 0)
			{
				if (isNormalEquip(RelevantItemID))
				{
					//先脱掉部件
					if (m_CustomEquipPartModel[slot][nPartIndex] && m_ActorBody->m_Entity)
						m_ActorBody->m_Entity->UnbindObject(m_CustomEquipPartModel[slot][nPartIndex]);

					DESTORY_GAMEOBJECT_BY_COMPOENT(m_CustomEquipPartModel[slot][nPartIndex]);

					//头盔、胸甲、披风等普通装备
					pEntity = NULL;
					skipEquip = true;
					putonNormalEquip(RelevantItemID);
				}
				else
				{
					//道具模型
					char path[256];
					//RelevantItemID = getRealModelItemId(RelevantItemID);
					ItemDef* relevantitem = GetDefManagerProxy()->getItemDef(RelevantItemID, true);
					sprintf(path, "itemmods/%s/body.omod", relevantitem->Model.c_str());
					pModel = g_BlockMtlMgr.getModel(path, NULL);

					//贴图
					int textureIndex = 0;
					if (IsColorableItem(RelevantItemID))
					{
						textureIndex = RelevantItemID == ITEM_COLORED_EGG ? 1 : 2;
					}

					if (textureIndex == 0)
					{
						if (relevantitem->TextureID == 0)
							sprintf(path, "itemmods/%s/texture.png", relevantitem->Model.c_str());
						else
							sprintf(path, "itemmods/%s/texture%d.png", relevantitem->Model.c_str(), relevantitem->TextureID);
					}
					else
					{
						sprintf(path, "itemmods/%s/texture%d.png", relevantitem->Model.c_str(), textureIndex);
					}
					SharePtr<Texture2D> tex = GetAssetManager().LoadAssetAsync<Texture2D>(path);
					/*MINIW::Texture* tex = static_cast<MINIW::Texture*>(ResourceManager::getSingleton().blockLoad(path, RLF_CONVERT_BIT16));*/
					if (tex && pModel)
					{
						pModel->SetTexture("g_DiffuseTex", tex);
						pEntity = Entity::Create();
						pEntity->Load(pModel);
					}
				}
			}
			else if (MeshType == CUSTOM_GEN_MESH)
			{
				//微缩模型
				//pEntity = CustomModelMgr::GetInstancePtr()->getActorEntity(partDef.Model);
				//pModel = CustomModelMgr::GetInstancePtr()->getItemModel(partDef.Model.c_str(), PROJECTILE_ACTOR_MESH);	//NORMAL_MESH??PROJECTILE_ACTOR_MESH
				pModel = CustomModelMgr::GetInstancePtr()->getAvatarModel(partDef.Model.c_str(), PROJECTILE_ACTOR_MESH);	//NORMAL_MESH??PROJECTILE_ACTOR_MESH
				if (pModel)
				{
					pEntity = Entity::Create();
					pEntity->Load(pModel);
				}
			}
			else if (MeshType == FULLY_CUSTOM_GEN_MESH)
			{
				//完全自定义模型
				//pEntity = FullyCustomModelMgr::GetInstancePtr()->getEntity(partDef.Model, NULL, false);
				pEntity = FullyCustomModelMgr::GetInstancePtr()->getEntityByResClass(MAP_MODEL_CLASS, partDef.Model, NULL, false);
				if (NULL == pEntity)
					pEntity = FullyCustomModelMgr::GetInstancePtr()->getEntityByResClass(RES_MODEL_CLASS, partDef.Model, NULL, false);
				if (NULL == pEntity)
					pEntity = FullyCustomModelMgr::GetInstancePtr()->getEntityByResClass(EQUIP_MODEL_CLASS, partDef.Model, NULL, false);

				if (pEntity)
				{
					//Rainbow::ColourValue cv = Rainbow::ColourValue(0.0f, 0.0f, 0.0f, 1.0f);
					//pEntity->setInstanceAmbient(cv);
					pEntity->PlayAnim(0);
				}
			}
			else if (MeshType == IMPORT_MODEL_GEN_MESH && ImportCustomModelMgr::GetInstancePtr())
			{
				pModel = ImportCustomModelMgr::GetInstancePtr()->getImportModel(partDef.Model);
				if (pModel)
				{
					pEntity = Entity::Create();
					pEntity->Load(pModel);
				}
			}

			//if (pModel)
			//	DESTORY_GAMEOBJECT_BY_COMPOENT(pModel);

			if (pEntity)
			{
				//先脱掉部件
				if (m_CustomEquipPartModel[slot][nPartIndex] && m_ActorBody->m_Entity)
					m_ActorBody->m_Entity->UnbindObject(m_CustomEquipPartModel[slot][nPartIndex]);

				DESTORY_GAMEOBJECT_BY_COMPOENT(m_CustomEquipPartModel[slot][nPartIndex]);

				//校准位置、角度
				EquipmentPartDef relPartDef;
				AdjustCustomEquipPartDef(relPartDef, partDef);
				int nAnchorID = relPartDef.nAnchorID;

				Rainbow::Quaternionf quat = XYZAngleToQuat((float)relPartDef.pitch, (float)relPartDef.yaw, (float)relPartDef.roll);
				pEntity->SetRotation(quat);
				pEntity->SetOffsetPosition(Rainbow::Vector3f((float)relPartDef.x, (float)relPartDef.y, (float)relPartDef.z));
				pEntity->SetScale(Rainbow::Vector3f(partDef.scaleX, partDef.scaleY, partDef.scaleZ));

				if (pEntity->GetMainModel())
					pEntity->GetMainModel()->SetInstanceData(Vector4f(1.0f, 1.0f, 0, 0));

				if (!m_ActorBody->m_Entity) return;

				Rainbow::Model* model = m_ActorBody->m_Entity->GetMainModel();
				m_CustomEquipPartModel[slot][nPartIndex] = pEntity;
				m_PartAnchorId[slot][nPartIndex] = nAnchorID;
				if (model && model->IsShow())
				{
					m_ActorBody->m_Entity->BindObject(nAnchorID, pEntity);
				}

				// 自定义装备和装扮互斥逻辑处理
				if (m_ActorBody->IsAvatarPlayer())
				{
					m_ActorBody->GetAvatarComponent()->SetEquipAvatar((EQUIP_SLOT_TYPE)slot, nAnchorID, skipEquip);
				}
			}
		}

		m_ActorBody->UpdateVisiableDistance();
	}
}

EquipmentPartDef* ActorBodyEquipComponent::GetUIEquipmentPartDef()
{
	if (NULL == m_UIEquipmentPartDef)
		m_UIEquipmentPartDef = SANDBOX_NEW(EquipmentPartDef);

	return m_UIEquipmentPartDef;
}

void ActorBodyEquipComponent::ReleaseUIEquipmentPartDef()
{
	SANDBOX_DELETE(m_UIEquipmentPartDef);
}

void ActorBodyEquipComponent::releaseCustomEquip()
{
	for (int slot = 0; slot < MAX_EQUIP_SLOTS; slot++)
	{
		for (int i = 0; i < MAX_EQUIPMENTPART_COUNT; i++)
		{
			if (m_ActorBody->m_Entity && m_CustomEquipPartModel[slot][i])
				m_ActorBody->m_Entity->UnbindObject(m_CustomEquipPartModel[slot][i]);

			DESTORY_GAMEOBJECT_BY_COMPOENT(m_CustomEquipPartModel[slot][i]);
			m_PartAnchorId[slot][i] = 0;
		}
	}
}

int ActorBodyEquipComponent::getCurShowEquipItemId(EQUIP_SLOT_TYPE slot)
{
	return m_NormalEquipItemIDs[slot];
}

bool ActorBodyEquipComponent::GetWorldPosFromWeapon(int anchorId, Rainbow::Vector3f& boneWorldPos)
{
	if (m_WeaponModel)
	{
		ModelItemMesh* modelMesh = dynamic_cast<ModelItemMesh*>(m_WeaponModel);
		if (modelMesh && modelMesh->GetModel())
		{
			Rainbow::BoneNode* boneNode = modelMesh->GetModel()->GetBoneNode(anchorId);
			if (boneNode)
			{
				boneWorldPos = boneNode->GetWorldPosition();
				return true;
			}
		}
	}
	return false;
}