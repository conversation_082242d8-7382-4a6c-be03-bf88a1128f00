#pragma once
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/MeshRenderData.h"
class CurveScreen;

namespace Rainbow
{
	class MeshVertexLayout;
	class MeshVertexFormat;

	class CurveScreenRenderObject : public BaseRenderObject
	{
	public:
		CurveScreenRenderObject(CurveScreen* face);
		~CurveScreenRenderObject();

		virtual Matrix4x4f CalculateLocalToWorld() const override;
		virtual AABB CalculateWorldBounds() const override;
		virtual void ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator) override;
	private:
		CurveScreen* m_CurveFace;
		MeshRenderData m_MeshRenderData;
		ShaderChannelMask m_Mask;
		MeshVertexFormat* m_MeshVertexFormat;
		dynamic_array<int> m_SubMeshIndex;
	};
}