#ifndef __CUSTOM_MODEL_PACKING_SELECTAREA_H__
#define __CUSTOM_MODEL_PACKING_SELECTAREA_H__

#include "world_types.h"
#include "CoreCommonDef.h"

namespace Rainbow
{
	class MechaMeshObject;
}


class CustomModelPackingSelectArea //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	CustomModelPackingSelectArea();
	virtual ~CustomModelPackingSelectArea();

	void tick();
	bool confirmArea();
	void init();
	void clearPreBlocksMesh();
	void setOperateDistance(int distance);
	void setOperateMode(PACKING_CM_MODE mode);
	PACKING_CM_MODE getOperateMode();
	void getAreaSize(WCoord &starpos, WCoord &endpos);
	WCoord getCurOperatePos();
	void createAreaBoxMesh(std::map<WCoord, std::vector<WCoord>> &sectionpreblocks);
	//tolua_end
private:
	bool isValidPos(WCoord &pos);
	void checkAreaBoxChange();
	void updateAreaBox(WCoord curoperatepos);
private:
	WCoord m_StartPos;
	WCoord m_EndPos;
	WCoord m_OperatePos;

	short m_iAreaLimitLength;
	short m_iAreaLimitWidth;
	short m_iAreaLimitHeight;

	short m_iOperateDistance;

	bool m_bIsExceedLimit;

	std::vector<Rainbow::MechaMeshObject*> m_SectionMeshs;
	PACKING_CM_MODE m_iMode;
}; //tolua_exports
#endif
