
#include "BlockOperation.h"
#include "world.h"
#include "SandBoxManager.h"
#include "DefManagerProxy.h"
#include "blocks/BlockMaterialMgr.h"
#include "OgreScriptLuaVM.h"
#include "Common/OgreWCoord.h"
#include "SandboxRemoteMsg.h"
#include "SandboxSceneManager.h"
#define __HIGH_PERFORMANCE__ 0
#include "SandboxReflexTypeEnumEx.h"
#include "blocks/special_blockid.h"
#include "base/stream/SandboxStream.h"
#include "SandboxCameraObject.h"
#include "chunk.h"
#include "SandboxCameraObject.h"

using namespace MNSandbox;


//static const char* Msg_PlaceBlock_CH = "Msg_PlaceBlock_CH";
//static const char* Msg_DestroyBlock_CH = "Msg_DestroyBlock_CH";
//static const char* Msg_MoveBlock_CH = "Msg_MoveBlock_CH";


IMPLEMENT_REFCLASS(BlockOperation)

BlockOperation::BlockOperation(World* pworld):m_pWorld(pworld)
{
	//RegisterNetEvent();
}

BlockOperation::~BlockOperation()
{
	m_blcokCacheMap.clear();
	//UnRegisterNetEvent();
}



void BlockOperation::PlaceBlock(const BlockOptPlace& data)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_PlaceBlock", sceneid, data);

	//bool isRemote = Config::GetSingleton().IsRemote();
	//if (isRemote)
	//{
	//	

	//	jsonxx::Object context = const_cast<MNJsonObject&>(jsonObj);
	//	SandBoxManager::getSingleton().sendToHost(const_cast<char*>(Msg_PlaceBlock_CH), context.bin(), context.binLen());

	//	return;
	//}

	//DoPlaceBlock(jsonObj);
}

void BlockOperation::PlaceBlockVec(const std::vector<BlockOptPlace>& vec)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_PlaceBlockVec", sceneid, vec);
}

void BlockOperation::DestroyBlock(const BlockOptDestroy& data)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_DestroyBlock", sceneid, data);

	//bool isRemote = Config::GetSingleton().IsRemote();
	//if (isRemote)
	//{
	//	jsonxx::Object context = const_cast<MNJsonObject&>(jsonObj);
	//	SandBoxManager::getSingleton().sendToHost(const_cast<char*>(Msg_DestroyBlock_CH), context.bin(), context.binLen());

	//	return;
	//}

	//DoDestroyBlock(jsonObj);
}

void BlockOperation::DestroyBlockVec(const std::vector<BlockOptDestroy>& vec)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_DestroyBlockVec", sceneid, vec);
}

void BlockOperation::MoveBlock(const BlockOptMove& data)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_MoveBlock", sceneid, data);

	//bool isRemote = Config::GetSingleton().IsRemote();
	//if (isRemote)
	//{
	//	jsonxx::Object context = const_cast<MNJsonObject&>(jsonObj);
	//	SandBoxManager::getSingleton().sendToHost(const_cast<char*>(Msg_MoveBlock_CH), context.bin(), context.binLen());

	//	return;
	//}

	//DoMoveBlock(jsonObj);
}

void BlockOperation::MoveBlockVec(const std::vector<BlockOptMove>& vec)
{
	SANDBOX_ASSERT(MNSandbox::RemoteMsg::GetSingletonPtr());
	if (!MNSandbox::RemoteMsg::GetSingletonPtr())
		return;

	auto sceneid = m_pWorld->GetWorldScene()->GetSceneid();
	MNSandbox::RemoteMsg::GetSingleton().HostRun(SdbSceneManager::ms_blockMtrlNodeid, "Inner_MoveBlockVec", sceneid, vec);
}

//void BlockOperation::RegisterNetEvent()
//{
//	//SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_PlaceBlock_CH), std::bind(&BlockOperation::HandleMsgPlaceBlock, this, std::placeholders::_1, std::placeholders::_2));
//	//SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_DestroyBlock_CH), std::bind(&BlockOperation::HandleMsgDestroyBlock, this, std::placeholders::_1, std::placeholders::_2));
//	//SandBoxManager::getSingleton().subscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_MoveBlock_CH), std::bind(&BlockOperation::HandleMsgMoveBlock, this, std::placeholders::_1, std::placeholders::_2));
//
//}
//
//void BlockOperation::UnRegisterNetEvent()
//{
//	//SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_PlaceBlock_CH));
//	//SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_DestroyBlock_CH));
//	//SandBoxManager::getSingleton().unSubscibeEx((IEventExcuteEx*)this, const_cast<char*>(Msg_MoveBlock_CH));
//}

//int BlockOperation::HandleMsgDestroyBlock(void* obj, int len)
//{
//	MNJsonObject* jsonObj = (MNJsonObject*)obj;
//	if (!jsonObj)
//	{
//		return -1;
//	}
//
//	int ret = DoDestroyBlock(*jsonObj) ? 0 : -1;
//
//	return ret;
//}
//
//int BlockOperation::HandleMsgPlaceBlock(void* obj, int len)
//{
//	MNJsonObject* jsonObj = (MNJsonObject*)obj;
//	if (!jsonObj)
//	{
//		return -1;
//	}
//
//	int ret = DoPlaceBlock(*jsonObj) ? 0: -1;
//
//	return ret;
//}
//
//int BlockOperation::HandleMsgMoveBlock(void* obj, int len)
//{
//	MNJsonObject* jsonObj = (MNJsonObject*)obj;
//	if (!jsonObj)
//	{
//		return -1;
//	}
//
//	return DoMoveBlock(*jsonObj) ? 0 : -1;
//}


bool BlockOperation::DoPlaceBlock(const BlockOptPlace& data, bool bcheck/*=false*/)
{
	int placeId = data._blockid;
	auto blockpos = WCoord(data._x, data._y, data._z);
	//被放置位置上的blockid
	int pos_id = m_pWorld->getBlockID(blockpos);
	int blockdata = data._blockdata;
	DirectionType targetface = data._targetface;
	Rainbow::Vector3f colpoint = data._colpoint.toVector3();

	//被放置位置的blockmaterial
	auto posMtl = g_BlockMtlMgr.getMaterial(pos_id);
	if (!posMtl)
	{
		return false;
	}
	WCoord placePos;
	DirectionType hitface; //相对于placepos那个block
	bool isagain = false;
	bool placeInto = false;
	hitface = ReverseDirection(targetface);

	bool bRet = false;
	GetPlacePos(pos_id, placeId, blockpos, placePos, bRet);
	if (!bRet)
	{
		return false;
	}
	//if (posMtl && posMtl->isReplaceable() && placeId != pos_id)
	//{
	//	placePos = blockpos;
	//	hitface = DIR_NEG_Y;
	//}
	//else if (posMtl && posMtl->canPlacedAgain(m_pWorld, placeId, colpoint, blockpos, true, hitface))
	//{
	//	placePos = blockpos;
	//	isagain = true;
	//	placeInto = true;
	//}
	//else
	//{
	//	placePos = NeighborCoord(blockpos, targetface);
	//	isagain = m_pWorld->getBlockMaterial(placePos)->canPlacedAgain(m_pWorld, placeId, colpoint, blockpos, false, hitface);
	//	if (!m_pWorld->getBlockMaterial(placePos)->isReplaceable()
	//		&& !isagain)
	//	{
	//		return false;
	//	}
	//}

	BlockMaterial* newMtl = g_BlockMtlMgr.getMaterial(placeId);
	if (!newMtl)
	{
		return false;
	}
	IClientPlayer* pTriggerPlayer = nullptr;
	int dir = DIR_NEG_X;
	auto cameraObj = MNSandbox::GetCurrentSandboxCameraObj();
	if (cameraObj)
	{
		Rainbow::Vector3f euler;
		cameraObj->GetWorldEuler(euler);
		dir = int((euler.y + 180.0f) / 90.0f + 0.5f) & 3;
		dir == 0 ? DIR_NEG_Z : (dir == 1 ? DIR_NEG_X : (dir == 2 ? DIR_POS_Z : (dir == 3 ? DIR_POS_X : DIR_NEG_Y)));
		pTriggerPlayer = World::GetDefaultTriggerPlayer(placePos, dir);
	}
	bool canplace = m_pWorld->canPlaceActorOnSide(placeId, placePos, false, hitface, NULL, colpoint, placeInto, pTriggerPlayer);
	if (!canplace)
	{
		return false;
	}
	if (blockdata < 0)
	{
		blockdata = newMtl->getPlaceBlockData(m_pWorld, placePos, (DirectionType)hitface, colpoint.x, colpoint.y, colpoint.z, 0);
	}
	if (blockdata < 0)
	{
		return false;
	}
	if (bcheck) // 校验通过，可以放置方块，校验过程不去放置方块
		return true;

	int placeDir = blockdata & 3;

	bool bFind = false;
	string retStr;
	GetBlockItemScriptString(newMtl->getCsvDefId(), "OnCreate", bFind, retStr);
	if (bFind)
	{
		bool ret = false;
		MINIW::ScriptVM::game()->callFunction(retStr.c_str(), "u[World]iiiii>b", m_pWorld, placeId, placePos.x, placePos.y, placePos.z, placeDir, &ret);
		if (ret)
		{
			return true;
		}
	}


	if (!isagain)
	{
		m_pWorld->setBlockAll(placePos, placeId, blockdata, 3);
	}

	if (m_pWorld->getBlockID(placePos) == placeId)
	{
		int flag = (strcmp(newMtl->getClassName(), "BlockOneQuarter") == 0) ? 1 : 0;

		//这里有bug 以前的block大多都没对player判空，会导致crash
		//newMtl->DoOnBlockPlacedBy(m_pWorld, placePos, NULL, flag, colpoint, placeInto, hitface);
	}

	const BlockDef* blockDef = newMtl->GetBlockDef();
	if (blockDef)
	{
		//由Block def中的高度，设置方块的data，第三个bit代表 block的上面
		for (int i = 1; i < blockDef->Height; i++)
		{
			m_pWorld->setBlockAll(placePos + WCoord(0, i, 0), placeId, blockdata | 8, 3);
		}
	}

	m_pWorld->playBlockPlaceSound(placeId, placePos.x, placePos.y, placePos.z);

	return true;
}

bool BlockOperation::DoPlaceBlockVec(const std::vector<BlockOptPlace>& vec)
{
#if __HIGH_PERFORMANCE__ 
	m_pWorld->setBlockVecAll(vec);
#else
	for (auto& item : vec)
	{
		if (!DoPlaceBlock(item))
		{
			//放置失败
			m_notifyPlaceInvalid.Emit(item);
		}
	}
#endif // __HIGH_PERFORMANCE__ 

	return true;
}

bool BlockOperation::DoDestroyBlock(const BlockOptDestroy& data)
{
	auto posX = data._x;
	auto posY = data._y;
	auto posZ = data._z;
	bool isDropItem = data._dropitem;

	//关闭全局光照(耗性能)
	g_EnableReLighting = false;
	m_pWorld->destroyBlockEx(posX, posY, posZ, isDropItem);
	g_EnableReLighting = true;

	return true;
}

bool BlockOperation::DoDestroyBlockVec(const std::vector<BlockOptDestroy>& vec)
{
#if __HIGH_PERFORMANCE__ 
	std::vector<BlockOperation::BlockOptPlace> blockDestroyVec = {};

	for (auto& item : vec)
	{
		BlockOperation::BlockOptPlace optDestroy;
		optDestroy._x = item._x;
		optDestroy._y = item._y;
		optDestroy._z = item._z;
		optDestroy._blockid = 0;
		optDestroy._blockdata = 0;

		blockDestroyVec.emplace_back(optDestroy);
	}

	m_pWorld->setBlockVecAll(blockDestroyVec);
#else
	for (auto& item : vec)
	{
		DoDestroyBlock(item);
	}
#endif // __HIGH_PERFORMANCE__ 

	return true;
}

bool BlockOperation::DoMoveBlock(const BlockOptMove& data)
{
	int src_x = data._srcx;
	int src_y = data._srcy;
	int src_z = data._srcz;
	int dst_x = data._dstx;
	int dst_y = data._dsty;
	int dst_z = data._dstz;

#if __HIGH_PERFORMANCE__ 
	m_pWorld->setBlockAll(WCoord(src_x, src_y, src_z), 0, 0);
	m_pWorld->setBlockAll(WCoord(dst_x, dst_y, dst_z), data._srcBlockID, data._srcData);
#else
	BlockOptDestroy destroyData;
	destroyData._x = src_x;
	destroyData._y = src_y;
	destroyData._z = src_z;
	destroyData._dropitem = false;

	BlockOptPlace placeData;
	placeData._x = dst_x;
	placeData._y = dst_y;
	placeData._z = dst_z;
	placeData._blockid = data._srcBlockID;
	placeData._blockdata = data._srcData;

	DoDestroyBlock(destroyData);
	DoPlaceBlock(placeData);
#endif // __HIGH_PERFORMANCE__ 

	return true;
}

bool BlockOperation::DoMoveBlockVec(const std::vector<BlockOperation::BlockOptMove>& vec)
{
#if __HIGH_PERFORMANCE__ 
	std::vector<BlockOperation::BlockOptPlace> blockDestroyVec = {};
	std::vector<BlockOperation::BlockOptPlace> blockPlaceVec = {};
	
	for (auto& item : vec)
	{
		BlockOperation::BlockOptPlace optDestroy;
		optDestroy._x = item._srcx;
		optDestroy._y = item._srcy;
		optDestroy._z = item._srcz;
		optDestroy._blockid = 0;
		optDestroy._blockdata = 0;

		blockDestroyVec.emplace_back(optDestroy);

		BlockOperation::BlockOptPlace optPlace;
		optPlace._x = item._dstx;
		optPlace._y = item._dsty;
		optPlace._z = item._dstz;
		optPlace._blockid = item._srcBlockID;
		optPlace._blockdata = item._srcData;

		blockPlaceVec.emplace_back(optPlace);
	}

	m_pWorld->setBlockVecAll(blockDestroyVec);
	m_pWorld->setBlockVecAll(blockPlaceVec);
#else
	for (auto& item : vec)
	{
		//床类方块特殊处理
		if (item._srcData > 0 && IsBedBlock(item._srcBlockID))
		{
			if (item._srcBlockID != BLOCK_SHELLBED)
			{
				continue;
			}
			else if (item._srcData != 3)
			{
				continue;
			}
		}

		DoMoveBlock(item);
	}
#endif // __HIGH_PERFORMANCE__ 

	return true;
}

bool BlockOperation::IsPreDelete(const WCoord& blockpos)
{
	auto hashKey = hash_val(blockpos.x, blockpos.y, blockpos.z);
	auto iter = m_blcokCacheMap.find(hashKey);
	if (iter != m_blcokCacheMap.end())
	{
		return iter->second.IsPreDelete();
	}
	return false;
}

void BlockOperation::SetPreDelete(const WCoord& blockpos, bool bDelete)
{
	auto signBlock = SignBlock(m_pWorld->getBlock(blockpos), bDelete ? 1 : 0);
	auto hashKey = hash_val(blockpos.x, blockpos.y, blockpos.z);
	m_blcokCacheMap[hashKey] = signBlock;

	if (m_blcokCacheMap.size() > 4096)
	{
		m_blcokCacheMap.clear();
		SANDBOX_ASSERT(false);
	}
}

void BlockOperation::ResetBlockPosCache()
{
	m_blcokCacheMap.clear();
}

void BlockOperation::GetBlockItemScriptString(int blockid, std::string suffix, bool& bFind, std::string& ret)
{
	vector<std::string> vec;
	vec.push_back("SetTwoBlockSizeData");
	vec.push_back("SetFourBlockSizeRectangle");
	vec.push_back("SetBlockUpAndDown");
	vec.push_back("SetLong3Width2Height1BlockSize");
	vec.push_back("SetTwoBlockSizeDataInWall");
	vec.push_back("SetLong2Width1Height1BlockSize");
	vec.push_back("SetFourBlockSizeDataSquareInWall");

	bFind = false;
	const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (!itemdef)
	{
		return;
	}
	string createScript = itemdef->UseScript.c_str();
	size_t pos = createScript.find("OnUse");
	if (pos == string::npos)
	{
		for (auto& str : vec)
		{
			if (str.compare(createScript) == 0)
			{
				createScript = str + "_";
				bFind = true;
				break;
			}
		}
	}
	else
	{
		createScript = createScript.substr(0, pos);
		bFind = true;
	}
	ret = createScript+ suffix;
}
//todo 这个方法还要再整理下
void BlockOperation::GetPlacePos(int posId, int placeId, const WCoord& blockpos, WCoord& placepos, bool& ret)
{
	//WCoord placePos;
	Rainbow::Vector3f colpoint(0,0,0);
	DirectionType hitface; //相对于placepos那个block
	bool isagain = false;
	bool placeInto = false;
	auto targetface = DIR_POS_Y;
	hitface = ReverseDirection(targetface);
	auto posMtl = g_BlockMtlMgr.getMaterial(posId);
	if (!posMtl)
	{
		ret = false;
		return;
	}

	if (posMtl && posMtl->isReplaceable() && placeId != posId)
	{
		placepos = blockpos;
		hitface = DIR_NEG_Y;
	}
	else if (posMtl && posMtl->canPlacedAgain(m_pWorld, placeId, colpoint, blockpos, true, hitface))
	{
		placepos = blockpos;
		isagain = true;
		placeInto = true;
	}
	else
	{
		placepos = NeighborCoord(blockpos, targetface);
		isagain = m_pWorld->getBlockMaterial(placepos)->canPlacedAgain(m_pWorld, placeId, colpoint, blockpos, false, hitface);
		if (!m_pWorld->getBlockMaterial(placepos)->isReplaceable()
			&& !isagain)
		{
			ret = false;
		}
	}
	ret = true;
}

void BlockOperation::OnClearNotify()
{
	m_notifyPlaceInvalid.Clear();
	Super::OnClearNotify();
}
///////////////////////////////////////////////////////////////////////////////////////

template<>
void ReflexPolicySerialize<BlockOperation::BlockOptPlace>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);
	if (!CToJson(out, v._x, v._y, v._z, v._blockid))
		return;

	if (!out.is<MNJsonObject>()) // CToJson 之后一定是MNJsonObject
		return;
	auto& jsonobj = out.get<MNJsonObject>();

	if (v._blockdata != -1)
		jsonobj << "data" << v._blockdata;
	if (v._targetface != DIR_POS_Y)
		jsonobj << "face" << (MNJsonNumber)v._targetface;
	if (v._colpoint != WCoord(0, 0, 0))
	{
		MNJsonVal json_colpoint;
		if (CToJson(json_colpoint, v._colpoint))
			jsonobj << "colpoint" << json_colpoint;
	}
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptPlace>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);
	if (!JsonToC(in, v._x, v._y, v._z, v._blockid))
	{
		SANDBOX_ASSERT(false);
		return false;
	}

	if (!in.is<MNJsonObject>())
		return false;
	auto& jsonobj = in.get<MNJsonObject>();

	if (jsonobj.has<MNJsonNumber>("data"))
		v._blockdata = (int)jsonobj.get<MNJsonNumber>("data");
	if (jsonobj.has<MNJsonNumber>("face"))
		v._targetface = (DirectionType)jsonobj.get<MNJsonNumber>("face");
	if (jsonobj.has<MNJsonVal>("colpoint"))
	{
		if (!JsonToC(jsonobj.get<MNJsonVal>("colpoint"), v._colpoint))
			return false;
	}
	return true;
}
template<>
size_t ReflexPolicySerialize<BlockOperation::BlockOptPlace>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	len += out->WriteNumber<int>(v._x);
	len += out->WriteNumber<int>(v._y);
	len += out->WriteNumber<int>(v._z);
	len += out->WriteNumber<int>(v._blockid);
	len += out->WriteNumber<int>(v._blockdata);
	len += out->WriteNumber<char>(static_cast<char>(v._targetface));
	auto& policy = ReflexType::GetSingleton<WCoord>().GetPolicy();
	size_t vLen = policy.m_cbReflexToBinary(&v._colpoint, out);
	if (vLen == Stream::Error)
		return Stream::Error;
	len += vLen;
	return len;
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptPlace>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	bool ret = true;
	char targetface;
	ret &= in->ReadNumber<int>(v._x);
	ret &= in->ReadNumber<int>(v._y);
	ret &= in->ReadNumber<int>(v._z);
	ret &= in->ReadNumber<int>(v._blockid);
	ret &= in->ReadNumber<int>(v._blockdata);
	ret &= in->ReadNumber<char>(targetface);
	v._targetface = static_cast<DirectionType>(targetface);
	auto& policy = ReflexType::GetSingleton<WCoord>().GetPolicy();
	ret &= policy.m_cbReflexFromBinary(&v._colpoint, in);
	return ret;
}
RegisterReflexTypePolicyClass(BlockOperation::BlockOptPlace, MNSandbox::REFLEXTYPEENUM_BLOCK_OPTPLACE, ReflexPolicySerializeOnlyReg<BlockOperation::BlockOptPlace>);

template<>
void ReflexPolicySerialize<BlockOperation::BlockOptDestroy>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);
	if (!CToJson(out, v._x, v._y, v._z))
		return;

	if (!out.is<MNJsonObject>()) // CToJson 之后一定是MNJsonObject
		return;
	auto& jsonobj = out.get<MNJsonObject>();

	if (v._dropitem != false)
		jsonobj << "dropitem" << v._dropitem;
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptDestroy>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);
	if (!JsonToC(in, v._x, v._y, v._z))
	{
		SANDBOX_ASSERT(false);
		return false;
	}

	if (!in.is<MNJsonObject>()) // CToJson 之后一定是MNJsonObject
		return false;
	auto& jsonobj = in.get<MNJsonObject>();

	if (jsonobj.has<MNJsonBool>("dropitem"))
		v._dropitem = jsonobj.get<MNJsonBool>("dropitem");
	return true;
}
template<>
size_t ReflexPolicySerialize<BlockOperation::BlockOptDestroy>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	len += out->WriteNumber<int>(v._x);
	len += out->WriteNumber<int>(v._y);
	len += out->WriteNumber<int>(v._z);
	len += out->WriteBool(v._dropitem);
	return len;
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptDestroy>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	bool ret = true;
	ret &= in->ReadNumber<int>(v._x);
	ret &= in->ReadNumber<int>(v._y);
	ret &= in->ReadNumber<int>(v._z);
	ret &= in->ReadBool(v._dropitem);
	return ret;
}
RegisterReflexTypePolicyClass(BlockOperation::BlockOptDestroy, MNSandbox::REFLEXTYPEENUM_BLOCK_OPTDESTROY, ReflexPolicySerializeOnlyReg<BlockOperation::BlockOptDestroy>);

template<>
void ReflexPolicySerialize<BlockOperation::BlockOptMove>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);
	if (!CToJson(out, v._srcx, v._srcy, v._srcz, v._dstx, v._dsty, v._dstz, v._srcBlockID, v._srcData))
		return;
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptMove>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);
	if (!JsonToC(in, v._srcx, v._srcy, v._srcz, v._dstx, v._dsty, v._dstz, v._srcBlockID, v._srcData))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	return true;
}
template<>
size_t ReflexPolicySerialize<BlockOperation::BlockOptMove>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	len += out->WriteNumber<int>(v._srcx);
	len += out->WriteNumber<int>(v._srcy);
	len += out->WriteNumber<int>(v._srcz);
	len += out->WriteNumber<int>(v._dstx);
	len += out->WriteNumber<int>(v._dsty);
	len += out->WriteNumber<int>(v._dstz);
	len += out->WriteNumber<int>(v._srcBlockID);
	len += out->WriteNumber<int>(v._srcData);
	return len;
}
template<>
bool ReflexPolicySerialize<BlockOperation::BlockOptMove>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	bool ret = true;
	ret &= in->ReadNumber<int>(v._srcx);
	ret &= in->ReadNumber<int>(v._srcy);
	ret &= in->ReadNumber<int>(v._srcz);
	ret &= in->ReadNumber<int>(v._dstx);
	ret &= in->ReadNumber<int>(v._dsty);
	ret &= in->ReadNumber<int>(v._dstz);
	ret &= in->ReadNumber<int>(v._srcBlockID);
	ret &= in->ReadNumber<int>(v._srcData);
	return ret;
}
RegisterReflexTypePolicyClass(BlockOperation::BlockOptMove, MNSandbox::REFLEXTYPEENUM_BLOCK_OPTMOVE, ReflexPolicySerializeOnlyReg<BlockOperation::BlockOptMove>);

template<>
void ReflexPolicySerialize<std::vector<BlockOperation::BlockOptMove>>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);

	MNJsonArray* optArray = ToJsonType<MNJsonArray>(out);
	for (auto& item : v)
	{
		MNJsonArray optItem;
		optItem << item._srcx << item._srcy << item._srcz << item._dstx << item._dsty << item._dstz << item._srcBlockID << item._srcData;
		*optArray << optItem;
	}
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptMove>>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);

	const MNJsonArray* optArray = ToJsonType<MNJsonArray>(in);
	auto i = 0;
	v.resize(optArray->size() / 8);

	for (auto& item : v)
	{
		item._srcx = optArray->get<MNJsonNumber>(i++);
		item._srcy = optArray->get<MNJsonNumber>(i++);
		item._srcz = optArray->get<MNJsonNumber>(i++);
		item._dstx = optArray->get<MNJsonNumber>(i++);
		item._dsty = optArray->get<MNJsonNumber>(i++);
		item._dstz = optArray->get<MNJsonNumber>(i++);
		item._srcBlockID = optArray->get<MNJsonNumber>(i++);
		item._srcData = optArray->get<MNJsonNumber>(i++);
	}

	return true;
}
template<>
size_t ReflexPolicySerialize<std::vector<BlockOperation::BlockOptMove>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	unsigned vLen = v.size();
	len += out->WriteNumber<unsigned>(vLen);
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		len += out->WriteNumber<int>(data._srcx);
		len += out->WriteNumber<int>(data._srcy);
		len += out->WriteNumber<int>(data._srcz);
		len += out->WriteNumber<int>(data._dstx);
		len += out->WriteNumber<int>(data._dsty);
		len += out->WriteNumber<int>(data._dstz);
		len += out->WriteNumber<int>(data._srcBlockID);
		len += out->WriteNumber<int>(data._srcData);
	}
	return len;
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptMove>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	unsigned vLen = v.size();
	if (!in->ReadNumber<unsigned>(vLen))
		return false;

	v.resize(vLen);
	bool ret = true;
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		ret &= in->ReadNumber<int>(data._srcx);
		ret &= in->ReadNumber<int>(data._srcy);
		ret &= in->ReadNumber<int>(data._srcz);
		ret &= in->ReadNumber<int>(data._dstx);
		ret &= in->ReadNumber<int>(data._dsty);
		ret &= in->ReadNumber<int>(data._dstz);
		ret &= in->ReadNumber<int>(data._srcBlockID);
		ret &= in->ReadNumber<int>(data._srcData);
		if (!ret)
			return false;
	}
	return ret;
}
RegisterReflexTypePolicyClass(std::vector<BlockOperation::BlockOptMove>, MNSandbox::REFLEXTYPEENUM_VECTOR_BLOCK_OPTMOVE, ReflexPolicySerializeOnlyReg<std::vector<BlockOperation::BlockOptMove>>);

template<>
void ReflexPolicySerialize<std::vector<BlockOperation::BlockOptDestroy>>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);

	MNJsonArray* optArray = ToJsonType<MNJsonArray>(out);
	for (auto& item : v)
	{
		MNJsonArray optItem;
		optItem << item._x << item._y << item._z << item._dropitem;
		*optArray << optItem;
	}
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptDestroy>>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);

	const MNJsonArray* optArray = ToJsonType<MNJsonArray>(in);
	auto i = 0;
	v.resize(optArray->size() / 4);

	for (auto& item : v)
	{
		item._x = optArray->get<MNJsonNumber>(i++);
		item._y = optArray->get<MNJsonNumber>(i++);
		item._z = optArray->get<MNJsonNumber>(i++);
		item._dropitem = optArray->get<MNJsonBool>(i++);
	}

	return true;
}
template<>
size_t ReflexPolicySerialize<std::vector<BlockOperation::BlockOptDestroy>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	unsigned vLen = v.size();
	len += out->WriteNumber<unsigned>(vLen);
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		len += out->WriteNumber<int>(data._x);
		len += out->WriteNumber<int>(data._y);
		len += out->WriteNumber<int>(data._z);
		len += out->WriteBool(data._dropitem);
	}
	return len;
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptDestroy>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	unsigned vLen = v.size();
	if (!in->ReadNumber<unsigned>(vLen))
		return false;

	v.resize(vLen);
	bool ret = true;
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		ret &= in->ReadNumber<int>(data._x);
		ret &= in->ReadNumber<int>(data._y);
		ret &= in->ReadNumber<int>(data._z);
		ret &= in->ReadBool(data._dropitem);
		if (!ret)
			return false;
	}
	return ret;
}
RegisterReflexTypePolicyClass(std::vector<BlockOperation::BlockOptDestroy>, MNSandbox::REFLEXTYPEENUM_VECTOR_BLOCK_OPTDESTROY, ReflexPolicySerializeOnlyReg<std::vector<BlockOperation::BlockOptDestroy>>);

template<>
void ReflexPolicySerialize<std::vector<BlockOperation::BlockOptPlace>>::CallbackSerialize(const void* data, MNJsonVal& out)
{
	auto& v = Data(data);

	MNJsonArray* optArray = ToJsonType<MNJsonArray>(out);
	for (auto& item : v)
	{
		MNJsonArray optItem;
		optItem << item._x << item._y << item._z << item._blockid << item._blockdata << (int)item._targetface << item._colpoint.x << item._colpoint.y << item._colpoint.z;
		*optArray << optItem;
	}
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptPlace>>::CallbackUnserialize(void* data, const MNJsonVal& in)
{
	auto& v = Data(data);

	const MNJsonArray* optArray = ToJsonType<MNJsonArray>(in);
	auto i = 0;
	v.resize(optArray->size() / 9);

	for (auto& item : v)
	{
		item._x = optArray->get<MNJsonNumber>(i++);
		item._y = optArray->get<MNJsonNumber>(i++);
		item._z = optArray->get<MNJsonNumber>(i++);
		item._blockid = optArray->get<MNJsonNumber>(i++);
		item._blockdata = optArray->get<MNJsonNumber>(i++);
		item._targetface = (DirectionType)(optArray->get<MNJsonNumber>(i++));
		item._colpoint.x = optArray->get<MNJsonNumber>(i++);
		item._colpoint.y = optArray->get<MNJsonNumber>(i++);
		item._colpoint.z = optArray->get<MNJsonNumber>(i++);
	}

	return true;
}
template<>
size_t ReflexPolicySerialize<std::vector<BlockOperation::BlockOptPlace>>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
{
	auto& v = Data(data);
	size_t len = 0;
	unsigned vLen = v.size();
	len += out->WriteNumber<unsigned>(vLen);
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		len += out->WriteNumber<int>(data._x);
		len += out->WriteNumber<int>(data._y);
		len += out->WriteNumber<int>(data._z);
		len += out->WriteNumber<int>(data._blockid);
		len += out->WriteNumber<int>(data._blockdata);
		len += out->WriteNumber<char>((char)data._targetface);
		len += out->WriteNumber<int>(data._colpoint.x);
		len += out->WriteNumber<int>(data._colpoint.y);
		len += out->WriteNumber<int>(data._colpoint.z);
	}
	return len;
}
template<>
bool ReflexPolicySerialize<std::vector<BlockOperation::BlockOptPlace>>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
{
	auto& v = Data(data);
	unsigned vLen = v.size();
	if (!in->ReadNumber<unsigned>(vLen))
		return false;

	v.resize(vLen);
	bool ret = true;
	char targetface;
	for (unsigned i = 0; i < vLen; ++i)
	{
		auto& data = v.at(i);
		ret &= in->ReadNumber<int>(data._x);
		ret &= in->ReadNumber<int>(data._y);
		ret &= in->ReadNumber<int>(data._z);
		ret &= in->ReadNumber<int>(data._blockid);
		ret &= in->ReadNumber<int>(data._blockdata);
		ret &= in->ReadNumber<char>(targetface);
		ret &= in->ReadNumber<int>(data._colpoint.x);
		ret &= in->ReadNumber<int>(data._colpoint.y);
		ret &= in->ReadNumber<int>(data._colpoint.z);
		if (!ret)
			return false;

		data._targetface = static_cast<DirectionType>(targetface);
	}
	return ret;
}
RegisterReflexTypePolicyClass(std::vector<BlockOperation::BlockOptPlace>, MNSandbox::REFLEXTYPEENUM_VECTOR_BLOCK_OPTPLACE, ReflexPolicySerializeOnlyReg<std::vector<BlockOperation::BlockOptPlace>>);
