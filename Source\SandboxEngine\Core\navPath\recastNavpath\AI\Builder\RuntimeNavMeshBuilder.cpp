#include "RuntimeNavMeshBuilder.h"
#include "CreateNavMeshTileData.h"
#include "Public/NavMeshBuildSettings.h"
#include "Public/NavMeshData.h"
#include "Public/NavMeshProjectSettings.h"
#include "Analytics/NavigationAnalytics.h"
#include "Builder/RuntimeNavMeshBuilderTypes.h"
#include "Graphics/Mesh/Mesh.h"
#include "Geometry/Intersection.h"
#include "Utilities/StrideIterator.h"
#include "Recast.h"
#include "RecastAlloc.h"
#include "Jobs/Jobs.h"
#include "Jobs/Internal/JobQueue.h"
#include "Utilities/Hash128.h"
#include "HashFunctions/SpookyV2.h"
#include "NavMesh.h"
#include "RecastDebugContext.h"
#include "MathUtil.h"
#include "HashUtilities.h"
#include "NavMeshBuildOperation.h"
const MemLabelId jobAllocLabel = kMemAI;
static const int kMaxTileDimension = 2500;

struct TileFilter
{
    int m_MinX;
    int m_MaxX;
    int m_MinZ;
    int m_MaxZ;

public:
    TileFilter()
        : m_MinX(1), m_MaxX(-1), m_MinZ(1), m_MaxZ(-1)
    {}

    TileFilter(int minX, int maxX, int minZ, int maxZ)
        : m_MinX(minX), m_MaxX(maxX), m_MinZ(minZ), m_MaxZ(maxZ)
    {}
};

static bool CalculateTriangleData(NavMeshTriangleData* triangleData, const SharedMeshBuildSource& src);
static void RasterizeModifier(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context, rcCompactHeightfield& chf);
static void CreateCubeMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform, const Rainbow::Vector3f& extents);
static void CreateSphereMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform,
    const float radius, const int lats, const int longs);
static void CreateCapsuleMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform,
    const float radius, const float halfHeight, const int lats, const int longs);

static bool BuildTileMesh(int ix, int iz, const SharedMeshBuildSource* sources, size_t nsources, const rcConfig& config,
    struct TileAllocations& mem);

static void CreateEmptyTile(int ix, int iz, const NavMeshBuildSettings& settings, NavMeshTileData* tileData);
static bool CreateTileData(int ix, int iz, const rcConfig& config, const NavMeshBuildSettings& settings,
    struct TileAllocations& mem, NavMeshTileData* tileData);

static bool ConstrainConfigBounds(rcConfig& cfg, int ix, int iz, const rcConfig& config);
static int CalculateTileIndices(dynamic_array<int>& tileXZ, const rcConfig& config, TileFilter& tileFilter, bool trimToFullTiles = false);

static void TransformSources(dynamic_array<SharedMeshBuildSource>& bsources, const Rainbow::Vector3f& position, const Rainbow::Quaternionf& rotation);
static Rainbow::MinMaxAABB CombineAndCullBounds(dynamic_array<SharedMeshBuildSource>& bsources, const Rainbow::MinMaxAABB& bounds);

static void ConfigureConfig(rcConfig& config, const NavMeshBuildSettings& settings, const Rainbow::MinMaxAABB& bounds)
{
    memset(&config, 0, sizeof(config));

    rcVcopy(config.bmin, bounds.m_Min.GetPtr());
    rcVcopy(config.bmax, bounds.m_Max.GetPtr());
    config.bmax[1] += settings.agentHeight + 0.1f; // Make sure the agent can stand at the top.

    // Clamp voxel size to sane range.
    config.cs = std::max(0.01f, settings.cellSize);

    // Height needs more precision so that walkableClimb is not too prone to rasterization errors.
    config.ch = config.cs * 0.5f;

    // Some places inside Recast store height as a byte, make sure the ratio between
    // the agent height (plus climb) and cell height does not exceed this limit.
    if ((int)ceilf((settings.agentHeight + settings.agentClimb) / config.ch) > 255)
    {
        const float cellHeight = (settings.agentHeight + settings.agentClimb) / 255.0f;
        config.ch = cellHeight;
    }

    // Warn about too large vertical dimension and adjust the cell height to fit the whole world.
    float worldHeight = config.bmax[1] - config.bmin[1];
    int worldHeightVoxels = (int)ceilf(worldHeight / config.ch);
    if (worldHeightVoxels >= RC_SPAN_MAX_HEIGHT)
    {
        config.ch = worldHeight / (float)(RC_SPAN_MAX_HEIGHT);
    }

    config.walkableSlopeAngle = clamp(settings.agentSlope, 0.1f, 90.0f);
    config.walkableHeight = (int)floorf(settings.agentHeight / config.ch);
    config.walkableClimb = (int)ceilf(settings.agentClimb / config.ch);
    // Bias the conversion a little so that inputs which intent to match certain radius/cs ratio will be treated correctly.
    config.walkableRadius = std::max(0, (int)ceilf(settings.agentRadius / config.cs - config.cs * 0.01f));
    config.maxEdgeLen = 0;
    config.maxSimplificationError = 1.3f;

    // Recast assumes that walkableClimb is less than walkableHeight when neighbour cell access is calculated.
    // Step height is clamped to agent height on validation, but it might get equal or larger than agent height because or rounding.
    if (config.walkableClimb >= config.walkableHeight)
        config.walkableClimb = std::max(0, config.walkableHeight - 1);

    config.minRegionArea = (int)(settings.minRegionArea / (config.cs * config.cs));

    config.tileSize = settings.tileSize;
    config.mergeRegionArea = 400;
    config.maxVertsPerPoly = kNavMeshVertsPerPoly;
    config.detailSampleDist = config.cs * 6.0f;
    config.detailSampleMaxError = config.ch * 1.0f;

    rcCalcGridSize(config.bmin, config.bmax, config.cs, &config.width, &config.height);

    // Adjust for tile border
    config.borderSize = config.walkableRadius + 3;
    config.width  = config.tileSize + 2 * config.borderSize;
    config.height = config.tileSize + 2 * config.borderSize;
}

static void RasterizeModifier(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context, rcCompactHeightfield& chf)
{
    if (src.shape != kNavMeshBuildSourceModifierBox)
        return;

    if (config.bmin[0] > src.bounds.m_Max[0]
        ||  config.bmin[2] > src.bounds.m_Max[2]
        ||  config.bmax[0] < src.bounds.m_Min[0]
        ||  config.bmax[2] < src.bounds.m_Min[2])
        return;

    const Rainbow::Vector3f center = src.transform.GetPosition();
    const Rainbow::Vector3f axisX = src.transform.GetAxisX();
    const Rainbow::Vector3f axisY = src.transform.GetAxisY();
    const Rainbow::Vector3f axisZ = src.transform.GetAxisZ();
    const Rainbow::Vector3f extents = Abs(src.size) * 0.5f;

    float planes[6 * 4] =
    {
        axisX.x, axisX.y, axisX.z, -Dot(axisX, center) - extents.x,
        -axisX.x, -axisX.y, -axisX.z, -Dot(-axisX, center) - extents.x,
        axisY.x, axisY.y, axisY.z, -Dot(axisY, center) - extents.y,
        -axisY.x, -axisY.y, -axisY.z, -Dot(-axisY, center) - extents.y,
        axisZ.x, axisZ.y, axisZ.z, -Dot(axisZ, center) - extents.z,
        -axisZ.x, -axisZ.y, -axisZ.z, -Dot(-axisZ, center) - extents.z,
    };

    UInt8 area = RC_WALKABLE_AREA + src.areaTag;

    if (src.areaTag == NavMeshProjectSettings::kNotWalkable)
        area = RC_NULL_AREA;

    rcMarkConvexPolyhedraArea(&context, src.bounds.m_Min.GetPtr(), src.bounds.m_Max.GetPtr(),
        planes, 6, area, chf);
}

static bool PrepareMeshSource(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context,
    NavMeshTriangleData& triData, dynamic_array<UInt8>& triAreas)
{
    if (!CalculateTriangleData(&triData, src))
        return false;

    // ComputeNavMesh triangle area
    const int triangleCount = triData.indices.size() / 3;

    // Not walkable area has to be tagged specifically, since it will be filtered later
    if (src.areaTag == NavMeshProjectSettings::kNotWalkable)
    {
        triAreas.resize_initialized(triangleCount, RC_FORCE_UNWALKABLE_AREA);
    }
    else
    {
        triAreas.resize_initialized(triangleCount, 0);
        const int* indexData = (int*)&triData.indices[0];
        rcMarkWalkableTriangles(&context, config.walkableSlopeAngle, triData.vertices[0].GetPtr(), triData.vertices.size(), indexData, triangleCount, &triAreas[0]);

        // during voxelization we use RC_WALKABLE_AREA + type. After voxelization we transform it back into areaType bits.
        for (size_t i = 0; i < triAreas.size(); ++i)
        {
            if (triAreas[i] != RC_WALKABLE_AREA)
                continue;

            triAreas[i] = RC_WALKABLE_AREA + src.areaTag;
            Assert(triAreas[i] < RC_FORCE_UNWALKABLE_AREA);
        }
    }

    return true;
}

static void RasterizeMesh(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context, rcHeightfield& heightfield, NavMeshTriangleData* debugTriData, dynamic_array<UInt8>* debugTriAreas)
{
    if (config.bmin[0] > src.bounds.m_Max[0]
        ||  config.bmin[2] > src.bounds.m_Max[2]
        ||  config.bmax[0] < src.bounds.m_Min[0]
        ||  config.bmax[2] < src.bounds.m_Min[2])
        return;

    NavMeshTriangleData triData;
    dynamic_array<UInt8> triAreas(kMemTempAlloc);

    if (!PrepareMeshSource(src, config, context, triData, triAreas))
        return;

    // Rasterize into the heightfield
    const int triangleCount = triData.indices.size() / 3;
    const int* indexData = (int*)&triData.indices[0];
    rcRasterizeTriangles(&context, triData.vertices[0].GetPtr(), triData.vertices.size(), indexData, &triAreas[0], triangleCount, heightfield, config.walkableClimb);

#if RAINBOW_EDITOR_
    NavMeshBuildDebugManager::AppendTriangleDataToDebug(triData, triAreas, debugTriData, debugTriAreas);
#endif
}

static void RasterizeAxisAlignedBox(rcContext* context,
    const unsigned char walkableArea, const unsigned char unwalkableArea,
    rcHeightfield& hf,
    const float* bmin, const float* bmax,
    const float* tmin, const float* tmax,
    const int flagMergeThr)
{
    const int w = hf.width;
    const int h = hf.height;
    const float by = bmax[1] - bmin[1];
    const float ics = 1.0f / hf.cs;
    const float ich = 1.0f / hf.ch;

    // Calculate the footprint of the box on the grid's x-axis
    int xmin = (int)((tmin[0] - bmin[0]) * ics);
    int xmax = (int)((tmax[0] - bmin[0]) * ics);
    int x0 = rcClamp(xmin, 0, w - 1);
    int x1 = rcClamp(xmax, 0, w - 1);

    // Calculate the footprint of the box on the grid's y-axis
    int ymin = (int)((tmin[2] - bmin[2]) * ics);
    int ymax = (int)((tmax[2] - bmin[2]) * ics);
    int y0 = rcClamp(ymin, 0, h - 1);
    int y1 = rcClamp(ymax, 0, h - 1);

    float smin = tmin[1] - bmin[1];
    float smax = tmax[1] - bmin[1];

    DebugAssertMsg(smax >= 0.0f && smin <= by, "The vertical coordinates of the Box source are outside of the heightfield but they should be inside.");

    // Clamp the span to the heightfield bbox.
    bool sminClampped = smin < 0.0f;
    if (sminClampped)
        smin = 0;
    bool smaxClampped = smax > by;
    if (smaxClampped)
        smax = by;

    // Snap the span to the heightfield height grid.
    unsigned short isminTop = (unsigned short)rcClamp((int)floorf(smax * ich), 0, RC_SPAN_MAX_HEIGHT);
    unsigned short ismaxTop = (unsigned short)rcClamp((int)ceilf(smax * ich), (int)isminTop + 1, RC_SPAN_MAX_HEIGHT);

    unsigned short isminBottom = (unsigned short)rcClamp((int)floorf(smin * ich), 0, RC_SPAN_MAX_HEIGHT);
    unsigned short ismaxBottom = (unsigned short)rcClamp((int)ceilf(smin * ich), (int)isminBottom + 1, RC_SPAN_MAX_HEIGHT);

    // Cover the borders of the box if they are inside the bounds
    if (y0 == ymin)
        for (int x = x0; x <= x1; ++x)
            rcAddSpan(context, hf, x, y0, ismaxBottom, isminTop, unwalkableArea, flagMergeThr);

    if (y1 == ymax)
        for (int x = x0; x <= x1; ++x)
            rcAddSpan(context, hf, x, y1, ismaxBottom, isminTop, unwalkableArea, flagMergeThr);

    int ystart = (y0 == ymin) ? y0 + 1 : y0;
    int yend = (y1 == ymax) ? y1 - 1 : y1;
    if (x0 == xmin)
        for (int y = ystart; y <= yend; ++y)
            rcAddSpan(context, hf, x0, y, ismaxBottom, isminTop, unwalkableArea, flagMergeThr);

    if (x1 == xmax)
        for (int y = ystart; y <= yend; ++y)
            rcAddSpan(context, hf, x1, y, ismaxBottom, isminTop, unwalkableArea, flagMergeThr);

    // Top plane
    if (!smaxClampped)
    {
        for (int y = y0; y <= y1; ++y)
        {
            for (int x = x0; x <= x1; ++x)
            {
                rcAddSpan(context, hf, x, y, isminTop, ismaxTop, walkableArea, flagMergeThr);
            }
        }
    }

    // Bottom plane
    if (!sminClampped)
    {
        for (int y = y0; y <= y1; ++y)
        {
            for (int x = x0; x <= x1; ++x)
            {
                rcAddSpan(context, hf, x, y, isminBottom, ismaxBottom, unwalkableArea, flagMergeThr);
            }
        }
    }
}

static void RasterizeAxisAlignedBox(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context, rcHeightfield& heightfield, NavMeshTriangleData* debugTriData, dynamic_array<UInt8>* debugTriAreas)
{
    if (config.bmin[0] > src.bounds.m_Max[0]
        || config.bmin[2] > src.bounds.m_Max[2]
        || config.bmax[0] < src.bounds.m_Min[0]
        || config.bmax[2] < src.bounds.m_Min[2])
        return;

    // Equivalent to PrepareMeshSource
    // Not walkable area has to be tagged specifically, since it will be filtered later
    UInt8 walkableArea;
    UInt8 unwalkableArea;
    if (src.areaTag == NavMeshProjectSettings::kNotWalkable)
    {
        walkableArea = RC_FORCE_UNWALKABLE_AREA;
        unwalkableArea = RC_FORCE_UNWALKABLE_AREA;
    }
    else
    {
        walkableArea = RC_WALKABLE_AREA + src.areaTag;
        unwalkableArea = 0;
    }

    RasterizeAxisAlignedBox(&context, walkableArea, unwalkableArea,
        heightfield, heightfield.bmin, heightfield.bmax,
        src.bounds.m_Min.GetPtr(), src.bounds.m_Max.GetPtr(),
        config.walkableClimb);

#if RAINBOW_EDITOR_
    if (debugTriData)
    {
        NavMeshTriangleData triData;
        dynamic_array<UInt8> triAreas(kMemTempAlloc);

        if (PrepareMeshSource(src, config, context, triData, triAreas))
        {
            NavMeshBuildDebugManager::AppendTriangleDataToDebug(triData, triAreas, debugTriData, debugTriAreas);
        }
    }
#endif
}

static void RasterizeSource(const SharedMeshBuildSource& src, const rcConfig& config, rcContext& context, rcHeightfield& heightfield, NavMeshTriangleData* debugTriData, dynamic_array<UInt8>* debugTriAreas)
{
    if (src.shape == kNavMeshBuildSourceBox && IsTransformAxisAligned(src.transform))
        RasterizeAxisAlignedBox(src, config, context, heightfield, debugTriData, debugTriAreas);
    else
        RasterizeMesh(src, config, context, heightfield, debugTriData, debugTriAreas);
}

struct BuildNavMeshInfo
{
    BuildNavMeshInfo(MemLabelId label)
        : sources(label)
        , tileXZ(label)
        , removeTileIDs(label)
        , invTileCount(0)
        , tilesDone(0)
        , cancel(false)
    {
    }

    // Shared job input
    NavMeshBuildSettings settings;
    rcConfig config;
    dynamic_array<SharedMeshBuildSource> sources;
    Rainbow::Vector3f buildPosition;
    Rainbow::Quaternionf buildRotation;

    // Per job input
    dynamic_array<int> tileXZ;

    // Per job output
    NavMeshTileDataVector tiles;

    //NavMeshData* data;
    //AABB localBounds;
    Rainbow::JobFence fence;
    dynamic_array<int> removeTileIDs;
    float invTileCount;
    int tilesDone;
    bool cancel;
};

// Classify the tiles by finding hashes - returns the indices of old tiles to remove and new tiles to compute.
// The computed index arrays are both in ascending order.
// At this stage it's assumed that 'newTiles' contain the updated hashes.
static void ClassifyTilesToRemoveAndCompute(const NavMeshTileDataVector& oldTiles, const NavMeshTileDataVector& newTiles,
    const TileFilter tileFilter, dynamic_array<int>& removeOldIDs, dynamic_array<int>& computeTileIDs)
{
    const size_t oldCount = oldTiles.size();
    const size_t newCount = newTiles.size();

    const bool tileFilterValid = tileFilter.m_MinX <= tileFilter.m_MaxX && tileFilter.m_MinZ <= tileFilter.m_MaxZ;

    // Identify removed tile ids (indices into the 'oldTiles' vector)
    for (size_t i = 0; i < oldCount; ++i)
    {
        if (tileFilterValid)
        {
            NavMeshDataHeader* header = (NavMeshDataHeader*)oldTiles[i].m_MeshData.begin();
            if (header->x < tileFilter.m_MinX || header->x > tileFilter.m_MaxX ||
                header->y < tileFilter.m_MinZ || header->y > tileFilter.m_MaxZ)
                continue;
        }

        size_t j = 0;
        while (j != newCount && newTiles[j].m_Hash != oldTiles[i].m_Hash)
            ++j;

        if (j == newCount)
            removeOldIDs.push_back(i);
    }

    // Identify added tile ids (indices into the 'newTiles' vector)
    for (size_t i = 0; i < newCount; ++i)
    {
        if (newTiles[i].m_Hash == Rainbow::Hash128())
            continue;

        size_t j = 0;
        while (j != oldCount && oldTiles[j].m_Hash != newTiles[i].m_Hash)
            ++j;

        if (j == oldCount)
            computeTileIDs.push_back(i);
    }
}

// In-place compact the tiles to compute and their xz locations - given a list of indices sorted in ascending order
static int CompactTilesAndLocations(const dynamic_array<int>& sortedIndices, NavMeshTileDataVector& tiles, dynamic_array<int>& tileXZ)
{
    const size_t count = sortedIndices.size();

    int iwrite = 0;
    for (size_t i = 0; i < count; ++i)
    {
        const int j = sortedIndices[i];

        AssertMsg(iwrite <= j, "CompactTilesAndLocations: Write index must be less than read index for in-place compact");


        if (iwrite < j)
        {
            tileXZ[2 * iwrite + 0] = tileXZ[2 * j + 0];
            tileXZ[2 * iwrite + 1] = tileXZ[2 * j + 1];
            tiles[iwrite].m_Hash = tiles[j].m_Hash;
        }
        iwrite++;
    }

    tiles.resize(iwrite);
    tileXZ.resize_uninitialized(2 * iwrite);

    return iwrite;
}

static void CalculateBuildSourceHash(const SharedMeshBuildSource& src, Rainbow::Hash128& hashInOut)
{
    HashValue(src.transform, hashInOut);
    HashValue(src.areaTag, hashInOut);
    HashValue(src.shape, hashInOut);

    if (src.shape == kNavMeshBuildSourceMesh)
    {
        // Hash vertices
        const Rainbow::VertexData& vertexData = src.sharedMeshData->GetVertexData();
        Rainbow::StrideIterator<Rainbow::Vector3f> it = vertexData.MakeStrideIterator<Rainbow::Vector3f>(Rainbow::kShaderChannelVertex);
        Rainbow::StrideIterator<Rainbow::Vector3f> end = vertexData.MakeEndIterator<Rainbow::Vector3f>(Rainbow::kShaderChannelVertex);
        for (; it != end; ++it)
            HashValue(*it, hashInOut);

        // Hash indices
        const Rainbow::SharedMeshData::IndexContainer& indices = src.sharedMeshData->GetIndexBuffer();
        SpookyHash::Hash128(&indices[0], sizeof(indices[0]) * indices.size(), &hashInOut.hashData.u64[0], &hashInOut.hashData.u64[1]);
    }
    else
    {
        // src.shape == kNavMeshBuildSourceBox, src.shape == kNavMeshBuildSourceSphere, src.shape == kNavMeshBuildSourceCapsule
        HashValue(src.size, hashInOut);
    }
}

static void ComputeTileHashJob(struct BuildNavMeshInfo* info, unsigned int i)
{
    // Initialize the null hash - can be skipped at a later stage
    info->tiles[i].m_Hash = Rainbow::Hash128();

    if (info->cancel)
        return;

    const int ix = info->tileXZ[2 * i + 0];
    const int iz = info->tileXZ[2 * i + 1];

    rcConfig cfg;
    if (!ConstrainConfigBounds(cfg, ix, iz, info->config))
        return;

    const Rainbow::Vector3f tileMin(cfg.bmin);
    const Rainbow::Vector3f tileMax(cfg.bmax);
    const Rainbow::MinMaxAABB tileBounds(tileMin, tileMax);
    Rainbow::MinMaxAABB sourcesBounds;

    NavMeshBuildSettings configurationSettings = info->settings;
    configurationSettings.maxJobWorkers = 0;
    configurationSettings.preserveTilesOutsideBounds = 0;

    Rainbow::Hash128 hash;
    HashValue(configurationSettings, hash);
    // Hash tile location
    HashValue(ix, hash);
    HashValue(iz, hash);

    const size_t nsources = info->sources.size();
    bool hasSources = false;
    for (size_t k = 0; k < nsources; ++k)
    {
        const SharedMeshBuildSource& src = info->sources[k];
        if (IntersectAABBAABB(src.bounds, tileBounds))
        {
            CalculateBuildSourceHash(src, hash);
            hasSources = true;

            sourcesBounds.Encapsulate(src.bounds);
        }
    }

    if (hasSources)
    {
        Rainbow::MinMaxAABB clampedBounds;
        IntersectionAABBAABB(sourcesBounds, tileBounds, &clampedBounds);

        // Hash potentially clamped tile bounds
        HashValue(clampedBounds.m_Min, hash);
        HashValue(clampedBounds.m_Max, hash);

        info->tiles[i].m_Hash = hash;
    }
}

static void ComputeTileMeshJob(struct BuildNavMeshInfo* info, unsigned int i)
{
    if (info->cancel)
        return;

    const int ix = info->tileXZ[2 * i + 0];
    const int iz = info->tileXZ[2 * i + 1];
    const rcConfig& config = info->config;
    const NavMeshBuildSettings& settings = info->settings;

    NavMeshTileData* tile = &info->tiles[i];
    Assert(tile != NULL);

    TileAllocations mem;
    const bool tileMeshOK = BuildTileMesh(ix, iz, info->sources.begin(), info->sources.size(), config, mem);

    if (!tileMeshOK || !CreateTileData(ix, iz, config, settings, mem, tile))
    {
        CreateEmptyTile(ix, iz, settings, tile);
    }

    atomic_fetch_add_explicit(&info->tilesDone, 1, Rainbow::memory_order_relaxed);
}

// Calculate config to match a given tile - constrains bounds to world bounds
static bool ConstrainConfigBounds(rcConfig& cfg, int ix, int iz, const rcConfig& config)
{
    // Quantize world bounds
    float cs1 = 1.0f / config.cs;
    const int quantWorldMinX = (int)floorf(config.bmin[0] * cs1);
    const int quantWorldMinZ = (int)floorf(config.bmin[2] * cs1);
    const int quantWorldMaxX = (int)ceilf(config.bmax[0] * cs1);
    const int quantWorldMaxZ = (int)ceilf(config.bmax[2] * cs1);

    // Constraint to world bounds.
    const int minX = std::max(ix * config.tileSize, quantWorldMinX);
    const int minZ = std::max(iz * config.tileSize, quantWorldMinZ);
    const int maxX = std::min(ix * config.tileSize + config.tileSize, quantWorldMaxX);
    const int maxZ = std::min(iz * config.tileSize + config.tileSize, quantWorldMaxZ);

    // Skip if outside world bounds
    if (minX >= maxX || minZ >= maxZ)
        return false;

    // Patch config bounds for this tile
    cfg = config;

    // Keep vertical world bounds. Expand x,z by border size.
    cfg.bmin[0] = (minX - cfg.borderSize) * cfg.cs;
    cfg.bmin[2] = (minZ - cfg.borderSize) * cfg.cs;
    cfg.bmax[0] = (maxX + cfg.borderSize) * cfg.cs;
    cfg.bmax[2] = (maxZ + cfg.borderSize) * cfg.cs;

    cfg.width = (maxX - minX) + cfg.borderSize * 2;
    cfg.height = (maxZ - minZ) + cfg.borderSize * 2;
    return true;
}

// Build tile coordinate index list with x,z pairs
// Returns the number of tiles covered
static int CalculateTileIndices(dynamic_array<int>& tileXZ, const rcConfig& config, TileFilter& tileFilter, bool trimToFullTiles)
{
    // Calculate the ranges for covered tiles
    const Rainbow::Vector3f worldMin(config.bmin[0], config.bmin[1], config.bmin[2]);
    const Rainbow::Vector3f worldMax(config.bmax[0], config.bmax[1], config.bmax[2]);
    const float tileSize = config.tileSize * config.cs;

    const int minX = trimToFullTiles ? (int)ceilf(worldMin.x / tileSize) : (int)floorf(worldMin.x / tileSize);
    const int minZ = trimToFullTiles ? (int)ceilf(worldMin.z / tileSize) : (int)floorf(worldMin.z / tileSize);
    const int maxX = trimToFullTiles ? (int)floorf(worldMax.x / tileSize) - 1 : (int)floorf(worldMax.x / tileSize);
    const int maxZ = trimToFullTiles ? (int)floorf(worldMax.z / tileSize) - 1 : (int)floorf(worldMax.z / tileSize);

    tileFilter = TileFilter(minX, maxX, minZ, maxZ);

    const int xDim = std::max((maxX - minX + 1), 0);
    const int zDim = std::max((maxZ - minZ + 1), 0);

    if (xDim > kMaxTileDimension || zDim > kMaxTileDimension)
    {
#if RAINBOW_EDITOR_
        WarningString(tr("Updating the NavMesh failed due to an excessive number of tiles. Try limiting the distance between surface objects or increasing either the tile size or the voxel size."));
#endif
        return 0;
    }

    const int tileCount = xDim * zDim;

    // Build tile coordinate list with x,z pairs
    tileXZ.resize_uninitialized(2 * tileCount);

    int k = 0;
    for (int iz = minZ; iz <= maxZ; ++iz)
    {
        for (int ix = minX; ix <= maxX; ++ix)
        {
            tileXZ[k++] = ix;
            tileXZ[k++] = iz;
        }
    }
    return tileCount;
}

static bool BuildTileMesh(int ix, int iz, const SharedMeshBuildSource* sources, size_t nsources, const rcConfig& config, struct TileAllocations& mem)
{
    rcConfig cfg;
    if (!ConstrainConfigBounds(cfg, ix, iz, config))
        return true;

    mem.m_ownsData = true;

    //  Step 1. create height field
    mem.m_polyMesh = rcAllocPolyMesh();
    if (!mem.m_polyMesh)
        return false;

    mem.m_detailMesh = rcAllocPolyMeshDetail();
    if (!mem.m_detailMesh)
        return false;

    mem.m_heightField = rcAllocHeightfield();
    if (!mem.m_heightField)
        return false;

    mem.m_cset = rcAllocContourSet();
    if (!mem.m_cset)
        return false;

    RecastDebugContext context(true);
    if (!rcCreateHeightfield(&context, *mem.m_heightField, cfg.width, cfg.height, cfg.bmin, cfg.bmax, cfg.cs, cfg.ch))
        return false;

    size_t modifierBoxIndices[NavMeshProjectSettings::kAreaCount] = {};

    for (size_t i = 0; i < nsources; ++i)
        if (sources[i].shape == kNavMeshBuildSourceModifierBox)
            modifierBoxIndices[sources[i].areaTag]++;

    for (size_t i = 1; i < NavMeshProjectSettings::kAreaCount; ++i)
        modifierBoxIndices[i] += modifierBoxIndices[i - 1];

    size_t modifierBoxesCount = modifierBoxIndices[NavMeshProjectSettings::kAreaCount - 1];
    dynamic_array<const SharedMeshBuildSource*> modifierBoxSources(kMemTempAlloc);
    modifierBoxSources.resize_initialized(modifierBoxesCount, nullptr);

    // Step 2. Rasterize all geometry
    for (size_t i = 0; i < nsources; ++i)
    {
        if (sources[i].shape == kNavMeshBuildSourceModifierBox)
        {
            modifierBoxSources[modifierBoxIndices[sources[i].areaTag] - 1] = &sources[i];
            modifierBoxIndices[sources[i].areaTag]--;
            continue;
        }
        RasterizeSource(sources[i], cfg, context, *mem.m_heightField, &mem.m_triData, &mem.m_triAreas);
    }

    // Merge the wall spans that were intentionally left unmerged during rasterization
    if (mem.m_heightField->wallSpansLeftUnmerged)
    {
        // Find out the top span in the sequence of adjacent spans
        dynamic_array<rcSpan*> adjacentSpans(kMemTempAlloc);

        size_t nCells = mem.m_heightField->width * mem.m_heightField->height;
        for (size_t i = 0; i < nCells; i++)
        {
            rcSpan* bottomSpan = mem.m_heightField->spans[i];
            if (!bottomSpan || !bottomSpan->next)
                continue;

            rcSpan* prevSpan = nullptr;
            adjacentSpans.clear();

            rcSpan* nextSpan = bottomSpan->next;
            while (nextSpan || adjacentSpans.size() >= 2)
            {
                if (nextSpan && nextSpan->smin == bottomSpan->smax)
                {
                    // collect the vertically-adjacent spans
                    if (adjacentSpans.size() == 0)
                    {
                        adjacentSpans.push_back(bottomSpan);
                    }

                    adjacentSpans.push_back(nextSpan);
                }
                else
                {
                    int s = adjacentSpans.size();
                    if (s >= 2) // at least two spans to merge
                    {
                        // merge any vertically-adjacent spans from top to bottom
                        rcSpan* const topSpan = adjacentSpans[--s];
                        if (prevSpan)
                            prevSpan->next = topSpan;
                        else
                            mem.m_heightField->spans[i] = topSpan;

                        prevSpan = topSpan;

                        while (--s >= 0)
                        {
                            rcSpan* const spanBelow = adjacentSpans[s];
                            topSpan->smin = spanBelow->smin;

                            // Merge flags.
                            const int topSpanDiff = (int)topSpan->smax - (int)spanBelow->smax;
                            const int thr = topSpan->area == 0 ? config.walkableClimb : 1;
                            if (topSpanDiff <= thr && spanBelow->area > topSpan->area)
                                topSpan->area = spanBelow->area;

                            // Remove lower span.
                            spanBelow->next = mem.m_heightField->freelist;
                            mem.m_heightField->freelist = spanBelow;
                        }

                        adjacentSpans.clear();
                    }
                    else
                    {
                        prevSpan = bottomSpan;
                    }
                }

                bottomSpan = nextSpan;
                if (nextSpan)
                    nextSpan = nextSpan->next;
            }
        }

        mem.m_heightField->wallSpansLeftUnmerged = false;
    }

    // Step 3. Filter walkable surfaces.

    // Once all geometry is rasterized, we do initial pass of filtering to
    // remove unwanted overhangs caused by the conservative rasterization
    // as well as filter spans where the character cannot possibly stand.
    rcFilterLowHangingWalkableObstacles(&context, cfg.walkableClimb, *mem.m_heightField);
    rcFilterForceUnwalkableArea(&context, *mem.m_heightField);
    rcFilterLedgeSpans(&context, cfg.walkableHeight, cfg.walkableClimb, *mem.m_heightField);
    rcFilterWalkableLowHeightSpans(&context, cfg.walkableHeight, *mem.m_heightField);

    // Skip if tile is empty, i.e. it has no spans allocated
    if (mem.m_heightField->freelist == NULL)
        return true;

    // Step 4. Partition walkable surface to simple regions.

    // Compact the heightfield so that it is faster to handle from now on.
    // This will result more cache coherent data as well as the neighbours
    // between walkable cells will be calculated.
    mem.m_chf = rcAllocCompactHeightfield();
    if (!mem.m_chf)
        return false;

    if (!rcBuildCompactHeightfield(&context, cfg.walkableHeight, cfg.walkableClimb, *mem.m_heightField, *mem.m_chf))
        return false;

    for (size_t i = 0; i < modifierBoxesCount; i++)
        RasterizeModifier(*(modifierBoxSources[i]), cfg, context, *mem.m_chf);

    // Erode the walkable area by agent radius.
    if (!rcErodeWalkableArea(&context, cfg.walkableRadius, *mem.m_chf))
        return false;

    // Partition the walkable surface into non-overlapping regions (may contain holes, handled later in contour creation).
    if (!rcBuildLayerRegions(&context, *mem.m_chf, cfg.borderSize, cfg.minRegionArea))
        return false;

    // Step 5. Trace and simplify region contours.

    // Create contours.
    if (!rcBuildContours(&context, *mem.m_chf, cfg.maxSimplificationError, cfg.maxEdgeLen, *mem.m_cset))
        return false;

    // Skip if tile is empty.
    if (!mem.m_cset->nconts)
        return true;

    // Step 6. Build polygons mesh from contours.

    // Build polygon navmesh from the contours.
    if (!rcBuildPolyMesh(&context, *mem.m_cset, cfg.maxVertsPerPoly, *mem.m_polyMesh))
        return false;

    // Step 7. Set polygon flags.

    // Update poly flags from areas.
    for (int i = 0; i < mem.m_polyMesh->npolys; ++i)
    {
        unsigned int area = mem.m_polyMesh->areas[i] - RC_WALKABLE_AREA;
        Assert(area < 32);

        // store area in flags as walkable mask
        mem.m_polyMesh->flags[i] = 1 << area;
        mem.m_polyMesh->areas[i] = area;
    }

    // Step 8. Create detail mesh which allows to access approximate height on each polygon.

    if (!rcBuildPolyMeshDetail(&context, *mem.m_polyMesh, *mem.m_chf, cfg.detailSampleDist, cfg.detailSampleMaxError, *mem.m_detailMesh))
        return false;

    return true;
}

static void CreateEmptyTile(int ix, int iz, const NavMeshBuildSettings& settings, NavMeshTileData* tileData)
{
    tileData->m_MeshData.resize_initialized(sizeof(NavMeshDataHeader), 0);
    NavMeshDataHeader* header = (NavMeshDataHeader*)tileData->m_MeshData.begin();
    header->magic = kNavMeshMagic;
    header->version = kNavMeshVersion;
    header->x = ix;
    header->y = iz;
    header->agentTypeId = settings.agentTypeID;
}

static bool CreateTileData(int ix, int iz, const rcConfig& config, const NavMeshBuildSettings& settings,
    struct TileAllocations& mem, NavMeshTileData* tileData)
{
    if (mem.m_polyMesh == NULL || mem.m_detailMesh == NULL)
        return false;

    NavMeshCreateParams params;
    memset(&params, 0, sizeof(params));

    // fill params
    params.verts = mem.m_polyMesh->verts;
    params.vertCount = mem.m_polyMesh->nverts;
    params.polys = mem.m_polyMesh->polys;
    params.polyAreas = mem.m_polyMesh->areas;
    params.polyFlags = mem.m_polyMesh->flags;
    params.polyCount = mem.m_polyMesh->npolys;
    params.nvp = mem.m_polyMesh->nvp;
    params.detailMeshes = mem.m_detailMesh->meshes;
    params.detailVerts = mem.m_detailMesh->verts;
    params.detailVertsCount = mem.m_detailMesh->nverts;
    params.detailTris = mem.m_detailMesh->tris;
    params.detailTriCount = mem.m_detailMesh->ntris;
    params.walkableHeight = settings.agentHeight;
    params.walkableRadius = settings.agentRadius;
    params.walkableClimb = settings.agentClimb;
    params.agentTypeId = settings.agentTypeID;
    params.tileX = ix;
    params.tileY = iz;
    params.bmin = Rainbow::Vector3f(mem.m_polyMesh->bmin);
    params.bmax = Rainbow::Vector3f(mem.m_polyMesh->bmax);
    params.cs = config.cs;
    params.ch = config.ch;
    params.buildBvTree = true;

    return CreateNavMeshTileData(&params, &tileData->m_MeshData);
}

static void* RecastAlloc(int size, rcAllocHint /*hint*/)
{
    return ENG_MALLOC_LABEL(size, jobAllocLabel);
}

static void RecastFree(void *ptr)
{
    ENG_FREE_LABEL(ptr, jobAllocLabel);
}

static Rainbow::MinMaxAABB CalculateBounds(const Rainbow::Matrix4x4f& mat, const Rainbow::Vector3f& size)
{
    const Rainbow::Vector3f extents = size * 0.5f;
    const Rainbow::Vector3f worldExtents = Abs(mat.GetAxisX() * extents.x) + Abs(mat.GetAxisY() * extents.y) + Abs(mat.GetAxisZ() * extents.z);
    const Rainbow::Vector3f min = mat.GetPosition() - worldExtents;
    const Rainbow::Vector3f max = mat.GetPosition() + worldExtents;
    return Rainbow::MinMaxAABB(min, max);
}

static void CreateCubeMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform, const Rainbow::Vector3f& extents)
{
    // Create box mesh
    static const int indices[36] =
    {
        0, 1, 2, 3, 2, 1,
        4, 0, 6, 6, 0, 2,
        5, 1, 4, 4, 1, 0,
        7, 3, 1, 7, 1, 5,
        5, 4, 7, 7, 4, 6,
        7, 2, 3, 7, 6, 2
    };
    triangleData->vertices.resize_uninitialized(8);
    for (int i = 0; i < 8; i++)
    {
        const Rainbow::Vector3f pos((i & 1) == 0 ? extents.x : -extents.x, (i & 2) == 0 ? extents.y : -extents.y, (i & 4) == 0 ? extents.z : -extents.z);
        triangleData->vertices[i] = transform.MultiplyPoint3(pos);
    }
    triangleData->indices.assign(indices, indices + 36);
}

static void CreateSphereMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform,
    const float radius, const int lats, const int longs)
{
    triangleData->vertices.resize_uninitialized(lats * longs);

    for (int i = 0; i < lats; i++)
    {
        float a = (float)i / (lats - 1) * kPI;
        float y  = radius * cosf(a);
        float yr =  radius * sinf(a);
        for (int j = 0; j < longs; j++)
        {
            float b = (float)(j - 1) / longs * kPI * 2.0f;
            float x = cosf(b);
            float z = sinf(b);
            Rainbow::Vector3f pos(x * yr, y, z * yr);
            triangleData->vertices[i * longs + j] = transform.MultiplyPoint3(pos);
        }
    }

    for (int i = 0; i < lats - 1; i++)
    {
        int i0 = i;
        int i1 = i + 1;
        for (int j = 0; j < longs; j++)
        {
            int j0 = j;
            int j1 = (j + 1) % longs;
            triangleData->indices.push_back(i0 * longs + j0);
            triangleData->indices.push_back(i0 * longs + j1);
            triangleData->indices.push_back(i1 * longs + j1);
            triangleData->indices.push_back(i0 * longs + j0);
            triangleData->indices.push_back(i1 * longs + j1);
            triangleData->indices.push_back(i1 * longs + j0);
        }
    }
}

static void CreateCapsuleMesh(NavMeshTriangleData* triangleData, const Rainbow::Matrix4x4f& transform,
    const float radius, const float halfHeight, const int lats, const int longs)
{
    const int hlats = (lats + 1) / 2;
    const float stemHalfHeight = std::max(0.0f, halfHeight - radius);

    triangleData->vertices.resize_uninitialized(hlats * 2 * longs);

    // Top
    for (int i = 0; i < hlats; i++)
    {
        const float a = (float)i / (hlats - 1) * kPI * 0.5f;
        const float y  = radius * cosf(a);
        const float yr =  radius * sinf(a);
        for (int j = 0; j < longs; j++)
        {
            const float b = (float)(j - 1) / longs * kPI * 2.0f;
            const float x = cosf(b);
            const float z = sinf(b);
            const Rainbow::Vector3f pos(x * yr, y + stemHalfHeight, z * yr);
            triangleData->vertices[i * longs + j] = transform.MultiplyPoint3(pos);
        }
    }

    // Bottom
    for (int i = hlats; i < hlats * 2; i++)
    {
        const float a = (float)(i - 1) / (hlats - 1) * kPI * 0.5f;
        const float y  = radius * cosf(a);
        const float yr =  radius * sinf(a);
        for (int j = 0; j < longs; j++)
        {
            const float b = (float)(j - 1) / longs * kPI * 2.0f;
            const float x = cosf(b);
            const float z = sinf(b);
            const Rainbow::Vector3f pos(x * yr, y - stemHalfHeight, z * yr);
            triangleData->vertices[i * longs + j] = transform.MultiplyPoint3(pos);
        }
    }

    for (int i = 0; i < hlats * 2 - 1; i++)
    {
        int i0 = i;
        int i1 = i + 1;
        for (int j = 0; j < longs; j++)
        {
            int j0 = j;
            int j1 = (j + 1) % longs;
            triangleData->indices.push_back(i0 * longs + j0);
            triangleData->indices.push_back(i0 * longs + j1);
            triangleData->indices.push_back(i1 * longs + j1);
            triangleData->indices.push_back(i0 * longs + j0);
            triangleData->indices.push_back(i1 * longs + j1);
            triangleData->indices.push_back(i1 * longs + j0);
        }
    }
}

static void ExtractTriangleIndices(Rainbow::Mesh::TemporaryIndexContainer& indices, const Rainbow::SharedMeshData* data)
{
    const Rainbow::SharedMeshData::SubMeshContainer& subMeshes = data->GetSubMeshes();
    for (size_t i = 0; i < subMeshes.size(); ++i)
    {
        // ignore unsupported submesh format
        (void)Rainbow::Mesh::AppendTriangles(indices, i, subMeshes, data);
    }
}

static bool CalculateTriangleData(NavMeshTriangleData* triangleData, const SharedMeshBuildSource& src)
{
    if (src.shape == kNavMeshBuildSourceMesh)
    {
        const Rainbow::VertexData& vertexData = src.sharedMeshData->GetVertexData();

        const size_t vertexCount = vertexData.GetVertexCount();

        if (vertexCount == 0)
            return false;

        triangleData->vertices.resize_uninitialized(vertexCount);

        // Transform all vertices
        dynamic_array<Rainbow::Vector3f>::iterator outIt = triangleData->vertices.begin();
        Rainbow::StrideIterator<Rainbow::Vector3f> it = vertexData.MakeStrideIterator<Rainbow::Vector3f>(Rainbow::kShaderChannelVertex);
        Rainbow::StrideIterator<Rainbow::Vector3f> end = vertexData.MakeEndIterator<Rainbow::Vector3f>(Rainbow::kShaderChannelVertex);
        for (; it != end; ++it, ++outIt)
            *outIt = src.transform.MultiplyPoint3(*it);

        triangleData->indices.clear();
        ExtractTriangleIndices(triangleData->indices, src.sharedMeshData);
    }
    else if (src.shape == kNavMeshBuildSourceBox)
    {
        const Rainbow::Vector3f extents = Abs(src.size) * 0.5f;
        CreateCubeMesh(triangleData, src.transform, extents);
    }
    else if (src.shape == kNavMeshBuildSourceSphere)
    {
        // TODO: calculate tessellation based on size.
        const Rainbow::Vector3f extents = Abs(src.size) * 0.5f;
        const float radius = std::max(std::max(extents.x, extents.y), extents.z);
        CreateSphereMesh(triangleData, src.transform, radius, 10, 20);
    }
    else if (src.shape == kNavMeshBuildSourceCapsule)
    {
        // TODO: calculate tessellation based on size.
        const Rainbow::Vector3f extents = Abs(src.size) * 0.5f;
        const float radius = std::max(extents.x, extents.z);
        const float halfHeight = extents.y;
        CreateCapsuleMesh(triangleData, src.transform, radius, halfHeight, 10, 20);
    }
    else
    {
        return false;
    }

    bool reverseWinding = src.transform.GetDeterminant() < 0;
    if (reverseWinding)
    {
        const int indexCount = triangleData->indices.size();
        for (int i = 0; i < indexCount; i += 3)
        {
            int i0 = triangleData->indices[i + 0];
            int i1 = triangleData->indices[i + 1];
            triangleData->indices[i + 0] = i1;
            triangleData->indices[i + 1] = i0;
        }
    }
    return true;
}

static void TransformSources(dynamic_array<SharedMeshBuildSource>& bsources, const Rainbow::Vector3f& position, const Rainbow::Quaternionf& rotation)
{
    Rainbow::Matrix4x4f worldToLocal;
    worldToLocal.SetTRInverse(position, rotation);

    // Transform sources to navmesh local space
    for (SharedMeshBuildSource& source : bsources)
    {
        Rainbow::Matrix4x4f transform;
        MultiplyMatrices4x4(&worldToLocal, &source.transform, &transform);
        source.transform = transform;
    }

    // Transform the bounds
    for (SharedMeshBuildSource& src : bsources)
    {
        // Translate the local bounds of mesh and convert to Rainbow::MinMaxAABB
        if (src.shape == kNavMeshBuildSourceMesh)
        {
            Rainbow::AABB transformedBounds;
            TransformAABBSlow(src.localBounds, src.transform, transformedBounds);
            src.bounds = Rainbow::MinMaxAABB(transformedBounds);
        }
        else if (src.shape == kNavMeshBuildSourceBox)
        {
            src.bounds = CalculateBounds(src.transform, src.size);
        }
        else if (src.shape == kNavMeshBuildSourceSphere)
        {
            // NOTE: assumes ext.x == ext.y == ext.z;
            src.bounds = CalculateBounds(src.transform, src.size);
        }
        else if (src.shape == kNavMeshBuildSourceCapsule)
        {
            // NOTE: should be possible to get tighter bounds.
            src.bounds = CalculateBounds(src.transform, src.size);
        }
        else if (src.shape == kNavMeshBuildSourceModifierBox)
        {
            src.bounds = CalculateBounds(src.transform, src.size);
        }
        else
        {
            ErrorStringMsg("RuntimeNavMeshBuilder. Unknown source type: %i\n", (int)src.shape);
            continue;
        }
    }
}

static Rainbow::MinMaxAABB CombineAndCullBounds(dynamic_array<SharedMeshBuildSource>& bsources, const Rainbow::MinMaxAABB& bounds)
{
    Rainbow::MinMaxAABB inputBounds;
    for (size_t i = 0; i < bsources.size(); ++i)
    {
        SharedMeshBuildSource& src = bsources[i];
        if (IntersectAABBAABB(src.bounds, bounds))
            inputBounds.Encapsulate(src.bounds);
        else
        {
            bsources.erase_swap_back(bsources.begin() + i);
            --i;
        }
    }
    Rainbow::MinMaxAABB combinedBounds;
    IntersectionAABBAABB(bounds, inputBounds, &combinedBounds);
    return combinedBounds;
}

BuildNavMeshInfo* CreateBuildNavMeshInfo()
{
    return ENG_NEW_LABEL(BuildNavMeshInfo, jobAllocLabel) (jobAllocLabel);
}

void Cancel(BuildNavMeshInfo* info)
{
    if (info)
    {
        info->cancel = true;
    }
}

void SyncComputationFence(BuildNavMeshInfo* info)
{
    if (info)
    {
        SyncFence(info->fence);
    }
}

bool Done(const BuildNavMeshInfo* info)
{
    return (info == NULL) || IsFenceDone(info->fence);
}

float Progress(const BuildNavMeshInfo* info)
{
    if (!info)
        return 0.0f;

    int tilesDone = atomic_load_explicit(&info->tilesDone, Rainbow::memory_order_relaxed);
    float progress = tilesDone * info->invTileCount;
    return progress;
}

void DestroyBuildNavMeshInfo(BuildNavMeshInfo* info)
{
    if (info == NULL)
        return;

    SyncFence(info->fence);

    // Release shared data. Usually this has already happened
    // - but is needed when a build is purged before integration.
    ReleaseSharedMeshData(info);

    ENG_DELETE_LABEL(info, jobAllocLabel);
}

// Acquire data for async operations - should do very little work except extract info
void AcquireSharedMeshData(BuildNavMeshInfo* info, const SharedMeshBuildSource* sources, size_t nsources,
    const Rainbow::Vector3f& position, const Rainbow::Quaternionf& rotation, const Rainbow::AABB& localBounds)
{
    rcAllocSetCustom(RecastAlloc, RecastFree);
    Assert(info->sources.empty());
    info->sources.reserve(nsources);
    info->buildPosition = position;
    info->buildRotation = rotation;
    for (size_t i = 0; i < nsources; ++i)
    {
        const SharedMeshBuildSource& src = sources[i];
        if (src.shape == kNavMeshBuildSourceTerrain || src.shape == kNavMeshBuildSourceModifierBox)
        {
            continue;
        }
        info->sources.push_back(src);
    }
}

void ReleaseSharedMeshData(BuildNavMeshInfo* info)
{
    for (const SharedMeshBuildSource& src : info->sources)
    {
        if (src.sharedMeshData)
            src.sharedMeshData->Release();
    }
    info->sources.clear_dealloc();
}

enum WorkType
{
    kHashTile,
    kTileMesh
};

template<typename T>
struct AggregateJobData
{
    T * unitJobsData;
    void (*concurrentJobFunc)(T*, unsigned);
    void (*combineJobFunc)(T*);
    WorkType workType;
    unsigned int unitJobsCount;
    int nextUnitJob;
};

template<typename T>
void AggregateJobFunc(AggregateJobData<T>* aggregateJob, unsigned int workerIndex)
{
    if (aggregateJob->concurrentJobFunc != nullptr)
    {
#if ENABLE_PROFILER
        switch (aggregateJob->workType)
        {
            case kHashTile:
                PROFILER_BEGIN(gRuntimeBuildHashTileAggregate);
                break;
            case kTileMesh:
                PROFILER_BEGIN(gRuntimeBuildTileMeshAggregate);
                break;
        }
#endif
        unsigned int index = workerIndex;
        while (index < aggregateJob->unitJobsCount)
        {
            aggregateJob->concurrentJobFunc(aggregateJob->unitJobsData, index);

            index = (unsigned int)atomic_fetch_add_explicit(&(aggregateJob->nextUnitJob), 1, Rainbow::memory_order_relaxed);
        }

#if ENABLE_PROFILER
        switch (aggregateJob->workType)
        {
            case kHashTile:
                PROFILER_END(gRuntimeBuildHashTileAggregate);
                break;
            case kTileMesh:
                PROFILER_END(gRuntimeBuildTileMeshAggregate);
                break;
        }
#endif
    }
}

template<typename T>
void AggregateJobCombineFunc(AggregateJobData<T>* aggregateJob)
{
    if (aggregateJob->combineJobFunc != nullptr)
    {
        aggregateJob->combineJobFunc(aggregateJob->unitJobsData);
    }

    ENG_DELETE_LABEL(aggregateJob, jobAllocLabel);
}

int GetMaxWorkers(const BuildNavMeshInfo* info)
{
    return info->settings.maxJobWorkers;
}

template<typename T>
void ScheduleJobForEach(Rainbow::JobFence& fence, void concurrentJobFunc(T*, unsigned int), T* jobData, unsigned int iterationCount, void combineJobFunc(T*), Rainbow::JobPriority priority, WorkType workType)
{
    const unsigned int maxWorkers = GetMaxWorkers(jobData);
    if (maxWorkers == 0
        || maxWorkers >= iterationCount
        /*|| maxWorkers >= (1 + Rainbow::GetJobQueue().GetWorkerThreadCount())*/)
    {
        // Schedule all the jobs
        ScheduleJobForEach(fence, concurrentJobFunc, jobData, iterationCount, combineJobFunc, priority);
    }
    else
    {
        // Schedule only as many jobs as there are desired workers
        AggregateJobData<T>* aggregateJobData = ENG_NEW_LABEL(AggregateJobData<T>, jobAllocLabel) ();
        aggregateJobData->unitJobsData = jobData;
        aggregateJobData->concurrentJobFunc = concurrentJobFunc;
        aggregateJobData->combineJobFunc = combineJobFunc;
        aggregateJobData->workType = workType;
        aggregateJobData->unitJobsCount = iterationCount;
        aggregateJobData->nextUnitJob = maxWorkers;

        ScheduleJobForEach(fence, AggregateJobFunc<T>, aggregateJobData, maxWorkers, AggregateJobCombineFunc<T>, priority);
    }
}

void ScheduleNavMeshDataUpdate(const NavMeshData* data, BuildNavMeshInfo* info,
    const NavMeshBuildSettings& settings, const Rainbow::AABB& localBuildBounds)
{
    Assert(info->tiles.empty());
    Assert(info->removeTileIDs.empty());

    const Rainbow::Vector3f position = data->GetPosition();
    const Rainbow::Quaternionf rotation = data->GetRotation();

    TransformSources(info->sources, position, rotation);
    const Rainbow::MinMaxAABB buildBounds = Rainbow::MinMaxAABB(localBuildBounds);
    Rainbow::MinMaxAABB combinedBounds = CombineAndCullBounds(info->sources, buildBounds);

    if (settings.preserveTilesOutsideBounds)
    {
        if (buildBounds.IsValid())
        {
            if (combinedBounds.IsValid())
            {
                // Retain the vertical extents of the combined bounds
                combinedBounds.m_Min.x = buildBounds.m_Min.x;
                combinedBounds.m_Min.z = buildBounds.m_Min.z;
                combinedBounds.m_Max.x = buildBounds.m_Max.x;
                combinedBounds.m_Max.z = buildBounds.m_Max.z;
            }
            else
            {
                combinedBounds = buildBounds;
            }
        }
        else
        {
            combinedBounds = Rainbow::MinMaxAABB(Rainbow::Vector3f::zero, Rainbow::Vector3f::zero);
        }
    }
    else if (!combinedBounds.IsValid())
    {
        const NavMeshTileDataVector& existingTiles = data->GetNavMeshTiles();
        info->removeTileIDs.resize_uninitialized(existingTiles.size());
        for (size_t i = 0; i < existingTiles.size(); ++i)
        {
            info->removeTileIDs[i] = i;
        }

        ClearFenceWithoutSync(info->fence);
        return;
    }

    info->settings = settings;
    if (info->settings.preserveTilesOutsideBounds)
    {
        if (data->GetNavMeshTiles().size() > 0)
        {
            // Revert the structural settings back to the the stored values
            const NavMeshBuildSettings& storedSettings = data->GetNavMeshBuildSettings();
            info->settings.agentTypeID = storedSettings.agentTypeID;
            info->settings.agentRadius = storedSettings.agentRadius;
            info->settings.agentHeight = storedSettings.agentHeight;
            info->settings.agentSlope = storedSettings.agentSlope;
            info->settings.agentClimb = storedSettings.agentClimb;
            info->settings.manualCellSize = storedSettings.manualCellSize;
            info->settings.cellSize = storedSettings.cellSize;
            info->settings.manualTileSize = storedSettings.manualTileSize;
            info->settings.tileSize = storedSettings.tileSize;
        }

        // Snap minY to the voxel grid in order to stabilize the heightfield origin
        const float ch = info->settings.cellSize * 0.5f;
        combinedBounds.m_Min.y = floorf(combinedBounds.m_Min.y / ch) * ch;
    }

    ConfigureConfig(info->config, info->settings, combinedBounds);

    TileFilter tileFilter;
    const int tileCount = CalculateTileIndices(info->tileXZ, info->config, tileFilter, info->settings.preserveTilesOutsideBounds);
    if (tileCount == 0)
    {
        ReleaseSharedMeshData(info);
        ClearFenceWithoutSync(info->fence);
        return;
    }

    if (!info->settings.preserveTilesOutsideBounds)
        tileFilter = TileFilter();

    info->tiles.resize(tileCount);
    info->tilesDone = 0;
    info->invTileCount = 1.0f / tileCount;

    // Compute the hashes for all tiles
    Rainbow::JobFence hashFence;
    ScheduleJobForEach(hashFence, ComputeTileHashJob, info, tileCount, (void (*)(BuildNavMeshInfo *))nullptr, Rainbow::kHighJobPriority, kHashTile);
    Rainbow::SyncFence(hashFence);

    // Figure out what needs to be removed and what needs to be built
    const NavMeshTileDataVector& existingTiles = data->GetNavMeshTiles();
    dynamic_array<int> computeTileIDs(kMemTempAlloc);
    ClassifyTilesToRemoveAndCompute(existingTiles, info->tiles, tileFilter, info->removeTileIDs, computeTileIDs);
    int computeCount = CompactTilesAndLocations(computeTileIDs, info->tiles, info->tileXZ);

    if (computeCount > 0 && !info->cancel)
    {
        ScheduleJobForEach(info->fence, ComputeTileMeshJob, info, computeCount, ReleaseSharedMeshData, Rainbow::kHighJobPriority, kTileMesh);
    }
    else
    {
        ReleaseSharedMeshData(info);
        ClearFenceWithoutSync(info->fence);
    }
}

#include "NavMeshManager.h"
void IntegrateNavMeshDataUpdate(NavMeshData* data, BuildNavMeshInfo* info, const Rainbow::AABB& localBounds)
{
    SyncFence(info->fence);

    // Skip if nothing changed
    if (info->removeTileIDs.empty() && info->tiles.empty())
        return;
    // Remove the usage of the data that is to be removed.
    NavMeshManager& manager = GetNavMeshManager();
    dynamic_array<unsigned long long> surfaceInstanceIDs(kMemTempAlloc);
    manager.GetSurfaceIDsFromData(surfaceInstanceIDs, data);

    for (const auto& surfaceInstanceID : surfaceInstanceIDs)
    {
        manager.RemoveTiles(surfaceInstanceID, info->removeTileIDs);
    }

    dynamic_array<int> newTileIDs(kMemTempAlloc);
    // is data still valid ?!
    {
        data->UpdateTiles(info->removeTileIDs, info->tiles, newTileIDs);
        if (info->settings.preserveTilesOutsideBounds && data->GetNavMeshTiles().size() > 0)
        {
            Rainbow::MinMaxAABB updatedBounds = Rainbow::MinMaxAABB(data->GetSourceBounds());
            updatedBounds.Encapsulate(localBounds);
            data->SetSourceBounds(Rainbow::AABB(updatedBounds));
        }
        else
        {
            data->SetSourceBounds(localBounds);
        }
        data->SetNavMeshBuildSettings(info->settings);
    }

    for (const auto& surfaceInstanceID : surfaceInstanceIDs)
    {
        // NOTE: No carving can be happening when doing this!!!
        manager.UpdateSurface(surfaceInstanceID, info->settings, newTileIDs);
    }
}
