/**
* file : SceneEffectCircle
* func : 场景效果 （圆）
* by : yangzy
*/
#include "SceneEffectCircle.h"
#include "world_types.h"
#include "world.h"
#include "SandboxMacros.h"
#include "SandboxPlane.h"

using namespace MNSandbox;
using namespace Rainbow;

SceneEffectCircle::SceneEffectCircle()
{
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_vecDrawCircleLine.resize(0);
	m_vecDrawDegreeLine.resize(0);
	m_vecDrawFan.resize(2);
}

SceneEffectCircle::SceneEffectCircle(const MNCoord3f& center, float radius, float ratio, CURVEFACEMTLTYPE mtltype)
{
	m_MtlType = mtltype;
	m_vCenter = center;
	m_radius = radius;
	m_originRadius = radius;

	m_vecDrawCircleLine.resize(0);
	m_vecDrawDegreeLine.resize(0);
	m_vecDrawFan.resize(2);

	SetRatio(ratio, MNCoord3f(0.0));
}

SceneEffectCircle::~SceneEffectCircle()
{
}

// 设置比率
void SceneEffectCircle::SetRatio(float ratio, MNCoord3f optCurShereDir)
{
	m_radius = m_originRadius * ratio;
	m_optSphereRadius = ms_optSphereRadius * ratio;
	m_circleLineMinWidth = ms_circleLineMinWidth * ratio;
	m_circleLineWidth = ms_circleLineWidth * ratio;
	m_degreeLineWidth = ms_degreeLineWidth * ratio;
	m_degreeLineWidth = m_degreeLineWidth < 2 ? 2 : m_degreeLineWidth;
	m_circleLineWidth = (m_circleLineWidth < 2&& m_circleLineWidth !=0) ? 2 : m_circleLineWidth;
	m_degreeLineLen = ms_degreeLineLen * ratio;
	optCurShereDir == MNCoord3f(0.0) ?
		(m_optSpherePos = MNCoord3f(0.0)) : 
		(m_optSpherePos = optCurShereDir * m_radius + m_vCenter);
}

void SceneEffectCircle::OnClear()
{
	for (auto& item : m_vecDrawCircleLine)
	{
		if (item._line)
		{
			SANDBOX_DELETE(item._line);
		}
	}

	for (auto& item : m_vecDrawDegreeLine)
	{
		if (item._line)
		{
			SANDBOX_DELETE(item._line);
		}
	}

	for (auto& item : m_vecDrawFan)
	{
		if (item)
		{
			SANDBOX_DELETE(item);
		}
	}

	SANDBOX_DELETE(m_selectedline._line);
	SANDBOX_DELETE(m_optSphere);
	SANDBOX_DELETE(m_centerSphere);
}

void SceneEffectCircle::Refresh()
{
	OnClear();
	RefreshCircleFrame(m_vCenter, m_radius);
}

void SceneEffectCircle::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow || m_radius == 0)
	{
		return;
	}

	for (auto& item : m_vecDrawCircleLine)
	{
		if (item._line)
		{
			item._line->SetDrawPos(item._startDrawPos);
			item._line->OnDraw(pWorld);
		}
	}

	for (auto& item : m_vecDrawDegreeLine)
	{
		if (item._line)
		{
			item._line->SetDrawPos(item._startDrawPos);
			item._line->OnDraw(pWorld);
		}
	}

	if (m_pickPosBeg != MNCoord3f(0.0) && m_pickPosEnd != MNCoord3f(0.0) && m_emCurRotateDir != EncircleDir::INVALID_AXIS)
	{
		static auto funGetVec = [](MNCoord3f pos, Vector3f centerPos, int radius)->Vector3f {
			//确保Normalize
			MNCoord3f vecOpt((pos - centerPos).m_x, (pos - centerPos).m_y, (pos - centerPos).m_z);
			vecOpt.Normalize();
			vecOpt *= radius;
			return vecOpt + centerPos;
		};

		auto centerPos = m_vCenter.ToVector3();
		auto vecBeg = funGetVec(m_pickPosBeg, centerPos, m_radius);
		auto vecEnd = funGetVec(m_pickPosEnd, centerPos, m_radius);

		auto vecFrom = vecBeg - centerPos;
		auto vecTo = vecEnd - centerPos;

		Matrix3x3f matrixRotate;
		QuaternionfToMatrix(FromToQuaternion(m_originRotationAxis, m_curRotationAxis), matrixRotate);

		matrixRotate.Transpose();
		vecFrom = matrixRotate.MultiplyPoint3(vecFrom);
		vecTo = matrixRotate.MultiplyPoint3(vecTo);

		vecFrom.Normalize();
		vecTo.Normalize();

		std::string szTexture = (m_emCurRotateDir == EncircleDir::X_AXIS ? "blocks/red.png" : (m_emCurRotateDir == EncircleDir::Y_AXIS ? "blocks/green.png" : "blocks/blue.png"));
		auto angle = Rad2Deg(Angle(vecFrom, vecTo));
		float radian = HALF_PI * 0.5;

		if (Abs(angle - 180) > kEpsilon)
		{
			auto vecRotate = QuaternionToEulerAngle(FromToQuaternion(vecFrom, vecTo)) * 0.5;
			angle = (m_emCurRotateDir == EncircleDir::X_AXIS ? vecRotate.x : (m_emCurRotateDir == EncircleDir::Y_AXIS ? vecRotate.y : vecRotate.z));

			radian = Angle(vecFrom, vecTo) * 0.5;
			radian = angle / (180.0 / ONE_PI);
		}
		
		Matrix3x3f matrix;
		matrix.SetAxisAngle(m_curRotationAxis, radian);
		Vector3f vecHalf = matrix.MultiplyPoint3(vecBeg - centerPos) + centerPos;

		m_vecDrawFan[0]->draw(centerPos, vecBeg, vecHalf, szTexture);
		m_vecDrawFan[1]->draw(centerPos, vecHalf, vecEnd, szTexture);
	}

	if (m_selectedline._line)
	{
		m_selectedline._line->SetDrawPos(m_selectedline._startDrawPos);
		m_selectedline._line->OnDraw(pWorld);
	}

	if (m_optSphere)
	{
		m_optSphere->OnDraw(pWorld);
	}

	if (m_centerSphere)
	{
		m_centerSphere->OnDraw(pWorld);
	}
}

void SceneEffectCircle::RefreshCircleFrame(const MNCoord3f& center, int radius)
{
	if (m_curRotationAxis == Vector3f(0.0))
	{
		return;
	}

	Vector3f posBeg = Vector3f(0.0);
	Vector3f posCenter(center.m_x, center.m_y, center.m_z);
	Matrix3x3f matrix;
	float angle = 0.0;

	Plane plane(posCenter, m_curRotationAxis);
	Vector3f posOther(posCenter.x + radius, posCenter.y + radius, posCenter.z + radius);

	auto begDir = plane.VectorProjection(posOther - posCenter);
	begDir.Normalize();
	posBeg = begDir * (float)radius + posCenter;

	//绘制圆
	{
		//构造圆线段
		angle = 0.0;
		m_vecDrawCircleLine.resize(360 / m_segmentAngle);
		for (auto& item : m_vecDrawCircleLine)
		{
			auto radians = angle / (180.0 / ONE_PI);
			matrix.SetAxisAngle(m_curRotationAxis, radians);

			auto startpos = (matrix.MultiplyPoint3(posBeg - posCenter) + posCenter);
			angle += m_segmentAngle;

			radians = angle / (180.0 / ONE_PI);
			matrix.SetAxisAngle(m_curRotationAxis, radians);
			auto endpos = (matrix.MultiplyPoint3(posBeg - posCenter) + posCenter);

			item._line = SANDBOX_NEW(SceneEffectLine, startpos, endpos, m_circleLineWidth, m_MtlType, false);
			item._startDrawPos = startpos;
			item._endDrawPos = endpos;
			item._line->SetColor(m_circleColor);

			/*auto v1 = posCenter - m_cameraPos;
			auto v2 = endpos - posCenter;
			float sqrMag = v1.x * v2.x + v1.y * v2.y + v1.z * v2.z;
			if (sqrMag > 1e-6f)
			{
				auto width = item._line->GetWidth();
				item._line->SetWidth(m_circleLineMinWidth);
			}*/
			item._line->SetCurveFaces(m_CurveFaces);
			item._line->Refresh();
		}
	}

	//绘制刻度
	if (m_degree > 0)
	{
		//构造刻度线段
		angle = 0.0;
		m_vecDrawDegreeLine.resize(360 / m_degree);
		for (auto& item : m_vecDrawDegreeLine)
		{
			auto radians = angle / (180.0 / ONE_PI);
			matrix.SetAxisAngle(m_curRotationAxis, radians);

			auto startpos = (matrix.MultiplyPoint3(posBeg - posCenter) + posCenter);
			auto vec = posCenter - startpos;
			vec.Normalize();
			vec *= m_degreeLineLen;
			auto endpos = vec + startpos;
			angle += m_degree;

			item._line = SANDBOX_NEW(SceneEffectLine, startpos, endpos, m_degreeLineWidth, m_MtlType, false);
			item._startDrawPos = startpos;
			auto temp = BlockVector(0,0,0,255);
			if (m_emCurRotateDir == EncircleDir::X_AXIS)
				temp.x = 255;
			else if(m_emCurRotateDir == EncircleDir::Y_AXIS)
				temp.y = 255;
			else if (m_emCurRotateDir == EncircleDir::Z_AXIS)
				temp.z = 255;
			if(m_emCurRotateDir== EncircleDir::INVALID_AXIS)
				item._line->SetColor(m_circleColor);
			else
				item._line->SetColor(temp);
			item._line->Refresh();
		}
	}

	if (m_pickPosBeg != MNCoord3f(0.0) && m_isSelected)
	{
		m_selectedline._line = SANDBOX_NEW(SceneEffectLine, m_pickPosBeg, posCenter, m_degreeLineWidth, m_MtlType, false);
		m_selectedline._startDrawPos = m_pickPosBeg;
		m_selectedline._line->SetColor(m_circleColor);
		m_selectedline._line->Refresh();
	}

	//绘制圆心
	{
		m_centerSphere = SANDBOX_NEW(SceneEffectSphere, m_vCenter, 10);
		m_centerSphere->SetColor(m_circleColor);
		m_centerSphere->Refresh();
	}

	//绘制操作点
	if (m_optSpherePos != MNCoord3f(0.0))
	{
		m_optSphere = SANDBOX_NEW(SceneEffectSphere, m_optSpherePos, m_optSphereRadius);
		m_optSphere->SetColor(m_optSphereColor);
		m_optSphere->SetCurveFaces(m_CurveFaces);
		m_optSphere->Refresh();
	}

	//绘制扇形
	if (m_pickPosBeg != MNCoord3f(0.0) && m_pickPosEnd != MNCoord3f(0.0))
	{
		m_vecDrawFan.resize(2);
		for (auto& item : m_vecDrawFan)
		{
			item = SANDBOX_NEW(SceneEffectFan);
			item->Refresh();
		}
	}
}

void SceneEffectCircle::SetDegree(int nDegree, bool bRefresh)
{
	m_degree = nDegree;
	nDegree > 0 ? m_vecDrawDegreeLine.resize(360 / m_degree) : m_vecDrawDegreeLine.resize(0);

	if (bRefresh)
	{
		Refresh();
	}
}

// 设置切分度数
void SceneEffectCircle::SetSegmentAngle(int nSegmentAngle, bool bRefresh)
{
	m_segmentAngle = nSegmentAngle;
	
	if (bRefresh)
	{
		Refresh();
	}
}

// 设置鼠标pick位置
void SceneEffectCircle::SetPickPos(MNCoord3f begPos, MNCoord3f endPos, EncircleDir emCurRotateDir, bool bRefresh)
{
	m_pickPosBeg = begPos; 
	m_pickPosEnd = endPos; 
	m_emCurRotateDir = emCurRotateDir;

	if (bRefresh)
	{
		Refresh();
	}
}

// 设置旋转轴
void SceneEffectCircle::SetRotationAxis(Vector3f curAxis, Vector3f originAxis, bool bRefresh)
{
	m_curRotationAxis = curAxis;
	m_originRotationAxis = originAxis;

	m_curRotationAxis.Normalize();
	m_originRotationAxis.Normalize();

	if (bRefresh)
	{
		Refresh();
	}
}

bool SceneEffectCircle::RayCircle(MNSandbox::Ray & ray, MNCoord3f& targetPos)
{
	for (auto& item : m_vecDrawCircleLine)
	{
		if (item._line)
		{
			MNSandbox::Ray rayLine(item._startDrawPos, item._endDrawPos - item._startDrawPos);
			float lenLineSquare = rayLine.GetDir().GetLengthSquare();
			auto plane = Plane::CreateByPointX3(item._startDrawPos, item._endDrawPos, ray.GetPos());

			//投影
			auto dir = ray.GetDir();
			dir.Normalize();
			auto vecProjection = plane.VectorProjection(dir);
			MNSandbox::Ray rayPlane(ray.GetPos(), vecProjection);

			float dstDist;

			// 平面交点
			if (!rayLine.IntersectRay(rayPlane, &targetPos, &dstDist, nullptr))
			{
				continue;
			}
				
			// 确保是线段
			if (dstDist * dstDist > lenLineSquare)
			{
				continue;
			}
				
			MNCoord3f vecPlane(targetPos - ray.GetPos());

			// 两条射线之间的最短线段
			MNCoord3f targetVec(vecPlane - (dir * vecPlane.DotProduct(dir))); 

			float distSquare = targetVec.GetLengthSquare();
			if (distSquare <= m_circleLineWidth * m_circleLineWidth *5)
			{
				AdjustDegreePos(targetPos);
				return true;
			}
		}
	}

	targetPos = MNCoord3f(0.0);
	return false;
}

void SceneEffectCircle::AdjustDegreePos(MNCoord3f& targetPos)
{
	// 根据刻度调整(距离最短)
	if (m_vecDrawDegreeLine.size() > 0)
	{
		auto keyTargetPos = MNCoord3f(m_vecDrawDegreeLine[0]._startDrawPos.m_x, m_vecDrawDegreeLine[0]._startDrawPos.m_y, m_vecDrawDegreeLine[0]._startDrawPos.m_z);
		MNCoord3f vecLine = targetPos - keyTargetPos;
		for (auto& line : m_vecDrawDegreeLine)
		{
			auto posBeg = MNCoord3f(line._startDrawPos.m_x, line._startDrawPos.m_y, line._startDrawPos.m_z);
			if (vecLine.GetLength() > (targetPos - posBeg).GetLength())
			{
				keyTargetPos = posBeg;
				vecLine = targetPos - posBeg;
			}
		}

		targetPos = keyTargetPos;
	}
}
