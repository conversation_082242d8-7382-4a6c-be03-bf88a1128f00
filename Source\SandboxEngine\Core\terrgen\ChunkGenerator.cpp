/*
*	file:	ChunkGenerator
*/
#include "ChunkGenerator.h"

#include "chunk.h"
#include "world.h"
#include "NoiseGeneratorOctaves.h"
#include "EcosysManager.h"
#include "Ecosystem.h"
#include "blocks/special_blockid.h"
#include "EcosysUnit_Lakes.h"
#include "EcosysUnit_Dungeons.h"
#include "LandformMod_Caves.h"
#include "EcosysUnit_VoxelModel.h"
#include "ActorManagerInterface.h"
//#include "OgreThread.h"
#include "Platforms/PlatformInterface.h"
#include "WorldProxy.h"
#include "chunkio.h"
#include "WorldManager.h"

#include <deque>

//#include "Profiny.h"
#include "OgreTimer.h"

#include "BiomeRegionGenConfig.h"
#include "PlayManagerInterface.h"
#include "EcosysUnit_DesertVillage.h"
#include "Jobs/Jobs.h"
#include "Jobs/JobBatchDispatcher.h"
#include "EcosysUnit_FishingVillageBuild.h"
#include "EcosysUnit_IsLandBuild.h"
#include "EcosysUnit_ShipWrecks.h"
#include "EcosysUnit_IceVillage.h"
#include "EcosysUnit_IceAltar.h"
#include "EcosysUnit_City.h"
#include "EcosysUnit_RoadBuild.h"
#include "Optick/optick.h"
#include "blocks/BlockMaterialMgr.h"
#include "UgcEcosysBuildInterface.h"
#include "BiomeModConfigInterface.h"
using namespace MINIW;

//----------------------------------------------------------------------------------------------------------
class GenTerrainThread
{
public:
	GenTerrainThread(ChunkGenerator *provider, World *pWorld);
	virtual ~GenTerrainThread();

	bool addRequest(CHUNK_INDEX index, bool isFast);
	bool addFindRequest(int centerX, int centerZ, int blockType, int range, int uin, int findType);
	bool popResult(GenTerrResult& out);
	bool popFindResult(FindTerrResult& out);
	virtual const char* getClassName()
	{
		return "GenTerrainThread";
	}

	void run();
	void activeJob();
	void stop();
private:
	void _runFind(const ChunkRequestFindData& findData, FindTerrResult& result);
private:
	Rainbow::JobFence m_jobFence;
	ChunkGenerator* m_Provider;
	World *m_pWorld;
	Rainbow::Mutex m_Mutex;
	std::deque<ChunkRequestGen> m_GenRequests;
	std::deque<GenTerrResult> m_GenResults;

	std::deque<ChunkRequestFindData>m_FindRequests;
	std::deque<FindTerrResult>m_FindResults;
	bool m_Shutdown;
};

static void GenTerrainJobFunc(GenTerrainThread* thread)
{
	OPTICK_EVENT();
	if (thread)
		thread->run();
}

GenTerrainThread::GenTerrainThread(ChunkGenerator *provider, World *pWorld) : m_Provider(provider), m_pWorld(pWorld)
{
	m_Shutdown = false;
}

GenTerrainThread::~GenTerrainThread()
{
	//std::deque<GenTerrResult>::iterator iter = m_GenResults.begin(); // 释放改到了对象析构中 codeby: chenzihang
	//for(; iter!=m_GenResults.end(); iter++)
	//{
	//	OGRE_DELETE_ARRAY(iter->chunkdata);
	//	OGRE_DELETE_ARRAY(iter->biomes);
	//	OGRE_DELETE_ARRAY(iter->airlandbiomes);
	//}
	stop();
}

void GenTerrainThread::stop()
{
	m_Shutdown = true;
	SyncFence(m_jobFence);
}

void GenTerrainThread::activeJob()
{
	if (IsFenceDone(m_jobFence))
	{
		Rainbow::JobBatchDispatcher dispatcher;
		dispatcher.ScheduleJob(m_jobFence, GenTerrainJobFunc, this);
		dispatcher.KickJobs();
	}
}

bool GenTerrainThread::addRequest(CHUNK_INDEX index, bool isFast)
{
	if (m_Shutdown) return false;

	{
		Rainbow::Mutex::AutoLock locker(m_Mutex);
		for (auto iter = m_GenRequests.begin(); iter != m_GenRequests.end(); iter++)
		{
			if (iter->index == index)
			{
				if (isFast == true)
				{
					m_GenRequests.erase(iter);
					break;
				}
				else
				{
					return false;
				}
			}
		}
		if (isFast)
		{
			m_GenRequests.push_front({ isFast,index });
		}
		else
		{
			m_GenRequests.push_back({ isFast,index });
		}
	}
	activeJob();
	return true;
}

bool GenTerrainThread::addFindRequest(int centerX, int centerZ, int blockType, int range, int uin, int findType)
{
	if (m_Shutdown) return false;
	EcosystemManager* p = m_Provider->getBiomeManager();
	p = dynamic_cast<EcosysMgrSimple*>(p);
	if (p != NULL)
	{
		return false;
	}

	ChunkRequestFindData data;
	data.blockType = blockType;
	data.centerX = centerX;
	data.centerZ = centerZ;
	data.range = range;
	data.uin = uin;
	data.findType = findType;
	{
		Rainbow::Mutex::AutoLock locker(m_Mutex);
		if (std::find(m_FindRequests.begin(), m_FindRequests.end(), data) != m_FindRequests.end()) // 避免重复加载
		{
			//assert(false);
			return false;
		}
		m_FindRequests.push_back(data);
	}
	activeJob();
	return true;
}

bool GenTerrainThread::popResult(GenTerrResult& out)
{
	if (m_Shutdown) return false;

	int cmdtodo = 0;
	{
		Rainbow::Mutex::AutoLock locker(m_Mutex);
		cmdtodo = m_GenRequests.size();
	}
	if (cmdtodo)
		activeJob();

	Rainbow::Mutex::AutoLock locker(m_Mutex);
	if (m_GenResults.empty())
		return false;

	GenTerrResult& ret = m_GenResults.front();
	out = ret;
	ret.clear();
	m_GenResults.pop_front();

	return true;
}

bool GenTerrainThread::popFindResult(FindTerrResult& out)
{
	if (m_Shutdown) return false;
	int cmdtodo = 0;
	{
		Rainbow::Mutex::AutoLock locker(m_Mutex);
		cmdtodo = m_FindRequests.size();
	}
	if (cmdtodo)
		activeJob();

	Rainbow::Mutex::AutoLock locker(m_Mutex);
	if (m_FindResults.empty())
		return false;

	FindTerrResult& ret = m_FindResults.front();
	out = ret;
	m_FindResults.pop_front();
	return true;
}

void GenTerrainThread::run()
{
	if (m_Shutdown) return;
	OPTICK_EVENT();
	m_Mutex.Lock();
	bool hasChunkReq = !m_GenRequests.empty();
	m_Mutex.Unlock();
	if (hasChunkReq)
	{
		m_Mutex.Lock();
		ChunkRequestGen cmd = m_GenRequests.front();
		m_GenRequests.pop_front();
		m_Mutex.Unlock();
		CHUNK_INDEX& chunkindex = cmd.index;
		GenTerrResult result;
		m_Provider->createChunkData(result, chunkindex.x, chunkindex.z);
		result.index = chunkindex;

		//GenTerrResult result2;
		//m_Provider->createChunkData(result2, chunkindex.x, chunkindex.z);
		//result2.index = chunkindex;
		//
		////test
		//if (GetPixelMapMgrInterface()) {
		//	GetPixelMapMgrInterface()->PushGenTerrResult(result2);
		//}

		m_Mutex.Lock();
		if (cmd.isFast)
		{
			m_GenResults.push_front(result);
		}
		else
		{
			m_GenResults.push_back(result);
		}
		m_Mutex.Unlock();
		result.clear();
	}

	m_Mutex.Lock();
	bool hasFindReq = !m_FindRequests.empty();
	m_Mutex.Unlock();

	if (hasFindReq)
	{
		m_Mutex.Lock();
		ChunkRequestFindData request = m_FindRequests.front();
		m_FindRequests.pop_front();
		m_Mutex.Unlock();
		FindTerrResult ret;
		_runFind(request, ret);
		m_Mutex.Lock();
		m_FindResults.push_back(ret);
		m_Mutex.Unlock();
	}
}

void GenTerrainThread::_runFind(const ChunkRequestFindData& request, FindTerrResult& ret)
{
	if (!m_Provider || !m_Provider->getBiomeManager())
	{
		return;
	}
	if (ChunkRequestFindData::Normal == request.findType)
	{
		WCoord pos;
		bool find = m_Provider->getBiomeManager()->findEcosystemOn(pos, request.centerX, request.centerZ, request.range, request.range, request.blockType);
		ret.pos = pos;
		ret.sucess = find;
		ret.uin = request.uin;
	}
	else if (ChunkRequestFindData::SpecialCity == request.findType)
	{
		m_Provider->getBiomeManager()->findSpecialBiomeOn(request.centerX, request.centerZ, request.range, request.range, TerrainSpecialDataType::TerrainSpecialData_SpecialSubOrderBiome, ret.specialBiome);
		ret.sucess = true; //不论怎样都返回true.
		ret.uin = request.uin;
		ret.findType = request.findType;
		ret.pos.x = request.centerX;
		ret.pos.z = request.centerZ;
	}
}

//----------------------------------------------------------------------------------------------------------
ChunkGenerator::ChunkGenerator(World* pworld, unsigned int seed1, unsigned int seed2, ChunkIndex startchunk, ChunkIndex endchunk) : 
m_World(pworld), m_BiomeMgr(NULL)
{
	m_GenChunks.reserve(10240);
	m_WorldSeed = seed1;
	m_WorldSeed = (m_WorldSeed << 16) ^ seed2;
	m_RandGen = ENG_NEW(ChunkRandGen);
	m_RandGen->setSeed64(m_WorldSeed);

	m_StartChunkX = startchunk.x;
	m_StartChunkZ = startchunk.z;
	m_EndChunkX = endchunk.x;
	m_EndChunkZ = endchunk.z;

	m_GenThread = ENG_NEW(GenTerrainThread)(this,pworld);
	m_spawnPointRangeNum = GetLuaInterfaceProxy().get_lua_const()->spawnPointRangeNum;
#ifdef BUILD_MINI_EDITOR_APP
	m_vecTerrainChunkInfo.clear();
#endif 
}

ChunkGenerator::~ChunkGenerator()
{
	for (size_t i = 0; i < m_ModelGens.size(); i++)
	{
		ENG_DELETE(m_ModelGens[i]);
	}

	ENG_DELETE(m_RandGen);
	ENG_DELETE(m_GenThread);
	ENG_DELETE(m_BiomeMgr);
}

void ChunkGenerator::startThread()
{
	//m_GenThread->start();
}

void ChunkGenerator::stopThread()
{
	m_GenThread->stop();
}

Chunk* ChunkGenerator::requesChunk(int chunkx, int chunkz, bool isFast)
{
	if (canProvideChunk(chunkx, chunkz))
	{
		m_GenThread->addRequest(ChunkIndex(chunkx, chunkz), isFast);
	}

	return NULL;
}

void ChunkGenerator::requesAllChunk()
{
	for (int x = m_StartChunkX; x <= m_EndChunkX; x++)
	{
		for (int z = m_StartChunkZ; z <= m_EndChunkZ; z++)
		{
			requesChunk(x, z, false);
		}
	}
}

bool ChunkGenerator::IsChunkGened(CHUNK_INDEX index)
{
	return m_GenChunks.find(index) != m_GenChunks.end();
}

void ChunkGenerator::findTerrain(int centerX, int centerZ, int blockType, int range, int uin)
{
	m_GenThread->addFindRequest(centerX, centerZ, blockType, range, uin, ChunkRequestFindData::Normal);
}

void ChunkGenerator::findCityTerrain(int centerX, int centerZ, int range, int uin)
{
	m_GenThread->addFindRequest(centerX, centerZ, 0, range, uin, ChunkRequestFindData::SpecialCity);
}
/*
extern profiny::Timer g_PreciseTimer;
int g_PopulateNum = 0;
double g_PopulateTime = 0;
*/

bool ChunkGenerator::check(bool bReset)
{
	OPTICK_EVENT();
	//CHUNK_INDEX index;
	//unsigned char *biomes;
	//unsigned char *airlandsbiomes = NULL;
	//unsigned short *chunkdata = m_GenThread->popResult(index, biomes,airlandsbiomes);
	//if(chunkdata == NULL) return;


	GenTerrResult result;
	if (!m_GenThread->popResult(result))
		return false;
	OPTICK_EVENT(); 

	CHUNK_INDEX& index = result.index;
	BLOCK_DATA_TYPE* chunkdata = result.chunkdata;
	unsigned char* biomes = result.biomes;
	unsigned char* airlandsbiomes = result.airlandbiomes;

	Chunk* pchunk = ENG_NEW(Chunk)(m_World, index.x, index.z, chunkdata);
	SharedChunkData& chunkData = pchunk->GetWritableSharedChunkData();

	memcpy(chunkData.getBiomeIDArray().data(), biomes, chunkData.getBiomeIDArray().size());
	if (airlandsbiomes != NULL) {
		chunkData.initAirLandsBiomeID();
		memcpy(chunkData.getAirLandsBiomeIDPtr(), airlandsbiomes, CHUNK_BLOCK_Z * CHUNK_BLOCK_X);
	}

	//g_PreciseTimer.start();

	pchunk->generateSkylightMap();
	if (!hasSky()) pchunk->resetRelightChecks();


	if (!result._specialPos.empty() || !result._specialBiome.empty() || !result._chunkBiome.empty())
		pchunk->SetChunkSpecialPos(result._specialPos, result._specialBiome, result._chunkBiome);

	if (m_World->addChunk(pchunk))
	{
		//g_PreciseTimer.start();
		m_World->populateChunk(pchunk);

#ifdef BUILD_MINI_EDITOR_APP
		m_World->InsertNewTerrainChunk(index, pchunk);
#endif

		//g_PopulateTime += g_PreciseTimer.getElapsedTime();
		//g_PopulateNum++;
		//LOG_INFO("populatenum: %f, %d, %f", float(g_PopulateTime), g_GenLightNum, float(g_PopulateTime/g_PopulateNum));
		//建筑都只在普通地形生成
		auto terrainType = m_World->getTerrainType();
		if (TERRAIN_NORMAL == terrainType)
		{
			if (m_World->getCurMapID() == 0)
			{
				//bool isAddBuild = true;
				do {
					if (GetEcosysUnitCityBuild().addToWorld(m_World, index)) break;
					if (GetDesertTradeCaravanMgrInterface()->checkGenVillage())
					{
						GetDesertTradeCaravanMgrInterface()->setShouldExplore(true);
						if (GetEcosysUnitDesertVillage().addToWorld(m_World->getWorldProxy(), index)) break;
					}
					if (GetEcosysUnitIceAltarBuild().addToWorld(m_World->getWorldProxy(), index)) break;
					if (GetEcosysUnitFishingVillageBuild().addToWorld(m_World->getWorldProxy(), index)) break;
					if (GetEcosysUnitIsLandBuild().addToWorld(m_World->getWorldProxy(), index)) break;
					if (GetEcosysUnitShipWrecks().addToWorld(m_World->getWorldProxy(), index)) break;
					if (GetEcosysUnitIceVillageBuild().addToWorld(m_World->getWorldProxy(), index)) break;
					//ugc添加建筑, 其中可能会添加多个建筑
					if (GetUgcEcosysBuildInterface() && GetUgcEcosysBuildInterface()->addToWorldMulTiple(m_World, index)) break;

					//isAddBuild = false;
				} while (false);
				//if (isAddBuild)
				//{
				//	//auto mapData = g_WorldMgr->getMapData(m_World->getCurMapID(), true);
				//	LOG_DEBUG("aaa");
				//}
			}
		}
		else if (TERRAIN_SOC == terrainType)
		{
			if (m_World->getCurMapID() == 0)
			{

				GetEcosysUnitCityBuild().addToWorld(m_World, index);
				// GetEcosysUnitRoadBuild().addToWorld(m_World, index);
			}
		}
	}
	return true;
}

void ChunkGenerator::checkFind()
{
	FindTerrResult result;
	if (!m_GenThread->popFindResult(result))
		return;
	if (result.sucess)
	{
		if (ChunkRequestFindData::Normal == result.findType)
		{
			m_World->getActorMgr()->spawnDesertTradeCaravan(result.uin, result.pos, g_WorldMgr->getWorldTime());
		}
		else if (ChunkRequestFindData::SpecialCity == result.findType)
		{
			//这里直接调用了业务逻辑.
			if (m_World->getCityMgr())
			{
				std::sort(result.specialBiome.begin(), result.specialBiome.end(), [pos = result.pos](const ChunkSpecialBiomeData& A, const ChunkSpecialBiomeData& B) {
					if (B.type == A.type && A.type == ChunkContainSpecialBiome::SpecialBiome_City)
					{
						int centerXA = (A.leftDown.x + A.range.x() / 2) - pos.x;
						int centerZA = (A.leftDown.z + A.range.y() / 2) - pos.z;
						int centerXB = (B.leftDown.x + B.range.x() / 2) - pos.x;
						int centerZB = (B.leftDown.z + B.range.y() / 2) - pos.z;
						return (centerXA * centerXA + centerZA * centerZA - centerXB * centerXB - centerZB * centerZB) < 0;
					}
					else if (B.type == ChunkContainSpecialBiome::SpecialBiome_City)
					{
						return true;
					}
					return false;
				});
				m_World->getCityMgr()->checkFindCity(result.specialBiome, result.uin);
			}
		}
	}
}

Chunk* ChunkGenerator::createChunk(int chunkx, int chunkz)
{
	OPTICK_EVENT();
	if (!canProvideChunk(chunkx, chunkz)) return NULL;

	GenTerrResult ret;
	createChunkData(ret, chunkx, chunkz);

	Chunk* pchunk = ENG_NEW(Chunk)(m_World, chunkx, chunkz, ret.chunkdata);

	memcpy(pchunk->GetWritableSharedChunkData().getBiomeIDArray().data(), ret.biomes, CHUNK_BLOCK_X * CHUNK_BLOCK_Z);

	pchunk->generateSkylightMap();
	if (!hasSky())
		pchunk->resetRelightChecks();

	return pchunk;
}

bool ChunkGenerator::canCoordBeSpawn(int x, int z)
{
	int blockid = m_World->getTopUncoverBlock(x, z);
	return (blockid == BLOCK_GRASS) || (blockid == BLOCK_SOLIDSAND) || (blockid == BLOCK_TALL_GRASS);
}

bool ChunkGenerator::canCoordBeSpawnWithY(int x, int& y, int z)
{
    int blockid = m_World->getTopUncoverBlockWithY(x,y,z);
	auto mtl = g_BlockMtlMgr.getMaterial(blockid);
	if (mtl != NULL)
	{
		return mtl->isSolid();
	}
	return false;
}

void ChunkGenerator::CalOffsetRange(int offset[3], int& range, int startchunk, int endchunk)
{
	offset[0] = (startchunk + endchunk + 1) * 8;

	int n = ((endchunk - startchunk + 1) / 2) * 8;
	if (n > 0) n--;
	if (n > 2048) n = 2048;
	offset[1] = offset[0] - n;
	offset[2] = offset[0] + n;

	range = ((endchunk - startchunk + 1) / 2) * 4;
	if (range > 256) range = 256;
	if (range < 1) range = 1;
}

/*
inline void CalOffsetRange(int &offset, int &range, int nchunks)
{
	if(nchunks > 0)
	{
		offset = (nchunks - 1)*16/2;
		if(offset > 2048) offset = 2048;
		range = nchunks*16/4;
		if(range > 256) range = 256;
	}
	else
	{
		offset = 2048;
		range = 256;
	}
}*/

WCoord ChunkGenerator::createSpawnPoint(ChunkRandGen& randgen)
{
	OPTICK_EVENT();
	WCoord spawnpoint;

	int centers[5][2];
	int offsetx[3], offsetz[3];
	int rangex, rangez;
	CalOffsetRange(offsetx, rangex, getStartChunkX(), getEndChunkX());
	CalOffsetRange(offsetz, rangez, getStartChunkZ(), getEndChunkZ());
	centers[0][0] = offsetx[0], centers[0][1] = offsetz[0];
	centers[1][0] = offsetx[1], centers[1][1] = offsetz[0];
	centers[2][0] = offsetx[2], centers[2][1] = offsetz[0];
	centers[3][0] = offsetx[0], centers[3][1] = offsetz[1];
	centers[4][0] = offsetx[0], centers[4][1] = offsetz[2];

	int i;
	for (i = 0; i < 5; i++)
	{
		bool findpoint = getBiomeManager()->findEcosystemOn(spawnpoint, centers[i][0], centers[i][1], rangex, rangez, randgen);
		if (findpoint) break;
	}
	if (i == 5)
	{
		LOG_INFO("Cannot find spawn point");
		spawnpoint = WCoord(0, 0, 0);
	}

	LOG_INFO("CreateSpanwpoint1: try=%d, x=%d, y=%d, z=%d", i, spawnpoint.x, spawnpoint.y, spawnpoint.z);

	m_World->syncLoadChunk(BlockDivSection(spawnpoint.x), BlockDivSection(spawnpoint.z));

	if (rangex > 64) rangex = 64;
	if (rangez > 64) rangez = 64;
	int count = 0;
	while (!canCoordBeSpawn(spawnpoint.x, spawnpoint.z))
	{
		if (count++ == 200) //1000
		{
			break;
		}

		int x = spawnpoint.x + randgen.nextInt(rangex) - randgen.nextInt(rangex);
		int z = spawnpoint.z + randgen.nextInt(rangez) - randgen.nextInt(rangez);
		if (m_World->syncLoadChunk(BlockDivSection(x), BlockDivSection(z)))
		{
			spawnpoint.x = x;
			spawnpoint.z = z;
		}
	}

	spawnpoint.y = getSpawnMinY();
	LOG_INFO("CreateSpanwpoint2: count=%d, x=%d, y=%d, z=%d", count, spawnpoint.x, spawnpoint.y, spawnpoint.z);

	return spawnpoint;
}

WCoord ChunkGenerator::createCitySpawnPoint(ChunkRandGen& randgen)
{
	OPTICK_EVENT();
	if (!getBiomeManager())
	{
		return WCoord(0, 0, 0);
	}
	if (!GetCityConfigInterface())
	{
		return WCoord(0, 0, 0);
	}
	WCoord spawnpoint(0, 0, 0);

	int centers[5][2];
	int offsetx[3], offsetz[3];
	int rangex, rangez;
	CalOffsetRange(offsetx, rangex, getStartChunkX(), getEndChunkX());
	CalOffsetRange(offsetz, rangez, getStartChunkZ(), getEndChunkZ());
	
	//我们就从 offsetx[0], offestz[0]处向四周探索, 
	centers[0][0] = offsetx[0], centers[0][1] = offsetz[0];
	centers[1][0] = offsetx[1], centers[1][1] = offsetz[0];
	centers[2][0] = offsetx[2], centers[2][1] = offsetz[0];
	centers[3][0] = offsetx[0], centers[3][1] = offsetz[1];
	centers[4][0] = offsetx[0], centers[4][1] = offsetz[2];
	int baseToCityRange = GetCityConfigInterface()->GetBaseToCityRange();
	auto isInCityRange = [baseToCityRange](const std::vector<ChunkSpecialBiomeData>& datas, ChunkIndex pos) ->bool
	{
		for (const auto& p : datas)
		{
			ChunkIndex leftDowm((p.leftDown.x - baseToCityRange) * CHUNK_BLOCK_X, (p.leftDown.z - baseToCityRange) * CHUNK_BLOCK_Z);
			ChunkIndex topRight((p.leftDown.x + baseToCityRange + p.range.x()) * CHUNK_BLOCK_X, (p.leftDown.z + baseToCityRange + +p.range.y()) * CHUNK_BLOCK_Z);
			if (pos.x >= leftDowm.x && pos.z >= leftDowm.z && pos.x <= topRight.x && pos.z <= topRight.z)
			{
				return true;
			}
		}
		return false;
	};
	bool bFind = false;
	int tempY = 0;
	//rangex *= 2; 
	//rangez *= 2;

	int cityRange = GetCityConfigInterface()->GetToCityRange();
	int baseHeightScore = GetCityConfigInterface()->GetBaseHeightScore();
	int baseMaxHeightDiff = GetCityConfigInterface()->GetBaseMaxHeightDiff();
	int baseAimScore = GetCityConfigInterface()->GetBaseAimScore();
	for (int test = 0; test < 5 && !bFind; test++)
	{
		//找到范围内的所有城市
		std::vector<ChunkSpecialBiomeData> cityDatas;
		if (!getBiomeManager()->findSpecialBiomeOn(centers[test][0], centers[test][1], rangex, rangez, TerrainSpecialDataType::TerrainSpecialData_SpecialSubOrderBiome, cityDatas))
		{
			continue;
		}
		//我们把符合的城镇挑出来
		std::vector<ChunkSpecialBiomeData> properCity;
		for (const auto& p : cityDatas)
		{
			if (getBiomeManager()->isVaildSpawnBiome(p.originBomeId))
			{
				properCity.push_back(p);
			}
		}
		int minX = (centers[test][0] - rangex) * CHUNK_BLOCK_X;
		int minZ = (centers[test][1] - rangex) * CHUNK_BLOCK_Z;
		int maxX = (centers[test][0] + rangex) * CHUNK_BLOCK_X;
		int maxZ = (centers[test][1] + rangex) * CHUNK_BLOCK_Z;
		//我们选定个城市,在城市周边找位置
		//不随机了,直接遍历吧
		for (int cityIndex = 0; cityIndex < properCity.size() && !bFind; cityIndex++)
		{
			const auto& cityInfo = properCity[cityIndex];
			//最大的城市也不会超过20chunk, 我们选取离中心点 rangex - 20范围内的城市, 这是为了防止在边界情况有未纳入的城市
			if ((abs(cityInfo.leftDown.x - centers[test][0]) >= rangex - 20) || (abs(cityInfo.leftDown.z - centers[test][1]) >= rangez - 20))
			{
				continue;
			}
			//我们从距离城市左下角10个chunk内开始检索.
			//还要保证城市距离出生点要>4个chunk,这是为了防止基地生成会与城市相互覆盖.
			ChunkIndex leftDown((cityInfo.leftDown.x - cityRange) * CHUNK_BLOCK_X, (cityInfo.leftDown.z - cityRange) * CHUNK_BLOCK_Z);
			leftDown.x = std::max(leftDown.x, minX);
			leftDown.z = std::max(leftDown.z, minZ);
			ChunkIndex topRight((cityInfo.leftDown.x + cityRange + cityInfo.range.x()) * CHUNK_BLOCK_X, (cityInfo.leftDown.z + cityRange + +cityInfo.range.y()) * CHUNK_BLOCK_Z);
			topRight.x = std::min(topRight.x, maxX);
			topRight.z = std::min(topRight.z, maxZ);
			for (int tz = leftDown.z; tz <= topRight.z && !bFind; tz += 32)
			{
				for (int tx = leftDown.x; tx <= topRight.x && !bFind; tx += 32)
				{
					//这里要和所有城市做判断的
					if (!isInCityRange(cityDatas, ChunkIndex(tx, tz)))
					{
						m_World->syncLoadChunk(BlockDivSection(tx), BlockDivSection(tz));
						//看地形是否适合
						if (getBiomeManager()->isVaildSpawnBiome(m_World->getBiomeId(tx, tz)))
						{
							int chunkX = BlockDivSection(tx);
							int chunkZ = BlockDivSection(tz);
							Chunk* curChunk = m_World->getChunkBySCoord(chunkX, chunkZ);
							if (curChunk)
							{
								//采样5个点看是否合适
								const char samplingBlock[5][2] = { {0, 0},{15, 0}, {8, 8}, {0, 15}, {15, 15} };
								int placeWater = 0;
								bool fit = true;
								for (int i = 0; i < 5; i++)
								{
									if (!getBiomeManager()->isVaildSpawnBiome(curChunk->getBiomeID(samplingBlock[i][0], samplingBlock[i][1])))
									{
										fit = false;
										break;
									}
								}
								if (!fit) continue;
								WCoord startPos = WCoord(tx * SECTION_BLOCK_DIM, EcosysBuildHelp::getRealHeightChunk(curChunk, 7, 7), tz * SECTION_BLOCK_DIM);
								//先看下单个chunk的是否平整
								BuildPlaceParameter placeParam;
								placeParam.heightScore = baseHeightScore;
								placeParam.maxHeightDiff = baseMaxHeightDiff;
								BuildSampleResult errResult;
								if (!EcosysBuildHelp::getChunkErrScore(m_World->getWorldProxy(), curChunk, startPos.y, placeParam, errResult))
								{
									continue;
								}
								//LOG_INFO("createCitySpawnPoint, errScore: %d", errResult.err);
								if (errResult.err >= baseAimScore)
								{
									continue;
								}
								int buildHeight = errResult.averageHeight + 0.5f;
								if (canCoordBeSpawnWithY(tx, buildHeight, tz))
								{
									//找到一个地方, 我们加载周围区域, 弄一个合适的高度
									EcosysBuildHelp::tryLoadSurroundingTrunk(m_World->getWorldProxy(), chunkX, chunkZ, 2);
									WCoord bottomLeft = GetCityConfigInterface()->getBaseBottomLeftByPos(WCoord(tx, buildHeight, tz));
									auto size = GetCityConfigInterface()->GetBaseDataSize();
									WCoord topLeft = bottomLeft + WCoord(size.x(), size.y(), size.z());
									placeParam.width = size.x();
									placeParam.height = size.z();
									WCoord startPos;
									int dir = 0;
									EcosysBuildHelp::calculateBuildingPos(m_World->getWorldProxy(), bottomLeft, topLeft, placeParam, startPos, dir);
									tempY = startPos.y + 1;
									spawnpoint.x = tx;
									spawnpoint.z = tz;
									bFind = true;
									//我们把玩家所在的位置下设置个方块
									m_World->setBlockAll(tx, tempY, tz, BLOCK_STONE, 0);
									//上面设为空气,防止玩家堵在方块中
									m_World->setBlockAll(tx, tempY + 1, tz, BLOCK_AIR, 0);
									m_World->setBlockAll(tx, tempY + 2, tz, BLOCK_AIR, 0);
									m_World->setBlockAll(tx, tempY + 3, tz, BLOCK_AIR, 0);
								}
							}
						}
					}
				}
			}
		}
	}

	if (!bFind) {
		spawnpoint.y = getSpawnMinY();
	}
	else
	{
		spawnpoint.y = tempY + 1;
	}
	////我们在这里把基地上面的方块全部置成空气, 弄个30格左右就行.
	////因为基地大小超过一个chunk, 出生点也只加载了当前chunk, 都加载周围chunk的, 量太大了.
	////基地放置容易出现 树被截了底部, 在山中这种情况
	////直接就加载周围基地大小区域,因为基地周围区域本身也是要强加载的, 并且半径是大于基地大小的.这里提前加载一些不碍事.
	//auto baseBottomLeft = CityConfig::getSingletonPtr()->getBaseBottomLeftByPos(spawnpoint);
	//auto baseTopRight = baseBottomLeft + WCoord(config.baseData.m_size.x() - 1, config.baseData.m_size.y(), config.baseData.m_size.z() - 1);
	//EcosysBuildHelp::tryLoadSurroundingTrunk3(m_World->getWorldProxy(), baseBottomLeft, baseTopRight);
	//baseBottomLeft.y += config.baseData.m_size.y();
	//baseTopRight.y += config.m_clearHeight;
	//EcosysBuildHelp::memsetWorldBlock(baseBottomLeft, baseTopRight, BLOCK_AIR, 0, m_World->getWorldProxy());
	return spawnpoint;
}

WCoord ChunkGenerator::MakeSpawnPoint(ChunkRandGen& randgen)
{
	OPTICK_EVENT();
	//城市替换出生点生成方式
	if (m_World && m_World->getCurMapID() == 0 && GetCityConfigInterface() && GetCityConfigInterface()->useNewBaseBuild())
	{
		return createCitySpawnPoint(randgen);
	}
	//mod控制出生点
	if (GetBiomeModConfigInterface() && GetBiomeModConfigInterface()->isEnableSpawnPointMod())
	{
		return GetBiomeModConfigInterface()->modGenSpawnPoint(m_World, randgen);
	}
	WCoord spawnpoint;

	int centers[5][2];
	int offsetx[3], offsetz[3];
	int rangex, rangez;
	CalOffsetRange(offsetx, rangex, getStartChunkX(), getEndChunkX());
	CalOffsetRange(offsetz, rangez, getStartChunkZ(), getEndChunkZ());

	int crangex = m_spawnPointRangeNum * CHUNK_SECTION_DIM;
	int crangez = m_spawnPointRangeNum * CHUNK_SECTION_DIM;
	if (crangex < abs(offsetx[2]))
	{
		rangex = crangex;
	}
	if (crangez < abs(offsetz[2]))
	{
		rangez = crangez;
	}
	centers[0][0] = offsetx[0], centers[0][1] = offsetz[0];
	centers[1][0] = offsetx[1], centers[1][1] = offsetz[0];
	centers[2][0] = offsetx[2], centers[2][1] = offsetz[0];
	centers[3][0] = offsetx[0], centers[3][1] = offsetz[1];
	centers[4][0] = offsetx[0], centers[4][1] = offsetz[2];

	int i;
	for (i = 0; i < 5; i++)
	{
		bool findpoint = getBiomeManager()->findEcosystemOn(spawnpoint, centers[i][0], centers[i][1], rangex, rangez, randgen);
		if (findpoint) break;
	}

	if (i == 5)
	{
		LOG_INFO("Cannot find spawn point");
		spawnpoint = WCoord(0, 0, 0);
	}

	LOG_INFO("MakeSpawnPoint1: try=%d, x=%d, y=%d, z=%d", i, spawnpoint.x, spawnpoint.y, spawnpoint.z);
	{
		m_World->syncLoadChunk(BlockDivSection(spawnpoint.x), BlockDivSection(spawnpoint.z));
	}

	//if (rangex > 64)
	//{
	//	rangex = 64;
	//}

	//if (rangez > 64)
	//{
	//	rangez = 64;
	//}

	int count = 0;
	int tempY = 0;
	bool bFind = true;

	while (!canCoordBeSpawnWithY(spawnpoint.x, tempY, spawnpoint.z))
	{
		if (count++ == 300) //1000
		{
			bFind = false;
			break;
		}

		int x = spawnpoint.x + randgen.nextInt(rangex) - randgen.nextInt(rangex);
		int z = spawnpoint.z + randgen.nextInt(rangez) - randgen.nextInt(rangez);
		if (m_World->syncLoadChunk(BlockDivSection(x), BlockDivSection(z)))
		{
			spawnpoint.x = x;
			spawnpoint.z = z;
		}
	}

	if (!bFind) {
		spawnpoint.y = getSpawnMinY();
	}
	else
	{
		spawnpoint.y = tempY + 1;
	}

	LOG_INFO("MakeSpawnPoint2: count=%d, x=%d, y=%d, z=%d", count, spawnpoint.x, spawnpoint.y, spawnpoint.z);
	return spawnpoint;
}

EcosysUnitVoxelModel* ChunkGenerator::addModelGen(const char* name, int palette/* =0 */)
{
	EcosysUnitVoxelModel* gen = ENG_NEW(EcosysUnitVoxelModel)(name, palette);
	m_ModelGens.push_back(gen);

	return gen;
}

EcosysUnitVoxelModel* ChunkGenerator::getModelGen(const char* name, int palette/* =0 */)
{
	for (size_t i = 0; i < m_ModelGens.size(); i++)
	{
		EcosysUnitVoxelModel* gen = m_ModelGens[i];
		if (strcmp(gen->getName(), name) == 0)
		{
			return gen;
		}
	}

	return addModelGen(name, palette);
}

float ChunkGenerator::GetGenPercent()
{
	if (m_ChunkCount <= 0) return 0.0f;
	return (float)m_GenChunks.size() / (float)m_ChunkCount;
}

#ifdef BUILD_MINI_EDITOR_APP
void ChunkGenerator::InsertTChunkInfo(TCHUNKINFO info)
{
	m_vecTerrainChunkInfo.push_back(info);
}

std::vector<TCHUNKINFO> & ChunkGenerator::GetTChunkInfoVec()
{
	return m_vecTerrainChunkInfo;
}

TERRAIN_TYPE ChunkGenerator::FindTerrType(int chunkx, int chunkz)
{
	TERRAIN_TYPE emType = TERRAIN_FLAT;
	std::for_each(m_vecTerrainChunkInfo.begin(), m_vecTerrainChunkInfo.end(), [&](TCHUNKINFO & info)->void {
		if (chunkx >= info.startChunkIndex.x && chunkx <= info.endChunkIndex.x && chunkz >= info.startChunkIndex.z && chunkz <= info.endChunkIndex.z)
		{
			emType = (TERRAIN_TYPE)info.nTerrType;
			return;
		}
	});

	return emType;
}

#endif 
