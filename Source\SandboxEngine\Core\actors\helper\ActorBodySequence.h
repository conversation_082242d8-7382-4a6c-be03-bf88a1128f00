#pragma once

#ifndef __ACTOR_BODY_SEQUENCE_H__
#define __ACTOR_BODY_SEQUENCE_H__
#include "SandboxEngine.h"

#define MAX_SADDLE 9
static const char* s_SaddleNames[MAX_SADDLE] = { "saddle01", "saddle02", "saddle03", "saddle04", "saddle05", "saddle06" , "saddle07" , "saddle08" ,"saddle09" };
#define MAX_NECKLACE 3
static const char* s_NecklaceNames[MAX_NECKLACE] = { "necklace01", "necklace02", "necklace03" };
#define MAX_RAKE 3
static const char* s_RakeNames[MAX_RAKE] = { "rake01", "rake02", "rake03" };

//国内新加动作
#define CHINA_SEQ_USED 1

//tolua_begin
enum ActorBodySeq
{
    SEQ_STAND = 0,
    SEQ_WALK,
    SEQ_ATTACK,
    SEQ_<PERSON>IE,
    SEQ_JUMP,
    SEQ_SKATEBOARD_DRIFT,
    SEQ_SKATEBOARD_SHOW1,
    SEQ_SKATEBOARD_SHOW2,
    SEQ_SKATEBOARD_SHOW3,
    SEQ_SKATEBOARD_IDLE2WALK,
    SEQ_SKATEBOARD_WALK2IDLE,
    SEQ_SKATEBOARD_JUMP2BLOCK,
    SEQ_SKATEBOARD_ONBLOCKFRONT,
    SEQ_SKATEBOARD_DRIFT2,
    SEQ_SKATEBOARD_JUMP2BLOCK2,
    SEQ_IDLEACTION,
    SEQ_IDLEACTION2,
    SEQ_IDLEACTIONINVITE,// 20210926 codeby:wangyu 互动装扮动画
    SEQ_BEHIT,
    SEQ_LAYDOWN,
    SEQ_SITDOWN,
    SEQ_HELM,
    SEQ_SWIM,
    SEQ_RUN,
    SEQ_MOBEAT,  //动物吃: 草等
    SEQ_MOBDRINK,  //动物喝水
    SEQ_MOBSLEEP, //动物睡觉
    SEQ_MOBDANCE,//动物跳舞
    SEQ_MOBTOPPLEOVER,//动物趴下
    SEQ_MOBLOGGERHEAD, //顶牛
    SEQ_MOBSCROLL,//滚动
    SEQ_MOBKICK, //踢走 
    SEQ_MOBDODGE,//躲避 
    SEQ_MOBCONCEAL, //潜伏
    SEQ_MOBHOWL,//嚎叫
    SEQ_MOBMAKETROUBLE,//捣乱
    SEQ_MOBBREATHE,//喘气
    SEQ_MOBHOLD,//抱着
    SEQ_MOBREVERSE,//在天花板倒着
    SEQ_SHOOTARROW,
    SEQ_EAT,
    SEQ_DRINK,  //喝水
    SEQ_SHOOTARROW_WALK,
    SEQ_TAMED,
    SEQ_FLY,
    SEQ_JUMPTWO,
    SEQ_SKINFLY, //皮肤带的飞行
    SEQ_SKINSTAND, //皮肤带的空中站立
    SEQ_SITCHAIR,
    SEQ_DISAPPEAR, //消失动作
    SEQ_SHOWTIME, // 商城展示动作
    SEQ_DIG_MULTI, //
    SEQ_DIG_CHARGE, //
    SEQ_SKINNING, //剥皮

    SEQ_TOOL_LOOP,
    SEQ_TOOL_ATTACK,
    SEQ_ITEMSKILL_START,
    SEQ_ITEMSKILL_ATTACK,

    //这三个放到最后
    SEQ_GUN_IDLE,
    SEQ_GUN_FIRE,
    SEQ_GUN_RELOAD,

    SEQ_KICK_BALL,
    SEQ_TACKLE,

    SEQ_WIZARDMAGIC,//重力巫师施法
    SEQ_BUMP_PRE,
    SEQ_THINK,  //歪头思考
    SEQ_RUB_HAND, //搓手
    SEQ_GET_UP, //起床
    SEQ_HUNGER_SIT, //饿得坐在地上
    SEQ_SCREAM, //尖叫
    SEQ_TIRED, //疲惫

    //半身石巨人
    SEQ_HALFGIANT_RIGHTHANDATK,	//右手攻击
    SEQ_HALFGIANT_LEFTHANDATK,	//左手攻击
    SEQ_HALFGIANT_BOTHHANDSATK,	//双手攻击
    SEQ_HALFGIANT_BORN, //出生动作，从地面爬出
    SEQ_HALFGIANT_STUN, //晕眩动作（虚弱状态）
    SEQ_HALFGIANT_TRANSFORM, //变身动作

    //全身石巨人
    SEQ_GIANT_BOW, //弯腰动作-远程索敌动作
    SEQ_GIANT_ATKGROUND, //锤地板动作（原用脚踩地）
    SEQ_GIANT_BORN, //出生动作，从地面爬出
    SEQ_GIANT_STUN, //晕眩动作（虚弱状态）
    SEQ_GIANT_ROCKWAVE_READY, //岩石海啸吟唱动作
    SEQ_GIANT_ROCKWAVE_CAST, //岩石海啸发射动作
    SEQ_GIANT_REVENGEROAR, //复仇吼叫动作
    SEQ_GIANT_ATKAIR, //空中挥拳动作
    SEQ_GIANT_TRANSFORM, //变身动作
    SEQ_PLAY_ACT,

    //物理
    SEQ_CATCH_GRAVITYACTOR, //重力手套抓取物体持续动作

    //水中坐骑
    SEQ_SWIM_DIVING, //深潜
    SEQ_SWIM_RUSH, //突进
    SEQ_SWIM_IDLE, //水中待机

    //篮球
    SEQ_BASKETBALL_OBSTRUCT,	//阻挡
    SEQ_BASKETBALL_BLOCK_SHOT, //盖帽
    SEQ_BASKETBALL_SHOOT_AND_PASS,//传球射门
    SEQ_BASKETBALL_DRIBBLE,	//运球	
    SEQ_BASKETBALL_GRAB_BEFORE,	//篮球抢断前摇
    SEQ_BASKETBALL_GRAB,		//抢断

    SEQ_SHAPE_SHIFT,		//变形
    SEQ_RE_SHAPE_SHIFT,		//反变形
    SEQ_SPITFIRE_LAND,     //陆地喷火
    SEQ_SPITFIRE_AIR,      //空中喷火
    SEQ_SPITFIRE_LAND_WAIT_CD, //陆地喷火等待CD
    SEQ_SPITFIRE_AIR_WAIT_CD,  //空中喷火等待CD

    SEQ_SAVAGE_DANCE, //野人跳舞
    SEQ_SAVAGE_CRUEL, //野人打掉头罩后的暴戾
    SEQ_SAVAGE_SHOCK, //野人发现敌人，受惊吓
    SEQ_SAVAGE_SING, //野人跳舞
    SEQ_CARRIED,		//被扛起
    SEQ_SHAKE_HEAD,		//摇头
    SEQ_SAVAGE_CELEBRATE, //野人庆祝

    SEQ_CARRYING,		//扛起
    SEQ_EXTREMIS_WALK,	//濒死走路
    SEQ_EXTREMIS_STAND_1, //濒死站立1
    SEQ_EXTREMIS_STAND_2, //濒死站立2
    SEQ_EXTREMIS_STAND_3, //濒死站立3
    SEQ_NOD,			//打瞌睡
    SEQ_STAND_HUNGER,	//站着饥饿摸肚子
    SEQ_FLEE,			//逃跑
    SEQ_CAY,			//哭泣
    SEQ_TALK,			//交谈
    SEQ_STARFIRE,       //发射星星
    SEQ_STARFIRE_CD,    //发射星星冷却
    SEQ_MOONFLY,		//月升飞行
    SEQ_SAYHELLO,		//打招呼
    SEQ_ANGRY,			//生气
    SEQ_THANKS,			//感谢
    SEQ_POSE,			//摆姿势
    SEQ_FORTUNEMOOSKILL,//福运哞哞-五谷丰登
    SEQ_HOMELAND_PET_STANDBY,//家园宠物待机
    SEQ_HOMELAND_PET_IDLE,//家园宠物休闲
    SEQ_HOMELAND_PET_SITDOWN,//家园宠物坐下
    SEQ_HOMELAND_PET_LYINGDOWN,//家园宠物趴着
    SEQ_HOMELAND_PET_HAPPY,//家园宠物开心
    SEQ_HOMELAND_PET_SHAKEINGHEAD,//家园宠物摇头
    SEQ_HOMECHEST_PLANT_STANDBY,	//家园植物待机
    SEQ_HOMECHEST_PLANT_FAWN,	//家园植物撒娇
    SEQ_VACANT2_ANNIM1,//虚空boss 二阶段 10个动作
    SEQ_VACANT2_ANNIM2,
    SEQ_VACANT2_ANNIM3,
    SEQ_VACANT2_ANNIM4,
    SEQ_VACANT2_ANNIM5,
    SEQ_VACANT2_ANNIM6,
    SEQ_VACANT2_ANNIM7,
    SEQ_VACANT2_ANNIM8,
    SEQ_VACANT2_ANNIM9,
    SEQ_VACANT2_ANNIM10,
    SEQ_VACANT2_ANNIM11,
    SEQ_VACANT1_ANNIM1,//站立//虚空boss一阶段 18个动作
    SEQ_VACANT1_ANNIM2,//吼叫
    SEQ_VACANT1_ANNIM3,//冲刺-预备动作
    SEQ_VACANT1_ANNIM4,//冲刺-向前
    SEQ_VACANT1_ANNIM5,//冲刺-俯冲
    SEQ_VACANT1_ANNIM6,//受击
    SEQ_VACANT1_ANNIM7,//旋转冲击波
    SEQ_VACANT1_ANNIM8,//蓄力召唤（火球）
    SEQ_VACANT1_ANNIM9,//闪避传送
    SEQ_VACANT1_ANNIM10,//前扑抓取
    SEQ_VACANT1_ANNIM11,//抓取控制
    SEQ_VACANT1_ANNIM12,//下砸-前摇
    SEQ_VACANT1_ANNIM13,//下砸-冲刺
    SEQ_VACANT1_ANNIM14,//移动
    SEQ_VACANT1_ANNIM15,//预备与冲刺的衔接动作
    SEQ_VACANT1_ANNIM16,//冲刺到待机的衔接动作
    SEQ_VACANT1_ANNIM17,//移动接下砸（513）的衔接动作
    SEQ_VACANT1_ANNIM18,//死亡（逃跑）


    SEQ_EARTHMAN_ANNIM,//地心人偷取体力时的动作
    SEQ_BUTTERFLY_CHANGEBLOCK,	// 蝴蝶专属动作，改变方块
    SEQ_BUTTERFLY_FLYSTAY,		// 蝴蝶专属动作，悬空停留
    SEQ_STAND_SLEEP,//站着睡觉
    SEQ_FLYMOB_FALLGROUND, //飞行类生物趴下
    SEQ_COOK, // 烹饪
    SEQ_FLASH,				//闪现
    SEQ_HOVER,	//悬停
    SEQ_HAPPLYFLY,	// 飞行生物高兴
    SEQ_UPSETFLY,	// 飞行生物失落

    SEQ_SCORPION_HIDE,//蝎子隐藏
    SEQ_SCORPION_DIRLLOUT,//蝎子钻出地表
    SEQ_SCORPION_TAIL,//蝎子断尾
    SEQ_SCORPION_NAIL,//蝎子碎甲
    SEQ_SCORPION_STANDBY,//蝎子待机
    SEQ_SCORPION_WALK,//蝎子行走
    SEQ_SCORPION_HIT,//蝎子受击
    SEQ_SCORPION_DIE,//蝎子死亡
    SEQ_SCORPION_ATTACK,//蝎子普通攻击
    SEQ_SCORPION_DEADLY,//蝎子致命蝎尾

    SEQ_SANDMAN_HIDE,//沙人躲藏
    SEQ_SANDMAN_RANGEDATK,//沙人远程攻击
    SEQ_SANDMAN_FAKEDEATH,//沙人假死
    SEQ_SANDMAN_REVIVE,//沙人复活
    SEQ_SANDMAN_ABSORB,//沙人吸收沙子

    SEQ_CAMEL_DRINK,//骆驼喝水
    SEQ_CAMEL_RANGEDATK,//骆驼远程攻击

    SEQ_SANDWORM_STANBY,//沙虫-待机
    SEQ_SANDWORM_WALK,//沙虫-行走
    SEQ_SANDWORM_HURT,//沙虫-受击
    SEQ_SANDWORM_DIE,//沙虫-死亡
    SEQ_SANDWORM_ROAR,//沙虫-咆哮
    SEQ_SANDWORM_ATTACK,//沙虫-普通攻击
    SEQ_SANDWORM_VERTIGO,//沙虫-眩晕
    SEQ_SANDWORM_BITE1,//沙虫-撕咬 前瑶
    SEQ_SANDWORM_BITE2,//沙虫-撕咬  打出
    SEQ_SANDWORM_MARACAS,//沙虫-沙球攻击
    SEQ_SANDWORM_DRILLING_IN,//沙虫-钻地   -躲藏形态
    SEQ_SANDWORM_DRILLING_OUT,//沙虫-冲出地面  钻出地表
    SEQ_SANDWORM_TOW,//沙虫-深渊巨口
    SEQ_SANDWORM_DASH,//沙虫-蓄力猛冲
    SEQ_SANDWORM_DRILLING_IN_STATE,//沙虫-潜入沙子中的形态
    SEQ_SANDWORM_NIBBLE,//沙虫-啃食的动作

    SEQ_DANCE1,//跳舞动作1 
    SEQ_DANCE2,//跳舞动作2 
    SEQ_DANCE3,//跳舞动作3 
    SEQ_DANCE4,//跳舞动作4 
    SEQ_DANCE5,//跳舞动作5 
    SEQ_WAKEUP,//吵醒动作

    SEQ_MUSIC_IDLE,
    SEQ_MUSIC_PLAY,
    //SEQ_PIANO_PLAY,
    //SEQ_TRANSFER, //变身
    SEQ_GUARD_NOD, //打瞌睡
    SEQ_DESERTBUSSINESS_CRYDOWN, //喝止
    SEQ_DESERTBUSSINESS_GEST, //比划
    SEQ_DESERTBUSSINESS_REFUSE, //拒绝
    SEQ_DESERTBUSSINESS_COLLECT, //收钱
    SEQ_DJUMP,    // 二段跳
    SEQ_ENTERWORLD, //进入地图
    SEQ_REVIVE,    // 复活
    SEQ_HIPPOCAMPUS_BIND,//海马缠缚
    SEQ_CRAB_DIG_BLOCK,//螃蟹扒土动作
    SEQ_CRAB_STRUGGLE,//螃蟹挣扎动作
    SEQ_CRAB_CLAMB,//螃蟹夹手动作
    SEQ_CRAB_SHOW_HAND,//螃蟹挥舞蟹钳
    SEQ_CRAB_CLIMB,//螃蟹攀爬动作
    SEQ_FLYINGFISH_SWIM,    // 飞鱼 游泳
    SEQ_FLYINGFISH_ESCAPA, // 飞鱼逃跑
    SEQ_FLYINGFISH_FLY,	  // 飞鱼飞
    SEQ_FLYINGFISH_TAKE_OFF,// 飞鱼起飞
    SEQ_FLYINGFISH_ENTRY_WATER,	// 飞鱼入水
    SEQ_JELLYFISH_SHAKE_1, // 水母 抖动挣扎
    SEQ_JELLYFISH_SHAKE_2, // 水母 抖动挣扎
    SEQ_JELLYFISH_EXHAUST,	  // 水母 排气泡
    SEQ_JELLYFISH_FLOOD_GAS,    // 水母 充斥气体
    SEQ_SHARK_BITE_1,    // 鲨鱼撕咬方块
    SEQ_SHARK_BITE_2,  // 鲨鱼撕咬方块
    SEQ_MOUBULA_TAIL_ATTACK,	// 魔鬼鱼尾刺攻
    SEQ_FISHCONCEAL,	// 荧光棒藻鱼潜伏动作
    SEQ_MOUBULA_REST_DOWN, //魔鬼鱼趴着休息
    SEQ_MOUBULA_REST_UP,//魔鬼鱼竖着休息
    SEQ_ENDING_FISHING,	// 钓鱼收杆动作
    SEQ_LAYEGG,//嘟嘟鸟下蛋孵蛋
    SEQ_BATREVERSE,	// 蝙蝠倒挂
    SEQ_BATATTACH,	// 蝙蝠抱脸
    SEQ_MOBCONCEAL_NEW,//生物新潜伏动作
    SEQ_AVATAT_SUMMON,//皮肤召唤动作

    //新手引导机器人
    SEQ_BOT_IDLE,
    SEQ_BOT_MOVE,
    SEQ_BOT_ENTER,
    SEQ_BOT_ENTER_FAST,
    SEQ_BOT_LEAVE,
    SEQ_BOT_LEAVE_FAST,
    SEQ_BOT_AWAIT1,
    SEQ_BOT_AWAIT2,
    SEQ_BOT_PROJECTION1,
    SEQ_BOT_PROJECTION2,
    SEQ_BOT_PROJECTION_END,
    SEQ_BOT_ANXIOUS,
    SEQ_BOT_TALK,
    SEQ_BOT_POINT_RIGHTUP,
    SEQ_BOT_POINT_DOWN,
    SEQ_BOT_POINT_LEFTUP,
    SEQ_BOT_AWAKE,

    SEQ_GLISSADE, //冰面滑行
    SEQ_DOG_PADDLE, //狗刨

    SEQ_SNOWMAN_STAND_PANIC,  //雪人待机-恐慌
    SEQ_SNOWMAN_MOVE_PANIC,  //雪人移动-恐慌
    SEQ_SNOWMAN_STAND,  //雪人小休闲

    SEQ_SNOWHARE_LOOKAROUND,  //雪兔张望
    SEQ_SNOWHARE_TEMPT,  //雪兔被吸引
    SEQ_SNOWHARE_HIDE,  //雪兔躲藏

    //冰原BOSS
    SEQ_IB_ATTACK1,
    SEQ_IB_FMOVE_BEGIN,
    SEQ_IB_FMOVE_LOOP,
    SEQ_IB_FMOVE_END,
    SEQ_IB_ICEROCK,
    SEQ_IB_ICEBALL,
    SEQ_IB_AWAKE,
    SEQ_IB_IDLE2,
    SEQ_IB_ATTACK2,
    SEQ_IB_JUMP_BEGIN,
    SEQ_IB_JUMP_LOOP,
    SEQ_IB_JUMP_END,
    SEQ_IB_ICEROCK2,
    SEQ_IB_ICEBALL2,
    SEQ_IB_SKILL1_BEGIN,
    SEQ_IB_SKILL1_LOOP,
    SEQ_IB_BEATEN2,
    SEQ_IB_SLEEP,
    SEQ_IB_JUMPIN,
    SEQ_IB_JUMPOUT,
    SEQ_IB_ATTACK2_B,
    SEQ_IB_ATTACK2_L,
    SEQ_IB_ATTACK2_E,
    SEQ_IB_SKILL2,
    SEQ_IB_SKILL3,
    SEQ_IB_SKILL1_END,
    SEQ_IB_INJURED1,
    SEQ_IB_INJURED2,

    SEQ_COMBOATK_IDLE_BODY, //手持武器-待机时-全身
    SEQ_COMBOATK_MOVE_BODY, //手持武器-移动时-全身

#ifdef CHINA_SEQ_USED
    //放开宏时记得tolua
    SEQ_PIANO_PLAY,
    SEQ_TRANSFER, //变身
#endif
    SEQ_IB_ATTACK2_F,
    SEQ_HUNT_ATTACK,
    SEQ_RAISE_SHIELD,       // 持盾防御
    SEQ_BROKEN_TOUGHNESS,   // 破韧
    SEQ_DOUBLEWEAPON_DIGING,//双持武器挖掘动作
    SEQ_VACANTVORTEX_CREATE, //创建虚空团子
    SEQ_VACANTVORTEX_END, //创建虚空团子结束

    SEQ_DUDU_THROB, //嘟嘟鸟 扑腾
    SEQ_ELK_PANIC,  //角鹿 小跳走
    SEQ_FOX_FIERCE,//狼 龇牙
    SEQ_LEFTRAISE_SHIELD,   // 左手持盾防御
    SEQ_ATTTACT_POWER, //引力蓄力
    SEQ_ATTTACT_BOMB, //爆炸

    SEQ_REVIVE_BASIC,//基础复活

    SEQ_FOLLOW_MOVE_BODY,//基础跟随移动动作
    SEQ_FOLLOW_MOVE_BODY1,//进阶跟随移动动作
    SEQ_CLIMB,//攀爬动作
    SEQ_MOB_FLY,//飞行动作

    SEQ_UI_STAND,  // 界面中的stand
    SEQ_SNEAK_IDLE,	// SEQ_SNEAK_IDLE,     // 潜行
    SEQ_SNEAK_WALK,	// SEQ_SNEAK_WALK,     // 潜行移动
    SEQ_CRAWL_FORWARD, //重伤状态前进爬行
    SEQ_CRAWL_BACKWARD, //重伤状态后退爬行
    SEQ_CRAWL_LEFT, //重伤状态左爬行
    SEQ_CRAWL_RIGHT, //重伤状态右爬行
    SEQ_LAYDOWN_IDLE, //仰头躺底倒地

    SEQ_TOOL_UPBODY_IDLE, // 工具待机
    SEQ_TOOL_UPBODY_WALK, // 工具移动
    SEQ_TOOL_UPBODY_RUN, // 工具跑动
    MAX_SEQ,

};
//tolua_end

//tolua_begin
inline int ComposePlayerIndex(int modelid, int geniuslv = 0, int skinid = 0)
{
	if (modelid <= 0 || modelid >= 16) modelid = 1;
	if (geniuslv < 0 || geniuslv >= 16) geniuslv = 0;
	//if (skinid < 0 || skinid >= 256) skinid = 0;
	if (skinid < 0 || (skinid >> 16)>0) skinid = 0;

	return modelid | (geniuslv << 4) | (skinid << 8);
};
//tolua_end
#endif // __ACTOR_BODY_SEQUENCE_H__