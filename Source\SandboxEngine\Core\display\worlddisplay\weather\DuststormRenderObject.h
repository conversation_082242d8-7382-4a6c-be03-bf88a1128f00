#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

//using namespace Rainbow;
class DuststormRenderable;
class DuststormMeshData;

class DuststormRenderObject : public Rainbow::RenderObject
{
public:
	explicit DuststormRenderObject(DuststormRenderable* component);
	~DuststormRenderObject();

	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;


private:
	DuststormRenderable* m_DuststormEffect;
	Rainbow::MeshRenderData m_MeshRenderData;
	DuststormMeshData* m_RenderBuffer;

	dynamic_array<Rainbow::DrawBuffersRange> m_DrawBuffersRanges;
};

