//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Math/Vector3f.h"
#include "Public/NavMeshTypes.h"

class QueryFilter;
class NavMeshQuery;

class LocalBoundary
{
public:
    LocalBoundary();
    ~LocalBoundary();

    void Reset();

    void Update(const NavMeshPolyRef centerRef, const Rainbow::Vector3f& center, const float collisionQueryRange,
        const NavMeshQuery* navquery, const QueryFilter* filter);

    inline const Rainbow::Vector3f& GetCenter() const { return m_center; }
    inline NavMeshPolyRef GetCenterRef() const { return m_centerRef; }
    inline float GetQueryRange() const { return m_range; }
    inline int GetSegmentCount() const { return m_segmentCount; }
    inline const Rainbow::Vector3f& GetSegmentStart(int i) const { return m_segments[i].start; }
    inline const Rainbow::Vector3f& GetSegmentEnd(int i) const { return m_segments[i].end; }

private:
    enum
    {
        kMaxSegments = 8,
    };

    struct Segment
    {
        Rainbow::Vector3f start, end;    // Segment start/end
        float dist;             // Distance for pruning.
    };

    void AddSegment(const float dist, const Rainbow::Vector3f& start, const Rainbow::Vector3f& end);

    Rainbow::Vector3f m_center;
    NavMeshPolyRef m_centerRef;
    float m_range;
    Segment m_segments[kMaxSegments];
    int m_segmentCount;
};
