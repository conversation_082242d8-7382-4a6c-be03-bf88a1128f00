/**
* file : SandboxLuaScript
* func : lua脚本对象
* by : chenzh
*/
#include "SandboxLuaScript.h"
#include "SandboxLua.h"
#include "SandboxSceneObjectBridge.h"
#include "SandboxAutoRef.h"
#include "Common/OgreShared.h"
#include "SandboxLuaScriptState.h"
#include "SandboxLuaCoroutine.h"
#include "File/Packages/DataStream.h"
#include "SandboxCustomBuffer.h"
#include "script/SandboxLuaCoroutineJob.h"


namespace MNSandbox {


	LuaScript::LuaScript(WeakRef<SandboxNode> node)
		: m_owner(node)
		, m_listenLuaPause(this, &LuaScript::OnListenLuaPause)
		, m_listenLuaReturn(this, &LuaScript::OnListenLuaReturn)
		, m_listenLuaOver(this, &LuaScript::OnListenLuaOver)
	{
	}

	LuaScript::~LuaScript()
	{
		StopScript();
	}

	bool LuaScript::StartScript(AutoRef<CustomBuffer>& luacode)
	{
#if defined SANDBOX_DEV
		if (Config::GetSingleton().m_debugShieldFlag.CheckFlag(Config::DEBUG_SHIELDFLAG::SANDBOX_TICK))
			return false;
#endif
#ifndef DEDICATED_SERVER
		// 本地云服调试，不执行
		if (Config::GetSingleton().IsLCSMode())
			return false;
#endif
		if (!luacode || luacode->Size() == 0)
			return false;
		if (!m_owner)
		{
			SANDBOX_ASSERT(false && "owner is nil");
			return false;
		}


		// 绑定协程任务
		AutoRef<Lua::CoJobData_LuaCode> cojob = SANDBOX_NEW(Lua::CoJobData_LuaCode, Lua::CoJobData_LuaCode::MODE::BUFFER, luacode);
		cojob->SetNode(m_owner);

		AutoRef<LuaCoroutine> co = GetSandboxScriptVM().CreateCoroutine(cojob);
		if (!co)
		{
			SANDBOX_ASSERT(false && "new thread failed!");
			return false;
		}
		co->SetNode(m_owner); // 设置节点
		m_coroutine = co;

		// 绑定lua_State* 与 节点关系
		ScriptState::NotifyLoadScript(m_coroutine, m_owner);

		return PlayScript();
	}

	bool LuaScript::StartScript(const std::string& luacode)
	{
		AutoRef<CustomBuffer> cb = CustomBuffer::CreateStatic((void*)luacode.c_str(), luacode.length());
		return StartScript(cb);
	}

	bool LuaScript::PlayScript()
	{
		// 通知
		m_coroutine->m_notifyPause.Subscribe(m_listenLuaPause);
		m_coroutine->m_notifyReturn.Subscribe(m_listenLuaReturn);
		m_coroutine->m_notifyOver.Subscribe(m_listenLuaOver);

		// 运行
		m_runState = RUNSTATE::RUNNING;
		m_coroutine->Play();
		return true;
	}

	void LuaScript::StopScript()
	{
		if (m_coroutine)
		{
			m_coroutine->m_notifyOver.Unsubscribe(m_listenLuaOver);
			m_coroutine->m_notifyReturn.Unsubscribe(m_listenLuaReturn); // 取消订阅
			GetSandboxScriptVM().DestroyCoroutine(m_coroutine);
			m_coroutine = nullptr;
			m_runState = RUNSTATE::IDLE;
		}
	}

	int LuaScript::Pause()
	{
		if (m_coroutine)
		{
			AutoRef<LuaCoroutine> co = m_coroutine;
			return m_coroutine->Pause();
		}
		return 0;
	}

	void LuaScript::Resume(int narg)
	{
		if (m_coroutine)
		{
			AutoRef<LuaCoroutine> co = m_coroutine;
			m_coroutine->Resume(narg);
		}
	}

	void LuaScript::OnListenLuaPause()
	{
		m_runState = RUNSTATE::PAUSE;
	}

	void LuaScript::OnListenLuaReturn(LuaCoroutine* co, const AutoRef<Lua::LinkerData>& result)
	{
		SANDBOX_ASSERT(m_coroutine == co);
		if (m_notifyLuaReturn.IsValid())
		{
			m_notifyLuaReturn.Emit(co, result);
		}
	}

	void LuaScript::OnListenLuaOver(LuaCoroutine* co, bool success)
	{
		m_runState = success ? RUNSTATE::SUCCESS : RUNSTATE::FAILED;
		if (m_notifyLuaOver.IsValid())
		{
			m_notifyLuaOver.Emit();
		}
	}

}