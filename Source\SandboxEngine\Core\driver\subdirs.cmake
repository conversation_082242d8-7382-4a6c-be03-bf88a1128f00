set(ret_subdirs "")
set(ret_subdirs ${ret_subdirs} "./")
set(ret_subdirs ${ret_subdirs} "asset")
set(ret_subdirs ${ret_subdirs} "asset/sync")
set(ret_subdirs ${ret_subdirs} "asset/new/http")
set(ret_subdirs ${ret_subdirs} "asset/new/upload")
set(ret_subdirs ${ret_subdirs} "asset/new")
set(ret_subdirs ${ret_subdirs} "asset/new/ref")
set(ret_subdirs ${ret_subdirs} "base")
set(ret_subdirs ${ret_subdirs} "base/reflex")
set(ret_subdirs ${ret_subdirs} "base/timer")
set(ret_subdirs ${ret_subdirs} "base/stream")
set(ret_subdirs ${ret_subdirs} "event")
set(ret_subdirs ${ret_subdirs} "event/notify")
set(ret_subdirs ${ret_subdirs} "event/remote")
set(ret_subdirs ${ret_subdirs} "factory")
set(ret_subdirs ${ret_subdirs} "fps")
set(ret_subdirs ${ret_subdirs} "math")
set(ret_subdirs ${ret_subdirs} "platform")
set(ret_subdirs ${ret_subdirs} "scene")
set(ret_subdirs ${ret_subdirs} "scene/map")
set(ret_subdirs ${ret_subdirs} "scene/node")
set(ret_subdirs ${ret_subdirs} "script")
set(ret_subdirs ${ret_subdirs} "script/bridge")
set(ret_subdirs ${ret_subdirs} "script/linker")
set(ret_subdirs ${ret_subdirs} "script/userdata")
set(ret_subdirs ${ret_subdirs} "util")
set(ret_subdirs ${ret_subdirs} "miscdata")
