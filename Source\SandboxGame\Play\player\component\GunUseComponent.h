#ifndef __GUNUSE_COMPONENT_H__
#define __GUNUSE_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "Math/Vector3f.h"
#include "SandboxGame.h"
#include "BasicIK.h"
#include "BaseClass/EventDispatcher.h"

#define AlotmRecoilData 1

struct ToolDef;
class ClientPlayer;
struct GunDef;

#include "SandboxCallback.h"

class EXPORT_SANDBOXGAME GunUseComponent;
class GunUseComponent : public ActorComponentBase//tolua_export
{//tolua_export
public:
	DECLARE_COMPONENTCLASS(GunUseComponent)

	GunUseComponent();
	~GunUseComponent();
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

	void registerThirdPersonEvent();
	void unregisterThirdPersonEvent();

	void registerFirstPersonEvent();
	void unregisterFirstPersonEvent();
	/**
	* 游戏帧更新
	*/
	virtual void OnUpdate(float dt) override;
	const char& getComViewMode() const { return m_cViewMode; }
public:
	void reset();
	//tolua_begin
	void setGunDef(const GunDef* def);
	const GunDef* getGunDef();
	bool fireOnce(bool bAuto = false);
	int getMagazine();
	void setMagazine(int count);
	void doReload(int count = -1);
	void doCostBulletItem(int bulletid, int num);
	bool isFirstShoot();
	bool doWaterCanoonSkill();
	bool isInGunAction()
	{
		return m_IsFire || m_IsReload;
	}
	WCoord getMuzzlePos();
	void setIsReload(bool isReload);

	void setIsFire(bool isFire)
	{
		m_IsFire = isFire;
		if (isFire)
		{
			m_IsReload = false;
		}
	}

	bool isReload()
	{
		return m_IsReload;
	}

	bool isFire()
	{
		return m_IsFire;
	}

	int getGunSpread();
	void getDir(float& jaw, float& pitch, Rainbow::Vector3f& pos) { jaw = m_CurrentJaw, pitch = m_CurrentPitch; pos = m_CurrentPos; }
	void setGunInfo(float spread, float jaw, float pitch, Rainbow::Vector3f pos);
	float getGunSpeedRatio();

	void resetRecoil();
	void setZoom(bool open);
	bool getZoom();

	//获取枪当前子弹量
	int getBulletNum();
	int getMaxMagazines();
	//当前是否能够换子弹
	bool canReload();

	int getFireInterval()
	{
		return m_FireInterval;
	}
	void setFireInterval(int fireInterval)
	{
		m_FireInterval = fireInterval;
	}

	float getCurrentSpread()
	{
		return m_CurrentSpread;
	}

	void setPulledGunId(int gunid) { m_nPulledGunId = gunid; }
	int  getPulledGunId() { return m_nPulledGunId; }
	bool canUseGun(int gunId, unsigned int useTick);
	void setGunUse(int gunId, unsigned int useTick);
	int getGunUse(int gunId);
	void clearGunCanFireTime() { m_GunCanFireTime.clear(); }
	//tolua_end
	void doGunFire(int id);
	void setUpdate();
	static int getReloadTime(int uin, int gunId);
	void increaseSpreadOnFire();
	void decreaseSpreadOnTime(float dt);
	void onUpdate(float dt);
	void OnAnimatorPostEvent(const Rainbow::EventContent* evt);
	void setComViewMode(char viewMode);
private:
	void openTelescope();
	void closeTelescope();

	bool doShootJob();

	bool isWaterCannon();
private:
	const GunDef* m_GunDef;
	const ToolDef* m_ToolDef;
	ClientPlayer* m_Host;
	float m_GunCoolDown;
	int m_Magazine;
	float m_CurrentSpread;
	float m_CurrentJaw;		// 客机传来的当前朝向
	float m_CurrentPitch;	// 客机传来的当前朝向
	Rainbow::Vector3f m_CurrentPos;	// 客机传来的当前位置
	int m_FireInterval;		//客机传来的换挡
	bool m_IsReload;
	bool m_IsFire;
	float m_LastRotateX;
	bool m_NeedCameraRecovery;
	bool m_ReloadRecovery;
	float m_LastFov;
	float m_LastSensitivity;
	int m_LastViewMode;
	int m_nPulledGunId;
	// 服务器射击时检测可射击时间
	std::map<int, unsigned> m_GunCanFireTime;

	float m_RecoilTotalRotate = 0.f;
	bool m_IsIKRecovery = false;

	BasicIK* m_pBasicIK = nullptr;
	bool m_IsRegisterAfterAnimationProcessEvent = false;
	char m_cViewMode = -1;

#if AlotmRecoilData
	//test 调试数据 完成要转到配置表中
	//第一人称
	float m_RecoilSpeedFirst = 0.1f;
	float m_MaxRecoilFirst = 3.f;
	float m_RecoilRecoverySpeedFirst = 0.4f;

	//右手数据
	Rainbow::Vector3f m_vRightHandOffsetFirst = Rainbow::Vector3f(0,5,-2);
	Rainbow::Vector3f m_vRightPoleRelativeToEffectorFirst = Rainbow::Vector3f(0.1,0.5,-0.1);
	float m_fRightWeightFirst = 0.5f;
	bool m_bRightStretchEnableFirst = true;
	float m_fRightStretchStartRatioFirst = 0.9f;
	float m_fRightStretchMaxRatioFirst = 1.05f;
	float m_fRightSecondaryAxisWeightFirst = 0.05f;

	//左手数据
	Rainbow::Vector3f m_vLeftHandOffsetFirst = Rainbow::Vector3f(0, 5, -2);
	Rainbow::Vector3f m_vLeftPoleRelativeToEffectorFirst = Rainbow::Vector3f(0.1, 0.5, -0.1);
	float m_fLeftWeightFirst = 0.5f;
	bool m_bLeftStretchEnableFirst = true;
	float m_fLeftStretchStartRatioFirst = 0.9f;
	float m_fLeftStretchMaxRatioFirst = 1.05f;
	float m_fLeftSecondaryAxisWeightFirst = 0.05f;


	//第三人称
	float m_RecoilSpeedThird = 0.f;
	float m_MaxRecoilThird = 0.f;
	float m_RecoilRecoverySpeedThird = 0.f;

	//右手数据
	Rainbow::Vector3f m_vRightHandOffsetThird = Rainbow::Vector3f::zero;
	Rainbow::Vector3f m_vRightPoleRelativeToEffectorThird = Rainbow::Vector3f::zero;
	float m_fRightWeightThird = 1.f;
	bool m_bRightStretchEnableThird = false;
	float m_fRightStretchStartRatioThird = 0.f;
	float m_fRightStretchMaxRatioThird = 0.f;
	float m_fRightSecondaryAxisWeightThird = 0.f;

	//左手数据
	Rainbow::Vector3f m_vLeftHandOffsetThird = Rainbow::Vector3f::zero;
	Rainbow::Vector3f m_vLeftPoleRelativeToEffectorThird = Rainbow::Vector3f::zero;
	float m_fLeftWeightThird = 1.f;
	bool m_bLeftStretchEnableThird = false;
	float m_fLeftStretchStartRatioThird = 0.f;
	float m_fLeftStretchMaxRatioThird = 0.f;
	float m_fLeftSecondaryAxisWeightThird = 0.f;

	MNSandbox::Callback m_pTestCallback;
#endif
};//tolua_export

#endif