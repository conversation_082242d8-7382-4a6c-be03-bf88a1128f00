//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Public/NavMeshTypes.h"
#include "Math/Vector3f.h"
#include "Utilities/dynamic_array.h"


typedef unsigned short NavMeshNodeIndex;
static const NavMeshNodeIndex kNavMeshNodeNullIndex = (NavMeshNodeIndex) ~0;
static const unsigned short kMaxNavMeshNodePoolSize = USHRT_MAX;

struct NavMeshNode
{
    enum NavMeshNodeFlags
    {
        kNew = 0x00,
        kOpen = 0x01,
        kClosed = 0x02,
    };

    Rainbow::Vector3f pos;               // Position of the node.
    float cost;                 // Cost from previous node to current node.
    float total;                // Cost up to the node.
    unsigned int pidx : 30;     // Index to parent node.
    unsigned int flags : 2;     // NavMeshNode flags new/open/closed.
    NavMeshPolyRef id;          // Polygon ref the node corresponds to.
};


class NavMeshNodePool
{
public:
    NavMeshNodePool(int maxNavMeshNodes, int hashSize, MemLabelId label = kMemAI);
    ~NavMeshNodePool();
    void Clear();
    NavMeshNode* GetNode(NavMeshPolyRef id);
    NavMeshNode* FindNavMeshNode(NavMeshPolyRef id);

    inline unsigned int GetNodeIdx(const NavMeshNode* node) const
    {
        if (!node) return 0;
        unsigned int idx = (node - &m_NavMeshNodes[0]) + 1;
        Assert(idx <= m_MaxNavMeshNodes);
        return idx;
    }

    inline NavMeshNode* GetNodeAtIdx(unsigned int idx)
    {
        Assert(idx <= m_MaxNavMeshNodes);
        if (!idx) return 0;
        return &m_NavMeshNodes[idx - 1];
    }

    inline const NavMeshNode* GetNodeAtIdx(unsigned int idx) const
    {
        Assert(idx <= m_MaxNavMeshNodes);
        if (!idx) return 0;
        return &m_NavMeshNodes[idx - 1];
    }

    inline int GetHashSize() const { return m_HashSize; }
    inline NavMeshNodeIndex GetFirst(int bucket) const { return m_First[bucket]; }
    inline NavMeshNodeIndex GetNext(int i) const { return m_Next[i]; }

private:

    const int m_MaxNavMeshNodes;
    const int m_HashSize;
    int m_NavMeshNodeCount;
    dynamic_array<NavMeshNode> m_NavMeshNodes;
    dynamic_array<NavMeshNodeIndex> m_First;
    dynamic_array<NavMeshNodeIndex> m_Next;
};

class NavMeshNodeQueue
{
public:
    explicit NavMeshNodeQueue(int n);
    ~NavMeshNodeQueue();

    inline void Clear()
    {
        m_Size = 0;
    }

    inline NavMeshNode* Pop()
    {
        NavMeshNode* result = m_Heap[0];
        m_Size--;
        TrickleDown(0, m_Heap[m_Size]);
        return result;
    }

    inline void Push(NavMeshNode* node)
    {
        m_Size++;
        BubbleUp(m_Size - 1, node);
    }

    inline void Modify(NavMeshNode* node)
    {
        for (int i = 0; i < m_Size; ++i)
        {
            if (m_Heap[i] == node)
            {
                BubbleUp(i, node);
                return;
            }
        }
    }

    inline bool empty() const { return m_Size == 0; }

private:
    void BubbleUp(int i, NavMeshNode* node);
    void TrickleDown(int i, NavMeshNode* node);

    dynamic_array<NavMeshNode*> m_Heap;
    int m_Size;
};
