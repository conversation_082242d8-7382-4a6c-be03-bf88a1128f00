#include "ActorCSProto.h"
#include "ActorAttrib.h"
#include "ActorBody.h"
#include "ActorLocoMotion.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "backpack.h"
#include "IRecordInterface.h"

#include "Math/FloatConversion.h"
#include "PlayerAttrib.h"
#include "SandboxIdDef.h"
#include "blocks/special_blockid.h"

void storeBuff(PB_ActorBuffList *pstBuffList, LivingAttrib *pstActorAttr)
{
	std::vector<ActorBuff>::iterator iter = pstActorAttr->m_Buffs.begin();
	while (iter != pstActorAttr->m_Buffs.end())
	{
		if (!iter->def) {
			iter++;
			continue;
		}
		PB_ActorBuff* actorBuff = pstBuffList->add_buffs();
		actorBuff->set_buffid(iter->buffid);
		actorBuff->set_bufflv(iter->bufflv);
		actorBuff->set_ticks(iter->ticks);
		iter++;
	}
}

void restoreBuff(const PB_ActorBuffList &actorBuffList, LivingAttrib *pstActorAttr)
{
	int i;

	if (pstActorAttr == NULL) return;

	if (!pstActorAttr->m_Attribs.empty()) memset(&pstActorAttr->m_Attribs[0], 0, pstActorAttr->m_Attribs.size() * sizeof(AttribModified));

	for (i = 0; i<actorBuffList.buffs_size(); i++)
	{
		pstActorAttr->addBuffOnLoad(actorBuffList.buffs(i).buffid(), actorBuffList.buffs(i).bufflv(), actorBuffList.buffs(i).ticks());
	}
}

void storeDir(PB_BodyDir *pstBodyDir, ActorLocoMotion *pLocoMotion)
{
	PB_Vector3 *pMotion = pstBodyDir->mutable_motion();
	pMotion->set_x((int)pLocoMotion->m_Motion.x);
	pMotion->set_y((int)pLocoMotion->m_Motion.y);
	pMotion->set_z((int)pLocoMotion->m_Motion.z);
	pstBodyDir->set_rotationpitch(pLocoMotion->m_RotationPitch);
	pstBodyDir->set_rotationyaw(pLocoMotion->m_RotateYaw);

	return;
}

void restoreDir(const PB_BodyDir &bodyDir, ActorLocoMotion *pLocoMotion)
{
	if (pLocoMotion == NULL) return;
	pLocoMotion->m_Motion.x = (float)bodyDir.motion().x();
	pLocoMotion->m_Motion.y = (float)bodyDir.motion().y();
	pLocoMotion->m_Motion.z = (float)bodyDir.motion().z();

	pLocoMotion->m_RotationPitch = bodyDir.rotationpitch();
	pLocoMotion->m_RotateYaw = bodyDir.rotationyaw();

	return;
}

void storeAttr(PB_RoleData *pData, PlayerAttrib *pstPlayerAttrib)
{
	//LOG_INFO("storeAttr():");
	if (pstPlayerAttrib == NULL) return;
	pData->set_exp(pstPlayerAttrib->getExp());

	pData->set_level(1);
	pData->set_usedstamina(	(int)pstPlayerAttrib->m_UsedStamina);
	pData->set_oxygen(		(int)pstPlayerAttrib->getOxygen());
	pData->set_foodlevel(	(int)pstPlayerAttrib->m_FoodLevel);
	pData->set_foodsatlevel((int)pstPlayerAttrib->m_FoodSatLevel);
	pData->set_hp(pstPlayerAttrib->getHP());
	pData->set_maxhp(pstPlayerAttrib->getMaxHP());
	pData->set_strength(pstPlayerAttrib->getStrength());
	pData->set_max_strength(pstPlayerAttrib->getMaxStrength());
	pData->set_enable_strength(pstPlayerAttrib->useCompatibleStrength());
	pData->set_strengthfoodshowstate(pstPlayerAttrib->strengthFoodShowState());
	//海外合入, 生命体力值优化-2022.7.4 caozegang
	pData->set_armor(pstPlayerAttrib->getArmor());
	pData->set_perseverance(pstPlayerAttrib->getPerseverance());
	pData->set_stardebuffstage(pstPlayerAttrib->getStarDebuffStage());
	return;
}

void restoreAttr(const PB_RoleData &roleData, PlayerAttrib *pstPlayerAttrib)
{
	//LOG_INFO("restoreAttr():");
	if (pstPlayerAttrib == NULL) return;
	pstPlayerAttrib->m_FoodLevel	= (float)roleData.foodlevel();
	pstPlayerAttrib->m_FoodSatLevel = (float)roleData.foodsatlevel();
	pstPlayerAttrib->setMaxHP(roleData.maxhp());
	pstPlayerAttrib->initHP (roleData.hp());
#ifndef IWORLD_SERVER_BUILD
	if (pstPlayerAttrib->getHP()<= 0 && GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		pstPlayerAttrib->initHP(pstPlayerAttrib->getMaxHP());
	}
#endif
	pstPlayerAttrib->setOxygen((float)roleData.oxygen());
	pstPlayerAttrib->m_UsedStamina	= (float)roleData.usedstamina();
	if (roleData.has_strength())
	{
		//LOG_INFO("restoreAttr(): roleData.strength() = %.2f", roleData.strength());
		pstPlayerAttrib->setStrength(roleData.strength());
	}
	if (roleData.has_max_strength())
	{
		pstPlayerAttrib->setMaxStrength(roleData.max_strength());
	}
	if (roleData.has_enable_strength())
	{
		pstPlayerAttrib->toggleUseCompatibleStrength(roleData.enable_strength());
	}
	if (roleData.has_strengthfoodshowstate())
	{
		pstPlayerAttrib->setStrengthFoodShowState(roleData.strengthfoodshowstate());
	}
	//海外合入, 生命体力值优化-2022.7.4 caozegang
	if (roleData.has_armor())
	{
		pstPlayerAttrib->setArmor(roleData.armor());
	}
	if (roleData.has_perseverance())
	{
		pstPlayerAttrib->setPerseverance(roleData.perseverance());
	}
	int exp = roleData.exp();
	if (exp & (1 << 31))
	{
		pstPlayerAttrib->setExp(DecodeActorAttr(exp, static_cast<ClientPlayer *>(pstPlayerAttrib->getOwnerActor())->getUin()));
	}
	else pstPlayerAttrib->setExp(exp);

	if (roleData.has_stardebuffstage())
	{
		pstPlayerAttrib->setStarDebuffStage(roleData.stardebuffstage());
	}
}

void storeGrid(PB_ItemGrid *pstItemGrid, const BackPackGrid *pstPackGrid, int iIdx)
{
	if (pstPackGrid == NULL || pstPackGrid->def == NULL)
	{
		pstItemGrid->set_type(ITEM_GRID_ITEM);
		PB_ItemGridData *pItemGridData = pstItemGrid->mutable_itemgriddata();
		PB_Item* pItem = pItemGridData->mutable_item();
		pItem->set_idx(iIdx);
		return;
	}

	DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
	ToolDef *tool = toolTable.GetRecord(pstPackGrid->def->ID);
	const GunDef* gundef = GetDefManagerProxy()->getGunDef(pstPackGrid->def->ID);

	if ((!tool) || (pstPackGrid->def->StackMax > 1))
	{
		pstItemGrid->set_type(ITEM_GRID_ITEM);
		PB_ItemGridData *pItemGridData = pstItemGrid->mutable_itemgriddata();
		PB_Item* pItem = pItemGridData->mutable_item();
		pItem->set_defid(pstPackGrid->def->ID);
		pItem->set_idx(iIdx);
		pItem->set_pile(pstPackGrid->getNum());
		pItem->set_userdatastr(pstPackGrid->userdata_str);
	}
	else
	{
		pstItemGrid->set_type(ITEM_GRID_ARM);
		PB_ItemGridData *pItemGridData = pstItemGrid->mutable_itemgriddata();
		PB_Arm* pArm = pItemGridData->mutable_arm();
		pArm->set_defid(pstPackGrid->def->ID);
		pArm->set_dur(pstPackGrid->getDuration());
		pArm->set_idx(iIdx);
		if (pstPackGrid->def && pstPackGrid->def->ID == ITEM_SPRINKLER || pstPackGrid->def->Type == 100)
		{
			//花洒剩余次数存放在userDataInt上 贴图依赖次数 所以联机增加该字段同步
			pArm->set_userdataint(pstPackGrid->getConstUserDataInt());
		}
		else if (pstPackGrid->def && gundef)
		{
			//射击武器子弹剩余数量存放在userDataInt上联机增加该字段同步
			pArm->set_userdataint(pstPackGrid->getConstUserDataInt());
		}
			
		pstPackGrid->saveRunesAndEnchants(pArm->mutable_fumo());
		pstPackGrid->saveDataComponentPB(pArm);
	}

	if (pstPackGrid->m_effects.size() > 0)
	{
		for (auto it = pstPackGrid->m_effects.begin(); it != pstPackGrid->m_effects.end(); it++)
		{
			auto info = pstItemGrid->add_grideffects();
			info->set_effectname(it->second.effectname);
			info->set_effecscale(it->second.scale);
		}
	}

	return;
}

void restoreGrid(const PB_ItemGrid &itemGrid, BackPackGrid *pstPackGrid)
{
	if (itemGrid.type() == ITEM_GRID_ITEM)
	{
		void* userdataInt = 0;
		const auto& item = itemGrid.itemgriddata().item();
		int itemid = item.defid();

		//SetBackPackGrid(*pstPackGrid, itemGrid.itemgriddata().item().defid(), itemGrid.itemgriddata().item().pile(), -1, 0, 1, 0, itemGrid.itemgriddata().item().userdatastr().c_str());
		SetBackPackGrid(*pstPackGrid, itemid, item.pile(), -1, -1, userdataInt, 1, 0, item.userdatastr().c_str());
		pstPackGrid->loadDataComponentPB(itemGrid.itemgriddata().arm());
	}
	else
	{
		const auto& arm = itemGrid.itemgriddata().arm();
		int itemid = arm.defid();
		SetBackPackGrid(*pstPackGrid, itemid, 1, arm.dur(), -1, (void *)arm.userdataint());
		
		pstPackGrid->loadRunesAndEnchants(arm.fumo());
		pstPackGrid->loadDataComponentPB(arm);
		pstPackGrid->m_effects.clear();
		if (itemGrid.grideffects_size() > 0)
		{
			for (int i = 0; i < itemGrid.grideffects_size(); i++)
			{
				auto info = itemGrid.grideffects(i);
				GridEffect grideff;
				grideff.effectname = info.effectname();
				grideff.scale = info.effecscale();
				pstPackGrid->m_effects[info.effectname()] = grideff;
			}
		}
	}
}

void storeGridData(PB_ItemGridData *pstItemGrid, const BackPackGrid *pstPackGrid, int iIdx, int *itemType)
{
	if (pstPackGrid == NULL || pstPackGrid->def == NULL)
	{
		*itemType = ITEM_GRID_ITEM;
		pstItemGrid->mutable_item()->set_idx(iIdx);
		return;
	}

	DefDataTable<ToolDef> &toolTable = GetDefManagerProxy()->getToolTable();
	ToolDef *tool = toolTable.GetRecord(pstPackGrid->def->ID);

	if ((!tool) || (pstPackGrid->def->StackMax > 1))
	{
		*itemType = ITEM_GRID_ITEM;
		PB_Item* pItem = pstItemGrid->mutable_item();
		pItem->set_defid(pstPackGrid->def->ID);
		pItem->set_idx(iIdx);
		pItem->set_pile(pstPackGrid->getNum());

		//新枪械没有tooldef
		PB_Arm* pArm = pstItemGrid->mutable_arm();
		pstPackGrid->saveDataComponentPB(pArm);
	}
	else
	{
		*itemType = ITEM_GRID_ARM;
		PB_Arm* pArm = pstItemGrid->mutable_arm();
		pArm->set_defid(pstPackGrid->def->ID);
		pArm->set_dur(pstPackGrid->getDuration());
		pArm->set_idx(iIdx);

		pstPackGrid->saveRunesAndEnchants(pArm->mutable_fumo());
		pstPackGrid->saveDataComponentPB(pArm);
	}
}

void restoreGridData(const PB_ItemGridData *pstItemGrid, BackPackGrid *pstPackGrid, int itemType)
{
	if (itemType == ITEM_GRID_ITEM)
	{
		SetBackPackGrid(*pstPackGrid, pstItemGrid->item().defid(), pstItemGrid->item().pile());
		pstPackGrid->loadDataComponentPB(pstItemGrid->arm());
	}
	else
	{
		SetBackPackGrid(*pstPackGrid, pstItemGrid->arm().defid(), pstItemGrid->arm().dur());
		pstPackGrid->loadRunesAndEnchants(pstItemGrid->arm().fumo());
		pstPackGrid->loadDataComponentPB(pstItemGrid->arm());
	}
}

void storeGridData(PB_ItemData *dest, const BackPackGrid *src, int src_index)
{
	if (src_index < 0) src_index = src->getIndex();

	if (src == NULL || src->def == NULL)
	{
		if(dest)
		{
			dest->Clear();
			dest->set_index(src_index);
		}
		return;
	}

	if(dest != NULL && src != NULL){
		dest->set_index(src_index);
		dest->set_itemid(src->def->ID);
		dest->set_num(src->getNum());
		dest->set_durable(src->getDuration());
		dest->set_maxdurable(src->getMaxDuration());
		dest->set_toughness(src->getToughness());
		dest->set_userdata((int)(size_t)src->userdata); //(char)
		//染色方块和调色架需要传userdata_str by：Jeff
		if (IsDyeableBlock(src->def->ID) || src->def->ID == BLOCK_COLOR_PALETTE_STAND || src->def->ID == BLOCK_COLOR_PALETTE_HANG || src->def->ID == ITEM_POLAROID_PHOTO)
		{
			dest->set_userdatastr(src->userdata_str);
		}
		src->saveRunesAndEnchants(dest);
		src->saveDataComponentPB(dest);
		if (src->m_effects.size() > 0)
		{
			for (auto it = src->m_effects.begin(); it != src->m_effects.end(); it++)
			{
				auto info = dest->add_grideffects();
				info->set_effectname(it->second.effectname);
				info->set_effecscale(it->second.scale);
			}
		}
	}
}

void restoreGridData(BackPackGrid *dest, const PB_ItemData &src)
{
	if (dest == NULL) return;

	SetBackPackGrid(*dest, src.itemid(), src.num(), src.durable(), src.toughness(),(void *)(long)(src.userdata()));
	dest->setMaxDuration(src.maxdurable());

	//染色方块和调色架需要传userdata_str by：Jeff
	if ((IsDyeableBlock(src.itemid()) || src.itemid() == BLOCK_COLOR_PALETTE_STAND || src.itemid() == BLOCK_COLOR_PALETTE_HANG || src.itemid() == ITEM_POLAROID_PHOTO) && src.has_userdatastr())
	{
		dest->setUserdataStr(src.userdatastr().c_str());
	}
	dest->setIndex(src.index());

	dest->loadRunesAndEnchants(src);
	dest->loadDataComponentPB(src);
	dest->m_effects.clear();
	if (src.grideffects_size() > 0)
	{
		for (int i = 0; i < src.grideffects_size(); i++)
		{
			auto info = src.grideffects(i);
			GridEffect grideff;
			grideff.effectname = info.effectname();
			grideff.scale = info.effecscale();
			dest->m_effects[info.effectname()] = grideff;
		}
	}
}

int storeGridArray(RepeatedPtrField<PB_ItemGrid>* pItemGrid, int maxitems, BackPackGrid *packgrids, int ngrid)
{
	int count = 0;
	for (int i = 0; i < ngrid; i++)
	{
		if (packgrids[i].def)
		{
			if (count == maxitems) break;

			count++;
			PB_ItemGrid* itemGrid = pItemGrid->Add();
			storeGrid(itemGrid, &packgrids[i], i);
		}
	}
	return count;
}

void restoreGridArray(const RepeatedPtrField<PB_ItemGrid>& itemGrids, int nitem, BackPackGrid *packgrids, int ngrid)
{
	for (int i = 0; i < nitem; i++)
	{
		int index;
		const PB_ItemGrid itemGrid = itemGrids.Get(i);
		if (itemGrid.type() == ITEM_GRID_ITEM)
		{
			PB_Item item = itemGrid.itemgriddata().item();
			index = item.idx();
		}
		else
		{
			PB_Arm arm = itemGrid.itemgriddata().arm();
			index = arm.idx();
		}

		assert(index >= 0 && index < ngrid);
		if (index >= ngrid) break;
		restoreGrid(itemGrid, &packgrids[index]);
	}
}

int storeGridArray(RepeatedPtrField<PB_ItemGrid>* pItemGrid, int maxitems, std::vector<BackPackGrid>&packgrids)
{
	if (packgrids.empty()) return 0;
	else return storeGridArray(pItemGrid, maxitems, &packgrids[0], int(packgrids.size()));
}

void restoreGridArray(const RepeatedPtrField<PB_ItemGrid>& itemGrid, int nitem, std::vector<BackPackGrid>&packgrids)
{
	if (!packgrids.empty())
	{
		restoreGridArray(itemGrid, nitem, &packgrids[0], int(packgrids.size()));
	}
}

void storePak(PB_RolePackage *pstPak, BackPack *pstBackPak, int iHandIdx)
{
	BaseContainer *pBaseEquip = pstBackPak->getContainer(EQUIP_START_INDEX);
	PackContainer *pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	PB_ArmPak* pArmPak = pstPak->mutable_armpak();
	for (int i = 0; i < MAX_ARMPAK_NUM; i++) pArmPak->add_grids();
	storeGridArray(pArmPak->mutable_grids(), MAX_ARMPAK_NUM, pPackEquip->m_Grids);

	pBaseEquip = pstBackPak->getContainer(BACKPACK_START_INDEX);
	pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	PB_GridPak* pGridPak = pstPak->mutable_gridpak();
	storeGridArray(pGridPak->mutable_grids(), MAX_GRIDPAK_NUM, pPackEquip->m_Grids);

	pBaseEquip = pstBackPak->getContainer(pstBackPak->getShortcutStartIndex());
	pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	PB_ShortcutPak* pShortcutPak = pstPak->mutable_shortcutpak();
	storeGridArray(pShortcutPak->mutable_grids(), MAX_SHORTCUTPAK_NUM, pPackEquip->m_Grids);
	pstPak->mutable_shortcutpak()->set_handidx(iHandIdx);
	return;
}

//扩展背包
void storeEXTPak(PB_RolePackage* pstPak, BackPack* pstBackPak, int iHandIdx, ClientPlayer* mPlayer)
{
	if (mPlayer && mPlayer->HaveExtBackPack() && pstBackPak && pstPak)
	{
		PB_GridPak* pExtPak = pstPak->mutable_extpak();
		BaseContainer* pBaseEquip = pstBackPak->getContainer(EXT_BACKPACK_START_INDEX);
		PackContainer* pPackEquip = dynamic_cast<PackContainer*>(pBaseEquip);
		if (pPackEquip)
		{
			storeGridArray(pExtPak->mutable_grids(), mPlayer->GetExtBackPackGridCount(), pPackEquip->m_Grids);
		}
	}
}

void restorePak(const PB_RolePackage &rolePackage, BackPack *pstBackPak, ClientPlayer *player)
{
	BaseContainer *pBaseEquip = pstBackPak->getContainer(EQUIP_START_INDEX);
	PackContainer *pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	restoreGridArray(rolePackage.armpak().grids(), rolePackage.armpak().grids_size(), pPackEquip->m_Grids);

	pBaseEquip = pstBackPak->getContainer(BACKPACK_START_INDEX);
	pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	restoreGridArray(rolePackage.gridpak().grids(), rolePackage.gridpak().grids_size(), pPackEquip->m_Grids);

	pBaseEquip = pstBackPak->getContainer(pstBackPak->getShortcutStartIndex());
	pPackEquip = dynamic_cast<PackContainer *>(pBaseEquip);
	restoreGridArray(rolePackage.shortcutpak().grids(), rolePackage.shortcutpak().grids_size(), pPackEquip->m_Grids);
	
	//扩展背包
	if (rolePackage.has_extpak())
	{
		pBaseEquip = pstBackPak->getContainer(EXT_BACKPACK_START_INDEX);
		pPackEquip = dynamic_cast<PackContainer*>(pBaseEquip);
		if (pPackEquip)
		{
			restoreGridArray(rolePackage.extpak().grids(), rolePackage.extpak().grids_size(), pPackEquip->m_Grids);
		}
	}

	player->applyEquips();
	player->onSetCurShortcut(rolePackage.shortcutpak().handidx());

	return;
}

void storePos(PB_Pos *pstPos, ActorLocoMotion *pLocoMotion)
{
	pstPos->set_x(pLocoMotion->m_Position.x);
	pstPos->set_z(pLocoMotion->m_Position.z);
	pstPos->set_y(Rainbow::Clamp(pLocoMotion->m_Position.y, int(Rainbow::MIN_SHORT), int(Rainbow::MAX_SHORT)));

	return;
}

void restorePos(const PB_Pos &pos, ActorLocoMotion *pLocoMotion)
{
	pLocoMotion->m_Position.x = pos.x();
	pLocoMotion->m_Position.y = pos.y();
	pLocoMotion->m_Position.z = pos.z();

	return;
}
