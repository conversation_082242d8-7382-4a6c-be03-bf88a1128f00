#if ENABLE_UNIT_TESTS

#include "Runtime/Testing/Testing.h"
#include "./FreeList.h"

UNIT_TEST_SUITE(FreeList)
{
    struct Item
    {
        Item() : data(123) { ctor_count++; }
        ~Item() { data = 0; dtor_count++; }

        unsigned int next;
        int data;

        static int ctor_count;
        static int dtor_count;
    };

    int Item::ctor_count = 0;
    int Item::dtor_count = 0;

    TEST(Zero_Capacity_After_Construction)
    {
        FreeList<Item> items;
        CHECK_EQUAL(0, items.Capacity());
    }

    TEST(Constructors_Called_On_Alloc)
    {
        FreeList<Item> items;
        unsigned int id = items.Alloc();

        CHECK_EQUAL(0, id);
        CHECK(items.Capacity() > 0);

        for (unsigned int i = 0; i < items.Capacity(); ++i)
            CHECK_EQUAL(123, items[i].data);
    }

    TEST(Destructor_Not_Called_On_Release)
    {
        Item::dtor_count = 0;

        FreeList<Item> items;
        unsigned int id = items.Alloc();
        const Item* item = &items[id];
        items.Release(id);
        CHECK_EQUAL(123, item->data);
        CHECK_EQUAL(0, Item::dtor_count);
    }

    TEST(Constructor_Count_Matches_Destructor_Count_After_Destructor)
    {
        Item::ctor_count = 0;
        Item::dtor_count = 0;

        unsigned int capacity;
        {
            FreeList<Item> items;
            int id = items.Alloc();
            CHECK(id != FreeList<Item>::kNullLinkId);

            capacity = items.Capacity();
            CHECK(capacity >= 1);
        }
        CHECK_EQUAL(capacity, Item::ctor_count);
        CHECK_EQUAL(capacity, Item::dtor_count);
    }

    TEST(Constructor_Count_Matches_Destructor_Count_After_Clear)
    {
        Item::ctor_count = 0;
        Item::dtor_count = 0;

        FreeList<Item> items;
        int id = items.Alloc();
        CHECK(id != FreeList<Item>::kNullLinkId);

        unsigned int capacity = items.Capacity();
        CHECK(capacity >= 1);

        items.Clear();

        CHECK_EQUAL(capacity, Item::ctor_count);
        CHECK_EQUAL(capacity, Item::dtor_count);
    }
}
#endif // ENABLE_UNIT_TESTS
