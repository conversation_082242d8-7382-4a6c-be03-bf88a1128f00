
#include "StaticSkyboxController.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "AssetPipeline/AssetManager.h"
#include "WorldManager.h"
#include "Components/SkyBoxRenderer.h"
#include "Serialize/TransferUtilities.h"
#include "Core/display/SandboxRenderSetting.h"
#include "Graphics/LegacyGlobalShaderParam.h"
#include "Misc/FrameTimeManager.h"
#include "Core/GameObject.h"

namespace Rainbow
{
	StaticSkyboxController::StaticSkyboxController()
		: m_Material(nullptr)
		, m_Renderer(nullptr)
	{

	}
	StaticSkyboxController::~StaticSkyboxController()
	{

	}
	void StaticSkyboxController::Init(Rainbow::GameObject* gameObject)
	{
		if (m_Renderer != nullptr)
			return;
		m_Material = MoveToSharePtr(GetMaterialManager().LoadFromFile("Materials/Skybox.templatemat")->CreateInstance());
		m_Renderer = gameObject->CreateComponent<SkyBoxRenderer>();
		m_Renderer->SetSharedMaterial(m_Material);
		if (m_Material->GetMaterial())
		{
			m_Material->GetMaterial()->SetAffectedByFog(0);
		}
	}

	void StaticSkyboxController::SetEnable(bool value)
	{
		if (m_Renderer != nullptr)
		{
			bool enable = m_Renderer->IsEnable();
			if (enable != value)
			{
				m_Renderer->SetEnable(value);
			}
		}
	}

	void StaticSkyboxController::SetTexture(Rainbow::Texture* texture)
	{
		m_Material->SetTexture("gSkyBoxTex", texture);
	}

	Texture* StaticSkyboxController::GetTexture()
	{
		if (m_Material) 
		{
			return m_Material->GetTexture("gSkyBoxTex");
		}
		return nullptr;
	}

	void StaticSkyboxController::OnBeginFadeTo(SharePtr<Rainbow::Texture> targetTex)
	{
		m_Material->SetTexture("gTargetSkyBoxTex", targetTex.Get());
		m_Material->SetFloat("gLerpTargetTime", 0.0f);
		m_Material->EnableKeyword("LERP_TARGETSKYBOX");
		m_FadeTargetTex = targetTex;
	}
	void StaticSkyboxController::OnUpdateFadeTo(float percentage)
	{
		m_Material->SetFloat("gLerpTargetTime", percentage);
	}
	void StaticSkyboxController::OnEndFadeTo()
	{
		m_Material->EnableKeyword("NONE");
		m_Material->SetTexture("gSkyBoxTex", m_FadeTargetTex.Get());
		m_Material->SetTexture("gTargetSkyBoxTex", nullptr);
	}
}

