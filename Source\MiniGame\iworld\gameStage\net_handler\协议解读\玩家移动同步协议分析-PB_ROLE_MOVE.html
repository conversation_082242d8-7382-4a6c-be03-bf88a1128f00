<!DOCTYPE html><html><head>
      <title>玩家移动同步协议分析-PB_ROLE_MOVE</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\katex\katex.min.css">
      
      
      <script type="text/javascript" src="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.18\crossnote\dependencies\mermaid\mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="沙盒游戏玩家移动同步协议深度分析">沙盒游戏玩家移动同步协议深度分析 </h1>
<h2 id="概述">概述 </h2>
<p>本文档深入分析沙盒创造类游戏中玩家移动同步的完整流程，包括 PB_ROLE_MOVE_CH 以及新版移动同步协议 PB_SYNC_MOVE_CH、PB_SYNC_MOVE_HC 的工作机制。</p>
<p><strong>重要说明</strong>: 经过代码验证，<strong>PB_ROLE_MOVE_HC 协议并不存在</strong>。服务器向客户端广播其他玩家移动信息使用的是 PB_ACTOR_MOVEV2_HC 等协议。</p>
<h2 id="移动同步完整时序图">移动同步完整时序图 </h2>
<div class="mermaid">sequenceDiagram
    participant C1 as 客户端A
    participant S as 服务器
    participant C2 as 客户端B
    participant C3 as 客户端C

    Note over C1,C3: 玩家A移动同步流程

    C1-&gt;&gt;S: PB_ROLE_MOVE_CH (玩家A移动)
    Note over S: handleRoleMove2Host()
    S-&gt;&gt;S: 验证玩家状态
    S-&gt;&gt;S: 解析移动数据
    S-&gt;&gt;S: 检查位置合法性

    alt 位置合法
        S-&gt;&gt;S: 更新玩家A位置
        S-&gt;&gt;C2: PB_ACTOR_MOVEV2_HC (广播给B)
        S-&gt;&gt;C3: PB_ACTOR_MOVEV2_HC (广播给C)
        Note over C2,C3: 其他客户端更新玩家A位置
    else 位置不合法
        S-&gt;&gt;C1: PB_SYNC_MOVE_HC (强制校正)
        Note over C1: handleSyncMove2Client()
        C1-&gt;&gt;C1: 强制设置位置
        C1-&gt;&gt;S: PB_SYNC_PLAYER_POS_CH (确认)
    end

    Note over C1,C3: 新版移动同步流程

    C1-&gt;&gt;S: PB_SYNC_MOVE_CH (新版移动)
    Note over S: handleSyncMove2Host()
    S-&gt;&gt;S: 验证时间戳
    S-&gt;&gt;S: 处理移动标志
    S-&gt;&gt;S: 检查移动合法性

    alt 移动合法
        S-&gt;&gt;S: 接受客户端位置
        Note over S: CRT_Accept
    else 移动不合法
        S-&gt;&gt;C1: PB_SYNC_MOVE_HC (回滚)
        Note over S: CRT_RollBack
        C1-&gt;&gt;S: PB_SYNC_PLAYER_POS_CH (确认)
    end

    Note over C1,C3: 批量移动广播优化

    S-&gt;&gt;S: 缓存多个玩家移动
    S-&gt;&gt;S: tickActorMoveSync()
    S-&gt;&gt;C2: PB_ACTOR_MOVEV3_HC (批量广播)
    S-&gt;&gt;C3: PB_ACTOR_MOVEV3_HC (批量广播)
</div><h2 id="协议体系架构">协议体系架构 </h2>
<h3 id="1-旧版移动协议">1. 旧版移动协议 </h3>
<ul>
<li><strong>PB_ROLE_MOVE_CH</strong>: 客户端向服务器发送移动信息</li>
<li><strong>PB_ROLE_MOVE_HC</strong>: 服务器向客户端广播其他玩家移动信息</li>
</ul>
<h3 id="2-新版移动同步协议">2. 新版移动同步协议 </h3>
<ul>
<li><strong>PB_SYNC_MOVE_CH</strong>: 客户端向服务器发送新版移动同步</li>
<li><strong>PB_SYNC_MOVE_HC</strong>: 服务器向客户端发送位置校正</li>
</ul>
<h3 id="3-actor-移动广播协议">3. Actor 移动广播协议 </h3>
<ul>
<li><strong>PB_ACTOR_MOVE_HC</strong>: 广播其他玩家移动信息</li>
<li><strong>PB_ACTOR_MOVEV2_HC</strong>: 优化版本的移动广播</li>
<li><strong>PB_ACTOR_MOVEV3_HC</strong>: 批量移动广播</li>
</ul>
<h2 id="协议定义">协议定义 </h2>
<h3 id="pb_rolemovech-客户端--服务器">PB_RoleMoveCH (客户端 → 服务器) </h3>
<p><strong>文件位置</strong>: <code>Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:43-50</code></p>
<pre data-role="codeBlock" data-info="protobuf" class="language-protobuf protobuf"><code><span class="token keyword keyword-message">message</span> <span class="token class-name">PB_RoleMoveCH</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_MoveMotion</span> MoveMotion <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3</span> AddMotion <span class="token operator">=</span> <span class="token number">2</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> rentToken <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">float</span> speed <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3</span> VehiclePos <span class="token operator">=</span> <span class="token number">5</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="pb_movesynchc-服务器--客户端">PB_MoveSyncHC (服务器 → 客户端) </h3>
<p><strong>文件位置</strong>: <code>Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:2032-2038</code></p>
<pre data-role="codeBlock" data-info="protobuf" class="language-protobuf protobuf"><code><span class="token keyword keyword-message">message</span> <span class="token class-name">PB_MoveSyncHC</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-required">required</span> <span class="token builtin">uint32</span> id <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>                    <span class="token comment">// 移动检查ID</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">bool</span> accept <span class="token operator">=</span> <span class="token number">2</span><span class="token punctuation">;</span>                  <span class="token comment">// 是否接受客机位置</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3</span> pos <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>   <span class="token comment">// 位置信息</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token positional-class-name class-name">game<span class="token punctuation">.</span>common<span class="token punctuation">.</span>PB_Vector3f</span> motion <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span> <span class="token comment">// 动量信息</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="pb_actormovev2hc-服务器--客户端广播">PB_ActorMoveV2HC (服务器 → 客户端广播) </h3>
<p><strong>文件位置</strong>: <code>Source\MiniBase\Protocol\Tools\protobuf\proto_hc.proto:141-147</code></p>
<pre data-role="codeBlock" data-info="protobuf" class="language-protobuf protobuf"><code><span class="token keyword keyword-message">message</span> <span class="token class-name">PB_ActorMoveV2HC</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">uint64</span> ObjID <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-repeated">repeated</span> <span class="token builtin">sint32</span> Position <span class="token operator">=</span> <span class="token number">2</span> <span class="token punctuation">[</span><span class="token annotation">packed</span> <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">uint32</span> Yaw_Pitch <span class="token operator">=</span> <span class="token number">3</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-optional">optional</span> <span class="token builtin">int32</span> ChangeFlags <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="1-客户端发送移动协议的情况">1. 客户端发送移动协议的情况 </h2>
<h3 id="11-旧版协议发送条件">1.1 旧版协议发送条件 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:215-235</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpPlayerControl</span><span class="token double-colon punctuation">::</span><span class="token function">sendMoveToHost</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    m_nMoveTick<span class="token operator">++</span><span class="token punctuation">;</span>
    WCoord pos <span class="token operator">=</span> <span class="token function">CoordDivBlock</span><span class="token punctuation">(</span><span class="token function">getPosition</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> mustSync <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-auto">auto</span> functionWrapper <span class="token operator">=</span> <span class="token function">getFuncWrapper</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>functionWrapper<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        mustSync <span class="token operator">=</span> functionWrapper<span class="token operator">-&gt;</span><span class="token function">getMustSyncPos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_pWorld<span class="token operator">-&gt;</span><span class="token function">getChunk</span><span class="token punctuation">(</span>pos<span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token constant">NULL</span> <span class="token operator">&amp;&amp;</span> <span class="token operator">!</span>mustSync<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token comment">// 避免长时间不发送移动协议</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_nMoveTick <span class="token operator">&gt;</span> <span class="token number">100</span><span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            m_nMoveTick <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
            PB_RoleMoveCH roleMoveCH<span class="token punctuation">;</span>
            PB_MoveMotion<span class="token operator">*</span> moveMotion <span class="token operator">=</span> roleMoveCH<span class="token punctuation">.</span><span class="token function">mutable_movemotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            moveMotion<span class="token operator">-&gt;</span><span class="token function">set_changeflags</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token class-name">GameNetManager</span><span class="token double-colon punctuation">::</span><span class="token function">getInstance</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">sendToHost</span><span class="token punctuation">(</span>PB_ROLE_MOVE_CH<span class="token punctuation">,</span> roleMoveCH<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token comment">// ... 继续处理正常移动同步</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>发送触发条件</strong>:</p>
<ol>
<li><strong>心跳保活</strong>: 当玩家在未加载区块时，每 100 帧发送一次心跳协议</li>
<li><strong>位置变化</strong>: 玩家位置发生变化时</li>
<li><strong>朝向变化</strong>: 玩家视角朝向发生变化时</li>
<li><strong>强制同步</strong>: 特殊功能要求强制同步位置时</li>
</ol>
<h3 id="12-新版协议发送条件">1.2 新版协议发送条件 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:574-605</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpPlayerControl</span><span class="token double-colon punctuation">::</span><span class="token function">sendNewMoveToHost</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    <span class="token comment">// ... 省略前置检查代码</span>

    <span class="token keyword keyword-bool">bool</span> not_sync_pos <span class="token operator">=</span> <span class="token boolean">false</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-auto">auto</span> sit <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetComponent</span><span class="token generic class-name"><span class="token operator">&lt;</span>ServerInterpolTick<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token punctuation">(</span>sit <span class="token operator">&amp;&amp;</span> sit<span class="token operator">-&gt;</span><span class="token function">isMoving</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token operator">||</span> <span class="token punctuation">(</span><span class="token function">getRiddenComponent</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> <span class="token function">getRiddenComponent</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">isRiding</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
        not_sync_pos <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>not_sync_pos<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> pos <span class="token operator">=</span> m_MoveControl<span class="token operator">-&gt;</span><span class="token function">getPrePosition</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>need_sync <span class="token operator">||</span> <span class="token punctuation">(</span>tick_match <span class="token operator">&amp;&amp;</span> m_MoveControl<span class="token operator">-&gt;</span><span class="token function">positionChanged</span><span class="token punctuation">(</span>pos<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            <span class="token keyword keyword-auto">auto</span> pb_pos <span class="token operator">=</span> pbMoveSync<span class="token punctuation">.</span><span class="token function">mutable_pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            pb_pos<span class="token operator">-&gt;</span><span class="token function">set_x</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>x<span class="token punctuation">)</span><span class="token punctuation">;</span>
            pb_pos<span class="token operator">-&gt;</span><span class="token function">set_y</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>y<span class="token punctuation">)</span><span class="token punctuation">;</span>
            pb_pos<span class="token operator">-&gt;</span><span class="token function">set_z</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>

            need_sync <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
            bits <span class="token operator">|=</span> <span class="token number">2</span><span class="token punctuation">;</span>
            m_MoveControl<span class="token operator">-&gt;</span><span class="token function">setLastPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>need_sync<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        pbMoveSync<span class="token punctuation">.</span><span class="token function">set_id</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        pbMoveSync<span class="token punctuation">.</span><span class="token function">set_tick</span><span class="token punctuation">(</span>tick<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">GetGameNetManagerPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">sendToHost</span><span class="token punctuation">(</span>PB_SYNC_MOVE_CH<span class="token punctuation">,</span> pbMoveSync<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>新版协议优势</strong>:</p>
<ul>
<li>包含时间戳验证，防止作弊</li>
<li>支持移动标志变化</li>
<li>更精确的位置同步</li>
</ul>
<h2 id="2-服务器处理移动协议">2. 服务器处理移动协议 </h2>
<h3 id="21-旧版协议处理">2.1 旧版协议处理 </h3>
<p><strong>代码位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1083-1105</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleRoleMove2Host</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> uin<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> PB_PACKDATA <span class="token operator">&amp;</span>pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    ClientPlayer <span class="token operator">*</span>player <span class="token operator">=</span> <span class="token function">uin2Player</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player <span class="token operator">==</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token function">sendError2Client</span><span class="token punctuation">(</span>uin<span class="token punctuation">,</span> PB_ERROR_OP_NOT_FOUND<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>player<span class="token operator">-&gt;</span><span class="token function">isSimPlayer</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> <span class="token class-name">AntiSetting</span><span class="token double-colon punctuation">::</span><span class="token function">forceUseNewSync</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token class-name">ActionLogger</span><span class="token double-colon punctuation">::</span><span class="token function">SimpleErrLog</span><span class="token punctuation">(</span>uin<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token string">"cheat_use_old_move"</span><span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    PB_RoleMoveCH roleMoveCH<span class="token punctuation">;</span>
    roleMoveCH<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-const">const</span> PB_MoveMotion <span class="token operator">&amp;</span>motion <span class="token operator">=</span> roleMoveCH<span class="token punctuation">.</span><span class="token function">movemotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> posChange <span class="token operator">=</span> motion<span class="token punctuation">.</span><span class="token function">changeflags</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">1</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> yawChange <span class="token operator">=</span> motion<span class="token punctuation">.</span><span class="token function">changeflags</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">2</span><span class="token punctuation">;</span>
    <span class="token comment">// 仅作为移动上报心跳的协议 不实际作用</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>posChange <span class="token operator">&amp;&amp;</span> <span class="token operator">!</span>yawChange<span class="token punctuation">)</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    <span class="token comment">// ... 继续处理位置验证和同步</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>处理步骤</strong>:</p>
<ol>
<li><strong>玩家验证</strong>: 检查玩家是否存在</li>
<li><strong>反作弊检查</strong>: 强制使用新版同步的玩家不能使用旧协议</li>
<li><strong>协议解析</strong>: 解析移动数据</li>
<li><strong>变化检测</strong>: 检查位置或朝向是否变化</li>
<li><strong>心跳过滤</strong>: 纯心跳协议直接返回</li>
</ol>
<h3 id="22-新版协议处理">2.2 新版协议处理 </h3>
<p><strong>代码位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:7439-7472</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleSyncMove2Host</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> uin<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> PB_PACKDATA<span class="token operator">&amp;</span> pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    ClientPlayer<span class="token operator">*</span> player <span class="token operator">=</span> <span class="token function">uin2Player</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    game<span class="token double-colon punctuation">::</span>ch<span class="token double-colon punctuation">::</span>PB_MoveSyncCH pbCH<span class="token punctuation">;</span>
    pbCH<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-unsigned">unsigned</span> id <span class="token operator">=</span> pbCH<span class="token punctuation">.</span><span class="token function">id</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 处理移动标志变化</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> i <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span> i <span class="token operator">&lt;</span> pbCH<span class="token punctuation">.</span><span class="token function">flag_change_size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token operator">++</span>i<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-auto">auto</span> <span class="token operator">&amp;</span>flag_change <span class="token operator">=</span> pbCH<span class="token punctuation">.</span><span class="token function">flag_change</span><span class="token punctuation">(</span>i<span class="token punctuation">)</span><span class="token punctuation">;</span>
        player<span class="token operator">-&gt;</span><span class="token function">changeMoveFlag</span><span class="token punctuation">(</span>flag_change<span class="token punctuation">.</span><span class="token function">type</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> flag_change<span class="token punctuation">.</span><span class="token function">on</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-unsigned">unsigned</span> <span class="token keyword keyword-long">long</span> <span class="token keyword keyword-long">long</span> tick <span class="token operator">=</span> pbCH<span class="token punctuation">.</span><span class="token function">tick</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-bool">bool</span> tick_valid <span class="token operator">=</span> player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">checkClientTick</span><span class="token punctuation">(</span>tick<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">has_pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>tick_valid<span class="token punctuation">)</span>
            player<span class="token operator">-&gt;</span><span class="token function">setCheckMoveResult</span><span class="token punctuation">(</span>id<span class="token punctuation">,</span> <span class="token function">MPVEC2WCoord</span><span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">,</span> tick<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-else">else</span>
            player<span class="token operator">-&gt;</span><span class="token function">setCheckMoveResult</span><span class="token punctuation">(</span>id<span class="token punctuation">,</span> <span class="token function">MPVEC2WCoord</span><span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">has_move_opera</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-auto">auto</span> move_info <span class="token operator">=</span> pbCH<span class="token punctuation">.</span><span class="token function">move_opera</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        player<span class="token operator">-&gt;</span><span class="token function">setMoveControl</span><span class="token punctuation">(</span>move_info<span class="token punctuation">.</span><span class="token function">opera</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> move_info<span class="token punctuation">.</span><span class="token function">yaw</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">1000.0f</span><span class="token punctuation">,</span> move_info<span class="token punctuation">.</span><span class="token function">pitch</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">/</span> <span class="token number">1000.0f</span><span class="token punctuation">,</span> tick_valid <span class="token operator">?</span> tick<span class="token operator">:</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>新版处理特点</strong>:</p>
<ul>
<li><strong>时间戳验证</strong>: 通过 CheatHandler 验证客户端时间戳</li>
<li><strong>移动标志</strong>: 处理跳跃、下蹲等移动状态变化</li>
<li><strong>精确控制</strong>: 支持操作类型和视角信息</li>
</ul>
<h3 id="23-本机客户端与第三方客户端处理差异">2.3 本机客户端与第三方客户端处理差异 </h3>
<p><strong>关键差异点</strong>:</p>
<ol>
<li><strong>本机客户端</strong>: 服务器直接处理移动逻辑，不需要额外验证</li>
<li><strong>第三方客户端</strong>: 需要进行反作弊检查、位置合法性验证</li>
</ol>
<p><strong>代码体现</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1091-1095</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>player<span class="token operator">-&gt;</span><span class="token function">isSimPlayer</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> <span class="token class-name">AntiSetting</span><span class="token double-colon punctuation">::</span><span class="token function">forceUseNewSync</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    <span class="token class-name">ActionLogger</span><span class="token double-colon punctuation">::</span><span class="token function">SimpleErrLog</span><span class="token punctuation">(</span>uin<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token string">"cheat_use_old_move"</span><span class="token punctuation">,</span> <span class="token string">""</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="3-服务器响应协议">3. 服务器响应协议 </h2>
<h3 id="31-位置校正协议-pb_sync_move_hc">3.1 位置校正协议 (PB_SYNC_MOVE_HC) </h3>
<p><strong>发送位置</strong>: <code>Source\SandboxGame\Play\player\ClientPlayer_Base.cpp</code> 中的 <code>checkMoveResult()</code> 方法</p>
<p><strong>发送情况</strong>:</p>
<h4 id="a-回滚情况-crt_rollback">A. 回滚情况 (CRT_RollBack) </h4>
<p>当客户端移动不合法时，服务器强制回滚：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>game<span class="token double-colon punctuation">::</span>hc<span class="token double-colon punctuation">::</span>PB_MoveSyncHC pbMoveSync<span class="token punctuation">;</span>
pbMoveSync<span class="token punctuation">.</span><span class="token function">set_id</span><span class="token punctuation">(</span>m_MoveControl<span class="token operator">-&gt;</span><span class="token function">getCheckID</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
pbMoveSync<span class="token punctuation">.</span><span class="token function">set_accept</span><span class="token punctuation">(</span><span class="token boolean">false</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 拒绝客机位置</span>

<span class="token comment">// 设置服务器当前位置</span>
<span class="token keyword keyword-auto">auto</span> pb_pos <span class="token operator">=</span> pbMoveSync<span class="token punctuation">.</span><span class="token function">mutable_pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> <span class="token operator">&amp;</span>pos <span class="token operator">=</span> player_loco<span class="token operator">-&gt;</span><span class="token function">getPosition</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_pos<span class="token operator">-&gt;</span><span class="token function">set_x</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>x<span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_pos<span class="token operator">-&gt;</span><span class="token function">set_y</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>y<span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_pos<span class="token operator">-&gt;</span><span class="token function">set_z</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 设置服务器当前动量</span>
<span class="token keyword keyword-auto">auto</span> pb_motion <span class="token operator">=</span> pbMoveSync<span class="token punctuation">.</span><span class="token function">mutable_motion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> <span class="token operator">&amp;</span>motion <span class="token operator">=</span> player_loco<span class="token operator">-&gt;</span><span class="token function">getMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_motion<span class="token operator">-&gt;</span><span class="token function">set_x</span><span class="token punctuation">(</span>motion<span class="token punctuation">.</span>x<span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_motion<span class="token operator">-&gt;</span><span class="token function">set_y</span><span class="token punctuation">(</span>motion<span class="token punctuation">.</span>y<span class="token punctuation">)</span><span class="token punctuation">;</span>
pb_motion<span class="token operator">-&gt;</span><span class="token function">set_z</span><span class="token punctuation">(</span>motion<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token function">GetGameNetManagerPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">sendToClient</span><span class="token punctuation">(</span><span class="token function">getUin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> PB_SYNC_MOVE_HC<span class="token punctuation">,</span> pbMoveSync<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h4 id="b-接受情况-crt_accept">B. 接受情况 (CRT_Accept) </h4>
<p>当客户端移动合法时，服务器接受位置：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token function">IsOnPlatform</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>  <span class="token comment">// 不在运动平台上才更新位置</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> pos <span class="token operator">=</span> m_MoveControl<span class="token operator">-&gt;</span><span class="token function">getPrePosition</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>pos<span class="token punctuation">.</span><span class="token function">isZero</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        player_loco<span class="token operator">-&gt;</span><span class="token function">setPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>x<span class="token punctuation">,</span> pos<span class="token punctuation">.</span>y<span class="token punctuation">,</span> pos<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>
        player_loco<span class="token operator">-&gt;</span><span class="token function">setMotion</span><span class="token punctuation">(</span>motion<span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token comment">// 地面检测</span>
        WCoord dest <span class="token operator">=</span> m_pWorld<span class="token operator">-&gt;</span><span class="token function">moveBox</span><span class="token punctuation">(</span>box<span class="token punctuation">,</span> <span class="token function">WCoord</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">,</span> <span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>dest<span class="token punctuation">.</span>y <span class="token operator">==</span> <span class="token number">0</span><span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            player_loco<span class="token operator">-&gt;</span><span class="token function">setOnGround</span><span class="token punctuation">(</span><span class="token boolean">true</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            player_loco<span class="token operator">-&gt;</span>m_JumpingTicks <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><h3 id="32-广播其他玩家移动信息">3.2 广播其他玩家移动信息 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:1269-1337</code></p>
<p>服务器使用 <code>PB_ACTOR_MOVEV2_HC</code> 协议广播其他玩家的移动信息：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>PB_ActorMoveV2HC actorMoveHC<span class="token punctuation">;</span>
<span class="token keyword keyword-uint64_t">uint64_t</span> objid <span class="token operator">=</span> mEntryActor<span class="token operator">-&gt;</span><span class="token function">getObjId</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
objid <span class="token operator">=</span> <span class="token function">GetISandboxActorSubsystem</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">PackObjId</span><span class="token punctuation">(</span>objid<span class="token punctuation">)</span><span class="token punctuation">;</span>
actorMoveHC<span class="token punctuation">.</span><span class="token function">set_objid</span><span class="token punctuation">(</span>objid<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 设置朝向信息</span>
<span class="token keyword keyword-uint32_t">uint32_t</span> yaw_pitch <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
yaw_pitch <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token punctuation">(</span>curyaw<span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xff</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token number">8</span><span class="token punctuation">;</span>
yaw_pitch <span class="token operator">|=</span> <span class="token punctuation">(</span>curpitch <span class="token operator">&amp;</span> <span class="token number">0xff</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
actorMoveHC<span class="token punctuation">.</span><span class="token function">set_yaw_pitch</span><span class="token punctuation">(</span>yaw_pitch<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 设置位置信息</span>
actorMoveHC<span class="token punctuation">.</span><span class="token function">add_position</span><span class="token punctuation">(</span>curpos<span class="token punctuation">.</span>x<span class="token punctuation">)</span><span class="token punctuation">;</span>
actorMoveHC<span class="token punctuation">.</span><span class="token function">add_position</span><span class="token punctuation">(</span>curpos<span class="token punctuation">.</span>y<span class="token punctuation">)</span><span class="token punctuation">;</span>
actorMoveHC<span class="token punctuation">.</span><span class="token function">add_position</span><span class="token punctuation">(</span>curpos<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 广播给追踪该玩家的所有客户端</span>
<span class="token keyword keyword-auto">auto</span> iter <span class="token operator">=</span> mTrackingPlayersTable<span class="token punctuation">.</span><span class="token function">iterate</span><span class="token punctuation">(</span><span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token punctuation">;</span> iter <span class="token operator">!=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span> iter <span class="token operator">=</span> mTrackingPlayersTable<span class="token punctuation">.</span><span class="token function">iterate</span><span class="token punctuation">(</span>iter<span class="token punctuation">)</span><span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    IClientPlayer<span class="token operator">*</span> pp <span class="token operator">=</span> iter<span class="token operator">-&gt;</span>key<span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pp <span class="token operator">!=</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token function">GetGameNetManagerPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">cacheActorMove</span><span class="token punctuation">(</span>pp<span class="token operator">-&gt;</span><span class="token function">getUin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> objid<span class="token punctuation">,</span> actorMoveHC<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>广播触发条件</strong>:</p>
<ol>
<li><strong>位置变化</strong>: 位置变化超过阈值 (DPSQ)</li>
<li><strong>朝向变化</strong>: 朝向变化超过阈值 (ASQ)</li>
<li><strong>强制同步</strong>: 特殊情况需要强制同步</li>
</ol>
<h2 id="4-客户端处理响应协议">4. 客户端处理响应协议 </h2>
<h3 id="41-处理位置校正-pb_sync_move_hc">4.1 处理位置校正 (PB_SYNC_MOVE_HC) </h3>
<p><strong>代码位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:8608-8627</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleSyncMove2Client</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> PB_PACKDATA_CLIENT<span class="token operator">&amp;</span> pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    PlayerControl<span class="token operator">*</span> playerCtrl <span class="token operator">=</span> m_root<span class="token operator">-&gt;</span><span class="token function">getPlayerControl</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>playerCtrl <span class="token operator">==</span> <span class="token constant">NULL</span> <span class="token operator">||</span> playerCtrl<span class="token operator">-&gt;</span><span class="token function">getWorld</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    game<span class="token double-colon punctuation">::</span>hc<span class="token double-colon punctuation">::</span>PB_MoveSyncHC pbHC<span class="token punctuation">;</span>
    pbHC<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pbHC<span class="token punctuation">.</span><span class="token function">has_pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        WCoord pos <span class="token operator">=</span> <span class="token function">MPVEC2WCoord</span><span class="token punctuation">(</span>pbHC<span class="token punctuation">.</span><span class="token function">pos</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>playerCtrl<span class="token operator">-&gt;</span><span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
            playerCtrl<span class="token operator">-&gt;</span><span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">setPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">.</span>x<span class="token punctuation">,</span> pos<span class="token punctuation">.</span>y<span class="token punctuation">,</span> pos<span class="token punctuation">.</span>z<span class="token punctuation">)</span><span class="token punctuation">;</span>
        playerCtrl<span class="token operator">-&gt;</span><span class="token function">setLastSyncPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">)</span><span class="token punctuation">;</span>
        playerCtrl<span class="token operator">-&gt;</span><span class="token function">updateClientMoveSyncInterval</span><span class="token punctuation">(</span><span class="token boolean">true</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

        <span class="token comment">// 发送位置确认响应</span>
        PB_ResetPosResponeCH msg<span class="token punctuation">;</span>
        msg<span class="token punctuation">.</span><span class="token function">set_tick</span><span class="token punctuation">(</span>m_root<span class="token operator">?</span> m_root<span class="token operator">-&gt;</span><span class="token function">getGameTick</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">:</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">GetGameNetManagerPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">sendToHost</span><span class="token punctuation">(</span>PB_SYNC_PLAYER_POS_CH<span class="token punctuation">,</span> msg<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>pbHC<span class="token punctuation">.</span><span class="token function">has_motion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token keyword keyword-auto">auto</span> <span class="token operator">&amp;</span>motion <span class="token operator">=</span> pbHC<span class="token punctuation">.</span><span class="token function">motion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        playerCtrl<span class="token operator">-&gt;</span><span class="token function">setMotionChange</span><span class="token punctuation">(</span><span class="token class-name">Rainbow</span><span class="token double-colon punctuation">::</span><span class="token function">Vector3f</span><span class="token punctuation">(</span>motion<span class="token punctuation">.</span><span class="token function">x</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> motion<span class="token punctuation">.</span><span class="token function">y</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> motion<span class="token punctuation">.</span><span class="token function">z</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>处理步骤</strong>:</p>
<ol>
<li><strong>强制设置位置</strong>: 直接设置玩家位置到服务器指定位置</li>
<li><strong>记录同步位置</strong>: 更新最后同步位置记录</li>
<li><strong>调整同步间隔</strong>: 更新客户端移动同步频率</li>
<li><strong>发送确认</strong>: 向服务器确认位置接收</li>
</ol>
<h3 id="42-处理其他玩家移动-pb_actor_movev2_hc">4.2 处理其他玩家移动 (PB_ACTOR_MOVEV2_HC) </h3>
<p><strong>代码位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:1113-1173</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleActorMoveV22Client</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> PB_PACKDATA_CLIENT<span class="token operator">&amp;</span> pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    PB_ActorMoveV2HC actorMoveHC<span class="token punctuation">;</span>
    actorMoveHC<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-long">long</span> <span class="token keyword keyword-long">long</span> ObjId <span class="token operator">=</span> actorMoveHC<span class="token punctuation">.</span><span class="token function">objid</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    ObjId <span class="token operator">=</span> <span class="token class-name">ClientActor</span><span class="token double-colon punctuation">::</span><span class="token function">UnpackObjId</span><span class="token punctuation">(</span>ObjId<span class="token punctuation">)</span><span class="token punctuation">;</span>
    ClientActor<span class="token operator">*</span> actor <span class="token operator">=</span> <span class="token function">objId2ActorOnClient</span><span class="token punctuation">(</span>ObjId<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>actor <span class="token operator">!=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token comment">// 解析位置信息</span>
        <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>actorMoveHC<span class="token punctuation">.</span><span class="token function">position_size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&gt;=</span> <span class="token number">3</span><span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            WCoord pos<span class="token punctuation">;</span>
            pos<span class="token punctuation">.</span>x <span class="token operator">=</span> actorMoveHC<span class="token punctuation">.</span><span class="token function">position</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            pos<span class="token punctuation">.</span>y <span class="token operator">=</span> actorMoveHC<span class="token punctuation">.</span><span class="token function">position</span><span class="token punctuation">(</span><span class="token number">1</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
            pos<span class="token punctuation">.</span>z <span class="token operator">=</span> actorMoveHC<span class="token punctuation">.</span><span class="token function">position</span><span class="token punctuation">(</span><span class="token number">2</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

            <span class="token comment">// 解析朝向信息</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>actorMoveHC<span class="token punctuation">.</span><span class="token function">has_yaw_pitch</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
            <span class="token punctuation">{</span>
                <span class="token keyword keyword-uint32_t">uint32_t</span> yaw_pitch <span class="token operator">=</span> actorMoveHC<span class="token punctuation">.</span><span class="token function">yaw_pitch</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword keyword-float">float</span> yaw <span class="token operator">=</span> <span class="token function">AngleChar2Float</span><span class="token punctuation">(</span><span class="token punctuation">(</span>yaw_pitch <span class="token operator">&gt;&gt;</span> <span class="token number">8</span><span class="token punctuation">)</span> <span class="token operator">&amp;</span> <span class="token number">0xff</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token keyword keyword-float">float</span> pitch <span class="token operator">=</span> <span class="token function">AngleChar2Float</span><span class="token punctuation">(</span>yaw_pitch <span class="token operator">&amp;</span> <span class="token number">0xff</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

                Rainbow<span class="token double-colon punctuation">::</span>Quaternionf rot <span class="token operator">=</span> <span class="token function">EulerAngleToQuaternion</span><span class="token punctuation">(</span><span class="token class-name">Rainbow</span><span class="token double-colon punctuation">::</span><span class="token function">Vector3f</span><span class="token punctuation">(</span>pitch<span class="token punctuation">,</span> yaw<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                actor<span class="token operator">-&gt;</span><span class="token function">moveToPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">,</span> rot<span class="token punctuation">,</span> INTERPOL_TICKS<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>本机客户端与第三方客户端处理差异</strong>:</p>
<ul>
<li><strong>本机客户端</strong>: 不处理自己的移动广播，避免位置冲突</li>
<li><strong>第三方客户端</strong>: 接收并应用其他玩家的移动信息，进行插值平滑</li>
</ul>
<h2 id="5-协议注册机制">5. 协议注册机制 </h2>
<p><strong>注册位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 服务器端处理器注册</span>
<span class="token function">REGIS_HOST_HANDLER</span><span class="token punctuation">(</span>PB_ROLE_MOVE_CH<span class="token punctuation">,</span> handleRoleMove2Host<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">REGIS_HOST_HANDLER</span><span class="token punctuation">(</span>PB_SYNC_MOVE_CH<span class="token punctuation">,</span> handleSyncMove2Host<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 客户端处理器注册</span>
<span class="token function">REGIS_CLIENT_HANDLER</span><span class="token punctuation">(</span>PB_SYNC_MOVE_HC<span class="token punctuation">,</span> handleSyncMove2Client<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">REGIS_CLIENT_HANDLER</span><span class="token punctuation">(</span>PB_ACTOR_MOVE_HC<span class="token punctuation">,</span> handleActorMove2Client<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">REGIS_CLIENT_HANDLER</span><span class="token punctuation">(</span>PB_ACTOR_MOVEV2_HC<span class="token punctuation">,</span> handleActorMoveV22Client<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">REGIS_CLIENT_HANDLER</span><span class="token punctuation">(</span>PB_ACTOR_MOVEV3_HC<span class="token punctuation">,</span> handleActorMoveV32Client<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h2 id="6-反作弊机制">6. 反作弊机制 </h2>
<h3 id="61-时间戳验证">6.1 时间戳验证 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxGame\Play\player\PlayerCheat.cpp</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-bool">bool</span> tick_valid <span class="token operator">=</span> player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">checkClientTick</span><span class="token punctuation">(</span>tick<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h3 id="62-移动协议监控">6.2 移动协议监控 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:2178-2203</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">GameNetManager</span><span class="token double-colon punctuation">::</span><span class="token function">onRecvClientMsg</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> uin<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> msgCode<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span><span class="token function">GetAntiCheatMgrInterface</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">CheckMove</span><span class="token punctuation">(</span>msgCode<span class="token punctuation">)</span><span class="token punctuation">)</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-unsigned">unsigned</span> now <span class="token operator">=</span> Rainbow<span class="token double-colon punctuation">::</span><span class="token class-name">Timer</span><span class="token double-colon punctuation">::</span><span class="token function">getSystemTick</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 客户端可能存在协议阻塞的情况 把移动的数据校正一下</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>now <span class="token operator">-</span> m_RoleLastMsgTimes<span class="token punctuation">[</span>uin<span class="token punctuation">]</span> <span class="token operator">&gt;</span> <span class="token number">5000</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        m_RoleMoveTimes<span class="token punctuation">[</span>uin<span class="token punctuation">]</span> <span class="token operator">=</span> now<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    m_RoleLastMsgTimes<span class="token punctuation">[</span>uin<span class="token punctuation">]</span> <span class="token operator">=</span> now<span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>msgCode <span class="token operator">==</span> PB_ROLE_MOVE_CH<span class="token punctuation">)</span>
        m_RoleMoveTimes<span class="token punctuation">[</span>uin<span class="token punctuation">]</span> <span class="token operator">=</span> now<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><h2 id="7-移动同步优化机制">7. 移动同步优化机制 </h2>
<h3 id="71-批量移动广播">7.1 批量移动广播 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxEngine\Play\gamenet\GameNetManager.cpp:460-486</code></p>
<p>为了优化网络性能，系统使用批量移动广播：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">GameNetManager</span><span class="token double-colon punctuation">::</span><span class="token function">tickActorMoveSync</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>m_ActorMoveTargetCache<span class="token punctuation">.</span><span class="token function">empty</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> data <span class="token operator">:</span> m_ActorMoveTargetCache<span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>shared_ptr<span class="token operator">&lt;</span>PB_ActorMoveV3HC_Batch<span class="token operator">&gt;</span> batch <span class="token operator">=</span> m_ProtoMoveV3HCPool<span class="token punctuation">.</span><span class="token function">Acquire</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-auto">auto</span> target <span class="token operator">=</span> data<span class="token punctuation">.</span>first<span class="token punctuation">;</span>
        <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> ovec <span class="token operator">=</span> data<span class="token punctuation">.</span>second<span class="token punctuation">;</span>
        <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-auto">auto</span> oid <span class="token operator">:</span> ovec<span class="token punctuation">)</span>
        <span class="token punctuation">{</span>
            <span class="token keyword keyword-auto">auto</span> it <span class="token operator">=</span> m_ActorMoveCache<span class="token punctuation">.</span><span class="token function">find</span><span class="token punctuation">(</span>oid<span class="token punctuation">)</span><span class="token punctuation">;</span>
            <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>it <span class="token operator">!=</span> m_ActorMoveCache<span class="token punctuation">.</span><span class="token function">end</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
            <span class="token punctuation">{</span>
                PB_ActorMoveV2HC<span class="token operator">&amp;</span> am <span class="token operator">=</span> it<span class="token operator">-&gt;</span>second<span class="token punctuation">;</span>
                <span class="token keyword keyword-auto">auto</span> mo <span class="token operator">=</span> batch<span class="token operator">-&gt;</span><span class="token function">add_movebatch</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                <span class="token operator">*</span>mo <span class="token operator">=</span> am<span class="token punctuation">;</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>

        <span class="token function">sendToClient</span><span class="token punctuation">(</span>target<span class="token punctuation">,</span> PB_ACTOR_MOVEV3_HC<span class="token punctuation">,</span> <span class="token operator">*</span>batch<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token boolean">true</span><span class="token punctuation">,</span> UNRELIABLE_SEQUENCED<span class="token punctuation">,</span> HIGH_PRIORITY<span class="token punctuation">,</span> <span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        m_ProtoMoveV3HCPool<span class="token punctuation">.</span><span class="token function">Release</span><span class="token punctuation">(</span>batch<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>优化特点</strong>:</p>
<ul>
<li><strong>批量处理</strong>: 将多个玩家的移动信息打包发送</li>
<li><strong>对象池</strong>: 使用对象池减少内存分配</li>
<li><strong>不可靠传输</strong>: 使用 UNRELIABLE_SEQUENCED 提高性能</li>
<li><strong>高优先级</strong>: 移动信息具有高传输优先级</li>
</ul>
<h3 id="72-移动缓存机制">7.2 移动缓存机制 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxEngine\Play\gameplay\mpgameplay\MpActorTrackerEntry.cpp:1322-1336</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token function">GetGameNetManagerPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">cacheActorMove</span><span class="token punctuation">(</span>target<span class="token operator">-&gt;</span><span class="token function">getUin</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">,</span> objid<span class="token punctuation">,</span> actorMoveHC<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p>系统将移动信息先缓存，然后批量发送，避免频繁的网络调用。</p>
<h3 id="73-插值平滑处理">7.3 插值平滑处理 </h3>
<p><strong>代码位置</strong>: <code>Source\SandboxGame\Core\actors\clientActor\ClientActor.cpp:2908-2926</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">ClientActor</span><span class="token double-colon punctuation">::</span><span class="token function">moveToPosition</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> WCoord <span class="token operator">&amp;</span>pos<span class="token punctuation">,</span> Rainbow<span class="token double-colon punctuation">::</span>Quaternionf <span class="token operator">&amp;</span>rot<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> interpol_ticks<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span> <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token function">IsObject</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> m_pWorld <span class="token operator">&amp;&amp;</span> m_pWorld<span class="token operator">-&gt;</span><span class="token function">isRemoteMode</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">{</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span>m_PosRotationIncrements <span class="token operator">=</span> interpol_ticks<span class="token punctuation">;</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span>m_ServerPos <span class="token operator">=</span> pos<span class="token punctuation">;</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span>m_ServerRot <span class="token operator">=</span> rot<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-else">else</span>
    <span class="token punctuation">{</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">setPosition</span><span class="token punctuation">(</span>pos<span class="token punctuation">)</span><span class="token punctuation">;</span>
        Rainbow<span class="token double-colon punctuation">::</span>Vector3f euler <span class="token operator">=</span> <span class="token function">QuaternionToEulerAngle</span><span class="token punctuation">(</span>rot<span class="token punctuation">)</span><span class="token punctuation">;</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span>m_RotateYaw <span class="token operator">=</span> euler<span class="token punctuation">.</span>x<span class="token punctuation">;</span>
        <span class="token function">getLocoMotion</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span>m_RotationPitch <span class="token operator">=</span> euler<span class="token punctuation">.</span>y<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre><p><strong>插值机制</strong>:</p>
<ul>
<li><strong>远程模式</strong>: 设置目标位置和旋转，进行平滑插值</li>
<li><strong>本地模式</strong>: 直接设置位置和旋转</li>
<li><strong>插值帧数</strong>: 通过 INTERPOL_TICKS 控制插值时间</li>
</ul>
<h2 id="8-网络可靠性保障">8. 网络可靠性保障 </h2>
<h3 id="81-协议可靠性级别">8.1 协议可靠性级别 </h3>
<ul>
<li><strong>PB_ROLE_MOVE_CH</strong>: RELIABLE_ORDERED (可靠有序)</li>
<li><strong>PB_SYNC_MOVE_CH</strong>: RELIABLE_ORDERED (可靠有序)</li>
<li><strong>PB_ACTOR_MOVEV2_HC</strong>: UNRELIABLE_SEQUENCED (不可靠有序)</li>
<li><strong>PB_SYNC_MOVE_HC</strong>: RELIABLE_ORDERED (可靠有序)</li>
</ul>
<h3 id="82-位置确认机制">8.2 位置确认机制 </h3>
<p><strong>代码位置</strong>: <code>Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:7474-7486</code></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-void">void</span> <span class="token class-name">MpGameSurviveNetHandler</span><span class="token double-colon punctuation">::</span><span class="token function">handleRespSyncMove2Host</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span> uin<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> PB_PACKDATA <span class="token operator">&amp;</span>pkg<span class="token punctuation">)</span>
<span class="token punctuation">{</span>
    ClientPlayer<span class="token operator">*</span> player <span class="token operator">=</span> <span class="token function">uin2Player</span><span class="token punctuation">(</span>uin<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player <span class="token operator">==</span> <span class="token constant">NULL</span><span class="token punctuation">)</span>
        <span class="token keyword keyword-return">return</span><span class="token punctuation">;</span>

    PB_ResetPosResponeCH pbCH<span class="token punctuation">;</span>
    pbCH<span class="token punctuation">.</span><span class="token function">ParseFromArray</span><span class="token punctuation">(</span>pkg<span class="token punctuation">.</span>MsgData<span class="token punctuation">,</span> pkg<span class="token punctuation">.</span>ByteSize<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&amp;&amp;</span> player<span class="token operator">-&gt;</span><span class="token function">GetCheatHandler</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">-&gt;</span><span class="token function">checkClientTick</span><span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">tick</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
        player<span class="token operator">-&gt;</span><span class="token function">onClientRspSyncPos</span><span class="token punctuation">(</span>pbCH<span class="token punctuation">.</span><span class="token function">tick</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-else">else</span>
        player<span class="token operator">-&gt;</span><span class="token function">onClientRspSyncPos</span><span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre><p>客户端收到位置校正后，必须发送确认响应，服务器验证时间戳确保同步的有效性。</p>
<h2 id="9-移动同步决策流程图">9. 移动同步决策流程图 </h2>
<div class="mermaid">flowchart TD
    A[客户端移动输入] --&gt; B{是否在未加载区块?}
    B --&gt;|是| C{移动计数&gt;100?}
    C --&gt;|是| D[发送心跳协议 PB_ROLE_MOVE_CH]
    C --&gt;|否| E[跳过发送]
    B --&gt;|否| F{使用新版同步?}

    F --&gt;|是| G[发送 PB_SYNC_MOVE_CH]
    F --&gt;|否| H[发送 PB_ROLE_MOVE_CH]

    G --&gt; I[服务器验证时间戳]
    H --&gt; J[服务器处理旧版协议]

    I --&gt; K{时间戳有效?}
    K --&gt;|是| L[处理移动数据]
    K --&gt;|否| M[忽略时间戳，继续处理]

    J --&gt; N{位置/朝向变化?}
    N --&gt;|否| O[仅心跳，直接返回]
    N --&gt;|是| L

    L --&gt; P{移动合法性检查}
    P --&gt;|合法| Q[CRT_Accept - 接受位置]
    P --&gt;|不合法| R[CRT_RollBack - 强制回滚]
    P --&gt;|忽略| S[CRT_Ignore - 无操作]

    Q --&gt; T[更新服务器玩家位置]
    R --&gt; U[发送 PB_SYNC_MOVE_HC 校正]

    T --&gt; V[广播给其他玩家]
    U --&gt; W[客户端强制设置位置]
    W --&gt; X[发送位置确认]

    V --&gt; Y{使用批量广播?}
    Y --&gt;|是| Z[缓存到批量队列]
    Y --&gt;|否| AA[直接发送 PB_ACTOR_MOVEV2_HC]

    Z --&gt; BB[定时批量发送 PB_ACTOR_MOVEV3_HC]
</div><h2 id="10-协议选择决策表">10. 协议选择决策表 </h2>
<table>
<thead>
<tr>
<th>场景</th>
<th>协议类型</th>
<th>可靠性</th>
<th>用途</th>
</tr>
</thead>
<tbody>
<tr>
<td>玩家主动移动</td>
<td>PB_ROLE_MOVE_CH</td>
<td>可靠有序</td>
<td>基础移动同步</td>
</tr>
<tr>
<td>新版移动同步</td>
<td>PB_SYNC_MOVE_CH</td>
<td>可靠有序</td>
<td>精确同步+反作弊</td>
</tr>
<tr>
<td>位置强制校正</td>
<td>PB_SYNC_MOVE_HC</td>
<td>可靠有序</td>
<td>服务器权威校正</td>
</tr>
<tr>
<td>其他玩家移动</td>
<td>PB_ACTOR_MOVEV2_HC</td>
<td>不可靠有序</td>
<td>高频移动广播</td>
</tr>
<tr>
<td>批量移动广播</td>
<td>PB_ACTOR_MOVEV3_HC</td>
<td>不可靠有序</td>
<td>性能优化</td>
</tr>
<tr>
<td>载具移动</td>
<td>PB_TrainMoveCH</td>
<td>可靠有序</td>
<td>特殊载具同步</td>
</tr>
<tr>
<td>位置确认</td>
<td>PB_SYNC_PLAYER_POS_CH</td>
<td>可靠有序</td>
<td>确认机制</td>
</tr>
</tbody>
</table>
<h2 id="总结">总结 </h2>
<p>沙盒游戏的移动同步系统采用了多层协议设计：</p>
<ol>
<li><strong>旧版协议</strong> (PB_ROLE_MOVE_CH) 主要用于兼容性和心跳保活</li>
<li><strong>新版协议</strong> (PB_SYNC_MOVE_CH/HC) 提供更精确的同步和反作弊能力</li>
<li><strong>广播协议</strong> (PB_ACTOR_MOVE 系列) 负责向其他玩家同步移动信息</li>
</ol>
<p>整个系统通过客户端预测、服务器验证、强制校正的机制，确保了多人游戏中移动的流畅性和一致性，同时具备完善的反作弊保护。</p>
<p><strong>关键技术特点</strong>:</p>
<ul>
<li><strong>分层协议设计</strong>: 不同场景使用不同协议</li>
<li><strong>批量优化</strong>: 减少网络调用次数</li>
<li><strong>插值平滑</strong>: 提供流畅的视觉体验</li>
<li><strong>AOI 管理</strong>: 只向相关玩家发送信息</li>
<li><strong>反作弊机制</strong>: 时间戳验证和移动监控</li>
<li><strong>可靠性保障</strong>: 关键协议使用可靠传输</li>
<li><strong>特殊场景支持</strong>: 载具、平台等特殊移动场景</li>
</ul>
<p>这套移动同步系统在保证游戏体验流畅性的同时，通过多层次的验证和优化机制，确保了多人在线游戏的稳定性和公平性。</p>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>