//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "Math/Vector2f.h"
#include "Utilities/dynamic_array.h"
typedef unsigned long long CrowdRef;
#define RESOLVE_HASH_COLLISIONS 1

class ProximityGrid
{
    struct Item
    {
        CrowdRef ref;
        int next;
#if RESOLVE_HASH_COLLISIONS
        short x, y;
#endif
    };

    int m_PoolHead;
    dynamic_array<Item> m_Pool;
    dynamic_array<int> m_Buckets;

    Rainbow::Vector2f m_InvCellSize;
    int m_Bounds[4];

public:
    ProximityGrid();
    ~ProximityGrid();

    bool Init(const int maxItems);
    void ResetCellSize(const Rainbow::Vector2f& cellSize);
    void Clear();
    void AddItem(CrowdRef ref, const float bounds[4]);
    int QueryItems(const float bounds[4], CrowdRef* ids, const int maxIds) const;

    // For debug draw
    Rainbow::Vector2f GetCellSize() const;
    void GetBounds(int bounds[4]) const;
    int GetItemCountAt(int x, int y) const;
};
