#pragma once

#define ASYNC_OPJ_FILE_PARSE 1

#if ASYNC_OPJ_FILE_PARSE
#include "obj_parser.h"
#include "SandboxType.h"
#include "Graphics/Mesh/Mesh.h"
#include "util/thread/SandboxThreadTask.h"
#include "util/thread/SandboxThreadObject.h"
#include "SandboxModelObject.h"

namespace MNSandbox 
{
	struct ObjFileParseJobData 
	{
		std::string	objFilePath;
		Rainbow::AutoRefPtr<Rainbow::DataStream> ds;
		// void callback(Rainbow::SharePtr<Rainbow::Mesh> pNonScaledMesh);
		std::function<void(Rainbow::SharePtr<Rainbow::Mesh>)> callback;
	private:
		friend class ObjFileParseManager;
		bool							 m_meshNeedUpload = false;
		Rainbow::SharePtr<Rainbow::Mesh> m_pOutputNonScaledMesh;
	};

	class ObjFileParseManager : public ThreadObject<ObjFileParseJobData>
	{
	public:
		ObjFileParseManager();
		~ObjFileParseManager() override;
		void OnGameToClose();
		void AsyncParse(ObjFileParseJobData jobData);
		static ObjFileParseManager& GetInstance();
	protected:
		void OnThreadRun(ObjFileParseJobData& job) override;
		void OnThreadMainCallback(ObjFileParseJobData& job) override;
	private:
		ListenerClass<ObjFileParseManager> m_listenGameToClose;
	};
}
#endif