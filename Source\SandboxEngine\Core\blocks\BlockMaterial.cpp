/**
* file : BlockMaterialEx
* func : �������������Ͷ���
*/
#include "BlockMaterial.h"
#include "SectionMesh.h"
#include "Collision.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "BlockGeom.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "Ecosystem.h"
#include "IClientActor.h"
#include "IClientMob.h"
#include "special_blockid.h"
#include "BlockBasic.h"
#include "RenderSection.h"
#include "Common/OgreShared.h"
#include "ActorManagerInterface.h"
#include "OgreScriptLuaVM.h"
#include "Graphics/Texture.h"
#include "SandboxCoreDriver.h"
//#include "BlockMaterialUtil.h"
#include "MiniCraftRenderer.h"
#include "Common/GameStatic.h"
#include "world.h"
#include "WorldManager.h"
#include "TemperatureManagerInterface.h"
#include "LuaInterfaceProxy.h"
#include "EffectManager.h"
#include "container_world.h"

#include "obj_parser.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

IMPLEMENT_BLOCKMATERIAL(AirBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(UnloadBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(SolidBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(CubeBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(ModelBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(MultiModelBlockMaterial)

MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl0(0, "Material_0", "cube", &CubeBlockMaterial::RenderMtlGet0, &CubeBlockMaterial::RenderMtlSet0);
MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl1(1, "Material_1", "cube", &CubeBlockMaterial::RenderMtlGet1, &CubeBlockMaterial::RenderMtlSet1);
MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl2(2, "Material_2", "cube", &CubeBlockMaterial::RenderMtlGet2, &CubeBlockMaterial::RenderMtlSet2);
MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl3(3, "Material_3", "cube", &CubeBlockMaterial::RenderMtlGet3, &CubeBlockMaterial::RenderMtlSet3);
MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl4(4, "Material_4", "cube", &CubeBlockMaterial::RenderMtlGet4, &CubeBlockMaterial::RenderMtlSet4);
MNSandbox::ReflexClassParam<CubeBlockMaterial, std::string> CubeBlockMaterial::R_RenderMtl5(5, "Material_5", "cube", &CubeBlockMaterial::RenderMtlGet5, &CubeBlockMaterial::RenderMtlSet5);

MNSandbox::ReflexClassMember<SolidBlockMaterial, bool> SolidBlockMaterial::R_IsModifyColor(6, "Modify_Color", "solid", &SolidBlockMaterial::m_bColorModify, ReflexConfig::NO_PUBLIC);
MNSandbox::ReflexClassParam<SolidBlockMaterial, Rainbow::ColorQuad> SolidBlockMaterial::R_TmpColor(7,"Block_Color", "solid", &SolidBlockMaterial::ColorTmpGet, &SolidBlockMaterial::ColorTmpSet, ReflexConfig::NO_SAVE);
MNSandbox::ReflexClassParam<SolidBlockMaterial, Rainbow::ColorQuad> SolidBlockMaterial::R_Color(8, "_Block_Color", "solid", &SolidBlockMaterial::ColorGet, &SolidBlockMaterial::ColorSet, ReflexConfig::NO_PUBLIC);

void __BlockVectorSet(Rainbow::ColorQuad& colorValue, BlockVector& blockVector, bool isAssignToBlock)
{
	if (isAssignToBlock)
	{
		blockVector.x = colorValue.r;
		blockVector.y = colorValue.g;
		blockVector.z = colorValue.b;
		blockVector.w = colorValue.a;
	}
	else
	{
		colorValue.r = blockVector.x;
		colorValue.g = blockVector.y;
		colorValue.b = blockVector.z;
		colorValue.a = blockVector.w;
	}
}
//-----------------------------------------------------------------------------------------------


//inline void InitVertLight(BlockGeomVert& vert, int avelt, const float* uvTile)
//{
//	SInt8 indexValue = uvTile != nullptr ? ((SInt8)(uvTile[0] + uvTile[1] * 10)) : 0;
//	int lt1 = (((avelt >> 4) & 0xf) * vert.color.a) >> 5;
//	int lt2 = (((avelt >> 20) & 0xf) * vert.color.a) >> 5;
//	short lightData = (lt1 << 8) | lt2;
//	vert.pos.w = (indexValue << 16) | lightData;
//	//vert.pos.w = (lt1 << 8) | lt2;
//}

void AirBlockMaterial::init(int resid)
{
	Super::init(resid);

	//���Գ�ʼ��
	SetToggle(BlockToggle_IsSolid, false);
	SetToggle(BlockToggle_IsAir, true);
	SetAttrRenderType(BLOCKRENDER_NONE);
}

static MINIW::GameStatic<std::unordered_map<unsigned int, dynamic_array<BlockVertUV>>> s_vertex_mtl_uv;
std::unordered_map<unsigned int, dynamic_array<BlockVertUV>>& SolidBlockMaterial::vertex_mtl_uv()
{
	return *s_vertex_mtl_uv.EnsureInitialized();
}

static MINIW::GameStatic<Rainbow::Mutex> s_mutex_block_uv_cache;
Rainbow::Mutex& SolidBlockMaterial::mutex_block_uv_cache()
{
	return *s_mutex_block_uv_cache.EnsureInitialized();
}

const float g_fUVSwellSinglePart = 1.414f;//(2�Ŀ���)
const float g_fUVBlockRealSize = BLOCK_SIZE + (g_fSnowSingleSwellMaxSize * g_fUVSwellSinglePart) * 2;
const float g_fUVScale = (1.f - (BLOCK_SIZE / g_fUVBlockRealSize)) / 2.f;
const dynamic_array<BlockVertUV>* SolidBlockMaterial::getBlcokMaterialVertexUV(int dir, bool isSanrioBlock, float height, unsigned char hor_ver, bool isSnow, unsigned int dataex)
{
	Rainbow::Mutex::AutoLock lock(mutex_block_uv_cache());
	bool specialdir = height < 0;
	unsigned int key = dir | (isSanrioBlock ? (1 << 3) : 0);
	unsigned int subKey = 0;
	if (height < 1.f)
	{
		height = specialdir ? -height : height;
		int realHeight = specialdir ? -(int)(height * BLOCK_SIZE) % 101 : (int)(height * BLOCK_SIZE) % 101;
		if ((6 == hor_ver && dir < 4) || (4 > hor_ver && (dir != hor_ver && dir != ReverseDirection(hor_ver))))
		{	//realHeight�����ֵֻ��100ռ7Ϊ128�͹��ˡ�hor_ver���ֵΪ6ռ3λ
			subKey = (realHeight) | (hor_ver << 7) | (specialdir ? (1 << (7 + 3)) : 0);
			key |= (subKey << 4);
		}
	}
	unsigned int snowKey = 0;
	if (isSnow)
	{
		snowKey = 1 | (dataex << 1);
		key |= (snowKey << (4 + 11));
	}
	// 	unsigned int key = dir | (isSanrioBlock ? (1 << 3) : 0) | (subKey << (3 + 1)) | (snowKey << 11);
	dynamic_array<BlockVertUV>* pqueue = NULL;
	auto queue = vertex_mtl_uv().find(key);
	if (queue == vertex_mtl_uv().end())
	{
		pqueue = &vertex_mtl_uv()[key];
	}
	else
	{
		pqueue = &queue->second;
	}
	if (pqueue->size() < 16)
	{
		float ROUND_DIFF_ = ROUND_DIFF;
		pqueue->resize_initialized(16);
		float texuv[8] = { 0 };
		// 		const float* texuv = BLOCKUV[dir];
		if (isSanrioBlock)
		{
			const float Z_FLIP[8] = { 1, 1, 0, 1, 0, 0, 1, 0 };
			const float X_FLIP[8] = { 1, 1, 1, 0, 0, 0, 0, 1 };
			const float Y_FLIP[8] = { 1, 0, 1, 1,0, 1, 0, 0 };
			if (dir == DIR_POS_Z)
			{
				// 				texuv = Z_FLIP;
				memcpy(texuv, Z_FLIP, 8 * sizeof(float));
			}
			else if (dir == DIR_POS_Y)
			{
				// 				texuv = Y_FLIP;
				memcpy(texuv, Y_FLIP, 8 * sizeof(float));
			}
			else if (dir == DIR_POS_X || dir == DIR_NEG_X)
			{
				// 				texuv = X_FLIP;
				memcpy(texuv, X_FLIP, 8 * sizeof(float));
			}
		}
		else
		{
			memcpy(texuv, Rainbow::BLOCKUV[dir], 8 * sizeof(float));
		}
		if (snowKey)
		{
			float roundChangeSize = g_fSnowSingleSwellMaxSize * height;
			ROUND_DIFF_ += (roundChangeSize / 100.f);
			if (dir < 4)
			{
				height += ((1.f - (BLOCK_SIZE / (BLOCK_SIZE + (g_fSnowSingleSwellMaxSize * height * g_fUVSwellSinglePart) * 2))) / 2.f);
			}
			else
			{
				for (int k = 0; k < 8; k++)
				{
					texuv[k] = texuv[k] < 0.5f ? g_fUVScale : 1 - g_fUVScale;
				}
			}
		}

		if (height < 1.f && subKey)
		{
			if (6 == hor_ver)
			{
				if (dir < 2)
				{
					if (!specialdir)
					{
						// 						texuv[0] = texuv[0] == 0 ? height : 1 - height;
						texuv[1] = texuv[1] < 0.5f ? 1 - height : height;
						// 						texuv[6] = texuv[6] == 0 ? height : 1 - height;
						texuv[7] = texuv[7] < 0.5f ? 1 - height : height;
					}
					else
					{
						// 						texuv[0] = texuv[0] == 0 ? height : 1 - height;
						texuv[1] = texuv[1] < 0.5f ? 1 - height : height;
						// 						texuv[6] = texuv[6] == 0 ? height : 1 - height;
						texuv[7] = texuv[7] < 0.5f ? 1 - height : height;
					}
				}
				else
				{
					if (!specialdir)
					{
						// 						texuv[0] = texuv[0] == 0 ? height : 1 - height;
						texuv[1] = texuv[1] < 0.5f ? 1 - height : height;
						// 						texuv[2] = texuv[2] == 0 ? height : 1 - height;
						texuv[3] = texuv[3] < 0.5f ? 1 - height : height;
					}
					else
					{
						// 						texuv[0] = texuv[0] == 0 ? height : 1 - height;
						texuv[1] = texuv[1] < 0.5f ? 1 - height : height;
						// 						texuv[2] = texuv[2] == 0 ? height : 1 - height;
						texuv[3] = texuv[3] < 0.5f ? 1 - height : height;
					}
				}
			}
			else
			{
				if (DIR_NEG_X == hor_ver)
				{
					if (4 > dir)
					{
						texuv[2] = texuv[2] < 0.5f ? 1 - height : height;
						// 						texuv[3] = texuv[3] == 0 ? 1 - height : height;
						texuv[4] = texuv[4] < 0.5f ? 1 - height : height;
						// 						texuv[5] = texuv[5] == 0 ? 1 - height : height;
					}
					else
					{
						texuv[4] = texuv[4] < 0.5f ? 1 - height : height;
						// 						texuv[5] = texuv[5] == 0 ? 1 - height : height;
						texuv[6] = texuv[6] < 0.5f ? 1 - height : height;
						// 						texuv[7] = texuv[7] == 0 ? 1 - height : height;
					}
				}
				else if (DIR_POS_X == hor_ver)
				{
					if (4 > dir)
					{
						texuv[0] = texuv[0] < 0.5f ? 1 - height : height;
						// 						texuv[1] = texuv[1] == 0 ? 1 - height : height;
						texuv[6] = texuv[6] < 0.5f ? 1 - height : height;
						// 						texuv[7] = texuv[7] == 0 ? 1 - height : height;
					}
					else
					{
						texuv[0] = texuv[0] < 0.5f ? 1 - height : height;
						// 						texuv[1] = texuv[1] == 0 ? 1 - height : height;
						texuv[2] = texuv[2] < 0.5f ? 1 - height : height;
						// 						texuv[3] = texuv[3] == 0 ? 1 - height : height;
					}
				}
				else if (DIR_NEG_Z == hor_ver)
				{
					if (4 > dir)
					{
						texuv[4] = texuv[4] < 0.5f ? 1 - height : height;
						// 						texuv[5] = texuv[5] == 0 ? 1 - height : height;
						texuv[6] = texuv[6] < 0.5f ? 1 - height : height;
						// 						texuv[7] = texuv[7] == 0 ? 1 - height : height;
					}
					else
					{
						//texuv[2] = texuv[2] < 0.5f ? 1 - height : height;
						 						texuv[3] = texuv[3] == 0 ? 1 - height : height;
						//texuv[4] = texuv[4] < 0.5f ? 1 - height : height;
						 						texuv[5] = texuv[5] == 0 ? 1 - height : height;
					}
				}
				else if (DIR_POS_Z == hor_ver)
				{
					if (4 > dir)
					{
						texuv[0] = texuv[0] < 0.5f ? 1 - height : height;
						// 						texuv[1] = texuv[1] == 0 ? 1 - height : height;
						texuv[2] = texuv[2] < 0.5f ? 1 - height : height;
						// 						texuv[3] = texuv[3] == 0 ? 1 - height : height;
					}
					else
					{
						//texuv[0] = texuv[0] < 0.5f ? 1 - height : height;
						 						texuv[1] = texuv[1] == 0 ? 1 - height : height;
						//texuv[6] = texuv[6] < 0.5f ? 1 - height : height;
						 						texuv[7] = texuv[7] == 0 ? 1 - height : height;
					}
				}
			}
		}
		for (int i = 0; i < 4; i++)
		{
			(*pqueue)[i].x = texuv[i * 2] * BLOCKUV_SCALE;
			(*pqueue)[i].y = texuv[i * 2 + 1] * BLOCKUV_SCALE;
		}
		(*pqueue)[4].x = (*pqueue)[0].x + ((*pqueue)[1].x - (*pqueue)[0].x) * ROUND_DIFF_;
		(*pqueue)[4].y = (*pqueue)[0].y + ((*pqueue)[1].y - (*pqueue)[0].y) * ROUND_DIFF_;
		(*pqueue)[5].x = (*pqueue)[0].x + ((*pqueue)[1].x - (*pqueue)[0].x) * (1 - ROUND_DIFF_);
		(*pqueue)[5].y = (*pqueue)[0].y + ((*pqueue)[1].y - (*pqueue)[0].y) * (1 - ROUND_DIFF_);
		(*pqueue)[6].x = (*pqueue)[1].x + ((*pqueue)[2].x - (*pqueue)[1].x) * ROUND_DIFF_;
		(*pqueue)[6].y = (*pqueue)[1].y + ((*pqueue)[2].y - (*pqueue)[1].y) * ROUND_DIFF_;
		(*pqueue)[7].x = (*pqueue)[1].x + ((*pqueue)[2].x - (*pqueue)[1].x) * (1 - ROUND_DIFF_);
		(*pqueue)[7].y = (*pqueue)[1].y + ((*pqueue)[2].y - (*pqueue)[1].y) * (1 - ROUND_DIFF_);
		(*pqueue)[8].x = (*pqueue)[2].x + ((*pqueue)[3].x - (*pqueue)[2].x) * ROUND_DIFF_;
		(*pqueue)[8].y = (*pqueue)[2].y + ((*pqueue)[3].y - (*pqueue)[2].y) * ROUND_DIFF_;
		(*pqueue)[9].x = (*pqueue)[2].x + ((*pqueue)[3].x - (*pqueue)[2].x) * (1 - ROUND_DIFF_);
		(*pqueue)[9].y = (*pqueue)[2].y + ((*pqueue)[3].y - (*pqueue)[2].y) * (1 - ROUND_DIFF_);
		(*pqueue)[10].x = (*pqueue)[3].x + ((*pqueue)[0].x - (*pqueue)[3].x) * ROUND_DIFF_;
		(*pqueue)[10].y = (*pqueue)[3].y + ((*pqueue)[0].y - (*pqueue)[3].y) * ROUND_DIFF_;
		(*pqueue)[11].x = (*pqueue)[3].x + ((*pqueue)[0].x - (*pqueue)[3].x) * (1 - ROUND_DIFF_);
		(*pqueue)[11].y = (*pqueue)[3].y + ((*pqueue)[0].y - (*pqueue)[3].y) * (1 - ROUND_DIFF_);
		(*pqueue)[12].x = (*pqueue)[11].x + ((*pqueue)[6].x - (*pqueue)[11].x) * ROUND_DIFF_;
		(*pqueue)[12].y = (*pqueue)[11].y + ((*pqueue)[6].y - (*pqueue)[11].y) * ROUND_DIFF_;
		(*pqueue)[13].x = (*pqueue)[11].x + ((*pqueue)[6].x - (*pqueue)[11].x) * (1 - ROUND_DIFF_);
		(*pqueue)[13].y = (*pqueue)[11].y + ((*pqueue)[6].y - (*pqueue)[11].y) * (1 - ROUND_DIFF_);
		(*pqueue)[14].x = (*pqueue)[10].x + ((*pqueue)[7].x - (*pqueue)[10].x) * (1 - ROUND_DIFF_);
		(*pqueue)[14].y = (*pqueue)[10].y + ((*pqueue)[7].y - (*pqueue)[10].y) * (1 - ROUND_DIFF_);
		(*pqueue)[15].x = (*pqueue)[10].x + ((*pqueue)[7].x - (*pqueue)[10].x) * ROUND_DIFF_;
		(*pqueue)[15].y = (*pqueue)[10].y + ((*pqueue)[7].y - (*pqueue)[10].y) * ROUND_DIFF_;
	}
	return pqueue;
}

//-----------------------------------------------------------------------------------------------
void UnloadBlockMaterial::init(int resid)
{
	Super::init(resid);
	SetAttrRenderType(BLOCKRENDER_NONE);
	m_TypeName = "unload";
	//m_Def = GetDefManagerProxy()->getBlockDef(resid);
}

bool UnloadBlockMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) 
{
	return true;
	//if (neighbor)
	//{
	//	if (neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID
	//		//|| !neighbor->isOpaqueCube()
	//		)
	//	{
	//		return true;
	//	}
	//}
	//return false;
}

//-----------------------------------------------------------------------------------------------
SolidBlockMaterial::SolidBlockMaterial() :/* m_Mtl(NULL),*/ m_HasEmissiveTex(false)
{
	//RegDefEvents();
}

SolidBlockMaterial::~SolidBlockMaterial()
{
//	ENG_RELEASE(m_Mtl);
	/*
	for(size_t i=0; i<m_ItemMtls.size(); i++)
	{
		m_ItemMtls[i].pmtl->release();
	}*/
}

void SolidBlockMaterial::initGeomName()
{
	if (isUseCustomModel())
	{
		m_geomName = GetBlockDef()->Texture2.c_str();
	}

	m_geomName = "cube";
}
//const char* SolidBlockMaterial::getGeomName()
//{
//	if (isUseCustomModel())
//	{
//		return GetBlockDef()->Texture2.c_str();
//	}
//
//	return "cube";
//}

void SolidBlockMaterial::init(int resid)
{
	BlockMaterial::init(resid);

	SetToggle(BlockToggle_IsOpaqueCube, true);
	SetAttrRenderType(isUseCustomModel() ? BLOCKRENDER_MODEL : BLOCKRENDER_CUBE);


}

void SolidBlockMaterial::initDefaultMtl()
{
	if (!isUseCustomModel())
	{
		return;
	}
	auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_LITTLE);
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl); // ������1
}
//
//bool SolidBlockMaterial::isOpaqueCube()
//{
//	/*if (isUseCustomModel())
//	{
//		return false;
//	}*/
//
//	return true;
//}


bool SolidBlockMaterial::isKeepRoundBlock(BlockMaterial* mtl)
{
	return !(mtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane);
}

bool SolidBlockMaterial::checkBlockMeshIsBuild(const BuildSectionMeshData& data, const WCoord& blockpos, const int& d, bool& isNoClip)
{
	auto psection = data.m_SharedSectionData;
	WCoord selfPos = psection->getOrigin() + blockpos;
	auto thisBlock = psection->getBlock(blockpos);
	auto pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	float blockheight = getBlockHeight(curblockdata);

	DirectionType dir = (DirectionType)d;

	bool isBuild = true;

	const WCoord* vNeighbor/*[8]*/ = RoundBlockNeighbor[dir];

	Block samesaidBlock;
	BlockMaterial* samesaidMtl = NULL;
	if (psection && psection->getSectionType() == SECTION_VEHICLE)
	{
		WCoord sameDirPos = blockpos + g_DirectionCoord[d];
		if (psection->isValidPos(sameDirPos))
		{
			samesaidBlock = psection->getBlock(sameDirPos);
			samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
		}
	}
	else if (psection)
	{
		//WCoord sameDirPos = selfPos + g_DirectionCoord[d];
		//blockpos ��section�е�λ��
		Block b = psection->getNeighborBlock(blockpos, g_DirectionCoord[d]);
		if (!b.isEmpty())
		{
			samesaidBlock = b;
			samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
		}
	}
	if (samesaidMtl)
	{
		//Ҫ���Ӱ�ש
		if (samesaidMtl->coverNeighbor(samesaidBlock.getData(), this, curblockdata, ReverseDirection(dir)))
		{
			isBuild = false;
			if ((this->BlockTypeId() == BlockMaterial::BlockType::BlockType_Snow || this->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab) && (this->getBlockHeight(thisBlock.getData()) == 1 || (this->getBlockHeight(thisBlock.getData()) < 0 && dir == DIR_NEG_Y) || (this->getBlockHeight(thisBlock.getData()) > 0 && dir == DIR_POS_Y))
				|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_HorizontalHalfSlantBlock && (this->getBlockHeight(thisBlock.getData()) == 0.5f && dir == DIR_POS_Y || this->getBlockHeight(thisBlock.getData()) == -0.5f && dir == DIR_NEG_Y)
				|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalHalfSlantBlock && (this->getBlockHeight(thisBlock.getData()) == 0.5f)
				|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalSlab && (this->getBlockHeight(thisBlock.getData()) < 1.f))
			{
				isBuild = true;
				isNoClip = true;
			}
			else
			{
				bool t_[8] = { false };
				int count = 0;
				Block neighborBlock;
				BlockMaterial* neighborMtl = NULL;
				for (int i = 0; i < 4; i++)
				{
					WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
					if (psection && psection->getSectionType() == SECTION_VEHICLE)
					{
						if (psection->isValidPos(diagonalPos))
						{
							neighborBlock = psection->getBlock(diagonalPos);
							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
						}
					}
					else if(psection)
					{
						//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
						Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[i], dir));
						if (!b.isEmpty())
						{
							neighborBlock = b;
							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
						}
					}

					if (neighborMtl)
					{
						if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
						{
							t_[i] = true;
							count++;
						}
					}
				}
				if (count <= 2)
				{
					for (int i = 4; i < 8; i++)
					{
						WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
						if (psection && psection->getSectionType() == SECTION_VEHICLE)
						{
							if (psection->isValidPos(diagonalPos))
							{
								neighborBlock = psection->getBlock(diagonalPos);
								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
							}
						}
						else if(psection)
						{
							//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
							Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[i], dir));
							if (!b.isEmpty())
							{
								neighborBlock = b;
								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
							}
						}

						if (neighborMtl)
						{
							if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
							{
								t_[i] = true;
							}
						}
					}
					bool t[8] = { false };
					for (int i = 0; i < 8; i++)
					{
						WCoord diagonalPos = blockpos + vNeighbor[i];
						if (psection && psection->getSectionType() == SECTION_VEHICLE)
						{
							if (psection->isValidPos(diagonalPos))
							{
								neighborBlock = psection->getBlock(diagonalPos);
								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
							}
						}
						else if(psection)
						{
							//WCoord sameDirPos = selfPos + vNeighbor[i];
							Block b = psection->getNeighborBlock(blockpos, vNeighbor[i]);
							if (!b.isEmpty())
							{
								neighborBlock = b;
								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
							}
						}

						if (neighborMtl)
						{
							if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
							{
								t[i] = true;
							}
						}
					}

					if (t[0] && t[5] && t[3] && !t_[0] && !t_[5] && !t_[3])
					{
						isBuild = true;
					}
					else if (t[3] && t[4] && t[1] && !t_[3] && !t_[4] && !t_[1])
					{
						isBuild = true;
					}
					else if (t[1] && t[6] && t[2] && !t_[1] && !t_[6] && !t_[2])
					{
						isBuild = true;
					}
					else if (t[2] && t[7] && t[0] && !t_[2] && !t_[7] && !t_[0])
					{
						isBuild = true;
					}
				}
			}
		}
		else if (samesaidMtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab || samesaidMtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Snow)
		{
			float samesaidHight = getBlockHeight(samesaidBlock.getData());
			if (this->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab && ((blockheight == samesaidHight && (blockheight == 1 || dir < DIR_NEG_Y)) || (dir == DIR_NEG_Y && blockheight > 0 && samesaidHight < 0) || (dir == DIR_POS_Y && blockheight < 0 && samesaidHight > 0))&& samesaidMtl->BlockTypeId() != BlockMaterial::BlockType::BlockType_Snow)
			{//��שֻ�Ͱ�ש�ϲ�
				isBuild = false;
			}
			else
			{
				isNoClip = true;
			}
		}
		else if (samesaidMtl->GetAttrRenderType() == this->GetAttrRenderType() && samesaidMtl->GetAttrRenderType() == BLOCKRENDER_SPECIAL_CUBE_MODEL)
		{
			isBuild = false;
		}
		else if ((samesaidBlock.getResID() > 0 && samesaidBlock.getResID()!=20 ) && isKeepRoundBlock(samesaidMtl)) //�����ǿ����Ϳ����з��鶼ΪԲ��
		{//��שֻ�Ͱ�ש�ϲ�
			isNoClip = true;
		}
	}
	return isBuild;
}

int SolidBlockMaterial::getBlockMeshAngleData(const BuildSectionMeshData& data, const WCoord& blockpos, BlockGeomMeshInfo& mesh, const int& d, bool isIgnoreTileUV, RenderBlockMaterial* pmtl, const bool& isNoClip, bool mergeface, bool isSnowSwell, int neighorData)
{
	auto psection = data.m_SharedSectionData;
	auto pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	WCoord selfPos = psection->getOrigin() + blockpos;

	unsigned int avelt[16];
	int useround = 0;
	DirectionType dir = (DirectionType)d;

	int blockdata = 0;
	// 	bool isNoClip = false;

	const float* texuv = Rainbow::BLOCKUV[dir];
	const char* origin_c/*[3]*/ = /*{ 0.f };*/ RoundBlockOriginC[dir];
	const WCoord* vNeighbor/*[8]*/ = RoundBlockNeighbor[dir];


	const char* du = Rainbow::DU[dir];
	const char* dv = Rainbow::DV[dir];

	const dynamic_array<UInt16>* indices = Rainbow::OrgBlockIndex[dir];

	// 	BlockGeomMeshInfo mesh;

	dynamic_array<BlockGeomVert> verts;
	verts.resize_initialized(16, kIncreaseToExactSize);

	//unsigned short dir_color = Normal2LightColor(g_DirectionCoord[d].toVector3());

	Vector3f normalVec = g_DirectionCoord[d].toVector3();
	//normalVec.y = 1.0f;
	Rainbow::Normalize(normalVec);
	BlockVector normal_dir = PackVertNormal(normalVec, 0);
	BlockVector vertcolor;
	vertcolor.v = 0xffffffff;
	vertcolor.w = 0;

	int vertex_size = 0;
	do
	{
		const float* uvtile = NULL;
		if (isIgnoreTileUV)
			uvtile = pmtl->getUVTile();
		// 		InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, uvtile, 255, normal_dir);

		unsigned int lt_me = psection->getLight2(NeighborCoord(blockpos, dir), true);
		unsigned int src_lt_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[0], dir), true);   //&GetGrid(grids, u - 1, v);
		unsigned int src_ltu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[1], dir), true);    // &GetGrid(grids, u + 1, v);
		unsigned int src_ltv = psection->getLight2(NeighborCoord(blockpos + vNeighbor[2], dir), true);    // &GetGrid(grids, u, v + 1);
		unsigned int src_lt_v = psection->getLight2(NeighborCoord(blockpos + vNeighbor[3], dir), true);   // &GetGrid(grids, u, v - 1);
		unsigned int src_lt_vu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[4], dir), true);   // &GetGrid(grids, u + 1, v - 1);
		unsigned int src_lt_v_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[5], dir), true);   // &GetGrid(grids, u - 1, v - 1);
		unsigned int src_ltvu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[6], dir), true);    // &GetGrid(grids, u + 1, v + 1);
		unsigned int src_ltv_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[7], dir), true);    // &GetGrid(grids, u - 1, v + 1);

		unsigned int block0 = psection->getLight2(blockpos + vNeighbor[0], true);   //&GetGrid(grids, u - 1, v);
		unsigned int block1 = psection->getLight2(blockpos + vNeighbor[1], true);    // &GetGrid(grids, u + 1, v);
		unsigned int block2 = psection->getLight2(blockpos + vNeighbor[2], true);    // &GetGrid(grids, u, v + 1);
		unsigned int block3 = psection->getLight2(blockpos + vNeighbor[3], true);   // &GetGrid(grids, u, v - 1);
		unsigned int block4 = psection->getLight2(blockpos + vNeighbor[4], true);   // &GetGrid(grids, u + 1, v - 1);
		unsigned int block5 = psection->getLight2(blockpos + vNeighbor[5], true);   // &GetGrid(grids, u - 1, v - 1);
		unsigned int block6 = psection->getLight2(blockpos + vNeighbor[6], true);    // &GetGrid(grids, u + 1, v + 1);
		unsigned int block7 = psection->getLight2(blockpos + vNeighbor[7], true);    // &GetGrid(grids, u - 1, v + 1);


		avelt[0] = ((lt_me + src_lt_u + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
		avelt[1] = ((lt_me + src_ltu + src_lt_vu + src_lt_v) >> 2) & 0xff00ff;
		avelt[2] = ((lt_me + src_ltv + src_ltvu + src_ltu) >> 2) & 0xff00ff;
		avelt[3] = ((lt_me + src_lt_u + src_ltv_u + src_ltv) >> 2) & 0xff00ff;

		//-v  -v-u ������
		avelt[4] = ((block3 + block5 + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
		//-v  -v+u ������
		avelt[5] = ((block3 + block4 + src_lt_v + src_lt_vu) >> 2) & 0xff00ff;
		// v  v-u ������
		avelt[6] = ((block2 + block7 + src_ltv + src_ltv_u) >> 2) & 0xff00ff;
		// v  v+u ������
		avelt[7] = ((block2 + block6 + src_ltv + src_ltvu) >> 2) & 0xff00ff;
		// -u -v-u  ������
		avelt[8] = ((block0 + block5 + src_lt_u + src_lt_v_u) >> 2) & 0xff00ff;
		// u  -v+u ������
		avelt[9] = ((block1 + block4 + src_ltu + src_lt_vu) >> 2) & 0xff00ff;
		// -u v-u  ������
		avelt[10] = ((block0 + block7 + src_lt_u + src_ltv_u) >> 2) & 0xff00ff;
		// u v+u  ������
		avelt[11] = ((block1 + block6 + src_ltu + src_ltvu) >> 2) & 0xff00ff;

		for (int o = 0; o < 8; o++)
		{
			Block neighborBlock;
			BlockMaterial* neighborMtl = NULL;
			if (psection && psection->getSectionType() == SECTION_VEHICLE)
			{
				if (psection->isValidPos(blockpos + vNeighbor[o]))
				{
					neighborBlock = psection->getBlock(blockpos + vNeighbor[o]);
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}
			else if (psection)
			{
				//WCoord sameDirPos = selfPos + vNeighbor[o];
				Block b = psection->getNeighborBlock(blockpos, vNeighbor[o]);
				if (!b.isEmpty())
				{
					neighborBlock = b;
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}

			bool isEven = false;
			if (neighborMtl)
			{
				if ((neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20)  && isKeepRoundBlock(neighborMtl))
				{
					isEven = true;
				}
			}

			if (isEven)
			{
				if (0 == o)
				{// -u
					blockdata |= 1;
				}
				else if (1 == o)
				{// +u
					blockdata |= (1 << 1);
				}
				else if (2 == o)
				{// +v
					blockdata |= (1 << 2);
				}
				else if (3 == o)
				{// -v
					blockdata |= (1 << 3);
				}
				else if (4 == o)
				{// -v + u
					blockdata |= (1 << 4);
				}
				else if (5 == o)
				{// -v - u
					blockdata |= (1 << 5);
				}
				else if (6 == o)
				{// +v + u
					blockdata |= (1 << 6);
				}
				else if (7 == o)
				{// v - u
					blockdata |= (1 << 7);
				}
			}
		}
		if (blockdata == 0xff)
		{
			vertex_size = 4;
			InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
			InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
			InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
			InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
			for (int i = 0; i < 4; i++)
			{
				InitBlockVertLight(verts[i], avelt[i], uvtile);
			}
			if (1.f > blockheight)
			{
				if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock)
				{
					int blockDir = curblockdata & 3;

					if (blockDir == DIR_POS_Z || blockDir == DIR_POS_X)
					{
						for (int i = 0; i < vertex_size; i++)
						{
							if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) > 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) > 50)
							{
								verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
								verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
							}
						}
					}
					else
					{
						for (int i = 0; i < vertex_size; i++)
						{
							if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) < 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) < 50)
							{
								verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
								verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
							}
						}
					}
				}
				else
				{
					if (specialdir == DIR_POS_Y)
					{
						for (int i = 0; i < vertex_size; i++)
						{
							BlockGeomVert* vert = &verts[i];
							if ((vert->pos.y - origin_c[1]) > 50/*blockheight * 100*/)
							{
								vert->pos.y -= (100 - blockheight * 100);
							}
						}
					}
					else
					{
						for (int i = 0; i < vertex_size; i++)
						{
							if ((verts[i].pos.y - origin_c[1]) < 50/*blockheight * 100*/)
							{
								verts[i].pos.y += (100 + blockheight * 100);
							}
						}
					}
				}
			}
			break;
		}

		for (int o = 0; o < 8; o++)
		{
			Block neighborBlock;
			BlockMaterial* neighborMtl = NULL;
			WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[o], dir);
			if (psection && psection->getSectionType() == SECTION_VEHICLE)
			{
				if (psection->isValidPos(diagonalPos))
				{
					neighborBlock = psection->getBlock(diagonalPos);
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}
			else if (psection)
			{
				//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[o], dir);
				Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[o], dir));
				if (!b.isEmpty())
				{
					neighborBlock = b;
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}

			bool isEven = false;
			if (neighborMtl)
			{
				if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
				{
					isEven = true;
				}
			}

			if (isEven)
			{
				if (0 == o)
				{// -u
					blockdata |= (1 << 8);
				}
				else if (1 == o)
				{// +u
					blockdata |= (1 << 9);
				}
				else if (2 == o)
				{// +v
					blockdata |= (1 << 10);
				}
				else if (3 == o)
				{// -v
					blockdata |= (1 << 11);
				}
				else if (4 == o)
				{// -v + u
					blockdata |= (1 << 12);
				}
				else if (5 == o)
				{// -v - u
					blockdata |= (1 << 13);
				}
				else if (6 == o)
				{// +v + u
					blockdata |= (1 << 14);
				}
				else if (7 == o)
				{// v - u
					blockdata |= (1 << 15);
				}
			}
		}
		if (isNoClip)
		{
			blockdata |= (1 << 16);
		}
		useround = 16;

		// 		const vertex_cache* cache = Section::getVertexCache(NULL, dir, blockdata, RoundAngleSize, mergeface);
		unsigned char hor_ver = 6;
		if (blockheight < 1.f)
		{
			if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock) hor_ver = curblockdata & 3;
		}
		const vertex_cache* cache = Section::getVertexCacheEx(blockheight, NULL, dir, blockdata, neighorData, hor_ver, isSnowSwell, RoundAngleSize, mergeface);
		if (cache)
		{
			const auto& find = *cache;
			vertex_size = find.vertex_size;
			useround = find.vertex_size | ((find.index_size) << 8);
			const dynamic_array<BlockVertUV>* uv_ = getBlcokMaterialVertexUV(dir, false, blockheight, hor_ver, isSnowSwell, neighorData);
			// 			BlockVertUV uv_[16];
			// 			uv_[0].x = verts[0].uv.x, uv_[0].y = verts[0].uv.y;
			// 			uv_[1].x = verts[1].uv.x, uv_[1].y = verts[1].uv.y;
			// 			uv_[2].x = verts[2].uv.x, uv_[2].y = verts[2].uv.y;
			// 			uv_[3].x = verts[3].uv.x, uv_[3].y = verts[3].uv.y;
			// 			uv_[4].x = uv_[0].x + (uv_[1].x - uv_[0].x) * ROUND_DIFF; uv_[4].y = uv_[0].y + (uv_[1].y - uv_[0].y) * ROUND_DIFF;
			// 			uv_[5].x = uv_[0].x + (uv_[1].x - uv_[0].x) * (1 - ROUND_DIFF); uv_[5].y = uv_[0].y + (uv_[1].y - uv_[0].y) * (1 - ROUND_DIFF);
			// 			uv_[6].x = uv_[1].x + (uv_[2].x - uv_[1].x) * ROUND_DIFF; uv_[6].y = uv_[1].y + (uv_[2].y - uv_[1].y) * ROUND_DIFF;
			// 			uv_[7].x = uv_[1].x + (uv_[2].x - uv_[1].x) * (1 - ROUND_DIFF); uv_[7].y = uv_[1].y + (uv_[2].y - uv_[1].y) * (1 - ROUND_DIFF);
			// 			uv_[8].x = uv_[2].x + (uv_[3].x - uv_[2].x) * ROUND_DIFF; uv_[8].y = uv_[2].y + (uv_[3].y - uv_[2].y) * ROUND_DIFF;
			// 			uv_[9].x = uv_[2].x + (uv_[3].x - uv_[2].x) * (1 - ROUND_DIFF); uv_[9].y = uv_[2].y + (uv_[3].y - uv_[2].y) * (1 - ROUND_DIFF);
			// 			uv_[10].x = uv_[3].x + (uv_[0].x - uv_[3].x) * ROUND_DIFF; uv_[10].y = uv_[3].y + (uv_[0].y - uv_[3].y) * ROUND_DIFF;
			// 			uv_[11].x = uv_[3].x + (uv_[0].x - uv_[3].x) * (1 - ROUND_DIFF); uv_[11].y = uv_[3].y + (uv_[0].y - uv_[3].y) * (1 - ROUND_DIFF);
			// 			uv_[12].x = uv_[11].x + (uv_[6].x - uv_[11].x) * ROUND_DIFF; uv_[12].y = uv_[11].y + (uv_[6].y - uv_[11].y) * ROUND_DIFF;
			// 			uv_[13].x = uv_[11].x + (uv_[6].x - uv_[11].x) * (1 - ROUND_DIFF); uv_[13].y = uv_[11].y + (uv_[6].y - uv_[11].y) * (1 - ROUND_DIFF);
			// 			uv_[14].x = uv_[10].x + (uv_[7].x - uv_[10].x) * (1 - ROUND_DIFF); uv_[14].y = uv_[10].y + (uv_[7].y - uv_[10].y) * (1 - ROUND_DIFF);
			// 			uv_[15].x = uv_[10].x + (uv_[7].x - uv_[10].x) * ROUND_DIFF; uv_[15].y = uv_[10].y + (uv_[7].y - uv_[10].y) * ROUND_DIFF;
			for (int i = 0; i < vertex_size; i++)
			{
				auto& vertex_temp = find.base[i];
				verts[i].color = vertex_temp.color;
				verts[i].color.r = vertcolor.x;
				verts[i].color.g = vertcolor.y;
				verts[i].color.b = vertcolor.z;
				verts[i].normal = vertex_temp.normal;
				verts[i].uv.x = (*uv_)[vertex_temp.uv_index].x;
				verts[i].uv.y = (*uv_)[vertex_temp.uv_index].y;
				verts[i].pos.x = vertex_temp.pos.x + (origin_c[0] - origin_zero[0]) * BLOCK_SIZE;
				verts[i].pos.y = vertex_temp.pos.y + (origin_c[1] - origin_zero[1]) * BLOCK_SIZE;
				verts[i].pos.z = vertex_temp.pos.z + (origin_c[2] - origin_zero[2]) * BLOCK_SIZE;
				int avelt_temp = 0;
				if (vertex_temp.avelt_size == 1)
				{
					avelt_temp = avelt[vertex_temp.avelt_index_0];
				}
				else if (vertex_temp.avelt_size == 2)
				{
					avelt_temp = (avelt[vertex_temp.avelt_index_0] + avelt[vertex_temp.avelt_index_1]) >> 1;
				}
				else
				{
					int lt1 = (((avelt[vertex_temp.avelt_index_0] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 4) & 0xf)) / 3;
					int lt2 = (((avelt[vertex_temp.avelt_index_0] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 20) & 0xf)) / 3;
					avelt_temp = (lt1 << 4) + (lt2 << 20);
				}
				InitBlockVertLight(verts[i], avelt_temp, uvtile);
			}
			indices = &find.index;
		}
	} while (0);

	// 	if (1.f > blockheight)
	// 	{
	// 		if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock)
	// 		{
	// 			int blockDir = curblockdata & 3;
	// 
	// 			if (blockDir == DIR_POS_Z || blockDir == DIR_POS_X)
	// 			{
	// 				for (int i = 0; i < vertex_size; i++)
	// 				{
	// 					if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) > 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) > 50)
	// 					{
	// 						verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
	// 						verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
	// 					}
	// 				}
	// 			}
	// 			else
	// 			{
	// 				for (int i = 0; i < vertex_size; i++)
	// 				{
	// 					if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) < 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) < 50)
	// 					{
	// 						verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
	// 						verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
	// 					}
	// 				}
	// 			}
	// 		}
	// 		else
	// 		{
	// 			if (specialdir == DIR_POS_Y)
	// 			{
	// 				for (int i = 0; i < vertex_size; i++)
	// 				{
	// 					BlockGeomVert* vert = &verts[i];
	// 					if ((vert->pos.y - origin_c[1]) > 50/*blockheight * 100*/)
	// 					{
	// 						vert->pos.y -= (100 - blockheight * 100);
	// 					}
	// 				}
	// 			}
	// 			else
	// 			{
	// 				for (int i = 0; i < vertex_size; i++)
	// 				{
	// 					if ((verts[i].pos.y - origin_c[1]) < 50/*blockheight * 100*/)
	// 					{
	// 						verts[i].pos.y += (100 + blockheight * 100);
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}
	mesh.vertices = verts;
	mesh.indices = *indices;
	if (!useround)
	{
		mesh.vertices.assign_range(mesh.vertices.begin(), mesh.vertices.begin() + 4);
	}
	return useround;
}

void SolidBlockMaterial::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;

	FaceVertexLight faceVertexLight;
	auto pblock = psection->getBlock(blockpos);
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		bool isNoClip = false;
		if (checkBlockMeshIsBuild(data, blockpos, d, isNoClip))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL) continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

			BlockGeomMeshInfo mesh;
			getBlockMeshAngleData(data, blockpos, mesh, d, psubmesh && !psubmesh->IsIgnoreTileUV(), pmtl, isNoClip);
			if (psubmesh)
			{
				psubmesh->addGeomFace(mesh, &blockpos,&facecolor);
			}
		}
	}
}


// void SolidBlockMaterial::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
// {
// 	auto psection = data.m_SharedSectionData;
// 
// 	FaceVertexLight faceVertexLight;
// 	auto pblock = psection->getBlock(blockpos);
// 	int curblockdata = pblock.getData();
// 
// 	float blockheight = getBlockHeight(curblockdata);
// 	DirectionType specialdir = DIR_NOT_INIT;
// 	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
// 	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;
// 
// 	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
// 	WCoord selfPos = psection->getOrigin() + blockpos;
// 
// 	auto thisBlock = psection->getBlock(blockpos);
// 
// // 	const float Z_UV[8] = { 0,1, 1,1, 1,0, 0,0 };
// // 	const float X_UV[8] = { 0,1, 0,0, 1,0, 1,1 };
// // 	const float Y_UV[8] = { 0,0, 0,1, 1,1, 1,0 };
// 	unsigned int avelt[16];
// 	for (int d = 0; d < 6; d++)
// 	{
// 		int useround = 0;
// 		DirectionType dir = (DirectionType)d;
// 
// 		int blockdata = 0;
// 		bool isBuild = true;
// 		bool isNoClip = false;
// 
// 		const float* texuv = BLOCKUV[dir];
// // 		int d_ = 0;
// // 		if (dir < DIR_NEG_Z)
// // 		{
// // // 			texuv = X_UV;
// // 			d_ = 0;
// // 		}
// // 		else if (dir > DIR_POS_Z)
// // 		{
// // // 			texuv = Y_UV;
// // 			d_ = 1;
// // 		}
// // 		else
// // 		{
// // 			d_ = 2;
// // 		}
// 		const char*origin_c/*[3]*/ = /*{ 0.f };*/ RoundBlockOriginC[dir];
// 		const WCoord *vNeighbor/*[8]*/ = RoundBlockNeighbor[dir];
// // 		int u_ = (d_ + 1) % 3;
// // 		int v_ = (d_ + 2) % 3;
// // 		int du[3] = { 0, 0, 0 };
// // 		int dv[3] = { 0, 0, 0 };
// // 		du[u_] = 1;
// // 		dv[v_] = 1;
// // 		if (dir % 2)
// // 			origin_c[d_] = 1;
// 
// // 		vNeighbor[0] = WCoord(-du[0], -du[1], -du[2]);
// // 		vNeighbor[1] = WCoord(du[0], du[1], du[2]);
// // 		vNeighbor[2] = WCoord(dv[0], dv[1], dv[2]);
// // 		vNeighbor[3] = WCoord(-dv[0], -dv[1], -dv[2]);
// // 		vNeighbor[4] = WCoord(-dv[0] + du[0], -dv[1] + du[1], -dv[2] + du[2]);
// // 		vNeighbor[5] = WCoord(-dv[0] - du[0], -dv[1] - du[1], -dv[2] - du[2]);
// // 		vNeighbor[6] = WCoord(dv[0] + du[0], dv[1] + du[1], dv[2] + du[2]);
// // 		vNeighbor[7] = WCoord(dv[0] - du[0], dv[1] - du[1], dv[2] - du[2]);
// 
// 		const char* du = DU[dir];
// 		const char* dv = DV[dir];
// 
// 		Block samesaidBlock;
// 		BlockMaterial* samesaidMtl = NULL;
// 		if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 		{
// 			WCoord sameDirPos = blockpos + g_DirectionCoord[d];
// 			if (psection->isValidPos(sameDirPos))
// 			{
// 				samesaidBlock = psection->getBlock(sameDirPos);
// 				samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
// 			}
// 		}
// 		else
// 		{
// 			WCoord sameDirPos = selfPos + g_DirectionCoord[d];
// 			if (data.m_World)
// 			{
// 				samesaidBlock = data.m_World->getBlock(sameDirPos);
// 				samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
// 			}
// 		}
// 		if (samesaidMtl)
// 		{
// 			//Ҫ���Ӱ�ש
// 			if (samesaidMtl->coverNeighbor(samesaidBlock.getData(), this, curblockdata, ReverseDirection(dir)))
// 			{
// 				isBuild = false;
// 				if (this->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab && (this->getBlockHeight(thisBlock.getData()) == 1 || (this->getBlockHeight(thisBlock.getData()) < 0 && dir == DIR_NEG_Y) || (this->getBlockHeight(thisBlock.getData()) > 0 && dir == DIR_POS_Y))
// 					|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_HorizontalHalfSlantBlock && (this->getBlockHeight(thisBlock.getData()) == 0.5f && dir == DIR_POS_Y || this->getBlockHeight(thisBlock.getData()) == -0.5f && dir == DIR_NEG_Y)
// 					|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalHalfSlantBlock && (this->getBlockHeight(thisBlock.getData()) == 0.5f)
// 					|| this->BlockTypeId() == BlockMaterial::BlockType::BlockType_VerticalSlab && (this->getBlockHeight(thisBlock.getData()) < 1.f))
// 				{
// 					isBuild = true;
// 					isNoClip = true;
// 				}
// 				else
// 				{
// 					bool t_[8] = { false };
// 					int count = 0;
// 					Block neighborBlock;
// 					BlockMaterial* neighborMtl = NULL;
// 					for (int i = 0; i < 4; i++)
// 					{
// 						WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
// 						if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 						{
// 							if (psection->isValidPos(diagonalPos))
// 							{
// 								neighborBlock = psection->getBlock(diagonalPos);
// 								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 							}
// 						}
// 						else
// 						{
// 							WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
// 							if (data.m_World)
// 							{
// 								neighborBlock = data.m_World->getBlock(sameDirPos);
// 								neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 							}
// 						}
// 
// 						if (neighborMtl)
// 						{
// 							if (neighborBlock.getResID() > 0 && isKeepRoundBlock(neighborMtl))
// 							{
// 								t_[i] = true;
// 								count++;
// 							}
// 						}
// 					}
// 					if (count <= 2)
// 					{
// 						for (int i = 4; i < 8; i++)
// 						{
// 							WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
// 							if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 							{
// 								if (psection->isValidPos(diagonalPos))
// 								{
// 									neighborBlock = psection->getBlock(diagonalPos);
// 									neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 								}
// 							}
// 							else
// 							{
// 								WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
// 								if (data.m_World)
// 								{
// 									neighborBlock = data.m_World->getBlock(sameDirPos);
// 									neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 								}
// 							}
// 
// 							if (neighborMtl)
// 							{
// 								if (neighborBlock.getResID() > 0 && isKeepRoundBlock(neighborMtl))
// 								{
// 									t_[i] = true;
// 								}
// 							}
// 						}
// 						bool t[8] = { false };
// 						for (int i = 0; i < 8; i++)
// 						{
// 							WCoord diagonalPos = blockpos + vNeighbor[i];
// 							if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 							{
// 								if (psection->isValidPos(diagonalPos))
// 								{
// 									neighborBlock = psection->getBlock(diagonalPos);
// 									neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 								}
// 							}
// 							else
// 							{
// 								WCoord sameDirPos = selfPos + vNeighbor[i];
// 								if (data.m_World)
// 								{
// 									neighborBlock = data.m_World->getBlock(sameDirPos);
// 									neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 								}
// 							}
// 
// 							if (neighborMtl)
// 							{
// 								if (neighborBlock.getResID() > 0 && isKeepRoundBlock(neighborMtl))
// 								{
// 									t[i] = true;
// 								}
// 							}
// 						}
// 
// 						if (t[0] && t[5] && t[3] && !t_[0] && !t_[5] && !t_[3])
// 						{
// 							isBuild = true;
// 						}
// 						else if (t[3] && t[4] && t[1] && !t_[3] && !t_[4] && !t_[1])
// 						{
// 							isBuild = true;
// 						}
// 						else if (t[1] && t[6] && t[2] && !t_[1] && !t_[6] && !t_[2])
// 						{
// 							isBuild = true;
// 						}
// 						else if (t[2] && t[7] && t[0] && !t_[2] && !t_[7] && !t_[0])
// 						{
// 							isBuild = true;
// 						}
// 					}
// 				}
// 			}
// 			else if (samesaidMtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab)
// 			{
// 				float samesaidHight = getBlockHeight(samesaidBlock.getData());
// 				if (this->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab && ((blockheight == samesaidHight && (blockheight == 1 || dir < DIR_NEG_Y)) || (dir == DIR_NEG_Y && blockheight > 0 && samesaidHight < 0) || (dir == DIR_POS_Y && blockheight < 0 && samesaidHight > 0)))
// 				{//��שֻ�Ͱ�ש�ϲ�
// 					isBuild = false;
// 				}
// 				else
// 				{
// 					isNoClip = true;
// 				}
// 			}
// 			else if (samesaidMtl->GetAttrRenderType() == this->GetAttrRenderType() && samesaidMtl->GetAttrRenderType() == BLOCKRENDER_SPECIAL_CUBE_MODEL)
// 			{
// 				isBuild = false;
// 			}
// 			else if (samesaidBlock.getResID() > 0 && isKeepRoundBlock(samesaidMtl))
// 			{//��שֻ�Ͱ�ש�ϲ�
// 				isNoClip = true;
// 			}
// 		}
// 
// 		if (isBuild)
// 		{
// 			const dynamic_array<UInt16>* indices = OrgBlockIndex[dir];
// // 			const dynamic_array<UInt16>* indices_round = (dir == DIR_NEG_X || dir == DIR_NEG_Y || dir == DIR_NEG_Z) ? &Pos_indices : &Neg_indices;
// 
// 			//WCoord sub = g_DirectionCoord[d] * anglesize;
// 			bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
// 
// 			BlockColor facecolor(255, 255, 255, 255);
// 			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
// 			if (pmtl == NULL) continue;
// 			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
// 
// 			BlockGeomMeshInfo mesh;
// 
// // 			if (getGeom() == NULL) continue;
// // 			getGeom()->getFaceVerts(mesh, dir, flipQuad);
// 
// 			dynamic_array<BlockGeomVert> verts;
// 			verts.resize_initialized(16, kIncreaseToExactSize);
// // 			verts = mesh.vertices;
// 
// 			unsigned short dir_color = Normal2LightColor(g_DirectionCoord[d].toVector3());
// 
// 			Vector3f normalVec = g_DirectionCoord[d].toVector3();
// 			normalVec.y = 1.0f;
// 			Rainbow::Normalize(normalVec);
// 			BlockVector normal_dir = PackVertNormal(normalVec, 0);
// 			BlockVector vertcolor;
// 			vertcolor.v = 0xffffffff;
// 			vertcolor.w = dir_color;
// 
// 			do
// 			{
// 				const float* uvtile = NULL;
// 				if (psubmesh && !psubmesh->IsIgnoreTileUV())
// 					uvtile = pmtl->getUVTile();
// 				InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
// 				InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
// 				InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
// 				InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
// 
// 				unsigned int lt_me = psection->getLight2(NeighborCoord(blockpos, dir), true);
// 				unsigned int src_lt_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[0], dir), true);   //&GetGrid(grids, u - 1, v);
// 				unsigned int src_ltu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[1], dir), true);    // &GetGrid(grids, u + 1, v);
// 				unsigned int src_ltv = psection->getLight2(NeighborCoord(blockpos + vNeighbor[2], dir), true);    // &GetGrid(grids, u, v + 1);
// 				unsigned int src_lt_v = psection->getLight2(NeighborCoord(blockpos + vNeighbor[3], dir), true);   // &GetGrid(grids, u, v - 1);
// 				unsigned int src_lt_vu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[4], dir), true);   // &GetGrid(grids, u + 1, v - 1);
// 				unsigned int src_lt_v_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[5], dir), true);   // &GetGrid(grids, u - 1, v - 1);
// 				unsigned int src_ltvu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[6], dir), true);    // &GetGrid(grids, u + 1, v + 1);
// 				unsigned int src_ltv_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[7], dir), true);    // &GetGrid(grids, u - 1, v + 1);
// 
// 				unsigned int block0 = psection->getLight2(blockpos + vNeighbor[0], true);   //&GetGrid(grids, u - 1, v);
// 				unsigned int block1 = psection->getLight2(blockpos + vNeighbor[1], true);    // &GetGrid(grids, u + 1, v);
// 				unsigned int block2 = psection->getLight2(blockpos + vNeighbor[2], true);    // &GetGrid(grids, u, v + 1);
// 				unsigned int block3 = psection->getLight2(blockpos + vNeighbor[3], true);   // &GetGrid(grids, u, v - 1);
// 				unsigned int block4 = psection->getLight2(blockpos + vNeighbor[4], true);   // &GetGrid(grids, u + 1, v - 1);
// 				unsigned int block5 = psection->getLight2(blockpos + vNeighbor[5], true);   // &GetGrid(grids, u - 1, v - 1);
// 				unsigned int block6 = psection->getLight2(blockpos + vNeighbor[6], true);    // &GetGrid(grids, u + 1, v + 1);
// 				unsigned int block7 = psection->getLight2(blockpos + vNeighbor[7], true);    // &GetGrid(grids, u - 1, v + 1);
// 
// 
// 				avelt[0] = ((lt_me + src_lt_u + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
// 				avelt[1] = ((lt_me + src_ltu + src_lt_vu + src_lt_v) >> 2) & 0xff00ff;
// 				avelt[2] = ((lt_me + src_ltv + src_ltvu + src_ltu) >> 2) & 0xff00ff;
// 				avelt[3] = ((lt_me + src_lt_u + src_ltv_u + src_ltv) >> 2) & 0xff00ff;
// 
// 				//-v  -v-u ������
// 				avelt[4] = ((block3 + block5 + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
// 				//-v  -v+u ������
// 				avelt[5] = ((block3 + block4 + src_lt_v + src_lt_vu) >> 2) & 0xff00ff;
// 				// v  v-u ������
// 				avelt[6] = ((block2 + block7 + src_ltv + src_ltv_u) >> 2) & 0xff00ff;
// 				// v  v+u ������
// 				avelt[7] = ((block2 + block6 + src_ltv + src_ltvu) >> 2) & 0xff00ff;
// 				// -u -v-u  ������
// 				avelt[8] = ((block0 + block5 + src_lt_u + src_lt_v_u) >> 2) & 0xff00ff;
// 				// u  -v+u ������
// 				avelt[9] = ((block1 + block4 + src_ltu + src_lt_vu) >> 2) & 0xff00ff;
// 				// -u v-u  ������
// 				avelt[10] = ((block0 + block7 + src_lt_u + src_ltv_u) >> 2) & 0xff00ff;
// 				// u v+u  ������
// 				avelt[11] = ((block1 + block6 + src_ltu + src_ltvu) >> 2) & 0xff00ff;
// 
// 
// 				for (int o = 0; o < 8; o++)
// 				{
// 					Block neighborBlock;
// 					BlockMaterial* neighborMtl = NULL;
// 					if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 					{
// 						if (psection->isValidPos(blockpos + vNeighbor[o]))
// 						{
// 							neighborBlock = psection->getBlock(blockpos + vNeighbor[o]);
// 							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 						}
// 					}
// 					else
// 					{
// 						WCoord sameDirPos = selfPos + vNeighbor[o];
// 						if (data.m_World)
// 						{
// 							neighborBlock = data.m_World->getBlock(sameDirPos);
// 							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 						}
// 					}
// 
// 					bool isEven = false;
// 					if (neighborMtl)
// 					{
// 						if (neighborBlock.getResID() > 0 && isKeepRoundBlock(neighborMtl))
// 						{
// 							isEven = true;
// 						}
// 					}
// 
// 					if (isEven)
// 					{
// 						if (0 == o)
// 						{// -u
// 							blockdata |= 1;
// 						}
// 						else if (1 == o)
// 						{// +u
// 							blockdata |= (1 << 1);
// 						}
// 						else if (2 == o)
// 						{// +v
// 							blockdata |= (1 << 2);
// 						}
// 						else if (3 == o)
// 						{// -v
// 							blockdata |= (1 << 3);
// 						}
// 						else if (4 == o)
// 						{// -v + u
// 							blockdata |= (1 << 4);
// 						}
// 						else if (5 == o)
// 						{// -v - u
// 							blockdata |= (1 << 5);
// 						}
// 						else if (6 == o)
// 						{// +v + u
// 							blockdata |= (1 << 6);
// 						}
// 						else if (7 == o)
// 						{// v - u
// 							blockdata |= (1 << 7);
// 						}
// 					}
// 				}
// 				if (blockdata == 0xff)
// 				{
// 					for (int i = 0; i < 4; i++)
// 					{
// 						InitBlockVertLight(verts[i], avelt[i], uvtile);
// 					}
// 					break;
// 				}
// 
// 				for (int o = 0; o < 8; o++)
// 				{
// 					Block neighborBlock;
// 					BlockMaterial* neighborMtl = NULL;
// 					WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[o], dir);
// 					if (psection && psection->getSectionType() == SECTION_VEHICLE)
// 					{
// 						if (psection->isValidPos(diagonalPos))
// 						{
// 							neighborBlock = psection->getBlock(diagonalPos);
// 							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 						}
// 					}
// 					else
// 					{
// 						WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[o], dir);
// 						if (data.m_World)
// 						{
// 							neighborBlock = data.m_World->getBlock(sameDirPos);
// 							neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
// 						}
// 					}
// 
// 					bool isEven = false;
// 					if (neighborMtl)
// 					{
// 						if (neighborBlock.getResID() > 0 && isKeepRoundBlock(neighborMtl))
// 						{
// 							isEven = true;
// 						}
// 					}
// 
// 					if (isEven)
// 					{
// 						if (0 == o)
// 						{// -u
// 							blockdata |= (1 << 8);
// 						}
// 						else if (1 == o)
// 						{// +u
// 							blockdata |= (1 << 9);
// 						}
// 						else if (2 == o)
// 						{// +v
// 							blockdata |= (1 << 10);
// 						}
// 						else if (3 == o)
// 						{// -v
// 							blockdata |= (1 << 11);
// 						}
// 						else if (4 == o)
// 						{// -v + u
// 							blockdata |= (1 << 12);
// 						}
// 						else if (5 == o)
// 						{// -v - u
// 							blockdata |= (1 << 13);
// 						}
// 						else if (6 == o)
// 						{// +v + u
// 							blockdata |= (1 << 14);
// 						}
// 						else if (7 == o)
// 						{// v - u
// 							blockdata |= (1 << 15);
// 						}
// 					}
// 				}
// 				if (isNoClip)
// 				{
// 					blockdata |= (1 << 16);
// 				}
// 				useround = 16;
// 
// 				const vertex_cache* cache = Section::getVertexCache(NULL, dir, blockdata);
// 
// 				if (cache)
// 				{
// 					const auto& find = *cache;
// // 					indices_round = &find.index;
// 					int vertex_size = find.vertex_size;
// 					useround = find.vertex_size | ((find.index_size) << 8);
// 					BlockVertUV uv_[16];
// 					uv_[0].x = verts[0].uv.x, uv_[0].y = verts[0].uv.y;
// 					uv_[1].x = verts[1].uv.x, uv_[1].y = verts[1].uv.y;
// 					uv_[2].x = verts[2].uv.x, uv_[2].y = verts[2].uv.y;
// 					uv_[3].x = verts[3].uv.x, uv_[3].y = verts[3].uv.y;
// 					uv_[4].x = uv_[0].x + (uv_[1].x - uv_[0].x) * ROUND_DIFF; uv_[4].y = uv_[0].y + (uv_[1].y - uv_[0].y) * ROUND_DIFF;
// 					uv_[5].x = uv_[0].x + (uv_[1].x - uv_[0].x) * (1 - ROUND_DIFF); uv_[5].y = uv_[0].y + (uv_[1].y - uv_[0].y) * (1 - ROUND_DIFF);
// 					uv_[6].x = uv_[1].x + (uv_[2].x - uv_[1].x) * ROUND_DIFF; uv_[6].y = uv_[1].y + (uv_[2].y - uv_[1].y) * ROUND_DIFF;
// 					uv_[7].x = uv_[1].x + (uv_[2].x - uv_[1].x) * (1 - ROUND_DIFF); uv_[7].y = uv_[1].y + (uv_[2].y - uv_[1].y) * (1 - ROUND_DIFF);
// 					uv_[8].x = uv_[2].x + (uv_[3].x - uv_[2].x) * ROUND_DIFF; uv_[8].y = uv_[2].y + (uv_[3].y - uv_[2].y) * ROUND_DIFF;
// 					uv_[9].x = uv_[2].x + (uv_[3].x - uv_[2].x) * (1 - ROUND_DIFF); uv_[9].y = uv_[2].y + (uv_[3].y - uv_[2].y) * (1 - ROUND_DIFF);
// 					uv_[10].x = uv_[3].x + (uv_[0].x - uv_[3].x) * ROUND_DIFF; uv_[10].y = uv_[3].y + (uv_[0].y - uv_[3].y) * ROUND_DIFF;
// 					uv_[11].x = uv_[3].x + (uv_[0].x - uv_[3].x) * (1 - ROUND_DIFF); uv_[11].y = uv_[3].y + (uv_[0].y - uv_[3].y) * (1 - ROUND_DIFF);
// 					uv_[12].x = uv_[11].x + (uv_[6].x - uv_[11].x) * ROUND_DIFF; uv_[12].y = uv_[11].y + (uv_[6].y - uv_[11].y) * ROUND_DIFF;
// 					uv_[13].x = uv_[11].x + (uv_[6].x - uv_[11].x) * (1 - ROUND_DIFF); uv_[13].y = uv_[11].y + (uv_[6].y - uv_[11].y) * (1 - ROUND_DIFF);
// 					uv_[14].x = uv_[10].x + (uv_[7].x - uv_[10].x) * (1 - ROUND_DIFF); uv_[14].y = uv_[10].y + (uv_[7].y - uv_[10].y) * (1 - ROUND_DIFF);
// 					uv_[15].x = uv_[10].x + (uv_[7].x - uv_[10].x) * ROUND_DIFF; uv_[15].y = uv_[10].y + (uv_[7].y - uv_[10].y) * ROUND_DIFF;
// 					for (int i = 0; i < vertex_size; i++)
// 					{
// 						auto& vertex_temp = find.base[i];
// 						verts[i].color = vertex_temp.color;
// 						verts[i].color.r = vertcolor.x;
// 						verts[i].color.g = vertcolor.y;
// 						verts[i].color.b = vertcolor.z;
// 						verts[i].normal = vertex_temp.normal;
// 						verts[i].uv.x = uv_[vertex_temp.uv_index].x;
// 						verts[i].uv.y = uv_[vertex_temp.uv_index].y;
// 						verts[i].pos.x = vertex_temp.pos.x + (origin_c[0] - origin_zero[0]) * BLOCK_SIZE;
// 						verts[i].pos.y = vertex_temp.pos.y + (origin_c[1] - origin_zero[1]) * BLOCK_SIZE;
// 						verts[i].pos.z = vertex_temp.pos.z + (origin_c[2] - origin_zero[2]) * BLOCK_SIZE;
// 						int avelt_temp = 0;
// 						if (vertex_temp.avelt_size == 1)
// 						{
// 							avelt_temp = avelt[vertex_temp.avelt_index_0];
// 						}
// 						else if (vertex_temp.avelt_size == 2)
// 						{
// 							avelt_temp = (avelt[vertex_temp.avelt_index_0] + avelt[vertex_temp.avelt_index_1]) >> 1;
// 						}
// 						else
// 						{
// 							int lt1 = (((avelt[vertex_temp.avelt_index_0] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 4) & 0xf)) / 3;
// 							int lt2 = (((avelt[vertex_temp.avelt_index_0] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 20) & 0xf)) / 3;
// 							avelt_temp = (lt1 << 4) + (lt2 << 20);
// 						}
// 						InitBlockVertLight(verts[i], avelt_temp, uvtile);
// 					}
// // 					mesh.vertices = verts;
// 					indices = &find.index;
// 				}
// 			} while (0);
// 
// 			if (1.f > blockheight)
// 			{
// 				if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock)
// 				{
// 					int blockDir = curblockdata & 3;
// 
// 					if (blockDir == DIR_POS_Z || blockDir == DIR_POS_X)
// 					{
// 						for (int i = 0; i < 16; i++)
// 						{
// 							if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) > 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) > 50)
// 							{
// 								verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
// 								verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
// 							}
// 						}
// 					}
// 					else
// 					{
// 						for (int i = 0; i < 16; i++)
// 						{
// 							if (g_DirectionCoord[blockDir].z && (verts[i].pos.z - origin_c[2]) < 50 || g_DirectionCoord[blockDir].x && (verts[i].pos.x - origin_c[0]) < 50)
// 							{
// 								verts[i].pos.z -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].z;
// 								verts[i].pos.x -= (100 - blockheight * 100) * g_DirectionCoord[blockDir].x;
// 							}
// 						}
// 					}
// 				}
// 				else
// 				{
// 					if (specialdir == DIR_POS_Y)
// 					{
// 						for (int i = 0; i < 16; i++)
// 						{
// 							BlockGeomVert* vert = &verts[i];
// 							if ((vert->pos.y - origin_c[1]) > 50/*blockheight * 100*/)
// 							{
// 								vert->pos.y -= (100 - blockheight * 100);
// 							}
// 						}
// 					}
// 					else
// 					{
// 						for (int i = 0; i < 16; i++)
// 						{
// 							if ((verts[i].pos.y - origin_c[1]) < 50/*blockheight * 100*/)
// 							{
// 								verts[i].pos.y += (100 + blockheight * 100);
// 							}
// 						}
// 					}
// 				}
// 			}
// 
// 			if (psubmesh)
// 			{
// 				if (!useround)
// 				{
// 					mesh.vertices = verts;
// 					mesh.vertices.assign_range(mesh.vertices.begin(), mesh.vertices.begin() + 4);
// 					mesh.indices = *indices;
// 					psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
// 				}
// 				else
// 				{
// 					mesh.vertices = verts;
// 					mesh.indices = *indices;
// 					psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
// 				}
// 			}
// 		}
// 	}
// }

void SolidBlockMaterial::createBlockMeshForCustomModel(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
#ifndef IWORLD_SERVER_BUILD
	if (isUseCustomModel())
	{
		BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
		if (!geom) return;
		auto psection = data.m_SharedSectionData;
		Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
		psection->getBlockVertexLight(blockpos, verts_light);

		int idbuf[32];
		int dirbuf[32];
		int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
		BlockGeomMeshInfo meshinfo;
		RenderBlockMaterial* pmtl = getDefaultMtl(); //getGeomMtl(psection, blockpos);
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl, false);
		Block pblock = psection->getBlock(blockpos);

		for (int i = 0; i < ngeom; i++)
		{
			if (!geom)
				break;

			int dir = dirbuf[i] & 0xffff;
			int mirrortype = (dirbuf[i] >> 16) & 3;

			if (mirrortype > 0) geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype);
			else geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir);

			if (isColorableBlock())
			{
				BlockColor bv = getBlockColor(pblock.getData());
				bv.a = 0;
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl->getUVTile());
			}
			else
			{
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
			}
		}
	}
#endif
}

void SolidBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	
#ifndef IWORLD_SERVER_BUILD
	if (isUseCustomModel())
	{
		createBlockMeshForCustomModel(data, blockpos, poutmesh);
	}
	else
	{
		auto psection = data.m_SharedSectionData;
		if (0 == BlockMaterialMgr::m_BlockShape)
		{
			bool isRound = !poutmesh->isSquareSectionMesh();
			if (isRound)
			{
				return createBlockMeshAngle(data, blockpos, poutmesh);
			}
		}
		BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
		if (!geom)
			return;

		FaceVertexLight faceVertexLight;
		//float block_light[16] = { 0 };
		Block pblock = psection->getBlock(blockpos);
		int curblockdata = pblock.getData();

		float blockheight = getBlockHeight(curblockdata);
		DirectionType specialdir = DIR_NOT_INIT;
		if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
		else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

		const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(BIOME_TYPE::BIOME_PLAINS);

		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;

			if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
			{
				bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

				BlockColor facecolor(255, 255, 255, 0);
				RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				if (pmtl == NULL)
					continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

				BlockGeomMeshInfo mesh;

				//if (geom == NULL) continue;
				if (blockheight == 1.0f) geom->getFaceVerts(mesh, dir, flipQuad);
				else geom->getFaceVerts(mesh, dir, blockheight, 1, DIR_NEG_Z, 0, nullptr, 0, flipQuad);


				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}
	}
#endif
}

void SolidBlockMaterial::getAvelt(const BuildSectionMeshData& data, const WCoord& blockpos, DirectionType dir, unsigned int* avelt)
{
	auto psection = data.m_SharedSectionData;
	const WCoord* vNeighbor/*[8]*/ = Rainbow::RoundBlockNeighbor[dir];
	unsigned int lt_me = psection->getLight2(NeighborCoord(blockpos, dir), true);
	unsigned int src_lt_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[0], dir), true);   //&GetGrid(grids, u - 1, v);
	unsigned int src_ltu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[1], dir), true);    // &GetGrid(grids, u + 1, v);
	unsigned int src_ltv = psection->getLight2(NeighborCoord(blockpos + vNeighbor[2], dir), true);    // &GetGrid(grids, u, v + 1);
	unsigned int src_lt_v = psection->getLight2(NeighborCoord(blockpos + vNeighbor[3], dir), true);   // &GetGrid(grids, u, v - 1);
	unsigned int src_lt_vu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[4], dir), true);   // &GetGrid(grids, u + 1, v - 1);
	unsigned int src_lt_v_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[5], dir), true);   // &GetGrid(grids, u - 1, v - 1);
	unsigned int src_ltvu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[6], dir), true);    // &GetGrid(grids, u + 1, v + 1);
	unsigned int src_ltv_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[7], dir), true);    // &GetGrid(grids, u - 1, v + 1);

	avelt[0] = ((lt_me + src_lt_u + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
	avelt[1] = ((lt_me + src_ltu + src_lt_vu + src_lt_v) >> 2) & 0xff00ff;
	avelt[2] = ((lt_me + src_ltv + src_ltvu + src_ltu) >> 2) & 0xff00ff;
	avelt[3] = ((lt_me + src_lt_u + src_ltv_u + src_ltv) >> 2) & 0xff00ff;
}

float SolidBlockMaterial::getBlockHeight(int blockdata)
{
	return 1.0f;
}

void SolidBlockMaterial::setCausticsEnable(bool value, float power)
{
	for (int i = 0; i < 6; i++)
	{
		RenderBlockMaterial* m = getRenderMtlMgr().getMtl(i);
		if (m && i == DIR_NEG_Y)
		{
			m->setCausticsEnable(false, power);
		}

		//RenderBlockMaterial* m = m_Mtls[i];
		if (m)
		{
			m->setCausticsEnable(value, power);
		}
	}
}

bool  SolidBlockMaterial::getEnableLightwave()
{
	return m_bOpenlightwave;
}



bool SolidBlockMaterial::CombineVertex()
{
	if (m_HasEmissiveTex)
	{
		return false;
	}
	BlockDrawType drawType = this->getDrawType();
	if (drawType == BLOCKDRAW_OPAQUE || drawType == BLOCKDRAW_GRASS) return true;
	return false;
}

/*
Material *SolidBlockMaterial::insertItemMtl(Texture *ptex, BlendMode blend, bool gray)
{
	for(size_t i=0; i<m_ItemMtls.size(); i++)
	{
		ItemMaterial &mtl = m_ItemMtls[i];
		if(mtl.blend==blend && mtl.ptex==ptex && mtl.gray==gray)
		{
			return mtl.pmtl;
		}
	}

	static ColourValue white(1.0f, 1.0f, 1.0f);
	static ColourValue green(0.47f, 0.78f, 0.47f);

	ItemMaterial tmp;
	tmp.gray = gray;
	tmp.blend = blend;
	tmp.ptex = ptex;
	tmp.pmtl = ENG_NEW(Material)("blockitem");
	tmp.pmtl->setParamMacro("BLEND_MODE", blend);
	tmp.pmtl->setParamMacro("USE_TEXTURE", 1);

	//LOG_INFO("m_Def->SampleMode, %d", m_Def->SampleMode);
	tmp.pmtl->setParamTexture("g_DiffuseTex", ptex, m_Def->SampleMode);

	if(gray) tmp.pmtl->setParamValue("GrassColor", &green);
	else tmp.pmtl->setParamValue("GrassColor", &white);

	m_ItemMtls.push_back(tmp);

	return tmp.pmtl;
}*/

SectionMesh* SolidBlockMaterial::createBlockProtoMesh(int protodata)
{
	if (isUseCustomModel())
	{
		if (getGeom(0) == NULL) return NULL;
		SectionMesh* pmesh = ENG_NEW(SectionMesh)();
		SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

		BlockGeomMeshInfo meshinfo;

		int idbuf[32];
		int dirbuf[32];
		int ngeom = getProtoBlockGeomID(idbuf, dirbuf);
		for (int i = 0; i < ngeom; i++)
		{
			getGeom(0)->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dirbuf[i]);

			if (isColorableBlock())
			{
				BlockColor bv = getBlockColor(protodata);
				if (psubmesh) psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &bv);
			}
			else
			{
				if (psubmesh)
					psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL);
			}
		}


		return pmesh;
	}
	else
	{
		if (1 == BlockMaterialMgr::m_BlockShape)
		{
			SectionMesh* pmesh = ENG_NEW(SectionMesh)();
			int blockdata = protodata;

			float blockheight = getBlockHeight(protodata);

			const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

			for (int d = 0; d < 6; d++)
			{
				DirectionType dir = (DirectionType)d;

				BlockColor facecolor(255, 255, 255, 0);
				RenderBlockMaterial* rbmtl = getFaceMtl(biome, dir, blockdata, facecolor);
				if (!rbmtl)
				{
					continue;
				}
				//bool needbiomecolor = desc.gray;

				SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);
				//psubmesh->m_NeedBiomeColor = needbiomecolor;

				BlockGeomMeshInfo meshinfo;

				if (getGeom(0) == NULL) continue;
				if (blockheight == 1.0f) getGeom(0)->getFaceVerts(meshinfo, dir);
				else
				{
					getGeom(0)->getFaceVerts(meshinfo, dir, blockheight, 1);
				}

				if (psubmesh)
					psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
			}

			return pmesh;
		}
		else
		{
			return createBlockProtoMeshAngle(protodata);
		}
	}
}

SectionMesh* SolidBlockMaterial::createBlockProtoMeshAngle(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	int blockdata = protodata;
	float blockheight = getBlockHeight(protodata);
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

	// 	int anglesize = RoundAngleSize;
	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;

		BlockColor facecolor(255, 255, 255, 0);
		RenderBlockMaterial* rbmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		if (rbmtl == NULL) continue;
		SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);

		BlockGeomMeshInfo mesh;
		dynamic_array<BlockGeomVert> verts;
		verts.resize_initialized(16, kIncreaseToExactSize);
		const char* origin_c/*[3]*/ = RoundBlockOriginC[dir];
		WCoord ndir = dir >= DIR_NEG_Z ? g_DirectionCoord[dir] : g_DirectionCoord[dir] + WCoord(0, 1, 0);
		//unsigned short dir_color = Normal2LightColor(ndir.toVector3());

		Rainbow::Vector3f normalVec = ndir.toVector3();
		normalVec.Normalize();
		BlockVector normal_dir = PackVertNormal(normalVec, 1);
		BlockColor vertcolor1;
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		// 		const float Z_UV[8] = { 0,1, 1,1, 1,0, 0,0 };
		// 		const float X_UV[8] = { 0,1, 0,0, 1,0, 1,1 };
		// 		const float Y_UV[8] = { 0,0, 0,1, 1,1, 1,0 };
		// 		const float* texuv = Z_UV;
		// 		int d_ = 0;
		// 		if (dir == DIR_NEG_X || dir == DIR_POS_X)
		// 		{
		// 			texuv = X_UV;
		// 			d_ = 0;
		// 		}
		// 		else if (dir == DIR_NEG_Y || dir == DIR_POS_Y)
		// 		{
		// 			texuv = Y_UV;
		// 			d_ = 1;
		// 		}
		// 		else
		// 		{
		// 			d_ = 2;
		// 		}

		const char* du = Rainbow::DU[dir];
		const char* dv = Rainbow::DV[dir];
		const float* texuv = Rainbow::BLOCKUV[dir];
		// 		WCoord vNeighbor[8];
		// 		int u_ = (d_ + 1) % 3;
		// 		int v_ = (d_ + 2) % 3;
		// 		int du[3] = { 0, 0, 0 };
		// 		int dv[3] = { 0, 0, 0 };
		// 		du[u_] = 1;
		// 		dv[v_] = 1;
		// 		if (dir % 2)
		// 			origin_c[d_] = 1;
		if (IsSanrioBlock(m_BlockResID))
		{
			const float Z_FLIP[8] = { 1, 1, 0, 1, 0, 0, 1, 0 };
			const float X_FLIP[8] = { 1, 1, 1, 0, 0, 0, 0, 1 };
			const float Y_FLIP[8] = { 1, 0, 1, 1,0, 1, 0, 0 };
			if (dir == DIR_POS_Z)
			{
				texuv = Z_FLIP;
			}
			else if (dir == DIR_POS_Y)
			{
				texuv = Y_FLIP;
			}
			else if (dir == DIR_POS_X || dir == DIR_NEG_X)
			{
				texuv = X_FLIP;
			}
		}
		do
		{
			float uvtile[4];
			uvtile[0] = 0;
			uvtile[1] = 0;
			uvtile[2] = 1.0f;
			uvtile[3] = 1.0f;
			InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, NULL, 255, normal_dir);
			InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, NULL, 255, normal_dir);
			InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, NULL, 255, normal_dir);
			InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, NULL, 255, normal_dir);
			int blockdata_ = 0;

			unsigned char hor_ver = 6;
			if (blockheight < 1.f)
			{
				if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock) hor_ver = 0;
			}
			const vertex_cache* cache = Section::getVertexCacheEx(blockheight, NULL, dir, blockdata_, 0, hor_ver);
			// 			const vertex_cache* cache = Section::getVertexCache(NULL, dir, blockdata_);
			if (cache)
			{
				const auto& find = *cache;
				int vertex_size = find.vertex_size;
				BlockVertUV uv_[16];
				uv_[0].x = verts[0].uv.x, uv_[0].y = verts[0].uv.y;
				uv_[1].x = verts[1].uv.x, uv_[1].y = verts[1].uv.y;
				uv_[2].x = verts[2].uv.x, uv_[2].y = verts[2].uv.y;
				uv_[3].x = verts[3].uv.x, uv_[3].y = verts[3].uv.y;
				uv_[4].x = uv_[0].x + (uv_[1].x - uv_[0].x) * ROUND_DIFF; uv_[4].y = uv_[0].y + (uv_[1].y - uv_[0].y) * ROUND_DIFF;
				uv_[5].x = uv_[0].x + (uv_[1].x - uv_[0].x) * (1 - ROUND_DIFF); uv_[5].y = uv_[0].y + (uv_[1].y - uv_[0].y) * (1 - ROUND_DIFF);
				uv_[6].x = uv_[1].x + (uv_[2].x - uv_[1].x) * ROUND_DIFF; uv_[6].y = uv_[1].y + (uv_[2].y - uv_[1].y) * ROUND_DIFF;
				uv_[7].x = uv_[1].x + (uv_[2].x - uv_[1].x) * (1 - ROUND_DIFF); uv_[7].y = uv_[1].y + (uv_[2].y - uv_[1].y) * (1 - ROUND_DIFF);
				uv_[8].x = uv_[2].x + (uv_[3].x - uv_[2].x) * ROUND_DIFF; uv_[8].y = uv_[2].y + (uv_[3].y - uv_[2].y) * ROUND_DIFF;
				uv_[9].x = uv_[2].x + (uv_[3].x - uv_[2].x) * (1 - ROUND_DIFF); uv_[9].y = uv_[2].y + (uv_[3].y - uv_[2].y) * (1 - ROUND_DIFF);
				uv_[10].x = uv_[3].x + (uv_[0].x - uv_[3].x) * ROUND_DIFF; uv_[10].y = uv_[3].y + (uv_[0].y - uv_[3].y) * ROUND_DIFF;
				uv_[11].x = uv_[3].x + (uv_[0].x - uv_[3].x) * (1 - ROUND_DIFF); uv_[11].y = uv_[3].y + (uv_[0].y - uv_[3].y) * (1 - ROUND_DIFF);
				uv_[12].x = uv_[11].x + (uv_[6].x - uv_[11].x) * ROUND_DIFF; uv_[12].y = uv_[11].y + (uv_[6].y - uv_[11].y) * ROUND_DIFF;
				uv_[13].x = uv_[11].x + (uv_[6].x - uv_[11].x) * (1 - ROUND_DIFF); uv_[13].y = uv_[11].y + (uv_[6].y - uv_[11].y) * (1 - ROUND_DIFF);
				uv_[14].x = uv_[10].x + (uv_[7].x - uv_[10].x) * (1 - ROUND_DIFF); uv_[14].y = uv_[10].y + (uv_[7].y - uv_[10].y) * (1 - ROUND_DIFF);
				uv_[15].x = uv_[10].x + (uv_[7].x - uv_[10].x) * ROUND_DIFF; uv_[15].y = uv_[10].y + (uv_[7].y - uv_[10].y) * ROUND_DIFF;
				for (int i = 0; i < vertex_size; i++)
				{
					auto& vertex_temp = find.base[i];
					verts[i].color = vertex_temp.color;
					verts[i].color.r = vertcolor.x;
					verts[i].color.g = vertcolor.y;
					verts[i].color.b = vertcolor.z;
					verts[i].normal = vertex_temp.normal;
					verts[i].uv.x = uv_[vertex_temp.uv_index].x;
					verts[i].uv.y = uv_[vertex_temp.uv_index].y;
					verts[i].pos.x = vertex_temp.pos.x + (origin_c[0] - origin_zero[0]) * BLOCK_SIZE;
					verts[i].pos.y = vertex_temp.pos.y + (origin_c[1] - origin_zero[1]) * BLOCK_SIZE;
					verts[i].pos.z = vertex_temp.pos.z + (origin_c[2] - origin_zero[2]) * BLOCK_SIZE;
					InitBlockVertLight(verts[i], 255, uvtile);
				}

				// 				if (1.f > blockheight)
				// 				{
				// 					if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab)
				// 					{
				// 						for (int i = 0; i < 16; i++)
				// 						{
				// 							if ((verts[i].pos.x - origin_c[0]) < 50)
				// 							{
				// 								verts[i].pos.x += (100 - blockheight * 100);
				// 							}
				// 						}
				// 					}
				// 					else
				// 					{
				// 						for (int i = 0; i < 16; i++)
				// 						{
				// 							if ((verts[i].pos.y - origin_c[1]) > blockheight * 100)
				// 							{
				// 								verts[i].pos.y -= (100 - blockheight * 100);
				// 							}
				// 						}
				// 					}
				// 				}
				mesh.vertices = verts;
				mesh.indices = find.index;
			}
			else
			{
				return NULL;
			}


		} while (0);


		if (psubmesh) psubmesh->addGeomFaceLight(mesh, NULL, s_DefaultFaceVertexLights, &facecolor);
	}
	return pmesh;
}

int SolidBlockMaterial::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	Block pblock = sectionData->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = pblock.getData() % 4;

	return 1;
}

int SolidBlockMaterial::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;

	return 1;
}

void SolidBlockMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));

	WCoord pos = blockpos * BLOCK_SIZE;
	if (blockheight >= 0)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, int(BLOCK_SIZE * blockheight), BLOCK_SIZE));
	}
	else
	{
		coldetect->addObstacle(pos + WCoord(0, int(BLOCK_SIZE * (1.0f + blockheight)), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
}

/*
BlockMaterial::COVER_TYPE SolidBlockMaterial::coverNeighbor(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	Block pblock = pworld->getBlock(blockpos);
	float blockheight = getBlockHeight(pblock.getData());

	if(blockheight==1.0f || blockheight==-1.0f) return COVER_ALL;
	else if(blockheight > 0)
	{
		if(dir == DIR_NEG_Y) return COVER_ALL;
		else
		{
			return COVER_HALF_NEGY;
		}
	}
	else if(blockheight < 0)
	{
		if(dir == DIR_POS_Y) return COVER_ALL;
		else
		{
			return COVER_HALF_POSY;
		}
	}

	return COVER_NONE;
}*/

bool SolidBlockMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	if (isUseCustomModel())
	{
		return false;
	}


	if (dir == DIR_POS_Y)
	{
		if (neighbor && (neighbor->getBlockHeight(neighbor_data)) < 0) return false;
		float h = getBlockHeight(curblockdata);
		return h == 1.0f || h < 0.0f;
	}
	else if (dir == DIR_NEG_Y)
	{
		if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID) return false;
		float h = 0.f;
		if (neighbor) {
			h = neighbor->getBlockHeight(neighbor_data);
		}
		if (h >= 0 && h < 1.0f) return false;
		return getBlockHeight(curblockdata) > 0;
	}
	else
	{
		float h = getBlockHeight(curblockdata);
		if (h == 1.0f) return true;

		float nh = 0.f;
		if (neighbor) {
			nh = neighbor->getBlockHeight(neighbor_data);
		}
		if (nh >= 0) return h >= nh;
		else return h <= nh;
	}
}


bool SolidBlockMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	return getBlockResID() >= 200;
}

//--------------------------------------------------------------------------------------------------------
void SolidBlockMaterial::ColorTmpSet(const Rainbow::ColorQuad& value)
{
	Rainbow::ColorQuad& inValue = const_cast<Rainbow::ColorQuad &>(value);
	__BlockVectorSet(inValue, m_solidColor, true);

	m_bColorModify = true;
	m_notifyRefreshChunk();
	OnAttributeChanged(this, &R_TmpColor);
}
bool SolidBlockMaterial::ColorTmpGet(Rainbow::ColorQuad& value) const
{
	BlockVector& inColor = const_cast<BlockVector&>(m_solidColor);
	__BlockVectorSet(value, inColor, false);
	return true;
}

void SolidBlockMaterial::ColorSet(const Rainbow::ColorQuad& value)
{
	if (m_bColorModify)
	{
		Rainbow::ColorQuad& inValue = const_cast<Rainbow::ColorQuad &>(value);
		__BlockVectorSet(inValue, m_solidColor, true);
		OnAttributeChanged(this, &R_Color);
	}
}
bool SolidBlockMaterial::ColorGet(Rainbow::ColorQuad& value) const
{
	if (m_bColorModify)
	{
		BlockVector& inColor = const_cast<BlockVector&>(m_solidColor);
		__BlockVectorSet(value, inColor, false);
		return true;
	}
	value = Rainbow::ColorQuad(0,0,0,0);
	return true;
}


//--------------------------------------------------------------------------------------------------------
CubeBlockMaterial::CubeBlockMaterial()
{
	//for (int i = 0; i < 6; i++)
	//{
	//	m_Mtls[i] = NULL;
	//}
	memset(m_mtlsIndex, UINT_MAX, sizeof(m_mtlsIndex));
}

CubeBlockMaterial::~CubeBlockMaterial()
{
	//for (int i = 0; i < 6; i++)
	//{
	//	ENG_RELEASE(m_Mtls[i]);
	//}
	memset(m_mtlsIndex, UINT_MAX, sizeof(m_mtlsIndex));
}

void CubeBlockMaterial::init(int resid)
{
	Super::init(resid);
}


void CubeBlockMaterial::setCausticsEnable(bool value, float power)
{
	for (int i = 0; i < 6; i++)
	{
		if (i == DIR_NEG_Y)
		{
			continue;
		}
		RenderBlockMaterial*  m = getRenderMtlMgr().getMtl(i);
		//RenderBlockMaterial* m = m_Mtls[i];
		if (m)
		{
			m->setCausticsEnable(value, power);
		}
	}

}


RenderBlockMaterial* CubeBlockMaterial::getCubeFaceMtl(int dir)
{
	return getRenderMtlMgr().getMtl(m_mtlsIndex[dir]);
}

bool CubeBlockMaterial::setFaceMtl(DirectionType dir, RenderBlockMaterial* rbmtl)
{
	// ����Ѿ�ָ���˲��ʣ�����ʹ��
	RenderBlockMaterial* defMtl = getDefaultMtl();
	if (!m_mtlRenderKey[dir].empty() && defMtl)
	{
		int gettextype = defMtl->GetTexType();
		BlockDrawType drawtype = (BlockDrawType)defMtl->GetDrawType();
		int mipmethod = defMtl->GetMipMethod();
		RenderBlockMaterial* mtl = nullptr;

		if (m_mtlsIndex[dir] == UINT_MAX)
		{
			// ��ʼ��
			mtl = g_BlockMtlMgr.createRenderMaterial(m_mtlRenderKey[dir].c_str(), GetBlockDef(), gettextype, drawtype, mipmethod);
			m_mtlsIndex[dir] = getRenderMtlMgr().addMtl(mtl);
			ENG_RELEASE(mtl);
			return true;
		}
		else if (mtl = getRenderMtlMgr().getMtl(m_mtlsIndex[dir]))
		{
			if (m_mtlRenderKey[dir] == mtl->getName())
				return false;

			// �滻
			mtl = g_BlockMtlMgr.createRenderMaterial(m_mtlRenderKey[dir].c_str(), GetBlockDef(), gettextype, drawtype, mipmethod);
			getRenderMtlMgr().changeMtl(m_mtlsIndex[dir], mtl);
			ENG_RELEASE(mtl);
			return true;
		}
	}

	if (!rbmtl)
		return false;

	if (m_mtlsIndex[dir] != UINT_MAX)
	{
		//getRenderMtlMgr().removeMtl(m_mtlsIndex[dir]);
		//m_mtlsIndex[dir] = getRenderMtlMgr().addMtl(rbmtl);
		getRenderMtlMgr().changeMtl(m_mtlsIndex[dir], rbmtl);
	}
	else
	{
		m_mtlsIndex[dir] = getRenderMtlMgr().addMtl(rbmtl);
	}
	return true;
}

RenderBlockMaterial* CubeBlockMaterial::getFaceMtl(const BiomeDef* biome, DirectionType dir, int blockdata, BlockColor& facecolor)
{
	if (isColorableBlock())
	{
		assert(blockdata >= 0 && blockdata < 16);
		facecolor = m_Colors[blockdata]; //!!!
		//int color = m_Colors[blockdata];
		//Rainbow::ColorQuad cq(color);
		//Rainbow::Swap(cq.r, cq.b);
		//vertcolor = *(BlockColor *)&cq;
	}
	return getRenderMtlMgr().getMtl(m_mtlsIndex[dir]);//m_Mtls[dir];
}

void CubeBlockMaterial::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho)
{
	//20211020 �����ƻ���֪ͨ���������ͼ codeby:�¹�ǿ
	GetISandboxActorSubsystem()->CubeBlockDestroyedBy(pworld, blockpos);	
}

BlockTexElement* CubeBlockMaterial::getDestroyTexture(Block pblock, BlockTexDesc& desc)
{
	desc.gray = false;
	desc.blendmode = BLEND_OPAQUE;
	auto mtl = getDefaultMtl();
	if (isUseCustomModel() && mtl)
		return mtl->getTexElement();

	//if (m_Mtls[DIR_NEG_X])
	//	return m_Mtls[DIR_NEG_X]->getTexElement();
	mtl = getRenderMtlMgr().getMtl(m_mtlsIndex[DIR_NEG_X]);
	if (mtl)
	{
		return mtl->getTexElement();
	}

	return NULL;
}



bool CubeBlockMaterial::__RenderMtlGet(std::string& value, int index) const
{
	if (!m_mtlRenderKey[index].empty())
	{
		value = m_mtlRenderKey[index];
		return true;
	}

	auto mtl = getRenderMtlMgr().getMtl(m_mtlsIndex[index]);
	if (mtl)
	{
		value = mtl->getName();
		return true;
	}
	return false;
}
void CubeBlockMaterial::__RenderMtlSet(const std::string& value, int index, MNSandbox::ReflexValue* refval)
{
	if (value == m_mtlRenderKey[index])
		return;

	m_mtlRenderKey[index] = value;
	if (setFaceMtl((DirectionType)index, nullptr))
	{
		m_notifyRefreshChunk.Emit();
	}
	OnAttributeChanged(this, refval);
}

//---------------------------------------------------------------------------------------------------------------------
ModelBlockMaterial::ModelBlockMaterial() :/* m_Mtl(NULL),*/ m_baseTexNeed4BitAlpha(false)
{

}

ModelBlockMaterial::~ModelBlockMaterial()
{
	//ENG_RELEASE(m_Mtl);
}

const char* ModelBlockMaterial::getBaseTexName(char* texname, const BlockDef* def, int& gettextype)
{
	gettextype = GETTEX_WITHDEFAULT;
	return def->Texture1.c_str();
}

//BlockDrawType ModelBlockMaterial::getDrawType()
//{
//	BlockDrawType drawtype = BLOCKDRAW_OPAQUE;
//	if (baseTexNeed4BitAlpha()) drawtype = BLOCKDRAW_XPARENT;
//
//	return drawtype;
//}

void ModelBlockMaterial::init(int resid)
{
	m_defaultGeomName = "block";

	InitUgcObjGeom(resid);
	BlockMaterial::init(resid);

	SetToggle(BlockToggle_IsOpaqueCube, false);

	if (m_LoadOnlyLogic) return;

	if (g_BlockMtlMgr.IsUseVertexAnimationEffect(resid))
	{
		AppendBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
		getDefaultMtl()->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		if (getSnowCoverMtl()) getSnowCoverMtl()->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
	}

	if (getSnowCoverMtl() && g_BlockMtlMgr.IsUseVertexSnowCoverEffect(resid))
		getSnowCoverMtl()->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_SNOW);
}

void ModelBlockMaterial::initDefaultMtl()
{
	if (m_defaultMtlIndex != UINT_MAX)
		return;

	if (m_UgcMtlIndex_Map.size() > 0)
	{
		m_defaultMtlIndex = m_UgcMtlIndex_Map.begin()->second;
		return;
	}

	char texname[256];
	int gettextype;
	const char* newtexname = getBaseTexName(texname, GetBlockDef(), gettextype);
	auto mtl = g_BlockMtlMgr.createRenderMaterial(newtexname, GetBlockDef(), gettextype, getDrawType(), getMipmapMethod());
	if (GetBlockDef()->ID == 1232) {
		mtl = g_BlockMtlMgr.createRenderMaterial(newtexname, GetBlockDef(), gettextype, BLOCKDRAW_OPAQUE, getMipmapMethod());
	}
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl);
}

bool ModelBlockMaterial::InitUgcObjGeom(int resid)
{
	//Ҫ��init֮ǰ����, ��Ϊinit������Ҳ���Texture2��Ӧ�����������Ĭ�ϵ�'block'
	BlockDef* def = GetDefManagerProxy()->getBlockDef(resid);
	if (!def || def->ModelType != ModelType_UGCObj)
		return false;

	const Rainbow::FixedString& geomname = def->Texture2;
	MINIW::obj_scene_data objSceneData;
	std::vector< Rainbow::SharePtr<Rainbow::Texture2D>> ObjTextures;
	std::vector<int> indices;

	if (GetUgcAssetMgrInterface()->GetObjSceneData(geomname.c_str(), objSceneData, ObjTextures, indices))
	{
		BlockGeomTemplate* geomPtr = g_BlockMtlMgr.getGeomTemplate(geomname);
		if (!geomPtr)
		{
			BlockGeomTemplate* pgeom = ENG_NEW(BlockGeomTemplate)();
			pgeom->loadFromObjSceneData(objSceneData, indices);
			g_BlockMtlMgr.SetBlockGeomTemplate(geomname, pgeom);
		}

		delete_obj_data(&objSceneData);
	}

	BlockDrawType drawtype = BLOCKDRAW_GRASS;

	for (auto& it : ObjTextures)
	{
		std::string texId = def->Texture1.c_str();
		std::string texName = it.Get()->GetName();
		texId.append("_");
		texId.append(texName);

		RenderBlockMaterial* mtl = g_BlockMtlMgr.createRenderMaterial(texId.c_str(), def, GETTEX_WITHDEFAULT, drawtype, 0, NULL, false);
		if (!mtl)
			continue;

		mtl->SetSrcTexture(it);
		m_UgcMtlIndex_Map[texName] = getRenderMtlMgr().addMtl(mtl);
		OGRE_RELEASE(mtl);
	}

	return true;
}

void ModelBlockMaterial::update(unsigned int dtick)
{
	BlockMaterial::update(dtick);
	if (IsBlockEffectDirty())
	{
		bool success = false;
		bool enableVertexAnimation = HasBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
		bool enableVertexSnow = HasBlockEffect(BLOCKEFFECT_VERTEX_SNOW);
		RenderBlockMaterial* mtl = getDefaultMtl();
		if (mtl)
		{
			success |= mtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
			//success |= mtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_SNOW, enableVertexSnow);
		}
		mtl = getSnowCoverMtl();
		if (mtl)
		{
			success |= mtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
			success |= mtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_SNOW, enableVertexSnow);
		}
		if (success)
		{
			SetBlockEffectDirty(false);
		}
	}
}

RenderBlockMaterial* ModelBlockMaterial::getGeomMtl(const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	return getDefaultMtl();
}

RenderBlockMaterial* ModelBlockMaterial::getGeomMtlOnJob(const SectionDataHandler* sectionData, const WCoord& blockpos, World* world, const SharedChunkData* sharedChunkData)
{
	return getDefaultMtl();
}

RenderBlockMaterial* ModelBlockMaterial::getGeomMtlProto(int protodata)
{
	return getDefaultMtl();
}

void ModelBlockMaterial::initDrawType()
{
	initBaseTexNeed4BitAlpha();
	m_blockDrawType = m_baseTexNeed4BitAlpha ? BLOCKDRAW_XPARENT : BLOCKDRAW_OPAQUE;
}
BlockTexElement* ModelBlockMaterial::getDestroyTexture(Block pblock, BlockTexDesc& desc)
{
	desc.blendmode = BLEND_ALPHATEST;
	desc.gray = false;

	auto mtl = getDefaultMtl();
	if (!mtl)
	{
		return NULL;
	}

	return mtl->getTexElement();//m_Mtl->getTexElement();
}

int ModelBlockMaterial::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	Block pblock = sectionData->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = pblock.getData() % 4;

	return 1;
}

int ModelBlockMaterial::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;

	return 1;
}

bool ModelBlockMaterial::dealNewGrow(World* pworld, const WCoord& blockpos, int dataindex, int maxdata)
{
	const BlockDef* def = GetBlockDef();
	if (!def || !pworld || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return false;
	}
	int blockdata = pworld->getBlockData(blockpos);
	WorldManager* worldmanager = pworld->GetWorldMgr();
	if (worldmanager && blockdata < maxdata)
	{
		int plantTime = worldmanager->getPlantTime(blockpos, pworld->getCurMapID());
		if (plantTime < 0)
		{
			return false;
		}
		int factor = getTemperatureFactor(pworld, blockpos);
		int downid = pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z);
		int curindex = (blockdata * def->GrowthTimeNum) / maxdata;
		int rate = 0;
		if (curindex < def->GrowthTimeNum)
		{
			rate = def->GrowthTime[curindex];
		}
		if (( (maxdata / def->GrowthTimeNum) * curindex) < blockdata)
		{
			curindex += 1;
		}
		int alltime = 0;
		for (int i = 0; i < def->GrowthTimeNum; ++i)
		{
			alltime += def->GrowthTime[i];
		}
		int realrate = rate;
		int realAllTime = alltime;
		int subrate = getBlockGrowSpeedUpTime(pworld, blockpos, rate);
		int suballtime = getBlockGrowSpeedUpTime(pworld, blockpos, alltime);;
		realrate -= subrate;
		realAllTime -= suballtime;
		subrate = getActorGrowSpeedUpTime(pworld, blockpos, rate);
		suballtime = getActorGrowSpeedUpTime(pworld, blockpos, alltime);;
		realrate -= subrate;
		realAllTime -= suballtime;
		realrate = realrate * factor;
		realAllTime = realAllTime * factor;
		int worldtime = worldmanager->getDayNightTime();
		//�ܹ�ȥ��tick
		int time = ((worldtime - plantTime) * 1.0);
		int FertilizedUpTime = worldmanager->getFertilizedUpTime(blockpos, pworld->getCurMapID());
		time += FertilizedUpTime;

		realrate = realrate / 24.0 * TICKS_ONEDAY;
		realAllTime = realAllTime / 24.0 * TICKS_ONEDAY;
		if (time >= realAllTime)
		{
			blockdata = maxdata;
			pworld->setBlockData(blockpos, blockdata, dataindex);
			return true;
		}
		else
		{
			for (int i = 0; i < curindex; ++i)
			{
				time -= (def->GrowthTime[i] / 24.0 * TICKS_ONEDAY);
			}

			if (time >= realrate)
			{
				curindex = curindex + 1;

				int blockdata = ((curindex * 1.0) * maxdata) / def->GrowthTimeNum;
				if (blockdata > maxdata || (curindex == def->GrowthTimeNum && blockdata < maxdata))
				{
					blockdata = maxdata;
				}
				pworld->setBlockData(blockpos, blockdata, dataindex);
				return true;
			}
		}

	}
	return false;
}

void ModelBlockMaterial::FertilizedPlayEffect(World* pworld, const WCoord& blockpos)
{
	pworld->getEffectMgr()->playParticleEffectAsync("particles/zhiwushenzhang.ent", BlockCenterCoord(blockpos), 200, 0, 0, true);
}
bool  ModelBlockMaterial::dealFertilized(World* pworld, const WCoord& blockpos, int itemid)
{
	const BlockDef* def = GetBlockDef();
	if (!def || !pworld || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return false;
	}
	WorldManager* worldmanager = pworld->GetWorldMgr();
	if (worldmanager)
	{
		int plantTime = worldmanager->getPlantTime(blockpos, pworld->getCurMapID());
		int blockdata = pworld->getBlockData(blockpos);
		int rate = 0;
		MINIW::ScriptVM::game()->callFunction("GetFertilizerValueByItemId", "i>i", itemid, &rate);
		rate *= (TICKS_ONEDAY / 24.0);
		worldmanager->addFertilizedUpTime(blockpos, pworld->getCurMapID(), rate);
		blockTick(pworld, blockpos);
		FertilizedPlayEffect(pworld, blockpos);
		return true;
	}
	return false;
}

int  ModelBlockMaterial::getMaxBlockdata()
{
	return 7;
}
int ModelBlockMaterial::getTemperatureFactor(World* pworld, const WCoord& blockpos)
{

	bool isTemperatureActive = pworld->GetWorldMgr()->getTemperatureMgr()->GetTemperatureActive();
	int defLevel = GetBlockDef()->GrowthTempRange;
	int factor = 0;
	if (defLevel != TEMPERATURE_LEVEL_NONE && isTemperatureActive)
	{
		int posLevel = TEMPERATURE_LEVEL_NONE;
		float temp = 0.0f;
		pworld->GetWorldMgr()->getTemperatureMgr()->GetBlockTemperatureAndLevel(pworld, blockpos, temp, posLevel);
		float factorRateMin = GetLuaInterfaceProxy().get_lua_const()->growth_rate_min / 100;
		factor = (1.0f - ((float)Rainbow::Abs(posLevel - defLevel) / 6)) * (1.0f - factorRateMin) + factorRateMin;
	}
	if (factor == 0)
	{
		factor = 1;
	}
	return factor;
}

int ModelBlockMaterial::getBlockGrowSpeedUpTime(World* pworld, const WCoord& blockpos, int time)
{
	int result = 0;
	int downid = pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z);
	BlockDef* downdef = GetDefManagerProxy()->getBlockDef(downid);
	if (downid == BLOCK_FARMLAND || downid == BLOCK_BURYLAND || downid == BLOCK_FARMLAND_PIT || downid == BLOCK_FARMLAND_RED)
	{

		if (pworld->getBlockData(blockpos.x, blockpos.y - 1, blockpos.z) > 0) //wet ʪ��
		{
			result = 0.1 * time;

		}
	}
	else if (downid == BLOCK_GRASS_WOOD_GRAY_FARMLAND) //��ľ������
	{
		result = 0.1 * time;
	}
	return result;
}
int ModelBlockMaterial::getActorGrowSpeedUpTime(World* pworld, const WCoord& blockpos, int time)
{
	//�����˼���
	int result = 0;
	float rangexz = 4;
	CollideAABB box;
	box.pos = blockpos * BLOCK_SIZE;
	box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	box.expand((int)(rangexz * BLOCK_SIZE), 2 * BLOCK_SIZE, (int)(rangexz * BLOCK_SIZE));
	std::vector<IClientActor*>actors;;
	pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
	int speedUpMonsterID = 3121;
	WCoord pos = BlockBottomCenter(blockpos);
	for (size_t i = 0; i < actors.size(); i++)
	{
		IClientMob* mob = dynamic_cast<IClientMob*>(actors[i]);
		if (mob->GetMobID() == speedUpMonsterID)
		{
			WCoord vec = actors[i]->getPosition() - pos;
			float dist = vec.length();
			if (dist < rangexz * BLOCK_SIZE)
			{
				result = 0.1 * time;
				break;
			}
		}
	}
	return result;
}

void ModelBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom) return;
	auto psection = data.m_SharedSectionData;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	RenderBlockMaterial* pmtl = getGeomMtl(psection, blockpos, data.m_World);
	//���ﲻ���пգ���Ӱ���Ʒ�
	/*if (pmtl == nullptr) 
	{
		return;
	}*/
	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl, false, data.m_LODLevel);
	Block pblock = psection->getBlock(blockpos);
	
	for (int i = 0; i < ngeom; i++)
	{
		/*	if (!m_Geom)
				break;*/

		int dir = dirbuf[i] & 0xffff;
		int mirrortype = (dirbuf[i] >> 16) & 3;

		if (mirrortype > 0) geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype, NULL, 0);
		else geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, 0, NULL, 0);

		float f = 0.0f;
		if (psubmesh) {  //�ǿ��ж�
			if (isColorableBlock() && !isUseCustomModel())
			{
				BlockColor bv = getBlockColor(pblock.getData());
				bv.a = 0;
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl ? pmtl->getUVTile() : &f);
			}
			else
			{
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl ? pmtl->getUVTile() : &f);
			}
		}
	}
}

SectionMesh* ModelBlockMaterial::createBlockProtoMesh(int protodata)
{
	if (getGeom(0) == NULL) return NULL;
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getGeomMtlProto(protodata), true);

	BlockGeomMeshInfo meshinfo;

	int idbuf[32];
	int dirbuf[32];
	int ngeom = getProtoBlockGeomID(idbuf, dirbuf);
	for (int i = 0; i < ngeom; i++)
	{
		getGeom(0)->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dirbuf[i]);

		if (isColorableBlock() && !isUseCustomModel())
		{
			BlockColor bv = getBlockColor(protodata);
			bv.a = 0;
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &bv);
		}
		else
		{
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL);
		}
	}

	return pmesh;
}

static bool s_CustomCollideHeight = true;
void ModelBlockMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	Section* psection = pworld->getSection(blockpos);
	if (psection == NULL) return;
	if (!getGeom(0))
		return;

	WCoord sgrid = blockpos - psection->m_Origin;

	int idbuf[16];
	int dirbuf[16];

	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, sgrid, pworld);
	WCoord minpos, maxpos;
	WCoord origin = blockpos * BLOCK_SIZE;

	if (s_CustomCollideHeight && getCollideAddHeight() != 0)
	{
		for (int i = 0; i < ngeom; i++)
		{
			if (getGeom(0)->getMeshCount() <= idbuf[i])
				continue;

			getGeom(0)->getBoundBox(minpos, maxpos, idbuf[i], 1.0f, dirbuf[i]);
			maxpos += origin;
			maxpos.y = origin.y + getCollideAddHeight();
			coldetect->addObstacle(minpos + origin, maxpos);
		}
	}
	else
	{
		for (int i = 0; i < ngeom; i++)
		{
			if (getGeom(0)->getMeshCount() <= idbuf[i])
				continue;

			getGeom(0)->getBoundBox(minpos, maxpos, idbuf[i], 1.0f, dirbuf[i]);
			coldetect->addObstacle(minpos + origin, maxpos + origin);
		}
	}
}

void ModelBlockMaterial::setPlantSnowCoverThickness(Vector4f value)
{
	if (getSnowCoverMtl() && m_PlantSnowCoverThickness != value)
	{
		m_PlantSnowCoverThickness = value;
		getSnowCoverMtl()->setPlantSnowCoverThickness(value);
	}
}




void ModelBlockMaterial::createPickData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	s_CustomCollideHeight = false;
	createCollideData(coldetect, pworld, blockpos);
	s_CustomCollideHeight = true;
}

//bool ModelBlockMaterial::isOpaqueCube()
//{
//	return false;
//}

bool ModelBlockMaterial::hasSolidTopSurface(int blockdata)
{
	return false;
}

//const char* ModelBlockMaterial::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

void ModelBlockMaterial::initGeomName()
{
	m_geomName = GetBlockDef()->Texture2.c_str();
}

int ModelBlockMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int curDir = blockdata;

	curDir = this->commonConvertDataByRotate(curDir, rotatetype);

	return curDir;
}





MultiModelBlockMaterial::MultiModelBlockMaterial()
{

}

MultiModelBlockMaterial::~MultiModelBlockMaterial()
{

}

void MultiModelBlockMaterial::init(int resid)
{
	ModelBlockMaterial::init(resid);
	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
}

WorldContainer* MultiModelBlockMaterial::createContainer(World* pworld, const WCoord& blockpos)
{
	if (isCoreBlock(pworld, blockpos))
	{
		return createMultiContainer(pworld, blockpos);
	}
	else
	{
		return nullptr;
	}
}

bool MultiModelBlockMaterial::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
	auto corePos = getCoreBlockPos(pworld, blockpos);
	if (corePos == WCoord(0, -1, 0)) return false;

	WorldContainer* container = pworld->getContainerMgr()->getContainer(corePos);

	return onMultiTrigger(pworld, blockpos, face, player, colpoint, container);
}

void MultiModelBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
	// 只有核心方块才创建网格
	if ((blockdata & 4) == 0)
	{
		ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
	}
}

void MultiModelBlockMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (pworld == NULL) return;
	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
	auto corepos = getCoreBlockPos(pworld, blockpos);
	// 只有核心方块才进行处理
	if (corepos.y >= 0)
	{
		int blockData = pworld->getBlockData(corepos);
		int placeDir = blockData & 3;
		std::vector<WCoord> poslist;
		getMultiBlockRange(poslist, placeDir, corepos, false, true);
		for (auto& pos : poslist)
		{
			auto block = pworld->getBlock(pos);
			if (block.getResID() != m_BlockResID || block.getData() != (4 | placeDir))
			{
				pworld->setBlockAll(pos, m_BlockResID, 4 | placeDir);
			}
		}
	}
}

void MultiModelBlockMaterial::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	if (pworld == NULL)
	{
		return;
	}

	ModelBlockMaterial::onBlockAdded(pworld, blockpos);
	onMultiBlockAdded(pworld, blockpos);
}

void MultiModelBlockMaterial::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}

	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockid , blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
		pworld->getContainerMgr()->destroyContainer(blockpos);
		std::vector<WCoord> poslist;
		getMultiBlockRange(poslist, placeDir, blockpos);
		for (auto& pos : poslist)
		{
			WCoord curPos = pos + blockpos;
			int blockid = pworld->getBlockID(curPos);
			if (blockid == m_BlockResID && !isCoreBlock(pworld, curPos))
			{
				pworld->setBlockAir(curPos);
			}
		}
		onMultiBlockRemoved(pworld, blockpos, blockid, blockdata);
	}
}

bool MultiModelBlockMaterial::onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container)
{
	return false;
}

WorldContainer* MultiModelBlockMaterial::createMultiContainer(World* pworld, const WCoord& blockpos)
{
	return nullptr;
}

void MultiModelBlockMaterial::onMultiBlockAdded(World* pworld, const WCoord& blockpos)
{

}

void MultiModelBlockMaterial::onMultiBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{

}