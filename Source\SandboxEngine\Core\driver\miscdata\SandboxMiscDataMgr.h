#pragma once

#include "SandboxSingleton.h"
#include "SandboxAutoRef.h"
#include "SandboxListener.h"
#include <vector>
#include "SandboxAssetRef.h"
class MMKV;
namespace MNSandbox {
	class  EXPORT_SANDBOXDRIVERMODULE SandboxMiscDataMgr :public Ref, public MNSandbox::Singleton<SandboxMiscDataMgr>
	{
	public:
		SandboxMiscDataMgr();
		virtual ~SandboxMiscDataMgr();
		bool OpenData(std::string& path);
		bool CloseData();
		bool HasData(std::string& key);
		bool WriteData(std::string& key, std::string& value);
		bool ReadData(std::string& key, std::string& value);
		void SaveDirtyData();
		void ClearDirtyData();
		void SaveAllData(std::unordered_map<std::string, int> &dataToSave);
	private:
		std::unordered_map<std::string, std::string> m_DirtyCacheMap;
		MMKV* m_pMmkvInstance;
		std::string m_sRootPath;
	};

}