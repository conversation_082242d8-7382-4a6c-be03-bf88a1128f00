#pragma once

#include "base/SandboxRef.h"
#include "SandboxSingleton.h"
#include "base/SandboxWeakRef.h"
#include "SandboxListener.h"
#include "SandboxAssetHttpType.h"


namespace MNSandbox {

	class AssetBaseHttp;
	class EXPORT_SANDBOXDRIVERMODULE AssetHttpMgr : public Ref, public MNSandbox::Singleton<AssetHttpMgr>
	{

	public:
		AssetHttpMgr();
		virtual ~AssetHttpMgr();

		void Request(AssetHttpReqType reqType, AutoRef<AssetBaseHttp> http);
		void SetHttpDirty(unsigned httpRefId);
		void Gc();
		//todo request fail retry 
	private:
		void OnUpdate(float f);
		void Init();
	private:
		ListenerClass<AssetHttpMgr, float> m_listenGlobalUpdate;
		enum AssetHttpState
		{
			Reqing,
			Remove
		};
		struct AssetHttpItem
		{
			AssetHttpState state;
			AutoRef<AssetBaseHttp> http;
		};
		std::unordered_map<unsigned, AssetHttpItem> m_reqMap;
	};

}