#include "Core/GameObject.h"
#include "Jobs/Internal/JobQueue.h"
#include "Jobs/JobTypes.h"

#include "NavMeshBuildManager.h"
#include "NavMeshBuildOperation.h"
#include "RuntimeNavMeshBuilder.h"
#include "AI/Public/NavMeshData.h"
#include "AI/NavMeshManager.h"
#include "Geometry/Intersection.h"
#include "Misc/GameObjectUtility.h"
#include "Components/Transform.h"
#include "Graphics/Mesh/Mesh.h"
#include "Threads/CurrentThread.h"
#include "Bootstrap/BootConfig.h"

static BootConfig::Parameter<UInt64> s_TempAllocatorSizeNavMeshWorker("memorysetup-temp-allocator-size-nav-mesh-worker", 64 * 1024);

void NavMeshBuildManager::UpdateAsyncOperations()
{
    Assert(Rainbow::CurrentThread::IsMainThread());
    // Check if async operations have been completed, integrate, trigger script callbacks and remove.
    // Note: script callback must happen on the main thread.

    // We use two passes here because invoking the coroutine can modify the container
    dynamic_array<NavMeshBuildOperation*> doneOperations(kMemTempAlloc);
    for (size_t i = 0; i < m_AsyncOperations.size(); ++i)
    {
        NavMeshBuildOperation* op = m_AsyncOperations[i];
        if (op->IsReadyToIntegrateToMainThread())
        {
            doneOperations.push_back(op);
            m_AsyncOperations.erase(m_AsyncOperations.begin() + i);
            i--;
        }
    }

    for (NavMeshBuildOperation* op : doneOperations)
    {
        op->Integrate();
        //op->InvokeCoroutine();
        op->Release();
       // ENG_DELETE_LABEL(op, kMemAI);
    }
}

void NavMeshBuildManager::ReleaseAsyncOperations()
{
    for (NavMeshBuildOperation* op : m_AsyncOperations)
    {
        // Purge operation to avoid releasing an already scheduled operation
        // Operation will abort as soon as possible, then ready to be destroyed
        // Note that this process is blocking, waiting for the scheduling or computing jobs to end
        op->Purge();
        ENG_DELETE_LABEL(op, kMemAI);
    }
    m_AsyncOperations.clear_dealloc();
}

void NavMeshBuildManager::Purge(const NavMeshData* data)
{
    for (NavMeshBuildOperation* op : m_AsyncOperations)
        op->Purge(data);
}

bool NavMeshBuildManager::IsAsyncOperationing(Rainbow::AABB& inBounds)
{
    for (NavMeshBuildOperation* op : m_AsyncOperations)
    {
        if (op->IsAsyncOperationing(inBounds))
            return true;
    }
    return false;
}

void NavMeshBuildManager::SyncOperationFence(Rainbow::JobGroupID& groupID)
{
    if (groupID.group == NULL || m_JobQueue == NULL)
        return;

    m_JobQueue->WaitForJobGroupID(groupID, Rainbow::JobQueue::kWorkStealAllJobs);
    ClearOperationFenceWithoutSync(groupID);
}

void NavMeshBuildManager::ClearOperationFenceWithoutSync(Rainbow::JobGroupID& groupID)
{
    groupID.group = 0;
    groupID.version = 0u;
}

NavMeshBuildManager::~NavMeshBuildManager()
{
    if (m_JobQueue != NULL)
    {
        m_JobQueue->Shutdown(Rainbow::JobQueue::kShutdownWaitForAllJobs);
        ENG_DELETE_LABEL(m_JobQueue, kMemAI);
    }
    ReleaseAsyncOperations();
}

static void AsyncJob(void* userData)
{
    NavMeshBuildOperation* op = reinterpret_cast<NavMeshBuildOperation*>(userData);
    op->Schedule();
}

void NavMeshBuildManager::ExecuteAsync(NavMeshBuildOperation* op)
{
    Assert(Rainbow::CurrentThread::IsMainThread());
    if (m_JobQueue == NULL)
    {
        // NOTE: Cannot alloc in ctor, since the the JobQueue is not properly initialized at that time.
        m_JobQueue = ENG_NEW_LABEL(Rainbow::JobQueue, kMemAI)(1, s_TempAllocatorSizeNavMeshWorker, -1, Rainbow::JobQueue::kAllowMutexLocks, "NavMesh Builder", "Worker");
        m_JobQueue->SetThreadPriority(Rainbow::kBelowNormalPriority);
    }

    // Purge existing operation working on the data
    const NavMeshData* data = op->GetData();
    for (NavMeshBuildOperation* op : m_AsyncOperations)
        op->Purge(data);
    op->AddRef();

    m_AsyncOperations.push_back(op);
    Rainbow::JobGroupID defaultJobGroupID;
    Rainbow::JobGroupID jobGroupID = m_JobQueue->ScheduleJob(AsyncJob, op, defaultJobGroupID, Rainbow::JobQueue::kNormalJobPriority);
    op->AddSchedulingDependency(jobGroupID);
}


// Update existing navmesh data - only tiles affected by changes in input are recalculated
bool NavMeshBuildManager::UpdateNavMeshData(NavMeshData* data, const NavMeshBuildSettings& buildSettings,
    const SharedMeshBuildSource* sources, size_t nsources, const Rainbow::AABB& localBounds)
{
    NavMeshBuildSettings validatedSettings;
    ValidateNavMeshBuildSettings(validatedSettings, NULL, buildSettings, localBounds);

    BuildNavMeshInfo* info = CreateBuildNavMeshInfo();
    AcquireSharedMeshData(info, sources, nsources, data->GetPosition(), data->GetRotation(), localBounds);

    ScheduleNavMeshDataUpdate(data, info, validatedSettings, localBounds);
    IntegrateNavMeshDataUpdate(data, info, localBounds);

    DestroyBuildNavMeshInfo(info);

    return true;
}

void NavMeshBuildManager::UpdateNavMeshDataAsync(NavMeshData* data, const NavMeshBuildSettings& buildSettings,
    const SharedMeshBuildSource* sources, size_t nsources, const Rainbow::AABB& localBounds)
{
    Assert(data);
    // Using 'new' here because AsyncOperation uses 'delete' on final deref in Release
    NavMeshBuildOperation* oper = ENG_NEW_LABEL(NavMeshBuildOperation, kMemAI)(kMemAI,data, buildSettings, sources, nsources, localBounds);

    NavMeshBuildManager* manager = GetNavMeshManager().GetNavMeshBuildManager();
    manager->ExecuteAsync(oper);

    return ;
}
