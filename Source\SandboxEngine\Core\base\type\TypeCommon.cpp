#include "TypeCommon.h"
#include "SandboxReflexTypePolicy.h"
#include "base/stream/SandboxStream.h"
#include "base/reflex/SandboxReflexTypePolicyEx.h"
#include "base/type/TypeBridge_Common.h"
#include "script/bridge/SandboxReflexReferenceBridge.h"

namespace MNSandbox {

		kvData_::kvData_()
		{
			key = "";
			value = 0;
			nick = "";
		}
		
		kvData_::kvData_(std::string inKey, int64_t inValue,std::string inNick) : key(inKey), value(inValue) , nick(inNick) {}




		template<>
		void ReflexPolicyFunc<kvData_>::CallbackSerialize(const void* data, MNJsonVal& out)
		{
			auto& v = Data(data);
			MNJsonArray jsonarray;
			jsonarray << (MNJsonStr)v.key << (MNJsonNumber)v.value << (MNJsonStr)v.nick;
			out.import(jsonarray);
		}
		template<>
		bool ReflexPolicyFunc<kvData_>::CallbackUnserialize(void* data, const MNJsonVal& in)
		{
			if (!in.is<MNJsonArray>())
				return false;
			auto& jsona = in.get<MNJsonArray>();
			auto& v = Data(data);
			if (jsona.has<MNJsonStr>(0) && jsona.has<MNJsonNumber>(1))
			{
				v.key = jsona.get<MNJsonStr>(0);
				v.value = jsona.get<MNJsonNumber>(1);
				if (jsona.has<MNJsonStr>(2))
				{
					v.nick = jsona.get<MNJsonStr>(2);
				}
				return true;
			}
			SANDBOX_ASSERT(false);
			return false;
		}
		template<>
		std::string ReflexPolicyFunc<kvData_>::CallbackToString(const void* data)
		{
			auto& v = Data(data);
			return ToString("{", v.key, ", ", v.value, ",", v.nick, "}");
		}
		template<>
		bool ReflexPolicyFunc<kvData_>::CallbackLuaToC(void* data, lua_State* L, int objindex)
		{
			if (lua_isnil(L, objindex))
				return false;
			Data(data) = *KVDataBridge::GetObject(L, objindex);
			return true;
		}
		template<>
		int ReflexPolicyFunc<kvData_>::CallbackLuaPushC(const void* data, lua_State* L)
		{
			KVDataBridge::PushKVData(L, Data(data));
			return 1;
		}
		template<>
		size_t ReflexPolicyFunc<kvData_>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
		{
			auto& v = Data(data);
			size_t len = 0;
			if (out->GetVersion() < Config::ToVersion(0, 0, 20))
			{
				len += out->WriteString(v.key);
				len += out->WriteNumber<int64_t>(v.value);
			}
			else
			{
				len += out->WriteString(v.key);
				len += out->WriteNumber<int64_t>(v.value);
				len += out->WriteString(v.nick);
			}

			return len;
		}
		template<>
		bool ReflexPolicyFunc<kvData_>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
		{
			auto& v = Data(data);
			bool ret = true;
			if (in->GetVersion() < Config::ToVersion(0, 0, 20))
			{
				ret &= in->ReadString(v.key);
				ret &= in->ReadNumber<int64_t>(v.value);
			}
			else
			{
				ret &= in->ReadString(v.key);
				ret &= in->ReadNumber<int64_t>(v.value);
				ret &= in->ReadString(v.nick);
			}
			return ret;
		}

		//RegisterReflexTypePolicyClass(MNSandbox::kvData_, REFLEXTYPEENUM_KVDATA, ReflexPolicyFunc<MNSandbox::kvData_>);
		RegisterReflexTypePolicyWithoutLine(_kvdataRegister, kvData_, REFLEXTYPEENUM_KVDATA, ReflexPolicyFunc<kvData_>, ReflexType::TYPE::CLASS);
		

		//REFLEXTYPE_CONTAINER_POLICY(vecKVData, REFLEXTYPEENUM_VECTOR_KVDATA, REFLEXTYPEENUM_VECTOR_KVDATA_PTR);
		RegisterReflexTypePolicyWithoutLine(_kvdataVecRegister, vecKVData, REFLEXTYPEENUM_VECTOR_KVDATA, ReflexPolicyContainer<vecKVData>, ReflexType::TYPE::CONTAINER_VECTOR);
		RegReflexVectorType(vecKVData);
		RegisterReflexReferencePtr_WithoutLine(_kvdataRefPtrRegister,vecKVData, REFLEXTYPEENUM_VECTOR_KVDATA_PTR);


		void kvData_::InitRegister()
		{
			_kvdataRegister.ManualInit();
			_kvdataVecRegister.ManualInit();
			_kvdataRefPtrRegister.ManualInit();
		}
}