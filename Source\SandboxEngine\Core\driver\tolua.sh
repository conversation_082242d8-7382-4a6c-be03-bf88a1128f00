#!/bin/bash
TMPL_PYTHON_TARGET=../../../../../Tools/tolua_sandbox/TmplToLua.py
TARGET_TEMP_PKG=SandboxCoreDriverToLua_Temp.pkg
PATH_PYTHON_EXE=python2.7
SRC_PYTHON_TARGET=../../../../../Tools/tolua_sandbox/checktolua.py

${PATH_PYTHON_EXE} ${TMPL_PYTHON_TARGET} -p SandboxCoreDriverToLua.pkg -d ${TARGET_TEMP_PKG}
../../../../../Source/External/Game/lua/tolua/bin/tolua++_linux -o SandboxCoreDriverToLua.cpp -n SandboxCoreDriverToLua ${TARGET_TEMP_PKG}
${PATH_PYTHON_EXE} ${SRC_PYTHON_TARGET} -s SandboxCoreDriverToLua.cpp -p SandboxCoreDriverToLua.pkg
