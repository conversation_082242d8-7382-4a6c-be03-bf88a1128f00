# 特殊开采协议 - PB_BLOCK_EXPLOIT

## 协议概述

PB_BLOCK_EXPLOIT 协议是沙盒游戏中处理特殊开采操作的网络协议。该协议主要用于使用特定工具进行的开采操作，与普通挖掘(PB_BLOCK_PUNCH)不同，它更专注于精确的开采行为。

**注意**: 在当前代码版本中，该协议的大部分客户端发送逻辑已被注释，但服务器端和客户端的处理逻辑仍然保留。

- **PB_BLOCK_EXPLOIT_CH**: 客户端发送给服务器
- **PB_BLOCK_EXPLOIT_HC**: 服务器发送给客户端

## 协议定义

### PB_BlockExploitCH (客户端 → 服务器)

```protobuf
message PB_BlockExploitCH
{
    optional int32 status = 1;                  // 开采状态
    optional int32 face = 2;                    // 开采面方向
    optional game.common.PB_Vector3 blockpos = 3;  // 目标方块位置
    optional int32 picktype = 4;               // 开采工具类型
}
```

**协议定义位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:957-963`

### PB_BlockExploitHC (服务器 → 客户端)

```protobuf
message PB_BlockExploitHC
{
    optional uint64 objid = 1;                  // 玩家对象ID
    optional int32 status = 2;                  // 开采状态
    optional int32 face = 3;                    // 开采面方向
    optional game.common.PB_Vector3 blockpos = 4;  // 方块位置
    optional int32 picktype = 5;               // 开采工具类型
}
```

## 协议流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant OtherClients as 其他客户端

    Note over Client: 玩家使用特殊工具开采
    Client->>Client: ExploitState::exploitBlock()
    Note over Client: (当前版本已注释)
    Client-->>Server: PB_BLOCK_EXPLOIT_CH

    Note over Server: 服务器处理
    Server->>Server: handleBlockExploit2Host()
    Server->>Server: 验证玩家状态
    Server->>Server: 获取ExploitState
    Server->>Server: exploitState->exploitBlock()

    Note over Server: 广播给其他玩家
    Server->>Server: notifyExploitBlock2Tracking()
    Note over Server: (当前版本已注释)
    Server-->>OtherClients: PB_BLOCK_EXPLOIT_HC

    Note over OtherClients: 客户端同步
    OtherClients->>OtherClients: handleBlockExploit2Client()
    OtherClients->>OtherClients: 执行开采逻辑
```

## 详细实现流程

### 1. 客户端发送协议 (已注释)

**原始触发位置**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:854-873` (已注释)

```cpp
// 注释的客户端发送代码
//bool MpPlayerControl::exploitBlock(const WCoord &targetblock, DirectionType targetface, int status, int pickType)
//{
//    if (!PlayerControl::exploitBlock(targetblock, targetface, status, pickType)) return false;
//
//    if (m_pWorld->isRemoteMode())
//    {
//        PB_BlockExploitCH blockExploitCH;
//        blockExploitCH.set_face(targetface);
//        blockExploitCH.set_status(status);
//
//        PB_Vector3* blockPos = blockExploitCH.mutable_blockpos();
//        blockPos->set_x(targetblock.x);
//        blockPos->set_y(targetblock.y);
//        blockPos->set_z(targetblock.z);
//
//        blockExploitCH.set_picktype(pickType);
//
//        GameNetManager::getInstance()->sendToHost(PB_BLOCK_EXPLOIT_CH, blockExploitCH);
//    }
//    return true;
//}
```

**协议参数说明**:

- `status`: 开采状态（开始/进行中/结束）
- `face`: 开采面方向
- `blockpos`: 目标方块坐标
- `picktype`: 开采工具类型标识

### 2. 服务器处理协议

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1703-1722`

```cpp
void MpGameSurviveNetHandler::handleBlockExploit2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
    if (player == NULL) return;

    PB_BlockExploitCH blockExploitCH;
    blockExploitCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    WCoord blockpos = MPVEC2WCoord(blockExploitCH.blockpos());
    auto pState = player->getActionStatePtr("Exploit");
    if (nullptr != pState)
    {
        auto pExploitState = dynamic_cast<ExploitState*>(pState);
        if (nullptr != pExploitState)
        {
            pExploitState->exploitBlock(blockpos, (DirectionType)blockExploitCH.face(),
                                      blockExploitCH.status(), blockExploitCH.picktype());
        }
    }

    // 注释的旧版本调用
    //player->exploitBlock(blockpos, (DirectionType)blockExploitCH.face(), blockExploitCH.status(), blockExploitCH.picktype());
}
```

**处理步骤**:

1. 验证玩家状态 (`checkDownedPlayerByMsg2Host`)
2. 解析协议数据
3. 获取玩家的 ExploitState 状态对象
4. 调用 ExploitState 的 exploitBlock 方法执行开采逻辑

### 3. 服务器响应广播 (已注释)

**原始响应函数**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:131-146` (已注释)

```cpp
// 注释的服务器响应代码
//void ClientPlayer::notifyExploitBlock2Tracking(const WCoord &blockpos, DirectionType face, int status, int pickType)
//{
//    if (!m_pWorld->isRemoteMode())
//    {
//        PB_BlockExploitHC blockExploitHC;
//        blockExploitHC.set_objid(getObjId());
//        blockExploitHC.set_status(status);
//        blockExploitHC.set_face(face);
//        blockExploitHC.mutable_blockpos()->set_x(blockpos.x);
//        blockExploitHC.mutable_blockpos()->set_y(blockpos.y);
//        blockExploitHC.mutable_blockpos()->set_z(blockpos.z);
//        blockExploitHC.set_picktype(pickType);
//
//        m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_EXPLOIT_HC, blockExploitHC, this);
//    }
//}
```

### 4. 客户端接收响应

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:3825-3840`

```cpp
void MpGameSurviveNetHandler::handleBlockExploit2Client(const PB_PACKDATA_CLIENT &pkg)
{
    PB_BlockExploitHC blockExploitHC;
    blockExploitHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockExploitHC.objid()));
    if (player == NULL) return;

    auto pState = player->getCurrentActionStatePtr();
    auto pExploitState = dynamic_cast<ExploitState*>(pState);
    if (nullptr != pExploitState)
    {
        pExploitState->exploitBlock(MPVEC2WCoord(blockExploitHC.blockpos()),
                                  (DirectionType)blockExploitHC.face(),
                                  blockExploitHC.status(),
                                  blockExploitHC.picktype());
    }

    // 注释的旧版本调用
    //player->exploitBlock(MPVEC2WCoord(blockExploitHC.blockpos()), (DirectionType)blockExploitHC.face(), blockExploitHC.status(), blockExploitHC.picktype());
}
```

**处理步骤**:

1. 解析协议数据
2. 根据对象 ID 找到对应玩家
3. 获取当前的 ExploitState 状态
4. 执行开采逻辑

## 协议注册

**注册位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp`

```cpp
// 服务器端处理器注册 (构造函数中)
REGIS_HOST_HANDLER(PB_BLOCK_EXPLOIT_CH, handleBlockExploit2Host);

// 客户端处理器注册 (行号482)
REGIS_CLIENT_HANDLER(PB_BLOCK_EXPLOIT_HC, handleBlockExploit2Client);
```

## ExploitState 状态管理

ExploitState 是专门处理开采操作的状态类，与 DigState 不同：

```cpp
class ExploitState : public ActionState
{
public:
    void exploitBlock(const WCoord& blockpos, DirectionType face, int status, int picktype);
    // ... 其他方法
};
```

**主要特点**:

- 专门处理特殊工具的开采操作
- 支持不同的开采工具类型 (`picktype`)
- 独立的状态管理机制

## 协议状态分析

### 当前状态

1. **服务器端处理**: ✅ 完全可用
2. **客户端处理**: ✅ 完全可用
3. **客户端发送**: ❌ 已注释禁用
4. **服务器广播**: ❌ 已注释禁用

### 可能的使用场景

1. **特殊工具开采**: 使用特定工具进行精确开采
2. **资源收集**: 针对特定资源的开采操作
3. **技能系统**: 与玩家技能相关的开采行为

## 与其他协议的区别

| 协议              | 用途     | 状态        | 特点                   |
| ----------------- | -------- | ----------- | ---------------------- |
| PB_BLOCK_PUNCH    | 普通挖掘 | ✅ 活跃使用 | 支持载具、多种挖掘模式 |
| PB_BLOCK_EXPLOIT  | 特殊开采 | ⚠️ 部分禁用 | 专用工具、精确开采     |
| PB_BLOCK_INTERACT | 方块放置 | ✅ 活跃使用 | 方块放置和交互         |

## 协议特点

1. **专业化**: 专门用于特殊工具的开采操作
2. **工具类型**: 支持不同开采工具的区分 (`picktype`)
3. **状态驱动**: 基于 ExploitState 状态机
4. **部分禁用**: 客户端发送和服务器广播功能已被注释
5. **向后兼容**: 保留完整的处理逻辑以备将来启用

## 潜在重启方案

如需重新启用该协议，需要：

1. **取消注释客户端发送逻辑**
2. **取消注释服务器广播逻辑**
3. **添加适当的触发条件**
4. **完善 ExploitState 状态管理**
5. **添加权限验证机制**

## 相关协议

- **PB_BLOCK_PUNCH**: 普通方块挖掘协议
- **PB_BLOCK_INTERACT**: 方块交互协议
- **PB_ITEM_USE**: 道具使用协议
