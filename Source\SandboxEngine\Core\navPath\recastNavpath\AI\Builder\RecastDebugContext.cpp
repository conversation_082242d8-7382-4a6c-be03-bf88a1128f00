
#include "RecastDebugContext.h"

void RecastDebugContext::doStartTimer(const rcTime<PERSON><PERSON>abe<PERSON> label)
{
    switch (label)
    {
        case RC_TIMER_RASTERIZE_TRIANGLES:
        case RC_TIMER_FILTER_BORDER:
        case RC_TIMER_FILTER_WALKABLE:
            
        case RC_TIMER_BUILD_COMPACTHEIGHTFIELD:
            
        case RC_TIMER_ERODE_AREA:
            
        case RC_TIMER_BUILD_REGIONS:
            
        case RC_TIMER_BUILD_REGIONS_FILTER:
            
        case RC_TIMER_BUILD_CONTOURS:
            
        case RC_TIMER_BUILD_POLYMESH:
            
        case RC_TIMER_BUILD_POLYMESHDETAIL:
            
        case RC_TIMER_MERGE_POLYMESHDETAIL:
            
        default: break;
    }
}

void RecastDebugContext::doStopTimer(const rcTimerLabel label)
{
    switch (label)
    {
        case RC_TIMER_RASTERIZE_TRIANGLES:
            
        case RC_TIMER_FILTER_BORDER:
            
        case RC_TIMER_FILTER_WALKABLE:
            
        case RC_TIMER_BUILD_COMPACTHEIGHTFIELD:
            
        case RC_TIMER_ERODE_AREA:
            
        case RC_TIMER_BUILD_REGIONS_FILTER:
            
        case RC_TIMER_BUILD_REGIONS:
            
        case RC_TIMER_BUILD_CONTOURS:
            
        case RC_TIMER_BUILD_POLYMESH:
            
        case RC_TIMER_BUILD_POLYMESHDETAIL:
            
        case RC_TIMER_MERGE_POLYMESHDETAIL:
            
        default: break;
    }
}
