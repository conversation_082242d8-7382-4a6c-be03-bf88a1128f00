#include "ActorManagerInterface.h"
#include "IClientActor.h"

//static ActorManagerInterface* s_ActorManagerInstance = nullptr;

Rainbow::AABB ActorOctreeSemantics::GetBoundingBox(IClientActor* Obj)
{
	return Obj->GetAABB();
}

const Rainbow::SceneMGTNode::Handle& ActorOctreeSemantics::GetElementHandle(IClientActor* Obj)
{
	return Obj->GetMGTHandle();
}

void ActorOctreeSemantics::SetElementHandle(IClientActor* Obj, Rainbow::SceneMGTNode::ConstReferenceHandle handle)
{
	Obj->SetMGTHandle(handle);
}

ActorManagerInterface::ActorManagerInterface()
{
	//s_ActorManagerInstance = this;
}

ActorManagerInterface::~ActorManagerInterface()
{
	//s_ActorManagerInstance = nullptr;
}

//ActorManagerInterface* GetActorManagerInterface()
//{
//	return s_ActorManagerInstance;
//}