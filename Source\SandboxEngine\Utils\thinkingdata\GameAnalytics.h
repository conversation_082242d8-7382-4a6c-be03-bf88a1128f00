#ifndef __GameAnalytics_H__
#define __GameAnalytics_H__

#include "SandboxEngine.h"

#include <map>
#include <string>
#include <type_traits>

#ifndef IWORLD_SERVER_BUILD
#include "ta_analytics_sdk.h"
#endif  

class EXPORT_SANDBOXENGINE GameAnalytics {
public:
    GameAnalytics();
    ~GameAnalytics();
    
    struct Value
    {
        enum class Type {
            Int,
            Int64,
            Float,
            String,
            Bool
        };

        Type type;

        union {
            int int_value;
            int64_t int64_value;
            float float_value;
            bool bool_value;
        };
        std::string string_value; // string需要单独处理，不能放在union中

        // 构造函数
        Value() {}
        Value(int value) : type(Type::Int), int_value(value) {}
        Value(int64_t value) : type(Type::Int64), int64_value(value) {}
        Value(float value) : type(Type::Float), float_value(value) {}
        Value(bool value) : type(Type::Bool), bool_value(value) {}
        Value(const std::string& value) : type(Type::String), string_value(value) {}
        Value(const char* value) : type(Type::String), string_value(value) {}

        // 复制构造函数
        Value(const Value& other) : type(other.type) {
            switch (type) {
            case Type::Int: int_value = other.int_value; break;
            case Type::Int64: int64_value = other.int64_value; break;
            case Type::Float: float_value = other.float_value; break;
            case Type::Bool: bool_value = other.bool_value; break;
            case Type::String: string_value = other.string_value; break;
            }
        }

        // 赋值操作符
        Value& operator=(const Value& other) {
            if (this != &other) {
                type = other.type;
                switch (type) {
                case Type::Int: int_value = other.int_value; break;
                case Type::Int64: int64_value = other.int64_value; break;
                case Type::Float: float_value = other.float_value; break;
                case Type::Bool: bool_value = other.bool_value; break;
                case Type::String: string_value = other.string_value; break;
                }
            }
            return *this;
        }

        ~Value() { }
    };

    struct CommonProperties {

        // 基础信息
        std::string device_id;
        int env;
        int apiid;
        int channel;
        std::string log_id;

        std::string app_version;


        std::string country;
        std::string province;

        //网络相关
        std::string ip_address;
        int apn; //"0=其他 1 = wifi  2 = 4G  3 = 5G"
        std::string os_type; // 安卓：Android  苹果：iOS 电脑端：PC  其他：自定义"

        // 会话信息
        std::string session_id;
        int64_t session_start_time;
        // 游戏会话信息
        std::string game_session_id;
        int64_t game_session_start_time;

        // 用户信息
        std::string uin;

        ////////////服务端通用参数///////////////////
        std::string roomid;
        std::string mapid;
        std::string host; //ip:port
        std::string mapver;
        std::string area;
    };
    static GameAnalytics* GetInstance();
    // 初始化
    static bool Init(const std::string& device_id, int env);
    static void InitCommonProps(CommonProperties& properties);

    // 设置会话相关信息
    static void SetSessionInfo(const std::string& session_id);
    // 设置游戏会话信息
    static void SetGameSessionInfo(const std::string& game_session_id);

    // 设置设备信息
    static void SetDeviceInfo(const std::string& ip_address, const std::string& os_type, int apn);


    // 登录/登出
    static void Login(const std::string& login_id);
    static void Logout();

    // 通用事件（用于自定义事件） 
    static void TrackEvent(const std::string& event_name, std::map<std::string, Value> data);

    // 通用事件（用于自定义事件，json数据） 
    static void TrackEvent(const std::string& event_name, const std::string& json = "") ;

    void Tick(double deltaTime);

    // 模板重载，支持直接传递不同类型的值
    template<typename T>
    static void SetUserProfile(const std::string& property_name, const T& value);

    static std::string genLogid();
    static int getSessionDuration();
    static int getGameSessionDuration();
   
    private:
        static bool m_initialized;
        // 存储公共参数
        static CommonProperties s_commonProps;

        //定时器相关
        double m_LastTick;
        double m_StartTick;

#ifndef IWORLD_SERVER_BUILD
        // 创建带公共属性的 TDJSONObject
        static thinkingdata::TDJSONObject createCommonProperties();
#endif  
};
 
#endif // __GameAnalytics_H__