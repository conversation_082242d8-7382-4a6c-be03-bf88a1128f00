#include "FullyCustomModel.h"
#include "OgreStringUtil.h"
#include "SandboxBodyComponent.h"
#include "blocks/BlockMaterialMgr.h"
#include "FullyCustomModelMgr.h"
#include "CustomModelMgr.h"
#ifndef SANDBOX_USE_ASSETPACKET
#include "asset/ref/SandboxAssetRefModel.h"
#endif

using namespace MNSandbox;
using namespace Rainbow;
using namespace MINIW;

bool FullyCustomModel::addCustomBone(Entity* entity, std::string name, std::string fathername, std::string modelfilename)
{
	return addCustomBone(entity, name, fathername, modelfilename, Quaternionf::identity);
}

bool FullyCustomModel::addCustomBone(Entity* entity, std::string name, std::string fathername, std::string modelfilename, Quaternionf r)
{
	return addCustomBone(entity, name, fathername, modelfilename, r, Vector3f::zero);
}

bool FullyCustomModel::addCustomBone(Entity *entity, std::string name, std::string parentName, std::string strModelPath, 
	Quaternionf r, Vector3f t, float s)
{
	if (strModelPath.empty())
	{
		strModelPath = "sandboxSysId://entity/custommodel/bone.omod";
	}

	FullyCustomBoneData* fcbd = SANDBOX_NEW(FullyCustomBoneData, this);
	fcbd->name = name;
	fcbd->fathername = parentName;
	fcbd->model = strModelPath;
	fcbd->setScale(s);
	fcbd->quat = r;
	fcbd->offsetpos = t;
	fcbd->submodelpos = Vector3f::zero;

	if(parentName.empty())
		m_vFcbd.push_back(fcbd);
	else
	{
		auto fcbdParent = findFullyCustomBoneData(parentName);
		if (fcbdParent) {
			fcbdParent->vChildFcbds.push_back(fcbd);
			if (fcbdParent->isstandard)
				fcbd->isstandard = fcbdParent->isstandard;
		}
	}

	m_bNeedSave = true;

	if (!CustomModelMgr::GetInstancePtr())
		return true;

	if (!strModelPath.empty() && CustomModelMgr::GetInstancePtr()->isDownloadCM(strModelPath))
	{
		m_iUseDownloadCMNum++;
		fcbd->isdownload = true;
	}

	Model* model = CustomModelMgr::GetInstancePtr()->getAvatarModel(strModelPath, PROJECTILE_ACTOR_MESH);
	if (!model)
	{
		WeakRef<MNSandbox::Ref> obj = m_ref.get();
		auto cb = [obj, this, entity, name](Entity* assetEntity, FullyCustomBoneData& fcbData) -> void {
			if (!obj)
			{
				return;
			}
			Model* model = nullptr;
			if (assetEntity)
			{
				Model* model = assetEntity->GetMainModel();
			}
			addCustomBoneCallFunc(entity, name, &fcbData, model);
		};
		fcbd->loadModelAsync(cb);
		return true;
	}
	return addCustomBoneCallFunc(entity, name, fcbd, model);
}

bool FullyCustomModel::addCustomBoneCallFunc(Entity* entity, std::string name, FullyCustomBoneData* fcbd, Model* model)
{
	int objclass = 0;

	if (m_splitSkeletonModel)
	{
		initDefaultSkeletonModel(entity, fcbd, objclass);
	}

	if (!model)
	{
		objclass = 1;
		if (FullyCustomModelMgr::GetInstancePtr())
		{
			model = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
		}
		if (!model)
		{
			return true;
		}
	}

	if (entity)
		entity->BindCunstomObject(name.c_str(), model, objclass);

	fcbd->setModel(model);
	fcbd->setTextureId(fcbd->texId);

	return true;
}

bool FullyCustomModel::setCustomBone(Entity *entity, std::string name, 
	float scale, short offsetx, short offsety, short offsetz, Quaternionf &quat)
{
	Vector3f translate(offsetx, offsety, offsetz);
	Vector3f scale3(scale);
	return setCustomBone(entity, name, translate, quat, scale3);
}

bool FullyCustomModel::setCustomBone(Entity *entity, std::string name, 
	Vector3f& translate, Quaternionf& rotate, Vector3f& scale3)
{
	MINIW::Transform_ trs(translate, rotate, scale3);
	return setCustomBone(entity, name, trs);
}

bool FullyCustomModel::setCustomBone(Entity *entity, std::string name, MINIW::Transform_& trs)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd)
		return false;

	Matrix4x4f tm_bone_old;
	makeSRTMatrix(tm_bone_old, fcbd->scale3, fcbd->quat, fcbd->offsetpos);
	fcbd->scale = trs.s.x;
	fcbd->scale3 = trs.s;
	fcbd->quat = trs.r;
	fcbd->offsetpos = trs.t;

	Matrix4x4f tm_bone_new;
	makeSRTMatrix(tm_bone_new, fcbd->scale3, fcbd->quat, fcbd->offsetpos);

	Matrix4x4f tm_bone_old_ = tm_bone_old;
	updateKeyFrameByBoneChange(entity, fcbd, tm_bone_old_.Invert_Full(), tm_bone_new);
	m_bNeedSave = true;

	/*if (m_splitSkeletonModel && fcbd->defaultSkeletonModel)
	{
		fcbd->defaultSkeletonModel->SetPosition(fcbd->offsetpos);
		fcbd->defaultSkeletonModel->SetRotation(fcbd->quat);
		fcbd->defaultSkeletonModel->SetScale(fcbd->scale3);
	}*/

	return true;
}


bool FullyCustomModel::changeBindModel(Rainbow::Entity* entity, std::string boneName, std::string strModelPath)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(boneName);
	if (!fcbd)
		return false;

	if (!CustomModelMgr::GetInstancePtr())
		return false;

	fcbd->model = strModelPath;
	m_bNeedSave = true;
	
	Model* model = nullptr;
	if (!strModelPath.empty() && CustomModelMgr::GetInstancePtr()->isDownloadCM(strModelPath))
	{
		m_iUseDownloadCMNum++;
		fcbd->isdownload = true;
	}
	else if (fcbd->isdownload)
	{
		m_iUseDownloadCMNum--;
		fcbd->isdownload = false;
	}

	model = CustomModelMgr::GetInstancePtr()->getAvatarModel(strModelPath, PROJECTILE_ACTOR_MESH);
	if (model)
	{
		return changeBindModelCallFunc(entity, boneName, fcbd, model);
	}

	fcbd->model = strModelPath;
	WeakRef<MNSandbox::Ref> self = m_ref.get();
	auto cb = [self, this, entity, boneName](Entity* assetEntity, FullyCustomBoneData& fcbd) -> void {
		if (!self)
		{
			return;
		}
		Model* model = nullptr;
		if (assetEntity)
		{
			model = assetEntity->GetMainModel();
		}
		if (!model)
		{
			if (FullyCustomModelMgr::GetInstancePtr())
			{
				model = FullyCustomModelMgr::GetInstancePtr()->getDefaultBoneModel();
			}
			fcbd.model = "";
		}
		changeBindModelCallFunc(entity, boneName, &fcbd, model);
	};
	return fcbd->loadModelAsync(cb);
}


bool FullyCustomModel::changeBindModelCallFunc(Entity* entity, std::string boneName, FullyCustomBoneData* fcbd, Model* model)
{
	if (fcbd->model2)
	{
		Rainbow::Model* modelOld = fcbd->model2.Get();
		if (entity->UnbindObject(modelOld))
			Model::Destory(modelOld);
	}
	model->SetPosition(fcbd->submodelpos);
	model->SetRotation(fcbd->submodelquat);
	model->SetScale(fcbd->submodelscale3);
	model->ShowSkins(fcbd->show);
	entity->UnbindCustomObject(boneName.c_str(), 0);
	entity->UnbindCustomObject(boneName.c_str(), 1);
	entity->BindCunstomObject(boneName.c_str(), model);
	fcbd->setModel(model);
	fcbd->setTextureId(fcbd->texId);
	return true;
}

void FullyCustomModel::updateBindModel(Entity* entity)
{
	PPtr<Rainbow::Entity> ppEntity(entity);
	function<bool(FullyCustomBoneData&)> func = [this, ppEntity](FullyCustomBoneData& fcbd) -> bool {
		if (fcbd.model.empty())
		{
			return true;
		}
		Model* model = CustomModelMgr::GetInstance().getAvatarModel(fcbd.model, PROJECTILE_ACTOR_MESH);
		if (model)
		{
			updateBindModelCallFunc(ppEntity ? ppEntity.Get() : nullptr, model, fcbd);
			return true;
		}
		WeakRef<MNSandbox::Ref> self = m_ref.get();
		auto cb = [self, this, ppEntity](Entity* assetEntity, FullyCustomBoneData& fcbd) -> void {
			if (!self)
			{
				return;
			}
			if (assetEntity)
			{
				Model* model = assetEntity->GetMainModel();
				if (model)
				{
					updateBindModelCallFunc(ppEntity ? ppEntity.Get() : nullptr, model, fcbd);
				}
			}
		};
		fcbd.loadModelAsync(cb);
		return true;
	};
	iterate(func);
}

void FullyCustomModel::updateBindModelCallFunc(Entity* entity, Model* model, FullyCustomBoneData& fcbd)
{
	if (!model)
	{
		return;
	}
	if (!entity)
	{
		return;
	}
	FixedString boneName = fcbd.name.c_str();
	Model* modelOld = nullptr;
	if (fcbd.model2)
	{
		modelOld = fcbd.model2;
		if (entity->UnbindObject(modelOld))
		{
			Model::Destory(modelOld);
		}
	}
	model->SetPosition(fcbd.submodelpos);
	model->SetRotation(fcbd.submodelquat);
	model->SetScale(fcbd.submodelscale3);
	entity->UnbindCustomObject(boneName);
	entity->BindCunstomObject(boneName, model);
	fcbd.setModel(model);
	fcbd.model2->ShowSkins(fcbd.show);
	fcbd.setTextureId(fcbd.texId);
}

void FullyCustomModel::setBindModelOffset(std::string name, short offsetx, short offsety, short offsetz)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd)
		return;

	fcbd->submodelpos.Set(offsetx, offsety, offsetz);
	m_bNeedSave = true;
	if (fcbd->model2)
	{
		fcbd->model2->SetPosition(fcbd->submodelpos);
	}
}

void FullyCustomModel::setBindModelRotate(std::string name, float yaw, float pitch, float roll)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd)
		return;

	fcbd->submodelquat = XYZAngleToQuat(pitch, yaw, roll);
	m_bNeedSave = true;
	if (fcbd->model2)
	{
		fcbd->model2->SetRotation(true, yaw, pitch, roll);
	}
}

void FullyCustomModel::setBindModelScale(std::string name, float scale)
{
	Vector3f scale3(scale);
	setBindModelScale(name, scale3);
}

void FullyCustomModel::setBindModelScale(std::string name, Vector3f& scale3)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd)
		return;

	fcbd->submodelscale3 = scale3;
	//新接口，兼容老scale。默认使用x
	fcbd->submodelscale = scale3.x;
	m_bNeedSave = true;
	if (fcbd->model2)
	{
		fcbd->model2->SetScale(fcbd->submodelscale3);
	}
}

void FullyCustomModel::setBindModel(std::string name, 
	Vector3f& translate, Quaternionf& rotate, Vector3f& scale3)
{
	MINIW::Transform_ trs(translate, rotate, scale3);
	setBindModel(name, trs);
}

void FullyCustomModel::setBindModel(std::string name, MINIW::Transform_& trs)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd)
		return;

	fcbd->submodelpos = trs.t;
	fcbd->submodelquat = trs.r;
	fcbd->submodelscale3 = trs.s;
	//新接口，兼容老scale。默认使用x
	fcbd->submodelscale = trs.s.x;
	m_bNeedSave = true;
	if (fcbd->model2)
	{
		fcbd->model2->SetPosition(fcbd->submodelpos);
		fcbd->model2->SetRotation(fcbd->submodelquat);
		fcbd->model2->SetScale(fcbd->submodelscale3);
	}
}

void FullyCustomModel::setModelOverlayColor(std::string name, bool show)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(name);
	if (!fcbd) 
	{
		return;
	}
	if (!fcbd->model2)
	{
		return;
	}
	if (show)
	{
		ColourValue color(0, 0, 1.0f);
  		fcbd->model2->SetOverlayColor(&color);
	}
	else
	{
		fcbd->model2->SetOverlayColor(NULL);
	}
}

void FullyCustomModel::setCurSelectBoneEffect(Entity *entity, std::string name)
{
	if (m_CurSelectBoneEffect.effect)
	{
		if(!m_CurSelectBoneEffect.bonename.empty())
			entity->UnbindObject(m_CurSelectBoneEffect.effect);

		if (!name.empty())
			entity->BindCunstomObject(name.c_str(), m_CurSelectBoneEffect.effect);
		
		m_CurSelectBoneEffect.bonename = name;
	}
	else 
	{
		auto effectModel = g_BlockMtlMgr.getModel("entity/custommodel/point.omod");
		if (!effectModel)
			return;
		//effectModel->SetLayer(RL_SCENE_FRONT);
		ColourValue color(0, 1.0f, 1.0f);
		effectModel->SetOverlayColor(&color);
		entity->BindCunstomObject(name.c_str(), effectModel);
		m_CurSelectBoneEffect.bonename = name;
		m_CurSelectBoneEffect.effect = effectModel;
	}
}

bool FullyCustomModel::setModelShow(std::string strBoneName, bool visible)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(strBoneName);
	if (!fcbd)
	{
		return false;
	}
	if(fcbd->model2)
		fcbd->model2->ShowSkins(visible);

	if (m_splitSkeletonModel && fcbd->defaultSkeletonModel)
	{
		fcbd->defaultSkeletonModel->ShowSkins(m_showSkeletonModel && visible);
	}

	fcbd->show = visible;
	m_bNeedSave = true;
	return true;
}

bool FullyCustomModel::delCustomBone(Entity *entity, std::string name, FullyCustomBoneData *fcbdRoot/* =NULL */)
{
	vector<FullyCustomBoneData*>* pvFcbds = &m_vFcbd;
	if (fcbdRoot)
	{
		pvFcbds = &fcbdRoot->vChildFcbds;
	}
	else
	{
		if (name == m_CurSelectBoneEffect.bonename)
		{
			entity->UnbindObject(m_CurSelectBoneEffect.effect);
			m_CurSelectBoneEffect.bonename = "";
		}
	}
	vector<FullyCustomBoneData*>& vFcbds = *pvFcbds;
	auto iter = vFcbds.begin();
	for (; iter != vFcbds.end(); iter++)
	{
		FullyCustomBoneData* fcbd = *iter;
		if (fcbd->name == name)
		{
			fcbd->vChildFcbds.clear();
			ENG_DELETE(fcbd);
			iter = vFcbds.erase(iter);
			return true;
		}

		if (delCustomBone(entity, name, fcbd))
			return true;
	}

	return false;
}

bool FullyCustomModel::changeCustomBoneName(std::string oldname, std::string newname)
{
	if (findFullyCustomBoneData(newname))
		return false;

	auto fcbd = findFullyCustomBoneData(oldname);
	if (!fcbd)
		return false;

	fcbd->name = newname;
	for (FullyCustomBoneData* fcbdChild : fcbd->vChildFcbds)
	{
		fcbdChild->fathername = newname;
	}
	return true;
}

void FullyCustomModel::changeCustomBoneStandard(std::string name, bool isstandard)
{
	auto boneData = findFullyCustomBoneData(name);
	if (!boneData)
		return;
	boneData->isstandard = isstandard;
}

bool FullyCustomModel::changeCustomBoneFather(std::string name, std::string fathername)
{
	auto boneData = findFullyCustomBoneData(name);
	if (!boneData)
		return false;

	if (!fathername.empty())
	{
		if (boneData->fathername == fathername)
			return false;

		auto fatherBoneData = findFullyCustomBoneData(fathername);
		if (!fatherBoneData)
			return false;

		fatherBoneData->vChildFcbds.push_back(boneData);
	}
	else
	{
		m_vFcbd.push_back(boneData);
	}

	auto *bones = &m_vFcbd;		
	if (!boneData->fathername.empty())
	{
		auto oldFather = findFullyCustomBoneData(boneData->fathername);
		if (!oldFather)
			return false;

		bones = &oldFather->vChildFcbds;
	}

	auto iter = bones->begin();
	for (; iter != bones->end(); iter++)
	{
		if ((*iter)->name == name)
		{
			bones->erase(iter);
			break;
		}
	}

	boneData->fathername = fathername;
	return true;
}

bool FullyCustomModel::setModelTexture(std::string modelName, std::string texturePath)
{
	auto pModel = findModelWithName(modelName, m_vFcbd);
	if (pModel)
	{
		SharePtr<Texture2D> ptexture = GetAssetManager().LoadAsset<Texture2D>(texturePath.c_str());
		pModel->SetTexture(ShaderParamNames::g_DiffuseTex, ptexture, modelName.c_str());
		return true;
	}
	return false;
}

bool FullyCustomModel::setTextureId(std::string boneName, std::string texId)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(boneName);
	if (!fcbd)
	{
		return false;
	}
	bool ok = fcbd->setTextureId(texId);
	m_bNeedSave |= ok;
	return ok;
}
