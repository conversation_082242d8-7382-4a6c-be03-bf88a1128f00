# SandboxTimer (MNTimer) 使用指南

## 概述

SandboxTimer 是游戏引擎中的定时器系统，主要类名为 `MNTimer`，位于 `MNSandbox` 命名空间中。提供了高效的定时器管理功能，支持延迟执行、循环执行、暂停/恢复等操作。

## 基本概念

### 时间单位
- **delay**: 延迟执行时间，单位是**秒**（seconds）
- **interval**: 循环间隔时间，单位是**秒**（seconds）
- **游戏内部换算**: 1秒 = 20 tick，每个 tick = 0.05秒 = 50毫秒

### 定时器状态
```cpp
enum class PLAYSTATE
{
    IDLE = 0,   // 空闲
    PLAY,       // 播放状态
    PAUSE,      // 暂停状态
};
```

## 主要接口

### 创建定时器
```cpp
// 基本创建方法
static AutoRef<MNTimer> CreateTimer(double delay, bool loop, double interval, bool defPlay = true);
static AutoRef<MNTimer> CreateTimer(double delay); // 简单版本，只延迟执行一次

// 带回调函数的创建方法
static AutoRef<MNTimer> CreateTimer(CallbackListener callback, double delay, bool loop, double interval, bool defPlay = true);
static AutoRef<MNTimer> CreateTimer(CallbackFunction callback, double delay);
```

### 控制方法
```cpp
void Play();     // 开始
void Pause();    // 暂停
void Resume();   // 恢复
void Stop();     // 停止
void Destroy();  // 销毁
```

### 状态查询
```cpp
bool IsPlaying() const;        // 是否正在播放
double CalcLeftTime();         // 计算剩余时间
bool IsValid();               // 是否有效
```

## 参数说明

- **delay**: 延迟执行时间（秒）
- **loop**: 是否循环执行（true/false）
- **interval**: 循环间隔时间（秒），如果 < 0 则使用 delay 时间
- **defPlay**: 是否默认开始播放（默认 true）

## 使用示例

### 示例1：简单的延迟执行
```cpp
// 创建一个延迟 2.5 秒后执行的定时器
auto timer = MNTimer::CreateTimer(2.5f);
```

### 示例2：带回调函数的定时器
```cpp
void ContainerDecomposition::StartTime(float delay, float interval)
{
    if (!m_timer)
    {
        m_timer = MNTimer::CreateTimer(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>,
            [this](AutoRef<MNTimer> t)
            {
                // 定时器触发时执行的代码
                Process();
            }
        ), delay, true, interval);  // delay: 延迟时间, true: 循环执行, interval: 间隔时间
    }
}
```

### 示例3：使用 Lambda 表达式作为回调
```cpp
m_touRecTimer = MNTimer::CreateTimer(SANDBOX_NEW(ListenerFunctionRef<AutoRef<MNTimer>>, 
    [this, recoverSpeed](AutoRef<MNTimer> t)
    {
        BackPackGrid *grid = this;
        if (grid)
        {
            grid->addToughness(recoverSpeed);
            if (grid->toughness >= grid->getMaxToughness())
            {
                SANDBOX_RELEASE(grid->m_touRecTimer);  // 条件满足时释放定时器
            }
        }
    }), recoverDelay, true, 1.0f);  // recoverDelay: 延迟, true: 循环, 1.0f: 每秒执行一次
```

### 示例4：常见使用模式
```cpp
// 延迟 1 秒后执行一次
auto onceTimer = MNTimer::CreateTimer(1.0f);

// 延迟 2 秒后开始，然后每 0.5 秒重复执行
auto repeatTimer = MNTimer::CreateTimer(callback, 2.0f, true, 0.5f);

// 立即开始，每 3 秒执行一次
auto immediateTimer = MNTimer::CreateTimer(callback, 0.0f, true, 3.0f);
```

## 内存管理

### 智能指针管理
- 使用 `AutoRef<MNTimer>` 智能指针管理内存
- 定时器会自动在管理器中注册和注销

### 释放定时器
```cpp
// 方法1：使用 SANDBOX_RELEASE 宏
SANDBOX_RELEASE(m_timer);

// 方法2：调用 Destroy 方法
timer->Destroy();

// 方法3：使用静态方法
MNTimer::DestroyTimer(timer);
```

## 实际使用建议

### 1. 创建定时器
- 推荐使用 `CreateTimer` 静态方法
- 根据需求选择合适的重载版本

### 2. 设置回调
- 推荐使用 Lambda 表达式
- 注意捕获变量的生命周期

### 3. 循环定时器
- 适合需要重复执行的任务
- 记得在适当时候停止，避免无限循环

### 4. 一次性定时器
- 适合延迟执行的任务
- 执行完毕后会自动清理

### 5. 及时释放
- 不需要时及时释放定时器避免内存泄漏
- 在对象析构函数中释放相关定时器

## 注意事项

### 1. 时间精度
- 定时器的精度依赖于游戏的帧率和 tick 系统
- 最小精度为一个 tick（0.05秒）

### 2. 对象生命周期
- 回调函数中要注意对象的生命周期
- 避免访问已销毁的对象
- 使用智能指针或弱引用来避免悬空指针

### 3. 性能考虑
- 避免创建过多的定时器
- 循环定时器比重复创建一次性定时器更高效

### 4. 线程安全
- 定时器回调在主线程中执行
- 不要在回调中执行耗时操作

## 常见应用场景

1. **技能冷却**: 延迟一定时间后重置技能状态
2. **状态恢复**: 定期恢复玩家的血量、魔法值等
3. **周期性检查**: 定期检查游戏状态、触发事件
4. **动画控制**: 控制动画的播放时机
5. **UI更新**: 定期更新界面显示内容

## 相关常量

```cpp
const float GAME_TICK_TIME = 0.05f;        // 每个tick的时间（秒）
const unsigned int GAME_TICK_MSEC = 50;    // 每个tick的时间（毫秒）
const int TICKS_PER_SECOND = 20;           // 每秒的tick数
```

## 总结

SandboxTimer 提供了一个强大而灵活的定时器系统，适合游戏开发中的各种定时任务。正确使用时需要注意时间单位（秒）、内存管理和对象生命周期等关键点。
