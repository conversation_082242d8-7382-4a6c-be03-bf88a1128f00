#include "ImportCustomModel.h"
#include "ImportCustomModel_generated.h"
#include "File/FileManager.h"
#include "DefManagerProxy.h"
#include "CustomModelMgr.h"
#include "ResourceCenter.h"
#include "OgreUtils.h"
#include "CustomModelMgr.h"
#include "ResourceCenter.h"
#include "OgreUtils.h"
#include "ClientInfoProxy.h"
#include "WorldManager.h"

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
ImportCustomModel::ImportCustomModel():m_sKey(""),
	m_sName(""),
	m_sDesc(""),
	m_iType(0),
	m_iAuthUin(0),
	m_sAuthName(""),
	m_sFileName(""),
	m_bLeaveworlddel(false)
{

}

ImportCustomModel::~ImportCustomModel(){

}

bool ImportCustomModel::load(std::string path, int checkuin)//����
{
	AutoRefPtr<DataStream> pstream = GetFileManager().OpenFile(path.c_str(),true, FileOpType::kFileOpAll);
	if (!pstream)
		return false;

	int buflen = pstream->Size();
	void *buf = malloc(buflen);
	pstream->Read(buf, buflen);
	pstream->Close();
		
	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
	if (!FBSave::VerifyImportCustomModelBuffer(verifier))
	{
		free(buf);
		return false;
	}

	const FBSave::ImportCustomModel *importModel = FBSave::GetImportCustomModel(buf);
	if (importModel == NULL)
	{
		free(buf);
		return false;
	}

	std::string filename;
	if (importModel->key())
	{
		m_sKey = importModel->key()->c_str();
		filename = m_sKey;
	}
	if (importModel->name()) {
		m_sName = importModel->name()->c_str();
		std::string nameKey(filename);
		nameKey.append("_name");
		if (!m_sName.empty())
		{
			//外部导入模型  TRANSLATE_TYPE
			m_sName = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)6, nameKey, m_sName);
		}
	}
	if (importModel->desc()) {
		m_sDesc = importModel->desc()->c_str();
		std::string descKey(filename);
		descKey.append("_desc");
		if (!m_sDesc.empty())
		{
			//外部导入模型  TRANSLATE_TYPE
			m_sDesc = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)6, descKey, m_sDesc);
		}
	}
	m_iType = importModel->type();
	m_iAuthUin = importModel->authuin();
	if(importModel->authorname()){
		m_sAuthName = importModel->authorname()->c_str();
	}
	if (importModel->filename()){
		m_sFileName = importModel->filename()->c_str();
	}
	
	bool bCheck = checkuin > 0;
	auto resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename);

	//不是地图作者下载的资源,也不是自己下载的资源，也不是地图模板UIN列表内的 不允许加载
	if (resInfo && ((bCheck && checkuin != resInfo->download_uin) &&
							 resInfo->download_uin != GetClientInfoProxy()->getUin() &&
							 (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false) ))
	{
		free(buf);
		return false;
	}

	free(buf);
	return true;
}
bool ImportCustomModel::save(std::string path, std::string skey, std::string name, std::string desc, int itype, int authuin, std::string authname, std::string filename)//����
{
	m_sKey = skey;
	m_sName = name;
	m_sDesc = desc;
	m_iAuthUin = authuin;
	m_sAuthName = authname;
	m_iType = itype;
	m_sFileName = filename;

	flatbuffers::FlatBufferBuilder builder;

	auto icm = FBSave::CreateImportCustomModel(builder, 
		builder.CreateString(m_sKey), 
		builder.CreateString(m_sName), 
		builder.CreateString(m_sDesc), 
		(int16_t)m_iType,
		m_iAuthUin,
		builder.CreateString(m_sAuthName),
		builder.CreateString(m_sFileName));
	builder.Finish(icm);

	return GetFileManager().SaveToWritePath(path.c_str(), builder.GetBufferPointer(), builder.GetSize());
}

void ImportCustomModel::initWithData(std::string skey, std::string name, std::string desc, int itype, int authuin, std::string authname, std::string filename)//����
{
	m_sKey = skey;
	m_sName = name;
	m_sDesc = desc;
	m_iAuthUin = authuin;
	m_sAuthName = authname;
	m_iType = itype;
	m_sFileName = filename;
}