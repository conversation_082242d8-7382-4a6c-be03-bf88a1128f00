#pragma once
#include "geometrySolid/Vector3db.h"
//#include "geometrySolid/Matrix4x4f.h"
namespace MNSandbox {
	namespace GeometrySolid {
		//保留这些注释，需要时再启用
		class Raydb
		{
		public:
			Vector3db    m_Origin;
			Vector3db    m_Direction; // Direction is always normalized

		public:
			Raydb() {}
			Raydb(const Vector3db& orig, const Vector3db& dir) { m_Origin = orig; SetDirection(dir); }

			const Vector3db& GetDirection() const { return m_Direction; }
			// Direction has to be normalized
			void SetDirection(const Vector3db& dir) { Assert(dir.IsNormalized()); m_Direction = dir; }
			void SetApproxDirection(const Vector3db& dir) { m_Direction = dir.GetNormalized(); }
			void SetOrigin(const Vector3db& origin) { m_Origin = origin; }

			const Vector3db& GetOrigin() const { return m_Origin; }
			Vector3db GetPoint(double t) const { return m_Origin + t * m_Direction; }

			double SqrDistToPoint(const Vector3db &v) const;
		};

		//FORCEINLINE void MultiplyRay(const Raydb& ray, const Matrix4x4f& matrix, Raydb& res)
		//{
		//	res.m_Origin = matrix.MultiplyPoint3(ray.m_Origin);
		//	res.m_Direction = matrix.MultiplyVector3(ray.m_Direction);
		//}

		//FORCEINLINE Raydb MultiplyRay(const Raydb& ray, const Matrix4x4f& matrix)
		//{
		//	Raydb result;
		//	result.m_Origin = matrix.MultiplyPoint3(ray.m_Origin);
		//	result.m_Direction = matrix.MultiplyVector3(ray.m_Direction);
		//	return result;
		//}
	}
}


