#include "IPlayerControl.h"
#include "IClientActor.h"
#include "IClientPlayer.h"

static IPlayerControl* s_instance = nullptr;

IPlayerControl::IPlayerControl()
{
	s_instance = this;
}
IPlayerControl::~IPlayerControl()
{
	s_instance = nullptr;
}
IPlayerControl* GetIPlayerControl()
{
	return s_instance;
}
void SetIPlayerControl(IPlayerControl* controlptr)
{
	s_instance = controlptr;
}
IClientActor* IPlayerControl::ControlCastToActor()
{
	return dynamic_cast<IClientActor*>(this);
}
IClientPlayer* IPlayerControl::CastToPlayer()
{
	return dynamic_cast<IClientPlayer*>(this);
}