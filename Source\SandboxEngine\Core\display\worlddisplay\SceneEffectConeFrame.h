#pragma once
/**
* file : SceneEffectConeFrame
* func : 场景效果 （圆锥、圆台框）
* by : pengdapu
*/
#include "SceneEffectGeom.h"
#include "world_types.h"
#include "SandboxRay.h"

class SceneEffectEllipse;
class SceneEffectLine;

class SceneEffectConeFrame : public SceneEffectGeom
{
public:
	SceneEffectConeFrame();
	virtual ~SceneEffectConeFrame();
	void OnClear() override;
	void Refresh() override;
	void OnDraw(World* pWorld) override;
	bool IsActive(World* pWorld) const override;
	void SetTRS(const Rainbow::Vector3f& vc, const Rainbow::Quaternionf& q, const Rainbow::Vector3f& vs) override;
public:
	void SetRadius(float radius) override;
	void SetHeight(float h);
	void SetAngle(float angle);
private:
	SceneEffectLine* m_aGeneratrices[4] = {nullptr, nullptr, nullptr, nullptr, };
	/**
	@brief	原点中心默认在底部
	 */
	SceneEffectEllipse* m_ellipseD = nullptr;
	
	SceneEffectEllipse* m_ellipseU = nullptr;
	/**
	@brief	上方半径。下方使用父类的m_fRadius
	 */
	float m_fUpRadius;
	/**
	@brief	圆锥母线与高的夹角
	 */
	float m_fAngle;
};
