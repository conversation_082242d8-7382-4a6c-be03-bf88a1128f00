
#include "EffectDestroyBlock.h"
#include "defdata.h"
#include "world.h"
#include "Particle/LegacyParticleSystem.h"
#include "blocks/BlockMaterialMgr.h"
#include "BlockScene.h"
#include "WorldManager.h"
#include "MiniCraftRenderer.h"
#include "coreMisc.h"
#include "SandboxIdDef.h"
#include "PlayManagerInterface.h"

using namespace MINIW;
using namespace Rainbow;
static void SetParticleBiomeColor(ParticleNode *pnode, World *pworld, const WCoord &blockgrid)
{
	if (pworld == NULL)
	{
		return;
	}
	const BiomeDef *def = pworld->getBiome(blockgrid.x, blockgrid.z);
	if(def == NULL)
	{
		return;
	}
	for(int i=0; i<3; i++)
	{
		if(pnode != NULL)
		{
			pnode->m_CurDesc.colorkeys[i] = ColorRGBA32((def->GrassColor & 0xff0000) >> 16, (def->GrassColor & 0x00ff00) >> 8, (def->GrassColor & 0x0000ff), 255);
			//pnode->m_CurDesc.colorkeys[i].a = 1.0f;
		}
	}
}

static void SetColoredBlockColor(ParticleNode *pnode, Block pblock, BlockMaterial *pmtl)
{
	BlockColor color = pmtl->getBlockColor(pblock.getData());
	
	for (int i = 0; i < 3; i++)
	{
		pnode->m_CurDesc.colorkeys[i] = color;
		pnode->m_CurDesc.colorkeys[i].a = 1.0f;
	}
}

EffectDestroyBlock::EffectDestroyBlock(World* pworld, int subtype, const WCoord& pos, DirectionType dir, int age, int id)
	:m_Pos(pos), m_Face(dir), m_Age(age), m_LiveTick(0)
{
    METRIC_GAUGE_INCREMENT(game_mapplay_info, { "type", "particle" }, { "obj", "EffectDestroyBlock" });
	m_UpdateAliveTime = Rainbow::EffectMaxAliveTime;
	BlockTexDesc desc;
	WCoord blockgrid = CoordDivBlock(pos);

	if (subtype == 0 || subtype == 2)
	{
		Block pblock = pworld->getBlock(blockgrid);
		if (pblock.isEmpty()) return;
		BlockMaterial* pmtl = NULL;
		if (id > 0)
			pmtl = g_BlockMtlMgr.getMaterial(id);
		else
			pmtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
		if (pmtl == NULL)
			return;

		// 堆叠方块需要获取核心方块的破坏贴图
		//排除BlockPile
		if (GetISandboxActorSubsystem()->BlockPileExclude(pworld, blockgrid, pmtl, pblock))
		{
			return;
		}

		auto ptex = pmtl->getDestroyTextureWithPos(pblock, desc, pworld, blockgrid);
		if (ptex == NULL) return;
// 		memcpy(desc.uvtile, ptex->m_UVTile, sizeof(desc.uvtile));
		if (pblock.getResID() != BLOCK_TREE_CORAL) // 部分方块贴图有透明区域导致修改uv后取到空的rgb code-by:lizb
		{
			const BlockClientSetting& setting = GetMiniCraftRenderer().GetClientSetting();
			desc.uvtile[2] = desc.uvtile[3] = setting.m_PackTextureSize / (float)setting.m_AtlasTextureSize;

			desc.uvtile[0] *= desc.uvtile[2];
			desc.uvtile[1] *= desc.uvtile[2];
// 			desc.uvtile[0] = ptex->m_UVTile[0] * desc.uvtile[2];
// 			desc.uvtile[1] = ptex->m_UVTile[1] * desc.uvtile[2];
		}
		
		Rainbow::ParticleTemplate* partmtl;
		WCoord posTemp = pos;
		if (subtype == 0)
		{
			partmtl = GetWorldManagerPtr()->getParticleMgr()->getTemplate("block_destroyed");
		}
		else
		{
			partmtl = GetWorldManagerPtr()->getParticleMgr()->getTemplate("block_destroying_2");
			int particlePos = 50;
			int particlePos_y = GenRandomInt(-35, 35);
			int particlePos_z = GenRandomInt(-35, 35);
			int particlePos_x = GenRandomInt(-35, 35);
			partmtl->m_Desc.sizevar = GenRandomFloatRange(0.1f,0.9f);
			if (dir == DIR_NEG_X)
			{
				posTemp = posTemp + WCoord(-particlePos, particlePos_y, particlePos_z);
				partmtl->m_Desc.gravitydir = Vector3f(-0.1f, -1.0f, 0);
				partmtl->m_Desc.spread = 50.0f;
			}
			else if (dir == DIR_POS_X)
			{
				posTemp = posTemp + WCoord(particlePos, particlePos_y, particlePos_z);
				partmtl->m_Desc.gravitydir = Vector3f(0.1f, -1.0f, 0);
				partmtl->m_Desc.spread = 50.0f;
			}
			else if (dir == DIR_NEG_Z)
			{
				posTemp = posTemp + WCoord(particlePos_x, particlePos_y, -particlePos);
				partmtl->m_Desc.gravitydir = Vector3f(0, -1.0f, -0.1f);
				partmtl->m_Desc.spread = 50.0f;
			}
			else if (dir == DIR_POS_Z)
			{
				posTemp = posTemp + WCoord(particlePos_x, particlePos_y, particlePos);
				partmtl->m_Desc.gravitydir = Vector3f(0, -1.0f, 0.1f);
				partmtl->m_Desc.spread = 50.0f;
			}
			else if (dir == DIR_NEG_Y)
			{
				posTemp = posTemp + WCoord(particlePos_x, -particlePos, particlePos_z);
				partmtl->m_Desc.gravitydir = Vector3f(0, -1.0f, 0);
				partmtl->m_Desc.spread = 80.0f;
				partmtl->m_Desc.needUp = false;
			}
			else if (dir == DIR_POS_Y)
			{
				posTemp = posTemp + WCoord(particlePos_x, particlePos, particlePos_z);
				partmtl->m_Desc.gravitydir = Vector3f(0, -1.0f, 0);
				partmtl->m_Desc.spread = 80.0f;
			}
		}
		if (partmtl) {
			ParticleNode* ptnode = ParticleNode::Create(GetWorldManagerPtr()->getParticleMgr(), partmtl);

			ptnode->SetTexture(ptex/*->getTexture()*/.CastTo<Texture>(), desc.uvtile);
			if (desc.gray) SetParticleBiomeColor(ptnode, pworld, blockgrid);
			if (pmtl->isColorableBlock() && !pmtl->isUseCustomModel()) SetColoredBlockColor(ptnode, pblock, pmtl);

			ptnode->SetPosition(posTemp.toWorldPos());

			m_PtNode = ptnode;
		}
	}
	else if (subtype == 1)
	{
		Block pblock = pworld->getBlock(blockgrid);
		if (pblock.isEmpty()) return;
		BlockMaterial* pmtl = NULL;
		if (id > 0)
			pmtl = g_BlockMtlMgr.getMaterial(id);
// 		else
// 			g_BlockMtlMgr.getMaterial(pblock.getResID());
		if (pmtl == NULL) return;
		auto ptex = pmtl->getDestroyTextureWithPos(pblock, desc, pworld, blockgrid);
		if (ptex == NULL) return;
// 		memcpy(desc.uvtile, ptex->m_UVTile, sizeof(desc.uvtile));

		Rainbow::ParticleTemplate* partmtl = GetWorldManagerPtr()->getParticleMgr()->getTemplate("block_destroying");
		if (partmtl) {
			ParticleNode* ptnode = ParticleNode::Create(GetWorldManagerPtr()->getParticleMgr(), partmtl);
			ptnode->SetTexture(ptex/*->getTexture()*/.CastTo<Texture>(), desc.uvtile);
			if (desc.gray) SetParticleBiomeColor(ptnode, pworld, blockgrid);
			if (pmtl->isColorableBlock()) SetColoredBlockColor(ptnode, pblock, pmtl);
			Quaternionf quat;
			if (dir == DIR_NEG_X)
			{
				quat = Rainbow::AxisAngleToQuaternionf(Vector3f::zAxis, kHalfPI);
			}
			else if (dir == DIR_POS_X)
			{
				quat = Rainbow::AxisAngleToQuaternionf(Vector3f::zAxis, -kHalfPI);
			}
			else if (dir == DIR_NEG_Y)
			{
				quat = Rainbow::AxisAngleToQuaternionf(Vector3f::xAxis, kOnePI);
			}
			else if (dir == DIR_POS_Y)
			{
			}
			else if (dir == DIR_NEG_Z)
			{
				quat = Rainbow::AxisAngleToQuaternionf(Vector3f::xAxis, -kHalfPI);
			}
			else if (dir == DIR_POS_Z)
			{
				quat = Rainbow::AxisAngleToQuaternionf(Vector3f::xAxis, kHalfPI);
			}

			ptnode->SetPosition(pos.toWorldPos());
			ptnode->SetRotation(quat);

			m_PtNode = ptnode;
		}
	}

	if (m_PtNode) {
		m_PtNode->AttachToScene(pworld->getScene());
		//更新一次绑定盒位置
		m_PtNode->updateWorldCache();
	}
}


EffectDestroyBlock::~EffectDestroyBlock()
{
    METRIC_GAUGE_DECREMENT(game_mapplay_info, { "type", "particle" }, { "obj", "EffectDestroyBlock" });

	DESTORY_GAMEOBJECT_BY_COMPOENT(m_PtNode);

}

void EffectDestroyBlock::SetPosition(const WCoord& pos)
{
	if (m_PtNode)
	{
		m_PtNode->SetPosition(pos.toWorldPos());
	}
}

Rainbow::AABB EffectDestroyBlock::GetAABB()
{
	if (m_PtNode) {
		//
		return m_PtNode->GetWorldBounds();
	}
	return Rainbow::AABB(m_Pos.toWorldPos().toVector3(), BLOCK_FSIZE / 2.0f);
}

void EffectDestroyBlock::tick()
{
	m_LiveTick++;
	if(m_LiveTick >= m_Age) setNeedClear();
}

void EffectDestroyBlock::update(float dtime)
{
	if(m_PtNode) m_PtNode->UpdateTick(Rainbow::TimeToTick(dtime));
}

//-------------------------------------------------------------------------------------------------------------------------------------------------------
#include "BlockDecalMesh.h"

EffectCrackBlock::EffectCrackBlock(World *pworld, const WCoord &blockpos, const char* texture) : m_World(pworld), m_BlockPos(blockpos)
{
    METRIC_GAUGE_INCREMENT(game_mapplay_info, { "type", "particle" }, { "obj", "EffectCrackBlock" });
	// if (strlen(g_ModMgr.tryGetDestroyTexture()) == 0)
	// {
	// 	m_Decal = ENG_NEW(BlockDecalMesh)("destroy", GETTEX_STAGES);
	// }
	// else
	// {
	// 	m_Decal = ENG_NEW(BlockDecalMesh)(g_ModMgr.tryGetDestroyTexture(), GETTEX_STAGES);
	// }
	if(texture == NULL || strlen(texture) == 0)
	{
		texture = "destroy";
	}
	m_Decal = BlockDecalMesh::Create(texture, GETTEX_STAGES);// ENG_NEW(BlockDecalMesh)(texture, GETTEX_STAGES);
	m_Decal->SetCustomMaterial();
	m_CurBlockID = pworld->getBlockID(blockpos);
}

EffectCrackBlock::~EffectCrackBlock()
{
    METRIC_GAUGE_DECREMENT(game_mapplay_info, { "type", "particle" }, { "obj", "EffectCrackBlock" });
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_Decal);
}

void EffectCrackBlock::tick()
{
	if(m_World->getBlockID(m_BlockPos) != m_CurBlockID) setNeedClear();
	else if (m_nLiveTime > 0)
	{
		m_nLiveTime--;
		if (m_nLiveTime <= 0) setNeedClear();
	}
}

void EffectCrackBlock::update(float dtime)
{

}

Rainbow::AABB EffectCrackBlock::GetAABB()
{
	return Rainbow::AABB(m_BlockPos.toWorldPos().toVector3(), BLOCK_FSIZE / 2.0f);
}

int EffectCrackBlock::getCrackProcess()
{
	int p = 0;
	for(size_t i=0; i<m_ActorCracks.size(); i++)
	{
		if(p < m_ActorCracks[i].process) p = m_ActorCracks[i].process;
	}

	return p;
}

void EffectCrackBlock::setCrackProcess(int process, WORLD_ID actorid)
{
	bool findactor = false;
	for(size_t i=0; i<m_ActorCracks.size(); i++)
	{
		if(m_ActorCracks[i].actorid == actorid)
		{
			if(process < 0)
			{
				m_ActorCracks.erase(m_ActorCracks.begin()+i);
				if(m_ActorCracks.empty())
				{
					setNeedClear();
					return;
				}
			}
			else m_ActorCracks[i].process = process;

			findactor = true;
			break;
		}
	}

	if(!findactor && process >= 0)
	{
		ActorCrackData data;
		data.actorid = actorid;
		data.process = process;
		m_ActorCracks.push_back(data);
	}

	if (needClear())
		return;
	
	if (!m_Decal->IsInScene()) m_Decal->AttachToScene(m_World->getScene());
	m_Decal->Show(true);
	m_Decal->setBlock(m_World, m_BlockPos, getCrackProcess());
}

