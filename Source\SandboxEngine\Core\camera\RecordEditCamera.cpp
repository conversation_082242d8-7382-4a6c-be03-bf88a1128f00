#include "IPlayerControl.h"
#include "IClientActor.h"
#include "RecordEditCamera.h"
#include "OgrePrerequisites.h"
#include "CameraManager.h"
#include "GameCamera.h"

#include "TPSCamera.h"
#include "OgreScriptLuaVM.h"
#include "Common/OgreShared.h"
#include "IClientGameManagerInterface.h"
#include "IClientGameInterface.h"
#include "ClientInfoProxy.h"
#include "Input/InputManager.h"
#include "Graphics/ScreenManager.h"

using namespace Rainbow;
using namespace MINIW;
RecordEditCamera::RecordEditCamera(CameraManager* cameraManager) : TPSCamera(cameraManager), m_IsInit(false),
                                                                   m_PreRotateY(0),
                                                                   m_PreRotateX(0)
{
	m_RotY = 0.0f;
	m_RotX = 0.0f;
	m_MoveStrafe = 0.0f;
	m_MoveForward = 0.0f;
	m_MoveUp = 0.0f;

	m_RotateID = -1;
}

RecordEditCamera::~RecordEditCamera()
{

}

void RecordEditCamera::update(float deltaSeconds)
{
	m_pCameraManager->m_GameCamera->applyToEngine(GetIPlayerControl()->getIWorld());

	if (abs(m_MoveForward) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveForward(m_MoveForward);
	}
	if (abs(m_MoveStrafe) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveSide(m_MoveStrafe);
	}
	if (abs(m_MoveUp) > 0.1f)
	{
		m_pCameraManager->m_GameCamera->moveUp(m_MoveUp);
	}

	m_pCameraManager->m_GameCamera->UpdateEngineCamera(deltaSeconds);
}

int RecordEditCamera::onInputEvent(const Rainbow::InputEvent &event)
{
	if (GetClientInfoProxy()->isMobile())
	{
		onTouchInputEvent(event);
	}
	else
	{
		if (!onPcInputEvent(event))
		{
			 return INPUTMSG_PASS; 
		}
	}

	return INPUTMSG_HANDLED;
}

int RecordEditCamera::onPcInputEvent(const Rainbow::InputEvent &event)
{
	m_RotX = m_RotY = 0.0f;

	for (int i = Rainbow::SDLK_1; i < Rainbow::SDLK_9; i++)
	{
		if ((event.type == InputEvent::kKeyDown || event.type == InputEvent::kKeyUp) && event.keycode == i)
		{
			return false;
		}
	}

	if (event.type == InputEvent::kKeyDown && event.keycode == SDLK_ESCAPE)
	{

	}
	else
	{
		if (GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isOperateUI())
		{
			return true;
		}
	}


	if (!GetIPlayerControl() || !GetIPlayerControl()->getPCControl())
	{
		return false;
	}

	if (event.type == InputEvent::kMouseMove)
	{
		Rectf rect = GetScreenManagerPtr()->GetRect();
		const int w = rect.GetWidth();
		const int h = rect.GetHeight();
		//int dx = event.mouse.x - m_PreMousePos.x;
		//int dy = event.mouse.y - m_PreMousePos.y;

		int dx = event.mousePosition.x - w / 2;
		int dy = event.mousePosition.y - h / 2;

		LOG_INFO("camera rotate: p1(%d, %d)", event.mousePosition.x, event.mousePosition.y);
		float pcSensity = 1.0f;
		bool isSightMode = false;
		bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<bool&, float&>("PlayerCtr_MouseMove", isSightMode, pcSensity);
		Assert(result);
		if (isSightMode)
		{
			if (m_IsInit)
			{
				if (abs(dx) >= 2 || abs(dy) >= 2)
				{
					m_RotX = float(dx) * pcSensity * 0.75f * 0.75f * 1.0f / w;
					m_RotY = float(dy) * pcSensity * 0.75f * 0.75f * 1.0f / h;
					m_pCameraManager->m_GameCamera->rotate(m_RotX, m_RotY);
					Rainbow::SetCursorPos(w / 2, h / 2);
				}
			}
			//m_PreMousePos.x = event.mouse.x;
			//m_PreMousePos.y = event.mouse.y;

			//if (abs(m_PreMousePos.x - w) < 4)
			//{
			//	m_PreMousePos.x -= 50;
			//}
			//else if (abs(m_PreMousePos.x - 0) < 4)
			//{
			//	m_PreMousePos.x += 50;
			//}
			m_IsInit = true;
		}
	}
	else if (event.type == InputEvent::kKeyDown)
	{
		float speed = 4.f;
		if (event.keycode == SDLK_w)
		{
			m_MoveForward += speed;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward += -speed;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe += -speed;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe += speed;
		}
		else if (event.keycode == SDLK_SPACE)
		{
			m_MoveUp += speed;
		}
		else if (event.modifiers & InputEvent::kShift)
		{
			m_MoveUp += -speed;
		}
		else if (event.keycode == SDLK_BACKQUOTE/*VK_OEM_3*/)
		{
			//GetIPlayerControl()->getPCControl()->setSightModel(!GetIPlayerControl()->getPCControl()->isSightMode());
			bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<>("PlayerCtr_RecordEditCamera_setSightModel");
			Assert(result);

			if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
			{
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
				bool isGuideState = true;
				MNSandbox::GetGlobalEvent().Emit<bool&,const char*>("ClientAccountMgr_getNoviceGuideState", isGuideState,"guidekey");
				if (!isGuideState)
				{
					MNSandbox::GetGlobalEvent().Emit<const char*, bool>("ClientAccountMgr_setNoviceGuideState","guidekey", true);
					MINIW::ScriptVM::game()->callFunction("GuideKey_Finish", "");
				}
			}
			m_IsInit = false;
		}
		else if (event.keycode == SDLK_ESCAPE)
		{
			int tmp = GetClientInfoProxy()->getGameData("hideui");
			MINIW::ScriptVM::game()->callFunction("AccelKey_Escape", "b", GetIPlayerControl()->ControlCastToActor()->isDead());
			if (tmp)
			{
				if (GetIClientGameManagerInterface()->getICurGame()->isInGame())
				{
					GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->ResetOperateState();
				}
			}
		}
	}
	else if (event.type == InputEvent::kKeyUp)
	{
		if (event.keycode == SDLK_w)
		{
			m_MoveForward = 0.0f;
		}
		else if (event.keycode == SDLK_s)
		{
			m_MoveForward = 0.0f;
		}
		else if (event.keycode == SDLK_a)
		{
			m_MoveStrafe = 0.0f;
		}
		else if (event.keycode == SDLK_d)
		{
			m_MoveStrafe = 0.0f;
		}
		else if (event.keycode == SDLK_SPACE)
		{
			m_MoveUp = 0.0f;
		}
		else if (event.modifiers & InputEvent::kShift)
		{
			m_MoveUp = 0.0f;
		}
	}

	return true;
}

int RecordEditCamera::onTouchInputEvent(const Rainbow::InputEvent &event)
{
	if (GetIPlayerControl() && GetIPlayerControl()->getTouchControl())
	{
		float moveup = 0.0f;
		float moveforward = m_MoveForward;
		float movestrafe = m_MoveStrafe;
		bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<float&, float&, float&, const Rainbow::InputEvent&>("Playercontrol_RecordEditCamera_Touch", moveup, moveforward, movestrafe, event);
		Assert(result);

		m_MoveUp = moveup;
		m_MoveForward = moveforward;
		m_MoveStrafe = movestrafe;

		
	}
	return true;
}

void RecordEditCamera::onSwitchTo()
{
	m_IsInit = false;

	m_RotY = 0.0f;
	m_RotX = 0.0f;
	m_MoveStrafe = 0.0f;
	m_MoveForward = 0.0f;
	m_MoveUp = 0.0f;
	
	// ���˺������ǰλ�ü������
	//m_CameraTransform.pos = m_pCameraManager->m_GameCamera->getPosition().toVector3();
	//m_CameraTransform.rot = m_pCameraManager->m_GameCamera->getRotation();
	if (GetIPlayerControl())
	{
		bool result = GetIPlayerControl()->ControlCastToActor()->Event2().Emit<>("Playercontrol_Switch");
		Assert(result);
	}
}