#pragma once
#include "geometrySolid/GeoSolidEnum.h"
#include "FrameworkPrefix.h"
#include "Math/FloatConversion.h"
#include "Math/Vector3f.h"
//#include "Serialize/SerializeUtility.h"
//#include "Utilities/Word.h"
//#include "Serialize/Reflect/ReflectUtility.h"
namespace MNSandbox {
	namespace GeometrySolid {
		//保留这些注释，需要时再启用
		class Vector3db
		{
			//DECLARE_REFLECT();
		public:

			//DEFINE_GET_TYPESTRING(Vector3db)
			//template<class TransferFunction> void Transfer(TransferFunction& transfer);

			FORCEINLINE Vector3db() {}
			FORCEINLINE Vector3db(const Vector3db &rhs) : x(rhs.x), y(rhs.y), z(rhs.z) {}
			FORCEINLINE Vector3db(double x1, double y1, double z1) : x(x1), y(y1), z(z1) {}
			FORCEINLINE Vector3db(double v) : x(v), y(v), z(v) {}
			FORCEINLINE Vector3db(double* v) : x(v[0]), y(v[1]), z(v[2]) {}
			FORCEINLINE Vector3db(const Rainbow::Vector3f &v) : x(v.x), y(v.y), z(v.z) {}

			FORCEINLINE double* GetPtr() { return &x; }
			FORCEINLINE const double* GetPtr() const { return &x; }
			FORCEINLINE void Set(const double* array) { x = array[0]; y = array[1]; z = array[2]; }
			FORCEINLINE void Set(double x1, double y1, double z1) { x = x1; y = y1; z = z1; }
			FORCEINLINE void Set(double* p) { x = p[0]; y = p[1]; z = p[2]; }
			FORCEINLINE void SetZero() { x = y = z = 0.0; }

			FORCEINLINE Rainbow::Vector3f ToFloat() const
			{
				return Rainbow::Vector3f((float)x, (float)y, (float)z);
			}

			//FORCEINLINE Vector3db Rcp(double epsilon = kEpsilon)const
			//{
			//	return Vector3db(fabs(x) > epsilon ? 1.0f / x : 0.0f, fabs(y) > epsilon ? 1.0f / y : 0.0f, fabs(z) > epsilon ? 1.0f / z : 0.0f);
			//}

			FORCEINLINE double Length() const
			{
				return sqrt(x * x + y * y + z * z);
			}

			FORCEINLINE double LengthSqr() const
			{
				return (x * x + y * y + z * z);
			}

			FORCEINLINE Vector3db GetNormalized() const
			{
				Vector3db result;
				const double len = Length();
				//AssertMsg(len > 1e-6, "GetNormalized() return a NAN value, please use GetNormalizedSafe()!");
				const double scale = 1.0 / len;
				return Vector3db(x * scale, y * scale, z * scale);
			}

			FORCEINLINE Vector3db GetNormalizedSafe(double epsilon = kEpsilon) const
			{
				const double len = Length();
				if (len > epsilon)
				{
					return (*this) / len;
				}
				return Vector3db::zero;
			}

			/**
			 @fn  double NormalizeSafe(double epsilon = kEpsilon)
			 @brief 自身归一化，并且返回之前vector的长度，有0长度的安全检查
			 @param 	epsilon (Optional) The epsilon.
			 @returns A double.
			 */
			FORCEINLINE double NormalizeSafe(double epsilon = kEpsilon)
			{
				double len = Length();
				if (len > epsilon)
				{
					(*this) /= len;
					return len;
				}
				else
				{
					(*this) = Vector3db::zero;
					return 0.0;
				}
			}

			/**
			 @fn  double NormalizeSafe(double epsilon = kEpsilon)
			 @brief 自身归一化，并且返回之前vector的长度
			 @param 	epsilon (Optional) The epsilon.
			 @returns A double.
			 */
			FORCEINLINE double Normalize()
			{
				double len = Length();
				//AssertMsg(len > kEpsilon, "Normalize() return a NAN value, please use NormalizeSafe()!");
				(*this) /= len;
				//AssertMsg(!(IsNAN(x) || IsNAN(y) || IsNAN(z)), "Normalize() get a NAN value, please use NormalizeSafe()!");
				return len;
			}

			FORCEINLINE bool IsNormalized(double epsilon = kEpsilon) const
			{
				return Rainbow::Abs(Length() - 1.0) < epsilon;
			}

			FORCEINLINE void Truncate(double maxLength)
			{
				if (Length() > maxLength)
				{
					Normalize();
					(*this) *= maxLength;
				}
			}

			FORCEINLINE Vector3db& Scale(const Vector3db& inV)
			{
				x *= inV.x; y *= inV.y; z *= inV.z;
				return *this;
			}

			FORCEINLINE Vector3db& operator = (const Vector3db &v)
			{
				x = v.x; y = v.y; z = v.z;
				return *this;
			}

			FORCEINLINE Vector3db& operator += (const Vector3db &v)
			{
				x += v.x; y += v.y; z += v.z;
				return *this;
			}

			FORCEINLINE Vector3db& operator -= (const Vector3db &v)
			{
				x -= v.x; y -= v.y; z -= v.z;
				return *this;
			}

			FORCEINLINE Vector3db& operator *= (double s)
			{
				x *= s; y *= s; z *= s;
				return *this;
			}

			FORCEINLINE Vector3db& operator *= (const Vector3db& v)
			{
				x *= v.x;
				y *= v.y;
				z *= v.z;
				return *this;
			}

			FORCEINLINE Vector3db& operator /= (const double s)
			{
				double invS = 1.0 / s;
				x *= invS; y *= invS; z *= invS;
				return *this;
			}

			FORCEINLINE Vector3db& operator /= (const Vector3db& v)
			{
				x /= v.x; y /= v.y; z /= v.z;
				return *this;
			}

			FORCEINLINE Vector3db operator/(const Vector3db &v)const
			{
				return Vector3db(x / v.x, y / v.y, z / v.z);
			}

			FORCEINLINE Vector3db operator/(const double s)const
			{
				return Vector3db(x / s, y / s, z / s);
			}

			FORCEINLINE operator double * ()
			{
				return &x;
			}

			FORCEINLINE operator const double * () const
			{
				return &x;
			}

			FORCEINLINE Vector3db operator + () const
			{
				return *this;
			}

			FORCEINLINE Vector3db operator - () const
			{
				return Vector3db(-x, -y, -z);
			}

			FORCEINLINE bool operator == (const Vector3db &v) const
			{
				return (x == v.x && y == v.y && z == v.z);
			}
			FORCEINLINE bool operator != (const Vector3db& v) const
			{
				return (x != v.x || y != v.y || z != v.z);
			}

			FORCEINLINE Vector3db operator + (const Vector3db &v) const
			{
				return Vector3db(x + v.x, y + v.y, z + v.z);
			}

			FORCEINLINE Vector3db operator + (const double val) const
			{
				return Vector3db(x + val, y + val, z + val);
			}

			FORCEINLINE Vector3db operator - (const Vector3db &v) const
			{
				return Vector3db(x - v.x, y - v.y, z - v.z);
			}

			FORCEINLINE Vector3db operator - (const double val) const
			{
				return Vector3db(x - val, y - val, z - val);
			}

			FORCEINLINE Vector3db operator * (const Vector3db &v) const
			{
				return Vector3db(x * v.x, y * v.y, z * v.z);
			}

			FORCEINLINE Vector3db operator * (const double s) const
			{
				return Vector3db(x * s, y * s, z * s);
			}
			//这个是只是拿来测试打印用的
			//FORCEINLINE core::string ToString() const
			//{
			//	return Format("x:%f,y:%f,z:%f", x, y, z);
			//}

			FORCEINLINE bool ContainsNaN() const
			{
				return 
					!Rainbow::IsFinite(x) ||
					!Rainbow::IsFinite(y)
				;
			}


			friend FORCEINLINE Vector3db operator * (const double s, const Vector3db& v)
			{
				return Vector3db(s * v.x, s * v.y, s * v.z);
			}

			static inline Vector3db Cross(const Vector3db& lhs, const Vector3db& rhs)
			{
				return Vector3db(
					lhs.y * rhs.z - lhs.z * rhs.y,
					lhs.z * rhs.x - lhs.x * rhs.z,
					lhs.x * rhs.y - lhs.y * rhs.x);
			}

			// 特定值
			static const Vector3db one;
			static const Vector3db zero;
			static const Vector3db infinity;
			static const Vector3db xAxis;
			static const Vector3db yAxis;
			static const Vector3db zAxis;
			static const Vector3db neg_xAxis;
			static const Vector3db neg_yAxis;
			static const Vector3db neg_zAxis;

			union
			{
				struct
				{
					double x, y, z;
				};
			};
		};

		FORCEINLINE bool IsZeroVector(const Vector3db &v, const double epsilon = kEpsilon)
		{
			return 
				Rainbow::Abs(v.x) < epsilon &&
				Rainbow::Abs(v.y) < epsilon && 
				Rainbow::Abs(v.z) < epsilon;
		}

		FORCEINLINE Vector3db Abs(const Vector3db &v)
		{
			return Vector3db(Rainbow::Abs(v.x), Rainbow::Abs(v.y), Rainbow::Abs(v.z));
		}

		FORCEINLINE double Distance(const Vector3db &p1, const Vector3db &p2)
		{
			return sqrt((p1.x - p2.x)*(p1.x - p2.x) + (p1.y - p2.y)*(p1.y - p2.y) + (p1.z - p2.z)*(p1.z - p2.z));
		}

		FORCEINLINE double DistanceSqr(const Vector3db &p1, const Vector3db &p2)
		{
			return (p1.x - p2.x)*(p1.x - p2.x) + (p1.y - p2.y)*(p1.y - p2.y) + (p1.z - p2.z)*(p1.z - p2.z);
		}

		FORCEINLINE bool Equal(const Vector3db &p1, const Vector3db &p2, const double epsilon = kEpsilon)
		{
			return (Rainbow::Abs(p1.x - p2.x) < epsilon) && (Rainbow::Abs(p1.y - p2.y) < epsilon) && (Rainbow::Abs(p1.z - p2.z) < epsilon);
		}

		FORCEINLINE double DotProduct(const Vector3db &v1, const Vector3db &v2)
		{
			return v1.x*v2.x + v1.y*v2.y + v1.z*v2.z;
		}
		FORCEINLINE double SqrMagnitude(const Vector3db& inV) { return DotProduct(inV, inV); }
		FORCEINLINE double Magnitude(const Vector3db& inV) { return sqrt(DotProduct(inV, inV)); }
		FORCEINLINE double Angle(const Vector3db& lhs, const Vector3db& rhs) 
		{ 
			return ::acos(Rainbow::Min(1.0, Rainbow::Max(-1.0, DotProduct(lhs, rhs) / (Magnitude(lhs) * Magnitude(rhs)))));
		}

		//bool CompareApproximately(const Vector3db& inV0, const Vector3db& inV1, const double inMaxDist = kEpsilon);

		//FORCEINLINE bool CompareApproximately(const Vector3db& inV0, const Vector3db& inV1, const double inMaxDist)
		//{
		//	return SqrMagnitude(inV1 - inV0) <= inMaxDist * inMaxDist;
		//}

		FORCEINLINE Vector3db V3Min(const Vector3db& lhs, const Vector3db& rhs) 
		{ 
			return Vector3db(Rainbow::Min(lhs.x, rhs.x), Rainbow::Min(lhs.y, rhs.y), Rainbow::Min(lhs.z, rhs.z));
		}
		// Returns a vector with the larger  of every component from v0 and v1
		FORCEINLINE Vector3db V3Max(const Vector3db& lhs, const Vector3db& rhs) 
		{ 
			return Vector3db(Rainbow::Max(lhs.x, rhs.x), Rainbow::Max(lhs.y, rhs.y), Rainbow::Max(lhs.z, rhs.z)); 
		}

		// this may be called for vectors `a' with extremely small magnitude, for
		// example the result of a cross product on two nearly perpendicular vectors.
		// we must be robust to these small vectors. to prevent numerical error,
		// first find the component a[i] with the largest magnitude and then scale
		// all the components by 1/a[i]. then we can compute the Length of `a' and
		// scale the components by 1/l. this has been verified to work with vectors
		// containing the smallest representable numbers.
		//EXPORT_ENGINEMODULE Vector3db NormalizeRobust(const Vector3db& a);
		// This also returns vector's inverse original Length, to avoid duplicate
		// invSqrt calculations when needed. If a is a zero vector, invOriginalLength will be 0.
		//EXPORT_ENGINEMODULE Vector3db NormalizeRobust(const Vector3db& a, double &invOriginalLength, double eps = kEpsilon);

		FORCEINLINE Vector3db Scale(const Vector3db& lhs, const Vector3db& rhs) { return Vector3db(lhs.x * rhs.x, lhs.y * rhs.y, lhs.z * rhs.z); }

		//FORCEINLINE Vector3db FloatKeep(const Vector3db& in, unsigned int keep) { return Vector3db(FloatKeep(in.x, keep), FloatKeep(in.y, keep), FloatKeep(in.z, keep)); }

		FORCEINLINE bool IsFinite(const Vector3db& f)
		{
			return Rainbow::IsFinite(f.x) && Rainbow::IsFinite(f.y) && Rainbow::IsFinite(f.z);
		}

		//EXPORT_ENGINEMODULE Vector3db OrthoNormalVectorFast(const Vector3db& n);
		//EXPORT_ENGINEMODULE void OrthoNormalize(Vector3db* inU, Vector3db* inV);
		//EXPORT_ENGINEMODULE void OrthoNormalize(Vector3db* inU, Vector3db* inV, Vector3db* inW);

		FORCEINLINE Vector3db Lerp(const Vector3db &v1, const Vector3db &v2, double t)
		{
			return Vector3db(v1.x + (v2.x - v1.x)*t, v1.y + (v2.y - v1.y)*t, v1.z + (v2.z - v1.z)*t);
		}

		FORCEINLINE Vector3db CrossProduct(const Vector3db &v1, const Vector3db &v2)
		{
			return Vector3db(v1.y*v2.z - v1.z*v2.y, v1.z*v2.x - v1.x*v2.z, v1.x*v2.y - v1.y*v2.x);
		}

		FORCEINLINE Vector3db Maximize(const Vector3db &v1, const Vector3db &v2)
		{
			return Vector3db(Rainbow::Max(v1.x, v2.x), Rainbow::Max(v1.y, v2.y), Rainbow::Max(v1.z, v2.z));
		}

		FORCEINLINE Vector3db Minimize(const Vector3db &v1, const Vector3db &v2)
		{
			return Vector3db(Rainbow::Min(v1.x, v2.x), Rainbow::Min(v1.y, v2.y), Rainbow::Min(v1.z, v2.z));
		}

		FORCEINLINE void GetPerpendicular(Vector3db &v1, Vector3db &v2, const Vector3db &v) //得到和他垂直的两个TVector
		{
			Vector3db tmp = Abs(v);

			if (tmp.x < tmp.y && tmp.x < tmp.z) v1 = Vector3db(1.0, 0, 0);
			else if (tmp.y < tmp.z) v1 = Vector3db(0, 1.0, 0);
			else v1 = Vector3db(0, 0, 1.0);

			v2 = CrossProduct(v, v1);
			v1 = CrossProduct(v2, v);
		}
		EXPORT_ENGINEMODULE Vector3db Slerp(const Vector3db& from, const Vector3db& to, double t);


		inline Vector3db Normalize(const Vector3db& inV)
		{
			const double len = Magnitude(inV);
			//AssertMsg(len > kEpsilon, "Normalize() return a NAN value, please use NormalizeSafe()!");
			//Vector3db n = inV / Magnitude(inV);
			//AssertMsg(!(IsNAN(n.x) || IsNAN(n.y) || IsNAN(n.z)), "Normalize() get a NAN value, please use NormalizeSafe()!");
			return inV / len;
		}

		inline double Dot(const Vector3db& lhs, const Vector3db& rhs) { return lhs.x * rhs.x + lhs.y * rhs.y + lhs.z * rhs.z; }

		//template<class TransferFunction>
		//FORCEINLINE void Vector3db::Transfer(TransferFunction& transfer)
		//{
		//	if (transfer.IsWriting())
		//	{
		//		double rx = FloatKeep(x, 4);
		//		TRANSFER_WITH_NAME(rx, "x");
		//		double ry = FloatKeep(y, 4);
		//		TRANSFER_WITH_NAME(ry, "y");
		//		double rz = FloatKeep(z, 4);
		//		TRANSFER_WITH_NAME(rz, "z");
		//	}
		//	else {
		//		TRANSFER(x);
		//		TRANSFER(y);
		//		TRANSFER(z);
		//	}
		//}


		Vector3db DirectionToEuler(const Vector3db& direction);

		FORCEINLINE Vector3db ProjectOnPlane(Vector3db vector, Vector3db planeNormal)
		{
			double sqrMag = Dot(planeNormal, planeNormal);
			if (sqrMag < kEpsilon)
				return vector;
			else
			{
				double dot = Dot(vector, planeNormal);
				return Vector3db(vector.x - planeNormal.x * dot / sqrMag,
					vector.y - planeNormal.y * dot / sqrMag,
					vector.z - planeNormal.z * dot / sqrMag);
			}
		}

		FORCEINLINE Vector3db Project(const Vector3db& v1, const Vector3db& v2)
		{
			return v2 * Dot(v1, v2);
		}
	}
}
