#pragma once
/**
* file : SandboxReflexEnum
* func : 沙盒枚举反射
* by : chenzh
*/
#include "SandboxReflexValue.h"
#include "SandboxReflexType.h"
#include "SandboxUnit.h"
#include <unordered_map>
#include <vector>
#include <string>


namespace MNSandbox {


	class EXPORT_SANDBOXDRIVERMODULE ReflexEnum
	{
	public:
		using EnumVal = EnumValType; // 枚举默认类型

		/* 子项 */
		class Item
		{
		public:
			ReflexEnum* m_owner; // 所属的枚举
			std::string m_name; // 名称
			EnumVal m_value; // 实际的枚举值
			unsigned m_index; // 在枚举中的索引
			std::vector<std::string> m_subnames;
		};

	public:
		static size_t StreamWriteNumber(int v, const AutoRef<Stream>& stream);
		static bool StreamReadNumber(int& v, const AutoRef<Stream>& stream);

	public:
		ReflexEnum(const std::string& name, ReflexConfig cfg = ReflexConfig::Def);
		~ReflexEnum();

		/* 获取子项 */
		Item* GetItem(const std::string& name);

		/* 通过值获取子项 */
		Item* GetItemByVal(EnumVal val);

		// 注册类型
		void RegisterTypePolicy(ReflexType& type, const ReflexTypePolicy& basepolicy);

		// lua_toC
		bool CallbackLuaToC(EnumVal& val, lua_State* L, int objindex);

		// lua_pushC
		int CallbackLuaPushC(const EnumVal& val, lua_State* L);

		// to string
		std::string CallbackToString(const EnumVal& val);

	public:

		// 增加子项
		bool AddItem(const std::string& name, EnumVal val);
		// 减少子项
		bool RemoveItem(const std::string& name);

		// 获取数量
		unsigned GetSize() const { return (unsigned)m_items.size(); }

		// 获取子项
		const Item& GetItem(int index) const { SANDBOX_ASSERT(index < (int)m_items.size()); return m_items.at(index);}

		// 获取子项列表
		const std::vector<Item>& GetItems() const { return m_items; }

		// 获取名称
		std::string GetName() const { return m_name; }

		void ClearItems();
	private:
		// 增加新的隐射
		void AddItemSubName(const std::string& name, EnumVal val);

	private:

		friend class ReflexEnumContainer;

		std::string m_name; // 名称
		std::vector<Item> m_items; // 按索引缓存
		std::unordered_map<std::string, unsigned> m_itemIdx; // 名称 - 索引
		std::unordered_map<EnumVal, unsigned> m_ValIdx; // 值 - 索引
		ReflexConfig m_cfg; // 配置
	};

	/* 枚举容器 */
	class EXPORT_SANDBOXDRIVERMODULE ReflexEnumContainer
	{
	public:
		/* 单例 */
		struct EXPORT_SANDBOXDRIVERMODULE Singleton
		{
			static ReflexEnumContainer& Get(); // 单例
		};

		// 插入
		void AddEnum(ReflexEnum* val);
		// 副名称
		void AddEnumSubName(ReflexEnum* val, const std::string& name);
		// 获取
		ReflexEnum* GetEnum(const std::string& name) const;
		// 获取列表
		const std::unordered_map<std::string, ReflexEnum*>& GetEnums();

	private:

		std::unordered_map<std::string, ReflexEnum*> m_datas; // 枚举名称映射
	};

	/* 枚举 */
	template<typename TEnum>
	class ReflexEnumDesc
	{
	public:
		using Items = std::vector<std::pair<TEnum, std::string>>; /* 子项组，用于初始化 */

		ReflexEnumDesc(const std::string& name, int eType, const Items& items, ReflexConfig cfg = ReflexConfig::Def)
		{
			if (!ms_enumInstance)
			{
				ms_enumInstance = SANDBOX_ORIGINAL_NEW(ReflexEnum, name, cfg);
				for (auto& val : items)
				{
					ms_enumInstance->AddItem(val.second, (ReflexEnum::EnumVal)val.first);
				}

				// 注册tolua
				ReflexType& type = ReflexType::GetSingleton<TEnum>();
				type.SetName(std::string("Enum.") + name);
				ReflexTypePolicy basepolicy; // 基础
				ReflexPolicyData<TEnum>::LocalFillPolicy(basepolicy);
				basepolicy.m_cbSerialize = &ReflexEnumDesc::CallbackSerialize;
				basepolicy.m_cbUnserialize = &ReflexEnumDesc::CallbackUnserialize;
				basepolicy.m_cbLuaToC = &ReflexEnumDesc::CallbackLuaToC;
				basepolicy.m_cbLuaPushC = &ReflexEnumDesc::CallbackLuaPushC;
				basepolicy.m_cbToString = &ReflexEnumDesc::CallbackToString;
				basepolicy.m_fToEnumVal = &ReflexEnumDesc::FuncToEnumVal;
				basepolicy.m_fGetEnumVal = &ReflexEnumDesc::FuncGetEnumVal;
				basepolicy.m_cbReflexToBinary = &ReflexEnumDesc::ReflexToBinary;
				basepolicy.m_cbReflexFromBinary = &ReflexEnumDesc::ReflexFromBinary;

				ms_enumInstance->RegisterTypePolicy(type, basepolicy);
				SANDBOX_ASSERT(type.GetReflexTypeEnum() == REFLEXTYPEENUM_IDLE);
				type.SetReflexTypeEnum((REFLEXTYPEENUM)eType);
			}
			else
			{
				//SANDBOX_ASSERT(false && "enum is already exit!");
				auto& container = ReflexEnumContainer::Singleton::Get();
				container.AddEnumSubName(ms_enumInstance, name);

				//auto& enumItems = ms_enumInstance->GetItems();
				for (auto& val : items)
				{
					ms_enumInstance->AddItem(val.second, (ReflexEnum::EnumVal)val.first);
				}
			}
		}
		~ReflexEnumDesc()
		{
			SANDBOX_ORIGINAL_DELETE(ms_enumInstance);
		}

		/* 获取子项 */
		ReflexEnum::Item* GetItem(const std::string& name)
		{
			if (ms_enumInstance)
			{
				return ms_enumInstance->GetItem(name);
			}
			return NULL;
		}

		bool AddItem(TEnum tenum, std::string name)
		{
			if (ms_enumInstance)
			{
				return ms_enumInstance->AddItem(name, (ReflexEnum::EnumVal)tenum);
			}
			return false;
		}

		bool RemoveItem(std::string name)
		{
			if (ms_enumInstance)
			{
				return ms_enumInstance->RemoveItem(name);
			}
			return false;
		}

		void ClearItems()
		{
			if (ms_enumInstance)
			{
				return ms_enumInstance->ClearItems();
			}
		}
		// serialize
		static void CallbackSerialize(const void* val, MNJsonVal& jsonval)
		{
			const TEnum* in = reinterpret_cast<const TEnum*>(val);
			ReflexEnum::EnumVal enumval = static_cast<ReflexEnum::EnumVal>(*in);
			MNJsonNumber v = static_cast<MNJsonNumber>(enumval);
			jsonval.import(v);
		}

		// unserialize
		static bool CallbackUnserialize(void* val, const MNJsonVal& jsonval)
		{
			TEnum* out = reinterpret_cast<TEnum*>(val);
			if (jsonval.is<MNJsonNumber>())
			{
				MNJsonNumber v = jsonval.get<MNJsonNumber>();
				ReflexEnum::EnumVal n = static_cast<ReflexEnum::EnumVal>(v);
				*out = static_cast<TEnum>(n);
				return true;
			}
			SANDBOX_ASSERT(false);
			return false;
		}

		// lua_toC
		static bool CallbackLuaToC(void* val, lua_State* L, int objindex)
		{
			TEnum* out = reinterpret_cast<TEnum*>(val);
			ReflexEnum::EnumVal enumval;
			if (ms_enumInstance->CallbackLuaToC(enumval, L, objindex))
			{
				*out = static_cast<TEnum>(enumval);
				return true;
			}
			return false;
		}

		// lua_pushC
		static int CallbackLuaPushC(const void* val, lua_State* L)
		{
			const TEnum* in = reinterpret_cast<const TEnum*>(val);
			ReflexEnum::EnumVal enumval = static_cast<ReflexEnum::EnumVal>(*in);
			return ms_enumInstance->CallbackLuaPushC(enumval, L);
		}

		// tostring
		static std::string CallbackToString(const void* val)
		{
			const TEnum* in = reinterpret_cast<const TEnum*>(val);
			ReflexEnum::EnumVal enumval = static_cast<ReflexEnum::EnumVal>(*in);
			return ms_enumInstance->CallbackToString(enumval);
		}

		// to enum val
		static bool FuncToEnumVal(const void* val, EnumValType& data)
		{
			const TEnum* in = reinterpret_cast<const TEnum*>(val);
			data = static_cast<ReflexEnum::EnumVal>(*in);
			return true;
		}

		// get enum val
		static bool FuncGetEnumVal(void* val, EnumValType data)
		{
			TEnum* in = reinterpret_cast<TEnum*>(val);
			*in = static_cast<TEnum>(data);
			return true;
		}

		// serialize
		static size_t ReflexToBinary(const void* val, const AutoRef<Stream>& stream)
		{
			const TEnum* in = reinterpret_cast<const TEnum*>(val);
			//return stream->WriteNumber<int>(static_cast<int>(*in));
			return ReflexEnum::StreamWriteNumber(static_cast<int>(*in), stream);
		}

		// unserialize
		static bool ReflexFromBinary(void* val, const AutoRef<Stream>& stream)
		{
			int data = 0;
			//if (!stream->ReadNumber<int>(data))
			if (!ReflexEnum::StreamReadNumber(data, stream))
				return false;

			TEnum* out = reinterpret_cast<TEnum*>(val);
			*out = static_cast<TEnum>(data);
			return true;
		}

		// ReflexEnum
		static ReflexEnum* ms_enumInstance;
	};

	template<typename TEnum>
	ReflexEnum* ReflexEnumDesc<TEnum>::ms_enumInstance = nullptr;

}