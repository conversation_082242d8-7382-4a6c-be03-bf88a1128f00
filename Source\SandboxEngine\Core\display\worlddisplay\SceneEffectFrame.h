#pragma once
/**
* file : SceneEffectFrame
* func : 区域特效框
* by : chenzh
*/
#include "SceneEffectLine.h"

enum CURVEFACEMTLTYPE;
struct BlockGeomVert;
class World;

namespace MNSandbox {
	namespace EditorSelect {

		// 选择框类型
		enum class SelectFrameShape
		{
			UNKNOWN,
			CUBE, // 立方体选择框
			SPHERE, // 球体选择框
			CYLINDER, // 圆柱体选择框
		};

		// 选择目标类型
		enum class SELTARGETTYPE
		{
			IDLE = 0,
			NODE = 1,	// 选择节点		节点系统只能选择节点
			BLOCK = 2,	// 选择方块		沙盒系统只能选择方块
			NODE_BLOCK = 3, // 选择节点和方块 (1+2)  废弃
		};

		// 编辑系统模式
		enum class EDITSYSMODE
		{
			SANDBOX_EDITSYS,		//沙盒系统(编辑方块)
			NODES_EDITSYS,			//节点系统(编辑节点)
		};

		// 编辑模式
		enum class EDITMODE
		{
			SELECT = -1,		// 节点选中(方块选区)
			MOVE = 0,			// 移动模式
			ROTATE,				// 旋转模式
			SCALE,				// 缩放模式（默认）
			NONE,				// 方块取消选区
		};

		// 缩放模式
		enum class SCALEMODE
		{
			SINGLE_AXIS = 0,	// 单轴缩放
			SIDE_AXIS = 1,		// 两侧同时缩放
			ALL_AXIS = 2, 		// 全轴同时缩放，以操作轴的另一端为中心
			XZ_AXIS = 3, 		// XZ同时缩放相同数值，仅在圆柱和圆锥中使用
			XYZ_AXIS = 4, 		// XYZ同时缩放相同数值，以节点中心进行旋转，仅在球中使用
		};

		//坐标模式
		enum class AXISMODE : std::uint16_t
		{
			WORLD_MODE = 1,			//世界坐标
			LOCAL_MODE,				//本地坐标
		};

		//沙盒系统选择模式
		enum class SANDBOXSELMODE
		{
			INVALID_SEL,		//无效选择
			MOUSE_SEL,			//鼠标选择(废弃)              
			AXIS_SEL,			//坐标轴选择(默认)
		};

		//相机拉近坐标轴模式
		enum class CAMERASCALEAXISMODE
		{
			NORMAL_AXIS,		//正常坐标轴
			OFFSET_AXIS,		//偏移坐标轴
		};
	}
}

//环绕方向
enum class EncircleDir : std::uint16_t
{
	INVALID_AXIS = 0x0000,
	X_AXIS = 0x0001,
	Y_AXIS = 0x0010,
	Z_AXIS = 0x0100,
	ALL_AXIS = X_AXIS | Y_AXIS | Z_AXIS,
};

class SceneEffectFrame : public SceneEffectGeom
{
public:
	// 显示模式
	enum class SHOWMODE
	{
		X = 1,
		Y = 2,
		Z = 4,
		XY = 1+2,
		XZ = 1+4,
		YZ = 2+4,
		XYZ = 1+2+4,
	};
public:
	SceneEffectFrame();
	SceneEffectFrame(const WCoord startpos, const WCoord endpos, int stroke = 12,
		CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE, CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectFrame();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;

	// 设置显示模式
	void SetShowMode(SHOWMODE mode) { m_showMode = mode; }
	SHOWMODE GetShowMode() const { return m_showMode; }
private:
	// 更新
	void RefreshBoxFrame(const WCoord& beg, const WCoord& end);

	// 绘制相关
	static const unsigned ms_dirCnt = 3; // 3个方向各生成一种
	static const unsigned ms_rowCnt = 4; // 每种最多4条
	struct DrawLine
	{
		SceneEffectLine* _line = nullptr; // 绘制线条
		unsigned _lineCnt = 0; // 绘制线条数量
		WCoord _startDrawPos[ms_rowCnt];
	};
	unsigned m_drawCnt = 0; // 绘制类型数量
	DrawLine m_drawData[ms_dirCnt];
	std::vector<SceneEffectLine*> mLines; 
	// 显示模式
	SHOWMODE m_showMode = SHOWMODE::XYZ;
};
