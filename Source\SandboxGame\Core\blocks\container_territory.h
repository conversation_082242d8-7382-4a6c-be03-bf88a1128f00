﻿#pragma once

#include "container_world.h"
#include "OgreBlock.h"
#include "OgreBezierCurve.h"
#include "TerritoryBoundingBox.h"
#include <map>

class ErosionContainer; // 前向声明，假设这是一个已定义的类

namespace Rainbow
{
	class EventContent;
	class GameObject;
}

/**********************************************************************************************
��    ����TerritoryContainer
��    �ܣ�ͼ��container �����Ƶ�
********************************************************************************************* */
class TerritoryContainer : public WorldStorageBox//tolua_exports
{//tolua_exports
public:
	//tolua_begin
	TerritoryContainer();
	TerritoryContainer(const WCoord& blockpos);
	virtual ~TerritoryContainer();
	virtual int getObjType() const
	{
		return OBJ_TYPE_BOX;
	}

	virtual FBSave::ContainerUnion getUnionType()
	{
		return FBSave::ContainerUnion_ContainerVillageTotemIce;
	}

	virtual bool load(const void* srcdata);
	virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder);
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld();
	virtual void updateTick() override;
	virtual void updateDisplay(float dtime);
	//WCoord BlockCenterCoordWithEvenOddOffset(const WCoord& grid);
	std::vector<WCoord> GetFlagPoint();
	bool HasFlagPoint(const WCoord& point);
	bool AddFlagPoint(const std::vector<WCoord>& points);
	bool AddFlagPoint(const WCoord& point);
	bool RemoveFlagPoint(const WCoord& point);
	bool ClearFlagPoint();


	std::vector<WORLD_ID> GetVillagersID();
	bool HasVillagersID(const WORLD_ID& villagerid);
	bool AddVillagerID(const std::vector<WORLD_ID>& villagerid);
	bool AddVillagerID(const WORLD_ID& villagerid);
	bool RemoveVillagerID(const WORLD_ID& villagerid);
	void SetUin(int uin) 
	{
		m_OwnerUin = uin;
	}
	int GetUin() 
	{
		return m_OwnerUin;
	}
	bool GetHaveAward() 
	{
		return m_haveAward;
	}
	void SetHaveAward(bool award);
	bool ClearVillagersID();
	void SwitchRender(World* pworld)
	{
		m_pworld = pworld;
		m_render = !m_render;
	}

	virtual float getAttrib(int i);
	virtual int getItemAndAttrib(google::protobuf::RepeatedPtrField<game::common::PB_ItemData>* pItemInfos, google::protobuf::RepeatedField<float>* pAttrInfos) override;
	//tolua_end
	virtual void onAttachUI();
	virtual void onDetachUI();
	
	const char* getContainerName() const override {
		return "TerritoryContainer";
	}
	void UpdateEffect(World* pworld);

	void OnTriggerEnter(const Rainbow::EventContent* touch);
	void OnTriggerExit(const Rainbow::EventContent* touch);
	bool IsModfiy(int userID);
	struct ConfigInfo
	{
		int m_Uuid; //	uuid 
		//World m_WorldPos; // ��������  �����Ѿ���
		//int m_OwnerId; //ӵ����   m_OwnerUin �����Ѿ���
		int authorizeId[8];//��Ȩ����� ���8��
 	};

	// 新增领地边界盒相关方法
	bool IsPointInTerritoryBounds(const Rainbow::Vector3f& point) const;
	void SetTerritoryBounds(const Rainbow::Vector3f& center, const Rainbow::Vector3f& halfExtents);
	TerritoryBoundingBox GetTerritoryBounds() const;
	void ShowBoundingBox(bool show);

	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	// 材料扣除接口 - 使用 blockId 参数
	bool DeductMaterialByBlockId(int blockId); // 扣除指定方块的维护材料
	
	// 批量操作方法 - 使用 blockId 参数
	int GetAvailableMaterialCountByBlockId(int blockId); // 获取可用材料数量（可执行多少次维护）
	bool DeductMaterialBatchByBlockId(int blockId, int cycles); // 批量扣除材料

	// 添加通知附近方块的方法
	void NotifyNearbyBlocksOnDestroy();
	void NotifyNearbyBlocksOnCreate();  // 新增：领地创建时通知

	// 高性能版本：获取管理的腐蚀方块列表，O(1)复杂度
	const std::vector<ErosionContainer*>& GetManagedErosionBlocks() const { return m_managedErosionBlocks; }
	// 高性能版本：获取管理的腐蚀方块数量，O(1)复杂度
	size_t GetManagedErosionBlockCount() const { return m_managedErosionBlocks.size(); }
	
	// 添加/移除管理的腐蚀方块（由腐蚀方块调用）
	void AddManagedErosionBlock(ErosionContainer* erosionBlock);
	void RemoveManagedErosionBlock(ErosionContainer* erosionBlock);

	// 计算24小时维护所需材料
	std::map<int, int> Calculate24HourMaintenanceNeeds() const;
	// 检查是否有足够材料维护24小时
	bool CanMaintain24Hours() ;

	// 获取24小时维护材料需求的详细信息（包含当前拥有量）
	struct MaintenanceInfo {
		int itemid;
		int required;
		int available;
		bool sufficient;
	};
	std::vector<MaintenanceInfo> Get24HourMaintenanceInfo();

private:
	WCoord getEffectPos();
	std::vector<WCoord> m_flagPoint;
	std::vector<WORLD_ID> m_villagersID;
	bool m_haveAward;

	std::string WorldIdToStr(WORLD_ID id);
	WORLD_ID StrToWorldId(std::string str);
	bool m_render;

	World* m_pworld;
	void DrawLine(World* pworld, const WCoord& BlockPos, const WCoord& distBlockPos);


	void InitTrigger();
	Rainbow::GameObject* m_GameObject = nullptr;

	// 定义边界盒大小的常量（完整尺寸，不是半尺寸）
	static const Rainbow::Vector3f DEFAULT_TERRITORY_BOX_SIZE;
	
	// 边界盒相关成员
	TerritoryBoundingBox m_boundingBox;
	bool m_showBoundingBox = false;

	// 安全的材料扣除方法
	bool SafeRemoveItemByCount(int itemid, int count);
	
	// 高性能管理：直接维护管理的腐蚀方块列表
	std::vector<ErosionContainer*> m_managedErosionBlocks;
	
	// 用于跟踪腐蚀方块数量变化
	mutable size_t m_lastErosionBlockCount = 0;
	
	// UI通知延时相关
	int m_uiNotificationDelayTicks = 0;
	static const int UI_NOTIFICATION_DELAY_TICKS; // 延时tick常量
	
	// 属性索引常量
	static const int MANAGED_BLOCK_COUNT_INDEX = 0; // 管理的腐蚀方块数量索引
	static const int MATERIAL_INFO_START_INDEX = 1; // 材料信息起始索引
	static const int MATERIAL_INFO_END_INDEX = 12; // 材料信息结束索引
	static const int PROPERTIES_PER_MATERIAL = 3; // 每种材料的属性数量（itemid, required, available）
	static const int MAINTENANCE_PERCENTAGE_INDEX = 13; // 维护百分比属性索引
	
	// 材料信息类型枚举
	enum MaterialInfoType {
		MATERIAL_ITEMID = 0,    // 材料ID
		MATERIAL_REQUIRED = 1,  // 所需数量
		MATERIAL_AVAILABLE = 2  // 当前拥有数量
	};
	
	// 万分比相关常量
	static const int DEFAULT_PROPORTION = 10000; // 默认万分比为10000 (100%)
	static const int PROPORTION_TO_PERCENTAGE = 100; // 使用整数
	
	// 维护需求缓存相关
	mutable std::vector<std::pair<int, int>> m_cachedMaintenanceNeeds; // 缓存的维护需求数据
	mutable bool m_maintenanceNeedsDirty = true; // 缓存是否过期标志
	void InvalidateMaintenanceCache() const { m_maintenanceNeedsDirty = true; } // 使缓存失效
	
	// 统一的材料消耗获取方法
	std::map<int, int> GetConsumedItemsByBlockId(int blockId) const;
	
	// 根据当前管理的腐蚀方块数量获取对应的万分比
	int GetMaintenanceProportion() const;
};//tolua_exports
