#pragma once
/**
* file : SceneEffectCircle
* func : 场景效果 （圆）
* by : yangzy
*/
#include "SceneEffectLine.h"
#include "SceneEffectFrame.h"
#include "SceneEffectSphere.h"
#include "SceneEffectFan.h"
#include "SandboxRay.h"

class SceneEffectSphere;
class SceneEffectFan;

class SceneEffectCircle : public SceneEffectGeom
{
public:
	SceneEffectCircle();
	SceneEffectCircle(const MNSandbox::MNCoord3f& center, float radius, float ratio, 
		CURVEFACEMTLTYPE mtltype);
	virtual ~SceneEffectCircle();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;

public:
	// 设置比率
	void SetRatio(float ratio, MNSandbox::MNCoord3f optCurShereDir);

	// 设置操作点位置
	inline void SetOptSpherePos(MNSandbox::MNCoord3f wPos) { m_optSpherePos = wPos; }

	inline MNSandbox::MNCoord3f GetOptSpherePos() { return m_optSpherePos; }

	inline void SetColor(BlockVector circleColor, BlockVector optSphereColor) { m_circleColor = circleColor; m_optSphereColor = optSphereColor; }

	// 更新圆框架
	void RefreshCircleFrame(const MNSandbox::MNCoord3f& center, int radius);

	// 设置刻度
	void SetDegree(int nDegree, bool bRefresh = false);

	// 设置切分度数
	void SetSegmentAngle(int nSegmentAngle, bool bRefresh = false);

	// 设置鼠标pick位置
	void SetPickPos(MNSandbox::MNCoord3f begPos, MNSandbox::MNCoord3f endPos, EncircleDir emCurRotateDir, bool bRefresh = false);

	// 设置旋转轴
	void SetRotationAxis(Rainbow::Vector3f curAxis, Rainbow::Vector3f originAxis, bool bRefresh = false);

	// 射线选中
	bool RayCircle(MNSandbox::Ray& ray, MNSandbox::MNCoord3f& targetPos);

	// 调整刻度位置
	void AdjustDegreePos(MNSandbox::MNCoord3f& targetPos);

	// 设置选中
	void SetSelected(bool bSelected) { m_isSelected = bSelected; }

private:
	// 属性
	BlockVector m_circleColor = MakeBlockVector(211, 211, 211, 255);	//圆颜色
	BlockVector m_optSphereColor = MakeBlockVector(211, 211, 211, 255);	//操作球颜色
	MNSandbox::MNCoord3f m_optSpherePos = MNSandbox::MNCoord3f(0.0);		  // 操作点位置
	int m_degree = 0;						  //刻度(旋转度数)
	int m_segmentAngle = 10;				  //切分度数
	Rainbow::Vector3f m_originRotationAxis = Rainbow::Vector3f::yAxis;	//初始旋转轴
	Rainbow::Vector3f m_curRotationAxis = Rainbow::Vector3f::yAxis;		//当前旋转轴
	EncircleDir m_emCurRotateDir = EncircleDir::INVALID_AXIS;			//当前旋转方向

	struct DrawLine
	{
		SceneEffectLine* _line = nullptr; // 绘制线条
		MNSandbox::MNCoord3f _startDrawPos = MNSandbox::MNCoord3f(0.0);
		MNSandbox::MNCoord3f _endDrawPos = MNSandbox::MNCoord3f(0.0);
	};

	static const unsigned  ms_optSphereRadius = 30;		// 操作球半径
	static const unsigned  ms_circleLineMinWidth = 1;	// 线宽
	static const unsigned  ms_circleLineWidth = 4;		// 线宽
	static const unsigned  ms_degreeLineWidth = 2;		// 线宽
	static const unsigned  ms_degreeLineLen = 15;		// 线长

	int m_optSphereRadius = 30;		// 操作球半径
	int m_circleLineMinWidth = 1;	// 线宽
	int m_circleLineWidth = 4;		// 线宽
	int m_degreeLineWidth = 2;		// 线宽
	int m_degreeLineLen = 15;		// 线长

	std::vector<DrawLine> m_vecDrawCircleLine;		// 绘制圆线条
	std::vector<DrawLine> m_vecDrawDegreeLine;		// 绘制刻度线条
	std::vector<SceneEffectFan*> m_vecDrawFan;		// 绘制扇形  
	DrawLine m_selectedline;
	SceneEffectSphere* m_optSphere = nullptr; // 操作点
	SceneEffectSphere* m_centerSphere = nullptr; // 圆心
	MNSandbox::MNCoord3f m_pickPosBeg = MNSandbox::MNCoord3f(0.0);	// 鼠标pick开始位置
	MNSandbox::MNCoord3f m_pickPosEnd = MNSandbox::MNCoord3f(0.0);	// 鼠标pick结束位置
	bool m_isSelected = false; // 是否选中
};
