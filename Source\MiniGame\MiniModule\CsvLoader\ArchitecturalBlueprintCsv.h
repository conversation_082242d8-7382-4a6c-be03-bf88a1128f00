#pragma once

#include "AbsCsv.h"
#include "LazySingleton.h"
#include "DefDataTable.h"
#include "defdata.h"
#include <string>
#include <map>
#include <vector>

class ArchitecturalBlueprintCsv : public AbsCsv {
public:
    ArchitecturalBlueprintCsv();
    ~ArchitecturalBlueprintCsv();
    
    void onParse(MINIW::CSVParser& parser) override;
    void onClear() override;
    const char* getName() override;
    const char* getClassName() override;
    
    int getNum();
    const ArchitecturalBlueprintCsvDef* get(int id);
    std::vector<const ArchitecturalBlueprintCsvDef*> getAll();
    
    // 根据制造道具ID查询消耗道具和数量 (消耗道具ID -> 数量)
    std::map<int, int> getConsumedItemsByProduceItemID(int produceItemID);
    
private:
    // 辅助方法：解析"制造道具|消耗道具|数量"格式的字符串
    bool parseMaterialConsumption(const std::string& input, ArchitecturalBlueprintCsvDef::MaterialConsumption& material);
    DefDataTable<ArchitecturalBlueprintCsvDef> m_Table;
    
    DECLARE_LAZY_SINGLETON(ArchitecturalBlueprintCsv)
}; 