/**
* file : SandboxPlatform
* func : ɳ��ƽ̨�ӿ�
* by : chenzh
*/
#include "platform/SandboxPlatform.h"

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include <windows.h>
#else
#include <unistd.h>
#if _USE_PTHREAD_
#include <pthread.h>
#endif
#endif


namespace MNSandbox { namespace Platform {


	ProcessId_t GetProcessId()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		return (ProcessId_t)::GetCurrentProcessId();
#else
		return (ProcessId_t)getpid();
#endif
	}

	ThreadId_t GetThreadId()
	{
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		return (ThreadId_t)::GetCurrentThreadId();
#elif OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
		return (unsigned)gettid();
#elif _USE_PTHREAD_
    #if OGRE_PLATFORM == OGRE_PLATFORM_LINUXPC
        return pthread_self();
    #elif OGRE_PLATFORM == OGRE_PLATFORM_APPLE
        uint64_t tid;
        pthread_t  pthread = pthread_self();
        pthread_threadid_np(pthread, &tid);
        return tid;
    #else
        uint64_t tid;
        pthread_t  pthread = pthread_self();
        pthread_threadid_np(pthread, &tid);
        return tid;
    #endif
#else
		return 0; //return (unsigned)gettid();
#endif
	}

}}
