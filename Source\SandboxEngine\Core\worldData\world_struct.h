#ifndef __WORLDSTRUCT_H__
#define __WORLDSTRUCT_H__

#include "world_types.h"
#include "SandboxEngine.h"
//tolua_begin
struct WorldBossData
{
	int defid;
	float hp;
	unsigned int flags;
	WCoord spawnpoint;
};
//tolua_end

struct BuildSaveFillParam
{
	int clipBlockId = 0;  //修剪id
	int maxClipRange = 0; //修剪范围
	int fillBlockId = 0;  //填充id
	int maxFillDepth = 0; //最大填充深度
	unsigned int choiceState = 0;  //状态,bit1:useClipSurround,bit2:useBuildFoundation, bit3:useBiomeblock, bit4:waterfill
	int progress = 0;     //进度
	int maxDropHeight = 0;
};

struct BuildVmoBaseData
{
	BuildSaveFillParam fillParam;
};

//加一个基础类型,大部分建筑所需要的基本一样,不用每次都新写个类,后面都用这个类或者继承就行,之前的就不管了
struct BuildVmoBasePlaceData : BuildVmoBaseData
{
	int		progress;	// 进度
	int		dir;		// 朝向
	WCoord	startPos;	// 位置
	int type; // 类型
};
//tolua_begin
struct UnderGroundPalaceData
{
	// 	WCoord buildStartPos;
	unsigned char buildStatus;
	short buildStepY;
	int buildCurArrayIdx;
	unsigned char buildDir;
	unsigned char subbuildDir;
	unsigned char roomChannelStatus;
	unsigned char mazeRoomId;
	WCoord subStartPos;
	WCoord subNextStartPos;
	std::map<unsigned char, WCoord> mainRoomOpenDoorPoses;//key方向，value位置
	std::map<unsigned char, WCoord> subRoomOpenDoorPoses;//key方向，value位置
	std::vector<WCoord> treasurePoses;
};
//tolua_end

enum DesrtVillageBuildIngType
{
	DesrtVillageBuildIngType_house1 = 1,
	DesrtVillageBuildIngType_house2 = 2,
	DesrtVillageBuildIngType_house3 = 3,
	DesrtVillageBuildIngType_house4 = 4,
	DesrtVillageBuildIngType_house5 = 5,
	DesrtVillageBuildIngType_house6 = 6,
	DesrtVillageBuildIngType_Drain = 7,
	DesrtVillageBuildIngType_GatherPlace = 8,
	DesrtVillageBuildIngType_Max = 8,
};

struct DesertVillageNpcInfo
{
	int x;
	int z;
	int defId;
	DesertVillageNpcInfo(int cx = 0, int cz = 0, int cdefId = 0) :x(cx), z(cz), defId(cdefId) {};
};

struct DesertVillageBuildData
{
	WCoord startPos;
	DesrtVillageBuildIngType type;
	int progress;
	WCoord villagePos;
	std::vector<DesertVillageNpcInfo> npcInfos;
};

enum IslandBuildType
{
	IslandBuildType_DesertIsLandTreasureBox = 0, //荒岛宝箱
	IslandBuildType_CoralIsLandTreasureBox = 1,  //珊瑚岛沉船
	IslandBuildType_NormalTreasureBox = 2,		 //普通宝箱
	IslandBuildType_IsLandPirateShop = 3,		 //海盗商店
	IslandBuildType_IsLandShop = 4,				 //海岛商店
};

struct IslandBuildData : public BuildVmoBaseData
{
	WCoord startPos;
	IslandBuildType type;
	int progress;
};

enum SpecialEcosysBuildStatus
{
	SpecialEcosysBuildStatus_NONE,
	SpecialEcosysBuildStatus_END,
	SpecialEcosysBuildStatus_UNFINISHED,
	SpecialEcosysBuildStatus_WAITCHUNKLOAD, //等待chunk加载, 用于一些已经确定好位置的建筑, 不是在chunk加载时临时确定的, 在对应chunk加载后要改下状态
};

struct SpecialEcosysBuildInfo
{
	char status;
	ChunkIndex index;
	ChunkIndex range;
	SpecialEcosysBuildInfo(int x, int z, int rangeX, int rangeZ, char cstatus) :index(x, z), range(rangeX, rangeZ), status(cstatus) {}
};

//added by dongjianan 2023.7.6
struct BuildingAdditionalData
{
	int datas;
	ChunkIndex index;
	BuildingAdditionalData(int x = 0, int z = 0, int indatas = 0)
		: index(x, z)
		, datas(indatas)
	{}
};

enum FishingVillageBuildIngType
{
	FishingVillageBuildIngType_Fishing = 1,
};

struct FishingVillageBuildData : public BuildVmoBaseData
{
	WCoord startPos;
	FishingVillageBuildIngType type;
	int progress;
	WCoord villagePos;
	WCoord wharfPos;
	int dir;
};

enum class MapDataShipWreckType : short
{
	MapDataShipWrecksBig = 1,
	MapDataShipWrecksSmall1,
	MapDataShipWrecksSmall2,
	MapDataShipWrecksSmall3,
	MapDataShipWrecksSmall4,
};
// 沉船群
struct ShipWreckData
{
	int		progress;	// 进度
	int		dir;		// 朝向
	WCoord	position;	// 位置
	MapDataShipWreckType type; // 类型
};

struct WorldShipWreckBuildData
{
	////沉船群标识 坐标
	//WCoord	shipWreckPos;
	//// 大船
	//ShipWreckData bigShip;
	//// 小船
	//std::vector<ShipWreckData> smallShips;

	int		progress;	// 进度
	int		dir;		// 朝向
	WCoord	position;	// 位置
	MapDataShipWreckType type; // 类型
};

struct BirthPointBigBuildRecord : public BuildVmoBaseData
{
	int		progress;	// 进度
	int		dir;		// 朝向
	WCoord	position;	// 位置
	BirthPointBigBuildRecord() :progress(-1), dir(0), position(WCoord(0, 0, 0)) {}
};

//宝箱数据
struct TreasureBoxData
{
	TreasureBoxData(ChunkIndex& cindex, bool cisGen) :index(cindex), isGen(cisGen) {};
	TreasureBoxData(int x = 0, int z = 0, bool cisGen = false) :index(x, z), isGen(cisGen) {};
	ChunkIndex index;
	bool isGen;
};

struct BiomeGroupWeatherData
{
	//int startPos;
	float strength;
	int curweathertime;
	int curweather;
	int nextweathertime;
	int nextweather;
	bool isCome;
	int dir;
	WCoord duststormInitPos;
	bool hadActorSandworm;//同一场沙尘暴已经出现过沙虫

	//bool raining;
	//bool thundering;
	//bool darking;
	//int raintime;
	//int thundertime;
	//float rainstrength;
	//float thunderstrength;
	//float darkstrength;
	//int curweathertime;
	//int curweather;
	//int nextweathertime;
	//int nextweather;
	//float strength;

	//bool duststormIsCome;
	//int duststormTime;
	//int duststormDir;
	//WCoord duststormInitPos;
	//bool hadActorSandworm;//同一场沙尘暴已经出现过沙虫

	//bool tempestIsCome;
	//int tempestTime;
	//int tempestDir;
};

//mapData里的建筑类型,都定义在这里面就好,每个都写个类也很浪费
enum MapDataBuildType
{
	MapDataBuildType_IceBuildCenter = 0,		//冰原建筑中心
	MapDataBuildType_IceBuildLoggCamp,		//伐木场
	MapDataBuildType_IceBuildHunt,			//狩猎小屋
	MapDataBuildType_IceBuildResidence,     //住宅小屋
	MapDataBuildType_IceBuildVillageAltar,  //村庄祭坛
	MapDataBuildType_IceBuildTower,			//箭塔
	MapDataBuildType_IceBuildLoggerPoint,   //伐木指示点
	MapDataBuildType_IceBuildAltar,
	MapDataBuildType_END,
};

//struct IceBuildData : BuildVmoBasePlaceData
//{
//	//
//};

struct EXPORT_SANDBOXENGINE WorldMapData;

struct WorldMapData //tolua_exports
{//tolua_exports
	//tolua_begin		
	WorldMapData(int id) : mapid(id), portalpos(0, -1, 0), raining(false), thundering(false), darking(false), raintime(0), thundertime(0), rainstrength(0), thunderstrength(0), darkstrength(0), curweathertime(0), curweather(0),
		iscreateBigBuild(false), godtempleprogress(0), godtempleminheight(0), godtempledir(0), nextweathertime(0), nextweather(0), duststormIsCome(false), duststormTime(0), duststormDir(0), duststormInitPos(0, 0, 0), isNeverShowEasyModeTips(false), openGuide(false)
	{
		weatherforecastblocks.clear();
		deathJarBlocks.clear();
	}
	~WorldMapData()
	{
		undergroundpalaceList.clear();
		desertVillageBuildList.clear();
		fishingVillageBuildList.clear();
		shipWrecksList.clear();
	}
	int mapid;
	WCoord portalpos;
	bool raining;
	bool thundering;
	bool darking;
	int raintime;
	int thundertime;
	float rainstrength;
	float thunderstrength;
	float darkstrength;
	int curweathertime;
	int curweather;
	int nextweathertime;
	int nextweather;

	std::vector<WorldBossData> bosses;
	std::vector<WCoord> weatherforecastblocks;

	//已经生成的神庙神龛信息
	std::vector<WCoord> templepos;
	std::vector<WCoord> templedim;
	std::vector<WCoord> baldachinepos;
	std::vector<WCoord> baldachinedim;

	//正在生成的神庙进度
	bool iscreateBigBuild;
	WCoord godtemplepos;
	WCoord godstatuepos;
	int godtempleprogress;
	int godtempleminheight;
	int godtempledir;

	std::map<WCoord, UnderGroundPalaceData> undergroundpalaceList;//key开始位置，value地宫数据

	std::vector<DesertVillageBuildData>	desertVillageBuildList;
	std::vector<IslandBuildData> islandBuildList;
	//待生成的宝箱
	std::vector<TreasureBoxData> treasureBoxGenList;
	//宝箱生成地点
	std::vector<WCoord> treasureBoxList;
	//沉船群相关建筑
	std::vector<WorldShipWreckBuildData> shipWrecksList;
	//特殊建筑信息
	std::map<SpecialEcosysType, std::vector<SpecialEcosysBuildInfo>> specialChunk;
	std::vector<FishingVillageBuildData> fishingVillageBuildList;
	//出生点建筑
	BirthPointBigBuildRecord birthPointBuild;
	//冰原建筑信息
	std::vector<BuildVmoBasePlaceData> iceBuildList;

	//added by dongjianan 2023.7.6
	//与ChunckIndex相关额外数据
	std::vector<BuildingAdditionalData> additionalDatas;

	bool duststormIsCome;
	int duststormTime;
	int duststormDir;
	WCoord duststormInitPos;
	bool hadActorSandworm;//同一场沙尘暴已经出现过沙虫

	bool tempestIsCome;
	int tempestTime;
	int tempestDir;
	std::map<int, BiomeGroupWeatherData> biomeGroupWeatherList; //自定组ID, 地形组天气信息

	std::vector<WCoord> deathJarBlocks; //遗落盒子的位置
	bool isNeverShowEasyModeTips;

	//Rainbow::FixedString buildMgrJsonData; //建筑的json数据
	//tolua_end
	std::vector<bool>   godstatues;
	bool openGuide;  //这个地图是否开启了新手引导, 因为建新地图都是从0开始的,所以只有0号地图存的值是准的
};//tolua_exports

#define NEEDSAVE_NULL 0
#define NEEDSAVE_PLAYERS 2
#define NEEDSAVE_GLOBAL 4
#define NEEDSAVE_CHUNKS 8
#define NEEDSAVE_SANDBOX 16
#define NEEDSAVE_ALL 0xffffffff
#define WEATHER_BLOCK_MAX 100

#define MAX_MAP    3
//#define MAP_CONVERT_TOOLS_OPEN 1
#define CHECK_PLANT_TICK 100
//tolua_begin
struct BasketballConfig 
{
	int blockShot_cd_tick;
	int steal_cd_tick;
	int blockShot_height;
	int bolckShot_special_height;

	float phys_linear_damping;
	float phys_Angular_damping;
	float phys_static_friciton;
	float phys_dynamic_friciton;
	float phys_restitution;
	float phys_mass;

	float basketball_way_move_ratio;
	float dribbing_move_ratio;
	float charge_move_ratio;
	float obstruct_move_ratio;

	float catch_ball_range;
	int catch_cd_tick;
	int normal_catch_maxv;
	int invalid_catch_tick;

	int basketframe_radius;
	int	rush_cd_tick;
	float rush_speed;
	float pb_full_charge_time;
	float pb_keep_full_charge_time;
	int aim_adjust_region_distance;
	int min_aim_region_unit;
	int aim_region_adjust_multiple;
	int aim_max_distance;

	float pass_ball_uint;
	float pass_ball_uintY;
	float pb_initial_motionY;
	float pb_initial_minV;
	float pass_hit_uintXZ;

	float shoot_uint;
	float shoot_uintY;
	float shoot_initial_motionY;
	float shoot_initial_minV;
	float shoot_hit_uintXZ;

	int play_ball_angularX_v;
	int play_ball_angularY_v;
	int play_ball_angularZ_v;

	int can_knock_up_motion;
	float ball_knock_up_motionX;
	float ball_knock_up_motionY;
	float ball_knock_up_motionZ;
	int grab_catch_maxv;
	int grab_stop_motion;
	int grab_initial_v;
	int grab_box_ex_size;
	int grab_box_ex_height;
	float grab_move_time;
	float grab_before_time;
	int grab_cd_tick;
	float dribbing_offset_angle;
	float dribbing_offset_xz_fac;
	int dribble_rush_stop_motion;
	int dribble_rush_cd;
	float play_ball_hold_time;
	float shoot_hold_time;
	int dribble_sound_tick;
	int threshold;
	int select_range;
	float blockShot_special_cd_tick;

	BasketballConfig():blockShot_cd_tick(60),
		steal_cd_tick(23),
		blockShot_height(200),
		bolckShot_special_height(200), 
		phys_linear_damping(0.5f), 
		phys_Angular_damping(0.4f), 
		phys_static_friciton(0.5f), 
		phys_dynamic_friciton(0.5f), 
		phys_restitution(0.9f), 
		phys_mass(1500), 
		basketball_way_move_ratio(1.4f), 
		dribbing_move_ratio(0.9f), 
		charge_move_ratio(0.4f), 
		basketframe_radius(40),
		pb_full_charge_time(0.8f),
		pb_keep_full_charge_time(0.5f),
		rush_cd_tick(5), 
		aim_adjust_region_distance(32), 
		aim_max_distance(32), 
		rush_speed(2), 
		pass_ball_uint(8.0f), 
		pass_ball_uintY(0.5f), 
		pb_initial_motionY(20.0f), 
		pb_initial_minV(20.0f), 
		shoot_uint(10.0f), 
		shoot_uintY(1.0f), 
		shoot_initial_motionY(100.0f), 
		shoot_initial_minV(20.0f), 
		catch_ball_range(250.0f), 
		catch_cd_tick(22), 
		normal_catch_maxv(15), 
		invalid_catch_tick(20), 
		play_ball_angularX_v(300), 
		play_ball_angularY_v(0), 
		play_ball_angularZ_v(0), 
		can_knock_up_motion(0), 
		ball_knock_up_motionX(0), 
		ball_knock_up_motionY(0), 
		ball_knock_up_motionZ(0), 
		grab_catch_maxv(0), 
		grab_stop_motion(0), 
		grab_initial_v(0), 
		grab_box_ex_size(0), 
		grab_box_ex_height(0), 
		grab_move_time(0), 
		shoot_hit_uintXZ(2.0f), 
		dribbing_offset_angle(0), 
		dribbing_offset_xz_fac(0), 
		grab_before_time(0), 
		dribble_rush_stop_motion(0), 
		pass_hit_uintXZ(3.0f), 
		min_aim_region_unit(0), 
		aim_region_adjust_multiple(0), 
		dribble_rush_cd(0), 
		grab_cd_tick(0), 
		play_ball_hold_time(0), 
		shoot_hold_time(0), 
		dribble_sound_tick(5), 
		threshold(50), 
		select_range(10), 
		obstruct_move_ratio(1), 
		blockShot_special_cd_tick(0)
	{

	}
};
//tolua_end
//tolua_begin
struct BallConfig
{
	float catch_ball_range;
	int catch_cd_tick;
	int normal_catch_maxv;
	int tackle_catch_maxv;
	int invalid_catch_tick;
	int tackle_cd_tick;
	float tackle_stop_motion;
	float tackle_box_ex_size;
	float tackle_box_ex_height;
	float pass_ball_uint;
	float pass_ball_uintY;
	float pb_initial_motionY;
	float pb_jump_initial_motionY;
	float pb_offset_motionY;
	float pb_initial_minV;
	float pb_jump_initial_minV;
	float pb_full_charge_time;
	float pb_keep_full_charge_time;
	float shoot_uint;
	float shoot_uintY;
	float shoot_initial_motionY;
	float shoot_jump_initial_motionY;
	float shoot_offset_motionY;
	float shoot_initial_minV;
	float shoot_jump_initial_minV;
	float shoot_full_charge_time;
	float shoot_keep_full_charge_time;
	int ignore_delay_max_charge;
	int strength_charge;
	int kick_ball_angularX_v;
	int kick_ball_angularY_v;
	int kick_ball_angularZ_v;
	int tackle_initial_v;
	float not_onground_tackle_radio;

	float physXscene_gravity;
	float phys_linear_damping;
	float phys_Angular_damping;
	float phys_static_friciton;
	float phys_dynamic_friciton;
	float phys_restitution;
	float phys_mass;

	float section_static_friciton;
	float section_dynamic_friciton;
	float section_restitution;

	float hit_ball_motionX;
	float hit_ball_motionY;
	float hit_ball_motionZ;

	float threshold;
	

	float can_knock_up_motion;
	float ball_knock_up_motionX;
	float ball_knock_up_motionY;
	float ball_knock_up_motionZ;

	float football_way__move_ratio;
	float catch_ball_move_ratio;
	float charge_move_speed_ratio;
	float soundrange;
	float weidgt;

	BallConfig()
		: catch_ball_range(250.0f), catch_cd_tick(22), normal_catch_maxv(15), tackle_catch_maxv(50),
		  invalid_catch_tick(0), tackle_cd_tick(60),
		  tackle_stop_motion(1.0f), tackle_box_ex_size(0), tackle_box_ex_height(0),
		  pass_ball_uint(8.0f), pass_ball_uintY(0.5), pb_initial_motionY(20.0f), pb_jump_initial_motionY(0),
		  pb_offset_motionY(20.0f),
		  pb_initial_minV(20.0f), pb_jump_initial_minV(0), pb_full_charge_time(0.8f), pb_keep_full_charge_time(0.5f),
		  shoot_uint(10.0f), shoot_uintY(1.0f), shoot_initial_motionY(100.0f), shoot_jump_initial_motionY(0),
		  shoot_offset_motionY(100.0f),
		  shoot_initial_minV(20.0f), shoot_jump_initial_minV(0), shoot_full_charge_time(1.5f),
		  shoot_keep_full_charge_time(0.5f),
		  ignore_delay_max_charge(20), strength_charge(90), kick_ball_angularX_v(50), kick_ball_angularY_v(10),
		  kick_ball_angularZ_v(50), tackle_initial_v(250), not_onground_tackle_radio(0.5f), physXscene_gravity(-980.0f),
		  phys_linear_damping(0.9f),
		  phys_Angular_damping(-1), phys_static_friciton(0.8f), phys_dynamic_friciton(0.8f), phys_restitution(0.8f),
		  phys_mass(1.0f), section_static_friciton(0.8f),
		  section_dynamic_friciton(0.8f), section_restitution(0.8f), hit_ball_motionX(50), hit_ball_motionY(30),
		  hit_ball_motionZ(50),
		  threshold(23.5f), can_knock_up_motion(50.0f), ball_knock_up_motionX(0.2f), ball_knock_up_motionY(0.2f),
		  ball_knock_up_motionZ(0.2f), football_way__move_ratio(1.2f), catch_ball_move_ratio(0.8f),
		  charge_move_speed_ratio(0.5f), soundrange(4000.0f), weidgt(1.0)
	{
	}
};
//tolua_end
//tolua_begin
struct RocketConfig
{
	float flying_v;
	float drop_v;
	float landing_v;
	int add_v;
	int add_max_v;
	float rotate;
	int landing_height;
	int flying_ticks;
	int dismount_state;

	RocketConfig()
		: flying_v(30.0f), drop_v(-120), landing_v(-15), add_v(5), add_max_v(50),
		  rotate(0.5f), landing_height(100), flying_ticks(1200), dismount_state(0)
	{
	}
};
//tolua_end
//tolua_begin
struct NpcConfig
{
	int namedisplay_dist;
	int namedisplay_offset_y;
};
//tolua_end
//tolua_begin
struct PhysXConfig
{
	bool enable;
	int mass_limit;
	float force_collide;
	float force_interact;
	float mass_scale;

	float hotcrystal_fly_velocity;
	float spring_gold_velocity;
	float spring_iron_velocity;
	float catapult_velocity;
	float explosion_velocity;
	float spring_gold_push_force;
	float spring_iron_push_force;
	float spring_gold_push_force_enlarge;
	float spring_iron_push_force_enlarge;

	PhysXConfig()
		: enable(true),mass_limit(1000),force_collide(1000),force_interact(1000),mass_scale(0),
		hotcrystal_fly_velocity(800000),spring_gold_velocity(300000),spring_iron_velocity(200000),
		catapult_velocity(4000000),explosion_velocity(50000),
		spring_gold_push_force(1000000), spring_iron_push_force(1000000),
		spring_gold_push_force_enlarge(100), spring_iron_push_force_enlarge(100)
	{
	}
};
//tolua_end
//tolua_begin
struct VehicleConfig
{
	//Wheel
	int drive_direction;
	int steering_direction;
	float steering_rate;
	int recover_mode;
	int left_key;
	int right_key;
	int icon_wheel;
	int propeller_lens_tensile;

	std::string wheeltravel_sound;
	float wheeltravel_sound_volume;
	float wheeltravel_val1;
	float wheeltravel_val2;
	float wheeltravel_sound_speed;
	float wheeltravel_sound_threshold;
	
	//Engine
	float max_torque;
	float max_omega;
	float max_speed;

	//Chassis
	float chassis_high;

	std::string startup_sound;
	float startup_sound_volume;
	float startup_sound_threshold;
	//float pitch_ratio;
	//float pitch_max;
	//float pitch_min;
	//float startup_sound_rpm;

	std::string work_sound;
	float work_sound_volume;
	float work_sound_pitch;
	float work_sound_val1;
	float work_sound_val2;

	float engine_sound_speed;
	float engine_sound_delayspeed;
	float engine_sound_delaytime;
	
	std::string brake_sound;
	float brake_sound_volume;
	float brake_sound_threshold;
	float brake_val1;
	float brake_val2;
	float brake_speed;
	
	int forward_key;
	int backward_key;
	int engine_icon;

	//Seat
	int leave_key;
	int seat_icon;

	//Reset
	int reset_key;
	int reset_icon;

	//Damping
	float linear_damping;
	float angular_damping;

	//SpeedRelateSteer
	float speed1;
	float steer1;
	float speed2;
	float steer2;
	float speed3;
	float steer3;
	float speed4;
	float steer4;

	//刹车多久变成倒车
	int brake_tick_limit;

	float attack_rate;

	float collide_role_ratio;

	int update_mode;
	float tire_friction;
	float mass_component;
	float mass_scale;
	float mass_role;
	float mass_display_ratio;
	std::string energy_destroy_effect;
	std::string energy_destroy_sound;

	//物理关节参数
	float inv_inertia_scale;
	float inv_mass_scale;

	std::string create_effect;
	int create_effect_range;
	std::string create_sound;
	float create_sound_volume;
	
	int joint_mass;
	long long revolute_stiffness;
	int revolute_damping;
	long long prismatic_stiffness;
	int  prismatic_damping;
	long long arm_prismatic_stiffness;
	int  arm_prismatic_damping;

	//推进器过热参数
	float heat_ratio1;
	float heat_ratio2;
	float heat_ratio3;

	int vehicle_liner_max_speed;
	int vehicle_liner_add_speed;
	int vehicle_s_liner_add_speed;
	int vehicle_reference_mass;
	float vehicle_curve;

	//机翼参数
	float wing_force_ratio;
	float wing_force_ratio_car;
	int wing_height_limit_min;
	int wing_height_limit_max;
	float wing_height_ratio_min;

	//尾翼参数
	float emp_force_ratio;
	float emp_force_ratio_car;

	float collide_effect_speed;
	std::string collide_effect;
	std::string collide_sound;

	float staticFriction_no_wheel;
	float dynamicFriction_no_wheel;
	float restitution_no_wheel;
	float staticFriction;
	float dynamicFriction;
	float restitution;

	VehicleConfig()
		: drive_direction(0), steering_direction(0), steering_rate(2.5f), recover_mode(0), left_key(65), right_key(68),
		  icon_wheel(0),
		  propeller_lens_tensile(13), wheeltravel_sound_volume(0), wheeltravel_val1(0), wheeltravel_val2(0),
		  wheeltravel_sound_speed(0),
		  wheeltravel_sound_threshold(0),
		  max_torque(0),
		  max_omega(0), max_speed(0),
		  chassis_high(0),
		  startup_sound_volume(0),
		  startup_sound_threshold(0), work_sound_volume(0),
		  work_sound_pitch(0),
		  work_sound_val1(0),
		  work_sound_val2(0),
		  engine_sound_speed(0),
		  engine_sound_delayspeed(0),
		  engine_sound_delaytime(0), brake_sound_volume(0),
		  brake_sound_threshold(0), brake_val1(0),
		  brake_val2(0),
		  brake_speed(0),
		  forward_key(87),
		  backward_key(83),
		  engine_icon(0), leave_key(16),
		  seat_icon(0), reset_key(82),
		  reset_icon(0), linear_damping(0),
		  angular_damping(0), speed1(0), steer1(0), speed2(0), steer2(0), speed3(0), steer3(0), speed4(0),
		  steer4(0),
		  brake_tick_limit(10),
		  attack_rate(1.0f),
		  collide_role_ratio(1.0f), update_mode(0), tire_friction(0), mass_component(10000), mass_scale(1.0f),
		  mass_role(100000), mass_display_ratio(1.0f), inv_inertia_scale(1),
		  inv_mass_scale(1), create_effect_range(16), create_sound_volume(0), joint_mass(100),
		  revolute_stiffness(100000000),
		  revolute_damping(0), prismatic_stiffness(100000000),
		  prismatic_damping(0), arm_prismatic_stiffness(0), arm_prismatic_damping(0), heat_ratio1(0.25f),
		  heat_ratio2(0.5f), heat_ratio3(0.75f),
		  vehicle_liner_max_speed(4000),
		  vehicle_liner_add_speed(300),
		  vehicle_s_liner_add_speed(300), vehicle_reference_mass(1000000), vehicle_curve(1.0f), wing_force_ratio(1.5f),
		  wing_force_ratio_car(0),
		  wing_height_limit_min(0), wing_height_limit_max(15000), wing_height_ratio_min(0.4f), emp_force_ratio(1.5f),
		  emp_force_ratio_car(0), collide_effect_speed(100.0f),
		  staticFriction_no_wheel(0.8f), dynamicFriction_no_wheel(0.8f), restitution_no_wheel(0.3f), staticFriction(0.5f),
		  dynamicFriction(0.5f), restitution(0.6f)
	{
	}
};
//tolua_end
//tolua_begin
struct CustomModelConfig
{
	float fpp_scale;
	int fpp_x;
	int fpp_y;
	int fpp_z;
	float tpp_scale;
	int tpp_x;
	int tpp_y;
	int tpp_z;
	int hand_y;

	CustomModelConfig() : fpp_scale(1.0f), fpp_x(-200), fpp_y(-1300), fpp_z(-1000), tpp_scale(1.0f), tpp_x(-450), tpp_y(-1000), tpp_z(-1700), hand_y(20)
	{
	}
};
//tolua_end
//tolua_begin
struct EditActorModelConfigInfo
{
	float scale;
	float yaw;
	float pitch;
	short offset_x;
	short offset_y;
	short offset_z;
	float roll;
	EditActorModelConfigInfo() : scale(1.0), yaw(0), pitch(0), roll(0), offset_x(0), offset_y(0), offset_z(0)
	{

	}
};
//tolua_end
//tolua_begin
struct EditActorModelConfig
{
	std::map<std::string, EditActorModelConfigInfo> m_configInfos;
	EditActorModelConfigInfo *getEditActorModelConfigInfo(std::string keyname, bool create=false)
	{
		auto iter = m_configInfos.find(keyname);
		if (iter != m_configInfos.end())
			return &iter->second;

		if (create)
		{
			EditActorModelConfigInfo info;
			m_configInfos[keyname] = info;

			return &m_configInfos[keyname];
		}
		else
			return NULL;
	}
};
//tolua_end
//tolua_begin
struct EditFullyCustomModelConfig
{
	float offset_x;
	float offset_y;
	float offset_z;
	float pitch;
	float yaw;
	float roll;
	float scale;

	EditFullyCustomModelConfig() : offset_x(0.0f), offset_y(100), offset_z(0), pitch(0), yaw(90), roll(0), scale(0.6f)
	{

	}
};
//tolua_end
//tolua_begin
struct TamingSavageConfig
{
	//这里tick假设每秒20个
	int heatvalue_small;        //火焰强度 小（整数）
	int heatvalue_medium;       //火焰强度 中（整数）
	int heatvalue_big;          //火焰强度 大（整数）
	int heatvalue_max;          //火焰强度 最大（整数）
	int heatvalue_initial;      //火焰强度 点燃初始火焰强度（整数）
	int heatvalue_weak_rate;    //火焰强度减弱速度（整数，tick/点）
	int raw_to_cooked;          //生肉烤熟的tick（整数）
	int cooked_to_burnt;        //熟肉烤到糊的tick（整数）
	int tick_rate_small;        //烤肉速率小火 （整数，百分比，数值越大越快）
	int tick_rate_medium;       //烤肉速率中火 （整数，百分比，数值越大越快）
	int tick_rate_big;          //烤肉速率大火 （整数，百分比，数值越大越快）
	int dance_tick;				//跳舞的时间
	int dance_pause_tick;		//跳舞暂停的时间
	int eat_tick;				//吃肉的时间
	int eat_pause_tick;			//吃肉暂停的时间
	int dance_range;			//触发跳舞的范围（格子）
	int awake_range;			//打掉面具后的唤醒范围（格子）
	int max_attract_num;	    //一个篝火吸引野人的最大数量
	int playing_range;          //演奏触发跳舞的范围（格子）

	TamingSavageConfig() : heatvalue_small(1), heatvalue_medium(31), heatvalue_big(71), heatvalue_max(100), heatvalue_initial(50), heatvalue_weak_rate(200), raw_to_cooked(200),
		cooked_to_burnt(2400), tick_rate_small(50), tick_rate_medium(100), tick_rate_big(200), dance_tick(300), dance_pause_tick(100), eat_tick(200), eat_pause_tick(100), 
		dance_range(3), awake_range(10), max_attract_num(6), playing_range(20)
	{

	}
};
//tolua_end
//tolua_begin
struct HPProgressConfig
{
	unsigned char color_self_r;
	unsigned char color_self_g;
	unsigned char color_self_b;
	unsigned char color_self_a;
	unsigned char color_teammate_r;
	unsigned char color_teammate_g;
	unsigned char color_teammate_b;
	unsigned char color_teammate_a;
	unsigned char color_enemy_r;
	unsigned char color_enemy_g;
	unsigned char color_enemy_b;
	unsigned char color_enemy_a;
	int progress_display_dist;
	int text_display_dist;
	int interval_to_name;
	int min_width;
	int max_width;
	int max_hight;
	int billboard_scale_dist;
	HPProgressConfig():color_self_r(0), color_self_g(255), color_self_b(0), color_self_a(255),
					   color_teammate_r(0), color_teammate_g(0), color_teammate_b(255), color_teammate_a(255),
					   color_enemy_r(255), color_enemy_g(0), color_enemy_b(0), color_enemy_a(255),
		progress_display_dist(1600), text_display_dist(800), interval_to_name(10), min_width(90), max_width(600), max_hight(40), billboard_scale_dist(500)
	{
	}
};
//tolua_end
//tolua_begin
struct ArmorProgressConfig
{
	unsigned char color_self_r;
	unsigned char color_self_g;
	unsigned char color_self_b;
	unsigned char color_self_a;
	unsigned char color_teammate_r;
	unsigned char color_teammate_g;
	unsigned char color_teammate_b;
	unsigned char color_teammate_a;
	unsigned char color_enemy_r;
	unsigned char color_enemy_g;
	unsigned char color_enemy_b;
	unsigned char color_enemy_a;
	int progress_display_dist;
	int text_display_dist;
	int interval_to_name;
	int min_width;
	int max_width;
	int max_hight;
	ArmorProgressConfig() :color_self_r(0), color_self_g(255), color_self_b(0), color_self_a(255),
		color_teammate_r(0), color_teammate_g(0), color_teammate_b(255), color_teammate_a(255),
		color_enemy_r(255), color_enemy_g(0), color_enemy_b(0), color_enemy_a(255),
		progress_display_dist(1600), text_display_dist(800), interval_to_name(10), min_width(90), max_width(600), max_hight(40)
	{
	}
};

struct HPChangeEffectConfig
{
	int per_actor_font_limit;
	int font_size;
	int font_size_critical;
	float offset_eye;
	float offset_y_rate;
	float first_view_offset_y_rate;
	float over_view_offset_y_rate;
	int alhpa_end;
	float main_angle;
	float mob_angle;
	float move_distance;
	float time;
	int display_dist;
	bool ZTest;
	unsigned char color_self_r;
	unsigned char color_self_g;
	unsigned char color_self_b;
	unsigned char color_self_a;
	unsigned char color_critical_r;
	unsigned char color_critical_g;
	unsigned char color_critical_b;
	unsigned char color_critical_a;
	unsigned char color_add_r;
	unsigned char color_add_g;
	unsigned char color_add_b;
	unsigned char color_add_a;
	unsigned char color_else_r;
	unsigned char color_else_g;
	unsigned char color_else_b;
	unsigned char color_else_a;
	HPChangeEffectConfig():per_actor_font_limit(20), font_size(20), font_size_critical(22),offset_eye(20), offset_y_rate(0.5),first_view_offset_y_rate(1.0), over_view_offset_y_rate(1.0), alhpa_end(0),main_angle(0), mob_angle(90), move_distance(200), time(1), display_dist(1600), ZTest(true),
		color_self_r(255), color_self_g(0), color_self_b(0), color_self_a(255),
		color_critical_r(255), color_critical_g(255), color_critical_b(0), color_critical_a(255),
		color_add_r(0), color_add_g(255), color_add_b(0), color_add_a(255),
		color_else_r(255), color_else_g(255), color_else_b(255), color_else_a(255)
	{
	}
};
//tolua_end
//tolua_begin
struct HolyTreeConfig
{
	int growth_tick;  //生长时间
	HolyTreeConfig() :growth_tick(20)
	{
	}
};
//tolua_end

//tolua_begin
struct PixelMapConfig
{
	char isEnable;                      //开关：0关闭，1 开启
	// 小地图亮度----------
	float normalLight;                //不在边缘的方块亮度
	float edgeFaceLight; 	        //边缘向光面亮度
	float edgeFaceLight1; 	        //临近边缘向光面亮度
	float edgeBackLight; 				//边缘背光面亮度
	float edgeBackLight1; 		   //-临近边缘背光面亮度
	// 小地图生成半径
	char viewRadius;
	char createShape;              //生成小地图形状  0 是圆形 2 是正方形
	int unitMax;                  //地图显示unitMap上限  控制纹理内存占用
	PixelMapConfig() :
		isEnable(0),
		normalLight(1.0f),
		edgeFaceLight(1.4f),
		edgeFaceLight1(1.2f),
		edgeBackLight(0.6f),
		edgeBackLight1(0.8f),
		viewRadius(4),
		createShape(0),
		unitMax(100)
	{}
};
//tolua_end

//tolua_begin
struct CanvasConfig
{
	float rate;  //生长时间
	int interval;
	CanvasConfig() :rate(0.2f), interval(120)
	{
	}
};
//tolua_end
 
//tolua_begin
struct BirthPointBuild
{
	bool useClipSurround;	    //启用修剪
	bool useBuildFoundation;	//启用地基填充
	//周围修剪
	int clipBlockId;
	bool useBiomeBlock;         //使用地形blockid填充
	int maxClipRange;
	//地基填充
	int fillBlockId;
	int maxFillDepth;
	bool waterFill;  //低于waterLevel 63时是否要填充为水

	int distance;  //和人的距离
	int dir;       //建筑朝向
	std::string filePath;
	BirthPointBuild() :useClipSurround(true), useBuildFoundation(true), useBiomeBlock(true), clipBlockId(101), maxClipRange(5), fillBlockId(101), maxFillDepth(20),
		waterFill(false), distance(20), dir(0), filePath("voxel/XSJX.vmo")
	{
	}
};
//tolua_end

//tolua_begin
struct SurviveGameConfig
{	
	BasketballConfig basketballConfig;
	BallConfig ballconfig;
	RocketConfig rocketconfig;
	NpcConfig npcconfig;
	PhysXConfig physxconfig;
	VehicleConfig vehicleconfig;
	CustomModelConfig custommodelconfig;
	EditActorModelConfig editactormodelcfg;
	EditFullyCustomModelConfig editfullycustommodelcfg;
	TamingSavageConfig tamingsavagecfg;
	HPProgressConfig hpprogresscfg;
	HPChangeEffectConfig hpchangeeffectcfg;
	HolyTreeConfig holytreecfg;
	ArmorProgressConfig armorprogresscfg;
	CanvasConfig canvasconfig;
	PixelMapConfig pixelMapConfig;
	BirthPointBuild birthPointBuilderConfig;
};
//tolua_end
//tolua_begin
struct CustomsModelMaterial
{
	int itemid;
	int num;
};
//tolua_end

//struct BluePrintArea
//{
//	WCoord start;
//	WCoord dim;
//};

//助手服务对象
//tolua_begin
struct VillageHelperTarget{
	WORLD_ID targetId;
	int needNum;
	int needType;
	VillageHelperTarget(){targetId = 0; needType = 0; needNum =0;}
};
//tolua_end

//绑定床的状态
//tolua_begin
struct VillagerBedData{
	int blockX;
	int blockY;
	int blockZ;
	char status;
	VillagerBedData(){blockX = 0; blockY=0; blockZ=0; status=0;};
};
//tolua_end

//图腾信息
//tolua_begin
struct TotemPoint
{
	WCoord pos;
	int mapid;
};
//tolua_end

struct PlantInfo
{
	WCoord pos;
	unsigned int planttime;
};
struct CookBookInfo
{
	int id;
	int state;
	bool operator== (const CookBookInfo& other)
	{
		if (other.id == this->id)
		{
			return true;
		}
		return false;
	}
};

struct PlantTimeInfo
{
	unsigned int planttime;
	unsigned int fertilizeduptime;//肥料加速时间
};

//玩家关联的村庄信息
//tolua_begin
struct VillageInfo
{
	WCoord villagePoint;				//村庄位置
	std::vector<WCoord> villageTotems;	//所有的图腾位置
	std::map<int,std::vector<WCoord>> villageFlags;	//所有的旗帜位置，分类型
	std::set<WORLD_ID> bindVillagers;	//所有的村民
	std::map<WORLD_ID, VillageHelperTarget> helperRelationships; //助手的服务绑定关系
	std::map<WORLD_ID, VillagerBedData> bedRelationships; //床的绑定关系

	VillageInfo(){villagePoint = WCoord(0,6,0);}
};
//tolua_end
//tolua_begin
struct AccountWorldPointInfo
{
	int mapid;
	WCoord spawnpoint;
	WCoord revivepoint;

	AccountWorldPointInfo()
	{
		mapid = -1;
		spawnpoint = WCoord(0, -1, 0);
		revivepoint = WCoord(0, -1, 0);
	}
};
//tolua_end

//tolua_begin
struct ReportWorldParam
{
	long long worldid;     //owid
	long long tureWorldId; //fromid
	long ownerUin;		   //作者uin
	int worldType;         //地图类型: 0:冒险模式（经典） 1：创造模式（编辑）2：冒险模式（极限）3:创造模式（转模拟冒险） 
	int label;             //地图标签: 1=综合, 2=生存, 3=创造, 4=对战, 5=电路, 6=解密, 7=跑酷, 8=其它
	int netType;           //联机类型 1:本地单机 2:联机 3:好友联机 4：单人游戏 99:异常
	int roomType;          //房间类型 个位 1:官方云服（迷你队长）；2：手机服务器；3：PC服务器 4： 普通云服 5：私有云服（租赁服 + 私有服）
	int mapState;		   //地图的状态 ：地图状态：1-未上传 2-公开 3-私有
	int mapOwn;            //地图所属作者：1：自己地图 2：别人地图
	int createOrJoin;      //区分创建还是加入 1:创建 2:加入 
	int room_id;           //云服有房间id
	int ctype;             //ctype类型 1:地图 2：家园地图  5：互动剧 10：专题
	int develop;           //1：开发模式 2：非开发模式 
}; 
//tolua_end

#define GOODS_MAX_NUM 8
//tolua_begin
struct TraderGoodsInfo
{
	unsigned int index;    //数组下标
	unsigned int itemId;   //Item Def ID
	unsigned int payItemId;//需要支付的item ID
	unsigned int price;    //一组商品单价
	unsigned int num;      //一组商品数量
	unsigned int leftcount;//剩余库存
	unsigned char enchants;//附魔
};
//tolua_end

struct TravelingTraderGoodsInfo
{
	unsigned int goodsMaxNum;
	std::vector<TraderGoodsInfo> goodsData;
};

//游商信息
struct TravelingTraderInfo
{
	bool inHome; //是否在家（居住等级 大于 0，才有意义）
	unsigned char hunger; //在家的饥饿状态：0初、1饿、2吃、3饱
	unsigned int lastTimePoint; //上次 出现或离开 的时间点
	unsigned int nextTimeLength; //下次 出现或离开 的时长
	int biome; //生态ID（影响服装类型 和 售卖物品）
	WCoord biomePos; //生态的位置
	WCoord lastInHomePos; //上次在家时的位置

	int housingLevel; //居住等级 0 1 2
	WCoord bedPos; //床位绑定点（居住等级 大于 0，才有意义）
	TravelingTraderGoodsInfo traderGoodsDatas;  //游商售卖的商品
};

// 固定长度的循环列表, 长度N, 存储类型T
template<class T, unsigned N>
class LengthQueue
{
public:
		static constexpr unsigned Size = N;
private:
		unsigned m_Index;
		unsigned m_Count;
		T m_Values[N];
public:
		LengthQueue():m_Index(0), m_Count(0)
		{}

		inline void push(T value)
		{
			if (++m_Index >= Size)
					m_Index %= Size;
			m_Values[m_Index] = value;
			++m_Count;
		}
		inline void resetLast(T value)
		{
			m_Values[m_Index] = value;
		}

		inline void rollback(unsigned count)
		{
			m_Index = (m_Index + Size - count) % Size;
			m_Count = this->count() - count;
		}
		/**
		 * @brief 取往前数index个位置的值
		 * @param index 往前数的数量
		 * @return 往前数index个的值
		*/
		inline const T &getReverse(unsigned index) const
		{
			return m_Values[(m_Index + Size - index) % Size];
		}
		inline const T &getLast() const
		{
			return getReverse(0);
		}
		inline static unsigned size()
		{
			return Size;
		}
		// 当前存储的数量
		inline unsigned count() const
		{
			return m_Count > Size? Size: m_Count;
		}
		inline void clear()
		{
			m_Count = 0;
		}
};

#endif
