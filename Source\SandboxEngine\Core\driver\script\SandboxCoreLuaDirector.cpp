/**
* file : SandboxCoreLuaDirector
* func : 沙盒核心lua脚本
* by : chen<PERSON><PERSON>
*/
#include "SandboxCoreLuaDirector.h"
#include "OgreScriptLuaVM.h"
#include "Common/OgreShared.h"
#include "File/Packages/DataStream.h"
#include "File/FileManager.h"
#include "OgreStringUtil.h"
//#include "PlatformInterface.h"
#include "SandboxCoreDriver.h"
#include "SandboxLua.h"

extern "C"
{
#include "lua.h"
#include "lauxlib.h"
#include "lualib.h"
#include "lua_cjson.h"
}
#include "Minitolua.h"
#include "File/FileUtilities.h"

//using namespace Rainbow;
NS_SANDBOX_BEG

/**
* lua栈顶维持
*/
using SandboxLuaStackTop = KeepStackTop;


/**
* lua文件加载
*/
struct SandboxCoreLuaReadInfo
{
	SandboxCoreLuaReadInfo()
		: fp(nullptr)
	{
	}

	~SandboxCoreLuaReadInfo()
	{
	}

	Rainbow::AutoRefPtr<Rainbow::DataStream> fp;
	char buffer[1024];
};

static const char * SandboxCoreLuaReader(lua_State *L, void *ud, size_t *sz)
{
	SandboxCoreLuaReadInfo* pinfo = (SandboxCoreLuaReadInfo*)ud;

	*sz = pinfo->fp->Read(pinfo->buffer, sizeof(pinfo->buffer));

	if(*sz == 0) return NULL;
	else return pinfo->buffer;
}

const size_t CLASS_NAME_SIZE = 256;
// 解析
bool ParseParams(lua_State* L, int defincnt, const char* szformat, va_list& vl)
{
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
	szformat = szformat ? szformat : "";
	const char* cur = szformat;
	const char* end = szformat + strlen(szformat);
	int c = 0;

	// 输入
	int incnt = defincnt;

	while (c = *cur)
	{
		if (c == '>')
		{
			++cur;
			break;
		}

		switch (c)
		{
		case 'i': // int to number
			lua_pushnumber(L, (lua_Number)va_arg(vl, int));
			++incnt;
			break;
		case 'b': // bool
			lua_pushboolean(L, va_arg(vl, int)); // 这里要使用va_arg(vl, int)
			++incnt;
			break;
		case 'w': // long long to number
			lua_pushnumber(L, (lua_Number)va_arg(vl, long long));
			++incnt;
			break;
		case 'f': // double to number
		case 'd': // 兼容旧的format
			lua_pushnumber(L, (lua_Number)va_arg(vl, double));
			++incnt;
			break;
		case 's': // char* to string
			lua_pushstring(L, va_arg(vl, const char*));
			++incnt;
			break;
        case 'S': // char* to string
		{	std::string& v = *(va_arg(vl, std::string*));
			lua_pushlstring(L, v.c_str(), v.length());
			++incnt; 
		}
            break;
		case 'u': // userdata
		{
			if (cur[1] == '[')
			{
				cur += 2;
				const char* pend = strchr(cur, ']');
				if (pend == nullptr)
				{
					ErrorString("ParseParams() input params ,userdata format error !");
					SANDBOX_ASSERT(false);
					return false;
				}
				size_t len = pend - cur;
				if (len >= CLASS_NAME_SIZE)
				{
					ErrorString("ParseParams() input params ,userdata name too long !");
					SANDBOX_ASSERT(false);
					return false;
				}
				char classname[CLASS_NAME_SIZE];
				memcpy(classname, cur, len);
				classname[len] = 0;

				tolua_pushusertype(L, va_arg(vl, void*), classname);
				cur = pend;
			}
			else
			{
				tolua_pushuserdata(L, va_arg(vl, void*));
			}
			++incnt;
		}
		break;
		default:
			ErrorString("ParseParams() input params,invalid type !");
			SANDBOX_ASSERT(false && "IN invalid type!");
			return false;
		}
		++cur;
	}

	// 计算out
	int outcnt = 0;
	const char* outcur = cur;
	while (c = *outcur)
	{
		switch (c)
		{
		case 'i':
		case 'b':
		case 'w':
		case 'f':
		case 'd':
		case 's':
		case 'S':
			++outcnt;
			break;
		case 'u':
			if (outcur[1] == '[')
			{
				outcur += 2;
				const char* pend = strchr(outcur, ']');
				if (pend == nullptr)
				{
					ErrorString("ParseParams() output params ,userdata format error !");
					SANDBOX_ASSERT(false);
					return false;
				}
				outcur = pend;
			}
			++outcnt;
			break;
		default:
			ErrorString("ParseParams() output params,invalid type !");
			SANDBOX_ASSERT(false && "OUT invalid type!");
			return false;
		}
		++outcur;
	}

	// 执行
	int ret = lua_pcall(L, incnt, outcnt, 0);
	if (ret != 0)
	{
		const char* szError = lua_tostring(L, -1);
		if (szError)
		{
			ErrorStringMsg("CallModuleFunction failed! %s", szError);
		}
#if !defined(IWORLD_SERVER_BUILD) && !defined(BUILD_MINI_EDITOR_APP) // studio 报的太多了
		SANDBOX_ASSERTEX(false, ToString("CallModuleFunction failed! err=", szError));
#endif		
		return false;
	}

	if (outcnt == 0)
		return true;
		
	// 输出参数
	outcnt = -outcnt;
	while (c = *cur)
	{
		switch (c)
		{
		case 'i': // int
			if (lua_isnumber(L, outcnt))
			{
				*va_arg(vl, int*) = (int)lua_tonumber(L, outcnt);
				break;
			}
			return false;
		case 'b': // bool
			if (lua_isboolean(L, outcnt))
			{
				*va_arg(vl,	bool*) = (bool)(lua_toboolean(L, outcnt) != 0);
				break;
			}
			return false;
		case 'w': // long long
			if (lua_isnumber(L, outcnt))
			{
				*va_arg(vl, long long*) = (long long)lua_tonumber(L, outcnt);
				break;
			}
			return false;
		case 'f': // double
		case 'd': // 兼容旧的format
			if (lua_isnumber(L, outcnt))
			{
				*va_arg(vl, double*) = lua_tonumber(L, outcnt);
				break;
			}
			return false;
		case 's': // char[]
			if (lua_isstring(L, outcnt))
			{
				strcpy(va_arg(vl, char*), lua_tostring(L, outcnt));
				break;
			}
			return false;
		case 'S': // std::string*
			if (lua_isstring(L, outcnt))
			{
				std::string& v = *(va_arg(vl, std::string*));
				v = lua_tostring(L, outcnt);
				break;
			}
		case 'u': // userdata
			if (cur[1] == '[')
			{
				cur += 2;
				const char* pend = strchr(cur, ']');
				if (pend == nullptr)
				{
					ErrorString("ParseParams() output params,invalid type !!");
					SANDBOX_ASSERT(false);
					return false;
				}
				size_t len = pend - cur;
				if (len >= CLASS_NAME_SIZE)
				{
					ErrorString("ParseParams() output params ,userdata name too long !!");
					SANDBOX_ASSERT(false);
					return false;
				}
				char classname[CLASS_NAME_SIZE];
				memcpy(classname, cur, len);
				classname[len] = 0;
				cur = pend;

				tolua_Error tolua_err;
				if (tolua_isusertype(L, outcnt, classname, 1, &tolua_err))
				{
					*va_arg(vl, void**) = tolua_tousertype(L, outcnt, NULL);
					break;
				}
			}
			else
			{
				if (lua_isuserdata(L, outcnt))
				{
					*va_arg(vl, void**) = tolua_tousertype(L, outcnt, NULL);
					break;
				}
			}
			return false;
		default:
			ErrorString("ParseParams() output params,invalid type !!");
			SANDBOX_ASSERT(false && "out invalid type!");
			return false;
		}
		++cur;
		++outcnt;
	}

	return true;
}


SandboxCoreLuaDirector::SandboxCoreLuaDirector(SandboxCoreDriver* driver, MINIW::ScriptVM* vm)
	: Super(driver), m_gameScriptVM(vm), m_enable(false)
{
}

SandboxCoreLuaDirector::~SandboxCoreLuaDirector()
{}

bool SandboxCoreLuaDirector::Init()
{
	m_enable = true;
	CallFunction("SandboxCoreInit", "u[MNSandbox::SandboxCoreDriver]", m_owner);
	return true;
}

void SandboxCoreLuaDirector::Release()
{
	CallFunction("SandboxCoreRelease");
	m_enable = false;
}

void SandboxCoreLuaDirector::StartSandbox()
{
	CallFunction("SandboxCoreStartSandbox");
}

void SandboxCoreLuaDirector::BeginGame()
{
#ifdef SANDBOX_SURVIVAL_LUASCRIPT_VM
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		m_sandboxScriptVM.OpenState();
	}
	else
	{
		lua_State* L = m_gameScriptVM ? m_gameScriptVM->getLuaState() : nullptr;
		SANDBOX_ASSERT(m_gameScriptVM && "m_gameScriptVM is nullptr!");
		SANDBOX_ASSERT(L && "m_gameScriptVM->getLuaState() is nullptr!");

		m_sandboxScriptVM.OpenState(L);
	}
#else
	m_sandboxScriptVM.OpenState();
#endif // SANDBOX_SURVIVAL_LUASCRIPT_VM
}

void SandboxCoreLuaDirector::EndGame()
{
	m_sandboxScriptVM.CloseState();
}



bool SandboxCoreLuaDirector::IsEnable(const char* errorfunction/*=nullptr*/) const
{
	if (!m_enable)
	{
		return false;
	}
	if (!m_gameScriptVM)
	{
		return false;
	}
	/*
	if (!m_gameScriptVM->checkCallValid())
	{
		LOG_WARNING("from non-main thread: functionname = %s", errorfunction);
		return false;
	}*/
	if (!m_gameScriptVM->isCallFunctionEnabled())
	{
		return false;
	}
	return true;
}

bool SandboxCoreLuaDirector::LoadTocFile(const std::string& toc)
{
    OPTICK_EVENT();
    OPTICK_TAG("TocFile", toc.c_str());
	/*
	if (!m_gameScriptVM->checkCallValid())
		return false;*/
	Rainbow::AutoRefPtr<Rainbow::DataStream> fp = Rainbow::GetFileManager().OpenFile(toc.c_str(), true);
	if (!fp)
	{
		ErrorStringMsg("SANDBOX ERROR! %s toc does not exist!", toc.c_str());
		SANDBOX_ASSERT(false && "SANDBOX ERROR! toc is not exist!");
		return false;
	}

	char buffer[256];
	bool ret = true;
	while(!fp->Eof())
	{
		fp->ReadLine(buffer, sizeof(buffer));
		std::string inbuf = buffer;

		size_t pos = inbuf.find(".lua");
		if ((pos != std::string::npos) && ((inbuf.find("##"))>0))
		{
			if(!m_gameScriptVM->loadPackage(inbuf.c_str()))
			{
				SANDBOX_ASSERT(false && "SANDBOX ERROR! load toc filed!");
				ret = false;
				break;
			}
		}
	}
	return ret;
}

bool SandboxCoreLuaDirector::LoadLuaFile(const std::string& luafile)
{
	lua_State* L = GetLuaStateSafe();
	if (!L) return false;
	SandboxLuaStackTop stackbackup(L);

    OPTICK_EVENT();
    OPTICK_TAG("LuaFile", luafile.c_str());

	core::string fullpath(luafile), pkgname, exten, outpath;
	Rainbow::StringUtil::splitFullFilename(fullpath, pkgname, exten, outpath);
	lua_getglobal(L, "SandboxLua");
	if (!lua_istable(L, -1))
	{
		lua_newtable(L);
		lua_pushvalue(L, -1);
		lua_setglobal(L, "SandboxLua");
	}
	lua_getfield(L, -1, "filecaches");
	if (!lua_istable(L, -1))
	{
		lua_newtable(L);
		lua_pushvalue(L, -1);
		lua_setfield(L, -3, "filecaches");
	}
	lua_getfield(L, -1, luafile.c_str());
	if (lua_isfunction(L, -1))
	{
		return true;
	}
	lua_pop(L, 1); // pop nil

	int tablepos = lua_gettop(L);
	SANDBOX_ASSERT(lua_istable(L, tablepos));

	SandboxCoreLuaReadInfo info;
	info.fp = Rainbow::GetFileManager().OpenFile(luafile.c_str(), true);
	if(!info.fp)
	{
		//LOG_INFO("script open failed: %s", luafile.c_str());
		return false;
	}

	lua_pushlstring(L, luafile.c_str(), luafile.length());
	if (lua_load(L, SandboxCoreLuaReader, &info, luafile.c_str()) != 0)
	{
		const char *perr = lua_tostring(L, -1);
		LOG_INFO("script load failed: %s, error: %s", luafile.c_str(), perr);
		SANDBOX_ASSERT(0);
		return false;
	}

	lua_rawset(L, tablepos); // 存档filecaches中
	return true;
}
// 卸载lua文件 -- 用于热更新 --- by charlesxie
bool SandboxCoreLuaDirector::UnloadLuaFile(const std::string& luafile)
{
	lua_State* L = GetLuaStateSafe();
	if (!L) return false;
	SandboxLuaStackTop stackbackup(L);

	lua_getglobal(L, "SandboxLua");
	if (!lua_istable(L, -1))
		return false;

	lua_getfield(L, -1, "filecaches");
	if (!lua_istable(L, -1))
		return false;

	// Remove the entry by setting it to nil
	lua_pushlstring(L, luafile.c_str(), luafile.length());
	lua_pushnil(L);
	lua_rawset(L, -3);

	return true;
}

bool SandboxCoreLuaDirector::DoLuaFile(const std::string& luafile)
{
	lua_State* L = GetLuaStateSafe();
	if (!L) return false;

	// 检查是否已经加载过
	{
		SandboxLuaStackTop stackbackup(L);
		std::string key = ToString("SandboxLua:fileresults:", luafile);
		LuaGetTableDataKey(L, key.c_str(), 1); // t[1] = true
		if (lua_isboolean(L, -1) && lua_toboolean(L, -1)) // 有结果了
			return true;
	}

	// 加载文件
	if (!LoadLuaFile(luafile))
		return false;

    OPTICK_EVENT();
    OPTICK_TAG("LuaFile", luafile.c_str());
	SandboxLuaStackTop stackbackup(L);
	
	lua_getglobal(L, "SandboxLua");
	if (!lua_istable(L, -1))
		return false;

	lua_getfield(L, -1, "filecaches");
	if (!lua_istable(L, -1))
		return false;

	int filesIdx = lua_gettop(L);
	int sandboxluaIdx = filesIdx - 1;

	lua_getfield(L, -1, luafile.c_str());
	if (!lua_isfunction(L, -1))
		return false;

	// 执行
	if (lua_pcall(L, 0, 1, 0) != 0)
	{
		const char *perr = lua_tostring(L, -1);
		SANDBOX_ASSERTEX(!perr, ToString("script call failed: ", luafile, ", error: ", perr));
		return false;
	}

	// 缓存的值
	lua_newtable(L);
	lua_pushboolean(L, (int)true);
	lua_rawseti(L, -2, 1); // t[1] = true
	if (!lua_isnil(L, -2))
	{
		lua_pushvalue(L, -2);
		lua_rawseti(L, -2, 2); // t[2] = value
	}

	// 缓存结果
	int validx = lua_gettop(L);
	lua_getfield(L, sandboxluaIdx, "fileresults"); // SandboxLua.fileresults
	if (lua_isnil(L, -1))
	{
		lua_newtable(L);
		lua_pushvalue(L, -1);
		lua_setfield(L, sandboxluaIdx, "fileresults");
	}
	lua_pushlstring(L, luafile.c_str(), luafile.length());
	lua_pushvalue(L, validx);
	lua_rawset(L, -3); // SandboxLua.fileresults[luafile] = value

	// 清理文件缓存
	lua_pushlstring(L, luafile.c_str(), luafile.length());
	lua_pushnil(L);
	lua_rawset(L, filesIdx); // SandboxLua.filecaches[luafile] = nil
	return true;
}

bool SandboxCoreLuaDirector::RequireLuaFile(const std::string& luafile, const std::function<void(int)>& callback)
{
	if (!MINIW::ScriptVM::IsVaild())
		return false;
	if (!IsEnable(luafile.c_str()))
		return false;
	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
		return false;

	OPTICK_EVENT();
	OPTICK_TAG("RequireFile", luafile.c_str());
	SandboxLuaStackTop stackbackup(L);

	// 加载
	lua_getglobal(L, "require");
	lua_pushlstring(L, luafile.c_str(), luafile.length());
	int ret = lua_pcall(L, 1, -1, 0);
	if (ret == 0)
	{
		// 成功
		int newtop = lua_gettop(L);
		int cnt = newtop - stackbackup.m_top;
		cnt = std::max(cnt, 0);
		callback(cnt);
		return true;
	}
	else if (ret == 1)
	{
		// yield
		return false;
	}
	else
	{
		// error
		const char* perr = lua_tostring(L, -1);
		if (perr)
		{
			LOG_INFO("script require failed: %s, error: %s", luafile.c_str(), perr);
		}
		return false;
	}
}

bool SandboxCoreLuaDirector::CallFunction(const std::string& functionname)
{
	return CallFunction(functionname, nullptr);
}

bool SandboxCoreLuaDirector::CallFunction(const std::string& functionname, const char* format, ...)
{
	if (!IsEnable(functionname.c_str()))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
    lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("Global", functionname.c_str());
	SandboxLuaStackTop stackbackup(L);

	lua_getglobal(L, functionname.c_str());
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERTEX(false, ToString("lua function is invalid! f=", functionname, ", format=", format));
		return false;
	}

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, 0, format, vl);

	va_end(vl);
	return ok;
}

lua_State* SandboxCoreLuaDirector::GetLuaState() const
{
	return m_gameScriptVM->getLuaState();
}

lua_State* SandboxCoreLuaDirector::GetLuaStateSafe() const
{
	if (!IsEnable())
		return nullptr;
	if (!MINIW::ScriptVM::IsVaild())
		return nullptr;

	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
		return nullptr;

	return L;
}

bool SandboxCoreLuaDirector::CallFunctionM(const std::string& modulename, const std::string& functionname, const char* format, ...)
{
	if (!IsEnable(functionname.c_str()))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
    lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG(modulename.c_str(), functionname.c_str());
	SandboxLuaStackTop stackbackup(L);

	int defincnt = 0;
	if (modulename.empty())
	{
		// module为空表示访问的时全局函数
		lua_getglobal(L, functionname.c_str());
		if (!lua_isfunction(L, -1))
		{
			SANDBOX_ASSERT(false);
			return false;
		}
	}
	else
	{
		// 先取module，再取函数
		lua_getglobal(L, modulename.c_str());
		if (!lua_istable(L, -1))
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		lua_getfield(L, -1, functionname.c_str());
		if (!lua_isfunction(L, -1))
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		lua_pushvalue(L, -2); //self
		++defincnt;
	}

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, defincnt, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionM(const char* modulename, const char* functionname, const char* format, ...)
{
	if (!IsEnable(functionname))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG(modulename, functionname);
	SandboxLuaStackTop stackbackup(L);

	int defincnt = 0;
	if (strlen(modulename) == 0)
	{
		// module为空表示访问的时全局函数
		lua_getglobal(L, functionname);
		if (!lua_isfunction(L, -1))
		{
#ifndef BUILD_MINI_EDITOR_APP // studio 的脚本缺失比较多
			SANDBOX_ASSERT(false);
#endif
			return false;
		}
	}
	else
	{
		// 先取module，再取函数
		lua_getglobal(L, modulename);
		if (!lua_istable(L, -1))
		{
			SANDBOX_ASSERTEX(false, ToString("can not found function! module=", modulename, ", f=", functionname));
			return false;
		}

		lua_getfield(L, -1, functionname);
		if (!lua_isfunction(L, -1))
		{
#ifndef BUILD_MINI_EDITOR_APP // studio 的脚本缺失比较多
			SANDBOX_ASSERT(false);
#endif
			return false;
		}

		lua_pushvalue(L, -2); //self
		++defincnt;
	}

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, defincnt, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionM2(const std::string& modulename1,
	const std::string& modulename2,
	const std::string& functionname,
	const char* format,
	...)
{
	if (!IsEnable(functionname.c_str()))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
    lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}

	SandboxLuaStackTop stackbackup(L);
	
	if (modulename1.empty() || modulename2.empty())
		return false;

	lua_getglobal(L, modulename1.c_str());
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename2.c_str());
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, functionname.c_str());
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("module1", modulename1.c_str());
    OPTICK_TAG("module2", modulename2.c_str());
    OPTICK_TAG("func", functionname.c_str());
	lua_pushvalue(L, -2); // self

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	// 略过"刷屏"的 log
	/*
	if (strcmp(format, "wu[MNSandbox::SandboxContext]>u[MNSandbox::SandboxResult]") != 0 &&
		functionname != "RegisterScheduler")
	{
		// 上传 log 至 firebase 以帮助定位崩溃:
		// 此后运行到 ltable.c#460 的崩溃有多组记录，加起来有 100多 users
		ErrorStringMsg("CallFunctionM2(%s, %s, %s, %s)", modulename1.c_str(), modulename2.c_str(), functionname.c_str(), format);
	}
	*/
	bool ok = ParseParams(L, 1, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionM2(const char* modulename1, const char* modulename2, char* functionname, const char* format, ...)
{
	if (!IsEnable(functionname))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
	if (strlen(modulename1) == 0 || strlen(modulename2) == 0)
		return false;

	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    
	SandboxLuaStackTop stackbackup(L);

	lua_getglobal(L, modulename1);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename2);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, functionname);
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("module1", modulename1);
    OPTICK_TAG("module2", modulename2);
    OPTICK_TAG("func", functionname);
	lua_pushvalue(L, -2); // self

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	// 略过"刷屏"的 log
	/*
	if (strcmp(format, "wu[MNSandbox::SandboxContext]>u[MNSandbox::SandboxResult]") != 0 &&
		functionname != "RegisterScheduler")
	{
		// 上传 log 至 firebase 以帮助定位崩溃:
		// 此后运行到 ltable.c#460 的崩溃有多组记录，加起来有 100多 users
		ErrorStringMsg("CallFunctionM2(%s, %s, %s, %s)", modulename1.c_str(), modulename2.c_str(), functionname.c_str(), format);
	}
	*/
	bool ok = ParseParams(L, 1, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionM3(const std::string& modulename1, const std::string& modulename2, const std::string& modulename3, const std::string& functionname, const char* format, ...)
{
	if (!IsEnable(functionname.c_str()))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
    lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}

	SandboxLuaStackTop stackbackup(L);
	
	if (modulename1.empty() || modulename2.empty() || modulename3.empty())
		return false;
	
	lua_getglobal(L, modulename1.c_str());
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename2.c_str());
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename3.c_str());
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, functionname.c_str());
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("module1", modulename1.c_str());
    OPTICK_TAG("module2", modulename2.c_str());
    OPTICK_TAG("module3", modulename3.c_str());
    OPTICK_TAG("func", functionname.c_str());
	lua_pushvalue(L, -2); //self

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, 1, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionM3(const char* modulename1, const char* modulename2, const char* modulename3, const char* functionname, const char* format, ...)
{
	if (!IsEnable(functionname))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
	if (strlen(modulename1) == 0 || strlen(modulename2) == 0 || strlen(modulename3) == 0)
		return false;

	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}

	SandboxLuaStackTop stackbackup(L);

	lua_getglobal(L, modulename1);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename2);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, modulename3);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, functionname);
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("module1", modulename1);
    OPTICK_TAG("module2", modulename2);
    OPTICK_TAG("module3", modulename3);
    OPTICK_TAG("func", functionname);
	lua_pushvalue(L, -2); //self

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, 1, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionMGroup(const SandboxVector<std::string>& modules, const std::string& functionname, const char* format, ...)
{
	if (!IsEnable(functionname.c_str()))
	{
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
    lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    
	SandboxLuaStackTop stackbackup(L);
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
	int defincnt = 0;
	if (modules.Empty())
	{
        OPTICK_TAG("Global",functionname.c_str());
		// module为空表示访问的时全局函数
		lua_getglobal(L, functionname.c_str());
		if (!lua_isfunction(L, -1))
		{
			SANDBOX_ASSERT(false);
			return false;
		}
	}
	else
	{
		// 先取module，再取函数
		bool first = true;
		bool ok = true;
#if USE_OPTICK
        const std::string* refModuleName = nullptr;
        modules.ForEach([&L, &first, &ok, &refModuleName](const std::string& modulename) -> bool {

#else
		modules.ForEach([&L, &first, &ok](const std::string& modulename) -> bool {
#endif
			if (first)
			{
				lua_getglobal(L, modulename.c_str());
				first = false;
#if USE_OPTICK
                refModuleName = &modulename;
#endif
			}
			else
			{
				if (!lua_istable(L, -1))
				{
					ok = false;
					SANDBOX_ASSERT(false);
					return false;
				}
				lua_getfield(L, -1, modulename.c_str());
#if USE_OPTICK
                refModuleName = &modulename;
#endif
			}
			return true;
		});
		if (!ok)
			return false;
        
#if USE_OPTICK
        if (refModuleName) {
            OPTICK_TAG(refModuleName->c_str(), functionname.c_str());
        } 
#endif
		if (!lua_isfunction(L, -1))
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		lua_pushvalue(L, -2); //self
		++defincnt;
	}

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, defincnt, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::CallFunctionUD(void* ud, const char* usertype, const char* methodname, const char* format, ...)
{
	if (!IsEnable(methodname))
		return false;

	if (!ud || !usertype || !usertype[0] || !methodname || !methodname[0])
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	if (!MINIW::ScriptVM::IsVaild())
	{
		return false;
	}
	lua_State* L = m_gameScriptVM->getLuaState();
	if (!ScriptVM::isCurrentThreadIsMainThread(L))
	{
		return false;
	}
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Script);
    OPTICK_TAG("UD", methodname);
	SandboxLuaStackTop stackbackup(L);

	lua_getglobal(L, usertype);
	if (!lua_istable(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	lua_getfield(L, -1, methodname);
	if (!lua_isfunction(L, -1))
	{
		SANDBOX_ASSERT(false);
		return false;
	}

	tolua_pushusertype(L, ud, usertype); //self

	// 解析不定参数
	va_list vl;
	va_start(vl, format);

	bool ok = ParseParams(L, 1, format, vl);

	va_end(vl);
	return ok;
}

bool SandboxCoreLuaDirector::GetTableData(const char* gmodules, const std::function<bool(lua_State* L)>& callback)
{
	lua_State* L = GetLuaStateSafe();
	if (!L) return false;

	SandboxLuaStackTop stackbackup(L);
	if (!GetTableDataByKey(L, gmodules))
		return false;

	return callback(L);
}

bool SandboxCoreLuaDirector::GetTableDataByKey(lua_State* L, const char* gmodules)
{
	if (!gmodules || !gmodules[0])
		return false;

	int keyidx = lua_gettop(L);

	// 获取索引的模块
	const char* param = gmodules;
	const char* end = gmodules + strlen(gmodules);
	const char* found = end;
	size_t idx = 0;
	int tidx = LUA_GLOBALSINDEX;

	while ((found = std::find(param, end, ':')) != end)
	{
		lua_pushlstring(L, param, found - param);
		lua_rawget(L, tidx);
		if (!lua_istable(L, -1))
		{
			lua_settop(L, keyidx);
			return false;
		}

		tidx = -2;
		param = found + 1;
	}

	lua_pushlstring(L, param, end - param);
	lua_rawget(L, tidx);
	if (!lua_istable(L, -1))
	{
		lua_settop(L, keyidx);
		return false;
	}

	lua_pushvalue(L, keyidx);
	lua_rawget(L, -2);

	// 数据保留
	lua_replace(L, keyidx);
	lua_settop(L, keyidx);
	return true;
}

int SandboxCoreLuaDirector::LuaGetTableData(lua_State* L, const char* gmodules)
{
	if (!GetTableDataByKey(L, gmodules))
	{
		lua_pop(L, 1);
		lua_pushnil(L);
	}
	return 1;
}

///////////////////////////////////////////////////////////////////////////////////

SandboxCoreLuaDirector& GetCoreLuaDirector()
{
	return SandboxCoreDriver::GetInstance().GetLua();
}

NS_SANDBOX_END
