#pragma once


#include "SandboxAssetUploadRef.h"
#include "Utilities/String.h"
#include "AssetPipeline/BuildTarget.h"

namespace MNSandbox {

	class /*EXPORT_SANDBOXENGINE*/ AssetUploadPrefab : public AssetUploadRef
	{
		DECLARE_REF_NEW_INSTANCE(AssetUploadPrefab)
	public:
		AssetUploadPrefab();
		virtual ~AssetUploadPrefab();

		
	virtual void OnGetRemoteResIdFinish()override;
	protected:
		virtual void OnInit() override;
		virtual void OnPrepareUploadFile() override;
		virtual void OnPrepareSetMeta() override;
	private:
		core::hash_map<core::string, core::hash_set<core::string>> m_dependMap;
		
	};

}