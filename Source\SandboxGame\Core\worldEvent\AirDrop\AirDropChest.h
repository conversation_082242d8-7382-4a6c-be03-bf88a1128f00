#pragma once
#include "Math/Vector3f.h"
#include <vector>
#include <memory>
#include "SandboxGame.h"
#include "ActorAirDropParachute.h"

class ActorAirDropParachute;
class ActorCubeChest;

class EXPORT_SANDBOXGAME AirDropChest {
public:
    AirDropChest(const Rainbow::Vector3f& spawnPos, const Rainbow::Vector3f& targetPos, int damageHP);
    ~AirDropChest();

    // 核心更新
    void Update(float deltaTime);
    bool HasLanded() const { return m_hasLanded; }
    void setChestId(int id);
    void setChestModleTyle(std::string model);
    WCoord& getChestPos() { return m_chestPos; }
    ActorCubeChest* getChestActor() { return m_chest; };
    long long getChestActorObjid() { return m_chest_objid; };
    bool  isSpawn() { return m_isSpawn; };
private:
    bool isActorExist(long long objId);
    // 空投伞
    ActorAirDropParachute* m_parachute;
    long long m_parachute_objid;
    // 空投箱
    int  m_chestId;
    WCoord m_chestPos;
    std::string m_chestModelType;
    ActorCubeChest* m_chest;
    long long m_chest_objid;
    bool m_isSpawn;

    // 位置相关
    Rainbow::Vector3f m_spawnPos;
    Rainbow::Vector3f m_targetPos;
    Rainbow::Vector3f m_currentPos;
    // 是否落地
    bool m_hasLanded{ false };
    // 空投伞伤害
    int m_damageHP;

    void createAirDropChest(WCoord &pos);
    

};

