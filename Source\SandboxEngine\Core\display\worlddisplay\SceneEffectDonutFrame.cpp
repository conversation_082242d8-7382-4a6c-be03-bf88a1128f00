/**
* file : SceneEffectDonutFrame
* func : 场景效果 （圆锥、圆台框）
* by : pengdapu
*/
#include "SceneEffectDonutFrame.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "SceneEffectEllipse.h"
#include "WorldRender.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectDonutFrame::SceneEffectDonutFrame()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_eSes = SceneEffectShape::DONUT_FRAME;
}

SceneEffectDonutFrame::~SceneEffectDonutFrame()
{
}

void SceneEffectDonutFrame::SetRadius(float radius)
{
	if (m_radius == radius)
		return;

	m_radius = radius;
	m_fInnerRadius = radius * 0.25f;
	m_originRadius = radius;
}

void SceneEffectDonutFrame::SetInnerRadius(float ir)
{
	if (m_fInnerRadius == ir)
		return;

	m_fInnerRadius = ir;
}

void SceneEffectDonutFrame::OnClear()
{
	for (int i = 0; i < 8; ++i)
	{
		SANDBOX_DELETE(m_aEllipses[i]);
	}
}

void SceneEffectDonutFrame::Refresh()
{
	OnClear();
	for (int i = 0; i < 8; ++i)
	{
		SceneEffectEllipse* ellipse = m_aEllipses[i] = SANDBOX_NEW(SceneEffectEllipse);
		ellipse->SetCenter(m_vCenter);
		ellipse->SetColor(m_Color, m_Color);
		ellipse->SetStroke(m_iStroke);
		ellipse->SetMtlType(m_MtlType);
		ellipse->SetRadius(m_radius);
	}

	for (int i = 4; i < 8; ++i)
	{
		SceneEffectEllipse* ellipse = m_aEllipses[i];
		ellipse->SetRadius(m_fInnerRadius);
	}

	m_aEllipses[4]->SetRotationAxis(Vector3f::neg_zAxis, Vector3f::neg_zAxis);
	m_aEllipses[5]->SetRotationAxis(Vector3f::xAxis, Vector3f::xAxis);
	m_aEllipses[6]->SetRotationAxis(Vector3f::neg_zAxis, Vector3f::neg_zAxis);
	m_aEllipses[7]->SetRotationAxis(Vector3f::xAxis, Vector3f::xAxis);

	for (int i = 4; i < 8; ++i)
	{
		m_aEllipses[4]->SetSector(16);
	}

	SetTRS(m_vCenter, m_qRotation, m_vScale);
}

void SceneEffectDonutFrame::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}
	for (int i = 0; i < 8; ++i)
	{
		SceneEffectEllipse* ellipse = m_aEllipses[i];
		if (ellipse) ellipse->OnDraw(pWorld);
	}
}

bool SceneEffectDonutFrame::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectDonutFrame::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;

	Vector3f vu = vc;
	Vector3f vd = vc;

	const float ri = m_fInnerRadius * vs.y;
	vu.y += ri;
	vd.y -= ri;
	const float rOuter = m_radius + ri;
	const float rInner = m_radius - ri;

	const float major = m_radius * vs.x;
	const float minor = m_radius * vs.z;
	Vector3f aVs[4];
	aVs[0].Set(major, 0, 0);
	aVs[1].Set(0, 0, minor);
	aVs[2].Set(-major, 0, 0);
	aVs[3].Set(0, 0, -minor);

	Matrix4x4f matRotate;
	QuaternionfToMatrix(q, matRotate);
	for (int i = 0; i < 4; ++i)
	{
		aVs[i] = matRotate.MultiplyPoint3(aVs[i]);
		aVs[i] += vc;
	}

	//统一成向后或向右
	if (m_aEllipses[0]) m_aEllipses[0]->RefreshEllipseFrame(vc, q, major + ri, minor + ri);
	if (m_aEllipses[1]) m_aEllipses[1]->RefreshEllipseFrame(vu, q, major, minor);
	if (m_aEllipses[2]) m_aEllipses[2]->RefreshEllipseFrame(vc, q, major - ri, minor - ri);
	if (m_aEllipses[3]) m_aEllipses[3]->RefreshEllipseFrame(vd, q, major, minor);
	if (m_aEllipses[4]) m_aEllipses[4]->RefreshEllipseFrame(aVs[0], q, ri, ri);
	if (m_aEllipses[5]) m_aEllipses[5]->RefreshEllipseFrame(aVs[1], q, ri, ri);
	if (m_aEllipses[6]) m_aEllipses[6]->RefreshEllipseFrame(aVs[2], q, ri, ri);
	if (m_aEllipses[7]) m_aEllipses[7]->RefreshEllipseFrame(aVs[3], q, ri, ri);
}
