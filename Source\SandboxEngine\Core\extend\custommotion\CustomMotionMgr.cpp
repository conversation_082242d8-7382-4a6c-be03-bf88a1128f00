#include "File/FileManager.h"
#include "File/DirVisitor.h"
#include "Entity/OgreModelData.h"
#include "Mesh/LegacyOgreSkeletonData.h"
#include "Mesh/LegacyOgreAnimTrackBone.h"
#include "OgreScriptLuaVM.h"
#include "GlobalFunctions.h"
#include "blocks/BlockMaterialMgr.h"

#include "CustomMotion_generated.h"
#include "CustomMotionMgr.h"
#include "CustomMotion.h"
#include "WorldManager.h"
#include "OgreUtils.h"
#include <time.h>
#include "SandboxGFunc.h"


#include "Jobs/JobTypes.h"
#include "Jobs/JobBatchDispatcher.h"
#include "Jobs/BackStageJob.h"
#include "Jobs/MainThreadJob.h"
#include "SandboxCoreDriver.h"
#include "ClientInfoProxy.h"
#include "WorldStringManagerProxy.h"
#include "OgreShared.h"
#include "Core/GameObject.h"
#include "OgreModel.h"
using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;

class CustomMotionBGLoadAsyncTask
{
public:
	std::string m_sFilePath;
	CustomMotion* m_pCustomMotion;
	bool m_bLoadingResult;
	int m_CheckUin;
	RES_LIB_TYPE m_Type;
	
	CustomMotionMgr* mgr;
	Rainbow::BackJobFence jobFence;

	CustomMotionBGLoadAsyncTask()
	{
		m_bLoadingResult = false;
	}
	void DoInJobThread()
	{
		if (m_pCustomMotion && m_pCustomMotion->load(m_sFilePath, m_CheckUin))
		{
			m_bLoadingResult = true;
		}
	}
	void DoInMainThread()
	{
        OPTICK_EVENT();
		if (mgr)
		{
			mgr->OnLoadAyncTaskCompleted(this);
		}
		else
		{
			ENG_DELETE(m_pCustomMotion);
		}
	}
};

static void LoadCustomMotionAyncComplete(CustomMotionBGLoadAsyncTask* task)
{
	task->DoInMainThread();
	ENG_DELETE_LABEL(task, kMemGame);
}

static void LoadCustomMotionAsync(CustomMotionBGLoadAsyncTask* task)
{
	OPTICK_EVENT();
	task->DoInJobThread();
	GetMainThreadJob().ScheduleMainThreadJob(LoadCustomMotionAyncComplete, task);
}

CustomMotionMgr::CustomMotionMgr() : m_bResIsLoaded(false), m_pCusMotionAct(NULL), m_iCusMotionIDSeed(ACTION_CUSTOM_ANIM_START_ID), m_IsInRelease(false)
{
	m_CusMotionBoneNames.push_back("Head");
	m_CusMotionBoneNames.push_back("Neck");
	m_CusMotionBoneNames.push_back("Bip01 Spine1");
	m_CusMotionBoneNames.push_back("Bip01 Spine");
	m_CusMotionBoneNames.push_back("Bip01 Pelvis");
	m_CusMotionBoneNames.push_back("Bip01");
	m_CusMotionBoneNames.push_back("Bip01 Neck");
	m_CusMotionBoneNames.push_back("Bip01 R Thigh");
	m_CusMotionBoneNames.push_back("Bip01 R Calf");
	m_CusMotionBoneNames.push_back("Bip01 R Foot");
	m_CusMotionBoneNames.push_back("Bip01 L Thigh");
	m_CusMotionBoneNames.push_back("Bip01 L Calf");
	m_CusMotionBoneNames.push_back("Bip01 L Foot");
	m_CusMotionBoneNames.push_back("Bip01 R Clavicle");
	m_CusMotionBoneNames.push_back("Bip01 R UpperArm");
	m_CusMotionBoneNames.push_back("Bip01 R Forearm");
	m_CusMotionBoneNames.push_back("Bip01 R Hand");
	m_CusMotionBoneNames.push_back("Bip01 L Clavicle");
	m_CusMotionBoneNames.push_back("Bip01 L UpperArm");
	m_CusMotionBoneNames.push_back("Bip01 L Forearm");
	m_CusMotionBoneNames.push_back("Bip01 L Hand");
	m_CusMotionBoneNames.push_back("Scene Root");
}

CustomMotionMgr::~CustomMotionMgr()
{
	m_IsInRelease = true;
	for (auto iter = m_LoadAsyncTasks.begin(); iter != m_LoadAsyncTasks.end(); ++iter)
	{
		CustomMotionBGLoadAsyncTask* task = *iter;
		task->mgr = nullptr;
		//GetBackStageJob().SyncFence(task->jobFence);
	}
	m_LoadAsyncTasks.clear();   //CustomMotionBGLoadAsyncTask �����߳�jobִ�к���Լ��ͷŶ���ʵ��

	for (size_t i = 0; i < m_MapCustomMotions.size(); i++)
	{
		if (m_MapCustomMotions[i] != NULL)
		{
			ENG_DELETE(m_MapCustomMotions[i]);
		}
	}
	m_MapCustomMotions.clear();

	for (size_t i = 0; i < m_ResCustomMotions.size(); i++)
	{
		if (m_ResCustomMotions[i] != NULL)
		{
			ENG_DELETE(m_ResCustomMotions[i]);
		}
	}
	m_ResCustomMotions.clear();

	DESTORY_GAMEOBJECT_BY_COMPOENT(m_pCusMotionAct);
}

void CustomMotionMgr::OnLoadAyncTaskCompleted(CustomMotionBGLoadAsyncTask* task)
{
	if (m_IsInRelease) return;
	m_LoadAsyncTasks.remove(task);

	if (!task->m_bLoadingResult)
	{
		ENG_DELETE(task->m_pCustomMotion);
	}
	else
	{
		if (task->m_pCustomMotion)
		{
			this->addCusMotionToAct(task->m_pCustomMotion->getKey(), task->m_pCustomMotion);
		}
		this->onLoadCustomMotion(task->m_pCustomMotion, task->m_Type);
	}
}

void CustomMotionMgr::loadMapCustomMotion(long long owid, int realowneruin, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char dir[256];
	sprintf(dir, "%s/w%lld/custommotion/", rootpath.c_str(), owid);

	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	sprintf(path, "%s/w%lld/custommotion", rootpath.c_str(), owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = path;
		filepath += "/" + filename + ".cmt";

		CustomMotion* custommotion = ENG_NEW(CustomMotion)();

		//MINIW::BGThreadTask::addTask(ENG_NEW(CustomMotionBGLoadTask)(filepath, custommotion, realowneruin, MAP_LIB));
		CustomMotionBGLoadAsyncTask* task = ENG_NEW_LABEL(CustomMotionBGLoadAsyncTask, kMemGame);
		task->mgr = this;
		task->m_sFilePath = filepath;
		task->m_pCustomMotion = custommotion;
		task->m_CheckUin = realowneruin;
		task->m_Type = MAP_LIB;
		task->m_bLoadingResult = false;
		task->jobFence = GetBackStageJob().ScheduleJob(LoadCustomMotionAsync, task);

		m_LoadAsyncTasks.push_back(task);
	}
}

void CustomMotionMgr::loadResCustomMotion()
{
	initCusMotionAct();

	if (m_bResIsLoaded)
		return;

	m_bResIsLoaded = true;

	char dir[32];
	sprintf(dir, "data/custommotion/");
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	std::string folderName = "custommotion";
	char path[32];
	sprintf(path, "data/");
	std::string realpath(path);
	realpath.append(folderName);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(realpath.c_str(), 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string filename = it->c_str();
		std::string filepath = realpath;
		filepath += "/" + filename + ".cmt";

		CustomMotion* custommotion = ENG_NEW(CustomMotion)();
		//MINIW::BGThreadTask::addTask(ENG_NEW(CustomMotionBGLoadTask)(filepath, custommotion, -1, PUBLIC_LIB));

		CustomMotionBGLoadAsyncTask* task = ENG_NEW_LABEL(CustomMotionBGLoadAsyncTask, kMemGame);
		task->mgr = this;
		task->m_sFilePath = filepath;
		task->m_pCustomMotion = custommotion;
		task->m_CheckUin = -1;
		task->m_Type = PUBLIC_LIB;
		task->m_bLoadingResult = false;
		task->jobFence = GetBackStageJob().ScheduleJob(LoadCustomMotionAsync, task);

		m_LoadAsyncTasks.push_back(task);
	}
}

void CustomMotionMgr::loadOneResCustomMotion(std::string path, int checkuin, RES_LIB_TYPE type /* = PUBLIC_LIB */)
{
	CustomMotion* custommotion = ENG_NEW(CustomMotion)();
	//MINIW::BGThreadTask::addTask(ENG_NEW(CustomMotionBGLoadTask)(path, custommotion, checkuin, type));

	CustomMotionBGLoadAsyncTask* task = ENG_NEW_LABEL(CustomMotionBGLoadAsyncTask, kMemGame);
	task->mgr = this;
	task->m_sFilePath = path;
	task->m_pCustomMotion = custommotion;
	task->m_CheckUin = checkuin;
	task->m_Type = type;
	task->m_bLoadingResult = false;
	task->jobFence = GetBackStageJob().ScheduleJob(LoadCustomMotionAsync, task);

	m_LoadAsyncTasks.push_back(task);
}

void CustomMotionMgr::onSwitchAccountSucceed(int uin)
{
	for (size_t i = 0; i < m_ResCustomMotions.size(); i++)
	{
		if (m_ResCustomMotions[i] != NULL)
		{
			ENG_DELETE(m_ResCustomMotions[i]);
		}
	}
	m_ResCustomMotions.clear();

	m_bResIsLoaded = false;

	loadResCustomMotion();
}

void CustomMotionMgr::onLoadCustomMotion(CustomMotion *custommotion, RES_LIB_TYPE type /* = PUBLIC_LIB */)
{
	if (custommotion)
	{
		addCustomMotion(custommotion, type);

		/*char sMotionGroupId[64] = { 0 };
		MINIW::ScriptVM::game()->callFunction("GetNewMotionData", "ii>s", 1, (int)MAP_LIB, &sMotionGroupId);

		if (sMotionGroupId[0] == 0)
			return;

		MINIW::ScriptVM::game()->callFunction("CusModelAddMotionsData", "sisis", sMotionGroupId, 100100, custommotion->getKey().c_str(), 1, "");*/
	}
		
}

void CustomMotionMgr::leaveWorld()
{
	for (size_t i = 0; i < m_MapCustomMotions.size(); i++)
	{
		if (m_MapCustomMotions[i] != NULL)
		{
			ENG_DELETE(m_MapCustomMotions[i]);
		}
	}
	m_MapCustomMotions.clear();
}

void CustomMotionMgr::addCustomMotion(CustomMotion *custommotion, RES_LIB_TYPE type /* = PUBLIC_LIB */)
{
	if (!custommotion || getCustomMotion(custommotion->getKey())) return;
	if (type == MAP_LIB)
	{
		m_MapCustomMotions.push_back(custommotion);
	}
	else if (type == PUBLIC_LIB)
	{
		m_ResCustomMotions.push_back(custommotion);
	}
}

CustomMotion* CustomMotionMgr::getCustomMotion(std::string skey)
{
	for (size_t i = 0; i < m_MapCustomMotions.size(); i++)
	{
		if (m_MapCustomMotions[i]->getKey() == skey)
		{
			return m_MapCustomMotions[i];
		}
	}

	for (size_t i = 0; i < m_ResCustomMotions.size(); i++)
	{
		if (m_ResCustomMotions[i]->getKey() == skey)
		{
			return m_ResCustomMotions[i];
		}
	}

	return NULL;
}

bool CustomMotionMgr::isCusMotionBone(std::string bonename)
{
	for (size_t i = 0; i < m_CusMotionBoneNames.size(); i++)
	{
		if (bonename == m_CusMotionBoneNames[i])
			return true;
	}

	return false;
}

void CustomMotionMgr::initCusMotionAct()
{
	if(m_pCusMotionAct)
		return;

	char actlPath[256];	
	sprintf(actlPath, "entity/player/playerAR/bodyactions.omod");

	m_pCusMotionAct = g_BlockMtlMgr.getModel(actlPath);
	
	if(!m_pCusMotionAct)
		return;

	ModelLegacy* model = nullptr;
	if (m_pCusMotionAct && m_pCusMotionAct->IsKindOf<ModelLegacy>())
	{
		model = static_cast<ModelLegacy*>(m_pCusMotionAct);
		if (model == nullptr)
			return;
	}

	SharePtr<ModelData> modelData = model->GetModelData();
	if (!modelData)
		return;
	//ACTION_CUSTOM_ANIM_START_ID  当作一个标记 标识这个anim是ar动作
	const SharePtr<AnimationData>& animationData = modelData->findAnimationDataBySequenceId(ACTION_CUSTOM_ANIM_START_ID);
	if (animationData)
		return;

	SkeletonData* skeletonData = modelData->getSkeletonData();
	if (!skeletonData)
		return;

	SharePtr<AnimationData> anim = MakeSharePtr<Rainbow::AnimationData>();
	SequenceDesc desc;
	desc.id = ACTION_CUSTOM_ANIM_START_ID;
	desc.time_start = 0;
	desc.time_end = 0;
	desc.loopmode = AnimPlayMode::ANIE_MODE_EDIT;
	anim->m_Sequences.push_back(desc);

	vector<ModelAnimData> anims = modelData->m_Anims;
	size_t animNum = modelData->GetNumAnim();
	for (int i = 0; i < (int)animNum; i++)
	{
		if (!anims[i].anim)
			continue;

		int length = skeletonData->getNumBone();
		if (length <= 0)
			continue;

		for (int j = 0; j < length; ++j) 
		{
			BoneTrack *ptrack = BoneTrack::Create();
			ptrack->setBoneInfo(skeletonData->getIthBone(j)->m_Name, j);
			anim->addBoneTrack(ptrack);
		}
		break;
	}

	modelData->AddAnimation(anim);
	//anim->Release();
}

bool CustomMotionMgr::addCusMotionToAct(std::string motionID, CustomMotion* cusm)
{
	if(!m_pCusMotionAct)
		return false;

	if(motionID.length() <= 0)
		return false;

	if(!cusm)
	{
		cusm = getCustomMotion(motionID);
	}

	if(!cusm)
		return false;

	int cusMotionID = CreateCustomMotionID();
	addCusMotionIDFileMap(motionID, cusMotionID);
	ModelLegacy* model = nullptr;
	if (m_pCusMotionAct && m_pCusMotionAct->IsKindOf<ModelLegacy>())
	{
		model = static_cast<ModelLegacy*>(m_pCusMotionAct);
		if (model != nullptr)
		{
			cusm->setMotionData(model->GetModelData(), cusMotionID, ANIM_MODE_ONCE);
		}
	}

	return true;
}

void CustomMotionMgr::addCusMotionIDFileMap(std::string fileID, int motionID)
{
	m_mapCusMotionFileMapID[fileID] = motionID;
}

int CustomMotionMgr::findCusMotionID(std::string fileID)
{
	if(fileID.size() <= 0)
		return 0;
	
	std::map<std::string, int>::iterator iter = m_mapCusMotionFileMapID.find(fileID);
	if (iter != m_mapCusMotionFileMapID.end())
		return iter->second;

	return 0;
}

void CustomMotionMgr::addAndSaveCustoMotionByAR(CustomMotion *custommotion, int duration, int motionid, std::string motionname/* ="" */)
{
	if (!custommotion)
		return;

	char sMotionGroupId[64] = { 0 };
	MINIW::ScriptVM::game()->callFunction("GetNewMotionData", "ii>s", 1, (int)PUBLIC_LIB, &sMotionGroupId);  //1 ��ԴAR¼��

	if (sMotionGroupId[0] == 0)
		return;

	char skey[32] = { 0 };
#if defined(_WIN32)
	sprintf(skey, "%d%I64d", GetClientInfoProxy()->getUin(), time(NULL));
#else
	sprintf(skey, "%d%ld", GetClientInfoProxy()->getUin(), time(NULL));
#endif
	custommotion->setMotionInfo(skey, GetClientInfoProxy()->getUin(), GetClientInfoProxy()->getNickName(), duration, motionid);

	char path[64] = { 0 };
	sprintf(path, "data/custommotion/%s.cmt", skey);

	custommotion->save(path);

	m_ResCustomMotions.push_back(custommotion);

	MINIW::ScriptVM::game()->callFunction("CusModelAddMotionsData", "sisis", sMotionGroupId, motionid, skey, 1, motionname.c_str());

	addCusMotionToAct(custommotion->getKey(), custommotion);

	//文本上报
	if (!motionname.empty())
	{
		GetWorldStringManagerProxy()->insert(sMotionGroupId, motionname, SAVEFILETYPE::CUSTOM_MOTION);
		//SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldStringManager_insert", SandboxContext(nullptr)
		//	.SetData_Number("type", 24)
		//	.SetData_String("content", motionname)
		//	.SetData_String("key", sMotionGroupId));
	}
}

bool CustomMotionMgr::reEncryptCustomMotion(long long owid, int olduin, int newuin)
{
	char dir[256];
	sprintf(dir, "data/w%lld/custommotion/", owid);
	if (!gFunc_isStdioDirExist(dir)) { return true; }

	char path[256];
	sprintf(path, "data/w%lld/custommotion", owid);

	OneLevelScaner scaner;
	scaner.setRoot(GetFileManager().GetWritePathRoot());
	scaner.scanTree(path, 1);

	for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
	{
		std::string fullname = std::string(path) + "/" + *it + ".cmt";

		int buflen = 0;
		void* buf = ReadWholeFile(fullname.c_str(), buflen);
		if (buf == NULL)
			continue;

		flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);
		if (!FBSave::VerifyCustomMotionBuffer(verifier))
		{
			free(buf);
			continue;
		}

		const FBSave::CustomMotion *custommotion = FBSave::GetCustomMotion(buf);
		if (!custommotion || custommotion->authuin() != olduin || custommotion->authuin() == newuin)
		{
			free(buf);
			continue;
		}

		flatbuffers::FlatBufferBuilder builder;
		std::vector<flatbuffers::Offset<FBSave::OneBoneCustomMotion>> onebonemotions;
		onebonemotions.clear();

		auto bonemotions = custommotion->bonemotions();
		if (bonemotions)
		{
			for (unsigned int i = 0; i < bonemotions->size(); i++)
			{
				auto fbsObcm = bonemotions->Get(i);
				if (!fbsObcm) { continue; }

				auto fbsvKfds = fbsObcm->keyframes();
				std::vector<flatbuffers::Offset<FBSave::KeyFrameData>> vFbsKfds;
				vFbsKfds.clear();
				if (fbsvKfds)
				{
					for (unsigned int j = 0; j < fbsvKfds->size(); j++)
					{
						auto fbsKfd = fbsvKfds->Get(j);
						if (!fbsKfd) 
						{ 
							continue; 
						}
						vFbsKfds.push_back(FBSave::CreateKeyFrameData(builder, 
							fbsKfd->idx(), fbsKfd->offsetpos(), fbsKfd->yaw(), fbsKfd->pitch(), fbsKfd->roll(), 
							fbsKfd->scale(), fbsKfd->scale3()
						));
					}
				}

				onebonemotions.push_back(FBSave::CreateOneBoneCustomMotion(builder, builder.CreateString(fbsObcm->bonename()), fbsvKfds ? builder.CreateVector(vFbsKfds) : NULL));
			}
		}

		unsigned long t = (unsigned long)time(NULL);
		auto custommotionfb = FBSave::CreateCustomMotion(builder, builder.CreateString(custommotion->name()), builder.CreateString(custommotion->authorname()),
			newuin, custommotion->motionid(), t, custommotion->duration(), builder.CreateString(custommotion->key()), builder.CreateVector(onebonemotions));
		builder.Finish(custommotionfb);

		free(buf);
		if (!GetFileManager().SaveToWritePath(fullname.c_str(), builder.GetBufferPointer(), builder.GetSize())) { return false; }
	}

	return true;
}