
#include "container_buildblueprint.h"
#include <vector>
#include "CoreCommonDef.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "BlockScene.h"
#include "SectionMesh.h"
#include "GameNetManager.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "MpActorManager.h"
#include "WorldManager.h"
#include "IPlayerControl.h"
#include "section.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "BlockMesh.h"
#include "ClientActorHelper.h"
#include "SandboxIdDef.h"
#include "gamemode/GameModeDef.h"
#include "ClientActorDef.h"
#include "PlayManagerInterface.h"

using namespace MINIW;
using namespace MNSandbox;

ContainerBuildBluePrint::ContainerBuildBluePrint() : WorldContainer(WCoord(0, 0, 0), BUILDBLUEPRINT_START_INDEX), m_FileName(""), m_Dim(WCoord(0, 0, 0)), m_buildRate(-1), m_buildIndex(FRIST_BUILD), m_buildTime(0)
	,m_BluePrint(NULL), m_StartPos(WCoord(0, -1, 0)), m_MaterialEnough(false), m_RotateType(ROTATE_0), m_bNeedDel(false), m_bUpdateHomeLandBackpack(false), m_bIsLoad(false)
{
	for (int i = 0; i < BUILDBLUEPRINT_MAX; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
	
}

ContainerBuildBluePrint::ContainerBuildBluePrint(const WCoord &blockpos) : WorldContainer(blockpos, BUILDBLUEPRINT_START_INDEX), m_FileName(""), m_Dim(WCoord(0, 0, 0)), m_buildRate(-1), m_buildIndex(FRIST_BUILD), m_buildTime(0)
	, m_BluePrint(NULL), m_StartPos(WCoord(0, -1, 0)), m_MaterialEnough(false), m_RotateType(ROTATE_0), m_bNeedDel(false), m_bUpdateHomeLandBackpack(false), m_bIsLoad(false)
{
	for (int i = 0; i < BUILDBLUEPRINT_MAX; i++)
	{
		m_Grids[i].reset(m_BaseIndex + i);
	}
}

ContainerBuildBluePrint::~ContainerBuildBluePrint()
{

	clearPreBlocksMesh();
	//for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
	//{
	//	m_SectionMeshs[i]->DetachFromScene();
	//	ENG_DELETE(m_SectionMeshs[i])
	//}
	//m_SectionMeshs.clear();
	m_PreBlocks.clear();
}

int ContainerBuildBluePrint::getObjType() const
{
	return OBJ_TYPE_BUILDBLUEPRINT;
}

flatbuffers::Offset<FBSave::ChunkContainer> ContainerBuildBluePrint::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveContainerCommon(builder);

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	flatbuffers::Offset<FBSave::ItemGrid> items[NUMGRIDS];
	int count = 0;
	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (!m_Grids[i].isEmpty())
		{
			items[count] = m_Grids[i].save(builder);
			count++;
		}
	}

	auto dim = WCoordToCoord3(m_Dim);
	auto start = WCoordToCoord3(m_StartPos);

	auto actor = FBSave::CreateContainerBuildBluePrint(builder, basedata, builder.CreateVector(items, count), builder.CreateString(m_DataStr), &dim, m_buildIndex, &start, m_RotateType, m_buildRate);
	return FBSave::CreateChunkContainer(builder, FBSave::ContainerUnion_ContainerBuildBluePrint, actor.Union());
	return true;
}

static void getBluePrintFileName(std::string datastr, std::string &filename)
{
	jsonxx::Object jsonData;
	if (jsonData.parse(datastr))
	{
		if (jsonData.has<jsonxx::String>("filename"))
			filename = jsonData.get<jsonxx::String>("filename");
	}
}

bool ContainerBuildBluePrint::load(const void *srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerBuildBluePrint *>(srcdata);
	loadContainerCommon(src->basedata());

	for (size_t i = 0; i < src->items()->size(); i++)
	{
		m_Grids[i].load(src->items()->Get(i));
	}

	if(src->dim())
		m_Dim = Coord3ToWCoord(src->dim());
	
	if (src->buildindex())
		m_buildIndex = src->buildindex();

	if (src->startpos())
		m_StartPos = Coord3ToWCoord(src->startpos());

	if (src->filename())
	{
		m_FileName = src->filename()->c_str();
		getBluePrintFileName(src->filename()->c_str(), m_FileName);
		m_DataStr = src->filename()->c_str();
		//m_BluePrint =  GetWorldManagerPtr()->getBluePrint(m_FileName);
		if (g_WorldMgr)
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_getBluePrint",
				SandboxContext(nullptr)
				.SetData_String("fileName", m_FileName)
				.SetData_Number("worldId", g_WorldMgr->getWorldId()));
			if (result.IsExecSuccessed())
			{
				m_BluePrint = result.GetData_UserObject<BluePrint*>("bluePrint");
			}
		}
		else
		{
			m_BluePrint = NULL;
		}

		if (!m_MaterialEnough && (m_buildIndex > 0 || m_buildIndex == FINISH_BUILD || checkMaterialEnough() || GetWorldManagerPtr()->isGodMode()))
			m_MaterialEnough = true;
	}
		
	if (src->rotatetype())
		m_RotateType = src->rotatetype();

	if (src->buildrate())
		m_buildRate = src->buildrate();

	if(m_buildIndex > 0) //开始建造了
		m_bUpdateHomeLandBackpack = true;

	m_bIsLoad = true;
	
	return true;
}

void ContainerBuildBluePrint::onAttachUI()
{
	m_AttachToUI = true;

	for (int i = 0; i < BUILDBLUEPRINT_MAX; i++)
	{
		//ge GetGameEventQue().postBackpackChange(BLUEPRINT_START_INDEX + i);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", BLUEPRINT_START_INDEX + i);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}
}

void ContainerBuildBluePrint::onDetachUI()
{
	m_AttachToUI = false;
}

void ContainerBuildBluePrint::updateTick()
{
	if (m_bNeedDel && m_World)
	{
		m_World->getContainerMgr()->destroyContainer(m_BlockPos);
		return;
	}

	if (m_World->isRemoteMode())
		return;

	//家园
	if (HOME_GARDEN_WORLD == m_World->getMapSpecialType())
	{
		//	玩法模式不能建造蓝图
		if(GetWorldManagerPtr() && GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER_RUN && GetWorldManagerPtr()->getGameMode() == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN)
			return;

		//没扫完 chunk 检测完数据 不能开始建造
		bool bTranverseChunkEnd = false;
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_TranverseChunkEnd", SandboxContext(nullptr));
		bTranverseChunkEnd = result.IsSuccessed();

		if (!bTranverseChunkEnd && m_bIsLoad)
			return;
	}

	if (m_buildRate > 0 && m_buildIndex >= 0 && m_StartPos.y >= 0 && m_MaterialEnough)// && m_MaterialEnough
	{
		if(m_BlockMeshs.size() > 0)
			clearPreBlocksMesh();

		if (m_buildTime % m_buildRate == 0)
		{
			if (m_BluePrint == NULL)
			{
				//m_BluePrint = GetWorldManagerPtr()->getBluePrint(m_FileName);
				if (g_WorldMgr)
				{
					SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_getBluePrint",
						SandboxContext(nullptr)
						.SetData_String("fileName", m_FileName)
						.SetData_Number("worldId", g_WorldMgr->getWorldId()));
					if (result.IsExecSuccessed())
					{
						m_BluePrint = result.GetData_UserObject<BluePrint*>("bluePrint");
					}
				}
			}

			if (m_BluePrint == NULL)
				return;

			if ( m_buildIndex == 0 )
				m_BluePrint->LoadContainerToCache(m_RotateType);

			m_buildIndex = m_BluePrint->buildBluePrintOneBlock(m_World, m_StartPos, m_buildIndex, this, m_RotateType);
			if (m_buildIndex == FINISH_BUILD)
			{
				if(HOME_GARDEN_WORLD == m_World->getMapSpecialType()) //家园蓝图建造完毕后把蓝图建造台清掉
					m_World->setBlockAll(m_BlockPos, 0, 0);

				markRelativeBlocksForUpdate();

				m_BluePrint->ClearContainerCache();
			}
				
		}

		m_buildTime++;
	}
}

void ContainerBuildBluePrint::leaveWorld()
{
	WorldContainer::leaveWorld();
}

void ContainerBuildBluePrint::updateDisplay(float dtime)
{
#ifdef DEDICATED_SERVER
	return;
#endif

	if (m_buildIndex == FINISH_BUILD)
		return;

	int id = getRenderIdByHomeLand();
	
	if (id < 0)
	{
		if (m_buildIndex > 0 && m_buildRate > 0)
			id = 9;
		else
			id = 8;
	}

	if (id == 0)
		return;

	float w = BLOCK_SIZE / 4; //宽度1/4个格子
	WCoord origin = BlockCenterCoord(m_StartPos);
	WCoord dim = m_Dim*BLOCK_SIZE;
	WCoord startPos = WCoord(0, 0, 0);

	std::vector<BlockGeomVert>verts1;
	std::vector<BlockGeomVert>verts2;
	std::vector<BlockGeomVert>verts3;
	std::vector<BlockGeomVert>verts4;
	std::vector<unsigned short>indices;

	if (GetISandboxActorSubsystem()->RegionReplicator_GetVertXCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
	{
		//int dir = dim.x > 0 ? 1 : -1;
		//X1												
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//X2
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirY = dim.y > 0 ? 1 : -1;
			verts1[i].pos.y += dim.y;
			verts2[i].pos.y += dim.y;
			verts3[i].pos.y += dim.y;
			verts4[i].pos.y += dim.y;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//X3
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirZ = dim.z > 0 ? 1 : -1;
			verts1[i].pos.z += dim.z;
			verts2[i].pos.z += dim.z;
			verts3[i].pos.z += dim.z;
			verts4[i].pos.z += dim.z;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//X4
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirY = dim.y > 0 ? 1 : -1;
			verts1[i].pos.y -= dim.y;
			verts2[i].pos.y -= dim.y;
			verts3[i].pos.y -= dim.y;
			verts4[i].pos.y -= dim.y;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
	}

	verts1.clear();
	verts2.clear();
	verts3.clear();
	verts4.clear();
	indices.clear();
	if (GetISandboxActorSubsystem()->RegionReplicator_GetVertZCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
	{
		int dir = dim.z > 0 ? 1 : -1;
		//Z1
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Z2
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirY = dim.y > 0 ? 1 : -1;
			verts1[i].pos.y += dim.y;
			verts2[i].pos.y += dim.y;
			verts3[i].pos.y += dim.y;
			verts4[i].pos.y += dim.y;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Z3
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirX = dim.x > 0 ? 1 : -1;
			verts1[i].pos.x += dim.x;
			verts2[i].pos.x += dim.x;
			verts3[i].pos.x += (short)(dim.x + dirX*w);
			verts4[i].pos.x += (short)(dim.x - dirX*w);
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Z4
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirY = dim.y > 0 ? 1 : -1;
			verts1[i].pos.y -= dim.y;
			verts2[i].pos.y -= dim.y;
			verts3[i].pos.y -= dim.y;
			verts4[i].pos.y -= dim.y;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
	}

	verts1.clear();
	verts2.clear();
	verts3.clear();
	verts4.clear();
	indices.clear();
	if (GetISandboxActorSubsystem()->RegionReplicator_GetVertYCuboidArray(verts1, verts2, verts3, verts4, indices, startPos, dim))
	{
		int dir = dim.y > 0 ? 1 : -1;
		//Y1
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Y2
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirX = dim.x > 0 ? 1 : -1;
			verts1[i].pos.x += dim.x;
			verts2[i].pos.x += dim.x;
			verts3[i].pos.x += dim.x;
			verts4[i].pos.x += dim.x;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Y3
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirZ = dim.z > 0 ? 1 : -1;
			verts1[i].pos.z += dim.z;
			verts2[i].pos.z += dim.z;
			verts3[i].pos.z += dim.z;
			verts4[i].pos.z += dim.z;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);

		//Y4
		for (int i = 0; i < (int)verts1.size(); i++)
		{
			int dirX = dim.x > 0 ? 1 : -1;
			verts1[i].pos.x -= dim.x;
			verts2[i].pos.x -= dim.x;
			verts3[i].pos.x -= dim.x;
			verts4[i].pos.x -= dim.x;
		}
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts1, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts2, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts3, indices);
		m_World->getRender()->getCurveRender()->addRect(id, origin, verts4, indices);
	}
}

bool ContainerBuildBluePrint::canPutItem(int index)
{
	return true;
}

BackPackGrid* ContainerBuildBluePrint::index2Grid(int index)
{
	assert(index >= BUILDBLUEPRINT_START_INDEX);
	index -= BUILDBLUEPRINT_START_INDEX;
	
	if (index < BUILDBLUEPRINT_MAX) return &m_Grids[index];
	return NULL;
}

void ContainerBuildBluePrint::afterChangeGrid(int index)
{
	WorldContainer::afterChangeGrid(index);
	if (m_AttachToUI)
	{
		//ge GetGameEventQue().postBackpackChange(index);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("grid_index", index);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_BACKPACK_CHANGE", sandboxContext);
		}
	}

	if (!m_MaterialEnough &&(m_buildIndex > 0 || m_buildIndex == FINISH_BUILD || checkMaterialEnough() || GetWorldManagerPtr()->isGodMode()))
		m_MaterialEnough = true;

}

void ContainerBuildBluePrint::enterWorld(World *pworld)
{
	WorldContainer::enterWorld(pworld);
	registerUpdateTick();
	registerUpdateDisplay();

	auto blockdef = GetDefManagerProxy()->getBlockDef(pworld->getBlockID(m_BlockPos));
	if (!blockdef || blockdef->ID != BLOCK_BUILDBULEPRINT)
	{
		m_bNeedDel = true;
		return;
	}

	preBuildBluePrint();

	//蓝图未建完的方块，要通知家园背包扣除材料
	if (m_World->getMapSpecialType() == HOME_GARDEN_WORLD && m_bUpdateHomeLandBackpack)  
	{
		m_bUpdateHomeLandBackpack = false;
		if (m_BluePrint == NULL)
		{
			//m_BluePrint = GetWorldManagerPtr()->getBluePrint(m_FileName);
			if (g_WorldMgr)
			{
				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_getBluePrint",
					SandboxContext(nullptr)
					.SetData_String("fileName", m_FileName)
					.SetData_Number("worldId", g_WorldMgr->getWorldId()));
				if (result.IsExecSuccessed())
				{
					m_BluePrint = result.GetData_UserObject<BluePrint*>("bluePrint");
				}
			}
		}

		if (m_BluePrint)
			m_BluePrint->updateHomeLandBackpak(m_buildIndex, m_World);
	}
}

bool ContainerBuildBluePrint::checkMaterialEnough()
{
	if (m_BluePrint == NULL)
		return false;

	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	int num = m_BluePrint->getMaterialsNum();
	if (num > 0 && NUMGRIDS <= 0)
		return false;

	bool enough = true;
	for (int i = 0; i < num; i++)
	{
		if (m_Grids[i].isEmpty())
			enough = false;

		auto materialInfo = m_BluePrint->getMaterialInfo(i);
		int num = m_Grids[i].getNum();
		if (m_Grids[i].getNum() < materialInfo->num)
			enough = false;

		if(!enough)
			break;
	}

	return enough;
}

int ContainerBuildBluePrint::getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos)
{
	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);
	if (pItemInfos)
	{
		for (int i = 0; i < NUMGRIDS; i++)
		{
			if (m_Grids[i].isEmpty()) continue;
			GetISandboxActorSubsystem()->StoreGridData(pItemInfos->Add(), &m_Grids[i]);
			//storeGridData(pItemInfos->Add(), &m_Grids[i]);
		}

		return NUMGRIDS;
	}

	return 0;
}

void ContainerBuildBluePrint::markRelativeBlocksForUpdate()
{
	m_World->markBlockForUpdate(m_BlockPos);
}

void ContainerBuildBluePrint::setBuildPrintData(std::string datastr, int realItemId /*=0*/)
{
	if (m_BluePrint)
		return;

	m_FileName = "";
	jsonxx::Object jsonData;
	bool isNewJson = false; //是否生成了新的json
	if (jsonData.parse(datastr))
	{
		if (jsonData.has<jsonxx::String>("filename"))
			m_FileName = jsonData.get<jsonxx::String>("filename");

		if (!jsonData.has<jsonxx::String>("realitemid"))
		{
			jsonData << "realitemid" << realItemId;
			isNewJson = true;
		}
	}
	else
	{
		string temp("");
		int index = 0;
		for (size_t i = 0; i < datastr.size(); i++)
		{
			if (datastr[i] == '|')
			{
				if (index == 4) //文件名
				{
					m_FileName = temp;
					break;
				}
				temp = "";
				index++;
			}
			else
				temp += datastr[i];
		}
	}
	

	//m_BluePrint = GetWorldManagerPtr()->getBluePrint(m_FileName);
	if (g_WorldMgr)
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_getBluePrint",
			SandboxContext(nullptr)
			.SetData_String("fileName", m_FileName)
			.SetData_Number("worldId", g_WorldMgr->getWorldId()));
		if (result.IsExecSuccessed())
		{
			m_BluePrint = result.GetData_UserObject<BluePrint*>("bluePrint");
		}
	}
	else
	{
		m_BluePrint = NULL;
	}

	if (m_BluePrint)
	{
		if (isNewJson)
			m_DataStr = jsonData.json();
		else
			m_DataStr = datastr;

		m_Dim = m_BluePrint->getDim();
		if (m_Dim.x >= 0)
			m_Dim.x--;
		else
			m_Dim.x++;

		if (m_Dim.y >= 0)
			m_Dim.y--;
		else
			m_Dim.y++;

		if (m_Dim.z >= 0)
			m_Dim.z--;
		else
			m_Dim.z++;

		if (!m_MaterialEnough && checkMaterialEnough())
			m_MaterialEnough = true;

		//notifyChange2Openers(-1, true, m_FileName);

		initGridByMaterial();
	}
}

void ContainerBuildBluePrint::initGridByMaterial()
{
	if (m_BluePrint)
	{
		int num = m_BluePrint->getMaterialsNum();
		int gridNum = getGridNum();//防止数组越界
		for (int i = 0; i < num && i<gridNum; i++)
		{
			auto materialInfo = m_BluePrint->getMaterialInfo(i);
			if(materialInfo == NULL) continue;
			int hasNum = 0;
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
				hasNum = materialInfo->num;

			m_Grids[i].setItem(materialInfo->itemid, hasNum, materialInfo->durable, -1, (void *)materialInfo->num);
			m_Grids[i].setEnchants(materialInfo->enchantnum, materialInfo->enchants);
			m_Grids[i].getRuneData().setdata(materialInfo->runedata);
			afterChangeGrid(m_Grids[i].getIndex());
			//notifyChange2Openers(m_Grids[i].getIndex(), true);
		}
	}
}

void ContainerBuildBluePrint::changeDim(int dirtype)
{
	if (m_StartPos.y == -1)
		m_StartPos = m_BlockPos;

	if (dirtype == NEG_X)
	{
		m_Dim.x = Rainbow::Abs(m_Dim.x);
		m_Dim.y = Rainbow::Abs(m_Dim.y);
		m_Dim.z = Rainbow::Abs(m_Dim.z);
		m_StartPos.x += 1;
		m_RotateType = ROTATE_0;
	}
	else if (dirtype == POS_X)
	{
		m_Dim.x = -Rainbow::Abs(m_Dim.x);
		m_Dim.y = Rainbow::Abs(m_Dim.y);
		m_Dim.z = -Rainbow::Abs(m_Dim.z);
		m_StartPos.x -= 1;
		m_RotateType = ROTATE_180;
	}
	else if (dirtype == NEG_Z)
	{
		int temp = m_Dim.x;
		m_Dim.x = -Rainbow::Abs(m_Dim.z);
		m_Dim.y = Rainbow::Abs(m_Dim.y);
		m_Dim.z = Rainbow::Abs(temp);
		m_StartPos.z += 1;
		m_RotateType = ROTATE_90;
	}
	else if (dirtype == POS_Z)
	{
		int temp = m_Dim.x;
		m_Dim.x = Rainbow::Abs(m_Dim.z);
		m_Dim.y = Rainbow::Abs(m_Dim.y);
		m_Dim.z = -Rainbow::Abs(temp);
		m_StartPos.z -= 1;
		m_RotateType = ROTATE_270;
	}
	else if (dirtype == NEG_Y)
	{
		m_Dim.x = Rainbow::Abs(m_Dim.x);
		m_Dim.y = -Rainbow::Abs(m_Dim.y);
		m_Dim.z = Rainbow::Abs(m_Dim.z);

		m_StartPos.y += 1;
	}
	else if (dirtype == POS_Y)
	{
		m_Dim.x = Rainbow::Abs(m_Dim.x);
		m_Dim.y = Rainbow::Abs(m_Dim.y);
		m_Dim.z = Rainbow::Abs(m_Dim.z);

		m_StartPos.y -= 1;
	}
	else if (dirtype == MIRROR_X)
	{
		m_Dim.x = -m_Dim.x;
		m_Dim.y = m_Dim.y;
		m_Dim.z = m_Dim.z;

		int oldType = m_RotateType;
		if (m_Dim.x >= 0)
		{
			m_StartPos.x += 2;
			m_RotateType = MIRROR_180;
		}
		else
		{
			m_StartPos.x -= 2;
			m_RotateType = MIRROR_0;
		}
		
		if (oldType == MIRROR_0)
			m_RotateType = ROTATE_0;
		else if (oldType == MIRROR_180)
			m_RotateType = ROTATE_180;
	}
	else if (dirtype == MIRROR_Z)
	{
		m_Dim.x = m_Dim.x; 
		m_Dim.y = m_Dim.y;
		m_Dim.z = -m_Dim.z;

		int oldType = m_RotateType;
		if (m_Dim.z >= 0)
		{
			m_StartPos.z += 2;
			m_RotateType = MIRROR_270;
		}
		else
		{
			m_StartPos.z -= 2;
			m_RotateType = MIRROR_90;
		}

		if (oldType == MIRROR_90)
			m_RotateType = ROTATE_90;
		else if(oldType == MIRROR_270)
			m_RotateType = ROTATE_270;
	}
	else if (dirtype == MIRROR_Y)
	{
		m_Dim.x = m_Dim.x;
		m_Dim.y = -m_Dim.y;
		m_Dim.z = m_Dim.z;

		if (m_Dim.y >= 0)
			m_StartPos.y += 2;
		else
			m_StartPos.y -= 2;
	}

	preBuildBluePrint();
}

WCoord ContainerBuildBluePrint::getDim()
{
	return m_Dim;
}

WCoord ContainerBuildBluePrint::getStartPos()
{
	return m_StartPos;
}

void ContainerBuildBluePrint::setRateByPower(int power)
{
	if (power > 0)
	{
		if (FRIST_BUILD == m_buildIndex)
		{
			m_buildIndex = 0;
			/*if (g_pPlayerCtrl)
			{
				g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30013, "", g_pPlayerCtrl->getCurWorldType());
			}*/
		}
			
		m_buildRate = 16 - power;
	}
		
	else
	{
		//if (m_buildIndex >= 0)
		//	m_buildIndex = -1;
		m_buildRate = -1;
	}
	
	markRelativeBlocksForUpdate();
}

void ContainerBuildBluePrint::removeItem(int itemid, int num)
{
	const int NUMGRIDS = sizeof(m_Grids) / sizeof(m_Grids[0]);

	for (int i = 0; i < NUMGRIDS; i++)
	{
		if (m_Grids[i].isEmpty())
			continue;

		if (m_Grids[i].getItemID() == itemid)
		{
			if (num >= m_Grids[i].getNum())
			{
				num -= m_Grids[i].getNum();
				m_Grids[i].clear();
				afterChangeGrid(m_Grids[i].getIndex());
			}
			else
			{
				m_Grids[i].addNum(-num);
				afterChangeGrid(m_Grids[i].getIndex());
				return;
			}
		}
	}
}

void ContainerBuildBluePrint::dropItems()
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
		return;
	for (int i = 0; i < BUILDBLUEPRINT_MAX; i++)
	{
		if(m_Grids[i].isEmpty() || m_Grids[i].getNum() <= 0)
			continue;

		dropOneItem(m_Grids[i]);
	}
}

BackPackGrid *ContainerBuildBluePrint::getReplaceGrid(int itemid)
{
	for (int i = 0; i < BUILDBLUEPRINT_MAX; i++)
	{
		if (m_Grids[i].isEmpty() || m_Grids[i].getNum() <= 0)
			continue;

		if (m_Grids[i].getItemID() == itemid)
		{
			return &m_Grids[i];
		}
	}

	return NULL;
}

void ContainerBuildBluePrint::clearPreBlocksMesh()
{

	for (size_t i = 0; i < m_BlockMeshs.size(); i++)
	{
		BlockMesh* blockMesh = m_BlockMeshs[i];
		DESTORY_GAMEOBJECT_BY_COMPOENT(blockMesh);
	}
	m_BlockMeshs.clear();
	//for (int i = 0; i < (int)m_SectionMeshs.size(); i++)
	//{
	//	m_SectionMeshs[i]->DetachFromScene();
	//	ENG_DELETE(m_SectionMeshs[i])
	//}

	//m_SectionMeshs.clear();
	m_PreBlocks.clear();
}

void ContainerBuildBluePrint::preBuildBluePrint()
{
	if (m_buildIndex < 0 && m_buildIndex != FINISH_BUILD)
	{
		if (!m_World->isRemoteMode() && m_BluePrint && m_StartPos.y >= 0)
		{
			clearPreBlocksMesh();
			m_BluePrint->preBuildBluePrint(m_World, m_StartPos, m_PreBlocks, m_RotateType);
			onAttatchMeshByScene(m_PreBlocks);
		}
		else
		{
			PB_BluePrintPreBlockCH bluePrintPreBlockCH;
			PB_Vector3* pos = bluePrintPreBlockCH.mutable_blockpos();
			pos->set_x(m_BlockPos.x);
			pos->set_y(m_BlockPos.y);
			pos->set_z(m_BlockPos.z);
			GetGameNetManagerPtr()->sendToHost(PB_BLUEPRINT_PREBLOCK_CH, bluePrintPreBlockCH);
		}
	}
}

void ContainerBuildBluePrint::onAttatchMeshByScene(std::vector<PreBlocksData> &preblocks)
{
	std::map<Section *, std::vector<PreBlocksData>> sectionPreBlocks;
	for (int i = 0; i < (int)preblocks.size(); i++)
	{
		Section *section = m_World->getSection(preblocks[i].pos);
		if (section)
		{
			std::map<Section *, std::vector<PreBlocksData>>::iterator iter = sectionPreBlocks.find(section);
			PreBlocksData data;
			data.pos = preblocks[i].pos;
			data.blockid = preblocks[i].blockid;
			data.specialblockcolor = preblocks[i].specialblockcolor;

			if (sectionPreBlocks.end() != iter)
				iter->second.push_back(data);
			else
				sectionPreBlocks[section].push_back(data);
		}
	}

	std::map<Section *, std::vector<PreBlocksData>>::iterator iter = sectionPreBlocks.begin();
	std::map<WCoord, int> specialBlockColorMap;
	for (; iter != sectionPreBlocks.end(); ++iter)
	{
		Chunk *chunk = m_World->getChunk(iter->second[0].pos);
		if(!chunk)
			m_World->syncLoadChunk(iter->second[0].pos, 1);
			//continue;

		Section *section = ENG_NEW(Section)(chunk, 0);
		section->m_Origin = iter->first->m_Origin;
		section->initialize();
		//section->allocBlocks();

		specialBlockColorMap.clear();
		for (int i = 0; i < (int)iter->second.size(); i++)
		{
			WCoord pos = iter->second[i].pos - section->m_Origin;
			section->setBlock(pos.x, pos.y, pos.z, iter->second[i].blockid, 0);
			if (iter->second[i].specialblockcolor != 0)
				specialBlockColorMap.insert(std::make_pair(pos, iter->second[i].specialblockcolor));
		}

		BlockMesh *blockMesh = section->createBluePrintPreMesh(specialBlockColorMap);
		if (blockMesh)
			m_BlockMeshs.push_back(blockMesh);
			

		ENG_DELETE(section);
	}

	if (m_World && m_World->getRenderer())
	{
		BlockScene *scene = m_World->getRenderer()->getScene();
		if (scene)
		{
			for (size_t i = 0; i < m_BlockMeshs.size(); i++)
			{
				m_BlockMeshs[i]->AttachToScene(scene);
				//m_SectionMeshs[i]->AttachToScene(scene);
			}
		}
	}
}

void ContainerBuildBluePrint::syncPreBlocks(int uin)
{
	if (!m_World)
		return;

	if (m_PreBlocks.size() <= 0)
		return;

	PB_BluePrintPreBlockHC bluePrintPreBlockHC;
	PB_Vector3* pos = bluePrintPreBlockHC.mutable_blockpos();
	pos->set_x(m_BlockPos.x);
	pos->set_y(m_BlockPos.y);
	pos->set_z(m_BlockPos.z);

	bluePrintPreBlockHC.set_mapid(m_World->getCurMapID());

	RepeatedPtrField<PB_PreBlockData>* pdatas = bluePrintPreBlockHC.mutable_preblocks();
	if (pdatas)
	{
		for (int i = 0; i < (int)m_PreBlocks.size(); i++)
		{
			PB_PreBlockData *pPreBlock = pdatas->Add();

			pPreBlock->set_id(Block::toBlockOriginData(m_PreBlocks[i].blockid));
			pPreBlock->set_exid(Block::toBlockDataEx(m_PreBlocks[i].blockid));
			PB_Vector3* pos = pPreBlock->mutable_blockpos();
			pos->set_x(m_PreBlocks[i].pos.x);
			pos->set_y(m_PreBlocks[i].pos.y);
			pos->set_z(m_PreBlocks[i].pos.z);
			pPreBlock->set_specialblockcolor(m_PreBlocks[i].specialblockcolor);
		}
	}
	GetGameNetManagerPtr()->sendToClient(uin, PB_BLUEPRINT_PREBLOCK_HC, bluePrintPreBlockHC);
}

void ContainerBuildBluePrint::attatchMeshByClient(const RepeatedPtrField<PB_PreBlockData> *pdatas)
{
	clearPreBlocksMesh();

	for (int i = 0; i < pdatas->size(); i++)
	{
		PreBlocksData data;
		const PB_PreBlockData &src = pdatas->Get(i);
		data.blockid = Block::toMakeBlockIDWithEx(src.id(), src.exid());
		data.pos = MPVEC2WCoord(src.blockpos());
		data.specialblockcolor = src.specialblockcolor();

		m_PreBlocks.push_back(data);
	}

	onAttatchMeshByScene(m_PreBlocks);
}

bool ContainerBuildBluePrint::outsideWorkArea()
{
	if (HOME_GARDEN_WORLD == m_World->getMapSpecialType()) //家园
	{
		WCoord endPos = m_StartPos + m_Dim;
		int homelandlevel = 1;
		//1--HOMELAND_REGION_TYPE_SELF_BUILD 自建区域
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_getRegionConfig", SandboxContext(nullptr).SetData_Number("type",1));

		if (result.IsSuccessed())
		{
			homelandlevel = result.GetData_Number("value");
		}

		if (homelandlevel == 1)
		{
			if (endPos.x < -16 || endPos.x > 32 || endPos.z < -16 || endPos.z > 16)
				return true;
		}
		else if (homelandlevel == 2)
		{
			if (endPos.x < -16 || endPos.x > 32 || endPos.z < -16 || endPos.z > 32)
				return true;
		}
		else if (homelandlevel == 3)
		{
			if (endPos.x < -32 || endPos.x > 48 || endPos.z < -16 || endPos.z > 32)
				return true;
		}
		else if (homelandlevel == 4)
		{
			if (endPos.x < -32 || endPos.x > 48 || endPos.z < -16 || endPos.z > 64)
				return true;
		}
	}

	return false;
}

int ContainerBuildBluePrint::getRenderIdByHomeLand()
{
	//超出自建区域，建造线框显示为红色
	if (outsideWorkArea())
		return 10;

	return -1;
}