/**********************************************
*	FUNC:	行动者事件管理器
*	FILE:	ObjectEventManager
*	BY:		chenzh
*	TIME:	2020-7-7
*/
#include "ObjectEventManager.h"
#include "OgreScriptLuaVM.h"
#include "File/FileManager.h"
#include "File/DirVisitor.h"

#include <assert.h>
#include "Common/OgreShared.h"


//--------------------------------------------------------
ObjectEventManager::ObjectEventManager()
	: m_LuaVM(NULL)
{
	m_LuaVM = MINIW::ScriptVM::game();

	if (!Init())
	{
		LOG_INFO("AE ERROR! manager init failed!");
		assert(false);
	}
}

ObjectEventManager::~ObjectEventManager()
{
	Release();
}

bool ObjectEventManager::Init()
{
	m_LuaVM->callFunction("CreateMobEventManager", "");
	return true;
}

void ObjectEventManager::Release()
{
	m_LuaVM->callFunction("DestroyMobEventManager", "");
}
