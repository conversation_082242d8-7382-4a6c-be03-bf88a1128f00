# 方块放置协议 - PB_BLOCK_INTERACT

## 协议概述

PB_BLOCK_INTERACT 协议是沙盒游戏中处理方块放置操作的核心网络协议。该协议采用双向通讯机制：
- **PB_BLOCK_INTERACT_CH**: 客户端发送给服务器
- **PB_BLOCK_INTERACT_HC**: 服务器发送给客户端

## 协议定义

### PB_BlockInteractCH (客户端→服务器)
```protobuf
message PB_BlockInteractCH
{
    optional int32 face = 1;                    // 放置面方向
    optional int32 colptx = 2;                  // 碰撞点X坐标(×100)
    optional int32 colpty = 3;                  // 碰撞点Y坐标(×100)
    optional int32 colptz = 4;                  // 碰撞点Z坐标(×100)
    optional game.common.PB_Vector3 blockpos = 5;  // 目标方块位置
    optional int32 BlueprintId = 6;             // 蓝图ID(可选)
    optional int32 BlueprintPlaceDirIdx = 7;    // 蓝图放置方向索引
}
```

**协议定义位置**: `Source\MiniBase\Protocol\Tools\protobuf\proto_ch.proto:93-102`

### PB_BlockInteractHC (服务器→客户端)
```protobuf
message PB_BlockInteractHC
{
    optional uint64 objid = 1;                  // 玩家对象ID
    optional int32 face = 2;                    // 交互面方向
    optional game.common.PB_Vector3 blockpos = 3;  // 方块位置
}
```

## 协议流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant OtherClients as 其他客户端

    Note over Client: 玩家右键放置方块
    Client->>Client: MpPlayerControl::interactBlock()
    Client->>Client: 构造PB_BlockInteractCH协议
    Client->>Server: PB_BLOCK_INTERACT_CH
    
    Note over Server: 服务器处理
    Server->>Server: handleBlockInteract2Host()
    Server->>Server: 验证玩家状态
    Server->>Server: 解析协议数据
    Server->>Server: 坐标精度恢复(÷100.0f)
    Server->>Server: player->interactBlock()
    
    Note over Server: 广播给其他玩家
    Server->>Server: notifyInteractBlock2Tracking()
    Server->>OtherClients: PB_BLOCK_INTERACT_HC
    
    Note over OtherClients: 客户端同步
    OtherClients->>OtherClients: handleBlockInteract2Client()
    OtherClients->>OtherClients: 播放放置效果
```

## 详细实现流程

### 1. 客户端发送协议

**触发位置**: `Source\SandboxGame\Play\gameplay\mpgameplay\MpPlayerControl.cpp:825-831`

```cpp
bool MpPlayerControl::interactBlock(const WCoord &targetblock, DirectionType targetface, const Rainbow::Vector3f &colpoint)
{
    if (m_pWorld->isRemoteMode())
    {
        PB_BlockInteractCH blockInteractCH;
        blockInteractCH.set_face(targetface);
        blockInteractCH.set_colptx(char(colpoint.x * 100));
        blockInteractCH.set_colpty(char(colpoint.y * 100));
        blockInteractCH.set_colptz(char(colpoint.z * 100));

        PB_Vector3* blockPos = blockInteractCH.mutable_blockpos();
        blockPos->set_x(targetblock.x);
        blockPos->set_y(targetblock.y);
        blockPos->set_z(targetblock.z);

        GetGameNetManagerPtr()->sendToHost(PB_BLOCK_INTERACT_CH, blockInteractCH);
    }
    return PlayerControl::interactBlock(targetblock, targetface, colpoint);
}
```

**关键实现细节**:
- 碰撞点坐标精度处理：乘以100转换为整数传输
- 只在远程模式(`isRemoteMode()`)下发送协议
- 调用基类方法执行本地逻辑

### 2. 服务器处理协议

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveHostHandler.cpp:1487-1505`

```cpp
void MpGameSurviveNetHandler::handleBlockInteract2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
    if (player == NULL) return;

    PB_BlockInteractCH blockInteractCH;
    blockInteractCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    Rainbow::Vector3f vec(blockInteractCH.colptx() / 100.0f, 
                         blockInteractCH.colpty() / 100.0f, 
                         blockInteractCH.colptz() / 100.0f);
    WCoord blockpos = MPVEC2WCoord(blockInteractCH.blockpos());
    
    if (blockInteractCH.has_blueprintid())
    {
        if (auto attrib = player->getPlayerAttrib())
        {
            attrib->SetCurBuildingId(blockInteractCH.blueprintid());
            attrib->SetBlueprintPlaceDirIdx(blockInteractCH.blueprintplacediridx());
        }
    }
    
    player->interactBlock(blockpos, (DirectionType)blockInteractCH.face(), vec);
}
```

**处理步骤**:
1. 验证玩家状态 (`checkDownedPlayerByMsg2Host`)
2. 解析协议数据
3. 坐标精度恢复 (除以100.0f)
4. 处理蓝图信息（如果存在）
5. 调用玩家交互逻辑

### 3. 服务器响应广播

**响应函数**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:99-112`

```cpp
void ClientPlayer::notifyInteractBlock2Tracking(const WCoord &blockpos, DirectionType face)
{
    if (!m_pWorld->isRemoteMode())
    {
        PB_BlockInteractHC blockInteractHC;
        blockInteractHC.set_objid(getObjId());
        blockInteractHC.set_face(face);
        blockInteractHC.mutable_blockpos()->set_x(blockpos.x);
        blockInteractHC.mutable_blockpos()->set_y(blockpos.y);
        blockInteractHC.mutable_blockpos()->set_z(blockpos.z);

        m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_BLOCK_INTERACT_HC, blockInteractHC, this);
    }
}
```

**调用位置**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:2831`

### 4. 客户端接收响应

**处理函数**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSurviveClientHandlerDetail.cpp:3749-3758`

```cpp
void MpGameSurviveNetHandler::handleBlockInteract2Client(const PB_PACKDATA_CLIENT &pkg)
{
    PB_BlockInteractHC blockInteractHC;
    blockInteractHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

    ClientPlayer *player = dynamic_cast<ClientPlayer *>(objId2ActorOnClient(blockInteractHC.objid()));
    if (player == NULL) return;

    player->interactBlock(MPVEC2WCoord(blockInteractHC.blockpos()), 
                         (DirectionType)blockInteractHC.face(), 
                         Rainbow::Vector3f(0, 0, 0));
}
```

## 协议注册

**注册位置**: `Source\MiniGame\iworld\gameStage\net_handler\MpGameSuviveNetHandler.cpp`

```cpp
// 服务器端处理器注册 (在构造函数中)
REGIS_HOST_HANDLER(PB_BLOCK_INTERACT_CH, handleBlockInteract2Host);

// 客户端处理器注册 (行号480)
REGIS_CLIENT_HANDLER(PB_BLOCK_INTERACT_HC, handleBlockInteract2Client);
```

## 权限验证机制

在实际放置前，系统会进行权限验证：

**验证位置**: `Source\SandboxGame\Play\player\ClientPlayer_Interact.cpp:2335-2346`

```cpp
SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canInteractorBlock",
    SandboxContext(nullptr)
    .SetData_Number("uin", getUin())
    .SetData_Number("tool", curToolID)
    .SetData_Number("blockid", blockid)
    .SetData_Usertype<std::vector<CSPermitBitType>>("csPermitType",&csPermitType));
```

## 协议特点

1. **精度优化**: 碰撞点坐标通过×100/÷100处理，减少网络传输数据量
2. **蓝图支持**: 支持蓝图放置功能，包含蓝图ID和方向信息
3. **权限控制**: 完整的权限验证系统
4. **实时同步**: 通过广播机制实现多人实时同步
5. **状态验证**: 服务器端验证玩家状态，防止异常操作

## 相关协议

- **PB_BLOCK_PUNCH**: 方块挖掘协议
- **PB_BLOCK_EXPLOIT**: 特殊开采协议
- **PB_PLAYER_CLICK**: 玩家点击协议