#ifndef __Rectangle_h__
#define __Rectangle_h__ 1

#include "GeoSolid.h"
#include "Common/LazySingleton.h"

#include "BaseClass/SharePtr.h"
#include "Graphics/Mesh/Mesh.h"

namespace MNSandbox { 
	class SceneGeoSolid;
	namespace GeometrySolid {
		class EXPORT_SANDBOXENGINE Rectangle : public GeoSolid
		{
		public:
			Rectangle();
			~Rectangle();
			void InitGeoSolidMeshData() override;
			void CreateDynamic() override;
			void SeparateSurfaces() override;
			int GetSurfaceCount() override { return 1; }
			GeoSolidFace GetGeoSolidFace(int ism) override
			{
				return GeoSolidFace::TOP;
			}
			const char* GetName() const override { return "Plane"; }
			const char* GetSurfaceName(const SceneModelObject::Surface& sf) override;
			DECLARE_GET_SINGLETON(Rectangle)
		private:
			static const int COUNT_OF_SQUARE_IN_SIDE;
		};

	}
}

#endif//__Rectangle_h__