#ifndef __CompositeGeoSolid_h__
#define __CompositeGeoSolid_h__ 1

#include "GeoSolid.h"

#include "flatbuffers/flatbuffers.h"

namespace FBSave {
	struct GeoSolidSubMesh;
	struct GeoSolid;
}

namespace MNSandbox { 
	namespace GeometrySolid {
		class GeoSolidSubMesh {
		public:
			std::unordered_set<GsIv> setVertexIndices;
			GeoSolidSet<GsIt> setTriangleIndices;
			int cpv;
			int cpi;
			//ibb、ivo、im的位域大小受GeoSolidTriangle的变化
			int ibb;
			int ivc;
			int im;
			/**
			@brief  仅长方体、三棱柱和四棱锥使用。
					运行时数据。
			*/
			int ivn;
			/**
			@brief  运行时数据。
			*/
			int ic;
			GeoSolidShape eGss : 4;
			GeoSolidFace eGsf : 4;
			//explicit GeoSolidSubMesh(bool _);
		private:
			GeoSolidSubMesh();
		public:
			~GeoSolidSubMesh();
			void Set(const FBSave::GeoSolidSubMesh& fbsGssm);
			flatbuffers::Offset<FBSave::GeoSolidSubMesh> ToFbs(flatbuffers::FlatBufferBuilder& builder) const;
			void FromFbs(const FBSave::GeoSolidSubMesh* fbsGssm);
			//inline GeoSolidSet<GsIv>& GetIvSet() { return setVertexIndices; }
			//inline GeoSolidSet<GsIt>& GetItSet() { return setTriangleIndices; }
			//inline const GeoSolidSet<GsIv>& GetIvSet() const { return setVertexIndices; }
			//inline const GeoSolidSet<GsIt>& GetItSet() const { return setTriangleIndices; }
			#if GEO_SOLID_RECYCLE
			DECLARE_THREAD_FLYWEIGHT_PATTERN(GeoSolidSubMesh)
			#else
			DECLARE_NON_FLYWEIGHT_PATTERN(GeoSolidSubMesh)
			#endif
			friend class GeoSolid;
		};

		class CompositeGeoSolid;
		struct GeoSolidPolygonVertex {
			GsIv iv = 0;
			UInt32 iuv = 0;
			//TODO: 2023-12-12 17:55:53: 可计算某种方差
			GsDigit someValue;
			std::set<GsIv> setNeighborIvs;
			bool ear = false;
			bool earDirty = true;
			GeoSolidPolygonVertex(const GsIv& iv, const UInt32& iuv);
			GeoSolidPolygonVertex(const GeoSolidPolygonVertex& gspv);
			void Copy(const GeoSolidPolygonVertex& gspv);
			GeoSolidPolygonVertex& operator=(const GeoSolidPolygonVertex& gspv);
			bool operator==(const GeoSolidPolygonVertex& gspv) const;
			bool operator==(const GsIv& iv) const;
		};
		class GeoSolidPolygon {
		public:
			explicit GeoSolidPolygon(CompositeGeoSolid* cgs, const UInt32& ivn);
			//GeoSolidPolygon(const GeoSolidPolygon* gsp);
			//void Copy(const GeoSolidPolygon& gsp);
		private:
			int AddTriangle(const GeoSolidTriangle& gstTplt);
			void AsTriangle(const GeoSolidTriangle& gst);
			bool RemoveLinearMiddleVertices();
			/**
			@brief 分离凸多边形
			@param 
			@return 
			*/
			bool DivideConvex(const GeoSolidTriangle& gstTplt, std::unordered_set<GsIt>& setItResults);
			/**
			@brief 合并凸多边形
			*/
			bool MergeIntoConvex(const GeoSolidPolygon& gsp);
			/**
			@brief 合并多边形，不包含重复路径与鸟洞情况
			*/
			bool MergeWithoutHoles(const GeoSolidPolygon& gsp);
			void UpdateEars();
			/**
			@brief 参考：https://www.cnblogs.com/xignzou/p/3721494.html
			*/
			void EarClipping(const GeoSolidTriangle& gstTplt, GeoSolidSet<GsIt>& setItResults);
			//void DivideIntoTriangles(const GeoSolidTriangle& gstTplt, std::unordered_set<GsIt>& setItResults);
			#if COMPOSITE_GEO_SOLID_DEBUG
			//TODO: 2023-11-10 17:21:02: 环形情况
			void GeoGebraAppendCmd(std::ostringstream& oss);
			void GeoGebraExecuteCmd(std::ostringstream& oss);
			void Print(std::ostringstream& oss);
			#endif
		private:
			//有顺序
			std::list<GeoSolidPolygonVertex> listGspvs;
			UInt32 ivn;
			GsDigit area;
			CompositeGeoSolid* cgs;
			friend class CompositeGeoSolid;
		};

		using GeoSolidSubMeshArray = GeoSolidArray<GeoSolidSubMesh*>;
		//using GeoSolidSubMeshArray = GeoSolidArray<GeoSolidSubMesh>;

		/**
		@TODO 函数名修改
				线段：line segment
				直线：line/straight line
		*/
		//TODO: 2023-12-06 16:16:12: 全部使用double进行计算？
		//TODO: 2023-12-06 16:16:22: 统一一维结果和二维结果的精度范围？
		//TODO: 2023-12-06 16:17:25: 太长的三角形，先分割再进行相交判断？
        class EXPORT_SANDBOXENGINE CompositeGeoSolid : public GeoSolid
		{
		public:
			struct InteractParams {
				GeoSolid* gsSrc;
				GeoSolid* gsCut;
				Rainbow::Matrix4x4f mlwSrc;
				Rainbow::Matrix4x4f mlwCut;
				Rainbow::Vector3f vc;
				Rainbow::ColorQuad cqSrc;
				Rainbow::ColorQuad cqCut;
				InteractMethod eIm;
			};
		private:
			struct InOutEdge {
				std::list<GeoSolidPolygonVertex> listGspvs;
				int ieFront;
				int ieBack;
			};
			//图的节点
			struct GeoSolidVertexGraphNode {
				GsIv iv;
				UInt32 value = 0;
			};
			struct WriteOnceBoolean {
				bool b = false;
				bool c = false;
			};
        public:
            CompositeGeoSolid();
			virtual ~CompositeGeoSolid();
			bool Load(Rainbow::AutoRefPtr<Rainbow::DataStream> ds);
			bool Load(const std::string& strPath);
			flatbuffers::Offset<FBSave::GeoSolid> ToFbs(flatbuffers::FlatBufferBuilder& builder) const;
			void FromFbs(const FBSave::GeoSolid* fbsGs);
			Rainbow::SharePtr<Rainbow::Mesh> GetMesh() override;
			void ReleaseInGameRuntime();
			/**
			@brief  球使用圆心到顶点方向的法线
					圆柱按水平横截面的圆的圆心来设置法线
					圆锥按照圆心到该线的垂线方向作为法线
			@param
			@return
			*/
			Rainbow::SharePtr<Rainbow::Mesh> CreateMeshWithVertexNormal();
			/**
			@brief 	测试函数：显示点
					//TODO: 2023-11-08 10:42:36: 改为绘制顶点的方式
			*/
			#if COMPOSITE_GEO_SOLID_DEBUG
			void CreatePointMesh(const GsIv& iv);
			#if COMPOSITE_GEO_SOLID_DEBUG
			#endif
			/**
			@brief  测试函数：显示线段
					读取文件的方式，GeoSolidTriangle没有保存ie，需要屏蔽调用
					//TODO: 2023-11-08 10:42:36: 改为绘制线段的方式
			*/
			void CreateEdgeMesh(const GsIe& ie);
			void CorrectOldNormals();
			#endif
			void UpdateBound();
			//void Center();
			void CreateDynamic() override {}
			void SeparateSurfaces() override;
			int GetSurfaceCount() override { return m_daSubMeshes.size(); }
			GeoSolidFace GetGeoSolidFace(int ism) override;
			GeoSolidShape GetGeoSolidShape(int ism) override;
			GeoSolidSubMesh* GetGeoSolidSubMesh(int ism);
			Rainbow::SharePtr<Rainbow::Mesh> GetSurfaceMesh(const int& i) override;
			const char* GetName() const override { return "Composite Geo Solid"; }
			void SetCacheKey(const std::string& strCacheKey) { m_strCacheKey = strCacheKey; }
			std::string GetCacheKey() const override { return m_strCacheKey; }

			GsIv AddVertex(GeoSolidVertex* gsv, const GsDigit errorThreshold = 1e-2);
			GsIv AddVertexAlways(const GeoSolidVertex* gsv);


			//TODO: 2024-06-21 14:27:38: 疑问：相比直接从数组取值，通过函数调用会返回错误值。引用到局部变量后使内部数组错乱。
			// GeoSolidSubMesh& GetSubMesh(const UInt32& ism);
			// const GeoSolidSubMesh& GetSubMesh(const UInt32& ism) const;

			GsVector2& GetUv(const GsIndex& iuv);

			UInt32 AddNormal(const GsVector3& vn);
			UInt32 AddUv(const GsVector2& uv);
			UInt32 AddColor(const Rainbow::ColorRGBA32& c);
			UInt32 AddBoxBound(const GsBoxBound& bb);
			UInt32 AddMatrix(const GsMatrix3x3& mat);
			void RegenerateEdges();
			void AddVertexToTriangle(const GsIv& iv, const GsIt& it);
			GsIe AddEdge(const GeoSolidEdge* gseIn);
			GsIe AddEdgeIntoTriangle(const GsIe& ieMain, const GsIt& it);
			GsIndex AddUvInTriangle(const GsIv& ivx, const GsIt& it);
			/**
			@brief  白色时，由外部的节点来修改颜色
					有异色设置时，将先前的白色修改为cq
			*/
			void SetColor(const Rainbow::ColorQuad& cq) override;

			static WriteOnceBoolean& GetWriteOnceBoolean(GeoSolidArray<WriteOnceBoolean>& aWobs, const GsIndex& i, const GsIndex& c);
			static GeoSolidArray<WriteOnceBoolean>& GetWriteOnceBooleanArray(
				GeoSolidArray<GeoSolidArray<WriteOnceBoolean>>& ddaWobs, 
				const GsIndex& i, const GsIndex& c
			);
			static WriteOnceBoolean& GetWriteOnceBoolean(
				GeoSolidArray<GeoSolidArray<WriteOnceBoolean>>& ddaWobs, 
				const GsIndex& r, const GsIndex& c, 
				const GsIndex& cr, const GsIndex& cc
			);
			void ClearWriteOnceBoolean();

			void ReplaceVertexIndex(const GsIv& ivBefore, const GsIv& ivAfter);
			void ReplaceVerticesIndices(const std::vector<int>& vReplaceIndices);
			void ReplaceUvsIndices(const std::vector<int>& vReplaceIndices);
			void ReplaceEdgesIndices(const std::vector<int>& vReplaceIndices);
			void ReplaceTrianglesIndices(const std::vector<int>& vReplaceIndices);
			void ExtendDivisionEdgeToSide(const GsIt& it);
			void AddNeighbor(const GsIv& iv0, const GsIv& iv1, const GsDigit& distance);
			void AddNeighborsToEach(const GsIe& ie);
			void DivideEdge(const GsIe& ie);
			bool DivideEdgeByVertex(const GsIe& ie, const GsIv& iv, GsIe& ie0, GsIe& ie1);
			/**
			@brief  先按点进行分割，然后再使用线段穿过这些小三角形来生成新三角形
					优点：计算更快。按点分割只需要一个结果
					//TODO: 2023-11-16 10:29:40: 对内部点进行排序，离三角形重心最近的点优先进行分割
			*/
			void DivideTriangle(const GsIt& itMain);
			void DivideTriangleByVertices(const GsIt& itMain, std::list<GsIt>& listItsResult);
			bool DivideTriangleByMostCentralVertex(const GsIt& itMain, std::list<GsIt>& listItsResult);
			bool DivideTriangleByVertex(const GsIt& it, const GsIv& ivx, std::list<GsIt>& listItsResult);
			void DivideTriangleByEdges(const GsIt& itMain, std::list<GsIt>& listItsResult);
			void RemoveShorterEdgesInEdge(const GsIt& itMain);
			void DivideTriangleEdges(const GsIt& itMain, std::list<GsIe>& listIes);
			void ExtendAndCollectInOutEdges(const GsIt& itMain, const std::vector<int>& vIesWhereIvIn, 
				std::list<InOutEdge>& listInOutEdges, std::list<GsIe>& listIesNew);
			void GetMinCycleInGraph(const std::set<GsIv>& setIvs, const std::list<GeoSolidPolygonVertex>& listGspvs, std::list<GsIv>& listIvsMinCycle);

			/**
			@deprecated? 使用最小环算法，遍历带鸟洞的三角形时，无法找出带鸟洞的多边形
			*/
			void DivideTriangleByPolygons(const GsIt& itMain);
			/**
			@brief 将处在两条共线的新增线段的共同的新增顶点移除
			*/
			void RemoveColinearDividerVertex(const GsIt& itMain);
			void DivideTriangleByRemovingEdges(const GsIt& itMain);
			bool WhetherTriangleHasEdge(const GeoSolidTriangle& gst, const GsIe& ie);
			bool IsVertexInTriangleEdge(const GeoSolidTriangle& gst, const GsIv& iv, int* pie = nullptr, const GsDigit errorThreshold = 1e-5);
			GsIe GetEdgeInTriangleEdge(const GeoSolidTriangle& gst, const GsIe& ie);
			bool IsEdgeInEdge(const GsIe& ieSrc, const GsIe& ie);
			bool WhetherEdgeCrossEdge(const GsIe& ie0, const GsIe& ie1);
			bool WhetherEdgeCrossTriangle(const GsIe& ie, const GsIt& it);
			int WhetherEdgeCrossTriangle(const GsIe& ie, const GsIt& it, GsVector3& vix, GsVector3& xiy);
			GsIt AddTriangle(GeoSolidTriangle* gst);
			/**
			@brief	三角形是否相交与模型，包括在模型内、穿过模型和与模型的三角面重合
			 */
			void WhetherTriangleIntersectGeoSolid(const GsIt& it, bool targetSrc);
			/**
			@brief	三角形重合，
					共面时，有一个点落入三角形内即可，或三个顶点分别重合或落入边中
					仅有两个点在边上或重合时，判断是否穿过该三角形
			 */
			bool WhetherTriangleCoincideWithGeoSolid(const GsIt& itMain, bool targetSrc);
			/**
			@brief	三角形属于模型表面的一部分
			 */
			bool WhetherTriangleIsPartOfTriangle(const GsIt& itMain, bool targetSrc);
			bool WhetherTriangleIsPartOfTriangle2(const GsIt& itMain, bool targetSrc);
			/**
			@brief	判断点在模型内部
			 */
			bool IsVertexInGeoSolid(const GsIv iv, bool src);
			bool IsVertexInGeoSolid(const GsVector3& vp, bool src);
			/**
			@brief	判断三角形在模型内部
			 */
			bool IsTriangleInGeoSolid(const GeoSolidTriangle& gst, bool src);
			bool IsEdgeInGeoSolid(const GsIe& ie, bool src);
			GsDigit GetDistanceFromEdge(const GsIv& iv, const GsIe& ie);
			GsVector3 CastVertexOnEdge(const GsIv& iv, const GsIe& ie);


			void RemoveSubMesh(const int ism);
			#if COMPOSITE_GEO_SOLID_DEBUG
			void GeoGebraExecuteCmdLists(std::ostringstream& ossVs, std::ostringstream& ossTs);
			void GeoGebraExecuteCmdLists(std::ostringstream& ossVs, std::ostringstream& ossEs, std::ostringstream& ossTs);
			void GeoGebraExecuteWhole(bool printDivided = false);
			void GeoGebraExecutePolygons(std::ostringstream& oss, std::list<GeoSolidPolygon>& listGsps);
			void GeoGebraAppendVertexCmd(std::ostringstream& oss, const GsIv& iv);
			void GeoGebraAppendEdgeCmd(std::ostringstream& oss, const GsIe& ie);
			void GeoGebraAppendAnonymousEdgeCmd(std::ostringstream& oss, const GsIv& iv0, const GsIv& iv1);
			void GeoGebraAppendTriangleCmd(std::ostringstream& oss, const GsIt& it);
			void GeoGebraAppendBarycenterCmd(std::ostringstream& oss, const GsIt& it);
			void GeoGebraAddTriangleCmd(std::ostringstream& oss, const GsIt& it);
			void PrintSingleTriangle(const GsIt& it, bool vn, bool uv, bool area, bool vbc, bool uvIndex);
			void PrintSingleGeoGebraExecution(const GsIt& itMain);
			void PrintBeforeDivision(const GsIt& itMain);
			void PrintAfterDivision(const GsIt& itMain, const std::list<GsIt>& listItsResult);
			void PrintSubMesh(const UInt32& ism, bool triangle = false);
			void PrintSubMeshes();
			#endif


			void CoplanarTrianglesIntersect2(const GsIt& it0, const GsIt& it1);
			void CoplanarTrianglesIntersect2(const GsIt& itSrc, const GsIt& itCut, 
				bool lineABIntersectDE, bool lineABIntersectDF, bool lineABIntersectEF,
				GsVector3& vOfABAndDE, GsVector3& vOfABAndDF, GsVector3& vOfABAndEF);
			void CoplanarTrianglesIntersect(const GsIt& it0, const GsIt& it1,
				bool lineABIntersectDE, bool lineACIntersectDE, bool lineBCIntersectDE,
				GsVector3& vBetweenABAndDE, GsVector3& vBetweenACAndDE, GsVector3& vBetweenBCAndDE);

			/**
			@deprecated
			 */
			void LineRayIntersectTriangleForExtensiveVertex(const GsIt it, const GsIv ivg, const GsIv ivh);


			//void Interact(dynamic_array<SceneGeoSolid*> daSgs, InteractMethod eIm = InteractMethod::CUT);
			//TODO: 2023-08-15 19:40:22: 为了UInt32的空间节省，限制总合并数目
			//TODO: 2023-11-16 21:22:35: 准确地合并顶点
			void Interact(const InteractParams& stip);
			void RecaculateNormals(GeoSolid* gsSrc, GeoSolid* gsCut);
			void RemapNeighborVertices();
			void MergeCoincidentVerticesContrast(const GsIv& cvSrc, const GsIv& cvCut);
			//TODO: 2023-11-14 10:39:12: 正确的算法
			void MergeCoincidentVertices();
			int MergeTwoTriangles(const GsIt& itL, const GsIt& itR);
			void MergeCoplanarAdjacentTriangles();
			void MergeIntoPolygons(std::list<GeoSolidPolygon>& listGsps);
			void SortPolygonsByArea(std::list<GeoSolidPolygon>& listGsps);
			void ReconstructWithPolygon(const UInt32& ism);
			void ReconstructWithPolygon2(const UInt32& ism);
			void ReconstructWithPolygon();
			void RemoveInvalidEdges();
			void RemoveInvalidTriangles();
			void RemoveTriangles(const std::vector<bool>& vItsToRemove);
			void KeepTrianglesToRender(const InteractMethod& eIm);
			void HollowTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise);
			void UnionTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise);
			void IntersectTriangle(const GeoSolidTriangle& gst, bool& clockwise, bool& anticlockwise);
			void FinalizeEdges();
			void FinalizeVertices();
			void FinalizeSubMeshes();
			void CreateSubMeshes();
			void ClearSubMeshes();
			GsBoxBound GetBoxBound(const int ibb);
			UInt64 GetNormalCount() { return m_daNormals.size(); }
			GsMatrix3x3 GetMatrix(const int im);
			GsVector3 GetNormal(GeoSolidSubMesh& gssm);
			/**
			@brief	不包括两端
					已多次修改精度，易往深度传递误差。改动需慎重修改。
					不可缓存。多大个地方使用的精度不同。
			 */
			bool IsVertexInEdge(const GsIv& iv, const GsIe& ie, const GsDigit& ep = epZero);
			/**
			@brief	判断AB与BC是否平行
			 */
			bool IsParalleled(const GsIv& iva, const GsIv& ivb, const GsIv& ivc, const GsDigit& ep = epZero);
			/**
			@brief	不包括顶点重合与在边上
			 */
			bool IsVertexInsideTriangle(const GsIv& iv, const GsIt& it, GsIe* pieInEdge = nullptr);
			bool IsVertexInsideTriangle(const GsVector3& vp, const GsIt& it, GsIe* pieInEdge = nullptr);
			/**
			@brief	包括顶点重合与在边上
			 */
			bool IsVertexOnTriangle(const GsIv& iv, const GsIt& it);
			bool IsTriangleClockwise(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2, const UInt32& ivn);
			bool IsConvex(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2, const UInt32& ivn);
			GsDigit GetRadian(const GsIv& iv0, const GsIv& iv1, const GsIv& iv2);
			GsDigit GetDistance(const GsIv& iv0, const GsIv& iv1);
			GsDigit GetVertexDistanceFromEdge(const GsIv& iv, const GsIe& ie);
			void CorrectClockwise(GeoSolidTriangle& gst);
			void CorrectClockwise(GeoSolidTriangle* gst);
		private:
			GeoSolidSubMeshArray m_daSubMeshes;
			/**
			@deprecated	不可缓存
			 */
			//GeoSolidArray<GeoSolidArray<WriteOnceBoolean>> m_ddaIvsInIes;
			GeoSolidArray<GeoSolidArray<WriteOnceBoolean>> m_ddaIvsInsideIts;
			GeoSolidArray<GeoSolidArray<WriteOnceBoolean>> m_ddaIvsOnIts;
			GeoSolidArray<WriteOnceBoolean> m_aIvsInSrc;
			GeoSolidArray<WriteOnceBoolean> m_aIvsInCut;
			GeoSolidArray<WriteOnceBoolean> m_aIesInSrc;
			GeoSolidArray<WriteOnceBoolean> m_aIesInCut;
			GsBoxBound m_bbSrc;
			GsBoxBound m_bbCut;
			/**
			@brief	本体坐标系到部位
			 */
			Quaterniondb m_qRotateInverseSrc = Quaterniondb::identity;
			Quaterniondb m_qRotateInverseCut = Quaterniondb::identity;
			GsMatrix3x3 m_matRotateInverseSrc = GsMatrix3x3::identity;
			GsMatrix3x3 m_matRotateInverseCut = GsMatrix3x3::identity;
			std::string m_strCacheKey;
			int m_cSrcTriangle : 32;
			int m_cCutTriangle : 32;
			GeoSolidShape m_eGssSrc : 8;
			GeoSolidShape m_eGssCut : 8;
			bool m_bIsSrcBasic : 1;
			bool m_bIsCutBasic : 1;
			#if COMPOSITE_GEO_SOLID_DEBUG
		public:
			/**
			@brief	对于较多实体模型的组合，从日志代码中扩大字符串数，以完全打印在GeoGebra建模的命令
			 */
			bool m_bDebugDetail : 1;
			bool m_bDebugSummary : 1;
			bool m_bDebugRender : 1;
			char : 2;
			#endif
			friend class GeoSolidPolygon;
        };
    }
}
#endif//__CompositeGeoSolid_h__