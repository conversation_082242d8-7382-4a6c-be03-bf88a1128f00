#include "SandboxCloudAssetLoader.h"
#include "AssetPipeline/Asset.h"
#include "AssetPipeline/AssetRemote/AssetDownloadManager.h"
#include "SandboxMacros.h"
#include "CloudAsset/CloudAssetLoader.h"
#include "SandboxLog.h"

namespace MNSandbox
{
#ifdef OPEN_SANDBOX_CLOUD_ASSET_LOADER
	//初始化的时候
	static void InitCloudAssetLoader(void* data)
	{
		//注册云服的下载流程
		Rainbow::GetAssetDownloadManager().SetCloudLoader(SandboxCloudAssetLoader::NewLoader, SandboxCloudAssetLoader::DeleteLoader);
	};

	RegisterRuntimeInitializeAndCleanup s_CloudAssetLoaderInit(InitCloudAssetLoader, nullptr);
#endif
	//IMPLEMENT_REFCLASS(SandboxCloudAssetLoader)


	SandboxCloudAssetLoader::SandboxCloudAssetLoader()
	{
		m_loaderRef = SANDBOX_NEW_LABEL(AssetLoaderRef, kMemSandboxNode, this);
	}


	SandboxCloudAssetLoader::~SandboxCloudAssetLoader()
	{
		m_loaderRef = nullptr;
	}

	core::string SandboxCloudAssetLoader::GetDownloadUrl() const
	{
		return m_loaderRef->GetDownloadUrl();
	}

	float SandboxCloudAssetLoader::GetDownloadSpeed() const
	{
		return m_loaderRef->GetDownloadSpeed();
	}

	bool SandboxCloudAssetLoader::CheckAssetHashEqual()
	{
		return m_loaderRef->CheckAssetHashEqual();
	}

	void SandboxCloudAssetLoader::StartLoad(Rainbow::SharePtr<Rainbow::Asset>& asset, int priority, Rainbow::OnCloudUrlCallBack callBack, int retryTimes)
	{
		SANDBOX_LOG("StartLoad: GUID:", asset->GetAssetGUID().ToString().c_str(), " ResHashPath:", asset->GetResHashPath().c_str());

		//m_defaultAssetLoader->StartLoad(asset, priority, callBack, retryTimes);
		m_loaderRef->StartLoad(asset , priority, callBack, retryTimes);
	}

	Rainbow::ICloudUrlLoader* SandboxCloudAssetLoader::NewLoader()
	{
		return SANDBOX_NEW_LABEL(SandboxCloudAssetLoader, kMemDefault);
	}

	void SandboxCloudAssetLoader::DeleteLoader(ICloudUrlLoader* loader)
	{
		SANDBOX_DELETE_LABEL(loader, kMemDefault);
	};

	void* SandboxCloudAssetLoader::SandboxCloudAssetLoaderTest()
	{
#ifdef OPEN_SANDBOX_CLOUD_ASSET_LOADER
		return &s_CloudAssetLoaderInit;
#else
		return Rainbow::CloudAssetLoader::CloudAssetLoaderTest();
#endif
	}




}
