
#ifndef __COLLISION_H__
#define __COLLISION_H__

#include "world_types.h"
#include "SandboxEngine.h"

class EXPORT_SANDBOXENGINE CollisionDetect;
class CollisionDetect //tolua_export
{//tolua_export
public:
	CollisionDetect();
	~CollisionDetect();

	//tolua_begin
	void reset();
	void reset(const WCoord &viewboundmin, const WCoord &viewboundmax);
	virtual void addObstacle(const WCoord &minpos, const WCoord &maxpos);
	void addObstacle(const WCoord &minpos, const WCoord &maxpos, const WCoord &origin, int placedir);

	float moveBox(const CollideAABB &box, const WCoord &mvec, Rainbow::Vector3f &colnormal);
	bool intersectBox(const CollideAABB &box);
	int intersectRay(const Rainbow::Vector3f &origin, const Rainbow::Vector3f &dir, float *pt);
	CollideAABB getObstacle(const CollideAABB& box, const WCoord& mvec);

	bool intersect(const WCoord &minpos, const WCoord &maxpos)
	{
		if(minpos.x>=m_ViewBoundMax.x || minpos.y>=m_ViewBoundMax.y || minpos.z>=m_ViewBoundMax.z
			|| maxpos.x<=m_ViewBoundMin.x || maxpos.y<=m_ViewBoundMin.y || maxpos.z<=m_ViewBoundMin.z ) return false;
		return true;
	}
	const WCoord &getCollideMin()
	{
		return m_CollideMin;
	}
	const WCoord &getCollideMax()
	{
		return m_CollideMax;
	}

	int GetCollideCount()
	{
		return (int)m_UseSize;
	}
	//tolua_end
private:
	bool m_hasViewBound;
	WCoord m_ViewBoundMin;
	WCoord m_ViewBoundMax;

	WCoord m_CollideMin;
	WCoord m_CollideMax;
	size_t m_UseSize;
	std::vector<CollideAABB>m_Obstacles;
}; //tolua_export

#endif