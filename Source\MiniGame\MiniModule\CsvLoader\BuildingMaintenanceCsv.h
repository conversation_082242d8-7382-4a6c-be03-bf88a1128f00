#pragma once

#include "AbsCsv.h"
#include "LazySingleton.h"
#include "DefDataTable.h"
#include "defdata.h"
#include <string>
#include <vector>

class BuildingMaintenanceCsv : public AbsCsv {
public:
    BuildingMaintenanceCsv();
    ~BuildingMaintenanceCsv();
    
    void onParse(MINIW::CSVParser& parser) override;
    void onClear() override;
    const char* getName() override;
    const char* getClassName() override;
    
    int getNum();
    const BuildingMaintenanceCsvDef* get(int num);
    std::vector<const BuildingMaintenanceCsvDef*> getAll();
    
private:
    DefDataTable<BuildingMaintenanceCsvDef> m_Table;
    
    DECLARE_LAZY_SINGLETON(BuildingMaintenanceCsv)
}; 