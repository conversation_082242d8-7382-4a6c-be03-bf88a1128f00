#include "OpenSandboxEngineToLua.h"
#include "ClientMacrosConfig.h"

struct lua_State;
extern int tolua_MiniShared_open(lua_State*);
extern int miniShared_open_manual_tolua(lua_State*);
extern int sandboxcoredriver_open_manual_tolua(lua_State*);
extern int  tolua_miniSandboxEngineActor_open(lua_State* tolua_S);
extern int tolua_GameAnalyticsTolua_open(lua_State*);


#ifdef IWORLD_SERVER_BUILD
    extern int tolua_miniSandboxEnginePlay_linux_open(lua_State*);
    extern int tolua_miniSandboxToLua_linux_open(lua_State*);
#else
    extern int tolua_miniSandboxEnginePlay_open(lua_State*);
    extern int tolua_miniSandboxToLua_open(lua_State*);
#endif

void OpenSandboxEngineToLuaStep1(lua_State* ls)
{
#ifndef IWORLD_SERVER_BUILD	
    tolua_MiniShared_open(ls);
#endif	
    miniShared_open_manual_tolua(ls);
    sandboxcoredriver_open_manual_tolua(ls);
    tolua_miniSandboxEngineActor_open(ls);
    tolua_GameAnalyticsTolua_open(ls);

#ifdef IWORLD_SERVER_BUILD
    tolua_miniSandboxEnginePlay_linux_open(ls);
    tolua_miniSandboxToLua_linux_open(ls);
#else
    tolua_miniSandboxEnginePlay_open(ls);
    tolua_miniSandboxToLua_open(ls);
#endif
}

void OpenSandboxEngineToLuaStep2(lua_State* ls)
{
    
}

void OpenSandboxEngineToLuaStep3(lua_State* ls)
{

}

void OpenSandboxEngineToLuaStep4(lua_State* ls)
{

}

void OpenSandboxEngineToLuaStep5(lua_State* ls)
{

}

void OpenSandboxEngineToLuaStep6(lua_State* ls)
{
    
}
