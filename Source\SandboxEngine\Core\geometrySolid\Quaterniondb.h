#pragma once
#include "geometrySolid/GeoSolidEnum.h"
#include "geometrySolid/Matrix3x3db.h"
//#include "geometrySolid/Matrix4x4db.h"

#include "Math/FloatConversion.h"
#include "Math/Simd/RotationOrder.h"
#include "Math/Quaternionf.h"
#include "geometrySolid/Vector3db.h"
//#include "Serialize/SerializeTraitsBase.h"

namespace MNSandbox {
	namespace GeometrySolid {
		//保留这些注释，需要时再启用
		/**
		@class	Quaterniondb.h
		@brief 四元数，Quaterniondb q1,Quaterniondb q2,  q1*q2 ==> q1 往 q2 旋转
		 */
		class Quaterniondb
		{
		public:
			//DEFINE_GET_TYPESTRING(Quaterniondb)
			//template<class TransferFunction> void Transfer(TransferFunction& transfer);
			FORCEINLINE Quaterniondb() :
				x(0.0), y(0.0), z(0.0), w(1.0)
			{ }
			FORCEINLINE Quaterniondb(double inX, double inY, double inZ, double inW)
			{
				x = inX; y = inY; z = inZ; w = inW;
			}
			FORCEINLINE Quaterniondb(const double* array)
			{
				x = array[0]; y = array[1]; z = array[2]; w = array[3];
			}

			FORCEINLINE Quaterniondb(const Rainbow::Quaternionf& q) : x(q.x), y(q.y), z(q.z), w(q.w) {}

			FORCEINLINE void Set(double inX, double inY, double inZ, double inW)
			{
				x = inX; y = inY; z = inZ; w = inW;
			}
			FORCEINLINE void Set(const Quaterniondb& quat)
			{
				x = quat.x; y = quat.y; z = quat.z; w = quat.w;
			}
			FORCEINLINE void Set(const double* array) { x = array[0]; y = array[1]; z = array[2]; w = array[3]; }
			FORCEINLINE const double* GetPtr() const { return &x; }
			FORCEINLINE double* GetPtr() { return &x; }

			FORCEINLINE double Dot(const Quaterniondb& q)const
			{
				return (x * q.x + y * q.y + z * q.z + w * q.w);
			}

			FORCEINLINE double SqrMagnitude()const
			{
				return Dot(*this);
			}

			FORCEINLINE double Magnitude()const
			{
				return sqrt(SqrMagnitude());
			}

			FORCEINLINE Quaterniondb GetNormalized()const
			{
				double mag = Magnitude();
				//AssertMsg(mag >= kEpsilon, "GetNormalized() return a NAN value, please use GetNormalizedSafe()!");
				return *this / mag;
			}

			FORCEINLINE Quaterniondb GetNormalizedSafe()const
			{
				double mag = Magnitude();
				if (mag < kEpsilon)
					return Quaterniondb::identity;
				else
					return *this / mag;
			}

			FORCEINLINE double Normalize()
			{
				double mag = Magnitude();
				//AssertMsg(mag >= kEpsilon, "Normalize() return a NAN value, please use NormalizeSafe()!");
				*this /= mag;
				//AssertMsg(!(IsNAN(x) || IsNAN(y) || IsNAN(z) || IsNAN(w)), "Normalize() get a NAN value, please use NormalizeSafe()!");
				return mag;
			}

			FORCEINLINE double NormalizeSafe()
			{
				double mag = Magnitude();
				if (mag > kEpsilon)
				{
					*this /= mag;
					return mag;
				}

				*this = Quaterniondb::identity;
				return 0.0;
			}

			FORCEINLINE Quaterniondb Conjugate()const
			{
				return Quaterniondb(-x, -y, -z, w);
			}

			FORCEINLINE Quaterniondb Inverse()const
			{
				return Conjugate();
			}

			FORCEINLINE bool ContainsNaN() const
			{
				return Rainbow::IsNAN(x) || Rainbow::IsNAN(y) || Rainbow::IsNAN(z) || Rainbow::IsNAN(w);
			}

			FORCEINLINE const double& operator[](int i) const { return GetPtr()[i]; }
			FORCEINLINE double& operator[](int i) { return GetPtr()[i]; }

			FORCEINLINE bool operator==(const Quaterniondb& q) const
			{
				return x == q.x && y == q.y && z == q.z && w == q.w;
			}

			FORCEINLINE bool operator!=(const Quaterniondb& q) const
			{
				return x != q.x || y != q.y || z != q.z || w != q.w;
			}

			FORCEINLINE Quaterniondb operator + (const Quaterniondb& q)const
			{
				return Quaterniondb(x + q.x, y + q.y, z + q.z, w + q.w);
			}

			FORCEINLINE Quaterniondb operator - (const Quaterniondb& q)const
			{
				return Quaterniondb(x - q.x, y - q.y, z - q.z, w - q.w);
			}

			FORCEINLINE Quaterniondb& operator += (const Quaterniondb& quat)
			{
				x += quat.x;
				y += quat.y;
				z += quat.z;
				w += quat.w;
				return *this;
			}

			FORCEINLINE Quaterniondb& operator -= (const Quaterniondb& quat)
			{
				x -= quat.x;
				y -= quat.y;
				z -= quat.z;
				w -= quat.w;
				return *this;
			}

			FORCEINLINE Quaterniondb& operator *= (double scalar)
			{
				x *= scalar;
				y *= scalar;
				z *= scalar;
				w *= scalar;
				return *this;
			}

			FORCEINLINE Quaterniondb& operator *= (const Quaterniondb& rhs)
			{
				double tempx = w * rhs.x + x * rhs.w + y * rhs.z - z * rhs.y;
				double tempy = w * rhs.y + y * rhs.w + z * rhs.x - x * rhs.z;
				double tempz = w * rhs.z + z * rhs.w + x * rhs.y - y * rhs.x;
				double tempw = w * rhs.w - x * rhs.x - y * rhs.y - z * rhs.z;
				x = tempx; y = tempy; z = tempz; w = tempw;
				return *this;
			}

			FORCEINLINE Quaterniondb& operator /= (const double scalar)
			{
				//Assert(!CompareApproximately(scalar, 0.0));
				Assert(scalar != 0.0);
				const double invScale = 1.0 / scalar;
				x *= invScale;
				y *= invScale;
				z *= invScale;
				w *= invScale;
				return *this;
			}

			FORCEINLINE Quaterniondb operator / (const double s)const
			{
				Quaterniondb t(*this);
				return t /= s;
			}

			FORCEINLINE Quaterniondb operator - () const
			{
				return Quaterniondb(-x, -y, -z, -w);
			}

			FORCEINLINE Quaterniondb operator * (const double s) const
			{
				return Quaterniondb(x * s, y * s, z * s, w * s);
			}

			friend FORCEINLINE Quaterniondb operator * (const double s, const Quaterniondb& q)
			{
				Quaterniondb t(q);
				return t *= s;
			}

			FORCEINLINE Quaterniondb operator * (const Quaterniondb& q)const
			{
				return Quaterniondb(
					w * q.x + x * q.w + y * q.z - z * q.y,
					w * q.y + y * q.w + z * q.x - x * q.z,
					w * q.z + z * q.w + x * q.y - y * q.x,
					w * q.w - x * q.x - y * q.y - z * q.z);
			}

			FORCEINLINE Vector3db operator * (const Vector3db& v)const
			{
				// Extract the vector part of the quaternion
				Vector3db u(x, y, z);

				// Extract the scalar part of the quaternion
				double s = w;

				// Do the math
				return 2.0 * DotProduct(u, v) * u
					+ (s*s - DotProduct(u, u)) * v
					+ 2.0 * s * CrossProduct(u, v);
			}

			FORCEINLINE core::string ToString() const
			{
				return Format("x:%f,y:%f,z:%f,w:%f", x, y, z, w);
			}

			FORCEINLINE Vector3db GetAxisX() const
			{
				double ys = y * 2.0;
				double zs = z * 2.0;

				double wy = w * ys;
				double wz = w * zs;

				double yy = y * ys;
				double zz = z * zs;
				double xy = x * ys;
				double xz = x * zs;
				return Vector3db(1 - yy - zz, xy + wz, xz - wy);
			}

			//FORCEINLINE UInt32 ToUInt32()
			//{
			//	unsigned int x1 = Clamp(int((x + 1.0f)*0.5f*255.0f), 0, 255);
			//	unsigned int y1 = Clamp(int((y + 1.0f)*0.5f*255.0f), 0, 255);
			//	unsigned int z1 = Clamp(int((z + 1.0f)*0.5f*255.0f), 0, 255);
			//	unsigned int w1 = Clamp(int((w + 1.0f)*0.5f*255.0f), 0, 255);

			//	return x1 | (y1 << 8) | (z1 << 16) | (w1 << 24);
			//}

			FORCEINLINE void FromUInt32(UInt32 v)
			{
				x = (v & 0xff) / 127.5f - 1.0f;
				y = ((v >> 8) & 0xff) / 127.5f - 1.0f;
				z = ((v >> 16) & 0xff) / 127.5f - 1.0f;
				w = ((v >> 24) & 0xff) / 127.5f - 1.0f;
			}

			FORCEINLINE Vector3db GetAxisY() const
			{
				double xs = x * 2.f;
				double ys = y * 2.f;
				double zs = z * 2.f;

				double wx = w * xs;
				double wz = w * zs;

				double xx = x * xs;
				double zz = z * zs;
				double xy = x * ys;
				double yz = y * zs;
				return Vector3db(xy - wz, 1 - xx - zz, yz + wx);
			}

			FORCEINLINE Vector3db GetAxisZ() const
			{
				double xs = x * 2.f;
				double ys = y * 2.f;
				double zs = z * 2.f;

				double wx = w * xs;
				double wy = w * ys;

				double xx = x * xs;
				double yy = y * ys;
				double xz = x * zs;
				double yz = y * zs;
				return Vector3db(xz + wy, yz - wx, 1 - xx - yy);
			}

			double x, y, z, w;
			static const Quaterniondb identity;
			static const Quaterniondb zero;


		};

		bool CompareApproximately(const Quaterniondb& q1, const Quaterniondb& q2, double epsilon = kEpsilon);

		Quaterniondb Lerp(const Quaterniondb& q1, const Quaterniondb& q2, double t);

		Quaterniondb Slerp(const Quaterniondb& q1, const Quaterniondb& q2, double t);

		//Vector3db QuaternionToEuler(const Quaterniondb& quat, math::RotationOrder order = math::kOrderDefault);

		//Vector3db QuaternionToEulerAngle(const Quaterniondb& quat, math::RotationOrder order = math::kOrderDefault);


		//参数是弧度的
		//Quaterniondb EulerToQuaterniondb(const Vector3db& euler, math::RotationOrder order = math::kOrderDefault);
		//FORCEINLINE Quaterniondb EulerToQuaterniondb(double x, double y, double z, math::RotationOrder order = math::kOrderDefault)
		//{
		//	return EulerToQuaterniondb(Vector3db(x, y, z), order);
		//}

		//参数是角度的
		//Quaterniondb AngleEulerToQuaterniondb(const Vector3db& AngleEuler, math::RotationOrder order = math::kOrderDefault);


		void QuaternionfToMatrix(const Quaterniondb& q, Matrix3x3db& m);

		//void  MatrixToQuaterniondb(const Matrix3x3db& m, Quaterniondb& q);
		//void  MatrixToQuaterniondb(const Matrix4x4db& m, Quaterniondb& q);

		//void QuaternionfToMatrix(const Quaterniondb& q, Matrix4x4db& m);

		//void QuaternionfToAxisAngle(const Quaterniondb& q, Vector3db* axis, double* targetAngle);

		//Quaterniondb AxisAngleToQuaterniondb(const Vector3db& axis, double angle);

		/// Generates a Right handed Quat from a look rotation. Returns if conversion was successful.
		//bool LookRotationToQuaterniondb(const Vector3db& viewVec, const Vector3db& upVec, Quaterniondb* res);

		FORCEINLINE Vector3db RotateVectorByQuat(const Quaterniondb& lhs, const Vector3db& rhs)
		{
			//  Matrix3x3db m;
			//  QuaternionToMatrix (lhs, &m);
			//  Vector3db restest = m.MultiplyVector3 (rhs);
			double x = lhs.x * 2.0;
			double y = lhs.y * 2.0;
			double z = lhs.z * 2.0;
			double xx = lhs.x * x;
			double yy = lhs.y * y;
			double zz = lhs.z * z;
			double xy = lhs.x * y;
			double xz = lhs.x * z;
			double yz = lhs.y * z;
			double wx = lhs.w * x;
			double wy = lhs.w * y;
			double wz = lhs.w * z;

			Vector3db res;
			res.x = (1.0 - (yy + zz)) * rhs.x + (xy - wz)          * rhs.y + (xz + wy)          * rhs.z;
			res.y = (xy + wz)          * rhs.x + (1.0 - (xx + zz)) * rhs.y + (yz - wx)          * rhs.z;
			res.z = (xz - wy)          * rhs.x + (yz + wx)          * rhs.y + (1.0 - (xx + yy)) * rhs.z;

			//  Assert (CompareApproximately (restest, res));
			return res;
		}

		// operator overloads
		//  inlines

		FORCEINLINE Quaterniondb Lerp(const Quaterniondb& q1, const Quaterniondb& q2, double t)
		{
			Quaterniondb tmpQuat;
			// if (dot < 0), q1 and q2 are more than 360 deg apart.
			// The problem is that quaternions are 720deg of freedom.
			// so we - all components when lerping
			if (q1.Dot(q2) < 0.0)
			{
				tmpQuat.Set(q1.x + t * (-q2.x - q1.x),
					q1.y + t * (-q2.y - q1.y),
					q1.z + t * (-q2.z - q1.z),
					q1.w + t * (-q2.w - q1.w));
			}
			else
			{
				tmpQuat.Set(q1.x + t * (q2.x - q1.x),
					q1.y + t * (q2.y - q1.y),
					q1.z + t * (q2.z - q1.z),
					q1.w + t * (q2.w - q1.w));
			}
			return tmpQuat.GetNormalized();
		}

		FORCEINLINE double AngularDistance(const Quaterniondb& lhs, const Quaterniondb& rhs)
		{
			double dot = lhs.Dot(rhs);
			if (dot < 0.0)
				dot = -dot;
			return acos(Rainbow::Min(1.0, dot)) * 2.0;
		}

		FORCEINLINE void QuaternionfToAxisAngle(const Quaterniondb& q, Vector3db* axis, double* targetAngle)
		{
			//Assert(CompareApproximately(q.SqrMagnitude(), 1.0));
			Assert(q.SqrMagnitude() == 1.0);
			*targetAngle = 2.0 * acosf(q.w);
			if (*targetAngle == 0.0)
			{
				*axis = Vector3db::xAxis;
				return;
			}

			double div = 1.0 / ::sqrt(1.0 - sqrt(q.w));
			axis->Set(q.x * div, q.y * div, q.z * div);
		}

		FORCEINLINE Quaterniondb AxisAngleToQuaterniondb(const Vector3db& axis, double angle)
		{
			Quaterniondb q;
			//Assert(CompareApproximately(SqrMagnitude(axis), 1.0F));
			Assert(SqrMagnitude(axis) == 1.0);
			double halfAngle = angle * 0.5;
			double s = sinf(halfAngle);

			q.w = cosf(halfAngle);
			q.x = s * axis.x;
			q.y = s * axis.y;
			q.z = s * axis.z;
			return q;
		}

		FORCEINLINE Quaterniondb AngularVelocityToQuaterniondb(const Vector3db& axis, double deltaTime)
		{
			double w = Magnitude(axis);
			if (w > kEpsilon)
			{
				double v = deltaTime * w * 0.5;
				double q = cosf(v);
				double s = sinf(v) / w;

				Quaterniondb integrated;
				integrated.w = q;
				integrated.x = s * axis.x;
				integrated.y = s * axis.y;
				integrated.z = s * axis.z;

				return integrated.GetNormalized();
			}
			else
			{
				return Quaterniondb::identity;
			}
		}

		FORCEINLINE Quaterniondb AxisAngleToQuaternionSafe(const Vector3db& axis, double angle)
		{
			Quaterniondb q;
			double mag = Magnitude(axis);
			if (mag > 0.000001F)
			{
				double halfAngle = angle * 0.5;

				q.w = cosf(halfAngle);

				double s = sinf(halfAngle) / mag;
				q.x = s * axis.x;
				q.y = s * axis.y;
				q.z = s * axis.z;
				return q;
			}
			else
			{
				return Quaterniondb::identity;
			}
		}

		// Generates a quaternion that rotates lhs into rhs.
		Quaterniondb FromToQuaternionSafe(const Vector3db& lhs, const Vector3db& rhs);
		// from and to are assumed to be normalized
		Quaterniondb FromToQuaternion(const Vector3db& from, const Vector3db& to);


		FORCEINLINE bool CompareApproximately(const Quaterniondb& q1, const Quaterniondb& q2, double epsilon)
		{
			//return SqrMagnitude (q1 - q2) < epsilon * epsilon;
			return ((q1 - q2).SqrMagnitude() <= epsilon * epsilon) || ((q1 + q2).SqrMagnitude() <= epsilon * epsilon);
			//return Abs (Dot (q1, q2)) > (1 - epsilon * epsilon);
		}

		FORCEINLINE Quaterniondb NormalizeFastEpsilonZero(const Quaterniondb& q)
		{
			double m = q.SqrMagnitude();
			if (m < kEpsilon)
				return Quaterniondb(0.0, 0.0, 0.0, 0.0);
			else
				return q * InvSqrt(m);
		}

		FORCEINLINE Quaterniondb NormalizeSafeIfUnnormalized(const Quaterniondb& q)
		{
			double mag = q.Magnitude();

			if (mag < kEpsilon)
				return Quaterniondb::identity;

			// Avoid dividing quaternions that are close enough to already normalized,
			// to avoid introducing noise from floating-point precision issues
			if (Rainbow::Abs(mag - 1.0) < kEpsilon)
				return q;

			return q / mag;
		}

		FORCEINLINE bool IsFinite(const Quaterniondb& f)
		{
			return IsFinite(f.x) && IsFinite(f.y) && IsFinite(f.z) && IsFinite(f.w);
		}

		FORCEINLINE double Dot(const Quaterniondb& q1, const Quaterniondb& q2)
		{
			return (q1.x * q2.x + q1.y * q2.y + q1.z * q2.z + q1.w * q2.w);
		}

		FORCEINLINE double SqrMagnitude(const Quaterniondb& q)
		{
			return Dot(q, q);
		}

		FORCEINLINE double Magnitude(const Quaterniondb& q)
		{
			return sqrt(SqrMagnitude(q));
		}

		//FORCEINLINE Vector3db QuaternionToEulerAngle(const Quaterniondb& quat, math::RotationOrder order)
		//{
		//	Vector3db euler = QuaternionToEuler(quat, order);
		//	return Vector3db(Rad2Deg(euler.x), Rad2Deg(euler.y), Rad2Deg(euler.z));
		//}

		//FORCEINLINE Quaterniondb AngleEulerToQuaterniondb(const Vector3db & AngleEuler, math::RotationOrder order)
		//{
		//	Vector3db raduis(Deg2Rad(AngleEuler.x), Deg2Rad(AngleEuler.y), Deg2Rad(AngleEuler.z));
		//	return EulerToQuaternionf(raduis, order);
		//}

		FORCEINLINE Quaterniondb NormalizeSafe(const Quaterniondb& q)
		{
			double mag = Magnitude(q);
			if (mag > kEpsilon)
				return q / mag;
			else
				return Quaterniondb::identity;
		}

		//template<class TransferFunction>
		//FORCEINLINE void Quaterniondb::Transfer(TransferFunction& transfer)
		//{
		//	//transfer.AddMetaFlag(kTransferUsingFlowMappingStyle);
		//	TRANSFER(x);
		//	TRANSFER(y);
		//	TRANSFER(z);
		//	TRANSFER(w);
		//}

		FORCEINLINE bool Equal(const Quaterniondb& p1, const Quaterniondb& p2, const double epilon = kEpsilon)
		{
			return 
				(Rainbow::Abs(p1.x - p2.x) < epilon) && 
				(Rainbow::Abs(p1.y - p2.y) < epilon) && 
				(Rainbow::Abs(p1.z - p2.z) < epilon) && 
				(Rainbow::Abs(p1.w - p2.w) < epilon);
		}

		//FORCEINLINE Quaterniondb Lerpto(const Quaterniondb& from, const Quaterniondb& to, double deltaTime, double interpSpeed)
		//{
		//	if (interpSpeed <= 0.0f)
		//	{
		//		return to;
		//	}

		//	if (Equal(from, to))
		//	{
		//		return to;
		//	}

		//	return Slerp(from, to, Clamp(deltaTime * interpSpeed, 0.0f, 1.0f));
		//}
	}
}
