#pragma once
/*
*	file: SceneEffectSelectFrame
*	func: 沙盒选区效果
*	by : yangzy
*/

#include "SceneEffectFrame.h"
#include "SceneEffectArrow.h"
enum CURVEFACEMTLTYPE;
struct BlockGeomVert;
class World;

class SceneEffectSelectFrame : public SceneEffectGeom //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	SceneEffectSelectFrame();
	SceneEffectSelectFrame(const WCoord& startpos, const WCoord& endpos, int stroke = 12, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, CurveFace* curveFaces = nullptr);
	virtual ~SceneEffectSelectFrame();

	virtual void OnClear() override;
	virtual void Refresh() override;
	virtual void OnDraw(World* pWorld) override;
	virtual bool IsActive(World* pWorld) const override;
	//tolua_end

	// 设置位置
	void SetPos(const WCoord& startpos, const WCoord& endpos);

	// 设置渲染材质类型
	void SetMtlType(CURVEFACEMTLTYPE mtltype) { m_MtlType = mtltype; }
	CURVEFACEMTLTYPE GetMtlType() const { return m_MtlType; }
	void SetColor(MNSandbox::MNColor selectFrameColor, MNSandbox::MNColor coverColor, MNSandbox::MNColor endArrowColor);
	void SetStroke(int stroke) { m_iStroke = stroke; }
	void ShowEndArrow(bool bShow) { m_bShowEndArrow = bShow;  }
	void SetEndArrowPos(MNSandbox::MNCoord3f pos);
	void CallbackNodeChanged(MNSandbox::SandboxNode* node, MNSandbox::SandboxNode::NODECHANGE eNodeChanged);

protected:
	void Normalize(const WCoord& startpos, const WCoord& endpos);
	bool IsValid();
	void CalcVertexsAndIndices();

private:
	// 属性
	WCoord m_StartBottom = WCoord(0, -1, 0);
	WCoord m_StartTop = WCoord(0, -1, 0);
	WCoord m_StartLeft = WCoord(0, -1, 0);
	WCoord m_StartRight = WCoord(0, -1, 0);
	WCoord m_StartFront = WCoord(0, -1, 0);
	WCoord m_StartBehind = WCoord(0, -1, 0);
	BlockVector m_SelectFrameColor = MakeBlockVector(255, 255, 255, 127);
	BlockVector m_CoverColor = MakeBlockVector(0, 170, 255, 50);
	BlockVector m_EndArrowColor = MakeBlockVector(0, 241, 1, 255);
	int m_BorderWidth = 10;
	bool m_bShowEndArrow = false;
	MNSandbox::MNCoord3f m_endArrowPos = MNSandbox::MNCoord3f(0.0);
	float m_endArrowScaleRatio = 1.0;	//缩放比率

	// 缓存
	std::vector<BlockGeomVert> m_VertexsBottom; 
	std::vector<BlockGeomVert> m_VertexsTop;
	std::vector<BlockGeomVert> m_VertexsLeft;
	std::vector<BlockGeomVert> m_VertexsRight;
	std::vector<BlockGeomVert> m_VertexsFront;
	std::vector<BlockGeomVert> m_VertexsBehind;
	std::vector<unsigned short> m_Indices; // 索引缓存

	SceneEffectFrame* m_optFrame = nullptr;
	SceneEffectFrame* m_optFrameAlpha = nullptr;
	SceneEffectArrow* m_endArrow = nullptr;
}; //tolua_exports
