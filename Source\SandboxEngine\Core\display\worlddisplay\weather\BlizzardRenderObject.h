#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

using namespace Rainbow;
class BlizzardRenderable;
class BlizzardMeshData;

class BlizzardRenderObject : public Rainbow::RenderObject
{
public:
	explicit BlizzardRenderObject(BlizzardRenderable* component);
	~BlizzardRenderObject();

	virtual bool PrepareRender(const Rainbow::PrimitiveFrameNode& node) override;
	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;


private:
	BlizzardRenderable* m_BlizzardEffect;
	MeshRenderData m_MeshRenderData;
	BlizzardMeshData* m_RenderBuffer;

	dynamic_array<DrawBuffersRange> m_DrawBuffersRanges;
};

