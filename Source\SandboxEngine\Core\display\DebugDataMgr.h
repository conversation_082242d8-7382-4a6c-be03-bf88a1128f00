﻿#pragma once

#include "Common/OgreWorldPos.h"
#include "Common/SingletonDefinition.h"
#include "SandboxEngine.h"
#include "BaseClass/SharePtr.h"
#include "Graphics/Texture2D.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
class TerrainGen;
class CollideAABB;

class EXPORT_SANDBOXENGINE DebugDataMgr;
class DebugDataMgr //tolua_exports
{ //tolua_exports
public:

	static DebugDataMgr& GetInstance();
	static DebugDataMgr* GetInstancePtr();

	//tolua_begin
	DebugDataMgr();
	~DebugDataMgr();
	//tolua_end
public:
	//tolua_begin
	void renderUI();

	void setShowTerrBisect(bool b);
	bool isShowTerrBisect()
	{
		return m_ShowTerrBisect;
	}
	void setShowPerfOn(bool b) { m_EnablePerfOn = b;}
	bool isShowPerfOn() { return m_EnablePerfOn;}
	//tolua_end
#ifdef BUILD_MINI_EDITOR_APP//codeby lich
	void setOpenPerfInfo(bool bOpen) { m_OpenPerfInfo = bOpen; }
	bool isOpenPerfInfo() { return m_OpenPerfInfo; }
#endif // BUILD_MINI_EDITOR_APP
	//tolua_begin
	void toggleRenderInfo();
	void toggleRenderDraw();
	void toggleDebugMenu();
	void renderInfoVersion(bool b);

	void screenshot(int detail);
	void pause();
	void step();
	void play();
	void closePanel();
	void speedUp();
	void speedDown();
	void switchCameraControlType(int type);
	void renderInfoSwitchFPS();
	void setRenderInfoFPS(bool b);
	void renderInfoSwitchPhysx();
	void setRenderInfoPhysx(bool b);
	int getMapStability();
	bool IsDevBuild();
	float getCameraFov();
	
	std::string getCameraInfo();
	Rainbow::Vector3f getRelativePos();
	//float getRelativeRot();
	
	

	Rainbow::WorldPos getCameraPos();
	Rainbow::Quaternionf getCameraRot();
	void drawBounds(const CollideAABB* box/*, RGBA color, float duration = 0.0f*/);
	void drawCamera(Rainbow::WorldPos& position, Rainbow::Quaternionf& rot);

	void setEnablePHY(bool enable);
	bool isEnablePHY()
	{
		return m_EnableDebugPHY;
	}

	bool isEnableRender()
	{
		return m_RenderInfo;
	}
	//tolua_end

#ifdef IWORLD_DEV_BUILD
    Rainbow::SharePtr<Rainbow::MaterialInstance>& getActorTopViewMat() { return m_ActorTopViewMat; }
	//开启关闭水印
	void setWaterInfoOn(bool on);

	/* 开发环境水印相关方法 */

	// getCurSvnUserNameAndLastUpdateBinDirTimeForDevVersion
	void getCurSvnInfoForDevVersion(void);
	bool loadSvnVersionInfo();
	std::string getSvnUserName() { return m_svnUserName; }
	std::string getSvnLastUpdateTime() { return m_svnLastUpdateTime; }
	void renderWaterinfo();
#endif
private:
	void renderTerrBisect();
private:
	Rainbow::SharePtr<Rainbow::Texture2D>  m_TerrBisectImg;
	bool m_ShowTerrBisect;
	Rainbow::SharePtr<Rainbow::Texture2D>   m_TerrBisectRes;

	//MINIW::UIRenderer *m_UIRenderer;
	//MINIW::DebugRenderer* m_DebugRenderer;
	//void* m_DebugInfoFont;

	bool m_RenderInfo;

	bool m_RenderInfoFPS;

	bool m_RenderInfoPhysx;

	bool m_RenderDebugDraw;

	bool m_RenderInfoVersion = true;//显示版本信息

	bool m_ShowDebugMenu;

	bool m_EnableDebugPHY;

	bool m_EnablePerfOn;
	bool m_onKey; //一键控制
	int m_waterMarkOnOff;
	int m_DebugInfoOffsetX;//显示偏移值
	int m_DebugInfoOffsetY;//显示偏移值
#ifdef IWORLD_DEV_BUILD
	std::string m_svnUserName;
	std::string m_svnLastUpdateTime;
	//void* m_WaterInfoFont;
    Rainbow::SharePtr<Rainbow::MaterialInstance> m_ActorTopViewMat;
#endif
#if USE_METRIC_STATICS
    void* m_DebugFont;
#endif //USE_METRIC_STATICS
#ifdef BUILD_MINI_EDITOR_APP
	bool m_OpenPerfInfo = false;//codeby lich
#endif // BUILD_MINI_EDITOR_APP
}; //tolua_exports


EXPORT_DECLARE_GETMETHOD(DebugDataMgr)
