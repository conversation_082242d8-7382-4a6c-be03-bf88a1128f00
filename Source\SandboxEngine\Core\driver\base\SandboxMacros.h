#pragma once
/**
* file : SandboxMacros
* func : 沙盒包含宏等
* by : chenzihang
*/
#ifndef __SANDBOX_MACROS_H__
#define __SANDBOX_MACROS_H__

#include "OgreShared.h"
#include "SandboxType2Lua.h"
#include <stdlib.h>
#include <functional>
#include <math.h>
#include <algorithm>
#include <assert.h>
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#include <intrin.h>
#endif
#include "SandboxDriverModule.h"
#include "Optick/optick.h"

// 命名空间
#define NS_SANDBOX ::MNSandbox
#define NS_SANDBOX_BEG namespace MNSandbox {
#define NS_SANDBOX_END }
#define USE_NS_SANDBOX // 不要在头文件里using namespace MNSandbox，会导致引用文件跟其他命名空间内的对象冲突

#define SDB_MACROSSPLICE_BASE(a, b) a ## b
#define SDB_MACROSSPLICE(a, b) SDB_MACROSSPLICE_BASE(a, b)

// 函数压栈顺序
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
#define SANDBOXAPI __stdcall
#elif PLATFORM_ANDROID || PLATFORM_IOS || PLATFORM_OHOS
#define SANDBOXAPI
#else
#define SANDBOXAPI __attribute__((stdcall))
#endif

// 申明
namespace MNSandbox {
	extern EXPORT_SANDBOXDRIVERMODULE void SandboxToAssert(const char* condition, const std::string& msg, const char* szfile, unsigned line);
	extern EXPORT_SANDBOXDRIVERMODULE void SandboxToWarning(const char* condition, const std::string& msg, const char* szfile, unsigned line);
	extern EXPORT_SANDBOXDRIVERMODULE void SandboxToDebug(const std::string& msg, const char* szfile, unsigned line);
	extern EXPORT_SANDBOXDRIVERMODULE void SandboxToError(const char* condition, const std::string& msg, const char* szfile, unsigned line);

	namespace DefaultEmpty 
	{
		EXPORT_SANDBOXDRIVERMODULE extern std::string s_emptyString;/* 空字符串 */
	}
}

// SANDBOX_ASSERT
#if (defined _DEBUG || defined IWORLD_DEV_BUILD || defined DEDICATED_SERVER || defined BUILD_MINI_EDITOR_APP)
#define SANDBOX_ASSERTEX(expression, msg) do \
{ \
	if (!(expression)) \
		MNSandbox::SandboxToAssert((#expression), msg, __FILE_STRIPPED__, __LINE__); \
} while(false)
#else
#define SANDBOX_ASSERTEX(...) do {} while(false)
#endif
#define SANDBOX_ASSERT(expression) SANDBOX_ASSERTEX(expression, MNSandbox::DefaultEmpty::s_emptyString)

// SANDBOX_WARNING
#define SANDBOX_WARNINGEX(expression, msg) do \
{ \
	if (!(expression)) \
		MNSandbox::SandboxToWarning((#expression), msg, __FILE_STRIPPED__, __LINE__); \
} while(false)
#define SANDBOX_WARNING(expression) SANDBOX_WARNINGEX(expression, MNSandbox::DefaultEmpty::s_emptyString)


/* 常用功能开关 */
#define SANDBOX_HIGH_TICK // 沙盒高帧率
//#define OPEN_SANDBOXLOG // 强制输出沙盒打印
#ifdef IWORLD_DEV_BUILD
	#define SANDBOX_GAMEPLAYMODE_ENABLE // 启用沙盒游戏玩法模式
#endif
#define SANDBOX_CHUNK_STREAM_LOAD
//#define TEST_SANDBOX_LEGACY_TOOL//测试sandboxtool使用
#define NODEACTIVE_BY_TRIGGER // 触发式加载节点
//#define SDBSTREAM_DEV_OPEN
#define SDBSCRIPT_AS_SERVICE // 脚本由服务节点统一同步
//#define STREAMLOAD_DELAY_SETPARENT
#define NODEPACKET_USE_STREAM // 节点包使用二进制序列化
#define SWITCHMODE_OPTIMIZE 1 //切换模式优化开关
#define ENABLE_LOCALCLOUDSERVER 1	//激活本地云服功能
#define JOIN_LOCALCLOUDSERVER	1	//添加测试玩家加入本地云服功能,需要ENABLE_LOCALCLOUDSERVER开启(支持局域网替换联机测试)
#define ENABLE_LCSMODE 1			//激活本地云服观察者模式,需要ENABLE_LOCALCLOUDSERVER开启
#define OPEN_CLOUD_VIEW_RANGE //云服视野开关
//#define SANDBOX_SURVIVAL_LUASCRIPT_VM // 支持ugc 使用新节点的脚本能力
#define SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX 1 //生存actor 沙盒节点信息扩展支撑
#ifdef IWORLD_DEV_BUILD
	// #define SDB_NETMONITOR_DEV_OPEN // 网络同步流量监视
#endif
#define LUAFUNCTION_CALL_SYNC // lua function 同步执行
//#define SANDBOX_LUACALL_TRACEBACK // 能够打印出lua 调用C++ 反射的堆栈
//#define SANDBOX_DEBUG_SERVICE			// 沙盒节点debugservice
//#define SANDBOX_SCOPE_FUNCTION //LUA调用C++耗时统计
#define SANDBOX_SURVIVAL_LUASCRIPT_VM			// 沙盒、生存 兼容的lua虚拟机 开启
#define SANDBOX_TICK_LUA_GC_SWITCH // 沙盒 虚拟机开启每tick做 gc 的开关
//#if defined(BUILD_MINI_EDITOR_APP) || defined(DEDICATED_SERVER) // 先让 minigame 可以联云服 // 【暂时不开启，等4.25大版本再打开】
#define SANDBOX_REMOTEMSG_USE_STREAM // 远程消息使用二进制流
//#endif
#ifdef SANDBOX_REMOTEMSG_USE_STREAM
	#define SANDBOX_SCRIPTSERVICE_REMOTEMSG // 脚本汇总文件，直接通过协议发，不需要额外打包上传文件
#endif
#ifdef SANDBOX_REMOTEMSG_USE_STREAM
	#define SANDBOX_SYNCFILE_REMOTEMSG // 沙盒同步文件（非在线云服）不使用CDN，直接通过协议发
#endif
#define SANDBOX_DYNAMIC_LOAD_0315 // 改版动态加载
#define SANDBOX_BODYCOMP_SPLIT // bodycomponent 拆分
#define SANDBOX_USE_ASSETPACKET // 沙盒使用资源包
#define SDB_MULTI_SCENE_OPEN //多场景支持 

#define OPEN_SYNC_STATIC_NODE //同步优化-静态节点

/* 调试开关 */
#if (OGRE_PLATFORM == OGRE_PLATFORM_WIN32) // debug 用
//#define GM_PROFILER 1 // 使用GM 命令
//#define SANDBOX_DEV // 沙盒开发模式
//#define SANDBOX_DEBUG // 沙盒调试
//#define SANDBOX_USE_DEBUGINFO // 调试信息
//#define SANDBOX_USE_PROFILE // 性能测试2
//#define SANDBOX_USE_PROFILE_REFCNT // 性能测试：ref 对象的引用技术追溯
//#define SANDBOX_USE_PROFILE_REFCNT_LUASTCK // 性能测试：lua 引用堆栈
//#define SANDBOX_USE_PROFILE_USED // 性能测试：通过SANDBOX_NEW 和 SANDBOX_DELETE 创建的内存回收状态
//#define SANDBOX_USE_PROFILE_LOGCOST // 性能测试：一些逻辑的性能消耗输出
//#define SANDBOX_USE_PROFILE_NEW // 性能测试：统计SANDBOX_NEW 对象数量
//#define SANDBOX_USE_STACK // 调用堆栈输出
//#define SANDBOX_OPEN_ENTRYMAPCOST // 开启宏，进入地图花费时间
//#define SANDBOX_DEBUG_LISTENER // 调试listener
//#define SANDBOX_NODEPACKET_LOG // 节点包打印输出
//#define SANDBOX_SHOW_LUASTACK // 展示lua 调用堆栈

#if (defined(IWORLD_DEV_BUILD) || defined(WINDOWS_SERVER)) && defined USE_SANDBOX_DEV // 调试用
#define GM_PROFILER 1
#define SANDBOX_DEV
//#define SANDBOX_DEBUG
//#define SANDBOX_USE_DEBUGINFO
//#define SANDBOX_USE_PROFILE
//#define SANDBOX_USE_PROFILE_REFCNT
//#define SANDBOX_USE_PROFILE_USED
//#define SANDBOX_USE_PROFILE_LOGCOST
//#define SANDBOX_USE_PROFILE_NEW
//#define SANDBOX_USE_PROFILE_REFCNT_LUASTCK
#define SANDBOX_OPEN_ENTRYMAPCOST // 开启宏，进入地图花费时间
#define SANDBOX_USE_STACK // 调用堆栈输出
#define SANDBOX_DEBUG_LISTENER // 调试listener
#define SANDBOX_NODEPACKET_LOG // 节点包打印
#define SDBSTREAM_DEV_OPEN // 打印流调试信息
//#undef SDB_MULTI_SCENE_OPEN // 和0环境云服目前不兼容
#endif

#if (defined IWORLD_DEV_BUILD && !defined DEDICATED_SERVER)
	//#define SANDBOX_USE_DEBUGINFO // 默认debug  打开profile
	#define SDBSTREAM_DEV_OPEN
#endif
#endif


//Entry Map Cost
#ifdef SANDBOX_OPEN_ENTRYMAPCOST
#include "fps/statistics/SandboxStatistics.h"
#define ENTRYMAPCOST_STEP(Module) MNSandbox::Statistics::SandboxLoadCost_Step(Module)
#define ENTRYMAPCOST_FINISH() MNSandbox::Statistics::SandboxLoadCost_Finish()
#else
#define ENTRYMAPCOST_STEP(Module)
#define ENTRYMAPCOST_FINISH()
#endif


// 屏蔽宏依赖项
#ifndef SANDBOX_USE_PROFILE
#undef SANDBOX_USE_PROFILE_REFCNT
#undef SANDBOX_USE_PROFILE_USED
#undef SANDBOX_USE_PROFILE_LOGCOST
#undef SANDBOX_USE_PROFILE_NEW
#elif defined(SANDBOX_USE_PROFILE_REFCNT_LUASTCK)
//#define SANDBOX_SHOW_LUASTACK
#define SANDBOX_USE_PROFILE_USED
#define SANDBOX_USE_PROFILE_NEW
#define SANDBOX_USE_STACK
#define SANDBOX_USE_PROFILE_REFCNT
#elif defined(SANDBOX_USE_PROFILE_REFCNT)
#define SANDBOX_USE_PROFILE_USED
#define SANDBOX_USE_PROFILE_NEW
#define SANDBOX_USE_STACK
#endif

// Lua Profiler 相关宏
#ifdef BUILD_MINI_EDITOR_APP
#define LUA_PROFILER
#endif

// 需求功能开关
#define SANDBOX_CUSTOM_LOADING_SWITCH // LOADING 界面增加自定义区间给开发者 90~100



#if (defined SANDBOX_USE_PROFILE_LOGCOST)
#define SANDBOXPROFILING_BEG(Module) MNSandbox::Profile::ProfileCostPrintStack::PushTask(Module);
#define SANDBOXPROFILING_END() MNSandbox::Profile::ProfileCostPrintStack::PopTask();
#define SANDBOXPROFILING_LOG(Param, Msg) MNSandbox::Profile::ProfileCostPrint SDB_MACROSSPLICE(_profile, Param)(Msg);
#define SANDBOXPROFILING_DYNAMIC_LOG(Param, Msg) SANDBOXPROFILING_LOG(Param, Msg)
#define SANDBOXPROFILING_STEP(Param, Msg) SDB_MACROSSPLICE(_profile, Param).PrintStep(Msg);
#define SANDBOXPROFILING_FUNC(FunctionName) MNSandbox::Profile::ProfileCostPrint SDB_MACROSSPLICE(_profile_logcost_, __LINE__)(FunctionName);
#define SANDBOSPROFILING_DYNAMIC_FUNC(FunctionName) MNSandbox::Profile::ProfileCostPrint SDB_MACROSSPLICE(_profile_logcost_, __LINE__)(FunctionName);
#else
#define SANDBOXPROFILING_BEG(Module)
#define SANDBOXPROFILING_END()
#define SANDBOXPROFILING_LOG(Param, Msg) OPTICK_EVENT_DYNAMIC(Msg)
#define SANDBOXPROFILING_DYNAMIC_LOG(Param, Msg) OPTICK_EVENT_DYNAMIC((Msg).c_str())
#define SANDBOXPROFILING_STEP(Param, Msg)
#define SANDBOXPROFILING_FUNC(FunctionName) OPTICK_EVENT(FunctionName)
#define SANDBOSPROFILING_DYNAMIC_FUNC(FunctionName) OPTICK_EVENT_DYNAMIC((FunctionName).c_str())
#endif


#if (defined SANDBOX_USE_PROFILE_NEW)
#define SANDBOXPROFILE_PUSHDATA(ins, Type, key) MNSandbox::Profile::Manager::GetInstance().PushData(ins, MNSandbox::Profile::Manager::TYPE::Type, key)
#define SANDBOXPROFILE_POPDATA(ins) MNSandbox::Profile::Manager::GetInstance().PopData(ins)
#define SANDBOXPROFILE_NEW(TClass, ptr) (TClass*)MNSandbox::Profile::Manager::GetInstance().NewData((ptr), typeid(TClass).name(), sizeof(TClass))
#define SANDBOXPROFILE_DELETE(ptr) MNSandbox::Profile::Manager::GetInstance().DeleteData(ptr)
#else
namespace MNSandbox { namespace Statistics {
	extern EXPORT_SANDBOXDRIVERMODULE void* SandboxInstance_Add();
	extern EXPORT_SANDBOXDRIVERMODULE void SandboxInstance_Remove();
}}
#define SANDBOXPROFILE_PUSHDATA(...)
#define SANDBOXPROFILE_POPDATA(...)
#define SANDBOXPROFILE_NEW(TClass, ptr) (MNSandbox::Statistics::SandboxInstance_Add(), ptr)
#define SANDBOXPROFILE_DELETE(ptr) MNSandbox::Statistics::SandboxInstance_Remove()
#endif

// 网络同步流量监视
#ifdef SDB_NETMONITOR_DEV_OPEN
#define SDB_NETMONITOR_PTR MNSandbox::NSNetMonitor::NetMonitor::GetSingletonPtr()
#define SDB_NETMONITOR_BEGIN  {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnEnterMap();}}
#define SDB_NETMONITOR_END   {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnExitMap();}}
#define SDB_NETMONITOR_SEND(code,bytes,name,cnt)  {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnSendUdpPacket((code),(bytes),(name),(cnt));}}
#define SDB_NETMONITOR_RECV(code,bytes)  {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvUdpPacket((code),(bytes));}}
#define SDB_NETMONITOR_RECV_CUSTOM(bytes,name,uin) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvCustomUdpPacket((bytes),(name),(uin));}}
#define SDB_NETMONITOR_REMOTEOPT_SEND(nid,reflex,uid,bytes)  {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnSendRemoteOptMsg((nid),(reflex),(uid),(bytes));}}
#define SDB_NETMONITOR_REMOTEOPT_RECV(nid,reflex,uid,bytes)  {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvRemoteOptMsg((nid),(reflex),(uid),(bytes));}}

//#define SDB_NETMONITOR_NODE_RECV(bytes,origin) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvFullMsg((bytes),(origin));}}
//#define SDB_NETMONITOR_NODE_RECV_SINGLE(bytes) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvSingleMsg((bytes));}}
//#define SDB_NETMONITOR_NODE_RECV_SINGLE_PARSE(msg) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvSingleMsg_ParseInfo((msg));}}
//#define SDB_NETMONITOR_NODE_RECV_SINGLE_OVER {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvSingleMsgFinished();}}
//#define SDB_NETMONITOR_NODE_RECV_PHYSIC(bytes,nid) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnRecvPhysicDetailMsg((bytes),(nid));}}
#define SDB_NETMONITOR_SEND_PHYSIC(bytes,nid,uin) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnSendPhysicMsg((bytes),(nid),(uin));}}
#define SDB_NETMONITOR_SEND_NODE(bytes,msg,uin) {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->OnSendNodeMsg((bytes),(msg),(uin));}}
#define SDB_NETMONITOR_PRINT    {auto ptr = SDB_NETMONITOR_PTR;if(ptr){ptr->Show();}}
#else //
#define SDB_NETMONITOR_PTR nullptr
#define SDB_NETMONITOR_BEGIN
#define SDB_NETMONITOR_END
#define SDB_NETMONITOR_SEND(code,bytes,name,cnt)
#define SDB_NETMONITOR_RECV(code,bytes)
#define SDB_NETMONITOR_RECV_CUSTOM(bytes,name,uin)
#define SDB_NETMONITOR_REMOTEOPT_SEND(nid,reflex,uid,bytes)
#define SDB_NETMONITOR_REMOTEOPT_RECV(nid,reflex,uid,bytes)
//#define SDB_NETMONITOR_NODE_RECV(bytes,origin)
//#define SDB_NETMONITOR_NODE_RECV_SINGLE(bytes)
//#define SDB_NETMONITOR_NODE_RECV_SINGLE_PARSE(msg)
//#define SDB_NETMONITOR_NODE_RECV_SINGLE_OVER
//#define SDB_NETMONITOR_NODE_RECV_PHYSIC(bytes,nid)
#define SDB_NETMONITOR_SEND_PHYSIC(bytes,nid,uin)
#define SDB_NETMONITOR_SEND_NODE(bytes,msg,uin)
#define SDB_NETMONITOR_PRINT 
#endif

#ifdef SDB_MULTI_SCENE_OPEN
#define SDB_GET_START_MAPID(mid,uin)  { auto _mgrservice = GetCurrentSceneMgrService(); if (_mgrservice && MNSandbox::Config::GetSingleton().IsSandboxMode() ) { mid = _mgrservice->GetStartSceneId( uin );} }
#else 
#define SDB_GET_START_MAPID(mid,uin)
#endif

#if defined(SANDBOX_DEBUG_SERVICE)
#include "util/SandboxDebugInterface.h"
#define DEBUG_SERVICE_HOST_RUN(reflexkey, ...) MNSandbox::DebugServiceMsgHost(reflexkey, MNSandbox::ReflexTuple::Make(__VA_ARGS__))
#else
#define DEBUG_SERVICE_HOST_RUN(...)
#endif


///////////////////////////////////////////////////////////////////////


/*
* 新建对象
* 内存标记：
*	kMemSandbox					-- 沙盒
*	kMemSandboxNode				-- 沙盒节点
*	kMemSandboxReserved1		-- 沙盒alloc
*	kMemSandboxReserved2		-- （预留）
*	kMemSandboxReserved3		-- （预留）
*/
#define SANDBOX_NEW(T, ...)					SANDBOXPROFILE_NEW(T, ENG_NEW_LABEL(T, kMemSandbox)(__VA_ARGS__))
#define SANDBOX_NEW_LABEL(T, Label, ...)	SANDBOXPROFILE_NEW(T, ENG_NEW_LABEL(T, Label)(__VA_ARGS__))
#define SANDBOX_ORIGINAL_NEW(T, ...)		(new T(__VA_ARGS__))
#define SANDBOX_NEW_ARRAY(T, Num)			(new T[Num]) // 由于新引擎没有对new 数组提供特殊的宏封装，需要带初始化值的，可以直接使用new T[number]{}

/*
* 释放指针
*/
#define SANDBOX_DELETE(p)					do { if (p) { SANDBOXPROFILE_DELETE(p); ENG_DELETE_LABEL(p, kMemSandbox); } } while(0)
#define SANDBOX_DELETE_LABEL(p, Label)		do { if (p) { SANDBOXPROFILE_DELETE(p); ENG_DELETE_LABEL(p, Label); } } while(0)
#define SANDBOX_ORIGINAL_DELETE(p)			do { if (p) { delete (p); (p) = nullptr; } } while(0)
#define SANDBOX_DELETE_ARRAY(p)				do { if (p) { delete[](p); (p) = nullptr; } } while (0)

/*
* alloc 相关内存申请和释放
*/
#define SANDBOX_MALLOC(TSize)				(ENG_MALLOC_LABEL((TSize), kMemSandboxReserved1))
#define SANDBOX_REALLOC(ptr, TSize)			(ENG_REALLOC_LABEL((ptr), (TSize), kMemSandboxReserved1))
#define SANDBOX_FREE(ptr)					do { if(ptr) ENG_FREE_LABEL((ptr), kMemSandboxReserved1); (ptr)=nullptr; } while(false)

/*
* 释放
*/
#define SANDBOX_RELEASE(p)					do { if (p) { (p)->Release(); (p) = nullptr; } } while(false)


/*
* 打印
*/
#define SANDBOX_LOG(...)		MNSandbox::MNPrint(MNSandbox::PrintLevel::ELog, MNSandbox::ToString("log : ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_LUALOG(...)		MNSandbox::MNPrint(MNSandbox::PrintLevel::ELuaLog, MNSandbox::ToString("lua : ", __VA_ARGS__), "", 0)
#define SANDBOX_LUAERROR(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::ELuaError, MNSandbox::ToString("lua error: ", __VA_ARGS__), "", 0)
#define SANDBOX_DEBUGLOG(...)	MNSandbox::SandboxToDebug(MNSandbox::ToString(__VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_WARNINGLOG(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::EWarning, MNSandbox::ToString("warning: ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_ERRORLOG(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::EError, MNSandbox::ToString("error: ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_SYNCLOG(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::ESync, MNSandbox::ToString("sync: ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_FAILEDLOG(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::EFailed, MNSandbox::ToString("failed: ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)
#define SANDBOX_LUAPROFILERLOG(...)	MNSandbox::MNPrint(MNSandbox::PrintLevel::ELuaProfilerLog, MNSandbox::ToString("lua profiler: ", __VA_ARGS__), __FILE_STRIPPED__, __LINE__)

// 用于模板限定类型
// 需要 #include <type_traits>
#define TYPE_INT(T) typename std::enable_if<std::is_integral<T>::value>::type* // 类型是整型
#define TYPE_FLOAT(T) typename std::enable_if<std::is_floating_point<T>::value>::type* // 类型是浮点型号
#define TYPE_ARITHMETIC(T) typename std::enable_if<std::is_arithmetic<T>::value>::type* // 类型是算术类型
#define TYPE_IS_INT(T) TYPE_INT(T) = nullptr // 类型是整型
#define TYPE_IS_FLOAT(T) TYPE_FLOAT(T) = nullptr // 类型是浮点型号
#define TYPE_IS_ARITHMETIC(T) TYPE_ARITHMETIC(T) = nullptr // 类型是算术类型

//开启沙盒资源详细打印日志
#ifdef USE_STATISTICS_ASSSET_DETAIL
#define OPEN_STATISTICS_ASSSET_DETAIL
#endif


#endif
