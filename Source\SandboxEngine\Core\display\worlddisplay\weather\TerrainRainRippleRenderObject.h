#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

//using namespace Rainbow;
class TerrainRainRippleRenderer;
class RainRippleMeshData;

class TerrainRainRippleRenderObject : public Rainbow::RenderObject
{
public:
	explicit TerrainRainRippleRenderObject(TerrainRainRippleRenderer* component);
	~TerrainRainRippleRenderObject();

	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;


private:
	TerrainRainRippleRenderer* m_Renderer;
	Rainbow::MeshRenderData m_MeshRenderData;
	RainRippleMeshData* m_RenderBuffer;

	dynamic_array<Rainbow::DrawBuffersRange> m_DrawBuffersRanges;
};

