/*
*	file: SceneEffectManager
*	func: 在世界场景中绘制管理器
*	by: chenzh
*	time: 2021.1.22
*/
#ifndef __SCENE_EFFECT_MANAGER_H__
#define __SCENE_EFFECT_MANAGER_H__

#include "SceneEffectGeom.h"
#include "WorldRender.h"
#include <map>
#include "SandboxEngine.h"
#include "math/SandboxCoord3.h"
#include "world_types.h"
#include "SandboxEffectObject.h"

class WCoord;
enum CURVEFACEMTLTYPE;

class EXPORT_SANDBOXENGINE SceneEffectManager;
// 管理器
class SceneEffectManager //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	SceneEffectManager(World* pWorld);
	virtual ~SceneEffectManager();

	// 更新
	void update(float dtime);

	// 创建场景特效线条
	SceneEffectID CreateSceneEffectLine(const WCoord& startpos, const WCoord& endpos, int stroke = 12, CURVEFACEMTLTYPE mtltype= CURVEFACEMTL_TEXWHITE, bool refreshFlag=true);
	SceneEffectID CreateSceneEffectLine();

	// 创建场景特效圆
	SceneEffectID CreateSceneEffectCircle(const MNSandbox::MNCoord3f& center, float radius, float ratio = 1.0, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, bool refreshFlag = true);
	SceneEffectID CreateSceneEffectCircle();
	//tolua_end

	SceneEffectID CreateSceneEffectRectangle();

	SceneEffectID CreateSceneEffectEllipse();

	//tolua_begin
	// 创建场景特效箭头
	SceneEffectID CreateSceneEffectArrow(MNSandbox::MNCoord3f arrowPos, MNSandbox::MNCoord3f arrowDir, DirectionType dir, float ratio = 1.0, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, bool refreshFlag = true);
	SceneEffectID CreateSceneEffectArrow();

	// 创建场景特效盒
	SceneEffectID CreateSceneEffectBox(const WCoord& startpos, const WCoord& endpos, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE, bool refreshFlag = true);
	SceneEffectID CreateSceneEffectBox();

	// 创建场景特效线框
	SceneEffectID CreateSceneEffectFrame(const WCoord& startpos, const WCoord& endpos, int stroke = 12, CURVEFACEMTLTYPE mtltype= CURVEFACEMTL_TEXWHITE, bool refreshFlag=true);
	SceneEffectID CreateSceneEffectFrame();

	// 创建沙盒选区效果
	SceneEffectID CreateSceneEffectSelectFrame(const WCoord& startpos, const WCoord& endpos, int stroke = 12, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, bool refreshFlag = true);
	SceneEffectID CreateSceneEffectSelectFrame();

	// 创建场景特效球
	SceneEffectID CreateSceneEffectSphere(const MNSandbox::MNCoord3f& pos, const MNSandbox::MNCoord3f& sphereDir, float radius, int accuracyGrade = 5, float ratio = 1.0, CURVEFACEMTLTYPE mtltype = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY, bool refreshFlag = true);
	SceneEffectID CreateSceneEffectSphere();
	//tolua_end

	SceneEffectID CreateSceneEffectSphereFrame();

	SceneEffectID CreateSceneEffectHemisphereFrame();

	SceneEffectID CreateSceneEffectConeFrame();

	SceneEffectID CreateSceneEffectDonutFrame();

	SceneEffectID CreateSceneEffectSpotLightFrame();

	SceneEffectID CreateSceneEffect(const SceneEffectShape& eSes);

	// 通过ID获取特效
	template<typename TType>
	TType* GetSceneEffect(SceneEffectID id)
	{
		auto iter = m_mapIdToEffect.find(id);
		if (iter == m_mapIdToEffect.end())
			return nullptr;

		return dynamic_cast<TType*>(iter->second);
	}
	SceneEffectGeom* GetSceneEffectGeom(SceneEffectID id)
	{
		auto iter = m_mapIdToEffect.find(id);
		return iter == m_mapIdToEffect.end() ? nullptr : iter->second;
	}

	//tolua_begin
	// 销毁场景特效
	void DestroySceneEffect(SceneEffectID id);

	// 移动场景特效
	void MoveSceneEffect(SceneEffectID id, const WCoord& topos);
	//tolua_end
protected:

	// 绘制
	void OnDraw();

	// 加入缓存
	SceneEffectID SpawnEffect(SceneEffectGeom* effect);

private:
	std::map<SceneEffectID, SceneEffectGeom*> m_mapIdToEffect;

	World* m_pWorld;

	static int ms_SceneEffectId;

}; //tolua_exports

#endif