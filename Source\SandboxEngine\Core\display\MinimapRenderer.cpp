
#include "MinimapRenderer.h"
#include "BlockScene.h"
#include "world.h"
#include "ChunkGenerator.h"
#include "WorldManager.h"
#include "coreMisc.h"
#include "OgreUtils.h"
#include "ChunkViewer.h"
#include "ui_scriptfunc.h"
#include "UICommon/UIModelView3DSceneOutter.h"
#include "chunk.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "AssetPipeline/AssetManager.h"
#include "Common/GameStatic.h"
#include "Graphics/ScreenManager.h"
#include "GfxDevice/GfxDeviceSetting.h"

using namespace MINIW;
using namespace Rainbow;
const int CUR_VIEW_RANGE = 2;

#define DRAW_IN_UI
const int DELAY_FRAME_NUM = 2;

MinimapRenderer::MinimapRenderer()
	: m_Range(64)
	, m_Yaw(45.0f)
	, m_Pitch(60.0f)
	, m_Dist(30000.0f)
	, m_Mode(0)
	, m_EnableRender(false)
	, m_Cur<PERSON>orld(nullptr)
	, m_UIModelView(nullptr)
	, m_Viewer(nullptr)
{
	m_Rect = MINIW::RectInt(0, 0, 256, 144);
	m_MapTex = Rainbow::MakeSharePtrWithLabel<Rainbow::Texture2D>(kMemTexture);
	ClearMapTex();

#ifndef DRAW_IN_UI
	GlobalCallbacks::Get().m_PreRenderCallbacks.Register<MinimapRenderer>(&MinimapRenderer::OnPreRender, this);
#endif
}

MinimapRenderer::~MinimapRenderer()
{
#ifndef DRAW_IN_UI
	GlobalCallbacks::Get().m_PreRenderCallbacks.Unregister<MinimapRenderer>(&MinimapRenderer::OnPreRender, this);
#endif
	enableRender(false);
}

void MinimapRenderer::setCenter(World *pworld, const WCoord &pos)
{
	if (!m_EnableRender)return;

	m_CurWorld = pworld;

	if(m_Mode == 0)
	{
		m_Center = pos;
		m_Center.y = m_CurWorld->getChunkProvider()->getSpawnMinY()*BLOCK_SIZE;
		m_Dist = 30000.0f;
		m_Range = 64;
	}
	else
	{
		WCoord s, e;
		m_CurWorld->getRangeXZ(s.x, s.z, e.x, e.z);
		WCoord spawnpt = GetWorldManagerPtr()->getSpawnPointEx(m_CurWorld)*BLOCK_SIZE;
		if(s.x == e.x)
		{
			s.x = spawnpt.x - CHUNK_SIZE_X*20;
			e.x = spawnpt.x + CHUNK_SIZE_X*20;
		}
		if(s.z == e.z)
		{
			s.z = spawnpt.z - CHUNK_SIZE_X*20;
			e.z = spawnpt.z + CHUNK_SIZE_X*20;
		}
		m_Center.x = (s.x + e.x)/2;
		m_Center.z = (s.z + e.z)/2;
		m_Center.y = m_CurWorld->getChunkProvider()->getSpawnMinY()*BLOCK_SIZE;
		//m_Dist = (e.z-s.z)/2 * Cotan(m_GodCamera->getFov()/2) * 1.2f;
		m_Dist = 600*BLOCK_FSIZE;
		m_Range = Rainbow::Max(e.x-s.x, e.z-s.z) / (BLOCK_SIZE*2);
	}

	if (!m_Viewer)
	{
		m_Viewer = ENG_NEW(ChunkViewer)();
		m_Viewer->enterWorld(m_CurWorld, m_Center, CUR_VIEW_RANGE);
	}
	m_Viewer->updateChunkView(m_CurWorld, m_Center, CUR_VIEW_RANGE);

	if (!m_UIModelView)
	{
		// m_UIModelView = ENG_NEW_LABEL(Rainbow::UIModelView3D, kMemScene);
		m_UIModelView = ENG_NEW_LABEL(Rainbow::UIModelView3DSceneOutter, kMemScene);
		m_UIModelView->SetOutterGameScene(m_CurWorld->getScene());
	}
	Rectf renderRect = GetScreenManagerPtr()->GetRect();
	m_Rect.m_Right = renderRect.GetWidth();
	m_Rect.m_Bottom = renderRect.GetHeight();

	Rainbow::RectInt rect;
	rect.SetTop(m_Rect.m_Top);
	rect.SetLeft(m_Rect.m_Left);
	rect.SetBottom(m_Rect.m_Bottom);
	rect.SetRight(m_Rect.m_Right);
	m_UIModelView->InitModelView(rect);

	if (m_Mode == 0)
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(m_Yaw);
		dir = Rainbow::Vector3f(-dir.x * Cos(m_Pitch), Rainbow::Sin(m_Pitch), -dir.z * Cos(m_Pitch));
		WorldPos goal = m_Center.toWorldPos();
		WorldPos eyepos = goal + dir * m_Dist;

		m_UIModelView->GetCamera()->LookAt(eyepos.toVector3(), goal.toVector3(), Rainbow::Vector3f(0, 1.0f, 0));
		m_UIModelView->GetCamera()->SetVerticalFieldOfView(30.0f);
	}
	else
	{
		WorldPos goal = m_Center.toWorldPos();
		WorldPos eyepos = goal;
		eyepos.y += m_Dist;

		m_UIModelView->GetCamera()->LookAt(eyepos.toVector3(), goal.toVector3(), Rainbow::Vector3f(0, 0, 1.0f));
		m_UIModelView->GetCamera()->SetOrthographicSize(m_Range * BLOCK_FSIZE * 3);
	}

	if (m_DelayCount < DELAY_FRAME_NUM)
	{
		++m_DelayCount;
	}

	m_UIModelView->GetCamera()->ClearAllLayerMask();
	m_UIModelView->GetCamera()->SetLayerMask(static_cast<LayerIndex>(kLayerMinimap), true);
	m_UIModelView->GetCamera()->SetAspect(float(m_Rect.getWidth()) / m_Rect.getHeight());
	m_UIModelView->GetCamera()->SetNear(10000.0f);
	m_UIModelView->GetCamera()->SetFar(50000.0f);
	m_UIModelView->GetCamera()->SetRenderFeatureFlags(kRenderFeatureNone);

	BlockScene* pscene = m_CurWorld->getScene();
	pscene->PrepareForMinimap(CoordDivBlock(m_Center), m_Range);
}

void MinimapRenderer::projectPointToScreen(int &x, int &y, const WCoord &inputpos, World *pworld)
{
	if (!m_UIModelView) 
		return;
	
	Camera* camera = m_UIModelView->GetCamera();
	if (!camera)
		return;
	
	WCoord pos = inputpos;
	pos.y = pworld->getChunkProvider()->getSpawnMinY()*BLOCK_SIZE;

	WCoord cc = CoordDivSection(m_Center);
	WCoord cp = CoordDivSection(pos);

	int crange = m_Range/SECTION_BLOCK_DIM;

	Vector3f pointInScreen;
	
	if(Abs(cp.x-cc.x)>crange || Abs(cp.z-cc.z)>crange)
	{
		const float dx = float(pos.x - m_Center.x);
		const float dz = float(pos.z - m_Center.z);
		const float r = Sqrt(dx * dx + dz * dz);
		const float cos = dx / r;
		const float sin = dz / r;

		float t = Rainbow::MAX_FLOAT;
		if (cos < 0)
		{
			const float tmp = ((cc.x - crange) * SECTION_BLOCK_DIM * BLOCK_SIZE - m_Center.x) / cos;
			if (tmp < t) t = tmp;
		}
		else if (cos > 0)
		{
			const float tmp = ((cc.x + crange + 1) * SECTION_BLOCK_DIM * BLOCK_SIZE - m_Center.x) / cos;
			if (tmp < t) t = tmp;
		}

		if (sin < 0)
		{
			const float tmp = ((cc.z - crange) * SECTION_BLOCK_DIM * BLOCK_SIZE - m_Center.z) / sin;
			if (tmp < t) t = tmp;
		}
		else if (sin > 0)
		{
			const float tmp = ((cc.z + crange + 1) * SECTION_BLOCK_DIM * BLOCK_SIZE - m_Center.z) / sin;
			if (tmp < t) t = tmp;
		}

		WCoord newpos = m_Center + WCoord(int(t * cos), 0, int(t * sin));
		pointInScreen = camera->WorldToScreenPoint(newpos.toVector3());
	}
	else
	{
		//m_GodCamera->pointWorldToWindow(viewx, viewy, pos.toWorldPos());
		pointInScreen = camera->WorldToScreenPoint(pos.toVector3());
	}

	const Rectf physicsScreenSize = GetScreenManager().GetRect();
	const WindowDesc& designWinDesc = GetGfxDeviceSetting().GetWindowDesc();

	//change physics screen position to design position
	x = (pointInScreen.x / physicsScreenSize.GetWidth()) * designWinDesc.width;
	y = (pointInScreen.y / physicsScreenSize.GetHeight()) * designWinDesc.height;

	//x = int(viewx * Root::GetInstance().getClientWidth() / GetScreenUIScale());
	//y = int(viewy * Root::GetInstance().getClientHeight() / GetScreenUIScale());
	//
	//x = pointInScreen.x;
	//y = pointInScreen.y;
}


void MinimapRenderer::OnPreRender()
{
	doRender();
}


Rainbow::Texture2D* MinimapRenderer::GetTexture()
{
	if (!m_EnableRender || !m_CurWorld || !m_UIModelView) 
		return nullptr;

	if (m_DelayCount < DELAY_FRAME_NUM)
	{
		return m_MapTex.Get();
	}

	RenderTexture* renderTexture = m_UIModelView->GetRenderTextureHolder()->GetRenderTexture();
	if (!renderTexture)
	{
		return m_MapTex.Get();
	}
	
	Image image(kMemTempAlloc);
	int width = renderTexture->GetGPUWidth();
	int height = renderTexture->GetGPUHeight();
	image.SetImage(width, height, kTexFormatRGBA32, true);
	RenderTexture::SetActive(renderTexture, 0, kCubeFaceUnknown, 0, RenderTexture::kFlagNone);
	bool ret = GetGfxDevice().ReadbackImage(image, 0, 0, width, height, 0, 0);
	if (!ret)
	{
		return m_MapTex.Get();
	}

	//@comment: need invert alpha to convert overblending texture to regular texture
	for (int y = 0; y < height; ++y)
	{
		for (int x = 0; x < width; ++x)
		{
			UInt8* pixel = image.GetRowPtr(y) + x * GetBytesFromTextureFormat(kTexFormatRGBA32);
			ColorRGBA32& data = *(ColorRGBA32*)pixel; //TODO CHECK 
			data.a = 255 - data.a;
		}
	}
	m_MapTex->SetImage(image, kTextureInitNone);
	return m_MapTex.Get();
}


void MinimapRenderer::doRender()
{
	if (!m_EnableRender || !m_CurWorld || !m_UIModelView)
		return;

#ifndef DRAW_IN_UI

	if (!m_Material)
	{
		m_Material = GetMaterialManager().LoadFromFile("Materials/UIRenderTarget.templatemat")->CreateInstance();
	}

	Rainbow::SharePtr<Rainbow::RenderTextureHolder> hold = m_UIModelView->GetRenderTextureHolder();
	Rainbow::SharePtr<Rainbow::Texture> tex = Rainbow::SharePtr<Rainbow::Texture>::NativeToSharePtr(hold->GetRenderTexture());
	if (m_Material)
	{
		m_Material->SetTexture("_MainTex", tex);
	}
	
	tex->Release();

	Rainbow::RectInt rect;
	rect.SetTop(m_Rect.m_Top);
	rect.SetLeft(m_Rect.m_Left);
	rect.SetBottom(m_Rect.m_Bottom);
	rect.SetRight(m_Rect.m_Right);

	GetUIRenderManager().DrawRect(RectIntToRectf(rect), ColorRGBAf::white, m_Material.Get());

#endif

	/*if(m_BGImg == 0)
	{
		m_BGImg = m_UIRenderer->CreateTexture("ui/mobile/texture2/bigtex/img_minmap_beijing.png");
	}
	m_UIRenderer->renderClearScreenTexture(m_BGImg, 0, 0, 256, 144);

	BlockScene *pscene = static_cast<BlockScene *>(m_pScene);

	if(m_Mode == 0)
	{
		Rainbow::Vector3f dir = Yaw2FowardDir(m_Yaw);
		dir = Rainbow::Vector3f(-dir.x*Cos(m_Pitch), Rainbow::Sin(m_Pitch), -dir.z*Cos(m_Pitch));
		WorldPos goal = m_Center.toWorldPos();
		WorldPos eyepos = goal + dir*m_Dist;

		m_GodCamera->setLookAt(eyepos, goal, Rainbow::Vector3f(0,1.0f,0));
		m_GodCamera->setDepth(m_Dist/30.0f, m_Dist*100.0f/30.0f);
		m_GodCamera->setFov(30.0f);
	}
	else
	{
		WorldPos goal = m_Center.toWorldPos();
		WorldPos eyepos = goal;
		eyepos.y += WorldPos::Flt2Fix(m_Dist);

		m_GodCamera->setLookAt(eyepos, goal, Rainbow::Vector3f(0,0,1.0f));
		m_GodCamera->setDepth(100*BLOCK_FSIZE, m_Dist);
		m_GodCamera->setOrtho(m_Range*BLOCK_FSIZE*3);
	}

	m_GodCamera->setRatio(float(Root::GetInstance().getClientWidth())/Root::GetInstance().getClientHeight());
	m_GodCamera->update(0);
	m_GodCamera->m_pCullresult->startCull(m_GodCamera);

	pscene->onCullForMinimap(m_GodCamera, CoordDivBlock(m_Center), m_Range);

	m_GodCamera->m_pCullresult->addRenderable(MapMarkingMgr::GetInstancePtr(), RL_SCENE, NULL);

	ShaderEnvData sceneenv;
	RenderResult(sceneenv, m_GodCamera->m_pCullresult, m_pTarget, 0, 0, 1.0f, 0, 0, NULL, RU_GENERAL);
	*/
}

void MinimapRenderer::clearResources()
{ 
	if(m_CurWorld == NULL)
		return;

	BlockScene* pscene = m_CurWorld->getScene();
	World* pworld = pscene->m_pWorld;
	if (pworld == NULL)
	{
		return;
	}
		
	for(size_t i=0; i< pworld->m_ChunkArray.size(); i++)
	{
		Chunk *pchunk = pworld->m_ChunkArray[i];
		if(pchunk == NULL)
			return;

		for(int s=0; s<CHUNK_SECTION_DIM; s++)
		{
			Section *ps = pchunk->getIthSection(s);
			if(ps) ps->clearMinimapMesh();
		}
	}
}

void MinimapRenderer::clearSceneWorld()
{
	//if(m_pScene == NULL) return;
	//static_cast<BlockScene *>(m_pScene)->m_pWorld = NULL;
}


bool MinimapRenderer::IsEnable()
{
	return m_EnableRender;
}

void MinimapRenderer::enableRender(bool b)
{
	m_EnableRender = b;
	m_DelayCount = 0;
	if (m_EnableRender)
	{
	}
	else
	{
		if (m_Viewer)
		{
			m_Viewer->leaveWorld(m_CurWorld);
		}
		if (m_UIModelView)
		{
			m_UIModelView->SetOutterGameScene(nullptr);
		}

		clearResources();
		ClearMapTex();
		m_CurWorld = nullptr;
	}
	ENG_DELETE(m_Viewer);
	ENG_DELETE_LABEL(m_UIModelView, kMemScene);
}

void MinimapRenderer::ClearMapTex()
{
	Image image(kMemTempAlloc);
	int width = 4;
	int height = 4;
	image.SetImage(width, height, kTexFormatRGBA32, true);
	for (int y = 0; y < height; ++y)
	{
		for (int x = 0; x < width; ++x)
		{
			UInt8* pixel = image.GetRowPtr(y) + x * GetBytesFromTextureFormat(kTexFormatRGBA32);
			ColorRGBA32& data = *(ColorRGBA32*)pixel;
			data.a = 0;
		}
	}
	m_MapTex->SetImage(image, kTextureInitNone);
}

GameStatic<MinimapRenderer> s_MinimapRenderer(kInitStatic, kGameStaticMiniMapRenderer);
MinimapRenderer* MinimapRenderer::GetInstancePtr()
{
	return s_MinimapRenderer;
}
