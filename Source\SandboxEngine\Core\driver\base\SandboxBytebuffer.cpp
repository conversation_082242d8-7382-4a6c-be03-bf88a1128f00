#include <memory>
#include <sstream>
#include <iostream>
#include <string.h>
#include "SandboxBytebuffer.h"
#include "SandboxUnit.h"

namespace MNSandbox {

	static unsigned isLittleEndian(void)
	{
		const union { unsigned int i; unsigned char c[4]; } one = { 1 };
		return one.c[0];
	}


	ByteBuffer::ByteBuffer(int defaultsize /*= 16*/) :m_buffer(nullptr), m_read_pos(0), m_write_pos(0), m_size(defaultsize)
	{
		//assert(m_size > 0);
		m_buffer = (char*)SANDBOX_MALLOC(m_size);
		if (!m_buffer) {
			m_size = 0;
		}
	}

	ByteBuffer::~ByteBuffer()
	{
		if (m_buffer) {
			SANDBOX_FREE(m_buffer);
			m_buffer = nullptr;
			m_size = 0;
		}
	}

	bool ByteBuffer::SeekRead(int pos)
	{
		if (pos < 0 || pos > m_write_pos)
			return false;
		m_read_pos = pos;
		return true;
	}

	bool ByteBuffer::SeekWrite(int pos)
	{
		if (pos < 0 || pos > m_size)
			return false;
		m_write_pos = pos;
		return true;
	}

	bool ByteBuffer::ReadChars(char* str, int len)
	{
		//assert(len > 0);
		if (m_read_pos + len <= m_write_pos) {
			memcpy(str, m_buffer + m_read_pos, len);
			m_read_pos += len;
			return true;
		}
		return false;
	}

	bool ByteBuffer::ReadChar(char& c)
	{
		if (m_read_pos + 1 <= m_write_pos) {
			c = m_buffer[m_read_pos];
			m_read_pos++;
			return true;
		}
		return false;
	}

	bool ByteBuffer::ReadShort(short& s)
	{
		if (m_read_pos + 2 <= m_write_pos) {

			unsigned char* pbyte = (unsigned char*)(m_buffer + m_read_pos);
			if (isLittleEndian())
				s = (short)((*pbyte) | (*(pbyte + 1) << 8));
			else
				s = (short)((*pbyte << 8) | (*(pbyte + 1)));

			m_read_pos += 2;
			return true;
		}
		return false;
	}

	bool ByteBuffer::ReadInt(int& i)
	{
		if (m_read_pos + 4 <= m_write_pos) {
			unsigned char* pbyte = (unsigned char*)(m_buffer + m_read_pos);
			if (isLittleEndian())
				i = (*pbyte) | (*(pbyte + 1) << 8) | (*(pbyte + 2) << 16) | (*(pbyte + 3) << 24);
			else
				i = (*pbyte << 24) | (*(pbyte + 1) << 16) | (*(pbyte + 2) << 8) | (*(pbyte + 3));
			m_read_pos += 4;
			return true;
		}
		return false;
	}

	bool ByteBuffer::WriteShort(short s)
	{
		if (m_write_pos + 2 > m_size) {
			if (!reallocate()) return false;
		}
		//
		if (isLittleEndian()) {
			m_buffer[m_write_pos] = (char)s;
			m_buffer[m_write_pos + 1] = (char)(s >> 8);
		}
		else {
			m_buffer[m_write_pos + 1] = (char)s;
			m_buffer[m_write_pos] = (char)(s >> 8);
		}
		//
		m_write_pos += 2;
		return true;
	}

	bool ByteBuffer::WriteInt(int i)
	{
		if (m_write_pos + 4 > m_size) {
			if (!reallocate()) return false;
		}
		//
		if (isLittleEndian()) {
			m_buffer[m_write_pos + 0] = (char)i;
			m_buffer[m_write_pos + 1] = (char)(i >> 8);
			m_buffer[m_write_pos + 2] = (char)(i >> 16);
			m_buffer[m_write_pos + 3] = (char)(i >> 24);
		}
		else {
			m_buffer[m_write_pos + 3] = (char)i;
			m_buffer[m_write_pos + 2] = (char)(i >> 8);
			m_buffer[m_write_pos + 1] = (char)(i >> 16);
			m_buffer[m_write_pos + 0] = (char)(i >> 24);
		}
		//
		m_write_pos += 4;
		return true;
	}

	bool ByteBuffer::WriteChar(char c)
	{
		if (m_write_pos + 1 > m_size) {
			if (!reallocate()) return false;
		}
		//
		m_buffer[m_write_pos] = c;
		//
		m_write_pos += 1;
		return true;
	}

	bool ByteBuffer::WriteChars(const char* str, int len)
	{
		while (m_write_pos + len > m_size) {
			if (!reallocate()) return false;
		}
		//
		memcpy(m_buffer + m_write_pos, str, len);
		//
		m_write_pos += len;
		return true;
	}

	bool ByteBuffer::WriteStringStream(std::stringstream& stream, int len)
	{
		while (m_write_pos + len > m_size) {
			if (!reallocate()) return false;
		}
		stream.read((char*)(m_buffer + m_write_pos), len);
		m_write_pos += len;
		return true;
	}

	void ByteBuffer::Reset()
	{
		m_read_pos = 0;
		m_write_pos = 0;
	}

	bool ByteBuffer::Resize(int s)
	{
		if (s < m_size)
			return false;

		if (s == m_size) return true;

		char* _buffer = (char*)SANDBOX_REALLOC(m_buffer, s);
		if (!_buffer) {
			MNSandbox::SandboxToError("ByteBuffer Resize failed!", MNSandbox::ToString(s), __FILE_STRIPPED__, __LINE__);
			return false;
		}

		m_buffer = _buffer;
		m_size = s;
		return true;
	}

	bool ByteBuffer::reallocate()
	{
		return Resize(m_size * 1.5);
	}
}
