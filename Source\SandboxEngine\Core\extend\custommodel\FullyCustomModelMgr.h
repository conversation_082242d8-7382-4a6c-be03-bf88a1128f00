#pragma once

#include "Utilities/Singleton.h"
#include "world_types.h"
#include "FullyCustomModel.h"
#include "CoreCommonDef.h"
#include <queue>
#include "BaseClass/SharePtr.h"
#include "SandboxEngine.h"


class IClientPlayer;
struct ResourceFolderSetInfo;
class WorldContainer;
class PackingFullyCustomModelMgr;
class IconDesc;
namespace Rainbow
{
	class TextureRenderGen;
	class ModelData;
	class IActorBody;
};


//tolua_begin
enum FCM_Downlad_State
{
	FCM_DOWNLOAD_WAIT,
	FCM_DOWNLOAD_REQ_URL,
	FCM_DOWNLOAD_ING,
	FCM_DOWNLOAD_FINISH,
};
//tolua_end

//tolua_begin
enum
{
	FCM_CLIENT_DOWNLOAD_BY_HOST_SYNC,		//客机下载主机同步过来的资源
	FCM_CLIENT_DOWNLOAD_BY_CLIENT_EDIT,	//客机下载编辑方块的模型资源
	FCM_HOST_DOWNLOAD_BY_EDIT_FINISH,	//主机下载客机编辑完成的模型资源
};
//tolua_end

//tolua_begin
enum
{
	FCM_HOST_UPLOAD_BY_CLIENT_SYNC,				//主机上传资源因为客机同步需要下载资源
	FCM_HOST_UPLOAD_BY_CLIENT_EDIT,			//主机上传资源因为客机编辑模型时需要下载最新的资源
	FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH,	//客机上传资源因为主机要下载客机编辑完成后的模型资源
};
//tolua_end

//tolua_begin
enum FCD_SAVE_TYPE
{
	HOST_SAVE,
	CLIENT_SAVE,
};
//tolua_end
//tolua_begin
//与枚举组 BLOCK_MODEL 有通用值，添加避免值冲突
enum CLOSE_EDIT_FCM_UI_TYPE
{
	/**
	@brief	eFcmSaveType
	 */
	NORMAL_CLOSE = -2,
	/**
	@brief	eFcmSaveType
	 */
	ONLY_SAVE = -1,
	/**
	@brief	eFcmSaveType
	 */
	SAVE_EXAMPLE = 99,
	/**
	@brief	eFcmSaveType
	 */
	SAVE_AND_CREATE_BLOCK = 100,
	/**
	@brief	eFcmSaveType
	 */
	SAVE_AND_CREATE_ITEM = 101,
	/**
	@brief	eFcmSaveType
	 */
	SAVE_AND_CREATE_ACTOR = 102,
	/**
	@brief	eFcmSaveType
	 */
	SAVE_EDITOR = 103,
};
//tolua_end

//tolua_begin
enum PRE_OPEN_EDIT_FCM_UI_STATE
{
	PRE_OPEN_UPLOADING,
	PRE_OPEN_UPLOAD_FAIL,
	PRE_OPEN_DOWNLOADING,
	PRE_OPEN_DOWNLOAD_FAIL,
};
//tolua_end

//tolua_begin
enum
{
	ONLY_CLOSE_FCM_UI_SUCCESS,
	SAVE_CLOSE_FCM_UI_SUCCESS,
	CLOSE_FCM_UI_ERR,
	SAVE_CLOSE_FCM_UI_UNLOAD_FAIL,
	SAVE_CLOSE_FCM_UI_DOWNLOAD_MISS,
	SAVE_CLOSE_FCM_UI_DOWNLOAD_FAIL,
	SAVE_CLOSE_FCM_UI_HOST_LOAD_FAIL,
};
//tolua_end

//tolua_begin
struct FCM_Res_Info
{
	std::string downloadurl;
	std::vector<int> waitUrlUins;  //等待下载url的uins
	short version;					//版本信息
	int type;						//FCM_HOST_UPLOAD_BY_CLIENT_SYNC FCM_HOST_UPLOAD_BY_CLIENT_EDIT FCM_CLIENT_UPLOAD_BY_CLENT_EDIT_FINISH
};
//tolua_end

//tolua_begin
struct FCM_Download_Info
{
	int taskid;
	FCM_Downlad_State state;
	std::string downloadurl;
	std::string filename;
	short version;
	std::string externdata;
	std::string skey;
	int trytimes;

	FCM_Download_Info() : taskid(0), state(), downloadurl(""), filename(""), version(0), externdata(""), skey(""),
		trytimes(5)
	{
	}
};
//tolua_end

//tolua_begin
struct InComplete_Icon
{
	int missmodelnum;  //缺少的模型数据
	int tryrefreshtime;  //尝试更新图标次数
};
//tolua_end

//TODO: 2022-04-22 17:45:42: 有较多待重构的地方
//TODO: 2022-06-24 存在大部分中间封装的代码，可通过直接调用ModelData来实现
class EXPORT_SANDBOXENGINE FullyCustomModelMgr;
class FullyCustomModelMgr //tolua_exports
	: public Rainbow::Singleton<FullyCustomModelMgr> //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	FullyCustomModelMgr();
	~FullyCustomModelMgr();
	//tolua_end

	//====================================文件start====================================//
public:
	//加载地图内完全自定义模型
	//tolua_begin
	void loadMapFullyCustomModelData(long long owid, int realowneruin, int specialType = 0);
	void loadOneModFullyCustomRes(std::string modelkey, int realowneruin, std::string modroot, bool islibmod = false, bool ignorecheck = false);
	void loadOneModFullyCustomRes(long long owid, std::string modelkey, int realowneruin, bool ignorecheck = false); // 资源整合加载单独的一个文件资源
	void loadResFullyCustomModel();
	void loadEquipFullyCustomModel(bool isReload = false);
	void loadResFullyCustomModel(int eFcmModelClass, std::string filename);
	void loadResFullyCustomModelByPath(int type, std::string filename, std::string filepath);
	//客机编辑后保存
	void clientSaveEditModelByResult(int result);
	bool removeOldFullyCustomModel(int eFcmImportType, std::string skey);
	void removeEditFailModel();
	bool isEdited(std::string skey = "");
	void reEncryptFullyCustomModelData(long long owid, int olduin, int newuin, std::string authnickname, int specialType = 0);
	/*
		预加载fully模型
	*/
	void preMapFullyCustomModelData(long long owid, int specialType = 0);
	/*
		加载一个fully模型数据
	*/
	void loadOneMapFullyCustomModelData(long long owid, int realowneruin, std::string& filename, int specialType = 0);
	//客机编辑时，检查一下编辑模型的版本号
	FCM_Res_Info* checkVersionByClientEdit(std::string skey);
	bool loadCusMotion(std::string skey, int motionid = -1);
	std::string saveCusMotion(int motionid = -1, std::string motionname = "");
	bool isDownloadFCM(std::string filename);
	//tolua_end
	FullyCustomModel* getOrCreate(const std::string& skey, int eFcmImportType = NEW_NORMALE_FCM_MODEL);
	FullyCustomModel* createNew(const std::string& skey, int eFcmImportType = NEW_NORMALE_FCM_MODEL);
	FullyCustomModel* createEmpty();
	FullyCustomModel* copyExample(const std::string& strKeyExample, const std::string& strKeySave, bool relativePath);
	FullyCustomModel* copyAndSave(FullyCustomModel* fcmSrc, const std::string& skey, bool relativePath);
	std::string copyEditModelFile(IClientPlayer* player, std::string srcSkey);
	/*资源中心重构新加接口 时间：2021.3.17版本内容 作者：keguanqiang*/
	void getResPath(int libtype, std::string filename, bool ispacking, char* path);
	//====================================文件end====================================//


	//====================================fcm start====================================//
public:
	//tolua_begin
	//设置要编辑的模型-（新模型、示例模型、导入其它的自定义模型）
	bool setCurEditModel(const std::string& skey, int eFcmImportType = NEW_NORMALE_FCM_MODEL);
	FullyCustomModel* getFullyCustomModelByIndex(int type, int index);
	int getFullyCustomModelNum(int type);
	//UI编辑相关接口---------------begin
	Rainbow::IActorBody* getCurEditActorBody(bool needcreate = true);
	FullyCustomModel* getCurEditFullyCustomModel();
	//设置选中编辑的骨骼播放一个特效
	void setCurSelectBoneEffect(std::string name);
	//设置选中编辑的模型高亮
	void setModelOverlayColor(std::string name, bool show);
	//UI编辑相关接口------------end
	std::string saveCurEditModel(IClientPlayer* player, FCD_SAVE_TYPE type, std::string name, std::string desc, int savetype);
	//TODO: 2022-04-02 10:15:27: 工具专用。待关联账号
	bool saveEditModel(std::string skey, CLOSE_EDIT_FCM_UI_TYPE savetype);
	//当前编辑的模型生成相对应的（方块、道具、生物）
	//operatetype -1普通保存 BLOCK_MODEL生成方块、WEAPON_MODEL生成道具、ACTOR_MODEL生成生物
	void createObjectByCurEditModel(IClientPlayer* player, const WCoord& containerpos, int type, std::string skey, std::string name, std::string desc);
	Rainbow::Entity* getEntity(std::string skey, bool bUpdateWhenCustomModelReady = false, bool bindingPack = false);
	Rainbow::Entity* getEntityByResClass(int resClass, std::string skey, bool bUpdateWhenCustomModelReady = false, bool bindingPack = false);
	PackingFullyCustomModelMgr* getPackingFCMMgr();
	void	clearMapFullyModels();
	void	clearResFullyModels();
	void	clearEquipResFullyModels();
	void	clearPreviewResFullyModels();
	FullyCustomModel* findFullyCustomModel(int eFcmModelClass, std::string skey, bool toFindPackingFcm = false);
	//tolua_end
	Rainbow::Entity* getCurEntity();
	Rainbow::Model* getCurModel();
	Rainbow::SharePtr<Rainbow::ModelData> getCurModelData();
	void setCurEditModel(FullyCustomModel* fcm);
	void addFcm(FullyCustomModel* fcm, int eFcmImportType);
	bool isEditorFcm(FullyCustomModel* fcm);
	std::vector<FullyCustomModel*>* getVectorFcms(int eFcmModelClass);
	
	//====================================fcm end====================================//


	//====================================UI start====================================//
public:
	//tolua_begin
	void update(float dtime);
	void tick();
	void closeEditModelUI(IClientPlayer* player, int operatetype, WCoord containerpos = WCoord(0, -1, 0), std::string name = "", std::string desc = "");
	//关闭编辑UI界面通知给主机
	void syncCloseUI2Host(int operatetype, std::string url = "", int version = 1, std::string skey = "", std::string name = "", std::string desc = "");
	//主机通知编辑完成的客机，可以关闭UI界面了
	void syncCloseUI2Client(int uin, int result, WCoord pos = WCoord(0, -1, 0), int mapid = 0, std::string skey = "");
	//主机通知要编辑的客机，可以打开UI界面编辑了
	void openEditUI2Client(WorldContainer* container, std::string url, bool edited, int version, int result);
	//通知客机打开等待自定义模型资源更新界面
	void syncPreOpenEditUI2Client(int uin, int state);
	void refreshIcon();
	Rainbow::SharePtr<Rainbow::Texture2D> getModelIcon(std::string skey, int modeltype, int& u, int& v, int& width, int& height, int& r, int& g, int& b);
	std::unordered_map<std::string, IconDesc*>& getIconDescs() { return m_IconDescs; }
	int getFreeId(int type);
	//tolua_end
	Rainbow::SharePtr<Rainbow::Texture2D> genModelTexture(std::string skey, int& missmodelnum, int modeltype, int& modellibtype, int lastmissmodelnum = 0, Rainbow::SharePtr<Rainbow::Texture2D> result = nullptr, bool bForceRegen = false);
	//====================================UI end====================================//


	//====================================骨骼操作start====================================//
public:
	//tolua_begin
	bool addCustomBone(std::string name, std::string fathername, std::string modelfilename);
	bool setCustomBone(std::string name, float scale, short offsetx, short offsety, short offsetz, float yaw, float pitch, float roll);
	bool changeBindModel(std::string name, std::string modelfilename);
	void setBindModelOffset(std::string name, short offsetx, short offsety, short offsetz);
	void setBindModelRotate(std::string name, float yaw, float pitch, float roll);
	void setBindModelScale(std::string name, float scale);
	bool setModelShow(std::string strBoneName, bool visible);
	bool delCustomBone(std::string name);
	bool changeCustomBoneName(std::string oldname, std::string newname);
	void changeCustomBoneStandard(std::string name, bool isstandard = false);
	bool changeCustomBoneFather(std::string name, std::string fathername);
	void getBoneData(std::string name, short& offsetx, short& offsety, short& offsetz, float& pitch, float& yaw, float& roll, float& scale);
	//tolua_end
	bool getCustomBoneData(std::string name, MINIW::Transform_& trs);
	bool setCustomBone(std::string name,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	bool setCustomBone(std::string name, MINIW::Transform_ trs);
	void setBindModelScale(std::string name, Rainbow::Vector3f& scale3);
	bool restoreData(FullyCustomBoneData* fcbd);
	bool copyBone(std::string strSrcBoneName, std::string strNewBoneName, std::string strParent,
		bool mirrorX, bool mirrorY, bool mirrorZ, bool withChildren);
	bool isAttachmentBoneName(std::string boneName);
	void setAttachmentBoneName(std::string boneName, bool isAttachment);
	void changeSkeletonShowState(bool isShowSkeleton);

	bool isValidCustomKeyFrame(std::string boneName, const int seq_id, const int tick);
	Rainbow::Model* getDefaultBoneModel();
	//====================================骨骼操作end====================================//

	//====================================关键帧与动画操作start====================================//
public:
	//tolua_begin
	bool setCustomSequenceEndtime(int seqId, int endtime);
	//注意欧拉角的参数顺序
	//旧版接口，因Vector3未导出Lua，使用基本变量代替Vector3。下方几个接口类似
	bool insertCustomKeyFrame(int seqId, std::string bonename, const int tick,
		short offsetx, short offsety, short offsetz, float yaw, float pitch, float roll, float scale
	);
	//注意欧拉角的参数顺序
	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick,
		short offsetx, short offsety, short offsetz, float yaw, float pitch, float roll, float scale
	);
	void getCustomKeyFrame(int seqId, std::string bonename, const int tick,
		short& offsetx, short& offsety, short& offsetz, float& pitch, float& yaw, float& roll, float& scale
	);
	bool delCustomKeyFrame(int seqId, std::string strBoneName, const int tick);
	//暂停动作
	void pauseMotion(bool pause, const int tick);
	//tolua_end
	bool insertCustomKeyFrame(int seqId, std::string bonename, const int tick,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	bool insertCustomKeyFrame(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool delCustomKeyframes(int seqId, const int tick);
	bool delAllCustomKeyframes(int seqId);
	bool getCustomKeyFrame(int seqId, std::string bonename, const int tick,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	bool getCustomKeyFrame(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool getKeyframe(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool getCustomKeyFrameOrBoneData(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool getOrCreateKeyframe(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs, bool& created);
	Rainbow::BoneTrack* getConflictBoneTrack(int seqId, int seqId2);

	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick,
		Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3
	);
	bool setCustomKeyFrame(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool setOrCreateKeyframe(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs);
	bool copyKeyframesToEachBone(int seqId, const int tickSrc, const int tickDst);
	bool moveCustomKeyFrame(int seqId, std::string bonename, const int oldTick, const int newTick);
	bool copyAnim(int seqIdSrc, int seqIdDst);
	bool insertCustomModelDataVector(std::string strBoneName, std::vector<CustomMotionData>& vCmds);
	bool transformTickPerKf(int seqId, int tickPerKf);
	//====================================关键帧与动画操作end====================================//

	//====================================Resource start====================================//
public:
	//tolua_begin
	//需要下载的模型资源添加到下载列表
	void addDownload(std::string skey);
	bool moveResFullyCustomModelToMap(std::string filename, std::string classname, int folderindex = 0, bool ispacking = false);
	void moveMapFullyCustomModelToRes(std::string filename, bool ispacking = false);
	//从资源中心删除资源
	bool removeResByResourceCenter(int libtype, std::string skey);
	//资源上传下载相关
	void preReqDownload();
	//请求主机获取到下载资源url
	void reqDownloadUrl(std::string skey);
	//主机返回资源的下载url后的处理
	void respDownloadUrl(std::string skey, std::string url);

	void reqDownloadFile(std::string url, int type, int version = 0, std::string externdata = "");
	void respDownloadFile(int result, std::string skey, int type);

	void reqUploadFullyCustomModel(std::string skey, std::string filename, int uin, int type, std::string data = "");
	void respUploadFullyCustomModel(int result, std::string downloadurl, std::string skey, std::string externdata);
	//获取自定义模型资源的信息
	FCM_Res_Info* getDownloadResInfo(std::string skey);
	/*资源中心重构新加接口 时间：2021.3.17版本内容 作者：keguanqiang*/
	bool moveFcmRes(int destlibtype, std::string filename, FullyCustomModel* fcm, ResourceFolderSetInfo* destclass);
	//tolua_end
	//====================================Resource end====================================//

#ifdef UGC_PROFILE_ENABLED
	float getMemStatForUGC();
#endif // UGC_PROFILE_ENABLED

	//tolua_begin
	void leaveWorld();
	void onSwitchAccountSucceed(int uin);
	/*
		加载完fully模型数据 后处理
	*/
	void afterLoadMapPackingFcm(long long owid, int realowneruin, int specialType = 0);
	//tolua_end
private:
	Rainbow::IActorBody* m_pCurEditActorBody;
	FullyCustomModel* m_pCurEditFcm;
	std::vector<FullyCustomModel*> m_vMapFcms;
	std::vector<FullyCustomModel*> m_vResFcms;
	std::vector<FullyCustomModel*> m_vEquipResFcms;
	std::vector<FullyCustomModel*> m_vPreviewResFcms;
	std::vector<FullyCustomModel*> m_vEditorFcms;

	std::deque<FCM_Download_Info> m_WaitDownloadListBySyncHostDeque;			//下载主机同步过来的所有模型
	FCM_Download_Info m_DownloadEditModelInfo;								 //下载要编辑的模型
	std::unordered_map<std::string, FCM_Download_Info> m_WaitDownloadListByClientEdit; //下载客机编辑后保存的模型
	std::unordered_map<std::string, FCM_Res_Info> m_FCMResInfos;   //模型资源的下载url信息

	long long m_CurOWID;
	int m_nSpecialType;
	bool m_bLoadedRes;

private:
	PackingFullyCustomModelMgr* m_pPackingFCMMgr;
protected:
	std::unordered_map<std::string, IconDesc*> m_IconDescs;
	std::unordered_map<std::string, InComplete_Icon> m_IncompleteIcons;
	int m_iRefreshIconTick;
	Rainbow::TextureRenderGen* m_TextureGen;
	int m_iAutoSaveTick;
	Rainbow::SharePtr<Rainbow::ModelData> m_defaultBoneModelData;
}; //tolua_exports
