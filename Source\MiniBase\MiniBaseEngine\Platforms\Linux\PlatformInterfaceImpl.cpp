
#include "Platforms/PlatformInterface.h"
#include "Misc/XMLData.h"
#include <unistd.h>
#include <time.h>
#include <sys/time.h>

static MINIW::ImagePickerCallback s_imagePickerCallback = NULL;
static std::string s_imagePickerTargetPath = "";
static MINIW::CameraQRScanerCallback s_cameraQRScannerCallback = NULL;
static MINIW::ContactPersonsCallback s_contactPersonsCallback = NULL;
static MINIW::WatchADCallback s_watchADCallback = NULL;

int daemon_exit(const char* pidfile)
{
	return unlink(pidfile);
}


 namespace MINIW
{
	static char* m_pClientVersion = NULL;
	static int m_pClientEnv = 0;
	static statisticsJson m_statisticsJson = NULL;

	void SetClientEnv(int env)
	{
		m_pClientEnv = env;
	}
	
	int GetClientEnv()
	{
		return m_pClientEnv;
	}

	void SetStatisticsJson(statisticsJson reportjson)
	{
		m_statisticsJson = reportjson;
	}

	statisticsJson getStatisticsJson()
	{
		return m_statisticsJson;
	}

	unsigned int GetTimeStamp()
	{
		time_t t;
		time(&t);
		return (unsigned int)t;
	}

	uint64_t GetTimeStampMS()
	{
		uint64_t nTimer = 0;
		struct timeval stNow1;
		gettimeofday(&stNow1, NULL);
		nTimer = stNow1.tv_sec * 1000 + stNow1.tv_usec / 1000;
		return nTimer;
	}
	
	int GetTimeZoneStamp()
	{
		time_t t = 0;
		struct tm* p;
		p = localtime(&t);
		int timeZone = (p->tm_hour > 12) ? (p->tm_hour -= 24) : p->tm_hour;
		return (int)timeZone * 3600;
	}

	void SetClientVersion(char* clientVersion)
	{
		m_pClientVersion = clientVersion;
	}

	char* GetClientVersion()
	{
		return m_pClientVersion;
	}

	void ThreadSleep(uint tick)
	{
		timespec ts;
		ts.tv_sec = tick/1000;
		ts.tv_nsec = int((tick%1000)*1000000);
		nanosleep(&ts, NULL);
	}

	void PopMessageBox(const char *content, const char *title)
	{

	}

        void GameVibrateWithTimeAndAmplitude(int time, int amplitude)
	{
	}

	void GetOpenFile(const char *title, const char *format, const char* initDir, char* resultBuffer)
	{
	}

	int GetProcessUsedMemory()
	{
		return 0;
	}
	
	bool GetMachineLocation(double &longitude, double &latitude)
	{
		longitude = 0;
		latitude = 0;
		return false;
	}

	int GetNetworkState()
	{
		return 1;
	}

    int GetNetworkSignal(){
        return 1;
    }

	void SetScreenBrightness(float bright)
	{
	}

	int GetNetworkCardState()
	{
		return 1;
	}


	void SetApiid(int apiid)
	{
	}

	int GameApiId()
	{
		return 999;
	}

	void crashlyticsLog(const char* strlog)
	{

	}

	float GetScreenDpi()
	{
		static float dpi = -1;
		return dpi;
	}

	unsigned long int getPthreadSelf()
	{
		return *(unsigned long int*)pthread_self();
	}

	

	void SavevideoWithphoto(const char *imgpath)
	{
	}

	void OnStatisticsGameEvent(const char *event, const char *paramsName1, const char *params1, const char *paramsName2, const char *params2, const char *paramsName3, const char *params3)
	{
		LOG_INFO("Begin OnStatisticsGameEvent: %s", event);
	}

	void OnAppsFlyerStatisticsGameEvent(const char *event, const char *paramsName1 /*= ""*/, const char *params1 /*= ""*/, const char *paramsName2 /*= ""*/, const char *params2 /*= ""*/, const char *paramsName3 /*= ""*/, const char *params3 /*= ""*/)
	{

	}

	void FirebaseAnalyticsEvent(const char* event, const char* paramJson)
	{

	}

	void OnStatisticsGameChooseRole(const char *roleName, const char *nickName, int uin)
	{
	}

	void OnStatisticsGameBuyRole(char *roleName)
	{
	}

	void OnStatisticsGameRewardMiniCoin(int num, char *reason)
	{
	}

	void OnStatisticsGamePurchaseMiniCoin(const char *name, int num, float price)
	{
	}

	void OnStatisticsOnChargeRequest(const char *sid, const char *productname, const char *paymentType, float price, int coinnum)
	{
	}

	void OnStatisticsOnChargeSuccess(const char *sid)
	{
	}


	void OnClickCopy(const char *content)
	{
	}

	void OnSdkLogin()
	{
	}

	void OnSdkSwitch()
	{
	}

	void OnSdkLogout()
	{
	}

	void OnSdkForum()
	{
	}

	void OnSdkGameCenter()
	{
	}

	void OnSdkAccountBinding(int type)
	{
	}

	void OnSdkLogin(int type)
	{

	}

	bool OnOpenMiniProgram()
	{
		return false;
	}

	bool OnOpenMiniProgramWithType(int type)
	{
		return false;
	}

	void OnSdkRealNameAuth()
	{
	}

	int OnSetGameEnv()
	{
		return 0;
	}

	int OnReqSdkAD(const char *type, int adid, WatchADCallback callback,int adindex, int rewardValue)
	{
		return 0;
	}

	void OnRespWatchAD(int result)
	{
	}

	void OnPlayAdSuccessCallback(int positionId)
	{
	}

	void OnRequestReview()
	{

	}

	

	bool OnInitAdvertisementsSDK(int adid)
	{
		return true;
	}

	bool OnAdvertisementsLoadStatus(int adid, int position)
	{
		return true;
	}

	void OnLoadSdkAD(int adid, int position)
	{
	}

	std::string OnGetSdkAdvertisementsInfo(int platformId, int positionId)
	{
		return "";
	}

	void OnSdkAdvertisementsShow(int platformId, int positionId)
	{
	}

	void OnSdkAdvertisementsOnClick(int platformId, int positionId)
	{
	}

	void OnScreenCaptureCallback(const char * snapshotPath)
	{
	}

	void OnSetSdkRoleInfo(const char *rolename, const char *type, int uin, int coinnum)
	{
	}

	void OnSetSdkFloatMenu(int type)
	{
	}

	bool IsSdkToStartGame()
	{
		return false;
	}
	
	void removeSplashView()
	{
	}
	
    void GameExit(bool restart, const char* pidfile)
	{
		printf("miniworldserver exit.\n");

		if (pidfile)
			daemon_exit(pidfile);
	}

	void GameVibrate(int val)
	{
	}

	std::string GetSchemeJson()
	{
		return "";
	}

	bool PullTPApp(const char *appname, const char *jsonstr)
	{
		return true;
	}

    void OpenSchemes(const char *url){
       
    }

	bool HotfixCopyFile(const char *fromPath, const char *toPath)
	{
		return true;
	}

	void GameDnsIps(const char *domain)
	{
	}
	
	int GameHasTPPay()
	{
		return 1;
	}

	void GameMoreGame()
	{
	}

	void GameStartUpdate()
	{
	}

	void GameSetAccount(int uin, const char* nickname)
	{
	}

	void SetCrashReportUserId(const char* userId)
	{

	}

	bool HasBuiltWithARM64()
	{
		return false;
	}

	bool CanShowARCameraBackground()
	{
		return true;
	}

	void TakeARAvatar(const char* str, const int i, const bool b)
	{

	}
  
    bool ShowCameraQRScannerforARSkin()
    {
        return false;
    }
    
	void prepareARCameraBackground()
	{

	}

	void showARCameraBackground()
	{

	}

	void hideARCameraBackground()
	{

	}

	std::string GetOperatorAndNetworkType()
	{
		return "";
	}

	void GamePay(const char *productName, float amount, const char *productId, int payType, int orderId, const char *sid/* ="" */)
	{
	}

	void SetPayExtendParams(int i, char *buf, int bufsize)
	{
		buf[0] = '\0';
	}

	void WindowBrowserOpenWebpage(const char* url, int left, int top, int width, int height)
	{

	}

	void WindowBrowserCloseWebpage()
	{

	}

	void WindowBrowserShowWebpage()
	{

	}

	void WindowBrowserHideWebpage()
	{

	}

	void BrowserShowWebpage(const char* url, int type)
	{

	}

	void OpenWebView(const char* url, int type, const char* extend)
    {
        
    }

	void StartOnlineShare(const char *jsonStr, const char *imgpath, const char *url, const char* title, const char* content)
	{

	}

	void StartMiniwShare(const char *platformName, const char *imgpath, const char *url, const char* title, const char* content)
	{
	}

	void ShareToQQ(const char *imgpath, const char *url, const char *title, const char *text)
	{
	}

	bool OpenQQBuLuo()
	{
		return false;
	}

	bool OpenQQVip(int type, int months)
	{
		return false;
	}

	std::string GetQQUserInfo()
	{
		return "";
	}

	void AddQQFriend(const char *openid, const char *label, const char *message)
	{
	}

	bool CheckQQLogin(bool dologin)
	{
		return false;
	}

	bool IsAppExist(const char *name)
	{
		return false;
	}
	bool IsAppInstall(const char *platformName)
	{
		return false;
	}
	std::string GetTraceRouteInfo(const char* ip)
	{
		return "";
	}

	bool ShowImagePicker(const char* targetPath, ImagePickerCallback callback, int type, bool crop, int x, int y)
	{
		LOG_INFO("ShowImagePicker %s", targetPath);
		return true;
	}
	void onImagePicked(int err)
	{
	}
    
    void QueryContactPersons(ContactPersonsCallback callback)
    {
        s_contactPersonsCallback = callback;
        OnContactPersons(0);
    }
    void OnContactPersons(int num)
    {
        s_contactPersonsCallback(num);
    }
	ContactPerson GetContactPerson(int index)
	{
		ContactPerson cp;
		return cp;
	}

	bool ShowCameraQRScanner(CameraQRScanerCallback callback)
	{
		return false;
	}
	void onCameraQRScanned(int result, const std::string& scaned_string)
	{
		if (s_cameraQRScannerCallback)
			s_cameraQRScannerCallback(result, scaned_string);
	}

	bool SendTextMessage(const std::string& phoneNumber, const std::string& message)
	{
		return false;
	}

	bool CheckHasPermission(DevicePermission perm)
	{
		return true;
	}

	int GetSpecialReviewMode()
	{
		return 0;
	}

	std::string GetAccount()
	{
		return "";
	}
    std::string GetDeviceToken()
    {
        return "";
    }
   
	std::string GetDeviceId()
	{
		return "Linux";
	}

	std::string OnGetClipBoard()
    {		
        return "";
    }

    void SaveAccount(const char *jsonChar)
	{
		
	}

	void Logout()
	{

	}
	
	std::string GetDeviceModel()
	{
		return "";
	}

	std::string GetMobilePhoneInfo()
	{
		return "";
	}

	void ScanImage(const char * imagePath)
	{
	}

    void developerCertificationToMinibox(const char* str)
    {
        
    }
	std::string GetShouQParams() 
	{
		return "";
	}
	std::string GetSHA512String(const char *str) 
	{
		return "";
	}
	int getSoundState()
	{
		return 1;
	}
	void setSoundState(int state)
	{
		
	}

	std::string GetIDFA()
	{
		return "";
	}
	void OpenAdTrackingSetting()
	{

	}
        void StopGameVibrate()
	{
	}
	std::string GetFlyerUID()
    {
        return "";
    }

	std::string GetDeviceStorageInfo()
	{
		return "";
	}
	std::string GetAdCampaignInfo()
	{
		return "";
	}
	std::string GetDeviceRegisterInfo()
	{
		return "";
	}
        std::string CallNativeFeatureQuery(int type, const char* params)
	{
		return "";
	}

	bool CallNativeView(int type, const char* params)
	{
		return false;
	}

	bool CallNativeFeature(int type, const char* params)
	{
		return false;
	}

	void setUserDataDir(const char* datas)
	{
	}

    std::string getUserManualData()
	{
		return "";
	}

	void saveKVForSP(const char* key, const char* value)
	{
	}

	std::string getValueForSP(const char* key)
	{
		return "";
	}

	void responseLuaWithCallback(const char *funcName, const char *sessionId, const char *responseJson)
	{
		
	}

    void setCrashTag(const char *tagKey, const char *tagValue)
	{

	}

    void rmCrashTag(const char *tagKey)
	{

	}

	std::string CallNativeFileSelector(const char* params)
	{
		return "";
	}

	void CallNativeFileCopy(const char* params)
	{
	}

	std::string fetchLaunchIp()
	{
		return "";
	}

	int checkFirstLaunch()
	{
		return 0;
	}

	long fetchFirstLaunchTimeStamp()
	{
		return 0;
	}

	int fetchApn()
	{
		return 0;
	}
}
