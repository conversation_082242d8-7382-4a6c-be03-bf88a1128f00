#include "CustomMotionData.h"

#include "Math/FloatConversion.h"

#include "ClientActorHelper.h"

#include "FullyCustomModel_generated.h"
#include "CustomCommonHead.h"

CustomMotionData::CustomMotionData():
	id(-1),
	time(0)
{
	ticks.clear();
	posoffsets.clear();
	quats.clear();
	scale3s.clear();
	scales.clear();
}


CustomMotionData::CustomMotionData(const CustomMotionData& cmd)
{
	copy(cmd);
}

CustomMotionData::CustomMotionData(const CustomMotionData* cmd)
{
	copy(*cmd);
}

CustomMotionData& CustomMotionData::operator=(const CustomMotionData& cmd)
{
	copy(cmd);
	return *this;
}

CustomMotionData::~CustomMotionData()
{
}

void CustomMotionData::clear()
{
	ticks.clear();
	posoffsets.clear();
	quats.clear();
	scale3s.clear();
	scales.clear();
}

void CustomMotionData::add(int tick, const Rainbow::Vector3f& translate,
	const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3)
{
	validateTick(tick);
	ticks.emplace_back(tick);
	posoffsets.emplace_back(translate);
	quats.emplace_back(rotate);
	scale3s.emplace_back(scale3);
	//新接口，兼容旧版scale默认使用x值。
	scales.emplace_back(scale3.x);
}

bool CustomMotionData::setIndex(int i, int tick,
	const Rainbow::Vector3f& translate, const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3)
{
	if (i < 0 || i >= ticks.size())
	{
		return false;
	}
	validateTick(tick);
	ticks[i] = tick;
	posoffsets[i] = translate;
	quats[i] = rotate;
	scale3s[i] = scale3;
	//新接口，兼容旧版scale默认使用x值。
	scales[i] = scale3.x;
	return true;
}

void CustomMotionData::removeIndex(int i)
{
	if (i < 0 || i >= ticks.size())
	{
		return;
	}
	ticks.erase(ticks.begin() + i);
	posoffsets.erase(posoffsets.begin() + i);
	quats.erase(quats.begin() + i);
	scale3s.erase(scale3s.begin() + i);
	scales.erase(scales.begin() + i);
}

void CustomMotionData::insert(int i, int tick,
	const Rainbow::Vector3f& translate, const Rainbow::Quaternionf& rotate, const Rainbow::Vector3f& scale3)
{
	validateTick(tick);
	ticks.insert(ticks.begin() + i, tick);
	posoffsets.insert(posoffsets.begin() + i, translate);
	quats.insert(quats.begin() + i, rotate);
	scale3s.insert(scale3s.begin() + i, scale3);
	//新接口，兼容旧版scale默认使用x值。
	scales.insert(scales.begin() + i, scale3.x);
}

void CustomMotionData::resetTicks()
{
	for (int i = ticks.size() - 1; i >= 0; --i)
	{
		if (ticks[i] <= time)
		{
			break;
		}
		removeIndex(i);
	}
}

void CustomMotionData::copy(const CustomMotionData& cmd)
{
	id = cmd.id;
	time = cmd.time;
	tickPerKf = cmd.tickPerKf;
	ticks = cmd.ticks;
	posoffsets = cmd.posoffsets;
	quats = cmd.quats;
	scale3s = cmd.scale3s;
	scales = cmd.scales;
}

int CustomMotionData::getIndexByTick(int tick)
{
	for (unsigned i = 0; i < ticks.size(); ++i)
	{
		if (ticks[i] == tick)
		{
			return i;
		}
	}
	return -1;
}

bool CustomMotionData::getTRS(int tick, MINIW::Transform_& trs)
{
	int i = getIndexByTick(tick);
	if (i < 0)
	{
		return false;
	}
	return getTRSByIndex(i, trs);
}

bool CustomMotionData::getTRSByIndex(int i, MINIW::Transform_& trs)
{
	if (i < 0 || i >= ticks.size())
	{
		return false;
	}
	trs.setTRS(posoffsets[i], quats[i], scale3s[i]);
	return true;
}

bool CustomMotionData::hasTicks()
{
	return !ticks.empty();
}

void CustomMotionData::addFbs(const FBSave::KeyFrameData* fbsKfd)
{
	if (!fbsKfd)
	{
		return;
	}
	short tick = fbsKfd->idx();
	CUSTOM_MODEL_LOG("tick = %d", tick);
	if (tick < 0)
	{
		tick = 0;
	}
	ticks.emplace_back(tick);
	Rainbow::Vector3f eulerAngle(fbsKfd->yaw(), fbsKfd->pitch(), fbsKfd->roll());
	CUSTOM_MODEL_LOG("CustomMotionData addFbs eulerAngle ");
	Rainbow::Quaternionf quat;
	if (IsFinite(eulerAngle))
	{
		CUSTOM_MODEL_LOG("eulerAngle = (%.f,%.f,%.f)", eulerAngle.x, eulerAngle.y, eulerAngle.z);
		quat = AngleEulerToQuaternionf(eulerAngle, math::kOrderZYX);
	}
	else
	{
		CUSTOM_MODEL_LOG("eulerAngle infinite");
		quat = Rainbow::Quaternionf::identity;
	}
	Rainbow::Vector3f t = Vec3ToVector3(fbsKfd->offsetpos());
	CUSTOM_MODEL_LOG("CustomMotionData addFbs offsetpos ");
	//nan处理
	if (!Rainbow::Equal(t, t))
	{
		assert(false);
		t.Set(0,0,0);
	}
	posoffsets.emplace_back(t);
	quats.emplace_back(quat);

	CUSTOM_MODEL_LOG("CustomMotionData addFbs 202 ");
	if (fbsKfd->scale3())
	{
		Rainbow::Vector3f scale3 = Vec3ToVector3(fbsKfd->scale3());
		limitScale(scale3);
		scale3s.emplace_back(scale3);
		scales.emplace_back(scale3.x);
	}
	else
	{
		Rainbow::Vector3f scale3;
		if (fbsKfd->scale())
		{
			float s = fbsKfd->scale();
			if (!Rainbow::IsFinite(s))
			{
				scale3.Set(1.f, 1.f, 1.f);
			}
			else
			{
				limitScale(s);
				scale3.Set(s, s, s);
			}
		}
		else
		{
			scale3.Set(1.f, 1.f, 1.f);
		}
		scale3s.emplace_back(scale3);
		scales.emplace_back(scale3.x);
	}
}

void CustomMotionData::fromFbs(const FBSave::MotionData* fbsMd)
{
	if (!fbsMd)
	{
		return;
	}
	id = fbsMd->id();
	time = fbsMd->time();
	clear();
	if (fbsMd->keyframes()) 
	{
		for (int k = 0; k < (int)fbsMd->keyframes()->size(); k++)
		{
			auto fbsKfd = fbsMd->keyframes()->Get(k);
			addFbs(fbsKfd);
		}
	}
	else
	{
		CUSTOM_MODEL_LOG("CustomMotionData keyframes null! ");
	}
	tickPerKf = fbsMd->tickperkf();
}

void CustomMotionData::validateTick(int& tick)
{
	if (tick < 0)
	{
		assert(false);
		tick = 0;
	}
}

void CustomMotionData::transformTickPerKf(int newTickPerKf)
{
	int oldTickPerKf = tickPerKf;
	for (unsigned& tick : ticks)
	{
		tick = tick * newTickPerKf / oldTickPerKf;
	}
	time = time * newTickPerKf / oldTickPerKf;
	tickPerKf = newTickPerKf;
}

void CustomMotionData::limitScale(Rainbow::Vector3f& scale3)
{
	for (int i = 0; i < 3; ++i)
	{
		limitScale(scale3[i]);
	}
}

void CustomMotionData::limitScale(float& s)
{
	if (s < 0.01f)
	{
		s = 1.f;
	}
	if (s > 10000)
	{
		s = 10000;
	}
}

int CustomMotionData::getFrameKeyNum()
{
	return (int)ticks.size();
}

int CustomMotionData::getTick(int index)
{
	if (index >= 0 && index < (int)ticks.size())
		return ticks[index];

	return -1;
}

void CustomMotionData::getOffsetPos(int index, float& x, float& y, float& z)
{
	if (index >= 0 && index < (int)posoffsets.size())
	{
		Rainbow::Vector3f& pos = posoffsets[index];
		x = pos.x;
		y = pos.y;
		z = pos.z;
	}
}

void CustomMotionData::getRotate(int index, float& pitch, float& yaw, float& roll)
{
	if (index >= 0 && index < (int)quats.size())
	{
		Rainbow::Vector3f eulerV = Rainbow::QuaternionToEulerAngle(quats[index], math::RotationOrder::kOrderZYX);
		pitch = eulerV.y;
		yaw = eulerV.x;
		roll = eulerV.z;
	}
}

float CustomMotionData::getScale(int index)
{
	if (index >= 0 && index < (int)scales.size())
	{
		return scales[index];
	}

	return 1;
}