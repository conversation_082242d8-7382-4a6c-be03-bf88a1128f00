#include "ArchitecturalBlueprintCsv.h"
#include "OgreUtils.h"
#include "OgreStringUtil.h"
#include "defmanager.h"
#include "ModManager.h"

using MINIW::CSVParser;
using namespace MINIW;

IMPLEMENT_LAZY_SINGLETON(ArchitecturalBlueprintCsv)

ArchitecturalBlueprintCsv::ArchitecturalBlueprintCsv()
{
}

ArchitecturalBlueprintCsv::~ArchitecturalBlueprintCsv()
{
    onClear();
}

const char* ArchitecturalBlueprintCsv::getName()
{
    return "ArchitecturalBlueprint_new";
}

const char* ArchitecturalBlueprintCsv::getClassName()
{
    return "ArchitecturalBlueprintCsv";
}

void ArchitecturalBlueprintCsv::onClear()
{
    m_Table.clear();
}

void ArchitecturalBlueprintCsv::onParse(CSVParser& parser)
{
    onClear();
    
    parser.SetTitleLine(1);
    int numLines = (int)parser.GetNumLines();
    
    for (int i = 2; i < numLines; ++i)
    {
        ArchitecturalBlueprintCsvDef def;
        def.ID = parser[i]["ID"].Int();
        
        // 跳过ID为0的行
        if (def.ID == 0) continue;
        
        def.Name = parser[i]["Name"].Str();
        def.Icon = parser[i]["Icon"].Str();
        def.Description = parser[i]["Desc"].Str();
        
        // 解析材料消耗 (木质的格式: 制造道具|消耗道具|数量)
        parseMaterialConsumption(parser[i]["WoodConsumption"].Str(), def.Wood);
        parseMaterialConsumption(parser[i]["StoneConsumption"].Str(), def.Stone);
        parseMaterialConsumption(parser[i]["IronConsumption"].Str(), def.Iron);
        parseMaterialConsumption(parser[i]["SteelConsumption"].Str(), def.Steel);
        
        // 添加到表格
        m_Table.AddRecord(def.ID, def);
    }
}

bool ArchitecturalBlueprintCsv::parseMaterialConsumption(const std::string& input, ArchitecturalBlueprintCsvDef::MaterialConsumption& material)
{
    // 初始化材料消耗为0
    material.ProduceItemID = 0;
    material.ConsumeItemID = 0;
    material.Count = 0;
    
    if (input.empty())
        return false;
    
    // 使用 stringstream 和 getline 代替 Ogre::StringUtil::split
    std::vector<std::string> parts;
    std::stringstream ss(input);
    std::string part;
    
    while (std::getline(ss, part, '|')) {
        parts.push_back(part);
    }
    
    // 格式必须是: 制造道具|消耗道具|数量
    if (parts.size() != 3)
        return false;
    
    try {
        material.ProduceItemID = std::stoi(parts[0]);
        material.ConsumeItemID = std::stoi(parts[1]);
        material.Count = std::stoi(parts[2]);
        return true;
    }
    catch (const std::exception&) {
        // 解析失败，保持为0值
        return false;
    }
}

int ArchitecturalBlueprintCsv::getNum()
{
    load();
    return m_Table.GetRecordSize();
}

const ArchitecturalBlueprintCsvDef* ArchitecturalBlueprintCsv::get(int id)
{
    load();
    return m_Table.GetRecord(id);
}

std::vector<const ArchitecturalBlueprintCsvDef*> ArchitecturalBlueprintCsv::getAll()
{
    load();
    std::vector<const ArchitecturalBlueprintCsvDef*> result;
    
    int size = m_Table.GetRecordSize();
    for (int i = 0; i < size; ++i)
    {
        const ArchitecturalBlueprintCsvDef* record = m_Table.GetRecordByIndex(i);
        if (record) {
            result.push_back(record);
        }
    }
    
    return result;
}

std::map<int, int> ArchitecturalBlueprintCsv::getConsumedItemsByProduceItemID(int produceItemID)
{
    load();
    std::map<int, int> result;
    
    int size = m_Table.GetRecordSize();
    for (int i = 0; i < size; ++i)
    {
        const ArchitecturalBlueprintCsvDef* record = m_Table.GetRecordByIndex(i);
        if (!record) continue;
        
        // 检查木材消耗
        if (record->Wood.ProduceItemID == produceItemID && record->Wood.ConsumeItemID > 0 && record->Wood.Count > 0)
        {
            result[record->Wood.ConsumeItemID] += record->Wood.Count;
        }
        
        // 检查石材消耗
        if (record->Stone.ProduceItemID == produceItemID && record->Stone.ConsumeItemID > 0 && record->Stone.Count > 0)
        {
            result[record->Stone.ConsumeItemID] += record->Stone.Count;
        }
        
        // 检查铁材消耗
        if (record->Iron.ProduceItemID == produceItemID && record->Iron.ConsumeItemID > 0 && record->Iron.Count > 0)
        {
            result[record->Iron.ConsumeItemID] += record->Iron.Count;
        }
        
        // 检查钢材消耗
        if (record->Steel.ProduceItemID == produceItemID && record->Steel.ConsumeItemID > 0 && record->Steel.Count > 0)
        {
            result[record->Steel.ConsumeItemID] += record->Steel.Count;
        }
    }
    
    return result;
} 

/*
// 获取制造道具ID为1001的所有消耗材料
auto consumedItems = ArchitecturalBlueprintCsv::getInstance()->getConsumedItemsByProduceItemID(1001);

// 遍历结果
for (const auto& item : consumedItems) {
    int consumeItemID = item.first;  // 消耗道具ID
    int count = item.second;         // 数量
    // 处理消耗道具信息...
}
*/

/*
// 现在也可以通过DefManager统一接口来访问 (推荐方式)
#include "defmanager.h"

// 获取制造道具ID为1001的所有消耗材料
auto consumedItems = DefManager::getInstance()->getConsumedItemsByProduceItemID(1001);

// 遍历结果
for (const auto& item : consumedItems) {
    int consumeItemID = item.first;  // 消耗道具ID
    int count = item.second;         // 数量
    // 处理消耗道具信息...
}
*/