#include "ClientMacrosConfig.h"
#include "OpenSandboxEngineToLua.h"
#include "SandboxGame/OpenSandboxGameToLua.h"
#include "OpenMiniModuleToLua.h"
#include <lua/tolua/tolua++.h>

struct lua_State;
extern int tolua_jsonxx_open(lua_State*);
//extern int tolua_PlatformInterface_open(lua_State*);//已经迁移到minibase

// 记录全局的luastate
lua_State* global_lua_state = 0;

#ifndef WINDOWS_SERVER
#ifdef BUILD_MINI_EDITOR_APP
extern int tolua_ClientToLua_ministudio_open(lua_State*);
#else
extern int tolua_ClientToLua_open(lua_State*);
#endif
#else
extern int tolua_ClientToLua_server_open(lua_State*);
#endif
//extern int tolua_MiniUITolua_open(lua_State*); 
//extern int tolua_UITolua_open(lua_State*); 

extern int luaopen_SnapshotToLua(lua_State*);
extern int luaopen_HomeLandToLua(lua_State*);
extern int homeland_open_manual_tolua(lua_State*);
extern int luaopen_AdventureGuideToLua(lua_State*);
extern int open_MiniBaseGameToLua(lua_State*);
extern int tolua_GameStageTolua_open(lua_State*);
 /**
@brief 统一打开tolua的注册
@TODO: 将使用脚本自动生成
*/
int tolua_open_all(lua_State* ls) 
{
	global_lua_state = ls;

	tolua_jsonxx_open(ls);

#ifndef WINDOWS_SERVER
#ifdef BUILD_MINI_EDITOR_APP
	tolua_ClientToLua_ministudio_open(ls);
#else
	tolua_ClientToLua_open(ls);
#endif
#else
	tolua_ClientToLua_server_open(ls);
#endif

	tolua_GameStageTolua_open(ls);

	//tolua_PlatformInterface_open(ls); //已经迁移到minibase
	OpenSandboxEngineToLuaStep1(ls);
	OpenSandboxGameToLuaStep1(ls);
	OpenMiniModuleToLuaStep1(ls);
	OpenSandboxEngineToLuaStep2(ls);
	OpenSandboxGameToLuaStep2(ls);
	OpenMiniModuleToLuaStep2(ls);
	OpenSandboxEngineToLuaStep3(ls);
	OpenSandboxGameToLuaStep3(ls);
	OpenMiniModuleToLuaStep3(ls);
	OpenSandboxEngineToLuaStep4(ls);
	OpenSandboxGameToLuaStep4(ls);
	OpenMiniModuleToLuaStep4(ls);
	OpenSandboxEngineToLuaStep5(ls);
	OpenSandboxGameToLuaStep5(ls);
	OpenMiniModuleToLuaStep5(ls);

	//tolua_MiniUITolua_open(ls); 
	//tolua_UITolua_open(ls); 
	//tolua_VideoDecoderToLua_open(ls);
	open_MiniBaseGameToLua(ls);
#ifdef MODULE_FUNCTION_ENABLE_HOMELAND
	luaopen_HomeLandToLua(ls);	
#endif
#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
	luaopen_SnapshotToLua(ls);
#endif
	OpenMiniModuleToLuaStep6(ls);
	OpenSandboxEngineToLuaStep6(ls);
	OpenSandboxGameToLuaStep6(ls);
//#ifndef WINDOWS_SERVER
//#ifdef BUILD_MINI_EDITOR_APP
//	tolua_ClientToLua_ministudio_open(ls);
//#else
//	tolua_ClientToLua_open(ls);
//#endif
//#else
//	tolua_ClientToLua_server_open(ls);
//#endif
	luaopen_AdventureGuideToLua(ls);
	return 1; 
} 


//打印lua堆栈
void dump_lua_stacktrace()
{
	if (global_lua_state)
	{
		lua_State* L = global_lua_state;
		lua_Debug entry;
		int depth = 0;

		lua_getglobal(L, "gCurHandleProtoId");
		if (lua_isnumber(L, -1))
		{
			int gCurHandleProtoId = lua_tointeger(L, -1);
			printf("[Lua Dump] gCurHandleProtoId = %d\n", gCurHandleProtoId);
		}

		lua_pop(L, 1);

		while (lua_getstack(L, depth, &entry))
		{
			int status = lua_getinfo(L, "Sln", &entry);

			printf("[Lua Dump] %s(%d): %s\n", entry.short_src, entry.currentline, entry.name ? entry.name : "?");
			depth++;
		}
	}
}
