/*
*	file: SceneEffectLine
*	func: 在世界场景中绘制线条
*	by: chenzh
*	time: 2021.1.22
*/
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "Math/FloatConversion.h"

using namespace Rainbow;

SceneEffectLine::SceneEffectLine()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE;
}

SceneEffectLine::SceneEffectLine(const WCoord startpos, const WCoord endpos, int stroke, 
	CURVEFACEMTLTYPE mtltype, bool bNormalize, CurveFace* curveFaces)
{

	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_iStroke = stroke;
	m_MtlType = mtltype;

	SetPos(startpos, endpos, bNormalize);

	SetCurveFaces(curveFaces);
}

SceneEffectLine::~SceneEffectLine()
{
	m_Vertices1.clear();
	m_Vertices2.clear();
	m_Vertices3.clear();
	m_Vertices4.clear();
	m_Indices.clear();
}

void SceneEffectLine::FillVertBuffer(BlockGeomVert& vert, const Vector3f& pos, float u, float v, BlockVector color)
{
	vert.pos.x = pos.x;
	vert.pos.y = pos.y;
	vert.pos.z = pos.z;
	vert.pos.w = (0x7f << 8) | 0x7f;
	vert.color.Set(color.x, color.y, color.z, color.w);
	vert.uv.x = short(u * 4096);
	vert.uv.y = short(v * 4096);
}

void SceneEffectLine::PushIndexBuffer(std::vector<unsigned short>& indices, unsigned short idx0, unsigned short idx1, unsigned short idx2)
{
	indices.push_back(idx0);
	indices.push_back(idx1);
	indices.push_back(idx2);
}

void SceneEffectLine::OnClear()
{
	m_Vertices1.clear();
	m_Vertices2.clear();
	m_Vertices3.clear();
	m_Vertices4.clear();
	m_Indices.clear();
}

void SceneEffectLine::Refresh()
{
	if (m_EndPos == m_StartPos)
	{
		return;
	}

	Vector3f dim = ((m_EndPos - m_StartPos)).toVector3();
	m_drawpos = m_StartPos;
	CalcVertexsAndIndices(dim);
}

void SceneEffectLine::OnDraw(World* pWorld)
{
	if (m_Vertices1.empty())
	{
		return;
	}

	auto CurveRender = m_CurveFaces ? m_CurveFaces : pWorld->getRender()->getCurveRender();
	if (CurveRender)
	{
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices1, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices2, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices3, m_Indices);
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertices4, m_Indices);
	}
}

bool SceneEffectLine::IsActive(World* pWorld) const
{
	return (m_StartPosBlock.y >= 0 && m_EndPosBlock.y >= 0) && (pWorld->getChunk(m_StartPosBlock) || pWorld->getChunk(m_EndPosBlock));
}

void SceneEffectLine::CalcVertexsAndIndices(const Vector3f& dim)
{
	OnClear(); // 清理缓存
	Vector3f startpos(0, 0, 0);
	Vector3f endpos = startpos + dim;
	//if (Abs(endpos.x) < BLOCK_SIZE && Abs(endpos.y) < BLOCK_SIZE && Abs(endpos.z) < BLOCK_SIZE)
	//	return;
	
	Vector3f dir = dim;
	dir.NormalizeSafe();
	Vector3f border = dir * (float)m_iStroke;
	startpos -= border;
	endpos += border;
	Vector3f dirVertical1, dirVertical2;
	Vector3f vertex[8];

	if (Abs(dir.x) < 0.1f && Abs(dir.z) < 0.1f)
	{
		dirVertical1.Set(1.0f, 0.0f, 0.0f);
	}
	else
	{
		dirVertical1.Set(0.0f, 1.0f, 0.0f);
	}
	dirVertical2 = CrossProduct(dir, dirVertical1);
	dirVertical1 = CrossProduct(dir, dirVertical2);
	dirVertical1.NormalizeSafe();
	dirVertical2.NormalizeSafe();
	dirVertical1 *= (float)m_iStroke;
	dirVertical2 *= (float)m_iStroke;

	vertex[0] = startpos + dirVertical1 + dirVertical2;
	vertex[1] = startpos + dirVertical1 - dirVertical2;
	vertex[2] = startpos - dirVertical1 + dirVertical2;
	vertex[3] = startpos - dirVertical1 - dirVertical2;
	vertex[4] = endpos + dirVertical1 + dirVertical2;
	vertex[5] = endpos + dirVertical1 - dirVertical2;
	vertex[6] = endpos - dirVertical1 + dirVertical2;
	vertex[7] = endpos - dirVertical1 - dirVertical2;

	float u1 = 0.0f;
	float u2 = (endpos - startpos).Length() / 100;
	float v1 = 0.0f;
	float v2 = m_iStroke / 100;

	// CURVEFACEMTL_RAILHINT 和 CURVEFACEMTL_RAILRED 两边有透明像素，特殊处理
	switch (m_MtlType)
	{
		case CURVEFACEMTL_TEXWHITE:
		case CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY:
			u1 = 0.49f;
			u2 = 0.51f;
			break;
		case CURVEFACEMTL_RAILHINT:
		case CURVEFACEMTL_RAILRED:
		case CURVEFACEMTL_LIGHTNING1:
			u1 = 0.3f;
			u2 = 0.7f;
			break;
	}

	m_Indices.push_back(0);
	m_Indices.push_back(2);
	m_Indices.push_back(1);
	m_Indices.push_back(1);
	m_Indices.push_back(2);
	m_Indices.push_back(3);

	m_Vertices1.resize(4);
	m_Vertices2.resize(4);
	m_Vertices3.resize(4);
	m_Vertices4.resize(4);

	FillVertBuffer(m_Vertices1[0], vertex[0], u1, v1, m_Color);
	FillVertBuffer(m_Vertices1[1], vertex[4], u2, v1, m_Color);
	FillVertBuffer(m_Vertices1[2], vertex[1], u1, v2, m_Color);
	FillVertBuffer(m_Vertices1[3], vertex[5], u2, v2, m_Color);

	FillVertBuffer(m_Vertices2[0], vertex[1], u1, v2, m_Color);
	FillVertBuffer(m_Vertices2[1], vertex[5], u2, v2, m_Color);
	FillVertBuffer(m_Vertices2[2], vertex[3], u1, v1, m_Color);
	FillVertBuffer(m_Vertices2[3], vertex[7], u2, v1, m_Color);

	FillVertBuffer(m_Vertices3[0], vertex[3], u1, v1, m_Color);
	FillVertBuffer(m_Vertices3[1], vertex[7], u2, v1, m_Color);
	FillVertBuffer(m_Vertices3[2], vertex[2], u1, v2, m_Color);
	FillVertBuffer(m_Vertices3[3], vertex[6], u2, v2, m_Color);

	FillVertBuffer(m_Vertices4[0], vertex[2], u1, v2, m_Color);
	FillVertBuffer(m_Vertices4[1], vertex[6], u2, v2, m_Color);
	FillVertBuffer(m_Vertices4[2], vertex[0], u1, v1, m_Color);
	FillVertBuffer(m_Vertices4[3], vertex[4], u2, v1, m_Color);

	// 两头
	// todo...
}