#include "ItemInHandDefCsv.h"
#include "Common/OgreStringUtil.h"
#include "OgreUtils.h"
#include "ItemDefCsv.h"
#include "ModManager.h"
using namespace MINIW;
using MINIW::CSVParser;
IMPLEMENT_LAZY_SINGLETON(ItemInHandDefCsv)

ItemInHandDefCsv::ItemInHandDefCsv()
{

}

ItemInHandDefCsv::~ItemInHandDefCsv()
{
	onClear();
}

void ItemInHandDefCsv::onParse(MINIW::CSVParser& parser)
{
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i = 2; i < numLines; ++i)
	{
		int id = parser[i]["itemID"].Int();
		if (id == 0) continue;
		ItemInHandDef* def = ENG_NEW_LABEL(ItemInHandDef, kMemConfigDef);
		vector<string> vDepth;
		std::string sDepth = parser[i]["rightHand"].Str();
		Rainbow::StringUtil::split(vDepth, sDepth, "|");
		def->itemId = id;

		GetDefManager().ParseItemPosData(parser[i]["rightHand"].Str(), def->RightHandItemPosData);
		GetDefManager().ParseItemPosData(parser[i]["leftHand"].Str(), def->LeftHandItemPosData);
		GetDefManager().ParseItemPosData(parser[i]["FPSrightHand"].Str(), def->FPSRightItemPosData);
		GetDefManager().ParseItemPosData(parser[i]["FPSleftHand"].Str(), def->FPSLeftItemPosData);

		def->FPSSlotId = parser[i]["FPSBindSlotId"].Int();
		def->TPSSlotId = parser[i]["TPSBindSlotId"].Int();
		// 默认101
		if (def->FPSSlotId == 0)
			def->FPSSlotId = 101;
		if (def->TPSSlotId == 0)
			def->TPSSlotId = 101;

		WCoord handPos;
		GetDefManager().ParseWCoord(parser[i]["handPos"].Str(), handPos);
		def->FPSHandPos = handPos.toVector3() * 0.1f;   // 单位统一，其他几个参数的位置参数填写时都有10倍的放大

		if (m_ItemInHandDefTable.find(id) != m_ItemInHandDefTable.end())
			ENG_DELETE_LABEL(m_ItemInHandDefTable[id], kMemConfigDef);
		m_ItemInHandDefTable[id] = def;
	}
}

void ItemInHandDefCsv::onClear()
{
	for (auto& pair : m_ItemInHandDefTable)
	{
		ENG_DELETE_LABEL(pair.second, kMemConfigDef);
	}
	m_ItemInHandDefTable.clear();
}

const char* ItemInHandDefCsv::getName()
{
	return "iteminhanddef";
}

const char* ItemInHandDefCsv::getClassName()
{
	return "ItemInHandDefCsv"; 
}

ItemInHandDef* ItemInHandDefCsv::get(int id)
{
	load();
	auto itr = m_ItemInHandDefTable.find(id);
	if (itr != m_ItemInHandDefTable.end())
		return itr->second;
	return nullptr;
}
