#ifndef __FullyCustomBoneData_h__
#define __FullyCustomBoneData_h__ 1

#include "BaseClass/PPtr.h"
#include "base/SandboxAutoRef.h"
#include "CustomMotionData.h"
#include "asset/SandboxAssetObject.h"
#include "SandboxObject.h"
#include "OgreEntity.h"
#include "BaseClass/PPtr.h"
#include "flatbuffers/flatbuffers.h"
#include "FullyCustomModel_generated.h"
#include "BaseClass/PPtr.h"
#include <vector>
#include <map>
#include <functional>
#include "SandboxEngine.h"

namespace Rainbow {
	class Model;
}

struct LoadMapModData;
class FullyCustomModel;

class EXPORT_SANDBOXENGINE FullyCustomBoneData;
class FullyCustomBoneData{//tolua_exports
public:
	FullyCustomBoneData(FullyCustomModel* fcm);
	~FullyCustomBoneData();
	FullyCustomBoneData(const FullyCustomBoneData& from);
	FullyCustomBoneData& operator= (const FullyCustomBoneData& from);
	void setScale(float scale);
	void setSubModelScale(float scale);
	void baseCopy(const FullyCustomBoneData& fcbd);
	void deepCopy(const FullyCustomBoneData& fcbd);
	void deepCopy(const FullyCustomBoneData* fcbd);
	void deepCopy(FullyCustomBoneData* fcbd, const int animId);
	void shallowCopy(const FullyCustomBoneData& fcbd);
	void shallowCopy(const FullyCustomBoneData* fcbd);
	void deleteChildren();
	void mirrorX();
	void mirrorY();
	void mirrorZ();
	void mirrorXYZ(bool mirrorX, bool mirrorY, bool mirrorZ);
	//tolua_begin
	void getOffsetPos(float &x, float &y, float &z);
	void getSubModelPos(float &x, float &y, float &z);
	void getRotate(float &pitch, float &yaw, float &roll);
	void getSubModelRotate(float &pitch, float &yaw, float &roll);
	int getCustomMotionDataNum();
	CustomMotionData *getCustomMotionData(int index);
	//tolua_end
	CustomMotionData* getCustomMotionData(int seqId, int tick);
	CustomMotionData* getCustomMotionDataBySeqId(int seqId);
	bool getCustomMotionDataTRS(int seqId, int tick, MINIW::Transform_& trs);
	//tolua_begin
	int getChildBoneDataNum();
	FullyCustomBoneData* getChildBoneDat(int index);
	//tolua_end
	int getEndtime(int seqId);
	std::vector<unsigned> getTicks(int seqId);
	bool hasTick(int seqId, int tick);
	bool hasTicks(int seqId);
	static void iterate(std::vector<FullyCustomBoneData*>& vFcbds, std::function<bool(FullyCustomBoneData&)>& func);
	static bool iterateCheck(std::vector<FullyCustomBoneData*>& vFcbds, std::function<bool(FullyCustomBoneData&)>& func, bool bIteAll);
	// 异步资源加载
	bool loadModelAsync(std::function<void(Rainbow::Entity*, FullyCustomBoneData&)> loadFinish);
	void setModel(Rainbow::Model* model);
	bool setTextureId(const std::string& strTexId);
	flatbuffers::Offset<FBSave::FullyCustomBoneData> toFbs(flatbuffers::FlatBufferBuilder& builder);
	void fromFbs(const FBSave::FullyCustomBoneData* fbsFcbd, 
		std::map<std::string, bool>* subcustommodels, int& downloadcmnum, LoadMapModData* moddata,bool bingorecheck);

	void setDefaultSkeletonModel(Rainbow::Model* model);
public:
	//tolua_begin
	std::string name;
	std::string fathername;
	std::string model;
	std::string texId;

	Rainbow::Vector3f offsetpos;
	Rainbow::Quaternionf quat;
	/**
	@brief	旧版仅使用了相同尺寸缩放，现扩展到各自的缩放。同submodelscale
	 */
	Rainbow::Vector3f scale3;
	/**
	@brief	保留旧版以兼容Lua代码。可重构去除
	 */
	float scale;

	Rainbow::Vector3f submodelpos;
	Rainbow::Quaternionf submodelquat;
	/**
	@brief	旧版仅使用了相同尺寸缩放，现扩展到各自的缩放。同scale
	 */
	Rainbow::Vector3f submodelscale3;
	/**
	@brief	保留旧版以兼容Lua代码。可重构去除
	 */
	float submodelscale;
	//tolua_end

	std::vector<CustomMotionData> vCmds;
	std::vector<FullyCustomBoneData*> vChildFcbds;

	FullyCustomModel* fcm;
	/**
	@brief	区分旧版字符串成员的名字
	 */
	 //Rainbow::Model* model2;
	Rainbow::PPtr<Rainbow::Model> model2;

	//默认的骨骼模型
	Rainbow::PPtr<Rainbow::Model> defaultSkeletonModel;
	MNSandbox::AutoRef<MNSandbox::SandboxNode> m_assetOwner;
	MNSandbox::AssetObject m_aoModel;	// 资源管理对象
	MNSandbox::AssetObject m_aoTexture;

	//tolua_begin
	bool show;
	bool isdownload;
	bool isstandard;
	//tolua_end
	volatile bool hasUploadedModel = false;
	volatile bool hasUploadedTexture = false;
};//tolua_exports
#endif//__FullyCustomBoneData_h__