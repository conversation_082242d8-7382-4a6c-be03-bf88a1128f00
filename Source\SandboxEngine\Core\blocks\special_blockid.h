
#ifndef __SPECIAL_BLOCKID_H__
#define __SPECIAL_BLOCKID_H__

#include "SandboxEngine.h"
#include "SandboxIdDef.h"
#include "DefManagerProxy.h"

inline bool IsJointBlockID(int blockid)
{
	return blockid==BLOCK_REVOLUTE_JOINT || blockid==BLOCK_PRISMATIC_JOINT || 
		BLOCK_JOINT_ARM_PRISMATIC == blockid || BLOCK_JOINT_T_REVOLUTE == blockid ||
		BLOCK_JOINT_SPHERICAL == blockid || blockid == BLOCK_SUSPENSION_JOINT ||
		BLOCK_ROPE_HEAD == blockid || BLOCK_ROPE == blockid || BLOCK_ROPE_TAIL == blockid;
}

inline bool IsThrusterBlockID(int blockid)
{
	return blockid == BLOCK_THRUSTER;
}

inline bool IsDynamicBlockID(int blockid)
{
	return blockid == BLOCK_WING || blockid == BLOCK_EMPENNAGE || blockid == BLOCK_STHRUSTER || blockid == BLOCK_THRUSTER || blockid == BLOCK_BOAT_FLOATBUCKET || blockid == BLOCK_BOAT_THRUSTER ;
}

inline bool IsWheelBlockID(int blockid)
{
	return blockid==BLOCK_FRONT_LEFT_WHEEL || blockid==BLOCK_FRONT_RIGHT_WHEEL || blockid==BLOCK_REAR_LEFT_WHEEL || blockid==BLOCK_REAR_RIGHT_WHEEL;
}

inline bool IsGunItemID(int itemid)
{
	return itemid == 15000 || itemid == 15001 || itemid == 15002 || itemid == 15004;
}

inline bool isBoomerangItem(int itemid) 
{
	return itemid == ITEM_BOOMERANG || (itemid >= ITEM_BOOMERANG_STICK && itemid <= ITEM_DOUBLE_BOOMERANG); //回旋镖组合类型
}

inline bool isDoubleWeapon(int itemId) //双持武器
{
	return itemId >= 540000 && itemId <= 545000;
}

inline bool IsLeavesBlockID(int blockid)
{
	static const int sBlockids[][2] = {
		{BLOCK_LEAVE_OAK, BLOCK_LEAVE_WALNUT},
		{BLOCK_LEAVE_BAMBOO, 0},
		{BLOCK_LEAVE_PEACH, 0},
		{BLOCK_PLANTSPACE_HASLEAF_LEAVS, 0},
		{BLOCK_LEAVE_BANANA, 0},
		{BLOCK_LEAVE_STAR_ARBOR, BLOCK_LEAVE_ARBOR},
		{BLOCK_LEAVE_HARVEST_FRUIT, BLOCK_LEAVE_HARVEST_BIRCH},
		{BLOCK_LEAVE_HOLY, 0},
		{BLOCK_LEAVE_POPULUS, 0},
		{BLCOK_COCONUT_LEAF, 0},
		{BLCOK_COCONUT_DIF_LEAF, 0},
		{BLOCK_VOID_TREE_LEAVES, 0},
		{BLOCK_VOID_FRUIT, BLOCK_VOID_FRUIT_END},
		{BLOCK_LEAVE_BREAD, 0}
	};
	static const unsigned sBlockNum = sizeof(sBlockids) / sizeof(sBlockids[0]);
	for (unsigned i = 0; i < sBlockNum; i++)
	{
		if (sBlockids[i][1] == 0) // 单个
		{
			if (sBlockids[i][0] == blockid)
				return true;
		}
		else if (blockid >= sBlockids[i][0] && blockid <= sBlockids[i][1]) // 区间
		{
			return true;
		}
	}
	return false;
}

inline bool IsAirBlockID(int blockid)
{
	return blockid==0 || blockid==BLOCK_PLANTSPACE_OXYGEN;
}

inline bool IsHookBlockID(int blockid)
{
	return blockid==ITEM_HOOK || blockid==ITEM_ATTRACT;
}

inline bool IsMantleID(int itemid)
{
	return (itemid >= 12205 && itemid <= 12210) || (itemid >= 12254 && itemid <= 12270) || itemid == ITEM_OXYGEN_MASK || itemid == ITEM_COTTON_CLOAK || itemid == ITEM_DEFENSE_SAND;
}

inline bool IsWoodBlockID(int blockid)
{
	static const int sBlockids[][2] = {
		{BLOCK_WOOD_OAK, BLOCK_WOOD_WALNUT},
		{BLOCK_BAMBOO, 0},
		{BLOCK_WOOD_PEACH, 0},
		{BLOCK_PLANTSPACE_HASLEAF_WOODS, 0},
		{BLOCK_PLANTSPACE_WOODS, 0},
		{BLOCK_WOOD_BANANA, 0},
		{BLOCK_WOOD_HEART_ARBOR, 0},
		{BLOCK_WOOD_ARBOR, 0},
		{BLOCK_WOOD_HOLY, 0},
		{BLOCK_WOOD_POPULUS, 0},
		{BLOCK_COCONUE_WOOD, 0},
		{BLOCK_VOID_TREE, 0 },
		{BLOCK_WOOD_BREAD, 0},
		{BLOCK_WOOD_ACACIA, 0},
		{BLOCK_WOOD_GLOWING, 0},
	};
	static const unsigned sBlockNum = sizeof(sBlockids) / sizeof(sBlockids[0]);
	for (unsigned i = 0; i < sBlockNum; i++)
	{
		if (sBlockids[i][1] == 0) // 单个
		{
			if (sBlockids[i][0] == blockid)
				return true;
		}
		else if (blockid >= sBlockids[i][0] && blockid <= sBlockids[i][1]) // 区间
		{
			return true;
		}
	}
	return false;
}

// 树枝方块判断
inline bool IsBranchBlockID(int blockid)
{
	return (blockid >= BLOCK_BRANCH_FRUIT_THICK && blockid <= BLOCK_BRANCH_BRICH_THIN)
		|| (blockid == BLOCK_BRANCH_POPULUS_THICK || blockid == BLOCK_BRANCH_POPULUS_THIN)
		|| (blockid == BLOCk_BRANCH_REDWOOD_THICK || blockid == BLOCk_BRANCH_REDWOOD_THIN)
		|| (blockid == BLOCK_VOID_TREE_THICK_BRANCH || blockid == BLOCK_VOID_TREE_THIN_BRANCH);
}

inline bool isDriftsandBlockID(int blockid)
{
	return blockid == BLOCK_FLOW_SAND || blockid == BLOCK_STILL_SAND;
}

// 草方块，小草和枯草
inline bool IsNormalGrassBlockID(int blockid)
{
	return blockid == BLOCK_WITHERED_GRASS || blockid == BLOCK_TALL_GRASS;
}

//星球云Block判断
inline bool IsCloudBlockID(int blockid)
{
	return blockid==BLOCK_PLANTSPACE_CLOUD;
}

EXPORT_SANDBOXENGINE bool IsWaterBlockID(int blockid);
//{
//	return blockid==BLOCK_STILL_WATER || blockid==BLOCK_FLOW_WATER;
//}

inline bool IsLilyBlockID(int blockid) //浮萍和荷叶
{
	return blockid==247 || blockid==248;
}

inline bool isWaterPlantID(int blockid)
{
	return (blockid > 313 && blockid < 321) || blockid == BLOCK_WATER_WEED || blockid == BLOCK_SEA_WEED || (blockid > 493 && blockid < 496);
}

EXPORT_SANDBOXENGINE bool IsLavaBlock(int blockid);
//{
//	return blockid==BLOCK_STILL_LAVA || blockid==BLOCK_FLOW_LAVA;
//}

EXPORT_SANDBOXENGINE bool IsHoneyBlock(int blockid);
//{
//	return blockid==BLOCK_STILL_HONEY || blockid==BLOCK_FLOW_HONEY;
//}

EXPORT_SANDBOXENGINE bool IsFluidBlock(int blockid);
//{
//	return (blockid>=BLOCK_STILL_WATER && blockid<=BLOCK_FLOW_LAVA) || IsHoneyBlock(blockid);
//}
/*
inline bool IsSaplingBlockID(int blockid)
{
    return isSapling(blockid);
	//return blockid>=BLOCK_SAPLING_OAK && blockid<=BLOCK_SAPLING_OAK+MAX_TREE_TYPE;
}*/

inline bool IsHurtBlock(int blockid)
{
	return blockid == BLOCK_BONFIRE || blockid == BLOCK_CACTUS || blockid == BLOCK_THICKET;
}

inline bool canSpawnTree(int blockid)
{ 
	//return blockid==BLOCK_GRASS || blockid==BLOCK_DIRT || blockid==BLOCK_BURYLAND || blockid==BLOCK_SAND;
	//return true;
	return blockid==BLOCK_GRASS || blockid==BLOCK_DIRT || blockid==BLOCK_BURYLAND || blockid == BLOCK_FARMLAND_PIT || blockid==BLOCK_PLANTSPACE_BURYLAND || blockid==BLOCK_PLANTSPACE_STONE || blockid== BLOCK_PLANTSPACE_SOIL || blockid == BLOCK_DIRT_FREEZE_PIT || blockid == BLOCK_DIRT_FREEZE;
}

inline bool IsStairBlock(int blockid)
{
	return (blockid>=520&&blockid<=525) || (blockid>=527&&blockid<=531) || (blockid >= 566 && blockid <= 567) || (blockid >= 3544 && blockid <= 3547);//169000-169999为多边形楼梯方块
}
inline bool IsHalfBlock(int blockid)
{
	return (3503 <= blockid && 161999 >= blockid) || 377 == blockid || 542 == blockid || 564 == blockid || 565 == blockid || 570 == blockid || 574 == blockid || 1382 == blockid || 1718 == blockid
		|| (506 <= blockid && 519 >= blockid) || (749 <= blockid && 752 >= blockid);//3503-161999为多边形1/4薄砖方块 
}

inline bool IsVerticalHalfBlock(int blockid)
{
	return (152 <= blockid && 174 >= blockid) || (1355 <= blockid && 1376 >= blockid) || blockid == 1388 || (3519 <= blockid && 3523 >= blockid);//竖薄砖
}

//inline bool IsFenceBlock(int blockid)
//{
//	return  blockid==BLOCK_FENCEGATE || (blockid>=BLOCK_COBBLE_WALL && blockid<=BLOCK_COBBLE_WALL+1);
//}

inline bool IsFenceBlock(int blockid)
{
	return blockid == 526 || blockid == 534 || blockid == 535 || blockid == 538 || blockid == 539 || blockid == 548 || blockid == 549 ||
		(blockid >= 551 && blockid <= 554) || blockid == 568 || blockid == 572 || blockid == 576 || blockid == 926 || blockid == 927
		|| blockid == 1239 || blockid == 1240 || blockid == 1241 || blockid == 1242 || blockid == 1823 || blockid == 1832 || blockid == 1851 || blockid == 1869 || blockid == 1880 || blockid == 390006
		|| blockid == BLOCK_CHINA_FENCE || blockid == BLOCK_WHITE_STONE_FENCE;
}

inline bool IsDoorBlock(int blockid)
{
	return blockid==BLOCK_WOODDOOR || blockid>=854&&blockid<=860 || blockid == BLOCK_IRONDOOR || blockid == 1111 || blockid == 1101 || blockid == 1567 || blockid == 1591 || blockid == 1615 || blockid == 1639 || blockid == 1663 || blockid == 1708 || blockid == 1739 || blockid == 1834 || blockid == 1853 || blockid == 1868 || blockid == 1879 || blockid == 1227 || blockid == 1228 || blockid == 150016 || blockid == 390007 || blockid == 390022 || blockid == BLOCK_CHINA_DOOR || blockid == BLOCK_CHINA_DOOR2;
}

EXPORT_SANDBOXENGINE bool IsWoodDoorBlock(int blockid);

inline bool IsFlexibleBlock(int blockid)
{
	return BLOCK_SPRING_IRON == blockid || BLOCK_SPRING_GOLD == blockid || BLOCK_JOINT_ARM_PRISMATIC == blockid;
}

inline bool IsDistortionBlock(int blockid)
{
	return BLOCK_FENCEGATE_BOO == blockid || BLOCK_FENCEGATE == blockid || BLOCK_TRAPDOOR == blockid;
}

inline bool IsWindowsBlock(int blockid)
{
	return blockid == 555 || blockid == 556 || blockid == 1229 || blockid == 1230 || blockid == 150023 || blockid == 390012;
}

inline bool IsBedBlock(int blockid)
{
	//add 睡袋;睡袋也是床的一种
	return 
		blockid==BLOCK_BED || 
		blockid == BLOCK_OLDBED ||
		blockid>=883&&blockid<=885 || 
		blockid == BLCOK_SLEEPING_BAG || 
		blockid == BLOCK_BED_SIMPLE || 
		blockid == BLOCK_BED_CHINESE || 
		blockid == BLOCK_BED_MODERN || 
		blockid == BLOCK_BED_FAIRY || 
		blockid == BLOCK_BED_SCIENTIFIC || 
		blockid == BLOCK_BED_EUROPEAN || 
		blockid == BLOCK_BED_WESTENRN || 
		blockid == BLOCK_CANVAS || 
		blockid == BLOCK_SHELLBED || 
		blockid == BLOCK_BED_SANRIO_HELLOKITTY || 
		blockid == BLOCK_BED_SANRIO_CINNAMOROLL || 
		blockid == BLOCK_BED_SANRIO_KUROMI || 
		blockid == BLOCK_BED_SANRIO_MELODY || 
		blockid == BLOCK_BED_SANRIO_KEROPPI || 
		blockid == BLOCK_BED_SANRIO_PURIN || 
		blockid == BLOCK_BED_SANRIO_TWINSTAR || 
		blockid == BLOCK_FUR_BED || 
		blockid == BLOCK_WOODEN_BED || 
		blockid == BLOCK_CHINA_BED;
}

inline bool IsBedNotSleepBagBlock(int blockid)
{
	static int blocks[] = {
		150015,1722,883,884,885,1550,1574,1598,1622,1646,1691,1722,BLOCK_BED_SANRIO_HELLOKITTY,BLOCK_BED_SANRIO_CINNAMOROLL,
		BLOCK_BED_SANRIO_KUROMI,BLOCK_BED_SANRIO_MELODY, BLOCK_BED_SANRIO_KEROPPI,
		BLOCK_BED_SANRIO_KEROPPI,BLOCK_BED_SANRIO_PURIN,BLOCK_SHELLBED,BLOCK_BED_SANRIO_TWINSTAR,BLOCK_CHINA_BED

	};
	for (int i = 0; i < sizeof(blocks)/sizeof(int); i++)
	{
		if (blockid == blocks[i])
			return true;
	}
	return false;
	// 由于基础木床已改成睡袋 这里需区分其他床是否要展示模型
	//return blockid == BLOCK_BED_SANRIO_HELLOKITTY || blockid == BLOCK_BED_SANRIO_CINNAMOROLL || blockid == BLOCK_BED_SANRIO_KUROMI || blockid == BLOCK_BED_SANRIO_MELODY || blockid == BLOCK_BED_SANRIO_KEROPPI || blockid == BLOCK_BED_SANRIO_PURIN || blockid == BLOCK_SHELLBED || blockid == BLOCK_BED_SANRIO_TWINSTAR;
}

inline bool IsSleepingbagBlock(int blockid)
{
	return blockid == BLCOK_SLEEPING_BAG;
}

inline bool IsBowItem(int itemid)
{
	return itemid==ITEM_BOW || itemid==ITEM_BOW2;
}

inline bool IsMaoItem(int itemid)
{
	return itemid == 12002 || itemid == 12063;
}

inline bool IsColorableItem(int itemid)
{
	return (itemid == ITEM_COLORED_EGG || itemid == ITEM_COLORED_EGG_SMALL || itemid == ITEM_COLORED_EGGBULLET);
}

inline bool IsFenceGate(int itemid)
{
	return itemid==BLOCK_FENCEGATE || itemid==BLOCK_FENCEGATE_BOO || itemid == 1240 || itemid == 1242;
}

inline bool IsCropsBlock(int itemid)
{
	return itemid == 229 || itemid == 236 || itemid == 241 || 234 == itemid || 230 == itemid || 239 == itemid;
}

//是否是按钮
inline bool isButtonBlock(int blockid) 
{
	return blockid == BLOCK_WOOD_BUTTON || blockid == BLOCK_STONE_BUTTON;
}

//是否是感压板
inline bool isPressureBlock(int blockid)
{
	return blockid == BLOCK_PRESSURE_WOOD || blockid == BLOCK_PRESSURE_STONE || blockid == BLOCK_PRESSURE_BAMBOO;
}

//是否是载具撞墙或者其它生物的时候，可以触发的按钮
inline bool isVehicleTriggerBlock(int blockid)
{
	return isButtonBlock(blockid) || isPressureBlock(blockid);
}

// 是否是树苗
inline bool isSapling(int blockid)
{
	// 改成可以种树木种子了 
	static const int treeSeed[][2] = {
		{11405, 11406}, // 野果种子, 松果种子
		{11408, 11412}, // 氧气果种子, 桃子种子, 白杨果种子, 核桃种子, 球球果种子
	};
	static const unsigned treeSeedNum = sizeof(treeSeed) / sizeof(treeSeed[0]);
	for (unsigned i = 0; i < treeSeedNum; i++)
	{
		if (treeSeed[i][1] == 0) // 单个
		{
			if (treeSeed[i][0] == blockid)
				return true;
		}
		else if (blockid >= treeSeed[i][0] && blockid <= treeSeed[i][1]) // 区间
		{
			return true;
		}
	}

	return (BLOCK_SAPLING_OAK <= blockid && BLOCK_SAPLING_OAK+MAX_TREE_TYPE > blockid) 
        || BLOCK_BAMBOO_SHOOTS == blockid || BLOCK_SAPLING_PEACH == blockid 
        || BLOCK_SAPLING_THICKET == blockid || BLOCK_SAPLING_ARBOR == blockid || BLOCK_SAPLING_BANANA == blockid || BLOCK_VOID_SAPLING == blockid;
}

//斧头
inline bool IsAxeID(int blockid)
{
	return blockid>=11001 && blockid<=11005;
}

inline bool IsAbleUseShovel(int blockid)
{
	///策划要求修改

	//子类型：1 - 剑，2 - 盾，3 - 锤，4 - 矛，5 - 镐，6 - 斧，7 - 铲，8 - 耙，9 - 弓，10 - 杖，11 - 箭，12 - 法球，13 - 回旋镖
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(blockid);

	if (tooldef && (tooldef->SubType == 7))
	{
		return true;
	}
	return false;
}

//铲子(不算木铲子)
inline bool IsShovelID(int blockid)
{
	//策划修改需求了  木铲变成了铜铲了 可以挖了 code-by:liwentao
	// 11021 木铲子(PS: 因为游戏中木铲不能挖土坑种树，得石铲以上才行)
	return (blockid >= 11021 && blockid <= 11025) || IsAbleUseShovel(blockid);
}

inline bool IsAbleUseHoelID(int blockid)
{
	//子类型：1 - 剑，2 - 盾，3 - 锤，4 - 矛，5 - 镐，6 - 斧，7 - 铲，8 - 耙，9 - 弓，10 - 杖，11 - 箭，12 - 法球，13 - 回旋镖
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(blockid);
	if (tooldef && (tooldef->SubType == 8))
	{
		return true;
	}
	return false;
}

//锄头
inline bool IsHoelID(int blockid)
{
	return (blockid >= 11031 && blockid <= 11035) || IsAbleUseHoelID(blockid);
}

//近战武器
inline bool IsShortRangeWeaponID(int blockid)
{
	return blockid>=12001 && blockid<=12005;
}

//近战武器
inline bool IsLongRangeWeaponID(int blockid)
{
	return blockid ==12002 || blockid == 12050 || blockid == 12281;
}

inline bool IsArchitectWeaponID(const int id)
{
	return id == 11613 || id == 11614 || id == 11615 || id == 11616;
}

// 种子
inline bool IsCropSeedID(int itemid)
{
	return itemid == 11404 || itemid == 236 || itemid == 241 || itemid == 11400;
}

//玻璃块
inline bool IsGlassBlockID(int blockid)
{
    return (blockid >= BLOCK_GLASS && blockid <= BLOCK_GLASS_END) || (blockid >= 1206 && blockid <= 1222);
}

//可攀爬的
inline bool IsClimbBlockID(int blockid)
{
    return blockid == BLOCK_LADDER || blockid == BLOCK_VINE || blockid == BLOCK_LADDERIRON || blockid == 2518 || blockid == 152 || blockid == 2513;
}

inline bool IsColorLeaveId(int blockid)
{
	return blockid == BLOCK_LEAVE_JUNGLE ||
		blockid == BLOCK_LEAVE_WALNUT ||
		blockid == BLOCK_LEAVE_BIRCH ||
		blockid == BLOCK_LEAVE_OAK ||
		blockid == BLOCK_LEAVE_PEACH ||
		blockid == BLOCK_LEAVE_SPUCE ||
		blockid == BLOCK_LEAVE_ACACIA ||
		blockid == BLOCK_LEAVE_BREAD ||
		blockid == BLOCK_LEAVE_BAMBOO ||
		blockid == BLOCK_LEAVE_GINKO ||
		blockid == BLOCK_LEAVE_MAPLE ||
		blockid == BLOCK_LEAVE_JACARANDA ||
		blockid == BLOCK_LEAVE_POPLAR;
}

//检查是否可以开垦
inline bool checkCanExploit(const int toolId, const int blockId)
{
	if (IsHoelID(toolId)|| IsAbleUseHoelID(toolId))
	{
		//锄头
		if (blockId == BLOCK_DIRT || blockId == BLOCK_GRASS || blockId == BLOCK_GRASS_WOOD_GRAY|| blockId == BLOCK_REDSOIL)
			return true;
	}
	else if (IsShovelID(toolId)|| IsAbleUseShovel(toolId))
	{
		//铲子
		if (blockId == BLOCK_DIRT || blockId == BLOCK_GRASS || blockId == BLOCK_PLANTSPACE_SOIL|| blockId == BLOCK_REDSOIL || blockId == BLOCK_DIRT_FREEZE)
			return true;
	}

	return false;
}

inline bool IsBucketID(const int toolId)
{
	return toolId == ITEM_WOODEN_BUCKET || toolId == ITEM_BUCKET || toolId == ITEM_TITANIUM_BUCKET || toolId == ITEM_SMALL_GLASS_BOTTLE;
}

//检查是否可以开垦 等需要显示工作进度条
//checkExploit 移动端 锄头 铲子 和其他工具得分开处理
EXPORT_SANDBOXENGINE bool DoByProgress(const int toolId, const int blockId, const bool checkExploit = true);
//{
//	if (checkExploit && checkCanExploit(toolId, blockId))
//	{
//		return true;
//	}
//	else if (toolId == ITEM_WOODEN_BUCKET)
//	{
//		//木桶可以收集水，岩浆(被损毁)
//		if (blockId == BLOCK_STILL_WATER || blockId == BLOCK_STILL_LAVA)
//		{
//			return true;
//		}
//	}
//	else if (toolId == ITEM_BUCKET)
//	{
//		//铁桶可以收集水，满的蜂巢
//		if (blockId == BLOCK_STILL_WATER || blockId == BLOCK_STILL_HONEY || blockId == BLOCK_HIVE_FULL)
//		{
//			return true;
//		}
//	}
//	else if (toolId == ITEM_TITANIUM_BUCKET)
//	{
//		//钛桶可以收集水，满的蜂巢，岩浆
//		if (blockId == BLOCK_STILL_WATER || blockId == BLOCK_STILL_HONEY || blockId == BLOCK_HIVE_FULL || blockId == BLOCK_STILL_LAVA)
//		{
//			return true;
//		}
//	}
//	else if (toolId == ITEM_FLINTSTEEL)
//	{
//		//点火器可以在有氧环境下使用
//		if (blockId == 0 || blockId == BLOCK_PLANTSPACE_OXYGEN)
//		{
//			return true;
//		}
//	}
//	else if (toolId == ITEM_SMALL_GLASS_BOTTLE)
//	{
//		//玻璃瓶收集毒液
//		if (blockId == BLOCK_STILL_VENOM || blockId == BLOCK_STILL_HONEY || blockId == BLOCK_STILL_WATER)
//		{
//			return true;
//		}
//	}
//
//	return false;
//}

inline bool IsBrassEquipID(int itemid)
{
	return itemid >= ITEM_BRASS_HELMET && itemid <= ITEM_BRASS_SHOE;
}

inline bool IsBox(int blockid)
{
	return blockid == BLOCK_CHEST_NORMAL || blockid == BLOCK_CHEST_BIGHT || blockid == BLOCK_CHEST_BIGVT || blockid == 1231|| 150022==blockid || blockid == BLOCK_INK_CHEST_BIGHT || blockid == BLOCK_INK_CHEST_BIGVT;
}

inline bool IsToolsHammer(int itemid) // 是否是工具锤
{
	return ( BLOCK_CRAFTTABLE == itemid || (itemid >= ITEM_STONE_HAMMER && itemid <= ITEM_TITANIUM_HAMMER));
}

//特殊雕像(保存复活点)
inline bool IsSpBlockRevive(int blockid)
{
	//增加星站传送,个人和公共集合点
	return blockid == BLOCK_REVIVAL_STATUE || blockid == BLOCK_BLACKDRAGON_STATUE || blockid == BLOCK_MAGMA_STATUE 
		|| blockid == BLOCK_CHAOSDRAGON_CUP || blockid == BLOCK_STONE_MONUMENT || blockid == BLOCK_STARSTATION_TRANSFER_CONSOLE
		|| blockid == BLOCK_PERSONALSPAWN || blockid == BLOCK_TEAMSPAWN0;
	  
}
inline bool IsWholeTriangleBlock(int blockid)
{
	return 1244 <= blockid && 1266 >= blockid || 1383 == blockid || (blockid >= 3512 && blockid <= 163999);
}

// 三丽鸥系列方块
inline bool IsSanrioBlock(int blockId)
{
	return blockId == 1824 || blockId == 1833 || blockId == 1843 || blockId == 1852 || blockId == 1854 || blockId == 1872 || blockId == 1883;
}

inline bool IsHorizontalhalfTriangleBlock(int blockid)
{
	return 1267 <= blockid && 1310 >= blockid || 1384 == blockid || 1385 == blockid || (blockid >= 3524 && blockid <= 3543);
}

inline bool IsVerticaHalflTriangleBlock(int blockid)
{
	return 1311 <= blockid && 1354 >= blockid || 1386 == blockid || 1387 == blockid || (blockid >= 167000 && blockid <= 167999);
}

inline bool IsHalfTriangleBlock(int blockid)
{
	return IsHorizontalhalfTriangleBlock(blockid) || IsVerticaHalflTriangleBlock(blockid);
}

inline bool IsTriangleBlock(int blockid)
{
	return IsWholeTriangleBlock(blockid) || IsHalfTriangleBlock(blockid);
}


inline bool IsWholeArcBlock(int blockid)
{
	return (blockid >= 171000 && blockid <= 171999);
}
inline bool IsBigHorizontalArcBlock(int blockid)
{
	return (blockid >= 173000 && blockid <= 173999);
}
inline bool IsBigVerticalArcBlock(int blockid)
{
	return (blockid >= 175000 && blockid <= 175999);
}
inline bool IsSmallHorizontalArcBlock(int blockid)
{
	return (blockid >= 172000 && blockid <= 172999);
}
inline bool IsSmallVerticalArcBlock(int blockid)
{
	return (blockid >= 174000 && blockid <= 174999);
}
inline bool IsHorizontalArcBlock(int blockid)
{
	return IsBigHorizontalArcBlock(blockid) || IsSmallHorizontalArcBlock(blockid);
}
inline bool IsVerticalArcBlock(int blockid)
{
	return IsBigVerticalArcBlock(blockid) || IsSmallVerticalArcBlock(blockid);
}
inline bool IsHalfArcBlock(int blockid)
{
	return IsHorizontalArcBlock(blockid) || IsVerticalArcBlock(blockid);
}
inline bool IsArcBlock(int blockid)
{
	return IsWholeArcBlock(blockid) || IsHalfArcBlock(blockid);
}
inline bool IsTriangularPrismBlock(int blockid) //三棱柱方块
{
	return blockid >= BLOCK_TRIANGULARPRISM && blockid <= 170999;
}
// 可染色方块
inline bool IsDyeableBlock(int blockId)
{
	return (blockId >= 600 && blockId <= 682) || (blockId >= 1120 && blockId <= 1135) || (blockId >= 1206 && blockId <= 1222) || (blockId >= 1900 && blockId <= 1983);
}

inline bool IsKeyDoorBlock(int blockid)
{
	return blockid == BLOCK_KEYDOOR;
}

inline bool IsCanEmitBlockLaser(int blockid)
{
	return blockid == BLOCK_STAR_ENERGYLIGHT || blockid == BLOCK_ELECTRIC_DELAY || blockid == BLOCK_ELECTRIC_SPLLITTER || blockid == BLOCK_NON_GATE
		|| blockid == BLOCK_AND_GATE || blockid == BLOCK_OR_GATE || blockid == BLOCK_DETECTION_PIPE || blockid == BLOCK_ELECTRIC_COUNTER
		|| blockid == BLOCK_WIRELESS_COMPARATOR || blockid == BLOCK_WIRELESS_ARITHMATIC || blockid == BLOCK_WIRELESS_RESISTER;
}

inline bool IsMusicBlock(int blockid)
{
	return blockid >= 690 && blockid <= 699;
}

//射线方块
inline bool IsLaserBlock(int blockid)
{
	return blockid == BLOCK_RAY_WIRE;
}

//是否为带电的射线方块
inline bool IsElectriferousLaserBlock(int blockid)
{
	return blockid == BLOCK_RAY_WIRE;
}

//新电路可以在上面摆放传感器的方块
inline bool IsWirlessElctricCanPlaceSenSor(int blockid)
{
	return blockid == BLOCK_STAR_ENERGYLIGHT || blockid == BLOCK_WIRELESS_PISTON_BASE || blockid == BLOCK_NON_GATE || blockid == BLOCK_AND_GATE || blockid == BLOCK_OR_GATE || blockid == 707 || blockid == 708;
}
// 细树枝
inline bool IsSlenderBranchBlockID(int blockid)
{
	return blockid == 333 || blockid == 335 || blockid == 337 || blockid == 339 || blockid == 341 || blockid == 343 || blockid == 350
		|| blockid == 479 || blockid == 11006 || blockid == 150048;
}

// 是否是雪方块
inline bool IsSnowID(int blockid)
{
	return blockid == BLOCK_SNOWPANE;
}

//是否是具有BP文件的预制蓝图ID
inline bool IsBuildBluePrintFileID(int itemid)
{
	return itemid <= 11113 && itemid >= 11104;
}

//是否投掷方块
inline bool IsCatapultBlock(int blockid)
{
	return blockid == BLOCK_CATAPULT;
}

inline bool IsVoidMelonBlock(int blockid)
{
	return (blockid == BLOCK_VOID_MELON_UNKNOWN 
		|| blockid == BLOCK_VOID_MELON_ORIGIN 
		|| blockid == BLOCK_VOID_MELON_SAD 
		|| blockid == BLOCK_VOID_MELON_UNWELL);
}

inline bool IsMossBlock(int blockid)
{
	return blockid == BLOCK_MOSS;
}

// 中箭返还相关的道具
inline bool IsArrowTypeItem(int itemId)
{
	return itemId == ITEM_ARROW || itemId == ITEM_OCEANARROW
		|| itemId == ITEM_IMPULSE || itemId == 12289;
}

inline bool isHorizontalBigChest(int blockid)
{
	return (blockid == BLOCK_CHEST_BIGHT || blockid == BLOCK_INK_CHEST_BIGHT);
}

inline bool isVerticalBigChest(int blockid)
{
	return (blockid == BLOCK_CHEST_BIGVT || blockid == BLOCK_CHEST_BIGVT_SCHOOL || blockid == BLOCK_INK_CHEST_BIGVT);
}


/**
	统一把这块搞在cpp
*/
EXPORT_SANDBOXENGINE bool IsPipelineBlock(int blockid);
EXPORT_SANDBOXENGINE bool IsPipelineFinalBlock(int blockid);
EXPORT_SANDBOXENGINE bool IsPipleConnectBlock(int blockid);
EXPORT_SANDBOXENGINE bool IsPipellineTransferBlock(int blockid);
EXPORT_SANDBOXENGINE int GetDefaultBlockLaserLength(int blockid);
#endif