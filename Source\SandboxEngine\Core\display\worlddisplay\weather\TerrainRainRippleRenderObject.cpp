#include "TerrainRainRippleRenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Render/RenderUtils.h"
#include "Render/ShaderMaterial/MaterialManager.h"
#include "Render/RenderScene.h"
#include "Render/VertexLayouts/MeshVertexLayout.h"
#include "display/worlddisplay/weather/TerrainRainRippleRenderer.h"
using namespace Rainbow;

TerrainRainRippleRenderObject::TerrainRainRippleRenderObject(TerrainRainRippleRenderer* component)
	: RenderObject(component)
{
	m_MeshRenderData.SetVertexLayout(GetMeshVertexLayout(0));
	m_Renderer = component;
}

TerrainRainRippleRenderObject::~TerrainRainRippleRenderObject()
{
}


void TerrainRainRippleRenderObject::ExtractMeshPrimitives(MeshPrimitiveExtractor& extractor, PrimitiveViewNode& viewNode, PerThreadPageAllocator& allocator)
{
	m_RenderBuffer = &m_Renderer->GetMeshData();
	dynamic_array<DrawBuffersRange> drawBuffersRanges(kMemTempAlloc);

	if (m_RenderBuffer->m_VertNum == 0 || m_RenderBuffer->m_IndexNum == 0) return;

	size_t vertexStride = m_RenderBuffer->m_VertStride;
	drawBuffersRanges.emplace_back(DrawBuffersRange::CreateIndexed(
		vertexStride, kPrimitiveTriangles, m_RenderBuffer->m_VertNum, 0, m_RenderBuffer->m_IndexNum, 0));

	m_MeshRenderData.UploadDynamicVertexIndexBuffer(
		GetGfxDevice(),
		m_RenderBuffer->m_VBBufferSize,
		m_RenderBuffer->m_IBBuffserSize,
		vertexStride,
		m_RenderBuffer->m_Mask,
		m_RenderBuffer->m_VB,
		m_RenderBuffer->m_IB,
		drawBuffersRanges
	);

	MeshPrimitiveRenderData& meshPrimitiveData = extractor.AddMeshPrimitiveRenderData(viewNode, allocator);

	meshPrimitiveData.m_MaterialRenderDatas.add(allocator, drawBuffersRanges.size());
	meshPrimitiveData.m_SharedMeshRenderingData = m_MeshRenderData.AcquireSharedMeshRenderingData();
	meshPrimitiveData.m_VertexLayout = m_MeshRenderData.GetVertexLayout();

	for (size_t i = 0; i < drawBuffersRanges.size(); i++)
	{
		m_Renderer->GetRenderMaterial()->CollectSharedMaterialDataList(meshPrimitiveData.m_MaterialRenderDatas[i].m_SharedMaterialDataList);
		auto& meshPrim = extractor.AllocateMeshPrimitive(allocator, 0, i);
		meshPrim.m_DrawBuffersRanges.emplace_back() = drawBuffersRanges[i];
		extractor.AddMeshPrimitive(meshPrim, viewNode);
	}
}
