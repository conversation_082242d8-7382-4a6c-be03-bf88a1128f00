#include "WorldUIMgr.h"
#include "world.h"
#include "defdata.h"
#include "LuaInterfaceProxy.h"
#include "Text3D/ProgressBar3D.h"
#include "Text3D/ImageBoard3D.h"
#include "BlockScene.h"
#include "GRoot.h"
#include "UIPackage.h"
#include "GTextField.h"
#include "GProgressBar.h"


using namespace Rainbow;
#ifndef IWORLD_SERVER_BUILD
	const char BLOCKHARMTIME = 25 * 5;//25tick为1秒
#endif
WorldUIMgr::WorldUIMgr()
//:	m_BlockHPProgressObj(NULL)
{
	//fairygui::UIPackage::addPackage("miniui/miniworld/soclogin");
	//m_blockHealthBar = fairygui::UIPackage::createObject("soclogin", "BlockHPBar")->as<fairygui::GComponent>();
	//m_blockHealthBar->setVisible(false);
	//fairygui::UIRoot->addChild(m_blockHealthBar);
}

WorldUIMgr::~WorldUIMgr()
{
	//if (m_BlockHPProgressObj)
	//{
	//	m_BlockHPProgressObj->DetachFromScene();
	//}
	//if (m_BlockHPLineImage)
	//{
	//	m_BlockHPLineImage->DetachFromScene();
	//}
	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_BlockHPProgressObj);
	//DESTORY_GAMEOBJECT_BY_COMPOENT(m_BlockHPLineImage);
	//m_blockHealthBar->removeFromParent();
}

void WorldUIMgr::showUIBlockHealthBar(World* pworld, const WCoord& blockpos, const WCoord& playerPos, int totalValue, int curValue, int showMode)
{
#ifndef IWORLD_SERVER_BUILD

	if (totalValue <= 0) m_nCurBlockHP = 0;
	else
	{
		if (showMode != 0)
		{
			auto iter = m_mBlockHealthList.find(blockpos);
			if (iter == m_mBlockHealthList.end())
			{
				hideBlockHealthBar();
				return;
			}
		}
		m_nCurBlockHP = (/*totalValue - */curValue) << 16 | (totalValue & 0xffff);
	}
	//if (m_blockHealthBar)
	//{
	//	m_blockHealthBar->setVisible(true);
	//	fairygui::GProgressBar* bar = static_cast<fairygui::GProgressBar*>(m_blockHealthBar);
	//	fairygui::GTextField* label = m_blockHealthBar->getChild("healthValue")->as<fairygui::GTextField>();
	//}
	//if (!m_BlockHPProgressObj)
	//{
	//	m_BlockHPProgressObj = ProgressBarIn3D::Create("img_blood_strip_board_b.png", "img_blood_white.png");//ProgressBarIn3DSOC::Create("miniui/miniworld/ui_common_bar.png", "miniui/miniworld/ui_common_bar_build.png");
	//	m_BlockHPProgressObj->GetGameObject()->SetLayer(kLayerIndexCustom_3DUI);
	//	float dist = 1000.f;
	//	m_BlockHPProgressObj->setBillboardScaleDist(dist);
	//	m_BlockHPProgressObj->setTextBillboardScale(true, 0, dist);
	//	float maxheight = 20.f;
	//	m_BlockHPProgressObj->setMaxHight(maxheight);
	//	m_BlockHPProgressObj->setScaleByWidth(100);
	//	m_BlockHPProgressObj->setTextVisible(true);
	//	m_BlockHPProgressObj->setColor(100,100,255);
	//	m_BlockHPProgressObj->setTextDisplayStyle((ProgressBarIn3D::PROGRESSBAR3D_TEXTDISPLY)1);
	//	//m_BlockHPProgressObj->setShieldByUI(true);
	//	if (pworld)
	//	{
	//		m_BlockHPProgressObj->AttachToScene(pworld->getScene());
	//	}
	//}
	//if (!m_BlockHPLineImage)
	//{
	//	m_BlockHPLineImage = ImageBoard3D::Create(250, 100, 250, 100);
	//	m_BlockHPLineImage->setBkgTexture("miniui/miniworld/ui_common_bar_line.png", 128, 128, 80, -30, 150, 100);
	//	m_BlockHPLineImage->SetVisibleDistance(16 * BLOCK_FSIZE);
	//	if (pworld)
	//	{
	//		m_BlockHPLineImage->AttachToScene(pworld->getScene());
	//	}
	//}
	//if (m_BlockHPProgressObj)
	//{
	//	auto pos = blockpos * 100 +WCoord(50, 80, 50);
	//	auto actorpos = playerPos;
	//	auto dist = actorpos - pos;
	//	auto distv = (dist.toVector3()).GetNormalizedSafe();
	//	pos += WCoord(80 * distv.x, 0, 80 * distv.z);
	//	m_BlockHPProgressObj->setVale(totalValue - curValue, totalValue);
	//	m_BlockHPProgressObj->setVisible(true);
	//	auto barPos = pos;// +WCoord(50, 100, 50);
	//	m_BlockHPProgressObj->SetPosition(barPos.toWorldPos());
	//}
	//if (m_BlockHPLineImage)
	//{
	//	auto barPos = pos + WCoord(50, 50, 50);
	//	m_BlockHPLineImage->setVisible(true);
	//	m_BlockHPLineImage->SetPosition(barPos.toVector3());
	//}

#endif
}

void WorldUIMgr::hideBlockHealthBar()
{
	m_nCurBlockHP = 0;
	//if (m_BlockHPProgressObj) m_BlockHPProgressObj->setVisible(false);
	//if (m_BlockHPLineImage) m_BlockHPLineImage->setVisible(false);
}

void WorldUIMgr::addHarmedBlockToUI(const WCoord& pos)
{
#ifndef IWORLD_SERVER_BUILD
    //// 获取当前时间并保存时间戳，用于计算过期时间
    //auto currentTime = std::chrono::system_clock::now();
    //std::time_t currentTimeT = std::chrono::system_clock::to_time_t(currentTime);
	m_mBlockHealthList[pos] = 0;
#endif
}


void WorldUIMgr::tick()
{
#ifdef IWORLD_SERVER_BUILD
    //// 获取当前时间
    //auto currentTime = std::chrono::system_clock::now();
    //auto time_since_epoch = currentTime.time_since_epoch();
    //// 转换为毫秒
    //auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch);
    //
    //// 每500毫秒执行一次逻辑
    //if (milliseconds.count() - m_llLastLogicRunTime >= 500)
    //{
    //    m_llLastLogicRunTime = milliseconds.count();
    //    std::time_t currentTimeT = std::chrono::system_clock::to_time_t(currentTime);

    //    // 遍历所有方块血条列表
    //    for (auto iter = m_mBlockHealthList.begin(); iter != m_mBlockHealthList.end(); )
    //    {
    //        // 检查方块是否已经存在超过5秒
    //        if (currentTimeT - iter->second >= 5)
    //        {
    //            // 超过5秒则移除
    //            iter = m_mBlockHealthList.erase(iter);
    //        }
    //        else 
    //        {
    //            ++iter;
    //        }
    //    }
    //}
#else
	for (auto iter = m_mBlockHealthList.begin(); iter != m_mBlockHealthList.end(); )
	{
		iter->second++;
		if (iter->second >= BLOCKHARMTIME)
		{
			iter = m_mBlockHealthList.erase(iter);
		}
		else iter++;
	}
#endif
}