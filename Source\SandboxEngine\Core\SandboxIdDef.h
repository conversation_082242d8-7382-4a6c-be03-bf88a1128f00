
#ifndef __SANDBOX_ID_DEF_H__
#define __SANDBOX_ID_DEF_H__

//tolua_begin
enum
{
	BLOCK_AIR = 0,
	BLOCK_BEDROCK = 1,
	BLOCK_STILL_WATER = 3,
	BL<PERSON><PERSON>_FLOW_WATER = 4,
	BLOCK_STILL_LAVA = 5,
	BLOCK_FLOW_LAVA = 6,
	BLOCK_PORTAL_FRAME = 8,
	BLOCK_PORTAL = 9,
	BLOCK_CRYSTAL = 10,
	BLOCK_STILL_HONEY = 11,
	BLOCK_FLOW_HONEY = 12,
	BLOCK_WIND_EROSIONS_STONE = 19,
	BLOCK_ALIEN_AIR = 20,
	BLOCK_PLUTONIC_ROCK = 23,
	BLOCK_DRIFTBOTTLE = 26,
	BLOCK_FAR_DRIFTBOTTLE = 27,
	BLOCK_PLACE_BOTTLE = 28,
	BLOCK_HARD_ICICLE = 31,//冰凌
	BLOCK_WEAK_ICICLE = 32,//脆冰凌
	BLOCK_ICE_CRYSTAL = 35,

	BLOCK_GRASS = 100,
	BLOCK_DIRT = 101,
	BL<PERSON><PERSON>_FARMLAND = 102,
	BLOCK_BURYLAND = 103,
	BLOCK_STONE = 104,
	BLOCK_LICHEN_STONE = 105,
	BLOCK_SAND = 106,
	BLOCK_GRAVEL = 107,
	BLOCK_SANDSTONE = 108,
	BLOCK_CLITTER = 110,
	BLOCK_OBSIDIAN = 112,
	BLOCK_CLAY = 114,
	BLOCK_SNOWPANE = 115,
	BLOCK_SEDIMENTARYROCK = 23,
	BLOCK_FARMLAND_RED=150,//红土耕地
	BLOCK_FARMLAND_PIT = 151,//红土土坑

	BLOCK_DIRT_RED=233,
	BLOCK_DIRT_FREEZE = 120,//冻土
	BLOCK_DIRT_FREEZE_PIT = 121,//冻土坑
	//--------------------------星球---------------------------
	BLOCK_PLANTSPACE_STONE = 116,
	BLOCK_PLANTSPACE_SOIL = 117,
	BLOCK_PLANTSPACE_CLOUD = 118,
	BLOCK_PLANTSPACE_DRYDIRT = 119,
	BLOCK_PLANTSPACE_BURYLAND = 137,
	BLOCK_PLANTSPACE_GAS = 0,
	BLOCK_PLANTSPACE_OXYGEN = 20,
	BLOCK_PLANTSPACE_TOTEM = 1042,
		
	BLOCK_PLANTSPACE_SEEDS = 118, //星球 地表 宇宙种子
	BLOCK_PLANTSPACE_HASLEAF_WOODS = 257,
	BLOCK_PLANTSPACE_HASLEAF_LEAVS = 258,
	BLOCK_PLANTSPACE_HASLEAF_SAPLING = 259,
	BLOCK_PLANTSPACE_WOODS = 260, //星球 地表 宇宙树木

	BLOCK_PLANTSPACE_STARMUSHROOM_WOODS = 261,
	BLOCK_PLANTSPACE_STARMUSHROOM_LEAVS = 270,
	BLOCK_PLANTSPACE_STARMUSHROOM_SAPLING = 263,
	MAX_PLANTSPACE_STARMUSHROOM_LEAVS_TYPE = 13,

	BLOCK_PLANTSPACE_MUTANTMUSHROOM_LEAVS = 266,
	BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_LEAVS = 267,
	BLOCK_PLANTSPACE_MUTANTMUSHROOM_WOODS = 268,
	BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_WOODS = 286,

	BLOCK_PLANTSPACE_RAINBOWGRASS = 269,

	BLOCK_PLANTSPACE_JEWS = 1043,
	BLOCK_PLANTSPACE_POLLUTION = 138,
	BLOCK_PLANTSPACE_GIANTBATTLETOTEM = 139,
	BLOCK_PLANTSPACE_GIANTTOTEM = 1049,
	BLOCK_NO_SAWTOOTH = 287,//无锯齿蕨
	BLOCK_SAWTOOTH = 288,//有锯齿蕨
	//-------------------------------------------------------------------
	
	BLOCK_SNOW = 122,//雪堆
	BLOCK_ICE = 123,//冰块
	BLOCK_BRITTLEICE =113,//脆冰
	BLOCK_SULPHURROCK = 124,
	BLOCK_HOTSAND = 125,
	BLOCK_HOTCRYSTAL = 132,
	BLOCK_HIVE_EMPTY = 133,
	BLOCK_HIVE_FULL = 134,
	BLOCK_WOOD_OAK = 200,
	BLOCK_WOOD_SPUCE = 201,
	BLOCK_WOOD_BIRCH = 202,
	BLOCK_WOOD_JUNGLE = 203,
	BLOCK_WOOD_WALNUT = 205,
    BLOCK_WOOD_BANANA      = 386,//香蕉树干
    BLOCK_WOOD_HEART_ARBOR = 396, // 雨林之芯乔木
	BLOCK_WOOD_ARBOR       = 579, // 雨林乔木原木
	BLOCK_WOOD_HOLY			= 590, //神圣木
	BLOCK_WOOD_HOLY_CORE	= 593, //神圣木-核心

	BLOCK_SUN_FLOWER = 300,		//风铃花
	BLOCK_RUOLAN = 301,			//若兰
	BLOCK_STAR_FLOWER = 302,	//星辰花
	BLOCK_DRAGON_FLOWER = 303,	//龙血花
	BLOCK_HYACINTH = 304,		//风信子
	BLOCK_DRAGON_GRASS = 305,	//龙血草
	BLOCK_DRAGON_TREE = 306,	//龙血树
	BLOCK_RED_MAGUEY = 307,		//红色龙舌兰
	BLOCK_ORANGE_MAGUEY = 308,	//橙色龙舌兰
	BLOCK_GREY_MAGUEY = 309,	//灰色龙舌兰
	BLOCK_PINK__MAGUEY = 310,	//粉色龙舌兰
	BLOCK_WHIT_EMAGUEY = 311,	//白椰花
	BLOCK_YELLOW_FLOWER = 312,	//黄钟花
	BLOCK_FLOWER_END = 313,		//ROSE BUSH 野蔷薇
	MAX_TREE_TYPE = 6,

	BLOCK_SAPLING_OAK		= 212,
	BLOCK_SAPLING_SPUCE		= 213,
	BLOCK_SAPLING_BIRCH		= 214,
	BLOCK_SAPLING_JUNGLE	= 215,
    BLOCK_SAPLING_THICKET	= 380,	//	灌木树苗
	BLOCK_SAPLING_ARBOR		= 382,	// 雨林乔木树苗
	BLOCK_SAPLING_BANANA	= 383,	//	香蕉树苗
	BLOCK_COTTON			= 469,	// 棉花树		


	BLOCK_LEAVE_OAK			= 218,	// 果木树叶
	BLOCK_LEAVE_SPUCE		= 219,	// 落叶松树叶
	BLOCK_LEAVE_BIRCH		= 220,	// 白杨树叶
	BLOCK_LEAVE_JUNGLE		= 221,	// 红杉树叶
	BLOCK_LEAVE_WALNUT		= 223,	// 胡桃树叶
	BLOCK_LEAVE_BANANA		= 384,	// 香蕉树叶//BLOCK_LEAF_BANANA,//香蕉树叶
    BLOCK_LEAVE_STAR_ARBOR	= 394,	// 星光粉尘树叶
    BLOCK_LEAVE_ARBOR		= 395,	// 雨林乔木树叶
	BLOCK_LEAVE_HOLY		= 592,	// 神圣树叶

	BLOCK_STONE_WALL = 289, //锯齿碎石墙
	BLOCK_MOSSYSTONE_WALL = 290, //锯齿青石墙
	BLOCK_HORASROCK_WALL = 291, //锯齿星球岩墙

	BLOCK_LEAVE_HARVEST_FRUIT	= 324,	// 结果的果树树叶
	BLOCK_PINCONE = 325,				// 松果
	BLOCK_LEAVE_HARVEST_ARBOR	= 326,	// 结果的乔木树叶
	BLOCK_LEAVE_HARVEST_GEN		= 327,	// 结果的珍木树叶
	BLOCK_LEAVE_HARVEST_JUNGLE	= 328,	// 结果的红杉树叶
	BLOCK_LEAVE_HARVEST_WALNUT	= 329,	// 结果的胡桃树叶
	BLOCK_LEAVE_HARVEST_PEACH	= 330,	// 结果的桃树树叶
	BLOCK_LEAVE_HARVEST_BIRCH	= 331,	// 结果的白杨树叶

	BLOCK_BRANCH_FRUIT_THICK	= 332,	// 粗果树树枝
	BLOCK_BRANCH_FRUIT_THIN		= 333,	// 细果树树枝
	BLOCK_BRANCH_CATHAYA_THICK	= 334,	// 粗落叶松树枝
	BLOCK_BRANCH_CATHAYA_THIN	= 335,	// 细落叶松树枝
	BLOCK_BRANCH_GEN_THICK		= 336,	// 粗珍木树枝
	BLOCK_BRANCH_GEN_THIN		= 337,	// 细珍木树枝
	BLOCK_BRANCH_WALNUT_THICK	= 338,	// 粗胡桃树树枝
	BLOCK_BRANCH_WALNUT_THIN	= 339,	// 细胡桃树树枝
	BLOCK_BRANCH_PEACH_THICK	= 340,	// 粗桃树树枝
	BLOCK_BRANCH_PEACH_THIN		= 341,	// 细桃树树枝
	BLOCK_BRANCH_BRICH_THICK	= 342,	// 粗白杨树枝
	BLOCK_BRANCH_BRICH_THIN		= 343,  // 细白杨树枝
	BLOCk_BRANCH_REDWOOD_THICK  = 349,	// 粗红杉树枝
	BLOCk_BRANCH_REDWOOD_THIN	= 350,	// 细红杉树枝
	BLOCK_GIANT_SCALLOPS_CLOSE  = 346,  // 巨型扇贝(关闭)
	BLOCK_SAPLING_HOLY = 591, //神圣树苗
	BLOCK_TALL_GRASS = 224,		// 小草
	BLOCK_WITHERED_GRASS = 225,	// 枯草

	BLOCK_MUSHROOM = 226,//土笋
	BLOCK_MUSHROOM_RED = 227,//紫苏
	BLOCK_MUSHROOM_SEED = 200418,//土笋种子
	BLOCK_MUSHROOM_RED_SEED = 200419,//紫苏种子
	BLOCK_MYCELIUM = 464,

    BLOCK_ARBOR  = 379,//乔木果实
    BLOCK_BANANA = 385,//香蕉果实

	BLOCK_THICKET = 392, // 灌木丛
    BLOCK_MOSS = 397,//苔藓
	BLOCK_MOSS_HUGE = 262,//大苔藓
    BLOCK_GRASS_WOOD_GRAY = 398,//草木灰
    BLOCK_GRASS_WOOD_GRAY_FARMLAND = 399,//草木灰耕地
    BLOCK_ALTAR = 580,//祭台
	BLOCK_WEATHERFORECAST = 581, //天气预报
    BLCOK_SLEEPING_BAG = 582,//睡袋

    BLOCK_GOD_STATUE = 578,//雨林神像
    //BLOCK_GOD_STATUE_FRAG = 577,//残缺雨林神像
    BLOCK_GOD_STATUE_BASE = 583,//神像基座

	BLOCK_REDSOIL = 233,
	BLOCK_REDSAND = 128,
	BLCOK_COCONUT_LEAF = 294, //巨大椰叶
	BLCOK_COCONUT_DIF_LEAF = 296, //巨大椰叶
	BLCOK_COCONUT = 295, //椰子
	BLCOK_COCONUT_DIF = 297, //异化大椰子
	BLOCK_RICE = 234,
	BLOCK_COCOA = 237,
	BLOCK_CACTUS = 242,
	BLOCK_WILDC_CORN = 243,//野生玉米
	BLOCK_CACTUSSMALLSEED = 458,//仙人掌幼苗
	BLOCK_CACTUSSEED = 459,//仙人掌苗
	BLOCK_CACTUSFLOWER = 460,//仙人掌花
	BLOCK_CACTUSFRUIT = 461,//仙人掌果实
	BLOCK_OSTRICHEGG = 462,//鸵鸟蛋
	BLOCK_CACTUSBRANCH = 463,//仙人掌分支	
	BLOCK_EMERALD = 406,
	BLOCK_EMERALDBLOCK = 409,
	BLOCK_FIRE = 500,
	BLOCK_COBBLESTONE = 505,
	BLOCK_GLOWSTONE = 536,
	BLOCK_FLUORESCENT = 550,
	BLOCK_POISON = 584,

	BLOCK_REED = 228,
	BLOCK_WHEAT = 229,
	BLOCK_WATERMELON = 239,
	BLOCK_MELONSTEM = 240,
	BLOCK_PUMKINSTEM = 231,
	BLOCK_POTATO = 241,
	BLOCK_CARROT = 236,

	BLOCK_LADDER = 813,
	BLOCK_LADDERIRON = 390112,
	BLOCK_PUMKIN = 230,
	BLOCK_VINE = 232,
	BLOCK_TRAPDOOR = 731,
	BLOCK_WOODDOOR = 812,
	BLOCK_IRONDOOR = 814,

	BLOCK_BRICK_MOSSY = 503,
	BLOCK_FENCE = 534,
	BLOCK_FENCEGATE = 535,
	BLOCK_FENCEGATE_BOO = 569,
	BLOCK_FENCE_HELL = 538,
	BLOCK_COBBLE_WALL = 548,
	BLOCK_MOSSY_WALL = 549,

	BLOCK_HONEY_PRODUCT1 = 558,
	BLOCK_HONEY_PRODUCT2 = 559,
	BLOCK_HONEY_PRODUCT3 = 560,
	BLOCK_HONEY_PRODUCT4 = 561,

	//----------  楼梯  --------------//
	BLOCK_FRUITTREEWOODSTAIR = 520,
	BLOCK_LARCHWOODSTAIR = 521,
	BLOCK_POPLARWOODSTAIR = 522,
	BLOCK_REDCEDARWOODSTAIR = 523,
	BLOCK_NANMUWOODSTAIR = 524,
	BLOCK_WALNUTWOODSTAIR = 525,
	BLOCK_SANDSTONESTAIR = 527,
	BLOCK_STONECUBESTAIR = 529,
	BLOCK_REDBRICKSTAIR = 530,
	BLOCK_STONEBRICKSTAIR = 531,
	BLOCK_SULFURBRICKSTAIR = 532,
	BLOCK_SILICASTAIR = 533,
	//----------  楼梯  --------------//
	//--火山祭坛钥匙方块--start
	BLOCK_MILA_STAR_PEDESTAL = 140,//对应钥匙id:587
	BLOCK_FLAME_STAR_PEDESTAL = 141,//对应钥匙id:588
	BLOCK_Q_EYE_STAR_PEDESTAL = 142,//对应钥匙id:589
	BLOCK_BROKEN_PEDESTAL = 143,
	BLOCK_ALTAR_STELA = 586,
	BLOCK_KEY_OF_FRUIT = 587,
	BLOCK_KEY_OF_BROKEN_SWORD = 588,
	BLOCK_KEY_OF_STONE_EYE = 589,
	//--火山祭坛方块--end

	BLOCK_WOOL = 600,
    BLOCK_GLASS = 632,
	BLOCK_GLASS_START = 633,
	BLOCK_GLASS_END = 648,

	BLOCK_WHITEHARDSAND = 667,

	BLOCK_REPEATER_OFF = 702,
	BLOCK_REPEATER_ON = 703,
	BLOCK_CHEST_NORMAL = 801,
	BLOCK_COMPARATOR_OFF = 704,
	BLOCK_COMPARATOR_ON = 705,
	BLOCK_ARITHMATIC = 1033,

	BLOCK_PRESSURE_WOOD = 711,
	BLOCK_PRESSURE_STONE = 712,
	BLOCK_PRESSURE_BAMBOO = 713,

	BLOCK_PISTON_STICKYBASE = 718,
	BLOCK_PISTON_BASE = 719,
	BLOCK_LUKER = 721,
	BLOCK_PISTON_EXTENSION = 840,
	BLOCK_PISTON_MOVING = 841,
	BLOCK_PISTON_MOVING2 = 1025,
	BLOCK_RANGE_PISTON = 1023,
	BLOCK_RANGE_PISTON_STICKY = 1024,

	BLOCK_SIGNS_STAND = 815,
	BLOCK_SIGNS_HANG = 818,
	BLOCK_SIGNS_ARROW = 200005,
	BLOCK_SIGNS_ARROW_HANG = 200007,
	BLOCK_BOOK_SHELF = 820,
	BLOCK_FUNNEL = 839,
	BLOCK_SPRING_IRON = 842,
	BLOCK_SPRING_EXTENSION = 843,
	BLOCK_SPRING_GOLD = 844,

	BLOCK_WOOD_BUTTON = 715,
	BLOCK_STONE_BUTTON = 716,
	BLOCK_EMITTER = 717,
	BLOCK_THROWER = 720,
	BLOCK_RAIL = 725,
	BLOCK_RAIL_ACCEL = 729,

	
	BLOCK_LAVA_STONE = 740,		// 熔岩之石
	BLOCK_CHAOS_STONE = 741,	// 混乱之石
	BLOCK_ANCIENT_EGG = 765,	// 远古之石头
	
	BLOCK_TORCH = 817,
	BLOCK_TORCH_OFF = 1046,
	BLOCK_SMALL_TORCH = 298,	// 燃烧的火炬
	BLOCK_SMALL_TORCH_OFF = 299,
	BLOCK_CRAFTTABLE = 800,
	BLOCK_CHEST = 801,
	BLOCK_CHEST_WELL = 730,
	BLOCK_CHEST_DUNGEON = 734,
	BLOCK_CHAOSDRAGON_CUP = 742,
	BLOCK_CHESTLOCKED_S = 845,
	BLOCK_CHESTLOCKED_M = 846,
	BLOCK_OLDBED = 828,
	BLOCK_BED = 2409,
	BLOCK_SHELLBED = 1223,
	BLOCK_CANVAS = 484,
	BLOCK_TNT = 834,

	BLOCK_BAMBOO = 251,
	BLOCK_LEAVE_BAMBOO = 252,
	BLOCK_BAMBOO_SHOOTS = 253,
	BLOCK_WOOD_PEACH = 254,
	BLOCK_LEAVE_PEACH = 255,
	BLOCK_SAPLING_PEACH = 256,

	BLOCK_SAPLING_POPULUS = 476,	//胡杨树苗
	BLOCK_WOOD_POPULUS = 477,
	BLOCK_BRANCH_POPULUS_THICK = 478,
	BLOCK_BRANCH_POPULUS_THIN = 479,
	BLOCK_LEAVE_POPULUS = 480,
	BLOCK_POPULUS_FLOWER = 481,
	BLOCK_POPULUS_TEARS = 482,
	BLOCK_WINTER_FLOWER_SEED = 760,
	BLOCK_WINTER_FLOWER = 761,
	BLOCK_SAPLING_THICKET_WITHERED = 762,
	BLOCK_THICK_WITHERED = 763,
	BLOCK_SNOW_LOTUS = 764,
	BLOCK_WHITE_FLOWER = 311,
	BLOCK_PETROLEUM = 448,
	BLOCK_SMOOTH_SAND_STONE = 540,

	BLOCK_CATAPULT = 722,   // 投射装置
	BLOCK_SHIP_CHEST = 758, // 沉船宝箱
	BLOCK_FLAG0 = 919,
	BLOCK_TEAMSPAWN0 = 990, //白色复活重生点
	BLOCK_TEAMSTART0 = 1072, //白色初始出生点
	BLOCK_PERSONALSPAWN = 1079, // 个人复活重生点
	BLOCK_INITITEMBOX = 998,
	BLOCK_REVIVEITEMBOX = 999,
	BLOCK_RANDITEMBOX = 1035,
	BLOCK_REPLICATOR_OFF = 1000,
	BLOCK_REPLICATOR_ON = 1002,

	BLOCK_MECHA_MARKER1 = 1004,
	BLOCK_MECHA_MARKER4 = 1007,
	BLOCK_MECHA_SLIDER = 1008,
	BLOCK_MECHA_ROTATE = 1010,
	BLOCK_TEAM_PREPOINT = 1036,

	BLOCK_HARDWIRE_B = 706, //蓝色线
	BLOCK_HARDWIRE_R = 1009, //红色线

	BLOCK_PATH_DIR = 1011,
	BLOCK_PATH_STOP = 1012,
	BLOCK_STAR = 1021,

	BLOCK_COLLIDER = 1037,
	BLOCK_MOBCOLLIDER = 1038,
	BLOCK_GOAL = 1039,
	BLOCK_BALLCOLLIDER = 1040,
	
	BLOCK_PHYSXCOLLIDER = 1067,
	BLOCK_FURNACE_OXYGEN = 1045,
	BLOCK_OXYGEN_JAR_EMPTY = 1047,
	BLOCK_OXYGEN_JAR_FULL = 1048,
	BLOCK_PLANTSPACE_DIRTBASE = 1050,

	BLOCK_CHEST_PASSWORD_S = 1055,
	BLOCK_CHEST_PASSWORD_M = 1056,
	BLOCK_PLANTSPACE_OXYGEN_FRUIT = 1051, //氧气果
	BLOCK_STONE_MONUMENT = 1058, //石像纪念碑
	BLOCK_REFLECT_MIRROR = 1060, //反射镜

	BLOCK_REGIONREPLICATOR = 1062,	//区域复制器
	BLOCK_BLUEPRINT = 1063,			//蓝图工作台
	BLOCK_BUILDBULEPRINT = 1064,	//图纸建造方块

	BLOCK_BASKETFRAME = 1068,	//篮框方块

	BLOCK_AIRWALL = 1001,
	BLOCK_AIRWALL_PASS = 1018,

	BLOCK_CHASSIS = 1082, //载具车身方块
	BLOCK_FRONT_LEFT_WHEEL = 1083, //载具左前车轮方块
	BLOCK_FRONT_RIGHT_WHEEL = 1086, //载具右前车轮方块
	BLOCK_REAR_LEFT_WHEEL = 1087, //载具左后车轮方块
	BLOCK_REAR_RIGHT_WHEEL = 1088, //载具右后车轮方块
	BLOCK_DRIVERS_SEAT = 1084, //载具驾驶座方块
	BLOCK_WORKSHOP = 1085, //机械车间方块
	BLOCK_VEHICLEENGINE = 1089,	//有限引擎方块
	BLOCK_INFINITEENGINE = 1090,
	BLOCK_PASSENGER_SEAT = 1091,
	BLOCK_THRUSTER = 1093, //推进器方块
	BLOCK_ACTIONER = 1092,
	BLOCK_WING = 1094, //机翼方块
	BLOCK_EMPENNAGE = 1095, //尾翼方块
	BLOCK_STHRUSTER = 1096, //航天推进器方块
	BLOCK_FUEL_TANK = 1099, //油箱

	//船只组件方块
	BLOCK_BOAT_THRUSTER = 1189, //螺旋推进器，用于船只
	BLOCK_BOAT_RUDDER = 1188, //船舵
	BLOCK_BOAT_FLOATBUCKET = 1190, //漂浮桶

	//物理关节方块
	BLOCK_PRISMATIC_JOINT_END = 1004, //滑动关节终点方块
	BLOCK_PRISMATIC_JOINT_START = 1005, //滑动关节起点方块
	BLOCK_PRISMATIC_JOINT = 1008, //滑动关节方块
	BLOCK_REVOLUTE_JOINT = 1010, //转动关节方块
	BLOCK_TRANSFER_CORE = 1140,//传送点核心方块
	BLOCK_SUSPENSION_JOINT = 1164, //悬挂关节方块
	BLOCK_JOINT_SWITCH = 724, //关节开关方块
	BLOCK_TRANSFERCORE = 1140, // 传送方块核心
	BLOCK_TRANSFER = 1141,	// 传送方块
	BLOCK_FESTIVE_LANTERN = 1146, //20210825：庆典花灯  codeby： yaoxinqun
	BLOCK_JOINT_ARM_PRISMATIC = 1161,//液压臂关节方块
	BLOCK_JOINT_SPHERICAL = 1158,//
	BLOCK_JOINT_T_REVOLUTE = 1159,//T铰链关节方块
	BLOCK_CLAW = 1160,//爪子
	BLOCK_ROPE_HEAD = 1165, //绞绳头
	BLOCK_ROPE_TAIL = 1166, //绞绳尾部
	BLOCK_ROPE = 1167,  //绞绳
	BLOCK_BONFIRE = 1200,  //篝火
	BLOCK_SENSOR_DISTANCE = 1168, //红外感应
	BLOCK_SENSOR_VALUE_LIGHT = 1169, //亮度感应
	BLOCK_SENSOR_VALUE_HEIGHT = 1170, //高度感应
	BLOCK_SENSOR_VALUE_VECLOCITY = 1171, //速度感应
	BLOCK_TOMBSTONE = 1201,//墓碑
	BLOCK_FERTILELAND = 1799,			//肥沃的土地
	BLOCK_VISUALIZER = 1184,			//星能展台
	BLOCK_RADIATION = 2020,			  // 辐射源
	BLOCK_DECOMPOSITION_LOW = 2429,  // 低级分解机
	BLOCK_DECOMPOSITION_HIGH = 2430, // 高级分解机
	ITEM_GRASS_ASH = 62804,				//草木灰
	ITEM_BELLS  = 1243,					//贝壳风铃
	ITEM_DUMMY = 200001,				//假人模型
	ITEM_MANUAL_EMITTER = 200002,        //手持发射器

	ITEM_SOCTORCH = 2426,				//soc火把

	//星站方块
	BLOCK_STARSTATION_TRANSFER_CONSOLE = 594, //星站传送控制台
	BLOCK_STARSTATION_TRANSFER_CABIN_LEVEL1 = 595,  //星站一级传送舱
	BLOCK_STARSTATION_TRANSFER_CABIN_LEVEL2 = 597,  //星站二级传送舱
	BLOCK_STARSTATION_CARGO	= 1098,	// 星链货运终端

	ITEM_SOC_KEY = 6000,
	ITEM_SOC_KEYLOCK = 6001,

	BLOCK_TRIANGULARPRISM = 170001,					//三棱柱
	ITEM_ENERGY_CORE = 11331,						//能源核心
	ITEM_INTELLIGENT_UNIT = 11335,					//智能单元

	ITEM_BLUEPRINT = 1064, //蓝图图纸
	ITEM_BLOCKMODELCRAFT = 1138, //方块模型工作台
	ITEM_ITEMMODELCRAFT = 1142, //道具模型工作台
	ITEM_ACTORMODELCRAFT = 1150, //生物模型编辑工作台
	ITEM_CUSTOMMODELMAKER = 1151, //自定义模型制作器

	SOCDoor = 2514,

	ITEM_TRANSFERSCROLL = 10024, //星球传送卷

	ITEM_WOOD_SHOVEL = 11021,		//木铲
	ITEM_ROCKET_CRAFT = 11072,
	ITEM_AERO_FUEL = 11096,
	ITEM_SANDY_SOIL = 11823,
	ITEM_ARROW = 12051,
	ITEM_BOW = 12050,
	ITEM_BOW2 = 12056,
	ITEM_BOW3 = 12291,
	ITEM_BOW4 = 12061,
	ITEM_COLORED_EGG_SMALL = 12240,
	ITEM_COLORED_GUN = 12247,
	ITEM_COLORED_EGG = 12248,
	ITEM_COLORED_EGGBULLET = 12249,
	ITEM_ARBATOR = 12283,
	ITEM_COBBLE = 12282,
	ITEM_SLINGSHOT = 12281,
	ITEM_RPG18 = 12284,
	ITEM_MISSILE =12285,
	ITEM_BOMB = 12280,
	ITEM_EGG = 12052,
	ITEM_EGG2 = 12053,
	ITEM_SNOWBALL = 12054,
	ITEM_FOOTBALLWEAR = 12821,
	ITEM_FLINTSTEEL = 11055,
	ITEM_SHEARS = 11056,
	ITEM_SMALL_GLASS_BOTTLE = 11320,
	ITEM_BOTTLED_MILK = 12509,
	ITEM_WOODEN_BUCKET = 11048,
	ITEM_WOODEN_BUCKET_WATER = 11049,
	ITEM_BUCKET = 11050,
	ITEM_BUCKET_WATER = 11051,
	ITEM_BUCKET_HONEY = 11053,
	ITEM_BUCKET_MILK = 12509,
	ITEM_TITANIUM_BUCKET = 11064,
	ITEM_TITANIUM_BUCKET_LAVA = 11052,  //原来的岩浆铁桶，换成了岩浆钛桶，ID不变
	ITEM_TITANIUM_BUCKET_WATER = 11065,
	ITEM_TITANIUM_BUCKET_HONEY = 11066,
	ITEM_ENDER_EYE = 11315,
	ITEM_BONE_POWDER = 11500,
	ITEM_TRAINCAR = 13800,
	ITEM_TRAINCAR_HEAD = 13802,
	ITEM_BOAT = 13807,
	ITEM_BAMBOO_RAFT = 390088,
	ITEM_ROCKET = 13803,
	ITEM_HANDGUN = 15002,
	ITEM_LAVABALL = 15508,

	ITEM_ANCIENT_HORN = 11233,	//远古号角
	ITEM_LAVA_HORN = 11215,		//熔岩号角
	ITEM_CHAOS_HORN = 11216,	//混乱号角

	ITEM_JETPACK = 12253,
	ITEM_FIRE_ROCKET= 12822,
	ITEM_OXYGEN_PACK = 12275,	//氧气背包
	ITEM_OXYGEN_MASK = 12276,	//氧气面罩
	ITEM_FIRESAFETY_PACK = 12277,	//防火服
	ITEM_COTTON_CLOAK = 12279,	//绵绵披风
	ITEM_SNAKEGOD_WING = 11591, //蛇神之翼
	ITEM_DEFENSE_SAND = 11639,	//防沙披风

	ITEM_DIVING_MASK = 11642, //潜水面罩
	ITEM_DIVING_MASK_SUPER = 11645, //高级潜水面罩
	ITEM_DIVING_SUIT = 11643, //潜水服
	ITEM_DIVING_SUIT_SUPER = 11646, //高级潜水服
	ITEM_DIVING_FINS = 11644, //潜水脚蹼，普通
	ITEM_DIVING_FINS_SUPER = 11647, //潜水脚蹼，高级
	ITEM_AIR_BALL = 11651, //空气球
	ITEM_TEASURE_MAP = 12617,

    ITEM_STAVES_MIN = 11580,
    ITEM_STAVES_MAX = 11584,
    ITEM_GOD_STATUE_GEM = 11597,//雨林神像宝石(雨林之眼宝石)

	ITEM_WRENCH = 11070,
	ITEM_BOOK = 11803, //书本
	ITEM_LETTERS_USED = 11804,
	ITEM_LETTERS = 11806,
	ITEM_INSTRUCTION = 11809,
	ITEM_HOOK = 12006,
	ITEM_LASER = 12007,
	ITEM_ATTRACT = 12008,
	ITEM_BLOCK_LASER = 12009,
	ITEM_IMPULSE = 12292,

	ITEM_LASER_BOSS_RED = 15509,
	ITEM_LASER_BOSS_BLUE = 15510,
	ITEM_LASER_BOSS_WHITE = 15511,

	ITEM_SPOUT = 11652,//海灵守卫水柱道具
	ITEM_SEA_SPIRIT_STONE = 11641,//海灵变身石

	ITEM_SPEAR = 12002,
	ITEM_REDFIREWORKDS = 12836,
	ITEM_PURPLEFIREWORKDS = 12837,
	ITEM_GREENFIREWORKDS = 128378,
	ITEM_BULLET = 128378,
	ITEM_BOOMERANG = 11592, //回旋镖
	ITEM_ENERGYSPEAR = 12013, // 能量矛
	ITEM_HARPOON = 12063,//魔鬼鱼叉
	ITEM_OCEANARROW = 12062,//海洋箭
	ITEM_TREASURE_MAP = 12617,
	ITEM_TERRAIN_MAP  = 12627, //地形地图 2024-01-16 add by zhijian for 新增地形地图的需求
	ITEM_CRAB = 13621,//螃蟹
	ITEM_HIPPOCAMPUS = 13623,//非驯服的海马
	ITEM_SMALL_HIPPOCAMPUS = 13624,//小海马
	ITEM_TAME_HIPPOCAMPUS = 13625,//驯服的海马
	ITEM_JELLYFISH  = 13626,//水母
		
	ITEM_WATER_CANNON = 11649, //水压炮
	ITEM_WATER_CANNON_ITEM_SKILL = 340, //水压炮在岸上的技能

	ITEM_GRAVITYGUN = 12293,

	ITEM_MARBLES_BALL = 12295,
	ITEM_BILLIARDS_BALL = 12296,
	ITEM_VOLLEY_BALL = 12297,
	ITEM_WOODEN_BOX = 12298,
	ITEM_TREASURE_DRIFT = 12616,

	ITEM_MILK_SUGAR = 12549,

	BLOCK_UNLOAD = 4095,
	MAX_BLOCK_ID = 4095,
	WEAPON_SNIPPER_ID = 15004,

	ITEM_FRESH_FRUIT = 12500,//樱桃
	ITEM_OXY_FRUIT = 12583,//珍木
	ITEM_REDWOOD = 12602,//红杉
	ITEM_WALNUT = 12603,//胡桃
	ITEM_PEACH = 12601,//桃花
	ITEM_POPLAR = 12604,//白杨
	ITEM_CANNED_BOMB = 15007,
	ITEM_PINEAPPLE_BOMB = 15008,
	BLOCK_BOOK_EDITOR = 1143,	//编书台
	BLOCK_BOOK_CABINET = 1144, //书架
	BLOCK_BOOK = 11803, //书
	ITEM_DEVELOPER = 11100, //开发者道具
	ITEM_BASKETBALLWEAR = 12820,//篮球衣
	ITEM_VEHICLE_LINK_TOOL = 11101, // 连接钳
	ITEM_EQUIT_TEMPLATE = 10114,	//自定义装备模板

	ITEM_ACETYLENE_LAMP = 707,	//电视信号灯
	ITEM_PASSPORT = 10113,//通行证
	ITEM_MAPEDIT = 10500,//地图编辑
	ITEM_CUSTOMMODELPACKING_TOOL = 10501,//微缩打包工具
	MONSTER_VEHICLE = 4002,//物理机械

	ITEM_BANDAGE = 12594, //绷带
	ITEM_THORNBALL = 12619,
	BLOCK_STARBULLET = 12300, //星星弹,月亮坐骑发射使用
	BLOCK_MUSIC_BOX = 1400, //音乐盒
	BLOCK_GRAVITY_SYSTEM = 1402,//重力系统

	BLOCK_LIGHTMUSHROOM = 837,//荧光小菇
	BLOCK_COAGULATIONENERGY = 416,//凝能块
	BLOCK_SIGN = 886,	// 石制留言板
	BLOCK_SIGN_GOLD = 888,  // 金制留言板
	BLOCK_SIGN_LAVA = 890,  // 熔岩留言板
		
	BLOCK_PRODUCER	= 1404,				// 制作台
	BLOCK_PET_NEST	= 1405,				// 宠物窝
	BLOCK_HEARTH1	= 1406,				// 1级灶台方块
	BLOCK_HEARTH2	= 1687,				// 2级灶台方块
	BLOCK_HEARTH3	= 1688,				// 3级灶台方块
	BLOCK_HEARTH4	= 1689,				// 4级灶台方块
	BLOCK_HEARTH5	= 1759,				// 5级灶台方块


	// 家园农作物方块
	BLOCK_HOMELAND_BANANA			= 1458,		// 家园香蕉树
	BLOCK_HOMELAND_MANGO			= 1460,		// 家园芒果树
	BLOCK_HOMELAND_PEAR				= 1461,		// 家园梨子树
	BLOCK_HOMELAND_PEACH			= 1463,		// 家园桃子树
	BLOCK_HOMELAND_BALLBEAN			= 1481,		// 家园球球豆树
	BLOCK_HOMELAND_OXY				= 1482,		// 家园氧气果树

	BLOCK_HOMELAND_MUSHROOM			= 1478,		// 家园小蘑菇
	BLOCK_HOMELAND_MUSHROOMRED		= 1479,		// 家园小红菇

	BLOCK_HOMELAND_TEA				= 1457,		// 家园茶树
	BLOCK_HOMELAND_ORANGE			= 1462,		// 家园柑橘树
	BLOCK_HOMELAND_CHILI			= 1470,		// 家园辣椒树
	BLOCK_HOMELAN_COTTON			= 1471,		// 家园棉花树
	BLOCK_HOMELAND_COFFEE			= 1480,		// 家园咖啡豆树

	BLOCK_HOMELAND_SUNFLOWER		= 1452,		// 家园向日葵
	BLOCK_HOMELAND_RICE				= 1464,		// 家园水稻
	BLOCK_HOMELAND_WHEAT			= 1465,		// 家园小麦
	BLOCK_HOMELAND_POTATO			= 1466,		// 家园土豆
	BLOCK_HOMELAND_REED				= 1468,		// 家园甘蔗
	BLOCK_HOMELAND_FRUITTREE		= 1472,		// 家园果树
	BLOCK_HOMELAND_CATHAYA			= 1473,		// 家园落叶松
	BLOCK_HOMELAND_BIRCH			= 1474,		// 家园白杨树
	BLOCK_HOMELAND_WALNUT			= 1477,		// 家园胡桃树
	BLOCK_HOMELAND_CARROT			= 1486,		// 家园胡萝卜
	BLOCK_HOMELAND_SUNFLOWER_SEED	= 1493,		// 家园向日葵苗
	BLOCK_HOMELAND_WHITEMUMS_SEED	= 1500,		// 家园小白菊苗
	BLOCK_HOMELAND_MELON			= 1501,		// 家园西瓜
	BLOCK_HOMELAND_PUMPKIN			= 1502,		// 家园南瓜
	BLOCK_HOMELAND_POPULUS			= 1507,		// 家园胡杨树

	//20210720： 增加新家园种植作物部分方块声明  codeby： yangzhenyu
	//BLOCKDRAW_ALPHTEST
	BLOCK_HOMELAND_PEANUT			= 1510,		// 家园花生
	BLOCK_HOMELAND_BEET				= 1514,		// 家园甜菜
	BLOCK_HOMELAND_SORBUS			= 1516,		// 家园高丹草(占用两格)
	BLOCK_HOMELAND_RADISH			= 1521,		// 家园萝卜
	BLOCK_HOMELAND_RAPEBLOSSOMS		= 1526,		// 家园油菜花(占用两格)
	BLOCK_HOMELAND_COCONUTTREE		= 1528,		// 家园椰树
	BLOCK_HOMELAND_RASPBERRY		= 1529,		// 家园覆盆子
	BLOCK_HOMELAND_PEA				= 1533,		// 家园豌豆
	BLOCK_HOMELAND_BLUEBERRYTREE	= 1534,		// 家园蓝莓
	BLOCK_HOMELAND_STRAWBERRY		= 1535,		// 家园草莓 
	BLOCK_HOMELAND_GRAPETREE		= 1537,		// 家园葡萄树 
	
	//占用两格
	BLOCK_HOMELAND_BARLEYWHEAT		= 1511,		// 家园大麦
	BLOCK_HOMELAND_SESAME			= 1512,		// 家园芝麻
	BLOCK_HOMELAND_TOMATO			= 1515,		// 家园西红柿
	BLOCK_HOMELAND_COM				= 1518,		// 家园玉米
	BLOCK_HOMELAND_JUJUBETREE		= 1524,		// 家园红枣树
	BLOCK_HOMELAND_CUCUMBER			= 1525,		// 家园黄瓜
	BLOCK_HOMELAND_BARLEY			= 1527,		// 家园青稞
	BLOCK_HOMELAND_LUCUMA			= 1530,		// 家园蛋黄果树
	BLOCK_HOMELAND_REDSORGHUM		= 1531,		// 家园红高粱

	//家园新加的床
	BLOCK_BED_SIMPLE				= 1550,		//简约床
	BLOCK_BED_CHINESE				= 1574,		//中式床
	BLOCK_BED_MODERN				= 1598,		//现代床
	BLOCK_BED_FAIRY					= 1622,		//童话床
	BLOCK_BED_SCIENTIFIC			= 1646,		//科幻床
	BLOCK_BED_EUROPEAN				= 1691,		//欧式床
	BLOCK_BED_WESTENRN				= 1722,		//西域床

	// 家园装饰雕像
	BLOCK_HOMELAND_SCULPTURE_CAMEL		= 1747,		// 手工骆驼雕像
	BLOCK_HOMELAND_SCULPTURE_PAGODA		= 1748,		// 莫高窟九层塔
	BLOCK_HOMELAND_SCULPTURE_APSARAS	= 1749,		// 敦煌飞天雕像

   	BLOCK_FEED_TROUGH = 1753,//饲养槽方块
	BLOCK_COAGULATION = 1757, //凝浆块
	BLOCK_BLACK_COAGULATION = 1758, //黑凝浆块
	BLOCK_PLANT_FEED_TROUGH = 1759,
	BLOCK_MEAT_FEED_TROUGH = 1760,

	ITEM_STONE_HAMMER = 11613, //石质创造锤
	ITEM_BRONZE_HAMMER = 11614,//	青铜创造锤
	ITEM_CASTIRON_HAMMER = 11615, // 铸铁创造锤
	ITEM_TITANIUM_HAMMER = 11616,	//钛金创造锤
    RUNE_CRAFT = 785,   // 符文台
	BROKEN_CRAFT = 796,
	STONE_CRAFT = 800, // 石质工匠台
	BRONZE_CRAFT = 824, // 青铜工匠台
	CASTIRON_CRAFT = 833, //铸铁工匠台
	TITANIUM_CRAFT = 797,//钛金工匠台
	BLOCK_STONE_POT = 794,	//石锅
	BLOCK_IRON_POT = 795,	//铁锅	
	CRAFT_UPDATE_ITEM = 11911,  //不完整的工匠台升级元件

	ITEM_SPRINKLER_EMPTY = 11617,		//空花洒
	ITEM_SPRINKLER		 = 11628,		//带水花洒
	ITEM_GUNTOEGG_BULLET = 12588,	//变蛋枪子弹
	
	ITEM_GARLAND = 11228,		//花环
	ITEM_GARLAND_PRO = 11650,		//强化花环
	BLOCK_CHEST_BIGHT = 1180,	//大储物箱(横)
	BLOCK_CHEST_BIGVT = 1181,	//大储物箱(竖)

	BLOCK_INK_CHEST_BIGHT = 390103,	//大储物箱(横)
	BLOCK_INK_CHEST_BIGVT = 390105,	//大储物箱(竖)

	//耙
	ITEM_IRONHOE = 11033,		//铸铁耙
	ITEM_GOLDHOE = 11034,		//钛合金耙
	ITEM_DIAMONDHOE = 11035,	//蓝钻耙

	//黄铜防御装备
	ITEM_BRASS_HELMET = 12216,		//黄铜头盔
	ITEM_BRASS_BREAST = 12217,		//黄铜胸甲
	ITEM_BRASS_LEGGING = 12218,		//黄铜护腿
	ITEM_BRASS_SHOE = 12219,		//黄铜靴子

	ITEM_MASK = 12276,				//口罩

	BLOCK_REVIVAL_STATUE = 596,		//石质雕像
	BLOCK_BLACKDRAGON_STATUE = 732,	//黑龙雕像
	BLOCK_MAGMA_STATUE = 746,		//黑龙雕像

	BLOCK_PERISTELE = 1755,			//石柱
	BLOCK_TOP_PERISTELE = 1756,		//柱顶

	//打火器能产生作用的一些方块
	BLOCK_KONGMINGHOLDER = 848,		//孔明灯
	BLOCK_CANDLEHOLDER = 931,       //蜡烛灯
	BLOCK_REDCANDLEHOLDER = 1137,   //红烛灯
	BLOCK_FIREWORKFLOWER = 881,     //喷火烟花

	ITEM_HOLOGRAPHIC = 12011,		//全息投影
	
	ITEM_FODDER_MEAT = 11535,	//肉饲料
	ITEM_FODDER_PLANT = 11534,	//草饲料

	ITEM_PAINTTANK = 12239,		//喷漆罐
	
	ITEM_FRESH_FISH = 12520,	//鲜鱼
	ITEM_DRY_FISH = 11659,	//鱼干

	//---------------------特殊生物---------------------
	MONSTER_WARBLER_WILD = 3883,	//野生雀莺
	MONSTER_WARBLER_TAMED = 3884,	//驯服的雀莺

	// 彩云
	BLOCK_PLANTSPACE_ROSY_CLOUD = 1754,
	//三角门碎片
	ITEM_SANJIAOMEN = 11333,
	//虚空宝罐
	BLOCK_VOID_JAR = 499,

	// 巴啦啦魔法棒 -- 2021/07/21 codeby wudeshen
	ITEM_BALALA_MAGIC1 = 12301,
	ITEM_BALALA_MAGIC2 = 12302,
	ITEM_BALALA_MAGIC3 = 12303,
	ITEM_BALALA_MAGIC4 = 12304,
	ITEM_BALALA_MAGIC5 = 12305,
	ITEM_BALALA_MAGIC6 = 12306,
	ITEM_BALALA_MAGIC7 = 12307,

	//钢琴
	BLOCK_PIANO = 726,
	//乐曲
	ITEM_MUSIC_PU = 11910,
	//流沙
	BLOCK_STILL_SAND = 13,
	BLOCK_FLOW_SAND = 14,
	//咒岩
	BLOCK_CURSE_STONE = 15,
	BLOCK_ACTIVATE_CURSE_STONE = 16,
	//剧毒(液体方块)
	BLOCK_STILL_VENOM = 17,
	BLOCK_FLOW_VENOM = 18,
	//剧毒瓶 
	ITEM_VENOM_BOTTLE = 11630,
	//壁画方块
	BLOCK_MURAL = 483,

	//三丽鸥家具-凯蒂猫公主床
	BLOCK_BED_SANRIO_HELLOKITTY = 1818,
	//三丽鸥家具-凯蒂猫衣柜
	BLOCK_CHEST_SANRIO_HELLOKITTY = 1819,
	//三丽鸥家具-大耳狗星愿床
	BLOCK_BED_SANRIO_CINNAMOROLL = 1827,
	//三丽鸥家具-大耳狗衣柜
	BLOCK_CHEST_SANRIO_CINNAMOROLL = 1828,

	//三丽鸥家具-酷洛米公主床
	BLOCK_BED_SANRIO_KUROMI = 1837,
	//三丽鸥家具-酷洛米衣柜
	BLOCK_CHEST_SANRIO_KUROMI = 1838,
	//三丽鸥家具-美乐蒂公主床
	BLOCK_BED_SANRIO_MELODY = 1846,
	//三丽鸥家具-美乐蒂衣柜
	BLOCK_CHEST_SANRIO_MELODY = 1847,

	//三丽鸥家具-大眼蛙床
	BLOCK_BED_SANRIO_KEROPPI = 1862,
	//三丽鸥家具-大眼蛙衣柜
	BLOCK_CHEST_SANRIO_KEROPPI = 1861,
	//三丽鸥家具-布丁狗床
	BLOCK_BED_SANRIO_PURIN = 1864,
	//三丽鸥家具-布丁狗衣柜
	BLOCK_CHEST_SANRIO_PURIN = 1863,

	//三丽鸥家具-双子星床
	BLOCK_BED_SANRIO_TWINSTAR = 1875,
	//三丽鸥家具-双子星衣柜
	BLOCK_CHEST_SANRIO_TWINSTAR = 1874,
	
	// 礁石
	BLOCK_REEF = 25,
	// 珊瑚幼体
	BLOCK_CORAL_LARVA = 485,
	// 角珊瑚
	BLOCK_HORN_CORAL = 486,
	// 白化角珊瑚
	BLOCK_HORN_CORAL_BLEACHING = 487,
	// 气泡珊瑚
	BLOCK_BUBBLE_CORAL = 488,
	// 白化气泡珊瑚
	BLOCK_BUBBLE_CORAL_BLEACHING = 489,
	// 圆盘珊瑚
	BLOCK_TURBAN_CORAL = 490,
	// 白化圆盘珊瑚
	BLOCK_TURBAN_CORAL_BLEACHING = 491,
	// 树珊瑚
	BLOCK_TREE_CORAL = 492,
	// 白化树珊瑚
	BLOCK_TREE_CORAL_BLEACHING = 493,

	//破海神像方块
	BLOCK_POSEIDON_STATUE = 1193,
	//珍珠
	ITEM_PEARL = 11655,

	/*水下植物相关*/
	BLOCK_WATER_WEED = 245, //水草方块
	BLOCK_SEA_WEED = 246, //海带方块
	BLOCK_GLOW_STICK_ALGAE_SEED = 494, //荧光棒藻幼苗方块
	BLOCK_GLOW_STICK_ALGAE = 495, //荧光棒藻方块
	ITEM_GLOW_STICK_UNUSED = 11656, //未用荧光棒
	ITEM_GLOW_STICK = 11657, //发光荧光棒
	ITEM_GLOW_STICK_USED = 11658, //用尽荧光棒
	BLOCK_GLOW_STICK = 496, //发光荧光棒对应的方块
	BLOCK_GLOW_STICK_USED = 497, //用尽荧光棒对应的方块
	ITEM_SEA_WEED = 12615,	//生海带

	ITEM_PIRATE_CHEST =12620, //海盗船掉的箱子

	BLOCK_COCONUE_WOOD = 1266,
	BLOCK_COCONUE_SAPLING = 293,//树苗
	BLOCK_COCONUE_LEAF,//树叶
	
	//调色架站立和挂墙
	BLOCK_COLOR_PALETTE_STAND = 498,
	BLOCK_COLOR_PALETTE_HANG = 508,
	
	BLOCK_SOLIDSAND = 29,		//固体沙子
	// 木桩
	BLOCK_WOODPILE = 821,

	BLOCK_WIRELESS_LEVER = 358, //开关
	BLOCK_TOUCH_BUTTON = 363, //触碰按钮
	BLOCK_NORMAL_BUTTON = 364, //普通按钮
	BLOCK_PRESSURE_ANY = 359, //触碰感压板
	BLOCK_PRESSURE_LIVING = 360, //普通感压板
	BLOCK_PRESSURE_MONSTER = 361, //生物感压板
	BLOCK_PRESSURE_SANDSTONE = 362, //沙石感压板（普通感压板）

	BLOCK_RAY_WIRE = 351,			//光束线
	BLOCK_STAR_ENERGYLIGHT = 352, 	//星能光束器
	BLOCK_KEYDOOR = 373,		//钥匙门
	ITEM_KEY = 12709,		//钥匙
	BLOCK_NON_GATE = 353,//非门
	BLOCK_AND_GATE = 354,//与门
	BLOCK_OR_GATE = 355,//或门
	BLOCK_COLLECTING_PIPE = 369,//收集管道
	BLOCK_TRANSPORT_PIPE = 370,//传输管道
	BLOCK_ELECTRIC_SPLLITTER = 356, //分流器
	BLOCK_ELECTRIC_DELAY = 357, //延时器
	BLOCK_ELECTRIC_COUNTER = 366, //计数器
	BLOCK_WIRELESS_EMITTER = 372, //增压器（弹射器）
	BLOCK_DETECTION_PIPE = 371,//检测管道
	BLOCK_WIRELESS_COMPARATOR = 374,//比较器
	BLOCK_WIRELESS_ARITHMATIC = 387,//算法器
	BLOCK_WIRELESS_RESISTER = 365,//阻隔器
	BLOCK_WIRELESS_PISTON_BASE = 367,//机械臂
	BLOCK_WIRELESS_PISTON_STICKYBASE = 368,//推拉机械臂
	BLOCK_WIRELESS_PISTON_EXTENSION = 1198,
	BLOCK_WIRELESS_PISTON_MOVING = 1199,
	BLOCK_WIRELESS_PISTON_MOVING2 = 1097,
	BLOCK_WIRELESS_RANGE_PISTON = 1196,
	BLOCK_WIRELESS_RANGE_PISTON_STICKY = 1197,
	BLOCK_FUSION_CAGE = 150029,//融合机
	BLOCK_FUR_BED = 150015,

	BLOCK_ICRCRYSTALSHROOM_SEEDLING	= 150007,		// 冰晶喷菇幼苗
	BLOCK_ICRCRYSTALSHROOM_DARK		= 150008,		// 无光冰晶喷菇
	BLOCK_ICRCRYSTALSHROOM_LIGHT	= 150009,		// 有光冰晶喷菇
	BLOCK_ICRCRYSTALFERN_BUD		= 150010,		// 冰晶蕨芽
	BLOCK_ICRCRYSTALFERN_SEEDLING	= 150011,		// 冰晶蕨幼苗
	BLOCK_ICRCRYSTALFERN			= 150012,		// 冰晶蕨

	//花海版本新增植被
	BLOCK_WILDFLOWER_WHITE			= 3751,		//白色野花
	BLOCK_WILDFLOWER_BLUE			= 3752,		//蓝色野花
	BLOCK_WILDFLOWER_YELLOW			= 3753,		//黄色野花
	BLOCK_DAISYBUSH_PINK_BLUE		= 3754,		//雏菊丛
	BLOCK_DAISYBUSH_WHITE_ORANGE	= 3755,		//雏菊丛
	BLOCK_DAISYBUSH_PINK_WHITE		= 3756,		//雏菊丛
	BLOCK_BUTTERFLY_FLOWER_PINK		= 3757,		//粉蝶花
	BLOCK_LAVENDER					= 3758,		//薰衣草

	BLOCK_GLOWING_GRASS				= 3759,		//荧光草
	BLOCK_GLOWING_FALLEN_LEAVES		= 3760,		//荧光落叶
	BLOCK_STARLIGHT_FUNGUS			= 3761,		//星光菌
	BLOCK_DANDELION					= 3762,		//薰衣草
	BLOCK_FOXTAIL_GRASS				= 3763,		//狗尾草
	BLOCK_REED_GRASS				= 3764,		//芦苇
	BLOCK_FLOWER_RAPESEED			= 3765,		//油菜花

	BLOCK_FLOWER_TULIP				= 3766,		//郁金香
	BLOCK_FLOWER_CLOVER				= 3767,		//三叶草

	BLOCK_LOTUS_LEAF				= 3768,		//荷叶
	BLOCK_DUCKWEED					= 3769,		//浮萍
	BLOCK_WATERWEED					= 3770,		//水藻
	BLOCK_FERN						= 3771,		//蕨
	BLOCK_BOUGAINVILLEA_PURPLE		= 3772,		//紫色三角梅
	BLOCK_BOUGAINVILLEA_WHITE		= 3773,		//白色三角梅

	BLOCK_GLOWING_VINE				= 150072,		//发光树藤
	BLOCK_GLOWING_LEAVES			= 150073,		//发光树叶
	BLOCK_WOOD_GLOWING				= 150074,		//发光树干

	BLOCK_BOUGAINVILLEA_GROUND_PURPLE = 150075,		//紫色三角梅
	BLOCK_BOUGAINVILLEA_GROUND_WHITE  = 150076,		//白色三角梅

	BLOCK_LEAVE_ACACIA				= 151016,		//金合欢树叶
	BLOCK_WOOD_ACACIA				= 151017,		//金合欢木
	BLOCK_LEAVE_BREAD				= 151018,		//猴面包树叶
	BLOCK_WOOD_BREAD				= 151019,		//猴面包木

	BLOCK_FIREWORK_BLUE				= 150500,		//蓝色烟花
	BLOCK_FIREWORK_GREEN			= 150501,		//绿色烟花
	BLOCK_FIREWORK_PINK				= 150502,		//粉色烟花
	BLOCK_FIREWORK_YELLOW			= 150503,		//黄色烟花
	BLOCK_FIREWORK_MULTICOLOR		= 150504,		//彩色烟花
	BLOCK_FIREWORK_HEART			= 150505,		//心形烟花

	BLOCK_LEAVE_GINKO 				= 151301,		//银杏树叶
	BLOCK_LEAVE_MAPLE				= 151302,		//枫树叶
	BLOCK_LEAVE_JACARANDA 			= 151303,		//蓝花楹树叶
	BLOCK_LEAVE_POPLAR 				= 151304,		//胡杨树叶

	BLOCK_FIREWORK_PINK_1 = 150506,		//粉色小烟花
	BLOCK_FIREWORK_HEART_1 = 150507,		//特殊爱心烟花
	BLOCK_FIREWORK_YELLOW_1 = 150508,		//黄色大烟花
	BLOCK_FIREWORK_MULTICOLOR_1 = 150509,		//烟花组（多个）

	BLOCK_FIREWORK_PAOTONG			= 150510,		//炮筒烟花


	BLOCK_SAPLING_ACACIA = 151006,					//金合欢树苗
	BLOCK_SAPLING_BREAD = 151007,					//猴面包树苗

	BLOCK_SAPLING_GINKGO = 151201,                 //银杏树苗
	BLOCK_WOOD_GINKGO = 151401,		               //银杏树木

	BLOCK_SAPLING_LIQUIDAMBAR = 151202,                 //枫香树苗
	BLOCK_WOOD_LIQUIDAMBAR = 151402,		            //枫香树木

	BLOCK_SAPLING_JACARANDA = 151203,                 //蓝花楹树苗
	BLOCK_WOOD_JACARANDA = 151403,		              //蓝花楹树木

	BLOCK_SAPLING_POPULUSINAUTUMN = 151204,                 //秋胡杨树苗
	BLOCK_WOOD_POPULUSINAUTUMN = 151404,		            //秋胡杨树木


	BLOCK_TALKING_STATUE = 200429,					//神灵雕像

	BLOCK_PERMAFROST = 	3502,//永冻冰块

	BLOCK_VOID_MUSHROOM			= 200380,	//虚空蘑菇
	BLOCK_VOID_MUSHROOM_CAP		= 200381,	//虚空蘑菇盖
	BLOCK_VOID_BELLFLOWER_SEED	= 200394,	//虚空风铃花种子
	BLOCK_VOID_BELLFLOWER		= 200395,	//虚空风铃花
	BLOCK_VOID_DEAGONGRASS      = 200397,   //虚空龙爪草
	
	ITEM_WINTER_GLOVES = 11231,//冬季手套
	//冰窟道具
	ITEM_ICE_BALL = 11669,
	ITEM_EXTREME_COLD = 11670,
	ITEM_ICE_BOTTLE = 11672, //冰雾瓶
	ITEM_ICECRYSTALSHROOMSEED = 11665,	// 冰菇孢子
	ITEM_ICECRYSTALFERNSEED = 11666,	// 冰晶蕨种子
	ITEM_ICE_CRYSTAL = 11824,			// 寒霜晶石
	ITEM_HARD_ICICLE = 11673,//冰凌
	ITEM_WEAK_ICICLE = 11674,//脆冰凌

	ITEM_WINTER_HELMET = 12312,//防寒头盔
	ITEM_WINTER_CLOTHES = 12313,//防寒胸甲
	ITEM_WINTER_LEGGING = 12314,//防寒护腿
	ITEM_WINTER_SHOE = 12315,//雪地靴

	BLOCK_ICE_TORCH = 150013,//寒光棒

	BLOCK_ICE_BONFIRE = 150018,

	BLOCK_CHEST_BIGVT_SCHOOL = 390008,//学校储物柜

	ITEM_UGC_BP = 10119,
	ITEM_UGC_TREE = 10120,
	ITEM_UGC_RIVER = 10121,
	ITEM_COLOR_BRUSH = 10125,

	ITEM_MAGIC_MASK = 11232,

	ITEM_HOMELAND_HOE = 75026, //石锄

	BLOCK_PENGUIN_NEST = 150028,
	BLOCK_VOID_CRYSTAL = 40,//虚空晶块
	BLOCK_MANUAL_EMITTER = 200002, //手持发射器
	BLOCK_WOODEN_BED = 200003, //木床
	BLOCK_VOID_FRUIT = 200382, //虚空果实
	BLOCK_VOID_FRUIT_END = 200388, //虚空果实
	ITEM_VOID_SUPER_FRUIT = 200389,     //超级虚空果实
	BLOCK_VOID_TREE = 200390, //虚空果木
	BLOCK_VOID_TREE_LEAVES = 200391, //虚空果木树叶
	BLOCK_VOID_TREE_THIN_BRANCH = 200392, //细虚空果木树枝
	BLOCK_VOID_TREE_THICK_BRANCH = 200410, //粗虚空果木树枝
	BLOCK_VOID_SAPLING = 200409, //虚空果实树苗

	BLOCK_MONSTER_STATUE_START = 200012,  //怪物雕像起始ID
	BLOCK_MONSTER_STATUE_END = 200100,	  //怪物雕像结束ID
	//虚空瓜
	BLOCK_VOID_MELON_UNKNOWN	= 200413,
	BLOCK_VOID_MELON_ORIGIN		= 200414,
	BLOCK_VOID_MELON_SAD		= 200415,
	BLOCK_VOID_MELON_UNWELL		= 200416,
	BLOCK_PILE_TOP = 200101,    //堆叠方块top
	BLOCK_PILE_BOTTOM = 200102, //堆叠方块bottom
	BLOCK_PILE_CENTER = 200103, //堆叠方块center
	BLOCK_PILE_LAYER = 200104,  //堆叠方块layer, 只有一层的特殊处理
	BLOCK_MONSTER_STATUE_BOTTOM = 200105, //雕像的bottom
	BLOCK_MONSTER_STATUE_FOUNDATION = 200106, //雕像底座
	BLOCK_MONSTER_STATUE_LAYER = 200107, //雕像layer
	ITEM_VOID_CRYSTAL = 200411,	//虚空晶体
	BLOCK_VACANT_FLOWER = 200417, //虚空之花
	BLOCK_VOID_BIAN_FLOWER = 200408, //虚空彼岸花
	BLOCK_DEATH_JAR = 200420, //遗落的罐子
	BLOCK_WEAPON_RACK = 200006, //独立装备架
	ITEM_BOOMERANG_STICK = 540457,
	ITEM_DOUBLE_BOOMERANG = 540465,
	BLOCK_NEWSTONE_POT = 200108,//新石锅
	BLOCK_NEWIRON_STOVE = 200109,//新秘银炉子
	BLOCK_CANDLE_LAMP = 200371, //蜡烛灯
	BLOCK_CHINA_DOOR = 390027,
	BLOCK_CHINA_DOOR2 = 390052,
	BLOCK_CHINA_FENCE = 390054,
	BLOCK_WHITE_STONE_FENCE = 390031,
	BLOCK_CHINA_BED = 390036,
	BLOCK_CHINA_HIGH_DOOR = 390080,

	BLOCK_INK_STILL_WATER = 390023, //水墨静态水
	BLOCK_INK_FLOW_WATER = 390024, //水墨动态水
	ITEM_POLAROID = 12733,//拍立得
	ITEM_POLAROID_PHOTO = 12734,//相片
	ITEM_POLAROID_ALBUM = 12735,//相册
	ITEM_POLAROID_FRAME = 201003,//相框
	ITEM_RARE_POLAROID = 12736, //稀有拍立得
	ITEM_POLAROID_RARE_ALBUM = 12737,//稀有相册
	ITEM_POLAROID_RARE_FRAME = 201006,//稀有相框
	ITEM_POLAROID_INNER_FRAME = 201007,//内部使用的相框

};
//tolua_end
#define	ITEM_SPEAR_MODEL "12002"
#define	ITEM_ARROW_MODEL "12051"
#define	ITEM_MISSILE_MODEL "12285"
#define	ITEM_REDFIREWORKDS_MODEL "12836"
#define	ITEM_PURPLEFIREWORKDS_MODEL "12837"
#define	ITEM_GREENFIREWORKDS_MODEL "128378"
#define	ITEM_BULLET_MODEL "128378"
#define ITEM_OCEANARROW_MODEL "12062"
#define ITEM_HARPOON_MODEL "12063"
#define ITEM_BRASSSPEAR_MODEL "12004"


namespace ItemIDs {
	constexpr int BLUEPRINT = 3030212;  // 建筑图纸
	// 可以添加其他物品ID
}
#endif