//
//#include "SkyCloudGen.h"
//#include "LegacyOgreColourValue.h"
//#include "OgreRandomNumber.h"
//#include "OgreRect.h"
//#include "Platforms/PlatformInterface.h"
//#include "OgreTimer.h"
//
//using namespace MINIW;
//
//SkyCloudGen::SkyCloudGen(int lsize, int csize): seed1(0), seed2(0), t(0), cloudsharpness(0), cloudcover(0)
//{
//	lmapsize = lsize;
//	cloudmapsize = csize;
//	pshadernoise = new unsigned char[lmapsize * lmapsize];
//	memset(pshadernoise, 0, lmapsize * lmapsize);
//
//	plmapbits = new unsigned char[lmapsize * lmapsize * 4];
//	memset(plmapbits, 0, lmapsize * lmapsize * 4);
//
//	popnoise = new unsigned char[cloudmapsize * cloudmapsize];
//	memset(popnoise, 0, cloudmapsize * cloudmapsize);
//
//	genstate = GEN_WAITCMD;
//	fullspeed = false;
//	m_LastDayTime = -9999.0f;
//}
//
//SkyCloudGen::~SkyCloudGen()
//{
//	OGRE_DELETE_ARRAY(pshadernoise);
//	OGRE_DELETE_ARRAY(plmapbits);
//	OGRE_DELETE_ARRAY(popnoise);
//}
//
//inline void ScatterOneCell(ColourValue &curcolor, float h, float cell_h, const ColourValue &scattercolor)
//{
//	float scale = 1.0f - cell_h/10.0f;
//	curcolor.r = curcolor.r * scale + scattercolor.r;
//	curcolor.g = curcolor.g * scale + scattercolor.g;
//	curcolor.b = curcolor.b * scale + scattercolor.b;
//}
//
//static ColorQuad ScatterRay(const Point2D &origin, const Point2D &goal, float h0, float h1, unsigned char *pbuf, int w, int h, const ColourValue &suncolor, const ColourValue &scattercolor)
//{
//	ColourValue curcolor = suncolor;
//
//	Point2D delta = goal - origin;
//	if( delta.x==0 && delta.y==0 )
//	{
//	}
//	else if( Abs(delta.x) >= Abs(delta.y) )
//	{
//		int step = origin.x<=goal.x? 1 : -1;
//		for( int x=origin.x; x!=goal.x; x+=step )
//		{
//			int y = (x-origin.x)*delta.y/delta.x + origin.y;
//			if( x>=0 && x<w && y>=0 && y<h )
//			{
//				float cell_h = pbuf[y*w + x] / 255.0f;
//				float h = (x-origin.x)*(h1-h0)/delta.x + h0;
//				if( h < cell_h ) ScatterOneCell( curcolor, h, cell_h, scattercolor );
//			}
//		}
//	}
//	else
//	{
//		int step = origin.y<=goal.y? 1 : -1;
//		for( int y=origin.y; y!=goal.y; y+=step )
//		{
//			int x = (y-origin.y)*delta.x/delta.y + origin.x;
//			if( x>=0 && x<w && y>=0 && y<h )
//			{
//				float cell_h = pbuf[y*w + x] / 255.0f;
//				float h = (y-origin.y)*(h1-h0)/delta.y + h0;
//				if( h < cell_h ) ScatterOneCell( curcolor, h, cell_h, scattercolor );
//			}
//		}
//	}
//
//	if( curcolor.r > 1.0f ) curcolor.r = 1.0f;
//	if( curcolor.g > 1.0f ) curcolor.g = 1.0f;
//	if( curcolor.b > 1.0f ) curcolor.b = 1.0f;
//
//	return ColorQuad(curcolor);
//}
//
//void SkyCloudGen::shaderingCloud(ColorQuad *presult, unsigned char *pnoise, int w, int h)
//{
//	Point2D origin, goal;
//	for( int y=0; y<h; y++ )
//	{
//		if(m_Shutdown) return;
//
//		if(!fullspeed) MINIW::ThreadSleep(8);
//		for( int x=0; x<w; x++ )
//		{
//			origin.x = int(sunpos.x*w);
//			origin.y = int(sunpos.z*h);
//			goal.x = x;
//			goal.y = y;
//			presult[y*w +x] = ScatterRay( origin, goal, sunpos.y, 0.0f, pnoise, w, h, suncolor, scattercolor);
//		}
//	}
//}
//
//static void CalCloudBlendEdge(unsigned char *buffer, int w, int h, int edge)
//{
//	int x, y;
//	for(y=0; y<h; y++)
//	{
//		unsigned char *rowbits = buffer + y*w;
//		for(x=0; x<edge; x++)
//		{
//			rowbits[x] = rowbits[x] * x/edge;
//		}
//		for(x=w-edge; x<w; x++)
//		{
//			rowbits[x] = rowbits[x] * (w-1-x)/edge;
//		}
//	}
//
//	for(x=0; x<w; x++)
//	{
//		unsigned char *colbits = buffer + x;
//		for(y=0; y<edge; y++)
//		{
//			colbits[y*w] = colbits[y*w] * y/edge;
//		}
//		for(y=h-edge; y<h; y++)
//		{
//			colbits[y*w] = colbits[y*w] * (h-1-y)/edge;
//		}
//	}
//}
//
//void SkyCloudGen::genCloud()
//{
//	int i;
//
//	//unsigned int dt1 = Timer::getSystemTick();
//
//	PerlinNoise2D mynoise( CLOUD_SIZE0, CLOUD_SIZE0);
//	if( t <= 0 ) mynoise.initNoise(seed1);
//	else mynoise.initNoise(seed1, seed2, t);
//
//	//unsigned int dt2 = Timer::getSystemTick();
//
//	int shasize = lmapsize;
//	for( i=0; i<shasize; i++ )
//	{
//		if(m_Shutdown) return;
//		mynoise.calNoiseDataRow( pshadernoise, CLOUD_SHALEVEL, i );
//	}
//
//	//unsigned int dt3 = Timer::getSystemTick();
//
//	PerlinNoise2D::makeNoiseSharp( pshadernoise, shasize, shasize, cloudcover, cloudsharpness );
//
//	shaderingCloud((ColorQuad *)plmapbits, pshadernoise, shasize, shasize);
//	if(m_Shutdown) return;
//
//	//unsigned int dt4 = Timer::getSystemTick();
//
//	int opsize = cloudmapsize;
//	for( i=0; i<opsize; i++ )
//	{
//		if(m_Shutdown) return;
//
//		mynoise.calNoiseDataRow( popnoise, CLOUD_OPLEVEL, i );
//		if(!fullspeed) MINIW::ThreadSleep(8);
//	}
//
//	//unsigned int dt5 = Timer::getSystemTick();
//
//	for( i=0; i<opsize; i++ )
//	{
//		if(m_Shutdown) return;
//		PerlinNoise2D::makeNoiseSharpRow( popnoise, opsize, i, cloudcover, cloudsharpness );
//	}
//
//	CalCloudBlendEdge(popnoise, opsize, opsize, 200);
//
//	//unsigned int dt6 = Timer::getSystemTick();
//
//	//char buffer[256];
//	//sprintf(buffer, "cloud:%d,%d,%d,%d,%d\n", dt2-dt1, dt3-dt2, dt4-dt3, dt5-dt4, dt6-dt5);
//
//	//OutputDebugString(buffer);
//}
//
//OSThread::RUN_RETTYPE SkyCloudGen::_run()
//{
//	if(m_Shutdown == 1) return OSThread::RUN_EXIT;
//	if(genstate == GEN_CMD)
//	{
//		genCloud();
//		genstate = GEN_COMPLETE;
//		return OSThread::RUN_CONTINUE;
//	}
//	else return OSThread::RUN_WAIT;
//}
