#pragma once
#include "Common/OgreShared.h"
#include "Core/physics/RigidDynamicActor.h"
#include "BlockMeshVert.h"
#include "world_types.h"
#include "SectionMesh.h"

class BlockExplosionFragment {
public:
    struct FragmentMeshInfo {
        dynamic_array<BlockGeomVert> vertices;
        dynamic_array<UInt16> indices;
        Rainbow::MinMaxAABB aabb;
        Rainbow::Vector3f velocity;
        Rainbow::Vector3f rotation;
        Rainbow::Vector3f position;
        float lifeTime;
        Rainbow::RigidDynamicActor* physicsActor;
    };

    static void GenerateFragments(const WCoord& blockPos, 
                                const Block& block,
                                dynamic_array<FragmentMeshInfo>& outFragments,
                                int fragmentCount = 8);

private:
    static void SubdivideBlock(const BlockGeomVert* originalVerts,
                              const UInt16* originalIndices,
                              int originalIndexCount,
                              dynamic_array<FragmentMeshInfo>& outFragments,
                              int fragmentCount, const Rainbow::Vector3f& worldCenter);

    static void SetupFragmentPhysics(FragmentMeshInfo& fragment, const Rainbow::Vector3f& worldCenter);

    static bool IsValidFragment(const FragmentMeshInfo& fragment);

    static Rainbow::Vector3f GetRandomNormal();

    static bool GenerateFragmentGeometry(const BlockGeomVert* originalVerts,
                                       const UInt16* originalIndices,
                                       int originalIndexCount,
                                       const Rainbow::Vector3f& planeNormal,
                                       float planeDistance,
                                       FragmentMeshInfo& outFragment);

    static float CalculateFragmentVolume(const FragmentMeshInfo& fragment);

    static bool CalculateIntersection(const BlockGeomVert* originalVerts,
                                    const UInt16* originalIndices,
                                    int originalIndexCount,
                                    const Rainbow::Vector3f& planeNormal,
                                    float planeDistance,
                                    dynamic_array<BlockGeomVert>& outVertices,
                                    dynamic_array<UInt16>& outIndices);

    static void UpdateFragmentBounds(FragmentMeshInfo& fragment);
}; 