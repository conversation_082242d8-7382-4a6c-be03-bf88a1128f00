#include "SandboxCoreScript.h"

namespace MNSandbox {
	IMPLEMENT_SCENEOBJECTCLASS(CoreScript);

	CoreScript::CoreScript()
	{

	}

	CoreScript::~CoreScript()
	{

	}

	bool CoreScript::checkIsCanPlay() const
	{
		return true;
	}

	void CoreScript::OnBeginPlay()
	{
		if (checkIsCanPlay())
		{
			StartPlayScript();
		}

		Super::OnBeginPlay();
	}

	void CoreScript::OnEndPlay()
	{
		Super::OnEndPlay();
		if (checkIsCanPlay())
		{
			EndPlayScript();
		}
	}
}