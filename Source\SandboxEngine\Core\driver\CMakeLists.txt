cmake_minimum_required(VERSION 3.6)

set(lib_name SandboxEngineDriver)
set(target_name lib${lib_name})

if(BUILD_DYNAMIC)
  set(PROJECT_STATIC FALSE)
else()
    set(PROJECT_STATIC TRUE)
endif()

include(CMakeFunc)
include(ExternalConfigLib) #设置 lib 还是 dll,还有平台的目录文件夹
include(ConfigTargets) #设置 config ARCHIVE_OUTPUT_DIRECTORY   LIBRARY_OUTPUT_DIRECTORY RUNTIME_OUTPUT_DIRECTORY

#设定可以include的路径
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_CURRENT_SOURCE_DIR})


include(files.cmake)

include(subdirs.cmake)
set(SandboxEngineDriverIncludeDirs ${ret_subdirs})
include_directories_array_without_root("${SandboxEngineDriverIncludeDirs}")

add_library(${target_name} ${LIB_TYPE} ${SOURCE_FILES} )
set_ios_framework(${target_name} ${lib_name})
config_common(${target_name})

if(BUILD_MINI_EDITOR_APP)
    add_definitions(-DBUILD_MINI_EDITOR_APP) #沙盒driver代码不区分工具使用还是minigame
endif()



add_definitions(-DUNENABLE_APPROVE)

target_link_libraries(${target_name} libMiniBaseEngine libEngine GameExternal)

if(BUILD_DYNAMIC)
	target_compile_definitions(${target_name} PRIVATE
		-DBUILD_SANDBOX_DLL	
		-DBUILD_SANDBOXDRIVER_DLL
	)

	if(BUILD_MINI_EDITOR_APP)
		set_target_properties(${target_name} PROPERTIES
			RUNTIME_OUTPUT_DIRECTORY_DEBUG ${MINI_STUDIO_RUNTIME_DIR} #运行时的输出目录
			RUNTIME_OUTPUT_DIRECTORY_RELEASE ${MINI_STUDIO_RUNTIME_DIR} #运行时的输出目录
			RUNTIME_OUTPUT_DIRECTORY_PROFILE ${MINI_STUDIO_RUNTIME_DIR}
		)
	endif()
endif()

# 使用引擎库编译，需要添加下面这行，否则会出现链接失败的问题
if(USE_RAINBOW_LIB)
	target_include_directories(${target_name} 
		PRIVATE ${PROJECT_INCLUDE_DIR}/Engine
	)
else()
	target_include_directories(${target_name} 
		PRIVATE ${PROJECT_ENGINE_DIR}
	)	  
endif()

add_definitions(
	-DUNICODE 
	-D_UNICODE 
	-DWIN32_LEAN_AND_MEAN
	-D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
	-D__STDC_LIMIT_MACROS
    -D__STDC_CONSTANT_MACROS
)

if($ENV{USE_SANDBOX_DEV})
    set(CMAKE_CXX_FLAGS_RELEASE   "${CMAKE_CXX_FLAGS_RELEASE} /Od /Ob0")
endif()

if(${USE_DEV_BUILD})
    add_definitions(-DIWORLD_DEV_BUILD)
endif()

target_include_directories(${target_name} 
	PUBLIC ${MODULE_INCLUDE_DIRS}
	${SandboxEngineDriverIncludeDirs}
)

if(WINDOWS)
	target_link_libraries(${target_name} Wininet.lib)
    target_link_libraries(${target_name} Ws2_32.lib)
endif()

#临时参数
if(BUILD_MINI_EDITOR_APP OR DEFINED ENV{USE_STIDIO_SERVER})
	MESSAGE("=========CLIENT_USE_LUA_JIT FALSE=========")
	set (CLIENT_USE_LUA_JIT FALSE)
else()
	MESSAGE("=========CLIENT_USE_LUA_JIT TRUE=========")
	set (CLIENT_USE_LUA_JIT TRUE)
endif()

if (SERVER_USE_LUA_JIT OR CLIENT_USE_LUA_JIT)
    target_include_directories(${target_name} 
	PRIVATE ${PROJECT_GAME_EXTERNAL_DIR}/lua/luajit/include
	)
else()
    target_include_directories(${target_name} 
	PRIVATE ${PROJECT_GAME_EXTERNAL_DIR}/lua/lua
	)
endif()

if(LINUX_SERVER)
  include_directories(${PROJECT_GAME_EXTERNAL_DIR}/MMKV_Linux/MMKV/Core)  
endif()

if (NOT OHOS)
    target_include_directories(${target_name}
        PRIVATE
        ${ENGINE_EXTERNAL_DIR}/OpenGLES
    )
endif()

target_include_directories(${target_name} 
	PRIVATE ${ENGINE_EXTERNAL_DIR}
	${ENGINE_EXTERNAL_DIR}/HashFunctions
	${ENGINE_EXTERNAL_DIR}/TextureCompressors
	${ENGINE_EXTERNAL_DIR}/TextureCompressors/Crunch
	${ENGINE_EXTERNAL_DIR}/zlib
	${PROJECT_MINI_SHARED_DIR}
	${PROJECT_MINI_SHARED_DIR}/base
	${PROJECT_MINI_SHARED_DIR}/utils
	${PROJECT_MINI_SHARED_DIR}/utility
	${PROJECT_BASE_ENGINE_DIR}
	${PROJECT_BASE_ENGINE_DIR}/Common
	${PROJECT_BASE_ENGINE_DIR}/ScriptVM
	${PROJECT_BASE_ENGINE_DIR}/ScriptVM/Profiler
	${PROJECT_BASE_ENGINE_DIR}/UILib
	${PROJECT_BASE_ENGINE_DIR}/FairyGUI
	${PROJECT_BASE_ENGINE_DIR}/FairyGUI/Cocos2dx
	${PROJECT_BASE_ENGINE_DIR}/Input
	${PROJECT_BASE_ENGINE_DIR}/Compress
	${PROJECT_BASE_ENGINE_DIR}/SceneManagement
	${ENGINE_EXTERNAL_GAME_DIR}
	${PROJECT_SANDBOX_EXTEND_DIR}
	${PROJECT_SANDBOXENGINE_DIR}/Utils/DebugSupport
	${PROJECT_GAME_EXTERNAL_DIR}
	${PROJECT_GAME_EXTERNAL_DIR}/lua
	${PROJECT_GAME_EXTERNAL_DIR}/lua/tolua
	${PROJECT_GAME_EXTERNAL_DIR}/lua/cjson
)


set_target_properties(${target_name}
    PROPERTIES
    FOLDER "SandboxEngine"
)

if(WINDOWS)
	set_target_properties(${target_name} PROPERTIES VS_GLOBAL_PreferredToolArchitecture "x64")
	#set_target_properties(${target_name} PROPERTIES LINK_FLAGS "/DEBUG:FASTLINK")
endif()