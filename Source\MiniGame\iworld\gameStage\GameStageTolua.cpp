/*
** Lua binding: GameStageTolua
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_GameStageTolua_open (lua_State* tolua_S);

#include "gameStage/LoadStepCounter.h"
#include "gameStage/ClientGame.h"
#include "gameStage/SurviveGame.h"
#include "gameStage/MainMenuGame.h"
#include "gameStage/SimpleLoadingGame.h"
#include "gameStage/MainMenuGame.h"
#include "gameStage/MpGameSurvive.h"
#include "gameStage/ClientGameRecord.h"
#include "gameStage/ClientGameManager.h"
#include "gameStage/UIStageInputHandler.h"
#include "gameStage/UIStageSandboxObject.h"

/* function to release collected object via destructor */
#ifdef __cplusplus

static int tolua_collect_SimpleLoadingStage (lua_State* tolua_S)
{
 SimpleLoadingStage* self = (SimpleLoadingStage*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_GameSurviveRecord (lua_State* tolua_S)
{
 GameSurviveRecord* self = (GameSurviveRecord*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_ClientGame (lua_State* tolua_S)
{
 ClientGame* self = (ClientGame*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_MainMenuStage (lua_State* tolua_S)
{
 MainMenuStage* self = (MainMenuStage*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_ClientGameManager (lua_State* tolua_S)
{
 ClientGameManager* self = (ClientGameManager*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}
#endif


/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"PlayerBriefInfo");
 tolua_usertype(tolua_S,"SimpleLoadingStage");
 tolua_usertype(tolua_S,"IControlInterface");
 tolua_usertype(tolua_S,"std::map<int,EffectParticle*>");
 tolua_usertype(tolua_S,"IClientGameManagerInterface");
 tolua_usertype(tolua_S,"UIStageInputHandler");
 tolua_usertype(tolua_S,"Rainbow::InputEvent");
 tolua_usertype(tolua_S,"SurviveGame");
 tolua_usertype(tolua_S,"MainMenuStage");
 tolua_usertype(tolua_S,"ClientPlayer");
 tolua_usertype(tolua_S,"MNSandbox::SandboxContext");
 tolua_usertype(tolua_S,"GameSurviveRecord");
 tolua_usertype(tolua_S,"PlayerControl");
 tolua_usertype(tolua_S,"ClientGameManager");
 tolua_usertype(tolua_S,"UIStageSandboxObject");
 tolua_usertype(tolua_S,"MNSandbox::Object");
 tolua_usertype(tolua_S,"Rainbow::InputInterface");
 tolua_usertype(tolua_S,"ClientGame");
 tolua_usertype(tolua_S,"LoadStepCounter");
 tolua_usertype(tolua_S,"WCoord");
 tolua_usertype(tolua_S,"MpGameSurvive");
 tolua_usertype(tolua_S,"ReachabilityObserver");
}

/* method: reset of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_reset00
static int tolua_GameStageTolua_LoadStepCounter_reset00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
  int s = ((int)  tolua_tonumber(tolua_S,2,0));
  int stage_total = ((int)  tolua_tonumber(tolua_S,3,0));
  int stage_len = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'reset'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->reset(s,stage_total,stage_len);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'reset'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: gotoStage of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_gotoStage00
static int tolua_GameStageTolua_LoadStepCounter_gotoStage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
  int s = ((int)  tolua_tonumber(tolua_S,2,0));
  int stage_total = ((int)  tolua_tonumber(tolua_S,3,0));
  int stage_len = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'gotoStage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->gotoStage(s,stage_total,stage_len);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'gotoStage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: step of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_step00
static int tolua_GameStageTolua_LoadStepCounter_step00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'step'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->step();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'step'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getProgress of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_getProgress00
static int tolua_GameStageTolua_LoadStepCounter_getProgress00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getProgress'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getProgress();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getProgress'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getStage of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_getStage00
static int tolua_GameStageTolua_LoadStepCounter_getStage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getStage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getStage();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getStage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: stageCompleted of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_stageCompleted00
static int tolua_GameStageTolua_LoadStepCounter_stageCompleted00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'stageCompleted'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->stageCompleted();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'stageCompleted'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setProgressFull of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_setProgressFull00
static int tolua_GameStageTolua_LoadStepCounter_setProgressFull00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setProgressFull'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setProgressFull();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setProgressFull'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setStep of class  LoadStepCounter */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_LoadStepCounter_setStep00
static int tolua_GameStageTolua_LoadStepCounter_setStep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"LoadStepCounter",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  LoadStepCounter* self = (LoadStepCounter*)  tolua_tousertype(tolua_S,1,0);
  int step = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setStep'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setStep(step);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setStep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_delete00
static int tolua_GameStageTolua_ClientGame_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getLoadStep of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getLoadStep00
static int tolua_GameStageTolua_ClientGame_getLoadStep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getLoadStep'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getLoadStep();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getLoadStep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getName of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getName00
static int tolua_GameStageTolua_ClientGame_getName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTypeName of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getTypeName00
static int tolua_GameStageTolua_ClientGame_getTypeName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTypeName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getTypeName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTypeName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInGame of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_isInGame00
static int tolua_GameStageTolua_ClientGame_isInGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInGame();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyPermits of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_applyPermits00
static int tolua_GameStageTolua_ClientGame_applyPermits00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyPermits'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyPermits(uin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyPermits'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: replyApplyPermits of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_replyApplyPermits00
static int tolua_GameStageTolua_ClientGame_replyApplyPermits00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int ret = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'replyApplyPermits'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->replyApplyPermits(uin,ret);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'replyApplyPermits'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameStage of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getGameStage00
static int tolua_GameStageTolua_ClientGame_getGameStage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameStage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameStage();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameStage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPing of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getPing00
static int tolua_GameStageTolua_ClientGame_getPing00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  const char* ip = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPing'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getPing(ip);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPing'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setInSetting of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setInSetting00
static int tolua_GameStageTolua_ClientGame_setInSetting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setInSetting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setInSetting(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setInSetting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInSetting of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_isInSetting00
static int tolua_GameStageTolua_ClientGame_isInSetting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInSetting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInSetting();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInSetting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setInModifyKey of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setInModifyKey00
static int tolua_GameStageTolua_ClientGame_setInModifyKey00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setInModifyKey'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setInModifyKey(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setInModifyKey'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInModifyKey of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_isInModifyKey00
static int tolua_GameStageTolua_ClientGame_isInModifyKey00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInModifyKey'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInModifyKey();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInModifyKey'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getMaxPlayerNum of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getMaxPlayerNum00
static int tolua_GameStageTolua_ClientGame_getMaxPlayerNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getMaxPlayerNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getMaxPlayerNum();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getMaxPlayerNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setMaxPlayerNum of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setMaxPlayerNum00
static int tolua_GameStageTolua_ClientGame_setMaxPlayerNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int num = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setMaxPlayerNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setMaxPlayerNum(num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setMaxPlayerNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurGameMaxPlayerSetLimit of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getCurGameMaxPlayerSetLimit00
static int tolua_GameStageTolua_ClientGame_getCurGameMaxPlayerSetLimit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurGameMaxPlayerSetLimit'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurGameMaxPlayerSetLimit();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurGameMaxPlayerSetLimit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRoomConnectMode of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getRoomConnectMode00
static int tolua_GameStageTolua_ClientGame_getRoomConnectMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRoomConnectMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getRoomConnectMode();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRoomConnectMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setHostPassword of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setHostPassword00
static int tolua_GameStageTolua_ClientGame_setHostPassword00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  const char* password = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setHostPassword'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setHostPassword(password);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setHostPassword'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHostPassword of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getHostPassword00
static int tolua_GameStageTolua_ClientGame_getHostPassword00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHostPassword'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getHostPassword();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHostPassword'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPublicType of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setPublicType00
static int tolua_GameStageTolua_ClientGame_setPublicType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int publicType = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPublicType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setPublicType(publicType);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPublicType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPublicType of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getPublicType00
static int tolua_GameStageTolua_ClientGame_getPublicType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPublicType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getPublicType();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPublicType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setCanTrace of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setCanTrace00
static int tolua_GameStageTolua_ClientGame_setCanTrace00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int canTrace = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setCanTrace'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setCanTrace(canTrace);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setCanTrace'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCanTrace of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getCanTrace00
static int tolua_GameStageTolua_ClientGame_getCanTrace00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCanTrace'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCanTrace();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCanTrace'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTeamResults of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getTeamResults00
static int tolua_GameStageTolua_ClientGame_getTeamResults00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTeamResults'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getTeamResults(teamid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTeamResults'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumTeam of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getNumTeam00
static int tolua_GameStageTolua_ClientGame_getNumTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumTeam();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setOperateUI of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setOperateUI00
static int tolua_GameStageTolua_ClientGame_setOperateUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setOperateUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setOperateUI(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setOperateUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getIMainPlayer of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getIMainPlayer00
static int tolua_GameStageTolua_ClientGame_getIMainPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getIMainPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IControlInterface* tolua_ret = (IControlInterface*)  self->getIMainPlayer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IControlInterface");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getIMainPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getMainPlayer of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getMainPlayer00
static int tolua_GameStageTolua_ClientGame_getMainPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getMainPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerControl* tolua_ret = (PlayerControl*)  self->getMainPlayer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerControl");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getMainPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getDebugInfo of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getDebugInfo00
static int tolua_GameStageTolua_ClientGame_getDebugInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  char* buffer = ((char*)  tolua_tostring(tolua_S,2,0));
  int buflen = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getDebugInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getDebugInfo(buffer,buflen);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getDebugInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: load of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_load00
static int tolua_GameStageTolua_ClientGame_load00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'load'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->load();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'load'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: unload of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_unload00
static int tolua_GameStageTolua_ClientGame_unload00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,2,NO_RELOAD));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unload'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unload(reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'unload'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateLoad of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_updateLoad00
static int tolua_GameStageTolua_ClientGame_updateLoad00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateLoad'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->updateLoad();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateLoad'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: reloadScene of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_reloadScene00
static int tolua_GameStageTolua_ClientGame_reloadScene00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'reloadScene'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->reloadScene();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'reloadScene'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: prepareTick of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_prepareTick00
static int tolua_GameStageTolua_ClientGame_prepareTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'prepareTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->prepareTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'prepareTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tick of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_tick00
static int tolua_GameStageTolua_ClientGame_tick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->tick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: update of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_update00
static int tolua_GameStageTolua_ClientGame_update00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  float dtime = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'update'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->update(dtime);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'update'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnInputEvent of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_OnInputEvent00
static int tolua_GameStageTolua_ClientGame_OnInputEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const Rainbow::InputEvent",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  const Rainbow::InputEvent* event = ((const Rainbow::InputEvent*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnInputEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->OnInputEvent(*event);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnInputEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: renderUI of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_renderUI00
static int tolua_GameStageTolua_ClientGame_renderUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool isHide = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'renderUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->renderUI(isHide);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'renderUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: renderUIEffect of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_renderUIEffect00
static int tolua_GameStageTolua_ClientGame_renderUIEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'renderUIEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->renderUIEffect();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'renderUIEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: beginGame of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_beginGame00
static int tolua_GameStageTolua_ClientGame_beginGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'beginGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->beginGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'beginGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: endGame of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_endGame00
static int tolua_GameStageTolua_ClientGame_endGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool isreload = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'endGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->endGame(isreload);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'endGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: pauseGame of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_pauseGame00
static int tolua_GameStageTolua_ClientGame_pauseGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'pauseGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->pauseGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'pauseGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: keepGame of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_keepGame00
static int tolua_GameStageTolua_ClientGame_keepGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'keepGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->keepGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'keepGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyGameSetData of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_applyGameSetData00
static int tolua_GameStageTolua_ClientGame_applyGameSetData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool viewchange = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyGameSetData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyGameSetData(viewchange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyGameSetData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applayGameSetData of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_applayGameSetData00
static int tolua_GameStageTolua_ClientGame_applayGameSetData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool viewchange = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applayGameSetData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applayGameSetData(viewchange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applayGameSetData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyScreenBright of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_applyScreenBright00
static int tolua_GameStageTolua_ClientGame_applyScreenBright00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyScreenBright'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyScreenBright();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyScreenBright'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumPlayers of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getNumPlayers00
static int tolua_GameStageTolua_ClientGame_getNumPlayers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumPlayers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumPlayers();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumPlayers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: kickoff of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_kickoff00
static int tolua_GameStageTolua_ClientGame_kickoff00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'kickoff'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->kickoff(uin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'kickoff'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: kickoffWithErrorCode of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_kickoffWithErrorCode00
static int tolua_GameStageTolua_ClientGame_kickoffWithErrorCode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int errcode = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'kickoffWithErrorCode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->kickoffWithErrorCode(uin,errcode);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'kickoffWithErrorCode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPlayerVisibleDispayName of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setPlayerVisibleDispayName00
static int tolua_GameStageTolua_ClientGame_setPlayerVisibleDispayName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPlayerVisibleDispayName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPlayerVisibleDispayName(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPlayerVisibleDispayName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isOperateUI of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_isOperateUI00
static int tolua_GameStageTolua_ClientGame_isOperateUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isOperateUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isOperateUI();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isOperateUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getMaxFPS of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getMaxFPS00
static int tolua_GameStageTolua_ClientGame_getMaxFPS00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getMaxFPS'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getMaxFPS();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getMaxFPS'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: canAcceptClientJoin of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_canAcceptClientJoin00
static int tolua_GameStageTolua_ClientGame_canAcceptClientJoin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'canAcceptClientJoin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->canAcceptClientJoin();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'canAcceptClientJoin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: canAcceptClientJoin of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_canAcceptClientJoin01
static int tolua_GameStageTolua_ClientGame_canAcceptClientJoin01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int kicktype = ((int)  tolua_tonumber(tolua_S,2,0));
  std::string reason = ((std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'canAcceptClientJoin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->canAcceptClientJoin(kicktype,reason);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)kicktype);
   tolua_pushcppstring(tolua_S,(const char*)reason);
  }
 }
 return 3;
tolua_lerror:
 return tolua_GameStageTolua_ClientGame_canAcceptClientJoin00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHasBegan of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getHasBegan00
static int tolua_GameStageTolua_ClientGame_getHasBegan00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHasBegan'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->getHasBegan();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHasBegan'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setHasBegan of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setHasBegan00
static int tolua_GameStageTolua_ClientGame_setHasBegan00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  bool began = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setHasBegan'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setHasBegan(began);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setHasBegan'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setGameLeaderUin of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_setGameLeaderUin00
static int tolua_GameStageTolua_ClientGame_setGameLeaderUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setGameLeaderUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setGameLeaderUin(uin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setGameLeaderUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameLeaderUin of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getGameLeaderUin00
static int tolua_GameStageTolua_ClientGame_getGameLeaderUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameLeaderUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameLeaderUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameLeaderUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: hostUpdateRoomInfoToServer of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_hostUpdateRoomInfoToServer00
static int tolua_GameStageTolua_ClientGame_hostUpdateRoomInfoToServer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'hostUpdateRoomInfoToServer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->hostUpdateRoomInfoToServer();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'hostUpdateRoomInfoToServer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnLoaded of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_OnLoaded00
static int tolua_GameStageTolua_ClientGame_OnLoaded00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnLoaded'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->OnLoaded();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnLoaded'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getLoadingStartTick of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGame_getLoadingStartTick00
static int tolua_GameStageTolua_ClientGame_getLoadingStartTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getLoadingStartTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getLoadingStartTick();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getLoadingStartTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: m_hasBegan of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_get_ClientGame_m_hasBegan
static int tolua_get_ClientGame_m_hasBegan(lua_State* tolua_S)
{
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_hasBegan'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->m_hasBegan);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: m_hasBegan of class  ClientGame */
#ifndef TOLUA_DISABLE_tolua_set_ClientGame_m_hasBegan
static int tolua_set_ClientGame_m_hasBegan(lua_State* tolua_S)
{
  ClientGame* self = (ClientGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_hasBegan'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->m_hasBegan = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: getName of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getName00
static int tolua_GameStageTolua_SurviveGame_getName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTypeName of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getTypeName00
static int tolua_GameStageTolua_SurviveGame_getTypeName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTypeName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getTypeName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTypeName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getDebugInfo of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getDebugInfo00
static int tolua_GameStageTolua_SurviveGame_getDebugInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  char* buffer = ((char*)  tolua_tostring(tolua_S,2,0));
  int buflen = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getDebugInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getDebugInfo(buffer,buflen);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getDebugInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: load of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_load00
static int tolua_GameStageTolua_SurviveGame_load00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'load'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->load();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'load'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: unload of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_unload00
static int tolua_GameStageTolua_SurviveGame_unload00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,2,NO_RELOAD));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unload'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unload(reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'unload'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameStage of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getGameStage00
static int tolua_GameStageTolua_SurviveGame_getGameStage00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameStage'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameStage();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameStage'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInGame of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_isInGame00
static int tolua_GameStageTolua_SurviveGame_isInGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInGame();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: prepareTick of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_prepareTick00
static int tolua_GameStageTolua_SurviveGame_prepareTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'prepareTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->prepareTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'prepareTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tick of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_tick00
static int tolua_GameStageTolua_SurviveGame_tick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->tick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: update of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_update00
static int tolua_GameStageTolua_SurviveGame_update00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  float dtime = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'update'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->update(dtime);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'update'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnInputEvent of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_OnInputEvent00
static int tolua_GameStageTolua_SurviveGame_OnInputEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const Rainbow::InputEvent",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const Rainbow::InputEvent* event = ((const Rainbow::InputEvent*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnInputEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->OnInputEvent(*event);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnInputEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: renderUI of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_renderUI00
static int tolua_GameStageTolua_SurviveGame_renderUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool isHide = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'renderUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->renderUI(isHide);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'renderUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: beginGame of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_beginGame00
static int tolua_GameStageTolua_SurviveGame_beginGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'beginGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->beginGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'beginGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: endGame of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_endGame00
static int tolua_GameStageTolua_SurviveGame_endGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool isreload = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'endGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->endGame(isreload);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'endGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: pauseGame of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_pauseGame00
static int tolua_GameStageTolua_SurviveGame_pauseGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'pauseGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->pauseGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'pauseGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurOpenContanierIndex of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getCurOpenContanierIndex00
static int tolua_GameStageTolua_SurviveGame_getCurOpenContanierIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurOpenContanierIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurOpenContanierIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurOpenContanierIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setOperateUI of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setOperateUI00
static int tolua_GameStageTolua_SurviveGame_setOperateUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setOperateUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setOperateUI(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setOperateUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isOperateUI of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_isOperateUI00
static int tolua_GameStageTolua_SurviveGame_isOperateUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isOperateUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isOperateUI();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isOperateUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getOperateUICount of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getOperateUICount00
static int tolua_GameStageTolua_SurviveGame_getOperateUICount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getOperateUICount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getOperateUICount();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getOperateUICount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getOperateUIState of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getOperateUIState00
static int tolua_GameStageTolua_SurviveGame_getOperateUIState00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getOperateUIState'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->getOperateUIState();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getOperateUIState'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setServerRoomId of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setServerRoomId00
static int tolua_GameStageTolua_SurviveGame_setServerRoomId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const std::string roomId = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setServerRoomId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setServerRoomId(roomId);
   tolua_pushcppstring(tolua_S,(const char*)roomId);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setServerRoomId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getServerRoomId of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getServerRoomId00
static int tolua_GameStageTolua_SurviveGame_getServerRoomId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const SurviveGame* self = (const SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getServerRoomId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getServerRoomId();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getServerRoomId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ResetOperateState of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_ResetOperateState00
static int tolua_GameStageTolua_SurviveGame_ResetOperateState00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ResetOperateState'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ResetOperateState();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ResetOperateState'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sendChat of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_sendChat00
static int tolua_GameStageTolua_SurviveGame_sendChat00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isstring(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const char* content = ((const char*)  tolua_tostring(tolua_S,2,0));
  int type = ((int)  tolua_tonumber(tolua_S,3,0));
  int targetuin = ((int)  tolua_tonumber(tolua_S,4,0));
  int language = ((int)  tolua_tonumber(tolua_S,5,1));
  const char* extend = ((const char*)  tolua_tostring(tolua_S,6,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sendChat'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sendChat(content,type,targetuin,language,extend);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sendChat'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sendChatToSelf of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_sendChatToSelf00
static int tolua_GameStageTolua_SurviveGame_sendChatToSelf00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const char* content = ((const char*)  tolua_tostring(tolua_S,2,0));
  int type = ((int)  tolua_tonumber(tolua_S,3,0));
  int language = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sendChatToSelf'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sendChatToSelf(content,type,language);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sendChatToSelf'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetSigns of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_SetSigns00
static int tolua_GameStageTolua_SurviveGame_SetSigns00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const char* content = ((const char*)  tolua_tostring(tolua_S,2,0));
  int x = ((int)  tolua_tonumber(tolua_S,3,0));
  int y = ((int)  tolua_tonumber(tolua_S,4,0));
  int z = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetSigns'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->SetSigns(content,x,y,z);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetSigns'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: summonAccountHorse of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_summonAccountHorse00
static int tolua_GameStageTolua_SurviveGame_summonAccountHorse00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int horseid = ((int)  tolua_tonumber(tolua_S,3,0));
  bool isshapeshift = ((bool)  tolua_toboolean(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'summonAccountHorse'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->summonAccountHorse(uin,horseid,isshapeshift);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'summonAccountHorse'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getMainPlayer of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getMainPlayer00
static int tolua_GameStageTolua_SurviveGame_getMainPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getMainPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerControl* tolua_ret = (PlayerControl*)  self->getMainPlayer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerControl");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getMainPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: enableMinimap of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_enableMinimap00
static int tolua_GameStageTolua_SurviveGame_enableMinimap00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'enableMinimap'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->enableMinimap(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'enableMinimap'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameTimeHour of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getGameTimeHour00
static int tolua_GameStageTolua_SurviveGame_getGameTimeHour00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool curworld = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameTimeHour'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameTimeHour(curworld);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameTimeHour'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameTimeMinute of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getGameTimeMinute00
static int tolua_GameStageTolua_SurviveGame_getGameTimeMinute00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool curworld = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameTimeMinute'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameTimeMinute(curworld);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameTimeMinute'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: playEffect of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_playEffect00
static int tolua_GameStageTolua_SurviveGame_playEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isstring(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int x = ((int)  tolua_tonumber(tolua_S,2,0));
  int y = ((int)  tolua_tonumber(tolua_S,3,0));
  int z = ((int)  tolua_tonumber(tolua_S,4,0));
  const char* ent = ((const char*)  tolua_tostring(tolua_S,5,0));
  int index = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'playEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->playEffect(x,y,z,ent,index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'playEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: playEffectVisibleBlock of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_playEffectVisibleBlock00
static int tolua_GameStageTolua_SurviveGame_playEffectVisibleBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isstring(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isboolean(tolua_S,7,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int x = ((int)  tolua_tonumber(tolua_S,2,0));
  int y = ((int)  tolua_tonumber(tolua_S,3,0));
  int z = ((int)  tolua_tonumber(tolua_S,4,0));
  const char* ent = ((const char*)  tolua_tostring(tolua_S,5,0));
  int index = ((int)  tolua_tonumber(tolua_S,6,0));
  bool sync2client = ((bool)  tolua_toboolean(tolua_S,7,true));
  int visibledistblock = ((int)  tolua_tonumber(tolua_S,8,16));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'playEffectVisibleBlock'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->playEffectVisibleBlock(x,y,z,ent,index,sync2client,visibledistblock);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'playEffectVisibleBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: playEffectInRoleFront of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_playEffectInRoleFront00
static int tolua_GameStageTolua_SurviveGame_playEffectInRoleFront00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  const char* ent = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'playEffectInRoleFront'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->playEffectInRoleFront(ent);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'playEffectInRoleFront'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: stopEffect of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_stopEffect00
static int tolua_GameStageTolua_SurviveGame_stopEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'stopEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->stopEffect(index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'stopEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: stopEffect of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_stopEffect01
static int tolua_GameStageTolua_SurviveGame_stopEffect01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'stopEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->stopEffect();
  }
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_SurviveGame_stopEffect00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: addmob of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_addmob00
static int tolua_GameStageTolua_SurviveGame_addmob00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int mobid = ((int)  tolua_tonumber(tolua_S,2,0));
  const char* effect = ((const char*)  tolua_tostring(tolua_S,3,NULL));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addmob'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addmob(mobid,effect);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addmob'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumPlayerBriefInfo of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getNumPlayerBriefInfo00
static int tolua_GameStageTolua_SurviveGame_getNumPlayerBriefInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumPlayerBriefInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumPlayerBriefInfo();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumPlayerBriefInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPlayerBriefInfo of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getPlayerBriefInfo00
static int tolua_GameStageTolua_SurviveGame_getPlayerBriefInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int i = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPlayerBriefInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerBriefInfo* tolua_ret = (PlayerBriefInfo*)  self->getPlayerBriefInfo(i);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerBriefInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPlayerBriefInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: findPlayerInfoByUin of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_findPlayerInfoByUin00
static int tolua_GameStageTolua_SurviveGame_findPlayerInfoByUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int i = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'findPlayerInfoByUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerBriefInfo* tolua_ret = (PlayerBriefInfo*)  self->findPlayerInfoByUin(i);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerBriefInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'findPlayerInfoByUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setTeamScore of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setTeamScore00
static int tolua_GameStageTolua_SurviveGame_setTeamScore00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
  int s = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setTeamScore'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setTeamScore(teamid,s);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setTeamScore'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTeamScore of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getTeamScore00
static int tolua_GameStageTolua_SurviveGame_getTeamScore00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTeamScore'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getTeamScore(teamid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTeamScore'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setTeamResults of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setTeamResults00
static int tolua_GameStageTolua_SurviveGame_setTeamResults00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
  int r = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setTeamResults'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setTeamResults(teamid,r);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setTeamResults'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTeamResults of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getTeamResults00
static int tolua_GameStageTolua_SurviveGame_getTeamResults00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTeamResults'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getTeamResults(teamid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTeamResults'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumTeam of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getNumTeam00
static int tolua_GameStageTolua_SurviveGame_getNumTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumTeam();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRandomPlayer of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getRandomPlayer00
static int tolua_GameStageTolua_SurviveGame_getRandomPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,-1));
  int alive = ((int)  tolua_tonumber(tolua_S,3,-1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRandomPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientPlayer* tolua_ret = (ClientPlayer*)  self->getRandomPlayer(teamid,alive);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRandomPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumPlayers of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getNumPlayers00
static int tolua_GameStageTolua_SurviveGame_getNumPlayers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,-1));
  int alive = ((int)  tolua_tonumber(tolua_S,3,-1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumPlayers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumPlayers(teamid,alive);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumPlayers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPlayerVisibleDispayName of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setPlayerVisibleDispayName00
static int tolua_GameStageTolua_SurviveGame_setPlayerVisibleDispayName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPlayerVisibleDispayName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPlayerVisibleDispayName(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPlayerVisibleDispayName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPlayersResults of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setPlayersResults00
static int tolua_GameStageTolua_SurviveGame_setPlayersResults00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,0));
  int r = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPlayersResults'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPlayersResults(teamid,r);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPlayersResults'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: requireArrayOfPlayers of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_requireArrayOfPlayers00
static int tolua_GameStageTolua_SurviveGame_requireArrayOfPlayers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int teamid = ((int)  tolua_tonumber(tolua_S,2,-1));
  int alive = ((int)  tolua_tonumber(tolua_S,3,-1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'requireArrayOfPlayers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->requireArrayOfPlayers(teamid,alive);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'requireArrayOfPlayers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getIthPlayerInArray of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getIthPlayerInArray00
static int tolua_GameStageTolua_SurviveGame_getIthPlayerInArray00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int i = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getIthPlayerInArray'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientPlayer* tolua_ret = (ClientPlayer*)  self->getIthPlayerInArray(i);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getIthPlayerInArray'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPlayerByUin of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getPlayerByUin00
static int tolua_GameStageTolua_SurviveGame_getPlayerByUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPlayerByUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientPlayer* tolua_ret = (ClientPlayer*)  self->getPlayerByUin(uin);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPlayerByUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: changePlayerTeam of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_changePlayerTeam00
static int tolua_GameStageTolua_SurviveGame_changePlayerTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int teamid = ((int)  tolua_tonumber(tolua_S,3,0));
  bool bResetAttr = ((bool)  tolua_toboolean(tolua_S,4,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'changePlayerTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->changePlayerTeam(uin,teamid,bResetAttr);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'changePlayerTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: RentChangePlayerTeam of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_RentChangePlayerTeam00
static int tolua_GameStageTolua_SurviveGame_RentChangePlayerTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int teamid = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'RentChangePlayerTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->RentChangePlayerTeam(uin,teamid);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'RentChangePlayerTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: hostStartGame of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_hostStartGame00
static int tolua_GameStageTolua_SurviveGame_hostStartGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'hostStartGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->hostStartGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'hostStartGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: resetGameRuleData of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_resetGameRuleData00
static int tolua_GameStageTolua_SurviveGame_resetGameRuleData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'resetGameRuleData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->resetGameRuleData();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'resetGameRuleData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRuleOptionVal of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getRuleOptionVal00
static int tolua_GameStageTolua_SurviveGame_getRuleOptionVal00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int ruleid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRuleOptionVal'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   float tolua_ret = (float)  self->getRuleOptionVal(ruleid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRuleOptionVal'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPersonalRentLeftSeconds of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getPersonalRentLeftSeconds00
static int tolua_GameStageTolua_SurviveGame_getPersonalRentLeftSeconds00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPersonalRentLeftSeconds'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getPersonalRentLeftSeconds();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPersonalRentLeftSeconds'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isLockViewMode of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_isLockViewMode00
static int tolua_GameStageTolua_SurviveGame_isLockViewMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isLockViewMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isLockViewMode();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isLockViewMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: showOperateUI of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_showOperateUI00
static int tolua_GameStageTolua_SurviveGame_showOperateUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool state = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'showOperateUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->showOperateUI(state);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'showOperateUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: HideTouchControlUi of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_HideTouchControlUi00
static int tolua_GameStageTolua_SurviveGame_HideTouchControlUi00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  bool bHide = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'HideTouchControlUi'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->HideTouchControlUi(bHide);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'HideTouchControlUi'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isHost of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_isHost00
static int tolua_GameStageTolua_SurviveGame_isHost00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isHost'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isHost(uin);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isHost'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHostUin of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getHostUin00
static int tolua_GameStageTolua_SurviveGame_getHostUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHostUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getHostUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHostUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: InviteJoinRoom of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_InviteJoinRoom00
static int tolua_GameStageTolua_SurviveGame_InviteJoinRoom00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isstring(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  char* RoomState = ((char*)  tolua_tostring(tolua_S,3,0));
  char* PassWorld = ((char*)  tolua_tostring(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'InviteJoinRoom'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->InviteJoinRoom(uin,RoomState,PassWorld);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'InviteJoinRoom'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: world2RadarPos of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_world2RadarPos00
static int tolua_GameStageTolua_SurviveGame_world2RadarPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int x = ((int)  tolua_tonumber(tolua_S,2,0));
  int z = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'world2RadarPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->world2RadarPos(x,z);
   tolua_pushnumber(tolua_S,(lua_Number)x);
   tolua_pushnumber(tolua_S,(lua_Number)z);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'world2RadarPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: world2RadarDist of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_world2RadarDist00
static int tolua_GameStageTolua_SurviveGame_world2RadarDist00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  int d = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'world2RadarDist'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->world2RadarDist(d);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'world2RadarDist'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setCameraDepth of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_setCameraDepth00
static int tolua_GameStageTolua_SurviveGame_setCameraDepth00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  float n = ((float)  tolua_tonumber(tolua_S,2,0));
  float f = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setCameraDepth'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setCameraDepth(n,f);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setCameraDepth'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHaveJudge of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getHaveJudge00
static int tolua_GameStageTolua_SurviveGame_getHaveJudge00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHaveJudge'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->getHaveJudge();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHaveJudge'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getJudgeUin of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getJudgeUin00
static int tolua_GameStageTolua_SurviveGame_getJudgeUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getJudgeUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getJudgeUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getJudgeUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: initNewShortcut of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_initNewShortcut00
static int tolua_GameStageTolua_SurviveGame_initNewShortcut00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  ClientPlayer* player = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'initNewShortcut'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->initNewShortcut(player);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'initNewShortcut'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: transferToTargetMap of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_transferToTargetMap00
static int tolua_GameStageTolua_SurviveGame_transferToTargetMap00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  WCoord* pos = ((WCoord*)  tolua_tousertype(tolua_S,2,0));
  int mapid = ((int)  tolua_tonumber(tolua_S,3,0));
  bool isRocketTypeTeleport = ((bool)  tolua_toboolean(tolua_S,4,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'transferToTargetMap'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->transferToTargetMap(*pos,mapid,isRocketTypeTeleport);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'transferToTargetMap'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameLeaderUin of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_getGameLeaderUin00
static int tolua_GameStageTolua_SurviveGame_getGameLeaderUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameLeaderUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameLeaderUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameLeaderUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: requireArrayOfAllPlayers of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_requireArrayOfAllPlayers00
static int tolua_GameStageTolua_SurviveGame_requireArrayOfAllPlayers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'requireArrayOfAllPlayers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->requireArrayOfAllPlayers();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'requireArrayOfAllPlayers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: initRoleInfo of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_initRoleInfo00
static int tolua_GameStageTolua_SurviveGame_initRoleInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'initRoleInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->initRoleInfo();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'initRoleInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: findNearestPlayerByPos of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SurviveGame_findNearestPlayerByPos00
static int tolua_GameStageTolua_SurviveGame_findNearestPlayerByPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SurviveGame",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
  float posx = ((float)  tolua_tonumber(tolua_S,2,0));
  float posy = ((float)  tolua_tonumber(tolua_S,3,0));
  float posz = ((float)  tolua_tonumber(tolua_S,4,0));
  int worldmapid = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'findNearestPlayerByPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->findNearestPlayerByPos(posx,posy,posz,worldmapid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'findNearestPlayerByPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: m_Effects of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_get_SurviveGame_m_Effects
static int tolua_get_SurviveGame_m_Effects(lua_State* tolua_S)
{
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_Effects'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)&self->m_Effects,"std::map<int,EffectParticle*>");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: m_Effects of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_set_SurviveGame_m_Effects
static int tolua_set_SurviveGame_m_Effects(lua_State* tolua_S)
{
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_Effects'",NULL);
  if ((tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::map<int,EffectParticle*>",0,&tolua_err)))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->m_Effects = *((std::map<int,EffectParticle*>*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: m_OpenCmd of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_get_SurviveGame_m_OpenCmd
static int tolua_get_SurviveGame_m_OpenCmd(lua_State* tolua_S)
{
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_OpenCmd'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->m_OpenCmd);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: m_OpenCmd of class  SurviveGame */
#ifndef TOLUA_DISABLE_tolua_set_SurviveGame_m_OpenCmd
static int tolua_set_SurviveGame_m_OpenCmd(lua_State* tolua_S)
{
  SurviveGame* self = (SurviveGame*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'm_OpenCmd'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->m_OpenCmd = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_new00
static int tolua_GameStageTolua_MainMenuStage_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MainMenuStage* tolua_ret = (MainMenuStage*)  Mtolua_new((MainMenuStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MainMenuStage");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_new00_local
static int tolua_GameStageTolua_MainMenuStage_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   MainMenuStage* tolua_ret = (MainMenuStage*)  Mtolua_new((MainMenuStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MainMenuStage");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_delete00
static int tolua_GameStageTolua_MainMenuStage_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: createClickPos of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_createClickPos00
static int tolua_GameStageTolua_MainMenuStage_createClickPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
  int x = ((int)  tolua_tonumber(tolua_S,2,0));
  int y = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'createClickPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->createClickPos(x,y);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'createClickPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: loadBGWorld of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_loadBGWorld00
static int tolua_GameStageTolua_MainMenuStage_loadBGWorld00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'loadBGWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->loadBGWorld();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'loadBGWorld'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: unloadBGWorld of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_unloadBGWorld00
static int tolua_GameStageTolua_MainMenuStage_unloadBGWorld00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unloadBGWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unloadBGWorld();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'unloadBGWorld'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSelectRoleIndex of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex00
static int tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSelectRoleIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSelectRoleIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSelectRoleIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setClickEffectEnabled of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled00
static int tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
  bool show = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setClickEffectEnabled'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setClickEffectEnabled(show);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setClickEffectEnabled'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getLoadStepCounter of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_getLoadStepCounter00
static int tolua_GameStageTolua_MainMenuStage_getLoadStepCounter00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getLoadStepCounter'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   LoadStepCounter* tolua_ret = (LoadStepCounter*)  self->getLoadStepCounter();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"LoadStepCounter");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getLoadStepCounter'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_new01
static int tolua_GameStageTolua_MainMenuStage_new01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  {
   MainMenuStage* tolua_ret = (MainMenuStage*)  Mtolua_new((MainMenuStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MainMenuStage");
  }
 }
 return 1;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_new00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_new01_local
static int tolua_GameStageTolua_MainMenuStage_new01_local(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  {
   MainMenuStage* tolua_ret = (MainMenuStage*)  Mtolua_new((MainMenuStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MainMenuStage");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_new00_local(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_delete01
static int tolua_GameStageTolua_MainMenuStage_delete01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_delete00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: createClickPos of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_createClickPos01
static int tolua_GameStageTolua_MainMenuStage_createClickPos01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
  int x = ((int)  tolua_tonumber(tolua_S,2,0));
  int y = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'createClickPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->createClickPos(x,y);
  }
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_createClickPos00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: loadBGWorld of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_loadBGWorld01
static int tolua_GameStageTolua_MainMenuStage_loadBGWorld01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'loadBGWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->loadBGWorld();
  }
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_loadBGWorld00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: unloadBGWorld of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_unloadBGWorld01
static int tolua_GameStageTolua_MainMenuStage_unloadBGWorld01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unloadBGWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unloadBGWorld();
  }
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_unloadBGWorld00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSelectRoleIndex of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex01
static int tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSelectRoleIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSelectRoleIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: setClickEffectEnabled of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled01
static int tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
  bool show = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setClickEffectEnabled'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setClickEffectEnabled(show);
  }
 }
 return 0;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: getLoadStepCounter of class  MainMenuStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MainMenuStage_getLoadStepCounter01
static int tolua_GameStageTolua_MainMenuStage_getLoadStepCounter01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MainMenuStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MainMenuStage* self = (MainMenuStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getLoadStepCounter'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   LoadStepCounter* tolua_ret = (LoadStepCounter*)  self->getLoadStepCounter();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"LoadStepCounter");
  }
 }
 return 1;
tolua_lerror:
 return tolua_GameStageTolua_MainMenuStage_getLoadStepCounter00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  SimpleLoadingStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SimpleLoadingStage_new00
static int tolua_GameStageTolua_SimpleLoadingStage_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SimpleLoadingStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SimpleLoadingStage* tolua_ret = (SimpleLoadingStage*)  Mtolua_new((SimpleLoadingStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SimpleLoadingStage");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  SimpleLoadingStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SimpleLoadingStage_new00_local
static int tolua_GameStageTolua_SimpleLoadingStage_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SimpleLoadingStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SimpleLoadingStage* tolua_ret = (SimpleLoadingStage*)  Mtolua_new((SimpleLoadingStage)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SimpleLoadingStage");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  SimpleLoadingStage */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_SimpleLoadingStage_delete00
static int tolua_GameStageTolua_SimpleLoadingStage_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SimpleLoadingStage",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SimpleLoadingStage* self = (SimpleLoadingStage*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sendChat of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_sendChat00
static int tolua_GameStageTolua_MpGameSurvive_sendChat00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isstring(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  const char* content = ((const char*)  tolua_tostring(tolua_S,2,0));
  int type = ((int)  tolua_tonumber(tolua_S,3,0));
  int targetuin = ((int)  tolua_tonumber(tolua_S,4,0));
  int language = ((int)  tolua_tonumber(tolua_S,5,1));
  const char* extend = ((const char*)  tolua_tostring(tolua_S,6,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sendChat'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sendChat(content,type,targetuin,language,extend);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sendChat'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getName of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getName00
static int tolua_GameStageTolua_MpGameSurvive_getName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTypeName of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getTypeName00
static int tolua_GameStageTolua_MpGameSurvive_getTypeName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTypeName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getTypeName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTypeName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getDebugInfo of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getDebugInfo00
static int tolua_GameStageTolua_MpGameSurvive_getDebugInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  char* buffer = ((char*)  tolua_tostring(tolua_S,2,0));
  int buflen = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getDebugInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getDebugInfo(buffer,buflen);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getDebugInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: load of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_load00
static int tolua_GameStageTolua_MpGameSurvive_load00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'load'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->load();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'load'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: unload of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_unload00
static int tolua_GameStageTolua_MpGameSurvive_unload00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,2,NO_RELOAD));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unload'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unload(reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'unload'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: mpGameUnload of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_mpGameUnload00
static int tolua_GameStageTolua_MpGameSurvive_mpGameUnload00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'mpGameUnload'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->mpGameUnload(reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'mpGameUnload'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: endGame of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_endGame00
static int tolua_GameStageTolua_MpGameSurvive_endGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  bool isreload = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'endGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->endGame(isreload);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'endGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: findPlayerInfoByUin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_findPlayerInfoByUin00
static int tolua_GameStageTolua_MpGameSurvive_findPlayerInfoByUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'findPlayerInfoByUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerBriefInfo* tolua_ret = (PlayerBriefInfo*)  self->findPlayerInfoByUin(uin);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerBriefInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'findPlayerInfoByUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addPlayerBriefInfo of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_addPlayerBriefInfo00
static int tolua_GameStageTolua_MpGameSurvive_addPlayerBriefInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addPlayerBriefInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PlayerBriefInfo* tolua_ret = (PlayerBriefInfo*)  self->addPlayerBriefInfo(uin);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"PlayerBriefInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addPlayerBriefInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNumPlayerBriefInfo of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getNumPlayerBriefInfo00
static int tolua_GameStageTolua_MpGameSurvive_getNumPlayerBriefInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNumPlayerBriefInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getNumPlayerBriefInfo();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNumPlayerBriefInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isHost of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_isHost00
static int tolua_GameStageTolua_MpGameSurvive_isHost00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isHost'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isHost(uin);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isHost'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHostUin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getHostUin00
static int tolua_GameStageTolua_MpGameSurvive_getHostUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHostUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getHostUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHostUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: canAcceptClientJoin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin00
static int tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'canAcceptClientJoin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->canAcceptClientJoin();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'canAcceptClientJoin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: canAcceptClientJoin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin01
static int tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  int kicktype = ((int)  tolua_tonumber(tolua_S,2,0));
  std::string reason = ((std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'canAcceptClientJoin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->canAcceptClientJoin(kicktype,reason);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)kicktype);
   tolua_pushcppstring(tolua_S,(const char*)reason);
  }
 }
 return 3;
tolua_lerror:
 return tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNetDelayTick of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getNetDelayTick00
static int tolua_GameStageTolua_MpGameSurvive_getNetDelayTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNetDelayTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   unsigned int tolua_ret = (unsigned int)  self->getNetDelayTick();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNetDelayTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setGameLeaderUin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_setGameLeaderUin00
static int tolua_GameStageTolua_MpGameSurvive_setGameLeaderUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setGameLeaderUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setGameLeaderUin(uin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setGameLeaderUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameLeaderUin of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_getGameLeaderUin00
static int tolua_GameStageTolua_MpGameSurvive_getGameLeaderUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameLeaderUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameLeaderUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameLeaderUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: broadCorrectNickName of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_MpGameSurvive_broadCorrectNickName00
static int tolua_GameStageTolua_MpGameSurvive_broadCorrectNickName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"MpGameSurvive",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
  const std::string ret = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'broadCorrectNickName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->broadCorrectNickName(ret);
   tolua_pushcppstring(tolua_S,(const char*)ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'broadCorrectNickName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: __ReachabilityObserver__ of class  MpGameSurvive */
#ifndef TOLUA_DISABLE_tolua_get_MpGameSurvive___ReachabilityObserver__
static int tolua_get_MpGameSurvive___ReachabilityObserver__(lua_State* tolua_S)
{
  MpGameSurvive* self = (MpGameSurvive*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '__ReachabilityObserver__'",NULL);
#else 
  if (!self) return 0;
#endif
#ifdef __cplusplus
   tolua_pushusertype(tolua_S,(void*)static_cast<ReachabilityObserver*>(self), "ReachabilityObserver");
#else
   tolua_pushusertype(tolua_S,(void*)((ReachabilityObserver*)self), "ReachabilityObserver");
#endif
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_new00
static int tolua_GameStageTolua_GameSurviveRecord_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   GameSurviveRecord* tolua_ret = (GameSurviveRecord*)  Mtolua_new((GameSurviveRecord)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"GameSurviveRecord");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_new00_local
static int tolua_GameStageTolua_GameSurviveRecord_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   GameSurviveRecord* tolua_ret = (GameSurviveRecord*)  Mtolua_new((GameSurviveRecord)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"GameSurviveRecord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_delete00
static int tolua_GameStageTolua_GameSurviveRecord_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getName of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_getName00
static int tolua_GameStageTolua_GameSurviveRecord_getName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTypeName of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_getTypeName00
static int tolua_GameStageTolua_GameSurviveRecord_getTypeName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTypeName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getTypeName();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTypeName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: load of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_load00
static int tolua_GameStageTolua_GameSurviveRecord_load00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'load'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->load();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'load'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: unload of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_unload00
static int tolua_GameStageTolua_GameSurviveRecord_unload00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,2,NO_RELOAD));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'unload'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->unload(reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'unload'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tick of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_tick00
static int tolua_GameStageTolua_GameSurviveRecord_tick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->tick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: update of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_update00
static int tolua_GameStageTolua_GameSurviveRecord_update00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
  float dtime = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'update'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->update(dtime);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'update'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sendChat of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_sendChat00
static int tolua_GameStageTolua_GameSurviveRecord_sendChat00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isstring(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
  const char* content = ((const char*)  tolua_tostring(tolua_S,2,0));
  int type = ((int)  tolua_tonumber(tolua_S,3,0));
  int targetuin = ((int)  tolua_tonumber(tolua_S,4,0));
  int language = ((int)  tolua_tonumber(tolua_S,5,1));
  const char* extend = ((const char*)  tolua_tostring(tolua_S,6,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sendChat'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sendChat(content,type,targetuin,language,extend);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sendChat'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: executeRecord of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_executeRecord00
static int tolua_GameStageTolua_GameSurviveRecord_executeRecord00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
  bool isRefresh = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'executeRecord'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->executeRecord(isRefresh);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'executeRecord'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: beginGame of class  GameSurviveRecord */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GameSurviveRecord_beginGame00
static int tolua_GameStageTolua_GameSurviveRecord_beginGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameSurviveRecord",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameSurviveRecord* self = (GameSurviveRecord*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'beginGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->beginGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'beginGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_delete00
static int tolua_GameStageTolua_ClientGameManager_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: createNetHandlerRegister of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_createNetHandlerRegister00
static int tolua_GameStageTolua_ClientGameManager_createNetHandlerRegister00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'createNetHandlerRegister'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->createNetHandlerRegister();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'createNetHandlerRegister'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: releaseGameData of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_releaseGameData00
static int tolua_GameStageTolua_ClientGameManager_releaseGameData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'releaseGameData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->releaseGameData();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'releaseGameData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applayGameSetData of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_applayGameSetData00
static int tolua_GameStageTolua_ClientGameManager_applayGameSetData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  bool viewchange = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applayGameSetData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applayGameSetData(viewchange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applayGameSetData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyScreenBright of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_applyScreenBright00
static int tolua_GameStageTolua_ClientGameManager_applyScreenBright00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyScreenBright'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyScreenBright();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyScreenBright'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyGameSetData of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_applyGameSetData00
static int tolua_GameStageTolua_ClientGameManager_applyGameSetData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  bool viewchange = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyGameSetData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyGameSetData(viewchange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyGameSetData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: gotoGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_gotoGame00
static int tolua_GameStageTolua_ClientGameManager_gotoGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  const char* name = ((const char*)  tolua_tostring(tolua_S,2,0));
  GAME_RELOAD_TYPE reloadtype = ((GAME_RELOAD_TYPE) (int)  tolua_tonumber(tolua_S,3,NO_RELOAD));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'gotoGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->gotoGame(name,reloadtype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'gotoGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_isInGame00
static int tolua_GameStageTolua_ClientGameManager_isInGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInGame();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_addGame00
static int tolua_GameStageTolua_ClientGameManager_addGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  const char* name = ((const char*)  tolua_tostring(tolua_S,2,0));
  ClientGame* game = ((ClientGame*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addGame(name,game);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_getCurGame00
static int tolua_GameStageTolua_ClientGameManager_getCurGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientGame* tolua_ret = (ClientGame*)  self->getCurGame();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientGame");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setCurGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_setCurGame00
static int tolua_GameStageTolua_ClientGameManager_setCurGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  ClientGame* game = ((ClientGame*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setCurGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setCurGame(game);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setCurGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getLoadingGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_getLoadingGame00
static int tolua_GameStageTolua_ClientGameManager_getLoadingGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getLoadingGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientGame* tolua_ret = (ClientGame*)  self->getLoadingGame();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientGame");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getLoadingGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setLoadingGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_setLoadingGame00
static int tolua_GameStageTolua_ClientGameManager_setLoadingGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  ClientGame* game = ((ClientGame*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setLoadingGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setLoadingGame(game);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setLoadingGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_getGame00
static int tolua_GameStageTolua_ClientGameManager_getGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  const char* name = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientGame* tolua_ret = (ClientGame*)  self->getGame(name);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientGame");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getMPGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_getMPGame00
static int tolua_GameStageTolua_ClientGameManager_getMPGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getMPGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MpGameSurvive* tolua_ret = (MpGameSurvive*)  self->getMPGame();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MpGameSurvive");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getMPGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateLoadingGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_updateLoadingGame00
static int tolua_GameStageTolua_ClientGameManager_updateLoadingGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateLoadingGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->updateLoadingGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateLoadingGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearCurGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_clearCurGame00
static int tolua_GameStageTolua_ClientGameManager_clearCurGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearCurGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearCurGame();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearCurGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: removeGame of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_removeGame00
static int tolua_GameStageTolua_ClientGameManager_removeGame00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientGame",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  ClientGame* game = ((ClientGame*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'removeGame'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->removeGame(game);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'removeGame'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getReloadGameType of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_getReloadGameType00
static int tolua_GameStageTolua_ClientGameManager_getReloadGameType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getReloadGameType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   GAME_RELOAD_TYPE tolua_ret = (GAME_RELOAD_TYPE)  self->getReloadGameType();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getReloadGameType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: onStop of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_onStop00
static int tolua_GameStageTolua_ClientGameManager_onStop00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'onStop'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->onStop();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'onStop'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: RegisterInputHandler of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_RegisterInputHandler00
static int tolua_GameStageTolua_ClientGameManager_RegisterInputHandler00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'RegisterInputHandler'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->RegisterInputHandler();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'RegisterInputHandler'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CheckWindowLostFocus of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_CheckWindowLostFocus00
static int tolua_GameStageTolua_ClientGameManager_CheckWindowLostFocus00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CheckWindowLostFocus'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->CheckWindowLostFocus();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CheckWindowLostFocus'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isJudgeOrSpectator of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_isJudgeOrSpectator00
static int tolua_GameStageTolua_ClientGameManager_isJudgeOrSpectator00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
  int type = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isJudgeOrSpectator'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isJudgeOrSpectator(uin,type);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isJudgeOrSpectator'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: disableChkRoomTicks of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_disableChkRoomTicks00
static int tolua_GameStageTolua_ClientGameManager_disableChkRoomTicks00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  bool disabled = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'disableChkRoomTicks'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->disableChkRoomTicks(disabled);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'disableChkRoomTicks'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: chkRoomTicks of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_chkRoomTicks00
static int tolua_GameStageTolua_ClientGameManager_chkRoomTicks00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'chkRoomTicks'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->chkRoomTicks();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'chkRoomTicks'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: mapSceneLoadFinish of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_mapSceneLoadFinish00
static int tolua_GameStageTolua_ClientGameManager_mapSceneLoadFinish00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'mapSceneLoadFinish'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->mapSceneLoadFinish();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'mapSceneLoadFinish'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setMapSceneLoadTips of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_setMapSceneLoadTips00
static int tolua_GameStageTolua_ClientGameManager_setMapSceneLoadTips00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  std::string tips = ((std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setMapSceneLoadTips'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setMapSceneLoadTips(tips);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setMapSceneLoadTips'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setMapSceneLoadProgress of class  ClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_ClientGameManager_setMapSceneLoadProgress00
static int tolua_GameStageTolua_ClientGameManager_setMapSceneLoadProgress00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ClientGameManager",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientGameManager* self = (ClientGameManager*)  tolua_tousertype(tolua_S,1,0);
  int progress = ((int)  tolua_tonumber(tolua_S,2,0));
  int max = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setMapSceneLoadProgress'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setMapSceneLoadProgress(progress,max);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setMapSceneLoadProgress'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* function: GetClientGameManager */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GetClientGameManager00
static int tolua_GameStageTolua_GetClientGameManager00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnoobj(tolua_S,1,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   ClientGameManager& tolua_ret = (ClientGameManager&)  GetClientGameManager();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"ClientGameManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetClientGameManager'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* function: GetClientGameManagerPtr */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_GetClientGameManagerPtr00
static int tolua_GameStageTolua_GetClientGameManagerPtr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnoobj(tolua_S,1,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   ClientGameManager* tolua_ret = (ClientGameManager*)  GetClientGameManagerPtr();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientGameManager");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetClientGameManagerPtr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnInputEvent of class  UIStageInputHandler */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_UIStageInputHandler_OnInputEvent00
static int tolua_GameStageTolua_UIStageInputHandler_OnInputEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"UIStageInputHandler",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const Rainbow::InputEvent",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  UIStageInputHandler* self = (UIStageInputHandler*)  tolua_tousertype(tolua_S,1,0);
  const Rainbow::InputEvent* event = ((const Rainbow::InputEvent*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnInputEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->OnInputEvent(*event);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnInputEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: EmitCustomEvent of class  UIStageSandboxObject */
#ifndef TOLUA_DISABLE_tolua_GameStageTolua_UIStageSandboxObject_EmitCustomEvent00
static int tolua_GameStageTolua_UIStageSandboxObject_EmitCustomEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"UIStageSandboxObject",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const MNSandbox::SandboxContext",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  UIStageSandboxObject* self = (UIStageSandboxObject*)  tolua_tousertype(tolua_S,1,0);
  const char* eventName = ((const char*)  tolua_tostring(tolua_S,2,0));
  const MNSandbox::SandboxContext* context = ((const MNSandbox::SandboxContext*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'EmitCustomEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->EmitCustomEvent(eventName,*context);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'EmitCustomEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_GameStageTolua_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  tolua_cclass(tolua_S,"LoadStepCounter","LoadStepCounter","",NULL);
  tolua_beginmodule(tolua_S,"LoadStepCounter");
   tolua_function(tolua_S,"reset",tolua_GameStageTolua_LoadStepCounter_reset00);
   tolua_function(tolua_S,"gotoStage",tolua_GameStageTolua_LoadStepCounter_gotoStage00);
   tolua_function(tolua_S,"step",tolua_GameStageTolua_LoadStepCounter_step00);
   tolua_function(tolua_S,"getProgress",tolua_GameStageTolua_LoadStepCounter_getProgress00);
   tolua_function(tolua_S,"getStage",tolua_GameStageTolua_LoadStepCounter_getStage00);
   tolua_function(tolua_S,"stageCompleted",tolua_GameStageTolua_LoadStepCounter_stageCompleted00);
   tolua_function(tolua_S,"setProgressFull",tolua_GameStageTolua_LoadStepCounter_setProgressFull00);
   tolua_function(tolua_S,"setStep",tolua_GameStageTolua_LoadStepCounter_setStep00);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"ClientGame","ClientGame","",tolua_collect_ClientGame);
  #else
  tolua_cclass(tolua_S,"ClientGame","ClientGame","",NULL);
  #endif
  tolua_beginmodule(tolua_S,"ClientGame");
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_ClientGame_delete00);
   tolua_function(tolua_S,"getLoadStep",tolua_GameStageTolua_ClientGame_getLoadStep00);
   tolua_function(tolua_S,"getName",tolua_GameStageTolua_ClientGame_getName00);
   tolua_function(tolua_S,"getTypeName",tolua_GameStageTolua_ClientGame_getTypeName00);
   tolua_function(tolua_S,"isInGame",tolua_GameStageTolua_ClientGame_isInGame00);
   tolua_function(tolua_S,"applyPermits",tolua_GameStageTolua_ClientGame_applyPermits00);
   tolua_function(tolua_S,"replyApplyPermits",tolua_GameStageTolua_ClientGame_replyApplyPermits00);
   tolua_function(tolua_S,"getGameStage",tolua_GameStageTolua_ClientGame_getGameStage00);
   tolua_function(tolua_S,"getPing",tolua_GameStageTolua_ClientGame_getPing00);
   tolua_function(tolua_S,"setInSetting",tolua_GameStageTolua_ClientGame_setInSetting00);
   tolua_function(tolua_S,"isInSetting",tolua_GameStageTolua_ClientGame_isInSetting00);
   tolua_function(tolua_S,"setInModifyKey",tolua_GameStageTolua_ClientGame_setInModifyKey00);
   tolua_function(tolua_S,"isInModifyKey",tolua_GameStageTolua_ClientGame_isInModifyKey00);
   tolua_function(tolua_S,"getMaxPlayerNum",tolua_GameStageTolua_ClientGame_getMaxPlayerNum00);
   tolua_function(tolua_S,"setMaxPlayerNum",tolua_GameStageTolua_ClientGame_setMaxPlayerNum00);
   tolua_function(tolua_S,"getCurGameMaxPlayerSetLimit",tolua_GameStageTolua_ClientGame_getCurGameMaxPlayerSetLimit00);
   tolua_function(tolua_S,"getRoomConnectMode",tolua_GameStageTolua_ClientGame_getRoomConnectMode00);
   tolua_function(tolua_S,"setHostPassword",tolua_GameStageTolua_ClientGame_setHostPassword00);
   tolua_function(tolua_S,"getHostPassword",tolua_GameStageTolua_ClientGame_getHostPassword00);
   tolua_function(tolua_S,"setPublicType",tolua_GameStageTolua_ClientGame_setPublicType00);
   tolua_function(tolua_S,"getPublicType",tolua_GameStageTolua_ClientGame_getPublicType00);
   tolua_function(tolua_S,"setCanTrace",tolua_GameStageTolua_ClientGame_setCanTrace00);
   tolua_function(tolua_S,"getCanTrace",tolua_GameStageTolua_ClientGame_getCanTrace00);
   tolua_function(tolua_S,"getTeamResults",tolua_GameStageTolua_ClientGame_getTeamResults00);
   tolua_function(tolua_S,"getNumTeam",tolua_GameStageTolua_ClientGame_getNumTeam00);
   tolua_function(tolua_S,"setOperateUI",tolua_GameStageTolua_ClientGame_setOperateUI00);
   tolua_function(tolua_S,"getIMainPlayer",tolua_GameStageTolua_ClientGame_getIMainPlayer00);
   tolua_function(tolua_S,"getMainPlayer",tolua_GameStageTolua_ClientGame_getMainPlayer00);
   tolua_function(tolua_S,"getDebugInfo",tolua_GameStageTolua_ClientGame_getDebugInfo00);
   tolua_function(tolua_S,"load",tolua_GameStageTolua_ClientGame_load00);
   tolua_function(tolua_S,"unload",tolua_GameStageTolua_ClientGame_unload00);
   tolua_function(tolua_S,"updateLoad",tolua_GameStageTolua_ClientGame_updateLoad00);
   tolua_function(tolua_S,"reloadScene",tolua_GameStageTolua_ClientGame_reloadScene00);
   tolua_function(tolua_S,"prepareTick",tolua_GameStageTolua_ClientGame_prepareTick00);
   tolua_function(tolua_S,"tick",tolua_GameStageTolua_ClientGame_tick00);
   tolua_function(tolua_S,"update",tolua_GameStageTolua_ClientGame_update00);
   tolua_function(tolua_S,"OnInputEvent",tolua_GameStageTolua_ClientGame_OnInputEvent00);
   tolua_function(tolua_S,"renderUI",tolua_GameStageTolua_ClientGame_renderUI00);
   tolua_function(tolua_S,"renderUIEffect",tolua_GameStageTolua_ClientGame_renderUIEffect00);
   tolua_function(tolua_S,"beginGame",tolua_GameStageTolua_ClientGame_beginGame00);
   tolua_function(tolua_S,"endGame",tolua_GameStageTolua_ClientGame_endGame00);
   tolua_function(tolua_S,"pauseGame",tolua_GameStageTolua_ClientGame_pauseGame00);
   tolua_function(tolua_S,"keepGame",tolua_GameStageTolua_ClientGame_keepGame00);
   tolua_function(tolua_S,"applyGameSetData",tolua_GameStageTolua_ClientGame_applyGameSetData00);
   tolua_function(tolua_S,"applayGameSetData",tolua_GameStageTolua_ClientGame_applayGameSetData00);
   tolua_function(tolua_S,"applyScreenBright",tolua_GameStageTolua_ClientGame_applyScreenBright00);
   tolua_function(tolua_S,"getNumPlayers",tolua_GameStageTolua_ClientGame_getNumPlayers00);
   tolua_function(tolua_S,"kickoff",tolua_GameStageTolua_ClientGame_kickoff00);
   tolua_function(tolua_S,"kickoffWithErrorCode",tolua_GameStageTolua_ClientGame_kickoffWithErrorCode00);
   tolua_function(tolua_S,"setPlayerVisibleDispayName",tolua_GameStageTolua_ClientGame_setPlayerVisibleDispayName00);
   tolua_function(tolua_S,"isOperateUI",tolua_GameStageTolua_ClientGame_isOperateUI00);
   tolua_function(tolua_S,"getMaxFPS",tolua_GameStageTolua_ClientGame_getMaxFPS00);
   tolua_function(tolua_S,"canAcceptClientJoin",tolua_GameStageTolua_ClientGame_canAcceptClientJoin00);
   tolua_function(tolua_S,"canAcceptClientJoin",tolua_GameStageTolua_ClientGame_canAcceptClientJoin01);
   tolua_function(tolua_S,"getHasBegan",tolua_GameStageTolua_ClientGame_getHasBegan00);
   tolua_function(tolua_S,"setHasBegan",tolua_GameStageTolua_ClientGame_setHasBegan00);
   tolua_function(tolua_S,"setGameLeaderUin",tolua_GameStageTolua_ClientGame_setGameLeaderUin00);
   tolua_function(tolua_S,"getGameLeaderUin",tolua_GameStageTolua_ClientGame_getGameLeaderUin00);
   tolua_function(tolua_S,"hostUpdateRoomInfoToServer",tolua_GameStageTolua_ClientGame_hostUpdateRoomInfoToServer00);
   tolua_function(tolua_S,"OnLoaded",tolua_GameStageTolua_ClientGame_OnLoaded00);
   tolua_function(tolua_S,"getLoadingStartTick",tolua_GameStageTolua_ClientGame_getLoadingStartTick00);
   tolua_variable(tolua_S,"m_hasBegan",tolua_get_ClientGame_m_hasBegan,tolua_set_ClientGame_m_hasBegan);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"SurviveGame","SurviveGame","ClientGame",NULL);
  tolua_beginmodule(tolua_S,"SurviveGame");
   tolua_function(tolua_S,"getName",tolua_GameStageTolua_SurviveGame_getName00);
   tolua_function(tolua_S,"getTypeName",tolua_GameStageTolua_SurviveGame_getTypeName00);
   tolua_function(tolua_S,"getDebugInfo",tolua_GameStageTolua_SurviveGame_getDebugInfo00);
   tolua_function(tolua_S,"load",tolua_GameStageTolua_SurviveGame_load00);
   tolua_function(tolua_S,"unload",tolua_GameStageTolua_SurviveGame_unload00);
   tolua_function(tolua_S,"getGameStage",tolua_GameStageTolua_SurviveGame_getGameStage00);
   tolua_function(tolua_S,"isInGame",tolua_GameStageTolua_SurviveGame_isInGame00);
   tolua_function(tolua_S,"prepareTick",tolua_GameStageTolua_SurviveGame_prepareTick00);
   tolua_function(tolua_S,"tick",tolua_GameStageTolua_SurviveGame_tick00);
   tolua_function(tolua_S,"update",tolua_GameStageTolua_SurviveGame_update00);
   tolua_function(tolua_S,"OnInputEvent",tolua_GameStageTolua_SurviveGame_OnInputEvent00);
   tolua_function(tolua_S,"renderUI",tolua_GameStageTolua_SurviveGame_renderUI00);
   tolua_function(tolua_S,"beginGame",tolua_GameStageTolua_SurviveGame_beginGame00);
   tolua_function(tolua_S,"endGame",tolua_GameStageTolua_SurviveGame_endGame00);
   tolua_function(tolua_S,"pauseGame",tolua_GameStageTolua_SurviveGame_pauseGame00);
   tolua_function(tolua_S,"getCurOpenContanierIndex",tolua_GameStageTolua_SurviveGame_getCurOpenContanierIndex00);
   tolua_function(tolua_S,"setOperateUI",tolua_GameStageTolua_SurviveGame_setOperateUI00);
   tolua_function(tolua_S,"isOperateUI",tolua_GameStageTolua_SurviveGame_isOperateUI00);
   tolua_function(tolua_S,"getOperateUICount",tolua_GameStageTolua_SurviveGame_getOperateUICount00);
   tolua_function(tolua_S,"getOperateUIState",tolua_GameStageTolua_SurviveGame_getOperateUIState00);
   tolua_function(tolua_S,"setServerRoomId",tolua_GameStageTolua_SurviveGame_setServerRoomId00);
   tolua_function(tolua_S,"getServerRoomId",tolua_GameStageTolua_SurviveGame_getServerRoomId00);
   tolua_function(tolua_S,"ResetOperateState",tolua_GameStageTolua_SurviveGame_ResetOperateState00);
   tolua_function(tolua_S,"sendChat",tolua_GameStageTolua_SurviveGame_sendChat00);
   tolua_function(tolua_S,"sendChatToSelf",tolua_GameStageTolua_SurviveGame_sendChatToSelf00);
   tolua_function(tolua_S,"SetSigns",tolua_GameStageTolua_SurviveGame_SetSigns00);
   tolua_function(tolua_S,"summonAccountHorse",tolua_GameStageTolua_SurviveGame_summonAccountHorse00);
   tolua_function(tolua_S,"getMainPlayer",tolua_GameStageTolua_SurviveGame_getMainPlayer00);
   tolua_function(tolua_S,"enableMinimap",tolua_GameStageTolua_SurviveGame_enableMinimap00);
   tolua_function(tolua_S,"getGameTimeHour",tolua_GameStageTolua_SurviveGame_getGameTimeHour00);
   tolua_function(tolua_S,"getGameTimeMinute",tolua_GameStageTolua_SurviveGame_getGameTimeMinute00);
   tolua_function(tolua_S,"playEffect",tolua_GameStageTolua_SurviveGame_playEffect00);
   tolua_function(tolua_S,"playEffectVisibleBlock",tolua_GameStageTolua_SurviveGame_playEffectVisibleBlock00);
   tolua_function(tolua_S,"playEffectInRoleFront",tolua_GameStageTolua_SurviveGame_playEffectInRoleFront00);
   tolua_function(tolua_S,"stopEffect",tolua_GameStageTolua_SurviveGame_stopEffect00);
   tolua_function(tolua_S,"stopEffect",tolua_GameStageTolua_SurviveGame_stopEffect01);
   tolua_function(tolua_S,"addmob",tolua_GameStageTolua_SurviveGame_addmob00);
   tolua_function(tolua_S,"getNumPlayerBriefInfo",tolua_GameStageTolua_SurviveGame_getNumPlayerBriefInfo00);
   tolua_function(tolua_S,"getPlayerBriefInfo",tolua_GameStageTolua_SurviveGame_getPlayerBriefInfo00);
   tolua_function(tolua_S,"findPlayerInfoByUin",tolua_GameStageTolua_SurviveGame_findPlayerInfoByUin00);
   tolua_function(tolua_S,"setTeamScore",tolua_GameStageTolua_SurviveGame_setTeamScore00);
   tolua_function(tolua_S,"getTeamScore",tolua_GameStageTolua_SurviveGame_getTeamScore00);
   tolua_function(tolua_S,"setTeamResults",tolua_GameStageTolua_SurviveGame_setTeamResults00);
   tolua_function(tolua_S,"getTeamResults",tolua_GameStageTolua_SurviveGame_getTeamResults00);
   tolua_function(tolua_S,"getNumTeam",tolua_GameStageTolua_SurviveGame_getNumTeam00);
   tolua_function(tolua_S,"getRandomPlayer",tolua_GameStageTolua_SurviveGame_getRandomPlayer00);
   tolua_function(tolua_S,"getNumPlayers",tolua_GameStageTolua_SurviveGame_getNumPlayers00);
   tolua_function(tolua_S,"setPlayerVisibleDispayName",tolua_GameStageTolua_SurviveGame_setPlayerVisibleDispayName00);
   tolua_function(tolua_S,"setPlayersResults",tolua_GameStageTolua_SurviveGame_setPlayersResults00);
   tolua_function(tolua_S,"requireArrayOfPlayers",tolua_GameStageTolua_SurviveGame_requireArrayOfPlayers00);
   tolua_function(tolua_S,"getIthPlayerInArray",tolua_GameStageTolua_SurviveGame_getIthPlayerInArray00);
   tolua_function(tolua_S,"getPlayerByUin",tolua_GameStageTolua_SurviveGame_getPlayerByUin00);
   tolua_function(tolua_S,"changePlayerTeam",tolua_GameStageTolua_SurviveGame_changePlayerTeam00);
   tolua_function(tolua_S,"RentChangePlayerTeam",tolua_GameStageTolua_SurviveGame_RentChangePlayerTeam00);
   tolua_function(tolua_S,"hostStartGame",tolua_GameStageTolua_SurviveGame_hostStartGame00);
   tolua_function(tolua_S,"resetGameRuleData",tolua_GameStageTolua_SurviveGame_resetGameRuleData00);
   tolua_function(tolua_S,"getRuleOptionVal",tolua_GameStageTolua_SurviveGame_getRuleOptionVal00);
   tolua_function(tolua_S,"getPersonalRentLeftSeconds",tolua_GameStageTolua_SurviveGame_getPersonalRentLeftSeconds00);
   tolua_function(tolua_S,"isLockViewMode",tolua_GameStageTolua_SurviveGame_isLockViewMode00);
   tolua_function(tolua_S,"showOperateUI",tolua_GameStageTolua_SurviveGame_showOperateUI00);
   tolua_function(tolua_S,"HideTouchControlUi",tolua_GameStageTolua_SurviveGame_HideTouchControlUi00);
   tolua_function(tolua_S,"isHost",tolua_GameStageTolua_SurviveGame_isHost00);
   tolua_function(tolua_S,"getHostUin",tolua_GameStageTolua_SurviveGame_getHostUin00);
   tolua_function(tolua_S,"InviteJoinRoom",tolua_GameStageTolua_SurviveGame_InviteJoinRoom00);
   tolua_function(tolua_S,"world2RadarPos",tolua_GameStageTolua_SurviveGame_world2RadarPos00);
   tolua_function(tolua_S,"world2RadarDist",tolua_GameStageTolua_SurviveGame_world2RadarDist00);
   tolua_function(tolua_S,"setCameraDepth",tolua_GameStageTolua_SurviveGame_setCameraDepth00);
   tolua_function(tolua_S,"getHaveJudge",tolua_GameStageTolua_SurviveGame_getHaveJudge00);
   tolua_function(tolua_S,"getJudgeUin",tolua_GameStageTolua_SurviveGame_getJudgeUin00);
   tolua_function(tolua_S,"initNewShortcut",tolua_GameStageTolua_SurviveGame_initNewShortcut00);
   tolua_function(tolua_S,"transferToTargetMap",tolua_GameStageTolua_SurviveGame_transferToTargetMap00);
   tolua_function(tolua_S,"getGameLeaderUin",tolua_GameStageTolua_SurviveGame_getGameLeaderUin00);
   tolua_function(tolua_S,"requireArrayOfAllPlayers",tolua_GameStageTolua_SurviveGame_requireArrayOfAllPlayers00);
   tolua_function(tolua_S,"initRoleInfo",tolua_GameStageTolua_SurviveGame_initRoleInfo00);
   tolua_function(tolua_S,"findNearestPlayerByPos",tolua_GameStageTolua_SurviveGame_findNearestPlayerByPos00);
   tolua_variable(tolua_S,"m_Effects",tolua_get_SurviveGame_m_Effects,tolua_set_SurviveGame_m_Effects);
   tolua_variable(tolua_S,"m_OpenCmd",tolua_get_SurviveGame_m_OpenCmd,tolua_set_SurviveGame_m_OpenCmd);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"MainMenuStage","MainMenuStage","ClientGame",tolua_collect_MainMenuStage);
  #else
  tolua_cclass(tolua_S,"MainMenuStage","MainMenuStage","ClientGame",NULL);
  #endif
  tolua_beginmodule(tolua_S,"MainMenuStage");
   tolua_function(tolua_S,"new",tolua_GameStageTolua_MainMenuStage_new00);
   tolua_function(tolua_S,"new_local",tolua_GameStageTolua_MainMenuStage_new00_local);
   tolua_function(tolua_S,".call",tolua_GameStageTolua_MainMenuStage_new00_local);
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_MainMenuStage_delete00);
   tolua_function(tolua_S,"createClickPos",tolua_GameStageTolua_MainMenuStage_createClickPos00);
   tolua_function(tolua_S,"loadBGWorld",tolua_GameStageTolua_MainMenuStage_loadBGWorld00);
   tolua_function(tolua_S,"unloadBGWorld",tolua_GameStageTolua_MainMenuStage_unloadBGWorld00);
   tolua_function(tolua_S,"getSelectRoleIndex",tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex00);
   tolua_function(tolua_S,"setClickEffectEnabled",tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled00);
   tolua_function(tolua_S,"getLoadStepCounter",tolua_GameStageTolua_MainMenuStage_getLoadStepCounter00);
   tolua_function(tolua_S,"new",tolua_GameStageTolua_MainMenuStage_new01);
   tolua_function(tolua_S,"new_local",tolua_GameStageTolua_MainMenuStage_new01_local);
   tolua_function(tolua_S,".call",tolua_GameStageTolua_MainMenuStage_new01_local);
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_MainMenuStage_delete01);
   tolua_function(tolua_S,"createClickPos",tolua_GameStageTolua_MainMenuStage_createClickPos01);
   tolua_function(tolua_S,"loadBGWorld",tolua_GameStageTolua_MainMenuStage_loadBGWorld01);
   tolua_function(tolua_S,"unloadBGWorld",tolua_GameStageTolua_MainMenuStage_unloadBGWorld01);
   tolua_function(tolua_S,"getSelectRoleIndex",tolua_GameStageTolua_MainMenuStage_getSelectRoleIndex01);
   tolua_function(tolua_S,"setClickEffectEnabled",tolua_GameStageTolua_MainMenuStage_setClickEffectEnabled01);
   tolua_function(tolua_S,"getLoadStepCounter",tolua_GameStageTolua_MainMenuStage_getLoadStepCounter01);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"SimpleLoadingStage","SimpleLoadingStage","ClientGame",tolua_collect_SimpleLoadingStage);
  #else
  tolua_cclass(tolua_S,"SimpleLoadingStage","SimpleLoadingStage","ClientGame",NULL);
  #endif
  tolua_beginmodule(tolua_S,"SimpleLoadingStage");
   tolua_function(tolua_S,"new",tolua_GameStageTolua_SimpleLoadingStage_new00);
   tolua_function(tolua_S,"new_local",tolua_GameStageTolua_SimpleLoadingStage_new00_local);
   tolua_function(tolua_S,".call",tolua_GameStageTolua_SimpleLoadingStage_new00_local);
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_SimpleLoadingStage_delete00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"MpGameSurvive","MpGameSurvive","SurviveGame",NULL);
  tolua_beginmodule(tolua_S,"MpGameSurvive");
   tolua_function(tolua_S,"sendChat",tolua_GameStageTolua_MpGameSurvive_sendChat00);
   tolua_function(tolua_S,"getName",tolua_GameStageTolua_MpGameSurvive_getName00);
   tolua_function(tolua_S,"getTypeName",tolua_GameStageTolua_MpGameSurvive_getTypeName00);
   tolua_function(tolua_S,"getDebugInfo",tolua_GameStageTolua_MpGameSurvive_getDebugInfo00);
   tolua_function(tolua_S,"load",tolua_GameStageTolua_MpGameSurvive_load00);
   tolua_function(tolua_S,"unload",tolua_GameStageTolua_MpGameSurvive_unload00);
   tolua_function(tolua_S,"mpGameUnload",tolua_GameStageTolua_MpGameSurvive_mpGameUnload00);
   tolua_function(tolua_S,"endGame",tolua_GameStageTolua_MpGameSurvive_endGame00);
   tolua_function(tolua_S,"findPlayerInfoByUin",tolua_GameStageTolua_MpGameSurvive_findPlayerInfoByUin00);
   tolua_function(tolua_S,"addPlayerBriefInfo",tolua_GameStageTolua_MpGameSurvive_addPlayerBriefInfo00);
   tolua_function(tolua_S,"getNumPlayerBriefInfo",tolua_GameStageTolua_MpGameSurvive_getNumPlayerBriefInfo00);
   tolua_function(tolua_S,"isHost",tolua_GameStageTolua_MpGameSurvive_isHost00);
   tolua_function(tolua_S,"getHostUin",tolua_GameStageTolua_MpGameSurvive_getHostUin00);
   tolua_function(tolua_S,"canAcceptClientJoin",tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin00);
   tolua_function(tolua_S,"canAcceptClientJoin",tolua_GameStageTolua_MpGameSurvive_canAcceptClientJoin01);
   tolua_function(tolua_S,"getNetDelayTick",tolua_GameStageTolua_MpGameSurvive_getNetDelayTick00);
   tolua_function(tolua_S,"setGameLeaderUin",tolua_GameStageTolua_MpGameSurvive_setGameLeaderUin00);
   tolua_function(tolua_S,"getGameLeaderUin",tolua_GameStageTolua_MpGameSurvive_getGameLeaderUin00);
   tolua_function(tolua_S,"broadCorrectNickName",tolua_GameStageTolua_MpGameSurvive_broadCorrectNickName00);
   tolua_variable(tolua_S,"__ReachabilityObserver__",tolua_get_MpGameSurvive___ReachabilityObserver__,NULL);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"GameSurviveRecord","GameSurviveRecord","MpGameSurvive",tolua_collect_GameSurviveRecord);
  #else
  tolua_cclass(tolua_S,"GameSurviveRecord","GameSurviveRecord","MpGameSurvive",NULL);
  #endif
  tolua_beginmodule(tolua_S,"GameSurviveRecord");
   tolua_function(tolua_S,"new",tolua_GameStageTolua_GameSurviveRecord_new00);
   tolua_function(tolua_S,"new_local",tolua_GameStageTolua_GameSurviveRecord_new00_local);
   tolua_function(tolua_S,".call",tolua_GameStageTolua_GameSurviveRecord_new00_local);
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_GameSurviveRecord_delete00);
   tolua_function(tolua_S,"getName",tolua_GameStageTolua_GameSurviveRecord_getName00);
   tolua_function(tolua_S,"getTypeName",tolua_GameStageTolua_GameSurviveRecord_getTypeName00);
   tolua_function(tolua_S,"load",tolua_GameStageTolua_GameSurviveRecord_load00);
   tolua_function(tolua_S,"unload",tolua_GameStageTolua_GameSurviveRecord_unload00);
   tolua_function(tolua_S,"tick",tolua_GameStageTolua_GameSurviveRecord_tick00);
   tolua_function(tolua_S,"update",tolua_GameStageTolua_GameSurviveRecord_update00);
   tolua_function(tolua_S,"sendChat",tolua_GameStageTolua_GameSurviveRecord_sendChat00);
   tolua_function(tolua_S,"executeRecord",tolua_GameStageTolua_GameSurviveRecord_executeRecord00);
   tolua_function(tolua_S,"beginGame",tolua_GameStageTolua_GameSurviveRecord_beginGame00);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"ClientGameManager","ClientGameManager","IClientGameManagerInterface",tolua_collect_ClientGameManager);
  #else
  tolua_cclass(tolua_S,"ClientGameManager","ClientGameManager","IClientGameManagerInterface",NULL);
  #endif
  tolua_beginmodule(tolua_S,"ClientGameManager");
   tolua_function(tolua_S,"delete",tolua_GameStageTolua_ClientGameManager_delete00);
   tolua_function(tolua_S,"createNetHandlerRegister",tolua_GameStageTolua_ClientGameManager_createNetHandlerRegister00);
   tolua_function(tolua_S,"releaseGameData",tolua_GameStageTolua_ClientGameManager_releaseGameData00);
   tolua_function(tolua_S,"applayGameSetData",tolua_GameStageTolua_ClientGameManager_applayGameSetData00);
   tolua_function(tolua_S,"applyScreenBright",tolua_GameStageTolua_ClientGameManager_applyScreenBright00);
   tolua_function(tolua_S,"applyGameSetData",tolua_GameStageTolua_ClientGameManager_applyGameSetData00);
   tolua_function(tolua_S,"gotoGame",tolua_GameStageTolua_ClientGameManager_gotoGame00);
   tolua_function(tolua_S,"isInGame",tolua_GameStageTolua_ClientGameManager_isInGame00);
   tolua_function(tolua_S,"addGame",tolua_GameStageTolua_ClientGameManager_addGame00);
   tolua_function(tolua_S,"getCurGame",tolua_GameStageTolua_ClientGameManager_getCurGame00);
   tolua_function(tolua_S,"setCurGame",tolua_GameStageTolua_ClientGameManager_setCurGame00);
   tolua_function(tolua_S,"getLoadingGame",tolua_GameStageTolua_ClientGameManager_getLoadingGame00);
   tolua_function(tolua_S,"setLoadingGame",tolua_GameStageTolua_ClientGameManager_setLoadingGame00);
   tolua_function(tolua_S,"getGame",tolua_GameStageTolua_ClientGameManager_getGame00);
   tolua_function(tolua_S,"getMPGame",tolua_GameStageTolua_ClientGameManager_getMPGame00);
   tolua_function(tolua_S,"updateLoadingGame",tolua_GameStageTolua_ClientGameManager_updateLoadingGame00);
   tolua_function(tolua_S,"clearCurGame",tolua_GameStageTolua_ClientGameManager_clearCurGame00);
   tolua_function(tolua_S,"removeGame",tolua_GameStageTolua_ClientGameManager_removeGame00);
   tolua_function(tolua_S,"getReloadGameType",tolua_GameStageTolua_ClientGameManager_getReloadGameType00);
   tolua_function(tolua_S,"onStop",tolua_GameStageTolua_ClientGameManager_onStop00);
   tolua_function(tolua_S,"RegisterInputHandler",tolua_GameStageTolua_ClientGameManager_RegisterInputHandler00);
   tolua_function(tolua_S,"CheckWindowLostFocus",tolua_GameStageTolua_ClientGameManager_CheckWindowLostFocus00);
   tolua_function(tolua_S,"isJudgeOrSpectator",tolua_GameStageTolua_ClientGameManager_isJudgeOrSpectator00);
   tolua_function(tolua_S,"disableChkRoomTicks",tolua_GameStageTolua_ClientGameManager_disableChkRoomTicks00);
   tolua_function(tolua_S,"chkRoomTicks",tolua_GameStageTolua_ClientGameManager_chkRoomTicks00);
   tolua_function(tolua_S,"mapSceneLoadFinish",tolua_GameStageTolua_ClientGameManager_mapSceneLoadFinish00);
   tolua_function(tolua_S,"setMapSceneLoadTips",tolua_GameStageTolua_ClientGameManager_setMapSceneLoadTips00);
   tolua_function(tolua_S,"setMapSceneLoadProgress",tolua_GameStageTolua_ClientGameManager_setMapSceneLoadProgress00);
  tolua_endmodule(tolua_S);
  tolua_function(tolua_S,"GetClientGameManager",tolua_GameStageTolua_GetClientGameManager00);
  tolua_function(tolua_S,"GetClientGameManagerPtr",tolua_GameStageTolua_GetClientGameManagerPtr00);
  tolua_cclass(tolua_S,"UIStageInputHandler","UIStageInputHandler","Rainbow::InputInterface",NULL);
  tolua_beginmodule(tolua_S,"UIStageInputHandler");
   tolua_function(tolua_S,"OnInputEvent",tolua_GameStageTolua_UIStageInputHandler_OnInputEvent00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"UIStageSandboxObject","UIStageSandboxObject","MNSandbox::Object",NULL);
  tolua_beginmodule(tolua_S,"UIStageSandboxObject");
   tolua_function(tolua_S,"EmitCustomEvent",tolua_GameStageTolua_UIStageSandboxObject_EmitCustomEvent00);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_GameStageTolua (lua_State* tolua_S) {
 return tolua_GameStageTolua_open(tolua_S);
};
#endif

