/**
* file : SandboxTimerManager
* func : 沙盒定时器管理器
* by : chenzh
*/
#include "SandboxTimerManager.h"
#include "SandboxTimer.h"
#include "SandboxCoreDriver.h"
#include "SandboxCoreManagers.h"
#include "event/notify/SandboxGlobalNotify.h"


namespace MNSandbox {


	TimerManager::TimerManager()
		: m_listenFpsChanged(this, &TimerManager::OnFpsChanged)
		, m_listenSysTick(this, &TimerManager::OnTick)
	{
		GlobalNotify::GetInstance().m_SysTick.Subscribe(m_listenSysTick);
	}

	TimerManager::~TimerManager()
	{
		Release();
	}

	void TimerManager::Release()
	{
		for (int i = 0; i < ms_pieceCnt; ++i)
		{
			auto& cell = m_allPiece[i];
			for (auto& v : cell)
			{
				v._timer->Clear();
			}
			m_allPiece[i].clear();
		}
	}

	void TimerManager::OnTick(int times)
	{
		OPTICK_EVENT();
		for (int i = 0; i < times; ++i)
		{
			// 执行下一片
			DoOnePiece();
		}
	}

	void TimerManager::DoOnePiece()
	{
		++m_pieceIndex;
		if (m_pieceIndex >= ms_pieceCnt)
			m_pieceIndex = 0;

		// 执行片
		auto& cell = m_allPiece[m_pieceIndex];
		if (cell.empty())
			return;

		std::vector<AutoRef<MNTimer>> activeTimers;

		auto iter = cell.begin();
		while (iter != cell.end())
		{
			if (iter->_waitTimes > 0) // 需要等待几轮
			{
				--(iter->_waitTimes);
				++iter;
				continue;
			}

			activeTimers.push_back(iter->_timer);
			iter->_timer->ClearMgrPieceIdx(); // 执行了就需要移除
			iter = cell.erase(iter); // 移除旧的
		}

		if (!activeTimers.empty())
		{
			for (auto& timer : activeTimers)
			{
				// 执行
				timer->Emit();

				// 循环执行？
				if (timer->IsPlaying())
				{
					if (timer->m_loop)
						RegisterTimer(timer, timer->m_interval);
					else
						timer->Clear();
				}
			}
		}
	}

	bool TimerManager::RegisterTimer(MNTimer* timer, double delay)
	{
		if (delay < 0.0f)
		{
			SANDBOX_ASSERT(false && "timer delay is < 0! need > 0!");
			return false;
		}

		delay = Rainbow::Max(delay, g_TickElapse); // 默认最少下一个tick 执行，即便延迟时间是0
		return RegisterTimerDelayReal(timer, delay);
	}

	bool TimerManager::UnregisterTimer(MNTimer* timer)
	{
		int pieceIdx = timer->GetMgrPieceIdx();
		if (pieceIdx < 0 || pieceIdx >= ms_pieceCnt)
			return false;

		auto& cell = m_allPiece[pieceIdx];
		for (size_t i=0; i<cell.size(); ++i)
		{
			if (cell[i]._timer == timer)
			{
				cell[i]._timer->ClearMgrPieceIdx();
				cell[i] = cell[cell.size() - 1];//交换到最后,避免移动元素 
				cell.pop_back();//移除 
				return true;
			}
		}

		return false;
	}

	bool TimerManager::RegisterTimerDelayReal(MNTimer* timer, double delay)
	{
		// 计算片索引
		unsigned timerPiece = unsigned((delay + g_TickElapse * 0.99) / g_TickElapse);
		unsigned pieceRow = timerPiece / ms_pieceCnt;
		unsigned pieceIdx = (timerPiece + m_pieceIndex) % ms_pieceCnt; // 需要加上当前的位置偏移

		m_allPiece[pieceIdx].push_back(PieceData{ timer, pieceRow }); // 新增
		timer->SetMgrPieceIdx((int)pieceIdx, this); // 记录在索引，用于移除等
		return true;
	}

	double TimerManager::CalcLeftTime(MNTimer* timer)
	{
		int pieceIdx = timer->GetMgrPieceIdx();
		if (pieceIdx < 0 || pieceIdx >= ms_pieceCnt)
			return 0.0;

		unsigned pieces = pieceIdx - m_pieceIndex;
		pieces = pieces > 0 ? pieces : pieces + ms_pieceCnt; // 如果等于0，也是要过一轮才能被执行

		bool found = false;
		auto& cell = m_allPiece[pieceIdx];
		for (auto iter = cell.begin(); iter != cell.end(); ++iter)
		{
			if (iter->_timer == timer)
			{
				pieces += iter->_waitTimes * ms_pieceCnt;
				found = true;
				break;
			}
		}
		return found ? (pieces * g_TickElapse) : 0.0; // * g_TickElapse 换算成秒
	}

	void TimerManager::OnFpsChanged(unsigned srcfps, unsigned dstfps)
	{
		if (srcfps == 0 || dstfps == 0)
		{
			SANDBOX_ASSERT(false);
			return;
		}

		// 缓存
		std::vector<PieceData> srcPieces[ms_pieceCnt];
		for (unsigned i = 0; i < ms_pieceCnt; ++i)
		{
			m_allPiece[i].swap(srcPieces[i]);
		}

		// 计算剩余延时，重新插入缓存
		const double tickElapseSrc = 1.0 / (double)srcfps;
		unsigned timerPieceSrc = 0;
		double delay = 0.0;

		for (unsigned i = 0; i < ms_pieceCnt; ++i)
		{
			for (auto& cell : srcPieces[i])
			{
				cell._timer->ClearMgrPieceIdx(); // 先清理索引
				timerPieceSrc = i - m_pieceIndex;
				if (timerPieceSrc < 0) timerPieceSrc += ms_pieceCnt; // 可能是下一轮才到
				timerPieceSrc += cell._waitTimes * ms_pieceCnt;
				delay = timerPieceSrc * tickElapseSrc;
				if (cell._timer->IsPlaying())
					RegisterTimer(cell._timer, delay);
			}
		}

	}

	/////////////////////////////////////////////////////////////////////

	TimerManager& GetCurrentTimerMgr()
	{
		// 地图内
		auto gamemap = GetCurrentGameMap();
		if (gamemap)
			return gamemap->GetTimerMgr();

		// 全局
		return SandboxCoreDriver::GetInstance().GetManagers().GetTimerMgr();
	}

}