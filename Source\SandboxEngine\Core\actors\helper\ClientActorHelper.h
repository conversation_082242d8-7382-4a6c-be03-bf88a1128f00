
#ifndef __CLIENT_ACTOR_HELPER_H__
#define __CLIENT_ACTOR_HELPER_H__

#include "OgreWCoord.h"
#include "json/jsonxx.h"
#include "ChunkSave_generated.h"
#include "CorePrerequisites.h"
//#include "OgreQuaternion.h"
#include "IClientActor.h"
#include "SandboxEngine.h"

namespace game
{
	namespace common
	{
		class PB_Vector3;
	}
}
class EXPORT_SANDBOXENGINE ClientActorHelper;
class ClientActorHelper
{
public:
	ClientActorHelper();
	~ClientActorHelper();
	static bool isTriggerProjectileType(int actorType); // �Ƿ���Ͷ��������
	static bool isTriggerCreatureType(int actorType); // �Ƿ�����������
};


inline jsonxx::Array WCoordToCoord3Json(const WCoord &c)
{
	return jsonxx::Array() << jsonxx::Number(c.x) << jsonxx::Number(c.y) << jsonxx::Number(c.z);
}

inline WCoord Coord3ToWCoordJson(const jsonxx::Array &c)
{
	return WCoord((int)c.get<jsonxx::Number>(0), (int)c.get<jsonxx::Number>(1), (int)c.get<jsonxx::Number>(2));
}

inline jsonxx::Array Vector3ToVec3Json(const Rainbow::Vector3f &c)
{
	return jsonxx::Array() << jsonxx::Number(c.x) << jsonxx::Number(c.y) << jsonxx::Number(c.z);
}

inline Rainbow::Vector3f Vec3ToVector3Json(const jsonxx::Array &c)
{
	return Rainbow::Vector3f((int)c.get<jsonxx::Number>(0), (int)c.get<jsonxx::Number>(1), (int)c.get<jsonxx::Number>(2));
}

inline FBSave::Coord3 WCoordToCoord3(const WCoord &c)
{
	return FBSave::Coord3(c.x, c.y, c.z);
}

inline WCoord Coord3ToWCoord(const FBSave::Coord3 *c)
{
	if (c)
		return WCoord(c->x(), c->y(), c->z());
	else
		return WCoord::zero;
}

inline FBSave::Vec3 Vector3ToVec3(const Rainbow::Vector3f &c)
{
	return FBSave::Vec3(c.x, c.y, c.z);
}

inline Rainbow::Vector3f Vec3ToVector3(const FBSave::Vec3 *c)
{
	if (c)
		return Rainbow::Vector3f(c->x(), c->y(), c->z());
	else
		return Rainbow::Vector3f::zero;
}

inline FBSave::Vec2 Vector2ToVec2(const Rainbow::Vector2f &c)
{
	return FBSave::Vec2(c.x, c.y);
}

inline Rainbow::Vector2f Vec2ToVector2(const FBSave::Vec2 *c)
{
	if (c)
		return Rainbow::Vector2f(c->x(), c->y());
	else
		return Rainbow::Vector2f::zero;
}

inline FBSave::Mat4x4f Matrix4x4fToFbsMat4x4f(const Rainbow::Matrix4x4f &m)
{
	return FBSave::Mat4x4f(
		m.m_Data44[0][0], m.m_Data44[0][1], m.m_Data44[0][2], m.m_Data44[0][3],
		m.m_Data44[1][0], m.m_Data44[1][1], m.m_Data44[1][2], m.m_Data44[1][3],
		m.m_Data44[2][0], m.m_Data44[2][1], m.m_Data44[2][2], m.m_Data44[2][3],
		m.m_Data44[3][0], m.m_Data44[3][1], m.m_Data44[3][2], m.m_Data44[3][3]
	);
}

inline Rainbow::Matrix4x4f FbsMat4x4ToMatrix4x4f(const FBSave::Mat4x4f *m)
{
	if (m)
		return Rainbow::Matrix4x4f(
			m->_11(), m->_12(), m->_13(), m->_14(),
			m->_21(), m->_22(), m->_23(), m->_24(),
			m->_31(), m->_32(), m->_33(), m->_34(),
			m->_41(), m->_42(), m->_43(), m->_44()
		);
	else
		return Rainbow::Matrix4x4f::identity;
}

Rainbow::Vector3f PbVec3ToVector3(const game::common::PB_Vector3& pbv3);

inline FBSave::Quat4 QuaternionToSave(const Rainbow::Quaternionf &q)
{
	return FBSave::Quat4(q.x, q.y, q.z, q.w);
}

inline Rainbow::Quaternionf QuaternionFromSave(const FBSave::Quat4 *q)
{
	if (q)
		return Rainbow::Quaternionf(q->x(), q->y(), q->z(), q->w());
	else
		return Rainbow::Quaternionf::identity;
}

#endif
