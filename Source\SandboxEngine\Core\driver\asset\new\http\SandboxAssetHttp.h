#pragma once

#include "SandboxAssetBaseHttp.h"

namespace MNSandbox {
	class AssetHttp :public AssetBaseHttp 
	{
		DECLARE_REF_NEW_INSTANCE(AssetHttp)
	public:
		AssetHttp();
		AssetHttp(HttpReq* ptr);
		virtual ~AssetHttp();
		virtual void RegisterByReqData(HttpReq* ptr)override;
	private:
		void BackpackGetResByIdsRequst(AutoRef<HttpReq> reqData);
		void BackpackGetResByUinRequst(AutoRef<HttpReq> reqData);
		void GetDownloadUrlByIdsRequst(AutoRef<HttpReq> reqData);
		
	private:

	};
}