//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

// Class for polygon filtering and cost calculation during query operations.
// - It is possible to derive a custom query filter from QueryFilter by overriding
//   the virtual functions PassFilter() and GetCost().
// - Both functions should be as fast as possible. Use cached local copy of data
//   instead of accessing your own objects where possible.
// - You do not need to adhere to the flags and cost logic provided by the default
//   implementation.
// - In order for the A* to work properly, the cost should be proportional to
//   the travel distance. Using cost modifier less than 1.0 is likely to lead
//   to problems during pathfinding.

class QueryFilter
{
public:

    enum { kMaxAreas = 32 };

    QueryFilter()
        : m_IncludeFlags(0xffffffff)
        , m_TypeID(-1)
    {
        for (int i = 0; i < kMaxAreas; ++i)
            m_AreaCost[i] = 1.0f;
    }

    QueryFilter(int typeID, unsigned int includeFlags)
        : m_IncludeFlags(includeFlags)
        , m_TypeID(typeID)
    {
        for (int i = 0; i < kMaxAreas; ++i)
            m_AreaCost[i] = 1.0f;
    }

    inline bool PassFilter(unsigned int flags) const
    {
        return (flags & m_IncludeFlags) != 0;
    }

    inline void Set(int typeID, unsigned int includeFlags, const float costs[kMaxAreas])
    {
        m_IncludeFlags = includeFlags;
        if (costs)
            memcpy(m_AreaCost, costs, kMaxAreas * sizeof(float));
    }

    // Getters and setters for the default implementation data.
    inline float GetAreaCost(const int i) const { Assert(i < kMaxAreas); return m_AreaCost[i]; }
    inline void SetAreaCost(const int i, const float cost) { m_AreaCost[i] = cost; }

    inline unsigned int GetIncludeFlags() const { return m_IncludeFlags; }
    inline void SetIncludeFlags(const unsigned int flags) { m_IncludeFlags = flags; }
    
    inline void SetTypeID(const int typeID) { m_TypeID = typeID; }
    inline int GetTypeID() const { return m_TypeID; }
private:
    float m_AreaCost[kMaxAreas];        // Array storing cost per area type, used by default implementation.
    unsigned int m_IncludeFlags;        // Include poly flags, used by default implementation.
    //int m_TypeID;                       // Which type of navmesh is considered
    int m_TypeID;                       // Which type of navmesh is considered
};
