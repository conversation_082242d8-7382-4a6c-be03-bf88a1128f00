/**
* file : SandboxAttribute
* func : 属性
* by : chenzh
*/
#include "SandboxAttribute.h"
#include "SandboxLua.h"
#include "SandboxLuaUnit.h"
#include "SandboxReflexSerialize.h"
#include "Math/Vector2f.h"
#include "Math/Vector3f.h"
#include "Math/Vector4f.h"
#include "LegacyOgreColourValue.h"
#include "OgreRect.h"
#include "SandboxReflexTypePolicy.h"
#include "SandboxSceneObject.h"
#include "base/stream/SandboxStream.h"
#include "SandboxNodeLinker.h"


namespace MNSandbox {
	static std::vector<std::pair<Attribute::TYPE, ReflexType*>>s_indices;

	/** @enum
	 * @name  AttributeType
	 * @brief  属性类型
	 */
	ReflexEnumDesc<Attribute::TYPE> Attribute::AttributeEnum("AttributeType", (int)REFLEXTYPEENUM_ENUM_EATTRIBUTE, {
		{Attribute::TYPE::IDLE, "IDLE"},                      /**< 闲置 */
		{Attribute::TYPE::Number, "Number"},                  /**< 数值 */
		{Attribute::TYPE::Bool, "Bool"},                      /**< 布尔 */
		{Attribute::TYPE::String, "String"},                  /**< 字符串 */
		{Attribute::TYPE::Vector3, "Vector3"},                /**< Vector3 */
		{Attribute::TYPE::Vector2, "Vector2"},                /**< Vector2 */
		{Attribute::TYPE::Vector4, "Vector4"},                /**< Vector4 */
		{Attribute::TYPE::Color, "Color"},                    /**< Color */
		{Attribute::TYPE::Rect, "Rect"},                      /**< Rect */
		{Attribute::TYPE::NumberSequence, "NumberSequence"},  /**< 数字序列 */
		{Attribute::TYPE::ColorSequence, "ColorSequence"},    /**< 颜色序列 */
		{Attribute::TYPE::NodeLinker, "NodeLinker"}			  /**< 节点链接 */
	});

	void InitAttrTypeIndices()
	{
		if (s_indices.empty())
		{
			s_indices = {
				{Attribute::TYPE::Number, &ReflexType::GetSingleton<double>()},
				{Attribute::TYPE::Bool, &ReflexType::GetSingleton<bool>()},
				{Attribute::TYPE::String, &ReflexType::GetSingleton<std::string>()},
				{Attribute::TYPE::Vector3, &ReflexType::GetSingleton<Rainbow::Vector3f>()},
				{Attribute::TYPE::Vector2, &ReflexType::GetSingleton<Rainbow::Vector2f>()},
				{Attribute::TYPE::Vector4, &ReflexType::GetSingleton<Rainbow::Vector4f>()},
				{Attribute::TYPE::Color, &ReflexType::GetSingleton<Rainbow::ColorQuad>()},
				{Attribute::TYPE::Rect, &ReflexType::GetSingleton<MINIW::TRect<float>>()},
				{Attribute::TYPE::NumberSequence, &ReflexType::GetSingleton<std::vector<float>>()},
				{Attribute::TYPE::ColorSequence, &ReflexType::GetSingleton<std::vector<Rainbow::ColorQuad>>()},
				{Attribute::TYPE::NodeLinker, &ReflexType::GetSingleton<NodeLinker>()}
			};
		}
	}

	Attribute::TYPE Attribute::GetAttrTypeByRefexType(const ReflexType* reflexType)
	{
		InitAttrTypeIndices();
		for (const auto& v : s_indices)
		{
			if (v.second == reflexType)
			{
				return v.first;
			}
		}
		return Attribute::TYPE::IDLE;
	}

	Attribute::Attribute(AttributeContainer* container, const std::string& name, TYPE attrType)
		: m_link(container), m_name(name), m_attrType(attrType)
	{
		InitAttrTypeIndices();
		for (const auto& v : s_indices)
		{
			if (v.first == m_attrType)
			{
				m_value.SetReflexType(v.second);
				break;
			}
		}
		if (m_value.GetType() == ReflexType::Idle)
		{
			SANDBOX_ASSERT(false);
			return;
		}

		//switch (m_attrType)
		//{
		//case Attribute::TYPE::Number:
		//	m_value.SetType<double>();
		//	break;
		//case Attribute::TYPE::Bool:
		//	m_value.SetType<bool>();
		//	break;
		//case Attribute::TYPE::String:
		//	m_value.SetType<std::string>();
		//	break;
		//case Attribute::TYPE::Vector3:
		//	m_value.SetType<Rainbow::Vector3f>();
		//	break;
		//case Attribute::TYPE::Vector2:
		//	m_value.SetType<Rainbow::Vector2f>();
		//	break;
		//case Attribute::TYPE::Vector4:
		//	m_value.SetType<Rainbow::Vector4f>();
		//	break;
		//case Attribute::TYPE::Color:
		//	m_value.SetType<Rainbow::ColorQuad>();
		//	m_value.SetValue(Rainbow::ColorQuad(255,255,255,1));
		//	break;
		//case Attribute::TYPE::Rect:
		//	m_value.SetType<MINIW::TRect<float>>();
		//	break;
		//case Attribute::TYPE::NumberSequence:
		//	m_value.SetType<std::vector<float>>();
		//	break;
		//case Attribute::TYPE::ColorSequence:
		//	m_value.SetType<std::vector<Rainbow::ColorQuad>>();
		//	break;

		//default:
		//	SANDBOX_ASSERT(false);
		//	break;
		//}

		m_value.LockType(); // 锁定类型，不能改变类型
	}

	void Attribute::setName(const std::string& name)
	{
		m_name = name;
	}

	bool Attribute::SetVariant(const ReflexVariant& val)
	{
		if (!m_value.IsSameType(val))
			return false;

		if (m_value == val)
			return true;

		m_value = val;

		//通知
		//m_notifyChanged.Emit(m_name);
		if (m_link)
		{
			m_link->OnCustomAttrChanged(this);
		}
		return true;
	}

	///////////////////////////////////////////////////////////////////////////////

	bool AttributeContainer::ModifyAttrName(const std::string& oldName, const std::string& newName)
	{
		if (m_pool.find(oldName) == m_pool.end())
		{
			return false;
		}
		auto attr = GetAttribute(oldName);

		m_pool.erase(oldName);
		attr->setName(newName);
		m_pool.insert(make_pair(attr->GetName(), attr)); // 插入缓存
		attr->SyncContainer(this); // 同步容器
		return true;
	}

	AutoRef<Attribute> AttributeContainer::NewAttribute(const std::string& name, Attribute::TYPE type, const ReflexVariant& val)
	{
		if (m_pool.find(name) != m_pool.end())
		{
			// 已经存在
			//SANDBOX_ASSERT(false && "error! is same name!");
			return nullptr;
		}

		AutoRef<Attribute> attr = SANDBOX_NEW(Attribute, this, name, type);
		if (val.IsValid())
		{
			attr->SetVariant(val);
		}
		m_pool.insert(make_pair(name, attr));
		if (m_owner)
			m_owner->OnCustomAttrAdd(attr.get());
		//m_notifyAttrAdd.Emit(name);
		return attr;
	}

	void AttributeContainer::DeleteAttribute(const std::string& name)
	{
		auto iter = m_pool.find(name);
		if (iter != m_pool.end())
		{
			if (m_owner)
				m_owner->OnCustomAttrRemove(iter->second.get());
			//m_notifyAttrRemove.Emit(name);
			iter->second->m_link = nullptr; // 删除关联
			m_pool.erase(iter);
		}
	}

	void AttributeContainer::ClearAllAttributes()
	{
		for (auto& v : m_pool)
		{
			if (m_owner)
				m_owner->OnCustomAttrRemove(v.second.get());
			//m_notifyAttrRemove.Emit(v.second->GetName());
			v.second->m_link = nullptr; // 删除关联
		}
		m_pool.clear();
	}

	AutoRef<Attribute> AttributeContainer::GetAttribute(const std::string& name) const
	{
		auto iter = m_pool.find(name);
		if (iter != m_pool.end())
		{
			return iter->second;
		}
		return nullptr;
	}

	void AttributeContainer::ForEach(std::function<void(const std::pair<std::string, AutoRef<Attribute>>)> callback) const
	{
		for (auto& v : m_pool)
		{
			callback(v);
		}
	}

	void AttributeContainer::ForEach(std::function<void(std::pair<std::string, AutoRef<Attribute>>)> callback)
	{
		for (auto& v : m_pool)
		{
			callback(v);
		}
	}

	void AttributeContainer::InsertAttribute(AutoRef<Attribute> attr)
	{
		if (attr->GetName().empty() || attr->GetAttrType() == Attribute::TYPE::IDLE)
		{
			SANDBOX_ASSERT(false && "attr is not init!");
			return;
		}
		if (m_pool.find(attr->GetName()) != m_pool.end())
		{
			// 已经存在
			//SANDBOX_ASSERT(false && "error! is same name!");
			return;
		}

		m_pool.insert(make_pair(attr->GetName(), attr)); // 插入缓存
		attr->SyncContainer(this); // 同步容器
	}

	void AttributeContainer::OnCustomAttrChanged(Attribute* attr)
	{
		if (m_owner)
			m_owner->OnCustomAttrChanged(attr);
		m_notifyAttrChanged.Emit(attr->GetName());
	}

	//////////////////////////////////////////////////////////////////////////////////////

	typedef AutoRef<Attribute> AttributeRef;

	// AttributeContainer
	template<>
	void ReflexPolicyFunc<AttributeContainer>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& value = Data(data);
		out.import(MNJsonArray());
		MNJsonArray& jsonarray = out.get<MNJsonArray>();
		value.ForEach([&](const std::pair<std::string, AutoRef<Attribute>> v) -> void {
			MNJsonVal jsonval;
			MNSandbox::SerializeToJson<AttributeRef>(v.second, jsonval);
			jsonarray << jsonval;
		});
	}
	template<>
	bool ReflexPolicyFunc<AttributeContainer>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& value = Data(data);
		if (!in.is<MNJsonArray>())
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		value.ClearAllAttributes(); // 先清除所有属性

		bool ret = true;
		const MNJsonArray& jsonobj = in.get<MNJsonArray>();
		for (auto& v : jsonobj.values())
		{
			AttributeRef attr;
			if (MNSandbox::ParseFromJson<AttributeRef>(attr, *v))
			{
				value.InsertAttribute(attr);
			}
			else
			{
				SANDBOX_ASSERT(false);
				ret = false;
			}
		}
		return ret;
	}
	template<>
	bool ReflexPolicyFunc<AttributeContainer>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		auto& value = Data(data);
		int luatype = lua_type(L, objindex);
		if (luatype != LUA_TTABLE)
		{
			SANDBOX_ASSERT(false);
			return false;
		}
		value.ForEach([=](std::pair<std::string, AutoRef<Attribute>> v) -> void {
			lua_getfield(L, objindex, v.first.c_str());
			if (!lua_isnil(L, -1))
			{
				lua_toC<AttributeRef>(L, -1, v.second);
			}
			lua_pop(L, 1);
		});
		return true;
	}
	template<>
	int ReflexPolicyFunc<AttributeContainer>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		auto& value = Data(data);
		lua_newtable(L);
		value.ForEach([=](const std::pair<std::string, AutoRef<Attribute>> v) -> void {
			int ret = lua_pushC<AttributeRef>(L, v.second);
			if (ret == 1)
			{
				lua_setfield(L, -2, v.first.c_str());
			}
			else
			{
				SANDBOX_ASSERT(false);
				lua_pop(L, ret);
			}
		});
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<AttributeContainer>::CallbackToString(const void* data)
	{
		return "AttributeContainer"; // todo...
	}
	template<>
	size_t ReflexPolicyFunc<AttributeContainer>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		unsigned size = v.GetSize();
		size_t len = 0, vLen;
		len += out->WriteNumber<unsigned>(size); // 先写数量
		auto& policy = ReflexType::GetSingleton<AutoRef<Attribute>>().GetPolicy();
		for (auto& cell : v.GetDatas())
		{
			vLen = policy.m_cbReflexToBinary(&cell.second, out);
			if (vLen == Stream::Error)
				return Stream::Error;
			len += vLen;
		}
		return len;
	}
	template<>
	bool ReflexPolicyFunc<AttributeContainer>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		unsigned size = 0;
		if (!in->ReadNumber<unsigned>(size))
		{
			SANDBOX_WARNING(false && "ReflexFromBinary error");
			return false;
		}

		v.ClearAllAttributes(); // 先清除所有属性

		auto& policy = ReflexType::GetSingleton<AutoRef<Attribute>>().GetPolicy();
		AutoRef<Attribute> attr;
		for (unsigned i = 0; i < size; ++i)
		{
			if (!policy.m_cbReflexFromBinary(&attr, in))
			{
				SANDBOX_WARNING(false && "ReflexFromBinary error");
				return false;
			}
			v.InsertAttribute(attr);
		}
		return true;
	}
	RegisterReflexTypePolicyClass(AttributeContainer, REFLEXTYPEENUM_ATTRCONTAINER, ReflexPolicyFunc<AttributeContainer>);

	//////////////////////////////////////////////////////////////////////////////////////

	// AutoRef<Attribute>
	template<>
	void ReflexPolicyFunc<AttributeRef>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& val = Data(data);
		out.import(MNJsonObject());
		MNJsonObject& jsonobj = out.get<MNJsonObject>();

		jsonobj << "attrtype" << (MNJsonNumber)val->GetAttrType();
		jsonobj << "name" << (MNJsonStr)val->GetName();

		MNJsonVal jsonval;
		val->GetVariant().SerializeToJson(jsonval);
		jsonobj << "value" << jsonval;
	}
	template<>
	bool ReflexPolicyFunc<AttributeRef>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& val = Data(data);
		if (!in.is<MNJsonObject>())
		{
			SANDBOX_ASSERT(false);
			return false;
		}
		const MNJsonObject& jsonobj = in.get<MNJsonObject>();

		if (!jsonobj.has<MNJsonNumber>("attrtype")
			|| !jsonobj.has<MNJsonStr>("name")
			|| !jsonobj.has<MNJsonVal>("value"))
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		Attribute::TYPE type = (Attribute::TYPE)(int)(jsonobj.get<MNJsonNumber>("attrtype"));
		const std::string& name = jsonobj.get<MNJsonStr>("name");
		const MNJsonVal& jsonval = jsonobj.get<MNJsonVal>("value");

		val = SANDBOX_NEW(Attribute, nullptr, name, type);
		if (!val->GetVariant().ParseFromJson(jsonval))
		{
			SANDBOX_ASSERT(false);
			val = nullptr;
			return false;
		}
		return true;
	}
	template<>
	bool ReflexPolicyFunc<AttributeRef>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		auto& val = Data(data);
		return val->GetVariant().LuaToC(L, objindex);
	}
	template<>
	int ReflexPolicyFunc<AttributeRef>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		const auto& val = Data(data);
		return val->GetVariant().LuaPushC(L);
	}
	template<>
	std::string ReflexPolicyFunc<AttributeRef>::CallbackToString(const void* data)
	{
		const auto& val = Data(data);
		return val->GetVariant().ToString();
	}
	template<>
	size_t ReflexPolicyFunc<AttributeRef>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);
		size_t len = 0;
		len += out->WriteNumber<unsigned short>((unsigned short)v->GetAttrType());
		len += out->WriteString(v->GetName());
		size_t vLen = v->GetVariant().ReflexToBinary(out);
		if (vLen == Stream::Error)
			return vLen;

		len += vLen;
		return len;
	}
	template<>
	bool ReflexPolicyFunc<AttributeRef>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);
		do
		{
			unsigned short attrType = 0;
			std::string name;
			if (!in->ReadNumber<unsigned short>(attrType))
				break;

			if (!in->ReadString(name))
				break;

			v = SANDBOX_NEW(Attribute, nullptr, name, (Attribute::TYPE)attrType); // 创建
			if (!v->GetVariant().ReflexFromBinary(in))
			{
				SANDBOX_RELEASE(v);
				break;
			}

			return true;
		} while(false);

		return false;
	}
	RegisterReflexTypePolicyClass(AttributeRef, REFLEXTYPEENUM_REF_ATTRIBUTE, ReflexPolicyFunc<AttributeRef>);


}