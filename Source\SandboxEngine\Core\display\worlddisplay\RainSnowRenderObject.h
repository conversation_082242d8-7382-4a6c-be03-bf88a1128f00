#pragma once

#include "BaseClass/SharedObject.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Render/SceneObjects/RenderObject.h"
#include "Graphics/Mesh/Mesh.h"
#include "Graphics/Mesh/MeshRenderData.h"

//using namespace Rainbow;
class RainSnowRenderable;
class RainSnowMeshData;

class RainSnowRenderObject : public Rainbow::RenderObject
{
public:
	explicit RainSnowRenderObject(RainSnowRenderable* component);
	~RainSnowRenderObject();

	virtual void ExtractMeshPrimitives(Rainbow::MeshPrimitiveExtractor& extractor, Rainbow::PrimitiveViewNode& viewNode, Rainbow::PerThreadPageAllocator& allocator) override;


private:
	RainSnowRenderable* m_RainSnowEffect;
	Rainbow::MeshRenderData m_MeshRenderData;
	RainSnowMeshData* m_RenderBuffer;

	//Rainbow::SharePtr<Rainbow::MaterialInstance> m_RainMaterial;
	////Rainbow::SharePtr<Rainbow::Mesh> m_RainMesh;

	//Rainbow::SharePtr<Rainbow::MaterialInstance> m_SnowMaterial;
	////Rainbow::SharePtr<Rainbow::Mesh> m_SnowMesh;
	//Rainbow::VertexLayout* m_VertexLayout;
};

