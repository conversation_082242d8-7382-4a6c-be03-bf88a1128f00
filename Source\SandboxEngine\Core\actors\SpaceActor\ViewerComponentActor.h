#pragma once
/**
* func : 生物view组件
* by : Demon
*/
#include "SpaceActorManager.h"
#include <ActorComponent_Base.h>

class EXPORT_SANDBOXENGINE ViewerComponentActor :public ActorComponentBase,public ViewerInterface
{
DECLARE_COMPONENTCLASS(ViewerComponentActor)
public:
	ViewerComponentActor();
	~ViewerComponentActor();
	inline WCoord GetBlockPos() override;
	inline int GetRadius() override {
		return m_Radius;
	}
	void SetRadius(int r);
	void EnterWorld();
	void LeaveWorld(bool keepInChunk);
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner) override;
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner) override;
private:
	void checkAgentValid();
private:
	int m_Radius; //单位chunk
	ViewerAgent* m_Agent;
	bool m_IsEnterWorld;
	bool m_IsEnterOwner;
	IClientActor* m_Actor; //缓存减少 dynamic_cast 运算
};
