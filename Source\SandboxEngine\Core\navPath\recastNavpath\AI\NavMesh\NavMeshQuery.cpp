#include "MathUtil.h"
#include "NavMeshQuery.h"
#include "NavMesh.h"
#include "NavMeshNode.h"
#include "Utilities/BitUtility.h"
#include "Utilities/Utility.h"
#include "Math/Quaternionf.h"
#include <float.h>

static const float H_SCALE = 0.999f; // Search heuristic scale.

static float GetCostModifier(const NavMesh* navmesh, const QueryFilter* filter, NavMeshPolyRef ref)
{
    if (navmesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = navmesh->GetOffMeshConnection(ref);
        const float cost = con->costModifier;
        if (cost != -1.0f)
            return cost;
        return filter->GetAreaCost(con->area);
    }
    else
    {
        const NavMeshTile* tile = 0;
        const NavMeshPoly* poly = 0;
        navmesh->GetTileAndPolyByRef(ref, &tile, &poly);
        return filter->GetAreaCost(poly->area);
    }
    Assert(false);  // bad
    return FLT_MAX;
}

static bool SqrDistancePointPolyEdge(const Rainbow::Vector3f& pt, const Rainbow::Vector3f* verts, const int nverts,
    float* ed, float* et)
{
    // TODO: Replace pnpoly with triArea2D tests?
    int i, j;
    bool c = false;
    for (i = 0, j = nverts - 1; i < nverts; j = i++)
    {
        const Rainbow::Vector3f& vi = verts[i];
        const Rainbow::Vector3f& vj = verts[j];
        if (((vi.z > pt.z) != (vj.z > pt.z)) &&
            (pt.x < (vj.x - vi.x) * (pt.z - vi.z) / (vj.z - vi.z) + vi.x))
            c = !c;
        ed[j] = SqrDistancePointSegment2D(&et[j], pt, vj, vi);
    }
    return c;
}

static void ProjectPoly(float* rmin, float* rmax, const Rainbow::Vector3f& axis, const Rainbow::Vector3f* poly, const int npoly)
{
    float min, max;
    min = max = Dot2D(axis, poly[0]);
    for (int i = 1; i < npoly; ++i)
    {
        const float d = Dot2D(axis, poly[i]);
        min = std::min(min, d);
        max = std::max(max, d);
    }
    *rmin = min;
    *rmax = max;
}

static inline bool OverlapRange(const float amin, const float amax,
    const float bmin, const float bmax,
    const float eps)
{
    return ((amin + eps) > bmax || (amax - eps) < bmin) ? false : true;
}

static bool OverlapPolyPoly2D(const Rainbow::Vector3f* polya, const int npolya,
    const Rainbow::Vector3f* polyb, const int npolyb)
{
    constexpr float eps{ 1e-4f };

    for (int i = 0, j = npolya - 1; i < npolya; j = i++)
    {
        const Rainbow::Vector3f& va = polya[j];
        const Rainbow::Vector3f& vb = polya[i];
        const Rainbow::Vector3f n(vb.z - va.z, 0, va.x - vb.x);
        float amin, amax, bmin, bmax;
        ProjectPoly(&amin, &amax, n, polya, npolya);
        ProjectPoly(&bmin, &bmax, n, polyb, npolyb);
        if (!OverlapRange(amin, amax, bmin, bmax, eps))
        {
            // Found separating axis
            return false;
        }
    }
    for (int i = 0, j = npolyb - 1; i < npolyb; j = i++)
    {
        const Rainbow::Vector3f& va = polyb[j];
        const Rainbow::Vector3f& vb = polyb[i];
        const Rainbow::Vector3f n(vb.z - va.z, 0, -va.x - vb.x);
        float amin, amax, bmin, bmax;
        ProjectPoly(&amin, &amax, n, polya, npolya);
        ProjectPoly(&bmin, &bmax, n, polyb, npolyb);
        if (!OverlapRange(amin, amax, bmin, bmax, eps))
        {
            // Found separating axis
            return false;
        }
    }
    return true;
}

static bool PointInPolygon2D(const Rainbow::Vector3f& pt, const Rainbow::Vector3f* verts, const int nverts)
{
    // Assume clockwise and convex
    Rainbow::Vector3f prev = verts[nverts - 1];
    for (int i = 0; i < nverts; ++i)
    {
        const Rainbow::Vector3f& curr = verts[i];
        float dlx = curr.x - prev.x;
        float dlz = curr.z - prev.z;
        float dpx = pt.x - prev.x;
        float dpz = pt.z - prev.z;
        if (dpx * dlz < dpz * dlx)
            return false;
        prev = curr;
    }
    return true;
}

NavMeshQuery::NavMeshQuery(const NavMesh* navmesh, const int maxNodes)
    : m_TinyNodePool(0)
    , m_NodePool(0)
    , m_OpenList(0)
{
    memset(&m_QueryData, 0, sizeof(NavMeshQueryData));
    InitPools(navmesh, maxNodes);
}

NavMeshQuery::~NavMeshQuery()
{
    ENG_DELETE_LABEL(m_TinyNodePool, kMemAI);
    ENG_DELETE_LABEL(m_NodePool, kMemAI);
    ENG_DELETE_LABEL(m_OpenList, kMemAI);
}

NavMeshStatus NavMeshQuery::InitPools(const NavMesh* nav, const int maxNodes)
{
    Assert(!m_TinyNodePool);
    Assert(!m_NodePool);
    Assert(!m_OpenList);

    m_NavMesh = nav;

    m_TinyNodePool = ENG_NEW_LABEL(NavMeshNodePool, kMemAI) (64, 32);
    if (!m_TinyNodePool)
        return kNavMeshFailure | kNavMeshOutOfMemory;

    if (maxNodes == 0)
        return kNavMeshSuccess;

    m_NodePool = ENG_NEW_LABEL(NavMeshNodePool, kMemAI) (maxNodes, maxNodes >= 4 ? NextPowerOfTwo(maxNodes / 4) : 1);
    if (!m_NodePool)
        return kNavMeshFailure | kNavMeshOutOfMemory;

    m_OpenList = ENG_NEW_LABEL(NavMeshNodeQueue, kMemAI) (maxNodes);
    if (!m_OpenList)
        return kNavMeshFailure | kNavMeshOutOfMemory;

    return kNavMeshSuccess;
}

//////////////////////////////////////////////////////////////////////////////////////////
NavMeshStatus NavMeshQuery::ClosestPointOnPoly(NavMeshPolyRef ref, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const
{
    Assert(m_NavMesh);

    if (m_NavMesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(ref);
        if (!con)
            return kNavMeshFailure | kNavMeshInvalidParam;

        if (!con->endPoints[0].tileRef || !con->endPoints[1].tileRef)
            return kNavMeshFailure;

        if (con->width > 0.0f)
        {
            unsigned int idx = con->firstLink;
            if (idx == kNavMeshNullLink)
                return kNavMeshFailure;

            dynamic_array<Rainbow::Vector3f> lineSegments(kMemTempAlloc);
            while (idx != kNavMeshNullLink)
            {
                const NavMeshLink* link = m_NavMesh->GetLink(idx);
                const OffMeshLinkEndPoint& endPoint = con->endPoints[(int)link->edge];
                lineSegments.push_back(Lerp(endPoint.mapped[0], endPoint.mapped[1], link->bmin / 255.0f));
                lineSegments.push_back(Lerp(endPoint.mapped[0], endPoint.mapped[1], link->bmax / 255.0f));
                idx = link->next;
            }
            ClosestPointOnLineSegments(lineSegments, pos, closest);
        }
        else
        {
            float d0 = SqrDistance(con->endPoints[0].mapped[0], pos);
            float d1 = SqrDistance(con->endPoints[1].mapped[0], pos);
            if (d0 < d1)
                *closest = con->endPoints[0].mapped[0];
            else
                *closest = con->endPoints[1].mapped[0];
        }
    }
    else
    {
        const NavMeshTile* tile = 0;
        const NavMeshPoly* poly = 0;
        if (NavMeshStatusFailed(m_NavMesh->GetTileAndPolyByRef(ref, &tile, &poly)))
            return kNavMeshFailure | kNavMeshInvalidParam;
        if (!tile)
            return kNavMeshFailure | kNavMeshInvalidParam;

        const Rainbow::Vector3f localPos = WorldToTile(*tile, pos);
        Rainbow::Vector3f localClosest;
        ClosestPointOnPolyInTileLocal(tile, poly, ref, localPos, &localClosest);
        *closest = TileToWorld(*tile, localClosest);
    }

    return kNavMeshSuccess;
}

static inline bool ProjectPointToPoly2DLocal(const Rainbow::Vector3f& pos, const NavMeshPoly* poly, const NavMeshTile* tile, Rainbow::Vector3f* projectedPos)
{
    Assert(poly);
    Assert(tile);

    Rainbow::Vector3f verts[kNavMeshVertsPerPoly];
    float edged[kNavMeshVertsPerPoly];
    float edget[kNavMeshVertsPerPoly];

    const int nv = poly->vertCount;
    for (int i = 0; i < nv; i++)
        verts[i] = tile->verts[poly->verts[i]];

    bool inside = SqrDistancePointPolyEdge(pos, verts, nv, edged, edget);
    if (inside)
    {
        // Point is inside the polygon, return the point.
        *projectedPos = pos;
    }
    else
    {
        // Point is outside the polygon, clamp to nearest edge.
        float dmin = FLT_MAX;
        int imin = -1;
        for (int i = 0; i < nv; ++i)
        {
            if (edged[i] < dmin)
            {
                dmin = edged[i];
                imin = i;
            }
        }
        Assert(imin < nv);
        const int iminnext = (imin + 1 == nv) ? 0 : imin + 1;
        const Rainbow::Vector3f& va = verts[imin];
        const Rainbow::Vector3f& vb = verts[iminnext];
        *projectedPos = Lerp(va, vb, edget[imin]);
        //points that are on (or very close to) an edge are also considered to be inside the polygon
        inside = dmin < FLT_EPSILON;
    }
    return inside;
}

NavMeshStatus NavMeshQuery::GetUpAxis(NavMeshPolyRef ref, Rainbow::Vector3f* up) const
{
    if (m_NavMesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        if (const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(ref))
        {
            *up = con->axisY;
            return kNavMeshSuccess;
        }
        return kNavMeshFailure | kNavMeshInvalidParam;
    }

    const Rainbow::Vector3f yAxis(0.0f, 1.0f, 0.0f);
    if (const NavMeshTile* tile = m_NavMesh->GetTileByRef(ref))
    {
        *up = Rainbow::RotateVectorByQuat(tile->rotation, yAxis);
        return kNavMeshSuccess;
    }

    return kNavMeshFailure | kNavMeshInvalidParam;
}

// Local space input/output
bool NavMeshQuery::ClosestPointOnPolyInTileLocal(const NavMeshTile* tile, const NavMeshPoly* poly, const NavMeshPolyRef ref, const Rainbow::Vector3f& pos, Rainbow::Vector3f* closest) const
{
    bool inside = ProjectPointToPoly2DLocal(pos, poly, tile, closest);
    if (inside)
    {
        // Find height at the location.
        GetPolyHeightLocal(ref, *closest, &closest->y);
    }
    else
    {
        m_NavMesh->ClosestPointOnPolyBoundaryInTileLocal(tile, poly, pos, closest);
    }

    return inside;
}

// Project point height to detail triangle and accept immediately if the projected point is inside triangle
static bool ProjectToPolyDetail(const NavMeshTile* tile, const NavMeshPoly* poly, const Rainbow::Vector3f& pos, float* height)
{
    const unsigned int ip = GetPolyIndex(tile, poly);
    const NavMeshPolyDetail* pd = &tile->detailMeshes[ip];
    for (int j = 0; j < pd->triCount; ++j)
    {
        const NavMeshPolyDetailIndex* t = &tile->detailTris[(pd->triBase + j) * 4];
        Rainbow::Vector3f v[3];
        for (int k = 0; k < 3; ++k)
        {
            if (t[k] < poly->vertCount)
                v[k] = tile->verts[poly->verts[t[k]]];
            else
                v[k] = tile->detailVerts[pd->vertBase + (t[k] - poly->vertCount)];
        }
        float h;
        if (ClosestHeightPointTriangle(&h, pos, v[0], v[1], v[2]))
        {
            *height = h;
            return true;
        }
    }
    return false;
}

// In the plane of projection find the height of the closest point on the edge of the detail triangles
static float ProjectToPolyDetailEdge(const NavMeshTile* tile, const NavMeshPoly* poly, const Rainbow::Vector3f& pos)
{
    const unsigned int ip = GetPolyIndex(tile, poly);
    const NavMeshPolyDetail* pd = &tile->detailMeshes[ip];

    float dmin = FLT_MAX;
    float h = FLT_MAX;
    for (int j = 0; j < pd->triCount; ++j)
    {
        const NavMeshPolyDetailIndex* t = &tile->detailTris[(pd->triBase + j) * 4];
        Rainbow::Vector3f v[3];
        for (int k = 0; k < 3; ++k)
        {
            if (t[k] < poly->vertCount)
                v[k] = tile->verts[poly->verts[t[k]]];
            else
                v[k] = tile->detailVerts[pd->vertBase + (t[k] - poly->vertCount)];
        }

        for (int kp = 2, k = 0; k < 3; kp = k++)
        {
            float tt;
            float d = SqrDistancePointSegment2D(&tt, pos, v[kp], v[k]);
            if (d < dmin)
            {
                dmin = d;
                h = Rainbow::Lerp(v[kp].y, v[k].y, tt);
            }
        }
    }
    return h;
}

NavMeshStatus NavMeshQuery::GetPolyHeightLocal(NavMeshPolyRef ref, const Rainbow::Vector3f& pos, float* height) const
{
    Assert(m_NavMesh);
    Assert(height);

    if (m_NavMesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(ref);
        if (con)
        {
            // TODO: handle segment
            const Rainbow::Vector3f v0 = con->endPoints[0].mapped[0];
            const Rainbow::Vector3f v1 = con->endPoints[1].mapped[0];
            const float d0 = Rainbow::Distance(pos, v0);
            const float d1 = Rainbow::Distance(pos, v1);
            const float u = d0 / (d0 + d1);
            *height = Rainbow::Lerp(v0.y, v1.y, u);
            return kNavMeshSuccess;
        }
    }
    else
    {
        const NavMeshTile* tile = 0;
        const NavMeshPoly* poly = 0;
        if (NavMeshStatusSucceed(m_NavMesh->GetTileAndPolyByRef(ref, &tile, &poly)))
        {
            // Most calls should terminate here
            if (ProjectToPolyDetail(tile, poly, pos, height))
                return kNavMeshSuccess;

            // Only rarely should this be executed - so we allow a second lookup of triangles
            *height = ProjectToPolyDetailEdge(tile, poly, pos);
            return kNavMeshSuccess;
        }
    }

    return kNavMeshFailure | kNavMeshInvalidParam;
}

// Project point to polygon along the local up-axis
NavMeshStatus NavMeshQuery::ProjectToPoly(Rainbow::Vector3f* projPos, NavMeshPolyRef ref, const Rainbow::Vector3f& pos) const
{
    Assert(m_NavMesh);
    Assert(projPos);
    *projPos = pos;

    if (m_NavMesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
    {
        const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(ref);
        if (con)
        {
            const Rainbow::Vector3f v0 = Lerp(con->endPoints[0].mapped[0], con->endPoints[0].mapped[1], 0.5f);
            const Rainbow::Vector3f v1 = Lerp(con->endPoints[1].mapped[0], con->endPoints[1].mapped[1], 0.5f);
            const float d0 = Rainbow::Distance(pos, v0);
            const float d1 = Rainbow::Distance(pos, v1);
            const float u = d0 / (d0 + d1);
            *projPos = Lerp(v0, v1, u);
            return kNavMeshSuccess;
        }
    }
    else
    {
        const NavMeshTile* tile = 0;
        const NavMeshPoly* poly = 0;
        if (NavMeshStatusSucceed(m_NavMesh->GetTileAndPolyByRef(ref, &tile, &poly)))
        {
            // Most calls should terminate here
            float localHeight;
            const Rainbow::Vector3f localPos = WorldToTile(*tile, pos);
            if (ProjectToPolyDetail(tile, poly, localPos, &localHeight))
            {
                const Rainbow::Vector3f worldPos = TileToWorld(*tile, Rainbow::Vector3f(localPos.x, localHeight, localPos.z));
                *projPos = worldPos;
                return kNavMeshSuccess;
            }

            // Only rarely should this be executed - so we allow a second lookup of triangles
            localHeight = ProjectToPolyDetailEdge(tile, poly, localPos);
            const Rainbow::Vector3f worldPos = TileToWorld(*tile, Rainbow::Vector3f(localPos.x, localHeight, localPos.z));
            *projPos = worldPos;
            return kNavMeshSuccess;
        }
    }

    return kNavMeshFailure | kNavMeshInvalidParam;
}

void NavMeshQuery::FindNearestPoly(const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents,
    const QueryFilter* filter, bool preferInside,
    NavMeshPolyRef* nearestRef, Rainbow::Vector3f* nearestPt) const
{
    Assert(m_NavMesh);
    Assert(nearestRef);
    Assert(nearestPt);

    struct NearestQuery : public NavMeshProcessCallback
    {
        const NavMeshQuery* m_NavMeshMeshQuery;
        const QueryFilter* m_Filter;
        Rainbow::Vector3f m_Center;

        float m_DistanceSqr[2];
        NavMeshPolyRef m_PolyRef[2];
        Rainbow::Vector3f m_LocalPoint[2];

        inline NearestQuery(const NavMeshQuery* navMeshQuery, const QueryFilter* filter, const Rainbow::Vector3f& center)
            : m_NavMeshMeshQuery(navMeshQuery), m_Filter(filter), m_Center(center)
        {
            m_DistanceSqr[0] = FLT_MAX;
            m_DistanceSqr[1] = FLT_MAX;
            m_PolyRef[0] = 0;
            m_PolyRef[1] = 0;
            // It's strictly not necessary to initialise the positions.
            // The user should be checking the corresponding polygon reference before reading the position
            m_LocalPoint[0].SetZero();
            m_LocalPoint[1].SetZero();
        }

        virtual void ProcessPolygons(const NavMeshTile* tile, const NavMeshPolyRef* polyRefs, const NavMeshPoly** polys, const int itemCount)
        {
            const Rainbow::Vector3f localPosition = WorldToTile(*tile, m_Center);

            for (int item = 0; item < itemCount; ++item)
            {
                NavMeshPolyRef ref = polyRefs[item];
                const NavMeshPoly* poly = polys[item];
                if (!m_Filter->PassFilter(poly->flags))
                    continue;

                Rainbow::Vector3f closestPtPoly;
                bool inside = m_NavMeshMeshQuery->ClosestPointOnPolyInTileLocal(tile, poly, ref, localPosition, &closestPtPoly);

                // Keep track of the closest position. Calculating the squared vertical and 3D distances.
                // The 3D distance is used for comparing points projected outside the polygon - otherwise using the vertical distance.
                float d[2];
                d[0] = SqrMagnitude(localPosition - closestPtPoly);
                d[1] = Rainbow::Sqr(localPosition.y - closestPtPoly.y);

                if (d[inside] < m_DistanceSqr[inside])
                {
                    m_LocalPoint[inside] = closestPtPoly;
                    m_DistanceSqr[inside] = d[inside];
                    m_PolyRef[inside] = ref;
                }
            }
        }
    };

    // Get the nearest polygons and projected points
    NearestQuery nearest(this, filter, center);
    m_NavMesh->QueryPolygons(filter->GetTypeID(), center, extents, &nearest);

    // Check if any of the closest points found (outside/inside) is within the query volume
    const Rainbow::AABB bounds(center, extents);
    Rainbow::Vector3f point;
    NavMeshPolyRef ref = 0;
    for (int inside = 0; inside < 2; ++inside)
    {
        if (const NavMeshTile* tile = m_NavMesh->GetTileByRef(nearest.m_PolyRef[inside]))
        {
            Rainbow::Vector3f worldPoint = TileToWorld(*tile, nearest.m_LocalPoint[inside]);
            if (bounds.IsInside(worldPoint))
            {
                ref = nearest.m_PolyRef[inside];
                point = worldPoint;
            }
        }

        if (!inside && !preferInside && ref != 0 && nearest.m_DistanceSqr[0] <= nearest.m_DistanceSqr[1])
            break;
    }

    if (nearestRef)
        *nearestRef = ref;

    if (nearestPt && ref)
        *nearestPt = point;
}

void NavMeshQuery::QueryPolygons(const Rainbow::Vector3f& center, const Rainbow::Vector3f& extents,
    const QueryFilter* filter,
    NavMeshPolyRef* polys, int* polyCount, const int maxPolys) const
{
    struct PolygonQuery : public NavMeshProcessCallback
    {
        const NavMeshQuery* m_NavMeshMeshQuery;
        const QueryFilter* m_Filter;
        NavMeshPolyRef* m_Results;
        const int m_MaxResults;
        int m_ResultCount;

        inline PolygonQuery(const NavMeshQuery* query, const QueryFilter* filter, NavMeshPolyRef* polys, const int maxPolys) :
            m_NavMeshMeshQuery(query), m_Filter(filter), m_Results(polys), m_MaxResults(maxPolys), m_ResultCount(0)
        {
        }

        virtual void ProcessPolygons(const NavMeshTile* /*tile*/, const NavMeshPolyRef* polyRefs, const NavMeshPoly** polys, const int itemCount)
        {
            // Find nearest polygon amongst the nearby polygons.
            for (int i = 0; i < itemCount; ++i)
            {
                if (!m_Filter->PassFilter(polys[i]->flags))
                    continue;

                if (m_ResultCount < m_MaxResults)
                    m_Results[m_ResultCount++] = polyRefs[i];
            }
        }
    };

    PolygonQuery polygons(this, filter, polys, maxPolys);
    m_NavMesh->QueryPolygons(filter->GetTypeID(), center, extents, &polygons);

    if (polyCount)
        *polyCount = polygons.m_ResultCount;
}

NavMeshStatus NavMeshQuery::InitSlicedFindPath(NavMeshPolyRef startRef, NavMeshPolyRef endRef,
    const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
    const int agentType, const int areaMask, const float costs[NavMeshProjectSettings::kAreaCount])
{
    if (!m_NavMesh)
        return kNavMeshFailure;
    m_Filter.Set(agentType, (unsigned int)areaMask, costs);
    return InitSlicedFindPath(startRef, endRef, startPos, endPos, &m_Filter);
}

NavMeshStatus NavMeshQuery::InitSlicedFindPath(NavMeshPolyRef startRef, NavMeshPolyRef endRef,
    const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
    const QueryFilter* filter)
{
    Assert(m_NavMesh);
    Assert(m_NodePool);
    Assert(m_OpenList);

    // Init path state.
    memset(&m_QueryData, 0, sizeof(NavMeshQueryData));
    m_QueryData.status = kNavMeshFailure;
    m_QueryData.startRef = startRef;
    m_QueryData.endRef = endRef;
    m_QueryData.startPos = startPos;
    m_QueryData.endPos = endPos;
    m_QueryData.filter = filter;

    if (!startRef || !endRef)
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Validate input
    if (!m_NavMesh->IsValidPolyRef(startRef) || !m_NavMesh->IsValidPolyRef(endRef))
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Validate first poly
    if (!m_QueryData.filter->PassFilter(m_NavMesh->GetPolyFlags(startRef)))
        return kNavMeshFailure;

    if (startRef == endRef)
    {
        m_QueryData.status = kNavMeshSuccess;
        return kNavMeshSuccess;
    }

    m_NodePool->Clear();
    m_OpenList->Clear();

    Rainbow::Vector3f endPosProjStart;
    ClosestPointOnPoly(startRef, endPos, &endPosProjStart);
    const float startHeuristic = Rainbow::Distance(endPosProjStart, endPos) * H_SCALE;

    NavMeshNode* startNode = m_NodePool->GetNode(startRef);
    startNode->pos = startPos;
    startNode->pidx = 0;
    startNode->cost = 0;
    startNode->total = startHeuristic;
    startNode->id = startRef;
    startNode->flags = NavMeshNode::kOpen;
    m_OpenList->Push(startNode);

    m_QueryData.status = kNavMeshInProgress;
    m_QueryData.lastBestNode = startNode;
    m_QueryData.lastBestNodeCost = startHeuristic;

    return m_QueryData.status;
}

NavMeshStatus NavMeshQuery::UpdateSlicedFindPath(const int maxIter, int* doneIters)
{
    Assert(m_NavMesh);
    Assert(m_NodePool);
    Assert(m_OpenList);

    if (!NavMeshStatusInProgress(m_QueryData.status))
    {
        if (doneIters)
            *doneIters = 0;
        return m_QueryData.status;
    }

    // Make sure the request is still valid.
    if (!m_NavMesh->IsValidPolyRef(m_QueryData.startRef) || !m_NavMesh->IsValidPolyRef(m_QueryData.endRef))
    {
        m_QueryData.status = kNavMeshFailure;
        if (doneIters)
            *doneIters = 0;
        return kNavMeshFailure;
    }

    int iter = 0;
    while (iter < maxIter && !m_OpenList->empty())
    {
        iter++;

        // Remove node from open list and put it in closed list.
        NavMeshNode* bestNode = m_OpenList->Pop();
        bestNode->flags &= ~NavMeshNode::kOpen;
        bestNode->flags |= NavMeshNode::kClosed;

        // Reached the goal, stop searching.
        if (bestNode->id == m_QueryData.endRef)
        {
            m_QueryData.lastBestNode = bestNode;
            const NavMeshStatus details = m_QueryData.status & kNavMeshStatusDetailMask;
            m_QueryData.status = kNavMeshSuccess | details;
            if (doneIters)
                *doneIters = iter;
            return m_QueryData.status;
        }

        // Get and verify best poly ref.
        const NavMeshPolyRef bestRef = bestNode->id;
        if (!m_NavMesh->IsValidPolyRef(bestRef))
        {
            // Things changed during the sliced query, fail.
            m_QueryData.status = kNavMeshFailure;
            if (doneIters)
                *doneIters = iter;
            return kNavMeshFailure;
        }

        // Get and verify parent poly ref.
        NavMeshPolyRef parentRef = 0;
        if (bestNode->pidx)
        {
            // Things changed during the sliced query, fail.
            parentRef = m_NodePool->GetNodeAtIdx(bestNode->pidx)->id;
            if (!m_NavMesh->IsValidPolyRef(parentRef))
            {
                m_QueryData.status = kNavMeshFailure;
                if (doneIters)
                    *doneIters = iter;
                return kNavMeshFailure;
            }
        }

        for (const NavMeshLink* link = m_NavMesh->GetFirstLink(bestRef); link != NULL; link = m_NavMesh->GetNextLink(link))
        {
            UpdateNeighbourLink(link, parentRef, bestRef, bestNode);
        }
    }

    // Exhausted all nodes, but could not find path.
    if (m_OpenList->empty())
    {
        const NavMeshStatus details = m_QueryData.status & kNavMeshStatusDetailMask;
        m_QueryData.status = kNavMeshSuccess | details;
    }

    if (doneIters)
        *doneIters = iter;
    return m_QueryData.status;
}

void NavMeshQuery::UpdateNeighbourLink(const NavMeshLink* link, const NavMeshPolyRef parentRef, const NavMeshPolyRef bestRef, const NavMeshNode* bestNode)
{
    Assert(m_OpenList);
    Assert(m_NodePool);
    NavMeshPolyRef neighbourRef = link->ref;

    // Skip invalid ids and do not expand back to where we came from.
    if (!neighbourRef || neighbourRef == parentRef)
        return;

    if (!m_QueryData.filter->PassFilter(m_NavMesh->GetPolyFlags(neighbourRef)))
        return;

    NavMeshNode* neighbourNode = m_NodePool->GetNode(neighbourRef);
    if (!neighbourNode)
    {
        m_QueryData.status |= kNavMeshOutOfNodes;
        return;
    }

    // If the node is visited the first time, calculate node position.
    if (neighbourNode->flags == NavMeshNode::kNew)
    {
        Rainbow::Vector3f left, right;
        if (NavMeshStatusFailed(GetPortalPoints(bestRef, neighbourRef, &left, &right)))
            return;

        Rainbow::Vector3f dl = right - left;
        const float ddl = Dot(dl, dl);
        if (ddl > 0.0001f)
        {
            Rainbow::Vector3f dp = bestNode->pos - left;
            float pdot = Dot(dl, dp) / ddl;
            pdot = Rainbow::FloatClamp(pdot, 0.05f, 0.95f); // Avoid degenerating to vertex

            dp = left + dl * pdot;
            neighbourNode->pos = dp;
        }
        else
        {
            neighbourNode->pos = left;
        }
    }

    // Calculate cost and heuristic.
    float heuristic = 0.0f;
    float cost = bestNode->cost + Rainbow::Distance(bestNode->pos, neighbourNode->pos) * GetCostModifier(m_NavMesh, m_QueryData.filter, bestRef);
    DebugAssertMsg(cost >= 0.0f, "Negative cost encountered");

    // Special case for last node.
    if (neighbourRef == m_QueryData.endRef)
    {
        cost += Rainbow::Distance(neighbourNode->pos, m_QueryData.endPos) * GetCostModifier(m_NavMesh, m_QueryData.filter, neighbourRef);
        DebugAssertMsg(cost >= 0.0f, "Negative cost encountered");
    }
    else
    {
        Rainbow::Vector3f endPosProjNeighbour;
        if (NavMeshStatusFailed(ClosestPointOnPoly(neighbourRef, m_QueryData.endPos, &endPosProjNeighbour)))
            return;
        heuristic = Rainbow::Distance(endPosProjNeighbour, m_QueryData.endPos) * H_SCALE;
    }

    // Update nearest node to target so far.
    if (heuristic < m_QueryData.lastBestNodeCost)
    {
        m_QueryData.lastBestNodeCost = heuristic;
        m_QueryData.lastBestNode = neighbourNode;
    }

    const float total = cost + heuristic;

    // The node is already in open list and the new result is worse, skip.
    if ((neighbourNode->flags & NavMeshNode::kOpen) && total >= neighbourNode->total)
        return;
    // The node is already visited and process, and the new result is worse, skip.
    if ((neighbourNode->flags & NavMeshNode::kClosed) && total >= neighbourNode->total)
        return;

    // Add or update the node.
    neighbourNode->pidx = m_NodePool->GetNodeIdx(bestNode);
    neighbourNode->id = neighbourRef;
    neighbourNode->flags &= ~NavMeshNode::kClosed;
    neighbourNode->cost = cost;
    neighbourNode->total = total;

    if (neighbourNode->flags & NavMeshNode::kOpen)
    {
        // Already in open, update node location.
        m_OpenList->Modify(neighbourNode);
    }
    else
    {
        // Put the node in open list.
        neighbourNode->flags |= NavMeshNode::kOpen;
        m_OpenList->Push(neighbourNode);
    }
}

NavMeshStatus NavMeshQuery::FinalizeSlicedFindPath(int* pathCount)
{
    Assert(m_NodePool);

    *pathCount = 0;

    if (NavMeshStatusFailed(m_QueryData.status))
    {
        // Reset query.
        memset(&m_QueryData, 0, sizeof(NavMeshQueryData));
        return kNavMeshFailure;
    }

    int n = 0;

    if (m_QueryData.startRef == m_QueryData.endRef)
    {
        // Special case: the search starts and ends at same poly.
        n = 1;
    }
    else
    {
        // Reverse the path.
        Assert(m_QueryData.lastBestNode);

        if (m_QueryData.lastBestNode->id != m_QueryData.endRef)
            m_QueryData.status |= kNavMeshPartialResult;

        NavMeshNode* prev = 0;
        NavMeshNode* node = m_QueryData.lastBestNode;
        do
        {
            NavMeshNode* next = m_NodePool->GetNodeAtIdx(node->pidx);
            node->pidx = m_NodePool->GetNodeIdx(prev);
            prev = node;
            node = next;
            n++;
        }
        while (node);

        m_QueryData.startNode = prev;
        DebugAssertMsg(m_QueryData.startRef == prev->id, "First node mismatch after reversing path");
    }

    const NavMeshStatus details = m_QueryData.status & kNavMeshStatusDetailMask;

    *pathCount = n;

    return kNavMeshSuccess | details;
}

NavMeshStatus NavMeshQuery::FinalizeSlicedFindPathPartial(int* pathCount, const NavMeshPolyRef* existing, const int existingSize)
{
    *pathCount = 0;

    if (existingSize == 0)
    {
        return kNavMeshFailure;
    }

    if (NavMeshStatusFailed(m_QueryData.status))
    {
        // Reset query.
        memset(&m_QueryData, 0, sizeof(NavMeshQueryData));
        return kNavMeshFailure;
    }

    int n = 0;

    if (m_QueryData.startRef == m_QueryData.endRef)
    {
        // Special case: the search starts and ends at same poly.
        n = 1;
    }
    else
    {
        // Find furthest existing node that was visited.
        NavMeshNode* prev = 0;
        NavMeshNode* node = 0;
        for (int i = existingSize - 1; i >= 0; --i)
        {
            node = m_NodePool->FindNavMeshNode(existing[i]);
            if (node && node->pidx)
                break;

            node = NULL;
        }

        if (!node)
        {
            return kNavMeshFailure;
        }

        // Reverse the path.
        do
        {
            NavMeshNode* next = m_NodePool->GetNodeAtIdx(node->pidx);
            node->pidx = m_NodePool->GetNodeIdx(prev);
            prev = node;
            node = next;
            n++;
        }
        while (node);

        m_QueryData.startNode = prev;
    }

    const NavMeshStatus details = m_QueryData.status & kNavMeshStatusDetailMask;

    *pathCount = n;

    return kNavMeshSuccess | details;
}

NavMeshStatus NavMeshQuery::GetPath(NavMeshPolyRef* path, int* pathCount, const int maxPath)
{
    if (NavMeshStatusFailed(m_QueryData.status))
    {
        // Reset query.
        memset(&m_QueryData, 0, sizeof(NavMeshQueryData));
        return kNavMeshFailure;
    }

    int n = 0;
    if (m_QueryData.startRef == m_QueryData.endRef)
    {
        // Special case: the search starts and ends at same poly.
        path[n++] = m_QueryData.startRef;
    }
    else
    {
        // startNode is updated when the path is reversed, must have it to continue.
        if (!m_QueryData.startNode)
            return kNavMeshFailure;
        // Store path
        NavMeshNode* node = m_QueryData.startNode;
        do
        {
            path[n++] = node->id;
            if (n >= maxPath)
            {
                m_QueryData.status |= kNavMeshBufferTooSmall;
                break;
            }
            node = m_NodePool->GetNodeAtIdx(node->pidx);
        }
        while (node);
    }

    const NavMeshStatus details = m_QueryData.status & kNavMeshStatusDetailMask;

    // Reset query.
    memset(&m_QueryData, 0, sizeof(NavMeshQueryData));

    *pathCount = n;

    return kNavMeshSuccess | details;
}

// returns the Closest Point of Approach (CPA) on segment p for two line segments (p,q).
// note: returns segment midpoint in the degenerate case.
static Rainbow::Vector3f SegmentSegmentCPA(const Rainbow::Vector3f& p0, const Rainbow::Vector3f& p1, const Rainbow::Vector3f& q0, const Rainbow::Vector3f& q1)
{
    Rainbow::Vector3f u = p1 - p0;
    Rainbow::Vector3f v = q1 - q0;
    Rainbow::Vector3f w0 = p0 - q0;

    float a = Dot(u, u);
    float b = Dot(u, v);
    float c = Dot(v, v);
    float d = Dot(u, w0);
    float e = Dot(v, w0);

    float den = (a * c - b * b);
    if (den == 0)
        return 0.5f * (p0 + p1);

    float t = (b * e - c * d) / den;
    return Lerp(p0, p1, clamp01(t));
}

// Retrace portals between corners register if type of polygon changes
bool NavMeshQuery::RetracePortals(const int startIndex, const int endIndex, const NavMeshPolyRef* const path, const Rainbow::Vector3f& termPos, const bool termPosIsPathFinalPos,
    Rainbow::Vector3f* const straightPath , unsigned char* const straightPathFlags, NavMeshPolyRef* const straightPathRefs, const int maxStraightPath, int& straightPathCurrentSize) const
{
    Assert(startIndex <= endIndex);

    // Early out when the path buffer is already full as we know this function will at least add 1 corner
    if (straightPathCurrentSize >= maxStraightPath)
    {
        return false;
    }

    // For the last path section (when the given term pos is the path final pos) we need to also consider the last polygon in order to find a potential link exit
    const int lastOffMeshLinkVerificationIndex = termPosIsPathFinalPos ? endIndex : endIndex - 1;

    for (int k = startIndex; k < lastOffMeshLinkVerificationIndex; ++k)
    {
        const NavMeshPolyRef& poly1 = path[k];
        const NavMeshPolyRef& poly2 = path[k + 1];
        const unsigned char type1 = NavMesh::DecodePolyIdType(poly1);
        const unsigned char type2 = NavMesh::DecodePolyIdType(poly2);
        if (type1 != type2)
        {
            Rainbow::Vector3f l, r;
            const NavMeshStatus status = GetPortalPoints(poly1, poly2, &l, &r);
            Assert(status == kNavMeshSuccess);  // Expect path elements k, k+1 to be verified
            const Rainbow::Vector3f cpa = SegmentSegmentCPA(l, r, straightPath[straightPathCurrentSize - 1], termPos);
            straightPath[straightPathCurrentSize] = cpa;
            straightPathRefs[straightPathCurrentSize] = poly2;
            straightPathFlags[straightPathCurrentSize] = (type2 == kPolyTypeOffMeshConnection) ? kStraightPathOffMeshConnection : 0;
            if (++straightPathCurrentSize == maxStraightPath)
            {
                return false;
            }
        }
    }

    straightPath[straightPathCurrentSize] = termPos;
    straightPathRefs[straightPathCurrentSize] = path[endIndex];
    straightPathFlags[straightPathCurrentSize] = NavMesh::DecodePolyIdType(path[endIndex]) == kPolyTypeOffMeshConnection ? kStraightPathOffMeshConnection : 0;
    ++straightPathCurrentSize;

    return true;
}

static inline Rainbow::Vector3f WorldToTileSafe(const NavMeshTile* tile, const Rainbow::Vector3f& p)
{
    return tile ? WorldToTile(*tile, p) : p;
}

static inline Rainbow::Vector3f TileToWorldSafe(const NavMeshTile* tile, const Rainbow::Vector3f& p)
{
    return tile ? TileToWorld(*tile, p) : p;
}

NavMeshPolyRef NavMeshQuery::ValidatePoly(NavMeshPolyRef ref) const
{
    if (!ref) return ref;

    const float k_CollinearPointsThreshold = 1e-6f;

    Rainbow::Vector3f verts[kNavMeshVertsPerPoly];
    const int nverts = m_NavMesh->GetPolyGeometry(ref, verts, NULL, 0);
    if (nverts > 2)
    {
        const auto firstEdge{ verts[1] - verts[0] };
        for (int i{ 2 }; i < nverts; ++i)
        {
            const auto edge{ verts[i] - verts[i - 1] };
            if (std::abs(Perp2D(firstEdge, edge)) > k_CollinearPointsThreshold)
            {
                return ref;
            }
        }
    }

    return 0;
}

NavMeshStatus NavMeshQuery::FindStraightPath(const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos
    , const NavMeshPolyRef* path, const int pathSize
    , Rainbow::Vector3f* straightPath
    , unsigned char* straightPathFlags
    , NavMeshPolyRef* straightPathRefs
    , int* straightPathCount
    , const int maxStraightPath) const
{
    Assert(m_NavMesh);
    Assert(maxStraightPath > 1);
    Assert(pathSize > 0);
    Assert(straightPath != NULL);
    Assert(straightPathRefs != NULL);
    Assert(straightPathFlags != NULL);

    if (!path[0] || !m_NavMesh->IsValidPolyRef(path[0]))
    {
        *straightPathCount = 0;
        return kNavMeshFailure | kNavMeshInvalidParam;
    }

    straightPath[0] = startPos;
    straightPathRefs[0] = path[0];
    straightPathFlags[0] = kStraightPathStart;

    int apexIndex = 0;
    int n = 1;

    if (pathSize > 1)
    {
        const NavMeshTile* startTile = m_NavMesh->GetTileByRef(path[0]);
        Rainbow::Vector3f apex = WorldToTileSafe(startTile, startPos);
        Rainbow::Vector3f left(0.0f, 0.0f, 0.0f);
        Rainbow::Vector3f right(0.0f, 0.0f, 0.0f);
        int leftIndex = -1;
        int rightIndex = -1;

        for (int i = 1; i <= pathSize; ++i)
        {
            const NavMeshTile* tile = m_NavMesh->GetTileByRef(path[apexIndex]);

            Rainbow::Vector3f vl, vr;
            if (i == pathSize)
            {
                vl = vr = WorldToTileSafe(tile, endPos);
            }
            else
            {
                if (NavMeshStatusFailed(GetPortalPoints(path[i - 1], path[i], &vl, &vr)))
                {
                    return kNavMeshFailure | kNavMeshInvalidParam;
                }
                Assert(m_NavMesh->IsValidPolyRef(path[i - 1]));
                Assert(m_NavMesh->IsValidPolyRef(path[i]));

                vl = WorldToTileSafe(tile, vl);
                vr = WorldToTileSafe(tile, vr);

                // If starting really close to the portal, advance.
                if (i == 1)
                {
                    float tseg;
                    float distSqr = SqrDistancePointSegment2D(&tseg, apex, vl, vr);
                    if (distSqr < Rainbow::Sqr(0.002f))
                        continue;
                }
            }

            vl = vl - apex;
            vr = vr - apex;

            for (int i = 0; i < 3; ++i)
            {
                if (std::abs(vl[i]) <= k_Eps) vl[i] = 0;
                if (std::abs(vr[i]) <= k_Eps) vr[i] = 0;
            }

            // Ensure left/right ordering
            if (Perp2D(vl, vr) < 0)
                Swap(vl, vr);

            // Terminate funnel by turning
            if (Perp2D(left, vr) < 0)
            {
                const Rainbow::Vector3f termPos = TileToWorldSafe(tile, apex + left);
                if (!RetracePortals(apexIndex, leftIndex, path, termPos, false, straightPath, straightPathFlags, straightPathRefs, maxStraightPath, n))
                {
                    *straightPathCount = n;
                    return kNavMeshBufferTooSmall | kNavMeshSuccess;
                }

                apex = WorldToTileSafe(tile, termPos);
                left.SetZero();
                right.SetZero();
                i = apexIndex = leftIndex;
                continue;
            }
            if (Perp2D(right, vl) > 0)
            {
                const Rainbow::Vector3f termPos = TileToWorldSafe(tile, apex + right);
                if (!RetracePortals(apexIndex, rightIndex, path, termPos, false, straightPath, straightPathFlags, straightPathRefs, maxStraightPath, n))
                {
                    *straightPathCount = n;
                    return kNavMeshBufferTooSmall | kNavMeshSuccess;
                }

                apex = WorldToTileSafe(tile, termPos);
                left.SetZero();
                right.SetZero();
                i = apexIndex = rightIndex;
                continue;
            }
            // Consider: additional termination test - based on changing up-vector in frame of reference

            // Narrow funnel
            if (Perp2D(left, vl) >= 0)
            {
                left = vl;
                leftIndex = i;
            }
            if (Perp2D(right, vr) <= 0)
            {
                right = vr;
                rightIndex = i;
            }
        }
    }

    // Remove the the next to last if duplicate point - e.g. start and end positions are the same
    // (in which case we have get a single point)
    if (n > 0 && (straightPath[n - 1] == endPos))
        n--;

    if (!RetracePortals(apexIndex, pathSize - 1, path, endPos, true, straightPath, straightPathFlags, straightPathRefs, maxStraightPath, n))
    {
        *straightPathCount = n;
        return kNavMeshBufferTooSmall | kNavMeshSuccess;
    }

    // Fix flag for final path point
    straightPathFlags[n - 1] = kStraightPathEnd;

    *straightPathCount = n;
    return kNavMeshSuccess;
}

NavMeshStatus NavMeshQuery::MoveAlongSurface(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
    const QueryFilter* filter,
    Rainbow::Vector3f* resultPos, NavMeshPolyRef* visited, int* visitedCount, const int maxVisitedSize) const
{
    return MoveAlongSurface(startRef, startPos, endPos, filter, resultPos, visited, visitedCount, maxVisitedSize, m_TinyNodePool);
}

NavMeshStatus NavMeshQuery::MoveAlongSurface(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
    const QueryFilter* filter,
    Rainbow::Vector3f* resultPos, NavMeshPolyRef* visited, int* visitedCount, const int maxVisitedSize,
    NavMeshNodePool* nodePool) const
{
    Assert(m_NavMesh);
    Assert(nodePool);

    *visitedCount = 0;

    // Validate input
    if (!startRef)
        return kNavMeshFailure | kNavMeshInvalidParam;
    if (!m_NavMesh->IsValidPolyRef(startRef))
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Test start poly vs filter
    if (!filter->PassFilter(m_NavMesh->GetPolyFlags(startRef)))
        return kNavMeshFailure;

    NavMeshStatus status = kNavMeshSuccess;

    // Here we assume all connected tiles are have the same world xform
    const NavMeshTile* startTile = m_NavMesh->GetTileByRef(startRef);
    const Rainbow::Vector3f localStart = WorldToTile(*startTile, startPos);
    const Rainbow::Vector3f localEnd = WorldToTile(*startTile, endPos);

    static const int kMaxStack = 48;
    NavMeshNode* stack[kMaxStack];
    int nstack = 0;

    nodePool->Clear();

    NavMeshNode* startNode = nodePool->GetNode(startRef);
    startNode->pidx = 0;
    startNode->cost = 0;
    startNode->total = 0;
    startNode->id = startRef;
    startNode->flags = NavMeshNode::kClosed;
    stack[nstack++] = startNode;

    Rainbow::Vector3f bestPos = localStart;
    float bestDist = FLT_MAX;
    NavMeshNode* bestNode = 0;


    // Search constraints
    Rainbow::Vector3f searchPos = Lerp(localStart, localEnd, 0.5f);
    float searchRadSqr = Rainbow::Sqr(Rainbow::Distance(localStart, localEnd) / 2.0f + 0.001f);

    Rainbow::Vector3f verts[kNavMeshVertsPerPoly];
    NavMeshPolyRef neighbours[kNavMeshVertsPerPoly * kMaxNeis];

    while (nstack)
    {
        // Pop front.
        NavMeshNode* curNode = stack[0];
        for (int i = 0; i < nstack - 1; ++i)
            stack[i] = stack[i + 1];
        nstack--;

        // Get poly and tile.
        // The API input has been checked already, skip checking internal data.
        const NavMeshPolyRef curRef = curNode->id;

        // Collect vertices.
        const int nverts = m_NavMesh->GetPolyGeometry(curRef, verts, neighbours, kMaxNeis);
        if (!nverts)
            continue;

        // If target is inside the poly, stop search.
        if (PointInPolygon2D(localEnd, verts, nverts))
        {
            bestNode = curNode;
            bestDist = 0;
            bestPos = localEnd;
            break;
        }

        // Find wall edges and find nearest point inside the walls.
        for (int i = 0, j = (int)nverts - 1; i < nverts; j = i++)
        {
            // Find links to neighbours.
            int nneis = 0;
            NavMeshPolyRef* neis = &neighbours[j * kMaxNeis];
            for (int k = 0; k < kMaxNeis; k++)
            {
                NavMeshPolyRef neiRef = neis[k];
                if (!neiRef)
                    break;

                // Skip if no node can be allocated.
                NavMeshNode* neighbourNode = nodePool->GetNode(neiRef);
                if (!neighbourNode)
                    continue;

                // Skip if already visited.
                if (neighbourNode->flags & NavMeshNode::kClosed)
                    continue;

                // Do not advance if the polygon is excluded by the filter.
                if (!filter->PassFilter(m_NavMesh->GetPolyFlags(neiRef)))
                    continue;

                // Skip the link if it is too far from search constraint.
                Rainbow::Vector3f left, right;
                const unsigned int curTileId = m_NavMesh->DecodePolyIdTile(curRef);
                const unsigned int neiTileId = m_NavMesh->DecodePolyIdTile(neiRef);
                if (curTileId == neiTileId)
                {
                    left = verts[j];
                    right = verts[i];
                }
                else
                {
                    NavMeshStatus status = GetPortalPoints(curRef, neiRef, &left, &right);
                    if (NavMeshStatusFailed(status))
                        continue;

                    left = WorldToTile(*startTile, left);
                    right = WorldToTile(*startTile, right);
                }

                float tseg;
                float distSqr = SqrDistancePointSegment2D(&tseg, searchPos, left, right);
                if (distSqr > searchRadSqr)
                    continue;

                // Mark as the node as visited and push to queue.
                if (nstack < kMaxStack)
                {
                    neighbourNode->pidx = nodePool->GetNodeIdx(curNode);
                    neighbourNode->flags |= NavMeshNode::kClosed;
                    stack[nstack++] = neighbourNode;
                }
                nneis++;
            }

            if (!nneis)
            {
                // Wall edge, calculate distance.
                const Rainbow::Vector3f& vj = verts[j];
                const Rainbow::Vector3f& vi = verts[i];
                float tseg;
                const float distSqr = SqrDistancePointSegment2D(&tseg, localEnd, vj, vi);
                if (distSqr < bestDist)
                {
                    // Update nearest distance.
                    bestPos = Lerp(vj, vi, tseg);
                    bestDist = distSqr;
                    bestNode = curNode;
                }
            }
        }
    }

    int n = 0;
    if (bestNode)
    {
        // Reverse the path.
        NavMeshNode* prev = 0;
        NavMeshNode* node = bestNode;
        do
        {
            NavMeshNode* next = nodePool->GetNodeAtIdx(node->pidx);
            node->pidx = nodePool->GetNodeIdx(prev);
            prev = node;
            node = next;
        }
        while (node);

        // Store result
        node = prev;
        do
        {
            visited[n++] = node->id;
            if (n >= maxVisitedSize)
            {
                status |= kNavMeshBufferTooSmall;
                break;
            }
            node = nodePool->GetNodeAtIdx(node->pidx);
        }
        while (node);
    }

    *resultPos = TileToWorld(*startTile, bestPos);

    *visitedCount = n;

    return status;
}

NavMeshStatus NavMeshQuery::GetPortalPoints(NavMeshPolyRef from, NavMeshPolyRef to, Rainbow::Vector3f* left, Rainbow::Vector3f* right) const
{
    Assert(m_NavMesh);

    // Handle ground->offmesh and offmesh->ground
    bool fromOffMeshCon = m_NavMesh->DecodePolyIdType(from) == kPolyTypeOffMeshConnection;
    bool toOffMeshCon = m_NavMesh->DecodePolyIdType(to) == kPolyTypeOffMeshConnection;
    if (fromOffMeshCon || toOffMeshCon)
    {
        for (const NavMeshLink* link = m_NavMesh->GetFirstLink(from); link != NULL; link = m_NavMesh->GetNextLink(link))
        {
            if (link->ref == to)
            {
                const int iv = link->edge;
                Assert(link->edge == 0 || link->edge == 1);

                Rainbow::Vector3f v0, v1;
                if (fromOffMeshCon)
                {
                    const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(from);
                    v0 = con->endPoints[iv].mapped[0];
                    v1 = con->endPoints[iv].mapped[1];
                }
                else if (toOffMeshCon)
                {
                    const OffMeshConnection* con = m_NavMesh->GetOffMeshConnection(to);
                    v0 = con->endPoints[iv].mapped[0];
                    v1 = con->endPoints[iv].mapped[1];
                }
                else
                {
                    AssertMsg(false, "Expected 'to' or 'from' to be an off-mesh connection.");
                }

                // Unpack portal limits.
                if (link->bmin != 0 || link->bmax != 255)
                {
                    const float s = 1.0f / 255.0f;
                    const float tmin = std::max(link->bmin * s, 0.0f);
                    const float tmax = std::min(link->bmax * s, 1.0f);
                    *left = Lerp(v0, v1, tmin);
                    *right = Lerp(v0, v1, tmax);
                }
                else
                {
                    *left = v0;
                    *right = v1;
                }

                return kNavMeshSuccess;
            }
        }
        return kNavMeshFailure;
    }

    // Handle ground->ground
    const NavMeshTile* fromTile = 0;
    const NavMeshPoly* fromPoly = 0;
    if (NavMeshStatusFailed(m_NavMesh->GetTileAndPolyByRef(from, &fromTile, &fromPoly)))
        return kNavMeshFailure | kNavMeshInvalidParam;

    const NavMeshTile* toTile = 0;
    const NavMeshPoly* toPoly = 0;
    if (NavMeshStatusFailed(m_NavMesh->GetTileAndPolyByRef(to, &toTile, &toPoly)))
        return kNavMeshFailure | kNavMeshInvalidParam;

    const unsigned int ip = GetPolyIndex(fromTile, fromPoly);
    const unsigned int firstLink = fromTile->polyLinks[ip];
    const NavMeshLink* link = NULL;
    for (const NavMeshLink* flink = m_NavMesh->GetLink(firstLink); flink != NULL; flink = m_NavMesh->GetNextLink(flink))
    {
        if (flink->ref == to)
        {
            link = flink;
            break;
        }
    }
    if (!link)
        return kNavMeshFailure | kNavMeshInvalidParam;

    // Find portal vertices.
    Assert(link->edge < fromPoly->vertCount);
    const int nextEdge = (link->edge + 1 == fromPoly->vertCount) ? 0 : link->edge + 1;
    const int v0 = fromPoly->verts[link->edge];
    const int v1 = fromPoly->verts[nextEdge];
    *left = fromTile->verts[v0];
    *right = fromTile->verts[v1];

    // If the link is at tile boundary, clamp the vertices to
    // the link width.
    if (link->side != 0xff)
    {
        // Unpack portal limits.
        if (link->bmin != 0 || link->bmax != 255)
        {
            const float s = 1.0f / 255.0f;
            const float tmin = std::max(link->bmin * s, 0.0f);
            const float tmax = std::min(link->bmax * s, 1.0f);
            *left = Lerp(fromTile->verts[v0], fromTile->verts[v1], tmin);
            *right = Lerp(fromTile->verts[v0], fromTile->verts[v1], tmax);
        }
    }

    *right = TileToWorld(*fromTile, *right);
    *left = TileToWorld(*fromTile, *left);

    return kNavMeshSuccess;
}

NavMeshStatus NavMeshQuery::Raycast(NavMeshPolyRef startRef, const Rainbow::Vector3f& startPos, const Rainbow::Vector3f& endPos,
    const QueryFilter* filter,
    NavMeshRaycastResult* result,
    NavMeshPolyRef* path, int* pathCount, const int maxPath) const
{
    Assert(m_NavMesh);

    // Clear output
    memset(result, 0, sizeof(*result));
    if (pathCount)
        *pathCount = 0;

    // Validate input
    const NavMeshTile* startTile = m_NavMesh->GetTileByRef(startRef);
    if (!startTile)
        return kNavMeshFailure | kNavMeshInvalidParam;

    const Rainbow::Vector3f localStartPos = WorldToTile(*startTile, startPos);
    const Rainbow::Vector3f localEndPos = WorldToTile(*startTile, endPos);

    NavMeshPolyRef curRef = startRef;
    NavMeshPolyRef lastPoly = startRef;
    NavMeshPolyRef validStartRef = ValidatePoly(startRef);
    Rainbow::Vector3f verts[kNavMeshVertsPerPoly];
    float t = 0;
    float costScale = 0;
    int n = 0;
    NavMeshStatus status = kNavMeshSuccess;

    while (curRef)
    {
        // Cast ray against current polygon.
        const int nverts = m_NavMesh->GetPolyGeometry(curRef, verts, NULL, 0);
        if (!nverts)
            break;

        float tmin, tmax;
        int segMin, segMax;
        if (!IntersectSegmentPoly2D(&tmin, &tmax, &segMin, &segMax, localStartPos, localEndPos, verts, nverts) && segMax != -1)
        {
            // Could not hit the polygon, keep the old t and report hit.

            // In some cases, when 'startPos' is collinear to an edge between two polygons, 'tmax' becomes a tiny
            // negative number and we would break here - causing the raycast to terminate prematurely.
            // We prevent that by ensuring tmax > 0 for the first polygon.
            if (curRef != startRef || tmax > 0)
                break;
        }

        // Keep track of furthest t so far.
        if (tmax > t)
        {
            const float dt = tmax - t;
            costScale += dt * filter->GetAreaCost(m_NavMesh->GetPolyArea(curRef));
            t = tmax;
        }

        lastPoly = curRef;

        // Store visited polygons.
        if (n < maxPath)
            path[n++] = curRef;
        else
            status |= kNavMeshBufferTooSmall;

        // Ray end is completely inside the polygon.
        if (segMax == -1)
        {
            t = 1.0f;
            break;
        }

        // Follow neighbours.
        NavMeshPolyRef nextRef = 0;

        for (const NavMeshLink* link = m_NavMesh->GetFirstLink(curRef); link != NULL; link = m_NavMesh->GetNextLink(link))
        {
            // Skip off-mesh connections.
            if (m_NavMesh->DecodePolyIdType(link->ref) == kPolyTypeOffMeshConnection)
                continue;

            // Find link which contains this edge.
            if ((int)link->edge != segMax)
                continue;

            // If the link is internal, accept and just return the ref.
            if (link->side == 0xff)
            {
                nextRef = ValidatePoly(link->ref);
                if (nextRef)
                    break;
            }

            // If the link is at tile boundary,

            // Check if the link spans the whole edge, and accept.
            if (link->bmin == 0 && link->bmax == 255)
            {
                nextRef = ValidatePoly(link->ref);
                if (nextRef)
                    break;
            }

            // Check for partial edge links.
            Assert(link->edge < nverts);
            const int nextEdge = (link->edge + 1 == nverts) ? 0 : link->edge + 1;
            const Rainbow::Vector3f& left = verts[link->edge];
            const Rainbow::Vector3f& right = verts[nextEdge];

            // Check that the intersection lies inside the link portal.
            if (link->side == 0 || link->side == 4)
            {
                // Calculate link size.
                const float s = 1.0f / 255.0f;
                float lmin = left.z + (right.z - left.z) * (link->bmin * s);
                float lmax = left.z + (right.z - left.z) * (link->bmax * s);
                if (lmin > lmax)
                    std::swap(lmin, lmax);

                // Find Z intersection.
                float z = localStartPos.z + (localEndPos.z - localStartPos.z) * tmax;
                if (z >= lmin && z <= lmax)
                {
                    nextRef = ValidatePoly(link->ref);
                    if (nextRef)
                        break;
                }
            }
            else if (link->side == 2 || link->side == 6)
            {
                // Calculate link size.
                const float s = 1.0f / 255.0f;
                float lmin = left.x + (right.x - left.x) * (link->bmin * s);
                float lmax = left.x + (right.x - left.x) * (link->bmax * s);
                if (lmin > lmax)
                    std::swap(lmin, lmax);

                // Find X intersection.
                float x = localStartPos.x + (localEndPos.x - localStartPos.x) * tmax;
                if (x >= lmin && x <= lmax)
                {
                    nextRef = ValidatePoly(link->ref);
                    if (nextRef)
                        break;
                }
            }
        }

        if (!nextRef || !filter->PassFilter(m_NavMesh->GetPolyFlags(nextRef)) || !validStartRef)
        {
            // No accessible neighbour, we hit a wall.

            // Calculate hit normal.
            const int a = segMax;
            const int b = segMax + 1 < nverts ? segMax + 1 : 0;
            const Rainbow::Vector3f& va = verts[a];
            const Rainbow::Vector3f& vb = verts[b];
            const float dx = vb.x - va.x;
            const float dz = vb.z - va.z;
            const float lenSq = (dx * dx + dz * dz);
            if (lenSq > 0)
            {
                const float s = 1.0f / sqrtf(lenSq);
                result->normal.x = dz * s;
                result->normal.y = 0.0f;
                result->normal.z = -dx * s;
            }

            // Advance ref - it allows us to obtain the blocking poly ref.
            curRef = nextRef;
            break;
        }

        // No hit, advance to neighbour polygon.
        curRef = nextRef;
    }

    result->t = t;
    result->totalCost = costScale * Rainbow::Distance(startPos, endPos);
    result->lastPoly = lastPoly;
    result->hitPoly = curRef;
    result->normal = TileToWorldVector(*startTile, result->normal);

    if (pathCount)
        *pathCount = n;

    return status;
}

NavMeshStatus NavMeshQuery::FindLocalNeighbourhood(NavMeshPolyRef startRef, const Rainbow::Vector3f& centerPos, const float radius,
    const QueryFilter* filter,
    NavMeshPolyRef* resultRef, NavMeshPolyRef* resultParent,
    int* resultCount, const int maxResult) const
{
    Assert(m_NavMesh);
    Assert(m_TinyNodePool);

    *resultCount = 0;

    // Validate input
    if (!startRef || !m_NavMesh->IsValidPolyRef(startRef))
        return kNavMeshFailure | kNavMeshInvalidParam;

    static const int kMaxStack = 48;
    NavMeshNode* stack[kMaxStack];
    int nstack = 0;

    m_TinyNodePool->Clear();

    NavMeshNode* startNode = m_TinyNodePool->GetNode(startRef);
    startNode->pidx = 0;
    startNode->id = startRef;
    startNode->flags = NavMeshNode::kClosed;
    stack[nstack++] = startNode;

    const float radiusSqr = Rainbow::Sqr(radius);

    Rainbow::Vector3f pa[kNavMeshVertsPerPoly];
    Rainbow::Vector3f pb[kNavMeshVertsPerPoly];

    NavMeshStatus status = kNavMeshSuccess;

    int n = 0;
    if (n < maxResult)
    {
        resultRef[n] = startNode->id;
        if (resultParent)
            resultParent[n] = 0;
        ++n;
    }
    else
    {
        status |= kNavMeshBufferTooSmall;
    }

    while (nstack)
    {
        // Pop front.
        NavMeshNode* curNode = stack[0];
        for (int i = 0; i < nstack - 1; ++i)
            stack[i] = stack[i + 1];
        nstack--;

        // Get poly and tile.
        // The API input has been checked already, skip checking internal data.
        const NavMeshPolyRef curRef = curNode->id;

        for (const NavMeshLink* link = m_NavMesh->GetFirstLink(curRef); link != NULL; link = m_NavMesh->GetNextLink(link))
        {
            NavMeshPolyRef neighbourRef = link->ref;

            // Skip off-mesh connections.
            if (m_NavMesh->DecodePolyIdType(neighbourRef) == kPolyTypeOffMeshConnection)
                continue;

            // Skip invalid neighbours.
            if (!neighbourRef)
                continue;

            // Skip if cannot alloca more nodes.
            NavMeshNode* neighbourNode = m_TinyNodePool->GetNode(neighbourRef);
            if (!neighbourNode)
                continue;
            // Skip visited.
            if (neighbourNode->flags & NavMeshNode::kClosed)
                continue;

            // Expand to neighbour
            const NavMeshTile* neighbourTile = 0;
            const NavMeshPoly* neighbourPoly = 0;
            m_NavMesh->GetTileAndPolyByRefUnsafe(neighbourRef, &neighbourTile, &neighbourPoly);

            // Do not advance if the polygon is excluded by the filter.
            if (!filter->PassFilter(neighbourPoly->flags))
                continue;

            // Find edge and calc distance to the edge.
            Rainbow::Vector3f va, vb;
            if (!GetPortalPoints(curRef, neighbourRef, &va, &vb))
                continue;

            // If the circle is not touching the next polygon, skip it.
            float tseg;
            float distSqr = SqrDistancePointSegment2D(&tseg, centerPos, va, vb);
            if (distSqr > radiusSqr)
                continue;

            // Mark node visited, this is done before the overlap test so that
            // we will not visit the poly again if the test fails.
            neighbourNode->flags |= NavMeshNode::kClosed;
            neighbourNode->pidx = m_TinyNodePool->GetNodeIdx(curNode);

            // Check that the polygon does not collide with existing polygons.

            // Collect vertices of the neighbour poly.
            const int npa = m_NavMesh->GetPolyGeometry(neighbourRef, pa, NULL, 0);

            bool overlap = false;
            for (int j = 0; j < n; ++j)
            {
                NavMeshPolyRef pastRef = resultRef[j];

                // Connected polys do not overlap.
                bool connected = false;
                for (const NavMeshLink* clink = m_NavMesh->GetFirstLink(curRef); clink != NULL; clink = m_NavMesh->GetNextLink(clink))
                {
                    if (clink->ref == pastRef)
                    {
                        connected = true;
                        break;
                    }
                }
                if (connected)
                    continue;
                // Get vertices and test overlap
                const int npb = m_NavMesh->GetPolyGeometry(pastRef, pb, NULL, 0);
                if (OverlapPolyPoly2D(pa, npa, pb, npb))
                {
                    overlap = true;
                    break;
                }
            }
            if (overlap)
                continue;

            // This poly is fine, store and advance to the poly.
            if (n < maxResult)
            {
                resultRef[n] = neighbourRef;
                if (resultParent)
                    resultParent[n] = curRef;
                ++n;
            }
            else
            {
                status |= kNavMeshBufferTooSmall;
            }

            if (nstack < kMaxStack)
            {
                stack[nstack++] = neighbourNode;
            }
        }
    }

    *resultCount = n;

    return status;
}

struct SegInterval
{
    NavMeshPolyRef ref;
    short tmin, tmax;
};

static int InsertInterval(SegInterval* ints, const int nints, const int maxInts,
    const short tmin, const short tmax, const NavMeshPolyRef ref)
{
    if (nints + 1 > maxInts)
        return maxInts;
    // Find insertion point.
    int idx = 0;
    while (idx < nints)
    {
        if (tmax <= ints[idx].tmin)
            break;
        idx++;
    }
    // Move current results.
    if (nints - idx)
        memmove(ints + idx + 1, ints + idx, sizeof(SegInterval) * (nints - idx));
    // Store
    ints[idx].ref = ref;
    ints[idx].tmin = tmin;
    ints[idx].tmax = tmax;
    return nints + 1;
}

NavMeshStatus NavMeshQuery::GetPolyWallSegments(NavMeshPolyRef ref, const QueryFilter* filter,
    Rainbow::Vector3f* segmentVerts, NavMeshPolyRef* segmentRefs, int* segmentCount,
    const int maxSegments) const
{
    Assert(m_NavMesh);

    if (m_NavMesh->DecodePolyIdType(ref) == kPolyTypeOffMeshConnection)
        return kNavMeshFailure | kNavMeshInvalidParam;

    const NavMeshTile* tile = 0;
    const NavMeshPoly* poly = 0;
    if (NavMeshStatusFailed(m_NavMesh->GetTileAndPolyByRef(ref, &tile, &poly)))
        return kNavMeshFailure | kNavMeshInvalidParam;

    int n = 0;
    static const int MAX_INTERVAL = 16;
    SegInterval ints[MAX_INTERVAL];

    const bool storePortals = segmentRefs != 0;

    NavMeshStatus status = kNavMeshSuccess;

    for (int i = 0, j = (int)poly->vertCount - 1; i < (int)poly->vertCount; j = i++)
    {
        // Skip non-solid edges.
        int nints = 0;
        if (poly->neis[j] & kNavMeshExtLink)
        {
            // Tile border.
            const unsigned int ip = GetPolyIndex(tile, poly);
            const unsigned int firstLink = tile->polyLinks[ip];
            for (const NavMeshLink* link = m_NavMesh->GetLink(firstLink); link != NULL; link = m_NavMesh->GetNextLink(link))
            {
                if (link->edge == j)
                {
                    if (link->ref != 0)
                    {
                        const NavMeshTile* neiTile = 0;
                        const NavMeshPoly* neiPoly = 0;
                        m_NavMesh->GetTileAndPolyByRefUnsafe(link->ref, &neiTile, &neiPoly);
                        if (neiPoly && filter->PassFilter(neiPoly->flags))
                        {
                            nints = InsertInterval(ints, nints, MAX_INTERVAL, link->bmin, link->bmax, link->ref);
                        }
                    }
                }
            }
        }
        else
        {
            // Internal edge
            NavMeshPolyRef ref = 0;
            if (poly->neis[j])
            {
                const unsigned int idx = (unsigned int)(poly->neis[j] - 1);
                ref = m_NavMesh->GetPolyRefBase(tile) | m_NavMesh->EncodeBasePolyId(kPolyTypeGround, idx);
                if (!storePortals && !filter->PassFilter(tile->polys[idx].flags))
                    ref = 0;
            }

            // If the edge leads to another polygon and portals are not stored, skip.
            if (ref != 0 && !storePortals)
                continue;

            if (n < maxSegments)
            {
                const Rainbow::Vector3f& vj = tile->verts[poly->verts[j]];
                const Rainbow::Vector3f& vi = tile->verts[poly->verts[i]];
                segmentVerts[2 * n + 0] = vj;
                segmentVerts[2 * n + 1] = vi;
                if (segmentRefs)
                    segmentRefs[n] = ref;
                n++;
            }
            else
            {
                status |= kNavMeshBufferTooSmall;
            }

            continue;
        }

        // Add sentinels
        nints = InsertInterval(ints, nints, MAX_INTERVAL, -1, 0, 0);
        nints = InsertInterval(ints, nints, MAX_INTERVAL, 255, 256, 0);

        // Store segments.
        const Rainbow::Vector3f& vj = tile->verts[poly->verts[j]];
        const Rainbow::Vector3f& vi = tile->verts[poly->verts[i]];
        for (int k = 1; k < nints; ++k)
        {
            // Portal segment.
            if (storePortals && ints[k].ref)
            {
                const float tmin = ints[k].tmin / 255.0f;
                const float tmax = ints[k].tmax / 255.0f;
                if (n < maxSegments)
                {
                    segmentVerts[2 * n + 0] = Lerp(vj, vi, tmin);
                    segmentVerts[2 * n + 1] = Lerp(vj, vi, tmax);
                    if (segmentRefs)
                        segmentRefs[n] = ints[k].ref;
                    n++;
                }
                else
                {
                    status |= kNavMeshBufferTooSmall;
                }
            }

            // Wall segment.
            const int imin = ints[k - 1].tmax;
            const int imax = ints[k].tmin;
            if (imin != imax)
            {
                const float tmin = imin / 255.0f;
                const float tmax = imax / 255.0f;
                if (n < maxSegments)
                {
                    segmentVerts[2 * n + 0] = Lerp(vj, vi, tmin);
                    segmentVerts[2 * n + 1] = Lerp(vj, vi, tmax);
                    if (segmentRefs)
                        segmentRefs[n] = 0;
                    n++;
                }
                else
                {
                    status |= kNavMeshBufferTooSmall;
                }
            }
        }
    }

    *segmentCount = n;

    return status;
}

NavMeshStatus NavMeshQuery::FindDistanceToWall(NavMeshPolyRef startRef, const Rainbow::Vector3f& centerPos,
    const QueryFilter* filter,
    float* hitDist, Rainbow::Vector3f* hitPos, Rainbow::Vector3f* hitNormal, unsigned int* hitFlags) const
{
    Assert(m_NavMesh);
    Assert(m_NodePool);
    Assert(m_OpenList);

    // Validate input
    const NavMeshTile* startTile = m_NavMesh->GetTileByRef(startRef);
    if (!startTile)
        return kNavMeshFailure | kNavMeshInvalidParam;

    const Rainbow::Vector3f localCenterPos = WorldToTile(*startTile, centerPos);

    m_NodePool->Clear();
    m_OpenList->Clear();

    NavMeshNode* startNode = m_NodePool->GetNode(startRef);
    startNode->pos = localCenterPos;
    startNode->pidx = 0;
    startNode->cost = 0;
    startNode->total = 0;
    startNode->id = startRef;
    startNode->flags = NavMeshNode::kOpen;
    m_OpenList->Push(startNode);

    float radiusSqr = FLT_MAX;

    NavMeshStatus status = kNavMeshSuccess;

    Rainbow::Vector3f verts[kNavMeshVertsPerPoly];
    NavMeshPolyRef neighbours[kNavMeshVertsPerPoly * kMaxNeis];
    Rainbow::Vector3f edgeNormal(0.0f, 0.0f, 0.0f);

    while (!m_OpenList->empty())
    {
        NavMeshNode* bestNode = m_OpenList->Pop();
        bestNode->flags &= ~NavMeshNode::kOpen;
        bestNode->flags |= NavMeshNode::kClosed;

        // Get poly and tile.

        // The API input has been checked already, skip checking internal data.
        const NavMeshPolyRef bestRef = bestNode->id;

        // Get parent poly and tile.
        NavMeshPolyRef parentRef = 0;
        if (bestNode->pidx)
            parentRef = m_NodePool->GetNodeAtIdx(bestNode->pidx)->id;

        // Collect vertices.
        const int nverts = m_NavMesh->GetPolyGeometry(bestRef, verts, neighbours, kMaxNeis);
        if (!nverts)
            continue;

        // Hit test walls, pruning the search distance too.
        for (int i = 0, j = nverts - 1; i < nverts; j = i++)
        {
            // Skip non-solid edges.
            bool solid = true;
            NavMeshPolyRef* neis = &neighbours[j * kMaxNeis];
            for (int k = 0; k < kMaxNeis; k++)
            {
                if (!neis[k])
                    break;
                if (filter->PassFilter(m_NavMesh->GetPolyFlags(neis[k])))
                {
                    // Edge is passable.
                    solid = false;
                    break;
                }
            }
            if (!solid)
                continue;

            // Calc distance to the edge.
            const Rainbow::Vector3f& vj = verts[j];
            const Rainbow::Vector3f& vi = verts[i];
            float tseg;
            float distSqr = SqrDistancePointSegment2D(&tseg, localCenterPos, vj, vi);

            // Edge is too far, skip.
            if (distSqr > radiusSqr)
                continue;

            // Hit wall, update radius.
            radiusSqr = distSqr;

            if (hitFlags)
                *hitFlags = m_NavMesh->GetPolyFlags(bestRef);

            // Calculate hit pos.
            *hitPos = Lerp(vj, vi, tseg);

            // Calculate the 2D edge normal
            edgeNormal.x = vi.z - vj.z;
            edgeNormal.z = vj.x - vi.x;
        }

        // Expand search
        for (int i = 0, j = nverts - 1; i < nverts; j = i++)
        {
            // Skip non-solid edges.
            NavMeshPolyRef* neis = &neighbours[j * kMaxNeis];
            for (int k = 0; k < kMaxNeis; k++)
            {
                if (!neis[k])
                    break;
                NavMeshPolyRef neighbourRef = neis[k];
                // Skip invalid neighbours and do not follow back to parent.
                if (!neighbourRef || neighbourRef == parentRef)
                    continue;
                // Skip off-mesh connections.
                if (m_NavMesh->DecodePolyIdType(neighbourRef) == kPolyTypeOffMeshConnection)
                    continue;
                // Calculate distance to the edge.
                const Rainbow::Vector3f& va = verts[j];
                const Rainbow::Vector3f& vb = verts[i];
                float tseg;
                float distSqr = SqrDistancePointSegment2D(&tseg, localCenterPos, va, vb);

                // If the circle is not touching the next polygon, skip it.
                if (distSqr > radiusSqr)
                    continue;

                if (!filter->PassFilter(m_NavMesh->GetPolyFlags(neighbourRef)))
                    continue;

                NavMeshNode* neighbourNode = m_NodePool->GetNode(neighbourRef);
                if (!neighbourNode)
                {
                    status |= kNavMeshOutOfNodes;
                    continue;
                }

                if (neighbourNode->flags & NavMeshNode::kClosed)
                    continue;

                // Cost
                if (neighbourNode->flags == NavMeshNode::kNew)
                {
                    neighbourNode->pos = Lerp(va, vb, 0.5f);
                }

                const float total = bestNode->total + Rainbow::Distance(bestNode->pos, neighbourNode->pos);

                // The node is already in open list and the new result is worse, skip.
                if ((neighbourNode->flags & NavMeshNode::kOpen) && total >= neighbourNode->total)
                    continue;

                neighbourNode->id = neighbourRef;
                neighbourNode->flags = (neighbourNode->flags & ~NavMeshNode::kClosed);
                neighbourNode->pidx = m_NodePool->GetNodeIdx(bestNode);
                neighbourNode->total = total;

                if (neighbourNode->flags & NavMeshNode::kOpen)
                {
                    m_OpenList->Modify(neighbourNode);
                }
                else
                {
                    neighbourNode->flags |= NavMeshNode::kOpen;
                    m_OpenList->Push(neighbourNode);
                }
            }
        }
    }

    // Calculate the 2D hit normal
    // Use the direction to the hit position to ensure smooth behaviour across concave corner points.
    // Use the navmesh edge normal when position mapped onto/outside the edge.
    Rainbow::Vector3f direction = localCenterPos - *hitPos;
    float dot2d = direction.x * edgeNormal.x + direction.z * edgeNormal.z;
    if (dot2d > k_Eps)
    {
        direction.y = 0.0f;
        *hitNormal = Normalize(direction);
    }
    else
    {
        *hitNormal = edgeNormal.GetNormalizedSafe();
    }

    *hitDist = Rainbow::Sqrt(radiusSqr);

    *hitNormal = TileToWorldVector(*startTile, *hitNormal);
    *hitPos = TileToWorld(*startTile, *hitPos);

    return status;
}

bool NavMeshQuery::IsInClosedList(NavMeshPolyRef ref) const
{
    if (!m_NodePool)
        return false;
    const NavMeshNode* node = m_NodePool->FindNavMeshNode(ref);
    return node && node->flags & NavMeshNode::kClosed;
}
