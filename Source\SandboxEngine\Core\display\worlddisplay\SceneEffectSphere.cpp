/**
* file : SceneEffectSphere
* func : 场景效果 （球体）
* by : chenzh
*/
#include "SceneEffectSphere.h"
#include "proto_common.h"
#include "world_types.h"
#include "world.h"
#include "SceneEffectLine.h"
#include "WorldRender.h"
#include "SceneEffectLine.h"
#include "SandboxPlane.h"
#include "CurveFace.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

SceneEffectSphere::SceneEffectSphere()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
}

SceneEffectSphere::SceneEffectSphere(const MNCoord3f& pos, const MNCoord3f& sphereDir, 
	float radius, int accuracyGrade, float ratio, CURVEFACEMTLTYPE mtltype)
	: m_sphereDir(sphereDir)
	, m_accuracyGrade(accuracyGrade)
{
	m_vCenter = pos;
	m_MtlType = mtltype;
	m_radius = radius;
	m_originRadius = radius;
	SetRatio(ratio);
}

SceneEffectSphere::SceneEffectSphere(const MNSandbox::MNCoord3f& center, float radius)
{
	m_drawpos = center;
	m_radius = radius;
}

SceneEffectSphere::~SceneEffectSphere()
{
}

// 设置比率
void SceneEffectSphere::SetRatio(float ratio)
{
	m_radius = m_originRadius * ratio;
	m_lenOffset = m_defaultOffset * ratio;
	m_drawpos = m_vCenter + m_sphereDir * (float)m_lenOffset;
	m_needRefreshFlag = true;
}

void SceneEffectSphere::SetSphere(MNCoord3f pos, MNCoord3f sphereDir)
{
	if (m_vCenter == pos && m_sphereDir == sphereDir)
	{
		return;
	}

	m_vCenter = pos;
	m_sphereDir = sphereDir;
	m_drawpos = m_vCenter + m_sphereDir * (float)m_lenOffset;
	m_needRefreshFlag = true;
}

bool SceneEffectSphere::RaySphere(MNSandbox::Ray& ray, MNCoord3f& targetPos)
{
	targetPos = MNCoord3f(0.0);
	auto dist = ray.CalcDistanceToPoint(m_drawpos);
	auto bRes = dist <= m_radius;

	if (bRes)
	{
		auto plane = Plane::CreateByPointAndVector(m_vCenter, m_sphereDir, ray.GetPos() - m_vCenter);
		if (!plane.IsValid())
		{
			return bRes;
		}
			
		ray.SetDir(plane.VectorProjection(ray.GetDir()));
		MNSandbox::Ray optRay(m_vCenter, m_sphereDir);
		optRay.IntersectRay(ray, &targetPos);
	}

	return bRes;
}

void SceneEffectSphere::SetRadius(float radius)
{
	if (m_radius == radius)
		return;

	m_radius = radius;
	m_originRadius = radius;
	m_needRefreshFlag = true;
}

void SceneEffectSphere::SetAccuracyGrade(int accuracyGrade)
{
	if (m_accuracyGrade == accuracyGrade)
		return;

	m_accuracyGrade = accuracyGrade;
	m_needRefreshFlag = true;
}

void SceneEffectSphere::SetColor(BlockVector color)
{
	if (m_Color.v == color.v)
	{
		return;
	}
	m_Color = color;
	m_needRefreshFlag = true;
}

void SceneEffectSphere::SetDefaultOffset(int defaultOffset) 
{
	if (m_defaultOffset == defaultOffset)
		return;


	m_defaultOffset = defaultOffset; 
	m_needRefreshFlag = true;
}

void SceneEffectSphere::OnClear()
{
	m_Vertexs.clear();
	m_Indices.clear();
}

void SceneEffectSphere::Refresh()
{
	if (m_needRefreshFlag)
	{
		CalcVertexsAndIndices((float)m_radius, m_accuracyGrade);
		m_needRefreshFlag = false; // 刷新过了
	}
}

void SceneEffectSphere::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow)
	{
		return;
	}

	if (m_Vertexs.empty())
		return;

	auto CurveRender = m_CurveFaces ? m_CurveFaces : pWorld->getRender()->getCurveRender();
	if (CurveRender)
	{
		CurveRender->addRect((int)m_MtlType, m_drawpos, m_Vertexs, m_Indices);	
	}
}

bool SceneEffectSphere::IsActive(World* pWorld) const
{
	return true;
}

void SceneEffectSphere::CalcVertexsAndIndices(float radius, int accuracyGrade)
{
	OnClear(); // 清理缓存

	if (radius <= 0 || accuracyGrade <= 0)
		return;

	float accuracyGrade_r = 1.0f / (float)accuracyGrade;
	float accuracyGradeR = HALF_PI * accuracyGrade_r;
	int vlen = accuracyGrade + 1;
	int vlen_xz = 1 + accuracyGrade * vlen;

	std::vector<float> rads(vlen);
	std::vector<float> rads_sin(vlen);
	std::vector<float> rads_cos(vlen);
	std::vector<float> len_sin(vlen);
	std::vector<float> len_cos(vlen); // len y
	std::vector<float> len_sinsin(vlen_xz); // len z
	std::vector<float> len_sincos(vlen_xz); // len x

	// 弧度预计算
	rads[0] = 0;
	rads[accuracyGrade] = HALF_PI;
	for (int i = 1; i < accuracyGrade; ++i)
	{
		rads[i] = accuracyGradeR * i;
	}

	// 三角函数预计算
	rads_cos[0] = rads_sin[accuracyGrade] = 1.0f;
	len_cos[0] = len_sin[accuracyGrade] = radius;
	rads_cos[accuracyGrade] = rads_sin[0] = 0.0f;
	len_cos[accuracyGrade] = len_sin[0] = 0.0f;
	for (int i = 1; i < accuracyGrade; ++i)
	{
		rads_sin[i] = rads_cos[accuracyGrade - i] = sin(rads[i]);
		len_sin[i] = len_cos[accuracyGrade - i] = rads_sin[i] * radius;
	}
	int idx = 0;
	len_sinsin[0] = 0.0f;
	len_sincos[0] = 0.0f;
	for (int i = 1; i <= accuracyGrade; ++i)
	{
		idx = vlen * (i - 1) + 1;
		len_sinsin[idx] = 0.0f;
		len_sincos[idx] = len_sin[i];
		len_sinsin[idx + accuracyGrade] = len_sin[i];
		len_sincos[idx + accuracyGrade] = 0.0f;
		for (int j = 1; j < accuracyGrade; ++j)
		{
			len_sinsin[idx + j] = len_sincos[idx + accuracyGrade - j] = len_sin[i] * rads_sin[j];
		}
	}

	// 计算顶点
	int vsize = 1 + (accuracyGrade * 4) * (accuracyGrade * 2 - 1) + 1; // 完整球体
	int ylen = 1 + (accuracyGrade * 2 - 1) + 1;
	int xzlen = accuracyGrade * 4;
	float uv_beg = 0.49f;
	float uv_uend = 0.51f, uv_vend = 0.50f;
	float uv_len = 0.02f; // 0.6 - 0.4
	float uv_len2 = uv_len * 0.5f;
	float uv_cell = uv_len * accuracyGrade_r;
	float uv_cell2 = uv_cell * 0.5f; // 1/2
	float uv_cell4 = uv_cell * 0.25f; // 1/4

	std::vector<Vector3f> vertexes(vsize);
	std::vector<Vector2f> uves(vsize);
	Vector3f temp;

	temp = Vector3f(len_sincos[0], len_cos[0], len_sinsin[0]);
	vertexes[0] = Vector3f(temp.x, temp.y, temp.z);
	uves[0] = Vector2f(uv_beg, uv_beg);
	vertexes[vsize - 1] = Vector3f(temp.x, -temp.y, temp.z);
	uves[vsize - 1] = Vector2f(uv_beg, uv_beg + uv_len);

	int v_idx = 0, v_ridx = 0;
	int y_idx = 0;
	int xz_idx = 0;
	float uv_udelta = 0.0f, uv_vdelta = 0.0f;
	for (int i = 1; i <= accuracyGrade; ++i)
	{
		v_idx = 1 + (i - 1) * xzlen;
		v_ridx = 1 + (accuracyGrade * 2 - i - 1) * xzlen;
		y_idx = i;
		xz_idx = 1 + (i - 1) * vlen;
		uv_udelta = uv_beg;
		uv_vdelta = uv_beg + i * uv_cell4;
		for (int j = 0; j <= accuracyGrade; ++j)
		{
			temp = Vector3f(len_sincos[xz_idx + j], len_cos[y_idx], len_sinsin[xz_idx + j]);
			uv_udelta += uv_cell4;
			idx = v_idx + j;
			vertexes[idx] = temp;
			uves[idx] = Vector2f(uv_udelta, uv_vdelta);
			idx = v_ridx + j;
			vertexes[idx] = Vector3f(temp.x, -temp.y, temp.z);
			uves[idx] = Vector2f(uv_udelta, uv_beg + uv_vend - uv_vdelta);

			idx = v_idx + accuracyGrade * 2 + j;
			vertexes[idx] = Vector3f(-temp.x, temp.y, -temp.z);
			uves[idx] = Vector2f(uv_udelta + uv_len2, uv_vdelta);
			idx = v_ridx + accuracyGrade * 2 + j;
			vertexes[idx] = Vector3f(-temp.x, -temp.y, -temp.z);
			uves[idx] = Vector2f(uv_udelta + uv_len2, uv_beg + uv_vend - uv_vdelta);

			if (j != 0 && j != accuracyGrade)
			{
				idx = v_idx + accuracyGrade * 2 - j;
				vertexes[idx] = Vector3f(-temp.x, temp.y, temp.z);
				uves[idx] = Vector2f(uv_beg + uv_beg + uv_len2 - uv_udelta, uv_vdelta);
				idx = v_ridx + accuracyGrade * 2 - j;
				vertexes[idx] = Vector3f(-temp.x, -temp.y, temp.z);
				uves[idx] = Vector2f(uv_beg + uv_beg + uv_len2 - uv_udelta, uv_beg + uv_vend - uv_vdelta);
				idx = v_idx + accuracyGrade * 4 - j;
				vertexes[idx] = Vector3f(temp.x, temp.y, -temp.z);
				uves[idx] = Vector2f(uv_beg + uv_uend - uv_udelta, uv_vdelta);
				idx = v_ridx + accuracyGrade * 4 - j;
				vertexes[idx] = Vector3f(temp.x, -temp.y, -temp.z);
				uves[idx] = Vector2f(uv_beg + uv_uend - uv_udelta, uv_beg + uv_vend - uv_vdelta);
			}
		}
	}

	// 生成顶点缓存
	m_Vertexs.resize(vsize);
	for (int i = 0; i < vsize; ++i)
	{
		SceneEffectLine::FillVertBuffer(m_Vertexs[i], vertexes[i], uves[i].x, uves[i].y, m_Color);
	}

	// 生成索引缓存
	v_idx = 1;
	for (int j = 0; j < xzlen - 1; ++j)
	{
		SceneEffectLine::PushIndexBuffer(m_Indices, v_idx + j, 0, (v_idx + j + 1));
	}
	SceneEffectLine::PushIndexBuffer(m_Indices, v_idx + (xzlen - 1), 0, v_idx);

	int v2_idx = 0;
	for (int i = 1; i < accuracyGrade * 2 - 1; ++i)
	{
		v_idx = 1 + (i - 1) * xzlen;
		v2_idx = 1 + i * xzlen;
		for (int j = 0; j < xzlen - 1; ++j)
		{
			SceneEffectLine::PushIndexBuffer(m_Indices, v2_idx + j, v_idx + j, v_idx + j + 1);
			SceneEffectLine::PushIndexBuffer(m_Indices, v_idx + j + 1, v2_idx + j + 1, v2_idx + j);
		}
		SceneEffectLine::PushIndexBuffer(m_Indices, v2_idx + xzlen - 1, v_idx + xzlen - 1, v_idx);
		SceneEffectLine::PushIndexBuffer(m_Indices, v_idx, v2_idx, v2_idx + xzlen - 1);
	}

	v_idx = 1 + (accuracyGrade * 2 - 2) * xzlen;
	for (int j = 0; j < xzlen - 1; ++j)
	{
		SceneEffectLine::PushIndexBuffer(m_Indices, v_idx + j, (v_idx + j + 1), vsize - 1);
	}
	SceneEffectLine::PushIndexBuffer(m_Indices, v_idx + (xzlen - 1), v_idx, vsize - 1);
}
