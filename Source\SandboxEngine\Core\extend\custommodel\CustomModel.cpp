
#include "CustomModel.h"
#include "CustomModel_generated.h"
#include "GameNetManager.h"
#include "genCustomModel.h"
#include "IRecordInterface.h"
#include "OgreCrc32.h"
#include "OgreMD5.h"
#include "OgreUtils.h"
#include "ClientInfoProxy.h"
#include "DefManagerProxy.h"
#include "IMiniDeveloperProxy.h"
#include "ClientActorHelper.h"
#include "container_world.h"
#ifdef UGC_PROFILE_ENABLED
#include "Mesh/LegacyOgreVertexIndexData.h"
#include "Mesh/LegacyOgreSkeletonData.h"
#include "Mesh/LegacyOgreAnimTrackBone.h"
#include "Mesh/LegacyOgreAnimTrackMaterialParam.h" 
#endif // UGC_PROFILE_ENABLED

#ifdef __PC_LINUX__
#include <sys/mman.h>
#include <sys/stat.h>
#endif
#include "File/FileManager.h"
#include "blocks/BlockMaterialMgr.h"
#include "ResourceCenter.h"
#include "LegacyCompress.h"
#include "OgreModel.h"
#include "IClientPlayer.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
extern unsigned int CalItemCrc(const ItemDef *itemdef, const BlockDef *blockdef);

#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#define O_BINARY (0)
#endif

CustomModel::CustomModel() :
	m_ProductContainerCoord(0, -1, 0), m_Type(0), m_Id(-1), m_DirType(0), m_HasCustFlag(false),
	m_BlocksCrc(0), m_RelativePosCrc(0), m_MD5(NULL), m_AuthUin(-1), m_bLeaveworlddel(false),
	m_MinPos(0, 0, 0), m_MaxPos(0, 0, 0), m_Box(0, 0, 0), m_EditorDim(0, 0, 0)
{
	for (int i = 0; i<MAX_MSEH_TYPE; i++)
	{
		m_ModelData[i] = nullptr; 
		m_ModelRPosAndColorOK[i] = false;
	}
}


CustomModel::CustomModel(int id, std::string filename) : m_ProductContainerCoord(0, -1, 0), m_strKey(filename),
m_Type(0), m_Id(id), m_DirType(0), m_HasCustFlag(false),
m_BlocksCrc(0), m_MinPos(0, 0, 0), m_MaxPos(0, 0, 0), m_Box(0, 0, 0),
m_RelativePosCrc(0),
m_MD5(NULL), m_AuthUin(-1), m_bLeaveworlddel(false),
m_EditorDim(0,0,0)
{
	m_EditorBlocks.clear();
	m_vEditorRelativeBitPos.clear();
	m_BlocksPos.clear();
	for (int i = 0; i<MAX_MSEH_TYPE; i++)
	{
		m_ModelData[i] = nullptr; 
		m_ModelRPosAndColorOK[i] = false;
	}
}

CustomModel::CustomModel(const CustomModel* cm) : CustomModel()
{
	m_EditorBlocks.clear();
	m_vEditorRelativeBitPos.clear();
	m_BlocksPos.clear();
	const vector<Block>& vBlocks = cm->m_Blocks;
	const vector<WCoord>& vRelativePos = cm->m_RelativePos;
	unsigned long blockCrc = Crc32Calc(&vBlocks[0], sizeof(Block) * vBlocks.size());
	unsigned long relativePosCrc = Crc32Calc(&vRelativePos[0], sizeof(WCoord) * vRelativePos.size());
    WCoord dim = WCoord(20, 20, 20);
	setData(vBlocks, vRelativePos, blockCrc, relativePosCrc,
		cm->getModelName(), cm->getModelDesc(), dim, BLOCK_MODEL, 0);

	for (int i = 0; i < MAX_MSEH_TYPE; i++)
	{
		m_ModelDataRPos[i].assign(cm->m_ModelDataRPos[i].begin(), cm->m_ModelDataRPos[i].end());
		m_ModelDataColors[i].assign(cm->m_ModelDataColors[i].begin(), cm->m_ModelDataColors[i].end());
		m_ModelDataHex[i] = cm->m_ModelDataHex[i];
		m_ModelRPosAndColorOK[i] = cm->m_ModelRPosAndColorOK[i];
	}
}

CustomModel::~CustomModel()
{
	for (int i = 0; i < MAX_MSEH_TYPE; i++)
	{
		m_ModelData[i] = nullptr;
	}
	OGRE_DELETE_ARRAY(m_MD5);
}

char *CustomModel::getMd5(int uin)
{
	if (!m_MD5)
	{
		m_MD5 = new char[16];
		ChunkRandGen randgen(uin ^ 0x429f3387);
		char tmpbuf[256];
		//char md5[16];

		unsigned int r1 = randgen.get();
		unsigned int r2 = randgen.get();
		unsigned int r3 = randgen.get();

		sprintf(tmpbuf, "%u%u%u", r1, r2, r3);
		MINIW::Md5Calc(m_MD5, tmpbuf, strlen(tmpbuf));

		return m_MD5;
	}
	
	return m_MD5;
}

void FastUnmapFile(void* ptr, int len) {
// #ifdef __PC_LINUX__
// 	if (ptr) {
// 		munmap(ptr, len);
// 	}
// #else
	free(ptr);
//#endif
}

void* FastMapWholeFile(const char* path, int& datalen)
{
	core::string fullpath;
	GetFileManager().ToWritePathFull(path, fullpath);

#ifdef __PC_LINUX__
	int fd = open(fullpath.c_str(), O_RDWR);
	if (fd < 0)
		return NULL;
	struct stat sb;
	int ret = fstat(fd, &sb);
	if (ret < 0) {
		close(fd);
		return NULL;
	}
	int fileSize = sb.st_size;
	void* pAddr = mmap(NULL, fileSize, PROT_READ, MAP_PRIVATE, fd, 0);
	if (!pAddr) {
		close(fd);
		return NULL;
	}
	datalen = fileSize;

	void* buf = malloc(fileSize);
	if (buf == NULL)
	{
		munmap(pAddr, fileSize);
		return NULL;
	}
	memcpy(buf, pAddr, fileSize);
	
	munmap(pAddr, fileSize);
	return buf;
#else
	FileAutoClose fp(fullpath, O_RDONLY | O_BINARY);
	if (fp.isNull())
		return NULL;

	datalen = fp.fileSize();
	if (datalen == 0) return NULL;

	void* buf = malloc(datalen);
	if (buf == NULL) return NULL;

	fp.seek(0, SEEK_SET);
	if (!fp.read(buf, datalen))
	{
		free(buf);
		return NULL;
	}
	return buf;
#endif
}
//2022117  codeby:liushuxin 云服下加载微缩资源，只需要关注m_Bos, m_Type, m_ModelName, m_ModelDesc, 用于GetDefManagerProxy()->addDefByCustomModel参数调用
bool CustomModel::loadCloudServer(long long owid, std::string filename, int realowneruin/* =0 */, std::string modroot /* = "" */, bool ignorecheck /*= false*/, int specialType/* = NORMAL_WORLD*/)
{
	char path[256];
	if (!modroot.empty())
	{
		sprintf(path, "%s/resource/custommodel/%s.cm", modroot.c_str(), filename.c_str());
	}
	else
	{
		if (owid < 0)
			if (owid == -2)
				sprintf(path, "data/custommodel_equip/%s.cm", filename.c_str());
			else if (owid == -3)
				sprintf(path, "data/custommodel_pre/%s.cm", filename.c_str());
			else
				sprintf(path, "data/custommodel/%s.cm", filename.c_str());
		else
		{
			std::string rootpath = GetWorldRootBySpecialType(specialType);

			sprintf(path, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, filename.c_str());
		}
	}

	int buflen = 0;
	//void *buf = ReadWholeFile(path, buflen);
	//20211125 使用mmap提升加载文件速度 codeby:liushuxin
	void *buf = FastMapWholeFile(path, buflen);
	if (buf == NULL)
		return false;

	uint8_t *custBuf = (uint8_t *)buf;

	if (buflen >= 8)
	{
		auto resInfo = ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename, owid);

		char name[5];
		memcpy(name, (const char *)buf, 5);
		name[4] = 0;

		int authuin;
		memcpy(&authuin, (const char *)buf + 4, 4);

		if (!ignorecheck && !GetClientInfoProxy()->IgnorCMCheck())
		{
			if (!resInfo)
			{

#ifdef  IWORLD_SERVER_BUILD 
				if (GetClientInfoProxy()->getUin() == authuin || (realowneruin > 0 && realowneruin == authuin) || owid == -2)
#else
				if (GetClientInfoProxy()->getUin() == authuin
					|| (realowneruin > 0 && realowneruin == authuin)
					|| owid == -2
					|| (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), authuin))
					|| GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
#endif
				{//模型作者是本人或者模型作者是地图真实作者
					if (strcmp(name, "CUST") == 0)
					{
						//文件头是CUST
						char* md5 = getMd5(authuin);

						custBuf = (uint8_t*)buf + 8;
						for (int i = 0; i < buflen - 8; i++)
						{
							custBuf[i] = custBuf[i] ^ md5[i % 16];
						}

						buflen = buflen - 8;
						m_HasCustFlag = true;
					}
				}

			}
			else
			{
				//不是地图作者下载的资源，不允许加载
				if ((realowneruin != resInfo->download_uin) &&
					(resInfo->download_uin != GetClientInfoProxy()->getUin()) &&
					(GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false))
				{
					//free(buf);
					FastUnmapFile(buf, buflen);
					return false;
				}
				else
				{
					if (strcmp(name, "CUST") == 0)
					{
						//文件头是CUST
						char* md5 = getMd5(authuin);

						custBuf = (uint8_t*)buf + 8;
						for (int i = 0; i < buflen - 8; i++)
						{
							custBuf[i] = custBuf[i] ^ md5[i % 16];
						}

						buflen = buflen - 8;
						m_HasCustFlag = true;
					}
				}
			}
		}
		else
		{
			if (strcmp(name, "CUST") == 0)
			{
				//文件头是CUST
				char* md5 = getMd5(authuin);

				custBuf = (uint8_t*)buf + 8;
				for (int i = 0; i < buflen - 8; i++)
				{
					custBuf[i] = custBuf[i] ^ md5[i % 16];
				}

				buflen = buflen - 8;
				m_HasCustFlag = true;
			}
		}
	}

	flatbuffers::Verifier verifier(custBuf, buflen);
	if (!FBSave::VerifyCustomModelBuffer(verifier))
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	const FBSave::CustomModel *custommodel = FBSave::GetCustomModel(custBuf);
	if (custommodel == NULL)
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	m_Blocks.clear();
	m_vRelativeBitPos.clear();
	m_strKey = filename;

	m_MinPos = Rainbow::Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	m_MaxPos = Rainbow::Vector3f(0, 0, 0);

	if (custommodel->blocks() || custommodel->bitblocks())
	{
		bool bUseBitPos = (custommodel->bitblocks() != NULL);
		int iBlockSize = (bUseBitPos ? custommodel->bitblocks()->size() : custommodel->blocks()->size());
		int x, y, z, iBitPos;
		for (int i = 0; i < iBlockSize; i++)
		{
			if (bUseBitPos) {
				auto src = custommodel->bitblocks()->Get(i);
				m_Blocks.push_back(src->block());
				iBitPos = src->bitpos();
			}
			else {
				auto src = custommodel->blocks()->Get(i);
				m_Blocks.push_back(src->block());
				iBitPos = covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z());
			}
			m_vRelativeBitPos.push_back(iBitPos);

			x = getRealPosByBit(iBitPos, 0);
			y = getRealPosByBit(iBitPos, 1);
			z = getRealPosByBit(iBitPos, 2);
			if (x < m_MinPos.x)
				m_MinPos.x = (float)x;
			if (y < m_MinPos.y)
				m_MinPos.y = (float)y;
			if (z < m_MinPos.z)
				m_MinPos.z = (float)z;
			if (x > m_MaxPos.x)
				m_MaxPos.x = (float)x;
			if (y > m_MaxPos.y)
				m_MaxPos.y = (float)y;
			if (z > m_MaxPos.z)
				m_MaxPos.z = (float)z;
		}
	}

	m_Box = m_MaxPos + Rainbow::Vector3f(1, 1, 1) - m_MinPos;

	m_Type = BLOCK_MODEL;

	if (custommodel->name())
	{
		m_ModelName = custommodel->name()->c_str();
		std::string transKey(m_strKey);
		transKey.append("_name");
		if (modroot.empty() && owid > 0) {
			//微缩模型  TRANSLATE_TYPE
			m_ModelName = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)2, transKey, m_ModelName);
		}
	}

	if (custommodel->desc())
	{
		m_ModelDesc = custommodel->desc()->c_str();
		std::string transDesc(m_strKey);
		transDesc.append("_desc");
		if (modroot.empty() && owid > 0) 
		{
			//微缩模型  TRANSLATE_TYPE
			m_ModelDesc = GetDefManagerProxy()->getTransStrByKey((TRANSLATE_TYPE)2, transDesc, m_ModelDesc);
		}
	}

	if (custommodel->modeltype())
		m_Type = custommodel->modeltype();

	if (m_Type == BLOCK_MODEL && owid >= 0)
		genGeomTemplate();
	//free(buf);
	FastUnmapFile(buf, buflen);
	return true;
}

#ifdef UGC_PROFILE_ENABLED
size_t CustomModel::getMemStatForUGC()
{
	size_t iSize = 0;
	iSize += sizeof(WCoord);
	iSize += sizeof(Rainbow::Vector3f) * 3;
	iSize += m_Blocks.size() * sizeof(Block);
	iSize += m_RelativePos.size() * sizeof(WCoord);
	iSize += m_vRelativeBitPos.size() * sizeof(int);
	iSize += (m_strKey.size() + m_ModelName.size() + m_ModelDesc.size()) * sizeof(char);
	iSize += sizeof(int) * 6;
	if (m_MD5)
	{
		iSize += 16 * sizeof(char);
	}

	for (size_t i = 0; i < MAX_MSEH_TYPE; ++i)
	{
		if (m_ModelData[i])
		{
			iSize += getMemStatModelDataForUGC(m_ModelData[i]);
		}
	}

	return iSize;
}
#endif // UGC_PROFILE_ENABLED

bool CustomModel::loadInternal(long long owid, std::string filename, int realowneruin/* =0 */, std::string modroot /* = "" */, bool ignorecheck /*= false*/, int specialType/* = NORMAL_WORLD*/)
{
	char path[256];
	if (!modroot.empty())
	{
		sprintf(path, "%s/resource/custommodel/%s.cm", modroot.c_str(), filename.c_str());
	}
	else
	{
		if (owid < 0)
			if (owid == -2)
				sprintf(path, "data/custommodel_equip/%s.cm", filename.c_str());
			else if (owid == -3)
				sprintf(path, "data/custommodel_pre/%s.cm", filename.c_str());
			else
				sprintf(path, "data/custommodel/%s.cm", filename.c_str());
		else
		{
			std::string rootpath = GetWorldRootBySpecialType(specialType);
			sprintf(path, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, filename.c_str());
		}
	}

	int buflen = 0;
	//void *buf = ReadWholeFile(path, buflen);
	//20211125 使用mmap提升加载文件速度 codeby:liushuxin
	void *buf = FastMapWholeFile(path, buflen);
	if (buf == NULL) 
		return false;

	uint8_t *custBuf = (uint8_t *)buf;

	if (buflen >= 8)
	{
		auto resInfo =  ResourceCenter::GetInstancePtr()->findDownloadItemInfo(filename, owid);

		char name[5];
		memcpy(name, (const char *)buf, 5);
		name[4] = 0;

		int authuin;
		memcpy(&authuin, (const char *)buf + 4, 4);

		if(!ignorecheck)
		{
			if (!resInfo)
			{

#ifdef  IWORLD_SERVER_BUILD 
				if (GetClientInfoProxy()->getUin() == authuin || (realowneruin > 0 && realowneruin == authuin) || owid == -2)
#else
				if (GetClientInfoProxy()->getUin() == authuin
					|| (realowneruin > 0 && realowneruin == authuin)
					|| owid == -2
					|| (GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), authuin))
					|| GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
#endif
				{//模型作者是本人或者模型作者是地图真实作者
					if (strcmp(name, "CUST") == 0)
					{
						//文件头是CUST
						char* md5 = getMd5(authuin);

						custBuf = (uint8_t*)buf + 8;
						for (int i = 0; i < buflen - 8; i++)
						{
							custBuf[i] = custBuf[i] ^ md5[i % 16];
						}

						buflen = buflen - 8;
						m_HasCustFlag = true;
					}
				}

			}
			else
			{
				//不是地图作者下载的资源，不允许加载
				if ((realowneruin != resInfo->download_uin) &&
					(resInfo->download_uin != GetClientInfoProxy()->getUin()) && 
					(GetWorldManagerPtr() && GetWorldManagerPtr()->IsUinInHistoryList(GetClientInfoProxy()->getCurWorldId(), resInfo->download_uin) == false))
				{
					//free(buf);
					FastUnmapFile(buf, buflen);
					return false;
				}
				else
				{
					if (strcmp(name, "CUST") == 0)
					{
						//文件头是CUST
						char* md5 = getMd5(authuin);

						custBuf = (uint8_t*)buf + 8;
						for (int i = 0; i < buflen - 8; i++)
						{
							custBuf[i] = custBuf[i] ^ md5[i % 16];
						}

						buflen = buflen - 8;
						m_HasCustFlag = true;
					}
				}
			}
		}
		else
		{
			if (strcmp(name, "CUST") == 0)
			{
				//文件头是CUST
				char* md5 = getMd5(authuin);

				custBuf = (uint8_t*)buf + 8;
				for (int i = 0; i < buflen - 8; i++)
				{
					custBuf[i] = custBuf[i] ^ md5[i % 16];
				}

				buflen = buflen - 8;
				m_HasCustFlag = true;
			}
		}
	}

	flatbuffers::Verifier verifier(custBuf, buflen);
	if (!FBSave::VerifyCustomModelBuffer(verifier))
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	const FBSave::CustomModel *custommodel = FBSave::GetCustomModel(custBuf);
	if (custommodel == NULL)
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	m_Materials.clear();
	m_Blocks.clear();
	m_RelativePos.clear();
	m_vRelativeBitPos.clear();
	m_strKey = filename;

	m_MinPos= Rainbow::Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	m_MaxPos = Rainbow::Vector3f(0, 0, 0);

	if (custommodel->blocks() || custommodel->bitblocks())
	{
		bool bUseBitPos = (custommodel->bitblocks() != NULL);
		int iBlockSize = (bUseBitPos ? custommodel->bitblocks()->size() : custommodel->blocks()->size());
		int x, y, z, iBitPos;
		for (int i = 0; i < iBlockSize; i++)
		{
			if (bUseBitPos) {
				auto src = custommodel->bitblocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = src->bitpos();
			}
			else {
				auto src = custommodel->blocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z());
			}
			//auto src = custommodel->blocks()->Get(i);
			//m_Blocks.push_back(src->block());
			//iBitPos = src->bitpos() == -1 ? covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z()) : src->bitpos();

			m_vRelativeBitPos.push_back(iBitPos);
			x = getRealPosByBit(iBitPos, 0);
			y = getRealPosByBit(iBitPos, 1);
			z = getRealPosByBit(iBitPos, 2);
			if (x < m_MinPos.x)
				m_MinPos.x = (float)x;
			if (y < m_MinPos.y)
				m_MinPos.y = (float)y;
			if (z < m_MinPos.z)
				m_MinPos.z = (float)z;
			if (x > m_MaxPos.x)
				m_MaxPos.x = (float)x;
			if (y > m_MaxPos.y)
				m_MaxPos.y = (float)y;
			if (z > m_MaxPos.z)
				m_MaxPos.z = (float)z;

			Block block = m_Blocks[i];
			int blockid = block.getResID();
			if (blockid > 0)
			{
				addMaterial(blockid);
			}
		}
	}

	m_Box = m_MaxPos + Rainbow::Vector3f(1, 1, 1) - m_MinPos;

	m_Type = BLOCK_MODEL;
	//if (custommodel->type())
		//m_Type = custommodel->type();

	if (custommodel->blockscrc())
		m_BlocksCrc = (unsigned long)custommodel->blockscrc();

	if (custommodel->relativeposcrc())
		m_RelativePosCrc = (unsigned long)custommodel->relativeposcrc();

	if (custommodel->name())
	{
		m_ModelName = custommodel->name()->c_str();
		std::string transKey(m_strKey);
		transKey.append("_name");
		if (modroot.empty() && owid > 0) 
		{
			//微缩模型  TRANSLATE_TYPE
			if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy())
			{
				m_ModelName = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getTransStrByKey(2, transKey, m_ModelName);
			}
		}
	}

	if (custommodel->desc())
	{
		m_ModelDesc = custommodel->desc()->c_str();
		std::string transDesc(m_strKey);
		transDesc.append("_desc");
		if (modroot.empty() && owid > 0)
		{
			//微缩模型  TRANSLATE_TYPE
			if (MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy())
			{
				m_ModelDesc = MNSandbox::IMiniDeveloperProxy::getMiniDeveloperProxy()->getTransStrByKey(2, transDesc, m_ModelDesc);
			}
		}
	}

	m_DirType = DIR_NEG_Z;
	if (custommodel->dirtype())
		m_DirType = custommodel->dirtype();

	if (custommodel->modeltype())
		m_Type = custommodel->modeltype();

	m_AuthUin = custommodel->authuin();

	auto t = custommodel->time();

	//free(buf);
	FastUnmapFile(buf, buflen);
	return true;
}

bool CustomModel::load(long long owid, std::string filename, int realowneruin/*=0*/, std::string modroot /*= ""*/, bool ignorecheck /*= false*/, int specialType /*= NORMAL_WORLD*/)
{
	 bool ret = loadInternal(owid, filename, realowneruin, modroot, ignorecheck, specialType);

	 if (ret && m_Type == BLOCK_MODEL && owid >= 0)
		 genGeomTemplate();

	 return ret;
}

bool CustomModel::loadInJobThread(long long owid, std::string filename, int realowneruin /*= 0*/, std::string modroot /*= ""*/, bool ignorecheck /*= false*/, int specialType /*= NORMAL_WORLD*/, void** ppGeom/* = nullptr*/)
{
	bool ret = loadInternal(owid, filename, realowneruin, modroot, ignorecheck, specialType);
	if (ppGeom)
	{
		*ppGeom = nullptr;
	}
	if (ret && m_Type == BLOCK_MODEL && owid >= 0) {
		*ppGeom = genGeomTemplateInJobThread();
	}
		
	return ret;
}

bool CustomModel::save(long long owid, std::string filename, std::string authname, int authuin, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	//注释把微雕文件放在插件库里的逻辑
	/*char dir[256];
	sprintf(dir, "%s/w%lld/mods/mapdefault_0.1_2b96d66d-509b-475b-96fe-7fc131bc2b90/resource/custommodels/", rootpath.c_str(), owid);
	if (!GetFileManager().IsFileExistWritePath(dir))
	{
		GetFileManager().CreateWritePathDir(dir);
	}

	char path[256];
	sprintf(path, "%s/w%lld/mods/mapdefault_0.1_2b96d66d-509b-475b-96fe-7fc131bc2b90/resource/custommodels/%s.cm", rootpath.c_str(), owid, filename.c_str());*/

	char dir[256];
	char path[256];
	if (owid > 0)
	{
		sprintf(dir, "%s/w%lld/custommodel/", rootpath.c_str(), owid);
		if (!GetFileManager().IsFileExistWritePath(dir))
		{
			GetFileManager().CreateWritePathDir(dir);
		}

		sprintf(path, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, filename.c_str());
	}
	else
	{
		sprintf(dir, "data/custommodel/");
		if (!GetFileManager().IsFileExistWritePath(dir))
		{
			GetFileManager().CreateWritePathDir(dir);
		}

		sprintf(path, "data/custommodel/%s.cm", filename.c_str());
	}


	flatbuffers::FlatBufferBuilder builder;

#if 0
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BPBlockData>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::BPBlockData>> blockdatas;
	auto iter = m_Blocks.begin();
	for (int i=0; i < m_Blocks.size(); i++)
	{
		/*auto relativePos = WCoordToCoord3(m_RelativePos[i]);
		blockdatas.push_back(FBSave::CreateBPBlockData(builder, m_Blocks[i].getAll(), &relativePos));*/
		blockdatas.push_back(FBSave::CreateBPBlockData(builder, m_Blocks[i].getAll(), 0, m_vRelativeBitPos[i]));
	}
	blockdatasoffset = builder.CreateVector(blockdatas);
#else
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BPBlockDataBit>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::BPBlockDataBit>> blockdatas;
	auto iter = m_Blocks.begin();
	for (int i = 0; i < (int)m_Blocks.size(); i++)
	{
		blockdatas.push_back(FBSave::CreateBPBlockDataBit(builder, m_Blocks[i].getOriginData(), m_vRelativeBitPos[i], m_Blocks[i].getDataEx()));
	}
	blockdatasoffset = builder.CreateVector(blockdatas);
#endif

	unsigned long t = (unsigned long)time(NULL);
	auto fbsCm = FBSave::CreateCustomModel(builder, 0, m_Type, builder.CreateString(authname), authuin,
		m_BlocksCrc, m_RelativePosCrc, builder.CreateString(m_ModelName), builder.CreateString(m_ModelDesc), m_DirType, m_Type, blockdatasoffset, t);
	builder.Finish(fbsCm);


	uint8_t *data = builder.GetBufferPointer();
	char *md5 = getMd5(authuin);
	int size = builder.GetSize();
	for (int i = 0; i < size; i++)
	{
		data[i] = data[i] ^ md5[i % 16];
	}

	char tmppath[256];
	sprintf(tmppath, "%s.tmp", path);
	core::string fullpath;
	GetFileManager().ToWritePathFull(tmppath,fullpath);

	FileAutoClose fp(fullpath, O_CREAT|O_WRONLY|O_TRUNC|O_BINARY);
	if (fp.isNull()) return false;

	//写入文件头CUST+作者uin
	if (!fp.write("CUST", sizeof(char) * 4))
	{
		
		return false;
	}
		
	if (!fp.seek(4, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return false;
	}

	if (!fp.write(&authuin, sizeof(int)))
	{
		fp.sync();
		fp.close();
		return false;
	}
		
	if (!fp.seek(8, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return false;
	}

	//写入微雕模型信息
	if (!fp.write(data, size))
	{
		fp.sync();
		fp.close();
		return false;
	}		

	fp.sync();
	fp.close();

	GetFileManager().RenameWritePathFile(tmppath, path);

	//==========================================================//
	int buflen = 0;
	void* buf = FastMapWholeFile(path, buflen);
	uint8_t* custBuf = (uint8_t*)buf;
	char name[5];
	memcpy(name, (const char*)buf, 5);
	name[4] = 0;
	//int authuin;
	memcpy(&authuin, (const char*)buf + 4, 4);
	if (strcmp(name, "CUST") == 0)
	{
		//文件头是CUST
		char* md5 = getMd5(authuin);

		custBuf = (uint8_t*)buf + 8;
		for (int i = 0; i < buflen - 8; i++)
		{
			custBuf[i] = custBuf[i] ^ md5[i % 16];
		}

		buflen = buflen - 8;
		m_HasCustFlag = true;
	}
	bool ok;
	flatbuffers::Verifier verifier(custBuf, buflen);
	ok = FBSave::VerifyCustomModelBuffer(verifier);
	FastUnmapFile(buf, buflen);
	//pengdapu test code
	//CustomModel* cm1 = SANDBOX_NEW(CustomModel);
	//ok = cm1->load(owid, filename, authuin, rootpath, false, specialType);
	//CustomModel* cm2 = SANDBOX_NEW(CustomModel);
	//ok = cm2->load(path);
	////==========================================================//

	m_AuthUin = authuin;
	m_strKey = filename; //设置文件名
	return true;
}
bool CustomModel::load(const char* path) {

	if (!path)
	{
		return false;
	}
	int buflen = 0;
	//文件读取 相对路径
	AutoRefPtr<DataStream> fp = GetFileManager().OpenFile(path, true, FileOpType::kFileOpAll);
	if (!fp.IsValid())
	{
		return false;
	}

	buflen = fp->Size();
	if (buflen == 0) return NULL; 

	void* buf = malloc(buflen);
	if (buf == NULL) return NULL;

	fp->Read(buf, buflen);
	fp->Close();

	if (buf == NULL)
		return false;

	uint8_t* custBuf = (uint8_t*)buf;

	if (buflen >= 8)
	{
		char name[5];
		memcpy(name, (const char*)buf, 5);
		name[4] = 0;

		int authuin;
		memcpy(&authuin, (const char*)buf + 4, 4);

		if (strcmp(name, "CUST") == 0)
		{
			//文件头是CUST
			char* md5 = getMd5(authuin);

			custBuf = (uint8_t*)buf + 8;
			for (int i = 0; i < buflen - 8; i++)
			{
				custBuf[i] = custBuf[i] ^ md5[i % 16];
			}

			buflen = buflen - 8;
			m_HasCustFlag = true;
		}

	}

	flatbuffers::Verifier verifier(custBuf, buflen);
	if (!FBSave::VerifyCustomModelBuffer(verifier))
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	const FBSave::CustomModel* custommodel = FBSave::GetCustomModel(custBuf);
	if (custommodel == NULL)
	{
		//free(buf);
		FastUnmapFile(buf, buflen);
		return false;
	}

	m_Materials.clear();
	m_Blocks.clear();
	m_RelativePos.clear();
	m_vRelativeBitPos.clear();
	m_EditorBlocks.clear();
	m_vEditorRelativeBitPos.clear();
	m_BlocksPos.clear();
#if BUILD_MINI_EDITOR_APP
	{
		m_ModelName = m_strKey = path;
	}
#else
	{
		m_strKey = "";
	}
#endif

	m_MinPos = Rainbow::Vector3f(BLOCK_FSIZE);
	m_MaxPos = Rainbow::Vector3f(0.f);
	if (custommodel->blocks() || custommodel->bitblocks())
	{
		bool bUseBitPos = (custommodel->bitblocks() != NULL);
		int iBlockSize = (bUseBitPos ? custommodel->bitblocks()->size() : custommodel->blocks()->size());
		int iBitPos;
		for (int i = 0; i < iBlockSize; i++)
		{
			if (bUseBitPos) {
				auto src = custommodel->bitblocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = src->bitpos();
			}
			else {
				auto src = custommodel->blocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z());
			}
			//auto src = custommodel->blocks()->Get(i);
			//m_Blocks.push_back(src->block());
			//iBitPos = src->bitpos() == -1 ? covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z()) : src->bitpos();

			m_vRelativeBitPos.push_back(iBitPos);
			WCoord w(
				getRealPosByBit(iBitPos, 0),
				getRealPosByBit(iBitPos, 1),
				getRealPosByBit(iBitPos, 2)
			);
			m_RelativePos.push_back(w);
			updateRange(w);

			Block block = m_Blocks[i];
			int blockid = block.getResID();
			if (blockid > 0)
			{
				addMaterial(blockid);
			}
		}
	}

	m_Box = m_MaxPos + Vector3f::one - m_MinPos;
	m_EditorBlocks.assign(m_Blocks.begin(), m_Blocks.end());
	m_vEditorRelativeBitPos.assign(m_vRelativeBitPos.begin(), m_vRelativeBitPos.end());
	for (const auto& p : m_vEditorRelativeBitPos)
	{
		m_BlocksPos.emplace(p);
	}

	m_Type = BLOCK_MODEL;
	//if (custommodel->type())
		//m_Type = custommodel->type();

	if (custommodel->blockscrc())
		m_BlocksCrc = (unsigned long)custommodel->blockscrc();

	if (custommodel->relativeposcrc())
		m_RelativePosCrc = (unsigned long)custommodel->relativeposcrc();

	if (custommodel->name())
	{
		m_ModelName = custommodel->name()->c_str();
		//std::string transKey(m_strKey);
		//transKey.append("_name");
		//if (modroot.empty() && owid > 0) {
		//	m_ModelName = WorldStringTranslateMgr::getInstance()->getTransStrByKey(TRANSLATE_TYPE::TRANSLATE_TYPE_CUSTOM_MODEL, transKey, m_ModelName);
		//}
	}

	if (custommodel->desc())
	{
		m_ModelDesc = custommodel->desc()->c_str();
		/*	std::string transDesc(m_strKey);
			transDesc.append("_desc");
			if (modroot.empty() && owid > 0) {
				m_ModelDesc = WorldStringTranslateMgr::getInstance()->getTransStrByKey(TRANSLATE_TYPE::TRANSLATE_TYPE_CUSTOM_MODEL, transDesc, m_ModelDesc);
			}*/
	}

	m_DirType = DIR_NEG_Z;
	if (custommodel->dirtype())
		m_DirType = custommodel->dirtype();

	if (custommodel->modeltype())
		m_Type = custommodel->modeltype();

	m_AuthUin = custommodel->authuin();

	auto t = custommodel->time();

	if (custommodel->dim())
	{
		m_EditorDim = Coord3ToWCoord(custommodel->dim());
	}
	calculateDim();
	//free(buf);
	FastUnmapFile(buf, buflen);
	return true;
}

bool CustomModel::save(const char* path) {

	m_Blocks.clear();
	m_vRelativeBitPos.clear();

	m_vRelativeBitPos.assign(m_vEditorRelativeBitPos.begin(), m_vEditorRelativeBitPos.end());
	m_Blocks.assign(m_EditorBlocks.begin(), m_EditorBlocks.end());

	flatbuffers::FlatBufferBuilder builder;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BPBlockDataBit>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::BPBlockDataBit>> blockdatas;
	auto iter = m_Blocks.begin();
	for (int i = 0; i < (int)m_Blocks.size(); i++)
	{
		blockdatas.push_back(FBSave::CreateBPBlockDataBit(builder, m_Blocks[i].getOriginData(), m_vRelativeBitPos[i], m_Blocks[i].getDataEx()));
	}
	blockdatasoffset = builder.CreateVector(blockdatas);

	unsigned long t = (unsigned long)time(NULL);
	int authuin = 1000;
	//没有保存一个
	if (m_EditorDim.isZero())
	{
		m_EditorDim = WCoord(10, 10, 10);
	}
	FBSave::Coord3 dim = WCoordToCoord3(m_EditorDim);
	auto cm = FBSave::CreateCustomModel(builder, 0, m_Type, builder.CreateString("custommodel"), authuin,
		m_BlocksCrc, m_RelativePosCrc, builder.CreateString(m_ModelName), builder.CreateString(m_ModelDesc), m_DirType, m_Type, blockdatasoffset, t, &dim);
	builder.Finish(cm);


	uint8_t* data = builder.GetBufferPointer();
	char* md5 = getMd5(authuin);
	int size = builder.GetSize();
	for (int i = 0; i < (int)builder.GetSize(); i++)
	{
		data[i] = data[i] ^ md5[i % 16];
	}
	//core::string fullpath;
	//GetFileManager().ToWritePathFull(path, fullpath);
	FileAutoClose fp(path, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
	if (fp.isNull()) return false;

	//写入文件头CUST+作者uin
	if (!fp.write("CUST", sizeof(char) * 4))
	{

		return false;
	}

	if (!fp.seek(4, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return false;
	}

	if (!fp.write(&authuin, sizeof(int)))
	{
		fp.sync();
		fp.close();
		return false;
	}

	if (!fp.seek(8, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return false;
	}

	//写入微雕模型信息
	if (!fp.write(data, builder.GetSize()))
	{
		fp.sync();
		fp.close();
		return false;
	}

	fp.sync();
	fp.close();

	//FileManager::getSingleton().renameStdioPath(tmppath, path);
	//load(path);

	return true;
}

bool CustomModel::save()
{
	if (m_strKey.empty())
	{
		return false;
	}
	return save(m_strKey.c_str());
}
void CustomModel::reEncrypt(long long owid, std::string filename, int olduin, int newuin, int specialType/* = NORMAL_WORLD*/)
{
	char path[256];
	if (owid < 0)
		sprintf(path, "data/custommodel/%s.cm", filename.c_str());
	else
	{
		std::string rootpath = GetWorldRootBySpecialType(specialType);

		sprintf(path, "%s/w%lld/custommodel/%s.cm", rootpath.c_str(), owid, filename.c_str());
	}

	int buflen = 0;
	void *buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return;

	uint8_t *custBuf = (uint8_t *)buf;

	if (buflen >= 8)
	{
		char name[5];
		memcpy(name, (const char *)buf, 5);
		name[4] = 0;

		int authuin;
		memcpy(&authuin, (const char *)buf + 4, 4);

		if (olduin == authuin)
		{
			if (strcmp(name, "CUST") == 0)
			{
				//文件头是CUST
				char *md5 = getMd5(authuin);

				custBuf = (uint8_t *)buf + 8;
				for (int i = 0; i < buflen - 8; i++)
				{
					custBuf[i] = custBuf[i] ^ md5[i % 16];
				}

				buflen = buflen - 8;
			}
		}
	}

	flatbuffers::Verifier verifier(custBuf, buflen);
	if (!FBSave::VerifyCustomModelBuffer(verifier))
	{
		free(buf);
		return;
	}

	const FBSave::CustomModel *custommodel = FBSave::GetCustomModel(custBuf);
	if (custommodel == NULL)
	{
		free(buf);
		return;
	}

	m_Materials.clear();
	m_Blocks.clear();
	m_RelativePos.clear();
	m_vRelativeBitPos.clear();
	m_strKey = filename;

	m_MinPos = Rainbow::Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	m_MaxPos = Rainbow::Vector3f(0, 0, 0);

	if (custommodel->blocks() || custommodel->bitblocks())
	{
		bool bUseBitPos = (custommodel->bitblocks() != NULL);
		int iBlockSize = (bUseBitPos ? custommodel->bitblocks()->size() : custommodel->blocks()->size());
		int x, y, z, iBitPos;
		for (int i = 0; i < iBlockSize; i++)
		{
			if (bUseBitPos) {
				auto src = custommodel->bitblocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = src->bitpos();
			}
			else {
				auto src = custommodel->blocks()->Get(i);
				m_Blocks.push_back(Block(src->block(), src->blockex()));
				iBitPos = covertPosToBit(src->relativepos()->x(), src->relativepos()->y(), src->relativepos()->z());
			}

			m_vRelativeBitPos.push_back(iBitPos);
			x = getRealPosByBit(iBitPos, 0);
			y = getRealPosByBit(iBitPos, 1);
			z = getRealPosByBit(iBitPos, 2);
			if (x < m_MinPos.x)
				m_MinPos.x = (float)x;
			if (y < m_MinPos.y)
				m_MinPos.y = (float)y;
			if (z < m_MinPos.z)
				m_MinPos.z = (float)z;
			if (x > m_MaxPos.x)
				m_MaxPos.x = (float)x;
			if (y > m_MaxPos.y)
				m_MaxPos.y = (float)y;
			if (z > m_MaxPos.z)
				m_MaxPos.z = (float)z;

			Block block = m_Blocks[i];
			int blockid = block.getResID();
			if (blockid > 0)
			{
				addMaterial(blockid);
			}
		}
	}

	m_Box = m_MaxPos + Rainbow::Vector3f(1, 1, 1) - m_MinPos;

	m_Type = BLOCK_MODEL;

	if (custommodel->blockscrc())
		m_BlocksCrc = (unsigned long)custommodel->blockscrc();

	if (custommodel->relativeposcrc())
		m_RelativePosCrc = (unsigned long)custommodel->relativeposcrc();

	if (custommodel->name())
		m_ModelName = custommodel->name()->c_str();

	if (custommodel->desc())
		m_ModelDesc = custommodel->desc()->c_str();

	m_DirType = DIR_NEG_Z;
	if (custommodel->dirtype())
		m_DirType = custommodel->dirtype();

	if (custommodel->modeltype())
		m_Type = custommodel->modeltype();

	auto t = custommodel->time();
	free(buf);
	OGRE_DELETE_ARRAY(m_MD5);

	//save
	flatbuffers::FlatBufferBuilder builder;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BPBlockDataBit>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::BPBlockDataBit>> blockdatas;
	auto iter = m_Blocks.begin();
	for (int i = 0; i < (int)m_Blocks.size(); i++)
	{
		blockdatas.push_back(FBSave::CreateBPBlockDataBit(builder, m_Blocks[i].getOriginData(), m_vRelativeBitPos[i], m_Blocks[i].getDataEx()));
	}
	blockdatasoffset = builder.CreateVector(blockdatas);

	auto cm = FBSave::CreateCustomModel(builder, 0, m_Type, builder.CreateString(GetClientInfoProxy()->getNickName()), newuin,
		m_BlocksCrc, m_RelativePosCrc, builder.CreateString(m_ModelName), builder.CreateString(m_ModelDesc), m_DirType, m_Type, blockdatasoffset, t);
	builder.Finish(cm);


	uint8_t *data = builder.GetBufferPointer();
	char *md5 = getMd5(newuin);
	int size = builder.GetSize();
	for (int i = 0; i < (int)builder.GetSize(); i++)
	{
		data[i] = data[i] ^ md5[i % 16];
	}

	char tmppath[256];
	sprintf(tmppath, "%s.tmp", path);
	core::string fullpath;
	GetFileManager().ToWritePathFull(tmppath, fullpath);

	FileAutoClose fp(fullpath, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
	if (fp.isNull()) return;

	//写入文件头CUST+作者uin
	if (!fp.write("CUST", sizeof(char) * 4))
		return;

	if (!fp.seek(4, SEEK_SET))
		return;
	if (!fp.write(&newuin, sizeof(int)))
		return;

	if (!fp.seek(8, SEEK_SET))
		return;

	//写入微雕模型信息
	if (!fp.write(data, builder.GetSize()))
		return;

	fp.sync();
	fp.close();

	GetFileManager().DeleteWritePathFileOrDir(path);
	GetFileManager().RenameWritePathFile(tmppath, path);

	return;
}

void converBlockPosByPlaceDir(WCoord &pos, int dirtype, WCoord &dim, int modeltype)
{
	int dimx = Abs(dim.x);
	int dimz = Abs(dim.z);

	if (modeltype == BLOCK_MODEL)
	{
		if (dirtype == DIR_POS_X)
		{
			int x = pos.x;
			pos.x = pos.z;
			pos.z = (dimx - x);
		}
		else if (dirtype == DIR_POS_Z)
		{
			pos.x = (dimx - pos.x);
			pos.z = (dimz - pos.z);
		}
		else if (dirtype == DIR_NEG_X)
		{
			int z = pos.z;
			pos.z = pos.x;
			pos.x = (dimz - z);
		}
	}
	else
	{
		if (dirtype == DIR_POS_X)
		{
			int x = dim.z - pos.z;
			pos.z = dim.x - pos.x;
			pos.x = x;
		}
		else if (dirtype == DIR_POS_Z)
		{
			pos.z = (dimz - pos.z);
		}
		else if (dirtype == DIR_NEG_X)
		{
			int z = pos.z;
			pos.z = pos.x;
			pos.x = z;
		}
		else
		{
			pos.x = (dimx - pos.x);
		}
	}
	
}

void CustomModel::setData(const vector<Block>& vBlocks, const vector<WCoord>& vPos,
	unsigned long blockCrc, unsigned long posCrc,
	std::string modelname, std::string modeldesc, WCoord& dim, int modeltype, int dirtype/* =DIR_NEG_Z */)
{
	m_Blocks.clear();
	m_RelativePos.clear();
	m_vRelativeBitPos.clear();
	m_Blocks.assign(vBlocks.begin(), vBlocks.end());
	m_RelativePos.assign(vPos.begin(), vPos.end());

	char x, y, z;
	int bit;
	m_MinPos = Rainbow::Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	m_MaxPos = Rainbow::Vector3f(0, 0, 0);
	for (size_t i = 0; i < vPos.size(); i++)
	{
		WCoord& pos = m_RelativePos[i];
		converBlockPosByPlaceDir(pos, dirtype, dim, modeltype);
		bit = covertPosToBit(pos.x, pos.y, pos.z);
		m_vRelativeBitPos.push_back(bit);
		x = getRealPosByBit(bit, 0);
		y = getRealPosByBit(bit, 1);
		z = getRealPosByBit(bit, 2);
		updateRange(x, y, z);
	}

	m_Box = m_MaxPos + Rainbow::Vector3f(1, 1, 1) - m_MinPos;

	m_BlocksCrc = blockCrc;
	m_RelativePosCrc = posCrc;
	m_ModelName = modelname;
	m_ModelDesc = modeldesc;
	m_Type = modeltype;
	m_DirType = dirtype;

	if(m_Type == BLOCK_MODEL)
		genGeomTemplate();
}

void CustomModel::setData(const vector<Block>& blocks, const vector<WCoord>& pos, bool needgengeom/* =false */)
{
	m_Blocks.clear();
	m_RelativePos.clear();
	m_vRelativeBitPos.clear();
	for (size_t i = 0; i < blocks.size(); i++)
	{
		m_Blocks.push_back(blocks[i]);
	}

	int x, y, z, iBitPos;
	m_MinPos = Rainbow::Vector3f(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	m_MaxPos = Rainbow::Vector3f(0, 0, 0);
	for (size_t i = 0; i < pos.size(); i++)
	{
		iBitPos = covertPosToBit(pos[i].x, pos[i].y, pos[i].z);
		m_vRelativeBitPos.push_back(iBitPos);
		x = getRealPosByBit(iBitPos, 0);
		y = getRealPosByBit(iBitPos, 1);
		z = getRealPosByBit(iBitPos, 2);
		updateRange(x, y, z);
	}

	m_Box = m_MaxPos + Rainbow::Vector3f(1, 1, 1) - m_MinPos;
	m_Type = BLOCK_MODEL;

	if(needgengeom)
		genGeomTemplate();
}

void CustomModel::addMaterial(int id)
{/*  废弃
	for (size_t i = 0; i < m_Materials.size(); i++)
	{
		if (m_Materials[i].itemid == id)
		{
			m_Materials[i].num++;
			return;
		}
	}

	CustomsModelMaterial material;
	material.itemid = id;
	material.num = 1;

	m_Materials.push_back(material);*/
}

void CustomModel::genGeomTemplateInternal(std::vector<unsigned int>& colors, std::vector<WCoord>& relativePos)
{
	unsigned int defaultColor = 0;
	sscanf("000000ff", "%x", &defaultColor);

	int iBitPos;
	for (size_t i = 0; i < m_Blocks.size(); i++)
	{
		int blockid = m_Blocks[i].getResID();

		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid, false);
		if (def && def->MiniColor > 0)
		{
			unsigned int color = ((def->MiniColor & 0xff) << 16) | (def->MiniColor & 0xff00) | ((def->MiniColor & 0xff0000) >> 16);
			BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
			if (mtl && mtl->isColorableBlock())
			{
				auto blockcolor = mtl->getBlockColor(m_Blocks[i].getData());
				color = ColorQuad(blockcolor.r, blockcolor.g, blockcolor.b).c;
			}
			//else if (color == 0)
				//color = 0xa6a899;

			color = (color << 8) | 0xff;
			colors.push_back(color);

			iBitPos = m_vRelativeBitPos[i];
			relativePos.push_back(WCoord(getRealPosByBit(iBitPos, 0), getRealPosByBit(iBitPos, 1), getRealPosByBit(iBitPos, 2)));
			//relativePos.push_back(m_RelativePos[i]);
		}
		//暂时屏蔽掉默认的
		//else
		//colors.push_back(defaultColor);
	}
}

void CustomModel::genGeomTemplate()
{
	std::vector<unsigned int> colors;
	std::vector<WCoord> relativePos;
	
	genGeomTemplateInternal(colors, relativePos);

	if(relativePos.size() > 0){
		g_BlockMtlMgr.genGeomTemplate(relativePos, colors, m_strKey.c_str());
	}
}

void* CustomModel::genGeomTemplateInJobThread() 
{
	std::vector<unsigned int> colors;
	std::vector<WCoord> relativePos;

	genGeomTemplateInternal(colors, relativePos);

	if (relativePos.size() > 0) {
		return (void*)g_BlockMtlMgr.genGeomTemplateInJobThread(relativePos, colors, m_strKey.c_str());
	}

	return nullptr;
}

void CustomModel::genRelativePosAndColors(ITEM_MESH_TYPE meshtype)
{
	if (m_ModelRPosAndColorOK[meshtype])
	{
		return;
	}
	std::vector<unsigned int>& colors = m_ModelDataColors[meshtype];
	std::vector<WCoord>& relativePos = m_ModelDataRPos[meshtype];
	std::string& strHex = m_ModelDataHex[meshtype];

	unsigned int defaultColor = 0;
	sscanf("000000ff", "%x", &defaultColor);

	int iBitPos;
	std::map<WCoord, bool> blockZeniths;
	if (meshtype == EDIT_BLOCK_MESH)
	{
		blockZeniths[WCoord(0, 0, 0)] = false;
		blockZeniths[WCoord(19, 0, 0)] = false;
		blockZeniths[WCoord(0, 0, 19)] = false;
		blockZeniths[WCoord(19, 0, 19)] = false;
		blockZeniths[WCoord(0, 19, 0)] = false;
		blockZeniths[WCoord(19, 19, 0)] = false;
		blockZeniths[WCoord(0, 19, 19)] = false;
		blockZeniths[WCoord(19, 19, 19)] = false;
	}

	for (size_t i = 0; i < m_Blocks.size(); i++)
	{
		int blockid = m_Blocks[i].getResID();

		const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
		if (def && def->MiniColor > 0)
		{
			unsigned int color = ((def->MiniColor & 0xff) << 16) | (def->MiniColor & 0xff00) | ((def->MiniColor & 0xff0000) >> 16);
			BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
			if (mtl && mtl->isColorableBlock())
			{
				auto blockcolor = mtl->getBlockColor(m_Blocks[i].getData());
				color = ColorQuad(blockcolor.r, blockcolor.g, blockcolor.b).c;
			}

			color = (color << 4) | def->CustommodelAlpha;
			color = (color << 4) | def->CustommodelLight;

			colors.push_back(color);
			iBitPos = m_vRelativeBitPos[i];
			WCoord realPos = WCoord(getRealPosByBit(iBitPos, 0), getRealPosByBit(iBitPos, 1), getRealPosByBit(iBitPos, 2));
			relativePos.push_back(realPos);
			if (meshtype == EDIT_BLOCK_MESH)
			{
				auto iter = blockZeniths.find(realPos);
				if (iter != blockZeniths.end())
					iter->second = true;
			}
		}
	}

	if (meshtype == EDIT_BLOCK_MESH)
	{
		auto iter = blockZeniths.begin();
		for (; iter != blockZeniths.end(); iter++)
		{
			if (iter->second)
				continue;

			relativePos.push_back(iter->first);
			unsigned int color = 0xffffff;
			color = (color << 4) | 0;
			color = (color << 4) | 15;
			colors.push_back(color);
		}
	}

	char output[16] = { 0 };
	char hex[33] = { 0 };
	if (relativePos.size())
	{
		MINIW::Md5Context context;
		context.begin();
		context.append((const UInt8*)&relativePos[0], relativePos.size() * sizeof(WCoord));
		context.append((const UInt8*)&colors[0], colors.size() * sizeof(unsigned int));
		context.append((const UInt8*)&meshtype, sizeof(ITEM_MESH_TYPE));
		context.end((UInt8*)output);
		MINIW::Md5ToHex((char*)hex, (char*)output);
	}
	strHex = hex;

	m_ModelRPosAndColorOK[meshtype] = true;
}

bool CustomModel::isItemModelInCache(ITEM_MESH_TYPE meshtype)
{
	if (m_ModelData[meshtype])
	{
		return true;
	}

	genRelativePosAndColors(meshtype);

	if (!m_ModelRPosAndColorOK[meshtype])
	{
		return false;
	}

	return GenCustomModelManager::GetInstance().isItemModelInCache(m_ModelDataHex[meshtype].c_str());
}

void CustomModel::genItemModelAsync(ITEM_MESH_TYPE meshtype)
{
	if (m_ModelData[meshtype])
	{
		return;
	}

	genRelativePosAndColors(meshtype);

	if (!m_ModelRPosAndColorOK[meshtype])
	{
		return;
	}
	std::vector<unsigned int>& colors = m_ModelDataColors[meshtype];
	std::vector<WCoord>& relativePos = m_ModelDataRPos[meshtype];

	GenCustomModelManager::GetInstance().genItemModelAsync(relativePos, colors, meshtype, 
		m_Type == BLOCK_MODEL, m_ModelDataHex[meshtype].c_str());
}

Rainbow::Model *CustomModel::getItemModel(ITEM_MESH_TYPE meshtype)
{
	if (meshtype < MAX_MSEH_TYPE)
	{
		if (m_ModelData[meshtype])
		{
			Model* model = Rainbow::Model::CreateInstanceLegacy(m_ModelData[meshtype], true);
			return model;
		}
	}

	genRelativePosAndColors(meshtype);
	std::vector<unsigned int>& colors = m_ModelDataColors[meshtype];
	std::vector<WCoord>& relativePos = m_ModelDataRPos[meshtype];

	//TODO: 2023-02-27 16:24:20: 无方块是否也返回模型？
	Rainbow::Model *tempmodel = GenCustomModelManager::GetInstance().genItemModel(relativePos, colors, meshtype,
		m_Type == BLOCK_MODEL, m_ModelDataHex[meshtype].c_str());

	if (!tempmodel)
		return nullptr;

	Rainbow::SharePtr<Rainbow::ModelData> tempmodeldata = nullptr;
	if (tempmodel && tempmodel->IsKindOf<Rainbow::ModelLegacy>())
	{
		Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(tempmodel);
		if (legacymodel != nullptr && legacymodel->GetModelData())
		{
			tempmodeldata = legacymodel->GetModelData();
		}
	}

	if (tempmodel && tempmodeldata)
	{
		m_ModelData[meshtype] = tempmodeldata;
	}
	return tempmodel;
}

void CustomModel::syncCustomModelDataPre(IClientPlayer *player, std::string classname, int index)
{
	if (!player->hasUIControl())
	{
		PB_CustomModelPrepareHC customModelPreHC;
		customModelPreHC.set_filename(m_strKey);
		customModelPreHC.set_index(index);
		GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_CUSTOM_MODEL_PRE_HC, customModelPreHC);
	}
	if (m_ProductContainerCoord.y != -1)
	{
		auto container = player->GetPlayerWorld()->getContainerMgr()->getContainer(m_ProductContainerCoord);
		if (container)
		{
			bool result = container->Event2().Emit<int>("ContainerModelCraft_ProductResult",m_Id);
			Assert(result);
		}
		m_ProductContainerCoord.y = -1;
	}
}

void CustomModel::syncCustomModelData(IClientPlayer *player, std::string classname, bool isSendData)
{	
	if (!player->hasUIControl())
	{
		PB_CustomModelHC customModelHC;

		unsigned int defaultColor = 0;
		sscanf("000000ff", "%x", &defaultColor);

		if (isSendData)
		{
			std::vector<SyncCustomModelData> datas;

			for (size_t i = 0; i < m_Blocks.size(); i++)
			{
				int blockid = m_Blocks[i].getResID();

				const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid);
				if (def && def->MiniColor > 0)
				{
					SyncCustomModelData data;
					//data.color = color; //这里原来传颜色值，修改成传 blockData
					data.blockData = m_Blocks[i].getAll();
					data.pos = m_vRelativeBitPos[i];//m_RelativePos[i].x + (m_RelativePos[i].y << 8) + (m_RelativePos[i].z << 16);

					datas.push_back(data);
				}
			}
			if (datas.size() == 0)
			{
				return;
			}

			size_t srclen = sizeof(SyncCustomModelData) * datas.size();

			int compresstype = CompressTool::COMPRESS_LZMA;
			CompressTool ctool(compresstype);
			size_t destlen = ctool.compressBound(srclen);

			char *blobDetail = new char[destlen];
			if (datas.size() <= 0 || (datas.size() > 0 && !ctool.compress(blobDetail, destlen, &datas[0], srclen, 0)))
			{
				assert(0);
				OGRE_DELETE_ARRAY(blobDetail);
				return;
			}

			customModelHC.set_blobdetail(blobDetail, destlen);
			customModelHC.set_unziplen(srclen | (compresstype << 28));
			customModelHC.set_bloblen(destlen);
			OGRE_DELETE_ARRAY(blobDetail);
		}
		else
		{
			customModelHC.set_unziplen(0);	
		}

		customModelHC.set_filename(m_strKey);
		customModelHC.set_itemid(m_Id);
		customModelHC.set_modelname(m_ModelName);
		customModelHC.set_modeldesc(m_ModelDesc);
		customModelHC.set_classname(classname);
		customModelHC.set_type(m_Type);
		customModelHC.set_authuin(m_AuthUin);
		customModelHC.set_blockidversion(1);

		PB_Vector3* box = customModelHC.mutable_box();
		box->set_x((int)m_Box.x);
		box->set_y((int)m_Box.y);
		box->set_z((int)m_Box.z);

		customModelHC.set_isdownload(isDownload());

		GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_CUSTOM_MODEL_HC, customModelHC);
	}
	
	if (m_ProductContainerCoord.y != -1)
	{
		auto container = player->GetPlayerWorld()->getContainerMgr()->getContainer(m_ProductContainerCoord);
		if (container)
		{
			bool result = container->Event2().Emit<int>("ContainerModelCraft_ProductResult", m_Id);
			Assert(result);
		}
		m_ProductContainerCoord.y = -1;
	}	
}


#ifdef BUILD_MINI_EDITOR_APP
char CustomModel::getRealPosByBit(int bit, int pos)
#else
int CustomModel::getRealPosByBit(int bit, int pos)
#endif
{
	if (pos == 0) {
		return bit & 0xff;
	}
	else if (pos == 1) {
		return (bit >> 8) & 0xff;
	}

	return (bit >> 16) & 0xff;
}
int CustomModel::covertPosToBit(int x, int y, int z)
{
#ifdef BUILD_MINI_EDITOR_APP
	return
		(x & 0xff)
		+ ((y & 0xff) << 8)
		+ ((z & 0xff) << 16)
		;
#else
	return x + (y << 8) + (z << 16);
#endif
}

#ifdef UGC_PROFILE_ENABLED
size_t CustomModel::getMemStatModelDataForUGC(Rainbow::SharePtr<Rainbow::ModelData> modelData)
{
	Rainbow::SharePtr<Rainbow::ModelData> obj = modelData;

	if (!obj)
	{
		return 0;
	}

	size_t tempISize = 0;
	//std::set<MINIW::TextureData*> mtlTexs;
	std::set<Rainbow::OgreVertexData*> vds;
	std::set<Rainbow::IndexData*> ids;
	for (size_t i = 0; i < obj->m_Meshes.size(); i++)
	{
		Rainbow::MeshData* pmesh = obj->m_Meshes[i];
		if (!pmesh)
		{
			continue;
		}

		// 名字
		tempISize += sizeof(Rainbow::MeshData) + pmesh->m_Name.length() * sizeof(char);

		for (size_t j = 0; j < pmesh->m_SubMeshes.size(); j++)
		{
			Rainbow::SubMeshData* psubmesh = pmesh->m_SubMeshes[j];
			if (!psubmesh)
			{
				continue;
			}

			// 顶点
			Rainbow::OgreVertexData* pVD = psubmesh->GetVertexData();
			if (pVD && vds.count(pVD) < 1 && pVD->getNumVertex() > 0)
			{
				tempISize += pVD->getMemSize();
				vds.insert(pVD);
			}

			// 索引
			Rainbow::IndexData* pID = psubmesh->GetIndexData();
			if (pID && ids.count(pID) < 1 && pID->getNumIndex() > 0)
			{
				tempISize += pID->getMemSize();
				ids.insert(pID);
			}

			for (size_t i = 0; i < psubmesh->m_Patches.size(); i++)
			{
				Rainbow::SkinPatch* patch = psubmesh->m_Patches[i];
				if (patch)
				{
					tempISize += sizeof(Rainbow::SkinPatch) +
						patch->m_RefBones.size() * sizeof(UInt16) +
						patch->m_BoneBindTM.size() * sizeof(Rainbow::Matrix4x4f);
				}
			}
		}
	}

	// 骨骼数据
	Rainbow::SkeletonData* skeletonData = obj->m_pSkeleton;
	if (skeletonData)
	{
		tempISize += skeletonData->getNumBone() * sizeof(Rainbow::BoneData);
		for (size_t i = 0; i < skeletonData->getNumBone(); i++)
		{
			Rainbow::BoneData* boneData = skeletonData->getIthBone(i);
			if (!boneData)
			{
				continue;
			}

			tempISize += boneData->m_Name.length() * sizeof(char);
			tempISize += boneData->m_UserData.length() * sizeof(char);
		}
		std::map<Rainbow::FixedString, int>& boneNames = skeletonData->getBoneNameTab();
		for (std::map<Rainbow::FixedString, int>::iterator iter = boneNames.begin();
			iter != boneNames.end(); ++iter)
		{
			tempISize += iter->first.length() * sizeof(char) + sizeof(int);
		}
	}

	// 动画数据
	tempISize += obj->GetNumAnim() * sizeof(Rainbow::ModelAnimData);
	for (size_t i = 0; i < obj->GetNumAnim(); i++)
	{
		Rainbow::SharePtr<Rainbow::AnimationData> animData = obj->getAnimation(i);
		if (animData)
		{
			tempISize += animData->m_TriggerArray.size() * sizeof(Rainbow::TriggerDesc);
			tempISize += animData->m_Sequences.size() * sizeof(Rainbow::SequenceDesc);
			tempISize += animData->m_BoneTracks.size() * sizeof(Rainbow::BoneTrack);
			for (size_t i = 0; i < animData->m_BoneTracks.size(); i++)
			{
				if (!animData->m_BoneTracks[i])
				{
					continue;
				}

				size_t cptSize = sizeof(Rainbow::KeyFrameArray<Rainbow::Vector3f>::CONTROL_POINT_T);
				size_t kfTSize = sizeof(Rainbow::KeyFrameArray<Rainbow::Vector3f>::KEYFRAME_T);
				tempISize += animData->m_BoneTracks[i]->m_TranslateKeys.m_Array.size() * kfTSize;
				tempISize += animData->m_BoneTracks[i]->m_TranslateKeys.m_CtrlPt.size() * cptSize;
				tempISize += animData->m_BoneTracks[i]->m_ScaleKeys.m_Array.size() * kfTSize;
				tempISize += animData->m_BoneTracks[i]->m_ScaleKeys.m_CtrlPt.size() * cptSize;
				cptSize = sizeof(Rainbow::KeyFrameArray<Rainbow::Quaternionf>::CONTROL_POINT_T);
				kfTSize = sizeof(Rainbow::KeyFrameArray<Rainbow::Quaternionf>::KEYFRAME_T);
				tempISize += animData->m_BoneTracks[i]->m_RotateKeys.m_Array.size() * kfTSize;
				tempISize += animData->m_BoneTracks[i]->m_RotateKeys.m_CtrlPt.size() * cptSize;
			}
			tempISize += sizeof(Rainbow::MaterialParamTrack) * animData->m_MtlParamTracks.size();
		}
	}

	return tempISize;
}
#endif // UGC_PROFILE_ENABLED

Rainbow::Vector3f CustomModel::getBlockCenterDrawOffsetPos()
{
	WCoord minpos = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	WCoord maxpos = WCoord(0, 0, 0);

	for (int i = 0; i < (int)m_vRelativeBitPos.size(); i++)
	{
		WCoord pos = WCoord(getRealPosByBit(m_vRelativeBitPos[i], 0), getRealPosByBit(m_vRelativeBitPos[i], 1), getRealPosByBit(m_vRelativeBitPos[i], 2));
		pos.x = BLOCK_MODEL_SIZE - 1 - pos.x;

		if (pos.x < minpos.x)
			minpos.x = pos.x;
		if (pos.y < minpos.y)
			minpos.y = pos.y;
		if (pos.z < minpos.z)
			minpos.z = pos.z;
		if (pos.x > maxpos.x)
			maxpos.x = pos.x;
		if (pos.y > maxpos.y)
			maxpos.y = pos.y;
		if (pos.z > maxpos.z)
			maxpos.z = pos.z;
	}
	maxpos = maxpos + WCoord(1, 1, 1);

	Rainbow::Vector3f offsetPos(0, 0, 0);
	WCoord box = (maxpos - minpos);// * 5;
	//minpos = minpos * 5;

	offsetPos.x = (float)(minpos.x + box.x / 2);
	offsetPos.y = (float)(minpos.y + box.y / 2);
	offsetPos.z = (float)(minpos.z + box.z / 2);

	offsetPos *= 5;

	return offsetPos;
}

bool CustomModel::isDownload()
{
	if(m_AuthUin > 0)
		return m_AuthUin != GetClientInfoProxy()->getUin();

	return false;
}

extern int convertLittleEndian4byte(int in);

int CustomModel::convert2SmallRoutine(int type)
{
	long long owid = -1;
	if (type == MAP_MODEL_CLASS)
	{
		if (!GetWorldManagerPtr())
			return 1;

		owid = GetWorldManagerPtr()->getWorldId();
	}

	char path[256];
	if(owid < 0)
		sprintf(path, "data/custommodel/%s.txt", m_strKey.c_str());
	else
	{
		std::string rootpath = GetWorldRootBySpecialType(GetWorldManagerPtr()->getSpecialType());

		sprintf(path, "%s/w%lld/custommodel/%s.txt", rootpath.c_str(), owid, m_strKey.c_str());
	}

	core::string fullpath;
	GetFileManager().ToWritePathFull(path,fullpath);

	FileAutoClose fp(fullpath, O_CREAT | O_WRONLY | O_TRUNC | O_BINARY);
	if (fp.isNull())
		return 2;

	std::vector<char> modelDatas;
	modelDatas.clear();

	short colorIndex = 0;
	std::map<unsigned int, char> colorInfos;
	colorInfos.clear();
	std::vector<unsigned int> convertColors;
	convertColors.clear();
	getModelDataBySmallRoutineFormat(modelDatas, colorInfos, convertColors, WCoord(-9, 1, -9));  //20*20*20��С��С������Դ�������(-9, 1, 9)

	int dataLen = convertLittleEndian4byte((int)modelDatas.size());

	if (!fp.write(&dataLen, sizeof(unsigned int)))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	int seekOffset = sizeof(unsigned int);
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	jsonxx::Array textContent;
	auto iter = colorInfos.begin();
	for (size_t i=0; i <  convertColors.size(); i++)
	{
		textContent << convertColors[i];
	}

	jsonxx::Object resObj;
	resObj << "colorArry" << textContent;
	resObj << "fromminiworld" << 1;
	resObj << "area" << 20;
	resObj << "mtype" << "3D";

	std::string jsonStr = resObj.json();
	//regex pattern(" |\n|\r|\t");
	//std::string fmt = "";

	//jsonStr = std::regex_replace(jsonStr, pattern, fmt);
	unsigned short jsonLen = static_cast<unsigned short>(jsonStr.length());
	unsigned short tempJsonLen = jsonLen;
	jsonLen = ((jsonLen & 0xff00) >> 8) | ((jsonLen & 0x00ff) << 8);

	if (!fp.write(&jsonLen, sizeof(unsigned short)))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	seekOffset += sizeof(unsigned short);
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	if (!fp.write(jsonStr.c_str(), tempJsonLen))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	seekOffset += tempJsonLen;
	if (!fp.seek(seekOffset, SEEK_SET))
	{
		fp.sync();
		fp.close();
		return 3;
	}

	for (size_t i = 0; i < modelDatas.size(); i++)
	{
		if (!fp.write(&modelDatas[i], 1))
		{
			fp.sync();
			fp.close();
			return 3;
		}

		seekOffset += 1;
		if (!fp.seek(seekOffset, SEEK_SET))
		{
			fp.sync();
			fp.close();
			return 3;
		}
	}

	fp.sync();
	fp.close();

	return 0;
}

void CustomModel::getModelDataBySmallRoutineFormat(std::vector<char> &modelDatas, std::map<unsigned int, char> &colorInfos, std::vector<unsigned int> &convertColors, WCoord offset, int cmdir/* =-1 */)
{
	int iBitPos;
	for (size_t i = 0; i < m_Blocks.size(); i++)
	{
		int blockid = m_Blocks[i].getResID();

		const BlockDef *def = GetDefManagerProxy()->getBlockDef(blockid, false);
		if (def && def->MiniColor > 0)
		{
			unsigned int color = ((def->MiniColor & 0xff) << 16) | (def->MiniColor & 0xff00) | ((def->MiniColor & 0xff0000) >> 16);
			BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
			if (mtl && mtl->isColorableBlock())
			{
				auto blockcolor = mtl->getBlockColor(m_Blocks[i].getData());
				color = ((blockcolor.GetUInt32() & 0xff) << 16) | (blockcolor.GetUInt32() & 0xff00) | ((blockcolor.GetUInt32() & 0xff0000) >> 16);
			}

			char index;
			unsigned int similarColor;
			auto iter = colorInfos.find(color);
			if (iter != colorInfos.end())
			{
				index = iter->second;
			}
			else
			{
				MINIW::ScriptVM::game()->callFunction("getSimilarColor", "i>i", color, &similarColor);
				index = (char)convertColors.size();
				convertColors.push_back(similarColor);
				colorInfos[color] = index;
			}

			iBitPos = m_vRelativeBitPos[i];

			WCoord pos = WCoord(getRealPosByBit(iBitPos, 0), getRealPosByBit(iBitPos, 1), getRealPosByBit(iBitPos, 2));
			if (cmdir == DIR_NEG_X)
			{
				int tempX = pos.x;
				pos.x = pos.z;
				pos.z = BLOCK_MODEL_SIZE - 1 - tempX;
			}
			else if (cmdir == DIR_POS_X)
			{
				int tempZ = pos.z;
				pos.z = pos.x;
				pos.x = BLOCK_MODEL_SIZE - 1 - tempZ;
			}
			else if (cmdir == DIR_POS_Z)
			{
				pos.x = BLOCK_MODEL_SIZE - 1 - pos.x;
				pos.z = BLOCK_MODEL_SIZE - 1 - pos.z;
			}

			char x = pos.x + offset.x;
			char y = pos.y + offset.y;
			char z = pos.z + offset.z;

			modelDatas.push_back(x);
			modelDatas.push_back(y);
			modelDatas.push_back(z);
			modelDatas.push_back(index);
		}
	}
}

int CustomModel::getBlockId(const WCoord& meter)
{
	for (unsigned i = 0; i < m_EditorBlocks.size(); ++i)
	{
		const Block& b = m_EditorBlocks[i];
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord w(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (w == meter)
		{
			return b.getResID();
		}
	}
	return -1;
}

int CustomModel::getBlockIndex(const WCoord& meter)
{
	for (unsigned i = 0; i < m_EditorBlocks.size(); ++i)
	{
		const Block& b = m_EditorBlocks[i];
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord w(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (w == meter)
		{
			return i;
		}
	}
	return -1;
}

int CustomModel::removeBlock(const WCoord& meter)
{
	for (unsigned i = 0; i < m_EditorBlocks.size(); ++i)
	{
		const Block& b = m_EditorBlocks[i];
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord w(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (w == meter)
		{
			m_EditorBlocks.erase(m_EditorBlocks.begin() + i);
			//m_RelativePos.erase(m_RelativePos.begin() + i);
			m_vEditorRelativeBitPos.erase(m_vEditorRelativeBitPos.begin() + i);
			m_BlocksPos.erase(posBit);
			return i;
		}
	}
	return -1;
}

void CustomModel::debugPrint()
{
	for (unsigned i = 0; i < m_Blocks.size(); ++i)
	{
		const Block& b = m_Blocks[i];
		const int& posBit = m_vRelativeBitPos[i];
		WCoord w(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		//LOG_DEBUG("(%4d, %4d, %4d) = %4d", w.x, w.y, w.z, b.getResID());
	}
}

bool CustomModel::placeBlock(const WCoord& meter, const int id, const int data)
{
	Block block = Block::makeBlock(id, data);
	int posBit = covertPosToBit(meter.x, meter.y, meter.z);
	if(m_BlocksPos.find(posBit) == m_BlocksPos.end())
	{
		m_EditorBlocks.push_back(block);
		//m_RelativePos.push_back(meter);
		m_vEditorRelativeBitPos.push_back(posBit);
		m_BlocksPos.emplace(posBit);
		return true;
	}
	return false;
}

int CustomModel::moveBlock(const WCoord& wSrc, const WCoord& wDst)
{
	if (wSrc == wDst)
	{
		return -1;
	}
	int i = getBlockIndex(wSrc);
	if (i < 0)
	{
		return -1;
	}
	const int& posBit = m_vEditorRelativeBitPos[i];
	m_vEditorRelativeBitPos[i] = covertPosToBit(wDst.x, wDst.y, wDst.z);
	m_BlocksPos.erase(posBit);
	m_BlocksPos.emplace(m_vEditorRelativeBitPos[i]);
	//m_RelativePos[i] = wDst;
	return i;
}

bool CustomModel::empty()
{
	return m_Blocks.size() <= 0;
}

Rainbow::Vector3f CustomModel::getCenter()
{
	return m_Box / 2.f;
}

void CustomModel::resetOriginCoord()
{
	if (m_Blocks.size() <= 0)
	{
		m_MinPos = Rainbow::Vector3f(0.f);
		m_MaxPos = Rainbow::Vector3f(0.f);
		m_Box = Rainbow::Vector3f(10,10,10);
		return;
	}

	m_MinPos = Rainbow::Vector3f(BLOCK_FSIZE);
	m_MaxPos = Rainbow::Vector3f(0.f);

	for (unsigned i = 0; i < m_Blocks.size(); ++i)
	{
		const Block& b = m_Blocks[i];
		const int& posBit = m_vRelativeBitPos[i];
		WCoord w(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		updateRange(w);
	}
	m_Box = m_MaxPos - m_MinPos + 1.f;
}

void CustomModel::updateRange(const WCoord& w)
{
	updateRange(w.x, w.y, w.z);
}

void CustomModel::updateRange(const int& x, const int& y, const int& z)
{
	if (x < m_MinPos.x)
		m_MinPos.x = x;
	if (y < m_MinPos.y)
		m_MinPos.y = y;
	if (z < m_MinPos.z)
		m_MinPos.z = z;
	if (x > m_MaxPos.x)
		m_MaxPos.x = x;
	if (y > m_MaxPos.y)
		m_MaxPos.y = y;
	if (z > m_MaxPos.z)
		m_MaxPos.z = z;
}
int CustomModel::maxX()
{
	int x = 0;
	for (unsigned i = 0; i < m_vEditorRelativeBitPos.size(); ++i)
	{
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord pos(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (pos.x >= x)
		{
			x = pos.x+1;
		}
	}
	return x;
}
int CustomModel::maxY()
{
	int y = 0;
	for (unsigned i = 0; i < m_vEditorRelativeBitPos.size(); ++i)
	{
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord pos(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (pos.y >= y)
		{
			y = pos.y+1;
		}
	}
	return y;
}
int CustomModel::maxZ()
{
	int z = 0;
	for (unsigned i = 0; i < m_vEditorRelativeBitPos.size(); ++i)
	{
		const int& posBit = m_vEditorRelativeBitPos[i];
		WCoord pos(
			getRealPosByBit(posBit, 0),
			getRealPosByBit(posBit, 1),
			getRealPosByBit(posBit, 2)
		);
		if (pos.z >= z)
		{
			z = pos.z+1;
		}
	}
	return z;
}
void CustomModel::clearBlocks()
{
	m_EditorBlocks.clear();
}
void CustomModel::clearRelativePos()
{
	m_vEditorRelativeBitPos.clear();
	m_BlocksPos.clear();
}

std::vector<Block> CustomModel::getBlocks()
{
	return m_Blocks;
}
std::vector<int> CustomModel::getRelativePos()
{
	return m_vRelativeBitPos;
}
void CustomModel::calculateDim()
{
	if (m_EditorDim.isZero())
	{
		auto dim = WCoord(maxX(), maxY(), maxZ());
		if (dim.isZero())
		{
			m_EditorDim = WCoord(10, 10, 10);
		}
		else
		{
			m_EditorDim = dim;
		}
	}
}
WCoord	CustomModel::getDim()
{
	return m_EditorDim;
}
void	CustomModel::setDim(WCoord dim)
{
	m_EditorDim = dim;
}
void CustomModel::clearModeData(ITEM_MESH_TYPE meshType)
{
	if (m_ModelData[meshType])
	{
		m_ModelData[meshType] = nullptr;
	}
}
