#include "AirDropChest.h"
#include "WorldManager.h"
#include "world.h"
#include "container_world.h"
#include "ActorManagerInterface.h"
#include "ClientActorManager.h"
#include <cmath>
#include "ActorLocoMotion.h"
#include "SandboxMacros.h"
#include "ActorCubeChest.h"
#include "ActorAirDropParachute.h"
#include "ActorManager.h"
#include "ActorSnowMan.h"

AirDropChest::AirDropChest(const Rainbow::Vector3f& spawnPos, const Rainbow::Vector3f& targetPos, int damageHP)
    : m_spawnPos(spawnPos)
    , m_targetPos(targetPos)
    , m_currentPos(spawnPos)
    , m_damageHP(damageHP)
    , m_parachute(nullptr)
{
    if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
        auto pWorld = g_WorldMgr->getWorld(0);
        // 空投伞
        m_parachute = ActorAirDropParachute::create(pWorld, targetPos.x, targetPos.y, targetPos.z, 0, 0, 0);
        m_parachute->setScale(2.0f);
        m_parachute->setDamageHP(m_damageHP);
        m_parachute_objid = m_parachute->getObjId();
    }
    m_chest = nullptr;
    m_chest_objid = 0;
    m_isSpawn = false;
    m_chestPos = WCoord(0, 0, 0);
    LOG_INFO("AirDropChest::AirDropChest() spawn");
}

AirDropChest::~AirDropChest(){
    LOG_INFO("AirDropChest::~AirDropChest()");
}
bool AirDropChest::isActorExist(long long objId) {
    auto pWorld = g_WorldMgr->getWorld(0);
    // 检查item指针是否有效，避免野指针异常
    if (pWorld && pWorld->getActorMgr()) {
        ClientActorMgr* actormgr = pWorld->getActorMgr()->ToCastMgr();
        return actormgr->isActorExist(objId);
    }
    return false;
}
void AirDropChest::Update(float deltaTime) {

#ifdef IWORLD_SERVER_BUILD
    if (m_hasLanded) {
        return;
    }

    if (!m_parachute || !isActorExist(m_parachute_objid)) {
        //LOG_INFO("ActorCubeChes::Update m_parachute isActorExist=false");
        return;
    }

    m_parachute->tick();
    m_parachute->update(deltaTime);
    auto* loclmo = m_parachute->getLocoMotion();

    auto* pchest = static_cast<ActorBall*>(m_parachute);
    bool isstop = pchest->isStoppedMoving();

    if (loclmo && isstop) {
        m_hasLanded = true;
        
        // 再次检查指针有效性
        if (!m_parachute || !isActorExist(m_parachute_objid)) {
            LOG_INFO("ActorCubeChes::Update m_parachute isActorExist=false" );
            return;
        }

        if (g_WorldMgr && g_WorldMgr->getWorld(0)) {
            auto pWorld = g_WorldMgr->getWorld(0);
            auto pos = m_parachute->getPosition();

            this->m_parachute->setNeedClear();
            this->m_parachute = nullptr;

            createAirDropChest(pos);
        }
    }
#endif
}
void AirDropChest::setChestModleTyle(std::string modelType)
{
    m_chestModelType = modelType;
}

void AirDropChest::setChestId(int chestId) {
     m_chestId = chestId;
}

void AirDropChest::createAirDropChest(WCoord &pos) {
    // 检查世界管理器和世界是否有效
    if (!g_WorldMgr || !g_WorldMgr->getWorld(0)) {
        return;
    }

    World* pWorld = g_WorldMgr->getWorld(0);
    
    if (m_chestModelType=="block") {
        //方块宝箱的坐标使用米
        pos.x = pos.x / 100;
        pos.z = pos.z / 100;
        pos.y = pos.y / 100 - 1;
        bool b = pWorld->setBlockAll(pos, m_chestId, 0, 3);
        if (b) {
            WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pWorld->getContainerMgr());
            if (containerMgr) {
                containerMgr->addDungeonChest(pos, m_chestId, nullptr);
                m_chestPos = pos;
                m_isSpawn = true;
            }
        }
        else {
            LOG_INFO("ActorCubeChest spawned error, at position (%d, %d, %d)", pos.x, pos.y, pos.z);
            m_isSpawn = false;
            m_chestPos = pos;
        }
    }else{
        //Actor宝箱的坐标使用厘米
        pos.x = pos.x;
        pos.y = pos.y + 200;
        pos.z = pos.z;
        // 创建一个储物箱Actor
        //ActorBall* actor = ActorBall::create(pWorld, pos.x, pos.y, pos.z, 0, 0, 0);
        ActorCubeChest* actor = ActorCubeChest::create(pWorld, pos.x, pos.y, pos.z, m_chestId);
        m_chestPos = pos;
        m_chest = actor;
        m_chest_objid = actor->getObjId();
        m_isSpawn = true;

    }

    // 记录日志
    LOG_INFO("ActorCubeChest spawned at position (%d, %d, %d)",pos.x, pos.y, pos.z);
}