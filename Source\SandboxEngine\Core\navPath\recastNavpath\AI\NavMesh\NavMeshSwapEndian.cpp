//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON> <EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//


#include "NavMeshSwapEndian.h"
#include "NavMesh.h"
#include "Utilities/Align.h"


inline void swapByte(unsigned char* a, unsigned char* b)
{
    unsigned char tmp = *a;
    *a = *b;
    *b = tmp;
}

inline void swapEndian(unsigned short* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 1);
}

inline void swapEndian(short* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 1);
}

inline void swapEndian(unsigned int* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 3); swapByte(x + 1, x + 2);
}

inline void swapEndian(int* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 3); swapByte(x + 1, x + 2);
}

inline void swapEndian(NavMeshPolyRef* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 7); swapByte(x + 1, x + 6); swapByte(x + 2, x + 5); swapByte(x + 3, x + 4);
}

inline void swapEndian(float* v)
{
    unsigned char* x = (unsigned char*)v;
    swapByte(x + 0, x + 3); swapByte(x + 1, x + 2);
}

bool NavMeshHeaderSwapEndian(unsigned char* data)
{
    NavMeshDataHeader* header = (NavMeshDataHeader*)data;

    int swappedMagic = kNavMeshMagic;
    int swappedVersion = kNavMeshVersion;
    swapEndian(&swappedMagic);
    swapEndian(&swappedVersion);

    if ((header->magic != kNavMeshMagic || header->version != kNavMeshVersion) &&
        (header->magic != swappedMagic || header->version != swappedVersion))
    {
        return false;
    }

    swapEndian(&header->magic);
    swapEndian(&header->version);
    swapEndian(&header->x);
    swapEndian(&header->y);
    swapEndian(&header->agentTypeId);
    swapEndian(&header->polyCount);
    swapEndian(&header->vertCount);
    swapEndian(&header->detailMeshCount);
    swapEndian(&header->detailVertCount);
    swapEndian(&header->detailTriCount);
    swapEndian(&header->bvNodeCount);
    swapEndian(&header->bmin[0]);
    swapEndian(&header->bmin[1]);
    swapEndian(&header->bmin[2]);
    swapEndian(&header->bmax[0]);
    swapEndian(&header->bmax[1]);
    swapEndian(&header->bmax[2]);
    swapEndian(&header->bvQuantFactor);

    // Freelist index and pointers are updated when tile is added, no need to swap.

    unsigned char* dataEnd = ((unsigned char*)&header->bvQuantFactor) + sizeof(header->bvQuantFactor);
    int size = (int)(dataEnd - data);

    return size == sizeof(NavMeshDataHeader);
}

bool NavMeshDataSwapEndian(unsigned char* data, const int dataSize)
{
    // Make sure the data is in right format.
    NavMeshDataHeader* header = (NavMeshDataHeader*)data;
    if (header->magic != kNavMeshMagic)
        return false;
    if (header->version != kNavMeshVersion)
        return false;

    // Patch header pointers.
    const int headerSize = Align4(sizeof(NavMeshDataHeader));
    const int vertsSize = Align4(sizeof(float) * 3 * header->vertCount);
    const int polysSize = Align4(sizeof(NavMeshPoly) * header->polyCount);
    const int detailMeshesSize = Align4(sizeof(NavMeshPolyDetail) * header->detailMeshCount);
    const int detailVertsSize = Align4(sizeof(float) * 3 * header->detailVertCount);
    const int detailTrisSize = Align4(sizeof(NavMeshPolyDetailIndex) * 4 * header->detailTriCount);
    const int bvtreeSize = Align4(sizeof(NavMeshBVNode) * header->bvNodeCount);

    unsigned char* d = data + headerSize;
    float* verts = (float*)d; d += vertsSize;
    NavMeshPoly* polys = (NavMeshPoly*)d; d += polysSize;
    NavMeshPolyDetail* detailMeshes = (NavMeshPolyDetail*)d; d += detailMeshesSize;
    float* detailVerts = (float*)d; d += detailVertsSize;
    NavMeshPolyDetailIndex* detailTris = (NavMeshPolyDetailIndex*)d; d += detailTrisSize;
    NavMeshBVNode* bvTree = (NavMeshBVNode*)d; d += bvtreeSize;

    const int swapSize = (int)(d - data);

    // Vertices
    for (int i = 0; i < header->vertCount * 3; ++i)
    {
        swapEndian(&verts[i]);
    }

    // Polys
    for (int i = 0; i < header->polyCount; ++i)
    {
        NavMeshPoly* p = &polys[i];
        // poly->firstLink is update when tile is added, no need to swap.
        for (int j = 0; j < kNavMeshVertsPerPoly; ++j)
        {
            swapEndian(&p->verts[j]);
            swapEndian(&p->neis[j]);
        }
        swapEndian(&p->flags);
    }

    // Links are rebuild when tile is added, no need to swap.

    // Detail meshes
    for (int i = 0; i < header->detailMeshCount; ++i)
    {
        NavMeshPolyDetail* pd = &detailMeshes[i];
        swapEndian(&pd->vertBase);
        swapEndian(&pd->triBase);
        swapEndian(&pd->vertCount);
        swapEndian(&pd->triCount);
    }

    // Detail verts
    for (int i = 0; i < header->detailVertCount * 3; ++i)
    {
        swapEndian(&detailVerts[i]);
    }

    // Detail triangles
    for (int i = 0; i < header->detailTriCount * 4; ++i)
    {
        swapEndian(&detailTris[i]);
    }

    // BV-tree
    for (int i = 0; i < header->bvNodeCount; ++i)
    {
        NavMeshBVNode* node = &bvTree[i];
        for (int j = 0; j < 3; ++j)
        {
            swapEndian(&node->bmin[j]);
            swapEndian(&node->bmax[j]);
        }
        swapEndian(&node->i);
    }

    return swapSize == dataSize;
}
