#include "CustomAvatarModelData.h"
#include "ClientActorHelper.h"
#include "FullyCustomBoneData.h"
#include "proto_common.pb.h"

CustomAvatarModelData::CustomAvatarModelData() : 
	scale(1.f), yaw(0), pitch(0), roll(0), offset_x(0), offset_y(0), offset_z(0),
	model(NULL), show(true), newrotatemode(true), isdownload(false),
	scale3(1.f)
{

}

void CustomAvatarModelData::setModelScale(float scale)
{
	this->scale = scale;
	scale3.Set(scale, scale, scale);
	if (model)
		model->SetScale(scale3);
}

void CustomAvatarModelData::setPosition(short x, short y, short z)
{
	this->offset_x = x;
	this->offset_y = y;
	this->offset_z = z;
	if (model)
		model->SetPosition(Rainbow::Vector3f(x, y, z));
}

void CustomAvatarModelData::setRotation(float yaw, float pitch, float roll)
{
	this->yaw = yaw;
	this->pitch = pitch;
	this->roll = roll;
	if (model)
	{
		//Rainbow::Quaternionf quat;
		//quat = Rainbow::Quaternionf::XYZAngleToQuat(pitch, yaw, roll);
		//quat.setEulerAngle(pitch, yaw, roll);
		this->newrotatemode = true;
		model->SetRotation(true, yaw, pitch, roll);
	}
}

void CustomAvatarModelData::setOverlayColor(bool show)
{
	if (!model)
		return;

	if (show)
	{
		Rainbow::ColourValue color(0, 0, 1.0f);
		model->SetOverlayColor(&color);
	}
	else
	{
		model->SetOverlayColor(NULL);
	}
}

void CamdFromFbs(CustomAvatarModelData& camd, const FBSave::AvatarModelData* fbsAmd)
{
	if (!fbsAmd)
	{
		return;
	}
	camd.modelfilename = fbsAmd->modelfilename()->c_str();
	camd.setModelScale(fbsAmd->scale());
	camd.yaw = fbsAmd->yaw();
	camd.pitch = fbsAmd->pitch();
	camd.offset_x = fbsAmd->offsetpos()->x();
	camd.offset_y = fbsAmd->offsetpos()->y();
	camd.offset_z = fbsAmd->offsetpos()->z();
	if (fbsAmd->roll())
		camd.roll = fbsAmd->roll();
	camd.newrotatemode = fbsAmd->newrotatemode();
	if (fbsAmd->scale3())
		camd.scale3 = Vec3ToVector3(fbsAmd->scale3());
}

void CamdFromPb(CustomAvatarModelData& camd, const game::common::PB_ActorOneAvatarModelData* pbAoamd)
{
	if (!pbAoamd)
	{
		return;
	}
	camd.modelfilename = pbAoamd->modelfilename();
	camd.setModelScale(pbAoamd->scale());
	if (pbAoamd->has_scale3())
		camd.scale3 = PbVec3ToVector3(pbAoamd->scale3());
	camd.yaw = (float)pbAoamd->yaw();
	camd.pitch = (float)pbAoamd->pitch();
	if (pbAoamd->has_roll())
		camd.roll = (float)pbAoamd->roll();
	camd.offset_x = pbAoamd->offsetpos().x();
	camd.offset_y = pbAoamd->offsetpos().y();
	camd.offset_z = pbAoamd->offsetpos().z();
	if (pbAoamd->has_newrotatemode())
		camd.newrotatemode = pbAoamd->newrotatemode();
}

void CamdFromFcbd(CustomAvatarModelData& camd, const FullyCustomBoneData* fcbd)
{
	if (!fcbd)
	{
		return;
	}
	camd.modelfilename = fcbd->model;
	camd.offset_x = (short)fcbd->submodelpos.x;
	camd.offset_y = (short)fcbd->submodelpos.y;
	camd.offset_z = (short)fcbd->submodelpos.z;
	camd.show = fcbd->show;
	Rainbow::Vector3f euler = Rainbow::QuaternionToEulerAngle(fcbd->submodelquat);
	camd.pitch = euler.x;
	camd.yaw = euler.y;
	camd.roll = euler.z;
	camd.setModelScale(fcbd->submodelscale);
	camd.scale3 = fcbd->submodelscale3;
}

void CamdToPb(const CustomAvatarModelData& camd, game::common::PB_ActorOneAvatarModelData* pbAoamd)
{
	if (!pbAoamd)
	{
		return;
	}
	pbAoamd->set_modelfilename(camd.modelfilename);
	pbAoamd->set_scale(camd.scale);
	pbAoamd->set_yaw((int)camd.yaw);
	pbAoamd->set_pitch((int)camd.pitch);
	pbAoamd->set_roll((int)camd.roll);

	game::common::PB_Vector3& offsetPos = *pbAoamd->mutable_offsetpos();
	offsetPos.set_x(camd.offset_x);
	offsetPos.set_y(camd.offset_y);
	offsetPos.set_z(camd.offset_z);
	pbAoamd->set_newrotatemode(camd.newrotatemode);
	game::common::PB_Vector3& pbv3Scale = *pbAoamd->mutable_scale3();
	pbv3Scale.set_x(camd.scale3.x);
	pbv3Scale.set_y(camd.scale3.y);
	pbv3Scale.set_z(camd.scale3.z);
}
