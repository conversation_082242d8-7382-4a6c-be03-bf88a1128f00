//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#include "./ObstacleAvoidanceQuery.h"
#include "CrowdTypes.h"
#include "Obstacles/HullAvoidance.h"

#include <float.h>
#include <math.h>
#include <algorithm>


#define DEBUG_VELOCITY_SAMPLES 1

static const float kSegmentTouchThr = 0.05f;

static inline float PerpDot(const Rainbow::Vector2f& u, const Rainbow::Vector2f& v)
{
    return u.y * v.x - u.x * v.y;
}

static inline Rainbow::Vector2f Vec2Load3(const Rainbow::Vector3f& u3)
{
    return Rainbow::Vector2f(u3.x, u3.z);
}

// return time of impacts between ray and circle
// returns 0 when no impact otherwise 1
// ray: starting at 'pos' moving with velocity 'vel'
// circle: positioned at 'cen' with radius 'rad'
static int RayCircle(const Rainbow::Vector2f& pos, const Rainbow::Vector2f& vel, const Rainbow::Vector2f& cen, float rad, float* tmin, float* tmax)
{
    const float EPS = 0.0001f;
    Rainbow::Vector2f s = cen - pos;
    const float c = SqrMagnitude(s) - rad * rad;
    float a = SqrMagnitude(vel);
    if (a < EPS)
        return 0;           // not moving

    // Overlap, calc time to exit.
    const float b = Dot(vel, s);
    const float d = b * b - a * c;
    if (d < 0.0f)
        return 0;           // no intersection.
    a = 1.0f / a;
    const float rd = sqrtf(d);
    *tmin = (b - rd) * a;
    *tmax = (b + rd) * a;
    return 1;
}

// return time of impact between ray and line segment (FLT_MAX when no impact)
// ray: starting at 'pos' moving with velocity 'vel'
// segment: between points 's1' and 's2'
static float RaySegment(const Rainbow::Vector2f& pos, const Rainbow::Vector2f& vel, const Rainbow::Vector2f& s1, const Rainbow::Vector2f& s2)
{
    Rainbow::Vector2f ds = s2 - s1;
    Rainbow::Vector2f dp = pos - s1;

    // test: ray/seg parallel
    float den = PerpDot(vel, ds);
    if (fabsf(den) < 1e-6f)
        return FLT_MAX;
    den = 1.0f / den;

    // test: seg behind ray
    const float tt = PerpDot(ds, dp) * den;
    if (tt < 0)
        return FLT_MAX;

    // test: ray misses seg
    const float ss = PerpDot(vel, dp) * den;
    if (ss < 0 || ss > 1)
        return FLT_MAX;

    return tt;
}

static int RaySlab(const Rainbow::Vector2f& pos, const Rainbow::Vector2f& vel,
    const Rainbow::Vector2f& center, const Rainbow::Vector2f& dir, float len, float rad,
    float* tmin, float* tmax)
{
    if (len < 1e-6f)
        return RayCircle(pos, vel, center, rad, tmin, tmax);

    const Rainbow::Vector2f axisX(dir.x, dir.y);
    const Rainbow::Vector2f axisY(-dir.y, dir.x);
    const float sizeX = len;
    const float sizeY = rad;

    const float distX = Dot(axisX, center);
    const float distY = Dot(axisY, center);
    *tmin = -FLT_MAX;
    *tmax = FLT_MAX;

    int hit = 0;
    float nom, den;

    den = Dot(axisX, vel);
    nom = Dot(axisX, pos);

    if (fabsf(den) > 1e-6f)
    {
        den = 1.0f / den;
        const float t0 = -(nom - distX + sizeX) * den;
        const float t1 = -(nom - distX - sizeX) * den;
        if (den > 0)
        {
            // t0 leaving, t1 entering
            *tmin = std::max(*tmin, t0);
            *tmax = std::min(*tmax, t1);
        }
        else
        {
            // t1 leaving, t0 entering
            *tmin = std::max(*tmin, t1);
            *tmax = std::min(*tmax, t0);
        }
        hit = 1;
    }

    den = Dot(axisY, vel);
    nom = Dot(axisY, pos);
    if (fabsf(den) > 1e-6f)
    {
        den = 1.0f / den;
        const float t0 = -(nom - distY + sizeY) * den;
        const float t1 = -(nom - distY - sizeY) * den;
        if (den > 0)
        {
            // t0 leaving, t1 entering
            *tmin = std::max(*tmin, t0);
            *tmax = std::min(*tmax, t1);
        }
        else
        {
            // t1 leaving, t0 entering
            *tmin = std::max(*tmin, t1);
            *tmax = std::min(*tmax, t0);
        }
        hit = 1;
    }

    if (*tmax < *tmin)
        return 0;

    return hit;
}

static inline float NearestPtPointSegment(const Rainbow::Vector2f& pt, const Rainbow::Vector2f& s1, const Rainbow::Vector2f& s2)
{
    const Rainbow::Vector2f ds = s2 - s1;
    const Rainbow::Vector2f dp = pt - s1;
    const float den = SqrMagnitude(ds);
    if (den == 0.0f)
        return 0.0f;
    float t = Dot(ds, dp) / den;
    return clamp01(t);
}

static inline float PointSegmentDistanceSqr(const Rainbow::Vector2f& pt, const Rainbow::Vector2f& s1, const Rainbow::Vector2f& s2)
{
    const Rainbow::Vector2f ds = s2 - s1;
    const Rainbow::Vector2f dp = pt - s1;
    const float den = SqrMagnitude(ds);
    if (den == 0.0f)
        return SqrMagnitude(dp);

    float tt = Dot(ds, dp) / den;
    tt = tt > 1.0f ? 1.0f : tt < 0.0f ? 0.0f : tt;

    Rainbow::Vector2f p =  ds * tt  - dp;
    return SqrMagnitude(p);
}

static inline float SpanArea(const Rainbow::Vector2f& a, const Rainbow::Vector2f& b, const Rainbow::Vector2f& c)
{
    Rainbow::Vector2f db = b - a;
    Rainbow::Vector2f dc = c - a;
    return PerpDot(db, dc);
}

ObstacleAvoidanceQuery::ObstacleAvoidanceQuery()
{
    Reset(Rainbow::Vector3f(0.0f, 0.0f, 0.0f), Rainbow::Quaternionf::identity, 0, 0);
}

ObstacleAvoidanceQuery::~ObstacleAvoidanceQuery()
{
}

void ObstacleAvoidanceQuery::Reset(const Rainbow::Vector3f& pos, const Rainbow::Quaternionf& rot, float rad, float height)
{
    m_CircleCount = 0;
    m_SegmentCount = 0;
    m_SlabCount = 0;

    m_Radius = rad;
    m_Height = height;

    m_WorldToLocal.SetTRInverse(pos, rot);
}

void ObstacleAvoidanceQuery::AddCircle(const Rainbow::Vector3f& pos, float rad, float weight,
    const Rainbow::Vector3f& vel, const Rainbow::Vector3f& dvel)
{
    if (m_CircleCount >= kCircleCapacity)
        return;

    Circle& cir = m_Circles[m_CircleCount++];
    Rainbow::Vector3f relPos = m_WorldToLocal.MultiplyPoint3(pos);
    Rainbow::Vector3f relVel = m_WorldToLocal.MultiplyVector3(vel);
    cir.pos = Vec2Load3(relPos);
    cir.vel = Vec2Load3(relVel);
    cir.radius = rad + m_Radius;
    cir.weight = weight;
}

void ObstacleAvoidanceQuery::AddBoundarySegment(const Rainbow::Vector3f& start, const Rainbow::Vector3f& end)
{
    if (m_SegmentCount >= kSegmentCapacity)
        return;

    Rainbow::Vector3f relStart = m_WorldToLocal.MultiplyPoint3(start);
    Rainbow::Vector3f relEnd = m_WorldToLocal.MultiplyPoint3(end);

    Rainbow::Vector2f start2 = Vec2Load3(relStart);
    Rainbow::Vector2f end2 = Vec2Load3(relEnd);

    // Backface cull only edges which do not touch.
    // Collision detection sometimes do not resolve the collision completelly,
    // but it is still important to avoid the edge.
    const Rainbow::Vector2f zero = Rainbow::Vector2f(0, 0);
    bool touch = PointSegmentDistanceSqr(zero, start2, end2) < Rainbow::Sqr(kSegmentTouchThr);
    if (!touch && SpanArea(start2, end2, zero) < 0.0f)
        return;

    if (touch)
    {
        // Push segment outwards to prevent special case handling in sampling code.
        Rainbow::Vector2f dir = NormalizeSafe(end2 - start2);
        Rainbow::Vector2f normal(-dir.y, dir.x);
        float dist = -Dot(normal, end2);
        Rainbow::Vector2f offset = normal * std::max(0.0f, dist + kSegmentTouchThr * 0.5f);
        start2 += offset;
        end2 += offset;
    }

    Segment& seg = m_Segments[m_SegmentCount++];
    seg.start = start2;
    seg.end = end2;
    // Boundary segments lower priority than other obstacles so that the agent will try to avoid
    // more important obstacles (i.e. other agents) before walls.
    seg.priority = 0.5f;
}

void ObstacleAvoidanceQuery::AddCapsule(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& extents, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& vel)
{
    Rainbow::Vector2f points[2];
    float radius = 0.0f;

    Rainbow::Vector3f relPos = m_WorldToLocal.MultiplyPoint3(pos);
    Rainbow::Vector3f relAxis = m_WorldToLocal.MultiplyVector3(yAxis);
    Rainbow::Vector3f relVel = m_WorldToLocal.MultiplyVector3(vel);

    int npoints = CalculateClippedCapsule(points, &radius, relPos, extents, relAxis, 0.0f, m_Height);

    if (npoints == 0)
        return;

    for (int i = 0; i < npoints; i++)
    {
        const Rainbow::Vector2f& pt = points[i];
        if (m_CircleCount < kCircleCapacity)
        {
            Circle& cir = m_Circles[m_CircleCount++];
            cir.pos = pt;
            cir.vel = Vec2Load3(relVel);
            cir.radius = radius + m_Radius;
            cir.weight = 1.0f; // No reciprocity, agent handles full avoidance.
        }
    }

    if (npoints > 1)
    {
        const Rainbow::Vector2f& s1 = points[0];
        const Rainbow::Vector2f& s2 = points[1];
        if (m_SlabCount < kSlabCapacity)
        {
            Slab& slab = m_Slabs[m_SlabCount++];
            slab.start = s1;
            slab.end = s2;
            slab.vel = Vec2Load3(relVel);
            slab.radius = radius + m_Radius;
            slab.weight = 1.0f; // No reciprocity, agent handles full avoidance.
        }
    }
}

void ObstacleAvoidanceQuery::AddBox(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& extents,
    const Rainbow::Vector3f& xAxis, const Rainbow::Vector3f& yAxis, const Rainbow::Vector3f& zAxis,
    const Rainbow::Vector3f& vel)
{
    Rainbow::Vector3f relPos = m_WorldToLocal.MultiplyPoint3(pos);
    Rainbow::Vector3f relAxisX = m_WorldToLocal.MultiplyVector3(xAxis);
    Rainbow::Vector3f relAxisY = m_WorldToLocal.MultiplyVector3(yAxis);
    Rainbow::Vector3f relAxisZ = m_WorldToLocal.MultiplyVector3(zAxis);
    Rainbow::Vector3f relVel = m_WorldToLocal.MultiplyVector3(vel);

    Rainbow::Vector3f boxCorners[8];
    CalculateOrientedBoxCorners(boxCorners, relPos, extents, relAxisX, relAxisY, relAxisZ);

    Rainbow::Vector2f hullSegments[2 * 12];
    Rainbow::Vector2f hullCorners[2 * 12];
    bool visible[12];
    bool touched[12];


    int cornerCount = CalculateExpandedClippedBoxConvexHull(hullSegments, hullCorners, boxCorners, 0.0f, m_Height, m_Radius);

    // Calculate segment visiblity based on the non-offset hull.
    for (int i = 0, j = cornerCount - 1; i < cornerCount; j = i++)
    {
        const Rainbow::Vector2f& c1 = hullCorners[j];
        const Rainbow::Vector2f& c2 = hullCorners[i];
        const Rainbow::Vector2f zero = Rainbow::Vector2f(0, 0);
        visible[j] = SpanArea(c1, c2, zero) <= 0.0f;

        const Rainbow::Vector2f& s1 = hullSegments[2 * j + 0];
        const Rainbow::Vector2f& s2 = hullSegments[2 * j + 1];
        // Backface cull only edges which do not touch.
        // Collision detection sometimes do not resolve the collision completelly,
        // but it is still important to avoid the edge.
        touched[j] = PointSegmentDistanceSqr(zero, s1, s2) < Rainbow::Sqr(kSegmentTouchThr);

        // Touched ones are always visible.
        visible[j] |= touched[j];
    }

    for (int i = 0, j = cornerCount - 1; i < cornerCount; j = i++)
    {
        if (visible[j])
        {
            if (m_SlabCount < kSlabCapacity)
            {
                Slab& slab = m_Slabs[m_SlabCount++];
                slab.start = hullCorners[j];
                slab.end = hullCorners[i];
                slab.vel = Vec2Load3(relVel);
                slab.radius = m_Radius;
                slab.weight = 1.0f; // No reciprocity, agent handles full avoidance.
            }
        }

        if (visible[i] || visible[j])
        {
            if (m_CircleCount < kCircleCapacity)
            {
                Circle& cir = m_Circles[m_CircleCount++];
                cir.pos = hullCorners[i]; // Using 'i' here, since it is the shared vertex between consequtive segments.
                cir.vel = Vec2Load3(relVel);
                cir.radius = m_Radius;
                cir.weight = 1.0f; // No reciprocity, agent handles full avoidance.
            }
        }
    }
}

void ObstacleAvoidanceQuery::Prepare(float vmax, float horizTime, const ObstacleAvoidanceParams* params)
{
    for (int i = 0; i < m_SlabCount; i++)
    {
        Slab& slab = m_Slabs[i];
        slab.center = Lerp(slab.start, slab.end, 0.5f);
        slab.dir = slab.end -  slab.start;
        slab.length = Magnitude(slab.dir);
        if (slab.length < 1e-6f)
        {
            slab.length = 0;
            slab.dir.x = 0;
            slab.dir.y = 0;
            continue;
        }
        slab.dir = slab.dir / slab.length;
        slab.length *= 0.5f; // Half extend
    }

    memcpy(&m_Parameters, params, sizeof(ObstacleAvoidanceParams));
    m_HorizTime = horizTime;
    m_InvHorizTime = 1.0f / horizTime;
    m_InvVmax = 1.0f / vmax;
}

float ObstacleAvoidanceQuery::ProcessSample(const Rainbow::Vector2f& vcand, const Rainbow::Vector2f& pos,
    const Rainbow::Vector2f& vel, const Rainbow::Vector2f& dvel, float rad) const
{
    // Find min time of impact and exit amongst all obstacles.
    float toi = m_HorizTime;

    const int ncircles = m_CircleCount;
    for (int i = 0; i < ncircles; ++i)
    {
        const Circle& cir = m_Circles[i];

        // RVO
        const Rainbow::Vector2f tmp = Lerp(vel, cir.vel, cir.weight);
        const Rainbow::Vector2f vab = vcand - tmp;

        float tmin = 0, tmax = 0;
        if (!RayCircle(pos, vab, cir.pos, cir.radius, &tmin, &tmax))
            continue;

        // Handle overlapping obstacles.
        if (tmin < 0.0f && tmax > 0.0f)
        {
            // Avoid more when overlapped.
            tmin = 2.0f * -tmin / tmax; // NOTE: 2.0f is chosen emprically
        }

        if (tmin < toi && tmin >= 0.0f)
        {
            // The closest obstacle is somewhere ahead of us, keep track of nearest obstacle.
            toi = tmin;
        }
    }

    const int nslabs = m_SlabCount;
    for (int i = 0; i < nslabs; ++i)
    {
        const Slab& slab = m_Slabs[i];

        // RVO
        const Rainbow::Vector2f tmp = Lerp(vel, slab.vel, slab.weight);
        const Rainbow::Vector2f vab = vcand - tmp;

        float tmin = 0, tmax = 0;
        if (!RaySlab(pos, vab, slab.center, slab.dir, slab.length, slab.radius, &tmin, &tmax))
            continue;

        // Handle overlapping obstacles.
        if (tmin < 0.0f && tmax > 0.0f)
        {
            // Avoid more when overlapped.
            tmin = 2.0f * -tmin / tmax; // NOTE: 2.0f is chosen emprically
        }

        // TODO: optimize
        // Slight slope towards the tips of the segment.
        // This makes the agents to steer towards the edges of the
        // segment, which will lead them to brush off the obstacle.
        Rainbow::Vector2f hitPt = pos + vab * tmin;
        float t = NearestPtPointSegment(hitPt, slab.start, slab.end);
        t = 1.0f - t * 2.0f;
        const float kSlope = 0.5f;
        tmin *= (1.0f - kSlope) + (t * t) * kSlope;

        if (tmin < toi && tmin >= 0.0f)
        {
            // The closest obstacle is somewhere ahead of us, keep track of nearest obstacle.
            toi = tmin;
        }
    }

    const int nsegments = m_SegmentCount;
    for (int i = 0; i < nsegments; ++i)
    {
        const Segment& seg = m_Segments[i];
        float t = RaySegment(pos, vcand, seg.start, seg.end);
        t /= seg.priority;  // Smaller priority items are avoided less.
        toi = std::min(toi, t);
    }

    const float vpen = m_Parameters.weightDesVel * (Magnitude(vcand - dvel) * m_InvVmax);
    const float vcpen = m_Parameters.weightCurVel * (Magnitude(vcand - vel) * m_InvVmax);
    const float tpen = m_Parameters.weightToi * (1.0f / (0.1f + toi * m_InvHorizTime));
    return vpen + vcpen + tpen;
}

int ObstacleAvoidanceQuery::SampleVelocityAdaptive(Rainbow::Vector3f& outVel3, const Rainbow::Vector3f& vel3, const Rainbow::Vector3f& dvel3,
    const float rad, const float height, const float vmax,
    const float horizTime, const ObstacleAvoidanceParams* params,
    CrowdAgentDebugInfo* debugInfo)
{
#if RAINBOW_EDITOR_ && DEBUG_VELOCITY_SAMPLES
    if (debugInfo != NULL)
    {
        debugInfo->transform = m_WorldToLocal;
        debugInfo->circleCount = 0;
        debugInfo->sampleCount = 0;
        debugInfo->segmentCount = 0;
    }
#endif
    if (horizTime < 1e-5f)
    {
        outVel3 = dvel3;
        return 0;
    }

    if (m_SegmentCount == 0 && m_CircleCount == 0)
    {
        outVel3 = dvel3;
        return 0;
    }

    outVel3 = Rainbow::Vector3f(0, 0, 0);
    if (vmax < 1e-5f)
        return 0;

    Prepare(vmax, horizTime, params);

    const Rainbow::Vector3f rvel3 = m_WorldToLocal.MultiplyVector3(vel3);
    const Rainbow::Vector3f rdvel3 = m_WorldToLocal.MultiplyVector3(dvel3);

    // Build sampling pattern aligned to desired velocity.
    Rainbow::Vector2f pat[kPatternMaxDivs * kPatternMaxRings + 1];

    const int ndivs = (int)m_Parameters.adaptiveDivs;
    const int nrings = (int)m_Parameters.adaptiveRings;

    const int nd = Rainbow::FloatClamp(ndivs, 1, (int)kPatternMaxDivs);
    const int nr = Rainbow::FloatClamp(nrings, 1, (int)kPatternMaxRings);
    const float da = kPI * 2.0f / nd;
    const float dang = atan2f(rdvel3.z, rdvel3.x);

    int npat = 0;

    // Always add sample at zero
    pat[npat++] = Rainbow::Vector2f(0, 0);

    for (int j = 0; j < nr; ++j)
    {
        const float rad = (float)(nr - j) / (float)nr;
        float a = dang + (j & 1) * 0.5f * da;
        for (int i = 0; i < nd; ++i)
        {
            pat[npat++] = Rainbow::Vector2f(cosf(a) * rad, sinf(a) * rad);
            a += da;
        }
    }

    Rainbow::Vector2f vel = Vec2Load3(rvel3);
    Rainbow::Vector2f dvel = Vec2Load3(rdvel3);

    // Start sampling.
    float cr = vmax * (1.0f - m_Parameters.velBias);
    Rainbow::Vector2f res(dvel.x * m_Parameters.velBias, dvel.y * m_Parameters.velBias);
    int ns = 0;

    // The sampling is offset by small factor to favor taking over
    // from right in symmetrical situations.
    const float kSampleOffsetFactor = 0.04f;
    res.x +=  dvel.y * kSampleOffsetFactor;
    res.y += -dvel.x * kSampleOffsetFactor;

    const float vmaxSqr = Rainbow::Sqr(vmax + 0.001f);

    const int depth = (int)m_Parameters.adaptiveDepth;
    for (int k = 0; k < depth; ++k)
    {
        float minPenalty = FLT_MAX;
        Rainbow::Vector2f bestVel(0, 0);

        for (int i = 0; i < npat; ++i)
        {
            Rainbow::Vector2f vcand;
            if (i == 0 && k == 0)
            {
                // Always explicitly test the desired velocity at first depth,
                // to ensure that the dvel is tested because the pattern is offset.
                vcand = dvel;
            }
            else
            {
                vcand = res + pat[i] * cr;
                if (Rainbow::SqrMagnitude(vcand) > vmaxSqr)
                    continue;
            }

            const Rainbow::Vector2f zero = Rainbow::Vector2f(0, 0);
            const float penalty = ProcessSample(vcand, zero, vel, dvel, rad);
            if (penalty < minPenalty)
            {
                minPenalty = penalty;
                bestVel = vcand;
            }
            ns++;
        }

        res = bestVel;
        cr *= 0.5f;
    }

    const Rainbow::Vector3f res3 = Rainbow::Vector3f(res.x, 0, res.y);
    outVel3 = m_WorldToLocal.InverseMultiplyVector3Affine(res3);

    return ns;
}
