#include "BlockMultiTriangle.h"
#include "world.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "basesection.h"
#include "container_sandboxGame.h"
#include "section.h"
#include "BlockGeom.h"
#include "WeatherManager.h"
#include "SectionMesh.h"
#include "ShareRenderMaterial.h"
#include "BlockMaterialMgr.h"
/*
* 
数据位结构
| 位 | 值范围 | 含义 |
|----|--------|------|
| 0-1 | 0-3 | 方向（Direction）|
| 2 | 0/1 | 上/下状态（Upside Down）|
| 3 | 0/1 | 扩展方块标志（Extension Flag）|
* 
索引
3 2
0 1
*/
//左下角为核心

// 根据方向定义扩展方块的位置
// [方向][扩展索引][x/y/z]
const int BlockMultiTriangle::TriangleExtendPos[4][3][3] = {
    // 方向0：E向三角形 (DIR_NEG_X)
    {
        {0, 0, -1},  // 右侧扩展
        {1, 1, -1},  // 对角扩展
        {1, 1, 0},   // 前方扩展
    },
    // 方向1：W向三角形 (DIR_POS_X)
    {
        {0, 0, 1},   // 右侧扩展
        {-1, 1, 1},  // 对角扩展
        {-1, 1, 0}   // 前方扩展
    },
    // 方向2：N向三角形 (DIR_NEG_Z)
    {
        {1, 0, 0},   // 右侧扩展
        {1, 1, 1},   // 对角扩展
        {0, 1, 1}    // 前方扩展
    },
    // 方向3：S向三角形 (DIR_POS_Z)
    {
        {-1, 0, 0},  // 右侧扩展
        {-1, 1, -1}, // 对角扩展
        {0, 1, -1}   // 前方扩展
    }
};

IMPLEMENT_BLOCKMATERIAL(BlockMultiTriangle)

BlockMultiTriangle::BlockMultiTriangle() : WholeTriangleMaterial()
{
    // 如有需要初始化
}

void BlockMultiTriangle::init(int resid)
{
	//void WholeTriangleMaterial::init(int resid)
	{
		CubeBlockMaterial::init(resid);
		SetToggle(BlockToggle_IsOpaqueCube, false);
		SetAttrRenderType(BLOCKRENDER_CUBE_MODEL);

		if (m_LoadOnlyLogic) return;

		char texname[256];
		RenderBlockMaterial* topmtl = nullptr, * sidemtl = nullptr, * bottommtl = nullptr;

		sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
		topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if (topmtl == NULL)
		{
			topmtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);

			sidemtl = topmtl;
			sidemtl->AddRef();
			bottommtl = topmtl;
			bottommtl->AddRef();
		}
		else
		{
			//if (!GetBlockDef()->Texture2.empty()) sidemtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
			//else
			{
				sprintf(texname, "%s_side", GetBlockDef()->Texture1.c_str());
				sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
			}

			sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
			bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
			if (bottommtl == NULL)
			{
				bottommtl = topmtl;
				bottommtl->AddRef();
			}
		}






		sprintf(texname, "ice_snow_top");
		snowTopMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_side", GetBlockDef()->Texture1.c_str());
		snowSideMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_triangle", GetBlockDef()->Texture1.c_str());
		snowTriangleMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_vertical_halftriangle", GetBlockDef()->Texture1.c_str());
		snowVerticalHalfTriangleMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_horizontal_halftriangle", GetBlockDef()->Texture1.c_str());
		snowHorizontalHalfTriangleMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_trapezoid_up", GetBlockDef()->Texture1.c_str());
		snowUpTrapezoidMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_trapezoid_down", GetBlockDef()->Texture1.c_str());
		snowDownTrapezoidMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_trapezoid_up_second", GetBlockDef()->Texture1.c_str());
		snowUpTrapezoidSecondMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		sprintf(texname, "%s_snow_down_more", GetBlockDef()->Texture1.c_str());
		snowDownMoreMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if (!snowTopMtl)
		{
			snowTopMtl = topmtl;
			snowTopMtl->AddRef();
		}
		if (!snowSideMtl)
		{
			snowSideMtl = topmtl;
			snowSideMtl->AddRef();
		}
		if (!snowTriangleMtl)
		{
			snowTriangleMtl = topmtl;
			snowTriangleMtl->AddRef();
		}
		if (!snowVerticalHalfTriangleMtl)
		{
			snowVerticalHalfTriangleMtl = topmtl;
			snowVerticalHalfTriangleMtl->AddRef();
		}
		if (!snowHorizontalHalfTriangleMtl)
		{
			snowHorizontalHalfTriangleMtl = topmtl;
			snowHorizontalHalfTriangleMtl->AddRef();
		}
		if (!snowUpTrapezoidMtl)
		{
			snowUpTrapezoidMtl = topmtl;
			snowUpTrapezoidMtl->AddRef();
		}
		if (!snowDownTrapezoidMtl)
		{
			snowDownTrapezoidMtl = topmtl;
			snowDownTrapezoidMtl->AddRef();
		}
		if (!snowDownMoreMtl)
		{
			snowDownMoreMtl = topmtl;
			snowDownMoreMtl->AddRef();
		}
		if (!snowUpTrapezoidSecondMtl)
		{
			snowUpTrapezoidSecondMtl = topmtl;
			snowUpTrapezoidSecondMtl->AddRef();
		}



		setFaceMtl(DIR_POS_X, sidemtl);
		setFaceMtl(DIR_NEG_Z, sidemtl);
		setFaceMtl(DIR_NEG_X, sidemtl);
		setFaceMtl(DIR_POS_Z, sidemtl);

		setFaceMtl(DIR_POS_Y, topmtl);
		setFaceMtl(DIR_NEG_Y, bottommtl);


		ENG_RELEASE(sidemtl);
		ENG_RELEASE(topmtl);
		ENG_RELEASE(bottommtl);

		m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
		initVertData();
	}
    SetToggle(BlockToggle_HasContainer, true);
}

//void BlockMultiTriangle::initDefaultMtl()
//{
//	if (!isUseCustomModel())
//	{
//		return;
//	}
//	auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_LITTLE);
//	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
//	ENG_RELEASE(mtl); // 计数减1
//}


void BlockMultiTriangle::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

// 从多方块三角形的任何部分找到核心方块位置
WCoord BlockMultiTriangle::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata)
{
    if (blockdata == -1)
        blockdata = pworld->getBlockData(blockpos);
    
    // 如果已经是核心方块，直接返回其位置
    if (isCoreBlock(blockdata))
        return blockpos;
    
    // 获取方向（0-3）
    int direction = blockdata & 3;
    
    // 检查每个扩展位置以找到核心
    for (int i = 0; i < 3; i++)
    {
        // 计算如果当前是扩展#i，核心方块应该在的位置
        WCoord possibleCorePos = blockpos - WCoord(
            TriangleExtendPos[direction][i][0],
            TriangleExtendPos[direction][i][1],
            TriangleExtendPos[direction][i][2]
        );
        
        // 检查此位置是否有有效的核心方块
        int coreBlockData = pworld->getBlockData(possibleCorePos);
        if (pworld->getBlockID(possibleCorePos) == getBlockResID() &&
            isCoreBlock(coreBlockData) && 
            (coreBlockData & 3) == direction)
        {
            return possibleCorePos;
        }
    }
    
    // 未找到有效的核心
    return WCoord(0, -1, 0);
}

int BlockMultiTriangle::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
    // 使用WholeTriangleMaterial获取基本数据（方向和上下状态）
    int baseData = getPlaceBlockDataByPlayer(pworld, player);
    // 核心方块不添加额外标志
    return baseData;
}

int BlockMultiTriangle::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
    // 获取玩家当前面向的方向并转换为0-3
    if (player)
    {
        ClientPlayer* playerTmp = player->GetPlayer();
        if (!playerTmp) return 0;
        
        int dir = playerTmp->getCurPlaceDir();
        float x, y, z;
        playerTmp->getFaceDir(x, y, z);
        int data = dir;
        
        // 保留WholeTriangleMaterial的上/下状态
        if (y > 0)
        {
            data += 4;
        }
        
        return data;
    }
    return 0;
}

bool BlockMultiTriangle::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
	return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount) > 0;
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //ClientPlayer* playerTmp = player->GetPlayer();
    //int toolID = playerTmp->getCurToolID();
    //const ItemDef* def = GetDefManagerProxy()->getItemDef(toolID);
    //if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
    //{
    //    int blockdata = pworld->getBlockData(blockpos);
    //    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //    containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //    if (container)
    //    {
    //        if (container->checkArchitectureResEnough(2, 0, playerTmp, true) >= 0)
    //        {
    //            //todo 维修恢复的hp 材料的扣除
    //            container->addHp(amount);
    //            return true;
    //        }
    //    }
    //}
    //// 斜板没有特殊交互
    //return false;
}

bool BlockMultiTriangle::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
	return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer()) > 0;
}

bool BlockMultiTriangle::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
	return onDamaged(pworld, blockpos, player->GetPlayer(), attack_type, damage);
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //int blockdata = pworld->getBlockData(blockpos);
    //WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //if (container)
    //{
    //    //todo 维修恢复的hp 材料的扣除   
    //    container->addHp(-damage); // 负值减少血量
    //    return true;
    //}
    //return false;
}

void BlockMultiTriangle::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
	onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}

void BlockMultiTriangle::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance)
{
    // 只有核心方块掉落物品
    if (isCoreBlock(blockdata))
    {
        // 移除扩展标志，保留方向和上下状态信息
        WholeTriangleMaterial::dropBlockAsItem(pworld, blockpos, blockdata & 7, droptype, chance);
    }
}

int BlockMultiTriangle::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
    int blockdata = sectionData->getBlock(blockpos).getData();
    
    // 清除扩展标志但保留方向和上下状态
    int cleanData = blockdata & 7;
    
    // 使用WholeTriangleMaterial的方法获取几何体ID
    int geomdir;
    bool is_upside_down;
    
    // 调用父方法获取几何体ID
    return WholeTriangleMaterial::getBlockGeomID(idbuf, dirbuf, sectionData, blockpos, world);
}

WorldContainer* BlockMultiTriangle::createContainer(World* pworld, const WCoord& blockpos)
{
    int blockdata = pworld->getBlockData(blockpos);
    // 获取核心方块位置
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos == blockpos)
    {
		int blockId = pworld->getBlockID(blockpos);
		int bpTypeId = 0;
		int bpLevel = 0;
		initBuildData(blockId, bpTypeId, bpLevel);
		containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
		return container;
    }
    return nullptr;
}

int BlockMultiTriangle::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

WorldContainer* BlockMultiTriangle::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	return GetArchitecturalCoreContainer(pworld, blockpos);
}

void BlockMultiTriangle::getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas)
{
    // 实现物理网格顶点的获取，如果需要的话
}

void BlockMultiTriangle::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	int blockdata = pworld->getBlockData(blockpos);

	// 只有核心方块创建扩展
	if (isCoreBlock(blockdata))
	{
		int direction = blockdata & 3;
		bool isUp = (blockdata & 4) != 0;

		// 创建扩展方块
		for (int i = 0; i < 3; i++)
		{
			WCoord extendPos = blockpos + WCoord(
				TriangleExtendPos[direction][i][0],
				TriangleExtendPos[direction][i][1],
				TriangleExtendPos[direction][i][2]
			);

			// 扩展方块数据：
			// - 位0-1：方向（与核心相同）
			// - 位2：上/下状态（与核心相同）
			// - 位3：扩展方块标志
			int extendData = (blockdata & 7) | 8; // 保留方向和上下状态，添加扩展标志

			// 放置扩展方块
			pworld->setBlockAll(extendPos, getBlockResID(), extendData);
		}
	}
}

void BlockMultiTriangle::onBlockAdded(World* pworld, const WCoord& blockpos)
{
}

bool BlockMultiTriangle::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	if (corePos != WCoord(0, -1, 0))
	{
		auto blockdata = pworld->getBlockData(corePos);
		int direction = blockdata & 3;
		// 收集所有需要删除的扩展方块
		for (int i = 0; i < 3; i++)
		{
			WCoord extendPos = corePos + WCoord(
				TriangleExtendPos[direction][i][0],
				TriangleExtendPos[direction][i][1],
				TriangleExtendPos[direction][i][2]
			);
			blockList.push_back(extendPos);
		}
		if (includeSelf) blockList.push_back(corePos);
		return true;
	}
	return false;
}

void BlockMultiTriangle::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
    // 收集需要移除的方块
    std::vector<WCoord> blocksToRemove;
    
    // 找到核心方块
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos.y < 0)
        return; // 无效的核心位置
    
    // 获取三角形数据（方向和上下状态）
    int direction = blockdata & 3;
    bool isUp = (blockdata & 4) != 0;
    
    // 找到所有需要移除的扩展方块
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = corePos + WCoord(
            TriangleExtendPos[direction][i][0],
            TriangleExtendPos[direction][i][1],
            TriangleExtendPos[direction][i][2]
        );
        
        // 检查这是否是有效的扩展方块
        if (pworld->getBlockID(extendPos) == getBlockResID() && extendPos != blockpos)
        {
            blocksToRemove.push_back(extendPos);
        }
    }
    
    // 如果我们删除的是扩展方块，将核心方块也加入删除列表
    if (!isCoreBlock(blockdata) && pworld->getBlockID(corePos) == getBlockResID() && corePos != blockpos)
    {
        blocksToRemove.push_back(corePos);
    }
    
    // 删除列表中的所有方块
    for (const auto& pos : blocksToRemove)
    {
        pworld->setBlockAll(pos, 0, 0);
    }
}

bool BlockMultiTriangle::canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    ClientPlayer* playerTmp = player->GetPlayer();
    
    // 确定方向
    int direction = playerTmp->getCurPlaceDir();
    float x, y, z;
    playerTmp->getFaceDir(x, y, z);
    bool isUp = y > 0;
    
    // 检查所有扩展位置
    for (int i = 0; i < 3; i++)
    {
        WCoord extendPos = blockpos + WCoord(
            TriangleExtendPos[direction][i][0],
            TriangleExtendPos[direction][i][1],
            TriangleExtendPos[direction][i][2]
        );
        
        auto* mtl = pworld->getBlockMaterial(extendPos);
        
        // 检查这个位置是否可以放置方块
        if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), extendPos))
            return false;
        
        // 检查建造权限
        if (!pworld->CanBuildAtPosition(extendPos, player->getUin()))
            return false;
    }
    
    // 检查核心方块位置权限
    return pworld->CanBuildAtPosition(blockpos, player->getUin());
}

void BlockMultiTriangle::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	int originalData = pworld->getBlockData(blockpos);
	// 设置临时清理的数据
	pworld->setBlockData(blockpos, originalData & 7, kBlockUpdateFlageNone);
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));
	WCoord pos = blockpos * BLOCK_SIZE;
	if (blockheight == 1)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	else
	{
		short step = 10;
		float movesize = 1.f / float(step);
		float heightOffset = 1.f / (float(step));
		int size = BLOCK_SIZE * 0.9f;
		int dir = pworld->getBlockData(blockpos) & 3;

		auto pblock = pworld->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;

		auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != dir && frontDir != ReverseDirection(dir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != dir && backDir != ReverseDirection(dir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		if (warp == -1)
		{
			if (blockheight > 0)
			{
				if (dir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				if (dir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
		else if (1 == warp)
		{
			if (blockheight > 0)
			{
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, int(movesize * i * size)));
						drawBox(pos + WCoord(0, int(heightOffset * float(i) * BLOCK_SIZE), 0), pos + WCoord(int(movesize * (i)*size), BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
		else
		{
			if (blockheight > 0)
			{
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}

			}
			else
			{
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
	}

	pworld->setBlockData(blockpos, originalData, kBlockUpdateFlageNone);
}


int BlockMultiTriangle::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	int blockdata = psection->getBlock(blockpos).getData();
	blockdata = blockdata & 7;
	if (blockdata < 8)
	{
		auto pblock = psection->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;
		DirectionType curDir = DirectionType(blockdata & 3);
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((blockdata & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((blockdata & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		if (warp == -1)
		{
			if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			{
				TrianglePhyModel* pTag = &m_mPhyModel[blockdata];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->triangleCount;
			}
		}
		else
		{
			int downUp = (blockdata & 4) ? 1 : 0;
			if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			{
				TrianglePhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->triangleCount;
			}
		}
	}
	return 0;
}


void BlockMultiTriangle::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;
	//float block_light[16] = { 0 };
	Block pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	curblockdata = curblockdata & 7; // // 临时修改为只包含方向和上/下状态的数据（位0-2）
	DirectionType curDir = DirectionType(curblockdata & 3);

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;
	int weather = 0;
	if (data.m_World && data.m_World->getWeatherMgr())
	{
		weather = data.m_World->getWeatherMgr()->getWeather(blockpos);
	}

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	std::vector<int> wholeFace;
	std::vector<int> triangleFace;
	std::vector<int> slantFace;
	// 	dynamic_array<Vector4f> turnslantFace;
	dynamic_array<int> turnslantFace;
	if (curblockdata & 8)
	{
		//CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		createBlockMeshAngleSnow(data, blockpos, poutmesh);
		return;
		// 		for (int ii = 0; ii < 6; ii++)
		// 		{
		// 			wholeFace.push_back(ii);
		// 		}
	}
	else
	{
		int warp = -1;
		int turnDir = -1;
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir)/* && psection->getNeighborBlock(blockpos, g_DirectionCoord[frontDir]) != pblock*/)
			{
				warp = 0;
				turnDir = frontDir;
			}
		}

		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir)/* && psection->getNeighborBlock(blockpos, g_DirectionCoord[backDir]) != pblock*/)
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}

		if (specialdir == DIR_POS_Y)
		{
			wholeFace.push_back(4);
		}
		else
		{
			wholeFace.push_back(5);
		}
		if (warp == -1)
		{
			wholeFace.push_back(ReverseDirection(curDir));
		}

		if (curDir == DIR_NEG_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(6);//4+2
					triangleFace.push_back(7);//4+3
					slantFace.push_back(0);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 4);//4+3
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(14);//3*4+2
					triangleFace.push_back(15);//3*4+3
					slantFace.push_back(4);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						// 						triangleFace.push_back(turnDir + 12);//3*4+3
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 12);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(2);
					triangleFace.push_back(3);
					slantFace.push_back(1);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(10);//2*4+2
					triangleFace.push_back(11);//2*4+3
					slantFace.push_back(5);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 8);//2*4+3
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 8);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_NEG_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(4);
					triangleFace.push_back(5);
					slantFace.push_back(2);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 4);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(12);//3*4+0
					triangleFace.push_back(13);//3*4+1
					slantFace.push_back(6);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 12);
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 12);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(0);
					triangleFace.push_back(1);
					slantFace.push_back(3);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(8);//2*4+0
					triangleFace.push_back(9);//2*4+1
					slantFace.push_back(7);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 8);
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 8);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
	}
	BlockColor facecolor(255, 255, 255, 0);
	bool isFaceUp = false;
	bool isSnowing = false;//��ѩlock
	if (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER)
	{
		isSnowing = true;
		WCoord blockpos_tmp = blockpos + psection->getOrigin();
		int y = data.m_World->getTopHeight(blockpos_tmp.x, blockpos_tmp.z);
		if ((y - 1) != blockpos_tmp.y)
		{
			isSnowing = false;
		}
	}
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)d;
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;
			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (isSnowing == true)
			{
				pmtl = snowSideMtl;
				if (dir == DIR_POS_Y)
				{
					pmtl = snowTopMtl;
					isFaceUp = true;
				}
				else if (dir == DIR_NEG_Y)
				{
					pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				}
			}
			if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();


			BlockGeomMeshInfo mesh;

			// 			mesh.vertices = vertices;
			mesh.vertices = m_mWholeFace()[d];
			mesh.indices = *indices;
			// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			//BlockColor vertcolor;// r g b a
			////vertcolor.v = 0xffffffff;
			//vertcolor.a = dir_color;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < m_mWholeFace()[d].size(); n++)
			{
				auto& vert = mesh.vertices[n];
				//int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				//int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				//vert.pos.w = (lt1 << 8) | lt2;
				//vert.color = vertcolor;
				//vert.color = vertcolor;
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			//if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			if (psubmesh)
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_NegTrIndices : */m_PosTrIndices;
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (isSnowing == true)
		{
			pmtl = snowTriangleMtl;
			if (isFaceUp == true)
			{
				pmtl = snowSideMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;
		// 		mesh.vertices = vertices;
		if (isSnowing == true)
		{
			mesh.vertices = m_mTriangleFace_uv[d];
		}
		else
		{
			mesh.vertices = m_mTriangleFace[d];
		}
		mesh.indices = *indices;
		//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//BlockColor vertcolor;
		////vertcolor.v = 0xffffffff;
		//vertcolor.a = dir_color;
		for (int n = 0; n < m_mTriangleFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir] + g_DirectionCoord[dir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int sideDir = specialdir == DIR_NEG_Y ? DIR_POS_Y : DIR_NEG_Y;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			InitBlockVertLight(vert, avelt, uvtile);
		}
		//if (psubmesh)
			//psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : turnslantFace)
	{
		// 		DirectionType dir = (DirectionType)d.z;
		DirectionType dir = (DirectionType)((d / 10) % 10);
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}
		}

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_NegTrIndices : */m_PosTrIndices;
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (isSnowing == true)
		{
			pmtl = snowTopMtl;
			if (isFaceUp == true)
			{
				pmtl = snowSideMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mTurnSlantFace[d];
		mesh.indices = *indices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		// }
		for (int n = 0; n < m_mTurnSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : slantFace)
	{
		DirectionType dir = (DirectionType)curDir;
		FaceVertexLight faceUpDownVertexLight;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}
		}

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (isSnowing == true)
		{
			pmtl = snowTopMtl;
			if (isFaceUp == true)
			{
				pmtl = snowSideMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		// 		mesh.vertices = vertices;
		mesh.vertices = m_mSlantFace[d];
		mesh.indices = *indices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		// }
		for (int n = 0; n < m_mSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
#endif
}

void BlockMultiTriangle::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	bool mirror;
	int dir = 3 & blockdata;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	mirror = false;


	mtl = getDefaultMtl();
	BlockColor facecolor(255, 255, 255, 0);
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	DirectionType tdir = (DirectionType)dir;
	if (mtl == NULL) mtl = getFaceMtl(biome, tdir, pblock.getData(), facecolor);
	geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);


	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, NULL);
}