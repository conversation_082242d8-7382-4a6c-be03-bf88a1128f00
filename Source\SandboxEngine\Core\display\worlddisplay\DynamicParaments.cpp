#include "DynamicParaments.h"
#include "Serialize/TransferUtilities.h"
#include "File/FileUtilities.h"
#include "File/FileManager.h"
#include "File/BufferEncrypt.h"
#include "Utilities/RuntimeStatic.h"

namespace Rainbow 
{
	template<class TransferFunction>
	void NodeColor::Transfer(TransferFunction& transfer)
	{
		TRANSFER(r);
		TRANSFER(g);
		TRANSFER(b);
		TRANSFER(a);
	}

	template<class TransferFunction>
	void NodeInfo::Transfer(TransferFunction& transfer) 
	{
		TRANSFER(nodeCoverage);
		TRANSFER(nodeSoftness);
		TRANSFER(nodeCloudAlpha);
		TRANSFER(nodeTopColor);
		TRANS<PERSON>ER(nodeMidColor);
		TRANSFER(nodesBottomColor);
		TRANSFER(nodeYPosScale1);
		TRANSFER(nodeYPosScale2);

		TRANSFER(nodeSunLightIntensity);
		TRANSFER(nodeSunLightSize);
		TRANSFER(nodeSunLightAlpha);
		TRA<PERSON>FER(nodeSunLightPower);
		T<PERSON><PERSON><PERSON><PERSON>(nodeSunRimPower);
		TRANSFER(nodeSunRimRange);

		TRANSFER(nodeMoonLightIntensity);
		TRANSFER(nodemoonLightSize);
		TRANSFER(nodemoonLightAlpha);
		TRANSFER(nodemoonLightPower);
		TRANSFER(nodemoonRimPower);
		TRANSFER(nodemoonRimRange);

		TRANSFER(nodeShadowSoftness);
		TRANSFER(nodeShadowIntensity);
		TRANSFER(nodeShadowAlpha);
		TRANSFER(nodeShadowColor);
		TRANSFER(nodeShadowDarkColor);

		TRANSFER(nodeStarry);
	}


	template<class TransferFunction>
	void DyParamentsSetting::Transfer(TransferFunction& transfer) 
	{
		TRANSFER(NodeInfos);
	}

	DyParamentsSetting::DyParamentsSetting()
	{
	}

	bool DyParamentsSetting::LoadConfig(const char* path)
	{
			AutoRefPtr<DataStream> data = GetFileManager().OpenFile(path, true);
			if (data.Get() == nullptr) return false;
			FromJSONDataStream(data, *this);
			return true;
	}

	//template<class TransferFunction>
	//void DyFogStartEnd::Transfer(TransferFunction& transfer)
	//{
	//	TRANSFER(start);
	//	TRANSFER(end);
	//}

	template<class TransferFunction>
	void DyFogRangeInfo::Transfer(TransferFunction& transfer)
	{
		//TRANSFER(range);
		TRANSFER(start);
		TRANSFER(end);
	}

	template<class TransferFunction>
	void DyEnvInfo::Transfer(TransferFunction& transfer)
	{
		TRANSFER(skyboxType);
		TRANSFER(rangeInfos);
		TRANSFER(windStrength);
		TRANSFER(shadowIntensity);
		TRANSFER(waterColor);
		TRANSFER(waterWaveSpeed);
		TRANSFER(isWaterGlow);
	}

	template<class TransferFunction>
	void DyEnvParamentsSetting::Transfer(TransferFunction& transfer)
	{
		TRANSFER(EnvInfos);
	}

	DyEnvParamentsSetting::DyEnvParamentsSetting()
	{
	}

	bool DyEnvParamentsSetting::LoadConfig(const char* path)
	{
		AutoRefPtr<DataStream> data = GetFileManager().OpenFile(path, true);
		if (data.Get() == nullptr) return false;
		FromJSONDataStream(data, *this);
		return true;
	}

	//DyParamentsSetting_Sun::DyParamentsSetting_Sun() : DyParamentsSetting()
	//{
	//	LoadConfig();
	//}

	//DyParamentsSetting_Rain::DyParamentsSetting_Rain() : DyParamentsSetting()
	//{
	//	LoadConfig();
	//}

	//void DyParamentsSetting_Sun::LoadConfig()
	//{
	//	AutoRefPtr<DataStream> data = GetFileManager().OpenFile("sky/dynamicSkyboxParaments_sun.json", true);
	//	DyParamentsSetting_Sun& self = *this;
	//	FromJSONDataStream(data, self);
	//}

	//void DyParamentsSetting_Rain::LoadConfig()
	//{
	//	AutoRefPtr<DataStream> data = GetFileManager().OpenFile("sky/dynamicSkyboxParaments_rain.json", true);
	//	DyParamentsSetting_Rain& self = *this;
	//	FromJSONDataStream(data, self);
	//}

	////RuntimeStatic<DyParamentsSetting> s_ParaSetting(kMemStatic, kManual, kOrderPlayerSetting);
	//RuntimeStatic<DyParamentsSetting_Sun> s_ParaSetting_Sun(kMemStatic, kManual, kOrderPlayerSetting);
	//RuntimeStatic<DyParamentsSetting_Rain> s_ParaSetting_Rain(kMemStatic, kManual, kOrderPlayerSetting);
	////DyParamentsSetting& GetDyParamentsSetting()
	////{
	////	return *s_ParaSetting.EnsureInitialized();
	////}
	//DyParamentsSetting_Sun& GetDyParamentsSetting_Sun()
	//{
	//	return *s_ParaSetting_Sun.EnsureInitialized();

	//}
	//DyParamentsSetting_Rain& GetDyParamentsSetting_Rain()
	//{
	//	return *s_ParaSetting_Rain.EnsureInitialized();

	//}
}
