#pragma once

#include <map>
#include <list>

#include "SandboxType.h"
#include "SandboxSingleton.h"

#include "base/SandboxAutoRef.h"
#include "event/notify/SandboxTCallback.h"


namespace MNSandbox {

	class SandboxNode;


	class EXPORT_SANDBOXDRIVERMODULE RemoteInstanceListenPool : public Singleton<RemoteInstanceListenPool>, public noncopyable
	{
		using Callback = TCallback<SandboxNodeID, SandboxNode*>;
	public:

		void Clear();

		void OnRecvRemoteInstance(SandboxNodeID nid, SandboxNode* instance);

		bool HadListener() const;
	//protected:
		//friend struct NodeInstanceReflex;

		void Clear(SandboxNodeID nodeid);

		void RegisterListen(SandboxNodeID id, Callback* cb);

		void UnRegisterListen(SandboxNodeID id, Callback* cb);

	private:
		std::map<SandboxNodeID, std::list<Callback*>> m_listenPool;

	};

} //namespace MNSandbox
