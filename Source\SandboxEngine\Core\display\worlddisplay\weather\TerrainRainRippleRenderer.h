
#ifndef __RAINRIPPLE_RENDERER_H__
#define __RAINRIPPLE_RENDERER_H__

#include "Common/LegacyOgreRandomNumber.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "GameScene/MovableObject.h"
#include "Components/Renderer.h"
#include "Render/SceneObjects/RenderObject.h"
#include "LegacyModule.h"
#include "Graphics/Mesh/Mesh.h"
#include "DuststormRenderObject.h"
#include "Components/Camera.h"
#include "OgreWCoord.h"
class World;

struct RainPippleVertex
{
	Rainbow::Vector3f pos;
	Rainbow::Vector2f uv;
};

struct RainRippleMeshData
{
	RainRippleMeshData();
	void* m_VB;
	void* m_IB;
	UInt16 m_VertNum;
	UInt16 m_IndexNum;
	UInt32 m_VBBufferSize;
	UInt32 m_VertStride;
	UInt32 m_IBBuffserSize;
	Rainbow::ShaderChannelMask m_Mask;
};

class TerrainRainRippleRenderer : public Rainbow::Renderer
{
	DECLARE_CLASS(TerrainRainRippleRenderer);

public:
	//static bool Open;
	static TerrainRainRippleRenderer* Create(Rainbow::Camera* sceneCamera, World* pworld);

	TerrainRainRippleRenderer(Rainbow::Camera* sceneCamera, World *pworld);
	virtual ~TerrainRainRippleRenderer();
	RainRippleMeshData& GetMeshData();

	void OnTick(const WCoord& center);

	void ResetMesh();
	void AddMesh(const WCoord& blockPos);

	Rainbow::SharePtr<Rainbow::MaterialInstance> GetRenderMaterial() const { return m_Mat; }
protected:
	virtual Rainbow::RenderObject* CreateSceneObject();
	virtual void UpdateWorldBounds(const Rainbow::Matrix4x4f& localToWorld);

	World *m_World;
	Rainbow::SharePtr<Rainbow::MaterialInstance> m_Mat;

	Rainbow::Camera* m_SceneCamera;
	dynamic_array<RainPippleVertex> m_Vertexes;
	dynamic_array<UInt16> m_Indices;
	RainRippleMeshData m_MeshData;
};

#endif