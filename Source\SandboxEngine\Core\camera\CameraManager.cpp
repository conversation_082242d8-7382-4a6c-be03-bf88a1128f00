#include "OgrePrerequisites.h"
#include "CameraManager.h"
#include "CameraInfo.h"
#include "FreeFlyCamera.h"
#include "TPSCamera.h"
#include "RecordEditCamera.h"
#include "world_types.h"

#include "Common/OgreShared.h"
#include "GameCamera.h"
#include "IPlayerControl.h"
#include "CameraInfo.h"
#include "IRecordInterface.h"
#include "GameStatic.h"
#include "SandboxGlobalNotify.h"
#include "SandboxCameraObject.h"

#include "ClientInfoProxy.h"
#include "UGCGameCamera.h"
#include "world.h"
#include "gamemode/GameModeDef.h"
#include "IWorldConfigProxy.h"

using namespace MINIW;
using namespace Rainbow;



IMPLEMENT_GETMETHOD_MANUAL_INIT(CameraManager)

CameraManager& CameraManager::GetInstance()
{
	return GetCameraManager();
}
CameraManager* CameraManager::GetInstancePtr()
{
	return GetCameraManagerPtr();
}

CameraManager::CameraManager()
: m_Near(0), m_Far(0)
{
	m_GameCamera = NULL;
	m_CameraEditState = CAMERA_EDIT_STATE_NULL;
	m_FreeFlyCamera = ENG_NEW(FreeFlyCamera)(this);
	m_TPSCamera = ENG_NEW(TPSCamera)(this);
	m_EditingCustomCamera = ENG_NEW(EditingCustomCamera)(this);
	m_RecordEditCamera = ENG_NEW(RecordEditCamera)(this);
	m_Fov = 75.0f; // 与CameraInterface.lua 保持一致
	//m_CameraList.resize(0);
	setActiveCamera(m_TPSCamera);

	m_Sensitive = 2;
	LOG_INFO("CameraManager cons");
}


CameraManager::~CameraManager()
{	
	ENG_DELETE(m_GameCamera);
	ENG_DELETE(m_FreeFlyCamera);
	ENG_DELETE(m_TPSCamera);
	ENG_DELETE(m_EditingCustomCamera);
	ENG_DELETE(m_RecordEditCamera);
	SANDBOX_DELETE(m_ChangeRootCameraCallback);
}

void CameraManager::newGameCamera()
{
	if (GetIWorldConfigProxy()->IsUGCGame())
	{
		newUGCGameCamera();
		return;
	}
	if (m_GameCamera)
	{
		ENG_DELETE(m_GameCamera);
	}
	m_GameCamera = ENG_NEW(GameCamera);
	// todo UNDONE
	//AssertMsg(m_Near>= m_Far, "Incorrect near plane [%f] or far plane [%f] for camera!", m_Near, m_Far);

	/*m_GameCamera->getEngineCamera()->SetNear(m_Near);
	m_GameCamera->getEngineCamera()->SetFar(m_Far);
	m_GameCamera->getEngineCamera()->SetAspect(m_Ratio);*/
	m_GameCamera->setFov(m_Fov);
}

void CameraManager::deleteGameCamera()
{
	if (GetIWorldConfigProxy()->IsUGCGame())
	{
		deleteUGCGameCamera();
		return;
	}
	ENG_DELETE(m_GameCamera);
}

void CameraManager::update(float dtime)
{
	OPTICK_EVENT("CameraManager::update")

	//auto iter = m_CameraList.begin();
	//if((*iter) != NULL){
	//	(*iter)->update(dtime);
	//}
	if (m_pActiveCamera)
	{
		m_pActiveCamera->update(dtime);
	}

	//在播放录像
	if (GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		if (GAMERECORD_INTERFACE_EXEC(isEdit(), false) && GAMERECORD_INTERFACE_EXEC(isPause(), false))
		{
			m_RecordEditCamera->update(dtime);
			return;
		}
		else
		{
			 Rainbow::Vector3f pos;
			 float yaw;
			 float pitch;
			 if (GAMERECORD_INTERFACE_EXEC(getRecordCameraPos(pos, yaw, pitch), false)  && m_GameCamera)
			 {
				m_GameCamera->SetEngineCameraPos(WorldPos::initVector3(pos));
				m_GameCamera->setPosition(WorldPos::initVector3(pos));
				m_GameCamera->setRotate(yaw, pitch);
				m_GameCamera->UpdateEngineCamera(dtime);
				return;
			 }
		}

	}
	
	if (m_ControlType == FreeFlyControl_Story)
	{  
		if(m_GameCamera != NULL ){
			m_GameCamera->SetEngineCameraPos(WorldPos::initVector3(m_FreeFlyCamera->m_CameraTransform.pos));
			m_GameCamera->setRotate(m_FreeFlyCamera->m_RotateY, m_FreeFlyCamera->m_RotateX);
			m_GameCamera->UpdateEngineCamera(dtime);
		}
	}
	else if (m_ControlType == FreeFlyControl)
	{
		if(m_GameCamera != NULL ){
			m_GameCamera->SetEngineCameraPos(WorldPos::initVector3(m_FreeFlyCamera->m_CameraTransform.pos));
			m_GameCamera->SetEngineCameraRot(m_FreeFlyCamera->m_CameraTransform.rot);
			// todo check if useful
//			Rainbow::Vector3f tmp = m_FreeFlyCamera->m_CameraTransform->rot.EulerAngle();
////			m_GameCamera->UpdateEngineCamera(dtime);
//			int dtick = TimeToTick(dtime);
//			m_GameCamera->getEngineCamera()->update(dtick);
		}
	}
	else if (m_ControlType == CustomCameraEditing)
	{
		if (getCameraEditState() != CAMERA_EDIT_STATE_EDIT)
		{
			switchCameraControleType(TPSControlBack);
		}
	}
	else
	{
		if(m_GameCamera != NULL && GetIPlayerControl() != NULL  && GetIPlayerControl()->getIWorld() != NULL){
			m_GameCamera->update(dtime, GetIPlayerControl()->getIWorld());
		}
	}
}

//void CameraManager::pushCamera(CameraBase* camera)
//{
//	m_CameraList.clear();
//	m_CameraList.push_front(camera);
//}

/*
void CameraManager::popCamera()
{
	m_CameraStack.pop();
}*/

int CameraManager::onInputEvent(const Rainbow::InputEvent &event)
{
	int result = INPUTMSG_PASS;
	//for (auto iter = m_CameraList.begin(); iter != m_CameraList.end(); iter++)
	//{
	//	result = result && (*iter)->onInputEvent(event);
	//}
	if (m_pActiveCamera)
	{
		m_pActiveCamera->onInputEvent(event);
	}

	return result;
}

void CameraManager::setSensitivity(int sensitive)
{
	m_Sensitive = sensitive;
}

void CameraManager::switchCameraControleType(CameraControlType controlType)
{
	m_ControlType = controlType;
	switch (controlType)
	{
	case TPSControlBack:
		setActiveCamera(m_TPSCamera);
		break;

	case TPSControlFront:
		break;
	
	case FPSControl:
		if(m_GameCamera) {
			m_GameCamera->setMode(CAMERA_FPS);
			setActiveCamera(m_TPSCamera);
		}
		break;

	case CustomCamera:
		if(m_GameCamera) {
			m_GameCamera->setMode(CAMERA_CUSTOM_VIEW);
			setActiveCamera(m_TPSCamera);
		}
		break;
	
	case FreeFlyControl:
		m_FreeFlyCamera->onSwitchTo();
		m_FreeFlyCamera->isInStoryMode = false;
		setActiveCamera(m_FreeFlyCamera);
		break;
	case FreeFlyControl_Story:
		m_FreeFlyCamera->onSwitchTo();
		m_FreeFlyCamera->isInStoryMode = true;
		setActiveCamera(m_FreeFlyCamera);
		break;
	case CustomCameraEditing:
		m_EditingCustomCamera->onSwitchTo();
		setActiveCamera(m_EditingCustomCamera);
		break;
	case RecordEditingCamera:
		m_RecordEditCamera->onSwitchTo();
		setActiveCamera(m_RecordEditCamera);
		break;
	default:
		break;
	}
}

void CameraManager::initEventCallBack()
{
	m_ChangeRootCameraCallback = SANDBOX_NEW(ListenerCameraChanged, [&](MNSandbox::AutoRef<MNSandbox::SandboxNode> cameranode) -> void {
		GetClientInfoProxy()->setRenderCamera(cameranode->ToCast<MNSandbox::SandboxCameraObject>()->GetCamera());
	});
	MNSandbox::GlobalNotify::GetInstance().m_ChangeSandboxCamera.Subscribe(*m_ChangeRootCameraCallback);
}
void CameraManager::initGameCamera(int windowWidth, int windowHeight, float fov, float Near, float Far)
{
	m_Near = Near;
	m_Far = Far;
	m_Fov = fov;
	m_Ratio = float(windowWidth) / windowHeight;
}

Rainbow::Camera * CameraManager::getEngineCamera()
{
	if(!m_GameCamera) newGameCamera();
	return m_GameCamera->getEngineCamera();
}

Rainbow::WorldPos CameraManager::getCameraPos()
{
	if(m_GameCamera)  return m_GameCamera->getPosition();
	return WorldPos(0,0,0);
}

Quaternionf CameraManager::getCameraRot()
{
	if(m_GameCamera)  return m_GameCamera->getRotation();
	return Quaternionf(0,0,0,0);
}

float CameraManager::getCameraFov()
{
	if(m_GameCamera) return m_GameCamera->getFov();
	return 0;
}

void CameraManager::translateCamera(int posX, int posY, int posZ, float rotX, float rotY, float rotZ, float moveSpeed, float rotSpeed)
{
	//if(m_FreeFlyCamera) 
	//	m_FreeFlyCamera->translateCamera(Rainbow::Vector3f(
	//	(float)posX, 
	//	(float)posY, 
	//	(float)posZ), Rainbow::Quaternionf::Euler(rotX, rotY, rotZ), moveSpeed, rotSpeed);
}

void CameraManager::lerpCamera(int posX, int posY, int posZ, float rotX, float rotY, float rotZ, float moveSpeed, float rotSpeed)
{
	/*if(m_FreeFlyCamera) 
		m_FreeFlyCamera->lerpCamera(Rainbow::Vector3f(
		(float)posX, 
		(float)posY, 
		(float)posZ), Rainbow::Quaternionf::Euler(rotX, rotY, rotZ), moveSpeed, rotSpeed);*/
}







void CameraManager::LerpBackToTPSBackCamera()
{
	//m_FreeFlyCamera->LerpBackToTPSBackCamera();
}




void CameraManager::setCameraTransform(int posX, int posY, int posZ, float rotX, float rotY, float rotZ)
{
	/*m_FreeFlyCamera->m_CameraTransform->pos.x = (float)posX;
	m_FreeFlyCamera->m_CameraTransform->pos.y = (float)posY;
	m_FreeFlyCamera->m_CameraTransform->pos.z = (float)posZ;
	m_FreeFlyCamera->m_CameraTransform->rot = Rainbow::Quaternionf::Euler(rotX, rotY, rotZ);*/
}

void CameraManager::tick()
{
	OPTICK_EVENT("CameraManager::tick")

	if (m_ControlType == FPSControl || m_ControlType == TPSControlBack || m_ControlType == TPSControlFront || m_ControlType == CustomCamera)
	{
		if (m_GameCamera != NULL)
		{
			m_GameCamera->tick();
		}
	}
}

int CameraManager::getCameraEditState()
{
	return m_CameraEditState;
}

void CameraManager::setCameraEditState(int state)
{
	switch (m_CameraEditState)
	{
	case CAMERA_EDIT_STATE_NULL:
	case CAMERA_EDIT_STATE_TEST:
		if (state == CAMERA_EDIT_STATE_EDIT)
		{
			switchCameraControleType(CustomCameraEditing);
			if (m_CameraEditState == CAMERA_EDIT_STATE_TEST)
			{
				// 测试前的朝向数据到逻辑相机
				m_GameCamera->m_RotatePitch = m_EditingCustomCamera->m_PreRotateX;
				m_GameCamera->m_RotateYaw = m_EditingCustomCamera->m_PreRotateY;
			}
		}
		break;
	case CAMERA_EDIT_STATE_EDIT:
		if (state == CAMERA_EDIT_STATE_TEST)
		{
			m_EditingCustomCamera->m_PreRotateY = m_GameCamera->m_RotateYaw;
			m_EditingCustomCamera->m_PreRotateX = m_GameCamera->m_RotatePitch;
		}
		if (state != CAMERA_EDIT_STATE_EDIT)
		{
			//switchCameraControleType(TPSControlBack);
		}
		break;
	}
	m_CameraEditState = state;
}
void CameraManager::addScriptCamera(char* name, const char* scriptName, float fov, float fnear, float ffar)
{
	//必须保证是在游戏场景中生成了场景主摄像机后，才能添加脚本摄像机
	if (!m_GameCamera)
	{
		return;
// 		initGameCamera();
	}
	fov = 0 == fov ? m_Fov : fov;
	fnear = 0 == fnear ? m_Near : fnear;
	ffar = 0 == ffar ? m_Far : ffar;

	AssertMsg(m_Near >= m_Far, "near/far clip plane is not correct!");

	m_GameCamera->addScriptCamera(name, scriptName, fov, fnear, ffar);
}

void CameraManager::removeScriptCamera(char* name)
{
	if (m_GameCamera)
	{
		m_GameCamera->removeScriptCamera(name);
	}
}

bool CameraManager::setSpCameraPosRot(char* name, Rainbow::WorldPos pos, Rainbow::Quaternionf quat/*float yaw, float pitch*/)
{
	if (m_GameCamera)
	{
		return m_GameCamera->setSpCameraPosRot(name, pos, quat);
	}
	return false;
}

bool CameraManager::setSpCameraPos(char* name, Rainbow::WorldPos pos)
{
	if (m_GameCamera)
	{
		return m_GameCamera->setSpCameraPos(name, pos);
	}
	return false;
}

bool CameraManager::setSpCameraRot(char* name, Rainbow::Quaternionf qut)
{
	if (m_GameCamera)
	{
		return m_GameCamera->setSpCameraRot(name, qut);
	}
	return false;
}

bool CameraManager::setSpCameraLookAt(char* name, Rainbow::WorldPos eye, Rainbow::Vector3f dir, Rainbow::Vector3f up)
{
	if (m_GameCamera)
	{
		return m_GameCamera->setSpCameraLookAt(name, eye, dir, up);
	}
	return false;
}

bool CameraManager::setSpCameraFov(char* name, float fov)
{
	if (m_GameCamera)
	{
		return m_GameCamera->setSpCameraFov(name, fov);
	}
	return false;
}

bool CameraManager::getSpCameraPos(char* name, Rainbow::WorldPos& pos)
{
	if (m_GameCamera)
	{
		return m_GameCamera->getSpCameraPos(name, pos);
	}
	return false;
}

bool CameraManager::getSpCameraRot(char* name, Rainbow::Quaternionf& rot)
{
	if (m_GameCamera)
	{
		return m_GameCamera->getSpCameraRot(name, rot);
	}
	return false;
}

int CameraManager::getSpCameraCount()
{
	if (m_GameCamera)
	{
		return m_GameCamera->getScriptCameraCount();
	}
	return 0;
}

bool CameraManager::changeScriptCamera(char* name)
{
	/*if (m_GameCamera)
	{
		if (getGameCamera()->getScriptCameraWithName(name))
		{
			GetClientInfoProxy()->setRenderCamera(getGameCamera()->getScriptCameraWithName(name)->m_pCamera);
			return true;
		}
	}*/
	return false;
}

bool CameraManager::changeMainGameCamera()
{
	//if (m_GameCamera)
	//{
	//	GetClientInfoProxy()->setRenderCamera(getEngineCamera());
	//	return true;
	//}
	return false;
}

void CameraManager::newUGCGameCamera()
{
	if (m_GameCamera)
		ENG_DELETE(m_GameCamera);

	m_GameCamera = ENG_NEW(UGCGameCamera);
	m_GameCamera->setFov(m_Fov);
}

void CameraManager::deleteUGCGameCamera()
{
	ENG_DELETE(m_GameCamera);
	m_GameCamera = NULL;
}

void CameraManager::ChangeCameraPos(Rainbow::Vector3f pos, int animType, float time)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	WorldPos targetPos = WorldPos::fromVector3(pos);
	unsigned int timePs = time * 10000;
	pUGCGameCamera->setCameraTargetPos(targetPos, timePs, animType);
}
void CameraManager::ChangeCameraRot(Rainbow::Vector3f rot, int animType, float time)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	unsigned int timePs = time * 10000;
	pUGCGameCamera->setCameraTargetRot(rot, timePs, animType);
}
void CameraManager::ChangeCameraFov(int fov, int animType, float time)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	unsigned int timePs = time * 10000;
	pUGCGameCamera->setCameraTargetFov(fov, timePs, animType);
}
void CameraManager::SetCamMoveFollowable(bool isFollow)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setMoveFollowable(isFollow);
}
void CameraManager::SetCamRotFollowable(bool isFollow)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setLockPitchRot(!isFollow);
	pUGCGameCamera->setLockYawRot(!isFollow);
}
void CameraManager::SetCamRotBySelfCenter(bool isSelfCenter)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setRotBySelfCenter(isSelfCenter);
}
void CameraManager::SetEnableAutoZoom(bool enableAutoZoom)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setEnableAutoZoom(enableAutoZoom);
}
void CameraManager::setEnableRoleTranslucent(bool enableAutoZoom)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setEnableRoleTranslucent(enableAutoZoom);
}
void CameraManager::ChangeCameraRotMode(int rotMode)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	if (rotMode < 1 || rotMode > 4)
		return;
	if (rotMode == 1)
	{
		pUGCGameCamera->setLockPitchRot(false);
		pUGCGameCamera->setLockYawRot(false);
	}
	else if (rotMode == 2)//仅左右可动
	{
		pUGCGameCamera->setLockPitchRot(true);
		pUGCGameCamera->setLockYawRot(false);
	}
	else if (rotMode == 3)//仅上下可动
	{
		pUGCGameCamera->setLockPitchRot(false);
		pUGCGameCamera->setLockYawRot(true);
	}
	else if (rotMode == 4)
	{
		pUGCGameCamera->setLockPitchRot(true);
		pUGCGameCamera->setLockYawRot(true);
	}
}
void CameraManager::SetCameraMountPos(Rainbow::Vector3f pos)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->setMoveFollowable(false);
	pUGCGameCamera->setStandFixedPoint(true);
	pUGCGameCamera->setCameraOffset(WorldPos::fromVector3(pos.x * BLOCK_SIZE, pos.y * BLOCK_SIZE, pos.z * BLOCK_SIZE));
}
void CameraManager::SetCameraMountObj(long long objid)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	World* pWorld = GetIPlayerControl()->getIWorld();
	if (pWorld == NULL)
		return;

	IClientActor* pActor = pWorld->getActorMgr()->iFindActorByWID(objid);
	if (pActor == NULL)
		pUGCGameCamera->setActorObjId(0);
	else
		pUGCGameCamera->setActorObjId(objid);
}
void CameraManager::ResetCamera()
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;
	pUGCGameCamera->resetGameCamera();
}
float CameraManager::GetCameraRotRoll()
{
	if (m_GameCamera == NULL)
		return 0.0f;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return 0.0f;
	return pUGCGameCamera->getRotateRoll();
}

void CameraManager::AdJustCamera(Rainbow::Vector3f pos, int animType, float time)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;

	pUGCGameCamera->AdJustCamera(pos, animType, time);
}

void CameraManager::FocusCamera(Rainbow::Vector3f center, float length, Rainbow::Vector3f lookdir, int animType, float time)
{
	if (m_GameCamera == NULL)
		return;
	UGCGameCamera* pUGCGameCamera = dynamic_cast<UGCGameCamera*>(m_GameCamera);
	if (pUGCGameCamera == NULL)
		return;

	pUGCGameCamera->FocusCamera(center, length, lookdir, animType, time);
}
