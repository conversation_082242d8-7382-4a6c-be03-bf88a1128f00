#include "FullyCustomModel.h"
#include "Entity/OgreEntity.h"
#include "FullyCustomModelMgr.h"
#include <functional>

using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
using std::function;

void FullyCustomModel::updateKeyFrameByBoneChange(Rainbow::Entity *entity, FullyCustomBoneData *fcbd, Matrix4x4f &tm_bone_old, Matrix4x4f &tm_bone_new)
{
	size_t size = fcbd->vCmds.size();
	for (size_t i = 0; i < size; i++)
	{
		auto &cmd = fcbd->vCmds[i];
		size_t keySize = cmd.ticks.size();
		for (size_t j = 0; j < keySize; j++)
		{
			Rainbow::Matrix4x4f tm_key_old;
			if (!IsFinite(cmd.scale3s[j]))
			{
				continue;
			}
			Rainbow::makeSRTMatrix(tm_key_old, cmd.scale3s[j], cmd.quats[j], cmd.posoffsets[j]);

			Rainbow::Matrix4x4f tm_key_diff;
			//tm_key_diff = tm_key_old * tm_bone_old;
			tm_key_diff = Matrix4x4Mul(tm_key_old, tm_bone_old);  //MultiplyMatrices3x4(tm_key_old, tm_bone_old, tm_key_diff);

			Rainbow::Matrix4x4f tm_key_new;
			//tm_key_new = tm_key_diff * tm_bone_new;
			tm_key_new = Matrix4x4Mul(tm_key_diff, tm_bone_new);  //MultiplyMatrices3x4(tm_key_diff, tm_bone_new, tm_key_new);

			cmd.posoffsets[j] = tm_key_new.GetPosition();
			//data.quats[j].setMatrix(tm_key_new);
			Rainbow::Vector3f scale3 = tm_key_new.GetLossyScale();
			if (!IsFinite(scale3))
			{
				continue;
			}
			cmd.scales[j] = scale3.x;
			cmd.scale3s[j] = scale3;

			Rainbow::Matrix4x4f tm = tm_key_new;
			tm.Get(0, 0) /= scale3.x; tm.Get(1, 0) /= scale3.y; tm.Get(2, 0) /= scale3.z;
			tm.Get(0, 1) /= scale3.x; tm.Get(1, 1) /= scale3.y; tm.Get(2, 1) /= scale3.z;
			tm.Get(0, 2) /= scale3.x; tm.Get(1, 2) /= scale3.y; tm.Get(2, 2) /= scale3.z;
			//data.quats[j].setMatrix(tm);
			MatrixToQuaternionf(tm, cmd.quats[j]);

			if (entity)
			{
				entity->SetCustomKeyFrame(cmd.id, fcbd->name.c_str(), cmd.ticks[j], cmd.posoffsets[j], cmd.quats[j], cmd.scale3s[j]);
			}
		}
	}
}

void FullyCustomModel::setCustomSequenceEndtime(int seqId, int endtime, FullyCustomBoneData* rootFcbd /* = NULL */)
{
	std::vector<FullyCustomBoneData*>* pvFcbd = &m_vFcbd;
	if (rootFcbd)
	{
		pvFcbd = &rootFcbd->vChildFcbds;
	}

	std::vector<FullyCustomBoneData*>& vFcbd = *pvFcbd;
	auto iter = vFcbd.begin();
	for (; iter != vFcbd.end(); iter++)
	{
		bool found = false;
		FullyCustomBoneData& fcbd = **iter;
		for (unsigned i = 0; i < fcbd.vCmds.size(); i++)
		{
			CustomMotionData& cmd = fcbd.vCmds[i];
			if (cmd.id != seqId)
			{
				continue;
			}
			cmd.time = endtime;
			cmd.resetTicks();
			found = true;
			m_bNeedSave = true;
			break;
		}
		if (!found)
		{
			CustomMotionData cmd;
			cmd.id = seqId;
			cmd.time = endtime;
			fcbd.vCmds.push_back(cmd);
		}

		setCustomSequenceEndtime(seqId, endtime, &fcbd);
	}
}

bool FullyCustomModel::insertCustomKeyFrame(int seqId, std::string bonename, const int tick, 
	short offsetx, short offsety, short offsetz, Rainbow::Quaternionf &rotate, float scale)
{
	Rainbow::Vector3f translate(offsetx, offsety, offsetz);
	Rainbow::Vector3f scale3(scale);
	return insertCustomKeyFrame(seqId, bonename, tick, translate, rotate, scale3);
}

bool FullyCustomModel::insertCustomKeyFrame(int seqId, std::string bonename, const int tick, 
	Rainbow::Vector3f& translate, Rainbow::Quaternionf &rotate, Rainbow::Vector3f& scale3)
{
	assert(tick >= 0);
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(bonename);
	if (!fcbd)
	{
		return false;
	}

	for (int i = 0; i < (int)fcbd->vCmds.size(); i++)
	{
		CustomMotionData& cmd = fcbd->vCmds[i];
		if (cmd.id != seqId)
		{
			continue;
		}
		//没有则直接插入
		int ntotal = cmd.ticks.size();
		if (ntotal == 0)
		{
			cmd.clear();
			cmd.add(tick, translate, rotate, scale3);
			return true;
		}
		//根据tick排序插入
		int start = 0;
		int end = ntotal - 1;
		if (start >= ntotal)
		{
			start = ntotal - 1;
		}
		if (end >= ntotal)
		{
			end = ntotal - 1;
		}
		if (tick > (int)cmd.ticks[end])
		{
			start = end + 1;
		}
		for (;;)
		{
			if (start >= end) break;
			int middle = (start + end) / 2;
			if (tick < (int)cmd.ticks[middle])
			{
				end = middle;
			}
			else if (tick > (int)cmd.ticks[middle])
			{
				start = middle + 1;
			}
			else
			{
				//存在的tick无法插入
				return false;
			}
		}
		//多线程处理重复帧，直接覆盖
		auto itTick = std::find(cmd.ticks.begin(), cmd.ticks.end(), tick);
		if (itTick != cmd.ticks.end())
		{
			start = itTick - cmd.ticks.begin();
			cmd.setIndex(start, tick, translate, rotate, scale3);
			m_bNeedSave = true;
			return true;
		}
		cmd.insert(start, tick, translate, rotate, scale3);
		m_bNeedSave = true;
		return true;
	}

	CustomMotionData cmd;
	cmd.id = seqId;
	cmd.clear();
	cmd.add(tick, translate, rotate, scale3);
	fcbd->vCmds.push_back(cmd);
	m_bNeedSave = true;
	return true;
}

bool FullyCustomModel::setCustomKeyFrame(int seqId, std::string bonename, const int tick,
	short offsetx, short offsety, short offsetz, Rainbow::Quaternionf &rotate, float scale)
{
	Rainbow::Vector3f translate(offsetx, offsety, offsetz);
	Rainbow::Vector3f scale3(scale);
	return setCustomKeyFrame(seqId, bonename, tick, translate, rotate, scale3);
}

bool FullyCustomModel::setCustomKeyFrame(int seqId, std::string bonename, const int tick, 
	Rainbow::Vector3f& translate, Rainbow::Quaternionf& rotate, Rainbow::Vector3f& scale3)
{
	Transform_ trs(translate, rotate, scale3);
	return setCustomKeyFrame(seqId, bonename, tick, trs);
}

bool FullyCustomModel::setCustomKeyFrame(int seqId, std::string bonename, const int tick, MINIW::Transform_& trs)
{
	auto fcbd = findFullyCustomBoneData(bonename);
	if (!fcbd)
	{
		return false;
	}
	for (unsigned i = 0; i < fcbd->vCmds.size(); i++)
	{
		auto& cmd = fcbd->vCmds[i];
		if (cmd.id != seqId)
		{
			continue;
		}
		for (unsigned j = 0; j < cmd.ticks.size(); j++)
		{
			if (tick != cmd.ticks[j])
			{
				continue;
			}
			cmd.setIndex(j, tick, trs.t, trs.r, trs.s);
			m_bNeedSave = true;
			return true;
		}
	}
	return false;
}


bool FullyCustomModel::delCustomKeyFrame(int seqId, std::string boneName, const int tick)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(boneName);
	if (!fcbd)
	{
		return false;
	}
	for (unsigned i = 0; i < fcbd->vCmds.size(); i++)
	{
		CustomMotionData& cmd = fcbd->vCmds[i];
		if (cmd.id == seqId)
		{
			for (unsigned j = 0; j < cmd.ticks.size(); j++)
			{
				if (tick == cmd.ticks[j])
				{
					cmd.removeIndex(j);
					m_bNeedSave = true;
					return true;
				}
			}
		}
	}
	return false;
}

bool FullyCustomModel::delCustomKeyframes(int seqId, const int tick)
{
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		for (CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			int i = cmd.getIndexByTick(tick);
			if (i < 0)
			{
				break;
			}
			cmd.removeIndex(i);
			break;
		}
		return true;
	};
	iterate(func);
	m_bNeedSave = true;
	return true;
}

void FullyCustomModel::delAllCustomKeyframes(int seqId)
{
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		for (CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			cmd.clear();
			cmd.time = 0;
			break;
		}
		return true;
	};
	iterate(func);
	m_bNeedSave = true;
}

bool FullyCustomModel::getCustomMotionDataTRS(int seqId, std::string strBoneName, int tick, MINIW::Transform_& trs)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(strBoneName);
	if (!fcbd)
	{
		return false;
	}
	return fcbd->getCustomMotionDataTRS(seqId, tick, trs);
}

bool FullyCustomModel::copyCustomKeyframes(int seqIdSrc, int seqIdDst)
{
	if (seqIdSrc == seqIdDst)
	{
		return false;
	}
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		CustomMotionData* cmdSrc = fcbd.getCustomMotionDataBySeqId(seqIdSrc);
		CustomMotionData* cmdDst = fcbd.getCustomMotionDataBySeqId(seqIdDst);
		if (cmdSrc)
		{
			if (cmdDst)
			{
				cmdDst->copy(*cmdSrc);
				cmdDst->id = seqIdDst;
			}
			else
			{
				CustomMotionData cmd(*cmdSrc);
				cmd.id = seqIdDst;
				fcbd.vCmds.push_back(cmd);
			}
		}
		else
		{
			if (cmdDst)
			{
				int i;
				for (i = 0; i < fcbd.vCmds.size(); ++i)
				{
					if (fcbd.vCmds[i].id == seqIdDst)
					{
						break;
					}
				}
				if (i < fcbd.vCmds.size())
				{
					fcbd.vCmds.erase(fcbd.vCmds.begin() + i);
				}
			}
		}
		return true;
	};
	iterate(func);
	m_bNeedSave = true;
	return true;
}

std::set<unsigned> FullyCustomModel::getTicks(int seqId)
{
	std::set<unsigned> setTicks;
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		for (const CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			for (unsigned t : cmd.ticks)
			{
				setTicks.emplace(t);
			}
			break;
		}
		return true;
	};
	iterate(func);
	return setTicks;
}

bool FullyCustomModel::hasTick(int seqId, int tick)
{
	if (tick < 0)
	{
		return false;
	}
	bool has = false;
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		if (fcbd.hasTick(seqId, tick))
		{
			has = true;
			return false;
		}
		return true;
	};
	iterate(func);
	return has;
}

bool FullyCustomModel::hasBoneTick(int seqId, std::string strBoneName, int tick)
{
	FullyCustomBoneData* fcbd = findFullyCustomBoneData(strBoneName);
	if (!fcbd)
	{
		return false;
	}
	return fcbd->hasTick(seqId, tick);
}

bool FullyCustomModel::hasTickExceptBone(int seqId, int tick, std::string strBoneName)
{
	bool has = false;
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		if (fcbd.name == strBoneName)
		{
			return true;
		}
		for (const CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			const std::vector<unsigned>& vTicks = cmd.ticks;
			if (std::find(vTicks.begin(), vTicks.end(), tick) != vTicks.end())
			{
				has = true;
				return false;
			}
		}
		return true;
	};
	iterate(func);
	return has;
}

int FullyCustomModel::getEndtime(int seqId)
{
	int endtime = 0;
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		for (const CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			if (endtime < cmd.time)
			{
				endtime = cmd.time;
			}
			break;
		}
		return true;
	};
	iterate(func);
	return endtime;
}

bool FullyCustomModel::isTickValid(int seqId, int tick)
{
	int endtime = getEndtime(seqId);
	return tick >= 0 && tick <= endtime;
}

int FullyCustomModel::getTickPerKf(int seqId)
{
	int tickPerKf = 100;
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		bool found = false;
		for (CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			tickPerKf = cmd.tickPerKf;
			found = true;
			break;
		}
		return !found;
	};
	iterate(func);
	return tickPerKf;
}


void FullyCustomModel::transformTickPerKf(int seqId, int tickPerKf)
{
	function<bool(FullyCustomBoneData&)> func = [&](FullyCustomBoneData& fcbd) -> bool {
		for (CustomMotionData& cmd : fcbd.vCmds)
		{
			if (cmd.id != seqId)
			{
				continue;
			}
			cmd.transformTickPerKf(tickPerKf);
			break;
		}
		return true;
	};
	iterate(func);
}

bool FullyCustomModel::getFrameEvent(int seqId, UInt32 beginTick, UInt32 endTick, std::vector<Rainbow::LegacyAnimationFrameEventData*>* ret)
{
	// quick check
	if (m_frameEventData.size() == 0)
	{
		return false;
	}
	if (endTick < beginTick)
	{
		return false;
	}

	bool hasEvent = false;
	for (auto& frameEvent : m_frameEventData)
	{
		if (frameEvent.id == seqId)
		{
			if (frameEvent.tick >= beginTick && frameEvent.tick <= endTick)
			{
				hasEvent = true;
				if (ret)
				{
					(*ret).push_back(&frameEvent);
				}
				else
				{
					break;
				}
			}
		}
	}
	return hasEvent;
}

void FullyCustomModel::addFrameEvent(int seqId, UInt32 tick, const std::string& event, UInt32 count)
{
	for (auto& frameEvent : m_frameEventData)
	{
		if (frameEvent.id == seqId && frameEvent.tick == tick)
		{
			frameEvent.event = event;
			frameEvent.triggerCount = count;
			return;
		}
	}
	m_frameEventData.emplace_back(seqId, tick, event, count, 0);
}

void FullyCustomModel::removeFrameEvent(int seqId, UInt32 tick)
{
	for(auto it = m_frameEventData.begin(); it != m_frameEventData.end(); it++)
	{
		const auto& frameEvent = *it;
		if(frameEvent.id == seqId && frameEvent.tick == tick)
		{
			it = m_frameEventData.erase(it);
			break;
		}
	}
}

void FullyCustomModel::updateFrameEvent(UInt32 maxTick)
{
	if(maxTick == 0)
	{
		m_frameEventData.clear();
	}
	else
	{
		for(auto it = m_frameEventData.begin(); it != m_frameEventData.end();)
		{
			if(maxTick > it->tick)
			{
				it = m_frameEventData.erase(it);
			}
			else
			{
				it++;
			}
		}
	}
}