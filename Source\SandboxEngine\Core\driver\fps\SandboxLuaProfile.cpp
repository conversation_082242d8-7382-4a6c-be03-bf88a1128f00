
#include <vector>
#include <algorithm>
#include "SandboxLuaProfile.h"
#include "Misc/TimeManager.h"
#include "util/SandboxUnit.h"
#include "util/SandboxCustomBuffer.h"
#include "util/thread/SandboxThreadTask.h"

NS_SANDBOX_BEG

bool CE_Single::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	return formula ? formula(a, b) : false;
}

SortCompareExpression& CE_Single::addFormula(CompareFormula inFormula)
{
	formula = std::move(inFormula);
	return *this;
}

CE_Sequence& CE_Sequence::addFormula(CompareFormula inFormula)
{
	std::shared_ptr<CE_Single> ce(new CE_Single);
	ce->addFormula(std::move(inFormula));
	return addExpression(ce);
}

bool CE_Sequence::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	return SortCompareExpression::expression(a, b);
}

bool CE_And::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	bool bRet = true;
	auto iter = exp.begin();
	while (bRet && iter != exp.end())
	{
		bRet = bRet && (*iter)->expression(a, b);
		++iter;
	}

	return bRet;
}

bool CE_Or::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	bool bRet = true;
	auto iter = exp.begin();
	while (iter != exp.end())
	{
		bRet = bRet || (*iter)->expression(a, b);
		++iter;
	}
	return bRet;
}

bool CE_Not::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	bool bRet = true;
	auto iter = exp.begin();
	while (bRet && iter != exp.end())
	{
		bRet = bRet && (*iter)->expression(a, b);
		++iter;
	}
	return !bRet;
}

bool SortCompareExe::expression(const ScopeFuncInfo* a, const ScopeFuncInfo* b)
{
	return exp->expression(a, b);
}

ScopeFuncInfo::ScopeFuncInfo(const std::string& className, const std::string& funcName)
	: _tempTimeStamp(0)
	, _counts(0)
	, _maxCost(0)
	, _currCost(0)
	, _totalCost(0)
	, _className(className)
	, _funcName(funcName)
{}

void ScopeFuncInfo::serialize(std::string& outContent)
{
	//uint64_t tc = _totalCost / 1000;
	//uint64_t mc = _maxCost / 1000;

	outContent += ToString("[", _className, "::", _funcName, "]");
	//outContent += ToString("[", _funcName, "]");
	outContent += ToString(" Counts:", _counts);
	outContent += ToString(" Total Cost(us):", _totalCost);
	outContent += ToString(" Max Cost(us):", _maxCost);
	outContent += "\n";
}

void ScopeFuncInfo::beginScope()
{
	_tempTimeStamp = Rainbow::GetTimeMS();
	++_counts;
}

void ScopeFuncInfo::endScope()
{
	_currCost = Rainbow::GetTimeMS() - _tempTimeStamp;
	_totalCost += _currCost;
	if (_currCost > _maxCost)
	{
		_maxCost = _currCost;
	}
}

ScopeFuncInfoWrap::ScopeFuncInfoWrap(const std::string& className, const std::string& funcName)
{
	_info = ScopeFuncContainer::getInstance()->findOrAddNode(className, funcName);
	if (_info != nullptr)
	{
		_info->beginScope();
	}
}

ScopeFuncInfoWrap::~ScopeFuncInfoWrap()
{
	if (_info != nullptr)
	{
		_info->endScope();
	}
}

ScopeFuncContainer::ScopeFuncContainer()
{
	std::shared_ptr<CE_And> ce_and(new CE_And);
	//ce_and->addFormula([](const ScopeFuncInfo* a, const ScopeFuncInfo* b) -> bool { return a->_totalCost > b->_totalCost; });
	//ce_and->addFormula([](const ScopeFuncInfo* a, const ScopeFuncInfo* b) -> bool { return a->_counts > b->_counts; });
	//ce_and->addFormula([](const ScopeFuncInfo* a, const ScopeFuncInfo* b) -> bool { return a->_maxCost > b->_maxCost; });

	ce_and->addFormula([](const ScopeFuncInfo* a, const ScopeFuncInfo* b) -> bool { 
		if (a->_totalCost != b->_totalCost)
		{
			return a->_totalCost > b->_totalCost;
		}
		else if (a->_counts != b->_counts)
		{
			return a->_counts > b->_counts;
		}
		else
		{
			return a->_maxCost > b->_maxCost;
		}
	});

	sortExe.buildExpression(ce_and);
}

std::shared_ptr<ScopeFuncInfo> ScopeFuncContainer::findOrAddNode(const std::string& className, const std::string& functionName)
{
	const std::string path = className + ":" + functionName;
	auto iter = nodes.find(path);
	if (iter == nodes.end())
	{
		std::shared_ptr<ScopeFuncInfo> info(new ScopeFuncInfo(className, functionName));
		iter = nodes.insert(std::make_pair(path, info)).first;
	}
	return iter->second;
}

void ScopeFuncContainer::serialize(std::string& outContent)
{
	std::vector<std::shared_ptr<ScopeFuncInfo>> temps;
	for (auto iter = nodes.begin(); iter != nodes.end(); ++iter)
	{
		if (iter->second->_totalCost != 0)
		{
			temps.push_back(iter->second);
		}
	}

	std::sort(temps.begin(), temps.end(), [this](std::shared_ptr<ScopeFuncInfo>& a, std::shared_ptr<ScopeFuncInfo>& b) -> bool {
		return sortExe.expression(a.get(), b.get());
		});

	for (auto iter = temps.begin(); iter != temps.end(); ++iter)
	{
		(*iter)->serialize(outContent);
	}
}

void ScopeFuncContainer::saveToFile()
{
	if (Thread::ThreadTaskManager::GetSingletonPtr())
	{
		std::string msg;
		serialize(msg);

		Thread::SaveFileData save;
		save._path = "LuaFunctionCost.txt";
		save._msg = CustomBuffer::CreateStatic((void*)msg.c_str(), msg.length());
		Thread::ThreadTaskManager::GetSingletonPtr()->PushSaveFileTask(save);


		Thread::SaveFileData save2;
		save2._path = "LuaFunctionShotcut.txt";
		save2._msg = CustomBuffer::CreateStatic((void*)ScopeFuncTemp::_shotcut.c_str(), ScopeFuncTemp::_shotcut.length());
		Thread::ThreadTaskManager::GetSingletonPtr()->PushSaveFileTask(save2);
	}
}

uint64_t ScopeFuncTemp::_counts = 0;
uint64_t ScopeFuncTemp::_maxCost = 0;
uint64_t ScopeFuncTemp::_currCost = 0;
uint64_t ScopeFuncTemp::_tempTimeStamp = 0;
std::string ScopeFuncTemp::_shotcut = "";

ScopeFuncTemp::ScopeFuncTemp()
{
	if (MNSandbox::Config::GetSingleton().IsPlayMode())
	{
		beginScope();
	}
	
}

ScopeFuncTemp::~ScopeFuncTemp()
{
	if (MNSandbox::Config::GetSingleton().IsPlayMode())
	{
		endScope();
	}
}

void ScopeFuncTemp::beginScope()
{
	ScopeFuncTemp::_tempTimeStamp = Rainbow::GetTimeMS();
	++ScopeFuncTemp::_counts;
}

void ScopeFuncTemp::endScope()
{
	ScopeFuncTemp::_currCost = Rainbow::GetTimeMS() - ScopeFuncTemp::_tempTimeStamp;
	if (_currCost > _maxCost)
	{
		_maxCost = _currCost;
		//if (_maxCost > 1500) //1.5ms
		//{
			ScopeFuncContainer::getInstance()->serialize(ScopeFuncTemp::_shotcut);
		//}
	}
}

NS_SANDBOX_END