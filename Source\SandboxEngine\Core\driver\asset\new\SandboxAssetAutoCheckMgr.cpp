
#include "SandboxAssetAutoCheckMgr.h"
#include "SandboxGlobalNotify.h"
#include "Misc/TimeManager.h"
namespace MNSandbox {

	AssetAutoCheckMgr::AssetAutoCheckMgr()
		:m_listenGlobalUpdate(this, &AssetAutoCheckMgr::OnUpdate)
	{

	}

	AssetAutoCheckMgr::~AssetAutoCheckMgr()
	{
		
	}


	void AssetAutoCheckMgr::OnUpdate(float f)
	{
		for (const auto& item: m_waitList){
			m_checkList.push_back(item);
		}
		m_waitList.clear();

		if (m_checkList.empty()){
			m_listenGlobalUpdate.ClearBindNotify();
			return;
		}
		//auto t1 = Rainbow::GetTimeUS();
		auto iter = m_checkList.begin();
		while (iter != m_checkList.end()){
			if (iter->state == CheckState::Remove){
				iter = m_checkList.erase(iter);
				continue;
			}
			if (iter->state == CheckState::None){
				iter->state = CheckState::Checking;
			}
			if (iter->checkCallback && iter->checkCallback()){
				if (iter->endCallback){
					iter->endCallback();
				}
				iter->state = CheckState::Remove;
			}
			iter++;
		}
		//SANDBOX_LOG("AssetAutoCheckMgr::OnUpdate:", std::to_string(Rainbow::GetTimeUS() - t1));
	}

	void AssetAutoCheckMgr::Push(AutoRef<AssetUploadRef> checkOwner, std::function<bool()> checkCallback, std::function<void()>endCallback)
	{
		CheckItem item;
		item.ref = checkOwner;
		item.checkCallback = checkCallback;
		item.endCallback = endCallback;
		m_waitList.push_back(item);

		if (!m_listenGlobalUpdate.GetListener()->IsBindNotify())
		{
			GlobalNotify::GetInstance().m_Update.Subscribe(m_listenGlobalUpdate);
		}
	}
}
