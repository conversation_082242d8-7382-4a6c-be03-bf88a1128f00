//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

// Reference to navigation polygon.
typedef unsigned long long NavMeshPolyRef;

// Reference to navigation mesh tile.
typedef unsigned long long NavMeshTileRef;

static const unsigned int kPolyRefSaltBits = 16; // Number of salt bits in the poly/tile ID.
static const unsigned int kPolyRefTileBits = 28; // Number of tile bits in the poly/tile ID.
static const unsigned int kPolyRefPolyBits = 16; // Number of poly bits in the poly/tile ID.
static const unsigned int kPolyRefTypeBits = 4;  // Number of type bits in the poly/tile ID.

static const NavMeshPolyRef kPolyRefSaltMask = ((NavMeshPolyRef)1 << kPolyRefSaltBits) - 1;
static const NavMeshPolyRef kPolyRefTileMask = ((NavMeshPolyRef)1 << kPolyRefTileBits) - 1;
static const NavMeshPolyRef kPolyRefPolyMask = ((NavMeshPolyRef)1 << kPolyRefPolyBits) - 1;
static const NavMeshPolyRef kPolyRefTypeMask = ((NavMeshPolyRef)1 << kPolyRefTypeBits) - 1;


typedef unsigned int NavMeshStatus;

// High level status.
static const unsigned int kNavMeshFailure = 1u << 31;           // Operation failed.
static const unsigned int kNavMeshSuccess = 1u << 30;           // Operation succeed.
static const unsigned int kNavMeshInProgress = 1u << 29;        // Operation still in progress.

// Detail information for status.
static const unsigned int kNavMeshStatusDetailMask = 0x0ffffff;
static const unsigned int kNavMeshWrongMagic = 1 << 0;      // Input data is not recognized.
static const unsigned int kNavMeshWrongVersion = 1 << 1;    // Input data is in wrong version.
static const unsigned int kNavMeshOutOfMemory = 1 << 2; // Operation ran out of memory.
static const unsigned int kNavMeshInvalidParam = 1 << 3;    // An input parameter was invalid.
static const unsigned int kNavMeshBufferTooSmall = 1 << 4;  // Result buffer for the query was too small to store all results.
static const unsigned int kNavMeshOutOfNodes = 1 << 5;      // Query ran out of nodes during search.
static const unsigned int kNavMeshPartialResult = 1 << 6;   // Query did not reach the end location, returning best guess.

// Returns true of status is success.
inline bool NavMeshStatusSucceed(NavMeshStatus status)
{
    return (status & kNavMeshSuccess) != 0;
}

// Returns true of status is failure.
inline bool NavMeshStatusFailed(NavMeshStatus status)
{
    return (status & kNavMeshFailure) != 0;
}

// Returns true of status is in progress.
inline bool NavMeshStatusInProgress(NavMeshStatus status)
{
    return (status & kNavMeshInProgress) != 0;
}

// Returns true if specific detail is set.
inline bool NavMeshStatusDetail(NavMeshStatus status, unsigned int detail)
{
    return (status & detail) != 0;
}
