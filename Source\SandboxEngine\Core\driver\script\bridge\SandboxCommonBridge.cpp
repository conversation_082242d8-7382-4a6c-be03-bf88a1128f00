#include "SandboxCommonBridge.h"
#include "SandboxLua.h"
#include "SandboxLuaUnit.h"
#include "SandboxEnumBridge.h"
#include "Math/Matrix4x4f.h"//#include "OgreMatrix4.h" 
#include "SandboxUtilMath.h"

namespace MNSandbox {

	float lua_tofloat(lua_State *L, int idx)
	{
		double value = lua_tonumber(L, idx);

		if (value == std::numeric_limits<double>::infinity())
		{
#if PLATFORM_WIN
			_ASSERT(false && "invalid number");
#endif			
			return 0.0f;
		}
			
		if (value == -std::numeric_limits<double>::infinity())
		{
#if PLATFORM_WIN			
			_ASSERT(false && "invalid number");
#endif						
			return 0.0f;
		}	

		// NAN:
		if (!((value < 0.0) || (value >= 0.0)))
			return (float)value;

		if (value > (double)std::numeric_limits<float>::max())
			return std::numeric_limits<float>::max();

		if (value < (double)-std::numeric_limits<float>::max())
			return -std::numeric_limits<float>::max();

		return (float)value;
	}

	const luaL_Reg Vector3Bridge::ms_ClassLibrary[] = {
		{"new", Vector3Bridge::NewVector3},
		{"New", Vector3Bridge::NewVector3},
		{"Normalize", Vector3Bridge::Normalize},
		{"FromQuaternion", Vector3Bridge::FromQuaternionf},
		{"Dot", Vector3Bridge::on_dot},
		{"Cross", Vector3Bridge::on_cross},
		{"Lerp", Vector3Bridge::on_lerp},
		{nullptr, nullptr}
	};

	int Vector3Bridge::NewVector3(lua_State* L)
	{
		float vector[3];
		int count = std::min(3, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tofloat(L, i + 1);
		for (int i = count; i < 3; i++)
			vector[i] = 0.0;

		PushVector3(L, vector);
		return 1;
	}

	void Vector3Bridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Vector3", ms_ClassLibrary);
		lua_pop(L, 1);
	}

#ifdef ENABLE_VEC3_POOL

	double Vector3Bridge::s_Vector3BridgePoolIndex = 0;
	double Vector3Bridge::s_Vector3BridgePoolStart = 0;
	Rainbow::Vector3f* Vector3Bridge::GetInstanceFromPool(lua_State* L)
	{
		Rainbow::Vector3f v(1, 0, 0);
		return GetInstanceFromPool(L, v);
	}

	Rainbow::Vector3f* Vector3Bridge::GetInstanceFromPool(lua_State* L, const Rainbow::Vector3f& v)
	{
		if (s_Vector3BridgePoolIndex > s_Vector3BridgePoolStart)
		{
			//int top = lua_gettop(L);
			lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));		//stack: u
			lua_rawget(L, LUA_REGISTRYINDEX);																		//stack: t(u)
			if (lua_isnil(L, -1))
			{
				//lua_pop(L, 1);
				//lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));	//stack:  u,
				//lua_newtable(L);																					//stack:  u , t
				//lua_rawset(L, LUA_REGISTRYINDEX);																	//stack: 
				//lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));	//stack: u
				//lua_rawget(L, LUA_REGISTRYINDEX);																	//stack: t(u)
			}
			else
			{
				s_Vector3BridgePoolStart++;
				lua_pushnumber(L, s_Vector3BridgePoolStart);			//stack: t, i
				lua_gettable(L, -2);									//stack: t, u
				if (lua_isuserdata(L, -1))
				{
					void* userdata = lua_touserdata(L, -1);
					Rainbow::Vector3f* v3 = reinterpret_cast<Rainbow::Vector3f*>(userdata);
					v3->Set(v);
					lua_pushnumber(L, s_Vector3BridgePoolStart);//stack: t, u, i
					lua_pushnil(L);								//stack: t, u, i, nil
					lua_rawset(L, -4);          //stack: t, u 
					lua_remove(L, -2);			//stack:  u
					return v3;
				}
			}
		}

		return PushNewObject(L, v);
	}

	template<>
	int Bridge<Rainbow::Vector3f>::__gc(lua_State* L)
	{
		Rainbow::Vector3f* userdata = GetObject(L, 1);
		int top = lua_gettop(L);
		if (!userdata)
		{
			SANDBOX_ASSERT(false && "param 1 type is not a TClass object!");
			return 0;
		}
		lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));		//stack: u
		lua_rawget(L, LUA_REGISTRYINDEX);	//stack: t(u)
		if (lua_isnil(L, -1))
		{
			lua_pop(L, 1);
			lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));	//stack:  u,
			lua_newtable(L);																					//stack:  u , t
			lua_rawset(L, LUA_REGISTRYINDEX);																	//stack: 
			lua_pushlightuserdata(L, LuaRegisterAddress::GetP(LuaRegisterAddress::LUA_BRIDGE_VECTOR3_POOL));	//stack: u
			lua_rawget(L, LUA_REGISTRYINDEX);																	//stack: t(u)
		}
		Vector3Bridge::s_Vector3BridgePoolIndex++;// 0开始，第一次放入pool所以是1，lua的习惯
		lua_pushnumber(L, Vector3Bridge::s_Vector3BridgePoolIndex);			//stack: t, i
		lua_pushvalue(L, top);
		lua_rawset(L, -3);
		lua_pop(L, 1);
		return 0;
	}
#endif

	int Vector3Bridge::on_add(lua_State *L)
	{
		const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 1);
		const Rainbow::Vector3f& b = *Vector3Bridge::GetObject(L, 2);
		PushVector3(L, a + b);
		return 1;
	};

	int Vector3Bridge::on_sub(lua_State *L)
	{
		const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 1);
		const Rainbow::Vector3f& b = *Vector3Bridge::GetObject(L, 2);
		PushVector3(L, a - b);
		return 1;
	};

	int Vector3Bridge::on_mul(lua_State *L)
	{
		Rainbow::Vector3f a;
		if (Vector3Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector3f b;
			if (Vector3Bridge::GetValue(L, 2, b))
				PushVector3(L, a * b);
			else if (!lua_isnumber(L, 2))
			{
				
			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector3(L, a * c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{
			
		}
		else
		{
			a = *Vector3Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector3(L, b * a);
		}
		return 1;
	};

	int Vector3Bridge::on_div(lua_State *L)
	{
		Rainbow::Vector3f a;
		if (Vector3Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector3f b;
			if (Vector3Bridge::GetValue(L, 2, b))
				PushVector3(L, a / b);
			else if (!lua_isnumber(L, 2))
			{
				
			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector3(L, a / c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{
			
		}
		else {
			a = *Vector3Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector3(L, Rainbow::Vector3f(b, b, b) / a);
		}
		return 1;
	};

	int Vector3Bridge::on_unm(lua_State *L)
	{
		PushVector3(L, -*Vector3Bridge::GetObject(L, 1));
		return 1;
	};

	int Vector3Bridge::Normalize(lua_State *L)
	{
		Rainbow::Vector3f &v3 = *Vector3Bridge::GetObject(L, 1);
		v3.NormalizeSafe();
		lua_pushvalue(L, 1);
		return 1;
	}

	int Vector3Bridge::SetValue(lua_State* L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		v3.x = lua_tofloat(L, 2);
		v3.y = lua_tofloat(L, 3);
		v3.z = lua_tofloat(L, 4);
		return 0;
	}

	int Vector3Bridge::FromQuaternionf(lua_State* L)
	{
		Rainbow::Quaternionf a(0, 0, 0, 1);
		QuaternionBridge::GetValue(L, 1, a);
		Rainbow::Vector3f eularAngle = Rainbow::QuaternionToEulerAngle(a);
		PushNewObject(L, eularAngle);
		return 1;
	}

	int Vector3Bridge::on_dot(lua_State *L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 2);
		lua_pushnumber(L, DotProduct(v3, a));
		return 1;
	}
	
	int Vector3Bridge::on_cross(lua_State *L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 2);
        PushVector3(L, CrossProduct(v3, a));
		return 1;
	}
	
	int Vector3Bridge::on_lerp(lua_State *L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
        const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 2);
		float alpha = lua_tofloat(L, 3);
        PushVector3(L, Lerp(v3, a, alpha));
		return 1;
	}

	int Vector3Bridge::on_compare(lua_State* L)
	{
		const Rainbow::Vector3f& a = *Vector3Bridge::GetObject(L, 1);
		const Rainbow::Vector3f& b = *Vector3Bridge::GetObject(L, 2);
		return Rainbow::Equal(a,b) ? 1 : 0;
	}

	int Vector3Bridge::OnGetX(lua_State* L)
	{
		const Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		lua_pushnumber(L, v3.x);
		return 1;
	}

	int Vector3Bridge::OnGetY(lua_State* L)
	{
		const Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		lua_pushnumber(L, v3.y);
		return 1;
	}

	int Vector3Bridge::OnGetZ(lua_State* L)
	{
		const Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		lua_pushnumber(L, v3.z);
		return 1;
	}

	int Vector3Bridge::OnLength(lua_State* L)
	{
		const Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		lua_pushnumber(L, v3.Length());
		return 1;
	}

	int Vector3Bridge::OnLengthSqr(lua_State* L)
	{
		const Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		lua_pushnumber(L, v3.LengthSqr());
		return 1;
	}

	int Vector3Bridge::OnSetX(lua_State* L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v3.x = lua_tonumber(L, 3);
		return 0;
	}
	int Vector3Bridge::OnSetY(lua_State* L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v3.y = lua_tonumber(L, 3);
		return 0;
	}
	int Vector3Bridge::OnSetZ(lua_State* L)
	{
		Rainbow::Vector3f& v3 = *Vector3Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v3.z = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<Rainbow::Vector3f>::ms_ClassName = "Bridge_Vector3";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector3f>::ms_defParams =
	{
		{"X", Vector3Bridge::OnGetX},
		{"Y", Vector3Bridge::OnGetY},
		{"Z", Vector3Bridge::OnGetZ},
		{"Length", Vector3Bridge::OnLength},
		{"LengthSqr", Vector3Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector3f>::ms_defExParams =
	{
		{"x", Vector3Bridge::OnGetX},
		{"y", Vector3Bridge::OnGetY},
		{"z", Vector3Bridge::OnGetZ},
		{"length", Vector3Bridge::OnLength},
		{"lengthSqr", Vector3Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector3f>::ms_defFunctions =
	{
		{"Normalize", Vector3Bridge::Normalize},
		{"SetValue", Vector3Bridge::SetValue},
		{"Dot", Vector3Bridge::on_dot},
		{"Cross", Vector3Bridge::on_cross},
		{"Lerp", Vector3Bridge::on_lerp},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector3f>::ms_defExFunctions =
	{
		{"normalize", Vector3Bridge::Normalize},
		{"setValue", Vector3Bridge::SetValue},
		{"dot", Vector3Bridge::on_dot},
		{"cross", Vector3Bridge::on_cross},
		{"lerp", Vector3Bridge::on_lerp},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector3f>::ms_newIndexAttributes =
	{
		{"X", Vector3Bridge::OnSetX},
		{"x", Vector3Bridge::OnSetX},
		{"Y", Vector3Bridge::OnSetY},
		{"y", Vector3Bridge::OnSetY},
		{"Z", Vector3Bridge::OnSetZ},
		{"z", Vector3Bridge::OnSetZ},
	};

	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::Vector3f);

#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Vector3f>::OnIndex(lua_State* L, Rainbow::Vector3f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			lua_pushnumber(L, obj.x);
			return 1;
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			lua_pushnumber(L, obj.y);
			return 1;
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			lua_pushnumber(L, obj.z);
			return 1;
		}
		else if (strcmp(key, "length") == 0 || strcmp(key, "Length") == 0)
		{
			lua_pushnumber(L, obj.Length());
			return 1;
		}
		else if (strcmp(key, "lengthSqr") == 0 || strcmp(key, "LengthSqr") == 0)
		{
			lua_pushnumber(L, obj.LengthSqr());
			return 1;
		}
		else if (strcmp(key, "normalize") == 0 || strcmp(key, "Normalize") == 0)
		{
			lua_pushcfunction(L, Vector3Bridge::Normalize);
			return 1;
		}
		else if (strcmp(key, "setValue") == 0 || strcmp(key, "SetValue") == 0)
		{
			lua_pushcfunction(L, Vector3Bridge::SetValue);
			return 1;
		}
		else if (strcmp(key, "dot") == 0 || strcmp(key, "Dot") == 0)
		{
			lua_pushcfunction(L, Vector3Bridge::on_dot);
			return 1;
		}
		else if (strcmp(key, "cross") == 0 || strcmp(key, "Cross") == 0)
		{
			lua_pushcfunction(L, Vector3Bridge::on_cross);
			return 1;
		}
		else if (strcmp(key, "lerp") == 0 || strcmp(key, "Lerp") == 0)
		{
			lua_pushcfunction(L, Vector3Bridge::on_lerp);
			return 1;
		}
		return OnIndexError(L,key);
	}
	template<>
	void Bridge<Rainbow::Vector3f>::OnNewIndex(lua_State* L, Rainbow::Vector3f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.x = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.y = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.z = lua_tonumber(L, 3);
		}
	}
#endif // LUA_BRIDGE_USE_CHAR


	void Vector3Bridge::RegisterMetatable(lua_State *L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());

		// Register the object events
		lua_pushcfunction(L, Vector3Bridge::on_add);
		lua_setfield(L, -2, "__add");

		lua_pushcfunction(L, Vector3Bridge::on_sub);
		lua_setfield(L, -2, "__sub");

		lua_pushcfunction(L, Vector3Bridge::on_mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, Vector3Bridge::on_div);
		lua_setfield(L, -2, "__div");

		lua_pushcfunction(L, Vector3Bridge::on_unm);
		lua_setfield(L, -2, "__unm");

		lua_pushcfunction(L, Vector3Bridge::on_compare);
		lua_setfield(L, -2, "__eq");
	}

	const luaL_Reg Vector2Bridge::ms_ClassLibrary[] = {
	{"new", Vector2Bridge::NewVector2},
	{"New", Vector2Bridge::NewVector2},
	{"Normalize", Vector2Bridge::Normalize},
	{nullptr, nullptr}
	};

	int Vector2Bridge::NewVector2(lua_State* L)
	{
		float vector[2];
		int count = std::min(2, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tofloat(L, i + 1);
		for (int i = count; i < 2; i++)
			vector[i] = 0.0;
		PushNewObject(L, vector[0], vector[1]);
		return 1;
	}

	void Vector2Bridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Vector2", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int Vector2Bridge::on_add(lua_State* L)
	{
		const Rainbow::Vector2f& a = *Vector2Bridge::GetObject(L, 1);
		const Rainbow::Vector2f& b = *Vector2Bridge::GetObject(L, 2);
		PushVector2(L, a + b);
		return 1;
	};

	int Vector2Bridge::on_sub(lua_State* L)
	{
		const Rainbow::Vector2f& a = *Vector2Bridge::GetObject(L, 1);
		const Rainbow::Vector2f& b = *Vector2Bridge::GetObject(L, 2);
		PushVector2(L, a - b);
		return 1;
	};

	int Vector2Bridge::on_mul(lua_State* L)
	{
		Rainbow::Vector2f a;
		if (Vector2Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector2f b;
			if (Vector2Bridge::GetValue(L, 2, b))
				PushVector2(L, a * b);
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector2(L, a * c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else
		{
			a = *Vector2Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector2(L, a*b/* b * a*/);
		}
		return 1;
	};

	int Vector2Bridge::on_div(lua_State* L)
	{
		Rainbow::Vector2f a;
		if (Vector2Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector2f b;
			if (Vector2Bridge::GetValue(L, 2, b))
				PushVector2(L, a / b);
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector2(L, a / c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else {
			a = *Vector2Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector2(L, Rainbow::Vector2f(b, b) / a);
		}
		return 1;
	};

	int Vector2Bridge::on_unm(lua_State* L)
	{
		PushVector2(L, -*Vector2Bridge::GetObject(L, 1));
		return 1;
	};

	int Vector2Bridge::on_compare(lua_State* L)
	{
		const Rainbow::Vector2f& a = *Vector2Bridge::GetObject(L, 1);
		const Rainbow::Vector2f& b = *Vector2Bridge::GetObject(L, 2);
		return Rainbow::Equal(a, b) ? 1 : 0;
	}

	int Vector2Bridge::SetValue(lua_State* L)
	{
		Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		v2.x = lua_tofloat(L, 2);
		v2.y = lua_tofloat(L, 3);
		return 0;
	}

	int Vector2Bridge::Normalize(lua_State* L)
	{
		Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		v2.NormalizeSafe();
		return 0;
	}

	int Vector2Bridge::OnLength(lua_State* L)
	{
		const Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		lua_pushnumber(L, v2.Length());
		return 1;
	}

	int Vector2Bridge::OnLengthSqr(lua_State* L)
	{
		const Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		lua_pushnumber(L, v2.LengthSqr());
		return 1;
	}

	int Vector2Bridge::OnGetX(lua_State* L)
	{
		const Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		lua_pushnumber(L, v2.x);
		return 1;
	}

	int Vector2Bridge::OnGetY(lua_State* L)
	{
		const Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		lua_pushnumber(L, v2.y);
		return 1;
	}

	int Vector2Bridge::OnSetX(lua_State* L)
	{
		Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v2.x = lua_tonumber(L, 3);
		return 0;
	}

	int Vector2Bridge::OnSetY(lua_State* L)
	{
		Rainbow::Vector2f& v2 = *Vector2Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v2.y = lua_tonumber(L, 3);
		return 0;
	}
	template<>
	const std::string Bridge<Rainbow::Vector2f>::ms_ClassName = "Bridge_Vector2";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector2f>::ms_defParams =
	{
		{"X", Vector2Bridge::OnGetX},
		{"Y", Vector2Bridge::OnGetY},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector2f>::ms_defExParams =
	{
		{"x", Vector2Bridge::OnGetX},
		{"y", Vector2Bridge::OnGetY},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector2f>::ms_defFunctions =
	{
		{"Normalize", Vector2Bridge::Normalize},
		{"SetValue", Vector2Bridge::SetValue},
		{"Length", Vector2Bridge::OnLength},
		{"LengthSqr", Vector2Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector2f>::ms_defExFunctions =
	{
		{"normalize", Vector2Bridge::Normalize},
		{"setValue", Vector2Bridge::SetValue},
		{"length", Vector2Bridge::OnLength},
		{"lengthSqr", Vector2Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector2f>::ms_newIndexAttributes =
	{
		{"X", Vector2Bridge::OnSetX},
		{"x", Vector2Bridge::OnSetX},
		{"Y", Vector2Bridge::OnSetY},
		{"y", Vector2Bridge::OnSetY},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::Vector2f);

#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Vector2f>::OnIndex(lua_State* L, Rainbow::Vector2f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			lua_pushnumber(L, obj.x);
			return 1;
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			lua_pushnumber(L, obj.y);
			return 1;
		}
		else if (strcmp(key, "length") == 0 || strcmp(key, "Length") == 0)
		{
			lua_pushnumber(L, obj.Length());
			return 1;
		}
		else if (strcmp(key, "lengthSqr") || strcmp(key, "LengthSqr") == 0)
		{
			lua_pushnumber(L, obj.LengthSqr());
			return 1;
		}
		else if (strcmp(key, "normalize") == 0 || strcmp(key, "Normalize") == 0)
		{
			lua_pushcfunction(L, Vector2Bridge::Normalize);
			return 1;
		}
		else if (strcmp(key, "setValue") == 0 || strcmp(key, "SetValue") == 0)
		{
			lua_pushcfunction(L, Vector2Bridge::SetValue);
			return 1;
		}
		return OnIndexError(L, key);
	}
	template<>
	void Bridge<Rainbow::Vector2f>::OnNewIndex(lua_State* L, Rainbow::Vector2f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.x = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.y = lua_tonumber(L, 3);
		}
	}
#endif // LUA_BRIDGE_USE_CHAR


	void Vector2Bridge::RegisterMetatable(lua_State* L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());

		// Register the object events
		lua_pushcfunction(L, Vector2Bridge::on_add);
		lua_setfield(L, -2, "__add");

		lua_pushcfunction(L, Vector2Bridge::on_sub);
		lua_setfield(L, -2, "__sub");

		lua_pushcfunction(L, Vector2Bridge::on_mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, Vector2Bridge::on_div);
		lua_setfield(L, -2, "__div");

		lua_pushcfunction(L, Vector2Bridge::on_unm);
		lua_setfield(L, -2, "__unm");

		lua_pushcfunction(L, Vector2Bridge::on_compare);
		lua_setfield(L, -2, "__eq");
	}

	/***********************Vector4*******************/
	const luaL_Reg Vector4Bridge::ms_ClassLibrary[] = {
		{"new", Vector4Bridge::NewVector4},
		{"New", Vector4Bridge::NewVector4},
		{"Normalize", Vector4Bridge::Normalize},
		{nullptr, nullptr}
	};

	int Vector4Bridge::NewVector4(lua_State* L)
	{
		const int COUNT = 4;
		float vector[COUNT] = {0.f,0.f,0.f,0.f};
		for (int i = 0; i < COUNT; i++)
		{
			if (lua_isnumber(L, i + 1))
			{
				vector[i] = lua_tofloat(L, i + 1);
			}
		}
			
		PushNewObject(L, vector[0], vector[1], vector[2], vector[3]);
		return 1;
	}

	void Vector4Bridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Vector4", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int Vector4Bridge::on_add(lua_State* L)
	{
		const Rainbow::Vector4f& a = *Vector4Bridge::GetObject(L, 1);
		const Rainbow::Vector4f& b = *Vector4Bridge::GetObject(L, 2);
		PushVector4(L, a + b);
		return 1;
	};

	int Vector4Bridge::on_sub(lua_State* L)
	{
		const Rainbow::Vector4f& a = *Vector4Bridge::GetObject(L, 1);
		const Rainbow::Vector4f& b = *Vector4Bridge::GetObject(L, 2);
		PushVector4(L, a - b);
		return 1;
	};

	int Vector4Bridge::on_mul(lua_State* L)
	{
		Rainbow::Vector4f a;
		if (Vector4Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector4f b;
			if (Vector4Bridge::GetValue(L, 2, b))
				PushVector4(L, a * b);
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector4(L, a * c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else
		{
			a = *Vector4Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector4(L, a*b/*b * a*/);
		}
		return 1;
	};

	int Vector4Bridge::on_div(lua_State* L)
	{
		Rainbow::Vector4f a;
		if (Vector4Bridge::GetValue(L, 1, a))
		{
			Rainbow::Vector4f b;
			if (Vector4Bridge::GetValue(L, 2, b))
				PushVector4(L, a / b);
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushVector4(L, a / c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else {
			a = *Vector4Bridge::GetObject(L, 2);
			float b = lua_tofloat(L, 1);
			PushVector4(L, Rainbow::Vector4f(b, b) / a);
		}
		return 1;
	};

	int Vector4Bridge::on_unm(lua_State* L)
	{
		PushVector4(L, -*Vector4Bridge::GetObject(L, 1));
		return 1;
	};

	int Vector4Bridge::on_compare(lua_State* L)
	{
		const Rainbow::Vector4f& a = *Vector4Bridge::GetObject(L, 1);
		const Rainbow::Vector4f& b = *Vector4Bridge::GetObject(L, 2);
		return Rainbow::Equal(a, b) ? 1 : 0;
	}

	int Vector4Bridge::SetValue(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		v4.x = lua_tofloat(L, 2);
		v4.y = lua_tofloat(L, 3);
		v4.z = lua_tofloat(L, 4);
		v4.w = lua_tofloat(L, 5);
		return 0;
	}

	int Vector4Bridge::Normalize(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		v4.Normalize();
		return 0;
	}

	int Vector4Bridge::OnLength(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.Length());
		return 1;
	}

	int Vector4Bridge::OnLengthSqr(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.LengthSqr());
		return 1;
	}

	int Vector4Bridge::OnGetX(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.x);
		return 1;
	}

	int Vector4Bridge::OnGetY(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.y);
		return 1;
	}

	int Vector4Bridge::OnGetZ(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.z);
		return 1;
	}

	int Vector4Bridge::OnGetW(lua_State* L)
	{
		const Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		lua_pushnumber(L, v4.w);
		return 1;
	}

	int Vector4Bridge::OnSetX(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v4.x = lua_tonumber(L, 3);
		return 0;
	}

	int Vector4Bridge::OnSetY(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v4.y = lua_tonumber(L, 3);
		return 0;
	}

	int Vector4Bridge::OnSetZ(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v4.z = lua_tonumber(L, 3);
		return 0;
	}

	int Vector4Bridge::OnSetW(lua_State* L)
	{
		Rainbow::Vector4f& v4 = *Vector4Bridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			v4.w = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<Rainbow::Vector4f>::ms_ClassName = "Bridge_Vector4";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector4f>::ms_defParams =
	{
		{"X", Vector4Bridge::OnGetX},
		{"Y", Vector4Bridge::OnGetY},
		{"Z", Vector4Bridge::OnGetZ},
		{"W", Vector4Bridge::OnGetW},
		{"Length", Vector4Bridge::OnLength},
		{"LengthSqr", Vector4Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector4f>::ms_defExParams =
	{
		{"x", Vector4Bridge::OnGetX},
		{"y", Vector4Bridge::OnGetY},
		{"z", Vector4Bridge::OnGetZ},
		{"w", Vector4Bridge::OnGetW},
		{"length", Vector4Bridge::OnLength},
		{"lengthSqr", Vector4Bridge::OnLengthSqr},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector4f>::ms_defFunctions =
	{
		{"Normalize", Vector4Bridge::Normalize},
		{"SetValue", Vector4Bridge::SetValue},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector4f>::ms_defExFunctions =
	{
		{"normalize", Vector4Bridge::Normalize},
		{"setValue", Vector4Bridge::SetValue},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Vector4f>::ms_newIndexAttributes =
	{
		{"X", Vector4Bridge::OnSetX},
		{"x", Vector4Bridge::OnSetX},
		{"Y", Vector4Bridge::OnSetY},
		{"y", Vector4Bridge::OnSetY},
		{"Z", Vector4Bridge::OnSetZ},
		{"z", Vector4Bridge::OnSetZ},
		{"W", Vector4Bridge::OnSetW},
		{"w", Vector4Bridge::OnSetW},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::Vector4f);

#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Vector4f>::OnIndex(lua_State* L, Rainbow::Vector4f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			lua_pushnumber(L, obj.x);
			return 1;
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			lua_pushnumber(L, obj.y);
			return 1;
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			lua_pushnumber(L, obj.z);
			return 1;
		}
		else if (strcmp(key, "w") == 0 || strcmp(key, "W") == 0)
		{
			lua_pushnumber(L, obj.w);
			return 1;
		}
		else if (strcmp(key, "length") == 0 || strcmp(key, "Length") == 0)
		{
			lua_pushnumber(L, obj.Length());
			return 1;
		}
		else if (strcmp(key, "lengthSqr") == 0 || strcmp(key, "LengthSqr") == 0)
		{
			lua_pushnumber(L, obj.LengthSqr());
			return 1;
		}
		else if (strcmp(key, "normalize") == 0 || strcmp(key, "Normalize") == 0)
		{
			lua_pushcfunction(L, Vector4Bridge::Normalize);
			return 1;
		}
		else if (strcmp(key, "setValue") == 0 || strcmp(key, "SetValue") == 0)
		{
			lua_pushcfunction(L, Vector4Bridge::SetValue);
			return 1;
		}

		return OnIndexError(L, key);
	}
	template<>
	void Bridge<Rainbow::Vector4f>::OnNewIndex(lua_State* L, Rainbow::Vector4f& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.x = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.y = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.z = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "w") == 0 || strcmp(key, "W") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.w = lua_tonumber(L, 3);
		}
	}
#endif // LUA_BRIDGE_USE_CHAR

	void Vector4Bridge::RegisterMetatable(lua_State* L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());

		// Register the object events
		lua_pushcfunction(L, Vector4Bridge::on_add);
		lua_setfield(L, -2, "__add");

		lua_pushcfunction(L, Vector4Bridge::on_sub);
		lua_setfield(L, -2, "__sub");

		lua_pushcfunction(L, Vector4Bridge::on_mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, Vector4Bridge::on_div);
		lua_setfield(L, -2, "__div");

		lua_pushcfunction(L, Vector4Bridge::on_unm);
		lua_setfield(L, -2, "__unm");

		lua_pushcfunction(L, Vector4Bridge::on_compare);
		lua_setfield(L, -2, "__eq");
	}

	/***********************Vector4*******************/


	const luaL_Reg WCoordBridge::ms_ClassLibrary[] = {
	{"new", WCoordBridge::NewWCoord},
	{"New", WCoordBridge::NewWCoord},
	{nullptr, nullptr}
	};
	int WCoordBridge::NewWCoord(lua_State* L)
	{
		int vector[3];
		int count = std::min(3, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tonumber(L, i + 1);
		for (int i = count; i < 3; i++)
			vector[i] = 0;
		PushNewObject(L, vector[0], vector[1], vector[2]);
		return 1;
	}

	void WCoordBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "WCoord", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int WCoordBridge::on_add(lua_State *L)
	{
		const WCoord& a = *WCoordBridge::GetObject(L, 1);
		const WCoord& b = *WCoordBridge::GetObject(L, 2);
		PushWCoord(L, a + b);
		return 1;
	};

	int WCoordBridge::on_sub(lua_State *L)
	{
		const WCoord& a = *WCoordBridge::GetObject(L, 1);
		const WCoord& b = *WCoordBridge::GetObject(L, 2);
		PushWCoord(L, a - b);
		return 1;
	};

	int WCoordBridge::on_mul(lua_State *L)
	{
		WCoord a;
		if (WCoordBridge::GetValue(L, 1, a))
		{
			WCoord b;
			if (WCoordBridge::GetValue(L, 2, b))
				PushWCoord(L, a * b);
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				float c = lua_tofloat(L, 2);
				PushWCoord(L, a * c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else
		{
			a = *Vector3Bridge::GetObject(L, 2);
			int b = lua_tonumber(L, 1);
			PushWCoord(L, a * b);
		}
		return 1;
	};

	int WCoordBridge::on_div(lua_State *L)
	{
		WCoord a;
		if (WCoordBridge::GetValue(L, 1, a))
		{
			WCoord b;
			if (WCoordBridge::GetValue(L, 2, b))
			{
			}
			else if (!lua_isnumber(L, 2))
			{

			}
			else
			{
				int c = lua_tonumber(L, 2);
				PushWCoord(L, a / c);
			}
		}
		else if (!lua_isnumber(L, 1))
		{

		}
		else {

		}
		return 1;
	};

	int WCoordBridge::on_unm(lua_State *L)
	{
		WCoord coord = *WCoordBridge::GetObject(L, 1);
		coord.x = -coord.x;
		coord.y = -coord.y;
		coord.z = -coord.z;
		PushWCoord(L, coord);
		return 1;
	};
	int WCoordBridge::OnGetX(lua_State* L)
	{
		const WCoord& coord = *WCoordBridge::GetObject(L, 1);
		lua_pushnumber(L, coord.x);
		return 1;
	}

	int WCoordBridge::OnGetY(lua_State* L)
	{
		const WCoord& coord = *WCoordBridge::GetObject(L, 1);
		lua_pushnumber(L, coord.y);
		return 1;
	}

	int WCoordBridge::OnGetZ(lua_State* L)
	{
		const WCoord& coord = *WCoordBridge::GetObject(L, 1);
		lua_pushnumber(L, coord.z);
		return 1;
	}

	int WCoordBridge::OnSetX(lua_State* L)
	{
		WCoord& coord = *WCoordBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			coord.x = lua_tonumber(L, 3);
		return 0;
	}

	int WCoordBridge::OnSetY(lua_State* L)
	{
		WCoord& coord = *WCoordBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			coord.y = lua_tonumber(L, 3);
		return 0;
	}

	int WCoordBridge::OnSetZ(lua_State* L)
	{
		WCoord& coord = *WCoordBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			coord.z = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<WCoord>::ms_ClassName = "Bridge_WCoord";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<WCoord>::ms_defParams =
	{
		{"X", WCoordBridge::OnGetX},
		{"Y", WCoordBridge::OnGetY},
		{"Z", WCoordBridge::OnGetZ},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<WCoord>::ms_defExParams =
	{
		{"x", WCoordBridge::OnGetX},
		{"y", WCoordBridge::OnGetY},
		{"z", WCoordBridge::OnGetZ},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<WCoord>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<WCoord>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<WCoord>::ms_newIndexAttributes =
	{
		{"X", WCoordBridge::OnSetX},
		{"x", WCoordBridge::OnSetX},
		{"Y", WCoordBridge::OnSetY},
		{"y", WCoordBridge::OnSetY},
		{"Z", WCoordBridge::OnSetZ},
		{"z", WCoordBridge::OnSetZ},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(WCoord);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<WCoord>::OnIndex(lua_State* L, WCoord& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			lua_pushnumber(L, obj.x);
			return 1;
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			lua_pushnumber(L, obj.y);
			return 1;
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			lua_pushnumber(L, obj.z);
			return 1;
		}
		
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<WCoord>::OnNewIndex(lua_State* L, WCoord& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.x = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.y = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.z = lua_tonumber(L, 3);
		}
	}
#endif

	void WCoordBridge::RegisterMetatable(lua_State *L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());

		// Register the object events
		lua_pushcfunction(L, WCoordBridge::on_add);
		lua_setfield(L, -2, "__add");

		lua_pushcfunction(L, WCoordBridge::on_sub);
		lua_setfield(L, -2, "__sub");

		lua_pushcfunction(L, WCoordBridge::on_mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, WCoordBridge::on_div);
		lua_setfield(L, -2, "__div");

		lua_pushcfunction(L, WCoordBridge::on_unm);
		lua_setfield(L, -2, "__unm");
	}


	const luaL_Reg QuaternionBridge::ms_ClassLibrary[] = {
		{"new", QuaternionBridge::NewQuaternion},
		{"lookat", QuaternionBridge::LookAt},
		{"fromeuler", QuaternionBridge::FromEulerAngles},
		{"axisangle", QuaternionBridge::FromAxisAngle},
		{"New", QuaternionBridge::NewQuaternion},
		{"LookAt", QuaternionBridge::LookAt},
		{"FromEuler", QuaternionBridge::FromEulerAngles},
		{"FromAxisAngle", QuaternionBridge::FromAxisAngle},
		{"Lerp", QuaternionBridge::Lerp},
		{nullptr, nullptr}
	};

	int QuaternionBridge::NewQuaternion(lua_State* L)
	{
		const int COUNT = 4;
		float vector[COUNT] = { 0.f,0.f,0.f,1.f };
		for (int i = 0; i < COUNT; i++)
		{
			if (lua_isnumber(L, i + 1))
			{
				vector[i] = lua_tofloat(L, i + 1);
			}
		}
		PushNewObject(L, vector[0], vector[1], vector[2], vector[3]);
		return 1;
	}

	int QuaternionBridge::LookAt(lua_State* L)
	{
		Rainbow::Vector3f a(0, 0, 0);
		Vector3Bridge::GetValue(L, 1, a);
		Rainbow::Vector3f b(0.0f, 1.0f, 0.0f);
		Vector3Bridge::GetValue(L, 2, b);
		///PushNewObject(L, a, b);
		// auto q = PushNewObject(L, 0, 0, 0, 0);
		// QuaternionLookAt(*q, a, b);
		// return 1;
		Rainbow::Quaternionf q;
		lua_pushboolean(L, Rainbow::LookRotationToQuaternionf(a, b, &q) ? 1 : 0);
		PushNewObject(L, q);
		return 2;
	}

	int QuaternionBridge::FromEulerAngles(lua_State* L)
	{
		float p1 = lua_tofloat(L, 1);
		float p2 = lua_tofloat(L, 2);
		float p3 = lua_tofloat(L, 3);
		PushNewObject(L, Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(p1, p2, p3)));//Rainbow::Quaternionf::Euler(p1, p2, p3));
		return 1;
	}

	int QuaternionBridge::Lerp(lua_State* L)
	{
		Rainbow::Quaternionf a(0, 0, 0, 1);
		Rainbow::Quaternionf b(0, 0, 0, 1);
		QuaternionBridge::GetValue(L, 1, a);
		QuaternionBridge::GetValue(L, 2, b);
		float t = lua_tofloat(L, 3);

		Rainbow::Quaternionf q = Rainbow::Lerp(a, b, t);

		PushNewObject(L, q);

		return 1;
	}



	int QuaternionBridge::FromAxisAngle(lua_State* L)
	{
		Rainbow::Quaternionf quat;
		Rainbow::Vector3f a;
		if (Vector3Bridge::GetValue(L, 1, a))
		{
			float angle = lua_tofloat(L, 2);
			//quat.setAxisAngle(a, angle);
			
			if (!Rainbow::CompareApproximately(Rainbow::SqrMagnitude(a), 1.0F)) // 校验四元数是否合法，不合法报脚本错误
			{
				ScriptState::LuaThrowError(L, "param [1] is invalid! The lengh of the vector needs tobe greater than 1.0");
				return 0;
			}
			quat = Rainbow::AxisAngleToQuaternionf(a, Rainbow::Deg2Rad(angle));
			PushNewObject(L, quat);
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_RotateAxisAngle(lua_State* L)
	{
		//const 
		Rainbow::Quaternionf& target = *QuaternionBridge::GetObject(L, 1);
		Rainbow::Vector3f dir;
		Rainbow::Vector3f dirtemp;
		if (Vector3Bridge::GetValue(L, 2, dir))
		{
			float angle = lua_tofloat(L, 3);
			//target.rotate(dir, angle);

			if (!Rainbow::CompareApproximately(Rainbow::SqrMagnitude(dir), 1.0F)) // 校验四元数是否合法，不合法报脚本错误
			{
				ScriptState::LuaThrowError(L, "param [1] is invalid! The lengh of the vector needs tobe greater than 1.0");
				return 0;
			}
			target = Rainbow::AxisAngleToQuaternionf(dir, Rainbow::Deg2Rad(angle)) * target;
		}
		return 0;
	}

	int QuaternionBridge::On_Lerp(lua_State *L)
	{
		const Rainbow::Quaternionf& a = *QuaternionBridge::GetObject(L, 1);
		const Rainbow::Quaternionf& other = *QuaternionBridge::GetObject(L, 2);
		float alpha = lua_tofloat(L, 3);

		PushNewObject(L, Slerp(a, other, alpha));
		return 1;
	}

	int QuaternionBridge::On_Mul(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Rainbow::Quaternionf b;
			Rainbow::Vector3f c;
			if (QuaternionBridge::GetValue(L, 2, b))
			{
				PushNewObject(L, a * b);
				return 1;
			}
			else if (Vector3Bridge::GetValue(L, 2, c))
			{
				Vector3Bridge::PushVector3(L, a * c);
				return 1;
			}
		}
		return 0;
	}

	int QuaternionBridge::On_LookDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, a.GetAxisZ());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_ForwardDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, -a.GetAxisZ());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_LeftDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, a.GetAxisX());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_RightDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, -a.GetAxisX());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_UpDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, a.GetAxisY());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_DownDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Vector3Bridge::PushVector3(L, -a.GetAxisY());
			return 1;
		}
		return 0;
	}

	int QuaternionBridge::On_RotateToDir(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Rainbow::Vector3f dir;
			if (Vector3Bridge::GetValue(L, 2, dir))
			{
				Rainbow::Vector3f lookdir;
				//a.rotate(lookdir, dir);
				lookdir = RotateVectorByQuat(a, dir);
				Vector3Bridge::PushVector3(L, lookdir);
				return 1;
			}
			return 0;
		}
		return 0;
	}

	int QuaternionBridge::On_Compare(lua_State* L)
	{
		Rainbow::Quaternionf a;
		if (QuaternionBridge::GetValue(L, 1, a))
		{
			Rainbow::Quaternionf b;
			if (QuaternionBridge::GetValue(L, 2, b))
			{
				return Rainbow::Equal(a,b) ? 1 : 0;
			}
		}
		return 0;
	}

	int QuaternionBridge::OnGetX(lua_State* L)
	{
		const Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		lua_pushnumber(L, q.x);
		return 1;
	}

	int QuaternionBridge::OnGetY(lua_State* L)
	{
		const Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		lua_pushnumber(L, q.y);
		return 1;
	}

	int QuaternionBridge::OnGetZ(lua_State* L)
	{
		const Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		lua_pushnumber(L, q.z);
		return 1;
	}

	int QuaternionBridge::OnGetW(lua_State* L)
	{
		const Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		lua_pushnumber(L, q.w);
		return 1;
	}

	int QuaternionBridge::OnSetX(lua_State* L)
	{
		Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			q.x = lua_tonumber(L, 3);
		return 0;
	}

	int QuaternionBridge::OnSetY(lua_State* L)
	{
		Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			q.y = lua_tonumber(L, 3);
		return 0;
	}

	int QuaternionBridge::OnSetZ(lua_State* L)
	{
		Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			q.z = lua_tonumber(L, 3);
		return 0;
	}

	int QuaternionBridge::OnSetW(lua_State* L)
	{
		Rainbow::Quaternionf& q = *QuaternionBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			q.w = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<Rainbow::Quaternionf>::ms_ClassName = "Bridge_Quaternion";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Quaternionf>::ms_defParams =
	{
		{"X", QuaternionBridge::OnGetX},
		{"Y", QuaternionBridge::OnGetY},
		{"Z", QuaternionBridge::OnGetZ},
		{"W", QuaternionBridge::OnGetW},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Quaternionf>::ms_defExParams =
	{
		{"x", QuaternionBridge::OnGetX},
		{"y", QuaternionBridge::OnGetY},
		{"z", QuaternionBridge::OnGetZ},
		{"w", QuaternionBridge::OnGetW},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Quaternionf>::ms_defFunctions =
	{
		{"Lerp", QuaternionBridge::On_Lerp},
		{"RotateAxisAngle", QuaternionBridge::On_RotateAxisAngle},
		{"LookDir", QuaternionBridge::On_LookDir},
		{"RotateToDir", QuaternionBridge::On_RotateToDir},
		{"ForwardDir", QuaternionBridge::On_ForwardDir},
		{"LeftDir", QuaternionBridge::On_LeftDir},
		{"RightDir", QuaternionBridge::On_RightDir},
		{"UpDir", QuaternionBridge::On_UpDir},
		{"DownDir", QuaternionBridge::On_DownDir},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Quaternionf>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Quaternionf>::ms_newIndexAttributes =
	{
		{"X", QuaternionBridge::OnSetX},
		{"x", QuaternionBridge::OnSetX},
		{"Y", QuaternionBridge::OnSetY},
		{"y", QuaternionBridge::OnSetY},
		{"Z", QuaternionBridge::OnSetZ},
		{"z", QuaternionBridge::OnSetZ},
		{"W", QuaternionBridge::OnSetW},
		{"w", QuaternionBridge::OnSetW},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::Quaternionf);

#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Quaternionf>::OnIndex(lua_State* L, Rainbow::Quaternionf& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			lua_pushnumber(L, obj.x);
			return 1;
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			lua_pushnumber(L, obj.y);
			return 1;
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			lua_pushnumber(L, obj.z);
			return 1;
		}
		else if (strcmp(key, "w") == 0 || strcmp(key, "W") == 0)
		{
			lua_pushnumber(L, obj.w);
			return 1;
		}
		else if (strcmp(key, "Lerp") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_Lerp);
			return 1;
		}
		else if (strcmp(key, "RotateAxisAngle") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_RotateAxisAngle);
			return 1;
		}
		else if (strcmp(key, "LookDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_LookDir);
			return 1;
		}
		else if (strcmp(key, "RotateToDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_RotateToDir);
			return 1;
		}
		else if (strcmp(key, "ForwardDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_ForwardDir);
			return 1;
		}		
		else if (strcmp(key, "LeftDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_LeftDir);
			return 1;
		}		
		else if (strcmp(key, "RightDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_RightDir);
			return 1;
		}		
		else if (strcmp(key, "UpDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_UpDir);
			return 1;
		}		
		else if (strcmp(key, "DownDir") == 0)
		{
			lua_pushcfunction(L, QuaternionBridge::On_DownDir);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<Rainbow::Quaternionf>::OnNewIndex(lua_State* L, Rainbow::Quaternionf& obj, const char* key)
	{
		if (strcmp(key, "x") == 0 || strcmp(key, "X") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.x = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "y") == 0 || strcmp(key, "Y") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.y = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "z") == 0 || strcmp(key, "Z") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.z = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "w") == 0 || strcmp(key, "W") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.w = lua_tonumber(L, 3);
		}
	}
#endif // LUA_BRIDGE_USE_CHAR


	void QuaternionBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Quaternion", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	void QuaternionBridge::RegisterMetatable(lua_State *L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());


		lua_pushcfunction(L, QuaternionBridge::On_Mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, QuaternionBridge::On_Compare);
		lua_setfield(L, -2, "__eq");
	}



	template<>
	const std::string Bridge<MINIW::Transform_>::ms_ClassName = "Bridge_Transform";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Transform_>::ms_defParams = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Transform_>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Transform_>::ms_newIndexAttributes = {};
	SANDBOX_BRIDGE_DEF_ONINDEX(MINIW::Transform_);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<MINIW::Transform_>::OnIndex(lua_State* L, MINIW::Transform_& obj, const char* key)
	{
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<MINIW::Transform_>::OnNewIndex(lua_State* L, MINIW::Transform_& obj, const char* key)
	{
	}
#endif //LUA_BRIDGE_USE_CHAR

	void TransformBridge::RegisterMetatable(lua_State* L)
	{
		Super::RegisterMetatable(L);

		KeepStackTop stack(L);
		luaL_getmetatable(L, ms_ClassName.c_str());

		lua_pushcfunction(L, TransformBridge::On_Mul);
		lua_setfield(L, -2, "__mul");

		lua_pushcfunction(L, TransformBridge::On_Add);
		lua_setfield(L, -2, "__add");

		lua_pushcfunction(L, TransformBridge::On_Sub);
		lua_setfield(L, -2, "__sub");
	}

	const luaL_Reg TransformBridge::ms_ClassLibrary[] = {
		{"New", TransformBridge::NewTransform},
		//{"LookAt", TransformBridge::LookAt},
		//{"FromEulerAngles", TransformBridge::FromEulerAngles},
		//{"FromAxisAngle", TransformBridge::FromAxisAngle},
		{nullptr, nullptr}
	};

	/* 注册类库 */
	void TransformBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Transform", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int TransformBridge::NewTransform(lua_State* L)
	{
		MINIW::Transform_ trans;

		// 0 args:   identity
		// 1 vector:   translation
		// 2 vectors:   translation, lookAt
		// 3 numbers:   translation
		// 7 numbers:   translation, quat
		// 12 numbers:  translation, rotation matrix
		const int count = lua_gettop(L);
		switch (count)
		{
		case 0:
			// Identity
			break;

		case 1:
			trans.t = *Vector3Bridge::GetObject(L, 1);
			break;

		case 2:
			trans.t = *Vector3Bridge::GetObject(L, 1);
			trans.LookAt(*Vector3Bridge::GetObject(L, 2));
			break;
		case 3:
			for (int i = 0; i < 3; i++)
				trans.t[i] = lua_tofloat(L, i + 1);
			break;

		case 7:
		{
			for (int i = 0; i < 3; i++)
				trans.t[i] = lua_tofloat(L, i + 1);
			//Rainbow::Quaternionf q;
			trans.r.x = lua_tofloat(L, 3 + 1);
			trans.r.y = lua_tofloat(L, 4 + 1);
			trans.r.z = lua_tofloat(L, 5 + 1);
			trans.r.w = lua_tofloat(L, 6 + 1);
			//trans.r = q;
		}
		break;
		default:
			//throw RBX::runtime_error("Invalid number of arguments: %d", count);
			break;
		}
		PushTransform(L, trans);
		return 1;
	}

	int TransformBridge::On_Add(lua_State* L)
	{
		/*const G3D::CoordinateFrame& a = CoordinateFrameBridge::getObject(L, 1);
		const G3D::Vector3f& b = Vector3Bridge::getObject(L, 2);
		pushCoordinateFrame(L, a + b);*/
		return 1;
	};

	int TransformBridge::On_Sub(lua_State* L)
	{
		/*const G3D::CoordinateFrame& a = CoordinateFrameBridge::getObject(L, 1);
		const G3D::Vector3f& b = Vector3Bridge::getObject(L, 2);
		pushCoordinateFrame(L, a - b);*/
		return 1;
	};

	int TransformBridge::On_Mul(lua_State* L)
	{
		return 1;
	}


	const luaL_Reg RayBridge::ms_ClassLibrary[] = {
		{"new", RayBridge::NewRay},
		{"New", RayBridge::NewRay},
		{nullptr, nullptr}
	};

	int RayBridge::NewRay(lua_State* L)
	{
		Rainbow::Vector3f origin = *Vector3Bridge::GetObject(L, 1);
		Rainbow::Vector3f dir  = *Vector3Bridge::GetObject(L, 2);
		PushNewObject(L, origin, dir);
		return 1;
	}

	void RayBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Ray", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int RayBridge::ClosestPointVector3(lua_State* L)
	{
		MINIW::Ray& self = *RayBridge::GetObject(L, 1);
		Rainbow::Vector3f& point = *Bridge<Rainbow::Vector3f>::GetObject(L, 2);
		Vector3Bridge::PushVector3(L, self.closestPoint(point));
		return 1;
	}

	int RayBridge::DistanceVector3(lua_State* L)
	{
		MINIW::Ray& self = *RayBridge::GetObject(L, 1);
		Rainbow::Vector3f& point = *Bridge<Rainbow::Vector3f>::GetObject(L, 2);
		lua_pushnumber(L, self.distance(point));
		return 1;
	}

	int RayBridge::OnOrigin(lua_State* L)
	{
		MINIW::Ray& self = *RayBridge::GetObject(L, 1);
		Bridge<Rainbow::Vector3f>::PushNewObject(L, self.m_Origin);
		return 1;
	}

	int RayBridge::OnDirection(lua_State* L)
	{
		MINIW::Ray& self = *RayBridge::GetObject(L, 1);
		Bridge<Rainbow::Vector3f>::PushNewObject(L, self.m_Dir);
		return 1;
	}

	int RayBridge::OnUnit(lua_State* L)
	{
		MINIW::Ray& self = *RayBridge::GetObject(L, 1);
		Bridge<MINIW::Ray>::PushNewObject(L, self.unit());
		return 1;
	}

	template<>
	const std::string Bridge<MINIW::Ray>::ms_ClassName = "Bridge_Ray";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Ray>::ms_defParams =
	{
		{"Origin", RayBridge::OnOrigin},
		{"Direction", RayBridge::OnDirection},
		{"Unit", RayBridge::OnUnit},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Ray>::ms_defExParams =
	{
		{"unit", RayBridge::OnUnit},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Ray>::ms_defFunctions =
	{
		{"ClosestPoint", RayBridge::ClosestPointVector3},
		{"Distance", RayBridge::DistanceVector3},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Ray>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::Ray>::ms_newIndexAttributes = {};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(MINIW::Ray);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<MINIW::Ray>::OnIndex(lua_State* L, MINIW::Ray& obj, const char* key)
	{
		if (strcmp(key, "Origin") == 0)
		{
			Vector3Bridge::PushVector3(L, obj.m_Origin);
			return 1;
		}
		else if (strcmp(key, "Direction") == 0)
		{
			Vector3Bridge::PushVector3(L, obj.m_Dir);
			return 1;
		}
		else if (strcmp(key, "Unit") == 0)
		{
			PushNewObject(L, obj.unit());
			return 1;
		}
		else if (strcmp(key, "ClosestPoint") == 0)
		{
			lua_pushcfunction(L, RayBridge::ClosestPointVector3);
			return 1;
		}
		else if (strcmp(key, "Distance") == 0)
		{
			lua_pushcfunction(L, RayBridge::DistanceVector3);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<MINIW::Ray>::OnNewIndex(lua_State* L, MINIW::Ray& obj, const char* key)
	{
	}
#endif // 


	const luaL_Reg RectFloatBridge::ms_ClassLibrary[] = {
		{"new", RectFloatBridge::NewRectFloat},
		{"New", RectFloatBridge::NewRectFloat},
		{nullptr, nullptr}
	};

	int RectFloatBridge::NewRectFloat(lua_State* L)
	{
		float vector[4];
		int count = std::min(4, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tofloat(L, i + 1);
		for (int i = count; i < 3; i++)
			vector[i] = 0.0;
		PushNewObject(L, vector[0], vector[1], vector[2], vector[3]);
		return 1;
	}

	void RectFloatBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Rect", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int RectFloatBridge::OnLeft(lua_State* L)
	{
		const MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		lua_pushnumber(L, r.m_Left);
		return 1;
	}

	int RectFloatBridge::OnTop(lua_State* L)
	{
		const MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		lua_pushnumber(L, r.m_Top);
		return 1;
	}

	int RectFloatBridge::OnRight(lua_State* L)
	{
		const MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		lua_pushnumber(L, r.m_Right);
		return 1;
	}

	int RectFloatBridge::OnBottom(lua_State* L)
	{
		const MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		lua_pushnumber(L, r.m_Bottom);
		return 1;
	}

	int RectFloatBridge::OnSetLeft(lua_State* L)
	{
		MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r.m_Left = lua_tonumber(L, 3);
		return 0;
	}

	int RectFloatBridge::OnSetTop(lua_State* L)
	{
		MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r.m_Top = lua_tonumber(L, 3);
		return 0;
	}

	int RectFloatBridge::OnSetRight(lua_State* L)
	{
		MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r.m_Right = lua_tonumber(L, 3);
		return 0;
	}

	int RectFloatBridge::OnSetBottom(lua_State* L)
	{
		MINIW::RectFloat& r = *RectFloatBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r.m_Bottom = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<MINIW::RectFloat>::ms_ClassName = "Bridge_Rect";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::RectFloat>::ms_defParams =
	{
		{"Left", RectFloatBridge::OnLeft},
		{"Top", RectFloatBridge::OnTop},
		{"Right", RectFloatBridge::OnRight},
		{"Bottom", RectFloatBridge::OnBottom},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::RectFloat>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::RectFloat>::ms_newIndexAttributes = 
	{
		{"Left", RectFloatBridge::OnSetLeft},
		{"Top", RectFloatBridge::OnSetTop},
		{"Right", RectFloatBridge::OnSetRight},
		{"Bottom", RectFloatBridge::OnSetBottom},
	};

	SANDBOX_BRIDGE_DEF_ONINDEX(MINIW::RectFloat);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<MINIW::RectFloat>::OnIndex(lua_State* L, MINIW::RectFloat& obj, const char* key)
	{
		if (strcmp(key, "Left") == 0)
		{
			lua_pushnumber(L, obj.m_Left);
			return 1;
		}
		else if (strcmp(key, "Top") == 0)
		{
			lua_pushnumber(L, obj.m_Top);
			return 1;
		}
		else if (strcmp(key, "Right") == 0)
		{
			lua_pushnumber(L, obj.m_Right);
			return 1;
		}
		else if (strcmp(key, "Bottom") == 0)
		{
			lua_pushnumber(L, obj.m_Bottom);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<MINIW::RectFloat>::OnNewIndex(lua_State* L, MINIW::RectFloat& obj, const char* key)
	{
		if (strcmp(key, "Left") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_Left = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "Top") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_Top = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "Right") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_Right = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "Bottom") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_Bottom = lua_tonumber(L, 3);
		}
	}
#endif // LUA_BRIDGE_USE_CHAR


	//--colorvalue
	const luaL_Reg ColorValueBridge::ms_ClassLibrary[] = {
		{"new", ColorValueBridge::NewColorValue},
		{"New", ColorValueBridge::NewColorValue},
		{nullptr, nullptr}
	};

	int ColorValueBridge::NewColorValue(lua_State* L)
	{
		float vector[4];
		int count = std::min(4, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tofloat(L, i + 1);
		for (int i = count; i < 3; i++)
			vector[i] = 0.0;
		PushNewObject(L, vector[0], vector[1], vector[2], vector[3]);
		return 1;
	}

	void ColorValueBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "ColorValue", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int ColorValueBridge::OnGetR(lua_State* L)
	{
		const Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		lua_pushnumber(L, c.r);
		return 1;
	}

	int ColorValueBridge::OnGetG(lua_State* L)
	{
		const Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		lua_pushnumber(L, c.g);
		return 1;
	}

	int ColorValueBridge::OnGetB(lua_State* L)
	{
		const Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		lua_pushnumber(L, c.b);
		return 1;
	}

	int ColorValueBridge::OnGetA(lua_State* L)
	{
		const Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		lua_pushnumber(L, c.a);
		return 1;
	}

	int ColorValueBridge::OnSetR(lua_State* L)
	{
		Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.r = lua_tonumber(L, 3);
		return 0;
	}

	int ColorValueBridge::OnSetG(lua_State* L)
	{
		Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.g = lua_tonumber(L, 3);
		return 0;
	}

	int ColorValueBridge::OnSetB(lua_State* L)
	{
		Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.b = lua_tonumber(L, 3);
		return 0;
	}

	int ColorValueBridge::OnSetA(lua_State* L)
	{
		Rainbow::ColourValue& c = *ColorValueBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.a = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<Rainbow::ColourValue>::ms_ClassName = "Bridge_ColorValue";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColourValue>::ms_defParams =
	{
		{"R", ColorValueBridge::OnGetR},
		{"G", ColorValueBridge::OnGetG},
		{"B", ColorValueBridge::OnGetB},
		{"A", ColorValueBridge::OnGetA},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColourValue>::ms_defExParams =
	{
		{"r", ColorValueBridge::OnGetR},
		{"g", ColorValueBridge::OnGetG},
		{"b", ColorValueBridge::OnGetB},
		{"a", ColorValueBridge::OnGetA},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColourValue>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColourValue>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColourValue>::ms_newIndexAttributes = 
	{
		{"R", ColorValueBridge::OnSetR},
		{"G", ColorValueBridge::OnSetG},
		{"B", ColorValueBridge::OnSetB},
		{"A", ColorValueBridge::OnSetA},
		{"r", ColorValueBridge::OnSetR},
		{"g", ColorValueBridge::OnSetG},
		{"b", ColorValueBridge::OnSetB},
		{"a", ColorValueBridge::OnSetA},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::ColourValue);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::ColourValue>::OnIndex(lua_State* L, Rainbow::ColourValue& obj, const char* key)
	{
		if (strcmp(key, "r") == 0 || strcmp(key, "R") == 0)
		{
			lua_pushnumber(L, obj.r);
			return 1;
		}
		else if (strcmp(key, "g") == 0 || strcmp(key, "G") == 0)
		{
			lua_pushnumber(L, obj.g);
			return 1;
		}
		else if (strcmp(key, "b") == 0 || strcmp(key, "B") == 0)
		{
			lua_pushnumber(L, obj.b);
			return 1;
		}
		else if (strcmp(key, "a") == 0 || strcmp(key, "A") == 0)
		{
			lua_pushnumber(L, obj.a);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<Rainbow::ColourValue>::OnNewIndex(lua_State* L, Rainbow::ColourValue& obj, const char* key)
	{
		if (strcmp(key, "r") == 0 || strcmp(key, "R") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.r = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "g") == 0 || strcmp(key, "G") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.g = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "b") == 0 || strcmp(key, "B") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.b = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "a") == 0 || strcmp(key, "A") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.a = lua_tonumber(L, 3);
		}
	}

#endif // 


	//ColorQuad
	const luaL_Reg ColorQuadBridge::ms_ClassLibrary[] = {
		{"new", ColorQuadBridge::NewColorQuad},
		{"New", ColorQuadBridge::NewColorQuad},
		{nullptr, nullptr}
	};

	int ColorQuadBridge::NewColorQuad(lua_State* L)
	{
		float vector[4];
		int count = std::min(4, lua_gettop(L));
		for (int i = 0; i < count; i++)
			vector[i] = lua_tofloat(L, i + 1);
		for (int i = count; i < 3; i++)
			vector[i] = 0.0;
		PushNewObject(L, vector[0], vector[1], vector[2], vector[3]);
		return 1;
	}

	void ColorQuadBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "ColorQuad", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int ColorQuadBridge::OnGetR(lua_State* L)
	{
		const Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		lua_pushnumber(L, c.r);
		return 1;
	}

	int ColorQuadBridge::OnGetG(lua_State* L)
	{
		const Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		lua_pushnumber(L, c.g);
		return 1;
	}

	int ColorQuadBridge::OnGetB(lua_State* L)
	{
		const Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		lua_pushnumber(L, c.b);
		return 1;
	}

	int ColorQuadBridge::OnGetA(lua_State* L)
	{
		const Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		lua_pushnumber(L, c.a);
		return 1;
	}

	int ColorQuadBridge::OnSetR(lua_State* L)
	{
		Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.r = lua_tonumber(L, 3);
		return 0;
	}

	int ColorQuadBridge::OnSetG(lua_State* L)
	{
		Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.g = lua_tonumber(L, 3);
		return 0;
	}

	int ColorQuadBridge::OnSetB(lua_State* L)
	{
		Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.b = lua_tonumber(L, 3);
		return 0;
	}

	int ColorQuadBridge::OnSetA(lua_State* L)
	{
		Rainbow::ColorQuad& c = *ColorQuadBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			c.a = lua_tonumber(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<Rainbow::ColorQuad>::ms_ClassName = "Bridge_ColorQuad";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColorQuad>::ms_defParams =
	{
		{"R", ColorQuadBridge::OnGetR},
		{"G", ColorQuadBridge::OnGetG},
		{"B", ColorQuadBridge::OnGetB},
		{"A", ColorQuadBridge::OnGetA},
		{"x", ColorQuadBridge::OnGetA},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColorQuad>::ms_defExParams =
	{
		{"r", ColorQuadBridge::OnGetR},
		{"g", ColorQuadBridge::OnGetG},
		{"b", ColorQuadBridge::OnGetB},
		{"a", ColorQuadBridge::OnGetA},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColorQuad>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColorQuad>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::ColorQuad>::ms_newIndexAttributes = 
	{
		{"R", ColorQuadBridge::OnSetR},
		{"G", ColorQuadBridge::OnSetG},
		{"B", ColorQuadBridge::OnSetB},
		{"A", ColorQuadBridge::OnSetA},
		{"r", ColorQuadBridge::OnSetR},
		{"g", ColorQuadBridge::OnSetG},
		{"b", ColorQuadBridge::OnSetB},
		{"a", ColorQuadBridge::OnSetA},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(Rainbow::ColorQuad);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::ColorQuad>::OnIndex(lua_State* L, Rainbow::ColorQuad& obj, const char* key)
	{
		if (strcmp(key, "r") == 0 || strcmp(key, "R") == 0)
		{
			lua_pushnumber(L, obj.r);
			return 1;
		}
		else if (strcmp(key, "g") == 0 || strcmp(key, "G") == 0)
		{
			lua_pushnumber(L, obj.g);
			return 1;
		}
		else if (strcmp(key, "b") == 0 || strcmp(key, "B") == 0)
		{
			lua_pushnumber(L, obj.b);
			return 1;
		}
		else if (strcmp(key, "a") == 0 || strcmp(key, "A") == 0)
		{
			lua_pushnumber(L, obj.a);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<Rainbow::ColorQuad>::OnNewIndex(lua_State* L, Rainbow::ColorQuad& obj, const char* key)
	{
		if (strcmp(key, "r") == 0 || strcmp(key, "R") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.r = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "g") == 0 || strcmp(key, "G") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.g = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "b") == 0 || strcmp(key, "B") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.b = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "a") == 0 || strcmp(key, "A") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.a = lua_tonumber(L, 3);
		}
	}
#endif //

	//-- TweenInfoBridge
	const luaL_Reg TweenInfoBridge::ms_ClassLibrary[] = {
		{"new", TweenInfoBridge::NewTweenInfo},
		{"New", TweenInfoBridge::NewTweenInfo},
		{nullptr, nullptr}
	};
	int TweenInfoBridge::NewTweenInfo(lua_State* L)
	{
		int easingdirection = 0;
		float time = 0;
		float delaytime = 0;
		int repeatcount = 0;
		int easingStyle = 0;
		bool reverses = false;
			
		time = lua_tofloat(L, 1);
		//easingStyle = lua_tointeger(L, 2);
		//easingdirection = lua_tointeger(L, 3);
		ReflexEnum::Item* item = NULL;
		auto itemStyle = EnumBridgeItem::GetInstance(L, 2);
		if (MNSandbox::Bridge<ReflexEnum::Item*>::GetValue(L, 2, item))
		{
			easingStyle = item->m_value;
		}
		item = NULL;
		if (MNSandbox::Bridge<ReflexEnum::Item*>::GetValue(L, 3, item))
		{
			easingdirection = item->m_value;
		}
		delaytime = lua_tofloat(L, 4);
		repeatcount = lua_tointeger(L, 5);
		reverses = lua_toboolean(L, 6);

		PushNewObject(L, easingdirection, time, delaytime, repeatcount, easingStyle, reverses);
		return 1;
	}
	void TweenInfoBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "TweenInfo", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int TweenInfoBridge::OnEasingDirection(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, t.m_nEasingDirection);
		return 1;
	}

	int TweenInfoBridge::OnTime(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, t.m_fTime);
		return 1;
	}

	int TweenInfoBridge::OnDelayTime(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, t.m_fDelayTime);
		return 1;
	}

	int TweenInfoBridge::OnRepeatCount(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, t.m_nRepeatCount);
		return 1;
	}

	int TweenInfoBridge::OnEasingStyle(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, t.m_nEasingStyle);
		return 1;
	}

	int TweenInfoBridge::OnReverses(lua_State* L)
	{
		const MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		lua_pushboolean(L, t.m_bReverses);
		return 1;
	}

	int TweenInfoBridge::OnSetEasingDirection(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			t.m_nEasingDirection = lua_tonumber(L, 3);
		return 0;
	}

	int TweenInfoBridge::OnSetTime(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			t.m_fTime = lua_tonumber(L, 3);
		return 0;
	}

	int TweenInfoBridge::OnSetDelayTime(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			t.m_fDelayTime = lua_tonumber(L, 3);
		return 0;
	}

	int TweenInfoBridge::OnSetRepeatCount(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			t.m_nRepeatCount = lua_tonumber(L, 3);
		return 0;
	}

	int TweenInfoBridge::OnSetEasingStyle(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			t.m_nEasingStyle = lua_tonumber(L, 3);
		return 0;
	}

	int TweenInfoBridge::OnSetReverses(lua_State* L)
	{
		MINIW::TweenInfo& t = *TweenInfoBridge::GetObject(L, 1);
		if (lua_isboolean(L, 3))
			t.m_bReverses = lua_toboolean(L, 3);
		return 0;
	}

	template<>
	const std::string Bridge<MINIW::TweenInfo>::ms_ClassName = "Bridge_TweenInfo";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::TweenInfo>::ms_defParams =
	{
		{"EasingDirection", TweenInfoBridge::OnEasingDirection},
		{"Time", TweenInfoBridge::OnTime},
		{"DelayTime", TweenInfoBridge::OnDelayTime},
		{"RepeatCount", TweenInfoBridge::OnRepeatCount},
		{"EasingStyle", TweenInfoBridge::OnEasingStyle},
		{"Reverses", TweenInfoBridge::OnReverses},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::TweenInfo>::ms_defExParams =
	{
		{"easingdirection", TweenInfoBridge::OnEasingDirection},
		{"time", TweenInfoBridge::OnTime},
		{"delaytime", TweenInfoBridge::OnDelayTime},
		{"repeatcount", TweenInfoBridge::OnRepeatCount},
		{"easingstyle", TweenInfoBridge::OnEasingStyle},
		{"reverses", TweenInfoBridge::OnReverses},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::TweenInfo>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::TweenInfo>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<MINIW::TweenInfo>::ms_newIndexAttributes = 
	{
		{"EasingDirection", TweenInfoBridge::OnSetEasingDirection},
		{"Time", TweenInfoBridge::OnSetTime},
		{"DelayTime", TweenInfoBridge::OnSetDelayTime},
		{"RepeatCount", TweenInfoBridge::OnSetRepeatCount},
		{"EasingStyle", TweenInfoBridge::OnSetEasingStyle},
		{"Reverses", TweenInfoBridge::OnSetReverses},
		{"easingdirection", TweenInfoBridge::OnSetEasingDirection},
		{"time", TweenInfoBridge::OnSetTime},
		{"delaytime", TweenInfoBridge::OnSetDelayTime},
		{"repeatcount", TweenInfoBridge::OnSetRepeatCount},
		{"easingstyle", TweenInfoBridge::OnSetEasingStyle},
		{"reverses", TweenInfoBridge::OnSetReverses},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(MINIW::TweenInfo);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<MINIW::TweenInfo>::OnIndex(lua_State* L, MINIW::TweenInfo& obj, const char* key)
	{
		if (strcmp(key, "easingdirection") == 0 || strcmp(key, "EasingDirection") == 0)
		{
			lua_pushnumber(L, obj.m_nEasingDirection);
			return 1;
		}
		else if (strcmp(key, "time") == 0 || strcmp(key, "Time") == 0)
		{
			lua_pushnumber(L, obj.m_fTime);
			return 1;
		}
		else if (strcmp(key, "delaytime") == 0 || strcmp(key, "DelayTime") == 0)
		{
			lua_pushnumber(L, obj.m_fDelayTime);
			return 1;
		}
		else if (strcmp(key, "repeatcount") == 0 || strcmp(key, "RepeatCount") == 0)
		{
			lua_pushnumber(L, obj.m_nRepeatCount);
			return 1;
		}
		else if (strcmp(key, "easingstyle") == 0 || strcmp(key, "EasingStyle") == 0)
		{
			lua_pushnumber(L, obj.m_nEasingStyle);
			return 1;
		}
		else if (strcmp(key, "reverses") == 0 || strcmp(key, "Reverses") == 0)
		{
			lua_pushboolean(L, obj.m_bReverses);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<MINIW::TweenInfo>::OnNewIndex(lua_State* L, MINIW::TweenInfo& obj, const char* key)
	{
		if (strcmp(key, "easingdirection") == 0 || strcmp(key, "EasingDirection") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_nEasingDirection = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "time") == 0 || strcmp(key, "Time") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_fTime = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "delaytime") == 0 || strcmp(key, "DelayTime") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_fDelayTime = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "repeatcount") == 0 || strcmp(key, "RepeatCount") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_nRepeatCount = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "easingstyle") == 0 || strcmp(key, "EasingStyle") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.m_nEasingStyle = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "reverses") == 0 || strcmp(key, "Reverses") == 0)
		{
			if (lua_isboolean(L, 3))
				obj.m_bReverses = lua_toboolean(L, 3);
		}
	}

#endif

	// RangeInfo
	const luaL_Reg MNSandbox::RangeInfoBridge::ms_ClassLibrary[] = {
		{"new", RangeInfoBridge::NewRangeInfo},
		{"New", RangeInfoBridge::NewRangeInfo},
		{nullptr, nullptr}
	};
	int RangeInfoBridge::NewRangeInfo(lua_State* L)
	{
		float min = 0;
		float max = 0;

		min = lua_tofloat(L, 1);
		max = lua_tofloat(L, 2);

		PushNewObject(L, min, max);
		return 1;
	}
	void RangeInfoBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "RangeInfo", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int RangeInfoBridge::OnMin(lua_State* L)
	{
		const RangeInfo& r = *RangeInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, r._min);
		return 1;
	}

	int RangeInfoBridge::OnMax(lua_State* L)
	{
		const RangeInfo& r = *RangeInfoBridge::GetObject(L, 1);
		lua_pushnumber(L, r._max);
		return 1;
	}

	int RangeInfoBridge::OnSetMin(lua_State* L)
	{
		RangeInfo& r = *RangeInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r._min = lua_tonumber(L, 3);
		return 0;
	}

	int RangeInfoBridge::OnSetMax(lua_State* L)
	{
		RangeInfo& r = *RangeInfoBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r._max = lua_tonumber(L, 3);
		return 0;
	}
	template<>
	const std::string Bridge<RangeInfo>::ms_ClassName = "Bridge_RangeInfo";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<RangeInfo>::ms_defParams =
	{
		{"Min", RangeInfoBridge::OnMin},
		{"Max", RangeInfoBridge::OnMax},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<RangeInfo>::ms_defExParams =
	{
		{"min", RangeInfoBridge::OnMin},
		{"max", RangeInfoBridge::OnMax},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<RangeInfo>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<RangeInfo>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<RangeInfo>::ms_newIndexAttributes = 
	{
		{"Min", RangeInfoBridge::OnSetMin},
		{"Max", RangeInfoBridge::OnSetMax},
		{"min", RangeInfoBridge::OnSetMin},
		{"max", RangeInfoBridge::OnSetMax},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(RangeInfo);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<RangeInfo>::OnIndex(lua_State* L, RangeInfo& obj, const char* key)
	{
		if (strcmp(key, "min") == 0 || strcmp(key, "Min") == 0)
		{
			lua_pushnumber(L, obj._min);
			return 1;
		}
		else if (strcmp(key, "max") == 0 || strcmp(key, "Max") == 0)
		{
			lua_pushnumber(L, obj._max);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<RangeInfo>::OnNewIndex(lua_State* L, RangeInfo& obj, const char* key)
	{
		if (strcmp(key, "min") == 0 || strcmp(key, "Min") == 0)
		{
			if (lua_isnumber(L, 3))
				obj._min = lua_tonumber(L, 3);
		}
		else if (strcmp(key, "max") == 0 || strcmp(key, "Max") == 0)
		{
			if (lua_isnumber(L, 3))
				obj._max = lua_tonumber(L, 3);
		}
	}

#endif


	// Matrix3fBridge
	const luaL_Reg Matrix3fBridge::ms_ClassLibrary[] = {
		{"new", Matrix3fBridge::NewMatrix3f},
		{"New", Matrix3fBridge::NewMatrix3f},
		{nullptr, nullptr}
	};

	void Matrix3fBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Matrix3f", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	template<>
	const std::string Bridge<Rainbow::Matrix3x3f>::ms_ClassName = "Bridge_Matrix3x3f";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix3x3f>::ms_defParams = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix3x3f>::ms_defFunctions =
	{
		{"GetElement", Matrix3fBridge::GetElement},
		{"SetElement", Matrix3fBridge::SetElement},
		{"GetColumn", Matrix3fBridge::GetColumn},
		{"SetColumn", Matrix3fBridge::SetColumn},
		{"Transpose", Matrix3fBridge::Transpose},
		{"Invert", Matrix3fBridge::Invert},
		{"MultiplyVector3", Matrix3fBridge::MultiplyVector3},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix3x3f>::ms_newIndexAttributes = {};
	SANDBOX_BRIDGE_DEF_ONINDEX(Rainbow::Matrix3x3f);

#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Matrix3x3f>::OnIndex(lua_State* L, Rainbow::Matrix3x3f& obj, const char* key)
	{
		if (strcmp(key, "GetElement") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::GetElement);
			return 1;
		}
		else if (strcmp(key, "SetElement") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::SetElement);
			return 1;
		}
		else if (strcmp(key, "GetColumn") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::GetColumn);
			return 1;
		}
		else if (strcmp(key, "SetColumn") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::SetColumn);
			return 1;
		}
		else if (strcmp(key, "Transpose") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::Transpose);
			return 1;
		}
		else if (strcmp(key, "Invert") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::Invert);
			return 1;
		}
		else if (strcmp(key, "MultiplyVector3") == 0)
		{
			lua_pushcfunction(L, Matrix3fBridge::MultiplyVector3);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<Rainbow::Matrix3x3f>::OnNewIndex(lua_State* L, Rainbow::Matrix3x3f& obj, const char* key)
	{
		// no new index attributes
	}
#endif


	int Matrix3fBridge::NewMatrix3f(lua_State* L)
	{
		float data[9];
		int count = std::min(9, lua_gettop(L));
		for (int i = 0; i < count; i++)
			data[i] = lua_tonumber(L, i + 1);
		for (int i = count; i < 9; i++)
			data[i] = 0;
		PushNewObject(L, 
			data[0], data[1], data[2],
			data[3], data[4], data[5],
			data[6], data[7], data[8]
		);
		return 1;
	}

	int Matrix3fBridge::GetElement(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 3)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			int column = lua_tonumber(L,3);
			if(row < 0 || row > 2)
			{
				break;
			}
			if(column < 0 || column > 2)
			{
				break;
			}
			lua_pushnumber(L,a->Get(row,column));
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::SetElement(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 4)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			int column = lua_tonumber(L,3);
			if(row < 0 || row > 2)
			{
				break;
			}
			if(column < 0 || column > 2)
			{
				break;
			}
			a->Get(row,column) = lua_tonumber(L,4);
			return 0;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::GetColumn(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 2)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			if(row < 0 || row > 2)
			{
				break;
			}

			Vector3Bridge::PushNewObject(L, a->m_Data33[row][0], a->m_Data33[row][1],a->m_Data33[row][2]);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::SetColumn(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 3)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			if(row < 0 || row > 2)
			{
				break;
			}
			Rainbow::Vector3f* b = Vector3Bridge::GetObject(L, 3);
			if(!b)
			{
				break;
			}
			a->m_Data33[row][0] = b->x;
			a->m_Data33[row][1] = b->y;
			a->m_Data33[row][2] = b->z;
			return 0;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::Transpose(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 1)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			a->Transpose();
			return 0;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::Invert(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 1)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			bool success = a->Invert();
			lua_pushboolean(L,success ? 1 : 0);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix3fBridge::MultiplyVector3(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 2)
			{
				break;
			}
			Rainbow::Matrix3x3f* a = Matrix3fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			Rainbow::Vector3f* b = Vector3Bridge::GetObject(L, 2);
			if(!b)
			{
				break;
			}
			Vector3Bridge::PushVector3(L, a->MultiplyVector3(*b));
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}


	const luaL_Reg Matrix4fBridge::ms_ClassLibrary[] = {
		{"new", Matrix4fBridge::NewMatrix4f},
		{"New", Matrix4fBridge::NewMatrix4f},
		{nullptr, nullptr}
	};

	void Matrix4fBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "Matrix4f", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	template<>
	const std::string Bridge<Rainbow::Matrix4x4f>::ms_ClassName = "Bridge_Matrix4x4f";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix4x4f>::ms_defParams = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix4x4f>::ms_defFunctions =
	{
		{"GetElement", Matrix4fBridge::GetElement},
		{"SetElement", Matrix4fBridge::SetElement},
		{"MultiplyVector3", Matrix4fBridge::MultiplyVector3},
		{"MultiplyVector4", Matrix4fBridge::MultiplyVector4},
		{"MultiplyPoint3", Matrix4fBridge::MultiplyPoint3},
		{"Transpose", Matrix4fBridge::Transpose},
		{"Invert", Matrix4fBridge::Invert},
		{"GetAxisX", Matrix4fBridge::GetAxisX},
		{"GetAxisY", Matrix4fBridge::GetAxisY},
		{"GetAxisZ", Matrix4fBridge::GetAxisZ},
		{"SetAxisX", Matrix4fBridge::SetAxisX},
		{"SetAxisY", Matrix4fBridge::SetAxisY},
		{"SetAxisZ", Matrix4fBridge::SetAxisZ},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<Rainbow::Matrix4x4f>::ms_newIndexAttributes = {};
	SANDBOX_BRIDGE_DEF_ONINDEX(Rainbow::Matrix4x4f);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<Rainbow::Matrix4x4f>::OnIndex(lua_State* L, Rainbow::Matrix4x4f& obj, const char* key)
	{
		if (strcmp(key, "GetElement") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::GetElement);
			return 1;
		}
		else if (strcmp(key, "SetElement") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::SetElement);
			return 1;
		}
		else if (strcmp(key, "MultiplyVector3") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::MultiplyVector3);
			return 1;
		}
		else if (strcmp(key, "MultiplyVector4") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::MultiplyVector4);
			return 1;
		}
		else if (strcmp(key, "MultiplyPoint3") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::MultiplyPoint3);
			return 1;
		}
		else if (strcmp(key, "Transpose") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::Transpose);
			return 1;
		}
		else if (strcmp(key, "Invert") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::Invert);
			return 1;
		}
		else if (strcmp(key, "GetAxisX") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::GetAxisX);
			return 1;
		}
		else if (strcmp(key, "GetAxisY") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::GetAxisY);
			return 1;
		}
		else if (strcmp(key, "GetAxisZ") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::GetAxisZ);
			return 1;
		}
		else if (strcmp(key, "SetAxisX") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::SetAxisX);
			return 1;
		}
		else if (strcmp(key, "SetAxisY") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::SetAxisY);
			return 1;
		}
		else if (strcmp(key, "SetAxisZ") == 0)
		{
			lua_pushcfunction(L, Matrix4fBridge::SetAxisZ);
			return 1;
		}
		return OnIndexError(L, key);
	}

	template<>
	void Bridge<Rainbow::Matrix4x4f>::OnNewIndex(lua_State* L, Rainbow::Matrix4x4f& obj, const char* key)
	{
		// no new index attributes
	}
#endif

	int Matrix4fBridge::NewMatrix4f(lua_State* L)
	{
		float data[16];
		int count = std::min(16, lua_gettop(L));
		for (int i = 0; i < count; i++)
			data[i] = lua_tonumber(L, i + 1);
		for (int i = count; i < 16; i++)
			data[i] = 0;
		PushNewObject(L, 
			data[0], 	data[1], 	data[2], 	data[3],
			data[4], 	data[5], 	data[6], 	data[7],
			data[8], 	data[9], 	data[10], 	data[11],
			data[12], 	data[13], 	data[14], 	data[15]
		);
		return 1;
	}

	int Matrix4fBridge::GetElement(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 3)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			int column = lua_tonumber(L,3);
			if(row < 0 || row > 3)
			{
				break;
			}
			if(column < 0 || column > 3)
			{
				break;
			}
			lua_pushnumber(L,a->Get(row,column));
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::SetElement(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 4)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			int row = lua_tonumber(L,2);
			int column = lua_tonumber(L,3);
			if(row < 0 || row > 2)
			{
				break;
			}
			if(column < 0 || column > 2)
			{
				break;
			}
			a->Get(row,column) = lua_tonumber(L,4);
			return 0;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::MultiplyVector3(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 2)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			Rainbow::Vector3f* b = Vector3Bridge::GetObject(L, 2);
			if(!b)
			{
				break;
			}
			auto ret = a->MultiplyVector3(*b);
			Vector3Bridge::PushNewObject(L, ret);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::MultiplyVector4(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 2)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			Rainbow::Vector4f* b = Vector4Bridge::GetObject(L, 2);
			if(!b)
			{
				break;
			}
			auto ret = a->MultiplyVector4(*b);
			Vector4Bridge::PushNewObject(L, ret);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::MultiplyPoint3(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 2)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			Rainbow::Vector3f* b = Vector3Bridge::GetObject(L, 2);
			if(!b)
			{
				break;
			}
			auto ret = a->MultiplyPoint3(*b);
			Vector3Bridge::PushNewObject(L, ret);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::Transpose(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 1)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			a->Transpose();
			return 0;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

	int Matrix4fBridge::Invert(lua_State* L)
	{
		do{
			int desiredNum = lua_gettop(L);
			if(desiredNum != 1)
			{
				break;
			}
			Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);
			if(!a)
			{
				break;
			}
			auto b = a->GetInvert_General3D();
			Matrix4fBridge::PushMatrix4f(L, b);
			return 1;
		}while(false);
		SANDBOX_ASSERT(false && "invalid params");
		return 0;
	}

#define QUIKC_GET_MATRIX4F_AXIS(L,DIR)							\
do{																\
	int desiredNum = lua_gettop(L);								\
	if(desiredNum != 1)											\
	{															\
		break;													\
	}															\
	Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);	\
	if(!a)														\
	{															\
		break;													\
	}															\
	auto ret = a->GetAxis##DIR();								\
	Vector3Bridge::PushNewObject(L, ret);						\
	return 1;													\
}while(false);													\
return 0;														\




	int Matrix4fBridge::GetAxisX(lua_State* L)
	{
		QUIKC_GET_MATRIX4F_AXIS(L,X)
	}

	int Matrix4fBridge::GetAxisY(lua_State* L)
	{
		QUIKC_GET_MATRIX4F_AXIS(L,Y)
	}

	int Matrix4fBridge::GetAxisZ(lua_State* L)
	{
		QUIKC_GET_MATRIX4F_AXIS(L,Z)
	}

#define QUIKC_SET_MATRIX4F_AXIS(L,DIR)							\
do{																\
	int desiredNum = lua_gettop(L);								\
	if(desiredNum != 2)											\
	{															\
		break;													\
	}															\
	Rainbow::Matrix4x4f* a = Matrix4fBridge::GetObject(L, 1);	\
	if(!a)														\
	{															\
		break;													\
	}															\
	Rainbow::Vector3f* b = Vector3Bridge::GetObject(L, 2);		\
	if(!b)														\
	{															\
		break;													\
	}															\
	a->SetAxis##DIR(*b);										\
	return 0;													\
}while(false);													\
SANDBOX_ASSERT(false && "invalid params");						\
return 0;														\

	int Matrix4fBridge::SetAxisX(lua_State* L)
	{
		QUIKC_SET_MATRIX4F_AXIS(L,X)
	}

	int Matrix4fBridge::SetAxisY(lua_State* L)
	{
		QUIKC_SET_MATRIX4F_AXIS(L,Y)
	}

	int Matrix4fBridge::SetAxisZ(lua_State* L)
	{
		QUIKC_SET_MATRIX4F_AXIS(L,Z)
	}


	// SceneConfig
	const luaL_Reg MNSandbox::SceneConfigBridge::ms_ClassLibrary[] = {
		{"new", SceneConfigBridge::NewSceneConfig},
		{"New", SceneConfigBridge::NewSceneConfig},
		{nullptr, nullptr}
	};
	int SceneConfigBridge::NewSceneConfig(lua_State* L)
	{

		SceneConfig cfg;// (0, "", "");
		cfg.sceneid = lua_tointeger(L, 1);
		cfg.name    = lua_tostring(L, 2);
		//cfg.loading = lua_tostring(L, 3);

		PushSceneConfig(L, cfg);
		return 1;
	}
	void SceneConfigBridge::RegisterClassLibrary(lua_State* L)
	{
		luaL_register(L, "SceneConfig", ms_ClassLibrary);
		lua_pop(L, 1);
	}

	int SceneConfigBridge::OnSceneid(lua_State* L)
	{
		const SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
		lua_pushnumber(L, r.sceneid);
		return 1;
	}

	int SceneConfigBridge::OnName(lua_State* L)
	{
		const SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
		lua_pushstring(L, r.name.c_str());
		return 1;
	}

	//int SceneConfigBridge::OnLoading(lua_State* L)
	//{
	//	const SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
	//	lua_pushstring(L, r.loading.c_str());
	//	return 1;
	//}

	int SceneConfigBridge::OnSetSceneid(lua_State* L)
	{
		SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
		if (lua_isnumber(L, 3))
			r.sceneid = lua_tonumber(L, 3);
		return 0;
	}

	int SceneConfigBridge::OnSetName(lua_State* L)
	{
		SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
		if (lua_isstring(L, 3))
			r.name = lua_tostring(L, 3);
		return 0;
	}

	//int SceneConfigBridge::OnSetLoading(lua_State* L)
	//{
	//	SceneConfig& r = *SceneConfigBridge::GetObject(L, 1);
	//	if (lua_isstring(L, 3))
	//		r.loading = lua_tostring(L, 3);
	//	return 0;
	//}

	template<>
	const std::string Bridge<SceneConfig>::ms_ClassName = "Bridge_SceneConfig";
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<SceneConfig>::ms_defParams =
	{
		{"Sceneid", SceneConfigBridge::OnSceneid},
		{"Name", SceneConfigBridge::OnName},
		//{"Loading", SceneConfigBridge::OnLoading},
	};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<SceneConfig>::ms_defExParams =
	{
		{"sceneid", SceneConfigBridge::OnSceneid},
		{"name", SceneConfigBridge::OnName},
		//{"loading", SceneConfigBridge::OnLoading},
	};

	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<SceneConfig>::ms_defFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<SceneConfig>::ms_defExFunctions = {};
	template<>
	std::unordered_map<std::string, lua_CFunction> Bridge<SceneConfig>::ms_newIndexAttributes = 
	{
		{"Sceneid", SceneConfigBridge::OnSetSceneid},
		{"Name", SceneConfigBridge::OnSetName},
		//{"Loading", SceneConfigBridge::OnSetLoading},
		{"sceneid", SceneConfigBridge::OnSetSceneid},
		{"name", SceneConfigBridge::OnSetName},
		//{"loading", SceneConfigBridge::OnSetLoading},
	};
	SANDBOX_BRIDGE_DEF_ONINDEX_EX(SceneConfig);
#ifdef LUA_BRIDGE_USE_CHAR
	template<>
	int Bridge<SceneConfig>::OnIndex(lua_State* L, SceneConfig& obj, const char* key)
	{
		if (strcmp(key, "sceneid") == 0 || strcmp(key, "Sceneid") == 0)
		{
			lua_pushnumber(L, obj.sceneid);
			return 1;
		}
		else if (strcmp(key, "name") == 0 || strcmp(key, "Name") == 0)
		{
			lua_pushstring(L, obj.name.c_str());
			return 1;
		}
		return OnIndexError(L, key);
	}
	
	template<>
	void Bridge<SceneConfig>::OnNewIndex(lua_State* L, SceneConfig& obj, const char* key)
	{
		if (strcmp(key, "sceneid") == 0 || strcmp(key, "Sceneid") == 0)
		{
			if (lua_isnumber(L, 3))
				obj.sceneid = lua_tonumber(L, 3);
			return;
		}
		else if (strcmp(key, "name") == 0 || strcmp(key, "Name") == 0)
		{
			if (lua_isstring(L, 3))
				obj.name = lua_tostring(L, 3);
			return;
		}
	}
#endif
}
