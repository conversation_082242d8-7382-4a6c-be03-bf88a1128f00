//
// Copyright (c) 2009-2010 <PERSON><PERSON><PERSON><EMAIL>
//
// This software is provided 'as-is', without any express or implied
// warranty.  In no event will the authors be held liable for any damages
// arising from the use of this software.
// Permission is granted to anyone to use this software for any purpose,
// including commercial applications, and to alter it and redistribute it
// freely, subject to the following restrictions:
// 1. The origin of this software must not be misrepresented; you must not
//    claim that you wrote the original software. If you use this software
//    in a product, an acknowledgment in the product documentation would be
//    appreciated but is not required.
// 2. Altered source versions must be plainly marked as such, and must not be
//    misrepresented as being the original software.
// 3. This notice may not be removed or altered from any source distribution.
//



#pragma once

#include "NavMesh/NavMesh.h"

// The units of the parameters are specified in parenthesis as follows:
// (vx) voxels, (wu) world units
struct NavMeshCreateParams
{
    // Navmesh vertices.
    const unsigned short* verts;            // Array of vertices, each vertex has 3 components. (vx).
    int vertCount;                          // Vertex count.
    // Navmesh polygons
    const unsigned short* polys;            // Array of polygons, uses same format as rcPolyMesh.
    const unsigned int* polyFlags;      // Array of flags per polygon.
    const unsigned char* polyAreas;         // Array of area ids per polygon.
    int polyCount;                          // Number of polygons.
    int nvp;                                // Number of verts per polygon.
    // Navmesh Detail (optional)
    const unsigned int* detailMeshes;       // Detail meshes, uses same format as rcPolyMeshDetail.
    const float* detailVerts;               // Detail mesh vertices, uses same format as rcPolyMeshDetail (wu).
    int detailVertsCount;                   // Total number of detail vertices.
    const NavMeshPolyDetailIndex* detailTris;           // Array of detail tris per detail mesh.
    int detailTriCount;                     // Total number of detail triangles.
    unsigned int agentTypeId;               // User ID bound to the tile.
    // Tile location
    int tileX, tileY;                       // Tile location (tile coords).
    Rainbow::Vector3f bmin, bmax;                    // Tile bounds (wu).
    // Settings
    float walkableHeight;                   // Agent height (wu).
    float walkableRadius;                   // Agent radius (wu).
    float walkableClimb;                    // Agent max climb (wu).
    float cs;                               // Cell size (xz) (wu).
    float ch;                               // Cell height (y) (wu).
    bool buildBvTree;                       // Flag indicating if BVTree for polygon query should be build.
};

// Build navmesh data from given input data.
bool CreateNavMeshTileData(const NavMeshCreateParams* params, dynamic_array<unsigned char>* outData);
