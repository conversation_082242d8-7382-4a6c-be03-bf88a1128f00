
#ifndef __CUSTOMMODELDATA_H__
#define __CUSTOMMODELDATA_H__
//#include "Entity/OgreModel.h"
//#include "LegacyOgreColourValue.h"
#include "Common/LegacyMath.h"
#include "CustomAvatarModelData.h"

#define BLOCK_MODEL_SIZE 20
//tolua_begin
//eCmModelType
enum ACTOR_MODEL_TYPE
{
	DEFAUT_MODEL = -1,
	HUMAN_MODEL,			//人型
	QUADRUPED_MODEL,		//四肢生物
	SINGLE_BONE_MODEL,		//单一骨骼生物
};
//tolua_end

//tolua_begin
enum
{
	/**
	@brief	eFcmImportType
	 */
	NEW_NORMALE_FCM_MODEL,
	/**
	@brief	eFcmImportType 示例模型
	 */
	NEW_EXAMPLE_FCM_MODEL,
	/**
	@brief	eFcmImportType 导入其它的自定义模型
	 */
	NEW_COPY_FCM_MODEL,
	/**
	@brief	eFcmImportType 导入微缩组合模型
	 */
	NEW_COPY_PACKING_FCM_MODEL,
	/**
	@brief	eFcmImportType 工具路径
	 */
	EDITOR_FCM_MODEL,
};
//tolua_end

//tolua_begin
struct CustomActorModelData
{
	std::map<std::string, std::vector<CustomAvatarModelData> > models;
	std::string modelmark;
	ACTOR_MODEL_TYPE type;
	bool leaveworlddel;		//退出存档时删除
	std::string modelname;
	bool skindisplay;		//默认子模型是否显示
	short usedownloadcmnum; //使用的下载微缩模型数量
	int authuin;		//作者

	CustomActorModelData() : modelmark(""), type(), leaveworlddel(false), modelname(""), skindisplay(true),
		usedownloadcmnum(0), authuin(0)
	{
	}

	bool isSecondaryCreation()
	{
		return usedownloadcmnum > 0;
	}

	bool isLeaveWorldDel()
	{
		return leaveworlddel;
	}

	int getAuthUin()
	{
		return authuin;
	}
	std::string getQuoteFiles()
	{
		std::string tstr;
		for (auto it = models.begin() ; it != models.end(); it++)
		{
			for (size_t i = 0; i < it->second.size(); i++)
			{
				tstr.append(it->second[i].modelfilename);
				tstr.append(",");
			}
		}
		return tstr;
	}
	/*bool isDownload()
	{
		if (authuin > 0 && g_AccountMgr)
			return authuin != IMiniGameProxy::getMiniGameProxy()->getAccontUin();

		return false;
	}*/
};
//tolua_end


//tolua_begin
struct CustomKeyFrameData
{
	short timeidx;  //关键帧
	Rainbow::Vector3f offserpos;
	Rainbow::Quaternionf quat;
	float scale;
};
//tolua_end
#endif