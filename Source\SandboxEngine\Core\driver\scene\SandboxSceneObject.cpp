/** @file
 * @name  SandboxSceneObject.cpp
 * @parent  GameObject
 * @brief  沙盒节点，场景对象，沙盒结构场景树基础节点
 * <AUTHOR>
 * @date
 * @version
 */


#include "SandboxSceneObject.h"
#include "SandboxListener.h"
#include "SandboxScene.h"
#include "SandboxLua.h"
#include "SandboxSceneObjectBridge.h"
#include "SandboxSceneManager.h"
#include "Common/OgreShared.h"
#include "SandboxSceneSerializeJsonKey.h"
#include "SandboxReflexTypePolicy.h"
#include "SandboxLoadSave.h"
#include "SandboxGameMap.h"
#include "SandboxNodeBoundingBox.h"
#include "SandboxSignal.h"
#include "SandboxGameNode.h"
#include "SandboxServiceNode.h"
#include "Common/GameStatic.h"
#include "base/tick/SandboxNodeTickManager.h"
#include "SandboxSceneChunk.h"
#include "base/stream/SandboxStreamBuffer.h"
#include "base/SandboxCustomRef.h"
#include "base/timer/SandboxTimer.h"
#include "util/thread/SandboxThreadTask.h"
#include "util/thread/SandboxThreadObjectInterface.h"
#include "util/thread/SandboxMainTask.h"
#include "Threads/ThreadChecks.h"
#include "fps/statistics/SandboxStatisticsNode.h"
#include "fps/statistics/SandboxStatistics.h"
#ifdef SANDBOX_USE_SANDBOXNODE_PROFILE
#include "SandboxNodeProfile.h"
#endif // SANDBOX_USE_SANDBOXNODE_PROFILE
#include "SandboxLuaResult.h"
//数据埋点
#include "SandboxReportFunctions.h"

namespace MNSandbox {

	DataContainer<const SandboxNode*, int> SandboxNode::ms_dataTags(0); // tag 数据
	DataContainer<const SandboxNode*, int> SandboxNode::ms_dataOwnerUins(0); // 所属uin
	DataContainer<const SandboxNode*, bool> SandboxNode::ms_dataIgnoreSafeMode(false); //标记不受安全模式控制  过渡阶段(少量)

	IMPLEMENT_REFCLASS_T(SandboxNode);
	//template<> ReflexContainer ReflexContainer::Singleton<SandboxNode>::ms_instance(nullptr, (SandboxNode*)nullptr);
	ReflexContainer SandboxNode::ms_reflexContainer(nullptr, (SandboxNode*)nullptr);
	NodeNotify<NODENOTIFY::ATTRCHANGED, SandboxNode, ReflexValue*, std::vector<ReflexValue*>*, ReflexVariant*>::Register SandboxNode::ms_NotifyRegAttrChanged(&SandboxNode::OnNotifyAttributeChanged);
	NodeNotify<NODENOTIFY::DATASYNC, SandboxNode, const ReflexValue*, bool, bool&>::Register SandboxNode::ms_NotifyRegDataSync(&SandboxNode::OnNotifyDataSync);

	// 配置需要存档的标记
	const FlagsLong SandboxNode::ms_flagsSaveCfg = FlagsLong::Make(FLAGTYPE_ENABLE
		//, FLAGTYPE_ACTIVE
		, FLAGTYPE_ISHIDEONTREE
		//, FLAGTYPE_SERIALIZABLE
		//, FLAGTYPE_SERIALIZABLE_IN_CHUNK
		//, FLAGTYPE_BINDNODEID
		//, FLAGTYPE_ISSERVICE
		//, FLAGTYPE_CANBINDNODEID
		//, FLAGTYPE_ISVISIBLE
		//, FLAGTYPE_LOCKPARENT
		, FLAGTYPE_MANUAL_RESOURCELOAD
		, FLAGTYPE_OPERATION_LOCK
	);

	// 配置需要同步的标记
	const FlagsLong SandboxNode::ms_flagsSyncCfg = FlagsLong::Make(FLAGTYPE_ENABLE
		// , FLAGTYPE_ACTIVE
		, FLAGTYPE_ISHIDEONTREE
		, FLAGTYPE_SERIALIZABLE
		, FLAGTYPE_SERIALIZABLE_IN_CHUNK
		//, FLAGTYPE_BINDNODEID
		, FLAGTYPE_ISSERVICE
		, FLAGTYPE_CANBINDNODEID
		//, FLAGTYPE_ISVISIBLE
		, FLAGTYPE_LOCKPARENT
		, FLAGTYPE_MANUAL_RESOURCELOAD
		, FLAGTYPE_OPERATION_LOCK
	);

	ReflexEnumDesc<SandboxNode::RESOURCE_LOAD_MODE> SandboxNode::R_ResourceLoadModeDesc("ResourceLoadMode", (int)REFLEXTYPEENUM_ENUM_RESOURCE_LOAD_MODE, {
		{RESOURCE_LOAD_MODE::Default, "Default"},
		{RESOURCE_LOAD_MODE::Manual, "Manual"},
		{RESOURCE_LOAD_MODE::Dynamic, "Dynamic"},
		});

	// 定义反射
	// 反射属性
#if defined(IWORLD_DEV_BUILD) || defined(SANDBOX_DEV)
	ReflexClassMember<SandboxNode, SandboxNodeID> SandboxNode::R_ID(0, "ID", "base", &SandboxNode::m_nodeid, ReflexConfig::REG_ONLY_SHOWEX);
#else
	/** @par
	 * @name  ID
	 * @brief  节点ID
	 */
	ReflexClassMember<SandboxNode, SandboxNodeID> SandboxNode::R_ID(0, "ID", "base", &SandboxNode::m_nodeid, ReflexConfig::REG_ONLY_SCRIPT);
#endif // _DEBUG

	/** @par
	 * @name  ClassType
	 * @brief  节点的ClassType名称（不可写）
	 */
	ReflexClassParam<SandboxNode, std::string> SandboxNode::R_ClassType(1, "ClassType", "base", &SandboxNode::GetClassType, ReflexConfig::REG_NO_WRITE);
	/** @par
	 * @name  Name
	 * @brief  节点名
	 */
	ReflexClassParam<SandboxNode, std::string> SandboxNode::R_Name(2, "Name", "base", &SandboxNode::GetName, &SandboxNode::SetName);
	/** @par
	 * @name  Tag
	 * @brief  节点标签
	 */
	ReflexClassParam<SandboxNode, int> SandboxNode::R_Tag(3, "Tag", "base", &SandboxNode::GetTag, &SandboxNode::SetTag);
	/** @par
	 * @name  Parent
	 * @brief  父节点
	 */
	ReflexClassParam<SandboxNode, AutoRef<SandboxNode>> SandboxNode::Parent(4, "Parent", "base", &SandboxNode::GetParentReflex, &SandboxNode::SetParentReflex, ReflexConfig::NO_SAVELOAD);
	/** @par
	 * @name  parent
	 * @brief  父节点（仅脚本可调用）
	 */
	ReflexClassParam<SandboxNode, AutoRef<SandboxNode>> SandboxNode::Parent2(5, "parent", "base", &SandboxNode::GetParentReflex, &SandboxNode::SetParentReflex, ReflexConfig::REG_ONLY_SCRIPT); // 不公开
	/** @par
	 * @name  Children
	 * @brief  全部子节点。（仅脚本可调用）
	 */
	ReflexClassParam<SandboxNode, std::vector<AutoRef<SandboxNode>>> SandboxNode::R_Children(6, "Children", "base", &SandboxNode::GetAllChildren, ReflexConfig::REG_ONLY_SCRIPT);
	/** @par
	 * @name  Enabled
	 * @brief  节点是否被禁用。被禁用后节点内逻辑，事件，通知等不生效。
	 */
	//ReflexClassParam<SandboxNode, bool> SandboxNode::R_Enabled(7, "Enabled", "base", &SandboxNode::EnabledGet, &SandboxNode::EnabledSet);
	ReflexClassParamDefault<SandboxNode, bool> SandboxNode::R_Enabled(7, "Enabled", "base", true, &SandboxNode::EnabledGet, &SandboxNode::EnabledSet);
	/** @par
	 * @name  Attibutes
	 * @brief  获取属性容器。（仅脚本可调用）
	 */
	ReflexClassParam<SandboxNode, AttributeContainer> SandboxNode::UseAttributes(8, "Attibutes", "attribute", &SandboxNode::GetAttributeContainer, ReflexConfig::REG_ONLY_SCRIPT);
	/** @par
	 * @name  Flag
	 * @brief  同步标识符（仅同步可用）
	 */
	ReflexClassParam<SandboxNode, unsigned int> SandboxNode::R_Flag(9, "Flag", "base", &SandboxNode::FlagSyncGet, &SandboxNode::FlagSyncSet, ReflexConfig::REG_ONLY_SYNC
		, ReflexVersion(0, Config::ToVersion(0, 0, 20)));

	static unsigned long long sDefaultFlag2 = FlagsLong::Make(SandboxNode::FLAGTYPE::FLAGTYPE_ENABLE
		, SandboxNode::FLAGTYPE::FLAGTYPE_SERIALIZABLE
		, SandboxNode::FLAGTYPE::FLAGTYPE_CANBINDNODEID).GetData();//, SandboxNode::FLAGTYPE::FLAGTYPE_ISVISIBLE
	ReflexClassParamDefault<SandboxNode, unsigned long long> SandboxNode::R_Flag2(18, "Flag", "base", sDefaultFlag2, &SandboxNode::FlagSyncGet2, &SandboxNode::FlagSyncSet2, ReflexConfig::REG_ONLY_SYNC
		, ReflexVersion(Config::ToVersion(0, 0, 21)));
	/** @par
	 * @name  FlagSave
	 * @brief  保存标识
	 */
	ReflexClassParam<SandboxNode, unsigned int> SandboxNode::R_FlagSave(13, "FlagSave", "base", &SandboxNode::FlagSaveGet, &SandboxNode::FlagSaveSet, ReflexConfig::REG_ONLY_SAVE
		, ReflexVersion(0, Config::ToVersion(0, 0, 20)));
	ReflexClassParam<SandboxNode, unsigned long long> SandboxNode::R_FlagSave2(19, "FlagSave", "base", &SandboxNode::FlagSaveGet2, &SandboxNode::FlagSaveSet2, ReflexConfig::REG_ONLY_SAVE
		, ReflexVersion(Config::ToVersion(0, 0, 21)));
	/** @par
	 * @name  Syncable
	 * @brief  是否可同步
	 * @deprecated discard
	 */
	ReflexClassParam<SandboxNode, bool> SandboxNode::R_Syncable(10, "Syncable", "base", &SandboxNode::IsSyncable, &SandboxNode::SetSyncable, (ReflexConfig::REG_SCRIPT_SAVE | ReflexConfig::USE_ONLYHOST | ReflexConfig::NO_SAVE)); // 弃用
	/** @par
	 * @name  SyncMode
	 * @brief  同步模式（仅主机能够设置）
	 */
	ReflexClassParam<SandboxNode, SYNCMODE> SandboxNode::R_SyncMode(11, "SyncMode", "base", &SandboxNode::GetSyncMode, &SandboxNode::SetSyncMode, ReflexConfig::USE_ONLYHOST); // 仅主机能够设置
	/** @par
	 * @name  LocalSyncFlag
	 * @brief  本地同步标识（本地属性，不需要同步）
	 */
	ReflexClassParam<SandboxNode, SYNCLOCALFLAG> SandboxNode::R_LocalSyncFlag(12, "LocalSyncFlag", "base", &SandboxNode::GetLocalSyncFlag, &SandboxNode::SetLocalSyncFlag, ReflexConfig::NO_SYNC); // 本地属性，不需要同步
	/** @par
	 * @name  ResourceDynamicLoad
	 * @brief  资源动态加载标记 0.0.19版本 去掉这个属性，使用ResourceLoadMode
	 */
	ReflexClassParam<SandboxNode, bool> SandboxNode::R_ResourceDynamicLoad(14, "ResourceDynamicLoad", "base", &SandboxNode::IsDynamicLoadAble, &SandboxNode::LuaSetDynamicLoadAble/*SetDynamicLoadAble*/, ReflexConfig::NO_PUBLIC);
	/** @par
	 * @name  OwnerUin
	 * @brief 所属uin（仅主机能够设置）
	 */
	ReflexClassParam<SandboxNode, int> SandboxNode::R_SyncOwnerUin(15, "OwnerUin", "base", &SandboxNode::GetOwnerUin, &SandboxNode::SetOwnerUin, ReflexConfig::USE_ONLYHOST | ReflexConfig::NO_PUBLIC);  // 仅主机能够设置，不在Studio中展示
	/** @par
	 * @name  IgnoreSafeMode
	 * @brief 忽略安全模式
	 */
	ReflexClassParam<SandboxNode, bool> SandboxNode::R_IgnoreSafeMode(16, "IgnoreSafeMode", "base", &SandboxNode::IsIgnoreSafeMode, &SandboxNode::SetIgnoreSafeMode);
	/** @event
	 * @name  ResourceLoadMode
	 * @brief 资源加载模式 Manual:主动加载 Dynamic:动态加载
	 */
	ReflexClassParam<SandboxNode, SandboxNode::RESOURCE_LOAD_MODE> SandboxNode::R_ResourceLoadMode(17, "ResourceLoadMode", "base", &SandboxNode::GetResourceLoadMode, &SandboxNode::SetResourceLoadMode, ReflexConfig::NO_SYNC);

#if defined(IWORLD_DEV_BUILD) || defined(SANDBOX_DEV)
	ReflexClassParam<SandboxNode, unsigned long long> SandboxNode::R_debug_Flag(100, "FlagDebug", "debug", &SandboxNode::FlagGet, ReflexConfig::REG_ONLY_SHOWEX);
#endif
	// 反射函数
	/** @fn
	 * @name  Clone
	 * @brief  节点克隆，克隆反射属性，自定义属性，以及包含的子对象
	 * @return  克隆得到的节点
	 */
	ReflexClassMethodRet<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_Clone("Clone", "base", &SandboxNode::Clone);
	/** @fn
	 * @name  FindFirstChild
	 * @brief  通过节点名找到节点对象
	 * @param  name  < 节点名
	 * @return  节点对象
	 */
	ReflexClassMethodRet<SandboxNode, AutoRef<SandboxNode>, std::string> SandboxNode::R_FindFirstChild("FindFirstChild", "base", &SandboxNode::GetChildByName);
	/** @fn
	 * @name  Destroy
	 * @brief  销毁节点
	 */
	ReflexClassMethod<SandboxNode> SandboxNode::R_Destroy("Destroy", "base", &SandboxNode::ReflexDestroy);
	/** @fn
	 * @name  ClearAllChildren
	 * @brief  清除所有子节点
	 */
	ReflexClassMethod<SandboxNode> SandboxNode::R_ClearAllChild("ClearAllChildren", "base", &SandboxNode::ClearAllChildren);
	/** @fn
	 * @name  SetParent
	 * @brief  设置父节点
	 * @param  parent  < 父节点
	 */
	ReflexClassMethod<SandboxNode, AutoRef<SandboxNode>> SandboxNode::UseSetParent("SetParent", "base", &SandboxNode::SetParent);
	/** @fn
	 * @name  GetNodeid
	 * @brief  获取节点id
	 * @return  节点id
	 */
	ReflexClassMethodRet<SandboxNode, SandboxNodeID> SandboxNode::R_GetNodeid("GetNodeid", "base", &SandboxNode::GetNodeid);
	/** @fn
	 * @name  GetAttribute
	 * @brief  获取attr的反射属性
	 * @param  attr  < 属性key字符串
	 * @return  反射属性
	 */
	ReflexClassMethodRet<SandboxNode, ReflexVariant, std::string> SandboxNode::UseGetAttribute("GetAttribute", "attribute", &SandboxNode::GetAttributeValue);
	/** @fn
	 * @name  SetAttribute
	 * @brief  设置反射的属性值
	 * @param  attr  < 属性key字符串
	 * @param  value  < 反射属性value值
	 * @return  返回true，表示设置成功
	 */
	ReflexClassMethodRet<SandboxNode, bool, std::string, ReflexVariant> SandboxNode::UseSetAttribute("SetAttribute", "attribute", &SandboxNode::SetAttributeValue);
	/** @fn
	 * @name  AddAttribute
	 * @brief  添加一条反射属性
	 * @param  attr  < 属性key字符串
	 * @param  type  < 属性key对应的属性值类型
	 */
	ReflexClassMethod<SandboxNode, std::string, Attribute::TYPE> SandboxNode::R_AddAtrribute("AddAttribute", "attribute", &SandboxNode::AddAttribute);
	/** @fn
	 * @name  DeleteAttribute
	 * @brief  通过attr名删除一条反射属性
	 * @param  attr  < 属性key字符串
	 */
	ReflexClassMethod<SandboxNode, std::string> SandboxNode::R_DeleteAtrribute("DeleteAttribute", "attribute", &SandboxNode::DeleteAttribute);
	/** @fn
	 * @name  IsA
	 * @brief  判断节点的ClassType是不是属于value代表的ClassType
	 * @param  value  < ClassType字符串
	 * @return  返回true，表示节点ClassType属于value代表的ClassType
	 */
	ReflexClassMethodRet<SandboxNode, bool, std::string> SandboxNode::R_IsA("IsA", "base", &SandboxNode::IsA);
	/** @fn
	 * @name  SetReflexSyncMode
	 * @brief  设置反射同步模式（仅主机能够设置）
	 * @param  rvname  < 反射名
	 * @param  mode  < 同步模式
	 */
	ReflexClassMethod<SandboxNode, std::string, SYNCMODE> SandboxNode::R_SetReflexSyncMode("SetReflexSyncMode", "method", &SandboxNode::SetReflexSyncMode, ReflexConfig::USE_ONLYHOST); // 仅主机能够设置
	/** @fn
	 * @name  GetReflexSyncMode
	 * @brief  获取反射同步模式
	 * @param  rvname  < 反射名
	 * @return  反射同步模式
	 */
	ReflexClassMethodRet<SandboxNode, SYNCMODE, std::string> SandboxNode::R_GetReflexSyncMode("GetReflexSyncMode", "method", &SandboxNode::GetReflexSyncMode);
	/** @fn
	 * @name  SetReflexLocalSyncFlag
	 * @brief  设置反射本地同步标记
	 * @param  rvname  < 反射名
	 * @param  flag  < 反射同步标记
	 */
	ReflexClassMethod<SandboxNode, std::string, SYNCLOCALFLAG> SandboxNode::R_SetReflexLocalSyncFlag("SetReflexLocalSyncFlag", "method", &SandboxNode::SetReflexLocalSyncFlag);
	/** @fn
	 * @name  GetReflexLocalSyncFlag
	 * @brief  获取反射本地同步标记
	 * @param  rvname  < 反射名
	 * @return  反射本地同步标记
	 */
	ReflexClassMethodRet<SandboxNode, SYNCLOCALFLAG, std::string> SandboxNode::R_GetReflexLocalSyncFlag("GetReflexLocalSyncFlag", "method", &SandboxNode::GetReflexLocalSyncFlag);

	// 反射事件
	/** 规范命名-非规范旧命名-保留 **/
	/** @event
	 * @name  NotifyAncestryChanged
	 * @brief  祖先节点变化时，会触发一个NotifyAncestryChanged通知
	 * @param  ancestry < 祖先节点
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_AncestryChanged("NotifyAncestryChanged", "notify", &SandboxNode::m_notifyAncestryChanged);      // 祖先节点变化
	/** @event
	 * @name  NotifyParentChanged
	 * @brief  父节点(或父级节点)变化时，会触发一个NotifyParentChanged通知
	 * @param  parent < 父节点
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ParentChanged("NotifyParentChanged", "notify", &SandboxNode::m_notifyParentChanged);        // 父节点变化
	/** @event
	 * @name  NotifyAttributeChanged
	 * @brief  属性发生变化时，会触发一个NotifyAttributeChanged通知
	 * @param  attr < 属性名
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, std::string> SandboxNode::R_AttributeChanged("NotifyAttributeChanged", "notify", &SandboxNode::m_notifyAttributeChangedKey);  // 用户属性变化
	/** @event
	 * @name  NotifyChildAdded
	 * @brief  新增子节点时，会触发一个NotifyChildAdded通知
	 * @param  child < 子节点
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ChildAdded("NotifyChildAdded", "notify", &SandboxNode::m_notifyChildAdded);           // 新增子节点
	/** @event
	 * @name  NotifyChildRemoved
	 * @brief  移除子节点时，会触发一个NotifyChildRemoved通知
	 * @param  child < 子节点
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ChildRemoved("NotifyChildRemoved", "notify", &SandboxNode::m_notifyChildRemoved);         // 移除子节点
	/** @event
	 * @name  NotifyCustomAttrChanged
	 * @brief  自定义属性发生变化，会触发一个NotifyCustomAttrChanged通知
	 * @param  attr < 属性名
	 * @deprecated discard
	 */
	ReflexClassNotify<SandboxNode, std::string> SandboxNode::R_CustomAttrChanged("NotifyCustomAttrChanged", "notify", &SandboxNode::GetNotifyAttributeChanged);    // 自定义属性变化
	/** 规范命名-规范命名-----新增 **/
	/** @event
	 * @name  AncestryChanged
	 * @brief  祖先节点变化时，会触发一个AncestryChanged通知
	 * @param  ancestry < 祖先节点
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_AncestryChanged_s("AncestryChanged", "notify", &SandboxNode::m_notifyAncestryChanged);       // 祖先节点变化
	/** @event
	 * @name  ParentChanged
	 * @brief  父节点(或父级节点)变化时，会触发一个ParentChanged通知
	 * @param  parent < 父节点
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ParentChanged_s("ParentChanged", "notify", &SandboxNode::m_notifyParentChanged);         // 父节点变化
	/** @event
	 * @name  AttributeChanged
	 * @brief  属性发生变化时，会触发一个AttributeChanged通知
	 * @param  attr < 属性名
	 */
	ReflexClassNotify<SandboxNode, std::string> SandboxNode::R_AttributeChanged_s("AttributeChanged", "notify", &SandboxNode::m_notifyAttributeChangedKey);   // 用户属性变化
	/** @event
	 * @name  ChildAdded
	 * @brief  新增子节点时，会触发一个ChildAdded通知
	 * @param  child < 子节点
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ChildAdded_s("ChildAdded", "notify", &SandboxNode::m_notifyChildAdded);            // 新增子节点
	/** @event
	 * @name  ChildRemoved
	 * @brief  移除子节点时，会触发一个ChildRemoved通知
	 * @param  child < 子节点
	 */
	ReflexClassNotify<SandboxNode, AutoRef<SandboxNode>> SandboxNode::R_ChildRemoved_s("ChildRemoved", "notify", &SandboxNode::m_notifyChildRemoved);          // 移除子节点
	/** @event
	 * @name  CustomAttrChanged
	 * @brief  自定义属性发生变化，会触发一个CustomAttrChanged通知
	 * @param  attr < 属性名
	 */
	ReflexClassNotify<SandboxNode, std::string> SandboxNode::R_CustomAttrChanged_s("CustomAttrChanged", "notify", &SandboxNode::GetNotifyAttributeChanged);     // 自定义属性变化
	/** @fn
	 * @name  ManualLoad
	 * @brief  主动加载 同步
	 */
	ReflexClassMethod<SandboxNode> SandboxNode::R_FuncManualLoad("ManualLoad", "method", &SandboxNode::LuaManualLoad);
	/** @fn
	 * @name  ManualLoadAsync 异步
	 * @brief  主动加载
	 */
	ReflexClassMethod<SandboxNode, AutoRef<LuaFunction>> SandboxNode::R_FuncManualLoadAsync("ManualLoadAsync", "method", &SandboxNode::LuaManualLoadAsync);
	/** @fn
	 * @name  ManualUnLoad
	 * @brief  主动卸载
	 */
	ReflexClassMethod<SandboxNode> SandboxNode::R_FuncManualUnLoad("ManualUnLoad", "method", &SandboxNode::LuaManualUnLoad);

	bool SandboxNode::StreamEnableGameSet = false;

	SandboxNode::SandboxNode()
		: m_syncMode(this)
		, m_pBindedSceneChunk(nullptr)
		, m_attrContainer(this)
		//, m_bDynamicStopTemp(false)
		//, m_SendSyncALl(true)
	{
		SANDBOXPROFILE_PUSHDATA(this, SANDBOXNODE, GetRTTI()->GetType());
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Add(Statistics::INSCOUNT_TYPE::NODE);

		// 数据埋点 节点数量
		MNSandbox::MiniReport::SetStudioReportData(MNSandbox::MiniReport::ReportDataType::StudioNodeNum, 1);

		m_nodePtr = SANDBOX_NEW(ListNodeRef<SandboxNode>, this);
		m_childrenFast = NULL;
		// 默认状态
		m_flags = FlagsLong::Make(FLAGTYPE_ENABLE
			, FLAGTYPE_ACTIVE
			, FLAGTYPE_SERIALIZABLE //默认可以序列化
			, FLAGTYPE_CANBINDNODEID // 默认可以绑定
			, FLAGTYPE_ISVISIBLE // 默认显示
			, FLAGTYPE_CANPUBLIC_DELETE
			, FLAGTYPE_CANPUBLIC_CLONE
			, FLAGTYPE_SENDSYNCALL
		);
		m_flags.GetCallbackObject().SetCallback(this, &SandboxNode::OnFlagsChanged); // 设置修改回调

	}

	SandboxNode::~SandboxNode()
	{
		UnregisterTick();

		SANDBOX_RELEASE(m_boundingBox);

		m_parent = NULL;
		//m_children.for_each([](SandboxNode* child) {
		//	child->SetParent(nullptr);
		//});
		m_children.clear(); // 释放前需要先把子节点全部清理掉
		SANDBOX_DELETE(m_nodePtr);

		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Remove(Statistics::INSCOUNT_TYPE::NODE);

		// 数据埋点 节点数量
		MNSandbox::MiniReport::SetStudioReportData(MNSandbox::MiniReport::ReportDataType::StudioNodeNum, -1);

		SANDBOXPROFILE_POPDATA(this);

		if (m_childrenFast)
		{
			delete m_childrenFast;
			m_childrenFast = NULL;
		}
	}

	void SandboxNode::InitBindChunks(bool recur)
	{
		if (IsStreamLoad()) {
			updateBindChunk();
			return;
		}

		if (recur) {
			m_children.for_each([](SandboxNode* target) -> void {
				target->InitBindChunks();
				});
		}
	}

	int SandboxNode::GetOwnerUin() const
	{
		return ms_dataOwnerUins.GetData(this);
	}

	void SandboxNode::SetOwnerUin(const int& value)
	{
		if (ms_dataOwnerUins.SetData(this, value))
		{
			OnAttributeChanged(this, &R_SyncOwnerUin);
		}
	}

	void SandboxNode::SetOwnerUinRecursive(int value)
	{
		SetOwnerUin(value);

		if (m_children.size() > 0) {
			m_children.for_each([value](SandboxNode* target) -> void {
				target->SetOwnerUinRecursive(value);
				});
		}
	}

	bool SandboxNode::IsIgnoreSafeMode()const
	{
		return ms_dataIgnoreSafeMode.GetData(this);
	}

	void SandboxNode::SetIgnoreSafeMode(const bool& value)
	{
		if (ms_dataIgnoreSafeMode.SetData(this, value))
		{
			OnAttributeChanged(this, &R_IgnoreSafeMode);
		}
	}

	void SandboxNode::HandleInit()
	{
		// 节点类型id 统计计数
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::NodeCount::GetInstance().AddByRTTIid(GetRTTI());

#if 1 //没必要这么早设置id //屏蔽---(id增长过快)
		// 设置默认ID
		if (m_nodeid == 0)
		{
			if (auto sceneMgr = GetSceneManagerPtr()) // 如果地图已经加载了，使用地图ID
				m_nodeid = sceneMgr->GetNewSceneObjid();
		}
#endif
		if (IsDefaultResourceLoad())
		{
			OnInitResouceBody();
		}

		GetStaticNotifyCreate().Emit(this);

		// handle init
		OnHandleInit();
	}

	void SandboxNode::InitBoundingBox()
	{
		// 创建包围盒
		m_boundingBox = SANDBOX_NEW(NodeBoundingBox, this);
	}

	void SandboxNode::SetName(std::string name)
	{
		if (name == m_name)
			return;

		m_name.swap(name); // 交换
		OnNameChanged(name, m_name);

		GetStaticNotifyNameChanged().Emit(this, name, m_name);

		// 改名时检测父节点的监听
		if (m_parent)
			m_parent->OnChildChangeName(this, name);

		OnAttributeChanged(this, &R_Name);
	}

	void SandboxNode::SetTag(int tag)
	{
		if (ms_dataTags.SetData(this, tag))
		{
			OnAttributeChanged(this, &R_Tag);
		}
	}

	int SandboxNode::GetTag() const
	{
		return ms_dataTags.GetData(this);
	}

	AutoRef<SandboxNode> SandboxNode::CreateInstance(const std::string& objtype)
	{
		auto factory = SandboxCoreFactorys::GetInstance().GetFactoryT<MNSandbox::SandboxNode>();
		return factory->ProduceSafe(objtype);
	}

	bool SandboxNode::IsAncestorOf(const SandboxNode* node) const
	{
		if (!node)
			return false;

		const SandboxNode* parent = node;
		while (parent = parent->GetParent())
		{
			if (parent == this)
				return true;
		}
		return false;
	}

	bool SandboxNode::IsDescendantOf(const SandboxNode* node) const
	{
		if (!node)
			return false;

		const SandboxNode* parent = this;
		while (parent = parent->GetParent())
		{
			if (parent == node)
				return true;
		}
		return false;
	}

	bool SandboxNode::IsDescendantOf(const std::string& classname)
	{
		const RuntimeClass* cls = RuntimeClass::Container::Get().FindRTTI(classname);
		if (cls == nullptr)
			return false;

		const SandboxNode* parent = this;
		while (parent)
		{
			if (parent->GetRTTI() == cls)
				return true;

			parent = parent->GetParent();
		}
		return false;
	}

	/* 同过遍历节点的父节点是否有支持这个脚本作用域执行的，来判断脚本是否能运行*/
	bool SandboxNode::isScriptScopeRun(ScriptScope scope) const
	{
		const SandboxNode* parent = this;
		while (parent = parent->GetParent())
		{
			if (parent->isSupportScriptScope(scope))
				return true;
		}

		return false;
	}

	void SandboxNode::SetParent(const AutoRef<SandboxNode>& parent)
	{
		if (IsParentLocked())
		{
			SANDBOX_LOG("node's parent attribute is locked! node = ", this);
			return;
		}

		SetParent(parent.get());
	}

	bool SandboxNode::CheckParentValid(SandboxNode* parent)
	{
		if (m_parent == parent || parent == this)
			return false;

		// 新parent 能够挂载当前节点
		if (parent)
		{
			SANDBOX_ASSERT(parent->GetRefCount() > 0);

			// 父节点不能已经是子节点
			if (parent->IsDescendantOf(this))
				return false;

			// 是否被限制
			if (!parent->CanAttachChild(this))
				return false;
		}
		return true;
	}

	bool SandboxNode::SetParent(SandboxNode* parent)
	{
		// 限制挂载子节点
		if (!CheckParentValid(parent))
			return false;

		AutoRef<SandboxNode> myself(this);
		SandboxNode* oldparent = m_parent;
		if (!SetParentCommon(oldparent, parent))
			return false;

		OnParentChangeAfter(oldparent); // 在parent 修改后的处理任务
		return true;
	}

	bool SandboxNode::SetParentCommon(SandboxNode* srcparent, SandboxNode* parent)
	{
		// 打印lua 调用堆栈，如果需要可以本地开打
		//if (auto L = GetSandboxScriptVM().GetTopLuaState())
		//	SANDBOX_LOG("xxx lua set node parent xxx -> ", ScriptState::GetLuaTraceBack(L));

		Scene* srcScene = GetScene();
		Scene* dstScene = parent ? parent->GetScene() : nullptr;
		ServiceNode* srcService = GetService();
		ServiceNode* dstService = parent ? parent->GetService() : nullptr;

		// game 根节点
		GameNode* game = GetCurrentGameRoot();
		bool srcIsGameDescendant = false, dstIsGameDescendant = false;
		if (game)
		{
			srcIsGameDescendant = game ? IsDescendantOf(game) : false;
			dstIsGameDescendant = (parent && game) ? (parent == game || parent->IsDescendantOf(game)) : false; // parent 可能就是 gameroot
		}

		if (srcparent) // 离开父节点
		{
			LeaveParent(srcparent
				, (srcScene != dstScene) ? srcScene : nullptr
				, (srcIsGameDescendant != dstIsGameDescendant) ? srcIsGameDescendant : false
				, (srcService != dstService) ? srcService : nullptr
			);
		}

		m_parent = parent;

		OnParentChanged(m_parent);
		if (srcService != dstService)
			OnAncestryChanged(srcService, dstService);

		// 绑定ID
		UpdateBindNodeID(parent);

		if (parent) // 进入父节点
		{
			EnterParent(parent
				, (srcScene != dstScene) ? dstScene : nullptr
				, (srcIsGameDescendant != dstIsGameDescendant) ? dstIsGameDescendant : false
				, (srcService != dstService) ? dstService : nullptr
			);
		}
		return true;
	}

	void SandboxNode::EnterParent(SandboxNode* parent, Scene* scene, bool isGameDescendant, ServiceNode* service)
	{
		UseDefaultName(); // 如果没有名称，使用默认名称

		SANDBOX_ASSERT(parent);
		parent->GetChildrenList().push_back(m_nodePtr);
		parent->OnAddChild(this);
		OnEnterParent(parent);

		// 通知根节点
		if (isGameDescendant)
		{
			NodeEnterGame();
		}
		// 进入场景
		if (scene)
		{
			NodeEnterScene(scene);
		}
		// 通知根节点有节点进入
		if (service)
		{
			service->StaticToCast<SandboxNode>()->DescendantNodeEnter(this);
			TryRegisterTick();
		}

		// 父节点正在运行，需要开始运行
		if (parent->IsPlaying())
		{
			BeginPlay();
		}

		// 更新状态
		if (bool active = parent->IsActive())
			OnActiveChanged(active && m_flags.CheckFlag(FLAGTYPE_ACTIVE));
	}

	void SandboxNode::LeaveParent(SandboxNode* parent, Scene* scene, bool isGameDescendant, ServiceNode* service)
	{
		SANDBOX_ASSERT(parent);
		if (isGameDescendant) // 离开game
		{
			// 更新状态
			OnActiveChanged(false);

			// 如果正在运行，离开父节点时，需要结束运行
			if (IsPlaying())
			{
				EndPlay();
			}
		}

		OnLeaveParent(parent);
		parent->OnRemoveChild(this);
		if (parent->GetChildrenFast_())
		{
			auto iter = parent->GetChildrenFast_()->find(GetName().c_str());
			if (iter != parent->GetChildrenFast_()->end())
			parent->GetChildrenFast_()->erase(iter);
		}
		parent->GetChildrenList().erase(m_nodePtr);
		m_parent = nullptr;

		// 通知根节点有节点离开
		if (service)
		{
			service->StaticToCast<SandboxNode>()->DescendantNodeLeave(this);
			UnregisterTick();
		}
		// 离开场景
		if (scene)
		{
			NodeLeaveScene(scene);
		}
		// 离开game 根节点
		if (isGameDescendant)
		{
			NodeLeaveGame();
		}
	}

	void SandboxNode::OnParentChangeAfter(SandboxNode* srcparent)
	{
		OnAttributeChanged(this, &Parent);
		if (m_flags.CheckFlag(FLAGTYPE_SYNC_ONLYPARENT))
			GetStaticNotifyParentChangedEx().Emit(this, srcparent, m_parent);
		else
			GetStaticNotifyParentChanged().Emit(this, srcparent, m_parent);

#ifdef SANDBOX_CHUNK_STREAM_LOAD
		if (SandboxNode::StreamEnableGameSet) {
			bool canbind = isChildCanBindChunk(m_parent);
			recursiveCheckBindChunk(canbind);
		}
#endif

		// 刷新动态加载标记
		UpdateStopDynamicFlag();
	}

	void SandboxNode::NodeLeaveGame()
	{
		// 子节点处理
		if (GetChildrenCount() > 0)
		{
			GetChildrenList().for_each([this](SandboxNode* child) -> void {
				child->NodeLeaveGame();
				});
		}

		OnNodeLeaveGame();
	}

	void SandboxNode::NodeEnterGame()
	{
		OnNodeEnterGame();

		// 子节点处理
		if (GetChildrenCount() > 0)
		{
			GetChildrenList().for_each([this](SandboxNode* child) -> void {
				child->NodeEnterGame();
				});
		}
	}

	void SandboxNode::OnNodeLeaveGame()
	{
		auto game = GetCurrentGameRoot();
		if (!game) { SANDBOX_ASSERT(false); return; }
		game.StaticToCast<SandboxNode>()->DescendantNodeLeave(this, false);
		GetStaticNotifyLeaveGame().Emit(this);
	}

	void SandboxNode::OnNodeEnterGame()
	{
		auto game = GetCurrentGameRoot();
		if (!game) { SANDBOX_ASSERT(false); return; }
		GetStaticNotifyEnterGame().Emit(this);
		game.StaticToCast<SandboxNode>()->DescendantNodeEnter(this, false);
	}

	void SandboxNode::recursiveCheckBindChunk(bool canbind)
	{
		//if (!target) return;
		if (IsStreamLoad()) {
			if (m_parent != nullptr && canbind) {
				updateBindChunk();
			}
			else {
				unBindChunk();
			}
			return;
		}

		m_children.for_each([canbind](SandboxNode* node) -> void {
			if (!node) return;
			node->recursiveCheckBindChunk(canbind);
			});

	}

	void SandboxNode::UpdateBindNodeID(SandboxNode* parent)
	{
		// 如果已经绑定了ID，从父节点离开，需要取消绑定
		// 如果没有绑定ID，进入父节点，需要绑定
		if (m_flags.CheckFlag(FLAGTYPE_BINDNODEID))
		{
			if (!parent || !parent->m_flags.CheckFlag(FLAGTYPE_BINDNODEID))
			{
				UnbindNodeID();
			}
		}
		else
		{
			if (parent && parent->m_flags.CheckFlag(FLAGTYPE_BINDNODEID))
				BindNodeID();
		}
	}

	void SandboxNode::BindNodeID()
	{
		if (m_flags.CheckFlag(FLAGTYPE_CANBINDNODEID) // 需要判断是否能够绑定节点ID
			&& !m_flags.CheckFlag(FLAGTYPE_BINDNODEID))
		{
			m_flags.SetFlag(FLAGTYPE_BINDNODEID, true);
			if (m_nodeid == 0)
			{
				SetNodeid(GetSceneManager().GetNewSceneObjid()); // 设置ID
			}
			GetSceneManager().BindNodeID(this); // 绑定到全局
		}

		// 递归子节点
		m_children.for_each([](SandboxNode* node) -> void {
			node->BindNodeID();
			});
	}
	void SandboxNode::UnbindNodeID()
	{
		if (m_flags.CheckFlag(FLAGTYPE_BINDNODEID))
		{
			m_flags.SetFlag(FLAGTYPE_BINDNODEID, false);

			if (GetSceneManagerPtr())
				GetSceneManagerPtr()->UnbindNodeID(this); // 绑定到全局
		}

		// 递归子节点
		m_children.for_each([](SandboxNode* node) -> void {
			node->UnbindNodeID();
			});
	}

	void SandboxNode::SetSceneChunk(SceneChunk* p)
	{
		m_pBindedSceneChunk = p;
	}

	SceneChunk* SandboxNode::GetSceneChunk() const
	{
		return m_pBindedSceneChunk;
	}

	core::hash_map<core::string, SandboxNode*>* SandboxNode::GetChildrenFast()
	{
		if (!m_childrenFast)
			m_childrenFast = new core::hash_map<core::string, SandboxNode*>;
		return m_childrenFast;
	}

	AutoRef<SandboxNode> SandboxNode::GetChildByName(const std::string& name) const
	{
		if (m_childrenFast)
		{
			auto iter = m_childrenFast->find(name.c_str());
			if (iter != m_childrenFast->end())
			{
				return iter->second;
			}
		}
		auto listNode = m_children.find([&name](SandboxNode* node) -> bool {
			return node->GetName() == name;
			});
		if (listNode)
		{
			auto me = const_cast<SandboxNode*>(this);
			(*me->GetChildrenFast())[listNode->Get()->GetName().c_str()] = listNode->Get();
			return listNode->Get();
		}
		else
		{
			return nullptr;
		}
	}

	AutoRef<SandboxNode> SandboxNode::GetChildByID(long long id) const
	{
		auto listNode = m_children.find([&id](SandboxNode* node) -> bool {
			return node->GetNodeid() == id;
			});
		return listNode ? listNode->Get() : nullptr;
	}

	std::vector<AutoRef<SandboxNode>> SandboxNode::GetAllChildren() const
	{
		std::vector<AutoRef<SandboxNode>> children(m_children.size());
		int idx = 0;
		m_children.for_each([&children, &idx](SandboxNode* node) -> void {
			children[idx++] = node;
			});
		return children;
	}

	unsigned SandboxNode::GetDescendantCount() const
	{
		unsigned cnt = GetChildrenCount();
		m_children.for_each([&cnt](SandboxNode* node) -> void {
			cnt += node->GetDescendantCount();
			});
		return cnt;
	}

	void SandboxNode::ClearAllChildren()
	{
		m_children.for_each([](SandboxNode* node) -> void {
			AutoRef<SandboxNode> child(node);
			node->SetParent(nullptr);
			});
		if (m_childrenFast)
		{
			m_childrenFast->clear();
		}
	}

	void SandboxNode::ReflexDestroy()
	{
		if (IsParentLocked())
			return;

		Destroy();
	}

	void SandboxNode::Destroy()
	{
		AutoRef<SandboxNode> self = this; // 不被释放
		m_children.for_each([](SandboxNode* node) -> void {
			node->Destroy();
			});
		this->SetParent(nullptr);
		this->Release();
	}

	bool SandboxNode::isChildCanBindChunk(SandboxNode* target)
	{
		if (target == nullptr) return false;

		auto root = target->GetRoot();
		if (root && root->IsKindOf<SceneRoot>())//场景树上的修改 才有效	 
		{
			////祖先节点已经有streamload开启
			SandboxNode* p = target;// ->GetParent();
			while (p && p != root)
			{
				if (p->IsStreamLoad())
				{
					return false;
				}
				p = p->GetParent();
			}
			return true;
		}
		return false;
	}

	void SandboxNode::unBindChunk()
	{
		if (m_pBindedSceneChunk) {
			m_pBindedSceneChunk->OnRemoveObject(this);
		}
		m_pBindedSceneChunk = nullptr;
	}

	// 主线程任务：删除节点对象
	class MainTask_DeleteNode : public Thread::MainThreadTaskObject<SandboxNode*>
	{
		using Super = ThisType;
	public:
		virtual void OnDoTask() override
		{
			m_data->DeleteSelf();
		}
	};

	void SandboxNode::ReleaseSelf()
	{
		if (Rainbow::CurrentThreadIsMainThread())
		{
			DeleteSelf();
		}
		else if (Thread::ThreadTaskManager::GetSingletonPtr())
		{
			//Rainbow::GetMainThreadJob().ScheduleMainThreadJobFunc<std::function<void()>>([this]() {
			//	this->DeleteSelf();
			//});
			auto& mainTaskGroup = Thread::ThreadTaskManager::GetSingletonPtr()->GetMainTaskGroup();
			mainTaskGroup.CreateMainTask<MainTask_DeleteNode>().second->Data() = this;
		}
		else
		{
			SANDBOX_WARNING(false);
			//DeleteSelf();
		}
	}

	void SandboxNode::OnClearNotify()
	{
		m_notifyChildAdded.Clear();
		m_notifyChildRemoved.Clear();
		m_notifyParentChanged.Clear();
		m_notifyAncestryChanged.Clear();
		m_notifyEnterScene.Clear();
		m_notifyLeaveScene.Clear();
		m_notifyAttributeChanged.Clear();
		m_notifyAttributeChangedEx.Clear();
		m_notifyAttributeChangedKey.Clear();
#ifdef BUILD_MINI_EDITOR_APP // 目前只有工具会使用
		m_notifyNodeChanged.Clear();
#endif
		m_notifyChildEnterByName.Clear();
		Super::OnClearNotify();
	}

	void SandboxNode::DeleteSelf()
	{
		// 节点类型id 统计计数
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::NodeCount::GetInstance().RemoveByRTTIid(GetRTTI());

		GetStaticNotifyNodeDirty().Emit(this);

		// 删除父节点之前要解除父子节点绑定关系
		ClearAllChildren();
		m_children.clear();

		// 清理数据
		ClearContainerDatas();

		//Super::ReleaseSelf();
		Release();
		SandboxNode* p = this;
		SANDBOX_DELETE_LABEL(p, kMemSandboxNode);
	}

	void SandboxNode::ClearContainerDatas()
	{
		ms_dataTags.Clear(this);
		ms_dataOwnerUins.Clear(this);
		ms_dataIgnoreSafeMode.Clear(this);
	}

	SandboxNode* SandboxNode::GetPrev() const
	{
		auto node = m_nodePtr->Prev();
		return node ? node->Get() : nullptr;
	}

	SandboxNode* SandboxNode::GetNext() const
	{
		auto node = m_nodePtr->Next();
		return node ? node->Get() : nullptr;
	}

	/* 更改child在m_children的列表中的位置 index:[0,size) */
	bool SandboxNode::ResetChildPos(int index, SandboxNode* child)
	{
		if (!child) return false;
		if (index < 0 || index >= m_children.size()) return false;
		//[0,size)
		//auto listNode = m_children.find([child](SandboxNode* node) -> bool {
		//	return child == node;
		//});
		//if (!listNode) {
		//	return false;
		//}
		if (child->m_nodePtr->IsInContainer(&m_children)) {
			AutoRef<SandboxNode> holder = child;
			m_children.erase(child->m_nodePtr);
			m_children.insert(index, child->m_nodePtr);
			if (m_childrenFast)
			{
				auto iter = m_childrenFast->find(child->GetName().c_str());
				if (iter != m_childrenFast->end())
				{
					m_childrenFast->erase(iter);
				}
			}
			return true;
		}
		return false;
	}

	SandboxNode* SandboxNode::GetRoot()
	{
		if (m_parent)
		{
			return m_parent->GetRoot();
		}
		return this;
	}

	const SandboxNode* SandboxNode::GetRoot() const
	{
		if (m_parent)
		{
			return m_parent->GetRoot();
		}
		return this;
	}

	bool SandboxNode::BeginPlay()
	{
		if (!IsEnable() || IsPlaying() || !IsReady())
			return false;

		m_flags.SetFlag(FLAGTYPE_ISPLAYING, true);
		OnBeginPlay();

		// 递归子节点
		m_children.for_each([](SandboxNode* node) -> void {
			node->BeginPlay();
			});
		return true;
	}

	void SandboxNode::EndPlay()
	{
		if (!IsPlaying())
			return;

		// 递归子节点
		m_children.for_each([](SandboxNode* node) -> void {
			node->EndPlay();
			});

		m_flags.SetFlag(FLAGTYPE_ISPLAYING, false);
		OnEndPlay();
	}

	void SandboxNode::SetNodeid(SandboxNodeID nodeid)
	{
		m_nodeid = nodeid;
	}

	void SandboxNode::SetEnable(bool enable, bool recursive)
	{
		if (enable != m_flags.CheckFlag(FLAGTYPE_ENABLE))
		{
			m_flags.SetFlag(FLAGTYPE_ENABLE, enable);
			GetStaticNotifyEnableChanged().Emit(this, enable);
			OnAttributeChanged(this, &R_Enabled);
		}

		if (recursive)
		{
			m_children.for_each([enable](SandboxNode* node) -> void {
				node->SetEnable(enable, true);
				});
		}
	}

	bool SandboxNode::IsEnable(bool recursive)
	{
		if (!m_flags.CheckFlag(FLAGTYPE_ENABLE))
			return false;

		if (recursive && m_parent)
		{
			return m_parent->IsEnable(recursive);
		}
		return true;
	}

	void SandboxNode::SetActive(bool active, bool recursive)
	{
		if (active != m_flags.CheckFlag(FLAGTYPE_ACTIVE))
		{
			m_flags.SetFlag(FLAGTYPE_ACTIVE, active);
			OnActiveChanged(active);
		}

		if (recursive)
		{
			m_children.for_each([active](SandboxNode* node) -> void {
				node->SetActive(active, true);
				});
		}
	}

	void SandboxNode::OnActiveChanged(bool active)
	{
		if (active == m_flags.CheckFlag(FLAGTYPE_ACTIVESTATE))
			return;

		m_flags.SetFlag(FLAGTYPE_ACTIVESTATE, active);

		// 活跃状态变化处理
		if (active)
			OnActive();
		else
			OnInactive();

		// 递归
		m_children.for_each([active](SandboxNode* child) -> void {
			child->OnActiveChanged(active && child->m_flags.CheckFlag(FLAGTYPE_ACTIVE));
			});
	}

	void SandboxNode::OnActive()
	{
		// 如果需要tick ，开始订阅
		TryRegisterTick();

		GetStaticNotifyActiveChanged().Emit(this, true); // 通知
	}

	void SandboxNode::OnInactive()
	{
		// 如果tick 已经订阅，取消订阅
		UnregisterTick();

		GetStaticNotifyActiveChanged().Emit(this, false); // 通知
	}

	void SandboxNode::OnEnergeticChange(bool able)
	{
		GetStaticNotifyEnergeticChanged().Emit(this, able); // 通知
	}

	void SandboxNode::SetEnergetic(bool able)
	{
		if (able == m_flags.CheckFlag(FLAGTYPE_ENERGETIC))
			return;

		m_flags.SetFlag(FLAGTYPE_ENERGETIC, able);
		OnEnergeticChange(able);
	}

	void SandboxNode::TryRegisterTick()
	{
		if (m_flags.CheckFlag(FLAGTYPE_TICKACTIVE)) // tick 已经活跃
			return;
		if (!m_flags.CheckFlag(FLAGTYPE_TICK)) // tick
			return;
		if (!IsActive()) // 不满足条件
			return;

#if defined NODEACTIVE_BY_TRIGGER
		AutoRef<ServiceNode> service = GetService();
		if (!service || !service->GetTickMgr())
			return;

		service->GetTickMgr()->Subscribe(this);
#endif

		m_flags.SetFlag(FLAGTYPE_TICKACTIVE, true);
	}

	void SandboxNode::UnregisterTick()
	{
		if (!m_flags.CheckFlag(FLAGTYPE_TICKACTIVE)) // tick 不活跃
			return;

		m_flags.SetFlag(FLAGTYPE_TICKACTIVE, false);

#if defined NODEACTIVE_BY_TRIGGER
		AutoRef<ServiceNode> service = GetService();
		if (!service || !service->GetTickMgr())
			return;

		service->GetTickMgr()->Unsubscribe(this);
#endif
	}

	void SandboxNode::BindTick()
	{
		m_flags.SetFlag(FLAGTYPE_TICK, true);
		TryRegisterTick();
	}

	void SandboxNode::UnbindTick()
	{
		m_flags.SetFlag(FLAGTYPE_TICK, false);
		UnregisterTick();
	}

	void SandboxNode::SetSelected(bool toggle, int uin)
	{
		if (uin == 0)
			uin = Config::GetSingleton().GetLocalUin();
		if (uin == 0) // 只有云服的uin 是0，云服不需要选中
		{
			SANDBOX_ASSERT(false);
			return;
		}

		if (toggle) // 选中
		{
			if (m_selectedUins.find(uin) == m_selectedUins.end())
			{
				m_selectedUins.insert(uin);
				GetStaticNotifySelectedChanged().Emit(this, true, uin);
			}
		}
		else if (uin < 0)
		{
			m_selectedUins.clear();
			GetStaticNotifySelectedChanged().Emit(this, false, -1);
		}
		else // 取消选中
		{
			auto iter = m_selectedUins.find(uin);
			if (iter != m_selectedUins.end())
			{
				m_selectedUins.erase(iter);
				GetStaticNotifySelectedChanged().Emit(this, false, uin);
			}
		}
	}

	bool SandboxNode::IsAncestorSelected() const
	{
		const SandboxNode* node = this;
		do
		{
			if (node->IsSelected())
				return true;
		} while (node = node->GetParent());
		return false;
	}

	bool SandboxNode::CheckSelectedUin(int uin) const
	{
		if (uin == 0)
			uin = Config::GetSingleton().GetLocalUin();
		if (uin == 0) // 只有云服的uin 是0，云服不需要选中
		{
			SANDBOX_ASSERT(false);
			return false;
		}
		return m_selectedUins.find(uin) != m_selectedUins.end();
	}

	void SandboxNode::GetSelectedDescendants(std::vector<SandboxNode*>& out)
	{
		m_children.for_each([&out](SandboxNode* node) -> void {
			if (node->IsSelected())
			{
				out.push_back(node);
			}
			else
			{
				node->GetSelectedDescendants(out);
			}
			});
	}

	void SandboxNode::ProcessRemoteEvent(ReflexValue* descriptor, AutoRef<ReflexTuple> args, int source_uin)
	{
		if (!descriptor)
			return;
		descriptor->Call(this, args);
	}

	void SandboxNode::SendEventInvocation(ReflexValue* descriptor, AutoRef<ReflexTuple> args, int target_uin /*= 0*/)
	{
		GetStaticNotifyRemoteInvocation().Emit(this, descriptor, args, target_uin);
	}

	void SandboxNode::NodeEnterScene(Scene* scene)
	{
		m_curScene = scene;

		BindToScene(scene);
		OnEnterScene(scene);

		// 递归子节点
		m_children.for_each([scene](SandboxNode* node) -> void {
			node->NodeEnterScene(scene);
			});
	}

	void SandboxNode::NodeLeaveScene(Scene* scene)
	{
		// 递归子节点
		m_children.for_each([scene](SandboxNode* node) -> void {
			node->NodeLeaveScene(scene);
			});

		UnbindFromScene(scene);
		OnLeaveScene(scene);
		m_curScene = nullptr;
	}

	void SandboxNode::BindToScene(Scene* scene)
	{
		scene->BindNode(this);
	}

	void SandboxNode::UnbindFromScene(Scene* scene)
	{
		// 如果选中，取消选中
		if (IsSelected())
		{
			SetSelected(false, -1);
		}
		scene->UnbindNode(this);
	}

	AutoRef<SandboxNode> SandboxNode::CreateNodeByFactory(const std::string& classtype)
	{
		auto factory = SandboxCoreFactorys::GetInstance().GetFactoryT<MNSandbox::SandboxNode>();
		return factory ? factory->Produce(classtype) : nullptr;
	}

	AutoRef<SandboxNode> SandboxNode::CreateNodeByType(const std::string& classtype)
	{
		const RuntimeClass* rtti = RuntimeClass::Container::Get().FindRTTI(classtype);
		return rtti ? rtti->NewObjectLonely<SandboxNode>() : nullptr;
	}

	AutoRef<SandboxNode> SandboxNode::Clone(TCallback<SandboxNode*>* cbLoadNode)
	{
		// 按类型创建对象
		AutoRef<SandboxNode> node = SandboxNode::CreateNodeByType(GetClassType());
		if (!node)
		{
			SANDBOX_ASSERT(false);
			return nullptr;
		}

		//auto nodeid = node->m_nodeid; // 缓存自动生成的id

		// 反射相关
		node->m_nodeid = 0; // 保证id 无关
		CloneTo(node, cbLoadNode);
		//if (node->m_nodeid == 0) // 还原id
		//	node->m_nodeid = nodeid;
		return node;
	}

	void SandboxNode::CloneTo(const AutoRef<SandboxNode>& node, TCallback<SandboxNode*>* cbLoadNode)
	{
		// 反射不能写ID，所以克隆也是不写ID的
		// 所以，ID不用特殊处理

		CopyReflexContainerTo(node); // 反射相关
		CopyCustomAttributesTo(node); // 自定义属性
		if (cbLoadNode)
			cbLoadNode->Emit(node);

		CopyChildrenTo(node); // 子对象
	}

	void SandboxNode::CopyReflexContainerTo(const AutoRef<SandboxNode>& node)
	{
		auto& reflexContainerSrc = GetReflexContainer();
		auto& reflexContainerDst = node->GetReflexContainer();
		SandboxNode* ins = node.get();
		ReflexVariant variant;

		std::vector<ReflexValue*> reflexs;
		if (reflexContainerSrc.GetSaveList(reflexs))
		{
			for (auto& reflexSrc : reflexs)
			{
				if (!reflexSrc)
					continue;

				if (!reflexSrc->CanSet()) // 需要等读
					continue;

				if (!reflexSrc->GetVariant(this, variant)) // 取数据
				{
					SANDBOX_ASSERT(false);
					continue;
				}

				if (!reflexSrc->SetVariant(ins, variant)) // 写数据
				{
					SANDBOX_ASSERT(false);
				}
			}
		}
	}

	void SandboxNode::CopyCustomAttributesTo(const AutoRef<SandboxNode>& node)
	{
		auto& attrContainerSrc = GetAttributeContainer();
		auto& attrContainerDst = node->GetAttributeContainer();
		//SandboxNode* ins = node.get();
		AutoRef<Attribute> attrDst;

		attrContainerDst.ClearAllAttributes(); // 先清理

		auto attrsSrc = attrContainerSrc.GetDatas();
		for (auto& attr : attrsSrc)
		{
			SANDBOX_ASSERT(!attrContainerDst.GetAttribute(attr.first));
			attrDst = attrContainerDst.NewAttribute(attr.first, attr.second->GetAttrType()); // 创建新的
			if (!attrDst)
			{
				SANDBOX_ASSERT(false);
				continue;
			}

			if (!attrDst->SetVariant(attr.second->GetVariant())) // 拷贝
			{
				SANDBOX_ASSERT(false);
			}
		}
	}

	void SandboxNode::CopyChildrenTo(const AutoRef<SandboxNode>& node, bool clearchild/*=true*/, TCallback<SandboxNode*>* cbLoadNode/*=nullptr*/)
	{
		auto& childrenSrc = GetChildrenList();
		//SandboxNode* ins = node.get();
		if (clearchild)
			node->ClearAllChildren(); // 先清理

		childrenSrc.for_each([&](SandboxNode* child) -> void {
			auto newnode = child->Clone(cbLoadNode);
			newnode->LoadReflexStart();
			newnode->SetParent(node);
			newnode->LoadReflexEnd();
			});
	}

	void SandboxNode::CloneAsync(AutoRef<CustomRef<TCallback<SANDBOXERR, AutoRef<SandboxNode>>*>> callback)
	{
		if (!callback || !callback->Data())
			return;

		SANDBOXERR result = SANDBOXERR::ERR_FAILED;
		AutoRef<SandboxNode> node;
		do
		{
			AutoRef<Stream> stream = SANDBOX_NEW(StreamBuffer);
			if (!SerializeToStream(stream))
			{
				result = SANDBOXERR::SDNODE_CLONESERIALIZEBIN;
				break;
			}
			stream->SetCurrentOffset(0);
#ifdef SDBSTREAM_DEV_OPEN
			stream->Dev_Clear();
#endif

#if 0 // 测试用，立即执行
			result = UnserializeFromStream(node, stream);
			break;
#else
			if (Thread::ThreadTaskManager::GetSingletonPtr())
			{
				Thread::NodeCloneData data;
				data._stream = stream;
				data._callback = callback;
				Thread::ThreadTaskManager::GetSingletonPtr()->PushNodeCloneTask(data);
				return;
			}
			SANDBOX_ASSERT(Thread::ThreadTaskManager::GetSingletonPtr());
#endif
		} while (false);

		MNTimer::CreateTimer([callback, result, node](AutoRef<MNTimer>) {
			callback->Data()->Emit(SANDBOXERR::FAILED, nullptr);
			}, 0.0); // 下一帧执行
	}

	void SandboxNode::CloneAsyncMethod(void (*f)(SANDBOXERR, AutoRef<SandboxNode>))
	{
		using CallbackBase = TCallback<SANDBOXERR, AutoRef<SandboxNode>>;
		using CallbackSub = TCallbackMethod<SANDBOXERR, AutoRef<SandboxNode>>;
		CloneAsync(CreateCustomSubObject<CallbackBase, CallbackSub, CallbackSub::Param>(f));
	}

	void SandboxNode::CloneAsyncFunction(const std::function<void(SANDBOXERR, AutoRef<SandboxNode>)>& f)
	{
		using CallbackBase = TCallback<SANDBOXERR, AutoRef<SandboxNode>>;
		using CallbackSub = TCallbackFunction<SANDBOXERR, AutoRef<SandboxNode>>;
		CloneAsync(CreateCustomSubObject<CallbackBase, CallbackSub, CallbackSub::Param>(f));
	}

	int SandboxNode::CloneLua(lua_State* L)
	{
		WeakRef<LuaCoroutine> co = GetSandboxScriptVM().GetLuaStateRef(L).get();
		if (!co || !co->IsValid())
			return 0;

		CloneAsyncFunction([co](SANDBOXERR result, AutoRef<SandboxNode> node) {
			if (!co || !co->IsValid()) return;

			lua_State* L2 = co->GetLuaState();
			int cnt = 0;
			if (result != SANDBOXERR::OK)
			{
				lua_pushnil(L2);
				lua_pushnumber(L2, static_cast<lua_Number>(result));
				cnt = 2;
			}
			else
			{
				SANDBOX_ASSERT(node);
				cnt = lua_pushC(L2, node); // 返回值
			}
			co->Resume(cnt);
			});
		return co->Pause();
	}

	/////////////////////////////////////////////////////////////

	void SandboxNode::OnAddChild(SandboxNode* child)
	{
		m_notifyChildAdded.Emit(child);
		GetStaticNotifyChildEnter().Emit(this, child);
		OnNotifyNodeChanged(NODECHANGE::ChildAdd);

		// 更新包围盒
		GetBoundingBox()->UpdateBoxForAddChild(child);

		if (m_notifyChildEnterByName.IsValid(child->GetName()))
			m_notifyChildEnterByName.Emit(child->GetName(), child);
	}

	void SandboxNode::OnRemoveChild(SandboxNode* child)
	{
		// 更新包围盒
		GetBoundingBox()->UpdateBoxForRemoveChild(child);

		GetStaticNotifyChildLeave().Emit(this, child);
		m_notifyChildRemoved.Emit(child);
		OnNotifyNodeChanged(NODECHANGE::ChildRemove);
	}

	void SandboxNode::OnChildChangeName(SandboxNode* child, const std::string& oldname)
	{
		if (m_notifyChildEnterByName.IsValid(child->GetName()))
			m_notifyChildEnterByName.Emit(child->GetName(), child);
		if (m_childrenFast)
		{
			m_childrenFast->erase(oldname.c_str());
		}
	}

	void SandboxNode::OnEnterParent(SandboxNode* parent)
	{
		//m_notifyEnterParent.Emit(this, parent);
	}

	void SandboxNode::OnLeaveParent(SandboxNode* parent)
	{
		//m_notifyLeaveParent.Emit(this, parent);
	}

	void SandboxNode::OnParentChanged(SandboxNode* parent)
	{
		if (m_notifyParentChanged.IsValid())
			m_notifyParentChanged.Emit(parent);
	}

	void SandboxNode::OnAncestryChanged(ServiceNode* src, ServiceNode* dst)
	{
		if (m_notifyAncestryChanged.IsValid())
			m_notifyAncestryChanged.Emit(dst);

		// 通知自己的子节点
		m_children.for_each([src, dst](SandboxNode* child) {
			child->OnAncestryChanged(src, dst);
			});
	}

	void SandboxNode::OnEnterScene(Scene* scene)
	{
		m_notifyEnterScene.Emit(this, scene);
		GetStaticNotifyEnterScene().Emit(this, scene);
	}

	void SandboxNode::OnLeaveScene(Scene* scene)
	{
		m_notifyLeaveScene.Emit(this, scene);
		GetStaticNotifyLeaveScene().Emit(this, scene);
	}

	void SandboxNode::OnBeginPlay()
	{
		GetStaticNotifyBeginPlay().Emit(this);
	}

	void SandboxNode::OnEndPlay()
	{
		GetStaticNotifyEndPlay().Emit(this);
	}

	void SandboxNode::OnNodeAttributeChanged(ReflexValue* reflexval, std::vector<ReflexValue*>* group, ReflexVariant* params)
	{
		OnNotifyNodeChanged(NODECHANGE::AttributeChanged);
		m_notifyAttributeChangedKey.Emit(reflexval->GetName());

		// ReflexValue 会被重写，需要取最新的值
		auto& reflexContainer = GetReflexContainer();
		reflexval = reflexContainer.GetOriginReflex(reflexval);
		SANDBOX_ASSERT(reflexval);
		if (reflexval)
		{
			m_notifyAttributeChanged.Emit(this, reflexval); // legacy 弃用，新的需要使用带 Ex
			m_notifyAttributeChangedEx.Emit(this, reflexval, group, params); // 多重反射 （params非空表示带参数）
			GetStaticNotifyAttributeChanged().Emit(this, reflexval, group, params); // 全局 （params非空表示带参数）
		}
	}

	void SandboxNode::OnCustomAttrAdd(Attribute* attr)
	{
		OnNotifyNodeChanged(NODECHANGE::CustomAttrAdd);
		GetStaticNotifyCustomAttrChanged().Emit(this, NODECHANGE::CustomAttrAdd, attr->GetName());
	}

	void SandboxNode::OnCustomAttrRemove(Attribute* attr)
	{
		OnNotifyNodeChanged(NODECHANGE::CustomAttrRemove);
		GetStaticNotifyCustomAttrChanged().Emit(this, NODECHANGE::CustomAttrRemove, attr->GetName());
	}

	void SandboxNode::OnCustomAttrChanged(Attribute* attr)
	{
		OnNotifyNodeChanged(NODECHANGE::CustomAttrChanged);
		GetStaticNotifyCustomAttrChanged().Emit(this, NODECHANGE::CustomAttrChanged, attr->GetName());
	}

	void SandboxNode::OnNotifyNodeChanged(NODECHANGE e)
	{
#ifdef BUILD_MINI_EDITOR_APP
		m_notifyNodeChanged.Emit(this, e);
#endif
		GetStaticNotifyNodeChanged().Emit(this, e);

		// 通知服务节点，节点属性变化
		auto service = GetService();
		if (service)
			service->CallbackNodeChanged(this, e);
	}

	/////////////////////////////////////////////////////////////

	int SandboxNode::OnLuaIndex(lua_State* L, const std::string& key) const
	{
		//todo...
		return 0;
	}

	bool SandboxNode::OnLuaNewIndex(lua_State* L, const std::string& key, int valueindex)
	{
		//lua_type(L, valueindex);
		//todo...
		return false;
	}

	void SandboxNode::UseDefaultName()
	{
		if (!m_name.empty())
			return; // 有名称了，不需要再设置默认名称

		auto& classtype = GetClassType();
		int idx = GetSceneManager().GetNewNodeIndex(classtype);

		char szName[256];
		sprintf(szName, "%s%d", classtype.c_str(), idx);
		m_name = szName; // 设置名称(不是主动设置，不通知)
	}

#ifdef BUILD_MINI_EDITOR_APP
    void SandboxNode::SetOperationLock(bool lock)
    {
		m_flags.SetFlag(FLAGTYPE_OPERATION_LOCK, lock);
        std::function<void(SandboxNode* parent)> func;
        func = [&func, lock](SandboxNode* parent) {
            parent->m_children.for_each([&, lock](SandboxNode* node) {
				node->SetOperationLock(lock);
                });
        };
		func(this);
    }

    bool SandboxNode::GetOperationLock()const
    {
		return m_flags.CheckFlag(FLAGTYPE_OPERATION_LOCK);
    }
#endif
    void SandboxNode::SetSerializable(const bool& toggle)
	{
		m_flags.SetFlag(FLAGTYPE_SERIALIZABLE, toggle);
	}

	void SandboxNode::SerializableGet(bool& value) const
	{
		value = IsSerializable();//value = m_bEditorSaveToMap;
	}

	bool SandboxNode::EnabledGet() const
	{
		return m_flags.CheckFlag(FLAGTYPE_ENABLE);
	}
	void SandboxNode::EnabledSet(const bool& value)
	{
		SetEnable(value, false);
	}

	bool SandboxNode::IsA(std::string value) const
	{
		return GetRTTI()->IsKindOf(RuntimeClass::Container::Get().FindRTTI(value));
	}

	unsigned long long SandboxNode::FlagFilterGet(const FlagsLong& filter) const
	{
		return m_flags.And(filter).GetData();
	}

	void SandboxNode::FlagFilterSet(const FlagsLong& filter, const unsigned long long& value)
	{
		FlagsLong flags(value);
		FlagsLong src = m_flags.And(filter.Neg());
		m_flags = flags.And(filter).Or(src);
	}


	AutoRef<Attribute> SandboxNode::GetAttribute(const std::string& attr) const
	{
		return m_attrContainer.GetAttribute(attr);
	}

	AutoRef<Attribute> SandboxNode::NewAttribute(const std::string& attr, Attribute::TYPE type)
	{
		return m_attrContainer.NewAttribute(attr, type);
	}

	void SandboxNode::AddAttribute(const std::string& attr, const Attribute::TYPE& type)
	{
		NewAttribute(attr, type);
	}

	void SandboxNode::DeleteAttribute(const std::string& attr)
	{
		m_attrContainer.DeleteAttribute(attr);
	}

	ReflexVariant SandboxNode::GetAttributeValue(std::string attr) const
	{
		auto attribute = m_attrContainer.GetAttribute(attr);
		return attribute ? attribute->GetVariant() : ReflexVariant();
	}

	bool SandboxNode::SetAttributeValue(std::string attr, ReflexVariant value)
	{
		auto attribute = m_attrContainer.GetAttribute(attr);
		if (attribute)
		{
			return attribute->SetVariant(value);
		}
		auto newAttr = m_attrContainer.NewAttribute(attr, Attribute::GetAttrTypeByRefexType(value.GetType()));
		if (newAttr)
		{
			return newAttr->SetVariant(value);
		}
		return false;
	}

	//Notify<std::string>* SandboxNode::GetAttributeChangedNotify(const std::string& attr)
	//{
	//	auto attribute = m_attrContainer.GetAttribute(attr);
	//	if (attribute)
	//	{
	//		return &attribute->m_notifyChanged;
	//	}
	//	return nullptr;
	//}

	Notify<std::string>* SandboxNode::GetNotifyAttributeChanged()
	{
		return &m_attrContainer.m_notifyAttrChanged;
	}

	bool SandboxNode::SerializeToJson(MNJsonObject& jsonObj)
	{
		MNJsonVal outJsonValue;
		if (!NodeSerialize::SerializeNodeToJson(this, outJsonValue))
		{
			SANDBOX_ASSERT(false && "sandboxnode serialize to json failed!");
			return false;
		}
		if (outJsonValue.is<MNJsonObject>()) // 必须是 MNJsonObject 类型
		{
			jsonObj = outJsonValue.get<MNJsonObject>();
		}
		else
		{
			SANDBOX_ASSERT(false && "sandboxnode serialize, return type is invalid!");
			return false;
		}
		return true;
	}

	bool SandboxNode::ParseFromJson(const MNJsonObject& jsonObj)
	{
		AutoRef<SandboxNode> self = this;
		return NodeSerialize::ParseNodeFromJson(self, jsonObj);
	}

	AutoRef<SandboxNode> SandboxNode::UnserializeFromJson(const MNJsonObject& jsonObj)
	{
		AutoRef<SandboxNode> node;
		if (!NodeSerialize::ParseNodeFromJson(node, jsonObj))
		{
			SANDBOX_ASSERT(false && "sandboxnode unserialize from json failed!");
		}
		return node;
	}

	bool SandboxNode::SerializeToStream(AutoRef<Stream>& stream)
	{
		if (!stream)
		{
			stream = SANDBOX_NEW(StreamBuffer);
		}

		if (!NodeSerialize::SerializeNodeToStream(this, stream))
		{
			SANDBOX_ASSERT(false && "sandboxnode serialize to json failed!");
			return false;
		}
#if defined(SDBSTREAM_DEV_OPEN) && defined(SANDBOX_DEV)
		std::string devmsg;
		stream->Dev_SaveToString(devmsg);
		SANDBOX_LOG("node serialize to stream : ", devmsg);
#endif
		return true;
	}

	SANDBOXERR SandboxNode::ParseFromStream(const AutoRef<Stream>& stream)
	{
		if (!stream)
			return SANDBOXERR::SDNODE_PARSESTREAM_NIL;

		AutoRef<SandboxNode> self = this;
		return NodeSerialize::ParseNodeFromStream(self, stream).first;
	}

	SANDBOXERR SandboxNode::UnserializeFromStream(AutoRef<SandboxNode>& node, const AutoRef<Stream>& stream)
	{
		auto ret = NodeSerialize::ParseNodeFromStream(node, stream);
		SANDBOX_ASSERTEX(ret.first == SANDBOXERR::OK, ToString("sandboxnode unserialize from stream failed! err = ", (int)ret.first));
		return ret.first;
	}

	void SandboxNode::OnNotifyDataSync(const ReflexValue* rvalue, bool issend, bool& ok)
	{
		if (issend)
		{
			ok = m_syncMode.IsSendEnable(rvalue, true);
		}
		else
		{
			ok = m_syncMode.IsReceiveEnable(rvalue, true);
		}
	}

	ReflexValue* SandboxNode::GetReflexValue(const std::string& rvname) const
	{
		return GetReflexContainer().GetReflex(rvname);
	}

	void SandboxNode::SetSyncMode(SYNCMODE mode)
	{
		bool issyncableBefore = IsSyncable();
		SYNCMODE beforeMode = m_syncMode.GetNodeSyncMode();
		if (m_syncMode.SetNodeSyncMode(mode)) {
			OnAttributeChanged(this, &R_SyncMode); // R_Syncable
			GetStaticNotifySyncModeChanged().Emit(this, beforeMode, mode);
		}
		OnUpdateNodeSync(issyncableBefore);
	}

	void SandboxNode::SetLocalSyncFlag(SYNCLOCALFLAG flag)
	{
		bool issyncableBefore = IsSyncable();
		SYNCLOCALFLAG beforeflag = m_syncMode.GetLocalSyncFlag();
		if (m_syncMode.SetLocalSyncFlag(flag)) {
			OnAttributeChanged(this, &R_LocalSyncFlag);
			GetStaticNotifySyncLocalFlagChanged().Emit(this, beforeflag, flag);
		}
		OnUpdateNodeSync(issyncableBefore);
	}

	void SandboxNode::SetReflexSyncMode(const std::string& rvname, const SYNCMODE& mode)
	{
		auto rvalue = GetReflexValue(rvname);
		if (rvalue && m_syncMode.SetReflexSyncMode(rvalue, mode))
			GetStaticNotifyRValueSyncModeChanged().Emit(this, rvalue, mode);
	}

	void SandboxNode::SetReflexLocalSyncFlag(const std::string& rvname, const SYNCLOCALFLAG& flag)
	{
		auto rvalue = GetReflexValue(rvname);
		if (rvalue && m_syncMode.SetReflexLocalSyncFlag(rvalue, flag))
			GetStaticNotifyRValueLocalSyncFlagChanged().Emit(this, rvalue, flag);
	}

	void SandboxNode::OnUpdateNodeSync(bool issyncableBefore)
	{
		bool issyncableAfter = IsSyncable();
		if (issyncableBefore)
		{
			if (!issyncableAfter)
			{
				OnNodeSyncDeactive();
			}
		}
		else if (issyncableAfter)
		{
			OnNodeSyncActive();
		}
	}

	void SandboxNode::DescendantNodeEnter(SandboxNode* node, bool recursion)
	{
		OnDescendantNodeEnter(node);

		// 子节点处理
		if (recursion && node->GetChildrenCount() > 0)
		{
			node->GetChildrenList().for_each([this](SandboxNode* child) -> void {
				DescendantNodeEnter(child);
				});
		}
	}

	void SandboxNode::DescendantNodeLeave(SandboxNode* node, bool recursion)
	{
		// 子节点处理
		if (recursion && node->GetChildrenCount() > 0)
		{
			node->GetChildrenList().for_each([this](SandboxNode* child) -> void {
				DescendantNodeLeave(child);
				});
		}

		OnDescendantNodeLeave(node);
	}

	void SandboxNode::OnFlagsChanged(unsigned type)
	{
		// 只有同步需要反射属性变化
		if (ms_flagsSyncCfg.CheckFlag(type))
			OnAttributeChanged(this, &R_Flag2);

#if (defined SANDBOX_DEV && defined SANDBOX_USE_PROFILE) && defined(SANDBOX_USE_STACK)
		//if (type == FLAGTYPE_BINDNODEID)
		//{
		//	SANDBOX_LOG("sandbox node flag [FLAGTYPE_BINDNODEID] changed : \n", Debug::StackInfo::GetCurStackInfo());
		//}
#endif
	}

	std::string SandboxNode::GetPath() const
	{
		std::string path = m_name;
		auto parent = this;
		while (parent = parent->GetParent())
		{
			path = parent->GetName() + std::string(".") + path;
		}
		return path;
	}

	AutoRef<SandboxNode> SandboxNode::GetNodeByPath(const std::string& path) const
	{
		AutoRef<SandboxNode> node = const_cast<SandboxNode*>(this);
		std::vector<std::string> strList;
		SplitStringToGroup(path, ".", strList);
		for (auto& v : strList)
		{
			node = node->GetChildByName(v);
			if (!node)
				return nullptr;
		}
		return node;
	}

	bool SandboxNode::ResourceLoad()
	{
		if (IsResourceLoading() || IsResourceLoaded())
			return false;
		m_flags.SetFlag(FLAGTYPE_RESOURCELOADING, true);

		OnInitResouceBody();
		OnResourceFinish();
		return true;
	}

	bool SandboxNode::ResourceUnload()
	{
		if (!IsResourceLoading() && !IsResourceLoaded())
			return false;
		//m_flags.SetFlag(FLAGTYPE_RESOURCELOADING | FLAGTYPE_RESOURCELOADED, false);
		m_flags.SetFlag(FLAGTYPE_RESOURCELOADING, false);
		m_flags.SetFlag(FLAGTYPE_RESOURCELOADED, false);

		OnDestroyResouceBody();
		return true;
	}

	void SandboxNode::OnResourceFinish()
	{
		OPTICK_EVENT(OPTICK_FUNC, Optick::Category::GameLogic);
		m_flags.SetFlag(FLAGTYPE_RESOURCELOADING, false);
		m_flags.SetFlag(FLAGTYPE_RESOURCELOADED, true);
	}

	bool SandboxNode::ResourceDynamicLoad()
	{
		if (m_flags.CheckFlag(FLAGTYPE_DYNAMICLOADFLAG))
			return false;
		if (!IsDynamicLoadAble()) return false;

		m_flags.SetFlag(FLAGTYPE_DYNAMICLOADFLAG, true);

		if (!m_flags.CheckFlag(FLAGTYPE_DYNAMICSTOPTEMP))
			ToLoadResource();

		return true;
	}

	bool SandboxNode::ResourceDynamicUnload()
	{
		if (!m_flags.CheckFlag(FLAGTYPE_DYNAMICLOADFLAG))
			return false;
		if (!IsDynamicLoadAble()) return false;

		m_flags.SetFlag(FLAGTYPE_DYNAMICLOADFLAG, false);
		if (!m_flags.CheckFlag(FLAGTYPE_DYNAMICSTOPTEMP))
			ToUnloadResource();
		return true;
	}

	// 加载资源
	void SandboxNode::ToLoadResource()
	{
		if (CheckResourceLoadCondition())
		{
			ResourceLoad();
		}
	}

	bool SandboxNode::CalcStopDynamicState()
	{
		if (IsStopDynamicLoad())
			return true;

		return m_parent && m_parent->HasStopDynamicLoadFlag();
	}

	void SandboxNode::UpdateStopDynamicFlag()
	{
		bool able = CalcStopDynamicState();
		if (able == HasStopDynamicLoadFlag())
			return;

		if (able)
		{
			// 阻止动态加载
			OnOpenStopDynamic();
		}
		else
		{
			// 允许动态加载
			OnCloseStopDynamic();
		}
	}

	void SandboxNode::OnOpenStopDynamic()
	{
		if (m_flags.CheckFlag(FLAGTYPE_STOPDYNAMICLOADFLAG))
			return;

		m_flags.SetFlag(FLAGTYPE_STOPDYNAMICLOADFLAG, true);
		ToLoadResource();

		// 递归
		m_children.for_each([](SandboxNode* node) {
			node->OnOpenStopDynamic();
			});
	}

	void SandboxNode::OnCloseStopDynamic()
	{
		if (!m_flags.CheckFlag(FLAGTYPE_STOPDYNAMICLOADFLAG))
			return;

		m_flags.SetFlag(FLAGTYPE_STOPDYNAMICLOADFLAG, false);
		ToUnloadResource();

		// 递归
		m_children.for_each([](SandboxNode* node) {
			node->OnCloseStopDynamic();
			});
	}

	void SandboxNode::StopDynamicRecursion()
	{
		m_flags.SetFlag(FLAGTYPE_STOPDYNAMICLOAD, true);
		UpdateStopDynamicFlag();
	}

	void SandboxNode::SetDynamicLoadAble(bool able)
	{
		if (m_flags.CheckFlag(FLAGTYPE_DYNAMICLOADABLE) == able)
			return;

		m_flags.SetFlag(FLAGTYPE_DYNAMICLOADABLE, able);

		//三种加载模式，这里不需要了，不然会混乱逻辑
		//if (!IsLoadingReflex())
		//{
		//	// 如果是关闭动态加载，需要立即加载资源
		//	if (!able)
		//	{
		//		ToLoadResource();
		//	}
		//}
	}

	bool SandboxNode::CheckGUIActive()
	{
		SandboxNode* parent = this;
		do
		{
			if (parent->CanActiveGUI())
				return true;
		} while (parent = parent->GetParent());
		return false;
	}

	void SandboxNode::OnLoadReflexFinish()
	{
		// 如果是关闭动态加载，需要立即加载资源
		if (IsDefaultResourceLoad())//(!m_flags.CheckFlag(FLAGTYPE_DYNAMICLOADABLE))
		{
			ToLoadResource();
		}
	}

	void SandboxNode::InitInstance()
	{
		//SANDBOXNODE_PROFILE_BEG(this, SandboxNodeProfile::Stage::Init);

		HandleInit();
		InitDefaultChildren();

		//SANDBOXNODE_PROFILE_END(this, SandboxNodeProfile::Stage::Init);
	}

	void SandboxNode::InitNodeLonely()
	{
		HandleInit();
	}

	void SandboxNode::LoadReflexStart()
	{
		//SANDBOXNODE_PROFILE_BEG(this, SandboxNodeProfile::Stage::LoadReflex);

		SANDBOX_ASSERT(!m_flags.CheckFlag(FLAGTYPE_LOADINGREFLEX));
		m_flags.SetFlag(FLAGTYPE_LOADINGREFLEX, true);

		OnLoadReflexStart();
	}
	void SandboxNode::LoadReflexEnd()
	{
		SANDBOX_ASSERT(m_flags.CheckFlag(FLAGTYPE_LOADINGREFLEX));
		m_flags.SetFlag(FLAGTYPE_LOADINGREFLEX, false);

		OnLoadReflexEnd();

		OnLoadReflexFinish();

		//SANDBOXNODE_PROFILE_END(this, SandboxNodeProfile::Stage::LoadReflex);
	}

	void SandboxNode::LuaSetDynamicLoadAble(bool able)
	{
		if (able)
		{
			SetResourceLoadMode(RESOURCE_LOAD_MODE::Dynamic);
		}
		else
		{
			SetDynamicLoadAble(false);
		}
		OnAttributeChanged(this, &R_ResourceDynamicLoad);
	}

	bool SandboxNode::CheckLuaManualLoad(lua_State* L)
	{
		if (!IsManualResourceLoad())
		{
			ScriptState::LogRunError(L, "node is not manual load!");
			return false;
		}
		return true;
	}

	void SandboxNode::OnResourceAssetFinish()
	{
		if (IsManualResourceLoad())
		{
			if (m_manualOperate.asyncCb)
			{
				m_manualOperate.asyncCb->CallLuaFunction<AutoRef<SandboxNode>>(this);
				SANDBOX_RELEASE(m_manualOperate.asyncCb);
			}
			if (m_manualOperate.syncCb)
			{
				m_manualOperate.syncCb->Exec(this);
				SANDBOX_RELEASE(m_manualOperate.syncCb);
			}
		}
	}

	int SandboxNode::LuaManualLoadAsync(lua_State* L, AutoRef<LuaFunction> luaFunc)
	{
		if (!CheckLuaManualLoad(L))
		{
			if (luaFunc)
			{
				luaFunc->CallLuaFunction<AutoRef<SandboxNode>>(this);
			}
			return 0;
		}
		m_manualOperate.asyncCb = luaFunc;
		SANDBOX_RELEASE(m_manualOperate.syncCb);

		ResourceLoad();
		return 0;
	}

	int SandboxNode::LuaManualUnLoad(lua_State* L)
	{
		if (!CheckLuaManualLoad(L))
		{
			return 0;
		}

		ResourceUnload();

		return 0;
	}

	int SandboxNode::LuaManualLoad(lua_State* L)
	{
		if (!CheckLuaManualLoad(L))
		{
			return 0;
		}
		MNSandbox::LuaResult::Data<AutoRef<SandboxNode>> data;
		m_manualOperate.syncCb = data.UsePause(L);
		SANDBOX_RELEASE(m_manualOperate.asyncCb);

		ResourceLoad();

		return data.Return();
	}

	SandboxNode::RESOURCE_LOAD_MODE SandboxNode::GetResourceLoadMode()const
	{
		if (IsManualResourceLoad())
		{
			return RESOURCE_LOAD_MODE::Manual;
		}
		else if (IsDynamicLoadAble())
		{
			return RESOURCE_LOAD_MODE::Dynamic;
		}

		return RESOURCE_LOAD_MODE::Default;
	}

	void SandboxNode::SetResourceLoadMode(const RESOURCE_LOAD_MODE& mode)
	{
		AutoRef<SandboxNode> self = this;
		if (IsLoadingReflex() && IsResourceBodyHadInit())
		{
			OnDestroyResouceBody();
		}

		if (mode == RESOURCE_LOAD_MODE::Default)
		{
			ResourceLoad();
		}

		m_flags.SetFlag(FLAGTYPE_MANUAL_RESOURCELOAD, mode == RESOURCE_LOAD_MODE::Manual);

		SetDynamicLoadAble(mode == RESOURCE_LOAD_MODE::Dynamic);

		OnAttributeChanged(this, &R_ResourceLoadMode);
	}

	void SandboxNode::OnInitResouceBody()
	{
		m_flags.SetFlag(FLAGTYPE_RESOURCE_BODY_INIT, true);
	}

	void SandboxNode::OnDestroyResouceBody()
	{
		m_flags.SetFlag(FLAGTYPE_RESOURCE_BODY_INIT, false);

		SANDBOX_RELEASE(m_manualOperate.syncCb);
		SANDBOX_RELEASE(m_manualOperate.asyncCb);
	}

	void SandboxNode::CreateAssetPacketRecursion(std::list<std::pair<std::string, AutoRef<AssetPacket>>>& out) const
	{
		auto assetPacket = CreateAssetPacket();
		if (assetPacket)
			out.push_back(std::make_pair(GetPath(), assetPacket)); // 压入数据

		// 递归
		m_children.for_each([&out](SandboxNode* target) -> void {
			target->CreateAssetPacketRecursion(out);
		});
	}

	////////////////////////////////////////////////////////////////////

	static MINIW::GameStatic<Notify<SandboxNode*>> s_notifyCreate;
	static MINIW::GameStatic<Notify<SandboxNode*, bool, int>> s_notifySelectedChanged;
	static MINIW::GameStatic<Notify<SandboxNode*>> s_notifyBeginPlay;
	static MINIW::GameStatic<Notify<SandboxNode*>> s_notifyEndPlay;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode::NODECHANGE>> s_notifyNodeChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, ReflexValue*, SYNCMODE>> s_notifyRValueSyncModeChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, ReflexValue*, SYNCLOCALFLAG>> s_notifyRValueLocalSyncFlagChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, SYNCMODE, SYNCMODE>> s_notifySyncModeChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, SYNCLOCALFLAG, SYNCLOCALFLAG>> s_notifySyncLocalFlagChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, ReflexValue*, AutoRef<ReflexTuple>, int>> s_notifyRemoteInvocation;
	static MINIW::GameStatic<Notify<SandboxNode*> > s_notifyNodeDirty;
	static MINIW::GameStatic<Notify<SandboxNode*, ReflexValue*, std::vector<ReflexValue*>*, ReflexVariant*>> s_notifyAttributeChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, const std::string&, const std::string&>> s_notifyNameChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, bool>> s_notifyNodeHidden;
	static MINIW::GameStatic<Notify<SandboxNode*, bool>> s_notifyActiveChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, bool>> s_notifyEnergeticChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, bool>> s_notifyEnableChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode*>> s_notifyChildEnter;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode*>> s_notifyChildLeave;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode*, SandboxNode*>> s_notifyParentChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode::NODECHANGE, std::string>> s_notifyCustomAttrChanged;
	static MINIW::GameStatic<Notify<SandboxNode*, SandboxNode*, SandboxNode*>> s_notifyParentChangedEx;
	static MINIW::GameStatic<Notify<SandboxNode*, Scene*>> s_notifyEnterScene;
	static MINIW::GameStatic<Notify<SandboxNode*, Scene*>> s_notifyLeaveScene;
	static MINIW::GameStatic<Notify<SandboxNode*>> s_notifyLeaveGame;
	static MINIW::GameStatic<Notify<SandboxNode*>> s_notifyEnterGame;
	static MINIW::GameStatic<Notify<SandboxNode*, Rainbow::Vector3f>> s_notifyTeleport;

	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyCreate()
	{
		return *s_notifyCreate.EnsureInitialized();
	}
	Notify<SandboxNode*, Rainbow::Vector3f>& SandboxNode::GetStaticNotifyTeleport()
	{
		return *s_notifyTeleport.EnsureInitialized();
	}
	Notify<SandboxNode*, bool, int>& SandboxNode::GetStaticNotifySelectedChanged()
	{
		return *s_notifySelectedChanged.EnsureInitialized();
	}
	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyBeginPlay()
	{
		return *s_notifyBeginPlay.EnsureInitialized();
	}
	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyEndPlay()
	{
		return *s_notifyEndPlay.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode::NODECHANGE>& SandboxNode::GetStaticNotifyNodeChanged()
	{
		return *s_notifyNodeChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, ReflexValue*, SYNCMODE>& SandboxNode::GetStaticNotifyRValueSyncModeChanged()
	{
		return *s_notifyRValueSyncModeChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, ReflexValue*, SYNCLOCALFLAG>& SandboxNode::GetStaticNotifyRValueLocalSyncFlagChanged()
	{
		return *s_notifyRValueLocalSyncFlagChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, SYNCMODE, SYNCMODE>& SandboxNode::GetStaticNotifySyncModeChanged()
	{
		return *s_notifySyncModeChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, SYNCLOCALFLAG, SYNCLOCALFLAG>& SandboxNode::GetStaticNotifySyncLocalFlagChanged()
	{
		return *s_notifySyncLocalFlagChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, ReflexValue*, AutoRef<ReflexTuple>, int>& SandboxNode::GetStaticNotifyRemoteInvocation()
	{
		return *s_notifyRemoteInvocation.EnsureInitialized();
	}
	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyNodeDirty()
	{
		return *s_notifyNodeDirty.EnsureInitialized();
	}
	Notify<SandboxNode*, ReflexValue*, std::vector<ReflexValue*>*, ReflexVariant*>& SandboxNode::GetStaticNotifyAttributeChanged()
	{
		return *s_notifyAttributeChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, const std::string&, const std::string&>& SandboxNode::GetStaticNotifyNameChanged()
	{
		return *s_notifyNameChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, bool>& SandboxNode::GetStaticNotifyHidden()
	{
		return *s_notifyNodeHidden.EnsureInitialized();
	}
	Notify<SandboxNode*, bool>& SandboxNode::GetStaticNotifyActiveChanged()
	{
		return *s_notifyActiveChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, bool>& SandboxNode::GetStaticNotifyEnergeticChanged()
	{
		return *s_notifyEnergeticChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, bool>& SandboxNode::GetStaticNotifyEnableChanged()
	{
		return *s_notifyEnableChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode*>& SandboxNode::GetStaticNotifyChildEnter()
	{
		return *s_notifyChildEnter.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode*>& SandboxNode::GetStaticNotifyChildLeave()
	{
		return *s_notifyChildLeave.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode*, SandboxNode*>& SandboxNode::GetStaticNotifyParentChanged()
	{
		return *s_notifyParentChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode*, SandboxNode*>& SandboxNode::GetStaticNotifyParentChangedEx()
	{
		return *s_notifyParentChangedEx.EnsureInitialized();
	}
	Notify<SandboxNode*, SandboxNode::NODECHANGE, std::string>& SandboxNode::GetStaticNotifyCustomAttrChanged()
	{
		return *s_notifyCustomAttrChanged.EnsureInitialized();
	}
	Notify<SandboxNode*, Scene*>& SandboxNode::GetStaticNotifyEnterScene()
	{
		return *s_notifyEnterScene.EnsureInitialized();
	}
	Notify<SandboxNode*, Scene*>& SandboxNode::GetStaticNotifyLeaveScene()
	{
		return *s_notifyLeaveScene.EnsureInitialized();
	}
	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyLeaveGame()
	{
		return *s_notifyLeaveGame.EnsureInitialized();
	}
	Notify<SandboxNode*>& SandboxNode::GetStaticNotifyEnterGame()
	{
		return *s_notifyEnterGame.EnsureInitialized();
	}
	////////////////////////////////////////////////////////////////////

	// AutoRef<SandboxNode>
	template<>
	void ReflexPolicyFunc<SandboxNode_Ref>::CallbackSerialize(const void* data, MNJsonVal& out)
	{
		auto& v = Data(data);
		MNJsonObject jsonobj;
		jsonobj << "_nodetype" << (v ? v->GetClassType() : std::string("invalid"));
		jsonobj << JsonKey_Sceneobjid << (MNJsonNumber)(v ? v->GetNodeid() : 0);
		out.import(jsonobj);
	}
	template<>
	bool ReflexPolicyFunc<SandboxNode_Ref>::CallbackUnserialize(void* data, const MNJsonVal& in)
	{
		auto& v = Data(data);
		if (!in.is<MNJsonObject>())
		{
			SANDBOX_ASSERT(false);
			return false;
		}

		auto& jsonobj = in.get<MNJsonObject>();
		if (!jsonobj.has<MNJsonNumber>(JsonKey_Sceneobjid))
			return false;

		v = GetSceneManager().GetNodeById((long long)jsonobj.get<MNJsonNumber>(JsonKey_Sceneobjid));
		return true;
	}
	template<>
	bool ReflexPolicyFunc<SandboxNode_Ref>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		auto& v = Data(data);
		v = SceneObjectBridge::GetInstance(L, objindex);
		return true;
	}
	template<>
	int ReflexPolicyFunc<SandboxNode_Ref>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		auto& v = Data(data);
		SceneObjectBridge::PushInstance(L, v);
		return 1;
	}
	template<>
	std::string ReflexPolicyFunc<SandboxNode_Ref>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		if (v)
		{
			return ToString("[node(", v->GetNodeid(), ")(", v->GetRTTI()->GetType(), "):", v->GetName(), "]");
		}
		else
		{
			return "[node(nil)]";
		}
	}
	template<>
	size_t ReflexPolicyFunc<SandboxNode_Ref>::ReflexToBinary(const void* data, const AutoRef<Stream>& out)
	{
		auto& v = Data(data);

		if (out->GetVersion() >= Config::ToVersion(0, 0, 2)) // 0.0.2 ~
		{
			AutoRef<Ref> ud = out->GetUserdata();
			using CustomSaveNodes = CustomRef<NodeSerialize::SaveNodeDatas>;
			if (ud.IsKindOf<CustomSaveNodes>()) // 按格式，使用序号
			{
				auto& indices = ud.StaticToCast<CustomSaveNodes>()->Data()._indices;

				int idx = -1; // 小于0 表示nullptr
				if (v)
				{
					auto iter = indices.find(v);
					if (iter == indices.end())
					{
						SANDBOX_LOG("SandboxNode_Ref to bin failed! not found in this tree! node=", v);
						return 0; //return Stream::Error;  找不到，不做处理即可，不去影响其他逻辑
					}

					idx = iter->second;
				}

				if (out->GetVersion() >= Config::ToVersion(0, 0, 3))// 0.0.3 ~ 写入的值加1，这样可以避免负数，负数存档占的空间会比较大
					++idx;

				return out->WriteNumber<int>(idx);
			}
		}

		// 原始 ~ 0.0.1
		return out->WriteNumber<long long>(v ? v->GetNodeid() : (SandboxNodeID)-1);
	}
	template<>
	bool ReflexPolicyFunc<SandboxNode_Ref>::ReflexFromBinary(void* data, const AutoRef<Stream>& in)
	{
		auto& v = Data(data);

		if (in->GetVersion() >= Config::ToVersion(0, 0, 2)) // 0.0.2 ~
		{
			AutoRef<Ref> ud = in->GetUserdata();
			using CustomSaveNodes = CustomRef<NodeSerialize::SaveNodeDatas>;
			if (ud.IsKindOf<CustomSaveNodes>()) // 按格式，使用序号
			{
				auto& datas = ud.StaticToCast<CustomSaveNodes>()->Data()._datas;

				int idx = 0;
				if (!in->ReadNumber<int>(idx))
					return false;

				if (in->GetVersion() >= Config::ToVersion(0, 0, 3))// 0.0.3 ~ 写入的值加1，这样可以避免负数，负数存档占的空间会比较大
					--idx;

				if (idx < 0) // 小于0 表示nullptr
				{
					v = nullptr;
				}
				else if (idx >= datas.size())
				{
					return false;
				}
				else
				{
					v = datas.at(idx)._node;
				}
				return true;
			}
		}

		// 原始 ~ 0.0.1
		long long nodeid = 0;
		if (!in->ReadNumber<long long>(nodeid))
			return false;

		v = GetSceneManager().GetNodeById(nodeid);
		return true;
	}
	RegisterReflexTypePolicy(SandboxNode_Ref, REFLEXTYPEENUM_REF_NODE, ReflexPolicyFunc<SandboxNode_Ref>, ReflexType::TYPE::SANDBOXNODE);

	// SandboxNode*
	typedef SandboxNode* ptr_sdbnode;
	template<>
	bool ReflexPolicyLua<ptr_sdbnode>::CallbackLuaToC(void* data, lua_State* L, int objindex)
	{
		auto& v = Data(data);
		v = SceneObjectBridge::GetInstance(L, objindex).get();
		return true;
	}
	template<>
	int ReflexPolicyLua<ptr_sdbnode>::CallbackLuaPushC(const void* data, lua_State* L)
	{
		auto& v = Data(data);
		SceneObjectBridge::PushInstance(L, AutoRef<SandboxNode>(v));
		return 1;
	}
	template<>
	std::string ReflexPolicyToString<ptr_sdbnode>::CallbackToString(const void* data)
	{
		auto& v = Data(data);
		AutoRef<SandboxNode> node = v;
		return ReflexPolicyFunc<SandboxNode_Ref>::CallbackToString(&node);
	}
	RegisterReflexTypePolicy(ptr_sdbnode, REFLEXTYPEENUM_PTR_NODE, ReflexPolicyLuaReg<ptr_sdbnode>, ReflexType::TYPE::SANDBOXNODE);

}



/** @example
 * @class  SandboxNode
 * @fn
 * @var
 * @event
 * ```lua
--SandboxNode node 有一个自定义属性 bool类型 名字是test_k
local v = node:GetAttribute("test_k")
 * ```
 */