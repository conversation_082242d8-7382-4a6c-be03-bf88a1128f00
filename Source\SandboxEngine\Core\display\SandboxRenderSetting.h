﻿#pragma once
#include "Render/RenderSetting.h"
#include "SandboxEngine.h"
#include "SkyPlaneDefine.h"
#include "Render/Postprocess/PostprocessGodRay.h"
#include "Render/Postprocess/PostprocessBloom.h"
#include "Render/Postprocess/PostprocessDof.h"
#include "Render/Postprocess/PostprocessAntialiasing.h"

class WorldRenderer;

namespace Rainbow
{
	struct GodrayParameter
	{
		GodrayParameter();
		PostprocessGodraySetting m_WaterConfig;
		PostprocessGodraySetting m_GroundConfig;
		PostprocessGodraySetting m_CustomConfig;
		bool m_UseCustomSetting;
		void AcceptConfig(PostprocessGodraySetting& setting, PostprocessGodraySetting& target);
	};


	struct SandboxRenderSettingData
	{
		SandboxRenderSettingData();
		bool m_UseBilinearSampler;
		bool m_KeepOriginTextureFormat;
		bool m_UsePlaneSkybox;
		
		bool m_FogEnable;
		bool m_ShadowEnable;
		bool m_WaterReflectEnable;
		SkyboxLevel m_SkyboxLevel;
		bool m_UseGrassAnimation;
		bool m_UseGrassSnowCover;
		bool m_UseTerrainNoiseUV;
		bool m_GodrayEnable;
		bool m_RainRipple;
		bool m_BloomEnable;
		bool m_DofEnable;
		bool m_AntialiasingEnable;
	};

	class  EXPORT_SANDBOXENGINE SandboxRenderSetting
	{
	public:
		SandboxRenderSetting();

		void Tick();

		SandboxRenderSettingData m_Data;
		void AcceptRenderSetting(bool overrdeCustomSetting);
		bool SetShadowEnable(bool value, bool showTips);
		void SetSkyboxLevel(SkyboxLevel level);
		void SetGodrayEnable(bool enable);
		void SetCausticsEnable(bool enable);
		void SetGrassAnimationEnable(bool enable);
		void SetGrassSnowCoverEnable(bool enable);
		bool CanShowShadow(float hour);
		bool IsNightMode(float hour);
		void SetGrassAnimationPower(Vector4f wind = Vector4f(0.0f, 0.0f, 1.0f, 1.0f), Vector4f wave = Vector4f(1.0f, 0.0f, 0.5f, 5.0f));
		void GetGrassAnimationParams(Vector4f& wind, Vector4f& wave);
		void SetRainRippleEnable(bool enable);

		void SetBloomEnable(bool enable);
		void SetBloomIntensity(float value);
		void SetBloomThreadHold(float value);

		void SetDofEnable(bool enable);
		void SetDofFocalRegion(float value);
		void SetDofNearTransitionRegion(float value);
		void SetDofFarTransitionRegion(float value);
		void SetDofFocalDistance(float value);
		void SetDofScale(float value);

		void SetAntialiasingEnable(bool value);

		void SetAntialiasingMethod(Rainbow::AntialiasingMethod method);
		bool GetAntialiasingMethod(Rainbow::AntialiasingMethod& method);
		void SetAntialiasingQuality(Rainbow::AntialiasingQuality quality);
		bool GetAntialiasingQuality(Rainbow::AntialiasingQuality& quality);

		//void SetGodRayScale(float value);
		//void SetGodRayThreshold(float value);
		//void SetGodRayTint(Rainbow::ColorRGBAf& value);

		void SetShadowDistance(float value);
		void SetShadowNumCascade(unsigned int value);
		void SetShadowTextureTileSize(UInt32 value);
		void SetShadowDistributeExponent(float value);
		void SetShadowFarScaleFactor(float value);

		void SetPostporcessEnable(bool enable);
		void SetLUTEnable(bool enable);

		bool CanShowShadowByTime(float hour);
	private:
		void UpdateTerrianVertexLayout();
		void InnerSetGodrayEnable(bool value, bool check_hour = true);
		bool CanShowGodray(float hour);
		void InnerSetBloomEnable(bool value);
		WorldRenderer* GetWorldRender();
		bool GetPostProcessingEnable();

	};

	class SandboxGraphicSettings
	{
	public:
		SandboxGraphicSettings();
		void Initialize();
		void Destroy();
		SandboxRenderSetting& GetCurSetting();
		static void OnPostGfxDeviceInit();
		static void OnInitGraphicsQualitySetting();
		static void OnRestoreGraphicsQualitySetting();

		void SetGodrayParams(PPtr<PostprocessGodray> postprocessGodray, bool onWater);
		void SetGodrayParamsUGC(PPtr<PostprocessGodray> postprocessGodray, const ColorRGBAf& color);

		void SetGodRayScale(float value);
		void SetGodRayThreshold(float value);
		void SetGodRayTint(const Rainbow::ColorRGBAf& value);
		void UseCustomGodrayParams(bool value);

	private:
		void SetDefaultGraphicsQualityMobile();
		void SetDefaultGraphicsQualityPC();
		GodrayParameter m_GodrayParameter;
		

	private:
		SandboxRenderSetting m_RenderSettings[kGraphicsQualityCount][kGraphicsPlatformCount];
	};
	EXPORT_SANDBOXENGINE SandboxRenderSetting* GetSandboxRenderSettingPtr();
	EXPORT_SANDBOXENGINE SandboxRenderSetting& GetSandboxRenderSetting();
	EXPORT_SANDBOXENGINE void SetSandboxGraphicQuality(int level, bool overrideCustomSetting = false);
	EXPORT_SANDBOXENGINE void InitSandboxRenderSettings();
	EXPORT_SANDBOXENGINE SandboxGraphicSettings& GetSandboxGraphicSettings();

}
