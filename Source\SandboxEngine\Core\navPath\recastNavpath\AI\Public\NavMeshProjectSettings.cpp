#include "NavMeshBuildSettings.h"
#include "NavMeshProjectSettings.h"
#include "NavMeshManager.h"
#include "Common/GameStatic.h"

const char* NavMeshProjectSettings::s_WarningCostLessThanOne = "Setting a NavMeshArea cost less than one can give unexpected results.";
const char* NavMeshProjectSettings::s_WarningUsingObsoleteAreaName = "The area name 'Default' is obsolete, please use 'Walkable' instead.";
const char* NavMeshProjectSettings::s_WarningTooFewSettingsNames = "Not all types of Agents defined for baking the NavMesh had names in the NavMeshAreas.asset file. Default names have now been added automatically.";
const char* NavMeshProjectSettings::s_WarningTooManySettingsNames = "There have been more names defined for settings in the NavMeshArea.asset file than there are Agent types defined for baking the NavMesh. The surplus names have now been deleted.";
const char* NavMeshProjectSettings::s_WarningDuplicateAgentTypeIds = "Duplicate settings for NavMesh agent type ID %i have been removed.";
static const char* kDefaultAgentTypeName = "Humanoid";

#define ASSERT_SETTINGS_SIZES() \
AssertFormatMsg (m_Settings.size () == m_SettingNames.size (), "The number of settings (%d) is out of sync with the number of setting names (%d).", m_Settings.size (), m_SettingNames.size ());

//static NavMeshProjectSettings m_instance;
static MINIW::GameStatic<NavMeshProjectSettings> m_instance;

NavMeshProjectSettings& GetNavMeshProjectSettings()
{ 
    return *m_instance.EnsureInitialized();
};

NavMeshProjectSettings::NavMeshProjectSettings()
{

}

void NavMeshProjectSettings::Reset()
{
    m_Areas[kWalkable].name = "Walkable";
    m_Areas[kWalkable].cost = 1.0f;

    m_Areas[kNotWalkable].name = "Not Walkable";
    m_Areas[kNotWalkable].cost = 1.0f;

    m_Areas[kJump].name = "Jump";
    m_Areas[kJump].cost = 2.0f;

    for (int i = kBuiltinAreaCount; i < kAreaCount; ++i)
        m_Areas[i].cost = 1.0f;

    // Create default agent with special id=0.
    m_Settings.resize(1);
    m_Settings[0].agentClimb = 0.75f;
    m_SettingNames.resize_initialized(1);
    m_SettingNames[0] = core::string(kDefaultAgentTypeName);

    m_LastAgentTypeID = 0;
}

void NavMeshProjectSettings::SetAreaCost(unsigned int index, float cost)
{
    if (index >= kAreaCount)
    {
        ErrorString("Index out of bounds");
        return;
    }
#if RAINBOW_EDITOR_
    if (cost < 1.0f)
    {
        WarningString(s_WarningCostLessThanOne);
    }
#endif
    m_Areas[index].cost = cost;
}

float NavMeshProjectSettings::GetAreaCost(unsigned int index) const
{
    if (index >= kAreaCount)
    {
        ErrorString("Index out of bounds");
        return 0.0f;
    }
    return m_Areas[index].cost;
}

void NavMeshProjectSettings::GetAllAreaCosts(float costs[kAreaCount]) const
{
    for (int i = 0; i < kAreaCount; ++i)
    {
        costs[i] = m_Areas[i].cost;
    }
}


int NavMeshProjectSettings::GetAreaFromName(const core::string& areaName) const
{
    for (int i = 0; i < kAreaCount; ++i)
    {
        if (m_Areas[i].name.compare(areaName) == 0)
        {
            return i;
        }
    }

    // The walkable area type got changed from "Default" to "Walkable" in 5.0.
    // If using old data, still accept "Default" as walkable.
    if (areaName.compare("Default") == 0)
    {
        WarningString(s_WarningUsingObsoleteAreaName);
        return kWalkable;
    }

    return -1;
}

dynamic_array<core::string> NavMeshProjectSettings::GetAreaNames() const
{
    dynamic_array<core::string> areas;
    for (int i = 0; i < kAreaCount; ++i)
    {
        if (m_Areas[i].name.length() != 0)
        {
            areas.push_back(m_Areas[i].name);
        }
    }
    return areas;
}

static bool IsUsedAgentTypeID(const std::vector<NavMeshBuildSettings>& settings, int id)
{
    if (id == NavMeshProjectSettings::kDefaultAgentTypeID || id == NavMeshProjectSettings::kInvalidAgentTypeID)
        return true;

    for (int i = 0; i < settings.size(); i++)
    {
        if (settings[i].agentTypeID == id)
            return true;
    }
    return false;
}

static bool IsUsedName(dynamic_array<core::string>& names, const core::string& name)
{
    for (int i = 0; i < names.size(); i++)
    {
        if (names[i].compare(name) == 0)
            return true;
    }
    return false;
}

int NavMeshProjectSettings::GetUnusedAgentTypeID(unsigned short userdata)
{
    int id;
    do
    {
        // Quick and dirty generator from Numerical Recipes
        m_LastAgentTypeID++;
        id = (((int)userdata)<<15) | m_LastAgentTypeID;
    }
    while (IsUsedAgentTypeID(m_Settings, id));

    return id;
}

const NavMeshBuildSettings& NavMeshProjectSettings::CreateSettings(unsigned short userdata)
{
    ASSERT_SETTINGS_SIZES();
    NavMeshBuildSettings settings;
    settings.agentTypeID = GetUnusedAgentTypeID(userdata);
    m_Settings.push_back(settings);

    int idx = 0;
    core::string name("New Agent");
    while (IsUsedName(m_SettingNames, name))
        name = Format("New Agent %d", ++idx);

    m_SettingNames.push_back(name);
    
    return m_Settings[m_Settings.size() - 1];
}

void NavMeshProjectSettings::UpdateSettings(const NavMeshBuildSettings& settings)
{
    ASSERT_SETTINGS_SIZES();
    for (size_t i = 0; i < m_Settings.size(); ++i)
    {
        if (m_Settings[i].agentTypeID == settings.agentTypeID)
        {
            m_Settings[i] = settings;
            return;
        }
    }
}

void NavMeshProjectSettings::RemoveSettings(int agentTypeID)
{
    ASSERT_SETTINGS_SIZES();
    if (agentTypeID == kDefaultAgentTypeID)
    {
        ErrorString("Default Agent type cannot be removed");
        return;
    }

    for (size_t i = 0; i < m_Settings.size(); ++i)
    {
        if (m_Settings[i].agentTypeID == agentTypeID)
        {
            m_Settings.erase(m_Settings.begin() + i);
            m_SettingNames.erase(m_SettingNames.begin() + i);
            return;
        }
    }
}

const NavMeshBuildSettings* NavMeshProjectSettings::GetSettingsByID(int agentTypeID) const
{
    ASSERT_SETTINGS_SIZES();
    for (size_t i = 0; i < m_Settings.size(); ++i)
    {
        if (m_Settings[i].agentTypeID == agentTypeID)
            return &m_Settings[i];
    }
    return NULL;
}

int NavMeshProjectSettings::GetSettingsCount() const
{
    ASSERT_SETTINGS_SIZES();
    return static_cast<int>(m_Settings.size());
}

const NavMeshBuildSettings* NavMeshProjectSettings::GetSettingsByIndex(int index) const
{
    ASSERT_SETTINGS_SIZES();
    if (index < 0 || index >= m_Settings.size())
        return NULL;
    return &m_Settings[index];
}

void NavMeshProjectSettings::SetSettingsNameForID(int agentTypeID, const core::string& name)
{
    ASSERT_SETTINGS_SIZES();
    for (size_t i = 0; i < m_Settings.size(); ++i)
    {
        if (m_Settings[i].agentTypeID == agentTypeID)
        {
            m_SettingNames[i] = name;
            return;
        }
    }
}

const core::string* NavMeshProjectSettings::GetSettingsNameFromID(int agentTypeID) const
{
    ASSERT_SETTINGS_SIZES();
    for (size_t i = 0; i < m_Settings.size(); ++i)
    {
        if (m_Settings[i].agentTypeID == agentTypeID)
            return &m_SettingNames[i];
    }
    return NULL;
}
