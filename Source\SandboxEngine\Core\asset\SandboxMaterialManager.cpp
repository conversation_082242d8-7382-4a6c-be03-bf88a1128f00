/**
* file : SandboxMaterialManager
* func : 沙盒材质相关
* by : chenzh
*/
#include "asset/SandboxMaterialManager.h"
#include "nodes/service/SandboxMaterialService.h"


namespace MNSandbox {


	Rainbow::SharePtr<Rainbow::MaterialInstance> MaterialManager::CreateMaterialInstance(const MaterialInfo& info)
	{
		auto materialService = GetCurrentMaterialService();
		if (materialService)
		{
			auto curMaterial = materialService->GetMaterialInstance((TemplateMaterialType)info._mtlType, info._alpha, info._color, info._texid, 0, info._texOverride);
			if (curMaterial)
				return curMaterial;
		}

		// 默认材质
		const char* defFile;
		if (info._alpha)
			defFile = "Materials/MiniGame/legacy/legacy_stdmtl_blend.templatemat";
		else
			defFile = "Materials/MiniGame/legacy/legacy_stdmtl_opaque.templatemat";
		return Rainbow::MoveToSharePtr<Rainbow::MaterialInstance>(Rainbow::GetMaterialManager().LoadFromFile(defFile)->CreateInstance());
	}

	std::string MaterialManager::GetMainTextureName(int mtlType)
	{
		auto materialService = GetCurrentMaterialService();
		if (materialService)
			return materialService->GetMainTexName((TemplateMaterialType)mtlType);

		return "g_DiffuseTex";
	}
}