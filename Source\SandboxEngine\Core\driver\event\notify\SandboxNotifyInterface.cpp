/**
* file : SandboxNotifyInterface
* func : 沙盒通知类
* by : chenzh
*/
#include "SandboxNotifyInterface.h"
#include "fps/statistics/SandboxStatistics.h"
#include "util/SandboxConfig.h"


namespace MNSandbox {


	const ::MNSandbox::RuntimeClass NotifyInterface::m_RTTI(&ReflexType::GetSingleton<NotifyInterface>(), "NotifyInterface", nullptr);

	NotifyInterface::NotifyInterface()
	{
	}

	NotifyInterface::~NotifyInterface()
	{
	}

	void NotifyInterface::_subscribe(const AutoRef<ListenerInterface>& listener, int sort)
	{
		listener->AddBindNotify(this, sort);
	}

	void NotifyInterface::_unsubscribe(const AutoRef<ListenerInterface>& listener)
	{
		if (CheckListenSelf(listener))
		{
			listener->RemoveBindNotify(this);
		}
	}

	/////////////////////////////////////////////////////////////////////////////////////////////

	IMPLEMENT_REFCLASS(ListenerInterface)

	ListenerInterface::ListenerInterface()
	{
		SANDBOXPROFILE_PUSHDATA(this, LISTENER, GetRTTI()->GetType());
		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Add(Statistics::INSCOUNT_TYPE::LISTENER);
	}

	ListenerInterface::~ListenerInterface()
	{
		if (!m_bindNotifys.empty())
		{
			auto iterCur = m_bindNotifys.begin();
			auto iterNext = iterCur;
			do
			{
				++iterNext;
				(*iterCur)->OnRemoveListener(this);
				iterCur = iterNext;
			} while (iterCur != m_bindNotifys.end());
			m_bindNotifys.clear();
		}

		if (Config::GetSingleton().IsShowDebugInfoAble())
			Statistics::SandboxInstanceCnt_Remove(Statistics::INSCOUNT_TYPE::LISTENER);
		SANDBOXPROFILE_POPDATA(this);
	}

	void ListenerInterface::AddBindNotify(NotifyInterface* notify, int sort)
	{
		if (!notify || IsBindNotify(notify))
			return;

		//AutoRef<Ref> self(this); // 避免提前释放
		if (!m_bindMultiNotify)
		{
			ClearBindNotify();
		}

		m_bindNotifys.insert(notify);
		notify->OnAddListener(this, sort);

	}
	void ListenerInterface::RemoveBindNotify(NotifyInterface* notify)
	{
		auto iter = m_bindNotifys.find(notify);
		if (iter == m_bindNotifys.end())
			return;

		//AutoRef<Ref> self(this); // 避免提前释放
		m_bindNotifys.erase(iter);
		notify->OnRemoveListener(this);
	}

	void ListenerInterface::UpdateClearNotify(NotifyInterface* notify)
	{
		m_bindNotifys.erase(notify);
	}

	void ListenerInterface::ClearBindNotify()
	{
		if (m_bindNotifys.empty())
			return;

		AutoRef<Ref> self(this); // 避免提前释放

		auto iterCur = m_bindNotifys.begin();
		auto iterNext = iterCur;
		do
		{
			++iterNext;
			(*iterCur)->OnRemoveListener(this);
			iterCur = iterNext;
		} while (iterCur != m_bindNotifys.end());
		m_bindNotifys.clear();
	}

	void ListenerInterface::CostRunTime()
	{
		//if (m_runtimes > 0)     --移到外面判断
		{
			m_runtimes--;
			if (m_runtimes == 0)
			{
				ClearBindNotify();
			}
		}
	}

}
