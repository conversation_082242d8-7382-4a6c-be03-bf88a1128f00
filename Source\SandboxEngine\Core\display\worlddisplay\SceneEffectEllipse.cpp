/**
* file : SceneEffectEllipse
* func : 场景效果 （椭圆）
* by : pengdapu
*/
#include "SceneEffectEllipse.h"
#include "world_types.h"
#include "world.h"
#include "SandboxMacros.h"
#include "SandboxPlane.h"

using namespace MNSandbox;
using namespace Rainbow;

SceneEffectEllipse::SceneEffectEllipse()
{
	m_Color = MakeBlockVector(0, 160, 255, 255);
	m_MtlType = CURVEFACEMTL_TEXWHITE_DEPTH_FUNC_ALWAY;
	m_vecDrawCircleLine.resize(0);
	m_iStroke = 1;
	m_eSes = SceneEffectShape::ELLIPSE;
}

SceneEffectEllipse::~SceneEffectEllipse()
{
}

void SceneEffectEllipse::OnClear()
{
	for (auto& item : m_vecDrawCircleLine)
	{
		if (item._line)
		{
			SANDBOX_DELETE(item._line);
		}
	}

	SANDBOX_DELETE(m_selectedline._line);
}

void SceneEffectEllipse::Refresh()
{
	OnClear();
	RefreshEllipseFrame(m_vCenter, m_qRotation, m_radius * m_vScale.m_x, m_radius * m_vScale.m_z);
}

void SceneEffectEllipse::OnDraw(World* pWorld)
{
	if (!pWorld || !m_bShow || m_radius == 0)
	{
		return;
	}

	for (auto& item : m_vecDrawCircleLine)
	{
		if (item._line)
		{
			item._line->SetDrawPos(item._startDrawPos);
			item._line->OnDraw(pWorld);
		}
	}

	if (m_selectedline._line)
	{
		m_selectedline._line->SetDrawPos(m_selectedline._startDrawPos);
		m_selectedline._line->OnDraw(pWorld);
	}
}

void SceneEffectEllipse::RefreshEllipseFrame(const Vector3f& vc, const Quaternionf& q, float major, float minor)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_semiMajorAxis = major;
	m_semiMinorAxis = minor;
	if (m_curRotationAxis == Vector3f(0.0))
	{
		return;
	}
	//绘制椭圆
	{
		//构造椭圆线段
		Matrix4x4f matRotate;
		QuaternionfToMatrix(q, matRotate);
		Matrix4x4f matAxis;
		matAxis.SetFromToRotation(Vector3f::neg_zAxis, m_curRotationAxis);
		Matrix4x4f mat = matAxis * matRotate;
		m_vecDrawCircleLine.resize(m_iSector);
		float radianSector = (m_fEndRadian - m_fStartRadian) / m_iSector;
		for (int i = 0; i < m_iSector; ++i)
		{
			float r1 = m_fStartRadian + radianSector * i;
			float r2 = r1 + radianSector;
			Vector3f vStartOnZ(0.f);
			vStartOnZ.x = major * cos(r1);
			vStartOnZ.y = minor * sin(r1);
			Vector3f vEndOnZ(0.f);
			vEndOnZ.x = major * cos(r2);
			vEndOnZ.y = minor * sin(r2);

			Vector3f vStart = mat.MultiplyPoint3(vStartOnZ) + vc;
			Vector3f vEnd = mat.MultiplyPoint3(vEndOnZ) + vc;

			DrawLine& item = m_vecDrawCircleLine[i];
			item._line = SANDBOX_NEW(SceneEffectLine, vStart, vEnd, m_circleLineWidth, m_MtlType, false);
			item._startDrawPos = vStart;
			item._endDrawPos = vEnd;
			item._line->SetColor(m_Color);
			item._line->SetCurveFaces(m_CurveFaces);
			item._line->Refresh();
		}
	}
}

void SceneEffectEllipse::SetSector(int sector, bool bRefresh)
{
	m_iSector = sector;
	if (bRefresh)
	{
		Refresh();
	}
}

// 设置旋转轴
void SceneEffectEllipse::SetRotationAxis(Vector3f curAxis, Vector3f originAxis, bool bRefresh)
{
	m_curRotationAxis = curAxis;
	m_originRotationAxis = originAxis;

	m_curRotationAxis.Normalize();
	m_originRotationAxis.Normalize();

	if (bRefresh)
	{
		Refresh();
	}
}

void SceneEffectEllipse::SetTRS(const Vector3f& vc, const Quaternionf& q, const Vector3f& vs)
{
	m_vCenter = vc;
	m_qRotation = q;
	m_vScale = vs;
	m_semiMajorAxis = m_radius * vs.x;
	m_semiMinorAxis = m_radius * vs.z;
}